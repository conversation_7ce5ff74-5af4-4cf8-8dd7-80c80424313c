{
  "extends": ["@react-native"],
  "plugins": [
    "prettier",
    "sonarjs",
    "import",
    "promise",
    "@typescript-eslint",
    "@mmt"
  ],
  "rules": {
    "import/no-unresolved": "off",
    "import/prefer-default-export": "off",
    "prettier/prettier": "warn",
//    "no-unused-vars": ["warn", { "vars": "all", "args": "none", "ignoreRestSiblings": false }],
    "@mmt/no-cross-module-imports": "error"//,
//    "no-console": "warn"
  },
  "reportUnusedDisableDirectives": false,
  "overrides": [
    {
      "files": ["{src,apps,packages}/**/*.{ts,tsx,js}"],
      "rules": {
        "@typescript-eslint/no-unused-vars": "off",
        "prettier/prettier": 0,
        "import/no-unresolved": "off",
        "import/prefer-default-export": "off",
        "no-const-assign": "off",
        "no-dupe-class-members": "off",
        "no-dupe-keys": "off",
        "no-duplicated-branches": "off",
        "no-eval": "off",
        "no-undef": "off",
        "no-unreachable": "off",
        "function-paren-sameline": "off",
        "promise/always-return": "off",
        "promise/catch-or-return": "off",
        "react-hooks/exhaustive-deps": "off",
        "react-hooks/rules-of-hooks": "off",
        "react/jsx-no-duplicate-props": "off",
        "react/jsx-no-undef": "off",
        "sonarjs/cognitive-complexity": "off"
      }
    }
  ],
  "globals": {
    "it": true,
    "expect": true,
    "element": true,
    "describe": true,
    "xdescribe": true,
    "by": true,
    "device": true,
    "beforeAll": true,
    "beforeEach": true,
    "afterAll": true,
    "jest": true,
    "jasmine": true
  }
}
