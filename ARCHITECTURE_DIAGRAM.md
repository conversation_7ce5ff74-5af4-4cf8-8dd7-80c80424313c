# MakeMyTrip React Native Application - High-Level Architecture

## Executive Summary
This is a large-scale, multi-module React Native application for MakeMyTrip's mobile platform, supporting multiple Lines of Business (LOBs) including flights, hotels, holidays, cabs, bus, rails, visa, and more.

## Architecture Overview

```
┌────────────────────────────────────────── ────────────────────────────────────────┐
│                                 PRESENTATION LAYER                                │
├─────────────────────────────────────────── ───────────────────────────────────────┤
│                                                                                   │
│  ┌──────────────────────────── App Modules ────────────────────────────┐          │
│  │                                                                      │         │
│  │  ┌─────────┐ ┌─────────┐ ┌──────────┐ ┌─────────┐ ┌─────────┐    │         │
│  │  │ Flights │ │  Hotels │ │ Holidays │ │   Cabs  │ │   Bus   │    │         │
│  │  └─────────┘ └─────────┘ └──────────┘ └─────────┘ └─────────┘    │         │
│  │                                                                      │         │
│  │  ┌─────────┐ ┌─────────┐ ┌──────────┐ ┌─────────┐ ┌─────────┐    │         │
│  │  │  Rails  │ │   Visa  │ │  Hubble  │ │  ACME   │ │  Growth │    │         │
│  │  └─────────┘ └─────────┘ └──────────┘ └─────────┘ └─────────┘    │         │
│  │                                                                      │         │
│  │  ┌─────────────┐ ┌─────────────┐ ┌──────────────┐                 │         │
│  │  │ Post-Sales  │ │ Travel Mall │ │  Trip Money  │                 │         │
│  │  └─────────────┘ └─────────────┘ └──────────────┘                 │         │
│  └──────────────────────────────────────────────────────────────────┘         │
│                                                                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              NAVIGATION & ROUTING                                 │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│  ┌─────────────────────────────────────────────────────────────────┐            │
│  │                     React Navigation (@react-navigation)         │            │
│  │  ┌──────────────┐  ┌──────────────┐  ┌───────────────┐        │            │
│  │  │ Stack Nav    │  │ MmtRouter    │  │ Route Config  │        │            │
│  │  └──────────────┘  └──────────────┘  └───────────────┘        │            │
│  └─────────────────────────────────────────────────────────────────┘            │
│                                                                                    │
│  Legacy: React Native Router Flux (being migrated)                               │
│                                                                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            STATE MANAGEMENT LAYER                                 │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│  ┌────────────────────────── Redux Architecture ──────────────────────┐          │
│  │                                                                     │          │
│  │  ┌──────────────┐  ┌──────────────┐  ┌────────────────┐         │          │
│  │  │ Global Store │  │ Module Stores│  │ Async Reducers │         │          │
│  │  └──────────────┘  └──────────────┘  └────────────────┘         │          │
│  │                                                                     │          │
│  │  ┌──────────────┐  ┌──────────────┐  ┌────────────────┐         │          │
│  │  │ Redux Thunk  │  │Redux Logger  │  │ Redux DevTools │         │          │
│  │  └──────────────┘  └──────────────┘  └────────────────┘         │          │
│  └─────────────────────────────────────────────────────────────────┘          │
│                                                                                    │
│  ┌────────────────────── Alternative State Management ────────────────┐          │
│  │  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐        │          │
│  │  │ Zustand  │  │  Immer   │  │  Formik  │  │React Hook│        │          │
│  │  │          │  │          │  │  (Forms) │  │   Form   │        │          │
│  │  └──────────┘  └──────────┘  └──────────┘  └──────────┘        │          │
│  └─────────────────────────────────────────────────────────────────┘          │
│                                                                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              SHARED COMPONENTS & UI                               │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│  ┌───────────────────── UI Component Libraries ─────────────────────┐           │
│  │                                                                   │           │
│  │  ┌─────────────┐  ┌──────────────┐  ┌────────────────┐        │           │
│  │  │   MMT-UI    │  │ @RN_UI_Lib   │  │@Frontend_Ui_Lib│        │           │
│  │  │  (Legacy)   │  │   (Current)  │  │     (New)      │        │           │
│  │  └─────────────┘  └──────────────┘  └────────────────┘        │           │
│  │                                                                   │           │
│  │  Common Components: Buttons, Modals, Sheets, Forms, etc.        │           │
│  └───────────────────────────────────────────────────────────────┘           │
│                                                                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                CORE PACKAGES                                      │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│  ┌─────────────────────────────────────────────────────────────────┐            │
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐         │            │
│  │  │legacy-commons│  │     core     │  │  navigation  │         │            │
│  │  └──────────────┘  └──────────────┘  └──────────────┘         │            │
│  │                                                                  │            │
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐         │            │
│  │  │   location   │  │xdm-analytics │  │     ui       │         │            │
│  │  └──────────────┘  └──────────────┘  └──────────────┘         │            │
│  │                                                                  │            │
│  │  ┌────────────────────┐  ┌─────────────────────┐               │            │
│  │  │ native-components   │  │ cab-location-picker │               │            │
│  │  └────────────────────┘  └─────────────────────┘               │            │
│  └─────────────────────────────────────────────────────────────────┘            │
│                                                                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                            NATIVE BRIDGE LAYER                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│  ┌─────────────────────── Native Modules ──────────────────────────┐            │
│  │                                                                  │            │
│  │  Payment │ Location │ Camera │ Analytics │ Push Notifications  │            │
│  │  Firebase │ Maps │ WebRTC │ Permissions │ Deep Linking         │            │
│  │  Performance │ AB Testing │ User Session │ Network             │            │
│  └─────────────────────────────────────────────────────────────────┘            │
│                                                                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                              DATA & API LAYER                                     │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│  ┌──────────────────────────────────────────────────────────────────┐           │
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐         │           │
│  │  │   REST APIs  │  │   GraphQL    │  │  WebSockets  │         │           │
│  │  │   (Axios)    │  │    (URQL)    │  │              │         │           │
│  │  └──────────────┘  └──────────────┘  └──────────────┘         │           │
│  │                                                                  │           │
│  │  ┌──────────────────────────────────────────────────┐         │           │
│  │  │        AsyncStorage (Local Persistence)          │         │           │
│  │  └──────────────────────────────────────────────────┘         │           │
│  └──────────────────────────────────────────────────────────────────┘           │
│                                                                                    │
├──────────────────────────────────────────────────────────────────────────────────┤
│                         EXTERNAL SERVICES & SDKS                                  │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│  Firebase │ Sentry │ Google Analytics │ Facebook SDK │ Google Ads              │
│  Lottie │ Maps │ Payment Gateways │ Push Services │ Chat Services              │
│                                                                                    │
└──────────────────────────────────────────────────────────────────────────────────┘
```

## Key Architectural Patterns

### 1. **Modular Monorepo Architecture**
- **Monorepo Structure**: Uses Yarn Workspaces with Lerna
- **Module Organization**: 
  - `apps/` - Individual LOB applications
  - `packages/` - Shared packages and utilities
  - `libraries/` - Custom implementations (Redux, React-Redux)

### 2. **Module Federation Pattern**
Each LOB (Line of Business) module follows a consistent structure:
```
module/
├── src/
│   ├── Navigation/     # Route configurations
│   ├── Components/     # Module-specific components
│   ├── Actions/        # Redux actions
│   ├── Reducers/       # Redux reducers
│   ├── Utils/          # Module utilities
│   └── module.ts       # Module definition & bootstrap
```

### 3. **State Management Architecture**

#### Redux-based Architecture:
- **Global Store**: Main application store
- **Module Stores**: Some modules (like holidays) have dedicated stores
- **Async Reducers**: Dynamic reducer injection for code splitting
- **Middleware**: Redux Thunk for async actions, Logger for debugging

#### Alternative State Solutions:
- **Zustand**: Used in newer modules for simpler state management
- **React Query**: For server state management
- **Formik/React Hook Form**: For form state management

### 4. **Navigation Architecture**

#### Dual Navigation System:
1. **React Navigation** (New - Primary):
   - Stack Navigator for screen management
   - Custom MmtRouter wrapper
   - Route configuration system

2. **React Native Router Flux** (Legacy - Being Migrated):
   - Still used in some older modules
   - Gradual migration to React Navigation

### 5. **Component Architecture**

#### Three-tier UI Component System:
1. **Legacy Components** (`MMT-UI`):
   - Original component library
   - Being phased out

2. **Current Components** (`@RN_UI_Lib`):
   - Standard component library
   - Used across most modules

3. **New Components** (`@Frontend_Ui_Lib`):
   - Latest component library
   - Modern design system implementation

### 6. **Data Flow Patterns**

```
User Action → Component → Action Creator → API Call → 
Reducer → Store Update → Component Re-render
```

### 7. **Code Splitting & Lazy Loading**
- **Dynamic Module Loading**: Modules loaded on-demand
- **Route-based Code Splitting**: Each route bundle loaded separately
- **Async Component Loading**: Components loaded when needed

## Technology Stack

### Core Technologies:
- **React Native**: 0.78.2
- **React**: 19.0.0
- **TypeScript**: 5.0.4
- **Redux**: 4.2.1
- **React Navigation**: 5.x

### Key Libraries:
- **State Management**: Redux, Redux Thunk, Zustand, Immer
- **Navigation**: React Navigation, React Native Router Flux (legacy)
- **Forms**: Formik, React Hook Form
- **API**: Axios, URQL (GraphQL)
- **Analytics**: Firebase, Sentry, Google Analytics
- **UI**: Lottie, React Native SVG, React Native Maps

## Performance Optimizations

1. **Bundle Optimization**:
   - Module-based code splitting
   - Dynamic imports for heavy components
   - React Native Bundle Visualizer for analysis

2. **Render Optimization**:
   - Performance profiler wrapper
   - Shopify's performance monitoring
   - Custom performance logging

3. **State Optimization**:
   - Reselect for memoized selectors
   - Immer for immutable updates
   - Module-specific stores to reduce scope

## Build & Deployment

### Build Process:
- **Metro Bundler**: Custom configuration for module resolution
- **Babel**: Custom presets for transformation
- **TypeScript**: Incremental compilation

### Platform Support:
- **Android**: Native modules via Java/Kotlin bridge
- **iOS**: Native modules via Objective-C/Swift bridge
- **Web**: Limited support via React Native Web

## Security & Monitoring

1. **Error Tracking**: Sentry integration
2. **Analytics**: Firebase Analytics, Custom event logging
3. **Performance Monitoring**: Custom performance profiler
4. **A/B Testing**: Native AB Config module

## Development Workflow

1. **Linting**: ESLint with Airbnb config
2. **Formatting**: Prettier
3. **Testing**: Jest, React Native Testing Library
4. **Pre-commit**: Husky, Lint-staged
5. **Code Review**: Gerrit integration

## Scalability Considerations

1. **Modular Architecture**: Each LOB can be developed independently
2. **Shared Components**: Reusable UI components across modules
3. **Dynamic Loading**: Modules loaded on-demand
4. **State Isolation**: Module-specific stores prevent state pollution
5. **Native Bridge**: Efficient native module communication

## Migration Strategy

The application is undergoing several migrations:
1. **Navigation**: Router Flux → React Navigation
2. **Components**: MMT-UI → @RN_UI_Lib → @Frontend_Ui_Lib
3. **State Management**: Exploring Zustand for new modules
4. **TypeScript**: Gradual migration from JavaScript

## Challenges & Solutions

### Challenges:
1. **Large Codebase**: 20+ modules in monorepo
2. **Legacy Code**: Multiple component libraries and navigation systems
3. **Performance**: Bundle size and initial load time
4. **State Management**: Complex state interactions across modules

### Solutions:
1. **Modular Architecture**: Independent module development
2. **Gradual Migration**: Phased approach to modernization
3. **Code Splitting**: Dynamic imports and lazy loading
4. **Performance Monitoring**: Comprehensive performance tracking

## Future Roadmap

1. **Complete React Navigation Migration**
2. **Unified Component Library**
3. **Enhanced TypeScript Coverage**
4. **Improved Bundle Optimization**
5. **Better State Management Patterns**
6. **Enhanced Performance Metrics**

---

## Interview Talking Points

When discussing this architecture in an interview:

1. **Scale & Complexity**: Emphasize managing 20+ business modules in a single app
2. **Migration Strategy**: Discuss gradual migration approach without disrupting users
3. **Performance**: Highlight bundle optimization and lazy loading strategies
4. **Team Collaboration**: Explain how modular architecture enables parallel development
5. **Technical Decisions**: Justify choices like Redux for state management, monorepo structure
6. **Challenges Overcome**: Share specific examples of performance improvements or architectural refactoring
7. **Future Vision**: Discuss potential improvements and modernization plans

This architecture demonstrates enterprise-scale mobile application development with focus on modularity, performance, and maintainability.
