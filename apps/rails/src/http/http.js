import _isEmpty from 'lodash/isEmpty';
import OmnitureModule from '@mmt/legacy-commons/Native/OmnitureModule';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import {getUserDetails, ProfileType} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import fetch2,{getLanguageHeaders} from '../fetch2';
import { isAndroid, isIos } from '../Utils/device';

let deviceDetails = null;
let userDetails = null;

let commonHeader = {
  Accept: 'application/json',
  'Content-Type': 'application/json',
};

const setCommonHeaders = () => {
  let userDetailsHeaderObject = null;
  let deviceDetailsHeaderObject = null;

  try {
    if (userDetails && userDetails.mmtAuth && userDetails.profileType) {
      userDetailsHeaderObject = {
        'mmt-auth': userDetails.mmtAuth,
        'mmt-my-business-subscription': userDetails.profileType === ProfileType.BUSINESS ? 'b2b' : 'b2c',
      };
    }
  } catch (e) {
    userDetailsHeaderObject = {
      'mmt-my-business-subscription': 'b2c',
    };
  }

  try {
    deviceDetailsHeaderObject = {
      'mmt-os': deviceDetails.os,
      'mmt-os-version': deviceDetails.osVersion,
      'mmt-app-version': deviceDetails.appVersion,
      'mmt-device-id': isIos() ? deviceDetails.deviceId : deviceDetails.pdtDeviceId,
    };
  } catch (e) {
    deviceDetailsHeaderObject = {};
  }

  commonHeader = {
    ...commonHeader,
    ...deviceDetailsHeaderObject,
    ...userDetailsHeaderObject,
  };
};

const createRailsDeviceAndUserDetails = async () => {
  try {
    if (isIos()) {
      const deviceInfo = await OmnitureModule.deviceInfo();
      if (!_isEmpty(deviceInfo)) {
        deviceDetails = {
          os: 'ios',
          osVersion: deviceInfo.dvc_os_ver,
          deviceType: deviceInfo.dvc_type,
          appVersion: deviceInfo.dvc_app_ver,
          deviceId: deviceInfo.dvc_did,
          networkType: deviceInfo.dvc_conn_type,
          deviceName: `${deviceInfo.dvc_manuf} ${deviceInfo.dvc_mdl}`,
        };
      }
    } else if (isAndroid()) {
      const deviceInfo = await GenericModule.getDeviceInfo();
      if (!_isEmpty(deviceInfo)) {
        const {
          ...rest
        } = deviceInfo;
        deviceDetails = {
          ...rest,
          os: 'android',
          osVersion: deviceInfo.androidVersion,
        };
      }
    }
  } catch (e) {
    deviceDetails = null;
  }
  try {
    userDetails = await getUserDetails();
  } catch (e) {
    userDetails = null;
  }
  setCommonHeaders();
};

const http = {};

http.get = async (url) => {
  await createRailsDeviceAndUserDetails();
  const config = {
    headers: {
      commonHeader,
      ...getLanguageHeaders(),
    },
  };
  const response = await fetch2(url, config);
  return response.json();
};

http.post = async (apiUrl, params = {}) => {
  await createRailsDeviceAndUserDetails();
  const body = JSON.stringify(params);
  // console.log('http post url is ', apiUrl, 'body is ', params, 'header is', commonHeader, 'userDetails', userDetails);
  const response = await fetch2(apiUrl, {
    method: 'POST',
    headers: {
      ...commonHeader,
      ...getLanguageHeaders(),
    },
    body,
  });
  return response.json();
};

export default http;
