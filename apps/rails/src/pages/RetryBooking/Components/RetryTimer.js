import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, Text} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

export default class RetryTimer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      elapseTime: this.props.timeOut,
    };
  }


  async UNSAFE_componentWillMount() {
    this.interval = setInterval(() => {
      const time = this.state.elapseTime - 1;
      if (time <= 0) {
        clearInterval(this.interval);
        this.props.onDone();
      }
      this.setState({
        elapseTime: time,
      });
    }, 1000);
  }

  _append0 = number => (number > 9 ? number : `0${number}`);

  _getBeautifiedElapsedTime = () => {
    const hrs = this._append0(Math.floor(this.state.elapseTime / 60));
    const mins = this._append0(Math.floor(this.state.elapseTime % 60));

    return `${hrs}m:${mins}s`;
  };

  componentWillUnmount() {
    if (this.interval) {
      clearInterval(this.interval);
    }
  }

  render() {
    return (
      <Text style={[styles.timerDisplayText, fontStyle('bold'), getLineHeight(16)]}>{` ${this._getBeautifiedElapsedTime()} `}</Text>
    );
  }
}

RetryTimer.propTypes = {
  onDone: PropTypes.func.isRequired,
  timeOut: PropTypes.number.isRequired,
};

const styles = StyleSheet.create({

  timerDisplayText: {
    color: colors.black,
    fontSize: 16,
    backgroundColor: colors.transparent,
  },
});

