import React from 'react';
import {connect} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import { BackHandler, Image, ScrollView, Text, View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { Actions } from '../../../navigation';
import fecha from 'fecha';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {TrainNameAndNumber} from '../../Common/JourneyDetailsCard';
import {getClassType} from '../../Types/ClassType';
import { getQuota } from '../../Types/QuotaType';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import CustomizedTripHeader from '@mmt/legacy-commons/Common/Components/Header/CustomizedTripHeader';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import UserNameModal from '../../NewUserFlow/UserNameModal';
import {updateLocalStorage} from '../../User/UserVerification/UserVerificationActions';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import fetch2 from '../../../fetch2';
import RailsConfig from '../../../RailsConfig';
import {
  RAILS_MMT_ID,
  setDataToAsyncStorage,
} from '../../../Utils/RailsConstant';
import {isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {loadUserDetails} from '../../TravelerDetails/TravelerDetailsActions';
import CustomizedSomethingWentWrong from '../../ErrorPages/CustomizedSomethingWentWrong';
import RetryTimer from './RetryTimer';
import { trackRetryEvent, trackRetryPageLoad, trackEvar71Event } from '../../../railsAnalytics';
import RetryObject from '../constants/RetryObject';
import RailRetrievePasswordContainer from '../../Common/RailRetrievePasswordContainer';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { isIos, isMweb } from '../../../Utils/device';
import { _label } from '../../../vernacular/AppLanguage';
import { GFT_PDT_EVENTS } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/GFT/PdtGftConstants';
import { logGftPageEventsToPdt } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/GFT';

import changeUserName from '@mmt/legacy-assets/src/ic-change-username.webp';
import resetPassword from '@mmt/legacy-assets/src/ic-reset-password-light-grey.webp';
import cancel from '@mmt/legacy-assets/src/ic-cancel.webp';
import wallet from '@mmt/legacy-assets/src/ic-wallet-green.webp';
import card from '@mmt/legacy-assets/src/ic-card-yellow.webp';
import PropTypes from 'prop-types';
import BottomSheetModalTrain from '../../Common/BottomSheetModalTrain';

const eventMapping = {
  whatsapp: 'fail_retry_wa',
  sms: 'fail_retry_sms',
  email: 'fail_retry_sms',
  mytrips: 'fail_retry_mt',
};

const trackRetryEventBasedOnSource = (source, pageName) => {
  const eventName = eventMapping[source];
  if (eventName) {
    trackEvar71Event(pageName, eventName);
  }
};

class RetryBooking extends React.Component {
  constructor(props) {
    super(props);
    this.initState = {
      showModal: false,
      showUserModal: false,
      showPasswordModal: false,
      bookingDetails: null,
      allowIrctcUserIdChange: true,
      irctcUserName: '',
      subHeader: '',
      doj: new Date(),
      showLoadingError: false,
      showBookingError: false,
      disabled: false,
      mmtAuth: null,
      elapsedTime: 600,
      loading: false,
      refundLoader:false,
    };
    this.state = {
      ...this.initState,
    };
  }

  async UNSAFE_componentWillMount() {
    this.loadPage();
    trackRetryPageLoad();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({
      irctcUserName: nextProps.irctcUserName,
    });
  }

  disableBooking = () => {
    this.setState({disabled: true});
  };

  loadPage = async () => {
    trackRetryEventBasedOnSource(this.props.source, 'RetryBookingPage');
    const isLoggedIn = await isUserLoggedIn();
    const userDetails = await loadUserDetails(isLoggedIn);
    if (!userDetails || !userDetails.mmtAuth) {
      this.setState({
        showLoadingError: true,
        subHeader: 'You are not logged in. Please log in to continue',
      });
      this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.GFT_NOT_LOGGED_IN_ERROR);
      return;
    }
    const {mmtAuth} = userDetails;
    this.setState({mmtAuth});
    const {mmtId, retryId} = this.props;
    const res = await fetch2(RailsConfig.failedBookingDetails, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'mmt-auth': mmtAuth,
      },
      body: JSON.stringify({
        mmtId,
        retryId,
      }),
    });
    const data = await res.json();
    if (res.status === 200) {
      const elapsedTime = (data.retryTimeoutInMins * 60) -
        (Math.floor(((new Date().getTime()) / 1000) - (data.retryStartTime / 1000)));
      this.setState({
        irctcUserName: data.irctcUserId,
        bookingDetails: data.bookingDetails,
        allowIrctcUserIdChange: data.allowIrctcUserIdChange,
        doj: new Date(data.bookingDetails.journeyDate),
        elapsedTime: elapsedTime >= 0 ? elapsedTime : data.retryTimeoutInMins * 60,
      });
      this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.GFT_PAGE_LOAD);
    } else {
      this.setState({
        showLoadingError: true,
        subHeader: data.errorDetails.errorMessage,
      });
      this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.GFT_PAGE_LOAD);
    }
  };

  continueBooking = async () => {
    trackRetryEvent('mob_rail_booking_retry_continue_booking_clicked');
    const {mmtAuth, irctcUserName, disabled, loading} = this.state;
    if (disabled || loading) { return; }
    const {mmtId, retryId} = this.props;
    this.setState({loading: true});
    try {
      const res = await fetch2(RailsConfig.retryUrl, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'mmt-auth': mmtAuth,
        },
        body: JSON.stringify({
          mmtId,
          retryId,
          irctcUserId: irctcUserName,
        }),
      });
      const data = await res.json();
      if (res.status === 200) {
        // TODO if irctcUserIdChange is false handle separately
        await setDataToAsyncStorage(RAILS_MMT_ID, mmtId);
        if (isMweb()) {
          Actions.openIrctcPage();
          return;
        }
        Actions.confirmBooking({
          type: 'replace',
          mmtId: mmtId,
          irctcUserId: irctcUserName,
        });
        this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.GFT_CONTINUE_BOOKING_CLICKED);
      } else {
        this.setState({
          showBookingError: true,
          subHeader: data.errorDetails.errorMessage,
        });
        this.props.logGftPdtEventsToPdt(
          GFT_PDT_EVENTS.GFT_CONTINUE_BOOKING_CLICKED,
          data?.errorDetails,
        );
      }
    } catch (e) {
      this.setState({showBookingError: true});
    }
    this.setState({loading: false});
  };

  refundBooking = refundOption => async () => {
    trackRetryEvent(refundOption.code === 'Refund_To_Wallet'
      ? 'mob_rail_booking_retry_refund_mmt_wallet_clicked'
      : 'mob_rail_booking_retry_refund_bank_clicked');
    const {mmtId} = this.props;
    try {
      this.setState({ refundLoader: true });
      const res = await fetch2(RailsConfig.refundOptionUrl + mmtId, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refundAdjustment: refundOption.code,
        }),
      });
      const data = await res.json();
      this.setState({ refundLoader: false });
      if (data && data.refundOptionAvailable) {
        Actions.railsRefund({
          type: 'replace',
          refundType: refundOption.id,
          totalCollectibleAmount: this.state.bookingDetails.totalCollectibleAmount,
        });
      } else {
        this.setState({showBookingError: true});
      }
    } catch (e) {
      this.setState({ showBookingError: true, refundLoader: false });
    }
  };

  onHardBackPress = () => {
    if (isIos()) {
      ViewControllerModule.thankyouDismiss(this.props.rootTag);
    } else if (isMweb()) {
      window.location.href = '//www.makemytrip.com';
    } else {
      BackHandler.exitApp();
    }
    return true;
  };
/* eslint-disable */
  refreshPage = () => {
    this.setState(() => ({
      ...this.initState,
    }), this.loadPage);
  };

  _setModalVisibility = (visibility) => {
    this.setState({showUserModal: visibility});
  };

  _updateUsernamePwdRequested = (irctcUserName, pwdRequested) => {
    updateLocalStorage(irctcUserName, pwdRequested);
  };

  _dismissModal = () => {
    this.setState({
      showPasswordModal: false,
    });
  };

  render() {
    const {
      bookingDetails, doj, showLoadingError, showBookingError,
    } = this.state;
    if (showLoadingError) {
      return (
        <CustomizedSomethingWentWrong
          onRefresh={this.onHardBackPress}
          header=""
          subHeader={this.state.subHeader}
          buttonText={_label('go_to_home_page', { uppercase: true })}
        />
      );
    }
    if (showBookingError) {
      return (
        <CustomizedSomethingWentWrong
          onRefresh={this.refreshPage}
          header=""
          subHeader={this.state.subHeader}
          buttonText={_label('refresh', { uppercase: true })}
        />
      );
    }
    if (!bookingDetails) {
      return null;
    }
    const {
      trainName, trainNumber, journeyQuota, journeyClass, totalCollectibleAmount,
      sourceStationCode, destinationStationCode, numberOfPassenger,
    } = bookingDetails;

    const trainInfo = {
      trainName,
      trainNumber,
      selectedQuota: getQuota(journeyQuota),
      classValue: getClassType(journeyClass),
      showBoardingStationChange: true,
    };
    const {
      showModal, showUserModal, showPasswordModal, irctcUserName, elapsedTime, disabled, loading,
    } = this.state;
    const { labels, logGftPdtEventsToPdt } = this.props;
    return (
      <View style={{flex: 1}}>
        <CustomizedTripHeader
          title={labels.headerContinueBooking}
          subTitle={`${sourceStationCode} to ${destinationStationCode}  |  ${fecha.format(doj, 'D MMM, ddd')}  |  ${numberOfPassenger} Travellers`}
          onPressBackHandler={this.onHardBackPress}
        />
        <ScrollView style={{
            marginTop: 2,
            flex: 1,
          }}
        >

          <View style={{
              paddingHorizontal: 16,
              paddingBottom: 16,
            }}
          >
            <TrainNameAndNumber {...trainInfo} labels={labels} />
            <View>
              <Card
                style={{
                    borderRadius: 2,
                    marginVertical: 8,
                    marginHorizontal: 0,
                    paddingBottom: 16,
                    marginTop: 16,
                  }}
                showBorder
              >
                <View style={{
                    paddingVertical: 16,
                    paddingHorizontal: 16,
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                  }}
                >
                  <Text style={[styles.headerText, fontStyle('bold'), getLineHeight(16)]}>{`Your payment of ${totalCollectibleAmount} was successful`}</Text>
                  <Text style={[styles.subHeaderText, fontStyle('regular'), getLineHeight(14)]}>
                    Please enter the password for your IRCTC username
                    <Text style={fontStyle('bold')}>{` ${irctcUserName} `}</Text>
                    and complete your booking!
                  </Text>
                </View>
              </Card>
              {
                !disabled
                  ?
                    <TouchableRipple onPress={this.continueBooking}>
                      <View style={styles.linearGradContainer}>
                        <LinearGradient
                          style={styles.linearGrad}
                          colors={['#53B2FE', '#065AF3']}
                          start={{
                        x: 0.0,
                        y: 0.0,
                      }}
                          end={{
                        x: 1.0,
                        y: 0.0,
                      }}
                        >
                          <View>
                            {
                              loading ?
                          <Spinner size={20} color={colors.white} />
                              :
                                <Text style={[styles.proceedText, fontStyle('regular'), getLineHeight(16)]}>
                                  {labels.btnContinueBooking}
                                </Text>
                            }

                          </View>
                        </LinearGradient>
                      </View>
                    </TouchableRipple>
                  :
                    <View>
                      <LinearGradient
                        style={styles.linearGrad}
                        colors={[colors.disabledButton, colors.disabledButton]}
                        start={{
                        x: 0.0,
                        y: 0.0,
                      }}
                        end={{
                        x: 1.0,
                        y: 0.0,
                      }}
                      >
                        <View>
                          <Text style={[styles.proceedText, fontStyle('regular'), getLineHeight(16)]}>
                            LINK EXPIRED
                          </Text>
                        </View>
                      </LinearGradient>
                    </View>
              }
            </View>
            <Text style={[styles.warningText, fontStyle('regular'), getLineHeight(14)]}>{labels.usernameCantBeChanged}</Text>
          </View>
          <View style={{
              backgroundColor: colors.grayBg,
              height: 20,
            }}
          />
          <View style={{
              paddingHorizontal: 16,
              paddingBottom: 16,
            }}
          >
            <Text style={{
                paddingVertical: 20,
                fontSize: 22,
                ...fontStyle('light'),
                ...getLineHeight(22),
              }}
            >{labels.needHelp}
            </Text>
            <Card
              style={{
                  flexDirection: 'column',
                  marginHorizontal: 0,
                  marginVertical: 0,
                }}
              showBorder
            >
              {
                this.state.allowIrctcUserIdChange &&
                <TouchableRipple
                  onPress={() => {
                  trackRetryEvent('mob_rail_booking_retry_another_username_clicked');
                  this.setState({showUserModal: true});
                    logGftPdtEventsToPdt(GFT_PDT_EVENTS.GFT_CHANGE_USERNAME_CLICKED);
                }}
                >
                  <View style={styles.bannerContainer}>
                    <View style={styles.imageContainer}>
                      <Image
                        style={styles.imgStyle}
                        source={changeUserName}
                      />
                    </View>
                    <View style={styles.textContainer}>
                      <Text style={[styles.headerText, fontStyle('bold'), getLineHeight(16)]}>{labels.useAnotherUsername}</Text>
                      <Text style={[styles.subHeaderText, fontStyle('regular'), getLineHeight(14)]}>{labels.editOrCreateUsername}</Text>
                    </View>
                  </View>
                </TouchableRipple>
              }
              <TouchableRipple
                onPress={() => {
                  trackRetryEvent('mob_rail_booking_retry_forgot_password_clicked');
                  this.setState({showPasswordModal: true});
                  logGftPdtEventsToPdt(GFT_PDT_EVENTS.GFT_FORGOT_PASSWORD_CLICKED);
                }}
              >
                <View style={styles.bannerContainer}>
                  <View style={styles.imageContainer}>
                    <Image
                      style={styles.imgStyle}
                      source={resetPassword}
                    />
                  </View>
                  <View style={styles.textContainer}>
                    <Text style={[styles.headerText, fontStyle('bold'), getLineHeight(16)]}>{labels.forgotPassword}</Text>
                    <Text style={[styles.subHeaderText, fontStyle('regular'), getLineHeight(14)]}>{labels.getANewPwd}</Text>
                  </View>
                </View>
              </TouchableRipple>
              {
                this.state.allowIrctcUserIdChange &&
                <TouchableRipple
                  onPress={() => {
                  trackRetryEvent('mob_rail_booking_retry_cancel_transaction_clicked');
                  this.setState({showModal: true});
                    logGftPdtEventsToPdt(GFT_PDT_EVENTS.GFT_CANCEL_BOOKING_CLICKED);
                }}
                >
                  <View style={styles.bannerContainerWithoutBorder}>
                    <View style={styles.imageContainer}>
                      <Image
                        style={styles.imgStyle}
                        source={cancel}
                      />
                    </View>
                    <View style={styles.textContainer}>
                      <Text style={[styles.headerText, fontStyle('bold'), getLineHeight(16)]}>{labels.cancelThisTransaction}</Text>
                      <Text style={[styles.subHeaderText, fontStyle('regular'), getLineHeight(14)]}>{labels.getARefundPayment}</Text>
                    </View>
                  </View>
                </TouchableRipple>
              }
            </Card>
            {
              !disabled &&
              <View style={{marginTop: 10}}>
                <Text>This page will expire in
                  <RetryTimer
                    timeOut={elapsedTime}
                    onDone={this.disableBooking}
                  />
                  and we’ll refund your payment automatically.
                </Text>
              </View>
            }
          </View>
        </ScrollView>
        {
            showModal &&
            <BottomSheetModalTrain onTouchOutside={() => {
              this.setState({showModal: false});
            }}
            testID="refund_bottomsheet_modal"
            >
              <View style={{backgroundColor: colors.white}}>
                <View style={styles.modalContainer}>
                  <Text style={{
                    ...fontStyle('light'),
                    ...getLineHeight(34),
                    fontSize: 34,
                    color: colors.black,
                  }}
                  >{labels.getARefund}
                  </Text>
                  <Text style={{
                    fontSize: 16,
                    ...getLineHeight(16),
                    color: colors.black,
                  }}
                  >{labels.whereToReceiveAmount}
                  </Text>
                </View>
                <TouchableRipple
                  onPress={!this.state.refundLoader ? this.refundBooking(
                    RetryObject(totalCollectibleAmount).REFUND_TO_BANK) : ()=>{
                      // do nothing
                    }}
                    testID="refund_into_original_paymode_button"
                >
                  <View style={styles.modalBannerContainer}>
                    <View style={{
                      padding: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    >
                      <Image
                        style={styles.imgStyle}
                        source={card}
                      />
                    </View>
                    <View style={{
                      flex: 1,
                      justifyContent: 'center',
                      paddingRight: 16,
                      paddingVertical: 16,
                    }}
                    >
                      <Text style={{
                        ...fontStyle('bold'),
                        ...getLineHeight(16),
                        fontSize: 16,
                        color: colors.black,
                      }}
                      >{labels.refundIntoOriginalPaymode}
                      </Text>
                      <Text style={{
                        fontSize: 14,
                        ...getLineHeight(14),
                        color: colors.defaultTextColor,
                      }}
                      >{labels.processedInApprox7Days}
                      </Text>
                    </View>
                  </View>
                </TouchableRipple>
                <TouchableRipple
                  onPress={!this.state.refundLoader ? this.refundBooking(
                    RetryObject(totalCollectibleAmount).REFUND_TO_WALLET) : ()=>{
                      // do nothing
                    }}
                >
                  <View style={styles.modalBannerContainerWithoutBorder}>
                    <View style={{
                      padding: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    >
                      <Image
                        style={styles.imgStyle}
                        source={wallet}
                      />
                    </View>
                    <View style={{
                      flex: 1,
                      justifyContent: 'center',
                      paddingRight: 16,
                      paddingVertical: 16,
                    }}
                    >
                      <Text style={{
                        ...fontStyle('bold'),
                        fontSize: 16,
                        ...getLineHeight(16),
                        color: colors.black,
                      }}
                      >{labels.refundIntoMakeMyTripWallet}
                      </Text>
                      <Text style={{
                        fontSize: 14,
                        ...getLineHeight(14),
                        color: colors.defaultTextColor,
                      }}
                      >{labels.processedInstantlyAsMyCash}
                      </Text>
                    </View>
                  </View>
                </TouchableRipple>
              </View>
            </BottomSheetModalTrain>
          }
        {
            showUserModal &&
            <UserNameModal
              modalVisible={showUserModal}
              setModalVisibility={this._setModalVisibility}
              from="railsFailedBookingRetryDeeplink"
              userName={irctcUserName}
            />
          }
        {showPasswordModal &&
          <RailRetrievePasswordContainer
            dismiss={this._dismissModal}
            irctcUserName={irctcUserName}
            updateUsernamePwdRequested={this._updateUsernamePwdRequested}
          />
          }
      </View>
    );
  }
}

const styles = {
  bannerContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: colors.white,
    paddingVertical: 5,
    borderBottomWidth: 2,
    borderBottomColor: colors.grayBg,
  },
  bannerContainerWithoutBorder: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: colors.white,
    paddingVertical: 5,
  },
  retryBookingContainer: {
    flexDirection: 'row',
    backgroundColor: colors.green,
    paddingVertical: 5,
    marginBottom: 10,
  },
  textContainer: {
    justifyContent: 'center',
    padding: 10,
  },
  modalHeaderText: {
    color: colors.black,
    fontSize: 34,
  },
  headerText: {
    fontSize: 16,
    color: colors.black,
  },
  subHeaderText: {
    fontSize: 14,
    color: colors.defaultTextColor,
  },
  warningText: {
    fontStyle: 'italic',
    fontSize: 14,
    alignSelf: 'center',
    color: colors.defaultTextColor,
    marginTop: 20,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    marginHorizontal: 10,
  },
  imgStyle: {
    width: 32,
    height: 32,
  },
  linearGradContainer: {
    height: 60,
    position: 'absolute',
    alignSelf: 'center',
    bottom: -16,
    elevation: 5,
  },
  linearGrad: {
    borderRadius: 100,
    height: 40,
    paddingHorizontal: 48,
    marginVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  proceedText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    backgroundColor: 'transparent',
  },
  modalBannerContainer: {
    flexDirection: 'row',
    borderBottomWidth: 2,
    borderBottomColor: colors.grayBg,
  },
  modalBannerContainerWithoutBorder: {
    flexDirection: 'row',
  },
  modalContainer: {
    borderBottomWidth: 2,
    borderBottomColor: colors.grayBg,
    paddingHorizontal: 20,
    paddingVertical: 30,
  },
};

const getLabels = texts => ({
  headerContinueBooking: texts.retry_header_continue_booking,
  successMessage$Amount: texts.retry_success_message_$amount,
  pleaseEnter$Username: texts.retry_please_enter_$username,
  btnContinueBooking: texts.retry_btn_continue_booking,
  usernameCantBeChanged: texts.retry_username_cant_be_changed,
  needHelp: texts.retry_need_help,
  useAnotherUsername: texts.retry_use_another_username,
  editOrCreateUsername: texts.retry_edit_or_create_username,
  forgotPassword: texts.retry_forgot_password,
  getANewPwd: texts.retry_get_a_new_pwd,
  cancelThisTransaction: texts.retry_cancel_this_transaction,
  getARefundPayment: texts.retry_get_a_refund_payment,
  pageWillExpireIn$Time: texts.retry_page_will_expire_in_$time,
  getARefund: texts.retry_get_a_refund,
  whereToReceiveAmount: texts.retry_where_to_receive_amount,
  refundIntoOriginalPaymode: texts.retry_refund_into_original_paymode,
  processedInApprox7Days: texts.retry_processed_in_approx_7_days,
  refundIntoMakeMyTripWallet: texts.retry_refund_into_make_my_trip_wallet,
  processedInstantlyAsMyCash: texts.retry_processed_instantly_as_my_cash,
});

const mapStateToProps = (state, ownProps) => {
  const {
    railsUserVerification: { irctcUserName },
    railsVernacular: { texts },
  } = state;
  return { ...ownProps, irctcUserName, labels: getLabels(texts) };
};

const mapDispatchToProps = (dispatch) => ({
  logGftPdtEventsToPdt: (eventValue, errorDetails) => {
    dispatch(logGftPageEventsToPdt(eventValue, errorDetails));
  },
});

RetryBooking.propTypes = {
  irctcUserName: PropTypes.string,
  source: PropTypes.string,
  logGftPdtEventsToPdt: PropTypes.func,
  mmtId: PropTypes.string,
  retryId: PropTypes.string,
  labels: PropTypes.object,
  rootTag: PropTypes.any
};

export default connect(mapStateToProps, mapDispatchToProps)(RetryBooking);

