import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import ASSETS from '../../../Utils/Assets/RailsAssets';

const retryObject = Object.freeze(amount => ({
  REFUND_TO_BANK: {
    code: 'Refund_To_Customer',
    header: 'Refund Initiated',
    subHeader: `${numAppendedWithRuppeeSymbol(amount)} will be added to your bank account`,
    content: 'This refund will be processed by MakeMyTrip in 7 business days. ' +
      'It could take 10-12 business days further to reflect in your bank account. ' +
      'We’ll keep you updated through SMS and email!',
    image: ASSETS.icRefundBank,
    id: 'REFUND_TO_BANK',
  },
  REFUND_TO_WALLET: {
    code: 'Refund_To_Wallet',
    header: 'Refund Complete',
    subHeader: `${numAppendedWithRuppeeSymbol(amount)} added to your wallet as My Cash`,
    content: 'You can use 100% of this amount without expiry date for any booking in trains, flights and hotels.',
    image: ASSETS.icRefundWallet,
    id: 'REFUND_TO_WALLET',
  },
  TIMEOUT: {
    code: '',
    header: 'Refund Initiated',
    subHeader: `${numAppendedWithRuppeeSymbol(amount)} will be added to your bank account`,
    content: 'This refund will be processed by MakeMyTrip in 7 business days. ' +
      'It could take 10-12 business days further to reflect in your bank account. ' +
      'We’ll keep you updated through SMS and email!',
    image: ASSETS.icTimeout,
    id: 'TIMEOUT',
  },
}));

export default retryObject;
