import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import React from 'react';
import url from 'url';
import {withRouterState} from '../../../../../../web/WebRouter';
import RetryBooking from './Components/RetryBooking';
import RailsImageCard from '../Common/RailsImageCard';

class RailsRetryBookingWeb extends React.Component {
  constructor(props) {
    super(props);
    const urlObj = url.parse(window.location.href, window.location.search);
    const {query = {}} = urlObj;
    this.state = {
      mmtId: query.mmtId,
      retryId: query.retryId,
    };
  }

  render() {
    return (
      <RetryBooking {...this.state} />
    );
  }
}

const RailsRetryBookingWebRoute = () => (
  <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
    <Switch>
      <Route exact path="/pwa/v2/railsRetryBooking" component={withRouterState(RailsRetryBookingWeb)} />
      <Route exact path="/pwa/v2/railsRetryBooking/railsRefund" component={withRouterState(RailsImageCard)} />
    </Switch>
  </View>
);

export default withRouter(RailsRetryBookingWebRoute);
