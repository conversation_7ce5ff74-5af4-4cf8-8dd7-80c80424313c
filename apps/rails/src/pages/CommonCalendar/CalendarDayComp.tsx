import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { NOT_AVAILABLE, CHECKOUT, formatDate, getDates } from './Utils/CalenderUtils';
import { styles } from './Styles/CalendarDayComp.styles';
import { hideAvailabilityMarkers } from '../../RailsAbConfig';
import { AvailabilityMarkers } from '../../RailsAbConstants';

export interface CalendarDayCompProps {
  date: string;
  dateStr: string;
  cellHeight: number;
  onselectionchange: (date: { dateStr: string }) => void;
  disabled: boolean;
  mode?: string;
  checkin: Date;
  checkout: Date;
  avlStatus: string;
}

function CalendarDayComp({
  date,
  dateStr,
  cellHeight,
  onselectionchange,
  disabled,
  mode,
  checkin,
  checkout,
  avlStatus,
}: CalendarDayCompProps) {
  const selected = formatDate(checkin);
  const nextSelected = formatDate(checkout);
  const getDiffDate = getDates(selected, nextSelected);
  const isDate = getDiffDate.indexOf(dateStr) !== -1;
  const wrapper = [
    styles.calDateViewWrap,
    { height: cellHeight },
    ...(dateStr === '' ? [styles.greyBox] : []),
  ];
  const dateViewWrapper = [styles.calDateView, { height: cellHeight }];
  const calDateWrapper = [styles.calDate];
  const naTxtStyles = [styles.naTxt];
  switch (true) {
    case selected === dateStr: {
      const wr = mode === CHECKOUT ? {} : {};
      wrapper.push(wr);
      dateViewWrapper.push(styles.selDateView);
      dateViewWrapper.push({ height: cellHeight });
      calDateWrapper.push(styles.selDateColor);
      naTxtStyles.push(styles.naTxtActv);
      break;
    }
    case (mode !== CHECKOUT && nextSelected === dateStr): {
      wrapper.push(styles.dateRangeEnd);
      dateViewWrapper.push(styles.selDateView);
      dateViewWrapper.push({ height: cellHeight });
      calDateWrapper.push(styles.selDateColor);
      break;
    } case isDate: {
      wrapper.push(styles.dateRange);
      break;
    }
    case disabled: {
      calDateWrapper.push(styles.prevDate);
      break;
    }
  }
  const avlDot = [styles.avlDotStyles, (avlStatus && date ? { backgroundColor: avlStatus } : {})];
  const showAvailabilityMarkers = ( hideAvailabilityMarkers() === AvailabilityMarkers.SHOWN);
  return (
    <TouchableOpacity
      activeOpacity={disabled ? 1 : 0.5}
      onPress={() => {
        if (!disabled) {
          onselectionchange({ dateStr });
        }
      }}
      style={wrapper}
    >
      <View style={dateViewWrapper}>
        <Text style={calDateWrapper}>{date}</Text>
        {showAvailabilityMarkers && (
          <>
            {!(avlStatus?.length > 0) && date && !disabled && <Text style={naTxtStyles}>--</Text>}
            {avlStatus !== NOT_AVAILABLE && date && <View style={avlDot} />}
          </>
        )}
      </View>
    </TouchableOpacity>
  );
}

export default CalendarDayComp;
