import React from 'react';
import { View, Text } from 'react-native';
import { getCalendarDayNamesFromMonday } from '../../vernacular/calendarUtil';
import { styles } from './Styles/CalendarWeekHeader.styles';

function CalendarWeekHeader() {
  const weekHeader = getCalendarDayNamesFromMonday();

  return (
    <View style={styles.calHead}>
      {(weekHeader.map((item: string, index: number) => (
        <View key={`weekday-${index}`} style={styles.calHeadCell}>
          <Text style={styles.weekname}>{item}</Text>
        </View>
      ))) || []}
    </View>
  );
}

export default CalendarWeekHeader;
