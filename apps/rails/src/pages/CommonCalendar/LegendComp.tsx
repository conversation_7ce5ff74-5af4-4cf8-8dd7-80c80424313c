import React from 'react';
import { View, Text } from 'react-native';
import { styles } from './Styles/LegendComp.styles';
import { getAvailabilityIntensities } from '../../vernacular/calendarUtil';

function LegendComp () {
    const availabilityIntensities = getAvailabilityIntensities();

    return (
    <View style={styles.avlLgndWrp}>
        <View style={styles.lgndItm}>
            <View style={[styles.lgndDot,styles.lowAvlDot]}/>
            <Text style={styles.lgndTxt}>{availabilityIntensities.low}</Text>
        </View>
        <View style={styles.lgndItm}>
            <View style={[styles.lgndDot,styles.medAvlDot]}/>
            <Text style={styles.lgndTxt}>{availabilityIntensities.medium}</Text>
        </View>
        <View style={styles.lgndItm}>
            <View style={[styles.lgndDot,styles.highAvlDot]}/>
            <Text style={styles.lgndTxt}>{availabilityIntensities.high}</Text>
        </View>
    </View>
    );
}

export default LegendComp;
