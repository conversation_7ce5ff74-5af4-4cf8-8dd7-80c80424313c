import { StyleSheet } from 'react-native';
import { width } from '../Utils/CalenderUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
    calHead: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: colors.white,
      paddingVertical: 9,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    calHeadCell: {
      width: width / 7,
    },
    weekname: {
      fontSize: 10,
      color: colors.gradientLightGrey,
      textAlign: 'center',
      fontWeight: '700',
    },
  });
