import { StyleSheet } from 'react-native';
import { width, sf } from '../Utils/CalenderUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
    calDateViewWrap: {
      width: (width  - 7) / 7,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.grey22,
      flex: 1,
      borderRightWidth: 1,
      borderBottomWidth: 1,
      borderColor: colors.grey11,
    },
    greyBox: {
      backgroundColor: colors.grey11,
    },
    calDateView: {
      width: width / 7,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
    },
    selDateView: {
      backgroundColor: colors.azure,
      width: width / 7,
      borderRadius: 4,
    },
    dateRange: {
      backgroundColor: colors.azureBlue8,
    },
    dateRangeStart: {
      backgroundColor: colors.azureBlue7,
      borderTopLeftRadius: sf(100),
      borderBottomLeftRadius: sf(100),
    },
    dateRangeEnd: {
      backgroundColor: colors.azureBlue7,
      borderTopRightRadius: sf(100),
      borderBottomRightRadius: sf(100),
    },
    calDate: {
      textAlign: 'center',
      color: colors.blackRussian,
      fontSize: 12,
    },
    prevDate: {
      color: colors.gradientLightGrey,
    },
    holidayName: {
      fontSize: 8,
      color: colors.gradientLightGrey,
      position: 'absolute',
      top: 2,
    },
    holidayNameActv: {
        color: colors.grey22,
    },
    selDateColor: {
      color: colors.white,
      fontWeight: 'bold',
    },
    avlDotStyles: {
      width: 4,
      height: 4,
      borderRadius: 4,
      position: 'absolute',
      bottom: 10,
      overflow: 'hidden',
    },
    avlDotStylesHigh: {
        backgroundColor: colors.lightGreen23,
    },
    avlDotStylesMed: {
        backgroundColor: colors.goldenYellow16,
    },
    avlDotStylesLow: {
        backgroundColor: colors.red24,
    },
    naTxt: {
        color: colors.lighterBlue2,
        position: 'absolute',
        bottom: 4,
    },
    naTxtActv: {
      color: colors.white,
    },
    caldendarContentWrap: {
      backgroundColor: colors.white,
    },
    weeknameColor: {
      fontSize: 10,
      color: colors.gradientLightGrey,
      textAlign: 'center',
      fontWeight: '700',
    },
});
