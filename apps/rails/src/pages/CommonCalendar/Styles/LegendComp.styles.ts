import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
    avlLgndWrp: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 8,
    },
    lgndItm: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    lgndDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginRight: 4,
    },
    lowAvlDot: {
        backgroundColor: colors.red24,
    },
    medAvlDot: {
        backgroundColor: colors.goldenYellow16,
    },
    highAvlDot: {
        backgroundColor: colors.lightGreen23,
    },
    lgndTxt: {
        fontSize: 11,
        color: colors.blackRussian,
    },
});
