import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import CalendarPicker from '@Frontend_Ui_Lib_App/CalendarPicker';
import fecha from 'fecha';
import LegendComp from './LegendComp';
import CalendarWeekHeader from './CalendarWeekHeader';
import CalendarDayComp, { CalendarDayCompProps } from './CalendarDayComp';
import {
  AVAILABILITY_COLORS,
  DATE_FORMAT,
  DAYS_COUNT,
  getMonths,
  getFromToDate,
  API_POST_METHOD,
  width,
} from './Utils/CalenderUtils';
import fetch2 from '../../fetch2';
import { hideAvailabilityMarkers } from '../../RailsAbConfig';
import { AvailabilityMarkers } from '../../RailsAbConstants';


interface AvailabilityDataProps {
  availabilityByDate: Record<string, unknown>;
}

interface CommonCalendarProps {
  source: string;
  destination: string;
  departureDate: Date;
  onDateClick: (date: Date) => void;
  arpDays?: number;
}

function CommonCalendar({
  source,
  destination,
  departureDate,
  onDateClick,
  arpDays,
}: CommonCalendarProps) {

  const [calendarData, setCalendarData] = useState({
    availability: {},
  });

  useEffect(() => {
    const showAvailabilityMarkers = hideAvailabilityMarkers() === AvailabilityMarkers.SHOWN;
    if (showAvailabilityMarkers && source && destination) {
      const { fromDate, toDate } = getFromToDate(arpDays ?? DAYS_COUNT);
      const url = AVAILABILITY_COLORS.url;
      const body = {
        source: source,
        destination: destination,
        from_date: fromDate,
        to_date: toDate,
      };
      fetch2(url, {
        method: API_POST_METHOD,
        headers: AVAILABILITY_COLORS.headers,
        body: JSON.stringify(body),
      }).then(res => res.json())
        .then(data => updateCalendar(data))
        .catch(err => console.error(err.message));
    }
  }, [source, destination]);

  const updateCalendar = (data: AvailabilityDataProps) => {
    const availability = data.availabilityByDate || {};
    setCalendarData({
      ...calendarData,
      availability,
    });
  };

  const { toDateObj, fromDateObj } = getFromToDate(arpDays ?? DAYS_COUNT);
  const CELL_HEIGHT = width / 7;
  const showAvailabilityMarkers = hideAvailabilityMarkers() === AvailabilityMarkers.SHOWN;

  return (
    <View style={{ flex: 1 }}>
      <CalendarPicker
        value={getMonths(DAYS_COUNT)}
        checkInDate={departureDate}
        onlyCheckin={true}
        onDateSelection={(selectedDate: Date) => onDateClick(selectedDate)}
        renderWeekHeader={() => <CalendarWeekHeader />}
        renderLegendItem={() => showAvailabilityMarkers && <LegendComp />}
        cellHeight={CELL_HEIGHT}
        renderDayItem={({
          date,
          dateStr,
          onselectionchange,
          checkin,
          checkout,
          dateInt,
        }: CalendarDayCompProps) => (
          <CalendarDayComp
            key={date}
            date={date}
            dateStr={dateStr}
            cellHeight={CELL_HEIGHT}
            disabled={(() => {
              if (date) {
                const currentDate = fecha.parse(dateStr, DATE_FORMAT).setHours(0, 0, 0, 0);
                return !(currentDate >= fromDateObj && currentDate <= toDateObj);
              }
              return true;
            })()}
            onselectionchange={onselectionchange}
            checkin={checkin}
            checkout={checkout}
            avlStatus={calendarData.availability[dateInt]}
          />
        )}
      />
    </View>
  );
}

export default CommonCalendar;
