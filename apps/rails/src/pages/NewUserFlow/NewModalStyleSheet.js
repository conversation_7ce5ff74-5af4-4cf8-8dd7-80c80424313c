import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from '../../vernacular/VernacularUtils';

const commonStyles = StyleSheet.create({
  justifyBetween: {
    justifyContent: 'space-between',
  },
});

const NewIrctcModalStyle = StyleSheet.create({
  container: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: colors.white,
    overflow: 'hidden',
  },
  modalBackground: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: colors.modalgrey,
  },
  headerContainer: {
    ...commonStyles.justifyBetween,
    flexDirection: 'row',
    marginRight: 20,
    marginLeft: 20,
    marginTop: 20,
  },
  textBoxContainer: {
    backgroundColor: colors.creamWhite,
    borderWidth: 1,
    marginTop: 16,
    marginLeft: 20,
    marginRight: 20,
    borderRadius: 10,
    borderColor: colors.creamWhite,
    height: 'auto',
    width: 'auto',
  },
  textBoxContainer1: {
    backgroundColor: colors.creamWhite,
    borderWidth: 1,
    marginTop: 8,
    marginLeft: 16,
    marginRight: 16,
    borderRadius: 10,
    borderColor: colors.creamWhite,
    height: 'auto',
    width: 'auto',
  },
  textBoxText: {
    paddingLeft: 12,
    paddingRight: 12,
    paddingTop: 8,
    paddingBottom: 8,
    color: colors.textGrey,
    fontWeight: '400',
    width: 'auto',
    height: 'auto',
    flexShrink: 1,
    lineHeight: 16.8,
  },
  textInputContainer: {
    marginTop: 16,
    marginLeft: 20,
    marginRight: 20,
    backgroundColor: colors.lighterBlue,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.primary,
    paddingHorizontal: 12,
    paddingTop: 5.5,
    paddingBottom: 3,
  },
  textInputHeader: {
    fontSize: 12,
    color: colors.primary,
    marginBottom: 5,
  },
  textInputStyle: {
    fontSize: 16,
    padding: 0,
  },
  text: {
    fontSize: 12,
    color: colors.azure,
  },
  greyText: {
    fontSize: 12,
    color: colors.greyText1,
  },
  additionalTextIcon: {
    width: 12,
    height: 12,
    alignSelf: 'center',
  },
  additionalTextContainer: {
    marginTop: 8,
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 4,
  },
  footerContainer: {
    ...commonStyles.justifyBetween,
    flexDirection: 'row',
    marginBottom: 20,
    marginTop: 4,
    color: colors.primary,
    marginHorizontal: 20,
    height: 32,
  },
  footerContainer1: {
    ...commonStyles.justifyBetween,
    flexDirection: 'row',
    marginTop: 4,
    color: colors.primary,
    marginHorizontal: 20,
    height: 32,
  },
  buttonContainer: {
    ...commonStyles.justifyBetween,
    flexDirection: 'column',
    paddingHorizontal: 16,
    marginBottom: 10,
    marginTop: -25,
    borderRadius: 10,
  },
  buttonContainer1: {
    ...commonStyles.justifyBetween,
    flexDirection: 'column',
    paddingHorizontal: 16,
    marginBottom: 42,
    borderRadius: 10,
  },
  buttonContainerDisabled: {
    ...commonStyles.justifyBetween,
    Color: colors.lightGrey17,
    paddingHorizontal: 16,
    marginBottom: 42,
  },
  crossIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  text1: {
    fontSize: 12,
    color: colors.greyText1,
    fontWeight: '400',
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontWeight: '400',
  },
  successText: {
    fontSize: 12,
    color: colors.lightGreen16,
    fontWeight: '400',
  },
  closeButtonText: {
    color: colors.primary,
    fontStyle: 'Lato',
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: colors.white,
    paddingVertical: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  closeButton1: {
    backgroundColor: colors.blue,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    width: '90%',
  },
  gradientText: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 5,
  },
  closeButtonText1: {
    color: colors.white,
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  buttonContainer2: {
    marginBottom: 25,
    alignItems: 'center',
  },
  forgotPasswordContainer: {
    alignItems: 'center',
    marginVertical: 8,
    marginHorizontal: 16,
    flexDirection: 'row',
    flexGrow: 1,
    justifyContent: 'center',
  },
  forgotPasswordText: {
    color: colors.textGrey,
    paddingRight: 8,
    paddingVertical: 7.5,
  },
  getNewPasswordText: {
    color: colors.primary,
    paddingTop: 9,
    paddingBottom: 9,
  },
  checkboxContainer: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 8,
    marginHorizontal: 27,
  },
  checkBoxIconContainer: {
    height: 24,
    width: 24,
  },
  title: {
    paddingRight: 11,
    marginRight: 16,
    fontSize: 14,
    paddingVertical: 7.5,
    fontFamily: 'lato',
    marginLeft: 10,
    fontWeight: '400',
    color: colors.textGrey,
  },
  footerContainer2: {
    marginTop: 16,
    padding: 12,
    marginHorizontal: 16,
    borderRadius: 12,
    backgroundColor: colors.grayBg,
    flexDirection: 'row',
    gap: 6,
    marginBottom: 40,
  },
  footerText: {
    fontWeight: '500',
    color: colors.textGrey,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  footerUpdateIcon: {
    width: 16,
    height: 16,
    alignSelf: 'center',
  },
  profileCompletionWrapper: {
    marginTop: 10,
    marginHorizontal: 16,
    backgroundColor: colors.lightRed4,
    borderRadius: 16,
    position: 'relative',
  },
  profileCompletionContainer: {
    padding: 12,
    borderRadius: 12,
  },
  profileCompletionTitle: {
    ...(fontStyle('bold')),
    fontWeight: '700',
    ...(getLineHeight(14)),
    color: colors.textGrey,
  },
  arrowUp: {
    left: 13,
    top: -8,
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderBottomWidth: 15,
    borderRightWidth: 13,
    borderTopWidth: 0,
    borderLeftWidth: 13,
    borderBottomColor: colors.lightRed4,
    borderRightColor: colors.transparent,
    borderTopColor: colors.transparent,
    borderLeftColor: colors.transparent,
    position: 'absolute',
  },
  redTickGradient: {
    height: 28,
    width: 28,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.lightRed5,
    borderRadius: 14,
  },
  redTickIcon: {
    width: 28,
    height: 28,
  },
  profileCompletionHeader: {
    position: 'relative',
    marginTop: 25,
  },
  profileCompletionStepsTitle: {
    marginHorizontal: 30,
    borderRadius: 18,
    position: 'absolute',
    alignSelf: 'center',
    top: -10,
    zIndex: 9,
    paddingHorizontal: 16,
    paddingVertical: 3,
    borderColor: colors.lightSilver,
    borderWidth: 0.5,
  },
  firstProfileStepContainer: {
    borderTopWidth: 0,
    marginTop: 10,
  },
  profileCompletionText: {
    ...(fontStyle('regular')),
    fontWeight: '600',
    ...(getLineHeight(12)),
    color: colors.textGrey,
    flexWrap: 'wrap',
    flexShrink: 1,
    letterSpacing: 1.2,
  },
  profileStepWrapper: {
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 0.5,
    borderColor: colors.lightSilver,
  },
  profileStepContainer: {
    alignItems: 'center',
    paddingLeft: 16,
    flexDirection: 'row',
    borderColor: colors.lightSilver,
    height: 66,
    borderTopWidth: 0.5,
    gap: 8,
    backgroundColor: colors.white,
  },
  profileStepText: {
    ...(fontStyle('bold')),
    fontWeight: '700',
    ...(getLineHeight(14)),
    color: colors.black,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  paddingHorizontal0: {
    paddingHorizontal: 0,
  },
  marginHorizontal0: {
    marginHorizontal: 0,
  },
});

export default NewIrctcModalStyle;
