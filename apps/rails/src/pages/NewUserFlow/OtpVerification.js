import PropTypes from 'prop-types';
import React from 'react';
import { Image, ScrollView, Text, View, StyleSheet } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import isEmpty from 'lodash/isEmpty';
import { connect } from 'react-redux';
import { Actions } from '../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {KeyboardAvoidingViewWrapper} from '../CreateNewAccount/CreateNewAccount';
import SimpleHeaderForPopup from '@mmt/legacy-commons/Common/Components/Header/SimpleHeaderForPopup';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {
  getOTP,
  STATUS_API_ERROR,
  STATUS_API_SUCCESS,
  STATUS_EMAIL_OTP_VERIFICATION_PENDING,
  STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING,
  STATUS_MOBILE_OTP_VERIFICATION_PENDING,
  verifyOTP,
} from '../../Utils/UserRepository';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {updateUsername} from '../User/UserVerification/UserVerificationActions';
import {_label} from '../../vernacular/AppLanguage';
import {checkEnglishKeyboard, fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { trackVerifyContactDetailsPageLoad } from '../../railsAnalytics';
import ASSETS from '../../Utils/Assets/RailsAssets';

import InputField from '@Frontend_Ui_Lib_App/InputField';

const NextButton = ({onClick, loading}) => (
  <TouchableRipple onPress={onClick}>
    <View>
      <LinearGradient
        style={nextButtonStyles.container}
        colors={['#53B2FE', '#065AF3']}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
      >
        <View>
          {
          loading &&
          <Spinner size={20} color={colors.white} />
        }
          {
          !loading &&
          <Text style={nextButtonStyles.nextText}>{_label('next',{uppercase : true})}
          </Text>
        }
        </View>
      </LinearGradient>
    </View>
  </TouchableRipple>
);

NextButton.propTypes = {
  loading: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
};

const nextButtonStyles = StyleSheet.create({
  container: {
    borderRadius: 8,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    backgroundColor: colors.transparent,
  },
});

const OtpVerificationHeader = props => (
  <View style={{flex: 1}}>
    <SimpleHeaderForPopup
      size = "normal"
      title = {_label('verify_contact_details',{capitalize : true})}
      backImage = "true"
      onPressCloseHandler = {() => { Actions.pop(props.from); }}
    />
    {
      props.apiError &&
      <View style={{backgroundColor: colors.lightPink, padding: 10}}>
        <Text style={[{color: colors.red, fontSize: 14}, fontStyle('bold'), getLineHeight(14)]}>{props.apiError}</Text>
      </View>
    }
    <KeyboardAvoidingViewWrapper>
      <ScrollView
        contentContainerStyle = {{flexGrow: 1, justifyContent: 'space-between'}}
        style = {{marginHorizontal: 20, marginTop: 12}}
        keyboardShouldPersistTaps = "handled"
        showsVerticalScrollIndicator = {false}
      >
        {props.children}
      </ScrollView>
    </KeyboardAvoidingViewWrapper>
  </View>
);

OtpVerificationHeader.propTypes = {
  children: PropTypes.node.isRequired,
  from: PropTypes.string.isRequired,
  apiError: PropTypes.string,
};

class OtpVerification extends React.Component {
  constructor(props) {
    super(props);
    if (!this.props.from) {
      Actions.pop();
    }
  }
  state = {
    emailOtp: '',
    mobileOtp: '',
    emailOtpError: null,
    mobileOtpError: null,
    success: false,
    loading: false,
    apiError: null,
  };

  validateGetOtpResponse = (response) => {
    let apiError = null;
    if (response) {
      if (response.status === STATUS_API_ERROR) {
        apiError = response.message;
      } else if (response.status === STATUS_API_SUCCESS) {
        return true;
      }
    } else {
      apiError = _label('something_went_wrong');
    }
    this.setState({apiError});
    return false;
  };

  componentDidMount() {
    trackVerifyContactDetailsPageLoad('mob_rail_traveler_irctc_verify_contact_detail');
  }

  async UNSAFE_componentWillMount() {
    const response = await getOTP(this.props.userName, this.props.otpType);
    if (this.validateGetOtpResponse(response, this.props.verify)) {
      showShortToast(_label('otp_sent'));
    }
  }

  _resendEmailOtp = async () => {
    this.setState({emailOtpError: null, apiError: null});
    const response = await getOTP(this.props.userName, 'E');
    if (this.validateGetOtpResponse(response, STATUS_EMAIL_OTP_VERIFICATION_PENDING)){
      showShortToast(_label('otp_sent'));
    }
  };

  _resendMobileOtp = async () => {
    this.setState({mobileOtpError: null, apiError: null});
    const response = await getOTP(this.props.userName, 'M');
    if (this.validateGetOtpResponse(response, STATUS_MOBILE_OTP_VERIFICATION_PENDING)) {
      showShortToast(_label('otp_sent'));
    }
  };

  _onVerifyOtpClick = async () => {
    const {emailOtp, mobileOtp} = this.state;
    let emailError = null;
    let mobileError = null;
    if (this.state.verify === STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING) {
      if (isEmpty(emailOtp)) {
        emailError = _label('please_enter_email_otp');
      }
      if (isEmpty(mobileOtp)) {
        mobileError = _label('please_enter_mobile_otp');
      }
    } else if (this.state.verify === STATUS_EMAIL_OTP_VERIFICATION_PENDING) {
      if (isEmpty(emailOtp)) {
        emailError = _label('please_enter_email_otp');
      }
    } else if (this.state.verify === STATUS_MOBILE_OTP_VERIFICATION_PENDING) {
      if (isEmpty(mobileOtp)) {
        mobileError = _label('please_enter_mobile_otp');
      }
    }
    if (emailError) {
      this.setState({emailOtpError: emailError});
    } else if (mobileError) {
      this.setState({mobileOtpError: mobileError});
    } else {
      this.setState({emailOtpError: null, mobileOtpError: null, loading: true, apiError: null});
      const response = await verifyOTP(
        this.props.userName,
        this.props.otpType,
        this.state.emailOtp,
        this.state.mobileOtp);
      if (this.validateGetOtpResponse(response, this.props.verify)) {
        this.setState({success: true});
        setTimeout(() => {
          if (this.props.probableTo) {
            Actions[this.props.probableTo]({type: 'replace', ...this.props.nextPageProps});
          } else {
            Actions.pop();
          }
        }, 1000);
        this.props.setUserNameToRedux(this.props.userName);
      }
      this.setState({loading: false});
    }
  };

  render() {
    let otpRender = null;
    if (this.state.success) {
      return (
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
          <Image
            source={ASSETS.icFpTick}
            style={{height: 124, width: 124}}
          />
        </View>
      );
    }

    const emailRender = (
      <View>
        <InputField
          label={_label('enter_email_otp', { capitalize: true })}
          placeholder={_label('sent_on_email')}
          inputProps={{
            returnKeyType: 'done',
            autoFocus: this.props.focus === 'email',
            autoCorrect: false,
            keyboardType: 'numeric',
          }}
          value={this.state.emailOtp}
          onChangeText={(text) => {
            this.setState({ emailOtp: text });
            checkEnglishKeyboard('emailOtpError', text, this);
          }}
          isError={!!this.state.emailOtpError}
          errorMessage={this.state.emailOtpError}
          customStyle={{
            wrapperStyle: inputFieldStyles.inputWrapperStyle,
            inputFieldStyle: [
              inputFieldStyles.inputFieldStyle,
              fontStyle('bold'),
              getLineHeight(16),
            ],
            labelStyle: this.state.emailOtpError
              ? { ...inputFieldStyles.labelStyle, ...inputFieldStyles.labelErrorStyle }
              : inputFieldStyles.labelStyle,
          }}
        />

        <TouchableRipple onPress={this._resendEmailOtp}>
          <View>
            <Text
              style={[{ fontSize: 14, color: colors.azure }, fontStyle('bold'), getLineHeight(14)]}
            >
              {_label('resend_email_otp')}
            </Text>
          </View>
        </TouchableRipple>
      </View>
    );
    const mobileRender = (
      <View>
        <InputField
          label={_label('enter_mobile_otp', { capitalize: true })}
          placeholder={_label('sent_on_registered_number')}
          inputProps={{
            returnKeyType: 'done',
            autoFocus: this.props.focus === 'mobile',
            autoCorrect: false,
            keyboardType: 'numeric',
          }}
          value={this.state.mobileOtp}
          onChangeText={(text) => {
            this.setState({ mobileOtp: text });
            checkEnglishKeyboard('mobileOtpError', text, this);
          }}
          isError={!!this.state.mobileOtpError}
          errorMessage={this.state.mobileOtpError}
          customStyle={{
            wrapperStyle: inputFieldStyles.inputWrapperStyle,
            inputFieldStyle: [
              inputFieldStyles.inputFieldStyle,
              fontStyle('bold'),
              getLineHeight(16),
            ],
            labelStyle: this.state.mobileOtpError
              ? { ...inputFieldStyles.labelStyle, ...inputFieldStyles.labelErrorStyle }
              : inputFieldStyles.labelStyle,
          }}
        />
        <TouchableRipple onPress={this._resendMobileOtp}>
          <View>
            <Text style={[{fontSize: 14, color: colors.azure}, fontStyle('bold'), getLineHeight(14)]}
            >
              {_label('resend_mobile_otp')}
            </Text>
          </View>
        </TouchableRipple>
      </View>
    );
    if (this.props.verify === STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING) {
      otpRender = (
        <View>
          {emailRender}
          <View style={{marginTop: 40}} />
          {mobileRender}
        </View>
      );
    } else if (this.props.verify === STATUS_EMAIL_OTP_VERIFICATION_PENDING) {
      otpRender = (
        <View>
          {emailRender}
        </View>
      );
    } else if (this.props.verify === STATUS_MOBILE_OTP_VERIFICATION_PENDING) {
      otpRender = (
        <View>
          {mobileRender}
        </View>
      );
    }
    return (
      <OtpVerificationHeader from={this.props.from} apiError={this.state.apiError}>
        <View>
          {otpRender}
        </View>
        <View style={{
marginTop: 30, marginBottom: 20, width: '100%', justifyContent: 'flex-end',
}}
        >
          <NextButton
            onClick={this._onVerifyOtpClick}
            loading={this.state.loading}
          />
        </View>
      </OtpVerificationHeader>
    );
  }
}

const mapStateToProps = (state, ownProps) => ({
  ...ownProps,
});

const mapDispatchToProps = dispatch => ({
  setUserNameToRedux: (userName) => {
    dispatch(updateUsername(userName));
  },
});

const inputFieldStyles = StyleSheet.create({
  labelStyle: {
    fontSize: 14,
    fontFamily: fonts.bold,
  },
  inputFieldStyle: {
    fontFamily: fonts.bold,
  },
  labelErrorStyle: {
    color: colors.red,
  },
  inputWrapperStyle: {
    marginBottom: 20,
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(OtpVerification);

OtpVerification.propTypes = {
  focus: PropTypes.string.isRequired,
  from: PropTypes.string.isRequired,
  otpType: PropTypes.string.isRequired,
  probableTo: PropTypes.string,
  setUserNameToRedux: PropTypes.func.isRequired,
  userName: PropTypes.string.isRequired,
  verify: PropTypes.string.isRequired,
  nextPageProps: PropTypes.object,
};

OtpVerification.defaultProps = {
  probableTo: null,
};
