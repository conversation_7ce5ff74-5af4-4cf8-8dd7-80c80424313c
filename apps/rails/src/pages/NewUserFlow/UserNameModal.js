/* eslint-disable */
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import LinearGradient from 'react-native-linear-gradient';
import { connect } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import {
  IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS,
  REVIEW_PAGE_TRACKING_KEY,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  updateAccountUserNameAPI,
  IRCTC_USERNAME_CREATE_CLICKED_EVAR99,
  IRCTC_USERNAME_DONE_CLOSE_EVAR99,
  IRCTC_TATKAL_AADHAR_NOT_VERIFIED_ERROR_CODE,
} from '@mmt/rails/src/Utils/RailsConstant';
import CheckBox from 'react-native-checkbox';
import {
  Modal,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
  Keyboard,
  TextInput,
  TouchableOpacity,
  Image,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { Actions } from '../../navigation';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import fetch2 from '../../fetch2';
import railsConfig from '../../RailsConfig';
import {
  parseValidateUserResponse,
  STATUS_API_ERROR,
  STATUS_API_SUCCESS,
  STATUS_EMAIL_OTP_VERIFICATION_PENDING,
  STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING,
  STATUS_MOBILE_OTP_VERIFICATION_PENDING,
  STATUS_USER_PROFILE_INCOMPLETE,
} from '../../Utils/UserRepository';
import {
  updateUsername,
  validateUsernameField,
  updateConfigKeyResponseForAadharVerification,
  updateVerificationFlow,
} from '../User/UserVerification/UserVerificationActions';
import {
  trackEnterUserNameEvent,
  updateEvar47or97Variable,
  trackGenericEvar99Event,
  trackPageLoad,
  trackTravellerPageEvent,
  trackVerifyIrctcUsernameEvent,
} from '../../railsAnalytics';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import BtnGeneric from '../PostPayment/Components/BtnGeneric';
import {
  setShowIrctcProfileCompletionFlow,
  showHideIRCTCUsernameError,
  updateIrctcUserNames,
} from '../TravelerDetails/TravelerDetailsActions';
import { _label } from '../../vernacular/AppLanguage';
import { fontStyle, checkEnglishKeyboard, getLineHeight } from '../../vernacular/VernacularUtils';
import { isAndroid, isIos, isMweb } from '../../Utils/device';
import {
  getRailsIrctcAccountToast,
  showProfileIncompleteFlowPokus,
  showNewIrctcBottomSheet,
} from '../../RailsAbConfig';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { trackClickEventProp61 } from '../RailsBusHomePage/Analytics';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import RailRetrievePasswordContainer from '../Common/RailRetrievePasswordContainer';
import { configKeys } from '../../configStore/Common/constants';
import NewIrctcModalStyle from './NewModalStyleSheet';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';

const BorderCta = ({ text, onClick, id }) => (
  <TouchableRipple onPress={onClick}>
    <View style={borderCtaStyles.container} testID={id}>
      <Text style={[borderCtaStyles.text, fontStyle('bold'), getLineHeight(12)]}>{text}</Text>
    </View>
  </TouchableRipple>
);

BorderCta.propTypes = {
  onClick: PropTypes.func.isRequired,
  text: PropTypes.string.isRequired,
  id: PropTypes.string,
};

const borderCtaStyles = StyleSheet.create({
  container: {
    borderRadius: 30,
    borderWidth: 1,
    borderColor: colors.azure,
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 12,
    color: colors.azure,
    borderRadius: 30,
  },
});

const EnableButtonCta = ({ text, onClick, loading, id }) => (
  <TouchableRipple onPress={onClick} testID={id}>
    <View>
      <LinearGradient
        style={enableButtonStyle.container}
        colors={['#53b2fe', '#065af3']}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
      >
        {loading && <Spinner size={20} color={colors.white} />}
        {!loading && (
          <Text style={[enableButtonStyle.next, fontStyle('black'), getLineHeight(12)]}>
            {text}
          </Text>
        )}
      </LinearGradient>
    </View>
  </TouchableRipple>
);

EnableButtonCta.propTypes = {
  loading: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  text: PropTypes.string.isRequired,
  id: PropTypes.string,
};

const enableButtonStyle = StyleSheet.create({
  container: {
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 28,
    paddingVertical: 10,
  },
  next: {
    fontSize: 12,
    color: colors.white,
    backgroundColor: colors.transparent,
    marginHorizontal: 7,
    marginVertical: 2,
  },
});

const newModalStyle = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    paddingTop: 15,
  },
  header: {
    fontSize: 20,
    color: colors.black,
  },
  textBoxContainer: {
    backgroundColor: colors.creamWhite,
    marginVertical: 10,
    borderRadius: 2,
    paddingVertical: 5,
    paddingHorizontal: 8,
  },
  yellowText: {
    fontSize: 10,
    color: colors.lightYello,
  },
  textInputContainer: {
    backgroundColor: colors.grey13,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: colors.grey12,
    paddingVertical: 8,
    paddingLeft: 16,
  },
  textInputHeader: {
    fontSize: 12,
    color: colors.lightTextColor,
    lineHeight: 20,
    marginBottom: 5,
  },
  textInputStyle: {
    fontSize: 18,
    padding: 0,
    marginVertical: 0,
  },
  errorTextStyle: {
    color: colors.red,
    fontSize: 12,
  },
  text: { fontSize: 12, color: colors.azure },
});

class UserNameModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      userName: '',
      error: null,
      loading: false,
      successMessage: '',
      modalVisible: true,
      showPasswordReminderModal: false,
      isChecked: false,
      passwordModalConfig: {},
      showProfileIncompleteFlow: false,
      irctcProfileIncompleteFlowConfig: {},
    };
  }
  async componentDidMount() {
    trackPageLoad(RAIL_EVENTS.USERFLOW.mob_rail_listing_irctc_userid_popup_RN);
    const irctcProfileIncompleteFlowConfig =
      (await getConfigStore(configKeys.RAILS_IRCTC_PROFILE_INCOMPLETE_FLOW_CONFIG)) || {};

    const passwordModalConfig = await getConfigStore(configKeys.RAILS_TRAVELLER_IRCTC_BS_CONFIG);
    this.setState({
      passwordModalConfig,
      irctcProfileIncompleteFlowConfig,
    });

    if (this.props.userName) {
      this.setState({ userName: this.props.userName });
    }
    setTimeout(() => {
      if (isAndroid() && this.inputRef) {
        this.inputRef.focus();
      }
    }, 100);
  }

  _getErrorMessage = (msg) => {
    if (msg && typeof msg === 'string') {
      const lbl = msg.toLowerCase().replace(/ /g, '_');
      const vernacLabel = _label(lbl);
      return vernacLabel && vernacLabel !== lbl ? vernacLabel : msg;
    }
    return msg;
  };

  _onCheckBoxClick = () => {
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLERS_IRCTC_USERNAME_CHECKBOX,
    );
    this.setState((prevState) => ({
      isChecked: !prevState.isChecked,
    }));
  };

  _onCreateNewPress = () => {
    trackEnterUserNameEvent(
      RAIL_EVENTS.USERFLOW.mob_rail_listing_irctc_userid_popup_RN_create_account,
    );
    trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERNAME_CREATE_CLICKED_EVAR99);

    const showIrctcAccountCreatingToast = getRailsIrctcAccountToast();

    if (showIrctcAccountCreatingToast) {
      return Actions.openCreateIRCTCAccountWebView({
        from: 'travelers',
      });
    }

    Actions.railsCreateAccountPage({
      from: 'travelers',
    });
  };

  _onVerifyClick = async () => {
    this.setState({ error: null });
    let error = null;
    let success = false;
    const { userName: irctcUserName } = this.state;
    const userName = irctcUserName.trim();
    trackVerifyIrctcUsernameEvent(
      RAIL_EVENTS.USERFLOW.mob_rail_travellers_irctc_userid_verify_username_verify_clicked,
    );
    if (isEmpty(userName)) {
      this.setState({ error: _label('enter_irctc_username_error') });
    }
    trackVerifyIrctcUsernameEvent(
      RAIL_EVENTS.USERFLOW.mob_rail_travellers_irctc_userid_verify_username_verify_clicked,
    );
    if (userName === '') {
      this.setState({ error: _label('enter_irctc_username_error') });
      return;
    }
    if (!validateUsernameField(userName)) {
      this.setState({ error: _label('alphanumeric_error') });
      return;
    }
    this.setState({ error: null, loading: true });
    try {
      const res = await fetch2(railsConfig.validateUserId, {
        method: 'POST',
        headers: {
          quota: this.props.quota,
        },
        body: JSON.stringify({ userName }),
      });
      if (res.status !== 200) {
        error = _label('something_went_wrong');
      } else {
        const response = await res.json();
        const userIdStatusObject = parseValidateUserResponse(response);
        if (
          userIdStatusObject.status === STATUS_API_SUCCESS ||
          userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE
        ) {
          this.props.showHideIRCTCUsernameError(false);
          this.props?.setShowIrctcProfileCompletionFlow?.(
            userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE,
          );
          this.setState({
            successMessage: _label('success_message'),
            error: null,
            loading: false,
            ...(showProfileIncompleteFlowPokus() !== 0 && {
              showProfileIncompleteFlow:
                userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE,
            }),
          });
          if (userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE) {
            updateEvar47or97Variable(
              IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_PROFILE_INCOMPLETE_STATUS_EVAR99,
            );
          } else {
            updateEvar47or97Variable(
              IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_PROFILE_COMPLETE_STATUS_EVAR99,
            );
          }
          this.props.updateVerificationFlow(response?.verificationFlow ?? {});
          error = null;
          success = true;
        } else if (userIdStatusObject.status === STATUS_API_ERROR) {
          error = this._getErrorMessage(userIdStatusObject.message);
        } else if (userIdStatusObject.status === STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING) {
          this.props.showHideIRCTCUsernameError(false);
          Keyboard.dismiss();
          setTimeout(() => {
            Actions.railsOtpVerification({
              verify: STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING,
              focus: 'email',
              userName,
              otpType: 'B',
              from: this.props.from,
              probableTo: this.props.probableTo,
              nextPageProps: this.props.nextPageProps,
            });
          }, 200);
        } else if (userIdStatusObject.status === STATUS_EMAIL_OTP_VERIFICATION_PENDING) {
          this.props.showHideIRCTCUsernameError(false);
          Keyboard.dismiss();
          setTimeout(() => {
            Actions.railsOtpVerification({
              verify: STATUS_EMAIL_OTP_VERIFICATION_PENDING,
              focus: 'email',
              userName,
              otpType: 'E',
              from: this.props.from,
              probableTo: this.props.probableTo,
              nextPageProps: this.props.nextPageProps,
            });
          }, 200);
        } else if (userIdStatusObject.status === STATUS_MOBILE_OTP_VERIFICATION_PENDING) {
          this.props.showHideIRCTCUsernameError(false);
          Keyboard.dismiss();
          setTimeout(() => {
            Actions.railsOtpVerification({
              verify: STATUS_MOBILE_OTP_VERIFICATION_PENDING,
              focus: 'mobile',
              userName,
              otpType: 'M',
              from: this.props.from,
              probableTo: this.props.probableTo,
              nextPageProps: this.props.nextPageProps,
            });
          }, 200);
        } else {
          error = _label('something_went_wrong');
        }
      }
    } catch (e) {
      error = _label('something_went_wrong') + ' Error Code: ' + e?.errorCode;
    }
    if (error) {
      this.setState({
        loading: false,
        error,
      });
    } else {
      this.setState({
        loading: false,
        error: null,
      });
      this.props.setModalVisibility(false);
      if (success) {
        this.props.setUserNameToRedux(this.state.userName);
        if (this.props?.successCallback) {
          this.props?.successCallback?.();
        }
        if (!this.props.setShowIrctcProfileCompletionFlow) {
          updateAccountUserNameAPI(this.state.userName);
        }
        if (this.props.probableTo) {
          if (this.props.replace) {
            Actions[this.props.probableTo]({ type: 'replace', ...this.props.nextPageProps });
          } else {
            Actions[this.props.probableTo]({ ...this.props.nextPageProps });
          }
        }
      }
    }
  };

  _showPasswordReminderModal = () => {
    this.setState({ showPasswordReminderModal: true });
  };

  _hidePasswordReminderModal = () => {
    this.setState({ showPasswordReminderModal: false });
  };

  _closeModalNew = (e) => {
    if (isMweb()) {
      e.preventDefault();
    }
    this.props.setModalVisibility(false);
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLERS_IRCTC_USERNAME_CLOSE,
    );
  };

  _handleUsernameVerification = async () => {
    this.setState({ error: null, checkingMessage: _label('irctc_validating_text') });
    let error = null;
    let success = false;
    const { userName: irctcUserName } = this.state;
    const userName = irctcUserName.trim();

    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLERS_IRCTC_USERNAME_CHECK,
    );

    if (isEmpty(userName)) {
      this.setState({ error: _label('enter_irctc_username_error'), checkingMessage: null });
      return;
    }
    if (userName === '') {
      this.setState({ error: _label('enter_irctc_username_error') });
      return;
    }
    if (!validateUsernameField(userName)) {
      this.setState({ error: _label('alphanumeric_error') });
      return;
    }
    this.setState({ error: null, loading: true });
    try {
      const res = await fetch2(railsConfig.validateUserId, {
        method: 'POST',
        headers: {
          quota: this.props.quota,
        },
        body: JSON.stringify({ userName }),
      });
      if (res.status !== 200) {
        error = _label('something_went_wrong');
      } else {
        const response = await res.json();
        if (response?.verificationFlow?.configKey) {
          const configKeyResponse =
            (await getConfigStore(response?.verificationFlow?.configKey)) || {};
          if (!isEmpty(configKeyResponse)) {
            this.props.updateConfigKeyResponseForAadhar(configKeyResponse);
            this.setState({
              irctcProfileIncompleteFlowConfig: configKeyResponse,
            });
          }
        }
        this.props.updateVerificationFlow(response?.verificationFlow ?? {});
        const userIdStatusObject = parseValidateUserResponse(response);

        switch (userIdStatusObject.status) {
          case STATUS_USER_PROFILE_INCOMPLETE:
          case STATUS_API_SUCCESS:
            this.props.showHideIRCTCUsernameError(false);
            this.props?.setShowIrctcProfileCompletionFlow?.(
              userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE,
            );
            this.setState({
              successMessage: _label('success_message'),
              error: null,
              loading: false,
              ...(showProfileIncompleteFlowPokus() !== 0 && {
                showProfileIncompleteFlow:
                  userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE,
              }),
            });
            if (
              showProfileIncompleteFlowPokus() !== 0 &&
              userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE
            ) {
              this.props.setUserNameToRedux(this.state.userName);
              if (!this.props?.setShowIrctcProfileCompletionFlow) {
                updateAccountUserNameAPI(this.state.userName);
              }
              if (this.props?.successCallback) {
                this.props?.successCallback?.();
                trackGenericEvar99Event(
                  REVIEW_PAGE_TRACKING_KEY,
                  IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.PROFILE_INCOMPLETE_BS_LOAD,
                );
              } else {
                trackGenericEvar99Event(
                  TRAVELERS_PAGE_TRACKING_KEY_NEW,
                  IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.PROFILE_INCOMPLETE_BS_LOAD,
                );
              }
            }
            if (userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE) {
              updateEvar47or97Variable(
                IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER
                  .IRCTC_PROFILE_INCOMPLETE_STATUS_EVAR99,
              );
            } else {
              updateEvar47or97Variable(
                IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_PROFILE_COMPLETE_STATUS_EVAR99,
              );
            }
            success = true;
            break;
          case STATUS_API_ERROR:
            error = this._getErrorMessage(userIdStatusObject.message);
            break;

          case STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING:
          case STATUS_EMAIL_OTP_VERIFICATION_PENDING:
          case STATUS_MOBILE_OTP_VERIFICATION_PENDING:
            this.props.showHideIRCTCUsernameError(false);
            this._closeModalNew();
            Keyboard.dismiss();
            setTimeout(() => {
              Actions.railsOtpVerification({
                verify: userIdStatusObject.status,
                focus:
                  userIdStatusObject.status === STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING
                    ? 'email'
                    : 'mobile',
                userName,
                otpType:
                  userIdStatusObject.status === STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING
                    ? 'B'
                    : userIdStatusObject.status === STATUS_EMAIL_OTP_VERIFICATION_PENDING
                    ? 'E'
                    : 'M',
                from: this.props.from,
                probableTo: this.props.probableTo,
                nextPageProps: this.props.nextPageProps,
              });
            }, 200);
            break;
          default:
            error = this._getErrorMessage(userIdStatusObject.message);
            break;
        }
      }
    } catch (e) {
      error = `${_label('something_went_wrong')} Error Code: ${e?.errorCode}`;
    }

    if (error) {
      const ErrorMessage = _label('irctc_invalid_user');
      this.setState({
        checkingMessage: null,
        loading: false,
        error: ErrorMessage,
      });
    } else {
      this.setState({
        loading: false,
        error: null,
      });
      if (success) {
        this.setState({
          successMessage: _label('irctc_valid_user'),
          checkingMessage: null,
        });

        if (this.props.probableTo) {
          if (this.props.replace) {
            Actions[this.props.probableTo]({ type: 'replace', ...this.props.nextPageProps });
          } else {
            Actions[this.props.probableTo]({ ...this.props.nextPageProps });
          }
        }
      }
    }
  };

  _onForgotUsernameClick = () => {
    trackEnterUserNameEvent(
      RAIL_EVENTS.USERFLOW.mob_rail_travellers_irctc_userid_popup_RN_get_username,
    );
    this.props.setModalVisibility(false);
    Actions.railsForgotUsername({
      from: this.props.from,
      probableTo: this.props.probableTo,
      nextPageProps: this.props.nextPageProps,
    });
  };

  _onCreateNewClick = () => {
    trackEnterUserNameEvent(
      RAIL_EVENTS.USERFLOW.mob_rail_travellers_irctc_userid_popup_RN_create_account,
    );
    trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERNAME_CREATE_CLICKED_EVAR99);

    this.props.setModalVisibility(false);

    const showIrctcAccountCreatingToast = getRailsIrctcAccountToast();

    if (showIrctcAccountCreatingToast) {
      return Actions.openCreateIRCTCAccountWebView({
        from: 'travelers',
      });
    }

    Actions.railsCreateAccountPage({
      from: this.props.from,
      probableTo: this.props.probableTo,
      nextPageProps: this.props.nextPageProps,
    });
  };

  _onNewPasswordClick = () => {
    this.setState({ showPasswordReminderModal: true });
  };

  _closeModal = (e) => {
    if (isMweb()) {
      e.preventDefault();
    }
    this.props.setModalVisibility(false);
    trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERNAME_DONE_CLOSE_EVAR99);
  };

  _crossIconClose = (e) => {
    if (isMweb()) {
      e.preventDefault();
    }
    this.props.setModalVisibility(false);
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLERS_IRCTC_USERNAME_CROSS,
    );
    trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERNAME_DONE_CLOSE_EVAR99);
  };

  _saveAndClose = async (e) => {
    if (isMweb()) {
      e.preventDefault();
    }
    this.props.setUserNameToRedux(this.state.userName);
    const updatedUserNames = await updateAccountUserNameAPI(this.state.userName);
    this.props.updateIrctcUsernames(updatedUserNames);
    this.props.handleUpdatedUsenames?.(updatedUserNames);
    this.props.setModalVisibility(false);
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLERS_IRCTC_USERNAME_DONE,
    );
    trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERNAME_DONE_CLOSE_EVAR99);
    if (this.props?.successCallback) {
      this.props?.successCallback?.();
    }
  };

  _getTextContent = () => {
    if (this.state.error) {
      return this.state?.passwordModalConfig?.rails_traveller_irctc_bs_config?.ErrorText;
    }
    if (this.state.successMessage) {
      return (
        this.state?.irctcProfileIncompleteFlowConfig?.successText?.replace(
          '{{irctcUsername}}',
          this.state?.userName ?? '',
        ) || ''
      );
    }
    if (this.state.checkingMessage) {
      return this.state?.passwordModalConfig?.rails_traveller_irctc_bs_config?.LoadingText;
    }
    return this.state?.passwordModalConfig?.rails_traveller_irctc_bs_config?.InitialpromptText;
  };

  _getTextStyle = () => {
    if (this.state.error) {
      return NewIrctcModalStyle.errorText;
    }
    if (this.state.successMessage) {
      return NewIrctcModalStyle.successText;
    }
    return NewIrctcModalStyle.text1;
  };

  _handleCompleteProfileClick = () => {
    if (this.props.verificationFlow?.errorCode === IRCTC_TATKAL_AADHAR_NOT_VERIFIED_ERROR_CODE) {
      Actions.openIrctcAadharCompletionWebView();
    } else {
      Actions.openCompleteIrctcProfileWebView();
    }
    this.props.setModalVisibility(false);
    trackTravellerPageEvent(
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.COMPLETE_PROFILE_CLICK_BS,
    );
    trackGenericEvar99Event(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.COMPLETE_PROFILE_CLICK_EVAR99,
    );
  };

  render() {
    const { showPasswordReminderModal, successMessage, passwordModalConfig } = this.state;
    if (showNewIrctcBottomSheet() === 1 || showNewIrctcBottomSheet() === 2) {
      return (
        <View testID="new_user_flow_modal_container">
          <Modal
            config={passwordModalConfig}
            transparent
            visible={this.props.modalVisible}
            onRequestClose={this._closeModal}
            testID="new_user_flow_modal"
          >
            <View
              style={NewIrctcModalStyle.modalBackground}
              testID="new_user_flow_modal_background"
            >
              <TouchableWithoutFeedback
                onPress={this._closeModal}
                testID="new_user_flow_modal_touchable"
              >
                <View style={{ flex: 1 }} testID="new_user_flow_modal_view" />
              </TouchableWithoutFeedback>
              <View style={NewIrctcModalStyle.container} testID={this.props?.id}>
                {!successMessage ? (
                  <View
                    style={NewIrctcModalStyle.mainContainer}
                    testID="new_user_flow_modal_main_container"
                  >
                    <View
                      style={NewIrctcModalStyle.headerContainer}
                      testID="new_user_flow_modal_header_container"
                    >
                      <Text
                        style={[
                          NewIrctcModalStyle.header,
                          fontStyle('lato'),
                          { fontWeight: 'bold', fontSize: 18, color: colors.black },
                        ]}
                        testID="new_user_flow_modal_header_text"
                      >
                        {_label('irctc_username')}
                      </Text>
                      <TouchableOpacity
                        onPress={this._crossIconClose}
                        testID="new_user_flow_modal_header_close_button"
                      >
                        <Image
                          source={ASSETS.crossIcon}
                          style={NewIrctcModalStyle.crossIcon}
                          testID="new_user_flow_modal_header_close_icon"
                        />
                      </TouchableOpacity>
                    </View>

                    <View
                      style={NewIrctcModalStyle.textBoxContainer}
                      testID="new_user_flow_modal_text_box_container"
                    >
                      <Text
                        style={[
                          NewIrctcModalStyle.textBoxText,
                          getLineHeight(16.8),
                          { fontSize: 14 },
                        ]}
                        testID="new_user_flow_modal_text_box_text"
                      >
                        {passwordModalConfig?.rails_traveller_irctc_bs_config?.warningText}
                      </Text>
                    </View>

                    <View
                      style={[
                        NewIrctcModalStyle.textInputContainer,
                        !!this.state.error && {
                          borderColor: colors.red17,
                          backgroundColor: colors.backgroundRed3,
                        },
                      ]}
                      testID={`${this.props?.id}_usernameInput`}
                    >
                      <Text
                        style={[
                          NewIrctcModalStyle.textInputHeader,
                          fontStyle('bold'),
                          getLineHeight(14.4),
                          !!this.state.error && { color: colors.red17 },
                        ]}
                        testID="new_user_flow_modal_text_input_header"
                      >
                        {_label('username', { uppercase: true })}
                      </Text>
                      <TextInput
                        testID={`${this.props?.id}_usernameInput`}
                        autoCorrect={false}
                        autoCapitalize="none"
                        placeholder={_label('enter_irctc_username')}
                        placeholderTextColor={colors.lightTextColor}
                        style={[
                          NewIrctcModalStyle.textInputStyle,
                          fontStyle('black'),
                          { fontSize: 16, lineHeight: 19.2 },
                        ]}
                        onChangeText={(text) => {
                          this.setState({ userName: text });
                          checkEnglishKeyboard('error', text, this);
                        }}
                        value={this.state.userName}
                        ref={(ref) => {
                          this.inputRef = ref;
                        }}
                      />
                    </View>
                    <View
                      style={NewIrctcModalStyle.additionalTextContainer}
                      testID="new_user_flow_modal_additional_text_container"
                    >
                      <Text
                        style={[this._getTextStyle(), fontStyle('regular'), getLineHeight(12)]}
                        testID="new_user_flow_modal_additional_text"
                      >
                        {this._getTextContent()}
                      </Text>
                    </View>
                    <View
                      style={NewIrctcModalStyle.footerContainer}
                      testID="new_user_flow_modal_footer_container"
                    >
                      <TouchableOpacity
                        onPress={this._onForgotUsernameClick}
                        testID="new_user_flow_modal_forgot_username_button"
                      >
                        <Text
                          style={[
                            NewIrctcModalStyle.text,
                            fontStyle('bold'),
                            getLineHeight(12),
                            { fontSize: 12, paddingVertical: 9 },
                          ]}
                          testID="new_user_flow_modal_forgot_username_text"
                        >
                          {_label('forgot_username', { capitalize: true })}
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={this._onCreateNewClick}
                        testID="new_user_flow_modal_create_new_account_button"
                      >
                        <Text
                          style={[
                            NewIrctcModalStyle.text,
                            fontStyle('bold'),
                            getLineHeight(12),
                            { fontSize: 12, paddingVertical: 9 },
                          ]}
                          testID="new_user_flow_modal_create_new_account_text"
                        >
                          {_label('create_new_account', { capitalize: true })}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <View
                    style={NewIrctcModalStyle.mainContainer}
                    testID="new_user_flow_modal_main_container"
                  >
                    <View
                      style={NewIrctcModalStyle.headerContainer}
                      testID="new_user_flow_modal_header_container"
                    >
                      <Text
                        style={[
                          NewIrctcModalStyle.header,
                          fontStyle('lato'),
                          { fontWeight: 'bold', fontSize: 18, color: colors.black },
                        ]}
                        testID="new_user_flow_modal_header_text"
                      >
                        {_label(
                          showProfileIncompleteFlowPokus() !== 0 &&
                            this.state.showProfileIncompleteFlow
                            ? 'irctc_profile_incomplete'
                            : 'irctc_username',
                        )}
                      </Text>
                      <TouchableOpacity
                        onPress={this._closeModal}
                        testID="new_user_flow_modal_header_close_button"
                      >
                        <Image
                          source={ASSETS.crossIcon}
                          style={NewIrctcModalStyle.crossIcon}
                          testID="new_user_flow_modal_header_close_icon"
                        />
                      </TouchableOpacity>
                    </View>

                    <View
                      style={[
                        NewIrctcModalStyle.textInputContainer,
                        !!this.state.error && {
                          borderColor: colors.red17,
                          backgroundColor: colors.backgroundRed3,
                        },
                      ]}
                      testID="new_user_flow_modal_username_input"
                    >
                      <Text
                        style={[
                          NewIrctcModalStyle.textInputHeader,
                          fontStyle('bold'),
                          getLineHeight(12),
                          !!this.state.error && { color: colors.red17 },
                        ]}
                        testID="new_user_flow_modal_username_input_header"
                      >
                        {_label('username', { uppercase: true })}
                      </Text>
                      <TextInput
                        testID={`${this.props?.id}_usernameInput`}
                        autoCorrect={false}
                        autoCapitalize="none"
                        placeholder={_label('enter_irctc_username')}
                        placeholderTextColor={colors.lightTextColor}
                        style={[
                          NewIrctcModalStyle.textInputStyle,
                          fontStyle('black'),
                          getLineHeight(16),
                          { color: this.state.userName ? colors.black : colors.lightTextColor },
                        ]}
                        onChangeText={(text) => {
                          this.setState({ userName: text });
                          checkEnglishKeyboard('error', text, this);
                        }}
                        value={this.state.userName}
                        ref={(ref) => {
                          this.inputRef = ref;
                        }}
                        editable={false}
                      />
                    </View>
                    <View
                      style={NewIrctcModalStyle.additionalTextContainer}
                      testID="new_user_flow_modal_additional_text_container"
                    >
                      {successMessage && (
                        <Image
                          source={ASSETS.greenTick}
                          style={NewIrctcModalStyle.additionalTextIcon}
                          testID="new_user_flow_modal_additional_text_icon"
                        />
                      )}
                      <Text
                        style={[this._getTextStyle(), fontStyle('regular'), getLineHeight(12)]}
                        testID="new_user_flow_modal_additional_text"
                      >
                        {this._getTextContent()}
                      </Text>
                    </View>
                    {showProfileIncompleteFlowPokus() && this.state.showProfileIncompleteFlow ? (
                      <>
                        <View
                          style={NewIrctcModalStyle.additionalTextContainer}
                          testID="new_user_flow_modal_additional_text_container"
                        >
                          <Image
                            source={ASSETS.errorInfoIcon}
                            style={NewIrctcModalStyle.additionalTextIcon}
                            testID="new_user_flow_modal_additional_text_icon"
                          />
                          <Text
                            style={[
                              NewIrctcModalStyle.errorText,
                              fontStyle('regular'),
                              getLineHeight(12),
                            ]}
                            testID="new_user_flow_modal_additional_text"
                          >
                            {this.state?.irctcProfileIncompleteFlowConfig?.errorText}
                          </Text>
                        </View>
                        <View
                          style={NewIrctcModalStyle.profileCompletionWrapper}
                          testID="new_user_flow_modal_profile_completion_wrapper"
                        >
                          <View
                            style={NewIrctcModalStyle.arrowUp}
                            testID="new_user_flow_modal_profile_completion_arrow_up"
                          />
                          <View
                            style={NewIrctcModalStyle.profileCompletionContainer}
                            testID="new_user_flow_modal_profile_completion_container"
                          >
                            <Text
                              style={NewIrctcModalStyle.profileCompletionTitle}
                              testID="new_user_flow_modal_profile_completion_title"
                            >
                              {this.state?.irctcProfileIncompleteFlowConfig?.travellerBs?.title}
                            </Text>
                            <View
                              style={NewIrctcModalStyle.profileCompletionHeader}
                              testID="new_user_flow_modal_profile_completion_header"
                            >
                              <LinearGradient
                                colors={[colors.white, colors.white, colors.lightRed4]}
                                style={NewIrctcModalStyle.profileCompletionStepsTitle}
                                testID="new_user_flow_modal_profile_completion_steps_title"
                                start={{ x: 0, y: 1 }}
                                end={{ x: 1, y: 0 }}
                              >
                                <Text
                                  style={NewIrctcModalStyle.profileCompletionText}
                                  testID="new_user_flow_modal_profile_completion_steps_text"
                                >
                                  {
                                    this.state?.irctcProfileIncompleteFlowConfig?.travellerBs
                                      ?.profileCompletionTitle
                                  }
                                </Text>
                              </LinearGradient>
                              <View
                                style={NewIrctcModalStyle.profileStepWrapper}
                                testID="new_user_flow_modal_profile_completion_steps_wrapper"
                              >
                                { }
                                {this.state?.irctcProfileIncompleteFlowConfig?.travellerBs?.profileCompletionSteps?.map(
                                  (item, index) => (
                                    <View
                                      key={index}
                                      style={[
                                        NewIrctcModalStyle.profileStepContainer,
                                        index === 0 && NewIrctcModalStyle.firstProfileStepContainer,
                                      ]}
                                    >
                                      <View
                                        style={NewIrctcModalStyle.redTickGradient}
                                        testID="new_user_flow_modal_profile_completion_steps_icon"
                                      >
                                        <Image
                                          source={ASSETS.redTick}
                                          style={NewIrctcModalStyle.redTickIcon}
                                          testID="new_user_flow_modal_profile_completion_steps_icon"
                                        />
                                      </View>
                                      <Text
                                        style={NewIrctcModalStyle.profileStepText}
                                        testID="new_user_flow_modal_profile_completion_steps_text"
                                      >
                                        {item ?? ''}
                                      </Text>
                                    </View>
                                  ),
                                )}
                              </View>
                            </View>
                          </View>
                        </View>
                        <View
                          style={NewIrctcModalStyle.footerContainer2}
                          testID="new_user_flow_modal_footer_container"
                        >
                          <Image
                            source={ASSETS.updatesIcon}
                            style={NewIrctcModalStyle.footerUpdateIcon}
                            testID="new_user_flow_modal_footer_icon"
                          />
                          <Text
                            style={[
                              NewIrctcModalStyle.footerText,
                              fontStyle('regular'),
                              getLineHeight(12),
                            ]}
                            testID="new_user_flow_modal_footer_text"
                          >
                            {this.state?.irctcProfileIncompleteFlowConfig?.travellerBs?.footerText}
                          </Text>
                        </View>
                        <View
                          style={NewIrctcModalStyle.buttonContainer}
                          testID="new_user_flow_modal_button_container"
                        >
                          <BtnGeneric
                            btnText={_label('complete_your_profile', { uppercase: true })}
                            clickAction={this._handleCompleteProfileClick}
                            disabled={!this.state.userName}
                            id={`${this.props?.id}_submitButton`}
                            showBtnLoader={false}
                            fontStyle="lato"
                            fontWeight="900"
                            fontsize="16"
                            lineHeight="19.2"
                          />
                        </View>
                        <View />
                      </>
                    ) : (
                      <>
                        <View
                          style={NewIrctcModalStyle.footerContainer1}
                          testID="new_user_flow_modal_footer_container"
                        >
                          <TouchableOpacity
                            onPress={this._onForgotUsernameClick}
                            testID="new_user_flow_modal_forgot_username_button"
                          >
                            <Text
                              style={[
                                NewIrctcModalStyle.greyText,
                                fontStyle('bold'),
                                getLineHeight(12),
                                { fontSize: 12, paddingVertical: 9 },
                              ]}
                              testID={`${this.props?.id}_forgotUsernameButton`}
                            >
                              {_label('forgot_username', { capitalize: true })}
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={this._onCreateNewClick}
                            testID="new_user_flow_modal_create_new_account_button"
                          >
                            <Text
                              style={[
                                NewIrctcModalStyle.greyText,
                                fontStyle('bold'),
                                getLineHeight(12),
                                { fontSize: 12, paddingVertical: 9 },
                              ]}
                              testID={`${this.props?.id}_createNewAccountButton`}
                            >
                              {_label('create_new_account', { capitalize: true })}
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={NewIrctcModalStyle.textBoxContainer1}
                          testID="new_user_flow_modal_text_box_container"
                        >
                          <Text
                            style={[
                              NewIrctcModalStyle.textBoxText,
                              getLineHeight(16.8),
                              { fontSize: 14, fontWeight: '400' },
                            ]}
                            testID="new_user_flow_modal_text_box_text"
                          >
                            {
                              passwordModalConfig?.rails_traveller_irctc_bs_config
                                ?.PostValidationText
                            }
                          </Text>
                        </View>

                        <View
                          style={NewIrctcModalStyle.forgotPasswordContainer}
                          testID="new_user_flow_modal_forgot_password_container"
                        >
                          <Text
                            style={[
                              NewIrctcModalStyle.forgotPasswordText,
                              getLineHeight(16.8),
                              fontStyle('lato'),
                              { fontSize: 14, fontWeight: '700' },
                            ]}
                            testID="new_user_flow_modal_forgot_password_text"
                          >
                            Forgot Password?
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              this._onNewPasswordClick();
                            }}
                            testID="new_user_flow_modal_forgot_password_button"
                          >
                            <Text
                              style={[
                                NewIrctcModalStyle.getNewPasswordText,
                                getLineHeight(16.8),
                                fontStyle('lato'),
                                { fontSize: 12, fontWeight: '700' },
                              ]}
                              testID="new_user_flow_modal_forgot_password_button_text"
                            >
                              Get a new password now
                            </Text>
                          </TouchableOpacity>
                        </View>

                        {!!showPasswordReminderModal && (
                          <View testID="new_user_flow_modal_password_reminder_container">
                            <Modal
                              transparent
                              visible={showPasswordReminderModal}
                              onRequestClose={this._hidePasswordReminderModal}
                              testID="new_user_flow_modal_password_reminder_modal"
                            >
                              <TouchableWithoutFeedback
                                onPress={this._hidePasswordReminderModal}
                                testID="new_user_flow_modal_password_reminder_touchable"
                              >
                                <View style={{ flex: 1, backgroundColor: colors.modalgrey }} />
                              </TouchableWithoutFeedback>
                              <View
                                style={{ backgroundColor: colors.white, height: 300 }}
                                testID="new_user_flow_modal_password_reminder_view"
                              >
                                <RailRetrievePasswordContainer
                                  dismiss={this._hidePasswordReminderModal}
                                  irctcUserName={this.props.irctcUserName}
                                  id={`${this.props?.id}_onnewPasswordButtonClicked`}
                                />
                              </View>
                              {isIos() && <KeyboardSpacer />}
                            </Modal>
                          </View>
                        )}
                        {showNewIrctcBottomSheet() === 1 && (
                          <View
                            style={NewIrctcModalStyle.buttonContainer2}
                            testID="new_user_flow_modal_button_container"
                          >
                            <TouchableOpacity
                              onPress={(e) => {
                                this._saveAndClose(e);
                              }}
                              style={NewIrctcModalStyle.closeButton1}
                              testID={`${this.props?.id}_closeButton`}
                            >
                              <LinearGradient
                                colors={[colors.lightBlue, colors.darkBlue]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                                style={NewIrctcModalStyle.gradientText}
                                testID="new_user_flow_modal_gradient_text"
                              />
                              <Text
                                style={NewIrctcModalStyle.closeButtonText1}
                                testID="new_user_flow_modal_close_button_text"
                              >
                                {_label('done')}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        )}
                        {showNewIrctcBottomSheet() === 2 && (
                          <View testID="new_user_flow_modal_checkbox_container">
                            <TouchableOpacity
                              onPress={this._onCheckBoxClick}
                              testID="new_user_flow_modal_checkbox_button"
                            >
                              <View
                                style={NewIrctcModalStyle.checkboxContainer}
                                testID="new_user_flow_modal_checkbox_container"
                              >
                                <View
                                  style={NewIrctcModalStyle.checkBoxIconContainer}
                                  testID="new_user_flow_modal_checkbox_icon_container"
                                >
                                  <CheckBox
                                    label=""
                                    onChange={this._onCheckBoxClick}
                                    style={{ height: 24, width: 24 }}
                                    checked={this.state.isChecked}
                                    checkedImage={ASSETS.checkBoxActive}
                                    uncheckedImage={ASSETS.checkBoxInactive}
                                    testID="new_user_flow_modal_checkbox"
                                  />
                                </View>
                                <Text
                                  style={NewIrctcModalStyle.title}
                                  testID="new_user_flow_modal_checkbox_text"
                                >
                                  {_label('irctc_checkbox_text')}
                                </Text>
                              </View>
                            </TouchableOpacity>
                            <View
                              style={
                                this.state.isChecked
                                  ? NewIrctcModalStyle.buttonContainer1
                                  : NewIrctcModalStyle.buttonContainerDisabled
                              }
                              testID="new_user_flow_modal_checkbox_button_container"
                            >
                              <BtnGeneric
                                btnText={this.state.loading ? '' : _label('save_and_close')}
                                clickAction={this._saveAndClose}
                                disabled={!this.state.isChecked || this.state.loading}
                                id={`${this.props?.id}_submitButton`}
                                showBtnLoader={this.state.loading}
                                fontStyle="lato"
                                fontWeight="900"
                                fontsize="16"
                                lineHeight="16.8"
                                marginHorizontal="16"
                              />
                            </View>
                          </View>
                        )}
                      </>
                    )}
                  </View>
                )}
                {!successMessage && (
                  <View
                    style={NewIrctcModalStyle.buttonContainer}
                    testID="new_user_flow_modal_button_container"
                  >
                    <BtnGeneric
                      btnText={this.state.loading ? '' : _label('check_button_text')}
                      clickAction={() => {
                        this._handleUsernameVerification();
                      }}
                      disabled={!this.state.userName}
                      id={`${this.props?.id}_submitButton`}
                      showBtnLoader={this.state.loading}
                      fontStyle="lato"
                      fontWeight="900"
                      fontsize="16"
                      lineHeight="19.2"
                    />
                    <TouchableOpacity
                      onPress={() => {
                        this._closeModalNew();
                      }}
                      style={NewIrctcModalStyle.closeButton}
                      testID={`${this.props?.id}_closeButton`}
                    >
                      <Text
                        style={NewIrctcModalStyle.closeButtonText}
                        testID="new_user_flow_modal_close_button_text"
                      >
                        {_label('close_button_text')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </View>
            {isIos() && <KeyboardSpacer />}
          </Modal>
        </View>
      );
    }
    return (
      <View>
        <Modal transparent visible={this.props.modalVisible} onRequestClose={this._closeModal}>
          <TouchableWithoutFeedback onPress={this._closeModal}>
            <View style={{ flex: 1, backgroundColor: colors.modalgrey }} />
          </TouchableWithoutFeedback>
          <View style={{ backgroundColor: colors.white }}>
            <View style={newModalStyle.container} testID={this.props?.id}>
              <View>
                <Text style={[newModalStyle.header, fontStyle('black'), getLineHeight(20)]}>
                  {_label('irctc_username')}
                </Text>
              </View>
              <View style={newModalStyle.textBoxContainer}>
                <Text style={[newModalStyle.yellowText, fontStyle('regular'), getLineHeight(10)]}>
                  {_label('irctc_username_info_new')}
                </Text>
              </View>
              <FloatingInput
                testID={`${this?.props?.id}_usernameInput`}
                label={_label('username', { uppercase: true })}
                customStyle={{
                  labelStyle: [fontStyle('bold'), getLineHeight(12)],
                  inputFieldStyle: [fontStyle('black'), getLineHeight(16)],
                  errorMessageStyle: [fontStyle('regular'), getLineHeight(12)],
                }}
                inputProps={{
                  autoCorrect: false,
                  autoCapitalize: 'none',
                }}
                value={this.state.userName}
                onChangeText={(text) => {
                  this.setState({ userName: text });
                  checkEnglishKeyboard('error', text, this);
                }}
                inputRef={(ref) => {
                  this.inputRef = ref;
                }}
                isError={!!this.state.error}
                errorMessage={this.state.error}
              />
              <View>
                <BtnGeneric
                  btnText={this.state.loading ? '' : _label('submit', { uppercase: true })}
                  clickAction={() => {
                    this._onVerifyClick();
                  }}
                  disabled={!this.state.userName}
                  id={`${this.props?.id}_submitButton`}
                  showBtnLoader={this.state.loading}
                />
              </View>
              <View>
                <TouchableOpacity
                  style={{ marginVertical: 15 }}
                  onPress={this._onForgotUsernameClick}
                >
                  <Text
                    style={[newModalStyle.text, fontStyle('bold'), getLineHeight(12)]}
                    testID={`${this.props?.id}_forgotUsernameButton`}
                  >
                    {_label('forgot_username', { capitalize: true })}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity style={{ marginBottom: 15 }} onPress={this._onCreateNewClick}>
                  <Text
                    style={[newModalStyle.text, fontStyle('bold'), getLineHeight(12)]}
                    testID={`${this.props?.id}_createNewAccountButton`}
                  >
                    {_label('create_new_irctc_account', { capitalize: true })}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          {isIos() && <KeyboardSpacer />}
        </Modal>
      </View>
    );
  }
}

const mapStateToProps = (state, ownProps) => {
  const {
    railsVernacular: { texts },
    railsTraveler: { selectedQuota = null } = {},
    railsUserVerification: { verificationFlow = {} } = {},
  } = state;
  return {
    ...ownProps,
    quota: selectedQuota?.code,
    labels: {
      enterIrctcUsername: texts.user_enter_irctc_username,
      hintIrctcUsername: texts.user_irctc_username_hint,
      createNew: texts.user_create_new,
      forgot: texts.user_forgot,
      next: texts.user_next,
      newIrctcAcc: texts.trv_create_new_irctc_account,
      forgotUsername: texts.trv_forgot_irctc_username,
      userNameForIrctc: texts.trv_username,
      irctcUserName: texts.trv_irctc_username,
      userNameInfo: texts.trv_irctc_username_info,
      userNameSubmit: texts.trv_username_submit,
    },
    verificationFlow,
  };
};

const mapDispatchToProps = (dispatch) => ({
  setUserNameToRedux: (userName) => {
    dispatch(updateUsername(userName));
  },
  showHideIRCTCUsernameError: (val) => {
    dispatch(showHideIRCTCUsernameError(val));
  },
  setShowIrctcProfileCompletionFlow: (val) => {
    dispatch(setShowIrctcProfileCompletionFlow(val));
  },
  updateIrctcUsernames: (irctcUsernames) => {
    dispatch(updateIrctcUserNames(irctcUsernames));
  },
  updateConfigKeyResponseForAadhar: (response) => {
    dispatch(updateConfigKeyResponseForAadharVerification(response));
  },
  updateVerificationFlow: (verificationFlow = {}) => {
    dispatch(updateVerificationFlow(verificationFlow));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(UserNameModal);

UserNameModal.propTypes = {
  from: PropTypes.string.isRequired,
  modalVisible: PropTypes.bool.isRequired,
  probableTo: PropTypes.string,
  replace: PropTypes.bool,
  setModalVisibility: PropTypes.func.isRequired,
  setUserNameToRedux: PropTypes.func.isRequired,
  userName: PropTypes.string,
  labels: PropTypes.object.isRequired,
  id: PropTypes.string,
  nextPageProps: PropTypes.object,
  irctcUserName: PropTypes.string,
  showHideIRCTCUsernameError: PropTypes.func,
  setShowIrctcProfileCompletionFlow: PropTypes.func,
  successCallback: PropTypes.func,
  updateIrctcUsernames: PropTypes.func,
  handleUpdatedUsenames: PropTypes.func,
  updateConfigKeyResponseForAadhar: PropTypes.func,
  quota: PropTypes.string,
  updateVerificationFlow: PropTypes.func,
  verificationFlow: PropTypes.object,
};

UserNameModal.defaultProps = {
  probableTo: null,
  replace: false,
  userName: null,
};
