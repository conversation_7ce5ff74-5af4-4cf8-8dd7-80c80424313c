import PropTypes from 'prop-types';
import React from 'react';
import { Image, ScrollView, Text, View, StyleSheet } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import isEmpty from 'lodash/isEmpty';
import fecha from 'fecha';
import { Actions } from '../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {KeyboardAvoidingViewWrapper} from '../CreateNewAccount/CreateNewAccount';
import {today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import Calendar from '../Common/CustomDateCalendar';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { isValidEmail, isValidMobile, isOnlyNumber } from '@mmt/legacy-commons/Helpers/validationHelpers';
import {
  retrieveUserId,
  STATUS_API_ERROR,
  STATUS_API_SUCCESS,
  STATUS_NON_RESPONSIVE_API_ERROR,
} from '../../Utils/UserRepository';
import {trackForgotUsernameEvent, trackPageLoad} from '../../railsAnalytics';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import {_label} from '../../vernacular/AppLanguage';
import {fontStyle, checkEnglishKeyboard, getLineHeight} from '../../vernacular/VernacularUtils';
import { isApp } from '../../Utils/device';
import { EMAIL_CONST, MOBILE_CONST } from '../../Utils/RailsConstant';
import ASSETS from '../../Utils/Assets/RailsAssets';
import InputField from '@Frontend_Ui_Lib_App/InputField';
import Header from '@Frontend_Ui_Lib_App/Header';
import getPlatformElevation from 'packages/legacy-commons/Styles/getPlatformElevation';
import closeGreyIcon from '@mmt/legacy-assets/src/ic-headerclose-grey.webp';

const closeIcon = closeGreyIcon;

const minDate = today();
minDate.setFullYear(minDate.getFullYear() - 100);
const maxDate = today();
maxDate.setFullYear(maxDate.getFullYear() - 18);

const NextButton = ({onClick, loading}) => (
  <TouchableRipple onPress={onClick}>
    <View style={nextButtonStyles.container}>
      <LinearGradient
        style={nextButtonStyles.lgStyles}
        colors={['#53B2FE', '#065AF3']}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
      >
        <View>
          {
            loading &&
            <Spinner size={20} color={colors.white} />
          }
          {
            !loading &&
            <Text style={nextButtonStyles.nextText}>{_label('next',{uppercase : true})}
            </Text>
          }
        </View>
      </LinearGradient>
    </View>
  </TouchableRipple>
);

const nextButtonStyles = StyleSheet.create({
  container: {width: '100%', flexDirection: 'row'},
  lgStyles: {
    borderRadius: 8,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    width: '100%',
  },
  nextText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    backgroundColor: colors.transparent,
  },
});

NextButton.propTypes = {
  loading: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
};

const UserNameSuccessScreen = () => (
  <View style={userSuccessStyles.container}>
    <Image
      source={ASSETS.icFpTick}
      style={userSuccessStyles.image}
    />
    <Text style={[userSuccessStyles.userSentStyles, fontStyle('bold'), getLineHeight(16)]}>
      {_label('username_sent_to_email',{sentenceCase : true})}
    </Text>
  </View>
);

const userSuccessStyles = StyleSheet.create({
  container: {flex: 1, alignItems: 'center', justifyContent: 'center'},
  image: {height: 124, width: 124},
  userSentStyles: {
     color: colors.black, fontSize: 16, marginTop: 12,
  },
});


export default class ForgotUsername extends React.Component {
  constructor(props) {
    super(props);
    if (!this.props.from) {
      Actions.pop();
    }
  }
  state = {
    text: '',
    error: null,
    dob: null,
    errorDob: null,
    success: false,
    loading: false,
    apiError: null,
  };

  componentDidMount() {
    trackPageLoad(RAIL_EVENTS.USERFLOW.mob_rail_listing_irctc_forgot_username);
  }

  _sendUserName = async () => {
    this.setState({error: null, errorDob: null, apiError: null});
    const {text, dob} = this.state;
    let emailOrMobileText = text.trim();
    let isEmail = false;
    if (!isOnlyNumber(emailOrMobileText)){
      isEmail = true;
      emailOrMobileText = emailOrMobileText?.toLowerCase();
    }
    let error = null;
    let errorDob = null;
    let apiError = null;

    trackForgotUsernameEvent(RAIL_EVENTS.USERFLOW.mob_rail_listing_irctc_forgot_username_get_username_clicked);
    if (isEmpty(emailOrMobileText)) {
      error = _label('please_enter_email_or_mobile',{sentenceCase : true});
    } else if (!isValidEmail(emailOrMobileText) && isEmail) {
      error = _label('please_enter_correct_email',{sentenceCase : true});
    } else if (!isValidMobile(emailOrMobileText) && !isEmail) {
      error = _label('please_enter_correct_mobile',{sentenceCase : true});
    } else if (!dob) {
      errorDob = _label('please_enter_dob',{sentenceCase : true});
    } else if (dob < minDate || dob > maxDate) {
      errorDob = _label('invalid_dob',
          undefined, {
                      minDate : fecha.format(minDate, 'DD MMM YYYY'),
                      maxDate : fecha.format(maxDate, 'DD MMM YYYY'),
                  });
    }
    if (errorDob) {
      this.setState({errorDob});
    } else if (error) {
      this.setState({error});
    } else {
      this.setState({loading: true});
      const isEmailOrMobile = isEmail ? EMAIL_CONST : MOBILE_CONST;
      const response = await retrieveUserId(
        fecha.format(dob, 'YYYYMMDD'),
        emailOrMobileText,
        isEmailOrMobile,
      );
      if (response.status === STATUS_API_SUCCESS) {
        this.setState({success: true});
        setTimeout(() => {
          Actions.pop();
        }, 2000);
      } else if (response.status === STATUS_API_ERROR) {
        apiError = response.message;
      } else if (response.status === STATUS_NON_RESPONSIVE_API_ERROR) {
        apiError = _label('something_went_wrong');
      } else {
        apiError = _label('something_went_wrong');
      }
      this.setState({loading: false, apiError});
    }
  };

  render() {
    if (this.state.success) {
      return (
        <UserNameSuccessScreen />
      );
    }
    return (
      <View style={{ flex: 1 }}>
        <Header
          customStyles={{
            wrapperStyle: forgotUsernameStyles.headerWrapperStyles,
          }}
          leftComponent={
            <View style={{ marginLeft: 20, flex: 1 }}>
              <Text style={[{ color: colors.black04 }, fontStyle('bold'), getLineHeight(16)]}>
                {_label('forgot_irctc_username')}
              </Text>
              <Text
                style={[
                  { color: colors.defaultTextColor },
                  fontStyle('regular'),
                  getLineHeight(12),
                ]}
              >
                {_label('get_username_via_email_or_mobile')}
              </Text>
            </View>
          }
          rightIcons={[
            {
              customStyles: { width: 24, height: 24 },
              icon: closeIcon,
              onPress: () => Actions.pop(),
            },
          ]}
        />
        {this.state.apiError && (
          <View style={{ backgroundColor: colors.lightPink, padding: 10 }}>
            <Text
              style={[{ color: colors.red, fontSize: 14 }, fontStyle('bold'), getLineHeight(14)]}
            >
              {this.state.apiError}
            </Text>
          </View>
        )}
        <KeyboardAvoidingViewWrapper>
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, justifyContent: 'space-between' }}
            style={{ flex: 1, paddingHorizontal: 20, paddingTop: 12 }}
            keyboardShouldPersistTaps="always"
          >
            <View>
              <InputField
                label={_label('irctc_registered_email_or_mobile')}
                placeholder={_label('email_id_or_phone_number')}
                placeholderTextColor={colors.disabledButton}
                inputProps={{
                  autoCapitalize: 'none',
                  returnKeyType: 'done',
                  autoFocus: true,
                }}
                value={this.state.text}
                onChangeText={(text) => {
                  this.setState({ text });
                  checkEnglishKeyboard('error', text, this);
                }}
                isError={!!this.state.error}
                errorMessage={this.state.error}
                customStyle={{
                  wrapperStyle: UsernameStyle.inputWrapperStyle,
                  inputFieldStyle: [
                    UsernameStyle.inputFieldStyle,
                    fontStyle('bold'),
                    getLineHeight(16),
                  ],
                  labelStyle: this.state.error
                    ? { ...UsernameStyle.labelStyle, ...UsernameStyle.labelErrorStyle }
                    : UsernameStyle.labelStyle,
                }}
              />

              <View>
                <Calendar
                  callbackDob={(dob) => { this.setState({dob}); }}
                  minDate={minDate}
                  maxDate={maxDate}
                  title={_label('select_dob')}
                >
                  <View>
                    {
                  this.state.errorDob &&
                  <View>
                    <Text style={[{color: colors.red, fontSize: 14}, fontStyle('bold'), getLineHeight(14)]}>{this.state.errorDob}</Text>
                  </View>
                }
                    <View>
                      <Text style={[{fontSize: 14,  color: colors.azure}, fontStyle('regular'), getLineHeight(14)]}>
                        {_label('select_dob')}
                      </Text>
                      {
                        isApp() &&
                        <Text style={[{fontSize: 12,  color: colors.defaultTextColor, marginTop: 4}, fontStyle('regular'), getLineHeight(12)]}
                        >
                          {this.state.dob ? fecha.format(this.state.dob, 'DD MMM YYYY') : 'DD MMM YYYY'}
                        </Text>
                      }
                    </View>
                  </View>
                </Calendar>
              </View>
            </View>
            <View style={{
              marginTop: 30, marginBottom: 30, width: '100%',
            }}
            >
              <NextButton
                onClick={this._sendUserName}
                loading={this.state.loading}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingViewWrapper>
      </View>
    );
  }
}

const UsernameStyle = StyleSheet.create({
  labelStyle: {
    fontSize: 14,
    fontFamily: fonts.bold,
  },
  inputFieldStyle: {
    fontFamily: fonts.bold,
  },
  labelErrorStyle: {
    color: colors.red,
  },
  inputWrapperStyle: {
    marginBottom: 20,
  },
});
const forgotUsernameStyles = StyleSheet.create({
  headerWrapperStyles: {
    marginHorizontal: 0,
    marginVertical: 0,
    paddingVertical: 0,
    ...getPlatformElevation(5),
    flexDirection: 'row',
    height: 56,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
});

ForgotUsername.propTypes = {
  from: PropTypes.string.isRequired,
};
