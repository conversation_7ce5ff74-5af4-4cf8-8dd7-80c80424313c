import find from 'lodash/find';
import { stringCompare } from '../TravelerDetails/TravelerDetailsUtils';

interface TrainTypeValue {
  id: string;
  value: string;
  key: string;
}

interface TrainType { [key: string]: TrainTypeValue }

export const TrainTypes: TrainType = Object.freeze({
  SV: {
    id: 'Suvidha Trains',
    value: 'suvidha_trains',
    key: 'SV',
  },
  ST: {
    id: 'Special Tatkal Trains',
    value: 'special_tatkal_trains',
    key: 'ST',
  },
  SP: {
    id: 'Special Trains',
    value: 'special_trains',
    key: 'SP',
  },
  G: {
    id: 'Garib Rath',
    value: 'garib_rath',
    key: 'G',
  },
  Y: {
    id: 'Yuva Express',
    value: 'yuva_express',
    key: 'Y',
  },
  O: {
    id: 'Others',
    value: 'others',
    key: 'O',
  },
  D: {
    id: 'Duronto',
    value: 'duronto',
    key: 'D',
  },
  R: {
    id: '<PERSON><PERSON><PERSON>',
    value: 'raj<PERSON><PERSON>',
    key: 'R',
  },
  JS: {
    id: '<PERSON>',
    value: 'jan_shatabdi',
    key: 'JS',
  },
  S: {
    id: 'Shatabdi',
    value: 'shatabdi',
    key: 'S',
  },
});

export const getTrainType = (trainTypeKey: string): TrainTypeValue =>
  find(TrainTypes, (trainType) => stringCompare(trainType.key, trainTypeKey)) ?? {
    id: trainTypeKey,
    value: trainTypeKey,
    key: trainTypeKey,
  };
