const BerthType = Object.freeze({
  UB: {
    code: 'UB',
    value: 'Upper',
  },
  LB: {
    code: 'LB',
    value: 'Lower',

  },
  SL: {
    code: 'SL',
    value: 'Side Lower',
  },
  SU: {
    code: 'SU',
    value: 'Side Upper',
  },
  MB: {
    code: 'MB',
    value: 'Middle',
  },
  WS: {
    code: 'WS',
    value: 'Window',
  },
  SM: {
    code: 'SM',
    value: 'Side Middle',
  },
  CB: {
    code: 'CB',
    value: 'Cabin',
  },
  CP: {
    code: 'CP',
    value: 'Coupe',
  },


});

export default BerthType;
