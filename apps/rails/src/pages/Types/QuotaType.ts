import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import { stringCompare } from '../TravelerDetails/TravelerDetailsUtils';

interface QuotaTypeValue {
  id: string;
  text: string;
  code: string;
  value: string;
  filterLabel: string;
  filterCode: string;
  filterKey: string;
}

export type QuotaTypeKeys = 'GN' | 'LD' | 'TQ' | 'PT' | 'SS' | 'HP' | 'HO' | 'PH' | 'DF' | 'FT' | 'DP' | 'CK' | 'RE' | 'GNRS' | 'OS' | 'PQ' | 'RS' | 'YU' | 'LB' | 'RC(RAC)';

const QuotaType: { [key in QuotaTypeKeys]: QuotaTypeValue } = Object.freeze({
  GN: {
    id: 'GN',
    text: 'general',
    code: 'GN',
    value: 'General',
    filterLabel: 'general_quota_filter',
    filterCode: 'GN',
    filterKey: 'GN_quota_filter',
  },
  LD: {
    id: 'LD',
    text: 'ladies',
    code: 'LD',
    value: 'Ladies',
    filterLabel: 'ladies_quota_filter',
    filterCode: 'LD',
    filterKey: 'LD_quota_filter',
  },
  TQ: {
    id: 'TQ',
    text: 'tatkal_quota',
    code: 'TQ',
    value: 'Tatkal',
    filterLabel: 'tatkal_quota_filter',
    filterCode: 'TQ',
    filterKey: 'TQ_quota_filter',
  },
  PT: {
    id: 'PT',
    text: 'premium_tatkal',
    code: 'PT',
    value: 'Premium Tatkal',
    filterLabel: 'premium_tatkal',
    filterCode: 'TQ',
    filterKey: 'TQ_quota_filter',
  },
  SS: {
    id: 'SS',
    text: 'Senior Citizen',
    code: 'SS',
    value: 'Senior Citizen',
    filterLabel: 'senior_citizen_quota_filter',
    filterCode: 'SS',
    filterKey: 'SS_quota_filter',
  },
  HP: {
    id: 'HP',
    text: 'physically_handicapped_quota',
    code: 'HP',
    value: 'Physically Handicapped Quota',
    filterLabel: 'physically_handicapped_quota',
    filterCode: 'HP',
    filterKey: 'HP_quota_filter',
  },
  HO: {
    id: 'HO',
    text: 'headquarters_high_official_quota',
    code: 'HO',
    value: 'Headquarters/High official Quota',
    filterLabel: 'headquarters_high_official_quota',
    filterCode: 'HO',
    filterKey: 'HO_quota_filter',
  },
  PH: {
    id: 'PH',
    text: 'parliament_house_quota',
    code: 'PH',
    value: 'Parliament House Quota',
    filterLabel: 'parliament_house_quota',
    filterCode: 'PH',
    filterKey: 'PH_quota_filter',
  },
  DF: {
    id: 'DF',
    text: 'defence',
    code: 'DF',
    value: 'Defence',
    filterLabel: 'defence',
    filterCode: 'DF',
    filterKey: 'DF_quota_filter',
  },
  FT: {
    id: 'FT',
    text: 'foreign_tourist',
    code: 'FT',
    value: 'Foreign Tourist',
    filterLabel: 'foreign_tourist',
    filterCode: 'FT',
    filterKey: 'FT_quota_filter',
  },
  DP: {
    id: 'DP',
    text: 'duty_pass',
    code: 'DP',
    value: 'Duty Pass',
    filterLabel: 'duty_pass',
    filterCode: 'DP',
    filterKey: 'DP_quota_filter',
  },
  CK: {
    id: 'CK',
    text: 'tatkal_quota',
    code: 'CK',
    value: 'Tatkal',
    filterLabel: 'tatkal_quota_filter',
    filterCode: 'TQ',
    filterKey: 'TQ_quota_filter',
  },
  RE: {
    id: 'RE',
    text: 'railway_employee_staff',
    code: 'RE',
    value: 'Railway Employee Staff',
    filterLabel: 'railway_employee_staff',
    filterCode: 'RE',
    filterKey: 'RE_quota_filter',
  },
  GNRS: {
    id: 'GNRS',
    text: 'general_quota_road_side',
    code: 'GNRS',
    value: 'General Quota Road Side',
    filterLabel: 'general_quota_road_side',
    filterCode: 'GNRS',
    filterKey: 'GNRS_quota_filter',
  },
  OS: {
    id: 'OS',
    text: 'out_station',
    code: 'OS',
    value: 'Out Station',
    filterLabel: 'out_station',
    filterCode: 'OS',
    filterKey: 'OS_quota_filter',
  },
  PQ: {
    id: 'PQ',
    text: 'pooled_quota',
    code: 'PQ',
    value: 'Pooled Quota',
    filterLabel: 'pooled_quota',
    filterCode: 'PQ',
    filterKey: 'PQ_quota_filter',
  },
  RS: {
    id: 'RS',
    text: 'road_side',
    code: 'RS',
    value: 'Road Side',
    filterLabel: 'road_side',
    filterCode: 'RS',
    filterKey: 'RS_quota_filter',
  },
  YU: {
    id: 'YU',
    text: 'yuva',
    code: 'YU',
    value: 'Yuva',
    filterLabel: 'yuva',
    filterCode: 'YU',
    filterKey: 'YU_quota_filter',
  },
  LB: {
    id: 'LB',
    text: 'lower_berth',
    code: 'LB',
    value: 'Lower Berth',
    filterLabel: 'lower_berth',
    filterCode: 'LB',
    filterKey: 'LB_quota_filter',
  },
  'RC(RAC)': {
    id: 'RC(RAC)',
    text: 'reservation_against_cancellation',
    code: 'RC(RAC)',
    value: 'Reservation Against Cancellation',
    filterLabel: 'reservation_against_cancellation',
    filterCode: 'RC(RAC)',
    filterKey: 'RC(RAC)_quota_filter',
  },
});

export const getQuota = (quotaCode: string): QuotaTypeValue =>
  find(QuotaType, (value) => stringCompare(value.code, quotaCode)) ?? {
    id: quotaCode,
    text: quotaCode,
    code: quotaCode,
    value: quotaCode,
    filterLabel: quotaCode,
    filterCode: quotaCode,
    filterKey: `${quotaCode}_quota_filter`,
  };

export const isTatkalQuota = (selectedQuota: QuotaTypeValue): boolean =>
  !isEmpty(selectedQuota) && stringCompare(selectedQuota?.code, QuotaType.TQ.code);


export const isLadiesQuota = (selectedQuota: QuotaTypeValue): boolean =>
  !isEmpty(selectedQuota) && stringCompare(selectedQuota?.code, QuotaType.LD.code);

export const isSeniorCitizenQuota = (selectedQuota: QuotaTypeValue): boolean =>
  !isEmpty(selectedQuota) && stringCompare(selectedQuota?.code, QuotaType.SS.code);

const quotasForFilter: string[] = [QuotaType.GN.code, QuotaType.TQ.code, QuotaType.LD.code, QuotaType.SS.code];
export const isQuotaFilter = (selectedQuota: QuotaTypeValue) =>
  !isEmpty(selectedQuota.filterCode) && quotasForFilter.some((quota) => stringCompare(quota, selectedQuota.filterCode));

export default QuotaType;
