import find from 'lodash/find';
import isEmpty from 'lodash/isEmpty';
import { stringCompare } from '../TravelerDetails/TravelerDetailsUtils';

interface AvailabilityTypeValue {
  id: string;
  text: string;
  code: string;
  value: string;
  filterId: string;
  filterLabel: string;
  filterCode: string;
  filterKey: string;
}

export type AvailabilityTypeKeys = 'NOT_AVAILABLE' | 'AVL' | 'RAC' | 'WL';

const AvailabilityType: { [key in AvailabilityTypeKeys]: AvailabilityTypeValue } = Object.freeze({
  NOT_AVAILABLE: {
    id: 'NOT_AVAILABLE',
    text: 'Not Available',
    code: '0',
    value: 'Not Available',
    filterId: 'NOT_AVAILABLE',
    filterLabel: 'Not Available',
    filterCode: '0',
    filterKey: 'NOT_AVAILABLE_availability_filter',
  },
  AVL: {
    id: 'AVL',
    text: 'available',
    code: '1',
    value: 'Available',
    filterId: 'AVL',
    filterLabel: 'available_availability_filter',
    filterCode: '1',
    filterKey: 'AVL_availability_filter',
  },
  RAC: {
    id: 'RAC',
    text: 'rac_availability_filter',
    code: '2',
    value: 'RAC',
    filterId: 'RAC',
    filterLabel: 'rac_availability_filter',
    filterCode: '2',
    filterKey: 'RAC_availability_filter',
  },
  WL: {
    id: 'WL',
    text: 'waiting_list_availability_filter',
    code: '3',
    value: 'Waiting List',
    filterId: 'WL',
    filterLabel: 'waiting_list_availability_filter',
    filterCode: '3',
    filterKey: 'WL_availabilty_filter',
  },
});

export const getAvailabilityType = (availabilityTypeCode: string): AvailabilityTypeValue =>
  find(AvailabilityType, (availabilityType) =>
    stringCompare(availabilityType.code, availabilityTypeCode),
  ) ?? {
    id: availabilityTypeCode,
    text: availabilityTypeCode,
    code: availabilityTypeCode,
    value: availabilityTypeCode,
    filterId: availabilityTypeCode,
    filterLabel: availabilityTypeCode,
    filterCode: availabilityTypeCode,
    filterKey: `${availabilityTypeCode}_availability_filter`,
  };

const availabilityTypesForFilter = [
  AvailabilityType.AVL.code,
  AvailabilityType.RAC.code,
  AvailabilityType.WL.code,
];
export const availabilityTypesForQuickFilterAvailable = [
  AvailabilityType.AVL.code,
  AvailabilityType.RAC.code,
];
export const isAvailabilityTypeFilter = (selectedAvailabilityType: AvailabilityTypeValue) =>
  !isEmpty(selectedAvailabilityType?.filterCode) &&
  availabilityTypesForFilter.some((availabilityType) =>
    stringCompare(availabilityType, selectedAvailabilityType?.filterCode),
  );

export default AvailabilityType;
