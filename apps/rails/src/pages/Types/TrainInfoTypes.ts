export interface TrainInfo {
  trainName: string;
  trainNumber: string;
  travelClass: string;
  travelQuota: string;
  platform: string;
  arrivalTime: string;
  arrivalDate: string;
  deptTime: string;
  deptDate: string;
  departStation: string;
  departCity: string;
  departStationCode: string;
  departExpectedPlatformNumber?: string;
  arrivalStation: string;
  arrivalCity: string;
  arrivalStationCode: string;
  arrivalExpectedPlatformNumber?: string;
  isChartPrepared: boolean;
  expectedChartPreparedTime?: string;
  journeyDate: string;
  duration: string;
  pnrNumber: string;
  trainCancelledFlag: boolean;
  trainStatusMsg: string;
}
