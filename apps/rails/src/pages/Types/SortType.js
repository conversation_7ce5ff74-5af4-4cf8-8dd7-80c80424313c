const SortType = Object.freeze({
  DEFAULT: { // cluster train last
    id: 'DEFAULT',
    text: '',
    value: '',
    label: '',
  },
  DurationFastest: {
    id: 'DF',
    text: 'Duration | Fastest',
    value: 'durationShortestFirst',
    label: 'Duration | Fastest',
  },
  DepartureEarliest: {
    id: 'DE',
    text: 'Departure | Earliest',
    value: 'earlyDepartureFirst',
    label: 'Departure | Earliest',
  },
  DepartureLatest: {
    id: 'DL',
    text: 'Departure | Latest',
    value: 'lateDepartureFirst',
    label: 'Departure | Latest',
  },
  ArrivalEarliest: {
    id: 'AE',
    text: 'Arrival | Earliest',
    value: 'earlyArrivalFirst',
    label: 'Arrival | Earliest',
  },
  ArrivalLatest: {
    id: 'AL',
    text: 'Arrival | Latest',
    value: 'lateArrivalFirst',
    label: 'Arrival | Latest',
  },

});

export const sortTypeV2 = [
  {
    id: 'DEFAULT',
    label: 'recommended_ticket_first',
  },
  {
    id: 'DF',
    label: 'shortest_duration',
  },
  {
    id: 'DE',
    label: 'early_departure_first',
  },
  {
    id: 'DL',
    label: 'late_departure_first',
  },
  {
    id: 'AE',
    label: 'early_arrival_first',
  },
  {
    id: 'AL',
    label: 'late_arrival_first',
  },
];

export const SortTypeValues = [SortType.DurationFastest,
                               SortType.DepartureEarliest,
                               SortType.DepartureLatest,
                               SortType.ArrivalEarliest,
                               SortType.ArrivalLatest];

export default SortType;
