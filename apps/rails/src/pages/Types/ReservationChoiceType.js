
const ReservationChoiceType = Object.freeze({
  BookIfAllBerthsAllottedInSameCoach: {
    key: 'Book,only if all berths are allotted in same coach',
    value: '1',
  },
  BookIfAtLeastOneLowerBerthsAllotted: {
    key: 'Book, only if at least 1 lower berth is allotted',
    value: '2',
  },
  BookIfTwoLowerBerthsAllotted: {
    key: 'Book, only if 2 lower berths are allotted',
    value: '3',
  },
  None: {
    key: 'None',
    value: '99',
  },

});

export const ReservationChoiceValues = [ReservationChoiceType.None.key,
                                        ReservationChoiceType.BookIfAllBerthsAllottedInSameCoach.key,
                                        ReservationChoiceType.BookIfAtLeastOneLowerBerthsAllotted.key,
                                        ReservationChoiceType.BookIfTwoLowerBerthsAllotted.key];
export const ReservationChoiceValuesWithoutLowerBerth = [ReservationChoiceType.None.key,
                                                         ReservationChoiceType.BookIfAllBerthsAllottedInSameCoach.key];

export default ReservationChoiceType;
