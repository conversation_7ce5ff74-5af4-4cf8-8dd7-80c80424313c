import find from 'lodash/find';
import { stringCompare } from '../TravelerDetails/TravelerDetailsUtils';

interface ClassTypeValue {
  code: string;
  value: string;
}

interface ClassType {[key: string]: ClassTypeValue}

const ClassTypes: ClassType = Object.freeze({
  OneA: {
    code: '1A',
    value: 'first_class_ac',
  },
  TwoA: {
    code: '2A',
    value: 'two_tier_ac',
  },
  ThreeA: {
    code: '3A',
    value: 'three_tier_ac',
  },
  SL: {
    code: 'SL',
    value: 'sleeper',
  },
  TwoS: {
    code: '2S',
    value: 'second_sitting',
  },
  CC: {
    code: 'CC',
    value: 'ac_chair_car',
  },
  EA: {
    code: 'EA',
    value: 'executive_anubhuti',

  },
  FC: {
    code: 'FC',
    value: 'first_class',
  },
  ThreeE: {
    code: '3E',
    value: 'ac_three_tier_economy',
  },
  EC: {
    code: 'EC',
    value: 'executive_chair_car',
  },
  'UR/GEN': {
    code: 'UR/GEN',
    value: 'unreserved_or_general',
  },
});

const AcClasses = [
  ClassTypes.OneA.code,
  ClassTypes.TwoA.code,
  ClassTypes.ThreeA.code,
  ClassTypes.ThreeE.code,
  ClassTypes.CC.code,
  ClassTypes.EA.code,
  ClassTypes.EC.code,
];

export const getClassType = (classType: string): ClassTypeValue =>
  find(ClassTypes, (value) => stringCompare(value.code, classType)) ?? {
    code: classType,
    value: classType,
  };

export const isAcClass = (classType: string): boolean => AcClasses.some((acClass) => stringCompare(acClass, classType));
