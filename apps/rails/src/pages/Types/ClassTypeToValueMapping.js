import { _label } from '../../vernacular/AppLanguage';

const ClassTypeToDescription = Object.freeze({
  '1A': _label('first_class_ac'),
  '2A': _label('two_tier_ac'),
  '3A': _label('three_tier_ac'),
  SL: _label('sleeper'),
  '2S': _label('second_sitting'),
  CC: _label('ac_chair_car'),
  EA: _label('executive_anubhuti'),
  FC: _label('first_class'),
  '3E': _label('ac_three_tier_economy'),
  EC: _label('executive_chair_car'),
  'UR/GEN': _label('unreserved_or_general'),
});

export default ClassTypeToDescription;
