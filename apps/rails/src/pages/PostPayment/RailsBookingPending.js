import React from 'react';
import {Text, View } from 'react-native';
import PropTypes from 'prop-types';
import NonSuccess from './NonSuccessCard';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import ManageBooking from './BookingStatus/ManageBookingButton';
import {getDataFromAsynStorage, getStatusWithBookingId, RAILS_MMT_ID} from '../../Utils/RailsConstant';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { isMweb } from '../../Utils/device';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

class ReactBookingPending extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      data: null,
      totalCollectibleAmount: '',
    };
  }

  async componentDidMount() {
    let mmtId = null;
    if (isMweb()) {
      mmtId = this.props.mmtId;
    } else {
      mmtId = await getDataFromAsynStorage(RAILS_MMT_ID);
    }
    const response = await getStatusWithBookingId(mmtId);
    const data = {
      bookingStatusMessage: response.data.bookingStatusDetails.bookingStatusMessage,
      bookingStatusSubMessage: response.data.bookingStatusDetails.bookingStatusSubMessage,
      bookingInfo: response.data.bookingInfo,
      bookingId: mmtId,
      status: response.status,
      colorArray: ['#f3d452', '#f09819'],
    };
    const totalCollectibleAmount = response.data.priceBreakUp.totalAmount.substring(1);

    this.setState({
      data,
      totalCollectibleAmount,
    });
  }

  render() {
    return (
      <View style={{ flex: 1, flexDirection: 'column', justifyContent: 'space-between' }} testID={this.props?.id}>
        <View style={{backgroundColor: colors.grayBg, flex: 1}} >
          {this.state.data && <NonSuccess data={this.state.data} />}
          <Card style={{
            marginVerical: 0, marginHorizontal: 0, paddingHorizontal: 16, paddingVertical: 10,
          }}
          >
            <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text style={[{fontSize: 22, lineHeight: 26, color: colors.defaultTextColor}, fontStyle('light'), getLineHeight(22)]}
              >{_label('total_payment')}
              </Text>
              <RupeeText style={[{fontSize: 22, lineHeight: 26, color: colors.black}, fontStyle('heavy'), getLineHeight(22)]}
              >{numAppendedWithRuppeeSymbol(this.state.totalCollectibleAmount)}
              </RupeeText>
            </View>
            <Text style={[{alignSelf: 'flex-end',  fontSize: 10, lineHeight: 18, color: colors.defaultTextColor}, fontStyle('regular'), getLineHeight(10)]}
            >{_label('total_amount')}
            </Text>
          </Card>
        </View>
        <ManageBooking id={`${this.props?.id}_manageBooking`} />
      </View>
    );
  }
}

export default ReactBookingPending;

ReactBookingPending.propTypes = {
  mmtId: PropTypes.string,
  id: PropTypes.string,
};
