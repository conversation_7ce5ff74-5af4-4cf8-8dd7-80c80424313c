import React from 'react';
import { connect } from 'react-redux';
import {View, Text} from 'react-native';
import { updateUsername } from '@mmt/rails/src/pages/User/UserVerification/UserVerificationActions';
import {trackRailPaymentDone} from '../../../railsAnalytics';
import PreRedirectLoader from '../PreRedirectLoader';
import {getIrctcBookingParams} from '../IrctcRedirect';
import {getQueryParamsFromUrl} from '@mmt/legacy-commons/Helpers/misc';

import {
  setDataToAsyncStorage,
  RAILS_MMT_ID,
} from '../../../Utils/RailsConstant';
import { timer } from '../../NeedHelp/Shared';
import { getStoredConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { IRCTC_FORM_URL } from 'apps/rails/src/RailsConfig';
import PropTypes from 'prop-types';

class IrctcWebPage extends React.Component {
  constructor(props) {
    super(props, 'confirmBooking');
    this.state = {
      loading: true,
      wsloginId: null,
      bookingId: null,
      callBackURL: null,
      errorMsg: '',
      irctcFormUrl: IRCTC_FORM_URL,
    };
    trackRailPaymentDone();
  }

  async componentDidMount() {
    try {
      try {
        let queries = getQueryParamsFromUrl(window.location.href);
        if (queries && queries.irctcUserId && queries.bookingRef){
          await setDataToAsyncStorage(RAILS_MMT_ID, queries.bookingRef);
        }
      } catch (e){
        console.log(e);
      }

      const bookingParams = await getIrctcBookingParams();
      const {
        wsloginId,
        wsTxnId: bookingId,
        wsReturnUrl: callBackURL,
      } = bookingParams;
      this.props.setUserNameToRedux(wsloginId);
      // if(!_isEmpty(bookingParams.errorDetails)){
      //   this.setState({
      //     loading:false,
      //     errorMsg:bookingParams.errorDetails && bookingParams.errorDetails.errorMessage
      //   })
      // }
      // else
      const railsMasterConfig = getStoredConfigStore(configKeys.RAILS_MASTER_CONFIG);
      const { irctcFormUrl } = railsMasterConfig || {};
      if (wsloginId && bookingId && callBackURL) {
        this.setState({
          wsloginId,
          bookingId,
          callBackURL,
          loading: false,
          irctcFormUrl: irctcFormUrl,
        });
      }
      const form = document.getElementById('irctcForm');
      setTimeout(() => {
        if (form && form.submit) {
          timer.updateTimer();
          form.submit();
        }
      }, 5000);
    } catch (e) {
      console.log('error is ', e);
    }
  }

  render() {
    // if (this.state.loading) {
    //   return (
    //     <View style={{flex: 1}}>
    //       <PreRedirectLoader />
    //     </View>
    //   );
    // }
    if (this.state.errorMsg){
      return (
        <View style={{flex: 1}}>
          {
            <Text> {this.state.errorMsg}</Text>
          }
        </View>
      );
    }
    const {
      wsloginId, bookingId, callBackURL, irctcFormUrl,
    } = this.state;
    // todo Rajesh to revisit it
    const loaderContainerStyle = {
      position: 'absolute', top: '25%', width: '100%',
    };
    return (
      <div>
        <View style={loaderContainerStyle}>
          <PreRedirectLoader />
        </View>
        <form
          id="irctcForm"
          action={irctcFormUrl}
          method="post"
          encType="application/x-www-form-urlencoded"
        >
          <input id="loginId" type="hidden" name="wsloginId" value={wsloginId} />
          <input id="taxId" type="hidden" name="wsTxnId" value={bookingId} />
          <input id="wsReturnUrl" type="hidden" name="wsReturnUrl" value={callBackURL} />
        </form>
      </div>
    );
  }
}

IrctcWebPage.propTypes = {
  setUserNameToRedux: PropTypes.func,
};

const mapDispatchToProps = (dispatch) => ({
  setUserNameToRedux: (userName) => {
    dispatch(updateUsername(userName));
  },
});

export default connect(null, mapDispatchToProps)(IrctcWebPage);
