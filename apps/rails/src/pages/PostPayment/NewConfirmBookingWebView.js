import React from 'react';
import {
  Image,
  Keyboard,
  Text,
  View,
  NativeModules,
  BackHandler,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import {WebView} from 'react-native-webview';
import PropTypes from 'prop-types';
import promiseAlert from '@mmt/legacy-commons/Common/Components/Alert/promiseAlert';
import {trackRailIrctcPasswordAfterPaymentLoad, trackNeedHelpPageEvent, trackIrctcPopupError} from '../../railsAnalytics';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import closeIcon from '@mmt/legacy-assets/src/ic_close_blue.webp';
import CtaButton from '../User/Common/CtaButton';
import ViewTextCard from './ViewTextCard';
import ViewCaptchaCard from './ViewCaptchaCard';
import RailsRetrievePasswordModal from '../Common/RailsRetrievePasswordModal';
import {getMmtId, RAILS_CALL_BACK_URL_PWA_SUCCESS} from './PostPaymentUtils';
import connectVernalar from './connectVernalar';
import NeedHelp from './Components/NeedHelp';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { _label } from '../../vernacular/AppLanguage';
import IrctcPasswordReader from '../irctc/irctcPasswordReader';
import PreRedirectLoader from './PreRedirectLoader';
import IrctcRedirectionFailed from './IrctcRedirectionFailed';
import { getCanReportIRCTCError, getIfAutoReloadIRCTC, getIrctcNewReloadButton, getRailsAgentTxnErrorRetry } from '../../RailsAbConfig';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { IRCTC } from '../../RailsOmnitureTracker';
import { GFT_PDT_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/GFT/PdtGftConstants';
import ErrorBoundary from 'apps/rails/src/pages/GenericErrorBoundary';

let CALLBACK_URL_INTERCEPTION_SUCCEDED = false;

class NewConfirmBookingWebView extends React.Component {
  webViewRef = React.createRef();
  constructor(props) {
    super(props); // FIXME This should be moved to parent component
    this.state = {
      canGoBack: false,
      password: '',
      captcha: '',
      showText: false,
      showPasswordReminderModal: false,
      irctcUserName: null,
      elementClicked: 'j_password',
      isLoading: true,
      webViewLoad: true,
      minHeight: 300,
      openNeedHelp: false,
      showPasswordTip: false,
      retryDelayCount: 0,
      isRetrying: false,
      pageLoadComplete: false,
      irctcPageHasError: false,
      autoReloadCount: 0,
    };
    this.injected = false;
    this.irctcPasswordReader = new IrctcPasswordReader();
    this.irctcPassword = '';
    this.retryDelayConfig = [];
  }

  async UNSAFE_componentWillMount() {
    this.irctcPassword = await this.irctcPasswordReader.getPassword();
    CALLBACK_URL_INTERCEPTION_SUCCEDED = false;

    const irctcUserName = this.props?.irctcUserName;
    this.setState({
      irctcUserName,
    });

    this.keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      this._keyboardDidShow,
    );
    this.keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      this._keyboardDidHide,
    );
  }

  _keyboardDidShow = () => {
    this.setState({
      showText: true,
    });
  };

  _onLoadEnd = () => {
    this.setState({webViewLoad: false });
    setTimeout(() => {
      if (NativeModules.GenericModule.resetSoftInputMode) {
        NativeModules.GenericModule.resetSoftInputMode();
      }
    }, 1000);
    this.props.onLoad();
  };

  _keyboardDidHide = () => {
    this.setState({
      showText: false,
    });
  };
  componentDidMount() {
    this.backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      this.onHardBackPress,
    );
    this.retryDelayConfig = getRailsAgentTxnErrorRetry();
  }

  componentWillUnmount() {
    this.keyboardDidShowListener?.remove();
    this.keyboardDidHideListener?.remove();
    this.backHandler?.remove();
  }

  onHardBackPress = () => {
    const {canGoBack} = this.state;
    if (canGoBack && this.webViewRef.current) {
      this.webViewRef.current.goBack();
    } else if (!canGoBack) {
      if (this.props.railsEnableHelpSectionOnIRCTCWebpage) {
        this.setState({openNeedHelp:  !this.state.openNeedHelp});
      } else {
        this._alertUser();
      }
    }
    return true;
  };

  _alertUser = async () => {
    const action = await promiseAlert('', 'Are you sure you want to cancel the booking?', 'Yes, cancel booking', 'No, don\'t cancel');
    if (action === 'Yes, cancel booking') {
      const mmtId = await getMmtId();
      this.props.callbackFunc(`${RAILS_CALL_BACK_URL_PWA_SUCCESS + mmtId}?bookingStatus=F`);
    }
  };

  _checkIfCallbackUrl = (url) => {
    for (let i = 0; i < this.props.callbackUrl.length; i++) {
      if ((url.startsWith('http') || url.startsWith('https')) &&
        (url.indexOf(this.props.callbackUrl[i]) > -1 ||
          url.startsWith(this.props.callbackUrl[i]))) {
        CALLBACK_URL_INTERCEPTION_SUCCEDED = true;
        return true;
      }
    }
    return false;
  };

  _onShouldStartLoadWithRequest = (request) => {
    if (request.loading) {
      this.setState({isLoading: true});
    } else {
      this.setState({isLoading: false});
    }
    if (!CALLBACK_URL_INTERCEPTION_SUCCEDED && this._checkIfCallbackUrl(request.url)) {
      if (this.webViewRef.current) {
        this.webViewRef.current.postMessage('');
        this.webViewRef.current.stopLoading();
        this.props.callbackFunc(request.url);
        if (request?.url?.includes('RETRY_F')){
          CALLBACK_URL_INTERCEPTION_SUCCEDED = false;
        }
      }
      return false;
    }
    return !CALLBACK_URL_INTERCEPTION_SUCCEDED;
  };

  _onNavigationStateChange = (navState) => {
    if (navState.url.includes('irctc.co.in/eticketing/home') && this.checkForPasswordAlert() ){
      this.setState({
        showPasswordTip: true,
      });
    }
    else {
      this.setState({
        showPasswordTip: false,
      });
    }
    this.setState({
      canGoBack: navState.canGoBack,
    });
    this._onShouldStartLoadWithRequest(navState);
  };

  checkForPasswordAlert = ()=>{
    const {password} = this.state;
    return (
      password.includes('O') ||
      password.includes('0') ||
      password.includes('I') ||
      password.includes('l')
    );
  };

  _injectCode = () => {
    if (this.injected) {return;}
    const {labels} = this.props;
      const script = `
        setTimeout(function() {

          var irctcErrors = document.getElementById("loginsErr");
          if (irctcErrors) {
            var postData = {
              element: 'irctc_popup_error',
              data: irctcErrors.innerHTML
            };
            window.ReactNativeWebView.postMessage(JSON.stringify(postData));
          }

          var errorInput = document.getElementById("error");
          if (errorInput && errorInput.value.match(/not found/i)) {
            var postData = {
              element: 'agent_transaction_id_error',
              data: errorInput.value
            };
            window.ReactNativeWebView.postMessage(JSON.stringify(postData));
            return;
          }
          var isIrctcForm = document.getElementById("irctcForm");
          if (isIrctcForm) {
            var postData = {
              element: 'irctc_form',
              data: ''
            }
            window.ReactNativeWebView.postMessage(JSON.stringify(postData));
          }

          var inputs = document.getElementsByTagName("input");
          if (inputs.length) {
            var postData = {
              element: 'page_load',
              data: ''
            }
            window.ReactNativeWebView.postMessage(JSON.stringify(postData));
          }

          document.getElementById('loginCancel').type = 'hidden';
          document.getElementById('loginForget').type = 'hidden';
          document.getElementById('loginbuttonw').type = 'hidden';
          document.getElementsByName('j_password')[0].value = '${this.irctcPassword}';
          document.getElementsByName('j_password')[0].addEventListener('input', function(event) {
              var postData = {
                  element: 'j_password',
                  data: event.currentTarget.value
              };
              window.ReactNativeWebView.postMessage(JSON.stringify(postData));
          });
          document.getElementsByName('j_captcha')[0].setAttribute('type', 'password');
          document.getElementsByName('j_captcha')[0].addEventListener('input', function(event) {
              var postData = {
                  element: 'j_captcha',
                  data: event.currentTarget.value
              };
              window.ReactNativeWebView.postMessage(JSON.stringify(postData));
          });
          document.getElementsByName('j_password')[0].addEventListener('focus', function(event) {
              var postData = {
                  element: 'j_password',
                  data: event.currentTarget.value
              };
              window.ReactNativeWebView.postMessage(JSON.stringify(postData));
          });

          document.getElementsByName('j_captcha')[0].addEventListener('focus', function(event) {
              var postData = {
                  element: 'j_captcha',
                  data: event.currentTarget.value
              };
              document.getElementsByName('j_captcha')[0].setAttribute('type', 'text');
              window.ReactNativeWebView.postMessage(JSON.stringify(postData));
          });

          var currentFunction = document.getElementById('loginCancel').onclick;
          document.getElementById('loginCancel').onclick = function() {
              var retVal = confirm('${labels.areYouSure}');
              if (retVal === true) {
                  currentFunction();
              }
          };
          var currentFunction1 = document.getElementById('loginbuttonw').onclick;
          document.getElementById('loginbuttonw').onclick = function() {
                var abcd = validateloginDetails();
                if (abcd === true) {
                  var postData = {
                  element: 'loginbutton',
                  data: ''
                };
                window.ReactNativeWebView.postMessage(JSON.stringify(postData));
              }
          };
        }, 1000);
        true;
      `;
    this.webViewRef.current.injectJavaScript(script);
    trackRailIrctcPasswordAfterPaymentLoad();
  };

  logPopupError = (plainErrorMessage, rawErrorMessage) => {
    try {
      trackIrctcPopupError(plainErrorMessage);
      if (getCanReportIRCTCError()) {
        RailsModule?.logFirebaseError(`irctc_popup_error ${rawErrorMessage}`);
      }
    } catch (error) {
      console.error('Error while logging irctc error');
    }
  };

  _onMessage = (event) => {
    let messageData;

    try {
      messageData = JSON.parse(event.nativeEvent.data);
      if (messageData.element === 'page_load') {
        this.setState({
          pageLoadComplete: true,
          irctcPageHasError: false,
          retryDelayCount: 0,
          delay: 0,
          isRetry: false,
        });
        this.props.trackOmintureEvent({
          event: 'success',
        });
        this.props.updateHeaderDetail({
          subTitle: _label('enter_password_captcha_now'),
        });
        if (this.state.autoReloadCount > 0){
          trackIrctcPopupError(IRCTC.IRCTC_LOAD_SUCCESS + this.state.autoReloadCount);
        }
      }
      if (messageData.element === 'irctc_form') {
        this.setState({
          irctcPageHasError: false,
          pageLoadComplete: false,
          retryDelayCount: 0,
        });
      }
      if (messageData.element === 'agent_transaction_id_error') {
        if (messageData.data !== 'no-error') {
          const retryDelay = this.retryDelayConfig[this.state.retryDelayCount];
          if (retryDelay) {
            this.props.setRetryTimer({
              delay: retryDelay,
              retryCount: this.state.retryDelayCount,
            });
            this.setState({
              isRetrying: true,
              retryDelay,
              retryDelayCount: this.state.retryDelayCount + 1,
              pageLoadComplete: false,
              irctcPageHasError: true,
            });
          } else {
            this.setState({
              pageLoadComplete: true,
              irctcPageHasError: true,
              retryDelayCount: 0,
              delay: 0,
              isRetry: false,
            });
            this.props.trackOmintureEvent({
              event: 'failed',
            });
            this.props.updateHeaderDetail({
              show: false,
            });
            this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.IRCTC_ERROR, messageData);
          }
        } else {
          this.setState({
            pageLoadComplete: true,
          });
        }
      }
      if (messageData.element === 'j_password') {
        this.setState({
          elementClicked: messageData.element,
          password: messageData.data,
        });
      } else if (messageData.element === 'j_captcha') {
        this.setState({
          elementClicked: messageData.element,
          captcha: messageData.data,
        });
      } else if (messageData.element === 'loginbutton') {
        this.setState({
          elementClicked: messageData.element,
          isLoading: true,
        });
        this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.COMPLETE_BOOKING_CLICKED);
      } else if (messageData?.element === 'irctc_popup_error' && messageData?.data?.includes('span')){
          try {
            let plainErrorMessage = messageData.data.replace(/<[^>]+>/g, '');
            plainErrorMessage = plainErrorMessage?.replace(/[\n\t]/g,'');
            plainErrorMessage = plainErrorMessage?.replace(/\s/g, '_');
            if (plainErrorMessage){
             this.logPopupError(`TRAINS:IRCTC_POPUP_ERROR_${plainErrorMessage}`,messageData.data);
            }
          const errorDetails = {
            errorCode: messageData?.element,
            errorMessage: plainErrorMessage,
          };
          this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.IRCTC_POPUP_ERROR, errorDetails);
          } catch (error) {
            console.error('Error while logging irctc error');
          }
      }
    } catch (error) {
      console.log(error);
    }
  };

  autoReloadWebView = (syntheticEvent) => {
    if (getIfAutoReloadIRCTC() && this.state.autoReloadCount < 3){
      this.webViewRef.current?.reload();
      this.setState({autoReloadCount : this.state.autoReloadCount + 1});
    } else {
      if (getIfAutoReloadIRCTC()){
        trackIrctcPopupError(IRCTC.IRCTC_LOAD_FAILED);
      }
      const { nativeEvent } = syntheticEvent;
      const plainErrorMessage = nativeEvent?.description?.replace(/\s/g, '_');
      if (plainErrorMessage){
        this.logPopupError(`${IRCTC.IRCTC_LOAD_ERROR}_${plainErrorMessage}`,JSON.stringify(nativeEvent));
      }
    }
    this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.AUTO_RELOAD_WEBVIEW);
  };

  _onWebviewError = (syntheticEvent) => {
    try {
      this.autoReloadWebView(syntheticEvent);
    } catch (error) {
      console.error('Error while logging irctc error');
    }
  };

  _cancelBooking = () => {
    this.webViewRef.current.injectJavaScript('document.getElementById("loginCancel").click();');
  };

  _needHelpCancel = () => {
    if (this.props.railsEnableHelpSectionOnIRCTCWebpage) {
      trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_needHelpClicked');
      this.setState({openNeedHelp: true});
    } else {
      this._cancelBooking();
    }
  };

  _forgotPassword = () => {
    this._enablePasswordReader();
    this.setState({
      showPasswordReminderModal: true,
    });
    this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.GET_NEW_PASSWORD_CLICKED);
  };

  _enablePasswordReader = () => {
    this.irctcPasswordReader.subscribeIrctcSms((irctcPassword) => {
      this.webViewRef.current.injectJavaScript?.(
        `document.getElementsByName('j_password')[0].value = '${irctcPassword}'`,
      );
    });
  };

  _completeBooking = () => {
    this.webViewRef.current.injectJavaScript('document.getElementById("loginbuttonw").click();');
  };

  _focusCaptchaTextInput = () => {
    this.webViewRef.current.injectJavaScript('document.getElementsByName("j_captcha")[0].focus();');
  };

  _blurCaptchaTextInput = () => {
    this.webViewRef.current.injectJavaScript('document.getElementsByName("j_captcha")[0].blur();');
  };

  _dismissModal = () => {
    this.setState({
      showPasswordReminderModal: false,
    });
  };

  _refreshCaptcha = () => {
    this.injected = true;
    this.webViewRef.current.injectJavaScript('document.getElementById("refresh").click();');
    this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.REFRESH_CAPTCHA);
  };

  _onLayout = ({nativeEvent}) => {
    this.setState({
      minHeight: nativeEvent.layout.height,
    });
  };

  displaySpinner() {
    return (
      <View style={webViewStyle.activityContainer}>
        <Spinner size={30} color={colors.primary} />
        <Text style={[webViewStyle.loadingText, fontStyle('regular')]}>
          {_label('loading')}
        </Text>
      </View>
    );
  }

  render() {
    const {htmlCode, labels, enableRefreshButton, disableRefreshButton, remainingTime} = this.props;
    const pageHasLoaded = this.state.pageLoadComplete && !this.props.loadingBookingDetails;
    const showErrorOnCompletion = this.state.pageLoadComplete && this.state.irctcPageHasError;
    if (showErrorOnCompletion) {
      return <IrctcRedirectionFailed />;
    }

    return (
      <ErrorBoundary>
      <View
        onLayout={this._onLayout}
        style={[webViewStyle.flex1, {
        justifyContent: 'flex-end', overflow: 'hidden',
      }]}
      >
        <PreRedirectLoader
          isRetry={this.state.isRetrying}
          delayInSeconds={this.state.retryDelay}
          isLoading={!pageHasLoaded}
          />
        {
          !this.props.loadingBookingDetails ? (
            <View  style={webViewStyle.flex1}>
              <WebView
                ref={this.webViewRef}
                startInLoadingState={true}
                renderLoading={() => this.displaySpinner()}
                onNavigationStateChange={this._onNavigationStateChange}
                onShouldStartLoadWithRequest={this._onShouldStartLoadWithRequest}
                source={{html: htmlCode}}
                style={{
                  flex: 1,
                  flexGrow: 1,
                  minHeight: this.state.minHeight,
                }}
                javaScriptEnabled
                onLoad={
                  !this.injected
                    ? this._injectCode
                    : () => {
                        // Intentionally empty - no action needed when already injected
                      }
                }
                onMessage={this._onMessage}
                onLoadEnd={this._onLoadEnd}
                onError={this._onWebviewError}
              />
            </View>
            ) : (
          this.displaySpinner()
        )}
        {
          pageHasLoaded && (
            <>
              {!this.state.isLoading && !this.state.webViewLoad && !this.state.showPasswordReminderModal &&
                <View style={[webViewStyle.passwordContainer, this.state.showText && webViewStyle.bottom40]}>
                  <PasswordAndGetHelpSection
                    labels={labels}
                    cancelBooking={this._cancelBooking}
                    forgotPassword={this._forgotPassword}
                    railsEnableHelpSectionOnIRCTCWebpage={this.props.railsEnableHelpSectionOnIRCTCWebpage}
                    _needHelpCancel={this._needHelpCancel}
                  />
                </View>
              }
              <View style={webViewStyle.overlay}>
                <View>
                  {this.state.showText && this.state.elementClicked === 'j_password' &&
                  <ViewTextCard
                    labels={labels}
                    inputText={this.state.password}
                    onClick={this._focusCaptchaTextInput}
                  />
                  }

                  {this.state.showText && this.state.elementClicked === 'j_captcha' &&
                  <ViewCaptchaCard
                    labels={labels}
                    inputText={this.state.captcha}
                    refreshCaptcha={this._refreshCaptcha}
                    onClick={this._blurCaptchaTextInput}
                  />
                  }
                </View>

                {!this.state.showText && !this.state.isLoading && !this.state.webViewLoad && <>
                {  this.state.showPasswordTip &&
                  <View style={webViewStyle.passwordTipConatiner}>
                    <Text style={webViewStyle.passwordAlertText}>
                      {_label('irctc_password_tip')}
                    </Text>
                  </View>
                }
                <IRCTCLoginPageCTA
                  labels={labels}
                  completeBooking={this._completeBooking}
                  disableRefreshButton={disableRefreshButton}
                  onRefreshClick={this.props.onRefreshClick}
                  remainingTime={remainingTime}
                />
                </>
                }
              </View>
            </>
          )
        }

        {this.state.showPasswordReminderModal &&
        <RailsRetrievePasswordModal
          dismiss={this._dismissModal}
          irctcUserName={this.state.irctcUserName}
            logGftPdtEventsToPdt={this.props.logGftPdtEventsToPdt}
        />
        }
        {this.state.openNeedHelp &&
          <NeedHelp
            onRefreshClick={this.props.onRefreshClick}
            irctcUserId={this.props.irctcUserId}
            railsEnableHelpSectionOnIRCTCWebpage={this.props.railsEnableHelpSectionOnIRCTCWebpage}
            closeNeedHelp={() => { this.setState({openNeedHelp: false}); }}
            cancelBooking={this._cancelBooking}
            callbackFunc={this.props.callbackFunc}
            enableRefreshButton={enableRefreshButton}
            disableRefreshButton={disableRefreshButton}
            enablePasswordReader={this._enablePasswordReader.bind(this)}
            logGftPdtEventsToPdt={this.props.logGftPdtEventsToPdt}
          />
        }
      </View>
      </ErrorBoundary>
    );
  }
}

NewConfirmBookingWebView.propTypes = {
  callbackUrl: PropTypes.array.isRequired,
  callbackFunc: PropTypes.func.isRequired,
  htmlCode: PropTypes.string,
  jsCode: PropTypes.string,
  onLoad: PropTypes.func.isRequired,
  railsEnableHelpSectionOnIRCTCWebpage: PropTypes.bool,
  irctcUserId: PropTypes.string.isRequired,
  onRefreshClick: PropTypes.func.isRequired,
  irctcUserName: PropTypes.string,
  trackOmintureEvent: PropTypes.func,
  updateHeaderDetail: PropTypes.func,
  setRetryTimer: PropTypes.func,
  logGftPdtEventsToPdt: PropTypes.func,
  labels: PropTypes.shape({
    areYouSure: PropTypes.string,
    completeBooking: PropTypes.string,
    getNewPassword: PropTypes.string,
    cancelBooking: PropTypes.string,
  }),
  enableRefreshButton: PropTypes.bool,
  disableRefreshButton: PropTypes.bool,
  remainingTime: PropTypes.number,
  loadingBookingDetails: PropTypes.bool,

};

NewConfirmBookingWebView.defaultProps = {
  htmlCode: '<html><body>Something is wrong, please try after sometime.</body></html>',
  jsCode: '',
  railsEnableHelpSectionOnIRCTCWebpage: false,
};

NewConfirmBookingWebView.navigationOptions = {
  header: null,
};


export const IRCTCLoginPageCTA = ({
  completeBooking,
  labels,
  onRefreshClick,
  disableRefreshButton,
  remainingTime,
}) => {
  const showNewReloadButton = getIrctcNewReloadButton();

  const handleRefreshClick = () => {
    trackNeedHelpPageEvent('rails_irctc_reload_new');
    onRefreshClick();
  };

  return (
    <View style={webViewStyle.container}>
      { showNewReloadButton &&
        <View style={webViewStyle.newReoladButton}>
            <Text style={[webViewStyle.latoBold14, fontStyle('regular'), getLineHeight(14), webViewStyle.newPageText]}>
              {_label('page_didnt_load')}
            </Text>
            <TouchableRipple disabaled={disableRefreshButton} onPress={handleRefreshClick}>
              <Text style={[webViewStyle.latoBold14, fontStyle('bold'), getLineHeight(14), {color: disableRefreshButton ? colors.grey23 : colors.azure}]}>
                <Text>{_label('reload')}</Text>
                {disableRefreshButton && <Text style={fontStyle('regular')}>{`  ${remainingTime}s`}</Text>}
              </Text>
            </TouchableRipple>
        </View>
      }
      <View style={webViewStyle.ctaContainer}>
        <CtaButton
          label={labels.completeBooking}
          onPress={() => completeBooking()}
          btnStyle={webViewStyle.ctaButton}
          textStyle={[webViewStyle.ctaText, fontStyle('black'), getLineHeight(16)]}
        />
      </View>
    </View>
  );
};

IRCTCLoginPageCTA.propTypes = {
  labels: PropTypes.object.isRequired,
  cancelBooking: PropTypes.func.isRequired,
  forgotPassword: PropTypes.func.isRequired,
  completeBooking: PropTypes.func.isRequired,
  railsEnableHelpSectionOnIRCTCWebpage: PropTypes.bool.isRequired,
  _needHelpCancel: PropTypes.func.isRequired,
  onRefreshClick: PropTypes.func,
  disableRefreshButton: PropTypes.bool,
  remainingTime: PropTypes.number,
};

const PasswordAndGetHelpSection = ({
  cancelBooking,
  forgotPassword,
  labels,
  railsEnableHelpSectionOnIRCTCWebpage,
  _needHelpCancel,
}) => {

  const newTextContainerStyle = railsEnableHelpSectionOnIRCTCWebpage ? {
    justifyContent: 'center',
    alignItems: 'center',
    height: 32,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.azure,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 18,
  } : {
    justifyContent: 'center',
    alignItems: 'center',
  };

  const containerStyle = railsEnableHelpSectionOnIRCTCWebpage ? {
    flexDirection: 'column-reverse', justifyContent: 'space-between', alignItems: 'flex-end',
  } : {
    justifyContent: 'space-between',
  };

  return (
    <View style={[webViewStyle.flexDirectionRow, containerStyle]}>
      <TouchableRipple
        onPress={() => {
          if (railsEnableHelpSectionOnIRCTCWebpage) {
            _needHelpCancel();
          } else {
            cancelBooking();
          }
        }}
      >
        <View style={[webViewStyle.flexDirectionRow, newTextContainerStyle]}>
          {!railsEnableHelpSectionOnIRCTCWebpage &&
            <Image source={closeIcon} style={webViewStyle.closeIcon} />
          }
          <Text style={[webViewStyle.latoBold14, fontStyle('bold'), getLineHeight(14), {color: colors.azure}]}>
            {railsEnableHelpSectionOnIRCTCWebpage ? _label('need_help') : labels.cancelBooking}
          </Text>
        </View>
      </TouchableRipple>
      <TouchableRipple
        onPress={() => forgotPassword()}
      >
        <View style={[webViewStyle.flexDirectionRow, newTextContainerStyle]}>
          <Text style={[webViewStyle.latoBold14, fontStyle('bold'), getLineHeight(14), {color: colors.azure}]}>
            {labels.getNewPassword}
          </Text>
        </View>
      </TouchableRipple>
    </View>
  );
};

PasswordAndGetHelpSection.propTypes = {
  cancelBooking: PropTypes.func,
  forgotPassword: PropTypes.func,
  labels: PropTypes.object,
  railsEnableHelpSectionOnIRCTCWebpage: PropTypes.bool,
  _needHelpCancel: PropTypes.func,
};

export const webViewStyle = {
  closeIcon: {
    width: 14,
    height: 14,
    marginRight: 4,
  },
  fpIcon: {
    width: 24,
    height: 24,
    marginRight: 4,
  },
  flex1: {
    flex: 1,
  },
  marginTop20: {
    marginTop: 20,
  },
  flexDirectionRow: {
    flexDirection: 'row',
  },
  marginTop32: {
    marginTop: 32,
  },
  latoRegular12: {
    fontSize: 12,
    color: colors.defaultTextColor,
  },
  latoBold18: {
    fontSize: 18,
    color: colors.black,
  },
  paddingLeft16: {paddingLeft: 16},
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  latoBold14: {
    fontSize: 14,
    color: colors.black,
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 50,
    height: 50,
  },
  passwordImage: {
    height: 24,
    width: 24,
  },
  overlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    marginTop: 34,
  },
  inputTextShowStyle: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.white,
  },
  inputTextHideStyle: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.lightTextColor,
  },
  latoBold10: {
    fontSize: 10,
    color: colors.white,
  },
  passwordTipConatiner:{
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  passwordAlertText:{
    color:'#9c4548',
  },
  customLoader: {
    zIndex: 5,
    width: '100%',
    height: '100%',
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  newReoladButton: {
    flexDirection: 'row',
    alignSelf: 'center',
  },
  newPageText: {
    color: colors.textGrey,
    marginRight: 8,
  },
  passwordContainer: {
    marginHorizontal: 16,
    bottom: 120,
  },
  bottom40: {
    bottom: 40,
  },
  ctaContainer: {
    marginTop: 33,
  },
  ctaButton: {
    borderWidth: 0,
    borderRadius: 8,
    height: 44,
    paddingHorizontal: normalisePx(0),
  },
  ctaText: {
    fontWeight: 'bold',
    color: colors.white,
    fontSize: 16,
  },
  activityContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: -60,
    left: 0,
    backgroundColor: colors.white,
    height: '100%',
    width: '100%',
  },
  loadingText: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    marginTop: 16,
    color: colors.primary,
  },
};


export default connectVernalar(NewConfirmBookingWebView);
