import {View} from 'react-native';
import React from 'react';
import {Route, Switch, withRouter} from 'react-router';
import url from 'url';
import {withRouterState} from '../../../../../../web/WebRouter';
import RailsBookingSuccess from './RailsBookingSuccess';
import RailsBookingPending from './RailsBookingPending';
import RailsBookingFailure from './RailsBookingFailure';
import railsListingReducer from '../NewListing/RailsListingReducer';
import PwaRating from 'MMT-UI/userRatings/pwaRating';

import {STATUS_FAILURE, STATUS_PENDING, STATUS_SUCCESS} from './VerifyBooking';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import PropTypes from 'prop-types';

class RailsThankYouWeb extends React.Component {
  constructor(props) {
    super(props);
    const urlObj = url.parse(window.location.href, window.location.search);
    const {query = {}} = urlObj;
    const {bookingStatus} = query;
    let status = STATUS_PENDING;
    if (bookingStatus) {
      if (bookingStatus === 'S') {
        status = STATUS_SUCCESS;
      } else if (bookingStatus === 'F') {
        status = STATUS_FAILURE;
      } else {
        status = STATUS_PENDING;
      }
    }
    this.state = {
      bookingStatus: status,
      mmtId: this.props.bookingID,
      id: 'thankYouPage',
      showRatingsModule:true,
    };
  }
  async UNSAFE_componentWillMount() {
      const thankYouPayload = await JSON.parse(window?.localStorage.getItem('_GTMReviewData'));
      thankYouPayload.pageType = 'Thank You';
      if (typeof window.dataLayer === 'object') {
        window.dataLayer.push(thankYouPayload);
      }
  }

  render() {
    const {bookingStatus, mmtId,showRatingsModule} = this.state;
    return (
      <View style={{ flex: 1 }} testID={this.state.id}>
        {bookingStatus === STATUS_SUCCESS &&
          <RailsBookingSuccess rootTag={this.props.rootTag} mmtId={mmtId} id={`${this.state.id}_bookingSuccessful`} />
        }

        {bookingStatus === STATUS_PENDING &&
          <RailsBookingPending rootTag={this.props.rootTag} mmtId={mmtId} id={`${this.state.id}_bookingPending`} />
        }

        {bookingStatus === STATUS_FAILURE &&
          <RailsBookingFailure rootTag={this.props.rootTag} mmtId={mmtId} id={`${this.state.id}_bookingFailure`} />
        }

        {showRatingsModule && (
              <PwaRating
                bookingDetails={{
                  lob: 'Rail',
                  source: 'MWEB',
                  bookingStatus: 'Confirmed',
                  category: 'Rail_ThankYouPage',
                  bookingId: mmtId,
                }}
                onFeedbackClose={()=> {
                  this.setState({showRatingsModule:false});
                }}
              />
             )}
      </View>
    );
  }
}

RailsThankYouWeb.propTypes = {
  bookingID: PropTypes.string,
  rootTag: PropTypes.number,
};

injectAsyncReducer('railsListing', railsListingReducer);

const RailsThankYouWebRoute = () => (
  <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
    <Switch>
      <Route exact path="/pwa/mobileThankyouPage/:bookingID" component={withRouterState(RailsThankYouWeb)} />
    </Switch>
  </View>
);

export default withRouter(RailsThankYouWebRoute);
