/**
 * @description the purpose of this component is to redirect
 * the need help page. This excludes the need of making a
 * configuration level change at nginx level.
 */
import { useEffect } from 'react';
import { Actions } from '../../navigation';

export default function ForgotPasswordWeb() {
  useEffect(() => {
    Actions.railsNeedHelp({
      type: 'replace',
    });
  }, []);
  return null;
}

