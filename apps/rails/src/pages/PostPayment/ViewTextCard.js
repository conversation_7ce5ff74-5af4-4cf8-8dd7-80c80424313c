import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import {Image, Keyboard, TextInput, View} from 'react-native';
import PropTypes from 'prop-types';
import React from 'react';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {webViewStyle} from './NewConfirmBookingWebView';
import CtaButton from '../User/Common/CtaButton';
import isEmpty from 'lodash/isEmpty';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import passwordShow from '@mmt/legacy-assets/src/ic-show-white.webp';
import passwordHide from '@mmt/legacy-assets/src/ic-hide-white.webp';

class ViewTextCard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      secureText: false,
    };
  }

  render() {
    const {inputText, labels} = this.props;
    return (
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.black04,
        justifyContent: 'space-around',
        paddingHorizontal: 10,
      }}
      >
        <TextInput
          ref={(ref) => {
            this._ref = ref;
          }}
          placeholder={labels.yourPassword}
          placeholderTextColor={colors.white}
          underlineColorAndroid="transparent"
          style={[this.state.secureText ? webViewStyle.inputTextHideStyle : webViewStyle.inputTextShowStyle, {
            width: 154,
            justifyContent: 'center',
          }, fontStyle('bold'), getLineHeight(14)]}
          maxlength={20}
          value={inputText}
          secureTextEntry={this.state.secureText}
          keyboardShouldPersistTaps="handled"
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          editable={false}
        />

        {!isEmpty(inputText) &&
        <TouchableRipple onPress={() => {
          this.setState({secureText: !this.state.secureText});
        }}
        >
          <View style={webViewStyle.imageContainer}>
            <Image
              source={this.state.secureText ? passwordHide : passwordShow}
              style={webViewStyle.passwordImage}
            />
          </View>
        </TouchableRipple>
        }
        <View style={{
          justifyContent: 'center',
          paddingLeft: 14,
        }}>
          <CtaButton
            label="Next"
            onPress={() => this.props.onClick()}
            btnStyle={{
              borderWidth: 0,
              height: 32,
              width: 120,
              paddingHorizontal: normalisePx(0),
              borderRadius: 32,
            }}
            textStyle={[{
              color: colors.white,
              fontSize: 12,
            }, fontStyle('black'), getLineHeight(12) ]}
          />
        </View>
      </View>
    );
  }
}

ViewTextCard.propTypes = {
  inputText: PropTypes.string.isRequired,
  labels: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
};
export default ViewTextCard;
