import React, { useEffect, useRef, useState, useCallback, createContext } from 'react';
import { Actions } from '../../navigation';
import { View, Text} from 'react-native';
import { connect } from 'react-redux';
import { updateUsername } from '@mmt/rails/src/pages/User/UserVerification/UserVerificationActions';
import {getCallbackURLArray} from './PostPaymentUtils';
import getIrctcRedirectHTML, { getIrctcBookingParams } from './IrctcRedirect';
import { trackEvar71Event, trackIrctcConnectionTries, trackRailPaymentDone } from '../../railsAnalytics';
import NewConfirmBookingWebView from './NewConfirmBookingWebView';
import ConfirmBookingWebView from './BookingConfirmationWebView';
import IrctcPageHeaderWithRefresh from './Components/IrctcPageHeaderWithRefresh';
import {
  RAILS_MMT_ID,
  setDataToAsyncStorage,
} from '../../Utils/RailsConstant';
import CustomizedSomethingWentWrong from '../ErrorPages/CustomizedSomethingWentWrong';
import PageSlowTooltip from './Components/PageSlowTooltip';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { _label } from '../../vernacular/AppLanguage';
import { delay, TIMER, useTimer } from './confirmBookingUtil';
import railsOmnitureKeys from '../../omnitureMap';
import { AbConfigKeyMappings, getPokusConfig } from 'packages/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from 'packages/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { GFT_PDT_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/GFT/PdtGftConstants';
import { logGftPageEventsToPdt } from '../../PdtAnalytics/PdtAnalyticsV2/GFT';
import PropTypes from 'prop-types';

const PAGE_LOAD_LIMIT = 7000;
const initialTime = 60;
export const CounterContext = createContext(null);

/* eslint-disable */
function ConfirmBooking (props) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState({
    hasError: false,
    errorMessage: '',
  });
  const [callBackURL, setCallbackURL] = useState('');
  const [htmlCode, setHtmlCode] = useState('');
  const [showPageSlowTooltip, setShowPageSlowTooltip] = useState(false);
  const [webViewLoaded, setWebViewLoaded] = useState(false);
  const [retryTimer, setRetryTimer] = useState({
    delay: 0,
    retryCount: -1,
  });
  const [headerDetail, setHeaderDetail] = useState({
    show: true,
    title: _label('complete_booking'),
    subTitle: _label('enter_password_on_irctc'),
  });

  const [railsShowCustomIrctcWebpage, setRailsShowCustomIrctcWebpage] = useState(false);

  const _refreshHeaderRef = useRef(null);
  const _newConfirmWebView = useRef(null);
  const _pageSpeedTimerIdRef = useRef(null);
  const triggerNextApiCall = useRef(true);
  const manualRefresh = useRef(false);
  const isAutoRetry = useRef(false);

  const { startCounter, remainingTime, timerState } = useTimer(initialTime);
  const { mmtId, irctcUserId, bookingRef, logGftPdtEventsToPdt } = props;
  const mmtBookingId = mmtId || bookingRef;

  const eventMapping = {
    whatsapp: 'gft_retry_wa',
    sms: 'gft_retry_sms',
    email: 'gft_retry_sms',
    mytrips: 'gft_retry_mt',
  };

  const trackRetryEventBasedOnSource = (source, pageName) => {
    const eventName = eventMapping[source];
    if (eventName) {
      trackEvar71Event(pageName, eventName);
    }
  };

  useEffect(() => {
    trackRailPaymentDone();
    const enableInjectionOnIOSIRCTCPage = getPokusConfig(PokusLobs.RAIL, 
      AbConfigKeyMappings.enableInjectionOnIOSIRCTCPage, false);
    setRailsShowCustomIrctcWebpage(enableInjectionOnIOSIRCTCPage);
    if (props.source) {
    trackRetryEventBasedOnSource(props.source, 'RetryBookingPage');
    }
  }, []);

  const onHardBackPress = () => {
    if (_newConfirmWebView.current && _newConfirmWebView.current.onHardBackPress) {
      return _newConfirmWebView.current.onHardBackPress();
    }
    Actions.pop({ isThankYouDismiss: true });
    logGftPdtEventsToPdt(GFT_PDT_EVENTS.BACK_PRESS);
    return true;
  };

  const _onRefreshClick = () => {
    manualRefresh.current = true;
    isAutoRetry.current = false;
    setRetryTimer({
      delay: 0,
    });
    startCounter();
    setError({
      error: false,
      errorMessage: '',
    });
    logGftPdtEventsToPdt(GFT_PDT_EVENTS.REFRESH_CLICKED);
  };

  const verifyBooking = (request) => {
    Actions.verifyIrctcBooking({
      request,
      type: 'replace',
    });
  };

  const _onWebViewLoad = () => {
    setWebViewLoaded(true);
    setShowPageSlowTooltip(false);
    if (_pageSpeedTimerIdRef.current) {
      clearTimeout(_pageSpeedTimerIdRef.current);
    }
  };

  const loadPage = useCallback((retryDetails) => {
    if (retryDetails.delay) {
      delay(retryDetails.delay).then(() => {
        _loadPage(retryDetails);
      });
      return;
    }
    _loadPage();
  }, []);

  const _loadPage = (retryDetails) => {
    if (mmtBookingId && irctcUserId) {
      setDataToAsyncStorage(RAILS_MMT_ID, mmtBookingId);
    }
    getIrctcBookingParams(mmtBookingId, retryDetails)
      .then(response => {
        if (manualRefresh.current) {
          triggerNextApiCall.current = false;
          manualRefresh.current = false;
        }

        if (response.errorDetails) {
          setLoading(false);
          setError({
            hasError: true,
            errorMessage: response.errorDetails.errorMessage,
          });
          setHeaderDetail({
            ...headerDetail,
            title: _label('booking_failed'),
            subTitle: _label('error_in_irctc_connection'),
          });
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.IRCTC_PAGE_LOAD, response.errorDetails);
          return;
        }
        props.setUserNameToRedux(response?.wsloginId);
        const htmlCode = getIrctcRedirectHTML(response);
        setHtmlCode(htmlCode);
        getCallbackURLArray().then(urls => setCallbackURL(urls));
        setLoading(false);

        if (_pageSpeedTimerIdRef.current) {
          clearTimeout(_pageSpeedTimerIdRef.current);
        }
        _pageSpeedTimerIdRef.current = setTimeout(() => {
          if (!webViewLoaded) {
            const headerRef = _refreshHeaderRef.current;
            if (headerRef && (timerState === TIMER.R || loading)) {
              return;
            }
            setShowPageSlowTooltip(true);
          }
        }, PAGE_LOAD_LIMIT);
      });
    logGftPdtEventsToPdt(GFT_PDT_EVENTS.IRCTC_PAGE_LOAD);
  };

  const updateHeaderDetail = useCallback((headerProps) => {
    setHeaderDetail({
      ...headerDetail,
      ...headerProps,
    });
  }, []);

  const updateRetryTimer = useCallback((retryDetail) => {
    isAutoRetry.current = true;
    setRetryTimer({
      ...retryTimer,
      ...retryDetail,
    });
  });

  useEffect(() => {
    if (!isAutoRetry.current && !triggerNextApiCall.current) {
      triggerNextApiCall.current = true;
      return;
    }

    setLoading(true);
    loadPage(retryTimer, 'useEffect');
    trackOmintureEvent({
      retryCount: retryTimer.retryCount,
    });

  }, [retryTimer.delay, retryTimer.retryCount, timerState, mmtId, irctcUserId]);

  const trackOmintureEvent = useCallback(({ event, retryCount }) => {
    let pageName = '';
    if (retryCount > -1) {
      pageName = `${railsOmnitureKeys.irctc_redirect.retry_transition}${retryCount + 1}`;
    }
    if (retryCount === -1) {
      pageName = railsOmnitureKeys.irctc_redirect.normal_transition;
    }
    if (isAutoRetry.current && event === 'success') {
      pageName = railsOmnitureKeys.irctc_redirect.retry_successful;
    }
    if (isAutoRetry.current && event === 'failed') {
      pageName = railsOmnitureKeys.irctc_redirect.retry_failed;
    }
    if (pageName) {
      trackIrctcConnectionTries({
        bookingId: mmtId,
        pageName,
      });
    }
  }, []);

  if (!loading && error.hasError) {
    return (
      <View style={{flex: 1}}>
        <IrctcPageHeaderWithRefresh
          onBackPress={onHardBackPress}
          onRefreshClick={_onRefreshClick}
          showRefreshButton
          enableRefreshButton={() => {
            // this.enableRefreshButton();
          }}
          disableRefreshButton={timerState === TIMER.R || loading}
          remainingTime={loading ? 0 : remainingTime}
          headerProps={headerDetail}
          logGftPdtEventsToPdt={logGftPdtEventsToPdt}
        />
        <CustomizedSomethingWentWrong
          onRefresh={onHardBackPress}
          header=""
          subHeader={error.errorMessage}
          buttonText={_label('go_to_home_page', { uppercase: true })}
        />
      </View>
    );
  }

  return (
    <View style={{flex: 1}}>
      <IrctcPageHeaderWithRefresh
        ref={_refreshHeaderRef}
        onBackPress={onHardBackPress}
        onRefreshClick={_onRefreshClick}
        showRefreshButton
        disableRefreshButton={timerState === TIMER.R || loading}
        remainingTime={loading ? 0 : remainingTime}
        headerProps={headerDetail}
        logGftPdtEventsToPdt={logGftPdtEventsToPdt}
      />
      {railsShowCustomIrctcWebpage ? (
        <NewConfirmBookingWebView
          callbackUrl={callBackURL}
          callbackFunc={verifyBooking}
          htmlCode={htmlCode}
          onLoad={_onWebViewLoad}
          ref={_newConfirmWebView}
          onRefreshClick={_onRefreshClick}
          irctcUserId={props.irctcUserId}
          railsEnableHelpSectionOnIRCTCWebpage
          disableRefreshButton={timerState === TIMER.R || loading}
          loadingBookingDetails={loading}
          loadPage={loadPage}
          setRetryTimer={updateRetryTimer}
          updateHeaderDetail={updateHeaderDetail}
          trackOmintureEvent={trackOmintureEvent}
          remainingTime={loading ? 0 : remainingTime}
        />
      ) : (
        <ConfirmBookingWebView
          callbackUrl={callBackURL}
          callbackFunc={verifyBooking}
          htmlCode={htmlCode}
          ref={_newConfirmWebView}
        />
      )}

      {(showPageSlowTooltip && headerDetail.show) &&
      <PageSlowTooltip layout={{x: 4, y: 56}} orientation="bottom">
        <Text
          style={{
            color: colors.white,
            ...fontStyle('medium'),
            ...getLineHeight(12),
            fontSize: 12,
          }}
        >
          {_label('irctc_taking_too_long_tooltip')}
        </Text>
      </PageSlowTooltip>
      }
    </View>
  );
}

ConfirmBooking.propTypes = {
  mmtId: PropTypes.string,
  irctcUserId: PropTypes.string,
  bookingRef: PropTypes.string,
  source: PropTypes.string,
  setUserNameToRedux: PropTypes.func,
  logGftPdtEventsToPdt: PropTypes.func,
};

const mapDispatchToProps = (dispatch) => ({
  setUserNameToRedux: (userName) => {
    dispatch(updateUsername(userName));
  },
  logGftPdtEventsToPdt: (eventValue, errorDetails) => {
    dispatch(logGftPageEventsToPdt(eventValue, errorDetails));
  },
});

export default connect(null, mapDispatchToProps)(ConfirmBooking);
