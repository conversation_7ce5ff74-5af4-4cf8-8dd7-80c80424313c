import {ScrollView, View, Text, Image, StyleSheet, ActivityIndicator} from 'react-native';
import React from 'react';
import { connect } from 'react-redux';
import { Actions } from '../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import CloseButton from '../User/Common/CloseButton';

import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import RailsRetrievePasswordModal from '../Common/RailsRetrievePasswordModal';
import {TouchableWithoutFeedback} from 'react-native-web';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import {_label} from '../../vernacular/AppLanguage';
import { trackPageVisits } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import get from 'lodash/get';
import { config } from '../NeedHelp/Shared';
import verifiedIcon from '@mmt/legacy-assets/src/ic-tick-green.webp';

const EnableButtonCta = ({text, onClick, loading}) => (
  <TouchableRipple onPress={onClick}>
    <View>
      <LinearGradient
        style={enableButtonStyle.container}
        colors={['#53b2fe', '#065af3']}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
      >
        {
          loading &&
          <ActivityIndicator size="small" color={colors.white} />
        }
        {
          !loading &&
          <Text style={[enableButtonStyle.next, fontStyle('black'), getLineHeight(16)]}>
            {text}
          </Text>
        }
      </LinearGradient>
    </View>
  </TouchableRipple>
);

EnableButtonCta.propTypes = {
  loading: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  text: PropTypes.string.isRequired,
};

const enableButtonStyle = StyleSheet.create({
  container: {
    borderRadius: 30, alignItems: 'center', justifyContent: 'center', paddingHorizontal: 28, paddingVertical: 10,
  },
  next: {
    fontSize: 16, color: colors.white, backgroundColor: colors.transparent, marginHorizontal: 7, marginVertical: 2,
  },
});

class ForgotIrctcPassword extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      userName: null,
      loading: false,
      mobileErrorMessage: null,
      mobileNumber: null,
      errorMessage: null,
      retrievePassword: false,
    };
    this.from = this.props.from;
  }
  async componentDidMount() {
    if (this.isFromNeedHelpPage()) {
      trackPageVisits('rail_need_help_forgotpassword_landing', {});
    }
    const userName = this.props?.irctcUserName;
    this.setState({userName});
  }

  onCloseClicked = () => {
    if (this.from === config.NEED_HELP_ROUTE_SOURCE) {
      Actions.pop();
    } else {
      Actions.openIrctcPage();
    }
  };

  onChange = (mobileNumber) => {
    this.setState({mobileNumber});
  };

  _getPassword = () => {
    this.setState({retrievePassword: true});
  };

  isFromNeedHelpPage = () => {
   return get(this, 'props.location.state.from', '') === config.NEED_HELP_ROUTE_SOURCE;
  };

  render() {
    const {retrievePassword} = this.state;

    return (
      <View>
        {!retrievePassword && <View style={
          {
            flexDirection: 'row',
            zIndex: 1,
            borderBottomColor: colors.grayBg,
            borderBottomWidth: 1,
            borderTopWidth: 0,
            borderTopColor: colors.white,
            height: 47,
            justifyContent: 'space-between',

          }}>
          <Text style={[{
             fontSize: 16, marginHorizontal: 14, color: colors.black, paddingVertical: 12,
          }, fontStyle('bold'), getLineHeight(16)]}
          >
            {_label('get_new_irctc_password')}
          </Text>
          <CloseButton onPress={this.onCloseClicked} style={{ marginRight: 20 }} />
        </View>}

        {retrievePassword && <View style={
          {
            flexDirection: 'row',
            zIndex: 1,
            justifyContent: 'flex-end',
            marginTop: 20,
          }}>
          <CloseButton onPress={this.onCloseClicked} style={{ marginRight: 20 }} />
        </View>}

        <ScrollView elevation keyboardShouldPersistTaps="handled">
          {!retrievePassword && <View style={{ marginTop: 20, paddingHorizontal: 16 }}>
            <Text style={[{ color: colors.azure, fontSize: 10}, fontStyle('regular'), getLineHeight(10)]}>
              {_label('irctc_username', {uppercase : true})}
            </Text>
            <View style={{flexDirection: 'row', paddingVertical: 8}}>
              <Text style={[{ fontSize: 14, color: colors.black}, fontStyle('bold'), getLineHeight(14)]}>
                {this.state.userName}
              </Text>
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginLeft: 10}}>
                <Image source={verifiedIcon} style={{width: 12, height: 12, marginRight: 4}} />
                <Text style={[{ color: colors.cyan, fontSize: 12}, fontStyle('bold'), getLineHeight(12)]}>{_label('verified')}</Text>
              </View>
            </View>
          </View>}
          <RailsRetrievePasswordModal
            fromPage={this.isFromNeedHelpPage() ? config.NEED_HELP_ROUTE_SOURCE : '' }
            irctcForgotpassword={true}
            getPassword={this._getPassword}
          />
          <View>
            <Text style={[{marginTop: 130, alignSelf: 'center',fontSize: 12, color: colors.defaultTextColor}, fontStyle('regular'), getLineHeight(12)]}>{_label('facing_trouble')}</Text>
            <TouchableWithoutFeedback onPress={() => Actions.railsCreateAccountPage({
              from: 'travelers',
            })}>
              <Text style={[{marginTop: 10, alignSelf: 'center', fontSize: 14, color: colors.azure}, fontStyle('bold'), getLineHeight(14)]}>{_label('create_new_account')}</Text>
            </TouchableWithoutFeedback>
          </View>
        </ScrollView>
      </View>
    );
  }
}

ForgotIrctcPassword.propTypes = {
  irctcUserName: PropTypes.string,
  from: PropTypes.string,
};

const mapStateToProps = (state) => ({
  irctcUserName: state?.railsUserVerification?.irctcUserName,
});

export default connect(mapStateToProps)(ForgotIrctcPassword);
