import React from 'react';
import { View} from 'react-native';
import PropTypes from 'prop-types';
import NonSuccess from './NonSuccessCard';
import {trackThankYouPageFailurePageLoad} from '../../railsAnalytics';
import ManageBooking from './BookingStatus/ManageBookingButton';
import {getDataFromAsynStorage, getStatusWithBookingId, RAILS_MMT_ID} from '../../Utils/RailsConstant';
import { isMweb } from '../../Utils/device';
import { logThankYouPageLoadEventFailure } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtRailsThankYou';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

class ReactBookingFailure extends React.Component {
  constructor(props) {
    super(props);
    trackThankYouPageFailurePageLoad();
    this.state = {
      data: null,
    };
  }

  async componentDidMount() {
    let mmtId = null;
    if (isMweb()) {
      mmtId = this.props.mmtId;
    } else {
      mmtId = await getDataFromAsynStorage(RAILS_MMT_ID);
    }
    const response = await getStatusWithBookingId(mmtId);
    const data = {
      bookingStatusMessage: response.data.bookingStatusDetails.bookingStatusMessage,
      bookingStatusSubMessage: response.data.bookingStatusDetails.bookingStatusSubMessage,
      bookingInfo: response.data.bookingInfo,
      bookingId: response.data.bookingDetails?.bookingId,
      status: response.status,
      colorArray: ['#ff7f3f', '#ff3e5e'],
    };
    this.setState({data});
    logThankYouPageLoadEventFailure(response?.data, 'THANKYOU_LOAD');
  }

  render() {
    return (
      <View style={{ flex: 1, flexDirection: 'column', justifyContent: 'space-between' }} testID={this.props?.id}>
        <View style={{backgroundColor: colors.grayBg, flex: 1}} >
          {this.state.data && <NonSuccess data={this.state.data} id={this.props?.id} />}
        </View>
        <ManageBooking id={`${this.props?.id}_manageBooking`} />
      </View>
    );
  }
}

export default ReactBookingFailure;

ReactBookingFailure.propTypes = {
  mmtId: PropTypes.string,
  id: PropTypes.string,
};

