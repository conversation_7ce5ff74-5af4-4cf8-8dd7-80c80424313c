import React from 'react';
import {Dimensions, Platform, StyleSheet, View} from 'react-native';
import PropTypes from 'prop-types';
import isNil from 'lodash/isNil';
import inRange from 'lodash/inRange';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import { isIos } from '../../../Utils/device';


class PageSlowToolTip extends React.Component {
  _onContentLayout = ({nativeEvent}) => {
    const {
      x, y, height, width,
    } = nativeEvent.layout;
    this._bounds = {
      top: y, left: x, bottom: y + height, right: x + width,
    };
  };

  _onPress = ({nativeEvent}) => {
    const {pageX, pageY} = nativeEvent;
    const {
      top, left, bottom, right,
    } = this._bounds;
    const inBounds = inRange(pageY, top, bottom) && inRange(pageX, left, right);
    if (!inBounds) {
      this.props.dismiss();
    }
  };

  render() {
    const {layout} = this.props;
    if (isNil(layout)) {
      return null;
    }
    const isBottom = this.props.orientation === 'bottom';
    const offset = isIos() ? 24 : 0;
    let position = {right: layout.x};
    if (!isBottom) {
      position = {...position, bottom: (Dimensions.get('window').height - layout.y)};
    } else {
      position = {...position, top: (layout.y - offset)};
    }
    return (
      <View style={[styles.rootContainer, {opacity: 1}]} pointerEvents="box-none">
        <View
          style={{
            position: 'absolute',
            ...position,
            ...getPlatformElevation(Platform.select({ios: 6, android: 20})),
          }}
          onLayout={this._onContentLayout}
        >
          <View style={styles.arrowTop} />
          <View style={styles.innerContainer}>
            <View style={styles.content}>
              {this.props.children}
            </View>
          </View>
        </View>
      </View>
    );
  }
}


const styles = StyleSheet.create({
  rootContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.transparent,
  },
  innerContainer: {
    backgroundColor: colors.successGreen,
    flexDirection: 'row',
    borderRadius: 2,
    borderColor: colors.transparent,
    padding: 4,
  },
  arrowTop: {
    width: 0,
    height: 0,
    alignSelf: 'flex-end',
    marginRight: normalisePx(16),
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: normalisePx(10),
    borderRightWidth: normalisePx(10),
    borderBottomWidth: normalisePx(8),
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.successGreen,
  },
  arrowBottom: {
    width: 0,
    height: 0,
    marginLeft: normalisePx(16),
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: normalisePx(10),
    borderRightWidth: normalisePx(10),
    borderTopWidth: 8,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderTopColor: colors.successGreen,
  },
  content: {
    padding: normalisePx(8),
  },
});

PageSlowToolTip.propTypes = {
  children: PropTypes.node.isRequired,
  dismiss: PropTypes.func.isRequired,
  layout: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
  }),
  orientation: PropTypes.oneOf(['top', 'bottom']),
};
PageSlowToolTip.defaultProps = {
  layout: null,
  orientation: 'top',
};

export default PageSlowToolTip;
