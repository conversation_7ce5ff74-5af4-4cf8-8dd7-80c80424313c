import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

import MultiStyleVernacText from '../../../../vernacular/MultiStyleVernacText';
import { _label } from '../../../../vernacular/AppLanguage';
import { fontStyle, getLineHeight } from '../../../../vernacular/VernacularUtils';
import { ITEM_WIDTH } from './FunFactItem';

const styles = StyleSheet.create({
  passwordInfo: {
  },
  nextStep: {
    display: 'flex',
    flexDirection: 'column',
    borderColor: colors.grayBg,
    borderWidth: 2,
    borderRadius: 8,
    paddingVertical: 6,
    paddingHorizontal: 16,
    marginBottom: 10,
    width: ITEM_WIDTH,
    alignItems: 'center',
  },
  nextStepTitle: {
    color: colors['343434'],
  },
  nextStepWrapper: {
    marginTop: 10,
  },
  nextStepDescription: {
    color: colors['343434'],
  },
});

const PasswordStep = () => {
  return (
    <View style={styles.passwordInfo}>
      <View style={styles.nextStep}>

        <Text
          style={[getLineHeight(16), fontStyle('bold'), styles.nextStepTitle]}
        >
          {_label('next_step')}
          <Text style={[getLineHeight(16), fontStyle('regular'), styles.nextStepDescription]}>{_label('enter_irctc_password')}</Text>
        </Text>

        <MultiStyleVernacText
          content = {_label('forgot_password_on_next_page')}
          contentHorizontal = {true}
          params = {{frgt_pswd: _label('forgot_password')}}
          defaultContentStyle = {[styles.nextStepDescription, fontStyle('regular'), getLineHeight(16)]}
          textStyles = {{
            frgt_pswd: [styles.nextStepDescription, fontStyle('bold'), getLineHeight(16)],
          }}
          viewStyle = {styles.nextStepWrapper}
        />
      </View>
    </View>
  );
};

export default PasswordStep;
