import React from 'react';
import { View, Text, StyleSheet, Dimensions, Image } from 'react-native';
import { fontStyle, getLineHeight } from '../../../../vernacular/VernacularUtils';
import PasswordStep from './PasswordStep';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

export const SLIDER_WIDTH = Dimensions.get('window').width + 80;
export const ITEM_WIDTH = 300;

const FunFactItem = ({ item }) => {
  if (item.id === 'pwd_next_step') {
    return <PasswordStep />;
  }
  return (
    <View style={styles.container} key={item.id}>
      <Image
        source={{
          uri: item.imgUrl,
        }}
        resizeMode="cover"
        style={{
          width: 120,
          height: 80,
          borderTopLeftRadius: 10,
          borderBottomLeftRadius: 10,
        }}
      />
      <View style={styles.text}>
        <Text style={[styles.header, fontStyle('bold'), getLineHeight(12)]}>{item.title}</Text>
        <Text style={[styles.body, fontStyle('regular'), getLineHeight(12)]}>{item.body}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderColor: colors.lightSilver,
    borderWidth: 1,
    borderRadius: 10,
    minWidth: ITEM_WIDTH,
    height: 80,
    display: 'flex',
    flexDirection: 'row',
  },
  text: {
    paddingVertical: 8,
    paddingLeft: 6,
    width: 180,
  },
  header: {
    color: colors.darkShade,
  },
  body: {
    color: colors.darkShade,
  },
});


FunFactItem.propTypes = {
  item: PropTypes.shape({
    id: PropTypes.string,
    imgUrl: PropTypes.string,
    title: PropTypes.string,
    body: PropTypes.string,
  }),
};

export default FunFactItem;
