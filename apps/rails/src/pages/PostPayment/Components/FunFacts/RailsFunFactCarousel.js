import React from 'react';
import Carousel from 'react-native-snap-carousel';
import { configKeys } from '../../../../configStore/Common/constants';
import useConfigStore from '../../../../configStore/Common/RailsConfigStoreUtils';
import isEmpty from 'lodash/isEmpty';

import FunFactItem, { ITEM_WIDTH, SLIDER_WIDTH } from './FunFactItem';

const defaultFunFacts = [
  {
    id: 'pwd_next_step',
    title: '',
    body: '',
    imgUrl: '',
    imageOnly: false,
  },
];

const RailsFunFactCarousel = () => {
  const isCarousel = React.useRef(null);
  const data = useConfigStore(configKeys.RAILS_RETRY_PAGE_FUN_FACTS);
  return (
    <Carousel
      containerCustomStyle={{
        flexGrow: 0,
      }}
      layout="default"
      layoutCardOffset={10}
      ref={isCarousel}
      data={isEmpty(data?.data) ? defaultFunFacts : data?.data}
      renderItem={FunFactItem}
      sliderWidth={SLIDER_WIDTH}
      itemWidth={ITEM_WIDTH}
      useScrollView={true}
      loop
      autoplay
      autoplayDelay={2000}
    />
  );
};

export default RailsFunFactCarousel;
