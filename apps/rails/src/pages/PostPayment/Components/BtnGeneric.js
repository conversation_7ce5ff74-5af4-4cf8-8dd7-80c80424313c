import PropTypes from 'prop-types';
import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { isAndroid } from '../../../Utils/device';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';


function BtnGeneric({
  btnText,
  showBtnLoader = false,
  clickAction,
  disabled = false,
  id,
  shape = 'normal',
}) {
  const btnColors = !disabled ? [colors.lightBlue, colors.darkBlue] :
    [colors.lightGray, colors.lightGray];
  const rippleStyle = isAndroid() ? {} : btnRippleStyle;
  return (
    <View style={{ display: 'flex' }} testID={`${btnText}_button`}>
      <LinearGradient
        style={[styles.linearGrad, shape === 'oval' ? styles.oval : null]}
        colors={btnColors}
        start={{
          x: 0,
          y: 2,
        }}
        end={{
          x: 1,
          y: 1,
        }}
        testID={`${btnText}_button_gradient`}
      >
        <View
          style={[styles.btnContainer, { width: '100%', flex: 1 }]}
          testID={`${btnText}_button_container`}
        >
          <TouchableRipple style={rippleStyle}
            onPress={() => {
              if (!disabled) {
                clickAction();
              }
            }}
            feedbackColor={colors.transparent}
            testID={`${btnText}_button_ripple`}
          >
            <View
              style={[
                styles.buttonBlue,
                { flex: 1, alignItems: 'center', justifyContent: 'center' },
              ]}
              testID={`${btnText}_button_view`}
            >
              <Text
                style={[
                  styles.btnText,
                  fontStyle('black'),
                  getLineHeight(14),
                  { color: disabled ? colors.greyText : colors.white },
                ]}
                testID={id}
              >
                {btnText}
              </Text>
            </View>
          </TouchableRipple>
        </View>
        {showBtnLoader &&
          <View style={styles.btnLoaderContainer} testID={`${btnText}_button_loader_container`}>
            <Spinner size={20} color={colors.white} />
          </View>
        }
      </LinearGradient>
    </View>
  );
}

const btnRippleStyle = {
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
};

const styles = StyleSheet.create({
  linearGrad: {
    overflow: 'hidden',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 44,
    marginTop: 16,
    height: 40,
  },
  btnText: {
    fontSize: 14,
    color: colors.white,
  },
  btnLoaderContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    zIndex: 50,
  },

  oval: {
    borderRadius: 42.5,
  },
});

BtnGeneric.propTypes = {
  btnText: PropTypes.string.isRequired,
  showBtnLoader: PropTypes.bool,
  clickAction: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  shape: PropTypes.string,
  id: PropTypes.string,
};

export default BtnGeneric;
