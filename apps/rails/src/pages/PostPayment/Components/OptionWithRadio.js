import PropTypes from 'prop-types';
import React from 'react';
import {View, StyleSheet, Text, TouchableOpacity} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import Radio from './GenericRadio';

function OptionWithRadio({
  val = '',
  selected = false,
  handleRadioClick,
  optionStyles = null,
  textStyle = null,
}) {
  return (
    <TouchableOpacity onPress={handleRadioClick}>
      <View style={[styles.optionContainer, optionStyles ]}>
        <Radio selected={selected} />
        <Text style={{...textStyle}}>{val}</Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  optionContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    height: 44,
    marginBottom: 10,
    backgroundColor: colors.grey13,
    borderColor: colors.lightGrey,
    borderWidth: 1,
    borderStyle: 'solid',
    borderRadius: 4,
  },
});

OptionWithRadio.propTypes = {
  val: PropTypes.string,
  selected: PropTypes.bool,
  handleRadioClick: PropTypes.func.isRequired,
  optionStyles: PropTypes.object,
  textStyle: PropTypes.object,
};

export default OptionWithRadio;

