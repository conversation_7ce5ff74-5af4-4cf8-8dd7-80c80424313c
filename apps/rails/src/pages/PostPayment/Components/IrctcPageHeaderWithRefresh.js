import {Image, StyleSheet, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import React, {Component} from 'react';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { _label } from '../../../vernacular/AppLanguage';

import refreshIconGrey from '@mmt/legacy-assets/src/ic-refresh-grey.webp';
import refreshIconBlue from '@mmt/legacy-assets/src/refreshBlue.webp';

export default class IrctcPageHeaderWithRefresh extends Component {

  isRefreshDisabled = () => this.props.disableRefreshButton;

  onRefreshClick = () => {
    this.props.onRefreshClick();
  };

  render() {
    const {
      onBackPress,
      showRefreshButton,
      disableRefreshButton,
      remainingTime,
      headerProps = {},
    } = this.props;
    const { show = true, title, subTitle } = headerProps;
    if (!show) {
      return null;
    }
    return (
      <Card
        style={{
          flexDirection: 'row',
          marginVertical: 0,
          marginHorizontal: 0,
          alignItems: 'center',
          borderBottomWidth: 2,
          borderColor: colors.greyBookedSeat,
          paddingHorizontal: 16,
          paddingVertical: 12,
        }}
        elevation={0}
      >
          <TouchableRipple
            onPress={onBackPress}
          >
            <Image
              style={headerStyles.backImage}
              source={backIcon}
            />
          </TouchableRipple>

          <View style={{paddingLeft: 16, flex: 1}}>
            <Text style={[headerStyles.completeText, fontStyle('bold'), getLineHeight(18)]}>
              {title || _label('complete_booking')}
            </Text>
            <Text style={[headerStyles.enterText, fontStyle('regular'), getLineHeight(12)]}>
              {subTitle || _label('enter_irctc_password_and_capcha_below')}
            </Text>
          </View>
        {
          showRefreshButton && !disableRefreshButton &&
          <TouchableRipple
            onPress={this.onRefreshClick}
          >
            <Image
              style={headerStyles.refreshImage}
              source={refreshIconBlue}
            />
          </TouchableRipple>
        }
        {
          showRefreshButton && disableRefreshButton &&
          <View style={{alignItems: 'center', justifyContent: 'center'}}>
            <Image
              style={headerStyles.refreshImage}
              source={refreshIconGrey}
            />
            {
              remainingTime ? (
                <Text style={[{ fontSize: 12, color: colors.lightTextColor}, fontStyle('regular','en'), getLineHeight(12)]}>
                  {remainingTime}s
                </Text>
              ) : null
            }
          </View>
        }
        {
          !showRefreshButton &&
          <View style={headerStyles.refreshImage} />
        }
      </Card>
    );
  }
}

IrctcPageHeaderWithRefresh.propTypes = {
  onBackPress: PropTypes.func.isRequired,
  onRefreshClick: PropTypes.func.isRequired,
  showRefreshButton: PropTypes.bool.isRequired,
  disableRefreshButton: PropTypes.bool,
  headerProps: PropTypes.any,
  remainingTime: PropTypes.number,
};
const headerStyles = StyleSheet.create({
  backImage: {
    width: 16,
    height: 16,
  },
  completeText: {
    fontSize: 18,
    color: colors.black,
  },
  enterText: {
    fontSize: 12,
    color: colors.defaultTextColor,
  },
  refreshImage: {
    width: 20,
    height: 20,
  },
});
