import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import PropTypes from 'prop-types';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';


const IrctcCustomLoader = ({labels}) => (
  <View>
    <Spinner size={30} color={colors.azure} />
    <View style={{alignItems: 'center', justifyContent: 'center'}}>
      <Text style={[styles.connectingText, fontStyle('regular'), getLineHeight(16)]}>{labels.connecting}
      </Text>
      <Text style={[styles.thisMayText, fontStyle('regular'), getLineHeight(14)]}>
        {labels.hintSlow}
      </Text>
      <Text style={[styles.pleaseText, fontStyle('regular'), getLineHeight(14)]}>
        {labels.warnBackPress}
      </Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  connectingText: {
    fontSize: 16,
    paddingHorizontal: 12,
    color: colors.black04,
  },
  thisMayText: {
    marginTop: 4,
    fontSize: 14,
    fontStyle: 'italic',
    color: colors.black04,
  },
  pleaseText: {
    marginTop: 4,
    fontSize: 14,
    fontStyle: 'italic',
    color: colors.black04,
  },
});

IrctcCustomLoader.propTypes = {
  labels: PropTypes.object.isRequired,
};

export default IrctcCustomLoader;
