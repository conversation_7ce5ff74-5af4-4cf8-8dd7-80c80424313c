
import PropTypes from 'prop-types';
import React from 'react';
import {View, StyleSheet, TouchableOpacity, Text, Image} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';


function LabelBoxWithCtaIcon({
  labelText, ctaText, ctaIcon, ctaAction, onTap, showCounter,
}) {
  const actionLblColor = showCounter ? colors.lightTextColor : colors.azure;
  if (!showCounter) {
    return (
      <TouchableOpacity
        onPress={
        () => {
          onTap(ctaAction);
        }}
      >
        <View style={styles.labelContainer}>
          {Boolean(labelText) && <Text style={[styles.labelText, fontStyle('regular'), getLineHeight(12)]}>{labelText}</Text>}
          <View style={styles.imgContainer}>
            {Boolean(ctaText) &&
              <Text style={[styles.ctaText, fontStyle('regular'), getLineHeight(12), {color: actionLblColor}]}>{ctaText}</Text>
            }
            {Boolean(ctaIcon) && <Image source={ctaIcon} style={styles.ctaIcon} />}
          </View>
        </View>
      </TouchableOpacity >);
  }
  return (
    <View style={styles.labelContainer}>
      {Boolean(labelText) && <Text style={[styles.labelText, fontStyle('regular'), getLineHeight(12)]}>{labelText}</Text>}
      <View style={styles.imgContainer}>
        {Boolean(ctaText) &&
        <Text style={[styles.ctaText, fontStyle('regular'), getLineHeight(12),{color: actionLblColor}]}>{ctaText}</Text>
        }
        {Boolean(ctaIcon) && <Image source={ctaIcon} style={styles.ctaIcon} />}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  imgContainer: {
    display: 'flex',
    flexDirection: 'row',
  },
  labelContainer: {
    backgroundColor: colors.lightGrey,
    marginBottom: 10,
    paddingVertical: 14,
    paddingHorizontal: 16,
    height: 44,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 4,
  },
  labelText: {
    fontSize: 12,
    lineHeight: 18,
  },
  ctaText:
  {
    fontSize: 12,
    lineHeight: 18,
    color: colors.azure,
    marginRight: 10,
  },
  ctaIcon: {
    height: 10,
    width: 28,
  },
});

LabelBoxWithCtaIcon.propTypes = {
  labelText: PropTypes.string,
  ctaText: PropTypes.string,
  ctaIcon: PropTypes.any,
  ctaAction: PropTypes.string,
  onTap: PropTypes.string.isRequired,
  showCounter: PropTypes.bool,
  enableRefreshButton: PropTypes.func,
};

LabelBoxWithCtaIcon.defaultProps = {
  labelText: PropTypes.bool,
  ctaText: '',
  ctaIcon: null,
  ctaAction: '',
  showCounter: false,
  enableRefreshButton: () => {
    // do nothing
  },
};

export default LabelBoxWithCtaIcon;
