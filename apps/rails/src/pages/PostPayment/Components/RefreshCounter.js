import React, {useContext} from 'react';
import {Text} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {CounterContext} from '../ConfirmBooking';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

export default function Counter() {
  const counterVal = useContext(CounterContext);
  return (
    <Text style={[{ fontSize: 12, color: colors.lightTextColor}, fontStyle('regular','en'), getLineHeight(12)]}>
      {counterVal.value}s
    </Text>);
}
