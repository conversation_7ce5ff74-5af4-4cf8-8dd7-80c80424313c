import PropTypes from 'prop-types';
import React from 'react';
import {View, StyleSheet} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';

function Radio({ selected = false }) {
  const background = selected ? colors.azure : colors.lightTextColor;
  return (
    <View
      style={[styles.outerCircleStyle, {borderColor: background}]}
    >
      {selected && <View style={[styles.innerCircleStyle, {backgroundColor: background}]} />}
    </View>
  );
}

Radio.propTypes = {
  selected: PropTypes.bool,
};

const styles = StyleSheet.create({
  outerCircleStyle: {
    height: 18,
    margin: 16,
    width: 18,
    borderRadius: 9,
    borderWidth: 1,
    borderStyle: 'solid',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerCircleStyle: {
    height: 12,
    width: 12,
    borderRadius: 6,
  },
});

export default Radio;
