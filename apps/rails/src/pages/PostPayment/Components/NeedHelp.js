import React, {useState, useEffect} from 'react';
import PropTypes from 'prop-types';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {Text, BackHandler, View, TouchableOpacity, StyleSheet, Image, KeyboardAvoidingView, Keyboard, TouchableWithoutFeedback} from 'react-native';
import blueIcon from '@mmt/legacy-assets/src/arrow_blue_right_long.webp';
import captchaImage from '@mmt/legacy-assets/src/captcha.webp';
import ASSETS from '../../../Utils/Assets/RailsAssets';
import BtnGeneric from './BtnGeneric';
import OptionWithRadio from './OptionWithRadio';
import LabelBoxWithCtaIcon from './LabelBoxWithCtaIcon';
import http from '../../../http/http.js';
import Loader from '../../Common/Loader';
import {trackNeedHelpPageEvent} from '../../../railsAnalytics';
import {getMmtId, RAILS_CALL_BACK_URL_PWA_SUCCESS} from '../PostPaymentUtils';
import {getCommonHeaders} from '@mmt/legacy-commons/Helpers/genericHelper';
import railsConfig from '../../../RailsConfig';
import promiseAlert from '@mmt/legacy-commons/Common/Components/Alert/promiseAlert';
import RailRetrievePasswordContainer from '../../Common/RailRetrievePasswordContainer';
import {checkEnglishKeyboard, fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { _label } from '../../../vernacular/AppLanguage';
import {LABELS} from '../../NeedHelp/Shared';
import SimpleTextInput from '@mmt/legacy-commons/Common/Components/Inputs/SimpleTextInput';
import { isIos } from '../../../Utils/device';
import fetch2 from '../../../fetch2';
import { GFT_PDT_EVENTS } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/GFT/PdtGftConstants';

const NeedHelpScreens = {
  HOME: 'home',
  CHANGE_USERNAME: 'NG_DIFFERENT_USERNAME_01',
  CAPTCHA_SCREEN: 'NG_WHAT_CAPTCHA_01',
  OTHER_ISSUES: 'NG_OTHER_ISSUES',
  OTHER_ISSUES_COMPLETE: 'otherIssuesComplete',
  CANCEL_SCREEN: 'NG_CANCEL_BOOKING_01',
  FORGOT_PASSWORD: 'NG_NEW_PASSWORD_01',
  CANCEL_SCREEN_COMPLETE: 'cancelComplete',
  WRONG_CREDENTIALS: 'NG_WRONG_CREDENTIAL_01',
  INVALID_CAPTCHA: 'NG_INVALID_CAPTCHA_01',
};


export default function NeedHelp({
  onRefreshClick,
  callbackFunc,
  irctcUserId = '',
  closeNeedHelp,
  cancelBooking,
  disableRefreshButton,
  enablePasswordReader,
  logGftPdtEventsToPdt,
}) {
  const [showModal, setShowModal] = useState(false);
  const [showPasswordReminderModal, setShowPasswordReminderModal] = useState(false);
  const [needHelpResponse, setNeedHelpResponse] = useState(null);
  const [showLoader, setShowLoader] = useState(false);
  const [mmtIdVal, setMmtIdVal] = useState(null);

  /* eslint-disable */

  useEffect(() => {
    getNeedHelpOptions();
    if (!mmtIdVal) {
      getMmtIdVal().then(x=> {setMmtIdVal(x);});
    }
  }, []);
  const getMmtIdVal = async () => {
    const mmtId = await getMmtId();
    return mmtId;
  };

  const _close = () => { closeNeedHelp(); };

  const getNeedHelpOptions = async () => {
    try {
      setShowLoader(true);
      const response = await http.get(railsConfig.needHelpOptionsURL);
      setShowLoader(false);
      setNeedHelpResponse(response);
      setShowModal(true);
    } catch (e) {
      setShowLoader(false);
      setShowModal(true);
      setNeedHelpResponse({error: true, errorObj: e});
    }
  };

  const handleRefreshClick = () => {
    trackNeedHelpPageEvent('rails_irctc_reload_old');
    onRefreshClick();
    closeNeedHelp();
  };

  if (showLoader) {
    return (
      <View style={[styles.container, styles.modalContainer]}>
        <Loader />
      </View>);
  }
  return (
    <View style={styles.container}>
      {showModal &&
        <DismissKeyboard>
          <Modal
            mmtId={mmtIdVal}
            optionResponse={needHelpResponse}
            onRefreshClick={handleRefreshClick}
            showPasswordReminder={(val) => { setShowPasswordReminderModal(val); }}
            _closeNeedHelp={_close}
            cancelBooking={cancelBooking}
            callbackFunc={callbackFunc}
            disableRefreshButton={disableRefreshButton}
            enablePasswordReader={enablePasswordReader}
            _dismissModal={() => {
             setShowModal(false);
           }}
            logGftPdtEventsToPdt={logGftPdtEventsToPdt}
          />
        </DismissKeyboard>
      }
      {showPasswordReminderModal &&
      <RailRetrievePasswordContainer
        dismiss={() => { setShowPasswordReminderModal(false); _close(); }}
        irctcUserName={irctcUserId}
      />
      }
    </View>
  );
}

NeedHelp.propTypes = {
  onRefreshClick: PropTypes.func.isRequired,
  irctcUserId: PropTypes.string,
  callbackFunc: PropTypes.func.isRequired,
  closeNeedHelp: PropTypes.func.isRequired,
  cancelBooking: PropTypes.func.isRequired,
  disableRefreshButton: PropTypes.bool.isRequired,
  enablePasswordReader: PropTypes.bool.isRequired,
  logGftPdtEventsToPdt: PropTypes.func,
};

function DismissKeyboard({children}) {
  return (
    <TouchableWithoutFeedback onPress={() => { Keyboard.dismiss(); }}>
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={isIos() ? 'padding' : 'height'}
      >
        {children}
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>);
}

function Modal({
  optionResponse,
  callbackFunc,
  onRefreshClick,
  _closeNeedHelp,
  showPasswordReminder,
  _dismissModal,
  mmtId,
  disableRefreshButton,
  enablePasswordReader,
  logGftPdtEventsToPdt,
}) {
  const [header, setHeader] = useState(optionResponse?.headerText);
  const [currentScreen, setCurrentScreen] = useState(NeedHelpScreens.HOME);
  const [showBtnLoader, setShowBtnLoader] = useState(false);
  const [btnText, setBtnText] = useState('');
  const [desc, setDesc] = useState([]);
  const [imageVal, setImageVal] = useState(null);
  const [otherIssueText, setOtherIssueText] = useState('');
  const [editableInput, setEditableInput] = useState(true);
  const [currentRadioSelected, setCurrentRadioSelected] = useState(null);
  const [footerText, setFooterText] = useState(optionResponse?.footerText);
  const [showloader, setShowLoader] = useState(false);
  const [cancelOptionsRes, setCancelOptionsRes] = useState(null);
  const [englishErrorMessage, setEnglishErrorMessage] = useState('');
  const setErrorState = (action) => {
    setShowLoader(false);
    setHeader(_label('need_help'));
    setCurrentScreen(action);
    setDesc([_label('something_went_wrong_try_again')]);
    setBtnText(_label('close'));
  };

  const resetState = () => {
    setHeader(optionResponse?.headerText);
    setCurrentScreen(NeedHelpScreens.HOME);
    setShowBtnLoader(false);
    setBtnText('');
    setDesc([]);
    setImageVal(null);
    setOtherIssueText('');
    setEditableInput(true);
    setCurrentRadioSelected(null);
    setFooterText(optionResponse?.footerText);
    setCancelOptionsRes(null);
  };
  const onBackPress = () => {
    // if home close need help
    if (currentScreen === NeedHelpScreens.HOME) {
      _closeNeedHelp();
      return true;
    }
    resetState();
    return true;
  };

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', onBackPress);
    return () => BackHandler.removeEventListener(
      'hardwareBackPress',
      onBackPress,
    );
  }), [];

  useEffect(() => {
    setHeader(optionResponse?.headerText);
    setFooterText(optionResponse?.footerText);
    if (optionResponse && !optionResponse.contentList) {
      setErrorState();
    }
  }, [optionResponse]);

  if (!optionResponse) {
    return null;
  }

  // Below method written as Generic method in http file returns response.json
  // this will return normal response only
  const postCall = async (apiUrl, params = {}) => {
    const baseHeaders = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    };
    const commonHeader = await getCommonHeaders(baseHeaders);
    const body = JSON.stringify(params);
    const response = await fetch2(apiUrl, {
      method: 'POST',
      headers: commonHeader,
      body,
    });
    return response;
  };

  const handleActions = async (action) => {
    switch (action) {
      case 'REFRESH_01': {
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_pageNotLoadedProperlyClicked');
        if (onRefreshClick) {
          onRefreshClick();
        }
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.NEED_HELP.REFRESH_CLICKED);
        break;
      }
      case NeedHelpScreens.CHANGE_USERNAME: {
        setShowLoader(true);
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_wantDifferentUsername');
        const response = await http.get(railsConfig.changeUserNameURL);
        // const response = await http.get('http://10.66.39.54/pwa/mobile/gfthelp/changeUsername');
        if (!response.error) {
          setShowLoader(false);
          setHeader(response.headerText);
          setCurrentScreen(action);
          setDesc(response.bodyList);
          setBtnText(response.buttonText);
          setFooterText(response.footerText);
        } else {
          setErrorState(action);
        }
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.NEED_HELP.CHANGE_USERNAME_CLICKED);
        break;
      }
      case NeedHelpScreens.CAPTCHA_SCREEN: {
        setShowLoader(true);
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_whatIsCaptchaClicked');
        const response = await http.get(railsConfig.whatIsCaptchaURL);
        // const response = await http.get('http://10.66.39.54/pwa/mobile/gfthelp/captcha');
        if (!response.error) {
          setShowLoader(false);
          setHeader(response.headerText);
          setCurrentScreen(action);
          setDesc(response.bodyList);
          setBtnText(response.buttonText);
          setImageVal(captchaImage || response.imageLink);
          setFooterText(response.footerText);
        } else {
          setErrorState(action);
        }
        break;
      }
      case NeedHelpScreens.OTHER_ISSUES: {
        setShowLoader(true);
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_otherIssuesClicked');
        const response = await postCall(railsConfig.feedbackFetchURL, { pageId: 'otherIssues' });
        // const response = await http.get('http://10.66.39.54/pwa/mobile/gfthelp/feedback/fetch');
        if (!response.error) {
          setShowLoader(false);
          setHeader(response?.headerText || _label('hear_from_you'));
          setOtherIssueText('');
          setCurrentScreen(action);
          setShowBtnLoader(false);
          setBtnText(response?.buttonText || _label('send_feedback'));
          setFooterText(response?.footerText);
          setDesc([]);
          setImageVal(null);
        } else {
          setErrorState(action);
        }
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.NEED_HELP.OTHER_ISSUES_CLCIKED);
        break;
      }
      case NeedHelpScreens.CANCEL_SCREEN: {
        setShowLoader(true);
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_cancelBookingClicked');
        const response = await http.get(railsConfig.whyCancelFetchURL);
        // const response = await http.get('http://10.66.39.54/pwa/mobile/gfthelp/cancelBooking/fetch');
        if (!response.error) {
          setShowLoader(false);
          setHeader(response?.headerText || _label('need_help'));
          setCancelOptionsRes(response);
          setCurrentScreen(NeedHelpScreens.CANCEL_SCREEN);
          setShowBtnLoader(false);
          setBtnText(response?.buttonText || _label('submit', { uppercase: true }));
          setDesc(response?.bodyTextList || [_label('let_us_know')]);
          setImageVal(null);
          setOtherIssueText('');
          setEditableInput(false);
          setCurrentRadioSelected(null);
        } else {
          setErrorState(action);
        }
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.NEED_HELP.CANCEL_BOOKING_CLICKED);
        break;
      }
      case NeedHelpScreens.FORGOT_PASSWORD: {
        enablePasswordReader();
        showPasswordReminder(true);
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_forgotPasswordClicked');
        _dismissModal();
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.NEED_HELP.FORGOT_PASSWORD_CLICKED);
        break;
      }
      case NeedHelpScreens.INVALID_CAPTCHA: {
        setShowLoader(true);
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_invalidCaptcha');
        const response = await http.get(railsConfig.invalidCaptcha);
        // const response = await http.get('http://10.66.39.54/pwa/mobile/gfthelp/cancelBooking/fetch');
        if (!response.error) {
          setShowLoader(false);
          setHeader(response.headerText);
          setCurrentScreen(action);
          setDesc(response.bodyList);
          setBtnText(response.buttonText || _label('got_it', { uppercase: true }));
          setImageVal(ASSETS.captchaNew || response.imageLink);
          setFooterText(response.footerText);
        } else {
          setErrorState(action);
        }
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.NEED_HELP.INVALID_CAPTCHA_CLICKED);
        break;
      }
      case NeedHelpScreens.WRONG_CREDENTIALS: {
        setShowLoader(true);
        trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments_NeedHelp_wrongCredentials');
        const response = await postCall(railsConfig.wrongCredentials, { pageId: 'wrongCredentials' });
        // const response = await http.get('http://10.66.39.54/pwa/mobile/gfthelp/cancelBooking/fetch');
        if (!response.error) {
          setShowLoader(false);
          setHeader(response.headerText);
          setCurrentScreen(action);
          setDesc(response.bodyList);
          setBtnText(response.buttonText || _label('got_it', { uppercase: true }));
          setFooterText(response.footerText);
        } else {
          setErrorState(action);
        }
        logGftPdtEventsToPdt(GFT_PDT_EVENTS.NEED_HELP.INVALID_CAPTCHA_CLICKED);
        break;
      }
      default: {
        setErrorState(action);
      }
    }
  };

  const handleBtnClick = async () => {
    try {
      if (currentScreen === NeedHelpScreens.CHANGE_USERNAME ||
        currentScreen === NeedHelpScreens.CAPTCHA_SCREEN ||
        currentScreen === NeedHelpScreens.OTHER_ISSUES_COMPLETE) {
        _dismissModal();
        _closeNeedHelp();
      } else if (currentScreen === NeedHelpScreens.CANCEL_SCREEN_COMPLETE) {
        callbackFunc(`${RAILS_CALL_BACK_URL_PWA_SUCCESS + mmtId}?bookingStatus=F`);
      } else if (currentScreen === NeedHelpScreens.OTHER_ISSUES) {
        setShowBtnLoader(true);
        setBtnText('');
        setEditableInput(false);
        const res = await postCall(
          railsConfig.feedbackPostURL,
          // 'http://10.66.39.54/pwa/mobile/gfthelp/feedback/post',
          {
            pageId: 'otherIssues',
            feedbackText: otherIssueText,
            mmtId,
          },
        );
        if (!res.error) {
          setCurrentScreen(NeedHelpScreens.OTHER_ISSUES_COMPLETE);
          setShowBtnLoader(false);
          setBtnText('OK');
          setDesc([_label('thanks_for_feedback')]);
          setImageVal(null);
          setOtherIssueText('');
          setEditableInput(true);
        } else {
          setErrorState();
        }
      } else if (currentScreen === NeedHelpScreens.CANCEL_SCREEN) {
        setShowBtnLoader(true);
        setBtnText('');
        setEditableInput(false);
        await _alertUser();
      } else if (currentScreen === NeedHelpScreens.WRONG_CREDENTIALS) {
        showPasswordReminder(true);
        // trackNeedHelpPageEvent('mob_rail_irctc_password_after_payments
        // _NeedHelp_forgotPasswordClicked');
        _dismissModal();
      } else {
        // resetState();
        _closeNeedHelp();
      }
    } catch (e) {
      console.log(e);
    }
  };

  const handleRadioClick = (val) => {
    setCurrentRadioSelected(val);
    if (val === 'OTHER_ISSUES_CAN_01') {
      setEditableInput(true);
    } else {
      setEditableInput(false);
      setOtherIssueText('');
    }
  };

  const _alertUser = async () => {
    const action = await promiseAlert('', _label('cancel_confirm_prompt'), _label('cancel_confirm'), _label('cancel_deny'));
    if (action === _label('cancel_confirm')) {
      const selectedCancelOption = cancelOptionsRes.optionsTextList.filter(item => item.id === currentRadioSelected);
      const currentRadioSelectedValue = selectedCancelOption[0].text;
      const feedback = otherIssueText?.trim() ? `${currentRadioSelectedValue} - ${otherIssueText.trim()}` : currentRadioSelectedValue;
      const res = await postCall(
        railsConfig.whyCancelPostURL,
        // 'http://10.66.39.54/pwa/mobile/gfthelp/cancelBooking/postFeedback',
        {
          pageId: 'cancelBooking',
          feedbackText: feedback,
          mmtId,
        },
      );
      if (!res.error) {
        setCurrentScreen(NeedHelpScreens.CANCEL_SCREEN_COMPLETE);
        setShowBtnLoader(false);
        setBtnText('OK');
        setDesc([_label('thanks_for_feedback')]);
        setImageVal(null);
        setOtherIssueText('');
        setEditableInput(true);
      } else {
        setErrorState();
      }
      callbackFunc(`${RAILS_CALL_BACK_URL_PWA_SUCCESS + mmtId}?bookingStatus=F`);
    } else {
      resetState();
    }
  };
  const inputBoxOpacity = editableInput ? 1 : 0.4;
  const btnDisabled = (currentScreen === NeedHelpScreens.CANCEL_SCREEN && !currentRadioSelected) ||
                      (currentScreen === NeedHelpScreens.OTHER_ISSUES && !otherIssueText) ||
                      (currentScreen === NeedHelpScreens.CANCEL_SCREEN && currentRadioSelected === 'OTHER_ISSUES_CAN_01' && !otherIssueText);
  try {
    return (
      <React.Fragment>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={{flex: 1}} onPress={() => {_closeNeedHelp();}} />
        </View>
        {!showloader &&
        <View style={styles.modalContentContainer}>
          <View>
            <Text style={[styles.headerText, fontStyle('black'), getLineHeight(20)]}>{header || _label('need_help')}</Text>
            <View style={styles.issueContainer}>
              {currentScreen === NeedHelpScreens.HOME &&
              optionResponse && optionResponse.contentList &&
              optionResponse.contentList.map((item, index) => {
                let arrIcon;
                let ctaText;
                if (item.buttonText === 'ARROW_01') {
                  arrIcon = blueIcon;
                } else {
                  ctaText = item.buttonText;
                }
                return (
                  <LabelBoxWithCtaIcon
                    key = {index}
                    labelText={item.text}
                    ctaText={ctaText}
                    ctaIcon={arrIcon}
                    ctaAction={item.functionality}
                    onTap={handleActions}
                    showCounter={disableRefreshButton && item.functionality === 'REFRESH_01'}
                  />);
                 })
              }
            </View>
            {currentScreen !== NeedHelpScreens.HOME &&
              <View>
                  {
                    desc.map(val => <Text style={[styles.desc, fontStyle('regular'), getLineHeight(14)]}>{val}</Text>)
                  }
                  {(currentScreen === NeedHelpScreens.CAPTCHA_SCREEN
                   || currentScreen === NeedHelpScreens.INVALID_CAPTCHA) && imageVal &&
                    <Image style={styles.captchaImgStyle} source={imageVal} />
                  }
                  {currentScreen === NeedHelpScreens.CANCEL_SCREEN &&
                   cancelOptionsRes && cancelOptionsRes.optionsTextList &&
                   cancelOptionsRes.optionsTextList.map(item => (<OptionWithRadio
                     val={item.text}
                     key={item.id}
                     selected={item.id === currentRadioSelected}
                     handleRadioClick={() => handleRadioClick(item.id)}
                   />
                                              ))
                  }
                  {(currentScreen === NeedHelpScreens.OTHER_ISSUES ||
                    currentScreen === NeedHelpScreens.CANCEL_SCREEN) &&
                    <SimpleTextInput
                      editable={editableInput}
                      minHeight={137}
                      maxLength={300}
                      multiline
                      textAlignVertical="top"
                      numberOfLines={isIos() ? null : 6}
                      textAlign="left"
                      value={otherIssueText}
                      style={[styles.otherIssuesInputContainer, {opacity: inputBoxOpacity}]}
                      placeholder={_label(LABELS.FEEDBACK_PLACEHOLDER)}
                      onChangeText={(text) => {
                        setOtherIssueText(text);
                        checkEnglishKeyboard(null,text,null,setEnglishErrorMessage);
                      }}
                      scrollEnabled
                      onBlur={() => { Keyboard.dismiss(); }}
                      error = {englishErrorMessage}
                    />
                  }
              </View>
            }
            {footerText &&
              <Text style={[styles.footerTextStyle, fontStyle('regular'), getLineHeight(14)]}>{footerText}</Text>
            }
          </View>
          {currentScreen !== NeedHelpScreens.HOME &&
            <View>
              <BtnGeneric
                btnText={btnText}
                showBtnLoader={showBtnLoader}
                clickAction={handleBtnClick}
                disabled={btnDisabled}
              />
            </View>
          }
        </View>
        }
        {showloader &&
          <View style={styles.modalContentContainer}>
            <Loader />
          </View>
        }
      </React.Fragment>
    );
  } catch (e) {
    console.log(e);
    setErrorState();
  }
}

Modal.propTypes = {
  onRefreshClick: PropTypes.func.isRequired,
  showPasswordReminder: PropTypes.func.isRequired,
  _dismissModal: PropTypes.func.isRequired,
  optionResponse: PropTypes.object,
  callbackFunc: PropTypes.func,
  cancelBooking: PropTypes.func,
  _closeNeedHelp: PropTypes.func,
  mmtId: PropTypes.string,
  disableRefreshButton: PropTypes.bool,
  enablePasswordReader: PropTypes.bool,
  logGftPdtEventsToPdt: PropTypes.func,
  
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  modalContainer: {
    flex: 1,
    opacity: 0.4,
    backgroundColor: colors.black,
  },
  modalContentContainer: {
    backgroundColor: colors.white,
    paddingVertical: 15,
    paddingHorizontal: 16,
    minHeight: 200,
    display: 'flex',
    justifyContent: 'space-between',
  },
  headerText: {
    fontSize: 20,
  },
  issueContainer: {
    marginTop: 20,
  },
  desc: {
    fontSize: 14,
    color: colors.black,
    marginBottom: 15,
  },
  captchaImgStyle: {
    height: 120,
    width: 250,
    marginHorizontal: 35,
    marginVertical: 15,
  },
  otherIssuesInputContainer: {
    borderRadius: 4,
    borderColor: colors.lightGrey,
    borderWidth: 1,
    marginTop: 0,
    backgroundColor: colors.grey13,
    maxHeight: 137,
  },
  footerTextStyle: {
    color: colors.lightTextColor,
    fontSize: 14,
    marginTop: 10,
  },
});
