import React from 'react';
import { View, Text, StyleSheet, TextStyle, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { TextStyleBE } from '../../../types/railofy.types';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { Actions } from '../../../navigation';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import railofyOfferIcon from '@mmt/legacy-assets/src/railofy_offer_icon.webp';

interface PassengerDetailsInterface {
  travellerName: string;
  travellerAge: number | string;
  travellerGender: string;
  travellerSeatDetails: string;
}

interface ConfirmationGuaranteeDetailsProps {
  confirmationGuaranteeDetails: {
    tagText: string;
    congratulationsText: string;
    passengerDetails: PassengerDetailsInterface[];
    guaranteeText: string;
    fareInfo: {
      key: TextStyleBE;
      value: TextStyleBE;
    }[];
    offerText: string;
    additionalText: string;
    additionalTextURL: string;
  },
  id?: string;
}

export default function ConfirmationGuaranteeDetails(props: ConfirmationGuaranteeDetailsProps) {
  const {
    confirmationGuaranteeDetails: {
      tagText,
      congratulationsText,
      passengerDetails,
      guaranteeText,
      fareInfo,
      additionalText,
      additionalTextURL,
      offerText,
    } = {},
  } = props;

  const onMoreDetailsPressed = () => {
    Actions.openWebView({
      url: additionalTextURL,
      headerText: 'MakeMyTrip',
      headerIcon: backIcon,
    });
  };

  const zeroZero = { x: 0.0, y: 0.0 };
  const oneZero = { x: 1.0, y: 0.0 };

  return (
    <View style={styles.container} testID={props?.id}>
      <LinearGradient // positioned absolute
        colors={['#c86dd7', '#3023ae']}
        start={zeroZero}
        end={oneZero}
        style={styles.tag}
      >
        <Text style={[styles.tagTextStyle, fontStyle('bold'), getLineHeight(12)]}>{tagText}</Text>
      </LinearGradient>
      <View style={styles.contentContainer}>
        <Text style={[styles.congratsText, fontStyle('regular'), getLineHeight(13)]}>{congratulationsText}</Text>
        <View style={styles.rowContainer}>
          {passengerDetails?.map((traveller, index) => {
            const {
              travellerName, travellerGender, travellerAge, travellerSeatDetails,
            } = traveller;
            return (
              <View key = {index} style={styles.row}>
                <Text style={[styles.travellerName, fontStyle('bold', 'en'), getLineHeight(13)]}>
                  {index + 1}. {travellerName}
                  <Text style={[styles.travellerAgeGender, fontStyle('regular','en'), getLineHeight(13)]}> {travellerAge} Y, {travellerGender}</Text>
                </Text>
                <Text style={[styles.travellerSeatDetails, fontStyle('bold'), getLineHeight(13)]}>
                  {travellerSeatDetails}
                </Text>
              </View>
            );
          })}
        </View>
        <View style={styles.fareInfoContainer}>
          <Text style={[styles.guaranteeText, fontStyle('medium'), getLineHeight(14)]}>{guaranteeText}</Text>
          <View style={styles.rowContainer}>
            {fareInfo?.map((fare, index) => {
              const { key, value } = fare;
              const keyStyle = getTextStyle(key);
              const valueStyle = getTextStyle(key);
              return (
                <View key = {index} style={styles.row}>
                  <Text style={keyStyle}>{key.text}</Text>
                  <Text style={valueStyle}>{value.text}</Text>
                </View>
              );
            })}
          </View>
        </View>
        <View style={styles.offerContainer}>
          <Image source={railofyOfferIcon} style={styles.offerImage} />
          <View style={styles.offerTextContainer}>
            <Text style={[styles.offerText, fontStyle('regular'), getLineHeight(12)]}>
              {offerText}
            </Text>
            <Text style={[styles.linkText, fontStyle('bold'), getLineHeight(10)]} onPress={onMoreDetailsPressed}>
              {additionalText}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}

function getTextStyle(dataObject: TextStyleBE): TextStyle {
  return {
    ...fontStyle(dataObject.fontFamily || 'regular'),
    ...getLineHeight(Number(dataObject.fontSize) || 10),
    fontSize: Number(dataObject.fontSize) || 10,
    color: dataObject.color,
    letterSpacing: Number(dataObject.letterSpacing) || 0,
  };
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingTop: 12,
    paddingBottom: 10,
    position: 'relative',
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
  tag: {
    position: 'absolute',
    top: 4,
    left: 26,
    zIndex: 999999,
    paddingHorizontal: 8,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    height: 18,
  },
  tagTextStyle: {
    fontSize: 12,
    color: colors.white,
  },
  contentContainer: {
    borderWidth: 1,
    borderRadius: 4,
    borderColor: colors.governorBay,
    paddingHorizontal: 8,
    paddingTop: 18,
    // paddingBottom: 15,
  },
  congratsText: {
    fontSize: 13,
    color: colors.defaultTextColor,
  },
  rowContainer: {
    marginTop: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 2,
  },
  travellerName: {
    fontSize: 13,
    color: colors.black,
  },
  travellerAgeGender: {
    fontSize: 13,
    color: colors.defaultTextColor,
  },
  travellerSeatDetails: {
    fontSize: 13,
    color: colors.yello,
  },
  fareInfoContainer: {
    paddingVertical: 8,
    paddingHorizontal: 6,
    backgroundColor: colors.grey2,
    borderRadius: 4,
    marginTop: 15,
  },
  guaranteeText: {
    fontSize: 14,
    color: colors.defaultTextColor,
  },
  linkText: {
    color: colors.azure,
    fontSize: 10,
    marginTop: 4,
  },
  offerContainer: {
    flexDirection: 'row',
    marginTop: 18,
  },
  offerTextContainer: {
    flex: 1,
    marginLeft: 20,
  },
  offerText: {
    color: colors.defaultTextColor,
    fontSize: 12,
  },
  offerImage: {
    height: 55,
    width: 55,
    marginBottom: 5,
    marginLeft: 5,
  },
});
