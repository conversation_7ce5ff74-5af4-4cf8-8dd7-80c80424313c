import React from 'react';
import {ActivityIndicator, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';

const PostRedirectLoader = () => (
  <View style={{
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  }}
  >
    <LinearGradient
      colors={['#ffffff', '#ffffff']}
      start={{
        x: 0.0,
        y: 0.0,
      }}
      end={{
        x: 1.0,
        y: 0.0,
      }}
      style={styles.cta}
    >
      <ActivityIndicator
        styleAttr="Inverse"
        color={colors.azure}
        style={{
          margin: 4,
          height: 36,
          width: 36,
        }}
        size="large"
      />
    </LinearGradient>
    <View style={{paddingTop: 20}}>
      <Text style={[styles.textStyle, fontStyle('regular'), getLineHeight(16)]}>{_label('confirming_your_tickets')}</Text>
    </View>
    <View style={{paddingTop: 11}}>
      <Text style={[styles.textStyleItalic, fontStyle('regular'), getLineHeight(14)]}>{_label('full_refund_in_case_of_issue')}</Text>
    </View>
  </View>
);

const styles = ({
  cta: {
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    height: 36,
    width: 36,
    borderRadius: 100,
  },
  textStyle: {
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.black04,
  },
  textStyleItalic: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
    fontStyle: 'italic',
    color: colors.black04,
  },
});

export default PostRedirectLoader;
