import React from 'react';
import isUndefined from 'lodash/isUndefined';
import isEmpty from 'lodash/isEmpty';
import some from 'lodash/some';
import {
  Text,
  View,
  Image,
  ScrollView,
  StyleSheet,
  BackHandler,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import SadImage from '@mmt/legacy-assets/src/ic-illustration-sad-2x.webp';
import {connect} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import CloseIcon from '@mmt/legacy-assets/src/ic-headerclose-grey.webp';
import CheckBox from 'react-native-checkbox';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {setAllReqData} from '../Review/RailsReviewActions';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import fetch2 from '../../fetch2';
import railsConfig from '../../RailsConfig';
import {Actions} from '../../navigation';
import {checkEnglishKeyboard, fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import {_label} from '../../vernacular/AppLanguage';
import SimpleTextInput from '@mmt/legacy-commons/Common/Components/Inputs/SimpleTextInput';
import { isIos } from '../../Utils/device';

import CB_ENABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-active.webp';
import CB_DISABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-inactive.webp';
import PropTypes from 'prop-types';

class WhatWentWrong extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      reasonsList: [],
      selectedList: [],
      loading: true,
      somethingElseState: false,
      text: '',
      englishError : '',
    };
  }

  onHardBackPress = () => {
    if (isIos()) {
      ViewControllerModule.thankyouDismiss(this.props.rootTag);
    } else {
      Actions.rails();
    }
    return true;
  };

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onHardBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onHardBackPress);
  }

  async UNSAFE_componentWillMount() {
    try {
      const response = await fetch2(railsConfig.failureReasonUrl);

      if (!isUndefined(response.status) && response.status !== 200) {
        throw new Error('wrong Status code');
      }

      const failureReasonsObject = await response.json();

      if (!isUndefined(failureReasonsObject.status) && failureReasonsObject.status !== 200) {
        throw new Error('wrong Status code');
      }

      const {failureReasons: reasonList} = failureReasonsObject;

      this.setState({
        reasonsList: reasonList,
        loading: false,
      });
    } catch (e) {
      console.log('error', e);
      this.props.setData([], this.props.mmtId);
    }
  }

  updateSelectedItemList = (item) => {
    const {selectedList} = this.state;
    if (selectedList.some(element => (element.id === item.id))) {
      this.setState({selectedList: selectedList.filter(element => element.id !== item.id)});
    } else {
      this.setState({selectedList: [...selectedList, item]});
    }
  };

  goToReviewPage = () => {
    this.setState({loading: true});
    const {selectedList} = this.state;
    let returnList;
    if (!isEmpty(this.state.text)) {
      returnList = [...selectedList, {description: this.state.text}];
    } else {
      returnList = selectedList;
    }
    this.props.setData(returnList, this.props.mmtId);
  };

  onTextInputChange = (text) => {
    this.setState({text});
    checkEnglishKeyboard('englishError',text,this);
  };

  onTextInputFocus = () => {
    this.setState({somethingElseState: true});
    if (!isEmpty(this._scrollView)) {
      this._scrollView.scrollToEnd();
    }
  };

  onTextInputBlur = () => {
    if (!isEmpty(this._checkSomeElse)) {
      this._checkSomeElse.onChange();
    }
  };

  render() {
    const {selectedList, reasonsList, loading} = this.state;
    if (loading) {
      return (
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
          <Spinner size={30} color={colors.azure} />
        </View>
      );
    }
    return (
      <ScrollView ref={(ref) => { this._scrollView = ref; }}>
        <View style={styles.container}>
          <View style={styles.closeImageContainer}>
            <TouchableRipple onPress={this.onHardBackPress}>
              <Image
                style={styles.closeImage}
                source={CloseIcon}
              />
            </TouchableRipple>
          </View>
          <View style={styles.sadImageContainer}>
            <Image
              style={styles.sadImage}
              source={SadImage}
            />
            <Text style={[styles.whatWentWrongText, fontStyle('bold','en'), getLineHeight(16)]}>
              {_label('what_went_wrong')}
            </Text>
            <Text style={[styles.pleaseSelect, fontStyle('regular'), getLineHeight(14)]}>
              {_label('please_select_all_that_apply')}
            </Text>
          </View>
        </View>
        <View style={styles.bottomContainer}>
          <View style={{marginBottom: 10}}>
            <Card style={{
              marginHorizontal: 20,
              paddingVertical: 20,
              borderRadius: 10,
              borderWidth: StyleSheet.hairlineWidth * 2,
              borderColor: colors.grayBg,
            }}
            >
              <View style={{marginHorizontal: 20}}>
                {
                    reasonsList.map(item => (
                      <CheckBox
                        ref={(ref) => { this._checkSomeElse = ref; }}
                        key={item.id}
                        label={item.description}
                        checked={some(selectedList, element => element.id === item.id)}
                        labelStyle={StyleSheet.flatten([ styles.checkBoxLabel, fontStyle('regular','en'), getLineHeight(16)])}
                        onChange={() => {
                          this.updateSelectedItemList(item);
                        }}
                        checkedImage={CB_ENABLED_IMAGE}
                        uncheckedImage={CB_DISABLED_IMAGE}
                        containerStyle={{marginBottom: 20}}
                      />))
                  }
                <CheckBox
                  ref={(ref) => { this._checkSomeElse = ref; }}
                  label={_label('something_else',{capitalize : true})}
                  checked={this.state.somethingElseState}
                  labelStyle={StyleSheet.flatten([ styles.checkBoxLabel, fontStyle('regular','en'), getLineHeight(16)])}
                  onChange={() => {
                    if (this.state.text === '') {
                      this.setState({somethingElseState: false});
                    } else {
                      this.setState({somethingElseState: true});
                    }
                    }}
                  checkedImage={CB_ENABLED_IMAGE}
                  uncheckedImage={CB_DISABLED_IMAGE}
                  containerStyle={{marginBottom: 20}}
                />
                <TouchableRipple onPress={() => {
                  this._checkSomeElse.onChange();
                  if (!isEmpty(this._textInput)) {
                    this._textInput.focus();
                  }
                }}
                >
                  <View style={styles.textInputContainer}>
                    <SimpleTextInput
                      value = {this.state.text}
                      inputStyle={styles.issueFeedbackTextStyle}
                      errorContainerStyle={styles.englishTypeErrorContainer}
                      refProp ={(ref) => { this._textInput = ref; }}
                      underlineColorAndroid="transparent"
                      onChangeText={this.onTextInputChange}
                      onBlur={this.onTextInputBlur}
                      onFocus={this.onTextInputFocus}
                      placeholder={_label('describe_other_issue')}
                      multiline
                      maxLength={250}
                      numberOfLines={4}
                      error = {this.state.englishError}
                    />
                  </View>
                </TouchableRipple>
                <View style={{marginBottom: 40}} />
              </View>
            </Card>
            <TouchableRipple onPress={this.goToReviewPage}
            >
              <View style={styles.linearGradContainer}>
                <LinearGradient
                  style={styles.linearGrad}
                  colors={['#53B2FE', '#065AF3']}
                  start={{x: 0.0, y: 0.0}}
                  end={{x: 1.0, y: 0.0}}
                >
                  <View>
                    <Text style={[styles.proceedText, fontStyle('regular'), getLineHeight(16)]}>
                      {_label('proceed', { uppercase: true })}
                    </Text>
                  </View>
                </LinearGradient>
              </View>
            </TouchableRipple>
          </View>
          {
            isIos() &&
            <KeyboardSpacer />
          }
          <View style={{height: 50}} />
        </View>
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'flex-start',
    flexDirection: 'column',
    flex: 3,
  },
  issueFeedbackTextStyle : {
    borderWidth : 0,
    marginBottom: 5,
    paddingBottom : 0,
  },
  englishTypeErrorContainer : {
    marginTop : 5,
    marginBottom : 5,
  },
  closeImageContainer: {
    height: 40,
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-end',
  },
  closeImage: {
    height: 32,
    width: 32,
    marginLeft: 20,
  },
  sadImageContainer: {
    flex: 4,
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 30,
  },
  sadImage: {
    height: 100,
    width: 150,
  },
  whatWentWrongText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.black,
    marginVertical: 10,
  },
  pleaseSelect: {
    fontSize: 14,
  },
  bottomContainer: {
    flex: 5,
    justifyContent: 'flex-start',
  },
  checkBoxLabel: {
    fontSize: 16,
    color: colors.black04,
    lineHeight: 22,
  },
  textInputContainer: {
    height: 80,
    borderWidth: StyleSheet.hairlineWidth * 2,
    marginLeft: 30,
    borderRadius: 5,
    borderColor: colors.black30,
  },
  linearGradContainer: {
    height: 68,
    width: '100%',
    position: 'absolute',
    bottom: -20,
    elevation: 5,
  },
  linearGrad: {
    borderRadius: 100,
    height: 54,
    marginHorizontal: 80,
    marginVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  proceedText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    backgroundColor: colors.transparent,
  },
});

WhatWentWrong.propTypes = {
  rootTag: PropTypes.any,
  setData: PropTypes.func,
  mmtId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

const mapDispatchToProps = dispatch => ({
  setData: (data, mmtId) => { dispatch(setAllReqData(data, mmtId)); },
});

export default connect(null, mapDispatchToProps)(WhatWentWrong);
