import React, { useState } from 'react';
import { Text, View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { getMmtAuth } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { Actions } from '../../../navigation';
import { RectangularGradientButton } from '../../Common/Buttons';
import {
  getTrackingParam,
  performBookingRetry,
} from './utils';
import { _label } from '../../../vernacular/AppLanguage';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import styles from './styles';
import { UserPreferenceErrorHandlingChildProps } from './types';
import retryIcon from '@mmt/legacy-assets/src/ic_retry_white.webp';

export const UserPreferenceErrorHandlingMessage = (
  props: UserPreferenceErrorHandlingChildProps,
) => {
  const [loading, setLoading] = useState(false);
  const { bookingRetryDetails, onBookingRetryError, bookingId } = props;
  const { retryErrorDetails } = bookingRetryDetails || {};
  const {
    retryErrorCode,
    retryErrorMessageText,
    retryErrorMessageSubText,
    cancelButtonText,
    continueButtonText,
  } = retryErrorDetails || {};
  const trackingParamEvar15 = getTrackingParam(retryErrorCode);
  const { evar15, continueTracking, cancelTracking, retryTracking } = trackingParamEvar15 || {};

  const retryBooking = async () => {
    const {irctcUserId, retryId } = bookingRetryDetails || {};
    const mmtAuth = await getMmtAuth();
    const response = await performBookingRetry(bookingId, retryId, mmtAuth, irctcUserId);
    setLoading(false);
    if (response.showBookingContinueError && response.bookingContinueError) {
      onBookingRetryError && onBookingRetryError(response.bookingContinueError);
    }
  };
  const onContinuePress = () => {
    setLoading(true);
    retryBooking();
    if (evar15 && continueTracking) {
      trackClickEventProp61(evar15, continueTracking);
    }
  };
  const onCancelPress = () => {
    if (evar15 && cancelTracking) {
      trackClickEventProp61(evar15, cancelTracking);
    }
    Actions.pop();
    Actions.railsBookingFailure();
  };
  const onRetryPress = () => {
    setLoading(true);
    retryBooking();
    if (evar15 && retryTracking) {
      trackClickEventProp61(evar15, retryTracking);
    }
  };

  if (loading) {
    return (
      <View style={styles.errorContainer}>
        <Spinner size={30} color={colors.primary} />
        <Text style={styles.description}>{_label('please_wait')}</Text>
      </View>
    );
  }

  return (
    <>
      {retryErrorCode?.includes('RESERVATION_CHOICE') ? (
        <View style={styles.contentContainer}>
          <Text style={[fontStyle('bold'), styles.title]}>{retryErrorMessageText}</Text>
          <Text style={[fontStyle('regular'), styles.description]}>{retryErrorMessageSubText}</Text>
          <View style={styles.btnContainer}>
            <TouchableRipple onPress={onCancelPress}>
              <View style={styles.btnStyle}>
                <Text style={[styles.btnTextStyle, fontStyle('black'), getLineHeight(16)]}>
                  {cancelButtonText ?? _label('cancel_irctc_booking')}
                </Text>
              </View>
            </TouchableRipple>
            <RectangularGradientButton label={continueButtonText ?? _label('$continue')} onPress={onContinuePress} />
          </View>
        </View>
      ) : (
        <View style={styles.errorContainer}>
          <Text style={[fontStyle('black'), styles.errorTitle]}>{retryErrorMessageText}</Text>
          <Text style={[fontStyle('regular'), styles.description]}>{retryErrorMessageSubText}</Text>
          <RectangularGradientButton
            label={_label('retry')}
            onPress={onRetryPress}
            btnImage={retryIcon}
            alignImageAtTheEnd
            gradientStyle={styles.marginTop22}
            disabled={!retryErrorCode?.includes('RETRY')}
          />
          {!retryErrorCode?.includes('RESERVATION_CHOICE') && (
            <Text style={[fontStyle('regular'), styles.safeMoney, styles.marginTop22]}>
              {_label('amount_is_safe')}
            </Text>
          )}
        </View>
      )}
    </>
  );
};
