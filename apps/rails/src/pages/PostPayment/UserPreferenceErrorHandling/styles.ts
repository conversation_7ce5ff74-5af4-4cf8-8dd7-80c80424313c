import { StyleSheet, Platform } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 44,
  },
  footerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorImage: {
    width: 78,
    height: 78,
    resizeMode: 'contain',
    marginBottom: 13,
  },
  serverErrorImage: {
    width: 192,
    height: 192,
    resizeMode: 'contain',
  },
  bookingPending: {
    color: colors.defaultTextColor,
    fontSize: 24,
    lineHeight: 28.8,
  },
  safeMoney: {
    color: colors.lightGreen4,
    fontSize: 16,
    lineHeight: 18.75,
    marginTop: 8,
    textAlign: 'center',
  },
  title: {
    color: colors.black,
    fontSize: 14,
    lineHeight: 18,
  },
  description: {
    color: colors.defaultTextColor,
    fontSize: 14,
    lineHeight: 18,
    marginTop: 4,
  },
  errorTitle: {
    color: colors.black,
    fontSize: 20,
    lineHeight: 20,
  },
  roundedBox: {
    borderColor: colors.lightGray,
    borderWidth: 1,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    marginTop: 16,
  },
  contentContainer: {
    backgroundColor: colors.white,
    height: 146,
    marginHorizontal: 36,
    padding: 16,
    ...getPlatformElevation(Platform.select({ android: 2, default: 0.5 })),
    shadowRadius: 2,
    shadowColor: colors.black,
    borderRadius: 5,
    marginTop: 55,
    marginBottom: 74,
  },
  errorContainer: {
    backgroundColor: colors.white,
    height: 146,
    marginHorizontal: 30,
    padding: 16,
    marginTop: 55,
    marginBottom: 74,
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnContainer: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'space-between',
  },
  btnStyle: {
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnTextStyle: {
    color: colors.azure,
    fontSize: 16,
    lineHeight: 18,
  },
  marginTop22: {
    marginTop: 22,
  },
});
