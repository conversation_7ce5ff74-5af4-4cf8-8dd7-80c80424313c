import React from 'react';
import { Text, View } from 'react-native';
import { _label } from '../../../vernacular/AppLanguage';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import styles from './styles';
import { UserPreferenceErrorHandlingChildProps } from './types';

export const UserPreferenceErrorHandlingFooter = ({
  retryErrorDetails,
}: UserPreferenceErrorHandlingChildProps) => {
  if (!retryErrorDetails?.retryErrorCode?.includes('RESERVATION_CHOICE')) {
    return null;
  }
  return (
    <View style={styles.footerContainer}>
      <Text style={[fontStyle('bold'), styles.title]}>{_label('do_not_back_press')}</Text>
      <View style={styles.roundedBox}>
        <Text style={[fontStyle('bold'), styles.title]}>
          {`${_label('next_step')} `}
          <Text style={[fontStyle('regular'), styles.description]}>
            {_label('enter_irctc_password')}
          </Text>
        </Text>
      </View>
    </View>
  );
};
