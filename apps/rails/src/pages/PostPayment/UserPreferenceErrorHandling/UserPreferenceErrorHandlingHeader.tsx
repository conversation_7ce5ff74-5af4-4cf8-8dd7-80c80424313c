import React from 'react';
import { Image, Text, View } from 'react-native';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import styles from './styles';
import { UserPreferenceErrorHandlingChildProps } from './types';
import additionalPreferencesIcon from '@mmt/legacy-assets/src/ic_warning_grey.webp';
import serverErrorIcon from '@mmt/legacy-assets/src/ic_servererror.webp';

export const UserPreferenceErrorHandlingHeader = ({
  retryErrorDetails,
}: UserPreferenceErrorHandlingChildProps) => {
  if (!retryErrorDetails) {
    return null;
  }
  return (
    <View style={styles.headerContainer}>
      {retryErrorDetails.retryErrorCode.includes('RESERVATION_CHOICE') ? (
        <>
          <Image source={additionalPreferencesIcon} style={styles.errorImage} />
          <Text style={[fontStyle('bold'), styles.bookingPending]}>
            {retryErrorDetails.retryBookingStatusText}
          </Text>
          <Text style={[fontStyle('black'), styles.safeMoney]}>
            {retryErrorDetails.retryBookingStatusSubText}
          </Text>
        </>
      ) : (
        <Image source={serverErrorIcon} style={styles.serverErrorImage} />
      )}
    </View>
  );
};
