import React, { useCallback, useEffect, useState } from 'react';
import { BackHandler, Text, View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { trackPageVisits } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import CustomizedSomethingWentWrong from '../../ErrorPages/CustomizedSomethingWentWrong';
import { _label } from '../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import styles from './styles';
import { BookingRetryDetails, UserPreferenceErrorHandlingProps } from './types';
import { UserPreferenceErrorHandlingFooter } from './UserPreferenceErrorHandlingFooter';
import { UserPreferenceErrorHandlingHeader } from './UserPreferenceErrorHandlingHeader';
import { UserPreferenceErrorHandlingMessage } from './UserPreferenceErrorHandlingMessage';
import { getBookingRetryDetails, getTrackingParam } from './utils';
import { Actions } from '../../../navigation';

export const UserPreferenceErrorHandling = (props: UserPreferenceErrorHandlingProps) => {
  const { bookingId } = props;
  const [loading, setLoading] = useState(false);
  const [bookingRetryDetails, setBookingRetryDetails] = useState<BookingRetryDetails | null>(null);
  const [bookingRetryError, setBookingRetryError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchErrorDetails() {
      try {
        setLoading(true);
        const response = await getBookingRetryDetails(bookingId);
        setLoading(false);
        setBookingRetryDetails(response);
        if (response?.retryErrorDetails?.retryErrorCode) {
          const trackingParamEvar15 = getTrackingParam(response?.retryErrorDetails.retryErrorCode)
            ?.evar15;
          if (trackingParamEvar15) {
            trackPageVisits(trackingParamEvar15, {});
          }
        } else if (response?.errorDetails?.errorMessage) {
          setBookingRetryError(response?.errorDetails?.errorMessage);
        }
      } catch (error: unknown) {
        let errorMessage = _label('error_message');
        if (typeof error === 'string') {
          errorMessage = error.toUpperCase();
        } else if (error instanceof Error) {
          errorMessage = error.message;
        }
        setBookingRetryError(errorMessage);
      }
    }
    // eslint-disable-next-line
    const errorDetailsTimeout = setTimeout(fetchErrorDetails, 5000); //5 sec delay as BE needs time to sync
    BackHandler.addEventListener('hardwareBackPress', onHardBackPress);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onHardBackPress);
      clearTimeout(errorDetailsTimeout);
    };
  }, []);

  const onHardBackPress = useCallback(() => {
    Actions.rails();
    return true;
  }, []);

  const onBookingRetryError = (errorMessage: string) => setBookingRetryError(errorMessage);
  if (loading) {
    return (
      <View style={styles.container}>
        <Spinner size={30} color={colors.primary} />
        <Text style={styles.description}>{_label('loading')}</Text>
      </View>
    );
  }

  if (bookingRetryError) {
    return (
      <CustomizedSomethingWentWrong
        header=""
        subHeader={bookingRetryError}
        buttonText={_label('go_to_home_page', { uppercase: true })}
      />
    );
  }

  if (!bookingRetryDetails) {
    return null;
  }
  const { retryErrorDetails } = bookingRetryDetails;

  return (
    <View style={styles.container}>
      <UserPreferenceErrorHandlingHeader retryErrorDetails={retryErrorDetails} />
      <UserPreferenceErrorHandlingMessage
        bookingRetryDetails={bookingRetryDetails}
        onBookingRetryError={onBookingRetryError}
        bookingId={bookingId}
      />
      <UserPreferenceErrorHandlingFooter retryErrorDetails={retryErrorDetails} />
    </View>
  );
};
