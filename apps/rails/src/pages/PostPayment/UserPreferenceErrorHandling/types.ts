export interface UserPreferenceErrorHandlingProps {
  bookingId: string;
  errorMessage?: string;
  errorCode?: string;
}

export interface ErrorMessageObj {
  title: string;
  description: string;
  errorMessage: string;
  id: string;
  evar15?: string;
  cancelTracking?: string;
  continueTracking?: string;
  retryTracking?: string;
  type: string | 'userPreference' | 'agentTransaction' | 'other';
}

export interface UserPreferenceErrorHandlingChildProps {
  bookingRetryDetails?: BookingRetryDetails;
  retryErrorDetails?: RetryErrorDetails;
  onBookingRetryError?: (errorMessage: string) => void;
  bookingId?: string;
}

export interface ErrorDetails {
  errorCode: string;
  errorMessage: string;
}

export interface BookingRetryDetails {
  mmtId: string;
  irctcUserId: string;
  allowIrctcUserIdChange: boolean;
  retryStartTime: number;
  retryId: string;
  retryTimeoutInMins: number;
  bookingDetails: BookingDetails;
  retryErrorDetails: RetryErrorDetails;
  errorDetails: ErrorDetails;
}

export interface BookingDetails {
  trainName: string;
  trainNumber: string;
  journeyQuota: string;
  journeyClass: string;
  sourceStationCode: string;
  sourceStationName: string;
  destinationStationName: string;
  destinationStationCode: string;
  numberOfPassenger: number;
  boardingStationName: string;
  boardingStationCode: string;
  totalCollectibleAmount: number;
  journeyDate: number;
}

export interface RetryErrorDetails {
  retryErrorMessageText: string;
  retryErrorMessageSubText: string;
  retryErrorCode: RetryErrorCode;
  retryBookingStatusText: string;
  retryBookingStatusSubText: string;
  retryButtonContinueText: string;
  cancelButtonText?: string;
  continueButtonText?: string;
}

export type RetryErrorCode =
  | 'RESERVATION_CHOICE_RETRY_ERROR_CODE_01'
  | 'RESERVATION_CHOICE_RETRY_ERROR_CODE_02'
  | 'RESERVATION_CHOICE_RETRY_ERROR_CODE_03';

export interface TrackingParam {
  evar15?: string;
  cancelTracking?: string;
  continueTracking?: string;
  retryTracking?: string;
}

export interface RetryResponse {
  showBookingContinueError: boolean;
  bookingContinueError: string | null;
}
