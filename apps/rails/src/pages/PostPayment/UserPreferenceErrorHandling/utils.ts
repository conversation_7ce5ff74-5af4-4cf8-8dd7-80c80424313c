import fetch2 from '../../../fetch2';
import { BookingRetryDetails, RetryErrorCode, RetryResponse, TrackingParam } from './types';
import railsConfig from '../../../RailsConfig';
import { RAILS_MMT_ID, setDataToAsyncStorage } from '../../../Utils/RailsConstant';
import { Actions } from '../../../navigation';
import { isMweb } from '../../../Utils/device';
import { _label } from '../../../vernacular/AppLanguage';

export const getBookingRetryDetails = async (
  bookingId: string,
): Promise<BookingRetryDetails | null> => {
  try {
    const response = await fetch2(`${railsConfig.getBookingRetryDetailsURL}${bookingId}`);
    const data = await response?.json();
    if (data) {
      return data;
    } else {
      throw new Error(_label('error_message'));
    }
  } catch (error) {
    throw new Error(_label('error_message'));
  }
};

export const performBookingRetry = async (
  mmtId: string,
  retryId: string,
  mmtAuth: string,
  irctcUserName: string,
): Promise<RetryResponse> => {
  try {
    const res = await fetch2(railsConfig.retryUrl, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'mmt-auth': mmtAuth,
      },
      body: JSON.stringify({
        mmtId,
        retryId,
        irctcUserId: irctcUserName,
      }),
    });
    const data = await res.json();
    if (res.status === 200) {
      // TODO if irctcUserIdChange is false handle separately
      await setDataToAsyncStorage(RAILS_MMT_ID, mmtId);
      if (isMweb()) {
        Actions.openIrctcPage();
        return { showBookingContinueError: false, bookingContinueError: null };
      }
      Actions.confirmBooking({
        type: 'replace',
        mmtId: mmtId,
        irctcUserId: irctcUserName,
      });
      return { showBookingContinueError: false, bookingContinueError: null };
    } else {
      return {
        showBookingContinueError: true,
        bookingContinueError: data?.errorDetails?.errorMessage
          ? data?.errorDetails?.errorMessage
          : 'UNKNOWN ERROR',
      };
    }
  } catch (e) {
    return { showBookingContinueError: true, bookingContinueError: 'UNKNOWN ERROR' };
  }
};

export const getTrackingParam = (retryErrorCode: RetryErrorCode): TrackingParam | null => {
  switch (retryErrorCode) {
    case 'RESERVATION_CHOICE_RETRY_ERROR_CODE_01':
      return {
        evar15: 'rail_error_handling_no_lower_berth',
        cancelTracking: 'rail_error_handling_no_lower_berth_cancel',
        continueTracking: 'rail_error_handling_no_lower_berth_continue',
      };
    case 'RESERVATION_CHOICE_RETRY_ERROR_CODE_02':
      return {
        evar15: 'rail_error_handling_no_same_coach',
        cancelTracking: 'rail_error_handling_no_same_coach_cancel',
        continueTracking: 'rail_error_handling_no_same_coach_continue',
      };
    case 'RESERVATION_CHOICE_RETRY_ERROR_CODE_03':
      return {
        evar15: 'rail_error_handling_no_confirmed_berths',
        cancelTracking: 'rail_error_handling_no_confirmed_berth_cancel',
        continueTracking: 'rail_error_handling_no_confirmed_berth_continue',
      };
    default:
      return {
        evar15: retryErrorCode?.toLowerCase(),
        retryTracking: `${retryErrorCode?.toLowerCase()}_retry`,
      };
  }
};
