import React from 'react';
import { Actions } from '../../navigation';
import { Image, ScrollView, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import CommonHeader from '../User/Common/CommonHeader';
import styles from '../User/Common/UserVerificationCSS';
import {trackPageName} from '../../railsAnalytics';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import ASSETS from '../../Utils/Assets/RailsAssets';

class ForgotPasswordSuccessPage extends React.Component {
  UNSAFE_componentWillMount() {
    setTimeout(() => {
      Actions.confirmBooking();
    }, 3000);
    trackPageName(RAIL_EVENTS.USER.MMT_RAIL_POST_PAYMENT_RETRIEVE_PASSWORD_SUCCESS);
  }

  render() {
    return (<View style={styles.container}>
        <View style={styles.userFlowContainer}>
          <ScrollView bounces={false}>

            <CommonHeader
              title={this.props.labels.title}
              titleStyle={styles.headerStyle}
              showCloseButton={false}
              titleTextStyle={{textAlign: 'center'}}
            />

            <View style={styles.marginHorizontal24}>
              <View style={[styles.paddingTop120, styles.alignItemsCenter]}>
                <Image
                  style={{
                    width: 102,
                    height: 102,
                  }}
                  source={ASSETS.icFpTick}
                />
              </View>


              <View style={[styles.paddingTop120, styles.alignItemsCenter]}>
                <Text style={[styles.latoRegularText16, {
                  color: colors.black,
                  textAlign: 'center',
                }]}
                >{this.props.labels.smsAffirmation}
                </Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
    );
  }
}

ForgotPasswordSuccessPage.propTypes = {
  mobile: PropTypes.string.isRequired,
  labels: PropTypes.object.isRequired,
};
ForgotPasswordSuccessPage.navigationOptions = {
  header: null,
};

const mapStateToProps = ({railsVernacular: {texts}}, ownProps) => ({
  labels: {
    title: texts.ns_new_pwd_requested,
    smsAffirmation: texts.ns_you_will_get_sms_on_$mobile.replace('$mobile', ownProps.mobile),
  },
  ...ownProps,
});

export default connect(mapStateToProps)(ForgotPasswordSuccessPage);
