import React from 'react';
import { Actions } from '../../navigation';
import { View, Text} from 'react-native';
import {getCallbackURLArray} from './PostPaymentUtils';
import getIrctcRedirectHTML, { getIrctcBookingParams } from './IrctcRedirect';
import PreRedirectLoader from './PreRedirectLoader';
import ConfirmBookingWebView from './BookingConfirmationWebView';
import {trackRailPaymentDone} from '../../railsAnalytics';
import {
  AbConfigKeyMappings,
  getPokusConfigWaitingPromise,
  getPokusConfig,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {PokusLobs} from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import NewConfirmBookingWebView from './NewConfirmBookingWebView';
import logAction from '@mmt/legacy-commons/Helpers/actionsLogger';
import IrctcPageHeaderWithRefresh from './Components/IrctcPageHeaderWithRefresh';
import {
  RAILS_MMT_ID,
  setDataToAsyncStorage,
} from '../../Utils/RailsConstant';
import connectVernalar from './connectVernalar';
import CustomizedSomethingWentWrong from '../ErrorPages/CustomizedSomethingWentWrong';
import PageSlowTooltip from './Components/PageSlowTooltip';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos, isAndroid } from '../../Utils/device';
import { _label } from '../../vernacular/AppLanguage';
import { GFT_PDT_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/GFT/PdtGftConstants';
import PropTypes from 'prop-types';

const PAGE_LOAD_LIMIT = 7000;
let htmlCode;
export const CounterContext = React.createContext(null);

export class ConfirmBooking extends React.Component {
  webViewRef = React.createRef();
  newConfirmWebView = React.createRef();
  constructor(props) {
    super(props);
    this.initState = {
      loading: true,
      webViewLoaded: false,
      showPageSlowTooltip: false,
      error: false,
      errorMessage: null,
      callBackURL: '',
      railsShowCustomIrctcWebpage: true,
      railsEnableHelpSectionOnIRCTCWebpage: false,
      disableRefreshButton: false,
      counterVal: 60,
    };
    this.state = {...this.initState};
    trackRailPaymentDone();
    this._refreshHeaderRef = React.createRef();
  }

  verifyBooking = (request) => {
    Actions.verifyIrctcBooking({
      request,
      type: 'replace',
    });
  };

  onHardBackPress = () => {
    if (this.webViewRef.current) {
      return this.webViewRef.current.onHardBackPress();
    } else if (this.newConfirmWebView.current) {
      return this.newConfirmWebView.current.onHardBackPress();
    }
    Actions.pop({ isThankYouDismiss: true });
    this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.BACK_PRESS);
    return true;
  };

  loadPage = async () => {
    let railsShowCustomIrctcWebpage = getPokusConfig(
      PokusLobs.RAIL,
      AbConfigKeyMappings.railsShowCustomIrctcWebpage,
      false,
    );
    if (isIos()) {
      railsShowCustomIrctcWebpage = false;
    }
    const railsEnableHelpSectionOnIRCTCWebpage = getPokusConfig(
      PokusLobs.RAIL,
      AbConfigKeyMappings.RailsEnableHelpSectionOnIRCTCWebpage,
      false,
    );
    const {mmtId, irctcUserId} = this.props;
    if (mmtId && irctcUserId) {
      await setDataToAsyncStorage(RAILS_MMT_ID, mmtId);
    }
    const irctcResponse = await getIrctcBookingParams();
    if (irctcResponse.errorDetails) {
      this.setState({
        loading: false,
        error: true,
        errorMessage: irctcResponse.errorDetails.errorMessage,
        railsEnableHelpSectionOnIRCTCWebpage,
        railsShowCustomIrctcWebpage,
      });
      this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.IRCTC_PAGE_LOAD, irctcResponse.errorDetails);
      return;
    }
    this.props.setUserNameToRedux(irctcResponse?.wsloginId);
    htmlCode = getIrctcRedirectHTML(irctcResponse);
    const callBackURL = await getCallbackURLArray();
    if (this._pageSpeedTimerId) {
      clearTimeout(this._pageSpeedTimerId);
    }
    this._pageSpeedTimerId = setTimeout(() => {
      if (!this.state.webViewLoaded) {
        const headerRef = this._refreshHeaderRef.current;
        if (headerRef && headerRef.isRefreshDisabled()) {
          return;
        }
        this.setState({
          showPageSlowTooltip: true,
          railsEnableHelpSectionOnIRCTCWebpage,
        });
      }
    }, PAGE_LOAD_LIMIT);
    this.setState({
      loading: false,
      callBackURL,
      railsShowCustomIrctcWebpage,
      railsEnableHelpSectionOnIRCTCWebpage,
    });
    this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.IRCTC_PAGE_LOAD);
  };

  async componentDidMount() {
    try {
      await getPokusConfigWaitingPromise();
      await this.loadPage();
    } catch (e) {
      logAction('irctc page didnot load');
    }
  }

  componentDidUpdate( prevProps ) {
    const {mmtId, irctcUserId} = this.props;
    if (prevProps.mmtId !== mmtId || prevProps.irctcUserId !== irctcUserId){
      this._onRefreshClick();
    }
 }

  componentWillUnmount() {
    if (this._pageSpeedTimerId) {
      clearTimeout(this._pageSpeedTimerId);
    }
  }

  _onWebViewLoad = () => {
    this.setState({webViewLoaded: true, showPageSlowTooltip: false});
    if (this._pageSpeedTimerId) {
      clearTimeout(this._pageSpeedTimerId);
    }
  };

  _onRefreshClick = () => {
    this.startCounter();
    this.setState(
      prevState => ({
        ...this.initState,
        disableRefreshButton: !prevState.disableRefreshButton,
      }),
      () => {
        try {
          this.loadPage();
        } catch (error) {
          console.error('Error in loadPage:', error);
        }
      },
    );
    this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.REFRESH_CLICKED);
  };

  startCounter = () => {
    const intervalId = setInterval(() => {
      if (this.state.counterVal <= 1) {
        this.enableRefreshButton(intervalId);
        return;
      }
      this.setState(prevState => ({counterVal: prevState.counterVal - 1}));
    }, 1000);
  };

  enableRefreshButton = (intervalId) => {
    if (intervalId) {
      clearInterval(intervalId);
    }
    this.setState(() => ({disableRefreshButton: false, counterVal: 60}));
  };

  render() {
    const {
      loading,
      callBackURL,
      railsShowCustomIrctcWebpage,
      showPageSlowTooltip,
      railsEnableHelpSectionOnIRCTCWebpage,
    } = this.state;
    if (!loading && this.state.error) {
      return (
        <CounterContext.Provider value={{value: this.state.counterVal}}>
          <View style={{flex: 1}}>
            <IrctcPageHeaderWithRefresh
              ref={this._refreshHeaderRef}
              labels={this.props.labels}
              onBackPress={this.onHardBackPress}
              onRefreshClick={this._onRefreshClick}
              showRefreshButton
              enableRefreshButton={this.enableRefreshButton}
              disableRefreshButton={this.state.disableRefreshButton}
              logGftPdtEventsToPdt={this.props.logGftPdtEventsToPdt}
            />
            <CustomizedSomethingWentWrong
              onRefresh={this.onHardBackPress}
              header=""
              subHeader={this.state.errorMessage}
              buttonText={_label('go_to_home_page', { uppercase: true })}
            />
          </View>
        </CounterContext.Provider>
      );
    }
    return (
      <CounterContext.Provider value={{value: this.state.counterVal}}>
        <View style={{flex: 1}}>
          <IrctcPageHeaderWithRefresh
            ref={this._refreshHeaderRef}
            labels={this.props.labels}
            onBackPress={this.onHardBackPress}
            onRefreshClick={this._onRefreshClick}
            showRefreshButton
            enableRefreshButton={this.enableRefreshButton}
            disableRefreshButton={this.state.disableRefreshButton}
            logGftPdtEventsToPdt={this.props.logGftPdtEventsToPdt}
          />

          {loading && <PreRedirectLoader />}

          {!loading && !railsShowCustomIrctcWebpage && (
            <ConfirmBookingWebView
              callbackUrl={callBackURL}
              callbackFunc={this.verifyBooking}
              htmlCode={htmlCode}
              ref={this.webViewRef}
            />
          )}

          {!loading && railsShowCustomIrctcWebpage && (
            <NewConfirmBookingWebView
              callbackUrl={callBackURL}
              callbackFunc={this.verifyBooking}
              htmlCode={htmlCode}
              onLoad={this._onWebViewLoad}
              ref={this.newConfirmWebView}
              onRefreshClick={this._onRefreshClick}
              irctcUserId={this.props.irctcUserId}
              railsEnableHelpSectionOnIRCTCWebpage={railsEnableHelpSectionOnIRCTCWebpage}
              enableRefreshButton={this.enableRefreshButton}
              disableRefreshButton={this.state.disableRefreshButton}
            />
          )}
          {showPageSlowTooltip && isAndroid() &&
          <PageSlowTooltip layout={{x: 4, y: 56}} orientation="bottom">
            <Text
              style={{
                color: colors.white,
                ...fontStyle('medium'),
                ...getLineHeight(12),
                fontSize: 12,
              }}
            >
              {_label('irctc_taking_too_long_tooltip')}
            </Text>
          </PageSlowTooltip>
          }
        </View>
      </CounterContext.Provider>
    );
  }
}

ConfirmBooking.propTypes = {
  labels: PropTypes.object,
  mmtId: PropTypes.string,
  irctcUserId: PropTypes.string,
  logGftPdtEventsToPdt: PropTypes.func,
  setUserNameToRedux: PropTypes.func,
};

export default connectVernalar(ConfirmBooking);
