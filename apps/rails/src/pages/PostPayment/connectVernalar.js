/* eslint-disable */
import React from 'react';
import { connect } from 'react-redux';
import { updateUsername } from '@mmt/rails/src/pages/User/UserVerification/UserVerificationActions';
import { initVernacular } from '../../vernacular/vernacularActions';
import {_label} from '../../vernacular/AppLanguage';
import { logGftPageEventsToPdt } from '../../PdtAnalytics/PdtAnalyticsV2/GFT';

export const mapVernacularLabels = (state) => ({
  labels: {
    connecting: _label('connecting_to_irctc'),
    redirecting: _label('redirecting_to_irctc'),
    enterPwdNext: _label('enter_password_in_next_step'),
    hintSlow: _label('this_may_take_few_seconds'),
    warnBackPress: _label('please_do_not_press_back'),
    completeBooking: _label('complete_booking'),
    enterPwdAndCaptcha: _label('enter_irctc_password_and_capcha_below'),
    cancelBooking: _label('cancel_booking'),
    getNewPassword: _label('get_new_password',{capitalize : true}),
    areYouSure: _label('are_you_sure'),
    yourPassword: _label('your_password'),
    yourCaptcha: _label('your_capcha'),
    refresh: _label('refresh'),
    bookingSuccessful: _label('booking_successful'),
    ticketConfirmationOn$Email: _label('you_will_recieve_your_tickets_on_email'),
    statusUpdateOn$mobile: _label('booking_status_message'),
    bookReturnTrip: _label('book_return_trip'),
  },
  irctcUserName: state?.railsUserVerification?.irctcUserName,
});

const mapDispatchToProps = dispatch => ({
  initVernacular: () => dispatch(initVernacular()),
  setUserNameToRedux: (userName) => {
    dispatch(updateUsername(userName));
  },
  logGftPdtEventsToPdt: (eventValue, errorDetails) => {
    dispatch(logGftPageEventsToPdt(eventValue, errorDetails));
  },
});


export default function connectVernalar(WrappedComponent) {
  const Connected = connect(mapVernacularLabels, mapDispatchToProps, null, { forwardRef: true })(
    WrappedComponent,
  );
  return React.forwardRef((props, ref) => <Connected {...props} ref={ref} />);
}
