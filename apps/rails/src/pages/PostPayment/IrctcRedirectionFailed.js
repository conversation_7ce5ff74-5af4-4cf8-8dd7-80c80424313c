import React from 'react';
import {View, Text, Image, StyleSheet, Button } from 'react-native';
import { _label } from '../../vernacular/AppLanguage';
import { fontStyle, getLineHeight } from '../../vernacular/VernacularUtils';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { Actions } from '../../navigation';
import ASSETS from '../../Utils/Assets/RailsAssets';

const styles = StyleSheet.create({
  pageWrapper: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -70,
    height: '100%',
  },
  redirectionInfo: {
    marginBottom: 100,
    display: 'flex',
    alignItems: 'center',
  },
  redirectionTitle: {
    color: colors['4d4d4d'],
    marginBottom: 8,
  },
  redirectionSubtitle: {
    color: colors.lightGreen4,
  },
  redirectLoader: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 35,
  },
  progressDone: {
    width: 110,
    marginHorizontal: 10,
    height: 4,
    backgroundColor: colors.disabledBtnBg,
  },
  conenctionFailed: {
    color: colors.red,
    position: 'absolute',
    right: -8,
    top: -11,
  },
  logo: {
    width: 48,
    height: 48,
  },
  redirectMessage: {
  },
  redirectionFailedImage: {
    marginBottom: 30,
  },
  redirectionFailedMessage: {
    marginBottom: 10,
    color: colors.textGrey,
  },
});

function IrctcRedirectionFailed () {
  return (
    <View style={styles.pageWrapper}>
      <View style={styles.redirectionInfo}>
        <Text style={[getLineHeight(24), fontStyle('bold'), styles.redirectionTitle]}>{_label('redirection_failed')}</Text>
        <Text style={[getLineHeight(16), fontStyle('bold'), styles.redirectionSubtitle]}>{_label('redirection_failed_subtitle')}</Text>
      </View>

      <View style={styles.redirectLoader}>
        <Image source={{ uri: 'https://imgak.mmtcdn.com/pwa_v3/pwa_mmt_ui_assets/favicon.png'}} style={styles.logo}/>
        <View style={styles.progressDone}>
          <Text style={[styles.conenctionFailed, fontStyle('black'), getLineHeight(24)]}>
            X
          </Text>
        </View>
        <Image source={require('@mmt/legacy-assets/src/rails/irctc_logo_v2.png')} style={styles.logo} />
      </View>

      <View style={styles.redirectMessage}>
        <Image source={ASSETS.redirectionFailed} style={styles.redirectionFailedImage} />
        <Text style={[styles.redirectionFailedMessage, fontStyle('regular'), getLineHeight(14)]}>
          {_label('irctc_did_not_respond')}
        </Text>
        <Button
          title={_label('go_home', { uppercase: true })}
          onPress={() => { Actions.rails();}}
        />
      </View>

    </View>
  );
}

export default IrctcRedirectionFailed;
