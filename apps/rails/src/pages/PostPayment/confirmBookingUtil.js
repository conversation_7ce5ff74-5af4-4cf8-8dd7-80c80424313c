import { useState, useEffect, useRef } from 'react';
import { RAILS_MMT_ID, setDataToAsyncStorage } from '../../Utils/RailsConstant';

import get from 'lodash/get';
import fetch2 from '../../fetch2';
import railsConfig from '../../RailsConfig';

export const TIMER = {
  R: 'RUNNING',
  NR: 'NOT_RUNNING',
};

/* eslint-disable */
const getBookingParams = (mmtId) => {
  return new Promise((resolve) => {
    if (mmtId === 1) {
      fetch2(`${railsConfig.getBookingIdV2}?mmtId=${mmtId}`)
        .then(res => res.json())
        .then(data => {
          resolve({
            wsTxnId: data.irctcBookingId,
            wsReturnUrl: data.callbackUrl,
            wsLogin,
          });
        });
    } else if (mmtId === 2) {
      resolve({
        data: 'Test',
      });
    } else {
      resolve({
        errorDetails: {
          errorMessage: '?Something went wrong!',
        },
      });
    }
  });
};


export const useGetBookingParams = ({ mmtId }) => {

  const [isLoading, setLoading] = useState(true);
  const [response, setResponse] = useState({

  });

  const [error, setError] = useState({
    hasError: false,
    message: '',
  });

  setDataToAsyncStorage(RAILS_MMT_ID, mmtId);


  const fetchDetails = () => {
    getBookingParams(2)
      // .then(res => res.json())
      .then(data => {
        if (data.errorDetails) {
          throw new Error(data.errorDetails.errorMessage);
        } else {
          setLoading(false);
        }
      })
      .catch(error => {
        const exceptionErrorMessage = get(error, 'message', '');
        const errorMessage = get(error, 'errorDetails.errorMessage', '') || exceptionErrorMessage;
        setLoading(false);
        setError({
          message: errorMessage,
          hasError: true,
        });
      });
  };

  const makeApiCallAndSetData = (delay) => {
    if (delay) {
      setTimeout(fetchDetails, delay * 1000);
    } else {
      fetchDetails();
    }
  };

  return {
    isLoading,
    response,
    error,
    getBookingDetails: makeApiCallAndSetData,
  };
};

export const useTimer = (initialTime) => {

  const [remainingTime, setRemainingTime] = useState(initialTime);
  const remainingTimeRef = useRef(initialTime);
  const [timerState, setTimerState] = useState(TIMER.NR);

  useEffect(() => {
    remainingTimeRef.current = remainingTime;
  });

  const startCounter = () => {
    setRemainingTime(initialTime);
    setTimerState(TIMER.R);
    const intervalId = setInterval(() => {
      if (remainingTimeRef.current <= 1) {
        endTimer(intervalId);
        return;
      }
      setRemainingTime(remainingTimeRef.current - 1);
    }, 1000);
  };

  const endTimer = (intervalId) => {
    if (intervalId) {
      clearInterval(intervalId);
    }
    setRemainingTime(initialTime);
    setTimerState(TIMER.NR);
  };

  return {
    startCounter,
    remainingTime,
    timerState,
  };
};

export const delay = (time) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time * 1000);
  });
};
