import React from 'react';
import { Actions } from '../../navigation';
import {Text, View, Image, ScrollView,  StyleSheet, BackHandler} from 'react-native';
import ASSETS from '../../Utils/Assets/RailsAssets';
import LinearGradient from 'react-native-linear-gradient';
import CloseIcon from '@mmt/legacy-assets/src/ic-headerclose-grey.webp';
import {numAppendedWithRuppeeSymbolWithoutRound} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import {trackRailsRefundInitiatedPageEvent, trackRailsRefundInitiatedPageLoad} from '../../railsAnalytics';
import {RAILS_POST_PAYMENT_FAILURE} from '../../RailsOmnitureTracker';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

class RefundProcessed extends React.Component {
  constructor(props) {
    super(props);
  }

  goToWhatWentWrongPage = () => {
    trackRailsRefundInitiatedPageEvent(RAILS_POST_PAYMENT_FAILURE.MOB_RAIL_FAILURE_CONTINUE_CLICKED);
    Actions.railsWhatWentWrong({
      type: 'replace',
      mmtId: this.props.mmtId,
    });
  };

  componentDidMount() {
    trackRailsRefundInitiatedPageLoad();
    BackHandler.addEventListener('hardwareBackPress', this.onHardBackPress);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onHardBackPress);
  }

  onHardBackPress = () => {
    trackRailsRefundInitiatedPageEvent(RAILS_POST_PAYMENT_FAILURE.MOB_RAIL_FAILURE_CLOSED_SCREEN);
    if (isIos()) {
      ViewControllerModule.thankyouDismiss(this.props.rootTag);
    } else {
      Actions.rails();
    }
    return true;
  };
  render() {
    let refundHeader = '';
    let refundSubHeader;
    let refundFooter;
    if (this.props.userRefundMode) {
      if (this.props.selectedRefundOption) {
        refundHeader = 'Refund Complete';
        refundSubHeader = ' added to your MakeMyTrip Wallet Cash';
        refundFooter = 'If you change your mind, you can use this cash on all MakeMyTrip services without any restrictions. You can always withdraw this amount from www.makemytrip.com';
      } else {
        refundHeader = 'Refund Initiated';
        refundSubHeader = ' will be added to your Bank Account';
        refundFooter = '';
      }
    } else {
      refundHeader = 'Refund Already Initiated';
      refundSubHeader = ' will be added to your Bank Account';
      refundFooter = '';
    }
    return (
      <ScrollView>
        <View style={styles.container}>
          <TouchableRipple onPress={this.onHardBackPress}>
            <View style={styles.closeIconContainer}>
              <Image
                style={{height: 32, width: 32, marginLeft: 20}}
                source={CloseIcon}
              />
            </View>
          </TouchableRipple>
          <View style={styles.tickImageContainer}>
            <Image
              style={styles.tickImage}
              source={ASSETS.icFpTick}
            />
          </View>
        </View>
        <View style={{flex: 4}} >
          <View style={styles.refundTextContainer}>
            <Text style={[styles.refundText, fontStyle('bold'), getLineHeight(24)]}>
              {refundHeader}
            </Text>
            <RupeeText style={[styles.cashText, fontStyle('bold'), getLineHeight(16)]}>
              {numAppendedWithRuppeeSymbolWithoutRound(this.props.refundAmount) + refundSubHeader}
            </RupeeText>

            <View style={styles.continueBookingContainer}>
              <Text style={[styles.continueText, fontStyle('regular'), getLineHeight(16)]}>
                Continue booking with your saved username,
              </Text>
              <Text style={[styles.continueText, fontStyle('regular'), getLineHeight(16)]}>
                train selection and traveller preferences.
              </Text>
            </View>
            <View style={{
              height: 68, width: '100%',
            }}
            >
              <TouchableRipple onPress={this.goToWhatWentWrongPage}>
                <View>
                  <LinearGradient
                    style={styles.linearGrad}
                    colors={['#53B2FE', '#065AF3']}
                    start={{x: 0.0, y: 0.0}}
                    end={{x: 1.0, y: 0.0}}
                  >
                    <View >
                      <Text style={[styles.continueBookingText, fontStyle('bold'), getLineHeight(16)]}>CONTINUE BOOKING</Text>
                    </View>
                  </LinearGradient>
                </View>
              </TouchableRipple>
            </View>
            <View style={{marginHorizontal: 30, marginTop: 20}}>
              <Text style={styles.bottomText}>
                {refundFooter}
              </Text>
            </View>
          </View>

        </View>

      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'flex-start',
    flexDirection: 'column',
    flex: 3,
  },
  closeIconContainer: {
    height: 40,
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-end',
  },
  tickImageContainer: {
    flex: 4,
    justifyContent: 'flex-end',
    marginBottom: 30,
  },
  tickImage: {
    height: 124,
    width: 124,
    alignSelf: 'center',
  },
  refundTextContainer: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  refundText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.black,
    marginTop: 10,
    marginBottom: 4,
  },
  cashText: {
    fontSize: 16,
    color: colors.black,
    fontWeight: 'bold',
  },
  continueBookingContainer: {
    marginTop: 20,
    marginBottom: 30,
    alignItems: 'center',
  },
  continueText: {
    fontSize: 16,
    color: colors.textGrey,
  },
  linearGrad: {
    borderRadius: 96,
    height: 54,
    marginHorizontal: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueBookingText: {
    fontSize: 16,
    color: colors.white,
    backgroundColor: colors.transparent,
  },
  bottomText: {
    fontSize: 14,
    color: colors.textGrey,
  },
});

RefundProcessed.propTypes = {
  mmtId: PropTypes.string,
  refundAmount: PropTypes.number,
  userRefundMode: PropTypes.bool,
  selectedRefundOption: PropTypes.bool,
  rootTag: PropTypes.number,
};

export default RefundProcessed;
