import React, {Component} from 'react';
import { Actions } from '../../navigation';
import isEmpty from 'lodash/isEmpty';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import PropTypes from 'prop-types';
import {trackHotelCrossSellEvent} from '../../railsAnalytics';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';

import hotelImage from '@mmt/legacy-assets/src/hotelimage.webp';
const DUMMY_CALLBACK_URL = 'accounts.google.com';

export default class TrainsVoucherCard extends Component {
  UNSAFE_componentWillMount() {
    trackHotelCrossSellEvent('mob_rail_Thank_You_Success', 'mob_rail_Thank_You_Success_banner_displayed');
  }

  render() {
    const {header, body, cdnTncUrl, hotelsDeepLinkMobileApp} = this.props;
    return (
      <View>
        <Card style={bannerStyles.cardStyle}>
          <View>
            <Text style={[bannerStyles.bannerHeaderText, fontStyle('regular'), getLineHeight(16)]}>
              {header}
            </Text>
          </View>

          <View style={bannerStyles.bannerCardContainer}>
            <View>
              <Image
                style={bannerStyles.imgStyle}
                source={hotelImage}
              />
            </View>

            <View style={bannerStyles.textContainer}>
              <Text style={[bannerStyles.bannerText, fontStyle('regular'), getLineHeight(14)]}>
                {body}
              </Text>
            </View>
          </View>

          <View style={bannerStyles.bannerCardContainer}>
            {!isEmpty(cdnTncUrl) &&
            <View style={bannerStyles.tncText}>
              <TouchableOpacity onPress={() => _onTermsAndConditionsClicked(cdnTncUrl)}>
                <Text style={[bannerStyles.viewTncText, fontStyle('regular'), getLineHeight(14)]}>Know more</Text>
              </TouchableOpacity>
            </View>
            }


            {!isEmpty(hotelsDeepLinkMobileApp) &&
            <View style={bannerStyles.searchHotelText}>
              <TouchableOpacity onPress={() => _searchHotels(hotelsDeepLinkMobileApp)}>
                <Text style={[bannerStyles.viewTncText, fontStyle('regular'), getLineHeight(14)]}>SEARCH HOTELS</Text>
              </TouchableOpacity>
            </View>
            }
          </View>
        </Card>
      </View>
    );
  }
}

TrainsVoucherCard.propTypes = {
  header: PropTypes.string.isRequired,
  body: PropTypes.string.isRequired,
  cdnTncUrl: PropTypes.string,
  hotelsDeepLinkMobileApp: PropTypes.string,
};

TrainsVoucherCard.defaultProps = {
  cdnTncUrl: '',
  hotelsDeepLinkMobileApp: '',
};

const callBackFunc = () => {
  Actions.pop();
};
const _onTermsAndConditionsClicked = (viewCdfTnc) => {
  trackHotelCrossSellEvent('mob_rail_Thank_You_Success', 'mob_rail_Thank_You_Success_banner_tnc_clicked');
  Actions.irctcWebViewPage({
    url: viewCdfTnc,
    headerText: 'Terms & Conditions',
    callbackUrl: DUMMY_CALLBACK_URL,
    callbackFunc: callBackFunc,
  });
};

const _searchHotels = (hotelsDeepLinkMobileApp) => {
  trackHotelCrossSellEvent('mob_rail_Thank_You_Success', 'mob_rail_Thank_You_Success_banner_Search_Hotels_clicked');
  GenericModule.openDeepLink(hotelsDeepLinkMobileApp);
};

export const bannerStyles = {
  bannerCardContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
  },
  textContainer: {
    paddingLeft: 10,
    width: '80%',
  },
  bannerText: {
    flexWrap: 'wrap',
    fontSize: 14,
    color: colors.black,
  },
  searchHotelText: {
    right: 0,
  },
  tncText: {
    left: 0,
  },
  bannerHeaderText: {
    flexWrap: 'wrap',
    fontSize: 16,
    color: colors.black,
    fontWeight: 'bold',
  },
  viewTncText: {
    fontSize: 14,
    letterSpacing: 0,
    color: colors.azure,
    fontWeight: 'bold',
  },
  imageContainer: {
    position: 'absolute',
    left: 0,
  },
  bannerStyle: {
    marginHorizontal: 16,
    marginVertical: 16,
  },
  bannerMarginTop: {
    marginTop: 16,
  },
  cardStyle: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    backgroundColor: '#f0fdfc',
  },
  imgStyle: {
    width: 48,
    height: 48,
  },
};
