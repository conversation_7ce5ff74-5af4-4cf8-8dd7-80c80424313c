import React from 'react';
import isEmpty from 'lodash/isEmpty';
import { connect } from 'react-redux';
import {AppState, Image, Keyboard, ScrollView, Text, View} from 'react-native';
import { Actions } from '../../navigation';
import styles from '../User/Common/UserVerificationCSS';
import CommonHeader from '../User/Common/CommonHeader';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {TextInputTypeActive, TextInputTypeErrorWithoutDesc, TextInputTypeStatic} from '../User/Common/TextInputType';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {JSON_FIELD, retrievePassword, STATUS_API_SUCCESS, STATUS_LIMIT_EXCEEDED} from '../../Utils/UserRepository';
import LoadingCTAButton from '../User/Common/LoadingCTAButton';
import DisabledCTAButton from '../User/Common/DisabledCTAButton';
import CtaButton from '../User/Common/CtaButton';
import { getDataFromAsynStorage, RAILS_BOOKING_TIMER_START_TIME } from '../../Utils/RailsConstant';
import {trackPageName, trackUserValidation} from '../../railsAnalytics';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import {IRCTC_BOOKING_TIME_LIMIT} from '../Review/Components/ReviewTimer';
import {_label} from '../../vernacular/AppLanguage';
import { isIos } from '../../Utils/device';
import verifiedIcon from '@mmt/legacy-assets/src/ic-tick-green.webp';
import PropTypes from 'prop-types';

let startTime;
let appStateListener;

class ForgotPassword extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      enableEmailInputField: false,
      mobile: '',
      loading: false,
      showError: false,
      enableCTA: false,
      limitExceded: false,
      autoFocus: true,
      userName: '',
      elapseTime: IRCTC_BOOKING_TIME_LIMIT,
      appState: AppState.currentState,

    };
  }

  onHardBackPress = () => {
    if (isIos()) {
      /* do nothing */
    } else {
      Actions.rails();
    }
    return true;
  };

  _getElapseTime = () => IRCTC_BOOKING_TIME_LIMIT - (Math.floor(((new Date().getTime()) / 1000) - startTime));

  _updateReviewTimer = (newAppState) => {
    if ((this.state.appState === 'inactive' || this.state.appState === 'background') && newAppState === 'active') {
      const newElapsedTime = this._getElapseTime();
      this.setState({
        appState: newAppState,
        elapseTime: newElapsedTime < 0 ? 0 : newElapsedTime,
      });
    } else {
      this.setState({
        appState: newAppState,
      });
    }
  };

  componentWillUnmount() {
    if (this.interval) {
      clearInterval(this.interval);
    }
    appStateListener?.remove();
  }

  async UNSAFE_componentWillMount() {
    const userName = this.props?.irctcUserName;
    this.setState({
      userName,
    });

    startTime = await getDataFromAsynStorage(RAILS_BOOKING_TIMER_START_TIME);
    this.setState({
      elapseTime: this._getElapseTime(),
    });

    this.interval = setInterval(() => {
      const time = this.state.elapseTime - 1;
      if (time <= 0) {
        clearInterval(this.interval);
        Actions.pop();
        Actions.railsBookingFailure();
      }
      this.setState({
        elapseTime: time,
      });
    }, 1000);
    appStateListener = AppState.addEventListener('change', this._updateReviewTimer);
  }

  _onCloseClicked = () => {
    Actions.confirmBooking();
    trackPageName(RAIL_EVENTS.USER.MMT_RAIL_POST_PAYMENT_FORGOT_PASSWORD_DISMISS);
  };

  onPageFocused() {
    if (this._textInput) {
      this._textInput.focus();
    }
  }

  _onEmailInputClicked = () => {
    this.setState({
      enableEmailInputField: true,
    });
  };

  _retrievePassword = async () => {
    Keyboard.dismiss();
    this.setState({loading: true});
    const {mobile} = this.state;
    const status = await retrievePassword(this.state.userName, mobile, true);
    if (isEmpty(status)) {
      this.setState({
        loading: false,
        enableCTA: false,
        showError: true,
        limitExceded: false,
        autoFocus: false,
      });
      return;
    }
    trackUserValidation(RAIL_EVENTS.USER.MMT_RAIL_POST_PAYMENT_FORGOT_PASSWORD,
                        RAIL_EVENTS.USER.MMT_RAIL_POST_PAYMENT_FORGOT_PASSWORD_CONTINUE);
    if (status[JSON_FIELD.status] === STATUS_API_SUCCESS) {
      this.setState({
        loading: false,
        autoFocus: false,
      });
      Actions.pop();
      Actions.forgotPasswordSuccessPage({mobile});
    } else if (status[JSON_FIELD.status] === STATUS_LIMIT_EXCEEDED) {
      this.setState({
        loading: false,
        enableCTA: false,
        showError: true,
        limitExceded: true,
        autoFocus: false,
      });
      trackPageName(RAIL_EVENTS.USER.MMT_RAIL_POST_PAYMENT_FORGOT_PASSWORD_ATTEMPT_LIMIT_EXCEEDED);
    } else {
      this.setState({
        loading: false,
        enableCTA: false,
        showError: true,
        limitExceded: false,
        autoFocus: false,
        error : '',
      });
      trackPageName(RAIL_EVENTS.USER.MMT_RAIL_POST_PAYMENT_RETRIEVE_PASSWORD_ERROR);
    }
  };


  _getInputField = (text) => {
    let enableCTA = false;
    if (text.length === 10) {
      enableCTA = true;
    }
    this.setState({
      mobile: text,
      enableCTA,
    });
  };

  _onMobileChangeClicked = () => {
    this.setState({
      loading: false,
      showError: false,
    });
  };

  render() {
    const {
      loading, showError, enableCTA, limitExceded,
    } = this.state;
    return (
      <View style={styles.container}>
        <View style={styles.userFlowContainer}>
          <ScrollView bounces={false} keyboardShouldPersistTaps="handled">
            <CommonHeader
              title={_label('request_new_irctc_password')}
              onPress={() => this._onCloseClicked()}
            />

            {limitExceded &&
            <Text
              style={[styles.errorTextDesc, styles.paddingTop7]}
            >{_label('too_many_password_retry')}
            </Text>
            }

            <View
              style={[styles.flex1Row, styles.paddingTop20]}
            >
              <TextInputTypeStatic
                label={_label('irctc_username',{uppercase : true})}
                value={this.state.userName}
                styles={{flex: 4}}
              />

              <View style={{
                flex: 2,
                paddingTop: 19,
                alignItems: 'flex-end',
              }}
              >
                <View style={styles.flex1Row}>
                  <View style={{paddingTop: 5}}>
                    <Image
                      style={{
                        width: 12,
                        height: 12,
                        alignItems: 'center',
                      }}
                      source={verifiedIcon}
                    />
                  </View>
                  <Text style={[styles.latoBoldText12, {
                    color: colors.cyan,
                    paddingLeft: 3,
                    lineHeight: 18,
                  }]}
                  >{_label('verified')}
                  </Text>
                </View>
              </View>
            </View>

            <View style={[styles.defaultLine, styles.paddingTop7]}/>

            {!limitExceded &&
            <Text
              style={[styles.latoRegularText, styles.paddingTop40, {color: colors.black}]}
            >
              {_label('get_password_messsage')}
            </Text>
            }

            {!loading && !showError && !limitExceded &&
            <TextInputTypeActive
              styles={styles.paddingTop8}
              label={_label('mobile_number_registered_with_irctc',{uppercase : true})}
              onChangeText={this._getInputField}
              value={this.state.mobile}
             keyboardType="numeric"
              maxLength={10}
              onSubmitEditing={() => Keyboard.dismiss()}
              innerRef={(e) => {
                this._textInput = e;
              }}
              autoFocus={this.state.autoFocus}
            />
            }

            {loading && !showError && !limitExceded &&
            <TextInputTypeStatic
              styles={styles.paddingTop8}
              label={_label('mobile_number_registered_with_irctc',{uppercase : true})}
              value={this.state.mobile}
            />
            }

            {showError && !limitExceded &&
            <View>
              <View
                style={[styles.flex1Row, styles.paddingTop8]}
              >
                <TextInputTypeErrorWithoutDesc
                    label={_label('mobile_number_registered_with_irctc',{uppercase : true})}
                  value={this.state.mobile}
                  styles={{flex: 7}}
                />

                <TouchableRipple onPress={() => this._onMobileChangeClicked()}>
                  <View style={styles.flex1}>
                    <Text style={[styles.latoBoldText12, {
                      color: colors.azure,
                      paddingTop: 23,
                    }]}
                    >{_label('change',{capitalize : true})}
                    </Text>
                  </View>
                </TouchableRipple>

              </View>
              <View style={[styles.errorLine, styles.paddingTop7]}/>
              <Text
                style={[styles.errorTextDesc, styles.paddingTop6]}
              >
                {_label('phone_number_username_not_linked')}
              </Text>
            </View>
            }

            <View style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'flex-end',
              justifyContent: 'flex-end',

            }}
            >
              {((!enableCTA && !loading) || showError) && !limitExceded &&
              <View style={styles.paddingTop20}>
                <DisabledCTAButton
                  label={_label('request')}
                  btnStyle={{
                    borderWidth: 0,
                  }}
                />
              </View>
              }

              {enableCTA && !loading &&
              <View style={styles.paddingTop20}>
                <CtaButton
                  label={_label('request',{uppercase : true})}
                  onPress={this._retrievePassword}
                  btnStyle={{
                    borderWidth: 0,
                  }}
                />
              </View>
              }

              {loading &&
              <View style={styles.paddingTop20}>
                <LoadingCTAButton
                  btnStyle={{
                    borderWidth: 0,
                  }}
                />
              </View>
              }

            </View>
          </ScrollView>
        </View>
      </View>
    );
  }
}

ForgotPassword.propTypes = {
  irctcUserName: PropTypes.string,
};

const mapStateToProps = (state) => ({
  irctcUserName: state?.railsUserVerification?.irctcUserName,
});

export default connect(mapStateToProps)(ForgotPassword);
