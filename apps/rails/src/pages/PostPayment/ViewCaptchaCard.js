import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import {Image, Keyboard, Text, TextInput, View} from 'react-native';
import PropTypes from 'prop-types';
import React from 'react';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {webViewStyle} from './NewConfirmBookingWebView';
import CtaButton from '../User/Common/CtaButton';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import refresh from '@mmt/legacy-assets/src/ic-header-refresh-white.webp';

class ViewCaptchaCard extends React.Component {
  render() {
    const {inputText, labels} = this.props;
    return (
      <View style={{
        flexDirection: 'row',
        flex: 1,
        backgroundColor: colors.black04,
        justifyContent: 'space-around',
        paddingHorizontal: 10,
      }}
      >
        <TextInput
          ref={(ref) => {
            this._ref = ref;
          }}
          placeholder={labels.yourCaptcha}
          placeholderTextColor={colors.white}
          underlineColorAndroid="transparent"
          style={[webViewStyle.inputTextShowStyle,
            {
              width: 154,
              justifyContent: 'center',
            }]}
          maxlength={20}
          value={inputText}
          keyboardShouldPersistTaps="handled"
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          editable={false}
        />
        <TouchableRipple onPress={() => this.props.refreshCaptcha()}>
          <View style={webViewStyle.imageContainer}>
            <Image
              source={refresh}
              style={webViewStyle.passwordImage}
            />
            <Text style={[webViewStyle.latoBold10, fontStyle('bold'), getLineHeight(10)]}>{labels.refresh}</Text>
          </View>
        </TouchableRipple>

        <View style={{
          justifyContent: 'center',
          paddingLeft: 14,
        }}>
          <CtaButton
            label="Done"
            onPress={() => {
              this.props.onClick();
            }}
            btnStyle={{
              borderWidth: 0,
              height: 32,
              width: 120,
              paddingHorizontal: normalisePx(0),
              borderRadius: 32,
            }}
            textStyle={[{
              color: colors.white,
              fontSize: 12,
            }, fontStyle('black'), getLineHeight(12) ]}
          />
        </View>
      </View>
    );
  }
}

ViewCaptchaCard.propTypes = {
  inputText: PropTypes.string.isRequired,
  labels: PropTypes.object.isRequired,
  refreshCaptcha: PropTypes.func.isRequired,
  onClick: PropTypes.func.isRequired,
};
export default ViewCaptchaCard;
