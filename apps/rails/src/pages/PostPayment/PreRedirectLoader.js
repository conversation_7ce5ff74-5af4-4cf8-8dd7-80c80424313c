import React from 'react';
import { ActivityIndicator, Text, View, StyleSheet, Image } from 'react-native';
import { ProgressBar as ProgressBarAndroid } from '@react-native-community/progress-bar-android';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import {_label} from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { connect } from 'react-redux';
import RailsCountdownTimer from '../Common/RailsCountdownTimer';
import FunFacts from './Components/FunFacts';

const version = 'V2';

const stylesV2 = StyleSheet.create({
  pageWrapper: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -70,
    height: '100%',
  },
  paymentInfo: {
    marginBottom: 50,
    display: 'flex',
    alignItems: 'center',
  },
  paymentTitle: {
    color: colors['4d4d4d'],
    marginBottom: 8,
  },
  paymentSubtitle: {
    color: colors.lightGreen4,
  },
  redirectLoader: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: 35,
  },
  progressBar: {
    width: 110,
    height: 20,
    marginHorizontal: 10,
  },
  redirectMessage: {
    marginBottom: 55,
  },
  redirectTitle: {
    color: colors.textGrey,
  },
  redirectSubtitle: {
    color: colors.black,
  },
  retryWrapper: {
    marginBottom: 30,
  },
  timerView: {
    display: 'flex',
    alignItems: 'center',
  },
  retryMessage: {
    color: colors.textGrey,
    marginBottom: 5,
    maxWidth: 200,
    textAlign: 'center',
  },
  logo: {
    width: 48,
    height: 48,
  },
});

const PreRedirectLoader = (props) => {
  if (props.languageLoadStatus !== 'DONE') {
    return null;
  }

  const { isLoading } = props;

  if (!isLoading) {
    return null;
  }

  if (version === 'V2') {
    const {
      isRetry,
      delayInSeconds = 5,
      onRefreshClick,
    } = props;
    return (
      <View style={stylesV2.pageWrapper} key={delayInSeconds}>
        <View style={stylesV2.paymentInfo}>
          <Text style={[getLineHeight(24), fontStyle('bold'), stylesV2.paymentTitle]}>{_label('payment_received')}</Text>
          <Text style={[getLineHeight(16), fontStyle('bold'), stylesV2.paymentSubtitle]}>{_label('amount_is_safe')}</Text>
        </View>

        {
          !isRetry && (
            <View style={stylesV2.redirectLoader}>
              <Image source={{ uri: 'https://imgak.mmtcdn.com/pwa_v3/pwa_mmt_ui_assets/favicon.png'}} style={stylesV2.logo}/>
              <ProgressBarAndroid style={stylesV2.progressBar} styleAttr="Horizontal" color="#2196F3" />
            </View>
          )
        }
        {
          isRetry && (
            <View style={stylesV2.retryWrapper}>
              <View style={stylesV2.timerView}>
                <Text style={[getLineHeight(14), fontStyle('regular'), stylesV2.retryMessage]}>{_label('fetching_booking_details_retry')}</Text>
                <RailsCountdownTimer
                  hoursMinSecs={{hours: 0, minutes: 0, seconds: delayInSeconds}} shouldReset={false}
                  timesUpCallback={() => {onRefreshClick && onRefreshClick();}}
                  timerTextStyle={{
                    ...fontStyle('bold'),
                    ...getLineHeight(24),
                    color: colors.textGrey,
                  }}
                />
              </View>
            </View>
          )
        }

        <View style={stylesV2.redirectMessage}>
          <Text style={[getLineHeight(14), fontStyle('regular'), stylesV2.redirectTitle]}>{_label('now_redirecting')}</Text>
          <Text  style={[getLineHeight(14), fontStyle('bold'), stylesV2.redirectSubtitle]}>{_label('no_back_press')}</Text>
        </View>

        <FunFacts />

      </View>
    );
  }

  return (
    <View style={{
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    }}
    >
      <LinearGradient
        colors={['#ffffff', '#ffffff']}
        start={{
          x: 0.0,
          y: 0.0,
        }}
        end={{
          x: 1.0,
          y: 0.0,
        }}
        style={styles.cta}
      >
        <ActivityIndicator
          styleAttr="Inverse"
          color={colors.azure}
          style={{
            margin: 4,
            height: 36,
            width: 36,
          }}
          size="large"
        />
      </LinearGradient>
      <View style={styles.textContainer}>
        <Text style={[styles.textRow1, fontStyle('regular'), getLineHeight(18)]}>{_label('redirecting_to_irctc')}</Text>
        <Text style={[styles.textRow2, fontStyle('bold'), getLineHeight(20)]}>{_label('irctc_password_next_screen')}</Text>
        <Text style={[styles.textRow3, fontStyle('regular'), getLineHeight(18)]}>{_label('note_irctc_load')}</Text>
        <View style={styles.horizontalText}>
          <Text style={[styles.textRow2, fontStyle('bold'), getLineHeight(18)]}>{_label('click_on_forgot_password_button')} </Text>
        </View>
      </View>
    </View>
  );
};

const styles = ({
  cta: {
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    height: 36,
    width: 36,
    borderRadius: 100,
  },
  textStyle: {
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.black04,
  },
  textStyleItalic: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
    fontStyle: 'italic',
    color: colors.black04,
  },
  textContainer: {
    paddingTop: 50, paddingHorizontal: 20,
  },
  horizontalText: {
    marginTop: 30,
  },
  textRow1: {
    fontSize: 16, textAlign: 'center',
  },
  textRow2: {
    textAlign: 'center',
    fontSize: 18,
    lineHeight: 26,
    display: 'flex',
    justifyContent: 'center',
  },
  textRow3: {
    marginTop: 30,
    fontSize: 18,
    textAlign: 'center',
  },
});

PreRedirectLoader.propTypes = {
  labels: PropTypes.object.isRequired,
  languageLoadStatus: PropTypes.string,
  isLoading: PropTypes.bool,
  isRetry: PropTypes.bool,
  delayInSeconds: PropTypes.number,
  onRefreshClick: PropTypes.func,
};

const mapStateToProps = ({ railsVernacular }) => {
  return {
    languageLoadStatus: railsVernacular?.status,
  };

};
export default connect(mapStateToProps, null)(PreRedirectLoader);
