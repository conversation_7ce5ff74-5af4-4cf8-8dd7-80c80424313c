import { Platform } from 'react-native';
import isEmpty from 'lodash/isEmpty';
import { getBookingCallbackURL, getMmtId } from './PostPaymentUtils';
import {
  RAILS_BOOKING_ID,
  setDataToAsyncStorage,
} from '../../Utils/RailsConstant';
import fetch2 from '../../fetch2';
import railsConfig, { IRCTC_FORM_URL_V2 } from '../../RailsConfig';
import { _label } from '../../vernacular/AppLanguage';
import { getPersistedConfigData } from '../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../configStore/Common/constants';

export const getIrctcBookingParams = async (mmtBookingId, retryDetails) => {
  try {
    const mmtId = mmtBookingId || await getMmtId();
    const callBackURL = getBookingCallbackURL(mmtId);
    const headers = {
          'regen-on-id': retryDetails?.retryCount === 0 ? true : false,
        };
    const res = await fetch2(`${railsConfig.getBookingIdV2}?mmtId=${mmtId}`, { headers });
    const response = await res.json();
    if (res.status === 200) {
      await setDataToAsyncStorage(RAILS_BOOKING_ID, response.irctcBookingId);
      const irctcFormObj = await getPersistedConfigData(
        Platform.OS === 'ios' ? configKeys.RAILS_IRCTC_URL_IOS : configKeys.RAILS_IRCTC_URL_ANDROID,
      );
      //TODO: Get IRCTC Html from BE
      return {
        mmtId,
        wsTxnId: response.irctcBookingId,
        wsReturnUrl: callBackURL,
        wsloginId: response.wsUserLogin,
        irctcFormUrl: response.irctcFormUrl || irctcFormObj?.irctcFormUrl,
      };
    } else {
      if (!isEmpty(response.errorDetails)) {
        return {
          errorDetails: response.errorDetails,
        };
      }
      return {
        errorDetails: {errorMessage: _label('something_went_wrong') + ' Error Code: ' + response?.errorCode },
      };
    }
  } catch (e) {
    console.log('error', e);
    return {
      errorDetails: {errorMessage: _label('something_went_wrong') + ' Error Code: ' + e?.errorCode },
    };
  }
};

const getIrctcRedirectHTML = (response) => {
  const {
    wsTxnId: bookingId,
    wsReturnUrl: callBackURL,
    wsloginId,
    irctcFormUrl,
  } = response;

  const htmlCode = `${'<html><head><Body>' +
      `<form id = "irctcForm" class="irctcForm" action="${
        irctcFormUrl || IRCTC_FORM_URL_V2
      }" method="post" enctype="application/x-www-form-urlencoded" style = "display:none;">` +
    '<input id = "loginId" type="hidden" name="wsloginId" value="'}${wsloginId}"/>` +
    `<input id = "taxId" type="hidden" name="wsTxnId"  value="${bookingId}"/>` +
    `<input type="hidden" id = "wsReturnUrl" name="wsReturnUrl" value="${callBackURL}"/></form></Body>` +
    `<script>
       document.getElementById("irctcForm").submit();
    </script>
    </head></html>`;
  return htmlCode;
};

export default getIrctcRedirectHTML;
