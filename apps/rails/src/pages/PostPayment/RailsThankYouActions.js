import {
  ACTION_SET_VOUCHER_OBJECT,
} from '../NewListing/RailsListingActions';
import RailsPdtThankYouHelper from '../../PdtAnalytics/PdtHelper/RailsPdtThankYouHelper';
import {getDataFromAsynStorage} from '../../Utils/RailsConstant';
import {firebaseThankyouTracker} from '../../Analytics/Firebase/util';
import { getCommonPdtData } from '../../PdtAnalytics/PdtHelper/RailsPdtUtils';
export const loadThankYouPage = (bookingDetails, mmtId) => async (dispatch) => {
  const {
    data: {
      bookingStatusDetails, priceBreakUp: {totalAmount} = {},travellersDetails:{travellerList}, voucherDetails,
    } = {},
    status,
  } = bookingDetails;
  const data = {bookingData: {bookingStatusDetails, totalAmount, mmtId, bookingStatus: status}};
  dispatch({
    type: ACTION_SET_VOUCHER_OBJECT,
    data: {voucherObject: voucherDetails},
  });

  const fireBaseData = await getDataFromAsynStorage('fireBaseTrackingData');
  const fireBaseTrackingData = {
    ...fireBaseData,
    extraData:{
      ...fireBaseData.extraData,
      mmtId,
      travelInfo:travellerList,
    },
  };
  firebaseThankyouTracker(fireBaseTrackingData);
  const pdtData = await getDataFromAsynStorage('pdtDataForThankYou');
  const pdtDataForThankYou = {
    ...pdtData,
    bookingData:{
      ...pdtData.bookingData,
      ...data.bookingData,
    },
  };
  trackPdtThankYouLoad(pdtDataForThankYou);
};

/**
 *
 * Pdt related actions
 *
 */

export const trackPdtThankYouLoad = async (data) => {
  const {bookingData, searchContextData} = data;
  const commonPdt = await getCommonPdtData();
  const pdtData = {
    ...commonPdt,
    bookingData,
    searchContextData,
  };
  RailsPdtThankYouHelper.trackPageLoad(pdtData);
};
