import React from 'react';
import {View} from 'react-native';
import {WebView} from 'react-native-webview';
import PropTypes from 'prop-types';
import promiseAlert from '@mmt/legacy-commons/Common/Components/Alert/promiseAlert';
import {trackRailIrctcPasswordAfterPaymentLoad} from '../../railsAnalytics';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import {getMmtId, RAILS_CALL_BACK_URL_PWA_SUCCESS} from './PostPaymentUtils';
import IrctcCustomLoader from './Components/IrctcCustomLoader';
import connectVernalar from './connectVernalar';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

let CALLBACK_URL_INTERCEPTION_SUCCEDED = false;

class ConfirmBookingWebView extends React.Component {
  webViewRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      canGoBack: false,
      webViewLoad: true,
    };
  }

  UNSAFE_componentWillMount() {
    CALLBACK_URL_INTERCEPTION_SUCCEDED = false;
  }

  onHardBackPress = () => {
    const {canGoBack} = this.state;
    if (canGoBack && this.webViewRef.current) {
      this.webViewRef.current.goBack();
    } else if (!canGoBack) {
      this._alertUser();
    }
    return true;
  };

  _alertUser = async () => {
    const action = await promiseAlert('', 'Are you sure you want to cancel the booking?', 'Yes, cancel booking', 'No, don\'t cancel');
    if (action === 'Yes, cancel booking') {
      const mmtId = await getMmtId();
      this.props.callbackFunc(`${RAILS_CALL_BACK_URL_PWA_SUCCESS + mmtId}?bookingStatus=F`);
    }
  };

  _checkIfCallbackUrl = (url) => {
    for (let i = 0; i < this.props.callbackUrl.length; i++) {
      if ((url.startsWith('http') || url.startsWith('https')) &&
        (url.indexOf(this.props.callbackUrl[i]) > -1 || url.startsWith(this.props.callbackUrl[i]))) {
        CALLBACK_URL_INTERCEPTION_SUCCEDED = true;
        return true;
      }
    }
    return false;
  };

  _onShouldStartLoadWithRequest = (request) => {
    if (!CALLBACK_URL_INTERCEPTION_SUCCEDED && this._checkIfCallbackUrl(request.url)) {
      if (this.webViewRef.current !== null) {
        this.webViewRef.current.stopLoading();
        this.props.callbackFunc(request.url);
      }
      return false;
    }
    return !CALLBACK_URL_INTERCEPTION_SUCCEDED;
  };

  _onNavigationStateChange = (navState) => {
    this.setState({
      canGoBack: navState.canGoBack,
    });
    this._onShouldStartLoadWithRequest(navState);
  };

  _injectCode = () => {
    const {labels} = this.props;
    const {areYouSure} = labels;
    const script = `
      var currentFunction = document.getElementById("loginCancel").onclick;
      document.getElementsByClassName('loginCaptcha')[0].style.fontSize = '16px';
      document.getElementsByClassName('loginPassword')[0].style.fontSize = '16px';
      document.getElementById("loginCancel").onclick = function (){
        var retVal = confirm("${areYouSure}");
        if( retVal === true ){
          currentFunction()
        }
      }
     `;
    this.webViewRef.current.injectJavaScript(script);
    trackRailIrctcPasswordAfterPaymentLoad();
  };

  _onLoadEnd = () => {
    this.setState({webViewLoad: false});
  };

  render() {
    const {htmlCode, labels} = this.props;
    return (
      <View style={{
        flex: 1,
        overflow: 'hidden',
      }}
      >
        {this.state.webViewLoad &&
        <View style={{
          zIndex: 5, ...getPlatformElevation(5),
          width: '100%',
          height: '100%',
          position: 'absolute',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: colors.white,
        }}
        >
          <IrctcCustomLoader labels={labels}/>
        </View>
        }
        <WebView
          ref={this.webViewRef}
          onNavigationStateChange={this._onNavigationStateChange}
          onShouldStartLoadWithRequest={this._onShouldStartLoadWithRequest}
          source={{html: htmlCode}}
          style={{flex: 1, flexGrow: 1}}
          javaScriptEnabled
          onLoad={this._injectCode}
          onLoadEnd={this._onLoadEnd}
        />


      </View>
    );
  }
}

ConfirmBookingWebView.propTypes = {
  callbackUrl: PropTypes.array.isRequired,
  callbackFunc: PropTypes.func.isRequired,
  htmlCode: PropTypes.string,
  jsCode: PropTypes.string,
  labels: PropTypes.shape({
    areYouSure: PropTypes.string,
  }),
};

ConfirmBookingWebView.defaultProps = {
  htmlCode: '<html><body>Something is wrong, please try after sometime.</body></html>',
  jsCode: '',
};

ConfirmBookingWebView.navigationOptions = {
  header: null,
};


export default connectVernalar(ConfirmBookingWebView);
