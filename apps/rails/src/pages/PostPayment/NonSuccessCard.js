import React from 'react';
import {Image,Text, View, BackHandler, StyleSheet} from 'react-native';
import { Actions } from '../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import { trackThankYouPageFailurePageLoad} from '../../railsAnalytics';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';

import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';
import ASSETS from '../../Utils/Assets/RailsAssets';
import homeIcon from '@mmt/legacy-assets/src/rails_home_icon.webp';
import pendingIcon from '@mmt/legacy-assets/src/rails_booking_pending.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';
const failureImg = ASSETS.railsBookingCancelled;
const mapStatusToImg = {Pending: pendingIcon, Failed: failureImg};

class NonSuccessCard extends React.Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onHardBackPress);
    trackThankYouPageFailurePageLoad(this.props.data?.bnppData?.dueAmount > 0);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onHardBackPress);
  }

  onHardBackPress = () => {
    if (isIos()) {
      ViewControllerModule.thankyouDismiss(this.props.rootTag);
    } else {
      Actions.rails({
        isFromNewLanding: true,
      });
    }
    return true;
  };


  render() {
    const {
      bookingStatusMessage,
      bookingStatusSubMessage,
      bookingInfo,
      bookingId,
      status,
      colorArray,
    } = this.props.data;

    return (
      <View style={{marginBottom: 50}}>
        <LinearGradient
          style={NonSuccessStyle.colorCard}
          colors={colorArray}
          start={{x: 0.0, y: 1.0}}
          end={{x: 1.0, y: 0.0}}
        >
          <View style={NonSuccessStyle.imageContainer}>
            <TouchableRipple onPress={this.onHardBackPress}>
              <Image style={NonSuccessStyle.homeIcon} source={homeIcon} testID={`${this.props?.id}_homeIcon`} />
            </TouchableRipple>
            <View style={NonSuccessStyle.mainIconContainer}>
              <Image style={NonSuccessStyle.mainIconStyle} source={mapStatusToImg[status]} />
            </View>
            <View style={NonSuccessStyle.placeholder} />

          </View>
          <View style={{alignItems: 'center'}}>
            <Text style={[NonSuccessStyle.statusMessage, fontStyle('black'), getLineHeight(24)]}>{bookingStatusMessage}</Text>
            <Text style={[NonSuccessStyle.statusSubMessage, fontStyle('regular'), getLineHeight(18)]}>{bookingStatusSubMessage}</Text>
            <Text style={[NonSuccessStyle.bookingId, fontStyle('regular'), getLineHeight(16)]}>{`${_label('booking_id')}: `}
              <Text style={fontStyle('bold')}>{bookingId}</Text>
            </Text>
          </View>
        </LinearGradient>
        <View style={NonSuccessStyle.bookingInfoCard}>
          <Text style={[NonSuccessStyle.bookingInfoText, fontStyle('regular'), getLineHeight(14)]}> {bookingInfo}</Text>
        </View>
      </View>


    );
  }
}

const NonSuccessStyle = StyleSheet.create({
  colorCard: {height: 280, width: '100%'},
  imageContainer: {flexDirection: 'row', marginHorizontal: 16},
  homeIcon: {
    width: 22, height: 22, marginTop: 21,
  },
  mainIconContainer: {flex: 1, alignItems: 'center', marginTop: 15},
  mainIconStyle: {width: 66, height: 66},
  placeholder: {width: 17, height: 17, marginTop: 21},
  statusMessage: {
     fontSize: 24, color: colors.white, textAlign: 'center',
  },
  statusSubMessage: {
     fontSize: 18, color: colors.white, textAlign: 'center', marginVertical: 5, marginHorizontal: 40,
  },
  bookingId: {
     fontSize: 16, color: colors.white, textAlign: 'center', marginVertical: 25,
  },
  bookingInfoCard: {
     height: 74, alignSelf: 'center', position: 'absolute', top: 240, marginVertical: 0, marginHorizontal: 12, borderRadius: 6,
    backgroundColor: colors.white,
  },
  bookingInfoText: {
     fontSize: 14, lineHeight: 18, color: colors.black, paddingHorizontal: 20, paddingVertical: 12,
  },
});

NonSuccessCard.propTypes = {
  data: PropTypes.any,
  rootTag: PropTypes.number,
  id: PropTypes.string,
};

export default NonSuccessCard;
