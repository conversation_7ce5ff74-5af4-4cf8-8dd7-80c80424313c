import React, { useState } from 'react';
import {View, Text, StyleSheet, Platform} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { RAIL_THANK_YOU_PAGE_APP } from 'packages/legacy-commons/adsConfig/adsConfigConstants';
import { getThankYouPageCouponLounge } from '@mmt/rails/src/RailsAbConfig';
import {getAdsCard} from '@mmt/legacy-commons/adsConfig/adsCard';
import PropTypes from 'prop-types';

const PriceBreakUp = (props) => {
  const { priceBreakUp } = props;
  const [errorOnAdCall, setErrorOnAdCall] = useState(false);

  function displayCouponLoungeAd() {
    const showAdAsPerPokus = getThankYouPageCouponLounge();
    if (showAdAsPerPokus) {
      function onError() {
        setErrorOnAdCall(true);
      }

      if (errorOnAdCall) {
        return null;
      } else {
        return (
          <View style={priceBreakup.marginBottom10}>
            <Text style={priceBreakup.coupontext}>
              {_label('congrats_you_got_access_to_wow_deals')}
            </Text>
            <View style={priceBreakup.adContainer}>
              {getAdsCard(Platform.OS, RAIL_THANK_YOU_PAGE_APP, null, onError)}
            </View>
          </View>
        );
      }
    }
    return;
  }

  return (
    <View style={priceBreakup.rootTagStyle} testID={props?.id}>
      <View style={priceBreakup.priceContainer}>
        <>
          {displayCouponLoungeAd()}
        </>
        <View style={priceBreakup.priceContainerPrice}>
          <Text style={[priceBreakup.breakUp, fontStyle('light'), getLineHeight(22)]}> {_label('price_breakup')} </Text>
          <View style={priceBreakup.priceSection}>
            <RupeeText style={[priceBreakup.totalFare, fontStyle('black'), getLineHeight(22)]}>{priceBreakUp.totalAmount}</RupeeText>
            <Text style={[priceBreakup.totalAmntLbl, fontStyle('regular'), getLineHeight(12)]}>{priceBreakUp.totalAmountLabel}</Text>
          </View>
        </View>
      </View>
      <Text style={[priceBreakup.baseFare, fontStyle('black'), getLineHeight(14)]}>{_label('base_fare')}</Text>
      {priceBreakUp.breakUpAmountAndLabelList.map((item, index) => (
        <View style={priceBreakup.container} key={item.label || index}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <RupeeText style={[priceBreakup.keyValue, fontStyle('regular'), getLineHeight(12)]}>{item.label}</RupeeText>
            <Text style={[priceBreakup.keyValue, fontStyle('regular'), getLineHeight(12)]}>{item.amount}</Text>
          </View>
        </View>
      ))}
    </View>
  );
};


const priceBreakup = StyleSheet.create({
  container: {
    marginTop: 5,
    marginHorizontal: 16,
  },
  keyValue: {
    fontSize: 12,
    color: colors.black,
    marginBottom: 2,
  },
  rootTagStyle: {
    marginBottom: 10,
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
    paddingBottom: 20,
  },
  priceContainer: {
    flexDirection: 'column',
    marginHorizontal: 16,
    justifyContent: 'space-between',
    marginTop: 10,
  },
  priceContainerPrice: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  breakUp: {
    fontSize: 22,
    color: colors.defaultTextColor,
  },
  priceSection: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  totalFare: {
    fontSize: 22, color: colors.black,
  },
  totalAmntLbl: {
     fontSize: 12,
     color: colors.defaultTextColor,
     alignSelf: 'flex-end',
  },
  baseFare: {
    fontSize: 14,
    marginTop: 10,
    marginHorizontal: 16,
    color: colors.defaultTextColor,
  },
  coupontext: {
    fontWeight: '900',
    color: colors.black,
    fontSize: 18,
    marginBottom: 10,
    display: 'flex',
    textAlign: 'left',
  },
  adContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  marginBottom10: {
    marginBottom: 10,
  },
});

PriceBreakUp.propTypes = {
  id: PropTypes.string,
  priceBreakUp: PropTypes.shape({
    totalAmount: PropTypes.string,
    totalAmountLabel: PropTypes.string,
    breakUpAmountAndLabelList: PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string,
        amount: PropTypes.string,
      }),
    ),
  }),
};

export default PriceBreakUp;
