import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import { _label } from '../../../vernacular/AppLanguage';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';


const TravellerPage = (props) => {
  const {travellerDetails} = props;
  return (
    <View style={travellerStyle.rootTagStyle} testID={props?.id}>
      <Text style={[travellerStyle.heading, fontStyle('light'), getLineHeight(22)]}>{ _label('travellers')}</Text>
      <Text style={[travellerStyle.travellerCount, fontStyle('bold'), getLineHeight(14)]}>{travellerDetails.travellerCountText}</Text>
      {travellerDetails.travellerList.map(item => (
        <View style={{marginBottom: 16}} key={`${item.travellerName}-${item.travellerSeatDetails}`}>
          <TravellerListView item={item}/>
        </View>
      ))}
    </View>
  );
};

const getTravellerData = (t) => {
  return `${t.travellerAge} Y, ${t.travellerGender}`;
};

const TravellerListView = ({item}) => (
  <View style={{marginHorizontal: 16}}>
    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
      <View>
        <View style={{flexDirection: 'row'}}>
          <Text style={[travellerStyle.name, fontStyle('bold'), getLineHeight(14)]}>{item.travellerName}</Text>
          <Text style={[travellerStyle.ageGender, fontStyle('regular'), getLineHeight(12)]}>{getTravellerData(item)}</Text>
        </View>
        {(item.travellerBirthPreference || item.travellerFoodPreference) &&
        <Text style={[travellerStyle.birth, fontStyle('regular'), getLineHeight(12)]}>{item.travellerBirthPreference || ''}
          <Text style={[travellerStyle.food, fontStyle('regular'), getLineHeight(12)]}> {item.travellerFoodPreference ? item.travellerBirthPreference ? `, ${item.travellerFoodPreference}` : `${item.travellerFoodPreference}` : ''}</Text>
        </Text>
        }
        {item.travellerSeniorCitizenConcessionText &&
          <Text style={[travellerStyle.seniorCitizen, fontStyle('regular'), getLineHeight(12)]}>{item.travellerSeniorCitizenConcessionText}</Text>
        }
      </View>
      <Text style={[travellerStyle.seatDetails, fontStyle('bold'), getLineHeight(14)]}>
        {item.travellerSeatDetails}/{item?.journeyClass}
      </Text>
    </View>
  </View>
);


const travellerStyle = StyleSheet.create({
  seniorCitizen: {
     fontSize: 12, color: colors.defaultTextColor, marginTop: 3,
  },
  seatDetails: {
     fontSize: 14, color: colors.cyan,
  },
  rootTagStyle: {
    marginTop: 10,
    marginBottom: 10,
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
  travellerCount: {
    fontSize: 14,
    marginTop: 10,
    marginHorizontal: 16,
    color: colors.defaultTextColor,
    marginBottom: 16,
  },
  heading: {
     fontSize: 22, marginHorizontal: 16, color: colors.defaultTextColor, marginTop: 16,
  },
  name: {
     fontSize: 14, color: colors.black,
  },
  ageGender: {
     fontSize: 12, color: colors.defaultTextColor, marginLeft: 8,
  },
  birth: {
     fontSize: 12, color: colors.defaultTextColor, marginTop: 3,
  },
  food: {
     fontSize: 12, color: colors.defaultTextColor, marginTop: 3,
  },
});

TravellerPage.propTypes = {
  id: PropTypes.string,
  travellerDetails: PropTypes.shape({
    travellerCountText: PropTypes.string,
    travellerList: PropTypes.arrayOf(
      PropTypes.shape({
        travellerName: PropTypes.string,
        travellerSeatDetails: PropTypes.string,
        travellerAge: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        travellerGender: PropTypes.string,
        travellerBirthPreference: PropTypes.string,
        travellerFoodPreference: PropTypes.string,
        travellerSeniorCitizenConcessionText: PropTypes.string,
        journeyClass: PropTypes.string,
      }),
    ),
  }),
};

TravellerListView.propTypes = {
  item: PropTypes.shape({
    travellerName: PropTypes.string,
    travellerSeatDetails: PropTypes.string,
    travellerAge: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    travellerGender: PropTypes.string,
    travellerBirthPreference: PropTypes.string,
    travellerFoodPreference: PropTypes.string,
    travellerSeniorCitizenConcessionText: PropTypes.string,
    journeyClass: PropTypes.string,
  }),
};

export default TravellerPage;
