import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import fecha from 'fecha';
import {getStnNameConcatenated} from '../../NewListing/Components/NewTrainInfo';
import {getTrainName} from '../../Common/JourneyDetailsCard';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';

const TrainDetailsThankUPage = (props) => {
const {trainDetails: trainInfo} = props;
const dept = getDateTime(trainInfo.boardingDateTime);

const arr = getDateTime(trainInfo.arrivalDateTime);

  return (
    <View style={trainInfoStyles.rootTagStyle} testID={props?.id}>
      <Text style={[trainInfoStyles.trainDetailHeader, fontStyle('light'), getLineHeight(22)]}>{ _label('train_summary') }</Text>
      <View style={trainHeader.container}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
          <Text style={[trainHeader.name, fontStyle('black'), getLineHeight(16)]}>{getTrainName(trainInfo.trainName.toLowerCase())}</Text>
          <Text style={[trainHeader.number, fontStyle('regular'), getLineHeight(16)]}>{`#${trainInfo.trainNumber}`}</Text>
        </View>
      </View>
      <View style={{
        marginHorizontal: 16,
        marginTop: 10,
        marginBottom: 16,
      }}>
        <View style={styles.timeContainer}>
          <View style={{flexDirection: 'row'}}>
              <Text style={[styles.timeText, fontStyle('black'), getLineHeight(12)]}>{dept.time}, </Text>
              <Text style={[styles.dayText, fontStyle('regular'), getLineHeight(12)]}>{dept.date}</Text>
          </View>
          <View style={styles.separator} />
          <Text style={[styles.duration, fontStyle('regular'), getLineHeight(12)]}>{trainInfo.duration}</Text>
          <View style={styles.separator} />
          <View style={{flexDirection: 'row'}}>
            <Text style={[styles.timeText, fontStyle('black'), getLineHeight(12)]}>{arr.time}, </Text>
            <Text style={[styles.dayText, fontStyle('regular'), getLineHeight(12)]}>{arr.date}</Text>
          </View>
        </View>

        <View style={styles.stationDetailContainer}>
          <Text style={[styles.stationDetailsText, fontStyle('regular'), getLineHeight(12)]}>
            {`${getStnNameConcatenated(trainInfo.boardingStation.stationName)} (${trainInfo.boardingStation.stationCode})`}
          </Text>
          <Text style={[styles.stationDetailsText, fontStyle('regular'), getLineHeight(12)]}>
            {`${getStnNameConcatenated(trainInfo.destinationStation.stationName)} (${trainInfo.destinationStation.stationCode})`}
          </Text>
        </View>
      </View>
    </View>
  );
};

const getDateTime = (dateTimeStr) => {
  const dateTime = fecha.parse(dateTimeStr, 'YYYY-MM-DDTHH:mm:ss');
  let date = '';
  let time = '';
  if (dateTime) {
    time = fecha.format(dateTime, 'hh:mm A');
    date = fecha.format(dateTime, 'DD MMM');
  }
  return {date, time};
};


const trainInfoStyles = StyleSheet.create({

  rootTagStyle: {
    justifyContent: 'space-between',
    marginTop: 20,
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
  trainDetailHeader: {
    fontSize: 22,
    color: colors.black,
    marginTop: 15,
    marginLeft: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

const regularLightText = {
  fontSize: 12,
  color: colors.defaultTextColor,
};

const styles = StyleSheet.create({
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: colors.black,
  },
  dayText: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  duration: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  stationDetailContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginTop: 10,
  },
  stationDetailsText: {
    ...regularLightText,
  },
  separator: {
    width: 30,
    height: 1,
    borderColor: colors.lightGrey,
    borderWidth: 1,
  },
});

const trainHeader = StyleSheet.create({
  container: {flexDirection: 'column', marginTop: 10, marginHorizontal: 16},
  name: {
    fontSize: 16,  color: colors.black, marginBottom: 2,
  },
  number: {
    fontSize: 16,  color: colors.lightTextColor, marginBottom: 6,
  },
});

TrainDetailsThankUPage.propTypes = {
  id: PropTypes.string,
  trainDetails: PropTypes.shape({
    boardingDateTime: PropTypes.string,
    arrivalDateTime: PropTypes.string,
    trainName: PropTypes.string,
    trainNumber: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    duration: PropTypes.string,
    boardingStation: PropTypes.shape({
      stationName: PropTypes.string,
      stationCode: PropTypes.string,
    }),
    destinationStation: PropTypes.shape({
      stationName: PropTypes.string,
      stationCode: PropTypes.string,
    }),
  }),
};

export default TrainDetailsThankUPage;
