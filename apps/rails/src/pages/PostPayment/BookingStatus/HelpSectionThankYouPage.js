import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import { _label } from '../../../vernacular/AppLanguage';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';

const HelpSection = (props) => (
  <View style={helpStyle.container} testID={props?.id}>
    <Text style={[helpStyle.helpText, fontStyle('light'), getLineHeight(22)]}>
        {`${_label('need_help')}?`}
    </Text>
    <Text style={[helpStyle.queriesText, fontStyle('normal'), getLineHeight(14)]}>
      {_label('have_queries')}
    </Text>
  </View>
);

const helpStyle = StyleSheet.create({
  container: {
    width: '100%',
    height: 123,
    backgroundColor: colors.white,
    marginTop: 10,
    marginBottom: 20,
  },
  helpText: {
     fontSize: 22,
     color: colors.defaultTextColor,
     marginLeft: 16,
     marginTop: 10,
  },
  queriesText: {
    marginLeft: 16,
    marginRight: 16,
    color: colors.black,
    marginTop: 9,
  },
});

HelpSection.propTypes = {
  id: PropTypes.string,
};

export default HelpSection;
