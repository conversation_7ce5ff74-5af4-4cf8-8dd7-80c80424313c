import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';

const WarningThankYouPage = (props) => {
  // eslint-disable-next-line react/prop-types
  const {importantInformation} = props;
  return (
    <View style={warningStyle.container} testID={props?.id}>
      <LinearGradient
        start={{x: 1.0, y: 0.0}}
        end={{x: 0.0, y: 1.0}}
        colors={['#ff3e5e', '#ff7f3f']}
        style={warningStyle.infoHeader}
        elevation={1}
      >
        <Text style={[warningStyle.infoLabel, fontStyle('bold'), getLineHeight(12)]}>
          {importantInformation.header}
        </Text>
      </LinearGradient>
      <LinearGradient
        start={{x: 1.0, y: 0.0}}
        end={{x: 0.0, y: 1.0}}
        colors={['#ffeae1', '#ffe3d5']}
        style={warningStyle.subHeaderContainer}
      >
        <View style={{flexDirection: 'row', marginLeft: 8}}>
          <View style={warningStyle.subHeader} />
          <Text style={[warningStyle.subHeaderText, fontStyle('bold'), getLineHeight(12)]}>
            {importantInformation.subHeader}
          </Text>
        </View>
        <CustomBoldText str={importantInformation.text} />
      </LinearGradient>
    </View>
  );
};

const CustomBoldText = ({str}) => {
    return (
      <View>
        <Text style={[warningStyle.infoText, fontStyle('regular'), getLineHeight(10)]}>{str}</Text>
      </View>);
};

const warningStyle = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    width: '100%',
    paddingVertical: 15,
  },
  infoHeader: {
    borderRadius: 12,
    height: 22,
    width: 176,
    position: 'absolute',
    top: -7,
    alignSelf: 'flex-start',
    marginRight: 5,
    flexDirection: 'row',
    marginLeft: 37,
    zIndex: 6,
  },
  infoLabel: {
    color: colors.white,
    marginLeft: 6,
    alignSelf: 'center',
  },
  subHeaderContainer: {
    marginRight: 8,
    marginLeft: 8,
    width: '95%',
    borderRadius: 4,
    paddingBottom: 15,
  },
  subHeader: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
    marginLeft: 13,
    backgroundColor: colors.defaultTextColor,
    marginTop: 18,
    alignSelf: 'center',
  },
  subHeaderText: {
    marginTop: 18,
    color: colors.defaultTextColor,
  },
  infoText: {
    marginLeft: 29,
    marginRight: 16,
    fontSize: 10,
    marginTop: 10,
    lineHeight: 16,
  },
});

WarningThankYouPage.propTypes = {
  id: PropTypes.string,
  importantInformation: PropTypes.shape({
    header: PropTypes.string,
    subHeader: PropTypes.string,
    text: PropTypes.string,
  }),
};

CustomBoldText.propTypes = {
  str: PropTypes.string,
};

export default WarningThankYouPage;
