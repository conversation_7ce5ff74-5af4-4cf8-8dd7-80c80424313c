import React from 'react';
import {View, Text, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { isMweb } from '../../../Utils/device';
import { THANK_YOU_PDT_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import PropTypes from 'prop-types';

const deeplink = 'mmyt://support/booking/listing/';
const mweblink = 'https://pwa-supportz.makemytrip.com/MyAccount/BookingSummary';

const ManageBooking = (props) => (
  <View style={{height: 60, backgroundColor: colors.white}}>
        <TouchableRipple onPress={() => {
      openDeepLink(deeplink);
      if (props?.logThankYouPageClickEvent) {
        props?.logThankYouPageClickEvent(THANK_YOU_PDT_CLICK_EVENTS.MANAGE_BOOKING_CLICK);
      }
    }}>
      <LinearGradient
        start={{x: 1.0, y: 0.0}}
        end={{x: 0.0, y: 1.0}}
        colors={['#065af3', '#53b2fe']}
        style={manageBooking.gradient}
      >
        <Text style={[manageBooking.manageText, fontStyle('black'), getLineHeight(16)]} testID={props?.id}>{_label('manage_booking', { uppercase: true })}</Text>
      </LinearGradient>
    </TouchableRipple>
  </View>
);

const openDeepLink = (deepLink) => {
  if (isMweb()) {
    window.location.href = mweblink;
  } else {
    GenericModule.openDeepLink(deepLink);
  }
};

const manageBooking = StyleSheet.create({
  gradient: {
    margin: 10, borderRadius: 5, justifyContent: 'center',height: 40,
  },
  manageText: {
    alignSelf: 'center', color: colors.white, fontSize: 16,
  },
});

ManageBooking.propTypes = {
  id: PropTypes.string,
  logThankYouPageClickEvent: PropTypes.func,
};

export default ManageBooking;
