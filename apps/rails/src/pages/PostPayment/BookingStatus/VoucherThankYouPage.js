import React, {useEffect, useState} from 'react';
import { Actions } from '../../../navigation';
import { View, Text, Image, StyleSheet, Clipboard } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {trackCongratsPageClickEvents} from '../../../railsAnalytics';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

import giftVoucher from '@mmt/legacy-assets/src/confetti_gift.webp';
import copyIcon from '@mmt/legacy-assets/src/copy_icon.webp';
import clockIcon from '@mmt/legacy-assets/src/ic_clock.webp';
import PropTypes from 'prop-types';

const VoucherThankYouPage = (props) => {
  const {
    voucherIssueTime, voucherExpirationTime, voucherCode, discountPercentage,
  } = props;
  const [timer, setTimer] = useState(voucherIssueTime);
  const [isActive, setActive] = useState(true);

  useEffect(() => {
    let interval = null;
    if (voucherIssueTime < voucherExpirationTime) {
      interval = setInterval(() => {
        // eslint-disable-next-line no-shadow
        setTimer(timer => timer + 1);
      }, 1000);
    } else {
      setActive(false);
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, []);

  return (
    <View elevation={3} testID={props?.id}>
      <LinearGradient
        start={{x: 1.0, y: 0.0}}
        end={{x: 0.0, y: 1.0}}
        colors={['#3023ae', '#9416aa', '#c86dd7']}
        style={voucherStyle.containerGradient}
      >
        {isActive &&
        <View style={{marginRight: 5}}>
          <LinearGradient
            start={{x: 1.0, y: 0.0}}
            end={{x: 0.0, y: 1.0}}
            colors={['#ff3e5e', '#ff7f3f']}
            style={voucherStyle.timerGradient}
          >
            <Image
              source={clockIcon}
              style={voucherStyle.clockIcon}
            />
            <Text style={[voucherStyle.timeLeft, fontStyle('bold'), getLineHeight(12)]}>
              {timeLeft(timer)}
            </Text>
          </LinearGradient>
        </View>}
        <View style={{flexDirection: 'row', marginBottom: 9}}>
          <Image source={giftVoucher} style={{margin: 7, width: 90, height: 63}} />
          <View style={{justifyContent: 'center', marginLeft: 22}}>
            <Text style={[voucherStyle.congrats, fontStyle('light'), getLineHeight(22)]}>
              {`${_label('congratulations')}!`}
            </Text>
            <Text style={[voucherStyle.discount, fontStyle('bold'), getLineHeight(14)]}>
              {_label('off_on_hotels', undefined, { discount: discountPercentage })}
            </Text>
          </View>
        </View>
        <View style={voucherStyle.voucherContainer}>
          <View style={voucherStyle.voucherCodeContainer}>
            <Text style={[voucherStyle.voucherCodeLabel, fontStyle('regular'), getLineHeight(10)]}>
              {_label('voucher_code')}
            </Text>
            <Text style={[voucherStyle.voucherCode, fontStyle('black'), getLineHeight(16)]}>
              {voucherCode.toUpperCase()}
            </Text>
          </View>
          <TouchableRipple onPress={() => copyCodeAndUseTapped(voucherCode)}>
            <View style={{ marginHorizontal: 10, justifyContent: 'center' }} testID={`${props?.id}_copyIcon`}>
              <Image source={copyIcon} style={{justifyContent: 'flex-end', width: 20, height: 24}} />
              <Text style={[voucherStyle.copyIcon, fontStyle('bold'), getLineHeight(10)]}>{_label('copy')}</Text>
            </View>
          </TouchableRipple>
        </View>
      </LinearGradient>
      <View style={voucherStyle.voucherInfo}>
        <Text style={[voucherStyle.voucherInfoText, fontStyle('regular'), getLineHeight(10)]}>
          {_label('code_will_be_sent')}
        </Text>
        <TouchableRipple onPress={() => onHotelTncClick()}>
          <Text style={[voucherStyle.tNc, fontStyle('bold'), getLineHeight(10)]} testID={`${props?.id}_tnc`}>
            {_label('view_tnc')}
          </Text>
        </TouchableRipple>
      </View>
    </View>
  );
};

const copyCodeAndUseTapped = (code) => {
  if (code) {
    Clipboard.setString(code);
    showShortToast(_label('voucher_code_copied'));
  }
};

const onHotelTncClick = () => {
  trackCongratsPageClickEvents('mob_rail_voucher_congrats_view_t&c_clicked');
  Actions.openWebView({
    headerText: _label('voucher_tnc'),
    url: 'https://promos.makemytrip.com/dh-railusers-15022019.html',
    headerIcon: backIcon,
  });
};


const timeLeft = (timer) => {
  const dateObj = new Date(timer * 1000);
  const hours = dateObj.getUTCHours();
  const minutes = dateObj.getUTCMinutes();
  const seconds = dateObj.getSeconds();

  const timeString = `${hours.toString().padStart(2, '0')}h` + ` : ${
    minutes.toString().padStart(2, '0')}m` + ` : ${
      seconds.toString().padStart(2, '0')}s`;
  return timeString;
};

const voucherStyle = StyleSheet.create({
  containerGradient: {
    height: 154, marginLeft: 8, marginRight: 8, marginTop: 21, borderTopLeftRadius: 5, borderTopRightRadius: 5,
  },
  timerGradient: {
    borderRadius: 12, height: 22, width: 126, position: 'absolute', bottom: -10, alignSelf: 'flex-end', marginRight: 5, flexDirection: 'row',
  },
  clockIcon: {
    width: 17, height: 17, alignSelf: 'center', marginLeft: 3,
  },
  timeLeft: {
    color: colors.white, fontSize: 12,  marginLeft: 6, alignSelf: 'center',
  },
  congrats: {
     fontSize: 22, color: colors.white,
  },
  discount: {
     fontSize: 14, color: colors.white, marginTop: 3,
  },
  voucherContainer: {
    backgroundColor: colors.blue9,
    width: '95%',
    height: 49,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'center',
    borderRadius: 3,
  },
  voucherCodeContainer: {
    marginHorizontal: 20, justifyContent: 'center', borderRadius: 4,
  },
  voucherCodeLabel: {
     fontSize: 10, color: colors.defaultTextColor,
  },
  voucherCode: {
     fontSize: 16, color: colors.black,
  },
  copyIcon: {
    fontSize: 10, color: colors.azure,
  },
  voucherInfoText: {
    color: colors.defaultTextColor, fontSize: 10, marginLeft: 16,
  },
  voucherInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 22,
    backgroundColor: colors.white,
    alignItems: 'center',
    marginLeft: 8,
    marginRight: 8,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
  },
  tNc: {
    color: colors.azure, fontSize: 10, marginRight: 16,
  },
});

VoucherThankYouPage.propTypes = {
  voucherIssueTime: PropTypes.number,
  voucherExpirationTime: PropTypes.number,
  voucherCode: PropTypes.string,
  discountPercentage: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  viewTnc: PropTypes.func,
  id: PropTypes.string,
};

export default VoucherThankYouPage;
