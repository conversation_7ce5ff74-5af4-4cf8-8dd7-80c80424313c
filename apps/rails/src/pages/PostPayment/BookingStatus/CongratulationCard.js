import {View, Text, Image, StyleSheet, Clipboard} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { getBusRailsPremiumConfig } from 'apps/rails/src/RailsAbConfig';
import { RAILS_BUS_LANDING_PREMIUM } from '@mmt/legacy-commons/Common/constants/AppConstants';
import { isPremiumUser as checkPremiumUser } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import React, { useEffect, useState } from 'react';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import MultiStyleVernacText from '../../../vernacular/MultiStyleVernacText';
import { isMweb, isApp } from '../../../Utils/device';
import { Actions } from 'apps/rails/src/navigation';
import { BookNowPayPartialStrip } from '../../TravelerDetails/Components/BookNowPayPartial';
import { THANK_YOU_PDT_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import homeIcon from '@mmt/legacy-assets/src/home_icon.webp';
import suitCaseIcon from '@mmt/legacy-assets/src/illustration.webp';
import copyIconBlue from '@mmt/legacy-assets/src/congratulationCardPageCopyButton.webp';
import PropTypes from 'prop-types';

const bookReturnTicket = (sourceCode, destinationCode, isPremiumUser = false) => {
  if (isMweb()) {
    window.location.href = `https://rails.makemytrip.com/pwa/v2/?from=${sourceCode}&to=${destinationCode}`;
  } else {
    Actions.pop();//Added this to clear the navigation history
    if (isPremiumUser)
    {
      GenericModule.openDeepLink(
        `mmyt://rails/?page=railsBusCommonLanding&defaultLob=${RAILS_BUS_LANDING_PREMIUM.RAILS_TAB}&from=${sourceCode}&to=${destinationCode}`,
      );
      return;
    }
    GenericModule.openDeepLink(`mmyt://rails/?page=rails&railsBusCommonLanding=true&isFromNewLanding=true&from=${sourceCode}&to=${destinationCode}`);
  }
};

const CongratulationCard = (props) => {
  const {
    bookingDetails, returnTripDetails, bookingInfo, pnrNo, bnppData,
  } = props;
  const {userCommunicationDetails: {email, mobile}} = bookingDetails;
  const { dueDate, dueAmount } = bnppData || {};

  const [isPremiumUser, setIsPremiumUser] = useState(false);

  useEffect(() => {
    async function setPremiumUser() {
      const railsBusPremiumPokus = await getBusRailsPremiumConfig();
      const isPremiumUser = (await checkPremiumUser(null)) ?? false;
      setIsPremiumUser(isPremiumUser && railsBusPremiumPokus === 2);
    }
    setPremiumUser();
  }, []);

  const copyButtonClick = (valueToCopy) => {
    if (isApp()) {
      Clipboard.setString(valueToCopy);
    } else {
      navigator.clipboard.writeText(valueToCopy);
    }
  };

  return (
    <View testID={props?.id}>
      <LinearGradient
        start={{x: 1.0, y: 0.0}}
        end={{x: 0.0, y: 1.0}}
        colors={['#3a7bd5', '#00d2ff']}
      >
        <TouchableRipple onPress={() => {
          props.backPress();
          props?.logThankYouPageClickEvent(THANK_YOU_PDT_CLICK_EVENTS.THANK_YOU_HOME_ICOON_CLICK);
        }}>
          <Image source={homeIcon} style={congStyles.homeIcon} testID={`${props?.id}_homeIcon`} />
        </TouchableRipple>
        <View style={{alignItems: 'center', marginRight: 16, marginLeft: 16}}>
          <Image source={suitCaseIcon} style={congStyles.suitCase} />
          <Text style={[congStyles.congratsText, fontStyle('black'), getLineHeight(24)]}>{bookingDetails.bookingStatusMessage}</Text>
          <Text style={[congStyles.bookingConfirm, fontStyle('regular'), getLineHeight(16)]}>{bookingDetails.bookingStatusSubMessage}</Text>
          <MultiStyleVernacText
              content = {_label('tickets_sent_to')}
              contentHorizontal = {true}
              params = {{mobile,email}}
              defaultContentStyle = {[congStyles.emailMobileText, fontStyle('regular'), getLineHeight(14)]}
              textStyles = {{
                email : [congStyles.emailMobileText , fontStyle('black'), getLineHeight(14)],
                mobile : [congStyles.emailMobileText, fontStyle('black'), getLineHeight(14)],
              }}
              viewStyle = {congStyles.emailMobileContainer}
          />
          <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems:'center'}}>
            <Text style={[congStyles.bookingIdContainer, fontStyle('regular'), getLineHeight(14)]}>
              {`${_label('booking_id')}: `}
            </Text>
            <Text style={[congStyles.bookingIdContainer, fontStyle('bold'), getLineHeight(14)]}>
              {' '}{bookingInfo}
            </Text>
            <TouchableRipple onPress={() => copyButtonClick(bookingInfo)} >
              <Image source={copyIconBlue} style={[congStyles.bookingIdContainerIcon, congStyles.copyIconStyle]} testID={`${props?.id}_bookingIDCopyIcon`} />
            </TouchableRipple>
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems:'center'}}>
            <Text style={[congStyles.pnrNoContainer, fontStyle('regular'), getLineHeight(14)]}>
            {`${_label('pnr_no')}: `}
            </Text>
            <Text style={[congStyles.pnrNoContainer, fontStyle('bold'), getLineHeight(14)]}>
              {' '}{pnrNo}
            </Text>
            <TouchableRipple onPress={() => copyButtonClick(pnrNo)} >
              <Image source={copyIconBlue} style={[congStyles.pnrNoContainerIcon, congStyles.copyIconStyle]} testID={`${props?.id}_PNRCopyIcon`} />
            </TouchableRipple>
          </View>
          {dueAmount > 0 && <BookNowPayPartialStrip type="THANK_YOU" dueDate={dueDate} dueAmount={dueAmount} />}
        </View>
      </LinearGradient>
      {returnTripDetails && (
        <View style={congStyles.cardStyle} elevation={3}>
        <View style={{ flex: 4, paddingHorizontal: 20 }}>
          <View style={{marginLeft: 16, justifyContent: 'center'}}>
            <Text numberOfLines={1} ellipsizeMode="tail" style={[congStyles.bookNow, fontStyle('black'), getLineHeight(14)]}>{returnTripDetails.returnTripMessage}</Text>
            <Text numberOfLines={1} ellipsizeMode="tail" style={fontStyle('regular')}>{returnTripDetails.returnTripSubMessage}</Text>
          </View>
        </View>
        <View style={{flex: 2}}>
          <TouchableRipple onPress={() => {
            const {sourceCode, destinationCode} = returnTripDetails;
              bookReturnTicket(sourceCode, destinationCode, isPremiumUser);
              props?.logThankYouPageClickEvent(THANK_YOU_PDT_CLICK_EVENTS.BOOK_RETURN_TICKET_CLICK);
          }}>
            <View style={congStyles.bookNowContainer} testID={`${props?.id}_bookReturnTicketButton`}>
              <Text numberOfLines={1} ellipsizeMode="tail" style={[congStyles.bookNowText, fontStyle('black'), getLineHeight(12)]}>{ _label('book_now', { uppercase: true }) }</Text>
            </View>
          </TouchableRipple>
        </View>
        </View>
      )}
    </View>
  );
};

const congStyles = StyleSheet.create({
  cardStyle: {
    height: 54,
    backgroundColor: colors.white,
    flexDirection: 'row',
    borderRadius: 3,
    position: 'relative',
    marginTop: 20,
    flex: 3,
    marginHorizontal: 20,
    alignItems: 'center',
  },
  bookNowContainer: {
    width: 103,
    height: 25,
    borderRadius: 18.5,
    backgroundColor: colors.azure,
    justifyContent: 'center',
    alignSelf: 'center',
    marginLeft: 0,
    marginRight: 16,
  },
  bookNow: {
    fontSize: 14,
    color: colors.black,
  },
  copyIconStyle : {
    marginLeft : 8,
    width : 20,
    height : 20,
  },
  homeIcon: {
    width: 16,
    height: 18,
    marginLeft: 16,
    marginTop: 44,
  },
  suitCase: {
    width: 66, height: 66, alignSelf: 'center', marginTop: -28,
  },
  congratsText: {
    fontSize: 24, color: colors.white, alignSelf: 'center', marginHorizontal: 16,
  },
  bookingConfirm: {
    fontSize: 16, color: colors.yellow7, alignSelf: 'center', marginHorizontal: 16,
  },
  bookingIdContainerIcon: {
    color: colors.white,
    marginTop: 15,
  },
  bookingIdContainer: {
    fontSize: 14,  color: colors.white, marginTop: 15,
  },
  pnrNoContainerIcon: {
    color: colors.white,
  },
  pnrNoContainer: {
    fontSize: 14,  color: colors.white,
  },
  bookNowText: {
     fontSize: 12, color: colors.white, alignSelf: 'center',
  },
  emailMobileText: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 18,
  },
  emailMobileContainer: {
    width: 288,
    justifyContent: 'center',
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop : 20,
    textAlign : 'center',
  },
});

CongratulationCard.propTypes = {
  id: PropTypes.string,
  logThankYouPageClickEvent: PropTypes.func,
  bookingDetails: PropTypes.shape({
    bookingStatusMessage: PropTypes.string,
    bookingStatusSubMessage: PropTypes.string,
    userCommunicationDetails: PropTypes.shape({
      email: PropTypes.string,
      mobile: PropTypes.string,
    }),
  }),
  returnTripDetails: PropTypes.shape({
    returnTripMessage: PropTypes.string,
    returnTripSubMessage: PropTypes.string,
    sourceCode: PropTypes.string,
    destinationCode: PropTypes.string,
  }),
  bookAgain: PropTypes.any,
  bookingInfo: PropTypes.string,
  pnrNo: PropTypes.string,
  bnppData: PropTypes.shape({
    dueDate: PropTypes.string,
    dueAmount: PropTypes.number,
  }),
  backPress: PropTypes.func,
};

export default CongratulationCard;
