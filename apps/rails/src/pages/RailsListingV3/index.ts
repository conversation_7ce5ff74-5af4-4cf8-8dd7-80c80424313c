import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { initialise } from '@mmt/legacy-commons/Common/Components/BookNowPayLater/BnplActions';
import {
  changeDepartureDate,
  closeConfirmOptionsBottomSheet,
  closeStationChangeAlert,
  railsListingBackPress,
  railsListingOmniture,
  railsListingSearchParamsChanged,
  refreshListingPage,
  setCrossSellModal,
  showHideEditWidget,
  showIsLoading,
  verifyTatkalExists,
  toggleTrainScheduleBottomSheet,
  toggleConnectedTravelBottomSheet,
  closeSeatLockBottomSheet,
  showSeatLockBottomSheet,
  displayAvailDepletionListingBottomSheet,
} from 'apps/rails/src/pages/NewListing/RailsListingActions';
import isEmpty from 'lodash/isEmpty';
import { default as RailsListing } from './RailsListingV3';
import { toggleScheduleTatakalBottomSheet } from '../NewListing/RailsListingActionsV3';

const mapStateToProps = (state) => {
  const {
    railsListing: {
      isLoading,
      showNoTrainView,
      trainsList,
      displayStationChangeAlert,
      availableQuotaList,
      originStation,
      destinationStation,
      departureDate,
      voucherObject,
      showEditWidget,
      isEditClicked,
      showCrossSellModal,
      settingListingDataForTravellers,
      displayConfirmOptionsBottomSheet,
      filteredTrainsList,
      displayTrainScheduleBottomSheet,
      trainNumberForSchedule,
      showBnpp,
      displayConnectedTravelBottomSheet,
      connectedBottomSheetData,
      connectedTravelApiResponseJson,
      listingErrorMessage,
      listingErrorSubtitle,
      listingErrorCode,
      displaySeatLockBottomSheet,
      displayAvailDepletionBottomSheet,
      displayScheduleTatkalBottomSheet,
      isCnfOptionsTrainSchedule,
      travelplexChatConfig,
      travelplexTickers,
    },
    railsLanding : {
      showCityPicker,
    },
  } = state;

  const tatkalExists = !isEmpty(availableQuotaList) && verifyTatkalExists(availableQuotaList);

  return {
    isLoading,
    showNoTrainView,
    displayStationChangeAlert,
    voucherObject,
    isEditClicked,
    departureDate,
    originStation,
    destinationStation,
    showCrossSellModal,
    settingListingDataForTravellers,
    displayConfirmOptionsBottomSheet,
    trainsList,
    tatkalExists,
    showEditWidget,
    filteredTrainsList,
    showCityPicker,
    displayTrainScheduleBottomSheet,
    trainNumberForSchedule,
    showBnpp,
    displayConnectedTravelBottomSheet,
    connectedBottomSheetData,
    connectedTravelApiResponseJson,
    listingErrorMessage,
    listingErrorSubtitle,
    listingErrorCode,
    displaySeatLockBottomSheet,
    displayAvailDepletionBottomSheet,
    displayScheduleTatkalBottomSheet,
    isCnfOptionsTrainSchedule,
    travelplexChatConfig,
    travelplexTickers,
  };
};

const actionCreators = {
  showHideEditWidget,
  changeDepartureDate,
  railsListingSearchParamsChanged,
  showIsLoading,
  refreshListingPage,
  setCrossSellModal,
  railsListingOmniture,
  closeStationChangeAlert,
  closeConfirmOptionsBottomSheet,
  closeSeatLockBottomSheet,
  showSeatLockBottomSheet,
  forceResetBnpl: initialise,
  railsListingBackPress,
  toggleTrainScheduleBottomSheet,
  toggleScheduleTatakalBottomSheet,
  toggleConnectedTravelBottomSheet,
  displayAvailDepletionListingBottomSheet,
};
const mapDispatchToProps = (dispatch) => bindActionCreators(actionCreators, dispatch);

export const RailsListingV3 = connect(mapStateToProps, mapDispatchToProps)(RailsListing);
