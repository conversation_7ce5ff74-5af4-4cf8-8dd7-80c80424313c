import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  blurCard1: {
    position: 'absolute',
    width: '100%',
    top: -240,
    height: 100,
    padding: 1,
  },

  blurCard2: {
    position: 'absolute',
    width: '100%',
    top: -160,
    height: 250,
    marginBottom: 107,
    padding: 1,
  },

  apply: {
    position: 'relative',
    top: -170,
    elevation: 8,
  },

  applyButton: {
    borderRadius: 24,
    height: 40,
    width: 220,
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyButtonText: {
    color: colors.white,
    fontSize: 12,
    alignSelf: 'center',
  },
  bottomArrowImage: {
    width: 12,
    height: 12,
    marginLeft: 10,
    marginBottom: -2,
    padding: 6,
    alignSelf: 'center',
  },
  fullCenter: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullFlex: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    width: '100%',
  },
  editWidgetContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    flex: 1,
    zIndex: 10,
    elevation: 10,
  },
  editButton: {
    flex: 1,
    backgroundColor: colors.lightBlack4,
  },
  editButtonContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  trainScheduleContainer: {
    height: '85%',
    backgroundColor: colors.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    overflow: 'hidden',
  },
  bottomSheetContainer: {
    zIndex: 9999,
  },
});
