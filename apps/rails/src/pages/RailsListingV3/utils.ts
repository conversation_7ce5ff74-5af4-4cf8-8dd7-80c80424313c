import { getQueryParamsFromUrl } from '@mmt/legacy-commons/Helpers/misc';
import {
  isIrctcScheduledMaintainance,
  isSelectedTravelDateInRange,
} from 'apps/rails/src/pages/RailsLandingPage/Store/RailsLandinStoreHelper';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import isEmpty from 'lodash/isEmpty';
import isNull from 'lodash/isNull';
import keyBy from 'lodash/keyBy';
import { today, diffDays } from '@mmt/legacy-commons/Helpers/dateHelpers';

export const getUpdatedRailSearchData = (
  deep_link_intent_url,
  trainNumber,
  trainsList,
  fromSeo,
) => {
  const queryParamsForTrainNumber =
    deep_link_intent_url && getQueryParamsFromUrl(deep_link_intent_url);
  const updatedTrainNumber = queryParamsForTrainNumber?.trainNumber || trainNumber;
  let updatedRailSearchData = trainsList;
  if (updatedTrainNumber && updatedRailSearchData && updatedRailSearchData.length > 0) {
    const selectedTrain = updatedRailSearchData.find((x) => x.trainNumber === updatedTrainNumber);
    if (selectedTrain) {
      const tempArr = updatedRailSearchData.filter((x) => x.trainNumber !== updatedTrainNumber);
      updatedRailSearchData = [selectedTrain, ...tempArr];
    }
    if (fromSeo) {
      updatedRailSearchData = updatedRailSearchData.slice(0, 2);
    }
  }
  return updatedRailSearchData;
};

export const validateDataFilled = (originStation, destinationStation, departureDate) => {
  if (isIrctcScheduledMaintainance()) {
    return { isValid: false, errorMessage: _label('irctc_scheduled_maintenance') };
  }
  if (isEmpty(originStation)) {
    return { isValid: false, errorMessage: _label('please_select_from') };
  }

  if (isEmpty(destinationStation)) {
    return { isValid: false, errorMessage: _label('please_select_from') };
  }

  if (originStation?.code?.toLowerCase() === destinationStation?.code?.toLowerCase()) {
    return { isValid: false, errorMessage: _label('origin_destination_same_error') };
  }

  if (isNull(departureDate)) {
    return { isValid: false, errorMessage: _label('select_travel_date') };
  }
  if (!isSelectedTravelDateInRange(departureDate)) {
    return { isValid: false, errorMessage: _label('travel_date_restriction') };
  }
  if (diffDays(departureDate, today()) < 0) {
    return { isValid: false, errorMessage: _label('travel_date_must_be_future') };
  }
  return { isValid: true, errorMessage: ' ' };
};

export const createFilteredListForRender = (trainsList = [], filteredTrainsList = []) => {
  const filteredTrainByTrainNumber = keyBy(filteredTrainsList, 'trainNumber');
  return trainsList.reduce((acc, train) => {
    if (filteredTrainByTrainNumber[train.trainNumber]) {
      acc.push({
        ...train,
        tbsAvailability: filteredTrainByTrainNumber[train.trainNumber].tbsAvailability,
      });
    }
    return acc;
  }, []);
};
