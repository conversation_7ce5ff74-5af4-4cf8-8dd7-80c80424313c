import React, { useEffect, useMemo, useState } from 'react';
import { Platform, TouchableWithoutFeedback, View } from 'react-native';
import { trackClickEvent } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { RailsSeatLockBottomSheet } from '@mmt/rails/src/pages/RailsListingV3/Components';
import { Actions } from 'apps/rails/src/navigation';
import HotelCrossSellVoucher from 'apps/rails/src/pages/HotelsCrossSell/HotelCrossSellVoucher';
import RailsListingEditWidget from 'apps/rails/src/pages/NewListing/Components/RailsListingEditWidget';
import RailsHeader from 'apps/rails/src/pages/NewListing/Containers/RailsHeaderContainer';
import StationChangeAlertContainer from 'apps/rails/src/pages/NewListing/Containers/StationChangeAlertContainer';
import UserNameModal from 'apps/rails/src/pages/NewUserFlow/UserNameModal';
import { getQueryParamsFromUrl } from '@mmt/legacy-commons/Helpers/misc';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import ListingErrorFallback from 'apps/rails/src/pages/NewListing/Components/ListingErrorFallback.json';

import ConnectedTravelBottomSheet from 'apps/rails/src/pages/NewListing/Components/ConnectedTravel/Components/ConnectedTravelBottomSheet';
import {
  AbConfigKeyMappings,
  getPokusConfig,
  getPokusConfigWaitingPromise,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import ErrorBoundary from 'apps/rails/src/pages/GenericErrorBoundary';
import {
  removeEventFromEvar47or97Variable,
  updateEvar47or97Variable,
} from 'apps/rails/src/railsAnalytics';
import { getRailsLanguageByKey } from 'apps/rails/src/vernacular/AppLanguage';
import { updateFechaWithLang } from 'apps/rails/src/vernacular/calendarUtil';
import {
  showNewTrainSchedule,
  getIfToShowConnectedTravelV3,
} from 'apps/rails/src/RailsAbConfig';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import ConfirmedOptionsBottonSheet from 'apps/rails/src/pages/NewListing/Components/ConfirmedOptions/ConfirmedOptionsBottomSheet';
import {
  createFilteredListForRender,
  getUpdatedRailSearchData,
  validateDataFilled,
} from 'apps/rails/src/pages/RailsListingV3/utils';
import { RailsCollapsibleList, RailsListingErrorView, RailsListingLoader } from './Components';
import styles from './styles';
import NewTrainScheduleScreen from 'apps/rails/src/pages/RIS/TrainSchedule/Components/NewTrainScheduleScreen';
import {
  removeFCKey,
  MAX_DAYS,
  SCREENER_LOADING,
  SCREENER_INTERACTIVE,
  LISTING_PAGE_TRACKING_KEY,
} from 'apps/rails/src/Utils/RailsConstant';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../configStore/Common/constants';
import { AvailDepletionBottomSheet } from '../NewListing/Components/AvailDepletion/AvailDepletionBottomSheet';
import ScheduleTatkalBottomSheet from '../NewListing/Components/ScheduleTatkalBottomSheet';
import { connectedTravelVariants, trainScheduleV2 , LISTING_TRAVELPLEX_POKUS } from '../../RailsAbConstants';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import RtbAffordanceBottomSheet from './Components/RtbAffordanceBottomsheet';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import BottomSheetModalTrain from '../Common/BottomSheetModalTrain';
import { getRailsListingTravelplex } from '../../RailsAbConfig';
import { TravelplexTrainsEntry } from '../TravelerDetails/Components/MyraBot/TrainsTravelplexEntry';

const RailsListingV3 = (props: unknown) => {
  const {
    isLoading,
    postPaymentBooking,
    isEditClicked,
    departureDate,
    originStation,
    destinationStation,
    settingListingDataForTravellers,
    crossSellEnabled,
    trainsList,
    filteredTrainsList,
    deep_link_intent_url,
    mmtlistingHighlights,
    errorFlow,
    refreshListingPage,
    showRtbListingBottomSheet,
    tbsRtbBottomSheet,
    rtbLandingActionsData,
    updateRtbListingBottomSheet,
    travelplexChatConfig,
    travelplexTickers,
  } = props;

  const [fromSeo, setFromSeo] = useState(false);
  const [showBnplWidget, setShowBnplWidget] = useState(false);
  const [showWidgetFromURL, setShowWidgetFromURL] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [showWidgetError, setShowWidgetError] = useState('');
  const [isListingRetry, setIsListingRetry] = useState(false);
  const [listingRetryErrorData, setListingRetryError] = useState(null);
  const [connectedTravelIndex, setConnectedTravelIndex] = useState(-1);
  const [arpMaxDays, setArpMaxDays] = useState(MAX_DAYS);
  const { updateState } = useScreenProfiler();
  const showListingTravelplexBot = getRailsListingTravelplex() === LISTING_TRAVELPLEX_POKUS.SHOWN;
  const shouldShowUniversalWidget = () => {
    if (showWidgetFromURL) {
      props.showHideEditWidget({ showEditWidget: true, isEditClicked: false });
      return;
    } else {
      const res =
        props.originStation &&
        props.destinationStation &&
        props.originStation.code &&
        props.destinationStation.code;
      if (!res && !props?.crossSellEnabled) {
        props.showHideEditWidget({ showEditWidget: true, isEditClicked: false });
      }
    }
  };

  useEffect(() => {
    props.forceResetBnpl(true);
    shouldShowUniversalWidget();
    if (Platform.OS === 'web') {
      const queryParams = getQueryParamsFromUrl(window.location.href);
      const trainNumber = queryParams?.trainNumber;
      const isFromSeo = queryParams?.isSeo === 'true' && trainNumber !== '';
      setFromSeo(isFromSeo);
    }

    async function fetchPokusConfig() {
      await getPokusConfigWaitingPromise(1000);
      if (deep_link_intent_url?.includes('railsListing')) {
        await removeFCKey();
        props.setFreeCancellation();
      }
      const canShowBnplWidget = getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.railsShowBnplWidget,
        false,
      );
      updateFechaWithLang(getRailsLanguageByKey('cookieCode'));
      setShowBnplWidget(canShowBnplWidget);
    }
    fetchPokusConfig();
  }, []);

  useEffect(() => {
    if (Platform.OS === 'android') {
      GenericModule?.showFloatingNotification?.(LISTING_PAGE_TRACKING_KEY);
    }
  }, []);

  useEffect(() => {
    const listingErrors = props?.errorFlows?.flows || ListingErrorFallback?.errorFlows?.flows;

    const errorDetails = listingErrors?.slice(2).find((item) => {
      const errorMessageArray = item?.errorMessage;
      const foundError = errorMessageArray?.find((error) =>
        props?.listingErrorMessage?.includes(error),
      );
      if (foundError) {
        return item;
      }
    });

    if (errorDetails?.errorMessage) {
      setIsListingRetry(true);
      setListingRetryError(errorDetails);
    }
  }, []);
  const retryTrainSearch = (postPaymentBooking) => () => {
    if (postPaymentBooking) {
      Actions.rails({ type: 'replace', postPaymentBooking });
    } else {
      Actions.pop();
    }
  };

  const filteredList = useMemo(
    () => createFilteredListForRender(trainsList, filteredTrainsList),
    [trainsList, filteredTrainsList],
  );
  const updatedTrainsList = useMemo(
    () => [
      ...filteredList.filter((train) => train.bookingAllowed),
      ...filteredList.filter((train) => !train.bookingAllowed),
    ],
    [filteredList],
  );
  let updatedRailSearchData = updatedTrainsList;

  updatedRailSearchData = useMemo(
    () =>
      deep_link_intent_url
        ? getUpdatedRailSearchData(
            deep_link_intent_url,
            props.trainNumber,
            updatedRailSearchData,
            fromSeo,
          )
        : updatedTrainsList,
    [deep_link_intent_url, props.trainNumber, trainsList, fromSeo, updatedTrainsList],
  );

  const fcStripIndex = useMemo(
    () => props?.fcStripIndex % updatedRailSearchData.length,
    [props.fcStripIndex, updatedRailSearchData.length],
  );

  const showConnectedTravel = getIfToShowConnectedTravelV3();

  useEffect(() => {
    const getDynamicPosition = (dynamicPositioning, trainCount) => {
      if (!dynamicPositioning) {
        return null;
      }
      const { small, medium, large } = dynamicPositioning;
      if (trainCount <= small?.maxTrains) {
        return small?.position;
      }
      if (trainCount <= medium?.maxTrains) {
        return medium?.position;
      }
      return large?.position;
    };

    const getConnectedTravelConfig = async () => {
      let scrollIndex = -1;
      const trainCount = updatedRailSearchData?.length || 0;
      if (showConnectedTravel === connectedTravelVariants?.INTRODUCING_CONNECTED_TRAVEL) {
        const response = await getConfigStore(configKeys.RAILS_CT_V2);
        const dynamicPosition = getDynamicPosition(response?.dynamicPositioning, trainCount);
        scrollIndex = dynamicPosition !== null ? dynamicPosition : response?.scrollIndex ?? -1;
      } else {
        const response = await getConfigStore(configKeys.RAILS_LISTING_CONNECTED_TRAVELLERS_INDEX);
        const dynamicPosition = getDynamicPosition(response?.dynamicPositioning, trainCount);
        scrollIndex =
          dynamicPosition !== null ? dynamicPosition : response?.connectedTravelIndex ?? -1;
      }
      scrollIndex = scrollIndex >= trainCount ? trainCount - 1 : scrollIndex;
      setConnectedTravelIndex(scrollIndex);
    };

    if (showConnectedTravel) {
      getConnectedTravelConfig();
    }
  }, [showConnectedTravel, updatedRailSearchData?.length]);

  useEffect(() => {
    async function getArpWindow() {
      const ARP_WINDOW = await getConfigStore(configKeys.RAILS_ARP_WINDOW_FOR_CALENDAR, false);
      const days = ARP_WINDOW?.irctc_arp_window;
      setArpMaxDays(days);
    }
    getArpWindow();
  }, []);

  useEffect(() => {
    const newTrainSchedule = showNewTrainSchedule() === trainScheduleV2.SHOWN;
    const hideNewTrainSchedule = showNewTrainSchedule() === trainScheduleV2.NOT_SHOWN;
    const { RAILS_LISTING_TRAIN_SCHEDULE_V1 } = RAIL_EVENTS.LISTING;
    const { RAILS_LISTING_TRAIN_SCHEDULE_V2 } = RAIL_EVENTS.LISTING;

    removeEventFromEvar47or97Variable(RAILS_LISTING_TRAIN_SCHEDULE_V1);
    removeEventFromEvar47or97Variable(RAILS_LISTING_TRAIN_SCHEDULE_V2);

    if (hideNewTrainSchedule || newTrainSchedule) {
      const event = hideNewTrainSchedule
        ? RAILS_LISTING_TRAIN_SCHEDULE_V1
        : RAILS_LISTING_TRAIN_SCHEDULE_V2;
      updateEvar47or97Variable(event);
    }
  }, []);

  const _changeDepartureDate = (date) => {
    setFromSeo(false);
    props.changeDepartureDate(date, crossSellEnabled);
    props.railsListingSearchParamsChanged(!crossSellEnabled);
  };

  const onHardBackPress = () => {
    props?.railsListingBackPress();
    return true;
  };

  const performNewSearch = () => {
    const { errorMessage, isValid } = validateDataFilled(
      originStation,
      destinationStation,
      departureDate,
    );
    setShowWidgetError(errorMessage);
    if (isValid) {
      props.showIsLoading(true);
      props.railsListingSearchParamsChanged(!props?.crossSellEnabled);
      if (!props?.crossSellEnabled) {
        props.refreshListingPage();
      }
      props.showHideEditWidget({ showEditWidget: false, isEditClicked: false });
      setShowWidgetFromURL(false);
      trackClickEvent('mob_rail_listing_v2', 'modify_search_clicked');
    } else {
      setTimeout(() => {
        setShowWidgetError('');
      }, 2500);
    }
  };

  const closeModal = () => {
    props.setCrossSellModal(false);
  };

  const _setModalVisibility = (visibility) => {
    setModalVisible(visibility);
  };

  if (isLoading) {
    updateState(SCREENER_LOADING);
  } else {
    updateState(SCREENER_INTERACTIVE);
  }

  return (
    <ErrorBoundary page="listing">
        {showListingTravelplexBot && (
          <TravelplexTrainsEntry
            pageType="listing"
            chatConfig={travelplexChatConfig}
            tickers={travelplexTickers}
          />
        )}
        {!crossSellEnabled && (
          <RailsHeader
            onBack={onHardBackPress}
            postPaymentBooking={postPaymentBooking}
            page="newListing"
            showEditIcon={!isLoading && !settingListingDataForTravellers}
            noHeader={crossSellEnabled}
            disableEditIcon={isLoading}
            showRailsLanguageChangeIcon
          />
        )}
        {/* we need to calculate if retry is required here and then send props here itself */}
        <RailsListingErrorView
          showCtV2={showConnectedTravel}
          railsListingOmniture={props.railsListingOmniture}
          postPaymentBooking={postPaymentBooking}
          crossSellEnabled={crossSellEnabled}
          retryTrainSearch={retryTrainSearch}
          originStation={originStation}
          destinationStation={destinationStation}
          departureDate={departureDate}
          trainsListLength={updatedRailSearchData?.length}
          tatkalExists={props.tatkalExists}
          showBnplWidget={showBnplWidget}
          _changeDepartureDate={_changeDepartureDate}
          isErrorRetry={isListingRetry}
          errorRetryData={listingRetryErrorData}
          onBack={onHardBackPress}
          errorFlow={errorFlow}
          refreshListingPage={refreshListingPage}
        />
        {/* We need to enable our date strip as well.. Please take care of this as well. */}
        {!isLoading && !props.showNoTrainView && updatedRailSearchData?.length > 0 && (
            <RailsCollapsibleList
              data={updatedRailSearchData}
              otherDayTrainsList={props.otherDayTrainsList}
              tatkalExists={props.tatkalExists}
              originStation={originStation}
              destinationStation={destinationStation}
              departureDate={departureDate}
              fromSeo={fromSeo}
              showBnplWidget={showBnplWidget}
              _changeDepartureDate={_changeDepartureDate}
              fcStripIndex={fcStripIndex}
              connectedTravelIndex={connectedTravelIndex}
              showConnectedTravelV2={showConnectedTravel}
              fcStripText={props.fcStripText}
              mmtlistingHighlights={mmtlistingHighlights}
              showBnpp={props.showBnpp}
              maxArpDays={arpMaxDays}
              listingEducationCard={props.listingEducationCard}
            />
          )}
          {showRtbListingBottomSheet &&
            rtbLandingActionsData?.actions?.bottom_sheet?.[tbsRtbBottomSheet?.templateType]?.[
              tbsRtbBottomSheet?.key?.key
            ] && (
              <RtbAffordanceBottomSheet
                tbsRtbBottomSheet={tbsRtbBottomSheet}
                rtbLandingActionsData={rtbLandingActionsData}
                updateRtbListingBottomSheet={updateRtbListingBottomSheet}
              />
            )}
          {props.showCrossSellModal && (
            <BottomSheetModalTrain onTouchOutside={closeModal} testID="cross_sell_bottomsheet_modal">
              <HotelCrossSellVoucher voucherObject={props.voucherObject} />
            </BottomSheetModalTrain>
          )}
          {modalVisible && (
            <UserNameModal
              modalVisible={modalVisible}
              setModalVisibility={_setModalVisibility}
              from="railsListing"
              probableTo="railsListing"
              nextPageProps={{
                originStation: props.originStation,
                destinationStation: props.destinationStation,
                departureDate: props.departureDate,
              }}
            />
          )}
          {props.displayStationChangeAlert && (
            <BottomSheetModalTrain
              onTouchOutside={props.closeStationChangeAlert}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="station_change_alert_bottomsheet_modal"
        >
              <StationChangeAlertContainer />
              <View style={{ height: 20, backgroundColor: colors.white }} />
            </BottomSheetModalTrain>
          )}
          {(isEditClicked ||
            (props.showEditWidget &&
              !(props.fromPage === 'landing' || props.fromPage === 'traveler'))) && (
            <View style={styles.editWidgetContainer}>
              <RailsListingEditWidget
                SearchClicked={performNewSearch}
                onBack={onHardBackPress}
                isEdit={props.isEditClicked}
                error={showWidgetError}
              />
              {props.showCityPicker || (
                <View style={styles.editButton}>
                  <View style={styles.editButtonContainer}>
                    <TouchableWithoutFeedback
                      style={styles.fullFlex}
                      onPress={() => {
                        props.showHideEditWidget({ showEditWidget: false, isEditClicked: false });
                      }}
                    >
                      <View style={styles.fullFlex} />
                    </TouchableWithoutFeedback>
                  </View>
                </View>
              )}
            </View>
          )}
          {props.displayConfirmOptionsBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={props.closeConfirmOptionsBottomSheet}
              additionalContainerStyle={styles.bottomSheetContainer}
              testID="confirm_options_bottomsheet_modal"
        >
              <ConfirmedOptionsBottonSheet />
            </BottomSheetModalTrain>
          )}
          {props.displaySeatLockBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={props.closeSeatLockBottomSheet}
              additionalContainerStyle={styles.bottomSheetContainer}
              testID="seat_lock_bottomsheet_modal"
        >
              <RailsSeatLockBottomSheet closeBottomSheet={props.closeSeatLockBottomSheet} />
            </BottomSheetModalTrain>
          )}
          {props.displayTrainScheduleBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={() => {
                props.toggleTrainScheduleBottomSheet(false);
              }}
              additionalContainerStyle={styles.bottomSheetContainer}
              testID="train_schedule_bottomsheet_modal"
        >
              <View style={styles.trainScheduleContainer}>
                <NewTrainScheduleScreen trainNumToSearch={props.trainNumberForSchedule} />
              </View>
            </BottomSheetModalTrain>
          )}
          {props?.displayConnectedTravelBottomSheet && props?.connectedBottomSheetData && (
            <BottomSheetModalTrain
              onTouchOutside={() => {
                props.toggleConnectedTravelBottomSheet(false);
              }}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="connected_travel_bottomsheet_modal"
        >
              <ConnectedTravelBottomSheet connectedBottomSheetData={props?.connectedBottomSheetData} />
            </BottomSheetModalTrain>
          )}
          {props?.displayAvailDepletionBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={() => {
                props.displayAvailDepletionListingBottomSheet(false);
              }}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="avail_depletion_bottomsheet_modal"
        >
              <AvailDepletionBottomSheet
                changeDisplayAvailDepletionBottomSheet={props.displayAvailDepletionListingBottomSheet}
              />
            </BottomSheetModalTrain>
          )}
          {props?.displayScheduleTatkalBottomSheet && (
            <BottomSheetModalTrain
              onTouchOutside={() => {
                props.toggleScheduleTatakalBottomSheet(false);
              }}
              additionalContainerStyle={{ zIndex: 9999 }}
              testID="schedule_tatkal_alert_bottomsheet_modal"
        >
              <ScheduleTatkalBottomSheet />
            </BottomSheetModalTrain>
          )}
          <RailsListingLoader
            isLoading={isLoading || settingListingDataForTravellers}
            itemClicked={settingListingDataForTravellers}
          />
    </ErrorBoundary>
  );
};

export default RailsListingV3;
