export interface ErrorFlowCta {
  action: string;
  text: string;
  code: string;
}

export interface ErrorFlow {
  title: string;
  subTitle: string;
  iconUrl: string;
  ctas: Array<ErrorFlowCta>;
}

export interface TbsWithAvailabilityAndRecommendation {
  otherDayTrainsList?: OtherDayTrainsList[];
  alternateAvailabilityList?: unknown;
  otherDayTbsCounts?: OtherDayTbsCount[];
  minPriceDetails?: MinPriceDetails;
  minDuration?: number;
  quotaList?: string[];
  clientConfig?: ClientConfig;
  lang?: unknown;
  showAlternateAvailability?: boolean;
  noTrainFound?: boolean;
  recommendedDate?: RecommendedDate;
  mySafetyCardImgLink?: string;
  mySafetyCardDetailsLink?: string;
  safetyGuideLines?: SafetyGuideLines;
  frmStationCode?: string;
  frmStationName?: string;
  frmStationCity?: string;
  frmStationState?: string;
  toStationState?: string;
  toStationCity?: string;
  toStationName?: string;
  toStationCode?: string;
  gioffers?: unknown;
  trainBtwnStnsList?: TrainBtwnStnsList[];
  errormessage?: string;
  errorDetails?: ErrorDetails;
  errorFlow?: ErrorFlow;
}
export interface ErrorDetails {
  errorCode?: string;
  errorMessage?: string;
}

export interface OtherDayTrainsList {
  arrivalTime: string;
  avlClasses: string[];
  departureTime: string;
  distance: number;
  duration: number;
  frmStnCode: string;
  frmStnName: string;
  runningFri: string;
  runningMon: string;
  runningSat: string;
  runningSun: string;
  runningThu: string;
  runningTue: string;
  runningWed: string;
  toStnCode: string;
  toStnName: string;
  trainName: string;
  trainNumber: string;
  trainType: string[];
  tbsAvailability: unknown;
  bookingAllowed: boolean;
  tatkalApplicable: boolean;
  sourceDistance: number;
  destinationDistance: number;
  clusterTrain: boolean;
  trainOwner: string;
  trainQuotaCombinedScore: number;
  trainNamePriority: number;
  trainAvailScore: number;
  possibleDates: string[];
  arrivalTimeEpochInSec: number;
  departureTimeEpochInSec: number;
  frmStnCity: string;
  frmStnState: string;
  toStnCity: string;
  toStnState: string;
}

export interface OtherDayTbsCount {
  date: string;
  trainCount: number;
}

export interface MinPriceDetails {
  amount: number;
  currency: string;
}

export interface ClientConfig {
  hotelCrossSellBannerText: string;
  cdnTncUrl: string;
  hotelsCrossSellDeepLink: string;
}

export interface SafetyGuideLines {
  heading: string;
  subHeading: string;
  guideLinesLinkText: string;
  guideLinesLinkUrl: string;
}

export interface TrainBtwnStnsList {
  arrivalTime: string;
  avlClasses: string[];
  departureTime: string;
  distance: number;
  duration: number;
  frmStnCode: string;
  frmStnName: string;
  runningFri: string;
  runningMon: string;
  runningSat: string;
  runningSun: string;
  runningThu: string;
  runningTue: string;
  runningWed: string;
  toStnCode: string;
  toStnName: string;
  trainName: string;
  trainNumber: string;
  trainType: string[];
  tbsAvailability: TbsAvailability[];
  bookingAllowed: boolean;
  tatkalApplicable: boolean;
  sourceDistance: number;
  destinationDistance: number;
  clusterTrain: boolean;
  trainOwner: string;
  trainQuotaCombinedScore: number;
  trainNamePriority: number;
  trainAvailScore: number;
  possibleDates: unknown;
  arrivalTimeEpochInSec: number;
  departureTimeEpochInSec: number;
  frmStnCity: string;
  frmStnState: string;
  toStnCity: string;
  toStnState: string;
}

export interface TbsAvailability {
  lastUpdatedOn?: string;
  lastUpdatedOnRaw: number;
  availablityDate?: string;
  availablityStatus?: string;
  prettyPrintingAvailablityStatus?: string;
  availablityType?: string;
  reason?: string;
  reasonType?: string;
  wlType?: string;
  currentBkgFlag?: string;
  classType?: string;
  quota: string;
  totalFare: number;
  className: string;
  availablityScore: number;
  classWt: number;
  availAndClassScoreCombined: number;
  cumulativeQuotaScore: number;
  quotaScore: number;
  wzqScore: number;
  predictionPercentage?: string;
  tgPremiumPercentage?: number;
  freeCancellationText?: string;
  confirmationGuaranteeText?: string;
}

export interface RecommendedDate {
  recommendedMessage?: string;
  recommendedList?: RecommendedList[];
}

export interface RecommendedList {
  order?: number;
  date?: string;
  trainCount?: number;
  displayMessage?: string;
}

//Common Redux props
//TODO: add types
export interface ReduxProps {
  texts: unknown;
  showAlternateAvailability: unknown;
  railsShowConfirmationChance: unknown;
  confirmationChances: unknown;
  originStation: unknown;
  destinationStation: unknown;
  departureDate: unknown;
  labels: unknown;
  covidSafetyCardData: unknown;
  bannerIndex: unknown;
  bannerAlternateIndex: unknown;
  bnplWidgetIndex: unknown;
  tatkalExists: unknown;
  trainsList: unknown;
  confirmationGuaranteeConfig: unknown;
  onViewTrainDetailsClicked: unknown;
  onEmptyAvailabilityCardClicked: unknown;
  onAvailabilityCardClicked: unknown;
  actionSaveRailofyZcShown: unknown;
  fcStripIndex: number;
  fcStripText: unknown;
  connectedTravelIndex: unknown;
  listingAdsConfig: unknown;
  connectedTravelApiResponseJson: unknown;
  nearbyDatesInfo: {
    selectedClassType: string;
    displayNearbyDates: boolean;
    selectedTrainNumber: string;
    selectedCardHash: string;
  };
  toggleNearbyDatesList: unknown;
  nearbyDatesCardClick: unknown;
}
