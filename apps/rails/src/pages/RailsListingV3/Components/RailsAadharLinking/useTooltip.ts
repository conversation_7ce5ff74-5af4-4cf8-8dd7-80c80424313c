import { useState, useRef } from 'react';
import { TouchableOpacity, View, Dimensions } from 'react-native';

interface LayoutInfo {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface TooltipPosition {
  tooltipLeft: number;
  tooltipWidth: number;
  tooltipTop: number;
  arrowRight: number;
}

export const useTooltip = () => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [buttonLayout, setButtonLayout] = useState<LayoutInfo | null>(null);
  const [bannerLayout, setBannerLayout] = useState<LayoutInfo | null>(null);
  const buttonRef = useRef<TouchableOpacity>(null);
  const bannerRef = useRef<View>(null);

  const getTooltipPosition = (): Partial<TooltipPosition> => {
    if (!buttonLayout || !bannerLayout) {
      return {};
    }

    const screenWidth = Dimensions.get('window').width;
    const tooltipWidth = 275;
    const padding = 16;

    const rightAdjustment = Math.min(15, screenWidth * 0.04); // 4% of screen width, max 15px
    const topAdjustment = Math.min(10, screenWidth * 0.025); // 2.5% of screen width, max 10px

    const buttonRight = buttonLayout.x + buttonLayout.width;
    const idealTooltipLeft = buttonRight - tooltipWidth + rightAdjustment;

    const minLeft = padding;
    const maxLeft = screenWidth - tooltipWidth - padding;
    let tooltipLeft = Math.max(minLeft, Math.min(idealTooltipLeft, maxLeft));

    const tooltipTop = bannerLayout.y - 60 - topAdjustment;

    const buttonCenter = buttonLayout.x + (buttonLayout.width / 2);
    const arrowCenterFromLeft = buttonCenter - tooltipLeft;

    const arrowRight = Math.max(10, Math.min(tooltipWidth - 26, tooltipWidth - arrowCenterFromLeft - 8));

    const finalTooltipWidth = screenWidth < tooltipWidth + 32 ? screenWidth - 32 : tooltipWidth;

    if (screenWidth < tooltipWidth + 32) {
      tooltipLeft = padding;
      const adjustedArrowRight = Math.max(10, Math.min(finalTooltipWidth - 26, finalTooltipWidth * 0.8));
      return {
        tooltipLeft,
        tooltipWidth: finalTooltipWidth,
        tooltipTop: bannerLayout.y - 50,
        arrowRight: adjustedArrowRight,
      };
    }

    return {
      tooltipLeft,
      tooltipWidth: finalTooltipWidth,
      tooltipTop,
      arrowRight,
    };
  };

  const showTooltipWithMeasurement = () => {
    if (buttonRef.current && bannerRef.current) {
      // Measure both button and banner positions
      buttonRef.current.measure((x, y, width, height, pageX, pageY) => {
        setButtonLayout({ x: pageX, y: pageY, width, height });
      });

      bannerRef.current.measure((x, y, width, height, pageX, pageY) => {
        setBannerLayout({ x: pageX, y: pageY, width, height });
        setShowTooltip(true);
      });
    }
  };

  const hideTooltip = () => {
    setShowTooltip(false);
  };

  return {
    // State
    showTooltip,
    buttonLayout,
    bannerLayout,

    // Refs
    buttonRef,
    bannerRef,

    // Functions
    getTooltipPosition,
    showTooltipWithMeasurement,
    hideTooltip,
  };
};
