/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { Modal, TouchableWithoutFeedback, View, Text, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface TooltipModalProps {
  visible: boolean;
  tooltipText: string;
  tooltipLeft: number;
  tooltipWidth: number;
  tooltipTop: number;
  arrowRight: number;
  onClose: () => void;
}

const TooltipModal: React.FC<TooltipModalProps> = ({
  visible,
  tooltipText,
  tooltipLeft,
  tooltipWidth,
  tooltipTop,
  arrowRight,
  onClose,
}) => {
  if (!visible) {
    return null;
  }

  return (
    <Modal transparent visible={visible} animationType="fade">
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <View style={[
            styles.tooltipContainer,
            {
              position: 'absolute',
              top: tooltipTop,
              left: tooltipLeft + 10,
              width: tooltipWidth,
            },
          ]}>
            <Text style={styles.tooltipText}>
              {tooltipText}
            </Text>
            <View style={[
              styles.tooltipArrow,
              {
                right: arrowRight,
              },
            ]} />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
  },
  tooltipContainer: {
    backgroundColor: colors.lightBlack,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    position: 'relative',
  },
  tooltipText: {
    fontSize: 14,
    lineHeight: 18,
    color: colors.white,
    fontFamily: 'Lato',
    fontWeight: '400',
    textAlign: 'left',
    paddingLeft: 8,
  },
  tooltipArrow: {
    position: 'absolute',
    bottom: -10,
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 12,
    borderRightWidth: 12,
    borderTopWidth: 11,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderTopColor: colors.lightBlack,
  },
});

export default TooltipModal;