import React from 'react';
import { StyleSheet, View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface Props {
  isLoading: boolean;
  itemClicked: boolean;
}

const RailsListingLoader = ({ isLoading }: Props) => {
  if (!isLoading) {
    return null;
  }
  return (
    <View style={styles.container}>
      <View style={styles.loader}>
        <Spinner size={30} color={colors.azure} />
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  loaderText: {
    color: colors.defaultTextColor,
    fontSize: 12,
    alignSelf: 'center',
    marginTop: 16,
  },
  loader: {
    marginTop: '20%',
  },
});

export default RailsListingLoader;
