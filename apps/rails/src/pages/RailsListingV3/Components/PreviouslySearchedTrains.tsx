import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Animated, Image, Text, TouchableWithoutFeedback, View, Platform } from 'react-native';
import { connect } from 'react-redux';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import TrainItem from '@mmt/rails/src/pages/NewListing/Components/NewTrainCard';
import {
  updateCardClicked,
  availabilityCardClicked,
  onViewDetailsClicked,
  saveRailofyZcShown,
  toggleNearbyDatesList,
  nearbyDatesCardClick,
} from '@mmt/rails/src/pages/NewListing/RailsListingActions';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import { LISTING_PAGE_TRACKING_KEY, ODT_EVENTS } from '@mmt/rails/src/Utils/RailsConstant';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import { removeEventFromEvar99Variable, trackNewListingClickEvent,trackGenericEvar99Event } from '@mmt/rails/src/railsAnalytics';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import styles from './styles';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface SearchedTrainsProps {
  interactedTrains: unknown[];
  prevSearchedHeading?: string;
  isOtherDayTrainsList?: boolean;
  tatkalExists: boolean;
  railsShowConfirmationChance: boolean;
  confirmationChances: unknown;
  originStation: unknown;
  destinationStation: unknown;
  departureDate: unknown;
  labels: unknown;
  nearbyDatesInfo: unknown;
  listingAdsConfig: unknown;
  dynamicPriceConfig: unknown;

  // Action creators
  onEmptyAvailabilityCardClicked: (className: unknown, trainData: unknown, quota: unknown) => void;
  onAvailabilityCardClicked: (className: unknown, trainData: unknown, quota: unknown) => void;
  onViewTrainDetailsClicked: (trainData: unknown) => void;
  actionSaveRailofyZcShown: (data: unknown) => void;
  toggleNearbyDatesList: (show: unknown, trainNumber: unknown, classType: unknown,
    quota: unknown, sectionIdentifier: unknown) => void;
  nearbyDatesCardClick: (trainData: unknown, className: unknown, quota: unknown) => void;
}

const PreviouslySearchedTrains = (props: SearchedTrainsProps) => {
  const {
    interactedTrains,
    listingAdsConfig,
    dynamicPriceConfig,
    onEmptyAvailabilityCardClicked,
    onAvailabilityCardClicked,
    isOtherDayTrainsList,
    nearbyDatesCardClick,
  } = props;

  const [isLoading, setIsLoading] = useState(true);

  const _onAvailabilityCardClicked = useCallback(
    (
      callback: unknown,
      className: unknown,
      trainData: unknown,
      quota: unknown,
      cardPosition = 0,
    ) => {
      removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_CARD_CLICK);

      const position = cardPosition + 1;
      trackGenericEvar99Event(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_CARD_CLICK,
      );
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        `mob_rail_listing_prevsearched_card_clicked_pos${position}`,
      );
      const trainDataWithSection = {
        ...trainData,
        sectionIdentifier: 'searched',
        selectedCardTrainNumber: trainData.trainNumber,
      };
      callback(className, trainDataWithSection, quota);
    },
    [],
  );

  const handleEmptyAvailabilityCardClick = useCallback(
    (className: unknown, trainData: unknown, quota: unknown) => {
      _onAvailabilityCardClicked(onEmptyAvailabilityCardClicked, className, trainData, quota, 0);
      if (isOtherDayTrainsList) {
        trackNewListingClickEvent(ODT_EVENTS.odt_card_clicked);
      }
    },
    [_onAvailabilityCardClicked, onEmptyAvailabilityCardClicked, isOtherDayTrainsList],
  );

  const handleAvailabilityCardClick = useCallback(
    (className: unknown, trainData: unknown, quota: unknown) => {
      _onAvailabilityCardClicked(onAvailabilityCardClicked, className, trainData, quota, 0);
    },
    [_onAvailabilityCardClicked, onAvailabilityCardClicked],
  );

  const handleNearbyDatesCardClick = useCallback(
    (trainData: unknown, className: unknown, quota: unknown) => {
      nearbyDatesCardClick({ ...trainData, sectionIdentifier: 'searched' }, className, quota);
    },
    [nearbyDatesCardClick],
  );

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  const [isInteractedExpanded, setIsInteractedExpanded] = useState(false);
  const animation = useRef(new Animated.Value(0)).current;
  const arrowAnimation = useRef(new Animated.Value(0)).current;

  // Filter out trains that have departed
  const filteredInteractedTrains = interactedTrains?.filter((train) => {
    if (!train.tbsAvailability || !Array.isArray(train.tbsAvailability)) {
      return true;
    }
    const hasTrainDeparted = train.tbsAvailability.some((availability: unknown) => {
    return (
      availability &&
      typeof availability === 'object' &&
      (availability.availablityStatus === _label('departed_train') ||
       availability.availablityStatus === _label('train_outside_booking_window') ||
       availability.availablityStatus === _label('cancelled_train'))
    );
    });
    return !hasTrainDeparted;
  });

  useEffect(() => {
    Animated.timing(animation, {
      toValue: isInteractedExpanded ? 0 : 1,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isInteractedExpanded, animation]);

  const toggleExpand = useCallback(() => {
    if (!isInteractedExpanded) {
      const { selectedNearbyCardsHashList } = props.nearbyDatesInfo || { selectedNearbyCardsHashList: [] };
      const searchedSectionCards = selectedNearbyCardsHashList.filter((hash: string) => hash.endsWith('*searched'));
      searchedSectionCards.forEach((cardHash: string) => {
        const [trainNumber, classType, quota] = cardHash.split('*');
        props.toggleNearbyDatesList(false, trainNumber, classType, quota, 'searched');
      });
    }

    setIsInteractedExpanded((prev) => !prev);

    if (isInteractedExpanded) {
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_EXPAND,
      );
    } else {
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_COLLAPSED,
      );
    }

    Animated.timing(arrowAnimation, {
      delay: 3500,
      toValue: isInteractedExpanded ? 0 : -150,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [isInteractedExpanded, arrowAnimation, props.nearbyDatesInfo, props.toggleNearbyDatesList]);

  if (!filteredInteractedTrains?.length) {
    return null;
  }

  const firstTrainData = {
    ...filteredInteractedTrains[0],
    uniqueCardId: `searched_${filteredInteractedTrains[0].trainNumber}_0`,
  };

  const trainListingId = `train_listing_${
    props?.isOtherDayTrainsList ? '_odt' : 'interactedTrains'
  }_trainCard_0`;

  return (
    <View style={styles.container1}>
      {filteredInteractedTrains?.length > 0 && (
        <View style={styles.headingContainer}>
          <Image source={ASSETS.historyIcon} style={styles.arrowContainer2} />
          <Text style={styles.headingText}>
            {props?.prevSearchedHeading || _label('prev_search_text')}
          </Text>
        </View>
      )}
      {filteredInteractedTrains?.[0] && (
        <View
          style={[
            styles.cardContainer,
            {
              borderColor: colors.lightGrey2,
              borderBottomLeftRadius: 8,
              borderBottomRightRadius: 8,
            },
          ]}
        >
          <TrainItem
            id={trainListingId}
            onEmptyAvailabilityCardClicked={handleEmptyAvailabilityCardClick}
            isLoading={isLoading}
            onAvailabilityCardClicked={handleAvailabilityCardClick}
            isOtherDayTrainsList={props?.isOtherDayTrainsList}
            tatkalExists={props?.tatkalExists}
            onViewDetailsClicked={props?.onViewTrainDetailsClicked}
            confirmationChance={
              props?.railsShowConfirmationChance
                ? props?.confirmationChances?.[firstTrainData.trainNumber]
                : null
            }
            railsShowConfirmationChance={props?.railsShowConfirmationChance}
            originStation={props?.originStation}
            destinationStation={props?.destinationStation}
            departureDate={props?.departureDate}
            labels={props?.labels}
            actionSaveRailofyZcShown={props?.actionSaveRailofyZcShown}
            trainPosition={1}
            listingAdsConfig={listingAdsConfig}
            nearbyDatesInfo={props?.nearbyDatesInfo}
            toggleNearbyDatesList={(show, trainNumber, classType, quota) => {
              props.toggleNearbyDatesList(show, trainNumber, classType, quota, 'searched');
            }}
            nearbyDatesCardClick={handleNearbyDatesCardClick}
            trainData={firstTrainData}
            dynamicPriceConfig={dynamicPriceConfig}
            disableNearbydates={true}
          />

          {!isInteractedExpanded && filteredInteractedTrains?.length > 1 && (
            <>
              {[...Array(Math.min((filteredInteractedTrains?.length || 1) - 1, 5))].map(
                (_, idx) => (
                  <View
                    key={`stack_${idx}`}
                    style={[
                      styles.stackPreview,
                      Platform.select({
                        ios: styles.stackPreviewIOS,
                        android: styles.stackPreviewAndroid,
                      }),
                      {
                        bottom: -(7 + idx * 5),
                        left: `${1 + idx * 0.75}%`,
                        right: `${1 + idx * 0.75}%`,
                      },
                    ]}
                  />
                ),
              )}
            </>
          )}
        </View>
      )}
      {isInteractedExpanded &&
        filteredInteractedTrains?.slice(1).map((train, idx) => (
          <Animated.View
            key={`${train?.trainNumber || idx}_interacted_${idx}`}
            style={[
              styles.cardContainer,
              {
                marginTop: isInteractedExpanded && idx > 0 ? 8 : 0,
                transform: [
                  {
                    translateY: animation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, idx * (Platform.OS === 'ios' ? 242 : 244)],
                    }),
                  },
                ],
              },
            ]}
            >
              <TrainItem
                id={`train_listing_${
                  props?.isOtherDayTrainsList ? '_odt' : 'interactedTrains'
                }_trainCard_${idx}`}
                onEmptyAvailabilityCardClicked={(className: unknown, trainData: unknown, quota: unknown) => {
                  _onAvailabilityCardClicked(
                    props.onEmptyAvailabilityCardClicked,
                    className,
                    trainData,
                    quota,
                    idx,
                  );
                  if (props?.isOtherDayTrainsList) {
                    trackNewListingClickEvent(ODT_EVENTS.odt_card_clicked);
                  }
                }}
                isLoading={isLoading}
                onAvailabilityCardClicked={(className: unknown, trainData: unknown, quota: unknown) => {
                  _onAvailabilityCardClicked(
                  props.onAvailabilityCardClicked,
                  className,
                    trainData,
                    quota,
                    idx,
                  );
              }}
              isOtherDayTrainsList={props?.isOtherDayTrainsList}
                tatkalExists={props?.tatkalExists}
                onViewDetailsClicked={props?.onViewTrainDetailsClicked}
                confirmationChance={
                  props?.railsShowConfirmationChance
                    ? props?.confirmationChances?.[train?.trainNumber]
                  : null
                }
                railsShowConfirmationChance={props?.railsShowConfirmationChance}
                originStation={props?.originStation}
                destinationStation={props?.destinationStation}
                departureDate={props?.departureDate}
                labels={props?.labels}
                actionSaveRailofyZcShown={props?.actionSaveRailofyZcShown}
                trainPosition={idx + 1}
                listingAdsConfig={listingAdsConfig}
                nearbyDatesInfo={props?.nearbyDatesInfo}
                toggleNearbyDatesList={(show: unknown, trainNumber: unknown, classType: unknown, quota: unknown) => {
                  props.toggleNearbyDatesList(show, trainNumber, classType, quota, 'searched');
                }}
                nearbyDatesCardClick={(trainData: unknown, className: unknown, quota: unknown) => {
                  props.nearbyDatesCardClick({...trainData, sectionIdentifier: 'searched'}, className, quota);
                }}
                trainData={{...train, uniqueCardId: `searched_${train.trainNumber}_${idx}`}}
                dynamicPriceConfig={dynamicPriceConfig}
                disableNearbydates={true}
              />
            </Animated.View>
        ))}
      {filteredInteractedTrains?.length > 1 && (
        <TouchableWithoutFeedback onPress={toggleExpand}>
          <View
            style={[
              styles.expandContainer,
              {
                marginTop: isInteractedExpanded ? -8 : 8,
                zIndex: 99,
              },
            ]}
          >
            <Image
              source={isInteractedExpanded ? ASSETS.upArrow : ASSETS.downArrow}
              style={styles.arrowContainer}
            />
          </View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
};

const mapDispatchToProps = {
  onEmptyAvailabilityCardClicked: updateCardClicked,
  onAvailabilityCardClicked: availabilityCardClicked,
  onViewTrainDetailsClicked: onViewDetailsClicked,
  actionSaveRailofyZcShown: saveRailofyZcShown,
  toggleNearbyDatesList,
  nearbyDatesCardClick,
};

export default connect(null, mapDispatchToProps)(React.memo(PreviouslySearchedTrains));
