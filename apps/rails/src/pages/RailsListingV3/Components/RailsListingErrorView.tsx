import { StyleSheet, View } from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { LISTING_DOWNTIME_ERROR_CODE, RETRY_ACTION } from 'apps/rails/src/Utils/RailsConstant';
import TooManyFiltersError from 'apps/rails/src/pages/ErrorPages/TooManyFiltersError';
import DownTimeView from 'apps/rails/src/pages/NewListing/Components/DownTimeView';
import CovidErrorView from 'apps/rails/src/pages/NewListing/Components/CovidErrorView';
import NoDirectTrainsFound from 'apps/rails/src/pages/NewListing/Components/NoDirectTrainsFound';
import NoTrainsView from 'apps/rails/src/pages/NewListing/Components/NoTrainsView';
import {
  changeDepartureDate,
  changeFiltersClicked,
  clearAllFiltersClickedInListing,
} from 'apps/rails/src/pages/NewListing/RailsListingActions';
import React, { useEffect, useState } from 'react';
import { default as mainStyle } from '../styles';
import { trackClickEventProp61, trackPageVisits } from '../../RailsBusHomePage/Analytics';
import RAIL_EVENTS from '../../../RailsOmnitureTracker';
import { ErrorFlow } from '../types';
import { Actions } from 'apps/rails/src/navigation';
import { getRailsTbsErrorHandlingFlowV1 } from 'apps/rails/src/RailsAbConfig';

interface ReduxProps {
  changeDepartureDate: unknown;
  onChangeFiltersClicked: unknown;
  onClearAllFiltersClickedInListing: unknown;
  listingErrorCode: string;
  listingErrorMessage: string;
  listingErrorSubtitle: string;
  isLoading: boolean;
  settingListingDataForTravellers: unknown;
  showNoTrainView: unknown;
  noTrainFound: unknown;
  recommendedList: unknown;
  recommendedMessage: unknown;
  connectedTravelApiResponseJson: unknown;
  showLoaderForRailsConnectedTravel: boolean;
  otherDayTrainsList: unknown;
}

interface Props {
  railsListingOmniture: (omnitureType: string) => void;
  postPaymentBooking: boolean;
  crossSellEnabled: boolean;
  retryTrainSearch: (postPaymentBooking: boolean) => void;
  originStation: unknown;
  destinationStation: unknown;
  departureDate: unknown;
  trainsListLength: unknown;
  isErrorRetry: boolean;
  errorRetryData: unknown;
  onBack: boolean;
  showCtV2: number;
  errorFlow: ErrorFlow | null;
  refreshListingPage: () => void;
  tatkalExists: boolean;
  changeDepartureDate: unknown;
  _changeDepartureDate: unknown;
}
//TODO: Handle RAILS_SEARCH_DATE_ERROR_01_CODE and show the rails list instead of bus.

const RailsListingError = (props: Props & ReduxProps) => {
  const {
    isLoading,
    listingErrorCode,
    noTrainFound,
    recommendedList,
    trainsListLength,
    errorFlow,
    refreshListingPage,
  } = props;

  // TBS error handling V1 approach
  const [retrySearchData, setRetrySearchData] = useState({ triesLeft: 2 });

  const resetSearchRetryData = () => {
    setRetrySearchData({ triesLeft: 2 });
  };

  const handleRetryButtonTap = (actionType: string) => {
    try {
      if (actionType === RETRY_ACTION && retrySearchData?.triesLeft > 0) {
        const currentRetryAttempt = 3 - retrySearchData.triesLeft;
        setRetrySearchData((prevState) => ({ triesLeft: prevState.triesLeft - 1 }));
        trackClickEventProp61(
          RAIL_EVENTS.LISTING.MMT_RAIL_RN_LISTING_V2,
          `${RAIL_EVENTS.LISTING.RAIL_LISTING_SEARCH_RETRY_EVENT_PREFIX}_${currentRetryAttempt}`,
        );
        refreshListingPage();
      } else {
        trackClickEventProp61(
          RAIL_EVENTS.LISTING.MMT_RAIL_RN_LISTING_V2,
          RAIL_EVENTS.LISTING.RAIL_LISTING_ERROR_GO_BACK_EVENT,
        );
        resetSearchRetryData();
        Actions.pop();
      }
    } catch (err) {
      console.error('Error handling retry button tap:', err);
    }
  };

  useEffect(() => {
    if (
      getRailsTbsErrorHandlingFlowV1() &&
      (recommendedList?.length > 0 || trainsListLength > 0)
    ) {
      resetSearchRetryData();
    }
  }, [recommendedList, trainsListLength]);

  useEffect(() => {
    if (!isLoading) {
      if (listingErrorCode !== 'MMT_BLOCK_BOOKING_COVID_19' && trainsListLength === 0) {
        trackPageVisits(RAIL_EVENTS.LISTING.RAILS_NO_TRAINS_NO_OTHER_TRAINS, {});
      }
      if (noTrainFound && recommendedList?.length > 0) {
        trackPageVisits(RAIL_EVENTS.LISTING.RAILS_NO_TRAINS_OTHER_TRAINS_FOUND, {});
      }
    }
  }, [isLoading, listingErrorCode, noTrainFound, trainsListLength]);

  return (
    <>
      {!isLoading &&
        !props.settingListingDataForTravellers &&
        props.showNoTrainView &&
        !noTrainFound && (
          <View style={styles.errorContainer}>
            {listingErrorCode === 'MMT_BLOCK_BOOKING_COVID_19' && (
              <CovidErrorView
                errorViewData={{
                  title: props.listingErrorMessage,
                  subTitle: props.listingErrorSubtitle,
                }}
              />
            )}
            {listingErrorCode === LISTING_DOWNTIME_ERROR_CODE && (
              <View>
                <DownTimeView />
              </View>
            )}
            {listingErrorCode !== 'MMT_BLOCK_BOOKING_COVID_19' &&
              listingErrorCode !== LISTING_DOWNTIME_ERROR_CODE && (
                <View style={mainStyle.fullFlex}>
                  <NoTrainsView
                    showCtV2={props?.showCtV2}
                    retry={props.retryTrainSearch(props.postPaymentBooking)}
                    showLoaderForRailsConnectedTravel={props?.showLoaderForRailsConnectedTravel}
                    connectedTravelApiResponseJson={props?.connectedTravelApiResponseJson}
                    errorMessage={props.listingErrorMessage}
                    errorSubtitle={props.listingErrorSubtitle}
                    noCrossSell={props?.crossSellEnabled}
                    changeDate={props?.changeDepartureDate}
                    error
                    errorFlow={errorFlow}
                    departureDate={props.departureDate}
                    retrySearchData={retrySearchData}
                    handleRetryButtonTap={handleRetryButtonTap}
                  />
                </View>
              )}
          </View>
        )}
      {noTrainFound && recommendedList?.length > 0 && (
        <View style={mainStyle.fullFlex}>
          <NoDirectTrainsFound
            showCtV2={props?.showCtV2}
            showLoaderForRailsConnectedTravel={props?.showLoaderForRailsConnectedTravel}
            connectedTravelApiResponseJson={props?.connectedTravelApiResponseJson}
            recommendedMessage={props.recommendedMessage}
            recommendedList={props.recommendedList}
            changeDate={props?.changeDepartureDate}
            originStation={props.originStation}
            destinationStation={props.destinationStation}
            departureDate={props.departureDate}
            noCrossSell={props.crossSellEnabled}
            otherDayTrainsList={props?.otherDayTrainsList}
            tatkalExists={props.tatkalExists}
            _changeDepartureDate={props._changeDepartureDate}
          />
        </View>
      )}

      {!props.isLoading &&
        !props.settingListingDataForTravellers &&
        !props.showNoTrainView &&
        props.trainsListLength <= 0 && (
          <View style={mainStyle.fullFlex}>
            <TooManyFiltersError
              onChangeFiltersClicked={props.onChangeFiltersClicked}
              onClearAllFiltersClicked={props.onClearAllFiltersClickedInListing}
            />
          </View>
        )}
    </>
  );
};
const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const mapStateToProps = (state: unknown) => {
  const {
    railsListing: {
      listingErrorMessage,
      listingErrorSubtitle,
      isLoading,
      settingListingDataForTravellers,
      showNoTrainView,
      noTrainFound,
      recommendedList,
      recommendedMessage,
      listingErrorCode,
      showLoaderForRailsConnectedTravel,
      connectedTravelApiResponseJson,
      otherDayTrainsList,
    },
  } = state;

  return {
    listingErrorMessage,
    listingErrorSubtitle,
    isLoading,
    settingListingDataForTravellers,
    showNoTrainView,
    noTrainFound,
    recommendedList,
    recommendedMessage,
    listingErrorCode,
    showLoaderForRailsConnectedTravel,
    connectedTravelApiResponseJson,
    otherDayTrainsList,
  };
};

const actionCreators = {
  changeDepartureDate,
  onChangeFiltersClicked: changeFiltersClicked,
  onClearAllFiltersClickedInListing: clearAllFiltersClickedInListing,
};
const mapDispatchToProps = (dispatch) => bindActionCreators(actionCreators, dispatch);

export const RailsListingErrorView = connect<ReduxProps, null, Props>(
  mapStateToProps,
  mapDispatchToProps,
)(RailsListingError);
