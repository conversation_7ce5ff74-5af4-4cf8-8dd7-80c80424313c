import React, { useCallback, useRef, useMemo } from 'react';
import { Image, Text, TouchableWithoutFeedback, View } from 'react-native';
import { connect } from 'react-redux';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import TrainItem from '@mmt/rails/src/pages/NewListing/Components/NewTrainCard';
import {
  setPrevBookedCollapsedState,
  updateCardClicked,
  availabilityCardClicked,
  onViewDetailsClicked,
  saveRailofyZcShown,
  toggleNearbyDatesList,
  nearbyDatesCardClick,
} from '@mmt/rails/src/pages/NewListing/RailsListingActions';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import { trackNewListingClickEvent, trackGenericEvar99Event,removeEventFromEvar99Variable } from '@mmt/rails/src/railsAnalytics';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { LISTING_PAGE_TRACKING_KEY, ODT_EVENTS } from '@mmt/rails/src/Utils/RailsConstant';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import styles from './styles';

interface BookedTrainsProps {
  bookedTrains: unknown[];
  originStation: unknown;
  destinationStation: unknown;
  prevBookedCollapsedRoutes: unknown;
  trainNumber: string;
  prevBookedHeading?: string;
  isOtherDayTrainsList?: boolean;
  trainPosition: number;
  tatkalExists: boolean;
  railsShowConfirmationChance: boolean;
  confirmationChances: unknown;
  departureDate: unknown;
  labels: unknown;
  nearbyDatesInfo: unknown;
  listingAdsConfig: unknown;
  dynamicPriceConfig: unknown;

  // Action creators
  setPrevBookedCollapsedState: (origin: unknown, destination: unknown, collapsed: boolean) => void;
  onEmptyAvailabilityCardClicked: (className: unknown, trainData: unknown, quota: unknown) => void;
  onAvailabilityCardClicked: (className: unknown, trainData: unknown, quota: unknown) => void;
  onViewTrainDetailsClicked: (trainData: unknown) => void;
  actionSaveRailofyZcShown: (trainNumber: string) => void;
  toggleNearbyDatesList: () => void;
  nearbyDatesCardClick: () => void;
}

const PreviouslyBookedTrains = (props: BookedTrainsProps) => {
  const {
    bookedTrains,
    originStation,
    destinationStation,
    prevBookedCollapsedRoutes,
    trainNumber,
    listingAdsConfig,
    dynamicPriceConfig,
  } = props;

  const routeKey = useMemo(() => `${originStation?.code}-${destinationStation?.code}`, [originStation?.code, destinationStation?.code]);
  const hasStoredState = useMemo(() => routeKey in prevBookedCollapsedRoutes, [routeKey, prevBookedCollapsedRoutes]);
  const isInitialRender = useRef(true);

  const isBookedExpanded = useMemo(() => {
    return hasStoredState ? !prevBookedCollapsedRoutes[routeKey] : true;
  }, [hasStoredState, prevBookedCollapsedRoutes, routeKey]);

  const handleBookedExpandedToggle = useCallback(() => {
    const newCollapsedState = !isBookedExpanded;

    const wasInitialRender = isInitialRender.current;
    isInitialRender.current = false;

    props.setPrevBookedCollapsedState(originStation, destinationStation, !newCollapsedState);

    if (isBookedExpanded) {
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_COLLAPSED,
      );
    } else {
      const isReturnVisit = hasStoredState && !wasInitialRender;

      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        isReturnVisit
          ? RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_EXPAND1
          : RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_EXPAND2,
      );
    }
  }, [isBookedExpanded, originStation, destinationStation, hasStoredState, props.setPrevBookedCollapsedState]);

  const _onAvailabilityCardClicked = useCallback(async (
    callback,
    className,
    trainData,
    quota,
  ) => {
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_CARD_CLICK);
    trackClickEventProp61(
      LISTING_PAGE_TRACKING_KEY,
      RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_CARD_CLICKED,
    );
    trackGenericEvar99Event(
      LISTING_PAGE_TRACKING_KEY,
      RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_CARD_CLICK,
    );
    const trainDataWithSection = {
      ...trainData,
      sectionIdentifier: 'booked',
      selectedCardTrainNumber: trainData.trainNumber,
    };

    callback(className, trainDataWithSection, quota);
  }, []);

  return (
    <View style={styles.bookedTrainscontainer}>
      <LinearGradient
        colors={[colors.purple4, colors.white]}
        start={{ x: 1.2, y: 0.52 }}
        end={{ x: 0.23, y: 0.52 }}
        style={styles.bookedTrainscontainer}
      >
        {bookedTrains?.length > 0 && (
          <TouchableWithoutFeedback onPress={handleBookedExpandedToggle}>
            <View style={styles.bookedTrainsexpandedContainer}>
              <View style={styles.iconAndTextContainer}>
              <Image source={ASSETS.favouriteIcon} style={styles.IconContainer} />
              <Text style={styles.bookedTrainsText}>
                {props?.prevBookedHeading || _label('previously_booked')}
              </Text>
              </View>
              <Image
                source={isBookedExpanded ? ASSETS.upArrow : ASSETS.downArrow}
                style={styles.IconContainer}
              />
            </View>
          </TouchableWithoutFeedback>
        )}
        {isBookedExpanded &&
          bookedTrains?.map((train, index) => (
            <View
              key={`${trainNumber}_booked_${index}`}
              style={styles.bookedTrainsCardContainer}
            >
              <TrainItem
                id={`train_listing_${
                  props?.isOtherDayTrainsList ? '_odt' : 'bookedTrains'
                }_trainCard_${index}`}
                onEmptyAvailabilityCardClicked={(className, trainData, quota) => {
                  _onAvailabilityCardClicked(
                    props.onEmptyAvailabilityCardClicked,
                    className,
                    trainData,
                    quota,
                  );
                  if (props?.isOtherDayTrainsList) {
                    trackNewListingClickEvent(ODT_EVENTS.odt_card_clicked);
                  }
                }}
                onAvailabilityCardClicked={(className, trainData, quota) => {
                  _onAvailabilityCardClicked(
                    props.onAvailabilityCardClicked,
                    className,
                    trainData,
                    quota,
                  );
                }}
                isOtherDayTrainsList={props?.isOtherDayTrainsList}
                tatkalExists={props.tatkalExists}
                onViewDetailsClicked={props.onViewTrainDetailsClicked}
                key={`${trainNumber}_booked_${index}`}
                confirmationChance={
                  props.railsShowConfirmationChance
                    ? props.confirmationChances[trainNumber]
                    : null
                }
                railsShowConfirmationChance={props.railsShowConfirmationChance}
                originStation={props.originStation}
                destinationStation={props.destinationStation}
                departureDate={props.departureDate}
                labels={props.labels}
                actionSaveRailofyZcShown={props.actionSaveRailofyZcShown}
                trainPosition={props.trainPosition}
                listingAdsConfig={listingAdsConfig}
                nearbyDatesInfo={props.nearbyDatesInfo}
                toggleNearbyDatesList={(show, trainNumber, classType, quota) => {
                  props.toggleNearbyDatesList(show, trainNumber, classType, quota, 'booked');
                }}
                nearbyDatesCardClick={(trainData, className, quota) => {
                  props.nearbyDatesCardClick({...trainData, sectionIdentifier: 'booked'}, className, quota);
                }}
                trainData={{...train, uniqueCardId: `booked_${train.trainNumber}_${index}`}}
                dynamicPriceConfig={dynamicPriceConfig}
                disableNearbydates={true}
              />
            </View>
          ))}
      </LinearGradient>
      <View style={styles.divider} />
    </View>
  );
};

const mapDispatchToProps = {
  setPrevBookedCollapsedState,
  onEmptyAvailabilityCardClicked: updateCardClicked,
  onAvailabilityCardClicked: availabilityCardClicked,
  onViewTrainDetailsClicked: onViewDetailsClicked,
  actionSaveRailofyZcShown: saveRailofyZcShown,
  toggleNearbyDatesList,
  nearbyDatesCardClick,
};

export default connect(null, mapDispatchToProps)(React.memo(PreviouslyBookedTrains));
