import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, gradient } from '@mmt/legacy-commons/Styles/globalStyles';
import React, { useEffect, useState } from 'react';
import {
  Clipboard,
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  CLOSE,
  ICON_ON_TOP,
  LISTING_PAGE_TRACKING_KEY,
  TITLE_ON_TOP,
} from '../../../Utils/RailsConstant';
import { Actions } from '../../../navigation';
import { trackGenericEvar99Event } from '../../../railsAnalytics';
import { trackClickEventProp61 } from '../../RailsBusHomePage/Analytics';
import { fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import { TERMS_AND_CONDITIONS } from '../../RIS/PnrStatus/TGSUtils/Constants';
import BottomSheetModalTrain from '../../Common/BottomSheetModalTrain';

const RtbAffordanceBottomSheet = (props) => {
  const {
    tbsRtbBottomSheet: {
      omnitureKey = '',
      templateType = '',
      key: { key: featureKey = '' } = {},
    } = {},
    rtbLandingActionsData = {},
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    updateRtbListingBottomSheet = () => {},
  } = props || {};

  const [featureData, setFeatureData] = useState(null);
  const [showRtbAffordanceBottomSheet, setShowRtbAffordanceBottomSheet] = React.useState(true);
  const [imageHeight, setImageHeight] = useState(0);

  useEffect(() => {
    if (featureKey && templateType && rtbLandingActionsData) {
      const bottomSheetData = rtbLandingActionsData?.actions?.bottom_sheet;
      if (
        bottomSheetData &&
        bottomSheetData[templateType] &&
        bottomSheetData[templateType][featureKey]
      ) {
        setFeatureData(bottomSheetData[templateType][featureKey]);
      }
    }
    trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY, `rs_${omnitureKey}`);
  }, [featureKey, templateType, rtbLandingActionsData, omnitureKey]);

  useEffect(() => {
    if (featureData?.highlight?.icon?.url) {
      Image.getSize(featureData?.highlight?.icon?.url, (width, height) => {
        setImageHeight(height);
      });
    }
  }, [featureData?.highlight?.icon?.url]);
  const handleModalClose = () => {
    setShowRtbAffordanceBottomSheet(false);
    updateRtbListingBottomSheet(false);
  };

  const handleTncPress = () => {
    if (featureData?.tncCta && featureData?.tncCta?.action === 'WEBVIEW') {
      handleModalClose();
      const webviewKey = featureData?.tncCta?.config?.key;
      const webviewUrl = rtbLandingActionsData?.actions?.webview?.[webviewKey]?.link;
      Actions.openWebView({
        url: webviewUrl,
        headerText: TERMS_AND_CONDITIONS,
      });
    }
    trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, `mob_rail_listing_rtb_bs_tnc_${omnitureKey}`);
  };

  const handleCtaPress = () => {
    trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, `mob_rail_listing_rtb_bs_cta_${omnitureKey}`);
    if (featureData?.cta && featureData?.cta?.action === CLOSE) {
      handleModalClose();
    }
  };
  const copyCouponText = () => {
    trackClickEventProp61(
      LISTING_PAGE_TRACKING_KEY,
      `mob_rail_listing_rtb_bs_coupon_${omnitureKey}`,
    );
    trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY, `cb_${omnitureKey}`);
    if (featureData?.couponRecommendation?.code) {
      Clipboard.setString(featureData.couponRecommendation.code);
    }
  };
  return (
    showRtbAffordanceBottomSheet && (
      <BottomSheetModalTrain
        onTouchOutside={handleModalClose}
        hardwareBackButtonClose={handleModalClose}
        additionalContainerStyle={{ zIndex: 999 }}
        testID="rtb_affordance_bottomsheet_modal"
      >
        <View style={styles.mainContainer}>
        <View style={styles.templateContainer}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollViewContent}
            style={styles.scrollView}
            testID="rtb_affordance_bottomsheet_scrollview"
          >
            {templateType === TITLE_ON_TOP && (
              <>
                <View>
                  <Text style={[styles.title, fontStyle('medium')]}>{featureData?.title}</Text>
                </View>
                <View style={styles.featureIconContainer}>
                  <Image
                    source={{ uri: featureData?.rtbTitle?.icon?.url }}
                    style={styles.iconStyleTitleOnTop}
                  />
                  <Text style={[styles.featureIconContainerText, fontStyle('medium')]}>
                    {featureData?.rtbTitle?.title}
                  </Text>
                </View>
              </>
            )}
            {templateType === ICON_ON_TOP && (
              <View style={styles.iconOnTopContainer}>
                <Image source={{ uri: featureData?.icon?.url }} style={styles.iconStyle} />
                <View style={styles.textDescription}>
                  <Text style={[styles.featureIconContainerText, fontStyle('medium')]}>
                    {featureData?.title}
                  </Text>
                  <Text style={[styles.description, fontStyle('medium')]}>
                    {featureData?.description}
                  </Text>
                </View>
              </View>
            )}
            {featureData?.couponRecommendation && (
              <View style={styles.couponRecommendation}>
              <Text style={[styles.couponDescription, fontStyle('medium')]}>
                {featureData?.couponRecommendation?.title}
              </Text>

              <TouchableRipple
                onPress={copyCouponText}
                testID="rtb_affordance_bottomsheet_copy_coupon_button"
              >
                <View style={styles.couponCodeContainer}>
                  <Text style={[styles.couponTextStyle, fontStyle('medium')]}>
                    {' '}
                    {rtbLandingActionsData?.actions?.copy_coupon?.[featureKey]?.code ||
                      featureData?.couponRecommendation?.code}
                  </Text>
                  <Image
                    source={{ uri: featureData?.couponRecommendation?.cta?.icon?.url }}
                    style={styles.copyStyle}
                    resizeMode="contain"
                  />
                </View>
                </TouchableRipple>
              </View>
            )}

            {featureData?.highlight && (
              <View style={styles.highlightOuterContainer} testID="hightlight_container">
              {featureData?.highlight?.imgTopContent && (
                <View style={styles.imgContentContainerTop}>
                  <Text style={styles.imgContentText}>
                    {featureData?.highlight?.imgTopContent?.text}
                  </Text>
                  {featureData?.highlight?.imgTopContent?.bulletPoints &&
                    featureData?.highlight?.imgTopContent?.bulletPoints?.length > 0 && (
                      <View style={styles.bulletPointsContainer}>
                        {featureData?.highlight?.imgTopContent?.bulletPoints?.map(
                          (point, index) => (
                            <Text key={`top-${index}`} style={styles.bulletText}>
                              {'\u2022'} {point}
                            </Text>
                          ),
                        )}
                      </View>
                    )}
                </View>
              )}
              <View style={styles.highlightContainer}>
                <Image
                  source={{ uri: featureData?.highlight?.icon?.url }}
                  style={[styles.flowImage, { height: Math.min(imageHeight, 150) }]}
                  accessibilityLabel={featureData?.highlight?.icon?.altText || 'Process flow'}
                  resizeMode="contain"
                />
              </View>
              {featureData?.highlight?.imgBottomContent && (
                <View style={styles.imgContentContainerBottom}>
                  {featureData?.highlight?.imgBottomContent?.text && (
                    <Text style={styles.imgContentText}>
                      {featureData?.highlight?.imgBottomContent?.text}
                    </Text>
                  )}
                  {featureData?.highlight?.imgBottomContent?.bulletPoints &&
                    featureData?.highlight?.imgBottomContent?.bulletPoints?.length > 0 && (
                      <View style={styles.bulletPointsContainer}>
                        {featureData?.highlight?.imgBottomContent?.bulletPoints?.map(
                          (point, index) => (
                            <Text key={`top-${index}`} style={styles.bulletText}>
                              {'\u2022'} {point}
                            </Text>
                          ),
                        )}
                      </View>
                    )}
                </View>
              )}
              <View style={styles.dividerLine} />
              {featureData?.highlight?.socialProofing &&
                featureData?.highlight?.socialProofing?.length > 0 && (
                  <View style={styles.socialProofContainer}>
                    {featureData?.highlight?.socialProofing?.map((proof, index) => (
                      <View
                        key={`social-${index}`}
                        style={[styles.socialProofBadge, { backgroundColor: proof.bgColor }]}
                      >
                        <Image
                          source={{ uri: proof?.icon?.url }}
                          style={styles.socialProofIcon}
                          accessibilityLabel={proof?.icon?.altText}
                        />
                        <Text
                          style={[
                            styles.socialProofText,
                            { color: proof?.textColor },
                            fontStyle('regular'),
                          ]}
                        >
                          {proof?.title}
                        </Text>
                      </View>
                      ))}
                  </View>
                )}
              </View>
            )}
          </ScrollView>
        </View>
        {featureData?.tncCta?.text && (
          <TouchableRipple
            feedbackColor={colors.white}
            onPress={handleTncPress}
            testID="terms_and_conditions"
          >
              <View style={styles.termsContainer}>
                <Text style={[styles.termsAndConditions, fontStyle('medium')]}>
                  {featureData?.tncCta?.text}
                </Text>
              </View>
          </TouchableRipple>
        )}
        {featureData?.cta?.text && (
          <TouchableRipple
            feedbackColor={colors.white}
              onPress={handleCtaPress}
              testID="rtb_button_okay"
          >
              <View style={styles.buttonContainer}>
                <LinearGradient
                  start={{ x: 1.0, y: 0.0 }}
                  end={{ x: 0.0, y: 1.0 }}
                  colors={gradient.lightBlue}
                  style={[styles.checkAvlBtn, styles.mar10Bt]}
                >
                  <Text style={[styles.clickableLink, fontStyle('medium')]}>
                    {featureData?.cta?.text}
                  </Text>
                </LinearGradient>
              </View>
          </TouchableRipple>
        )}
        </View>
      </BottomSheetModalTrain>
    )
  );
};

const styles = StyleSheet.create({
  featureIconContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
  },
  flowImage: {
    width: '90%',
    maxHeight: 200,
  },
  couponDescription: {
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
    color: colors.black,
  },
  highlightOuterContainer: {
    borderWidth: 1,
    borderColor: colors.lightSilver,
    borderRadius: 12,
    marginBottom: 5,
    overflow: 'hidden',
    width: '100%',
  },
  iconOnTopContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 8,
  },
  couponCodeContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3,
  },
  textDescription: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 8,
  },
  bulletPointsContainer: {
    height: 50,
    marginTop: 8,
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.textGrey,
    marginTop: 6,
    marginRight: 8,
  },
  bulletText: {
    fontSize: 12,
    color: colors.greyText1,
    fontWeight: '400',
    flex: 1,
    lineHeight: 20,
  },
  highlightContainer: {
    alignItems: 'center',
    width: '100%',
  },
  dividerLine: {
    height: 1,
    backgroundColor: colors.lightSilver,
  },
  socialProofContainer: {},
  socialProofBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  socialProofIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
    marginRight: 4,
  },
  socialProofText: {
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
    lineHeight: 20,
  },
  templateContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 24,
    paddingBottom: 5,
    paddingHorizontal: 16,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    backgroundColor: colors.white,
    overflow: 'hidden',
  },
  mainContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalBackdrop: {
    position: 'absolute',
    bottom: 0,
    top: 0,
    right: 0,
    left: 0,
    flex: 1,
    backgroundColor: colors.greyTransparent,
    justifyContent: 'flex-end',
  },
  imgContentContainerBottom: {
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  imgContentContainerTop: {
    paddingHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  imgContentText: {
    fontSize: 14,
    color: colors.greyText1,
    fontWeight: '400',
    lineHeight: 20,
    marginBottom: 8,
  },
  title: {
    color: colors.black,
    textAlign: 'center',
    fontSize: 22,
    fontStyle: 'normal',
    fontWeight: '900',
    lineHeight: 26,
  },
  iconStyle: {
    width: 56,
    height: 56,
    aspectRatio: 1.3,
    marginRight: 8,
  },
  iconStyleTitleOnTop: {
    width: 32,
    height: 32,
    aspectRatio: 1.3,
    marginRight: 8,
  },
  featureIconContainerText: {
    color: colors.black,
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: '900',
  },
  description: {
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 20,
  },
  marginTop16: {
    marginTop: 16,
  },
  couponRecommendation: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 24,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: colors.lightSilver,
    marginTop: 24,
    marginBottom: 20,
  },
  copyStyle: {
    width: 14,
    height: 18,
    marginLeft: 8,
    color: colors.primary,
  },
  couponTextStyle: {
    color: colors.primary,
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '900',
  },
  termsAndConditions: {
    color: colors.primary,
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: '400',
  },
  termsContainer: {
    justifyContent: 'flex-start',
    backgroundColor: colors.white,
    paddingBottom: 12,
    paddingTop: 0,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    backgroundColor: colors.white,
    paddingBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 5,
  },
  checkAvlBtn: {
    paddingVertical: 18,
    paddingHorizontal: 15,
    borderRadius: 16,
    justifyContent: 'center',
  },
  mar10Bt: {
    marginBottom: 10,
  },
  clickableLink: {
    color: colors.white,
    fontStyle: 'normal',
    lineHeight: 22,
    fontSize: 16,
    fontWeight: '900',
    textAlign: 'center',
  },
  socialProofingContainer: {
    paddingTop: 6,
    paddingRight: 8,
    paddingBottom: 8,
    paddingLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    borderWidth: 1,
    borderColor: colors.lightSilver,
    backgroundColor: colors.lightGreen1,
  },
  scrollViewContent: {
    paddingBottom: 16,
  },
  scrollView: {
    width: '100%',
    maxHeight: Dimensions.get('window').height * 0.8,
  },
});

export default RtbAffordanceBottomSheet;
