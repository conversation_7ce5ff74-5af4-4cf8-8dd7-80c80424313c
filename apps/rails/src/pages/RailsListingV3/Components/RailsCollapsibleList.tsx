
import React, { useEffect, useState, useRef } from 'react';
import isArray from 'lodash/isArray';
import { FlatList, Platform, StyleSheet, Text, View, Image, ActivityIndicator } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { today } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import { MAX_DAYS } from 'apps/rails/src/Utils/RailsConstant';
import Calendar from 'apps/rails/src/pages/Calendar/DatePicker';
import RailsFilterBar from 'apps/rails/src/pages/NewListing/Containers/RailFilterBarContainer';
import { RailsListItemV3 } from 'apps/rails/src/pages/RailsListingV3/Components';
import {
  TbsWithAvailabilityAndRecommendation,
  TrainBtwnStnsList,
} from 'apps/rails/src/pages/RailsListingV3/types';
import { trackNewListingLoad } from '../../../railsAnalytics';
import { tuneRailsListingLoad } from '../../NewListing/RailsListingActions';
import { trackTotalCardsOnListing } from '../../../Utils/railofyUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import ListingBanner from 'apps/rails/src/pages/NewListing/Components/ListingBanner/ListingBanner';
import { MmtlistingHighlight } from 'apps/rails/src/configStore/Landing/types';
import { RAILS_LISTING_COLLAPSIBLE_Constants } from '@mmt/rails/src/Utils/RailsConstant';
import { AadharLinkingBanner, AadharTicketsRunningOut } from './RailsAadharLinking';
import { LISTING_EDUCATION_CARD, LISTING_EDUCATION_CARD_Pokus } from 'apps/rails/src/RailsAbConstants';
import { getRailsListingEducationCard } from 'apps/rails/src/RailsAbConfig';
interface Props {
  tatkalExists: unknown;
  originStation: unknown;
  destinationStation: unknown;
  departureDate: unknown;
  data: unknown;
  fromSeo: unknown;
  showBnplWidget: unknown;
  _changeDepartureDate: unknown;
  fcStripIndex: number;
  fcStripText: unknown;
  mmtlistingHighlights: MmtlistingHighlight;
  showBnpp: boolean;
  appVersion?: string;
  isOtherDayTrainsList: boolean;
  otherDayTrainsList: unknown;
  maxArpDays?: number;
  showConnectedTravelV2: number;
  listingEducationCard?: string;
}

import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';
import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';

interface RailsCollapsibleListProps {
  data?: TbsWithAvailabilityAndRecommendation;
  connectedTravelIndex?: number;
  showConnectedTravelV2?: number;
}

const RailsCollapsibleList = (props: Props & RailsCollapsibleListProps) => {
  const [viewMoreOtherDayTrains, setOtherDayTrains] = useState(false);
  const [visibleData, setVisibleData] = useState([]);
  const [lastItemVisible, setLastItemVisible] = useState(false);

  const maxDaysArpWindow =
    typeof props.maxArpDays === 'string'
      ? Number(props.maxArpDays) + 1
      : props?.maxArpDays ?? MAX_DAYS + 1;

  const ITEMS_TO_RENDER = 20;

  const {
    originStation,
    destinationStation,
    departureDate,
    data,
    fromSeo,
    otherDayTrainsList = [],
  } = props;

  useEffect(() => {
    trackNewListingLoad(originStation, destinationStation, true);
    tuneRailsListingLoad(originStation, destinationStation, departureDate);
    trackTotalCardsOnListing(data, data);
  }, [originStation.code, destinationStation.code, departureDate]);

  const getOtherDayTrainsList = () => {
    if (!viewMoreOtherDayTrains) {
      return otherDayTrainsList.slice(0, 3);
    }
    return otherDayTrainsList;
  };

  const combinedList = [
    ...(isArray(data) && data?.length > 0 ? data.map((item) => ({ type: 'regular', item })) : []),
    ...(otherDayTrainsList && otherDayTrainsList.length > 0 ? [{ type: 'sectionHeader' }] : []),
    ...(otherDayTrainsList
      ? getOtherDayTrainsList().map((item) => ({ type: 'otherDay', item }))
      : []),
  ];

  const loadMoreData = () => {
    if (visibleData.length >= combinedList.length) {
      return false;
    }

    const nextBatch = [];
    let count = 0;

    for (let i = visibleData.length; i < combinedList.length && count < ITEMS_TO_RENDER; i++) {
      const item = combinedList[i];
      nextBatch.push(item);
      // Increment count only for train items, not headers
      if (item.type === 'regular' || item.type === 'otherDay') {
        count++;
      }
    }

    setVisibleData((prevVisibleData) => [...prevVisibleData, ...nextBatch]);
  };

  const _renderTrainListItem = ({
    item,
    index,
  }: {
    item: { type: string; item?: TrainBtwnStnsList };
    index: number;
  }) => {
    if (item.type === 'sectionHeader') {
      return (
        <View style={styles.weeklyTrains} testID="other_day_trains_section_header">
          <Text
            style={[styles.weeklyTrainsText, fontStyle('regular')]}
            testID="other_day_trains_section_header_text"
          >
            {RAILS_LISTING_COLLAPSIBLE_Constants.weeklyTrainsHeader}
          </Text>
          <Text style={[styles.weeklyTrainsText, fontStyle('bold')]}>
            {props?.originStation?.cityName} to {props?.destinationStation?.cityName}
          </Text>
        </View>
      );
    }

    if (item.type === 'otherDay') {
      return (
        <RailsListItemV3
          item={item.item}
          index={index}
          connectedTravelIndex={-1}
          showConnectedTravelV2={props?.showConnectedTravelV2}
          fcStripIndex={-1}
          fcStripText={null}
          isOtherDayTrainsList={true}
          bnplWidgetIndex={-1}
        />
      );
    }

    return (
      <RailsListItemV3
        item={item.item}
        index={index}
        fromSeo={fromSeo}
        connectedTravelIndex={props?.connectedTravelIndex}
        showConnectedTravelV2={props?.showConnectedTravelV2}
        fcStripIndex={props.fcStripIndex}
        fcStripText={props.fcStripText}
      />
    );
  };

  const renderFooter = () => {
    return (
      <View style={{ paddingVertical: 20 }}>
        <ActivityIndicator animating size="large" />
      </View>
    );
  };
  const _listFooterComponent = () => (
    <View style={styles.flatListContainer}>
      {otherDayTrainsList.length > 3 ? (
        !viewMoreOtherDayTrains ? (
          <TouchableRipple onPress={() => setOtherDayTrains(!viewMoreOtherDayTrains)}>
            <View style={styles.flexDirection}>
              <Text style={[styles.moreOptionsText, fontStyle('regular')]}>
                {' '}
                {otherDayTrainsList.length - 3} more options available |
              </Text>
              <TouchableRipple onPress={() => setOtherDayTrains(!viewMoreOtherDayTrains)}>
                <Text style={[styles.moreOptionsLink, fontStyle('bold')]}>
                  {RAILS_LISTING_COLLAPSIBLE_Constants.viewOptions}{' '}
                </Text>
              </TouchableRipple>
              <Image source={arrowDown} style={styles.arrowContainer} />
            </View>
          </TouchableRipple>
        ) : (
          <View style={styles.flexDirection}>
            <TouchableRipple onPress={() => setOtherDayTrains(!viewMoreOtherDayTrains)}>
              <Text style={[styles.moreOptionsLink, fontStyle('bold')]}>
                {RAILS_LISTING_COLLAPSIBLE_Constants.hideOptions}{' '}
              </Text>
            </TouchableRipple>
            <Image source={arrowUp} style={styles.arrowContainer} />
          </View>
        )
      ) : (
        <View style={styles.endContainer}>
          <Text style={styles.endText}>{RAILS_LISTING_COLLAPSIBLE_Constants.endOfResults}</Text>
        </View>
      )}
    </View>
  );

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 100,
  };

  const renderAadharLinkingBanner = () => {
    if (getRailsListingEducationCard() === LISTING_EDUCATION_CARD_Pokus.SHOW) {
      switch (props?.listingEducationCard) {
        case LISTING_EDUCATION_CARD.SHOW_LINK_AADHAR:
          return <AadharLinkingBanner />;
        case LISTING_EDUCATION_CARD.SHOW_TICKETS_RUNNING_OUT:
          return <AadharTicketsRunningOut />;
        default:
          return null;
      }
    } else {
      return null;
    }
  };

  const onViewableItemsChanged = ({ viewableItems }) => {
    if (viewableItems.some((item) => item.index === combinedList.length - 1)) {
      setLastItemVisible(true);
    }
  };
  const viewabilityConfigCallbackPairs = useRef([{ viewabilityConfig, onViewableItemsChanged }]);

  return (
    <View style={styles.container}>
      <FlatList
        data={combinedList}
        keyExtractor={(item, index) => `${item.type}-${index}`}
        renderItem={_renderTrainListItem}
        ListHeaderComponent={
          isArray(data) && data?.length > 0 ? (
            <View>
              <Calendar
                title={RAILS_LISTING_COLLAPSIBLE_Constants.calendarTitle}
                minDate={today()}
                maxDays={maxDaysArpWindow || MAX_DAYS}
                selectedDate={departureDate}
                changeDate={props._changeDepartureDate}
                srcCode={originStation?.code}
                destCode={destinationStation?.code}
              />
              <ListingBanner
                mmtlistingHighlights={props.mmtlistingHighlights}
                appVersion={props.appVersion}
                showBnpp={props.showBnpp}
              />
              {renderAadharLinkingBanner()}
            </View>
          ) : null
        }
        ListFooterComponent={
          visibleData.length < combinedList.length
            ? lastItemVisible
              ? _listFooterComponent()
              : renderFooter()
            : _listFooterComponent()
        }
        initialNumToRender={ITEMS_TO_RENDER}
        maxToRenderPerBatch={ITEMS_TO_RENDER}
        windowSize={3}
        onEndReached={loadMoreData}
        onEndReachedThreshold={0.5}
        viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
      />
      <View style={styles.filterContainer}>
        <RailsFilterBar />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterContainer: {
    flex: 1,
    position: 'absolute',
    bottom: 12,
  },
  flatListContainer: {
    paddingBottom: 80,
  },
  moreOptionsText: {
    fontSize: 12,
    color: colors.defaultTextColor,
    padding: 5,
  },
  moreOptionsLink: {
    fontSize: 12,
    color: colors.primary,
  },
  endContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 16,
    paddingBottom: Platform.select({ default: 0, web: 16 }),
  },
  endText: {
    color: colors.defaultTextColor,
    fontSize: 12,
    alignSelf: 'center',
  },
  weeklyTrains: {
    backgroundColor: colors.lightGreen18,
    padding: 10,
    paddingLeft: 15,
    marginBottom: 10,
  },
  weeklyTrainsText: {
    color: colors.black,
    lineHeight: 19,
    fontSize: 16,
  },
  arrowContainer: {
    width: 20,
    height: 20,
  },
  flexDirection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RailsCollapsibleList;
