import React, { useCallback } from 'react';
import { View } from 'react-native';
import { connect, DefaultRootState } from 'react-redux';
import { bindActionCreators } from 'redux';
import BookNowPayLater from '@mmt/legacy-commons/Common/Components/BookNowPayLater';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';

import ConnectedTravelWidget from 'apps/rails/src/pages/NewListing/Components/ConnectedTravel/ConnectedTravelWidget';
import {
  AbConfigKeyMappings,
  getPokusConfig,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import WalletBanner from 'apps/rails/src/pages/Common/WalletBanner';
import LoginPersuassionCard from 'apps/rails/src/pages/Landing/Components/Cards/LoginPersuassionCard';
import { getLabels } from 'apps/rails/src/pages/NewListing/Components/ConfirmedOptions/ConfirmedOptionsUtils';
import TrainItem from 'apps/rails/src/pages/NewListing/Components/NewTrainCard';
import {
  availabilityCardClicked,
  onViewDetailsClicked,
  saveRailofyZcShown,
  updateCardClicked,
  verifyTatkalExists,
  toggleNearbyDatesList,
  nearbyDatesCardClick,
  setPrevBookedCollapsedState,
  resetPrevBookedCollapsedState,
} from 'apps/rails/src/pages/NewListing/RailsListingActions';
import { ReduxProps, TrainBtwnStnsList } from 'apps/rails/src/pages/RailsListingV3/types';
import {
  removeEventFromEvar99Variable,
  trackNewListingClickEvent,
} from 'apps/rails/src/railsAnalytics';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import isEmpty from 'lodash/isEmpty';
import styles from './styles';
import FCStrip from 'apps/rails/src/pages/Landing/Components/Cards/FCStrip';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import useConfigStore from '@mmt/rails/src/configStore/Common/RailsConfigStoreUtils';
import ConnectedTravelWidgetV2 from '../../NewListing/Components/ConnectedTravelV2/ConnectedTravelWidgetV2';
import { ODT_EVENTS } from '@mmt/rails/src/Utils/RailsConstant';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import { showPreviouslyBooked, showPreviouslySearched } from '@mmt/rails/src/RailsAbConfig';
import {
  connectedTravelVariants,
  PreviouslyBooked,
  PreviouslySearched,
} from '@mmt/rails/src/RailsAbConstants';
import PreviouslyBookedTrains from './PreviouslyBookedTrains';
import PreviouslySearchedTrains from './PreviouslySearchedTrains';
import PropTypes from 'prop-types';
const DEFAULT_BANNER_INDEX = 2;

interface Props {
  item: TrainBtwnStnsList;
  index: unknown;
  fromSeo: unknown;
  fcStripIndex: number;
  fcStripText: unknown;
  showConnectedTravelV2: number;
}

const LoginPersuassionCardComponent = () => {
  return (
    <LoginPersuassionCard
      loginStatus={_label('login_now')}
      subTitle2={_label('see_saved_guest_list')}
      subTitle1={_label('pay_using_wallet')}
      fromRailsList={true}
      referrer={'railsListing'}
    />
  );
};

const RailsListItem = (props: Props & ReduxProps) => {
  const { item, index } = props;
  const showBnplWidget = getPokusConfig(
    PokusLobs.RAIL,
    AbConfigKeyMappings.railsShowBnplWidget,
    false,
  );

  const listingAdsConfig = useConfigStore(configKeys.RAILS_LISTING_ADS_CONFIG);
  const dynamicPriceConfig = useConfigStore(configKeys.RAILS_DYNAMIC_PRICE_CONFIG);

  const _onAvailabilityCardClicked = useCallback(async (
    callback: unknown,
    className: unknown,
    trainData: unknown,
    quota: unknown,
  ) => {
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_CARD_CLICK);
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_CARD_CLICK);
    callback(className, trainData, quota);
    if (props?.isOtherDayTrainsList) {
      trackNewListingClickEvent(ODT_EVENTS.odt_card_clicked);
    }
  }, [props?.isOtherDayTrainsList]);

  const bnplWidget =
    showBnplWidget && props.bnplWidgetIndex === index ? (
      <BookNowPayLater
        lob="Rails"
        lobLabel={_label('train_tickets')}
        amount={3000}
        onCheckEligibilityClicked={() => {
          trackNewListingClickEvent('rails_listing_BNPL_eligibility_checked', '');
        }}
      />
    ) : null;

  return (
    <>
      <View key={item.trainNumber} testID="rails_listing_item_container">
        {index === props?.prevBookedPosition &&
          showPreviouslyBooked() === PreviouslyBooked.SHOWN && (
            <PreviouslyBookedTrains
              bookedTrains={props.bookedTrains || []}
              originStation={props.originStation}
              destinationStation={props.destinationStation}
              prevBookedCollapsedRoutes={props.prevBookedCollapsedRoutes}
              trainNumber={item.trainNumber}
              prevBookedHeading={props.prevBookedHeading}
              isOtherDayTrainsList={props.isOtherDayTrainsList}
              trainPosition={props.index + 1}
              tatkalExists={props.tatkalExists}
              railsShowConfirmationChance={props.railsShowConfirmationChance}
              confirmationChances={props.confirmationChances}
              departureDate={props.departureDate}
              labels={props.labels}
              nearbyDatesInfo={props.nearbyDatesInfo}
              listingAdsConfig={listingAdsConfig}
              dynamicPriceConfig={dynamicPriceConfig}
            />
          )}
        {index === props?.prevSearchPosition - 1 &&
          showPreviouslySearched() === PreviouslySearched.SHOWN && (
            <PreviouslySearchedTrains
              interactedTrains={props.interactedTrains || []}
              prevSearchedHeading={props.prevSearchedHeading}
              isOtherDayTrainsList={props.isOtherDayTrainsList}
              tatkalExists={props.tatkalExists}
              railsShowConfirmationChance={props.railsShowConfirmationChance}
              confirmationChances={props.confirmationChances}
              originStation={props.originStation}
              destinationStation={props.destinationStation}
              departureDate={props.departureDate}
              labels={props.labels}
              nearbyDatesInfo={props.nearbyDatesInfo}
              listingAdsConfig={listingAdsConfig}
              dynamicPriceConfig={dynamicPriceConfig}
            />
          )}
        <TrainItem
          id={`train_listing_${props?.isOtherDayTrainsList ? '_odt' : ''}_trainCard_${index}`}
          onEmptyAvailabilityCardClicked={(className, trainData, quota) => {
            _onAvailabilityCardClicked(
              props.onEmptyAvailabilityCardClicked,
              className,
              trainData,
              quota,
            );
            if (props?.isOtherDayTrainsList) {
              trackNewListingClickEvent(ODT_EVENTS.odt_card_clicked);
            }
          }}
          onAvailabilityCardClicked={(className, trainData, quota) => {
            _onAvailabilityCardClicked(
              props.onAvailabilityCardClicked,
              className,
              trainData,
              quota,
            );
          }}
          isOtherDayTrainsList={props.isOtherDayTrainsList}
          trainData={item}
          tatkalExists={props.tatkalExists}
          onViewDetailsClicked={props.onViewTrainDetailsClicked}
          key={item.trainNumber}
          confirmationChance={
            props.railsShowConfirmationChance ? props.confirmationChances[item.trainNumber] : null
          }
          railsShowConfirmationChance={props.railsShowConfirmationChance}
          originStation={props.originStation}
          destinationStation={props.destinationStation}
          departureDate={props.departureDate}
          labels={props.labels}
          actionSaveRailofyZcShown={props.actionSaveRailofyZcShown}
          trainPosition={props.index + 1}
          listingAdsConfig={listingAdsConfig}
          nearbyDatesInfo={props.nearbyDatesInfo}
          toggleNearbyDatesList={props.toggleNearbyDatesList}
          nearbyDatesCardClick={props.nearbyDatesCardClick}
          dynamicPriceConfig={dynamicPriceConfig}
          index={index}
          notAvailableCard={props.notAvailableCard}
        />
        {index === 0 && !props.fromSeo && <LoginPersuassionCardComponent />}
        <View style={styles.divider} />
        {!props.showAlternateAvailability && (
          <View style={{ backgroundColor: colors.grayBg }}>
            <WalletBanner funnel="listing" />
          </View>
        )}
        {index === props.fcStripIndex && <FCStrip fcStripText={props?.fcStripText} />}
        {(props?.showConnectedTravelV2 === connectedTravelVariants.SHOWN_EXPANDED_CARD ||
          props?.showConnectedTravelV2 === connectedTravelVariants.MOVED_DIRECT_LISTING) &&
          index === props.connectedTravelIndex && (
            <ConnectedTravelWidget
              showCtV2={props?.showConnectedTravelV2}
              connectedTravelData={props?.connectedTravelApiResponseJson}
            />
          )}
        {props?.showConnectedTravelV2 === connectedTravelVariants.INTRODUCING_CONNECTED_TRAVEL &&
          index === props.connectedTravelIndex && (
            <ConnectedTravelWidgetV2 connectedTravelData={props?.connectedTravelApiResponseJson} />
          )}
      </View>
      {bnplWidget}
    </>
  );
};

RailsListItem.propTypes = {
  item: PropTypes.object,
  index: PropTypes.any,
  fromSeo: PropTypes.any,
  fcStripIndex: PropTypes.number,
  fcStripText: PropTypes.any,
  showConnectedTravelV2: PropTypes.number,
  isOtherDayTrainsList: PropTypes.bool,
  onEmptyAvailabilityCardClicked: PropTypes.func,
  onAvailabilityCardClicked: PropTypes.func,
  tatkalExists: PropTypes.bool,
  onViewTrainDetailsClicked: PropTypes.func,
  railsShowConfirmationChance: PropTypes.bool,
  confirmationChances: PropTypes.object,
  originStation: PropTypes.object,
  destinationStation: PropTypes.object,
  departureDate: PropTypes.object,
  labels: PropTypes.object,
  actionSaveRailofyZcShown: PropTypes.func,
  nearbyDatesInfo: PropTypes.object,
  toggleNearbyDatesList: PropTypes.func,
  nearbyDatesCardClick: PropTypes.func,
  showAlternateAvailability: PropTypes.bool,
  connectedTravelIndex: PropTypes.number,
  connectedTravelApiResponseJson: PropTypes.object,
  prevSearchPosition: PropTypes.number,
  prevBookedPosition: PropTypes.number,
  bookedTrains: PropTypes.array,
  interactedTrains: PropTypes.array,
  bnplWidgetIndex: PropTypes.number,
};

const mapStateToProps: (state: DefaultRootState, ownProps: Props) => ReduxProps = (state, ownProps) => {
  const {
    railsListing: {
      showAlternateAvailability,
      railsShowConfirmationChance,
      confirmationChances,
      originStation,
      destinationStation,
      departureDate,
      covidSafetyCardData,
      confirmationGuaranteeConfig,
      availableQuotaList,
      trainsList,
      connectedTravelApiResponseJson,
      nearbyDatesInfo,
      interactedTrains,
      bookedTrains,
      prevSearchPosition,
      prevBookedPosition,
      prevSearchedHeading,
      prevBookedHeading,
      prevBookedCollapsedRoutes,
      notAvailableCard,
    },
    railsVernacular: { texts },
  } = state;

  const tatkalExists = !isEmpty(availableQuotaList) && verifyTatkalExists(availableQuotaList);
  const trainsCount = trainsList?.length;
  const bannerIndex =
    trainsCount >= DEFAULT_BANNER_INDEX ? DEFAULT_BANNER_INDEX - 1 : trainsCount - 1;
  const bannerAlternateIndex =
    trainsCount >= DEFAULT_BANNER_INDEX
      ? trainsCount === DEFAULT_BANNER_INDEX
        ? DEFAULT_BANNER_INDEX - 2
        : DEFAULT_BANNER_INDEX
      : trainsCount - 1;
  const bnplWidgetIndex =
      ownProps?.bnplWidgetIndex  ? ownProps?.bnplWidgetIndex : trainsCount >= 2 ? 2 : trainsCount - 1;


  return {
    texts,
    showAlternateAvailability,
    railsShowConfirmationChance,
    confirmationChances,
    originStation,
    destinationStation,
    departureDate,
    labels: getLabels(texts),
    covidSafetyCardData,
    confirmationGuaranteeConfig,
    bannerIndex,
    bannerAlternateIndex,
    bnplWidgetIndex,
    tatkalExists,
    trainsList,
    connectedTravelApiResponseJson,
    nearbyDatesInfo,
    interactedTrains,
    bookedTrains,
    prevSearchPosition,
    prevBookedPosition,
    prevSearchedHeading,
    prevBookedHeading,
    prevBookedCollapsedRoutes,
    notAvailableCard,
  };
};

const actionCreators = {
  onViewTrainDetailsClicked: onViewDetailsClicked,
  onEmptyAvailabilityCardClicked: updateCardClicked,
  onAvailabilityCardClicked: availabilityCardClicked,
  actionSaveRailofyZcShown: saveRailofyZcShown,
  toggleNearbyDatesList,
  nearbyDatesCardClick,
  setPrevBookedCollapsedState,
  resetPrevBookedCollapsedState,
};
const mapDispatchToProps = (dispatch) => bindActionCreators(actionCreators, dispatch);

export const RailsListItemV3 = connect<ReduxProps, null, Props>(
  mapStateToProps,
  mapDispatchToProps,
)(React.memo(RailsListItem));
