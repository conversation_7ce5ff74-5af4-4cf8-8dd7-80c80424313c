import React from 'react';
import { View, Text, StyleSheet, Image, Platform } from 'react-native';
import { configKeys } from '@mmt/rails/src/configStore/Common/constants';
import FastImage from 'react-native-fast-image';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import useConfigStore from '../../../configStore/Common/RailsConfigStoreUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getLineHeight } from '../../../vernacular/VernacularUtils';
import { Actions } from '../../../navigation/railsNavigation';
import {
  WHITE_BACK_ARROW_IMAGE_SOURCE,
  DONE_IMAGE_SOURCE,
  CANCEL_IMAGE_SOURCE,
  TERMS_AND_CONDITIONS_URL,
  TERMS_AND_CONDITIONS_HEADER_TEXT,
  OK,
  SEAT_LOCK,
  SEAT_LOCK_IMAGE_URI,
  HOW_SEAT_LOCK_WORKS_IMAGE_URI,
} from '@mmt/rails/src/Utils/RailsConstant';

interface Props {
  closeBottomSheet: () => void;
}
const RailsSeatLockContent = (props: Props) => {
  const railsListingHighLights = useConfigStore(configKeys.RAILS_LISTING_PAGE_MMT_HIGHLIGHTS);
  const { travelGuideLines = '', howThisWorks = [] } =
    railsListingHighLights?.mmtSeatLockConfigListing ?? {};

  const termsAndConditionOnClick = () => {
    Actions.openWebView({
      url: TERMS_AND_CONDITIONS_URL,
      headerText: TERMS_AND_CONDITIONS_HEADER_TEXT,
      headerIcon: WHITE_BACK_ARROW_IMAGE_SOURCE,
    });
  };

  return (
    <View style={styles.container} testID="seat_lock_bottomsheet_container">
      <View style={styles.headerContainer}>
        <Text style={[fontStyle('black'), styles.headingText]}>{SEAT_LOCK}</Text>
        <TouchableRipple
          onPress={props.closeBottomSheet}
          style={styles.crossIcon}
          testID="seat_lock_bottomsheet_close_button"
        >
          <Image source={CANCEL_IMAGE_SOURCE} style={styles.crossIcon} />
        </TouchableRipple>
      </View>
      <FastImage
        style={styles.imageSeatLockDiscount}
        source={{ uri: SEAT_LOCK_IMAGE_URI, priority: FastImage.priority.high }}
        resizeMode={FastImage.resizeMode.contain}
      />
      <FastImage
        style={styles.imageHowSeatLockWorks}
        source={{ uri: HOW_SEAT_LOCK_WORKS_IMAGE_URI, priority: FastImage.priority.high }}
        resizeMode={FastImage.resizeMode.contain}
      />
      <View style={styles.listContainer}>
        {howThisWorks.map((text: string, i: number) => (
          <View key={i} style={styles.listItem}>
            <Image source={DONE_IMAGE_SOURCE} style={styles.doneImage} />
            <Text style={[fontStyle('regular'), styles.listText]}>{text}</Text>
          </View>
        ))}
      </View>
      <View style={styles.travelGuideLines}>
        <Text style={[fontStyle('regular'), getLineHeight(14)]}>{travelGuideLines}</Text>
      </View>
      <TouchableRipple
        style={styles.linkContainer}
        onPress={termsAndConditionOnClick}
        testID="seat_lock_bottomsheet_terms_and_conditions_button"
      >
        <View style={Platform.OS === 'android' && styles.linkContainer}>
          <Text style={styles.link}>{TERMS_AND_CONDITIONS_HEADER_TEXT}</Text>
        </View>
      </TouchableRipple>
      <TouchableRipple
        style={styles.button}
        onPress={props.closeBottomSheet}
        testID="seat_lock_bottomsheet_ok_button"
      >
        <View style={Platform.OS === 'android' && styles.button}>
          <Text style={[styles.buttonText, fontStyle('black')]}>{OK}</Text>
        </View>
      </TouchableRipple>
    </View>
  );
};

export const RailsSeatLockBottomSheet = React.memo(RailsSeatLockContent);

export const styles = StyleSheet.create({
  container: {
    height: Platform.select({
      android: 600,
      ios: 590,
    }),
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headingText: {
    fontSize: 22,
  },
  headerContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginBottom: 15,
  },
  crossIcon: {
    height: 28,
    width: 28,
  },
  imageSeatLockDiscount: {
    width: '100%',
    height: 70,
    marginTop: 10,
  },
  imageHowSeatLockWorks: {
    width: '100%',
    height: 144,
    marginTop: 20,
  },
  listContainer: {
    marginTop: 20,
  },
  listItem: {
    flexDirection: 'row',
  },
  doneImage: {
    height: 15,
    width: 15,
    marginRight: 5,
  },
  listText: {
    color: colors.defaultTextColor,
  },
  travelGuideLines: {
    backgroundColor: colors.creamWhite,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginTop: 15,
    color: colors.defaultTextColor,
  },
  link: {
    color: colors.primary,
    fontSize: 12,
  },
  linkContainer: {
    marginTop: 10,
    marginBottom: 20,
  },
  button: {
    borderColor: colors.primary,
    padding: 15,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginTop: 10,
  },
  buttonText: {
    color: colors.primary,
  },
});
