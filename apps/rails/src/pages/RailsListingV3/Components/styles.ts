import { Platform, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  blurCard1: {
    position: 'absolute',
    width: '100%',
    top: -240,
    height: 100,
    padding: 1,
  },

  blurCard2: {
    position: 'absolute',
    width: '100%',
    top: -160,
    height: 250,
    marginBottom: 107,
    padding: 1,
  },

  apply: {
    position: 'relative',
    top: -170,
    elevation: 8,
  },

  applyButton: {
    borderRadius: 24,
    height: 40,
    width: 220,
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyButtonText: {
    color: colors.white,
    fontSize: 12,
    alignSelf: 'center',
  },
  bottomArrowImage: {
    width: 12,
    height: 12,
    marginLeft: 10,
    marginBottom: -2,
    padding: 6,
    alignSelf: 'center',
  },
  divider: {
    backgroundColor: colors.grayBg,
    height: 10,
  },
  bookedTrainscontainer: {
    width: '100%',
    backgroundColor: colors.grayBg,
    elevation: 2,
    position: 'relative',
  },
  bookedTrainsCardContainer: {
    padding: 4,
    backgroundColor: colors.grey22,
    borderRadius: 16,
    marginHorizontal: 12,
    marginBottom: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    ...Platform.select({
      android: {
        borderWidth: 1,
        borderColor: colors.greyLight2,
      },
    }),
  },
  bookedTrainsexpandedContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 12,
  },
  iconAndTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookedTrainsText: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.black,
    fontFamily: 'Lato',
    marginRight: 8,
    lineHeight: 16.8,
  },
  IconContainer: {
    width: 18,
    height: 18,
    marginRight: 10,
  },
  arwImg: {
    height: 20,
    width: 20,
  },
  container1: {
    width: '100%',
    backgroundColor: colors.grayBg,
    elevation: 2,
    position: 'relative',
  },
  cardContainer: {
    padding: 4,
    backgroundColor: colors.white,
    borderRadius: 16,
    marginHorizontal: 12,
    marginBottom: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0.5, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    ...Platform.select({
      android: {
        borderWidth: 1.5,
        borderColor: colors.greyLight2,
      },
    }),
  },
  expandContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 14,
  },
  headingText: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.textGrey,
    fontFamily: 'Lato',
    marginLeft: 6,
    marginBottom: 4,
    lineHeight: 16.8,
  },
  headingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
    marginHorizontal: 10,
  },
  arrowContainer: {
    width: 18,
    height: 18,
    marginTop: 16,
    marginLeft: 4,
  },
  arrowContainer2: {
    width: 18,
    height: 18,
    marginLeft: 4,
    marginBottom: 4,
  },
  stackPreview: {
    position: 'absolute',
    height: 6,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    borderTopEndRadius: -280,
    borderTopRightRadius: -280,
  },
  stackPreviewIOS: {
    borderColor: colors.greyLight2,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 3,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 1,
  },
  stackPreviewAndroid: {
    borderColor: colors.greyLight2,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 2,
    elevation: 2,
  },
});
