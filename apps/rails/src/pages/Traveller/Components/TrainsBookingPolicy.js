import React from 'react';
import PropTypes from 'prop-types';
import { Actions } from '../../../navigation';
import {Linking, ScrollView, Text, View, StyleSheet} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import styles from '../../User/Common/UserVerificationCSS';
import CloseButton from '../../User/Common/CloseButton';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import {_label} from '../../../vernacular/AppLanguage';

const dataStyles = StyleSheet.create({
  container: {
    ...styles.paddingTop14,
    ...styles.flex1Row,
  },
  counter: {
    ...styles.latoRegularText14,
  },
  text: {
    ...styles.latoRegularText14,
    paddingLeft: 16,
    textAlign: 'left',
  },
  listContainer: {
    marginLeft: 26,
    marginTop: 5,
    marginRight: 30,
  },
  listText: {
    marginTop: 5,
  },
  id: {
    fontWeight: 'bold',
  },
  linkText: {
    marginLeft: 26,
    marginTop: 5,
    color: colors.azureBlue,
  },
});

const List = ({ list = [] }) => {
  return (
    <View style={dataStyles.listContainer}>
      {
        list.map((item, index)=> {
          return (
            <Text key={item.replace(/ /g, '')} style={dataStyles.listText}>
              <Text style={dataStyles.id}>{`${String.fromCharCode((97 + index))}. `}</Text>
              <Text>{item}</Text>
            </Text>
          );
        })
      }
    </View>
  );
};
/* eslint-disable */
const Clickable = ({ link }) => {
  return (
    <View>
      <Text style={dataStyles.linkText} onPress={() => Linking.openURL(link.url)}>
        {link.text}
      </Text>
    </View>
  );
};
const Data = ({data, counter, list, links}) => (
  <>
    <View style={dataStyles.container}>
      <Text style={[dataStyles.counter, fontStyle('regular'), getLineHeight(14)]}>{`${counter}.`}</Text>
      <Text style={[dataStyles.text, fontStyle('regular'), getLineHeight(14)]}>
        {data}
      </Text>
    </View>
    {
      list && list.length && <List list={list} />
    }
    {
      links && links.length && links.map(item => <Clickable key={item.id} link={item} />)
    }
  </>
);

Data.propTypes = {
  data: PropTypes.string.isRequired,
  counter: PropTypes.number.isRequired,
  list: PropTypes.array,
  links: PropTypes.array,
};

List.propTypes = {
  list: PropTypes.array,
};

Clickable.propTypes = {
  link: PropTypes.object,
};

const HEADER_HEIGHT = 40;

export default class TrainsBookingPolicy extends React.Component {
  render() {
    return (
      <View style={{backgroundColor: colors.white, flex: 1}}>
        <Card
          style={stylesConst.container}
          elevation={0}
        >
          <View style={stylesConst.content}>
            <Text style={[stylesConst.title, fontStyle('bold','en'), getLineHeight(18)]}>{_label('booking_policy')}</Text>
          </View>
          <CloseButton onPress={() => { Actions.pop(); }} />
        </Card>


        <ScrollView bounces={false}>
            <View style={{
              paddingHorizontal: 16,
              paddingTop: 13,
              backgroundColor: colors.white,
            }}>
              <Text style={[styles.latoBoldText14, {color: colors.black04}]}>
                {_label('steps_to_retrieve_old_id')}
              </Text>
            </View>
            <View style={{
              paddingHorizontal: 16,
              paddingBottom: 16,
              display: 'flex',
            }}
            >
              <Data
                data={_label('policy_point_1')}
                counter={1}
              />

              <Data
                data={_label('policy_point_2')}
                counter={2}
              />

              <Data
                data={_label('policy_point_2')}
                counter={3}
              />
              <Data
                data={_label('policy_point_4')}
                counter={4}
              />

              <Data
                data={_label('policy_point_5')}
                counter={5}
                links={[
                  {
                    id: 'luggage_Rule',
                    url: 'http://www.indianrail.gov.in/luggage_Rule.html',
                    text: 'http://www.indianrail.gov.in/luggage_Rule.html',
                  },
                ]}
              />

              <Data
                data={_label('policy_point_6')}
                counter={6}
                links={[
                  {
                    id: 'indianrail',
                    url: 'http://www.indianrail.gov.in',
                    text: 'http://www.indianrail.gov.in',
                  },
                  {
                    id: 'irctc',
                    url: 'https://www.irctc.co.in',
                    text: 'https://www.irctc.co.in',
                  },
                ]}
              />

              <Data
                data={_label('policy_point_7')}
                counter={7}
              />
              <Data
                data={_label('policy_point_8')}
                counter={8}
                list={[
                  _label('policy_point_8_a'),
                  _label('policy_point_8_b'),
                  _label('policy_point_8_c'),
                ]}
              />
              <Data
                data={_label('policy_point_9')}
                counter={9}
              />
              <Data
                data={_label('policy_point_10')}
                counter={10}
              />

              <Data
                data={_label('policy_point_11')}
                counter={11}
              />
              <Data
                data={_label('policy_point_12')}
                counter={12}
              />
              <Data
                data={_label('policy_point_13')}
                counter={13}
              />
              <Data
                data={_label('policy_point_14')}
                counter={14}
              />


            <View style={stylesConst.divider}/>

          </View>
        </ScrollView>
      </View>
    );
  }
}

const stylesConst = {
  container: {
    flexDirection: 'row',
    height: HEADER_HEIGHT,
    backgroundColor: colors.azure,
  },
  content: {
    flexDirection: 'row',
    flex: 1,
    height: HEADER_HEIGHT,
    alignItems: 'center',
    paddingTop: 14,
    marginHorizontal: 16,
    paddingBottom: 14,
  },
  title: {
    color: colors.white,
    height: 27,
  },
  divider: {
    width: '100%',
    borderBottomColor: '#ededed',
    borderBottomWidth: (1.5),
    marginTop: 10,
  },
};
TrainsBookingPolicy.navigationOptions = {
  header: null,
};
