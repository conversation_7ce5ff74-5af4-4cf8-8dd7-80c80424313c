import React from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

const enabledColors = ['#53b2ff', '#065af3'];
const disabledColors = ['#989898', '#989898'];

const SaveOrSubmitButton = ({
  onSaveOrSubmit, buttonText, enableButton, id,
}) => (


  <View>
    <LinearGradient
      style={{
            overflow: 'hidden',
            borderRadius: 100,
            alignItems: 'center',
            justifyContent: 'center',
          }}
      colors={enableButton ? enabledColors : disabledColors}
      start={{
            x: 0,
            y: 2,
          }}
      end={{
            x: 1,
            y: 1,
          }}
    >
      <TouchableRipple onPress={onSaveOrSubmit}>
        <View style={styles.buttonBlue} testID={id}>
          <Text style={[styles.buttonText, fontStyle('bold'), getLineHeight(16)]}>{buttonText}</Text>
        </View>
      </TouchableRipple>

    </LinearGradient>
  </View>

);

SaveOrSubmitButton.propTypes = {
  buttonText: PropTypes.string.isRequired,
  onSaveOrSubmit: PropTypes.func.isRequired,
  enableButton: PropTypes.bool,
  id : PropTypes.string,
};

const styles = StyleSheet.create({

  buttonText: {
    color: colors.white,
    borderRadius: 100,
    fontSize: 16,
    minWidth: 125,
    textAlign: 'center',
  },
  buttonBlue: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
    width: 120,
    borderBottomRightRadius: 100,
    borderTopRightRadius: 100,
  },
});


export default SaveOrSubmitButton;
