import {StyleSheet} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../../vernacular/VernacularUtils';

export const textStyle = {
  getTitleWithItemSelectedFontStyle : () => {
      return fontStyle('regular');
  },
  getChangeTextStyleFontStyle : () => {
      return fontStyle('bold');
  },
  getDropDownTextFontStyle : () => {
      return fontStyle('light');
  },
};

export const dropDownStyles = StyleSheet.create({

  titleWithItemSelected: {
    width: '70 %',
    color: colors.grey,
    fontSize: 12,
  },

  headerStyle: {
    paddingVertical: 10,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 0,
  },

  contentAlignmentStyle: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  changeTextStyle: {
    fontSize: 14,
    letterSpacing: 0,
    color: colors.azure,
    paddingLeft: 15,
  },
  arrowImageStyle: {
    width: 18,
    height: 24,
  },
  dropDownItemStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    margin: 0,
    backgroundColor: colors.white,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.lightGrey14,
    borderRadius: 4,
  },

  dropDownText: {
    fontSize: 14,
    color: colors.blackGradient,
    letterSpacing: 0,
    lineHeight: 20,


  },


});
