import React, {Component} from 'react';
import {DeviceEventEmitter, Image, StyleSheet, Text, View} from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {performLogin, isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {LOGIN_EVENT} from '@mmt/legacy-commons/Common/constants/AppConstants';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';
import { isApp } from '../../../../Utils/device';
import { _label } from '../../../../vernacular/AppLanguage';
import PropTypes from 'prop-types';

import arrowImage from '@mmt/legacy-assets/src/arrow_cta.webp';
import travellerImage from '@mmt/legacy-assets/src/rails_login_group.webp';
let loginEventSubscription;

class LoginPersuassionCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      userLoggedIn: false,
      fromRailsList: props.fromRailsList,
    };
  }

  componentDidMount() {
    this.loadCardSequence();
    loginEventSubscription = DeviceEventEmitter?.addListener(LOGIN_EVENT, this.checkCardDataValidity);
  }

  componentWillUnmount() {
    loginEventSubscription?.remove();
  }

  checkCardDataValidity =  () => {
    this.loadCardSequence();
  };


  async loadCardSequence() {
    const userLoggedIn = await isUserLoggedIn();
    this.setState({userLoggedIn});
  }

  onItemClick = () => {
    performLogin(false, _label('login_persuassion_header'));
  };

  render() {
    const {referrer,loginStatus,subTitle1,subTitle2} = this.props;
    const isUserLoggedIn = this.state.userLoggedIn;
    const title = this.state.fromRailsList ? subTitle1 : subTitle2;
    const topMargin = this.state.fromRailsList ? 10 : 0;

    if (!isUserLoggedIn && isApp()) {
      return (
        <View style={cardStyle.container} testID={`${referrer}_LoginPersuassion`}>
          <TouchableRipple onPress={() => this.onItemClick()} feedbackColor="transparent">
            <View style={[cardStyle.cardContainer, { marginTop: topMargin }]} elevation={3}>
              <View style={{ flex: 4, flexDirection: 'row'}}>
                <Image source={travellerImage} style={{width: 40, height: 40, marginLeft: 16}} />
                <View>
                  <Text numberOfLines={1} ellipsizeMode="tail" style={[cardStyle.rewardCardTitle, fontStyle('bold'), getLineHeight(16)]} testID={this.props?.id}>
                    {loginStatus}
                  </Text>
                  <Text numberOfLines={1} ellipsizeMode="tail" style={[cardStyle.textSubtitle, fontStyle('bold'), getLineHeight(12)]}>{title}</Text>
                </View>
              </View>
              <Image source={arrowImage} style={{ flex: 1, marginRight: 30 }} />
            </View>
          </TouchableRipple>
        </View>
      );
    }
    return (
      <View testID={this.props?.id} />
    );
  }
}

const cardStyle = StyleSheet.create({
  container: {
    backgroundColor: colors.grayBg,
  },
  cardContainer: {
    flexDirection: 'row',
    height: 75,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: colors.creamWhite4,
    marginBottom: 10,
    alignItems: 'center',
    display: 'flex',

  },
  rewardCardTitle: {
    fontSize: 16,
    marginLeft: 10,
    color: colors.black,
  },
  textSubtitle: {
    marginLeft: 10,
    color: colors.black,
    marginTop: 8,
  },
});

LoginPersuassionCard.propTypes = {
  referrer: PropTypes.string,
  loginStatus: PropTypes.string,
  subTitle1: PropTypes.string,
  subTitle2: PropTypes.string,
  fromRailsList: PropTypes.bool,
  id: PropTypes.string,
};

export default LoginPersuassionCard;
