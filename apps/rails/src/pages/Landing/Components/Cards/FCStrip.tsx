import React from 'react';
import { connect } from 'react-redux';
import { View, Text, StyleSheet, Image } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import { setFreeCancellationOption } from 'apps/rails/src/pages/RailsLandingPage/Store/RailsLandingPageActions';
import { RAILS_IS_PRE_FC_SELECTED, setDataToAsyncStorage } from '../../../../Utils/RailsConstant';
import { trackFCTGEvar47or97Event, trackNewListingClickEvent } from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import ASSETS from '../../../../Utils/Assets/RailsAssets';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface Props {
  isFreeCancellationEnabled: boolean;
  onClick: () => void;
  fcStripText: {
    title: string;
    subTitle: string;
    title_selected: string;
    subTitle_selected: string;
  };
}

const FCStrip = (props: Props) => {
  if (!props?.fcStripText?.title) {
    return null;
  }

  const onClick = () => {
    props.onClick();
    //FC_listing_select or FC_listing_unselect
    trackFCTGEvar47or97Event(
      null,
      `${RAIL_EVENTS.FCTG.FC}${RAIL_EVENTS.FCTG.LISTING}${
        !props.isFreeCancellationEnabled ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
      }`,
      RAIL_EVENTS.FCTG.FC,
    );
    trackNewListingClickEvent(
      `rails_listing_fcstrip_${!props.isFreeCancellationEnabled ? 'selected' : 'not_selected'}`,
    );
    setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, !props.isFreeCancellationEnabled);
  };
  return (
    <>
      <View style={styles.border} testID="fc_strip_border" />
      <TouchableRipple onPress={onClick} testID="fc_strip_touchable_ripple">
        <View style={styles.container} testID="fc_strip_container">
          <View style={styles.checkBoxContainer} testID="fc_strip_check_box_container">
            <CheckBox
              isChecked={props.isFreeCancellationEnabled}
              textLine1=""
              onPress={onClick}
              testID="fc_strip_check_box"
            />
          </View>
          <View style={styles.zIndex}>
            <Text style={[styles.textBold, fontStyle('black')]} testID="fc_strip_text_bold">
              {props.isFreeCancellationEnabled
                ? props.fcStripText?.title_selected
                : props.fcStripText?.title}
            </Text>
            <Text style={styles.textLight} testID="fc_strip_text_light">
              {props.isFreeCancellationEnabled
                ? props.fcStripText?.subTitle_selected
                : props.fcStripText?.subTitle}
            </Text>
          </View>
          <Image
            source={props.isFreeCancellationEnabled ? ASSETS.fcSelected : ASSETS.fcNotSelected}
            resizeMode="cover"
            style={styles.image}
            testID="fc_strip_image"
          />
        </View>
      </TouchableRipple>
      <View style={styles.border} />
    </>
  );
};
const styles = StyleSheet.create({
  container: {
    paddingLeft: 20,
    height: 60,
    backgroundColor: colors.white,
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    height: 60,
    width: 150,
    position: 'absolute',
    right: 0,
  },
  textBold: {
    fontSize: 14,
    color: colors.black,
  },
  textLight: {
    fontSize: 12,
    color: colors.textGrey,
  },
  border: {
    height: 10,
    backgroundColor: colors.grayBg,
  },
  zIndex: {
    zIndex: 1000,
  },
  checkBoxContainer: {
    marginRight: 10,
  },
});

const mapStateToProps = (state: unknown) => {
  const { railsLanding: { isFreeCancellationEnabled = false } = {} } = state;
  return { isFreeCancellationEnabled };
};

const mapDispatchToProps = (dispatch: unknown) => ({
  onClick: () => dispatch(setFreeCancellationOption()),
});

export default connect(mapStateToProps, mapDispatchToProps)(FCStrip);
