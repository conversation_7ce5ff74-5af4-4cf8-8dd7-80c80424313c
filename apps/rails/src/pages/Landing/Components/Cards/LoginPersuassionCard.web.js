import React, {Component} from 'react';
import {DeviceEventEmitter, Image, StyleSheet, Text, View} from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {LOGIN_EVENT} from '@mmt/legacy-commons/Common/constants/AppConstants';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {withCookies} from 'react-cookie';
import arrowImage from '@mmt/legacy-assets/src/arrow_cta.webp';
import travellerImage from '@mmt/legacy-assets/src/rails_login_group.webp';
import {isPhonePePlatform} from '../../../../Utils/RisUtils';
import Loadable from 'react-loadable';
import {fontStyle,getLineHeight} from '../../../../vernacular/VernacularUtils';
import { isApp } from '../../../../Utils/device';
import PropTypes from 'prop-types';

let loginEventListener;

// lazyload PhonePe Login Component
const PhonePeLogin = Loadable({
    loader: () =>
        import('MMT-UI/phonePe/PhonePeLoginV2').then(
            (module) => module.PhonePeLogin,
        ),
    loading() {
        return '';
    },
});

class LoginPersuassionCard extends Component {
    constructor(props) {
        super(props);
        this.state = {
            userLoggedIn: false,
            fromRailsList: props.fromRailsList,
            openPhonePe: false,
        };
    }

    componentDidMount() {
        this.loadCardSequence();
        loginEventListener = DeviceEventEmitter?.addListener(LOGIN_EVENT, () => {
            this.checkCardDataValidity();
        });
    }

    componentWillUnmount() {
        loginEventListener?.remove();
    }

    checkCardDataValidity = async () => {
        await this.loadCardSequence();
    };

     _openLoginPage = (props) => {
         if (isPhonePePlatform()) {
             this.setState({openPhonePe: true});
         } else {
             const loginActionDetails = {
                 actionURL: window.location.href,
                 verifyOTP: false,
                 isOTPVerified: false,
                 openVerifiedPage: false,
                 optPageOpened: false,
             };
             const expires = new Date(Date.now() + 1200000);
             props.cookies.set('loginActionDetails', JSON.stringify(loginActionDetails), {
                 expires,
                 path: '/',
                 domain: '.makemytrip.com',
             });
             window.location.href = 'https://www.makemytrip.com/pwa/hlp/v3/login';
         }
    };

    _onPhonePeModalDismissed = () => {
       this.setState({openPhonePe: false});
    };

    _onLoginSuccess = () => {
        window.location.reload();
    };


    async loadCardSequence() {
        const userLoggedIn = await isUserLoggedIn();
        this.setState({userLoggedIn});
    }

    render() {
        const {referrer,loginStatus,subTitle1,subTitle2} = this.props;
        const isUserLoggedIn = this.state.userLoggedIn;
        const title = this.state.fromRailsList ? subTitle1 : subTitle2;
        const topMargin = this.state.fromRailsList ? 10 : 0;
        if (!isUserLoggedIn || (isApp() && !isPhonePePlatform())) {
            return (
                <View style={[cardStyle.container, {marginTop: topMargin}]}>
                    <TouchableRipple onPress={() => this._openLoginPage(this.props)} feedbackColor="transparent">
                        <View style={[cardStyle.cardContainer, {marginTop: topMargin}]} elevation={3}>
                            <Image source={travellerImage} style={cardStyle.loginUserIconStyle}/>
                            <View>
                                <Text style={[cardStyle.rewardCardTitle, fontStyle('bold'), getLineHeight(16)]}>
                                {loginStatus}

                                </Text>
                                <Text style={[cardStyle.textSubtitle, fontStyle('bold'), getLineHeight(12)]}>{title}</Text>
                            </View>
                            {this.state.openPhonePe && (
                                <PhonePeLogin
                                    referrer={referrer}
                                    initiateLogin={this.state.openPhonePe}
                                    onDismissed={this._onPhonePeModalDismissed}
                                    onPhonePeLogin={this._onLoginSuccess}
                                    showMmtLogin={false}
                                />
                            )}
                            <Image source={arrowImage} style={cardStyle.arrowIconStyle} />
                        </View>
                    </TouchableRipple>
                </View>
            );
        }
        return (
            <View />
        );
    }
}

const cardStyle = StyleSheet.create({
    container: {
        backgroundColor: colors.grayBg,
    },
    cardContainer: {
        flexDirection: 'row',
        height: 75,
        marginLeft: 10,
        backgroundColor: colors.creamWhite4,
        marginRight: 10,
        marginBottom: 10,
        alignItems: 'center',
    },
    rewardCardTitle: {
        fontSize: 16,
        marginLeft: 10,
        color: colors.black,
    },
    textSubtitle: {
        fontSize: 12,
        marginLeft: 10,
        color: colors.black,
        marginTop: 8,
    },
    arrowIconStyle : {
        width: 56,
        height: 56,
        marginLeft : 'auto',
        marginRight : 26,
    },
    loginUserIconStyle : {
        width: 40,
        height: 40,
        marginLeft: 16,
    },
});

LoginPersuassionCard.propTypes = {
    referrer: PropTypes.string,
    loginStatus: PropTypes.string,
    subTitle1: PropTypes.string,
    subTitle2: PropTypes.string,
    fromRailsList: PropTypes.bool,
    cookies: PropTypes.object,
  };

export default withCookies(LoginPersuassionCard);
