import React from 'react';
import PropTypes from 'prop-types';
import {Image, Text, View} from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import styles from '../Css/RailsLandingCSS';
import {getScreenWidth} from '../../Review/AvailabilityStatusChange/AvailabilityChangeUtils';
const getAppropriateFontSizeRIS = () => {
    const width = getScreenWidth();
    if (width < 360) {
      return 12;
    }
    else {
      return 14;
    }
};
const RisOptionsComponent = ({icon, iconText, onClick}) => (
  <View style={{flex: 1, maxWidth: '50%'}}>
    <Card style={{
      marginVertical: 5,
      marginHorizontal: 5,
      paddingHorizontal: 16,
      height: 50,
      justifyContent: 'center',
    }} showBorder>
      <TouchableRipple onPress={onClick}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Image style={{width: 30, height: 30}} source={icon} />
          <Text style={[styles.header, {marginLeft: 12, fontSize: getAppropriateFontSizeRIS()}]}>{iconText}</Text>
        </View>
      </TouchableRipple>
    </Card>
  </View>
);

RisOptionsComponent.propTypes = {
  icon: PropTypes.string.isRequired,
  iconText: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
};

export default RisOptionsComponent;
