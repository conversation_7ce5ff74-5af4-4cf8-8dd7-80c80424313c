import React from 'react';
import isNull from 'lodash/isNull';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { DeviceEventEmitter, Text, View, StyleSheet, Image } from 'react-native';
import PropTypes from 'prop-types';
import fecha from 'fecha';
import { DateItemListContainer, RailCityInput } from './RailsCityInput_v2';
import styles from '../Css/RailsLandingCSS';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import StaggeredView from '@mmt/legacy-commons/Common/Components/StaggeredView';
import calendarIcon from '@mmt/legacy-assets/src/ic_calendar_v2.webp';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { isAndroid, isIos } from '../../../Utils/device';

const CALENDAR_EVENT_RECEIVED = 'calendar_event_received';
let calendarEventListener;

class DateInput extends React.Component {
    calendarDateReceived = (selectedDate) => {
        let departDate = null;
        if (isIos()) {
            departDate = selectedDate.departureDate.split('-').join('/');
        } else {
            departDate = selectedDate.selectedDate;
        }
        const date = fecha.parse(departDate, 'DD/MM/YYYY');
        this.props.setDepartureDate(date);
    };

    componentDidMount() {
        calendarEventListener = DeviceEventEmitter?.addListener(CALENDAR_EVENT_RECEIVED, this.calendarDateReceived);
    }
    componentWillUnmount() {
        calendarEventListener?.remove();
    }

    onDateClick = () => {
        this.props.onDateClick();
    };

    render() {
        const { departureDate, labelText } = this.props;
        if (isNull(departureDate)) {
            return (
                <RailCityInput
                    id={this.props?.id}
                    placeholder="Enter Travel Date"
                    label="Date"
                    onClick={this.props.onDateClick}
                />
            );
        }
        const dateStr = fecha.format(departureDate, 'DD-MMM YYYY-dddd');
        const [date, monthYear, day] = dateStr.split('-');
        const [month] = monthYear.split(' ');
        return (
            <View style={style.container} testID={this.props?.id}>
                <TouchableRipple onPress={this.onDateClick}>
                    <View style={{...styles.dataComponent, paddingVertical:10}}>
                        <Image
                            source={calendarIcon}
                            style={style.calendarStyle}
                        />
                        <Text style={[style.dateComponentLabel, fontStyle('regular'), getLineHeight(12)]}>{labelText}</Text>
                        <StaggeredView>
                            <Text style={[style.dateStyle, fontStyle('bold'), getLineHeight(12)]}>{date} {month}</Text>
                            {/* <Text style={styles.month}>{year}</Text> */}
                            <Text style={[style.dayStyle, fontStyle('regular'), getLineHeight(12)]}>{day.substr(0, 3)}</Text>
                        </StaggeredView>
                    </View>
                </TouchableRipple>
                <View style={{ position: 'absolute', right: 12 }}>
                    <DateItemListContainer fromPage={this.props.fromPage} />
                </View>
            </View>
        );
    }
}

const dateStyle = {
    calendarStyle: { width: 24, height: 24, position: 'absolute',left:0, top: 5, marginLeft:10 },
    dateStyle: {
        fontSize: 14,
        color: colors.black04,
        lineHeight: 16,
        marginLeft:15,
        position:'absolute',
        top:5,
    },
    dayStyle: {
        fontSize: 12,
        color: colors.disabledBtnBg,
        lineHeight: 18,
        height:16,
        position:'absolute',
        top:5,
        left:60,
        paddingHorizontal:5,
    },
    dateComponentLabel: {
        fontSize: 12,
        color: colors.lightTextColor,
        marginBottom:5,
        marginLeft:15,
        width:60,
        height:20,
        position:'absolute',
        top:0,
        left:30,
    },
};

const androidDateStyle = {
    calendarStyle: { width: 24, height: 24, position: 'absolute',left:0, top: 15, marginLeft:10 },
    dateStyle: {
        fontSize: 14,
        color: colors.black04,
        lineHeight: 16,
        marginLeft:15,
        // width: 40,
        // height: 38
    },
    dayStyle: {
        fontSize: 12,
        color: colors.disabledBtnBg,
        lineHeight: 16,
    },
    dateComponentLabel: {
        fontSize: 12,
        color: colors.lightTextColor,
        marginBottom:5,
        marginLeft:15,
    },
};

const dateCalendarStyle = isAndroid() ? androidDateStyle : dateStyle;

const style = StyleSheet.create({
    container: {
        borderStyle: 'solid',
        borderColor:colors.grey12,
        borderWidth:1,
        margin: 10,
        marginTop:0,
        marginHorizontal:15,
        backgroundColor: colors.grey13,
        justifyContent: 'center',
        height:60,
    },
    ...dateCalendarStyle,
});


DateInput.propTypes = {
    departureDate: PropTypes.object,
    onDateClick: PropTypes.func.isRequired,
    setDepartureDate: PropTypes.func.isRequired,
    labelText: PropTypes.string.isRequired,
    fromPage: PropTypes.string,
    id: PropTypes.string,

};

DateInput.defaultProps = {
    departureDate: null,
};

export default DateInput;
