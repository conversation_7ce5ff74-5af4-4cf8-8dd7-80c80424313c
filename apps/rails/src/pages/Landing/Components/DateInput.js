import React from 'react';
import isNull from 'lodash/isNull';
import {DeviceEventEmitter, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import fecha from 'fecha';
import {DateItemListContainer, RailCityInput} from './RailsCityInput';
import styles from '../Css/RailsLandingCSS';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import StaggeredView from '@mmt/legacy-commons/Common/Components/StaggeredView';
import { isIos } from '../../../Utils/device';

const CALENDAR_EVENT_RECEIVED = 'calendar_event_received';
let calendarEventListener;
class DateInput extends React.Component {
  calendarDateReceived = (selectedDate) => {
    let departDate = null;
    if (isIos()) {
      departDate = selectedDate.departureDate.split('-').join('/');
    } else {
      departDate = selectedDate.selectedDate;
    }
    const date = fecha.parse(departDate, 'DD/MM/YYYY');
    this.props.setDepartureDate(date);
  };

  async componentDidMount() {
    calendarEventListener = DeviceEventEmitter?.addListener(CALENDAR_EVENT_RECEIVED, this.calendarDateReceived);
  }
  componentWillUnmount() {
    calendarEventListener?.remove();
  }

  onDateClick = () => {
    this.props.onDateClick();
  };

  render() {
    const {departureDate, labelText} = this.props;
    if (isNull(departureDate)) {
      return (
        <RailCityInput
          placeholder="Enter Travel Date"
          label="Date"
          onClick={this.props.onDateClick}
        />
      );
    }
    const dateStr = fecha.format(departureDate, 'DD-MMM YYYY-dddd');
    const [date, monthYear, day] = dateStr.split('-');
    return (
      <View style={{justifyContent: 'center'}}>
        <TouchableRipple onPress={this.onDateClick}>
          <View style={styles.dataComponent}>
            <Text style={styles.dateComponentLabel}>{labelText}</Text>
            <StaggeredView>
              <Text style={styles.date}>{date}</Text>
              <Text style={styles.month}>{monthYear}</Text>
              <Text style={styles.day}>{day}</Text>
            </StaggeredView>
          </View>
        </TouchableRipple>
        <View style={{position: 'absolute', right: 12}}>
          <DateItemListContainer />
        </View>
      </View>
    );
  }
}

DateInput.propTypes = {
  departureDate: PropTypes.object,
  onDateClick: PropTypes.func.isRequired,
  setDepartureDate: PropTypes.func.isRequired,
  labelText: PropTypes.string.isRequired,
};

DateInput.defaultProps = {
  departureDate: null,
};

export default DateInput;
