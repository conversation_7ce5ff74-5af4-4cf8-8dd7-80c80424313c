
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

const newRecentSearchCardStyles = StyleSheet.create({

    recentSearchContainerOld: {
        marginLeft: '3.5%',
    },
    recentSearchContainerNew: {
        marginLeft: '4.44%',
    },
    cardPnrRecentContainer: {
        width: 150,
        minHeight: 68,
        paddingHorizontal: 10,
        paddingVertical: 10,
        backgroundColor: colors.white,
        marginRight:10,
        borderRadius:16,
        borderWidth: 1,
        borderColor: colors.lightSilver,
    },
    pnrHeaderText: {
        fontSize: 14,
        color: colors.black,
        marginLeft:'1%',
        marginTop:'4%',
        fontWeight:'600',
        lineHeight:16,
    },
    pnrTextTitle: {
        fontSize: 12,
        fontWeight: '700',
        color: colors.textGrey,
        marginBottom:2,
    },
    pnrTextSubtitle: {
        fontSize: 10,
        fontWeight: '500',
        color: colors.textGrey,
    },
    pnrTextDate: {
        marginLeft: 5,
        fontSize: 8,
        color: colors.textGrey,
    },
    calendarContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 6,
    },
    messageIcon: {
        position: 'absolute',
        right: 13,
        top: 10,
        height: 13.5,
        width: 13.5,
    },
    image: {
        height: '100%',
        width: '100%',
    },
});

export default newRecentSearchCardStyles;
