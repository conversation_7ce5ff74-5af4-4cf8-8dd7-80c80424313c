import React, { useState, useEffect, useRef } from 'react';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {
  Text,
  View,
  Keyboard,
  Platform,
  Animated,
  TouchableOpacity,
  Image,
} from 'react-native';
import { CHECK_PNR, ENTER_PNR } from 'apps/rails/src/pages/TGS/Components/TGSConstants';
import TGSSearchButton from '../../TGS/Components/TGSSearchButton';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import { tgsLandingPageStyles, newPnrLandingStyles } from 'apps/rails/src/pages/TGS/Components/TGSStylesSheet.styles';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getPnrRecentSearch, validatePnr } from '../../../Utils/RisUtils';
import { RailsLandingTrackingHelper, RAILS_LANDING_PNR_PAGE_NAME } from '../Analytics';
import { Actions } from '../../../navigation';
import { pnrFaqs } from '../Common';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import { RIS_PNR_LANDING, RIS_PNR_LANDING_ADFEED1, RIS_PNR_LANDING_ADFEED2, RIS_PNR_LANDING_ADFEED3 } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { FaqExpandableList, RisCards } from '../../RisLiveStationForm/Components';
import { NewPnrLandingPageProps } from '../Interfaces';
import { getAllTypesAdsAb } from '../../Common/utils';
import useConfigStore from '../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../configStore/Common/constants';
import TgsFloatingVideo from './TgsFloatingVideo';
import { trackClickEventProp61, trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { omnitureMapping } from '@mmt/rails/src/pages/RIS/PnrStatus/TGSUtils/Constants';
import { removeDataFromAsyncStorage, WHY_BOOK_WITH_US } from 'apps/rails/src/Utils/RailsConstant';
import { USER_PNR_CONTEXT } from '@mmt/legacy-commons/AppState/LocalStorage';
import MMTHighlightsCarousel from 'apps/rails/src/configStore/Landing/MMTListingCarousel';
import TGStaticComponent from './TGStaticComponent';
import { GetTgFeedbackContainer as TGFeedback } from 'apps/rails/src/pages/RIS/PnrStatus/Components/TGS/NewTgsTravellersView';
import InputField from '@Frontend_Ui_Lib_App/InputField';

// import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

// const pnrPageTgBanner = ASSETS.tgPnrPageBanner;
import newLeftIcon from '@mmt/legacy-assets/src/tgs_pnr_left_icon.webp';
const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

const NewPnrLandingPage = (props: NewPnrLandingPageProps) => {
  const [recentSearchPnrHistory, setRecentSearchPnrHistory] = useState<unknown[]>([]);
  const [isMinimised, setIsMinimised] = useState(true);
  const scrollY = new Animated.Value(0);
  const [pnrNumber, setPNRNumber] = useState<string>(props.pnrNumber || '');
  const [showDiffTypeOfAds, setShowDiffTypeOfAds] = 
        useState({ multi_banner: 0, snackbar: 0, adfeed: 0, interstitial: 0 });
  const [_focusValue, setFocusVal] = useState<boolean>(false);
  const railsAdfeedTitle = useConfigStore(configKeys.RAILS_ADFEED_TITLE);
  const animation = useRef(new Animated.Value(0)).current;
  const [closeAnimationVideo, setCloseAnimationVideo] = useState(false);

  const mmtCarouselData = useConfigStore(configKeys.RAILS_LISTING_PAGE_MMT_HIGHLIGHTS);
  useEffect(() => {
    const getRecentSearchPnrHistory = async () => {
      const recentSearchPnr: unknown[] = await getPnrRecentSearch();
      if (recentSearchPnr) {
        setRecentSearchPnrHistory(recentSearchPnr);
      }
      const AdsAb = await getAllTypesAdsAb();
      setShowDiffTypeOfAds(AdsAb);
      trackAdLoad('mob:funnel:ris:pnr:pnrlanding', AdsAb.trackingPayload);
    };
    getRecentSearchPnrHistory();
    RailsLandingTrackingHelper.trackLandingPnrPageVisit();
    removeDataFromAsyncStorage(USER_PNR_CONTEXT);
  }, []);

  const onSearchClick = () => {
    const pnr = pnrNumber;
    Actions.pnrStatusPage({ pnr });
    RailsLandingTrackingHelper.trackClick(RAILS_LANDING_PNR_PAGE_NAME, 'ris_check_pnr_clicked');
  };

  const onChangeText = (text: string) => {
    const validText = validatePnr(text);
    if (validText.length === 10) {
      Keyboard.dismiss();
    }
    setPNRNumber(validText);
  };

  const handleScroll = (event: unknown) => {
    scrollY.setValue(event.nativeEvent.contentOffset.y);
  };

  const submitButtonDisabled = () => pnrNumber?.length !== 10;

  const onCtaPressed = () => {
    if (!submitButtonDisabled()) {
    onSearchClick();
    }
  };

  const onBack = () => {
    const { deeplink } = props;
    if (deeplink){
      Actions.railsBusCommonLanding(undefined,'replace');
    } else {
      Actions.pop();
    }
  };

  const toggleVideoSize = () => {
    if (!isMinimised) {
      setCloseAnimationVideo(true);
    }
    else if (isMinimised) {
      trackClickEventProp61(RAILS_LANDING_PNR_PAGE_NAME, omnitureMapping.PNR_LANDING_VIDEO_EXPAND_CLICKED);
    }

    Animated.timing(animation, {
      toValue: isMinimised ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start(() => setIsMinimised(!isMinimised));
  };

  const videoAspects = {
    width: animation.interpolate({
      inputRange: [0, 1],
      outputRange: ['25%', '91%'],
    }),
    height: animation.interpolate({
      inputRange: [0, 1],
      outputRange: ['22%', '40%'],
    }),
  };
  return (
    <>
      <Animated.ScrollView
        onScroll={handleScroll}
        stickyHeaderIndices={[0]}
        style={{ backgroundColor: colors.white }}
      >
        <View style={newPnrLandingStyles.gradientContainer}>
          <View style={newPnrLandingStyles.tgsWidetViewCss}>
            <TouchableRipple onPress={onBack} style={tgsLandingPageStyles.leftArrowContainer}>
              <View
                style={
                  Platform.OS === 'ios'
                    ? tgsLandingPageStyles.leftArrowContainerIos
                    : tgsLandingPageStyles.leftArrowContainer
                }
              >
                <Image
                  source={newLeftIcon}
                  style={newPnrLandingStyles.image}
                  resizeMode="contain"
                />
              </View>
            </TouchableRipple>
            <Text style={[fontStyle('black'), newPnrLandingStyles.tripGuranteeText]}>
              {CHECK_PNR}
            </Text>
          </View>
          <View style={newPnrLandingStyles.pnrContainer}>
            <Text style={[newPnrLandingStyles.pnrText, fontStyle('semiBold')]}>{ENTER_PNR}</Text>
            <InputField
              label={''}
              placeholder={'Eg.: 8947502345'}
              placeholderTextColor={colors.lightTextColor}
              inputProps={{
                maxLength: 10,
                keyboardType: 'phone-pad',
                multiline: true,
              }}
              onFocus={() => setFocusVal(true)}
              onBlur={() => setFocusVal(false)}
              value={pnrNumber}
              onChangeText={onChangeText}
              customStyle={{
                wrapperStyle: newPnrLandingStyles.marB20,
                inputFieldStyle: [
                  newPnrLandingStyles.inputText,
                  fontStyle('bold'),
                  getLineHeight(16),
                ],
              }}
            />
            <View>
              <TGSSearchButton
                label={'Submit'}
                onPress={onCtaPressed}
                disabled={submitButtonDisabled()}
                buttonStyle={newPnrLandingStyles.button}
              />
            </View>
          </View>
        </View>
        {recentSearchPnrHistory.length !== 0 && (
          <RisCards recentSearchItems={recentSearchPnrHistory} option={'PNR'} />
        )}

        <View style={newPnrLandingStyles.pnrSearchTgsPnrView}>
          <Text style={newPnrLandingStyles.bookWithUsHeading}>{WHY_BOOK_WITH_US}</Text>
          <MMTHighlightsCarousel
            mmtlistingHighlights={mmtCarouselData?.mmtlistingHighlights}
            appVersion={'9.0.3'}
            showBnpp={false}
            isPnrSearchPage={true}
          />
          <TGStaticComponent isStatusPage={true} />
          <View style={newPnrLandingStyles.feedbackContainer}>
            <TGFeedback />
          </View>
          {!!showDiffTypeOfAds.adfeed && (
            <View style={newPnrLandingStyles.pnrAdFeed1Container}>
              <View style={newPnrLandingStyles.marginHorizontal16}>
                {getAdsCard(Platform.OS, RIS_PNR_LANDING_ADFEED2)}
              </View>
              <View style={newPnrLandingStyles.adsSeparator} />
            </View>
          )}
          {!!showDiffTypeOfAds.multi_banner && (
            <View style={newPnrLandingStyles.pnrAdFeed2Container}>
              {railsAdfeedTitle.Adfeed_title && (
                <Text style={[newPnrLandingStyles.adsHeading, fontStyle('latoBlack')]}>
                  {railsAdfeedTitle.Adfeed_title[0].title_1}
                </Text>
              )}
              <View style={newPnrLandingStyles.marginHorizontal16}>
                {getAdsCard(Platform.OS, RIS_PNR_LANDING)}
              </View>
              <View style={newPnrLandingStyles.adsSeparator} />
            </View>
          )}
          {!!showDiffTypeOfAds.adfeed && (
            <View style={{ marginBottom: 30 }}>
              <View style={newPnrLandingStyles.pnrAdFeed2Container}>
                {railsAdfeedTitle.Adfeed_title && (
                  <Text style={[newPnrLandingStyles.adsHeading, fontStyle('latoBlack')]}>
                    {railsAdfeedTitle.Adfeed_title[0].title_2}
                  </Text>
                )}
                <View style={newPnrLandingStyles.marginHorizontal16}>
                  {getAdsCard(Platform.OS, RIS_PNR_LANDING_ADFEED1)}
                </View>
                <View style={newPnrLandingStyles.adsSeparator} />
              </View>
              <View style={newPnrLandingStyles.pnrAdFeed2Container}>
                {railsAdfeedTitle.Adfeed_title && (
                  <Text style={[newPnrLandingStyles.adsHeading, fontStyle('latoBlack')]}>
                    {railsAdfeedTitle.Adfeed_title[0].title_4}
                  </Text>
                )}
                <View style={newPnrLandingStyles.marginHorizontal16}>
                  {getAdsCard(Platform.OS, RIS_PNR_LANDING_ADFEED3)}
                </View>
                <View style={newPnrLandingStyles.adsSeparator} />
              </View>
            </View>
          )}
          {Platform.OS === 'web' && <FaqExpandableList faqList={pnrFaqs} />}
        </View>
      </Animated.ScrollView>
      {!closeAnimationVideo && (
        <AnimatedTouchable style={[newPnrLandingStyles.videoContainer, videoAspects]}>
          <TgsFloatingVideo toggleVideoSize={toggleVideoSize} isMinimised={isMinimised} />
        </AnimatedTouchable>
      )}
    </>
  );
};

export default NewPnrLandingPage;
