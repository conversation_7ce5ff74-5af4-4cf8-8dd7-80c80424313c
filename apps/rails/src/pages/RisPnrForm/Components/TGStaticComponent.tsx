import React from 'react';
import { isEmpty } from 'lodash';
import { Image, Text, View, Platform, Clipboard, StyleSheet } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import { styles } from 'apps/rails/src/pages/TravelerDetails/Components/AssuredConfirmation/AssuredConfirmationUtils';
import { configKeys } from '../../../configStore/Common/constants';
import useConfigStore from '../../../configStore/Common/RailsConfigStoreUtils';
import { DEFAULT_TG_MARKETING_COUPON, THREE_TIMES_REFUND } from 'apps/rails/src/Utils/RailsConstant';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';

import percentageIcon from '@mmt/legacy-assets/src/TGS/Tg_marketing_percentage.webp';
import copyIconBlue from '@mmt/legacy-assets/src/pnrStatusPageCopyButton.webp';
import PropTypes from 'prop-types';

const TGStaticComponent = (props: unknown) => {
    const tg_marketing_content = useConfigStore(configKeys.RAILS_TG_MARKETING_CONTENT);
    const coupon_discount_text = useConfigStore(configKeys.RAILS_TG_MARKETING_COUPON_DISCOUNT);
    const tg_offer_default = useConfigStore(configKeys.RAILS_TG_MARKETING_COUPON_DEFAULT);
    const tgConfig = tg_marketing_content?.TG;
    const couponCode = props?.couponCode;
    const couponText = isEmpty(couponCode) ?
      `${tg_offer_default}` : `${coupon_discount_text} ${couponCode}`;

    const copyPnr = () => {
      if (Platform.OS === 'web') {
        navigator.clipboard.writeText('Copy this text to clipboard');
      }
      else {
        if (!isEmpty(couponCode)) {
          Clipboard?.setString(couponCode);
        showShortToast(`${couponCode} copied!`);
        } else {
        Clipboard?.setString(DEFAULT_TG_MARKETING_COUPON);
        showShortToast(`${DEFAULT_TG_MARKETING_COUPON} copied!`);
        }
      }
    };
    // eslint-disable-next-line
    const RefundContainer = React.memo(() => {
      if (tgConfig?.refunds) {
        return (
            tgConfig?.refunds.map((refund: unknown, index: unknown) => {
            return (
              <>
                <View style={styles.refundContainer}>
                  <LinearGradient
                    colors={[colors.grey5, colors.grey7, colors.lightGrey]}
                    start={{ x: 0.0, y: 0.0 }}
                    end={{ x: 1.0, y: 0.0 }}
                    style={styles.verticalGradient}
                  >
                    <View style={styles.refundLabelContainer}>
                      <Text style={[styles.refundLabel, fontStyle('regular')]}>{refund.label}</Text>
                    </View>
                  </LinearGradient>
                  <View style={styles.fareContainer}>
                    <Text style={index === 0 ? ([internalStyles.fareTitle, fontStyle('bold')]) : ([styles.fareTitle, fontStyle('bold')])}>
                      {refund.title}
                    </Text>
                    <Text style={[styles.fareDescription, fontStyle('regular')]}>
                      {refund.description}
                    </Text>
                    <Text style={[styles.refundMode, fontStyle('regular')]}>{refund.refundMode}</Text>
                  </View>
                </View>
                {(index === 0) && (<Text style={internalStyles.plusIcon}>{'+'}</Text>)}
              </>
            );
          })
        );
      } else {
        return <></>;
      }
    });
    // eslint-disable-next-line
    const CouponContainer = React.memo(() => {
      return (
        !props.isStatusPage && (
          <View style={internalStyles.couponContainer}>
            <View style={internalStyles.percentageIconContainer}>
              <Image source={percentageIcon} style={internalStyles.percentageIcon} />
            </View>
          <Text style={internalStyles.couponText} numberOfLines={1} ellipsizeMode="tail">
                {`${couponText}`}
              </Text>
            <TouchableRipple onPress={copyPnr} >
              <View>
                <Image source={copyIconBlue} style={internalStyles.shareIconStyle}/>
              </View>
            </TouchableRipple>
          </View>
        )
      );
    });

    return (
        <View style={internalStyles.fcTgStaticContainer}>
          <LinearGradient
            colors={['#c86dd7', '#3023ae']}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
        style={internalStyles.gradientContainer}
          >
            <Text style={[internalStyles.header, fontStyle('bold')]}>{tgConfig?.header}</Text>
            <Text style={[internalStyles.description, fontStyle('bold')]}>{tgConfig?.subHeader}</Text>
          </LinearGradient>
          <View>
            <Text style={internalStyles.refundText3x}>{THREE_TIMES_REFUND}</Text>
          </View>
          <View style={[styles.contentDataContainer, styles.flex0]}>
            <View style={styles.refundsContainer}>
              <RefundContainer />
            </View>
            <View style={internalStyles.usageTextContainer}>
                <Text style={internalStyles.usageText}>
                        {tgConfig?.usages[0]}
                </Text>
            </View>
            <CouponContainer />
          </View>
        </View>
      );
};

const internalStyles = StyleSheet.create({
  fcTgStaticContainer: {
    paddingBottom: 6,
    backgroundColor: colors.white,
    marginBottom: 10,
    borderRadius: 12,
    marginHorizontal: 15,
    overflow: 'hidden',
    borderColor: colors.lightGrey,
    borderWidth: 1,
  },
  shareIconStyle: {
    marginLeft: 5,
    marginRight: 13,
    marginTop: 3,
    width: 16,
    height: 18,
    justifyContent: 'center',
    paddingTop: 2,
    paddingBottom: 3,
  },
  couponContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
    marginTop: 10,
    height: 'auto',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    backgroundColor: colors.lightGreen17,
    marginBottom: -5,
    paddingTop: 7,
    paddingBottom: 7,
    paddingHorizontal: 10,
    marginLeft: -10,
    flexWrap: 'wrap',
  },
  percentageIconContainer: {
    marginLeft: 15,
    marginRight: -5,
  },
  percentageIcon: {
    width: 25,
    height: 25,
    marginRight: 5,
  },
  couponText: {
    fontSize: 16,
    fontWeight: 'bold',
    width: 'auto',
    flex: 4,
    textAlign: 'center',
  },
  usageTextContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 7,
    marginBottom: 3,
  },
  usageText: {
    fontSize: 14,
    color: colors.greyText1,
    fontWeight: '400',
  },
  refundText3x: {
    marginTop: 6,
    fontWeight: '700',
    fontFamily: fonts.regular,
    fontSize: 14,
    marginLeft: 10,
    color: colors.black,
  },
  header: {
    fontSize: 15,
    marginTop: 3,
    marginBottom: 3,
    color: colors.white,
  },
  description: {
    fontSize: 13,
    marginTop: 3,
    marginBottom: 3,
    color: colors.white,
  },
  plusIcon: {
    marginTop: 25,
    fontSize: 16,
  },
  fareTitle: {
    fontSize: 12,
    color: colors.black,
    fontWeight: '900',
    marginBottom: 2,
  },
  gradientContainer: {
    paddingVertical: 13,
    paddingLeft: 16,
    paddingRight: 15,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
});

TGStaticComponent.propTypes = {
  isStatusPage: PropTypes.bool, 
};

export default React.memo(TGStaticComponent);
