import React, { useState, useCallback } from 'react';
import { StyleSheet, TouchableOpacity, Image } from 'react-native';
import Video from 'react-native-video';
import { VIDEO_URL } from '../../TGS/Components/TGSConstants';
import { useFocusEffect } from '@react-navigation/native';
import crossButton from '@mmt/legacy-assets/src/tgs_video_cross.webp';
import fullScreenButton from '@mmt/legacy-assets/src/tgs_video_fullscreen.webp';
import muteButton from '@mmt/legacy-assets/src/tgs_video_mute.webp';
import unMuteButton from '@mmt/legacy-assets/src/tgs_video_unmute.webp';
import PropTypes from 'prop-types';

const TgsFloatingVideo = ( { toggleVideoSize, isMinimised } ) => {
  const [muteVideo, setMuteVideo] = useState(true);
  const [pauseVideo, setPauseVideo] = useState(false);

  const handleVolumeClick = () => {
    setMuteVideo(!muteVideo);
  };

  const handleToggleVideoSizeClick = () => {
    toggleVideoSize();
    setMuteVideo(!isMinimised);
  };

  useFocusEffect(
    useCallback(() => {
      setPauseVideo(false);
      return () => {
        setPauseVideo(true);
      };
    }, []),
  );

  return (
    <TouchableOpacity activeOpacity={1} style={styles.container}>
      <Video
        source={{uri:VIDEO_URL}}
        style={styles.fullscreenVideo}
        resizeMode="cover"
        paused={pauseVideo}
        repeat={true}
        muted={muteVideo}
        ignoreSilentSwitch="obey"
        activeOpacity={0}
      />
      <TouchableOpacity style={isMinimised ? styles.minButton : styles.maxButton} onPress={handleToggleVideoSizeClick}>
        <Image resizeMode="contain" style={styles.image} source={isMinimised ? fullScreenButton : crossButton}/>
      </TouchableOpacity>
      <TouchableOpacity style={isMinimised ? styles.minVolume : styles.maxVolume} onPress={handleVolumeClick}>
        <Image resizeMode="contain" style={styles.image} source={muteVideo ? muteButton : unMuteButton}/>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullscreenVideo: {
    width: '100%',
    height: '100%',
  },
  maxButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    height: 32,
    width: 32,
  },
  minButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    height: 20,
    width: 20,
  },
  maxVolume: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    height: 32,
    width: 32,
  },
  minVolume: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    height: 20,
    width: 20,
  },
  image: {
    height: '100%',
    width: '100%',
  },
});

TgsFloatingVideo.propTypes = {
  toggleVideoSize: PropTypes.func,
  isMinimised: PropTypes.bool,
};

export default TgsFloatingVideo;
