import React, { useState, useEffect } from 'react';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { Text, View, Keyboard, Platform, Image, Animated } from 'react-native';
import { LeftArrow, CHECK_PNR, ENTER_PNR } from 'apps/rails/src/pages/TGS/Components/TGSConstants';
import { omnitureKeys } from '../../RisLiveTrainStatusForm/Common/index';
import { importedStyles } from '../Styles';
import { TGSScrollView } from 'apps/rails/src/pages/TGS/Components/TGSCarousel';
import TGSTestimonalFlow from 'apps/rails/src/pages/TGS/Components/TGSTestimonalFlow';
import TGSSearchButton from '../../TGS/Components/TGSSearchButton';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import { tgsLandingPageStyles, tgsPnrStyles } from 'apps/rails/src/pages/TGS/Components/TGSStylesSheet.styles';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getPnrRecentSearch, validatePnr } from '../../../Utils/RisUtils';
import { RailsLandingTrackingHelper, RAILS_LANDING_PNR_PAGE_NAME } from '../Analytics';
import { Actions } from '../../../navigation';
import { pnrFaqs } from '../Common';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import { RIS_PNR_LANDING, RIS_PNR_LANDING_SNACKBAR, RIS_PNR_LANDING_ADFEED1, RIS_PNR_LANDING_ADFEED2, RIS_PNR_LANDING_ADFEED3 } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { FaqExpandableList, RisCards } from '../../RisLiveStationForm/Components';
import { RisPnrSearchFormProps } from '../Interfaces';
import { getAllTypesAdsAb } from '../../Common/utils';
import { trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import useConfigStore from '../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../configStore/Common/constants';
import { LtsCard } from './';
import { USER_PNR_CONTEXT } from '@mmt/legacy-commons/AppState/LocalStorage';
import { removeDataFromAsyncStorage } from '../../../Utils/RailsConstant';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import InputField from '@Frontend_Ui_Lib_App/InputField';

const tgBanner = ASSETS.tgsTgBanner;
const RisPnrSearchForm = (props: RisPnrSearchFormProps) => {
  const [recentSearchPnrHistory, setRecentSearchPnrHistory] = useState<unknown[]>([]);
  const scrollY = new Animated.Value(0);
  const translateY = scrollY.interpolate({
    inputRange: [0, 190],
    outputRange: [190, 0],
    extrapolate: 'clamp',
  });

  const translateEmptyPNRResults = scrollY.interpolate({
    inputRange: [0, 70],
    outputRange: [70, 0],
    extrapolate: 'clamp',
  });

  const [pnrNumber, setPNRNumber] = useState<string>(props.pnrNumber || '');
  const [_focusValue, setFocusVal] = useState<boolean>(false);
  const [showDiffTypeOfAds, setShowDiffTypeOfAds] = 
        useState({ multi_banner: 0, snackbar: 0, adfeed: 0, interstitial: 0 });
  const railsAdfeedTitle = useConfigStore(configKeys.RAILS_ADFEED_TITLE);

  useEffect(() => {
    const getRecentSearchPnrHistory = async () => {
      const recentSearchPnr: unknown[] = await getPnrRecentSearch();
      if (recentSearchPnr) {
        setRecentSearchPnrHistory(recentSearchPnr);
      }
      const AdsAb = await getAllTypesAdsAb();
      setShowDiffTypeOfAds(AdsAb);
      trackAdLoad('mob:funnel:ris:pnr:pnrlanding', AdsAb.trackingPayload);
    };
    getRecentSearchPnrHistory();
    RailsLandingTrackingHelper.trackLandingPnrPageVisit();
    removeDataFromAsyncStorage(USER_PNR_CONTEXT);
  }, []);

  const onSearchClick = () => {
    const pnr = pnrNumber;
    Actions.pnrStatusPage({ pnr });
    // Actions.replace({ key: 'pnrStatusPage', params: { pnr } });
    RailsLandingTrackingHelper.trackClick(RAILS_LANDING_PNR_PAGE_NAME, 'ris_check_pnr_clicked');
  };

  const onChangeText = (text: string) => {
    const validText = validatePnr(text);
    if (validText.length === 10) {
      Keyboard.dismiss();
    }
    setPNRNumber(validText);
  };

  const handleScroll = (event: unknown) => {
    scrollY.setValue(event.nativeEvent.contentOffset.y);
  };

  const submitButtonDisabled = () => pnrNumber?.length !== 10;

  const onCtaPressed = () => {
    onSearchClick();
  };

  const onBack = () => {
    const { deeplink } = props;
    if (deeplink){
      Actions.railsBusCommonLanding(undefined,'replace');
    } else {
      Actions.pop();
    }
  };

  return (
    <>
      <Animated.ScrollView
        onScroll={handleScroll}
        stickyHeaderIndices={[0]}
        style={{ backgroundColor: colors.grey11 }}
      >
        <View style={tgsLandingPageStyles.container}>
          <LinearGradient
            colors={['#7C7792', '#1F1C2C']}
            start={{ x: 0.0, y: 1.0 }}
            end={{ x: 1.0, y: 0.0 }}
            style={tgsLandingPageStyles.gradientContainer}
          >
            <View style={tgsLandingPageStyles.tgsWidetViewCss}>
              <TouchableRipple onPress={onBack} style={Platform.OS === 'ios' ? tgsLandingPageStyles.leftArrowIos : tgsLandingPageStyles.leftArrowContainer}>
                <View  style={Platform.OS === 'ios' ? tgsLandingPageStyles.leftArrowContainerIos : tgsLandingPageStyles.leftArrowContainer}>
                <LeftArrow height={34} width={34}  />
                </View>
              </TouchableRipple>
              <Text style={[fontStyle('black'), tgsLandingPageStyles.tripGuranteeText, { marginRight: 20 }]}>{CHECK_PNR}</Text>
            </View>
            <View style={tgsLandingPageStyles.pnrContainer}>
              <Text style={[tgsLandingPageStyles.pnrText, fontStyle('semiBold')]}>{ENTER_PNR}</Text>
              <InputField
                label={''}
                placeholder={'Eg.: 8947502345'}
                placeholderTextColor={colors.disabledButton}
                inputProps={{
                  maxLength: 10,
                  keyboardType: 'phone-pad',
                }}
                onFocus={() => setFocusVal(true)}
                onBlur={() => setFocusVal(false)}
                value={pnrNumber}
                onChangeText={onChangeText}
                customStyle={{
                  wrapperStyle: tgsLandingPageStyles.marB20,
                  inputFieldStyle: [fontStyle('bold'), getLineHeight(16)],
                }}
              />
              <View>
                <TGSSearchButton label={'Submit'} onPress={onCtaPressed} disabled={submitButtonDisabled()} buttonStyle={tgsLandingPageStyles.buttonStyle} />
              </View>
            </View>
          </LinearGradient>
        </View>
        {recentSearchPnrHistory.length !== 0 ? <Animated.View
          style={{
            height: translateY,
          }}
        ><LinearGradient
          useAngle={true} angle={350.44}
          colors={['#7C7792', '#1F1C2C']}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 0.0 }}
          style={tgsPnrStyles.pnrAnimation}
        >
            <RisCards recentSearchItems={recentSearchPnrHistory} option={'PNR'} />
          </LinearGradient>
        </Animated.View> :
          <Animated.View
            style={{
              height: translateEmptyPNRResults,
            }}
          ><LinearGradient
            useAngle={true} angle={350.44}
            colors={['#7C7792', '#1F1C2C']}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
            style={tgsPnrStyles.emptyPnrAnimation}
          >
              <View />
            </LinearGradient>
          </Animated.View>
        }
        <View style={[tgsPnrStyles.tgsPnrView, 
          { top: recentSearchPnrHistory.length === 0 ? -65 : -50, borderRadius: 8, paddingTop: 10 }]}>
          <View style={tgsLandingPageStyles.pnrBottomSection}>
            <View style={tgsLandingPageStyles.pnrImage}>
              <Image style={tgsLandingPageStyles.tgBanner} source={tgBanner} resizeMode="contain" />
            </View>
            <View style={tgsLandingPageStyles.pnrScrollView}>
              <TGSScrollView />
            </View>
            <View style={tgsLandingPageStyles.pnrTestimonalFlow}>
              <TGSTestimonalFlow />
            </View>
          </View>
          <LtsCard tryNow componentLocation="PNRLP" omnitureKey={omnitureKeys.LTSoffline_card_click_PNRLP} 
          pageName={RAILS_LANDING_PNR_PAGE_NAME} />
          {!!showDiffTypeOfAds.adfeed &&
            <View style={importedStyles.adfeed2Container}>
              <View >{getAdsCard(Platform.OS, RIS_PNR_LANDING_ADFEED2)}</View>
            </View>
          }
          {!!showDiffTypeOfAds.multi_banner && <View style={importedStyles.multiAdContainer}>
            {railsAdfeedTitle.Adfeed_title && 
            <Text style={importedStyles.adsHeading}>
              {railsAdfeedTitle.Adfeed_title[0].title_1}
            </Text>}
            <View >{getAdsCard(Platform.OS, RIS_PNR_LANDING)}</View>
          </View>}
          {!!showDiffTypeOfAds.adfeed && <View style={{ marginBottom: 30 }}>
            <View style={importedStyles.adfeed1Container}>
              {railsAdfeedTitle.Adfeed_title && 
              <Text style={importedStyles.adsHeading}>
                {railsAdfeedTitle.Adfeed_title[0].title_2}
              </Text>}
              <View >{getAdsCard(Platform.OS, RIS_PNR_LANDING_ADFEED1)}</View>
            </View>
            <View style={importedStyles.multiAdContainer}>
              {railsAdfeedTitle.Adfeed_title && 
              <Text style={importedStyles.adsHeading}>
                {railsAdfeedTitle.Adfeed_title[0].title_4}
              </Text>}
              <View >{getAdsCard(Platform.OS, RIS_PNR_LANDING_ADFEED3)}</View>
            </View>
          </View>}
          {Platform.OS === 'web' && <FaqExpandableList faqList={pnrFaqs} />}
        </View>
      </Animated.ScrollView>
      {!!showDiffTypeOfAds.snackbar && <View >{getAdsCard(Platform.OS, RIS_PNR_LANDING_SNACKBAR)}</View>}
    </>
  );
};

export default RisPnrSearchForm;

