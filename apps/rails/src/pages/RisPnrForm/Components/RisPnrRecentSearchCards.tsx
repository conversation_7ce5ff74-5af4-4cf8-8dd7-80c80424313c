import React from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import { importedStyles } from '../Styles';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import { getPnrRecentCardDateFormat } from '../../../Utils/RisUtils';
import { Actions } from '../../../navigation';
import { RisPnrRecentSearchCardsProps } from '../Interfaces';
import newRecentSearchesCard from '../Styles/NewRecentSearchesCard.styles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { getPNRPageConfig } from 'apps/rails/src/RailsAbConfig';

import iconMessage from '@mmt/legacy-assets/src/TGS/reload-time-outline.webp';
import iconCalender from '@mmt/legacy-assets/src/TGS/uiw_date.webp';
import newIconMessage from '@mmt/legacy-assets/src/tgs_pnr_message.webp';
import newIconCalender from '@mmt/legacy-assets/src/tgs_pnr_calendar.webp';
const headerText = 'Recent PNR Searches';

const RisPnrRecentSearchCards = (props: RisPnrRecentSearchCardsProps) => {
  const {recentSearchPnrHistory = [] } = props;
  const isOldPnrPage = getPNRPageConfig();

  const onItemClick = (item) => {
    const pnr = item.pnrNumber;
    Actions.pnrStatusPage({ pnr });
    // Actions.replace({ key: 'pnrStatusPage', params: { pnr } });
  };

  const renderCardData = ({ item }) => (
    isOldPnrPage ?
    (
      <TouchableRipple onPress={() => onItemClick(item)} key={item.pnrNumber}>
        <View style={importedStyles.cardPnrRecentContainer}>
            <Image style={importedStyles.messageIcon} source={iconMessage} resizeMode="contain" />
            <Text style={importedStyles.pnrTextTitle} numberOfLines={1}>
              {item.pnrNumber}
            </Text>
            <Text style={importedStyles.pnrTextSubtitle} numberOfLines={1}>
              {'Trip to '}
              {item.destCityName}
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 14 }}>
              <Image style={importedStyles.calenderIcon} source={iconCalender} resizeMode="contain" />
              <Text style={importedStyles.pnrTextDate} numberOfLines={1}>
                {getPnrRecentCardDateFormat(item.travelStartDate)}
              </Text>
            </View>
        </View>
      </TouchableRipple>
    ) : (
      <TouchableOpacity onPress={() => onItemClick(item)} key={item.pnrNumber}>
          <View style={newRecentSearchesCard.cardPnrRecentContainer}>
              <View style={newRecentSearchesCard.messageIcon}>
                <Image style={newRecentSearchesCard.image} source={newIconMessage} resizeMode="contain" />
              </View>
              <Text style={newRecentSearchesCard.pnrTextTitle} numberOfLines={1}>
                {item.pnrNumber}
              </Text>
              <Text style={newRecentSearchesCard.pnrTextSubtitle} numberOfLines={1}>
                {_label('trip_to_city', undefined, { city:item.destCityName })}
              </Text>
              <View style={newRecentSearchesCard.calendarContainer}>
                <View style={importedStyles.calenderIcon}>
                  <Image style={newRecentSearchesCard.image} source={newIconCalender} resizeMode="contain" />
                </View>
                <Text style={newRecentSearchesCard.pnrTextDate} numberOfLines={1}>
                  {getPnrRecentCardDateFormat(item.travelStartDate)}
                </Text>
              </View>
          </View>
      </TouchableOpacity>
    )
  );

  const renderHeader = () => (
    <View>
        <Text style={isOldPnrPage ? importedStyles.pnrHeaderText : 
          newRecentSearchesCard.pnrHeaderText}>{headerText}
        </Text>
    </View>
  );

  if (!recentSearchPnrHistory || recentSearchPnrHistory.length === 0) {
    return null;
  }

    return (
      <View style={isOldPnrPage ? newRecentSearchesCard.recentSearchContainerOld : 
        newRecentSearchesCard.recentSearchContainerNew}>
      {renderHeader()}
      <FlatList
        horizontal
        style={{ marginBottom: 5 ,marginTop:10}}
        showsHorizontalScrollIndicator={false}
        data={recentSearchPnrHistory}
        renderItem={renderCardData}
        keyExtractor={(item) => item.pnrNumber}
      />
      </View>
    );
};

export default RisPnrRecentSearchCards;
