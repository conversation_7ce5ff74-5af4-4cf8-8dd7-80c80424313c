export interface RisPnrFormPageProps {
  isFromNewLanding: boolean;
  railsBusCommonLanding?: boolean;
  deeplink?: boolean;
}

export interface RisPnrRecentSearchCardsProps {
  headerText?: string;
  recentSearchPnrHistory?: unknown[];
  verticalIndex: number;
}

export interface RisPnrSearchFormProps {
  pnrNumber: string;
  onPnrNumberEntered: () => void;
  deeplink?: boolean;
}
export interface NewPnrLandingPageProps {
  pnrNumber: string;
  onPnrNumberEntered: () => void;
  deeplink?: boolean;
}
