import React, { useEffect } from 'react';
import { BackHandler, Platform, View } from 'react-native';
import { Actions } from '../../../navigation';
import { importedStyles } from '../Styles';
import {removeTGSOmniture,tgsTrackPageDetails} from 'apps/rails/src/Utils/RisUtils'
import PnrSearchForm from '../Components/RisPnrSearchForm';
import {RAILS_LANDING_PNR_PAGE_NAME} from 'apps/rails/src/Utils/RailsLandingTrackingHelper'
import NewPnrLandingPage from '../Components/NewPnrLandingPage';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import Helmet from '@mmt/legacy-commons/Common/Components/Helmet';
import { pnrFaqs } from '../Common';
import { addWebEngageScriptToDOM } from '../../../webEngageHelper';
import { RisSeoHeaderUtil } from '../../RisLiveStationForm/Components';
import { jslonld } from '../../RisLiveStationForm/Common';
import {
  RAILS_BUS_COMMON_LANDING,
  setDataToAsyncStorage,
  UNIQUE_PNR_PAGENAMES,
} from '../../../Utils/RailsConstant';
import { RisPnrFormPageProps } from '../Interfaces';
import { getPNRPageConfig } from 'apps/rails/src/RailsAbConfig';

const RisPnrFormPage = (props: RisPnrFormPageProps) => {
  const { getRailwaysPNRHeader } = RisSeoHeaderUtil;
  const tags = getRailwaysPNRHeader();
  const { isFromNewLanding = false } = props;
  const isOldPnrPage = getPNRPageConfig();

  useEffect(() => {
    const setData = async () => {
      if (props.railsBusCommonLanding) {
        await setDataToAsyncStorage(RAILS_BUS_COMMON_LANDING, props.railsBusCommonLanding);
      }
    };
    setData();
    removeTGSOmniture();
    if (Platform.OS === 'web') {
      addWebEngageScriptToDOM();
    }
    BackHandler.addEventListener('hardwareBackPress', onBack);

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onBack);
    };
  }, []);

  useEffect(() => {
    if (isOldPnrPage) {
      tgsTrackPageDetails(RAILS_LANDING_PNR_PAGE_NAME, UNIQUE_PNR_PAGENAMES.LANDING_PAGE);
    }
    else {
      tgsTrackPageDetails(RAILS_LANDING_PNR_PAGE_NAME,UNIQUE_PNR_PAGENAMES.NEW_LANDING_PAGE)
    }
  }, [isOldPnrPage])

  const onBack = () => {
    if (isFromNewLanding) {
      // Actions.railsBusCommonLanding({ type: 'replace' });
      // Actions.railNewLanding({ type: 'replace' });
      if (props?.railsBusCommonLanding) {
        Actions.railsBusCommonLanding({ type: 'replace' });
      } else {
        Actions.railNewLanding({ type: 'replace' });
      }
      // Actions.pop();
      // Actions.replace({ key: 'railNewLanding', params: 'replace' });
    } else if (Platform.OS === 'ios') {
      ViewControllerModule.popViewController();
    } else {
      BackHandler.exitApp();
    }
    return true;
  };

  return (
    <View style={importedStyles.container}>
      { isOldPnrPage ? <PnrSearchForm deeplink={props.deeplink}/> :  <NewPnrLandingPage deeplink={props.deeplink}/> }
      <Helmet>
        <script type="application/ld+json">
          {`
    ${jslonld(pnrFaqs)}
  `}
        </script>
        {tags.props.children}
      </Helmet>
    </View>
  );
};

export default RisPnrFormPage;
