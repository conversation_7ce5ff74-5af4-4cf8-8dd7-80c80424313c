
import { Actions } from '../../navigation';
import { initRailsFilterObject } from '../NewListing/RailsListingReducer';
import {
  ACTION_CLEAR_ALL_FILTERS_ON_FILTER_PAGE,
  ACTION_CLEAR_SELECTED_FILTERS_ON_FILTER_PAGE,
  ACTION_CLEAR_ALL_FILTERS_ON_LISTING_PAGE,
  ACTION_COMMIT_TRAIN_AND_FILTER,
  ACTION_INIT_FILTER_STATE,
  ACTION_SET_FILTER,
  ACTION_SET_LISTING_QUICK_FILTER,
  ACTION_SET_QUICK_FILTER,
  ACTION_SET_TO_PREV_FILTER_STATE,
} from '../NewListing/RailsListingActions';
import { trackFilterBarEvent, trackFilterPageEvent, trackNewListingFilterEvent } from '../../railsAnalytics';
import {
  filterTickets,
  filterTrains,
  fillJourneyClassDetails,
  fillQuotaDetails,
  fillAvailabilityDetails,
  fillTrainTypes,
  fillDepartureTime,
  fillArrivalTime,
  fillFromStn,
  fillToStn,
  fillQuickFilters,
  setMetaDataForFilters,
} from './railsFilter.utils';
import { _label } from '../../vernacular/AppLanguage';
import { getRailsLDAndSSQuotaEnabled } from '../../RailsAbConfig';
import QuotaType from '../Types/QuotaType';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import { trackClickEventProp61 } from '../RailsBusHomePage/Analytics';
import { isEmpty } from 'lodash';
import { logListingPdtClickEvents, logListingSortAndFilterPdtClickEvents } from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import { LISTING_PDT_CLICK_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

export const removeSpaceAndLowercase = (str) => {
  return str.replace(/ /g, '').toLowerCase();
};

const sort_and_filter_omniture_map = {};
  sort_and_filter_omniture_map['bottomSheet_' + removeSpaceAndLowercase(_label('journey_class')) + '_Done'] = 'mob_rail_listing_quickfilter_class_done';
  sort_and_filter_omniture_map['bottomSheet_' + removeSpaceAndLowercase(_label('availability_filter')) + '_Done'] = 'mob_rail_listing_quickfilter_availability_done';
  sort_and_filter_omniture_map['bottomSheet_' + removeSpaceAndLowercase(_label('timing_filter')) + '_Done'] = 'mob_rail_listing_quickfilter_timing_done';
  sort_and_filter_omniture_map['bottomSheet_' + removeSpaceAndLowercase(_label('journey_class')) + '_Clear'] = 'mob_rail_listing_quickfilter_class_clear';
  sort_and_filter_omniture_map['bottomSheet_' + removeSpaceAndLowercase(_label('availability_filter')) + '_Clear'] = 'mob_rail_listing_quickfilter_availability_clear';
  sort_and_filter_omniture_map['bottomSheet_' + removeSpaceAndLowercase(_label('timing_filter')) + '_Clear'] = 'mob_rail_listing_quickfilter_timing_clear';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_1A = 'mob_rail_listing_quickfilter_class_1A';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_2A = 'mob_rail_listing_quickfilter_class_2A';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_3A = 'mob_rail_listing_quickfilter_class_3A';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_SL = 'mob_rail_listing_quickfilter_class_SL';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_2S = 'mob_rail_listing_quickfilter_class_2S';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_CC = 'mob_rail_listing_quickfilter_class_CC';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_EC = 'mob_rail_listing_quickfilter_class_EC';
  sort_and_filter_omniture_map.bottomSheet_journeyClassFilter_3E = 'mob_rail_listing_quickfilter_class_3E';
  sort_and_filter_omniture_map.bottomSheet_availabilityFilter_AVL_availability_filter = 'mob_rail_listing_quickfilter_availability_avl';
  sort_and_filter_omniture_map.bottomSheet_availabilityFilter_WL_availabilty_filter = 'mob_rail_listing_quickfilter_availability_wl';
  sort_and_filter_omniture_map.bottomSheet_availabilityFilter_RAC_availability_filter = 'mob_rail_listing_quickfilter_availability_rac';
  sort_and_filter_omniture_map.bottomSheet_availabilityFilter_TQ_quota_filter = 'mob_rail_listing_quickfilter_availability_tatkal';
  sort_and_filter_omniture_map.bottomSheet_departureTimeFilter_morningFilter = 'mob_rail_listing_quickfilter_timing_departure_morning';
  sort_and_filter_omniture_map.bottomSheet_departureTimeFilter_afternoonFilter = 'mob_rail_listing_quickfilter_timing_departure_midday';
  sort_and_filter_omniture_map.bottomSheet_departureTimeFilter_eveningFilter = 'mob_rail_listing_quickfilter_timing_departure_evening';
  sort_and_filter_omniture_map.bottomSheet_departureTimeFilter_nightFilter = 'mob_rail_listing_quickfilter_timing_departure_night';
  sort_and_filter_omniture_map.bottomSheet_arrivalTimeFilter_morningFilter = 'mob_rail_listing_quickfilter_timing_arrival_morning';
  sort_and_filter_omniture_map.bottomSheet_arrivalTimeFilter_afternoonFilter = 'mob_rail_listing_quickfilter_timing_arrival_midday';
  sort_and_filter_omniture_map.bottomSheet_arrivalTimeFilter_eveningFilter = 'mob_rail_listing_quickfilter_timing_arrival_evening';
  sort_and_filter_omniture_map.bottomSheet_arrivalTimeFilter_nightFilter = 'mob_rail_listing_quickfilter_timing_arrival_night';
  sort_and_filter_omniture_map.Sort_Apply = 'mob_rail_listing_quickfilter_sort_apply';
  sort_and_filter_omniture_map.Sort_Close = 'mob_rail_listing_quickfilter_sort_cross';
  sort_and_filter_omniture_map.Filter_Close = 'mob_rail_listing_quickfilter_filter_cross';
  sort_and_filter_omniture_map.filterSheet_apply = 'mob_rail_listing_quickfilter_filter_apply';
  sort_and_filter_omniture_map.clear_filterPage = 'mob_rail_listing_quickfilter_filter_clear';
  sort_and_filter_omniture_map.filterSheet_quotaFilter_TQ_quota_filter = 'mob_rail_listing_quickfilter_filter_quota_tatkal';
  sort_and_filter_omniture_map.filterSheet_quotaFilter_GN_quota_filter = 'mob_rail_listing_quickfilter_filter_quota_general';
  sort_and_filter_omniture_map.filterSheet_toStnFilter = 'mob_rail_listing_quickfilter_filter_arrivalstation';
  sort_and_filter_omniture_map.filterSheet_fromStnFilter = 'mob_rail_listing_quickfilter_filter_departurestation';
  sort_and_filter_omniture_map.filterSheet_ticketTypeFilter_freeCancellationFilter = 'mob_rail_filtersScreen_ticketTypeFilter_freeCancellationFilter';
  sort_and_filter_omniture_map.filterSheet_ticketTypeFilter_confirmationGuaranteeFilter = 'mob_rail_filtersScreen_ticketTypeFilter_confirmationGuaranteeFilter';
  sort_and_filter_omniture_map.filterSheet_trainTypeFilter = 'mob_rail_listing_filter_quickfilter_filter_traintype';

  sort_and_filter_omniture_map.filterSheet_journeyClassFilter_1A = 'mob_rail_filtersScreen_journeyClassFilter_1A';
  sort_and_filter_omniture_map.filterSheet_journeyClassFilter_2A = 'mob_rail_filtersScreen_journeyClassFilter_2A';
  sort_and_filter_omniture_map.filterSheet_journeyClassFilter_3A = 'mob_rail_filtersScreen_journeyClassFilter_3A';
  sort_and_filter_omniture_map.filterSheet_journeyClassFilter_SL = 'mob_rail_filtersScreen_journeyClassFilter_SL';
  sort_and_filter_omniture_map.filterSheet_journeyClassFilter_CC = 'mob_rail_filtersScreen_journeyClassFilter_CC';
  sort_and_filter_omniture_map.filterSheet_journeyClassFilter_EC = 'mob_rail_filtersScreen_journeyClassFilter_EC';
  sort_and_filter_omniture_map.filterSheet_journeyClassFilter_3E = 'mob_rail_filtersScreen_journeyClassFilter_3E';

  sort_and_filter_omniture_map.filterSheet_availabilityFilter_AVL_availability_filter = 'mob_rail_listing_filter_quickfilter_filter_availability_avl';
  sort_and_filter_omniture_map.filterSheet_availabilityFilter_WL_availabilty_filter = 'mob_rail_listing_filter_quickfilter_filter_availability_wl';
  sort_and_filter_omniture_map.filterSheet_availabilityFilter_RAC_availability_filter = 'mob_rail_listing_filter_quickfilter_filter_availability_rac';

  sort_and_filter_omniture_map.filterSheet_departureTimeFilter_morningFilter = 'mob_rail_filtersScreen_departureTimeFilter_morning';
  sort_and_filter_omniture_map.filterSheet_departureTimeFilter_afternoonFilter = 'mob_rail_filtersScreen_departureTimeFilter_afternoon';
  sort_and_filter_omniture_map.filterSheet_departureTimeFilter_eveningFilter = 'mob_rail_filtersScreen_departureTimeFilter_evening';
  sort_and_filter_omniture_map.filterSheet_departureTimeFilter_nightFilter = 'mob_rail_filtersScreen_departureTimeFilter_night';
  sort_and_filter_omniture_map.filterSheet_arrivalTimeFilter_morningFilter = 'mob_rail_filtersScreen_arrivalTimeFilter_morning';
  sort_and_filter_omniture_map.filterSheet_arrivalTimeFilter_afternoonFilter = 'mob_rail_filtersScreen_arrivalTimeFilter_afternoon';
  sort_and_filter_omniture_map.filterSheet_arrivalTimeFilter_eveningFilter = 'mob_rail_filtersScreen_arrivalTimeFilter_evening';
  sort_and_filter_omniture_map.filterSheet_arrivalTimeFilter_nightFilter = 'mob_rail_filtersScreen_arrivalTimeFilter_night';

export const onFilterClick = (filterItem, filterTypeObject) => {
  return ({
    type: ACTION_SET_FILTER,
    data: {filterItem, filterTypeObject},
  });
};

export const onQuickFilterClick = (filterItem, filterTypeObject) => ({
  type: ACTION_SET_QUICK_FILTER,
  data: {filterItem, filterTypeObject},
});

export const onListingQuickFilterClick = (filterItem, filterTypeObject) => (dispatch) => {
  if (!filterTypeObject.filterTypes[filterItem].selected) {
    trackNewListingFilterEvent(`mob_rail_listing_v2_filter_quickFilter_${filterItem}`);
  }
  dispatch({
    type: ACTION_SET_LISTING_QUICK_FILTER,
    data: {filterItem, filterTypeObject},
  });
  dispatch(logListingSortAndFilterPdtClickEvents(`${filterItem}_${LISTING_PDT_CLICK_EVENTS.QUICK_FILTER_CLICK}`));
};


export const fireOmnitureEventsNew = (location) => {
  try {
    trackFilterBarEvent(sort_and_filter_omniture_map[location]);
  } catch (e) {
    console.log('error is', e);
  }
};

// export const fireFilterOmnitureEvents = (filterObject) => {
//   try {
//     const filteredObjectArray = Object.keys(filterObject);
//     filteredObjectArray.forEach((item) => {
//       if (!(item === 'quickFilter')) {
//         const filterTypesList = Object.keys(filterObject[item].filterTypes);
//         filterTypesList.forEach((filterItem) => {
//           if (filterObject[item].filterTypes[filterItem].selected) {
//             if (item === 'trainTypeFilter') {
//               const label = _label(getTrainType(filterObject[item].filterTypes[filterItem].key).value);
//               const trainTypeFilterName = label.replace(/ /g, '_');
//               trackFilterPageEvent(`mob_rail_filtersScreen_${item}_${trainTypeFilterName}`);
//             } else {
//               trackFilterPageEvent(`mob_rail_filtersScreen_${item}_${filterObject[item].filterTypes[filterItem].key}`);
//             }
//           }
//         });
//       }
//     });
//   } catch (e) {
//     console.log('error is', e);
//   }
// };

export const fireFilterOmnitureEvents = (filterObject , location) => {
  try {
    const filteredObjectArray = Object.keys(filterObject);
    filteredObjectArray.forEach((item) => {
      if (!(item === 'quickFilter')) {
        const filterTypesList = Object.keys(filterObject[item].filterTypes);
        filterTypesList.forEach((filterItem) => {
          if (filterObject[item].filterTypes[filterItem].selected) {
            if (item === 'trainTypeFilter') {
              // const label = _label(getTrainType(filterObject[item].filterTypes[filterItem].key).value);
              // const trainTypeFilterName = label.replace(/ /g, '_');
              // trackFilterPageEvent(`mob_rail_filtersScreen_${item}_${trainTypeFilterName}`);
              fireOmnitureEventsNew('filterSheet_trainTypeFilter');
            }
            else {
              if (location === null) {
                if (item === 'departureTimeFilter' || item === 'arrivalTimeFilter'){
                  fireOmnitureEventsNew(`filterSheet_${item}_${filterObject[item].filterTypes[filterItem].key}`);
                } else
                {trackFilterPageEvent(`mob_rail_filtersScreen_${item}_${filterObject[item].filterTypes[filterItem].key}`);}
              }
              if (location !== null && location.includes('bottomSheet'))
              {
                fireOmnitureEventsNew(`bottomSheet_${item}_${filterObject[item].filterTypes[filterItem].key}`);
              }
              if (location !== null && !location.includes('bottomSheet')){
                if (item === 'toStnFilter' || item === 'fromStnFilter')
                {
                  fireOmnitureEventsNew(`filterSheet_${item}`);
                } else {
                fireOmnitureEventsNew(`filterSheet_${item}_${filterObject[item].filterTypes[filterItem].key}`);
                }
              }
            }
          }
        });
      }
    });
  } catch (e) {
    console.log('error is', e);
  }
};

export const onFilterIconClick = (pageData = null) => (dispatch) => {
  dispatch({
    type: ACTION_INIT_FILTER_STATE,
    data: null,
  });
  if (pageData) {
    Actions.railsFilters(pageData);
  } else {
    Actions.railsFilters();
  }
};

export const onFilterCloseClick = (dispatch) => {
  dispatch({
    type: ACTION_SET_TO_PREV_FILTER_STATE,
    data: null,
  });
  Actions.pop();
  dispatch(logListingPdtClickEvents(LISTING_PDT_CLICK_EVENTS.FILTERS_PAGE_CROSS_CLICK));
};

export const onFilterCloseClickV2 = () => {
  return {
    type: ACTION_SET_TO_PREV_FILTER_STATE,
    data: null,
  };
};

export const onClearAllFiltersClicked = () => (dispatch) => {
  dispatch({
    type: ACTION_CLEAR_ALL_FILTERS_ON_FILTER_PAGE,
    data: null,
  });
  dispatch(logListingPdtClickEvents(LISTING_PDT_CLICK_EVENTS.FILTERS_PAGE_CLEAR_ALL_CLICK));
};

export const onClearSelectedFilter = (filterItem , location) =>{
const callLocation =  typeof location !== 'undefined' ? location : null;
  // tracking
  if (location !== null) {fireOmnitureEventsNew(callLocation);}
  return (
  {type: ACTION_CLEAR_SELECTED_FILTERS_ON_FILTER_PAGE,
  data: {filterItem},
  });
};

export const onClearAllFiltersClickedInListing = () => (dispatch) =>  {
  dispatch({
    type: ACTION_CLEAR_ALL_FILTERS_ON_LISTING_PAGE,
    data: null,
  });

  dispatch(logListingSortAndFilterPdtClickEvents(LISTING_PDT_CLICK_EVENTS.CLEAR_ALL_FILTERS_LISTING_CLICK));
};

export const getNewFilterObject = (filterObject, selectedFilterItem, filterTypeObject) => {
  const selectedFilterObject = filterTypeObject.filterTypes[selectedFilterItem];
  const selectedFilterState = !selectedFilterObject.selected;
  return Object.entries(filterObject)
    .reduce((a, [key, val]) => {
      if (key === selectedFilterObject.parentFilterType) {
        const filterTypes = Object.entries(val.filterTypes)
          .reduce((a, [filterKey, filterVal]) => {
            if (selectedFilterObject.filterList.includes(filterKey)) {
              return ({
                ...a,
                [filterKey]: {
                  ...filterVal,
                  selected: selectedFilterState,
                },
              });
            } else {
              return ({
                ...a,
                [filterKey]: filterVal,
              });
            }
          }, {});
        return ({
          ...a,
          [key]: { ...val, filterTypes },
        });
      } else if (key === selectedFilterObject.id) {
        return ({
          ...a,
          [key]: {
            ...val,
            filterTypes: {
              ...val.filterTypes,
              [selectedFilterItem]: {
                ...selectedFilterObject,
                selected: selectedFilterState,
              },
            },
          },
        });
      } else {
        return ({
          ...a,
          [key]: val,
        });
      }
    }, {});
};

export const getFilterObject = (trainsList, originStation, destinationStation, textss, getState) => {
  const { railsVernacular: { texts } } = getState();
  let filterObject = initRailsFilterObject();
  try {
    trainsList.forEach((train) => {
      const journeyClassFilterTypes = fillJourneyClassDetails(train, {
        ...(filterObject?.journeyClassFilter?.filterTypes ?? {}),
      });
      filterObject.journeyClassFilter.filterTypes = {
        ...filterObject?.journeyClassFilter?.filterTypes,
        ...journeyClassFilterTypes,
      };

      const quotaFilterTypes = fillQuotaDetails(train, {
        ...(filterObject?.quotaFilter?.filterTypes ?? {}),
      });
      filterObject.quotaFilter.filterTypes = {
        ...filterObject?.quotaFilter?.filterTypes,
        ...quotaFilterTypes,
      };

      const availabilityFilterTypes = fillAvailabilityDetails(train, {
        ...(filterObject?.availabilityFilter?.filterTypes ?? {}),
      });
      const tqAvailability = {
        TQ_quota_filter: {...quotaFilterTypes.TQ_quota_filter},
      };
      filterObject.availabilityFilter.filterTypes = {
        ...filterObject?.availabilityFilter?.filterTypes,
        ...availabilityFilterTypes,
        ...(tqAvailability.TQ_quota_filter.key ? tqAvailability : {}),
      };

      const trainTypeFilters = fillTrainTypes(train, {
        ...(filterObject?.trainTypeFilter?.filterTypes ?? {}),
      });
      filterObject.trainTypeFilter.filterTypes = {
        ...filterObject?.trainTypeFilter?.filterTypes,
        ...trainTypeFilters,
      };

      const departureTimeFilters = fillDepartureTime(train, {
        ...(filterObject?.departureTimeFilter?.filterTypes ?? {}),
      });
      filterObject.departureTimeFilter.filterTypes = {
        ...filterObject?.departureTimeFilter?.filterTypes,
        ...departureTimeFilters,
      };

      const arrivalTimeFilters = fillArrivalTime(train, {
        ...(filterObject?.arrivalTimeFilter?.filterTypes ?? {}),
      });
      filterObject.arrivalTimeFilter.filterTypes = {
        ...filterObject?.arrivalTimeFilter?.filterTypes,
        ...arrivalTimeFilters,
      };

      const fromStnFilters = fillFromStn(train, {
        ...(filterObject?.fromStnFilter?.filterTypes ?? {}),
      });
      filterObject.fromStnFilter.filterTypes = {
        ...filterObject?.fromStnFilter?.filterTypes,
        ...fromStnFilters,
      };

      const toStnFilters = fillToStn(train, { ...(filterObject?.toStnFilter?.filterTypes ?? {}) });
      filterObject.toStnFilter.filterTypes = {
        ...filterObject?.toStnFilter?.filterTypes,
        ...toStnFilters,
      };
    });
    const quickFilters = fillQuickFilters(
      { ...(filterObject?.quickFilter?.filterTypes ?? {}) },
      filterObject?.journeyClassFilter?.filterTypes,
      texts,
    );
    filterObject.quickFilter.filterTypes = {
      ...filterObject?.quickFilter?.filterTypes,
      ...quickFilters,
    };

    // Preselect GN and TQ quota filters if LD and SS quota is enabled and if quotaFilterTypes has LD or SS quota available
    if (
      getRailsLDAndSSQuotaEnabled() &&
      (filterObject?.quotaFilter?.filterTypes?.[QuotaType.LD.filterKey] ||
        filterObject?.quotaFilter?.filterTypes?.[QuotaType.SS.filterKey])
    ) {
      try {
        let preselectQuotaInQuickFilter = false;
        if (filterObject?.quotaFilter?.filterTypes?.[QuotaType.GN.filterKey]) {
          filterObject.quotaFilter.filterTypes[QuotaType.GN.filterKey].selected = true;
          preselectQuotaInQuickFilter = true;
        }
        if (filterObject?.quotaFilter?.filterTypes?.[QuotaType.TQ.filterKey]) {
          filterObject.quotaFilter.filterTypes[QuotaType.TQ.filterKey].selected = true;
          preselectQuotaInQuickFilter = true;
        }

        // Preselect quota filter in quick filter
        if (preselectQuotaInQuickFilter && filterObject?.quickFilter?.filterTypes?.quota) {
          filterObject.quickFilter.filterTypes.quota.selected = true;
        }
      } catch (err) {
        console.error('error in getFilterObject', err);
      }
    }
    filterObject = setMetaDataForFilters(filterObject, originStation, destinationStation);
  } catch (e) {
    console.log('Error in getFilterObject: ', e);
    filterObject = initRailsFilterObject(filterObject);
  } finally {
    return Object.entries(filterObject).reduce((a, [key, val]) => {
      const filterKeys = Object.keys(val.filterTypes);
      return {
        ...a,
        [key]: {
          ...val,
          filterTypesArray: filterKeys,
        },
      };
    }, {});
  }
};

export const getStagedTrainsList = (trainsList, newFilteredObject) => {
  const filteredTrainsList = filterTrains(trainsList, newFilteredObject);
  const finalTrainList = filterTickets(filteredTrainsList, newFilteredObject);
  return finalTrainList;
};

export const setFiltersAndTrainsList = (callLocation) => (dispatch) => {
  const location = typeof callLocation !== 'undefined' ? callLocation : null;
  dispatch({
    type: ACTION_COMMIT_TRAIN_AND_FILTER,
    data: { location },
  });

  dispatch(logListingSortAndFilterPdtClickEvents(LISTING_PDT_CLICK_EVENTS.SORT_AND_FILTER_APPLY_CLICK));
};

export const handleNewQuotaClickEvent = (filterItem) => {
  let eventName = '';
  switch (filterItem) {
    case QuotaType.GN.filterKey:
      eventName = RAIL_EVENTS.LISTING.RAIL_LISTING_QUOTA_GENERAL;
      break;
    case QuotaType.TQ.filterKey:
      eventName = RAIL_EVENTS.LISTING.RAIL_LISTING_QUOTA_TATKAL;
      break;
    case QuotaType.LD.filterKey:
      eventName = RAIL_EVENTS.LISTING.RAIL_LISTING_QUOTA_LADIES;
      break;
    case QuotaType.SS.filterKey:
      eventName = RAIL_EVENTS.LISTING.RAIL_LISTING_QUOTA_SR_CITIZEN;
  }
  trackClickEventProp61(RAIL_EVENTS.LISTING.MMT_RAIL_RN_LISTING_V2, eventName);
};

export const getFilteredTrainsWithGNAndTQClass = (trainsList = []) => {
  try {
    if (!isEmpty(trainsList)) {
      return trainsList.map((train) => {
        const filteredAvailability = train.tbsAvailability.filter(
          (avail) => avail.quota === QuotaType.GN.code || avail.quota === QuotaType.TQ.code,
        );
        return { ...train, tbsAvailability: filteredAvailability };
      });
    } else {
      return trainsList;
    }
  } catch (err) {
    console.log('error in getFilteredTrainsWithGNAndTQClass', err);
    return trainsList;
  }
};
