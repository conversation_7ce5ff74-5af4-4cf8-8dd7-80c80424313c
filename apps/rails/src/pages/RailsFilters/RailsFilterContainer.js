import { connect } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import {
  onClearAllFiltersClicked,
  onFilterClick,
  onFilterCloseClick,
  onQuickFilterClick,
  setFiltersAndTrainsList,
} from './RailsFilterActions';
import {RailsFilters} from './RailsFilters';
import {onSortTrainClicked} from '../NewListing/RailsListingActions';
import { _label } from '../../vernacular/AppLanguage';

const getListingLabels = (texts, originCityName, destinationCityName) => ({
  departure_from_city: _label('departure_from_city', undefined, { city: originCityName }),
  arrival_in_city: _label('arrival_in_city', undefined, { city: destinationCityName }),
  stations_in_desitination_city: _label('stations_in_city', undefined, { city: destinationCityName}),
  stations_in_origin_city: _label('stations_in_city', undefined, { city: originCityName}),
});

const mapStateToProps = (state) => {
  const {
    railsListing: {
      stagedFilterObject,
      trainsList,
      stagedTrainsList,
      selectedSortParam,
      destinationStation,
      originStation,
    },
    railsVernacular: {texts},
  } = state;
  return {
    hasTrainsList: !isEmpty(trainsList),
    filterObject: stagedFilterObject,
    labels: getListingLabels(texts, originStation?.cityName, destinationStation?.cityName),
    stagedTrainsList,
    selectedSortParam,
  };
};

const mapDispatchToProps = dispatch => ({
  onSortTrainClicked: (selectedSortParam) => {
    dispatch(onSortTrainClicked(selectedSortParam));
  },
  onFilterClick: (filterItem, filterTypeObject) => {
    dispatch(onFilterClick(filterItem, filterTypeObject));
  },
  onQuickFilterClick: (filterItem, filterTypeObject) => {
    dispatch(onQuickFilterClick(filterItem, filterTypeObject));
  },
  onDoneClick: () => { dispatch(setFiltersAndTrainsList()); },
  onCloseClick: () => { dispatch(onFilterCloseClick); },
  onClearAllFiltersClicked: () => { dispatch(onClearAllFiltersClicked()); },
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsFilters);
