import isEmpty from 'lodash/isEmpty';
import intersectionWith from 'lodash/intersectionWith';
import type {
  Train,
  FilterObject,
  FilterObjectValue,
  TbsAvailability,
  TicketFilterCondition,
  FilterObjectFilterTypes,
} from './RailsFilterInterface';
import type { StationFC } from '../NewListing/interface';
import { _label } from '../../vernacular/AppLanguage';
import { getTrainType } from '../Types/TrainTypes';
import { isAcClass, getClassType } from '../Types/ClassType';
import { isQuotaFilter, getQuota } from '../Types/QuotaType';
import {
  availabilityTypesForQuickFilterAvailable,
  getAvailabilityType,
  isAvailabilityTypeFilter,
} from '../Types/AvailabilityType';
import { stringCompare } from '../TravelerDetails/TravelerDetailsUtils';
import { getTimeFilterType } from './railsFilterConstants';
import { getRailsLDAndSSQuotaEnabled } from '../../RailsAbConfig';

export const QUOTA_FILTER_KEY = 'quota';

export const fillJourneyClassDetails = (
  train: Train,
  currentJourneyClassFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const newClassFiltersToBeAdded = train.avlClasses.reduce((acc, item) => {
      if (isEmpty(currentJourneyClassFilters[item])) {
        acc[item] = {
          key: item,
          heading: _label(getClassType(item).value),
          subHeading: item,
          selected: false,
          noOfTrains: 1,
          condition: (train) =>
            isEmpty(train?.avlClasses) ||
            train?.avlClasses.some((avlClass) => stringCompare(avlClass, item)), // if information is missing, include trains
          ticketFilterCondition: (ticket: TbsAvailability) =>
            isEmpty(ticket?.className) || stringCompare(ticket?.className, item), // if information is missing, include trains
        };
      } else {currentJourneyClassFilters[item].noOfTrains += 1;}
      return acc;
    }, {} as FilterObjectFilterTypes);
    return {
      ...currentJourneyClassFilters,
      ...newClassFiltersToBeAdded,
    };
  } catch (error) {
    console.log('Error in fillJourneyClassDetails: ', error);
    return {};
  }
};

export const fillQuotaDetails = (
  train: Train,
  currentQuotaFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const newQuotaFilters = train.tbsAvailability.reduce((acc, item) => {
      const availabilityCardQuotaTypeValue = getQuota(item.quota);
      if (isEmpty(currentQuotaFilters[availabilityCardQuotaTypeValue.filterKey])) {
        if (!isEmpty(acc[availabilityCardQuotaTypeValue.filterKey]))
          {acc[availabilityCardQuotaTypeValue.filterKey].noOfTrains += 1;}
        else if (isQuotaFilter(availabilityCardQuotaTypeValue)) {
          acc[availabilityCardQuotaTypeValue.filterKey] = {
            key: availabilityCardQuotaTypeValue.filterKey,
            heading: _label(availabilityCardQuotaTypeValue.filterLabel),
            selected: false,
            noOfTrains: 1,
            condition: (train) =>
              train?.tbsAvailability?.some(
                (ticket) =>
                  isEmpty(ticket?.quota) || // if information is missing, include trains
                  stringCompare(
                    getQuota(ticket?.quota)?.filterCode,
                    availabilityCardQuotaTypeValue.filterCode,
                  ),
              ),
            ticketFilterCondition: (ticket) =>
              isEmpty(ticket?.quota) || // if information is missing, include trains
              stringCompare(
                getQuota(ticket?.quota)?.filterCode,
                availabilityCardQuotaTypeValue.filterCode,
              ),
          };
        }
      } else {currentQuotaFilters[availabilityCardQuotaTypeValue.filterKey].noOfTrains += 1;}
      return acc;
    }, {} as FilterObjectFilterTypes);
    return {
      ...currentQuotaFilters,
      ...newQuotaFilters,
    };
  } catch (error) {
    console.log('Error in fillQuotaDetails: ', error);
    return {};
  }
};

export const fillAvailabilityDetails = (
  train: Train,
  currentAvailabilityFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const newAvailabilityFilters = train.tbsAvailability.reduce((acc, item) => {
      if (!isEmpty(item?.availablityType)) {
        const cardAvailableTypeValue = getAvailabilityType(item?.availablityType);
        if (isEmpty(currentAvailabilityFilters[cardAvailableTypeValue.filterKey])) {
          if (!isEmpty(acc[cardAvailableTypeValue.filterKey]))
            {acc[cardAvailableTypeValue.filterKey].noOfTrains += 1;}
          else if (isAvailabilityTypeFilter(cardAvailableTypeValue)) {
            acc[cardAvailableTypeValue.filterKey] = {
              key: cardAvailableTypeValue.filterKey,
              heading: _label(cardAvailableTypeValue.filterLabel),
              subHeading: cardAvailableTypeValue.filterId,
              selected: false,
              noOfTrains: 1,
              condition: (train) =>
                train?.tbsAvailability?.some(
                  (ticket) =>
                    isEmpty(ticket?.availablityType) || // if information is missing, include trains
                    stringCompare(
                      getAvailabilityType(ticket?.availablityType)?.filterCode,
                      cardAvailableTypeValue?.filterCode,
                    ),
                ),
              ticketFilterCondition: (ticket) =>
                isEmpty(ticket?.availablityType) || // if information is missing, include trains
                stringCompare(
                  getAvailabilityType(ticket?.availablityType)?.filterCode,
                  cardAvailableTypeValue?.filterCode,
                ),
            };
          }
        } else {
          currentAvailabilityFilters[cardAvailableTypeValue.filterKey].noOfTrains += 1;
        }
      }
      return acc;
    }, {} as FilterObjectFilterTypes);
    return {
      ...currentAvailabilityFilters,
      ...newAvailabilityFilters,
    };
  } catch (error) {
    console.log('Error in fillAvailabilityDetails: ', error);
    return {};
  }
};

export const fillTrainTypes = (
  train: Train,
  currentTrainTypeFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const trainType = train.trainType[0];
    if (isEmpty(currentTrainTypeFilters[trainType])) {
      currentTrainTypeFilters[trainType] = {
        key: trainType,
        heading: _label(getTrainType(trainType).value),
        selected: false,
        noOfTrains: 1,
        condition: (trainItem) => trainItem.trainType[0] === trainType,
      };
    } else {currentTrainTypeFilters[trainType].noOfTrains += 1;}
    return currentTrainTypeFilters;
  } catch (error) {
    console.log('Error in fillTrainTypes: ', error);
    return {};
  }
};

export const fillDepartureTime = (
  train: Train,
  currentDepartureTimeFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const hours = train.departureDateAndTime.getHours();
    const filterType = getTimeFilterType(hours);
    if (!isEmpty(filterType)) {currentDepartureTimeFilters[filterType].noOfTrains += 1;}
    return currentDepartureTimeFilters;
  } catch (error) {
    console.log('Error in fillDepartureTime: ', error);
    return {};
  }
};

export const fillArrivalTime = (
  train: Train,
  currentArrivalTimeFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const hours = train.arrivalDateAndTime.getHours();
    const filterType = getTimeFilterType(hours);
    if (!isEmpty(filterType)) {currentArrivalTimeFilters[filterType].noOfTrains += 1;}
    return currentArrivalTimeFilters;
  } catch (error) {
    console.log('Error in fillArrivalTime: ', error);
    return {};
  }
};

export const fillFromStn = (
  train: Train,
  currentFromStnFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const { frmStnCode, frmStnName } = train;
    if (isEmpty(currentFromStnFilters[frmStnCode])) {
      currentFromStnFilters[frmStnCode] = {
        key: frmStnCode,
        heading: frmStnName,
        subHeading: frmStnCode,
        selected: false,
        noOfTrains: 1,
        condition: (trainItem) => trainItem.frmStnCode === frmStnCode,
      };
    } else {currentFromStnFilters[frmStnCode].noOfTrains += 1;}
    return currentFromStnFilters;
  } catch (error) {
    console.log('Error in fillFromStn: ', error);
    return {};
  }
};

export const fillToStn = (
  train: Train,
  currentToStnFilters: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const { toStnCode, toStnName } = train;
    if (isEmpty(currentToStnFilters[toStnCode])) {
      currentToStnFilters[toStnCode] = {
        key: toStnCode,
        heading: toStnName,
        subHeading: toStnCode,
        selected: false,
        noOfTrains: 1,
        condition: (trainItem) => trainItem.toStnCode === toStnCode,
      };
    } else {currentToStnFilters[toStnCode].noOfTrains += 1;}
    return currentToStnFilters;
  } catch (error) {
    console.log('Error in fillToStn: ', error);
    return {};
  }
};

export const fillQuickFilters = (
  currentQuickFilters: FilterObjectFilterTypes,
  journeyClassFilterTypes: FilterObjectFilterTypes,
): FilterObjectFilterTypes => {
  try {
    const classFilterArray = Object.keys(journeyClassFilterTypes ?? {});
    const filteredAcClassArray = classFilterArray.filter((classType) => isAcClass(classType));
    if (filteredAcClassArray.length > 0) {
      currentQuickFilters.AC = {
        key: 'AC',
        heading: _label('ac'),
        filterList: filteredAcClassArray,
        parentFilterType: 'journeyClassFilter',
        selected: false,
        id: 'quickFilter',
        condition: () => true,
      };
    }

    currentQuickFilters.available = {
      key: 'available',
      heading: _label('available'),
      filterList: availabilityTypesForQuickFilterAvailable.map(
        (availabilityType) => getAvailabilityType(availabilityType).filterKey,
      ),
      parentFilterType: 'availabilityFilter',
      selected: false,
      id: 'quickFilter',
      condition: () => true,
    };

    if (getRailsLDAndSSQuotaEnabled()) {
      currentQuickFilters.quota = {
        key: 'quota',
        heading: _label('quota'),
        filterList: [],
        parentFilterType: 'quotaFilter',
        selected: false,
        id: 'quickFilter',
        condition: () => true,
      };
    }

    currentQuickFilters.departureAfter6 = {
      key: 'departureAfter6',
      heading: _label('departure_after_6_pm'),
      filterList: ['eveningFilter'],
      parentFilterType: 'departureTimeFilter',
      selected: false,
      id: 'quickFilter',
      condition: () => true,
    };

    currentQuickFilters.arrivalBefore12 = {
      key: 'arrivalBefore12',
      heading: _label('arrival_before_12_pm'),
      filterList: ['nightFilter', 'morningFilter'],
      parentFilterType: 'arrivalTimeFilter',
      selected: false,
      id: 'quickFilter',
      condition: () => true,
    };

    return currentQuickFilters;
  } catch (error) {
    console.log('Error in fillQuickFilters: ', error);
    return {};
  }
};

export const createTicketTypeFilterObj = (
  noOfConfirmationGuaranteeTrains: number,
  noOfFreeCancellationTrains: number,
): FilterObjectValue => {
  const FILTER_ID = 'ticketTypeFilter';
  const FILTER_HEADING = _label('ticket_type_filter');
  return {
    heading: FILTER_HEADING,
    id: FILTER_ID,
    filterTypesArray: ['confirmationGuaranteeFilter', 'freeCancellationFilter'],
    filterTypes: {
      confirmationGuaranteeFilter: {
        heading: _label('trip_guarantee'),
        key: 'confirmationGuaranteeFilter',
        id: 'confirmationGuaranteeFilter',
        noOfTrains: noOfConfirmationGuaranteeTrains,
        selected: false,
        condition: (train) =>
          train?.tbsAvailability?.some((ticket) => !isEmpty(ticket?.confirmationGuaranteeText)),
        ticketFilterCondition: (ticket) => !isEmpty(ticket?.confirmationGuaranteeText),
      },

      freeCancellationFilter: {
        heading: _label('free_cancellation'),
        key: 'freeCancellationFilter',
        id: 'freeCancellationFilter',
        noOfTrains: noOfFreeCancellationTrains,
        selected: false,
        condition: (train) =>
          train?.tbsAvailability?.some((ticket) => !isEmpty(ticket?.freeCancellationText)),
        ticketFilterCondition: (ticket) => !isEmpty(ticket?.freeCancellationText),
      },
    },
  };
};

export const setMetaDataForFilters = (
  filterObject: FilterObject,
  originStation: StationFC,
  destinationStation: StationFC,
): FilterObject => {
  const filterObjectArray = Object.keys(filterObject);
  let filterReturnObject = filterObject;
  filterObjectArray.forEach((item) => {
    switch (item) {
      case 'quickFilter':
        filterReturnObject = {
          ...filterReturnObject,
          quickFilter: {
            ...(filterReturnObject?.quickFilter ?? {}),
            heading: _label('quick_filter'),
            id: 'quickFilter',
          },
        };
        break;
      case 'journeyClassFilter':
        filterReturnObject = {
          ...filterReturnObject,
          journeyClassFilter: {
            ...(filterReturnObject?.journeyClassFilter ?? {}),
            heading: _label('journey_class_filter'),
            id: 'journeyClassFilter',
          },
        };
        break;
      case 'availabilityFilter':
        filterReturnObject = {
          ...filterReturnObject,
          availabilityFilter: {
            ...(filterReturnObject?.availabilityFilter ?? {}),
            heading: _label('availability_filter'),
            id: 'availabilityFilter',
          },
        };
        break;
      case 'quotaFilter':
        filterReturnObject = {
          ...filterReturnObject,
          quotaFilter: {
            ...(filterReturnObject?.quotaFilter ?? {}),
            heading: _label('quota_filter'),
            id: 'quotaFilter',
          },
        };
        break;
      case 'trainTypeFilter':
        filterReturnObject = {
          ...filterReturnObject,
          trainTypeFilter: {
            ...(filterReturnObject?.trainTypeFilter ?? {}),
            heading: _label('train_types'),
            id: 'trainTypeFilter',
          },
        };
        break;
      case 'departureTimeFilter':
        filterReturnObject = {
          ...filterReturnObject,
          departureTimeFilter: {
            ...(filterReturnObject?.departureTimeFilter ?? {}),
            heading: _label('departure_from_city', undefined, { city: originStation?.cityName }),
            id: 'departureTimeFilter',
          },
        };
        break;
      case 'arrivalTimeFilter':
        filterReturnObject = {
          ...filterReturnObject,
          arrivalTimeFilter: {
            ...(filterReturnObject?.arrivalTimeFilter ?? {}),
            heading: _label('arrival_in_city', undefined, { city: destinationStation?.cityName }),
            id: 'arrivalTimeFilter',
          },
        };
        break;
      case 'fromStnFilter':
        filterReturnObject = {
          ...filterReturnObject,
          fromStnFilter: {
            ...(filterReturnObject?.fromStnFilter ?? {}),
            heading: _label('stations_in_city', undefined, { city: originStation?.cityName }),
            id: 'fromStnFilter',
          },
        };
        break;
      case 'toStnFilter':
        filterReturnObject = {
          ...filterReturnObject,
          toStnFilter: {
            ...(filterReturnObject?.toStnFilter ?? {}),
            heading: _label('stations_in_city', undefined, { city: destinationStation?.cityName }),
            id: 'toStnFilter',
          },
        };
        break;
      default:
        break;
    }
  });
  return filterReturnObject;
};

export const filterTrains = (
  trainsList: Train[] = [],
  filterObject: FilterObject = {},
): Train[] => {
  let finalTrainList = trainsList.slice();
  try {
    Object.keys(filterObject)
      .map((filterKey) => filterObject?.[filterKey])
      .forEach((filter) => {
        const filterListValues = Object.keys(filter?.filterTypes).map(
          (filterTypeKey) => filter?.filterTypes?.[filterTypeKey],
        );
        const selectedFilterListValues = filterListValues.filter(
          (filterItem) => filterItem.selected,
        );
        const filteredTrainsList = finalTrainList.filter(
          (train) =>
            isEmpty(selectedFilterListValues) ||
            selectedFilterListValues.some((filter) => filter.condition(train)),
        );
        finalTrainList = intersectionWith(
          finalTrainList,
          filteredTrainsList,
          (obj1, obj2) => obj1.trainNumber === obj2.trainNumber,
        );
      });
  } catch (error) {
    console.log('Error in filterTrains: ', error);
  } finally {
    return finalTrainList ?? [];
  }
};

export const filterTickets = (
  trainsList: Train[] = [],
  filterObject: FilterObject = {},
): Train[] => {
  let finalTrainList = trainsList.slice();
  try {
    Object.keys(filterObject).forEach((filterKey) => {
      const filterConditions: TicketFilterCondition[] = [];
      const filterTypes = filterObject?.[filterKey]?.filterTypes;
      Object.keys(filterTypes).forEach((filterTypeKey) => {
        const filterType = filterTypes?.[filterTypeKey];
        if (filterType?.ticketFilterCondition && filterType?.selected)
          {filterConditions.push(filterType?.ticketFilterCondition);}
      });
      const filteredTrainList = finalTrainList.map((train) => {
        const filteredTbsAvailability = train.tbsAvailability.filter(
          (ticket) =>
            filterConditions.some((filterCondition) => filterCondition(ticket)) ||
            isEmpty(filterConditions),
        );
        return {
          ...train,
          tbsAvailability: filteredTbsAvailability,
        };
      });
      finalTrainList = filteredTrainList.filter((train) => train.tbsAvailability.length > 0);
    });
  } catch (error) {
    console.log('Error in filterTickets: ', error);
  } finally {
    return finalTrainList ?? [];
  }
};

export const clearSelectedFilter = (filterObject: FilterObject, selectedFilterId: string) => {
  const filterArr = Object.keys(filterObject).map((filterKey) => filterObject[filterKey]);
  const finalFilterObject = filterArr.reduce((acc, filter) => {
    if (stringCompare(filter.id, selectedFilterId)) {
      const filterTypesArr = Object.keys(filter.filterTypes).map(
        (filterTypeKey) => filter.filterTypes[filterTypeKey],
      );
      filterTypesArr.forEach((filterType) => {
        filterType.selected = false;
      });
      const updatedFilterTypes = filterTypesArr.reduce((acc, filterType) => {
        return {
          ...acc,
          [filterType.key]: filterType,
        };
      }, {} as FilterObjectFilterTypes);
      return {
        ...acc,
        [filter.id]: {
          ...filter,
          filterTypes: updatedFilterTypes,
        },
      };
    }
    const quickFilterFilterTypesArrWithSelectedFilter = Object.keys(filter.filterTypes)
      .map((filterTypeKey) => filter.filterTypes[filterTypeKey])
      .filter(
        (filterType) =>
          !isEmpty(filterType.parentFilterType) &&
          stringCompare(filterType.parentFilterType, selectedFilterId) &&
          filterType.selected,
      );
    if (!isEmpty(quickFilterFilterTypesArrWithSelectedFilter)) {
      const filterTypesArr = Object.keys(filter.filterTypes).map(
        (filterTypeKey) => filter.filterTypes[filterTypeKey],
      );
      filterTypesArr.forEach((filterType) => {
        if (
          !isEmpty(filterType?.parentFilterType) &&
          stringCompare(filterType?.parentFilterType, selectedFilterId) &&
          filterType.selected
        ) {
          filterType.selected = false;
        }
      });
      const updatedFilterTypes = filterTypesArr.reduce((acc, filterType) => {
        return {
          ...acc,
          [filterType.key]: filterType,
        };
      }, {} as FilterObjectFilterTypes);
      return {
        ...acc,
        [filter.id]: {
          ...filter,
          filterTypes: updatedFilterTypes,
        },
      };
    }
    return {
      ...acc,
      [filter.id]: filter,
    };
  }, {} as FilterObject);
  return finalFilterObject;
};
