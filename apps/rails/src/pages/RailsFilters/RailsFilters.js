import PropTypes from 'prop-types';
import React from 'react';
import {Image, StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import { Actions } from '../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import ViewPager from '@mmt/legacy-commons/Common/Components/ViewPager';
import SortPage from '../NewListing/Components/SortPage';
import FiltersPage from '../NewListing/Components/Filters/FiltersPage';
import { _label } from '../../vernacular/AppLanguage';
import { trackFilterBarEvent } from '../../railsAnalytics';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';

import closeIcon from '@mmt/legacy-assets/src/ic-headerclose-grey.webp';

const FiltersHeader = ({showClearButton, onCloseClick, onClearAllClick, numberOfTrains, id}) => {
  const subtitle = _label('count_trains_found', undefined, { count: numberOfTrains });
  return (
    <Card style={{
      marginHorizontal: 0,
      marginVertical: 0,
      height: 56,
      paddingRight: 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    }}>
      <View style={filterHeaderStyles.container}>
        <TouchableRipple onPress={onCloseClick} testID={`${id}_closeButton`}>
          <View style={filterHeaderStyles.imageContainer}>
            <Image source={closeIcon} style={filterHeaderStyles.image}/>
          </View>
        </TouchableRipple>
        <View style={filterHeaderStyles.textContainer} testID={`${id}_details`}>
          <Text style={[filterHeaderStyles.filterText, fontStyle('bold'), getLineHeight(18)]}>
            {_label('sort_and_filter')}
          </Text>
          <Text style={[filterHeaderStyles.noOfTrains, fontStyle('regular'), getLineHeight(12)]}>
            {subtitle}
          </Text>
        </View>
      </View>
      {
        showClearButton &&
        <TouchableRipple onPress={onClearAllClick} testID={`${id}_clearAllButton`}>
          <View>
            <Text style={[filterHeaderStyles.clearAll, fontStyle('bold'), getLineHeight(12)]}>{_label('clear_all')}</Text>
          </View>
        </TouchableRipple>
      }
    </Card>
  );
};

const filterHeaderStyles = StyleSheet.create({
  container: {flexDirection: 'row', alignItems: 'center'},
  imageContainer: {
    height: 48, width: 48, justifyContent: 'center', alignItems: 'center',
  },
  image: {height: 24, width: 24},
  textContainer: {justifyContent: 'center', marginLeft: 4},
  filterText: { color: colors.defaultTextColor, fontSize: 18},
  noOfTrains: {color: colors.lightTextColor, fontSize: 12},
  clearAll: { fontSize: 12, color: colors.azure},
});

export class RailsFilters extends React.Component {
  constructor(props) {
    super(props);
    if (!this.props.hasTrainsList) {
      Actions.pop();
    }
    this.state = {
      selectedIndex: this.props?.selectedTab === 'filter' ? 1 : 0,
      id: 'newListingPage_railsFilterBar_onSortAndFilterButtonClicked',
    };
  }
  onDoneClick = () => {
    this.props.onDoneClick();
    Actions.pop();
  };

  changePage = (index) => () => {
    this.setState({selectedIndex: index});
  };

  changeSelectedIndex = (index) => {
    this.setState({selectedIndex: index});
  };

  render() {
    const {onQuickFilterClick, onFilterClick, onSortTrainClicked, selectedSortParam} = this.props;
    const {
      quickFilter,
      journeyClassFilter,
      trainTypeFilter,
      departureTimeFilter,
      arrivalTimeFilter,
      fromStnFilter,
      toStnFilter,
      ticketTypeFilter,
      quotaFilter,
      availabilityFilter, // remove this after dev, this was only for dev testing
    } = this.props.filterObject;
    const {labels} = this.props;
    const titles = [_label('sort'), _label('filter')];
    const noTrainsFlag = this.props.stagedTrainsList.length === 0;
    return (
      <View style={{ flex: 1 }} testID={this.state.id}>
        <FiltersHeader
          showClearButton={this.state.selectedIndex !== 0}
          labels={labels}
          onCloseClick={this.props.onCloseClick}
          numberOfTrains={this.props.stagedTrainsList.length}
          onClearAllClick={this.props.onClearAllFiltersClicked}
          id={`${this.state.id}_header`}
        />
        <View style={{flexDirection: 'row'}}>
          {
            titles.map((title, index) => (
              <TouchableOpacity
                style={index === this.state.selectedIndex ? pagerStyles.selectedTitleContainer :
                  pagerStyles.titleContainer}
                key={index}
                onPressIn={()=>{if (index === 1) {trackFilterBarEvent('mob_rail_listing_filters_click');}}}
                onPress={this.changePage(index)}
              >
                <View testID={`${this.state.id}_${title.toLowerCase()}Option`}>
                  <Text style={index === this.state.selectedIndex ? [
                      pagerStyles.indicatorSelectedText,
                      fontStyle('bold'),
                    ] : [
                      pagerStyles.indicatorText,
                      fontStyle('regular'),
                    ]}>
                    {title}
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          }
        </View>
        <ViewPager
          count={titles.length}
          selectedIndex={this.state.selectedIndex}
          onSelectedIndexChange={this.changeSelectedIndex}
        >
          <View style={{flex: 1}}>
            <SortPage labels={labels} onSortClicked={onSortTrainClicked} selectedSortParam={selectedSortParam} id={`${this.state.id}_sort`} />
          </View>
          <View style={{flex: 1,backgroundColor: colors.white}}>
            <FiltersPage
              labels={labels}
              noTrainsFlag={noTrainsFlag}
              quickFilter={quickFilter}
              journeyClassFilter={journeyClassFilter}
              trainTypeFilter={trainTypeFilter}
              departureTimeFilter={departureTimeFilter}
              arrivalTimeFilter={arrivalTimeFilter}
              fromStnFilter={fromStnFilter}
              toStnFilter={toStnFilter}
              onQuickFilterClick={onQuickFilterClick}
              onFilterClick={onFilterClick}
              ticketTypeFilter={ticketTypeFilter}
              quotaFilter={quotaFilter}
              availabilityFilter ={availabilityFilter}
              id={`${this.state.id}_filters`}
            />
          </View>
        </ViewPager>
        <TouchableRipple onPress={this.onDoneClick}>
          <View style={{
            height: 68, width: '100%',
          }}
            testID={`${this.state.id}_applyButton`}
          >
            <LinearGradient
              style={{
                borderRadius: 4, height: 44, margin: 12, alignItems: 'center', justifyContent: 'center',
              }}
              colors={['#53B2FE', '#065AF3']}
              start={{x: 0.0, y: 0.0}}
              end={{x: 1.0, y: 0.0}}
            >
              <Text style={{
                fontSize: 16, fontWeight: 'bold', color: colors.white, backgroundColor: colors.transparent, ...fontStyle('regular'), ...getLineHeight(16),
              }}
              >{_label('apply')}
              </Text>
            </LinearGradient>
          </View>
        </TouchableRipple>
      </View>
    );
  }
}

const pagerStyles = {
  indicatorText: {
    color: colors.defaultTextColor,
  },
  indicatorSelectedText: {
    color: colors.black,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 16,
    paddingBottom: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.lightGrey,
  },
  selectedTitleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 16,
    paddingBottom: 12,
    borderBottomWidth: 4,
    borderBottomColor: colors.azure,
  },
};

RailsFilters.propTypes = {
  hasTrainsList: PropTypes.bool,
  selectedTab: PropTypes.string,
  onDoneClick: PropTypes.func,
  onQuickFilterClick: PropTypes.func,
  onFilterClick: PropTypes.func,
  onSortTrainClicked: PropTypes.func,
  selectedSortParam: PropTypes.any,
  filterObject: PropTypes.shape({
    quickFilter: PropTypes.any,
    journeyClassFilter: PropTypes.any,
    trainTypeFilter: PropTypes.any,
    departureTimeFilter: PropTypes.any,
    arrivalTimeFilter: PropTypes.any,
    fromStnFilter: PropTypes.any,
    toStnFilter: PropTypes.any,
    ticketTypeFilter: PropTypes.any,
    quotaFilter: PropTypes.any,
    availabilityFilter: PropTypes.any,
  }),
  labels: PropTypes.object,
  stagedTrainsList: PropTypes.array,
  onCloseClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  initTrainSchedule: PropTypes.func,
  trainNumToSearch: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onHardBackPressed: PropTypes.func,
  trainNumber: PropTypes.string,
  trainName: PropTypes.string,
  daysOfRun: PropTypes.array,
  trainClasses: PropTypes.array,
  schedule: PropTypes.array,
  totalDuration: PropTypes.string,
  numberOfStops: PropTypes.number,
  tsViewState: PropTypes.oneOf(['LOADING','NO_INTERNET','ERROR','SHOW_DETAIL']),
  tsErrorMsg: PropTypes.string,
  showLoader: PropTypes.bool,
  disclaimer: PropTypes.string,
  onBackIconPressed: PropTypes.func,
};

FiltersHeader.propTypes = {
  numberOfTrains: PropTypes.number.isRequired,
  onClearAllClick: PropTypes.func.isRequired,
  onCloseClick: PropTypes.func.isRequired,
  id: PropTypes.string,
  showClearButton: PropTypes.bool,
};
