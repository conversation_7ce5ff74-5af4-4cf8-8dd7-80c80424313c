import type { AvailabilityTypeKeys } from '../Types/AvailabilityType';
import type { QuotaTypeKeys } from '../Types/QuotaType';

export interface TbsAvailability {
  className: string;
  quota: QuotaTypeKeys;
  availablityType: AvailabilityTypeKeys | null;
  confirmationGuaranteeText: string | null;
  freeCancellationText: string | null;
}

export interface Train {
  trainNumber: string;
  avlClasses: string[];
  trainType: string[];
  departureDateAndTime: Date;
  arrivalDateAndTime: Date;
  tbsAvailability: TbsAvailability[];
  frmStnCode: string;
  frmStnName: string;
  toStnCode: string;
  toStnName: string;
}

export type TrainFilterCondition = (train: Train) => boolean;

export type TicketFilterCondition = (ticket: TbsAvailability) => boolean;

export interface FilterObjectFilterTypesValue {
  key: string;
  heading: string;
  subHeading?: string;
  selected: boolean;
  noOfTrains?: number;
  condition: TrainFilterCondition;
  ticketFilterCondition?: TicketFilterCondition;
  id?: string; // quickFilters
  filterList?: string[]; // quickFilters
  parentFilterType?: string; // quickFilters
}

export interface FilterObjectFilterTypes { [key: string]: FilterObjectFilterTypesValue }

export interface FilterObjectValue {
  heading: string;
  filterTypes: FilterObjectFilterTypes;
  id: string;
  filterTypesArray: string[];
}

export interface FilterObject { [key: string]: FilterObjectValue }
