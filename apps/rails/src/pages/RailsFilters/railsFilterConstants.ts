const timeFilterTypes = ['nightFilter', 'morningFilter', 'afternoonFilter', 'eveningFilter'];

export const getTimeFilterType = (hours: number) => timeFilterTypes?.[Math.floor(hours / 6)] ?? '';

const filterBarIcons = {
  // journey_class: AvailabilityOff,
  // ac: AvailabilityOff,
  // availability: AvailabilityOff,
  // available: AvailabilityOff,
  // timing: AvailabilityOff,
  // sort: AvailabilityOff,
  // filters: AvailabilityOff,
  // ac: require('src/Assets/Rails/rails_class_off.svg'),
  // availability: require('src/Assets/rails_availability_filter.webp'),
  // available: require('src/Assets/rails_availability_filter.webp'),
  // timing: require('src/Assets/rails_timing_filter.webp'),
  // sort: require('src/Assets/rails_sort.webp'),
  // filters: require('src/Assets/rails_filter.webp'),
};

export const getFilterIcon = (id: string) => filterBarIcons?.[id];
