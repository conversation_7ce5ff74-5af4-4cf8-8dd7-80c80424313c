import React from 'react';
import {Image, ScrollView, Text, View} from 'react-native';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import CtaButton from '../User/Common/CtaButton';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';
import ASSETS from '../../Utils/Assets/RailsAssets';
import PropTypes from 'prop-types';

const UGCMismatchError = props => (
  <ScrollView style={style.container}>
    <View style={{alignItems: 'center'}}>
      <Image
        style={{
          width: 300,
          height: 200,
        }}
        source={ASSETS.icLogin}
      />
      <Text style={[style.latoBold24, style.marginTop24, fontStyle('bold'), getLineHeight(24)]}>
        {_label('login_again')}
      </Text>

      <Text style={[style.latoRegular14, style.marginTop20, {textAlign: 'center'}, fontStyle('regular'), getLineHeight(14) ]}>
        {_label('different_mmt_acc')}
      </Text>

      <View style={{
        paddingTop: 30,
        alignItems: 'center',
      }}>
        <CtaButton
          label="Close"
          onPress={() => {
            if (isIos()) {
              ViewControllerModule.thankyouDismiss(props.rootTag);
            } else {
              GenericModule.openRails({});
            }
          }}
          btnStyle={{
            borderWidth: 0,
            borderRadius: 30,
            height: 44,
            width: 180,
            paddingHorizontal: normalisePx(0),
          }}
          textStyle={[{
            color: colors.white,
            fontSize: 16,
          }, fontStyle('bold'), getLineHeight(16)]}
        />
      </View>
    </View>
  </ScrollView>
);

UGCMismatchError.propTypes = {
  rootTag: PropTypes.number,
};

export default UGCMismatchError;

const style = {
  container: {
    marginTop: 80,
    marginHorizontal: 20,
  },
  greyErrorBox: {
    width: '100%',
    height: 200,
    backgroundColor: colors.lightGray,
  },
  latoBold24: {
    fontSize: 24,
    color: colors.black,
  },
  latoRegular14: {
    fontSize: 14,
    color: colors.black04,
  },
  marginTop24: {marginTop: 24},
  marginTop20: {marginTop: 20},
};
