import React from 'react';
import {Image, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import ugcStyle from './UGCDataCollectionCSS';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';

const RailsRating = ({ rating, selected = false, onClick }) => {
  const img = selected ? rating.filledImg : rating.emptyImg;
  return (
    <TouchableRipple
      onPress={() => {
        onClick(rating);
      }}
    >
      <View style={{alignItems: 'center'}}>
        <Image source={img} style={ugcStyle.icon} />
        <Text style={[ugcStyle.marginTop16, ugcStyle.latoMedium12, fontStyle('medium'), getLineHeight(12)]}>{rating.value}</Text>
        <Text style={[ugcStyle.marginTop16, ugcStyle.latoRegular16, fontStyle('regular'), getLineHeight(16)]}>{rating.key}
          <Text style={[ugcStyle.latoRegular12, fontStyle('regular','en'), getLineHeight(12)]}>/5</Text>
        </Text>
      </View>
    </TouchableRipple>
  );
};

RailsRating.propTypes = {
  rating: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
  selected: PropTypes.bool,
};

export default RailsRating;
