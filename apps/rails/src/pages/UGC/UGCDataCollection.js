import React from 'react';
import { Actions } from '../../navigation';
import PropTypes from 'prop-types';
import {BackHandler, Image, ScrollView, Text, View} from 'react-native';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import RailsFeedbackCard from './RailsFeedbackCard';
import RailsRating from './RailsRating';
import isNull from 'lodash/isNull';
import isEmpty from 'lodash/isEmpty';
import ugcStyle,{textStyle} from './UGCDataCollectionCSS';
import ratingConst, {getRatingConst} from '../Constants/Ratings';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import CtaButton from '../User/Common/CtaButton';
import DisabledCTAButton from '../User/Common/DisabledCTAButton';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import {getFormattedDateFromMillis} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {getDefaultHeadersWithMMTAuth} from '../../Utils/RailsConstant';
import fetch2 from '../../fetch2';
import railsConfig from '../../RailsConfig';
import MultiStyleVernacText from '../../vernacular/MultiStyleVernacText';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';

import longArrow from '@mmt/legacy-assets/src/ic_arrow_right_long.webp';
import feedbackIcon from '@mmt/legacy-assets/src/ic-feedback.webp';


const ratings = [ratingConst.TERRIBLE, ratingConst.BAD,
  ratingConst.OKAY, ratingConst.GOOD, ratingConst.GREAT];
const submitFeedbackApi = railsConfig.ugcSubmitFeedback;

class UGCDataCollection extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedRating: null,
      showFeedbackCard: false,
      isFeedbackPositive: false,
      enableCTA: false,
      selectedOptions: [],
      feedbackComment: '',
      title: 'What went well',
    };
  }

  UNSAFE_componentWillMount() {
    let ratingNum = 3;
    try {
      ratingNum = parseInt(this.props.rating, 10);
    } catch (e) {
      console.log('error in parsing rating', e);
    }
    const rating = getRatingConst(ratingNum);
    this._onRatingSelected(rating);
  }

  _isRatingSelected = (rating, selectedRating) => {
    if (isEmpty(rating) || isEmpty(selectedRating)) {
      return false;
    }
    return selectedRating.key === rating.key;
  };

  _onRatingSelected = (rating) => {
    let title = '';
    if (rating.key > 3) {
      title = 'What went well';
    } else {
      title = 'What went wrong';
    }
    this.setState({
      selectedRating: rating,
      showFeedbackCard: true,
      isFeedbackPositive: rating.key > 3,
      title,
    }, () => this._enableCTA());
  };

  _enableCTA = () => {
    this.setState({
      enableCTA: !isNull(this.state.selectedRating),
    });
  };

  _update = (selectedOptions, feedbackComment) => {
    this.setState({
      selectedOptions,
      feedbackComment,
    });
  };
  _onDone = async () => {
    const {selectedRating, feedbackComment, selectedOptions} = this.state;
    const requestBody = {
      mmtId: this.props.mmtId,
      ratingByUser: selectedRating.key,
      feedbackOptionsSelected: selectedOptions,
      journeyDescription: feedbackComment,
    };

    const response = await fetch2(submitFeedbackApi, {
      method: 'POST',
      headers: getDefaultHeadersWithMMTAuth(this.props.mmtAuth),
      body: JSON.stringify(requestBody),
    });
    if (response.status === 200) {
      const jsonResponse = await response.json();
      if (!isEmpty(jsonResponse) && jsonResponse.submitStatusSuccess) {
        Actions.feedbackSubmitSuccess();
      } else {
        Actions.feedbackSubmitFailure();
      }
    } else {
      Actions.feedbackSubmitFailure();
    }
  };

  render() {
    const {
      sourceStationName, destinationStationName, trainName, 
      journeyDate, wentWellFeedbackOptions, wentWrongFeedbackOptions,
    } = this.props.feedbackOptionsResponse;
    const {selectedRating, title} = this.state;
    const date = getFormattedDateFromMillis(journeyDate, 'D MMM, YYYY');
    return (
      <ScrollView style={[ugcStyle.flex1, ugcStyle.marginTop32]}>
        <Card
          style={ugcStyle.headerContainer}
          elevation={0}
        >
          <TouchableRipple
            onPress={() => {
              if (isIos()) {
                ViewControllerModule.popViewController(this.props.rootTag);
              } else {
                BackHandler.exitApp();
              }
            }}
          >
            <Image style={ugcStyle.backIcon} source={backIcon}/>
          </TouchableRipple>

          <Text style={[ugcStyle.headerText, textStyle.getHeaderTextFontStyle(), getLineHeight(14)]}>
            {_label('write_a_review')}
          </Text>
        </Card>
        <View style={ugcStyle.container}>
          <Text
            style={[ugcStyle.marginTop32, ugcStyle.latoLight28, fontStyle('light'), getLineHeight(21)]}>
              {_label('hi_user', undefined, { firstName: this.props.firstName })}
            </Text>

          <Text style={[ugcStyle.marginTop2, ugcStyle.latoBold12, fontStyle('bold'), getLineHeight(12)]}>
            {_label('review_your_recent_journey')}
          </Text>

          <View style={[ugcStyle.marginTop4, ugcStyle.flexDirectionRow, {flexWrap: 'wrap'}]}>
            <Text style={[ugcStyle.latoBold16, fontStyle('bold'), getLineHeight(16)]}>{sourceStationName}</Text>
            <Image style={{margin: 7}} source={longArrow}/>
            <Text style={[ugcStyle.latoBold16, fontStyle('bold'), getLineHeight(16)]}>{destinationStationName}</Text>
          </View>

          <Text style={[ugcStyle.latoRegular12, ugcStyle.marginTop2, fontStyle('regular'), getLineHeight(12)]}>{trainName}</Text>

          <Text style={[ugcStyle.latoRegular9, ugcStyle.marginTop2, fontStyle('regular'), getLineHeight(9)]}>{date}</Text>

          <Text style={[ugcStyle.marginTop32, ugcStyle.latoBold16, fontStyle('bold'), getLineHeight(16)]}>
            {_label('how_was_your_train_exp')}
          </Text>

          <View
            style={[ugcStyle.marginTop16, ugcStyle.flexDirectionRow, {justifyContent: 'space-between'}]}>
            {
              ratings.map(rating => (
                <RailsRating
                  rating={rating}
                  selected={this._isRatingSelected(rating, selectedRating)}
                  key={rating.key}
                  onClick={this._onRatingSelected}
                />
              ))
            }
          </View>

          {this.state.showFeedbackCard && this.state.isFeedbackPositive &&
          <RailsFeedbackCard
            title={title}
            feedbackOptions={wentWellFeedbackOptions}
            update={this._update}
          />
          }

          {this.state.showFeedbackCard && !this.state.isFeedbackPositive &&
          <RailsFeedbackCard
            title={title}
            feedbackOptions={wentWrongFeedbackOptions}
            update={this._update}
          />
          }

          <View style={[ugcStyle.marginTop32, ugcStyle.flexDirectionRow]}>
            <Image style={ugcStyle.feedbackIcon} source={feedbackIcon}/>
            <MultiStyleVernacText
                content = {_label('posting_publicly_as')}
                contentHorizontal = {true}
                params = {{
                  firstName : this.props.firstName,
                }}
                defaultContentStyle = {[ugcStyle.latoRegular12, fontStyle('regular'), getLineHeight(12)]}
                textStyles = {{
                  firstName : [ugcStyle.latoBold12 , fontStyle('bold'),{ lineHeight : 18 }, getLineHeight(12)],
                }}
            />
          </View>

          <View style={{marginTop: 16}}>
            {this.state.enableCTA &&
            <CtaButton
              label="Done"
              onPress={this._onDone}
              btnStyle={{
                borderWidth: 0,
                borderRadius: 0,
                height: 32,
                paddingHorizontal: normalisePx(0),
              }}
              textStyle={[{
                color: colors.white,
                fontSize: 12,
              }]}
            />
            }

            {!this.state.enableCTA &&
            <DisabledCTAButton
              label="Done"
              btnStyle={{
                borderWidth: 0,
                borderRadius: 0,
                height: 32,
                paddingHorizontal: normalisePx(0),
              }}
              textStyle={[{
                color: colors.white,
                fontSize: 12,
              }]}
            />
            }
          </View>
        </View>
      </ScrollView>
    );
  }
}

UGCDataCollection.propTypes = {
  feedbackOptionsResponse: PropTypes.object.isRequired,
  firstName: PropTypes.string.isRequired,
  rating: PropTypes.string.isRequired,
  mmtId: PropTypes.string.isRequired,
  mmtAuth: PropTypes.string.isRequired,
  rootTag: PropTypes.number,
};
export default UGCDataCollection;
