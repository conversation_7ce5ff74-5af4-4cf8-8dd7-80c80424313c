import React from 'react';
import {Image, ScrollView, Text, View} from 'react-native';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import CtaButton from '../User/Common/CtaButton';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';

import ovalGrey from '@mmt/legacy-assets/src/ic-fp-tick-large.webp';
import PropTypes from 'prop-types';

const UGCFeedbackAlreadySubmittedError = (props) => (
  <ScrollView style={style.container}>
    <View style={{alignItems: 'center'}}>
      <Image
        style={{
          width: 200,
          height: 200,
        }}
        source={ovalGrey}
      />
      <Text style={[style.latoBold24, style.marginTop24, fontStyle('bold'), getLineHeight(24)]}>
        {_label('review_submitted')}
      </Text>

      <Text style={[style.latoRegular14, style.marginTop20, {textAlign: 'center'}, fontStyle('regular'), getLineHeight(14)]}>
        {_label('already_reviewed_train')}
      </Text>

      <View style={{
        paddingTop: 30,
        alignItems: 'center',
      }}>
        <CtaButton
          label="Close"
          onPress={() => {
            if (isIos()) {
              ViewControllerModule.thankyouDismiss(props.rootTag);
            } else {
              GenericModule.openRails({});
            }
          }}
          btnStyle={{
            borderWidth: 0,
            borderRadius: 30,
            height: 44,
            width: 180,
            paddingHorizontal: normalisePx(0),
          }}
          textStyle={[{
            color: colors.white,
            fontSize: 16,
          }, fontStyle('bold'), getLineHeight(16)]}
        />
      </View>
    </View>
  </ScrollView>
);

UGCFeedbackAlreadySubmittedError.propTypes = {
  rootTag: PropTypes.number,
};

export default UGCFeedbackAlreadySubmittedError;

const style = {
  container: {
    marginTop: 80,
    marginHorizontal: 20,
  },
  latoBold24: {
    fontSize: 24,
    color: colors.black,
  },
  latoRegular14: {
    fontSize: 14,
    color: colors.black04,
  },
  marginTop24: {marginTop: 24},
  marginTop20: {marginTop: 20},
};
