import React from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import {getUserDetails, isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import UGCDataCollection from './UGCDataCollection';
import fetch2 from '../../fetch2';
import {getDefaultHeadersWithMMTAuth} from '../../Utils/RailsConstant';
import { Actions } from '../../navigation';
import {ActivityIndicator, View} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import railsConfig from '../../RailsConfig';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { isIos } from '../../Utils/device';

const getFeedbackOptionsURL = railsConfig.ugcGetFeedbackOptions;

const JOURNEY_FEEDBACK_LOGIN_ERROR = 'JOURNEY_FEEDBACK_ERROR_01';
const JOURNEY_FEEDBACK_BOOKING_NOT_FOUND_ERROR = 'JOURNEY_FEEDBACK_ERROR_02';
const JOURNEY_FEEDBACK_UUID_MISMATCH_ERROR = 'JOURNEY_FEEDBACK_ERROR_03';
const JOURNEY_FEEDBACK_ALREADY_SUBMITTED_ERROR = 'JOURNEY_FEEDBACK_ERROR_04';

class UGCDataCollectionDeeplink extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isSuccess: false,
      feedbackOptionsResponse: {},
      firstName: null,
      mmtAuth: null,
    };
  }

  _loadUserDetails = async (isLoggedIn) => {
    if (!isLoggedIn) {
      return null;
    }
    const userDetails = await getUserDetails();
    return userDetails;
  };

  async UNSAFE_componentWillMount() {
    const isLoggedIn = await isUserLoggedIn();

    if (!isLoggedIn) {
      Actions.userLoginError();
      return;
    }
    const userDetails = await this._loadUserDetails(isLoggedIn);
    const mmtAuth = get(userDetails, 'mmtAuth', null);
    const firstName = get(userDetails, 'firstName', null);

    const requestBody = {
      mmtId: this.props.mmtId,
    };
    try {
      const response = await fetch2(getFeedbackOptionsURL, {
        method: 'POST',
        headers: getDefaultHeadersWithMMTAuth(mmtAuth),
        body: JSON.stringify(requestBody),
      });
      if (response.status === 200) {
        const jsonResponse = await response.json();
        if (!isEmpty(jsonResponse.error) && !isEmpty(jsonResponse.error.errorCode)) {
          if (jsonResponse.error.errorCode === JOURNEY_FEEDBACK_LOGIN_ERROR) {
            Actions.userLoginError();
          } else if (jsonResponse.error.errorCode === JOURNEY_FEEDBACK_UUID_MISMATCH_ERROR) {
            Actions.uGCMismatchError();
          } else if (jsonResponse.error.errorCode === JOURNEY_FEEDBACK_BOOKING_NOT_FOUND_ERROR) {
            Actions.uGCBookingNotFoundError();
          } else if (jsonResponse.error.errorCode === JOURNEY_FEEDBACK_ALREADY_SUBMITTED_ERROR) {
            Actions.uGCFeedbackAlreadySubmittedError();
          }
        } else {
          this.setState({
            feedbackOptionsResponse: jsonResponse,
            firstName,
            mmtAuth,
            isSuccess: true,
          });
        }
      }
    } catch (e) {
      console.log('Error ', e);
      if (isIos()) {
        ViewControllerModule.thankyouDismiss(this.props.rootTag);
      } else {
        GenericModule.openRails({});
      }
    }
  }

  render() {
    return (
      <View style={{flex: 1}}>
        {this.state.isSuccess &&
        <UGCDataCollection
          feedbackOptionsResponse={this.state.feedbackOptionsResponse}
          firstName={this.state.firstName}
          rating={this.props.rating}
          mmtId={this.props.mmtId}
          mmtAuth={this.state.mmtAuth}/>
        }

        {!this.state.isSuccess &&
        <Loader/>
        }
      </View>
    );
  }
}

UGCDataCollectionDeeplink.propTypes = {
  mmtId: PropTypes.string,
  rating: PropTypes.string,
  rootTag: PropTypes.number,
};


const Loader = () => (
  <View style={{
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  }}
  >
    <LinearGradient
      colors={['#ffffff', '#ffffff']}
      start={{
        x: 0.0,
        y: 0.0,
      }}
      end={{
        x: 1.0,
        y: 0.0,
      }}
      style={{
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        height: 36,
        width: 36,
        borderRadius: 100,
      }}
    >
      <ActivityIndicator
        styleAttr="Inverse"
        color={colors.azure}
        style={{
          margin: 4,
          height: 36,
          width: 36,
        }}
        size="large"
      />
    </LinearGradient>
  </View>
);
export default UGCDataCollectionDeeplink;
