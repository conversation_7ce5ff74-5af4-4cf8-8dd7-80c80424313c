import filter from 'lodash/filter';
import isEmpty from 'lodash/isEmpty';
import includes from 'lodash/includes';
import React from 'react';
import PropTypes from 'prop-types';
import {Text, View} from 'react-native';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import ugcStyle from './UGCDataCollectionCSS';
import { _label } from '../../vernacular/AppLanguage';
import {checkEnglishKeyboard, fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import SimpleTextInput from '@mmt/legacy-commons/Common/Components/Inputs/SimpleTextInput';

class RailsFeedbackCard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedOptions: [],
      feedbackComment: '',
      englishError : '',
    };
  }

  _onOptionSelected = (option, selected) => {
    let {selectedOptions, feedbackComment} = this.state;
    if (selected) {
      selectedOptions = [...selectedOptions, option];
    } else {
      selectedOptions = filter(selectedOptions, o => o !== option);
    }
    this.setState({
      selectedOptions,
    }, () => this.props.update(selectedOptions, feedbackComment));
  };

  _onChangeText = (feedbackComment) => {
    const {selectedOptions} = this.state;
    this.setState({
      feedbackComment,
    }, () => this.props.update(selectedOptions, feedbackComment));
    checkEnglishKeyboard('englishError',feedbackComment,this);
  };

  render() {
    const {title, feedbackOptions} = this.props;
    const {selectedOptions} = this.state;
    return (
      <View style={ugcStyle.marginTop32}>
        <Card elevation={2}>
          <View style={{
            paddingHorizontal: 16,
            paddingVertical: 32,
          }}
          >
            <Text style={[ugcStyle.latoBlack16, fontStyle('black'), getLineHeight(16)]}>{title}</Text>
            <Text style={[ugcStyle.marginTop2, ugcStyle.latoItalic10]}>
              {_label('select_all_that_apply')}
            </Text>

            <View style={[ugcStyle.marginTop16, ugcStyle.flexDirectionRow, {flexWrap: 'wrap'}]}>
              {!isEmpty(feedbackOptions) &&
              feedbackOptions.map(feedbackOption => (
                <FeedbackOption
                  option={feedbackOption}
                  onClick={this._onOptionSelected}
                  selected={includes(selectedOptions, feedbackOption)}
                  key={feedbackOption}
                />
              ))
              }
            </View>
          </View>

          <View style={ugcStyle.line} />

          <View style={{
            paddingHorizontal: 16,
            paddingVertical: 32,
          }}
          >
            <Text style={[ugcStyle.latoBlack16, fontStyle('black'), getLineHeight(16)]}>
              {_label('describe_your_journey')}
            </Text>
            <Text style={[ugcStyle.marginTop2, ugcStyle.latoItalic10, fontStyle('italic'), getLineHeight(10)]}>
              {_label('optional')}
            </Text>


            <TouchableRipple onPress={() => {
              if (!isEmpty(this._textInput)) {
                this._textInput.focus();
              }
            }}
            >
              <View style={ugcStyle.textInputContainer}>
                <SimpleTextInput
                  refProp ={(ref) => {
                    this._textInput = ref;
                  }}
                  inputStyle={[ugcStyle.feedbackCommentInput, ugcStyle.latoRegular12, fontStyle('regular'), getLineHeight(12)]}
                  underlineColorAndroid="transparent"x
                  value={this.state.feedbackComment}
                  onChangeText={this._onChangeText}
                  placeholder={_label('share_your_journey_story')}
                  multiline
                  maxLength={250}
                  error = {this.state.englishError}
                  numberOfLines = {3}
                />
              </View>
            </TouchableRipple>
          </View>
        </Card>
      </View>
    );
  }
}

const FeedbackOption = ({ option, onClick, selected = false }) => (
  <Card
    style={selected ? {backgroundColor: colors.azure} : {}}
  >
    <TouchableRipple
      onPress={() => onClick(option, !selected)}
    >
      <Text
        style={[ugcStyle.padding8, ugcStyle.latoRegular10, fontStyle('regular'), getLineHeight(10),selected ? {color: colors.white} : {}]}
      >{option}
      </Text>
    </TouchableRipple>
  </Card>
);


FeedbackOption.propTypes = {
  option: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  selected: PropTypes.bool,
};

RailsFeedbackCard.propTypes = {
  title: PropTypes.string.isRequired,
  feedbackOptions: PropTypes.array.isRequired,
  update: PropTypes.func.isRequired,
};
export default RailsFeedbackCard;
