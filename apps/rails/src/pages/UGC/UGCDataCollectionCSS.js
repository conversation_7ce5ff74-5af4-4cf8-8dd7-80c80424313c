import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {StyleSheet} from 'react-native';
import {fontStyle} from '../../vernacular/VernacularUtils';

export const textStyle = {
  getHeaderTextFontStyle : () => {
      return fontStyle('bold');
  },
};
const ugcStyle = {
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    marginVertical: 0,
    marginHorizontal: 0,
    justifyContent: 'flex-start',
    height: 32,
    borderBottomWidth: 2,
    borderColor: '#e7e7e7',
    paddingHorizontal: 16,
  },
  marginTop32: {
    marginTop: 32,
  },
  marginTop2: {
    marginTop: 2,
  },
  marginTop16: {
    marginTop: 16,
  },
  marginTop4: {
    marginTop: 4,
  },
  flex1: {
    flex: 1,
  },
  flexDirectionRow: {
    flexDirection: 'row',
  },
  backIcon: {
    width: 16,
    height: 16,
  },
  feedbackIcon: {
    width: 24,
    height: 24,
  },
  padding: {
    paddingTop: 20,
  },
  headerText: {
    fontSize: 14,
    color: colors.black,
    paddingLeft: 16,
  },
  latoLight28: {
    fontSize: 21,
    color: colors.defaultTextColor,
  },
  latoBold12: {
    fontSize: 12,
    color: colors.black,
  },
  latoBold16: {
    fontSize: 16,
    color: colors.black,
  },
  latoRegular12: {
    fontSize: 12,
    color: colors.black04,
  },
  latoRegular16: {
    fontSize: 16,
    color: colors.black04,
  },
  latoRegular10: {
    fontSize: 10,
    color: colors.black04,
  },
  latoRegular9: {
    fontSize: 9,
    color: colors.black04,
  },
  latoBlack16: {
    fontSize: 16,
    color: colors.black04,
  },
  latoItalic10: {
    fontSize: 10,
    color: colors.black04,
  },
  padding8: {
    padding: 8,
  },
  line: {
    borderBottomColor: colors.lightestTextColor,
    borderBottomWidth: 1,
  },
  textInputContainer: {
    height: 105,
    borderWidth: StyleSheet.hairlineWidth * 2,
    borderRadius: 5,
    borderColor: '#4b4b4b',
    marginTop: 16,
  },
  icon: {
    width: 48,
    height: 48,
  },
  latoMedium12: {
    fontSize: 12,
    color: colors.black,
  },
  feedbackCommentInput : {
    paddingHorizontal: 8,
    paddingTop : 2,
    paddingBottom : 0,
    height : 70,
    borderWidth : 0,
  },
};

export default ugcStyle;
