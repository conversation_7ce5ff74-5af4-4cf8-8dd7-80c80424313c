import React from 'react';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import { Image, ScrollView, Text, View } from 'react-native';
import CtaButton from '../User/Common/CtaButton';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';
import ASSETS from '../../Utils/Assets/RailsAssets';
import PropTypes from 'prop-types';

class FeedbackSubmitSuccess extends React.Component {
  render() {
    return (
      <ScrollView style={styles.container}>
        <View style={[styles.imagePadding, styles.alignItemsCenter]}>
          <Image
            style={{
              width: 100,
              height: 100,
            }}
            source={ASSETS.icFpTick}
          />
        </View>

        <View style={styles.alignItemsCenter}>
          <Text style={[styles.latoBold24, styles.paddingTop20, fontStyle('bold'), getLineHeight(24)]}>
            {_label('review_submitted')}
          </Text>
        </View>
        <View style={[styles.alignItemsCenter, styles.paddingTop18]}>
          <Text style={[styles.latoRegularText14, {textAlign: 'center'}, fontStyle('regular'), getLineHeight(14)]}>
            { _label('thank_you_for_sharing') }
          </Text>
        </View>

        <View style={{
          paddingTop: 132,
          alignItems: 'center',
        }}>
          <CtaButton
            label="Close"
            onPress={() => {
              if (isIos()) {
                ViewControllerModule.thankyouDismiss(this.props.rootTag);
              } else {
                GenericModule.openRails({});
              }
            }}
            btnStyle={{
              borderWidth: 0,
              borderRadius: 30,
              height: 44,
              width: 180,
              paddingHorizontal: normalisePx(0),
            }}
            textStyle={[{
              color: colors.white,
              fontSize: 16,
            }, fontStyle('bold'), getLineHeight(16)]}
          />
        </View>
      </ScrollView>
    );
  }
}

FeedbackSubmitSuccess.propTypes = {
  rootTag: PropTypes.number,
};

const styles = {
  container: {
    backgroundColor: colors.white,
    flex: 1,
    marginHorizontal: 16,
    marginTop: 16,
  },
  alignItemsCenter: {
    alignItems: 'center',
  },
  latoRegularText14: {
    fontSize: 14,
    color: colors.black04,
  },
  latoBold24: {
    fontSize: 24,
    color: colors.black,
  },
  paddingTop18: {paddingTop: 18},
  imagePadding: {
    paddingTop: 100,
  },
  paddingTop20: {
    paddingTop: 20,
  },
  paddingTop30: {
    paddingTop: 50,
  },
};


export default FeedbackSubmitSuccess;
