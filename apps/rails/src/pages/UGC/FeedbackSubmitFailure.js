import React from 'react';
import {Image, ScrollView, Text, View} from 'react-native';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import CtaButton from '../User/Common/CtaButton';
import { Actions } from '../../navigation';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import ASSETS from '../../Utils/Assets/RailsAssets';

const FeedbackSubmitFailure = () => (
  <ScrollView style={style.container}>
    <View style={{alignItems: 'center'}}>
      <Image
        style={{
          width: 200,
          height: 200,
        }}
        source={ASSETS.icGenericError}
      />
      <Text style={[style.latoBold24, style.marginTop24, fontStyle('bold'), getLineHeight(24)]}>
        {_label('something_went_wrong')}
      </Text>

      <Text style={[style.latoRegular14,fontStyle('regular'), getLineHeight(14), style.marginTop20, {textAlign: 'center'}]}>
        {_label('please_try_again')}
      </Text>

      <View style={{
        paddingTop: 30,
        alignItems: 'center',
      }}>
        <CtaButton
          label="Back"
          onPress={() => {
            Actions.pop();
          }}
          btnStyle={{
            borderWidth: 0,
            borderRadius: 30,
            height: 44,
            width: 180,
            paddingHorizontal: normalisePx(0),
          }}
          textStyle={[{
            color: colors.white,
            fontSize: 16,
          }, fontStyle('bold'), getLineHeight(16)]}
        />
      </View>
    </View>
  </ScrollView>
);

export default FeedbackSubmitFailure;

const style = {
  container: {
    marginTop: 80,
    marginHorizontal: 20,
  },
  latoBold24: {
    fontSize: 24,
    color: colors.black,
  },
  latoRegular14: {
    fontSize: 14,
    color: colors.black04,
  },
  marginTop24: {marginTop: 24},
  marginTop20: {marginTop: 20},
};
