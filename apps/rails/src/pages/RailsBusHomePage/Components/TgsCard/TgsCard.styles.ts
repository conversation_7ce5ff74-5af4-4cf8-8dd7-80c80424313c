import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({

    container: {
        width: '100%',
        height : 49,
        alignItems: 'flex-start',
    },
    tgsIcon: {
        width: '88.8%',
        height: 47,
        borderRadius: 5,
        marginTop: 18,
        left: '3.35%',
    },
    tgCard: {
        width: '100%',
        height: '100%',
    },
    separator: {
        width: '100%',
        height: 2,
        backgroundColor: colors.grayBg,
    },
    tgIcon: {
        height: 25,
        width: 37,
        left: '1.9%',
        top: 10,
    },
    tripsContainer: {
        height: 15,
        width: '39%',
        left: '55.4%',
        alignItems: 'center',
        backgroundColor: colors.lighterBlue,
        borderRadius: 4,
        marginTop: 8,
        marginBottom: 10,
        overflow: 'hidden',
    },
    tripsText: {
        top: -1,
        fontSize: 10,
        lineHeight: 16,
        color: colors.primary,
    },
    newTagContainer: {
        top: -39,
        left: '81%',
        height: 16,
        width: 40,
        borderRadius: 10,
        justifyContent: 'center',
        elevation: 99999,
    },
    newTagText: {
    color: colors.white,
    fontSize: 10,
    lineHeight: 12,
    textAlign: 'center',
    },
    tgTxtContainer: {
        position:'absolute',
        width: '40%',
        height: 18,
        top: 6,
        left: '14%',
    },
    tgTxt: {
        fontSize: 16,
        lineHeight: 16,
        color: colors.black,
    },
    tgDescContainer: {
        position:'absolute',
        width:'76%',
        height: 16,
        top: 25,
        left: '14%',
        display: 'flex',
        flexDirection: 'row',
    },
    descTxt1: {
        fontSize: 12,
        lineHeight: 16,
        color: colors.textGrey,
    },
    tgTrainIcon: {
        width: 10,
        height: 13.16,
        marginLeft: 4,
        marginRight: 4,
    },
    descTxt2: {
        position: 'relative',
        fontSize: 12,
        lineHeight: 16,
        color: colors.textGrey,
    },
});
