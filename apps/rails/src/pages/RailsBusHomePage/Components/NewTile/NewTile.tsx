import React from 'react';
import { Image, Text, View } from 'react-native';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import styles from '../../Styles/commonCss';
import { _label } from '../../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface NewTileProps {
  onClick: () => void;
  width: number;
  icon: unknown;
  label: string;
}

const NewTile = (props: NewTileProps) => {
  const { onClick, width, icon, label } = props;
  return (
    <View
      style={[{ marginBottom: 1 }, { width }]}
      testID={`new_tile_container_${label}`}
    >
      <TouchableRipple
       
        onPress={onClick}
       
        style={[styles.tile, { width }]}
        testID={`new_tile_touchable_ripple_${label}`}
      
        feedbackColor={colors.white}
      >
        <View style={styles.newTileContainer} testID={`new_tile_view_${label}`}>
          <View testID={`new_tile_icon_view_${label}`}>
            <Image source={icon} style={styles.labelIcon} testID={`new_tile_icon_${label}`} />
          </View>
          <View style={styles.tileLabelContainer} testID={`new_tile_label_container_${label}`}>
            <Text
              style={[styles.tileLable, fontStyle('regular')]}
              testID={`new_tile_label_${label}`}
            >
              {_label(label)}
            </Text>
          </View>
        </View>
      </TouchableRipple>
    </View>
  );
};

export default NewTile;
