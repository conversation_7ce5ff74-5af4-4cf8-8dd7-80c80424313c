export const data = {
  CPS: 'Check PNR\nStatus',
  LTS: 'Live Train\nStatus',
  STS: 'See Train\nSchedule',
  CSA: 'Check Train\nAvailability',
  LSS: 'Live Station',
  CPP: 'Train Coach\nPosition',
  FIT: 'Food in\nTrains',
  VCH: 'Vacant Chart',
  RMD: 'Rail Madad',
  AVLCAL: 'Availability Calendar',
  SEAT_CHART_URL: 'https://www.irctc.co.in/online-charts',
  FIT_URL: 'https://www.makemytrip.com/railways/food-ordering.html',
  LTS_WEB_URL: 'https://www.makemytrip.com/railways/liveStatus/',
  RAIL_MADAD_URL: 'https://railmadad.indianrailways.gov.in/',
  AVAILABILITY_CALENDAR_URL: 'https://www.makemytrip.com/railways/train-seat-availability',
  STS_TITLE: 'Train Route and Schedule',
  CPP_TITLE: 'Coach Position',
  FIT_TITLE: 'Food In Trains',
  PAGE_NAME: 'mob:funnel:railsris:railsrislanding',
  PAGE_KEY: 'railNewLanding',
  TRAIN: 'Book Trains Tickets',
  BUS: 'Book Bus Tickets',
  RIS_TITLE: 'train_information_services',
  NEW_RIS_TITLE: 'Get more info for your train journey',
  SSR_PAGE: 'mob_rail_landing_ssr',
  CSR_PAGE: 'mob_rail_landing_csr',
  LTS_LANDING_DEEPLINK: 'mmyt://react/?page=railLiveTrainStatusLanding&railsBusCommonLanding=${railsBusCommonLanding}&isFromNewLanding=true',
};

export const risIconPalette = {
  TRIP_GUARANTEE: 'trip_guarantee',
  CHECK_PNR: 'check_pnr_status',
  LIVE_TRAIN_STATUS: 'live_train_status',
  FOUR_COLUMNS: 4,
  THREE_COLUMNS: 3,
};

export const POWERED_BY = 'powered by chatGPT';
export const CAN_ASK = 'You can ask';
export const RAIL_GPT = 'powered by chatGPT';
export const GET_CONFIRMED_TICKET_OR = 'Get Confirmed ticket or';
export const GET_CONFIRMED_TICKET = 'Get Confirmed ticket';
export const OR = 'or';
export const XREFUND = '3x Refund';

