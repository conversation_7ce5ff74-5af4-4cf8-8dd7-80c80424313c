import { Actions } from '../../../navigation';
import { BackHandler, Platform } from 'react-native';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { trackClickEventProp61 } from '../Analytics';
import {
  getPokusConfig,
  getPokusConfigWaitingPromise,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import AbConfigKeyMappings, { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { isPhonePePlatform } from '../../../Utils/RisUtils';
import _isEmpty from 'lodash/isEmpty';
import { RISData, RISDataMap } from '../Interfaces';
import { data } from './constants';
import { getScreenWidth } from '../../Review/AvailabilityStatusChange/AvailabilityChangeUtils';
import {
  setDataToAsyncStorage,
  RAILS_BUS_COMMON_LANDING,
  MOB_RAIL_IRCTC_UPDATES_RIScard_CLICKED,
} from '../../../Utils/RailsConstant';
import { _label } from '../../../vernacular/AppLanguage';
import { isIos, isMweb, isApp } from '../../../Utils/device';
import BusSharedModuleHolder from '@mmt/bus-shared/src';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import ASSETS from '../../../Utils/Assets/RailsAssets';
import { showMealsWebView } from '@mmt/rails/src/RailsAbConfig';
import { logCommonLandingClickEvent } from '../../../PdtAnalytics/PdtAnalyticsV2/PdtRailsCommonLanding';
import { COMMON_LANDING_PDT_CLICK_EVENTS } from '../../../PdtAnalytics/PdtAnalyticsV2/PdtRailsCommonLanding/PdtCommonLandingConstants';

import bookTrainIcon from '@mmt/legacy-assets/src/ic_raillanding_booktrain.webp';
import busIcon from '@mmt/legacy-assets/src/bus_icon_whitebg.webp';
import pnrStatusIcon from '@mmt/legacy-assets/src/rails/checkPNRStatus.webp';
import newPnrStatusIcon from '@mmt/legacy-assets/src/rails/newCheckPNRStatus.webp';
import newIRCTCupdate from '@mmt/legacy-assets/src/rails/newIRCTCupdate.webp';
import tgsStatusIcon from '@mmt/legacy-assets/src/TG_RIS.webp';
import seatAvailabilityIcon from '@mmt/legacy-assets/src/ic_raillanding_seatavailability.webp';
import newTrainScheduleIcon from '@mmt/legacy-assets/src/new_ic_raillanding_seatavailability.webp';
import liveStationIcon from '@mmt/legacy-assets/src/rails/liveStation.webp';
import newLiveStationIcon from '@mmt/legacy-assets/src/rails/newLiveStation.webp';
import trainScheduleIcon from '@mmt/legacy-assets/src/rails/trainSchedule.webp';
import coachPositionIcon from '@mmt/legacy-assets/src/rails/coachPosition.webp';
import newCoachPositionIcon from '@mmt/legacy-assets/src/rails/newCoachPosition.webp';
import food_web_icon from '@mmt/legacy-assets/src/rails/foodInTrain.webp';
import vaccant_chart_icon from '@mmt/legacy-assets/src/vaccant_chart.webp';
import new_vaccant_chart_icon from '@mmt/legacy-assets/src/new_vaccant_chart.webp';
import liveTrainStatus from '@mmt/legacy-assets/src/rails/liveTrainStatus.webp';
import newLiveTrainStatus from '@mmt/legacy-assets/src/rails/newLiveTrainStatus.webp';
import rail_madad_icon from '@mmt/legacy-assets/src/rail_madad.webp';
import newRailMadadIcon from '@mmt/legacy-assets/src/new_rail_madad.webp';
import availabilityCalendar from '@mmt/legacy-assets/src/availabilityCalendar.webp';

export const getBookingLOBS = (railsBusCommonLanding: boolean = false) => {
  const bookingLOBSMap = {
    train: {
      label: 'book_train_tickets',
      icon: bookTrainIcon,
      onClick: () => onBookTrainClicked(railsBusCommonLanding),
    },
    bus: {
      label: 'book_bus_tickets',
      icon: busIcon,
      onClick: () => onBusClicked(railsBusCommonLanding),
    },
  };
  return bookingLOBSMap;
};

export const getRisAb = async () => {
  if (isApp()) {
    try {
      await getPokusConfigWaitingPromise(1000);
    } catch (error) {
      console.log('RIS ab error');
    }
  }

  const risOptions: string = getPokusConfig(
    PokusLobs.RAIL,
    AbConfigKeyMappings.rails_RIS_options_NEW,
    'CPS|FIT|LTS|STS|CSA|LSS|CPP|VCH|AVLCAL|IRCTCUPDATE',
  );
  const arr = risOptions.split('|');
  return arr;
};

export const getAdsAb = async () => {
  const adsOption: boolean = getPokusConfig(
    PokusLobs.RAIL,
    AbConfigKeyMappings.railsBusAdsOptions,
    false,
  );
  return adsOption;
};

export const onHardBackPress = () => {
  if (isIos()) {
    ViewControllerModule.thankyouDismiss();
  } else if (isMweb()) {
    //redirect back to phonepev2 landing page
    if (isPhonePePlatform()) {
      window.location.href = 'https://www.makemytrip.com?action=phonePeV2';
      return true;
    }
    window.location.href = 'https://www.makemytrip.com';
  } else {
    BackHandler.exitApp();
    // Actions.pop();
  }
  setDataToAsyncStorage(RAILS_BUS_COMMON_LANDING, null);
  return true;
};

export const getAppropriateFontSizeRIS = () => {
  const width = getScreenWidth();
  if (width < 360) {
    return 12;
  } else {
    return 14;
  }
};

export const getRisOptions = (
  options: string[],
  railsBusCommonLanding: boolean = false,
  isNewLanding: boolean = false,
): RISData[] => {
  if (!options) {
    return [];
  }
  const risOptionsData: RISData[] = [];
  const risDataMap: RISDataMap = getRISData(railsBusCommonLanding, isNewLanding);
  options.forEach((risOption) => {
    if (risDataMap[risOption] !== undefined) {
      risOptionsData.push(risDataMap[risOption]);
    }
  });
  if (_isEmpty(risOptionsData)) {
    return [];
  }

  return risOptionsData;
};

export const onTGSStatusClicked = () => {
  trackClickEventProp61(data.PAGE_NAME,'rails_tgs_icon_rislp_clicked');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.TG_BANNER_CLICKED);
  Actions.tgsLandingPage();
};

const openRISAnnounceMentsPages = () => {
  trackClickEventProp61(data.PAGE_NAME, MOB_RAIL_IRCTC_UPDATES_RIScard_CLICKED);
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.IRTCTC_UPDATES_RISCARD);
  Actions.openRISAnnounceMentPage();
};


const getRISData = (railsBusCommonLanding: boolean, isNewLanding: boolean) => {
  return {
    TGS: {
      icon: tgsStatusIcon,
      text: 'trip_guarantee',
      onClick: onTGSStatusClicked,
    },
    CPS: {
      icon: isNewLanding ? newPnrStatusIcon : pnrStatusIcon,
      text: 'check_pnr_status',
      onClick: (isRis : boolean) => onPnrStatusClicked(railsBusCommonLanding, isRis),
    },
    LTS: {
      icon: isNewLanding ? newLiveTrainStatus : liveTrainStatus,
      text: 'live_train_status',
      onClick: () => onLiveTrainStatusClicked(railsBusCommonLanding),
    },
    STS: {
      icon: isNewLanding ? newTrainScheduleIcon : trainScheduleIcon,
      text: 'see_train_schedule',
      onClick: () => onSeeTrainScheduleClicked(railsBusCommonLanding),
    },
    CSA: {
      icon: isNewLanding ? availabilityCalendar : seatAvailabilityIcon,
      text: 'check_train_availability',
      onClick: () => onSeeSeatAvailabilityClicked(railsBusCommonLanding),
    },
    LSS: {
      icon: isNewLanding ? newLiveStationIcon : liveStationIcon,
      text: 'live_station',
      onClick: () => onLiveStationClicked(railsBusCommonLanding),
    },
    CPP: {
      icon: isNewLanding ? newCoachPositionIcon : coachPositionIcon,
      text: 'train_coach_position',
      onClick: () => onCoachPositionClicked(railsBusCommonLanding, _label('train_coach_position')),
    },
    FIT: {
      icon: food_web_icon,
      text: 'food_in_trains',
      onClick: openFoodInTrainWebView,
    },
    VCH: {
      icon: isNewLanding ? new_vaccant_chart_icon : vaccant_chart_icon,
      text: 'vacant_chart',
      onClick: openVacantChartWebView,
    },
    RMD: {
      icon: isNewLanding ? newRailMadadIcon : rail_madad_icon,
      text: 'rail_madad',
      onClick: openRailMadadWebView,
    },
    AVLCAL: {
      icon: availabilityCalendar,
      text: 'Availability\nCalendar',
      onClick: openAvailabilityCalendarWebView,
    },
    IRCTCUPDATE: {
      icon: isNewLanding ? newIRCTCupdate : ASSETS.irctcNotifications,
      text: 'irctc_update',
      onClick: openRISAnnounceMentsPages,
    },
  };
};




export const onPnrStatusClicked = (railsBusCommonLanding: boolean, isRis: boolean) => {
  let trackEvent = 'railris_pnr_status_train&bus_icon_clicked';
  if (isRis) {
    trackEvent = 'railris_pnr_status_pnrstatus_icon_clicked';
  }
  trackClickEventProp61(data.PAGE_NAME, trackEvent);
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.PNR_STATUS);
  Actions.railsPNRStatusLanding({
    isFromNewLanding: true,
    railsBusCommonLanding,
  });
  // Actions.replace({
  //   key: 'railsPNRStatusLanding', params: {
  //     isFromNewLanding: true,
  //     railsBusCommonLanding,
  //   }
  // });
};

export const onLiveTrainStatusClicked = (railsBusCommonLanding: boolean) => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_live_train_status_clicked');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.LIVE_TRAIN_STATUS_CLICKED);
  if (Platform.OS === 'web') {
    const url = data.LTS_WEB_URL;
    window.location.href = url;
  } else {
    GenericModule.openDeepLink(data.LTS_LANDING_DEEPLINK.replace('${railsBusCommonLanding}',railsBusCommonLanding.toString()));
  }
};

const onSeeTrainScheduleClicked = (railsBusCommonLanding: boolean) => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_train_schedule_clicked');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.TRAIN_SCHEDULE_CLICKED);
  Actions.trainScheduleCheck({
    isFromNewLanding: true,
    title: data.STS_TITLE,
    railsBusCommonLanding,
  });
};

const onSeeSeatAvailabilityClicked = (railsBusCommonLanding: boolean) => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_train_availability_clicked');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.TRAIN_AVAILABILITY_CLICKED);
  Actions.rails({
    isFromNewLanding: true,
    railsBusCommonLanding,
  });
};

const onLiveStationClicked = (railsBusCommonLanding: boolean) => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_live_station_clicked');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.LIVE_STATION_CLICKED);
  Actions.railLiveStationLanding({
    isFromNewLanding: true,
    railsBusCommonLanding,
  });
};

const onCoachPositionClicked = (railsBusCommonLanding: boolean) => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_coach_position_clicked');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.COACH_POSITION_CLICKED);
  Actions.trainScheduleCheck({
    isCoachPosition: true,
    title: data.CPP_TITLE,
    isFromNewLanding: true,
    railsBusCommonLanding,
  });
};

const openFoodInTrainWebView = () => {
  const mealsWebView = showMealsWebView();
  if (mealsWebView) {
    Actions.openMealsFunnelWebView({
      pnr: '',
    });
  } else {
    Actions.openMealsPage();
  }
};

const openVacantChartWebView = () => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_vacantcharts_iconclicked');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.VACANTCHARTS_ICONCLICKED);
  Actions.openWebView({
    url: data.SEAT_CHART_URL,
    headerText: data.VCH,
    headerIcon: backIcon,
  });
};

const openRailMadadWebView = async () => {
  trackClickEventProp61(data.PAGE_NAME, RAIL_EVENTS.LANDING.RAILS_RAIL_MADAD_CLICKED);
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.RAIL_MADAD_CLICKED);
  const response = await getConfigStore(configKeys.RAILS_MASTER_CONFIG);
  const railMadadUrl = response?.railMadadUrl || data.RAIL_MADAD_URL;
  Actions.openWebView({
    url: railMadadUrl,
    headerText: data.RMD,
    headerIcon: backIcon,
  });
};

const openAvailabilityCalendarWebView = async () => {
  const availabilityCalendarUrl = data.AVAILABILITY_CALENDAR_URL;
  Actions.openAvailabilityCalendarWebView({
    url: availabilityCalendarUrl,
    headerText: data.AVLCAL,
    headerIcon: backIcon,
  });
};

const onBookTrainClicked = (railsBusCommonLanding: boolean) => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_book_train_tickets');
  logCommonLandingClickEvent(COMMON_LANDING_PDT_CLICK_EVENTS.BOOK_TRAIN_TICKETS);
  Actions.rails({
    isFromNewLanding: true,
    railsBusCommonLanding,
  });
};

const onBusClicked = (railsBusCommonLanding: boolean) => {
  trackClickEventProp61(data.PAGE_NAME, 'railris_book_bus_tickets');
  const busSharedResourceProvider = BusSharedModuleHolder.get();
  if (!busSharedResourceProvider) {
    throw new Error('Bus-Shared module not bootstrapped');
  }
  const busNavigation = busSharedResourceProvider.getBusSharedNavigation();
  busNavigation.openBus({ railsBusCommonLanding });
};
