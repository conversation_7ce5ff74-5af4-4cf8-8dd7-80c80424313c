// Do make changes carefully as same stylesheet is also used in all RIS Landing pages

import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { TextStyle, ViewStyle, ImageStyle, Dimensions,Platform } from 'react-native';
import {fontStyle} from '../../../vernacular/VernacularUtils';

const dimensions = Dimensions.get('window').width;

interface Styles {
  adfeed2Container: ViewStyle;
  adfeed1Container: ViewStyle;
  adsHeading: TextStyle;
  multiAdContainer: ViewStyle;
  container: ViewStyle;
  containerStatus: ViewStyle;
  dataComponent: ViewStyle;
  dateComponentLabel: TextStyle;
  date: TextStyle;
  month: TextStyle;
  day: TextStyle;
  form: ViewStyle;
  header: TextStyle;
  headingStyle: TextStyle;
  bookingContainer: ViewStyle;
  pnrFormContainer: ViewStyle;
  pnrInputContainer: ViewStyle;
  pnrEnterTitle: TextStyle;
  pnrInput: TextStyle;
  touchableDone: ViewStyle;
  doneButtonContainer: ViewStyle;
  doneButton: TextStyle;
  flex1: ViewStyle;
  searchSection: ViewStyle;
  searchSectionTitle: TextStyle;
  searchSectionMessage: TextStyle;
  searchSectionMessageTitle: TextStyle;
  searchSectionMessageSubtitle: TextStyle;
  seperatorLine: ViewStyle;
  pickerSectionHeaderContainer: ViewStyle;
  pickerSectionHeaderIcon: ImageStyle;
  pickerSectionHeaderText: TextStyle;
  pickerSectionItemContainer: ViewStyle;
  pickerSectionText: TextStyle;
  pickerSectionSubText: TextStyle;
  stationPickerSectionItemContainer: ViewStyle;
  stationPickerSectionTitle: TextStyle;
  stationPickerStationCode: TextStyle;
  datePickerSectionItemContainer: ViewStyle;
  headerContainer: ViewStyle;
  headerText: TextStyle;
  headerImage: ImageStyle;
  cardContainer: ViewStyle;
  lineOrange: ViewStyle;
  balloonImage: ImageStyle;
  textSubtitle: TextStyle;
  pnrTextTitle: TextStyle;
  pnrTextSubtitle: TextStyle;
  textDate: TextStyle;
  messageIcon: ImageStyle;
  calenderIcon: ImageStyle;
  cardPnrRecentContainer: ViewStyle;
  cardSeatAvailRecentContainer: ViewStyle;
  cardTrainRecentContainer: ViewStyle;
  horizontalCenterAligned: ViewStyle;
  trainRecentTextTitle: TextStyle;
  seatAvailRecentCardCityText: TextStyle;
  trainRecentTextSubtitle: TextStyle;
  lottieView: ImageStyle;
  lottieContainer: ViewStyle;
  newForm: ViewStyle;
  newBottomTile: ViewStyle;
  pnrLTSCardContainer: ViewStyle;
  pnrHeaderText: TextStyle;
  labelContainer: ViewStyle;
  tileLabelContainer: ViewStyle;
  card: ViewStyle;
  newBorder: ViewStyle;
  tile: ViewStyle;
  newCardContainer: ViewStyle;
  newTileContainer: ViewStyle;
  icon: ImageStyle;
  labelIcon: ImageStyle;
  label: TextStyle;
  tileLable: TextStyle;
  arrowIcon: ImageStyle;
  pnrTextDate: TextStyle;
  calendarContainer: ViewStyle;
  newHeading: TextStyle;
  newHeadingContainer: ViewStyle;
  multiBannerConatainer: ViewStyle;
  noAdContainer: ViewStyle;
  newIconBanner: ViewStyle;
  tgsBanner: ImageStyle;
  carousel: ViewStyle;
  tgsBannerContainer: ViewStyle;
}

// Modifications in this file has to be done
// carefully as this file is also used in all
// RIS Forms files
export const textStyles = {
    getSearchSectionTitleFontStyle : () => {
      return fontStyle('regular');
    },
    getSearchSectionMessageFontStyle : () => {
      return fontStyle('bold');
    },
    getSearchSectionMessageTitleFontStyle : () => {
      return fontStyle('bold');
    },
    getSearchSectionMessageSubtitleFontStyle : () => {
      return fontStyle('bold');
    },
    getDoneButtonFontStyle : () => {
      return fontStyle('bold');
    },
    getPickerSectionHeaderTextFontStyle : () => {
      return fontStyle('bold');
    },
    getPickerSectionTextFontStyle : () => {
      return fontStyle('regular');
    },
    getPickerSectionSubTextFontStyle : () => {
      return fontStyle('regular');
    },
    getStationPickerStationCodeFontStyle : () => {
      return fontStyle('bold');
    },
    getDateComponentLabelFontStyle : () => {
      return fontStyle('regular');
    },
    getDateFontStyle : () => {
      return fontStyle('bold');
    },
    getMonthFontStyle : () => {
      return fontStyle('regular');
    },
    getDayFontStyle : () => {
      return fontStyle('regular');
    },
    getLabelStyleFontStyle : () => {
      return fontStyle('regular');
    },
    getSectionTitleFontStyle : () => {
      return fontStyle('black');
    },
    getHeaderFontStyle : () => {
      return fontStyle('bold');
    },
    getDescriptionFontStyle : () => {
      return fontStyle('regular');
    },
    getHeadingStyleFontStyle : () => {
      return fontStyle('light');
    },
    getPnrEnterTitleFontStyle : () => {
      return fontStyle('regular');
    },
    getPnrInputFontStyle : () => {
      return fontStyle('bold');
    },
    getPnrDoneButtonFontStyle : () => {
      return fontStyle('bold');
    },
    getHeaderTextFontStyle : () => {
      return fontStyle('bold');
    },
    getTextSubtitleFontStyle : () => {
      return fontStyle('bold');
    },
    getPnrTextTitleFontStyle : () => {
      return fontStyle('black');
    },
    getPnrTextSubtitleFontStyle : () => {
      return fontStyle('regular');
    },
    getTextDateFontStyle : () => {
      return fontStyle('regular');
    },
    getTrainRecentTextTitle : () => {
      return fontStyle('black');
    },
    getSeatAvailRecentCardCityTextFontStyle : () => {
        return fontStyle('black');
    },
    getTrainRecentTextSubtitleFontStyle : () => {
      return fontStyle('regular');
    },
    getStationPickerSectionTitleFontStyle : () => {
        return fontStyle('bold');
    },
};

const styles: Styles = {
  container: {
    flex: 1,
    backgroundColor: colors.grayBg,
  },
  lottieView: {
    position: 'relative',
    width: 83,
  },
  lottieContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    marginLeft: 14,
    justifyContent: 'space-between',
    marginRight: 22,
    alignItems: 'center',
  },
  containerStatus: {
    paddingBottom: 48,
  },
  dataComponent: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    flex: 1,
  },
  dateComponentLabel: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  date: {
    fontSize: 32,
    color: colors.black04,
    width: 40,
    height: 38,
  },
  month: {
    marginTop: 4,
    fontSize: 12,
    color: colors.black,
  },
  day: {
    fontSize: 12,
    color: colors.disabledBtnBg,
  },
  newForm:{
    backgroundColor: colors.white,
    paddingVertical: 12,
    marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  newBottomTile:{
    backgroundColor: colors.white,
    marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  form: {
    backgroundColor: colors.white,
    paddingTop: 16,
  },
  header: {
    fontSize:13,
    flex: 1,
    color: colors.black,
  },
  headingStyle: {
    fontSize: 22,
    color: colors.black04,
  },
  bookingContainer: {
    borderRadius: 4,
    paddingHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    height: 70,
    backgroundColor: colors.lighterBlue,
  },
  pnrFormContainer: {
    paddingBottom: 48,
  },
  pnrInputContainer: {
    height: 153,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pnrLTSCardContainer: {
    marginLeft: 'auto',
    marginRight: 'auto',
    marginBottom: 15,
    width: dimensions - 40,
  },
  pnrEnterTitle: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  pnrInput: {
    width: '80%',
    color: colors.black,
    fontSize: 20,
    textAlign: 'center',
  },
  touchableDone: {
    position: 'absolute',
    bottom: 25,
    backgroundColor: colors.transparent,
    alignSelf: 'center',
  },
  doneButtonContainer: {
    height: 70,
    width: 70,
    borderRadius: 35,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  doneButton: {
    fontSize: 12,
    color: colors.white,
  },
  flex1: {
    flex: 1,
  },
  searchSection: {
    paddingHorizontal: 30,
    paddingVertical: 16,
    backgroundColor: colors.white,
  },
  searchSectionTitle: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  searchSectionMessage: {
    marginTop: 4,
    color: colors.disabledButton,
    fontSize: 20,
  },
  searchSectionMessageTitle: {
    marginTop: 4,
    color: colors.black,
    fontSize: 20,
  },
  searchSectionMessageSubtitle: {
    color: colors.lightTextColor,
    fontSize: 16,
  },
  seperatorLine: {
    backgroundColor: colors.lightGrey,
    height: 1,
  },
  pickerSectionHeaderContainer: {
    paddingTop: 15,
    paddingBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  pickerSectionHeaderIcon: {
    width: 20,
    height: 20,
    marginLeft: 14,
  },
  pickerSectionHeaderText: {
    fontSize: 12,
    marginLeft: 9,
    color: colors.lightTextColor,
  },
  pickerSectionItemContainer: {
    backgroundColor: colors.white,
    paddingTop: 12,
    paddingBottom: 20,
    paddingLeft: 44,
    paddingRight: 14,
  },
  pickerSectionText: {
    fontSize: 16,
    color: colors.defaultTextColor,
  },
  pickerSectionSubText: {
    fontSize: 14,
    color: colors.lightTextColor,
  },
  stationPickerSectionItemContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingVertical: 25,
    flexDirection: 'row',
  },
  stationPickerSectionTitle: {
    flex: 1,
    paddingRight: 8,
    fontSize: 16,
    color: colors.defaultTextColor,
  },
  stationPickerStationCode: {
    fontSize: 16,
    alignSelf: 'center',
    color: colors.lightTextColor,
  },
  datePickerSectionItemContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    paddingTop: 24,
  },
  headerContainer: {
    paddingStart: 14,
    paddingVertical: 14,
    width: 130,
  },
  headerText: {
    fontSize: 16,
    color: colors.defaultTextColor,
  },
  pnrHeaderText: {
    fontSize: 16,
    color: colors.white,
    marginLeft:'1%',
    marginTop:'4%',
    fontWeight:'600',
    lineHeight:16,
  },
  headerImage: {
    marginTop: 12,
  },
  labelContainer:{
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tileLabelContainer:{
    width: dimensions * 0.29,
    flexDirection: 'row',
    alignItems: 'center',
  },
  card: {
    height: 60,
    flexGrow: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
  },
  newBorder: {
    borderWidth: 1,
    borderColor: '#d8d8d8',
    borderRadius: 16,
  },
  tile: {
    height: 52,
    flexGrow: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
  },
  newCardContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 11,
    paddingVertical: 11,
    height: 60,
    width: '100%',
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 16,
    backgroundColor: colors.white,
  },
  newTileContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 15,
    paddingRight: 4,
    height: 52,
    width: '100%',
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 16,
    backgroundColor: colors.white,
  },
  icon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    flexGrow: 0,
  },
  labelIcon: {
    width: dimensions * 0.066,
    height: dimensions * 0.066,
    resizeMode: 'contain',
    flexGrow: 0,
  },
  label: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.black,
  },
  tileLable:{
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.4,
    width: dimensions * 0.29,
    color: colors.defaultTextColor,
  },
  arrowIcon: {
    width: 24,
    height: 24,
    tintColor: '#007AFF',
    resizeMode: 'contain',
  },
  cardContainer: {
    width: 252,
    minHeight: 80,
    marginHorizontal: 4,
    paddingLeft: 10,
    paddingVertical: 10,
  },
  lineOrange: {
    width: 3,
    height: 13,
    backgroundColor: '#ff5549',
    position: 'absolute',
    marginTop: 12,
  },
  balloonImage: {
    width: 63,
    height: 37,
  },
  textSubtitle: {
    fontSize: 12,
    color: colors.azure,
    marginTop: 8,
  },
  pnrTextTitle: {
    fontSize: 14,
    color: colors.white,
  },
  pnrTextSubtitle: {
    fontSize: 12,
    color: colors.white,
    marginTop: 2,
  },
  textDate: {
    marginLeft: 5,
    fontSize: 10,
    color: colors.defaultTextColor,
  },
  pnrTextDate: {
    marginLeft: 5,
    fontSize: 10,
    color: colors.white,
  },
  messageIcon: {
    width: 20,
    height: 20,
    position: 'absolute',
    right: 6,
    top: 6,
  },
  calenderIcon: {
    width: 9,
    height: 10,
  },
  calendarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 14,
  },
  cardPnrRecentContainer: {
    width: 163,
    minHeight: 80,
    paddingLeft: 10,
    paddingRight: 6,
    paddingVertical: 10,
    backgroundColor: '#ffffff26',
    marginRight:10,
    borderRadius:6,
  },
  cardSeatAvailRecentContainer: {
    minWidth: 163,
    maxWidth: 180,
    height: 70,
    marginHorizontal: 4,
    paddingLeft: 10,
    paddingRight: 6,
    paddingVertical: 10,
  },
  cardTrainRecentContainer: {
    width: 163,
    minHeight: 90,
    marginHorizontal: 4,
    paddingLeft: 10,
    paddingRight: 6,
    paddingVertical: 10,
  },
  horizontalCenterAligned: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trainRecentTextTitle: {
    fontSize: 14,
    color: colors.black,
  },
  newHeading:{
    fontWeight: '700',
    color:colors.defaultTextColor,
    fontSize: 18,
    lineHeight: 21.6,
  },
  newHeadingContainer: {
    paddingLeft: 16,
    paddingTop: 16,
    paddingBottom: 12,
  },
  seatAvailRecentCardCityText: {
    maxWidth: 75,
    fontSize: 14,
    color: colors.black,
  },
  trainRecentTextSubtitle: {
    marginTop: 2,
    fontSize: 12,
    color: colors.lightTextColor,
  },
  multiBannerConatainer: {
    backgroundColor: 'white',
    paddingVertical: 21,
    paddingHorizontal: 16,
    marginBottom: 5,
  },
  adfeed1Container: {
    backgroundColor: 'white',
    paddingVertical: 21,
    paddingHorizontal: 16,
    marginBottom: 5,
  },
  adfeed2Container: {
    backgroundColor: 'white',
    paddingVertical: 19,
    paddingHorizontal: 16,
    marginBottom: 5,
  },
  multiAdContainer: {
    backgroundColor: 'white',
    paddingVertical: 21,
    paddingLeft: 16,
    marginBottom: 5,
  },
  adsHeading: {
    fontSize: 18,
    color: 'black',
    lineHeight: 20,
    letterSpacing: 0.23,
    marginBottom: 11,
  },
  noAdContainer: {
    margin: 0,
    padding: 0,
    backgroundColor: 'transparent',
  },
  newIconBanner: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    height: 17,
    width: 36,
    position: 'absolute',
    right: 15,
    elevation: 99999,
    top: -4,
    zIndex: 99999,
  },
  tgsBanner: {
    height: 145,
    width: Dimensions.get('screen').width - 30,
    marginBottom: 'auto',
  },
  carousel: {
    height: 148,
    marginTop: 15,
    paddingLeft: 15,
    paddingRight: 15,
  },
  tgsBannerContainer: {
    height: 145,
    marginRight: Platform.select({ ios: 16 }),
  },
};
export default styles;
