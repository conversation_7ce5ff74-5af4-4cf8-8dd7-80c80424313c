{"nm": "Flow 3", "ddd": 0, "h": 20, "w": 84, "meta": {"g": "LottieFiles Figma v42"}, "layers": [{"ty": 4, "nm": "Rectangle 18352", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [41.5, 9.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [41.5, 9.5], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [41.5, 9.5], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [41.5, 9.5], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [41.5, 9.5], "t": 140}, {"s": [41.5, 9.5], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 140}, {"s": [42, 10], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [76.37, 0], [83, 6.63], [83, 12.37], [76.37, 19], [6.63, 19], [0, 12.37]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [76.37, 0], [83, 6.63], [83, 12.37], [76.37, 19], [6.63, 19], [0, 12.37]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [76.37, 0], [83, 6.63], [83, 12.37], [76.37, 19], [6.63, 19], [0, 12.37]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [76.37, 0], [83, 6.63], [83, 12.37], [76.37, 19], [6.63, 19], [0, 12.37]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [76.37, 0], [83, 6.63], [83, 12.37], [76.37, 19], [6.63, 19], [0, 12.37]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [76.37, 0], [83, 6.63], [83, 12.37], [76.37, 19], [6.63, 19], [0, 12.37]]}], "t": 185}]}}, {"ty": "st", "bm": 0, "hd": false, "nm": "", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}, "w": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1], "t": 140}, {"s": [1], "t": 185}]}, "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8, 0.9098, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8, 0.9098, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8, 0.9098, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8, 0.9098, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8, 0.9098, 1], "t": 140}, {"s": [0.8, 0.9098, 1], "t": 185}]}}], "ind": 1}, {"ty": 4, "nm": "Find all your Bookin", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30.5, 7], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30.5, 7], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30.5, 7], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30.5, 7], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [30.5, 7], "t": 140}, {"s": [30.5, 7], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [51.5, 13], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [51.5, 13], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [120.5, 13], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [120.5, 13], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [51.5, 13], "t": 140}, {"s": [51.5, 13], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.26], [0, 0], [0.26, 0], [0, 0], [0.12, 0.24], [0, 0], [-0.01, -0.13], [0, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [-0.05, 0], [-0.04, -0.02], [-0.03, -0.04], [-0.03, -0.06], [0, 0], [-0.06, -0.14], [-0.06, -0.14], [-0.06, 0.14], [-0.07, 0.13], [0, 0], [-0.04, 0.04], [-0.04, 0.02], [-0.05, 0], [-0.07, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.22], [0, 0], [-0.12, 0.24], [0, 0], [-0.26, 0], [0, 0], [0.01, 0.13], [0.01, 0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, 0], [0.05, 0], [0.04, 0.02], [0.04, 0.04], [0, 0], [0.07, 0.13], [0.06, 0.14], [0.06, -0.15], [0.06, -0.14], [0, 0], [0.03, -0.06], [0.04, -0.04], [0.04, -0.02], [0.05, 0], [0, 0], [0, 0]], "v": [[9.41, 0.24], [9.41, 8.92], [7.99, 8.92], [7.99, 3.31], [8.02, 2.59], [5.4, 7.51], [4.83, 7.87], [4.6, 7.87], [4.03, 7.51], [1.38, 2.57], [1.41, 2.96], [1.42, 3.31], [1.42, 8.92], [0, 8.92], [0, 0.24], [1.22, 0.24], [1.4, 0.25], [1.54, 0.28], [1.66, 0.35], [1.76, 0.5], [4.36, 5.32], [4.54, 5.71], [4.72, 6.13], [4.89, 5.71], [5.08, 5.3], [7.64, 0.5], [7.75, 0.35], [7.86, 0.28], [8, 0.25], [8.19, 0.24], [9.41, 0.24]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.26], [0, 0], [0.26, 0], [0, 0], [0.12, 0.24], [0, 0], [-0.01, -0.13], [0, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [-0.05, 0], [-0.04, -0.02], [-0.03, -0.04], [-0.03, -0.06], [0, 0], [-0.06, -0.14], [-0.06, -0.14], [-0.06, 0.14], [-0.07, 0.13], [0, 0], [-0.04, 0.04], [-0.04, 0.02], [-0.05, 0], [-0.07, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.22], [0, 0], [-0.12, 0.24], [0, 0], [-0.26, 0], [0, 0], [0.01, 0.13], [0.01, 0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, 0], [0.05, 0], [0.04, 0.02], [0.04, 0.04], [0, 0], [0.07, 0.13], [0.06, 0.14], [0.06, -0.15], [0.06, -0.14], [0, 0], [0.03, -0.06], [0.04, -0.04], [0.04, -0.02], [0.05, 0], [0, 0], [0, 0]], "v": [[9.41, 0.24], [9.41, 8.92], [7.99, 8.92], [7.99, 3.31], [8.02, 2.59], [5.4, 7.51], [4.83, 7.87], [4.6, 7.87], [4.03, 7.51], [1.38, 2.57], [1.41, 2.96], [1.42, 3.31], [1.42, 8.92], [0, 8.92], [0, 0.24], [1.22, 0.24], [1.4, 0.25], [1.54, 0.28], [1.66, 0.35], [1.76, 0.5], [4.36, 5.32], [4.54, 5.71], [4.72, 6.13], [4.89, 5.71], [5.08, 5.3], [7.64, 0.5], [7.75, 0.35], [7.86, 0.28], [8, 0.25], [8.19, 0.24], [9.41, 0.24]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.26], [0, 0], [0.26, 0], [0, 0], [0.12, 0.24], [0, 0], [-0.01, -0.13], [0, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [-0.05, 0], [-0.04, -0.02], [-0.03, -0.04], [-0.03, -0.06], [0, 0], [-0.06, -0.14], [-0.06, -0.14], [-0.06, 0.14], [-0.07, 0.13], [0, 0], [-0.04, 0.04], [-0.04, 0.02], [-0.05, 0], [-0.07, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.22], [0, 0], [-0.12, 0.24], [0, 0], [-0.26, 0], [0, 0], [0.01, 0.13], [0.01, 0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, 0], [0.05, 0], [0.04, 0.02], [0.04, 0.04], [0, 0], [0.07, 0.13], [0.06, 0.14], [0.06, -0.15], [0.06, -0.14], [0, 0], [0.03, -0.06], [0.04, -0.04], [0.04, -0.02], [0.05, 0], [0, 0], [0, 0]], "v": [[9.41, 0.24], [9.41, 8.92], [7.99, 8.92], [7.99, 3.31], [8.02, 2.59], [5.4, 7.51], [4.83, 7.87], [4.6, 7.87], [4.03, 7.51], [1.38, 2.57], [1.41, 2.96], [1.42, 3.31], [1.42, 8.92], [0, 8.92], [0, 0.24], [1.22, 0.24], [1.4, 0.25], [1.54, 0.28], [1.66, 0.35], [1.76, 0.5], [4.36, 5.32], [4.54, 5.71], [4.72, 6.13], [4.89, 5.71], [5.08, 5.3], [7.64, 0.5], [7.75, 0.35], [7.86, 0.28], [8, 0.25], [8.19, 0.24], [9.41, 0.24]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.26], [0, 0], [0.26, 0], [0, 0], [0.12, 0.24], [0, 0], [-0.01, -0.13], [0, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [-0.05, 0], [-0.04, -0.02], [-0.03, -0.04], [-0.03, -0.06], [0, 0], [-0.06, -0.14], [-0.06, -0.14], [-0.06, 0.14], [-0.07, 0.13], [0, 0], [-0.04, 0.04], [-0.04, 0.02], [-0.05, 0], [-0.07, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.22], [0, 0], [-0.12, 0.24], [0, 0], [-0.26, 0], [0, 0], [0.01, 0.13], [0.01, 0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, 0], [0.05, 0], [0.04, 0.02], [0.04, 0.04], [0, 0], [0.07, 0.13], [0.06, 0.14], [0.06, -0.15], [0.06, -0.14], [0, 0], [0.03, -0.06], [0.04, -0.04], [0.04, -0.02], [0.05, 0], [0, 0], [0, 0]], "v": [[9.41, 0.24], [9.41, 8.92], [7.99, 8.92], [7.99, 3.31], [8.02, 2.59], [5.4, 7.51], [4.83, 7.87], [4.6, 7.87], [4.03, 7.51], [1.38, 2.57], [1.41, 2.96], [1.42, 3.31], [1.42, 8.92], [0, 8.92], [0, 0.24], [1.22, 0.24], [1.4, 0.25], [1.54, 0.28], [1.66, 0.35], [1.76, 0.5], [4.36, 5.32], [4.54, 5.71], [4.72, 6.13], [4.89, 5.71], [5.08, 5.3], [7.64, 0.5], [7.75, 0.35], [7.86, 0.28], [8, 0.25], [8.19, 0.24], [9.41, 0.24]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.26], [0, 0], [0.26, 0], [0, 0], [0.12, 0.24], [0, 0], [-0.01, -0.13], [0, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [-0.05, 0], [-0.04, -0.02], [-0.03, -0.04], [-0.03, -0.06], [0, 0], [-0.06, -0.14], [-0.06, -0.14], [-0.06, 0.14], [-0.07, 0.13], [0, 0], [-0.04, 0.04], [-0.04, 0.02], [-0.05, 0], [-0.07, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.22], [0, 0], [-0.12, 0.24], [0, 0], [-0.26, 0], [0, 0], [0.01, 0.13], [0.01, 0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, 0], [0.05, 0], [0.04, 0.02], [0.04, 0.04], [0, 0], [0.07, 0.13], [0.06, 0.14], [0.06, -0.15], [0.06, -0.14], [0, 0], [0.03, -0.06], [0.04, -0.04], [0.04, -0.02], [0.05, 0], [0, 0], [0, 0]], "v": [[9.41, 0.24], [9.41, 8.92], [7.99, 8.92], [7.99, 3.31], [8.02, 2.59], [5.4, 7.51], [4.83, 7.87], [4.6, 7.87], [4.03, 7.51], [1.38, 2.57], [1.41, 2.96], [1.42, 3.31], [1.42, 8.92], [0, 8.92], [0, 0.24], [1.22, 0.24], [1.4, 0.25], [1.54, 0.28], [1.66, 0.35], [1.76, 0.5], [4.36, 5.32], [4.54, 5.71], [4.72, 6.13], [4.89, 5.71], [5.08, 5.3], [7.64, 0.5], [7.75, 0.35], [7.86, 0.28], [8, 0.25], [8.19, 0.24], [9.41, 0.24]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.02, 0.26], [0, 0], [0.26, 0], [0, 0], [0.12, 0.24], [0, 0], [-0.01, -0.13], [0, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [-0.05, 0], [-0.04, -0.02], [-0.03, -0.04], [-0.03, -0.06], [0, 0], [-0.06, -0.14], [-0.06, -0.14], [-0.06, 0.14], [-0.07, 0.13], [0, 0], [-0.04, 0.04], [-0.04, 0.02], [-0.05, 0], [-0.07, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.22], [0, 0], [-0.12, 0.24], [0, 0], [-0.26, 0], [0, 0], [0.01, 0.13], [0.01, 0.13], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, 0], [0.05, 0], [0.04, 0.02], [0.04, 0.04], [0, 0], [0.07, 0.13], [0.06, 0.14], [0.06, -0.15], [0.06, -0.14], [0, 0], [0.03, -0.06], [0.04, -0.04], [0.04, -0.02], [0.05, 0], [0, 0], [0, 0]], "v": [[9.41, 0.24], [9.41, 8.92], [7.99, 8.92], [7.99, 3.31], [8.02, 2.59], [5.4, 7.51], [4.83, 7.87], [4.6, 7.87], [4.03, 7.51], [1.38, 2.57], [1.41, 2.96], [1.42, 3.31], [1.42, 8.92], [0, 8.92], [0, 0.24], [1.22, 0.24], [1.4, 0.25], [1.54, 0.28], [1.66, 0.35], [1.76, 0.5], [4.36, 5.32], [4.54, 5.71], [4.72, 6.13], [4.89, 5.71], [5.08, 5.3], [7.64, 0.5], [7.75, 0.35], [7.86, 0.28], [8, 0.25], [8.19, 0.24], [9.41, 0.24]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.07, -0.05], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.06, -0.06], [-0.03, -0.07], [0, 0], [-0.03, -0.11], [-0.03, -0.11], [-0.04, 0.11], [-0.04, 0.11], [0, 0], [-0.07, 0.05], [-0.09, 0], [0, 0]], "o": [[0, 0], [-0.04, 0.1], [-0.07, 0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, 0], [0.07, 0.06], [0, 0], [0.04, 0.11], [0.03, 0.11], [0.04, -0.11], [0.04, -0.11], [0, 0], [0.03, -0.08], [0.08, -0.05], [0, 0], [0, 0]], "v": [[16.57, 2.76], [13.22, 10.61], [13.04, 10.84], [12.73, 10.93], [11.62, 10.93], [12.77, 8.45], [10.28, 2.76], [11.59, 2.76], [11.86, 2.84], [12.01, 3.04], [13.31, 6.23], [13.42, 6.55], [13.51, 6.88], [13.62, 6.55], [13.74, 6.22], [14.98, 3.04], [15.13, 2.84], [15.38, 2.76], [16.57, 2.76]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.07, -0.05], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.06, -0.06], [-0.03, -0.07], [0, 0], [-0.03, -0.11], [-0.03, -0.11], [-0.04, 0.11], [-0.04, 0.11], [0, 0], [-0.07, 0.05], [-0.09, 0], [0, 0]], "o": [[0, 0], [-0.04, 0.1], [-0.07, 0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, 0], [0.07, 0.06], [0, 0], [0.04, 0.11], [0.03, 0.11], [0.04, -0.11], [0.04, -0.11], [0, 0], [0.03, -0.08], [0.08, -0.05], [0, 0], [0, 0]], "v": [[16.57, 2.76], [13.22, 10.61], [13.04, 10.84], [12.73, 10.93], [11.62, 10.93], [12.77, 8.45], [10.28, 2.76], [11.59, 2.76], [11.86, 2.84], [12.01, 3.04], [13.31, 6.23], [13.42, 6.55], [13.51, 6.88], [13.62, 6.55], [13.74, 6.22], [14.98, 3.04], [15.13, 2.84], [15.38, 2.76], [16.57, 2.76]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.07, -0.05], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.06, -0.06], [-0.03, -0.07], [0, 0], [-0.03, -0.11], [-0.03, -0.11], [-0.04, 0.11], [-0.04, 0.11], [0, 0], [-0.07, 0.05], [-0.09, 0], [0, 0]], "o": [[0, 0], [-0.04, 0.1], [-0.07, 0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, 0], [0.07, 0.06], [0, 0], [0.04, 0.11], [0.03, 0.11], [0.04, -0.11], [0.04, -0.11], [0, 0], [0.03, -0.08], [0.08, -0.05], [0, 0], [0, 0]], "v": [[16.57, 2.76], [13.22, 10.61], [13.04, 10.84], [12.73, 10.93], [11.62, 10.93], [12.77, 8.45], [10.28, 2.76], [11.59, 2.76], [11.86, 2.84], [12.01, 3.04], [13.31, 6.23], [13.42, 6.55], [13.51, 6.88], [13.62, 6.55], [13.74, 6.22], [14.98, 3.04], [15.13, 2.84], [15.38, 2.76], [16.57, 2.76]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.07, -0.05], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.06, -0.06], [-0.03, -0.07], [0, 0], [-0.03, -0.11], [-0.03, -0.11], [-0.04, 0.11], [-0.04, 0.11], [0, 0], [-0.07, 0.05], [-0.09, 0], [0, 0]], "o": [[0, 0], [-0.04, 0.1], [-0.07, 0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, 0], [0.07, 0.06], [0, 0], [0.04, 0.11], [0.03, 0.11], [0.04, -0.11], [0.04, -0.11], [0, 0], [0.03, -0.08], [0.08, -0.05], [0, 0], [0, 0]], "v": [[16.57, 2.76], [13.22, 10.61], [13.04, 10.84], [12.73, 10.93], [11.62, 10.93], [12.77, 8.45], [10.28, 2.76], [11.59, 2.76], [11.86, 2.84], [12.01, 3.04], [13.31, 6.23], [13.42, 6.55], [13.51, 6.88], [13.62, 6.55], [13.74, 6.22], [14.98, 3.04], [15.13, 2.84], [15.38, 2.76], [16.57, 2.76]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.07, -0.05], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.06, -0.06], [-0.03, -0.07], [0, 0], [-0.03, -0.11], [-0.03, -0.11], [-0.04, 0.11], [-0.04, 0.11], [0, 0], [-0.07, 0.05], [-0.09, 0], [0, 0]], "o": [[0, 0], [-0.04, 0.1], [-0.07, 0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, 0], [0.07, 0.06], [0, 0], [0.04, 0.11], [0.03, 0.11], [0.04, -0.11], [0.04, -0.11], [0, 0], [0.03, -0.08], [0.08, -0.05], [0, 0], [0, 0]], "v": [[16.57, 2.76], [13.22, 10.61], [13.04, 10.84], [12.73, 10.93], [11.62, 10.93], [12.77, 8.45], [10.28, 2.76], [11.59, 2.76], [11.86, 2.84], [12.01, 3.04], [13.31, 6.23], [13.42, 6.55], [13.51, 6.88], [13.62, 6.55], [13.74, 6.22], [14.98, 3.04], [15.13, 2.84], [15.38, 2.76], [16.57, 2.76]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.07, -0.05], [0.14, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.06, -0.06], [-0.03, -0.07], [0, 0], [-0.03, -0.11], [-0.03, -0.11], [-0.04, 0.11], [-0.04, 0.11], [0, 0], [-0.07, 0.05], [-0.09, 0], [0, 0]], "o": [[0, 0], [-0.04, 0.1], [-0.07, 0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0.12, 0], [0.07, 0.06], [0, 0], [0.04, 0.11], [0.03, 0.11], [0.04, -0.11], [0.04, -0.11], [0, 0], [0.03, -0.08], [0.08, -0.05], [0, 0], [0, 0]], "v": [[16.57, 2.76], [13.22, 10.61], [13.04, 10.84], [12.73, 10.93], [11.62, 10.93], [12.77, 8.45], [10.28, 2.76], [11.59, 2.76], [11.86, 2.84], [12.01, 3.04], [13.31, 6.23], [13.42, 6.55], [13.51, 6.88], [13.62, 6.55], [13.74, 6.22], [14.98, 3.04], [15.13, 2.84], [15.38, 2.76], [16.57, 2.76]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.08], [-0.14, 0.13], [-0.07, 0.18], [0, 0.22], [0.29, 0.24], [0.6, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.23, -0.08], [0.15, -0.14], [0.07, -0.18], [0, -0.44], [-0.29, -0.24], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.05, 4.37], [20.84, 4.25], [21.4, 3.94], [21.73, 3.46], [21.84, 2.86], [21.4, 1.84], [20.07, 1.49], [19.04, 1.49], [19.04, 4.37], [20.05, 4.37]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.08], [-0.14, 0.13], [-0.07, 0.18], [0, 0.22], [0.29, 0.24], [0.6, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.23, -0.08], [0.15, -0.14], [0.07, -0.18], [0, -0.44], [-0.29, -0.24], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.05, 4.37], [20.84, 4.25], [21.4, 3.94], [21.73, 3.46], [21.84, 2.86], [21.4, 1.84], [20.07, 1.49], [19.04, 1.49], [19.04, 4.37], [20.05, 4.37]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.08], [-0.14, 0.13], [-0.07, 0.18], [0, 0.22], [0.29, 0.24], [0.6, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.23, -0.08], [0.15, -0.14], [0.07, -0.18], [0, -0.44], [-0.29, -0.24], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.05, 4.37], [20.84, 4.25], [21.4, 3.94], [21.73, 3.46], [21.84, 2.86], [21.4, 1.84], [20.07, 1.49], [19.04, 1.49], [19.04, 4.37], [20.05, 4.37]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.08], [-0.14, 0.13], [-0.07, 0.18], [0, 0.22], [0.29, 0.24], [0.6, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.23, -0.08], [0.15, -0.14], [0.07, -0.18], [0, -0.44], [-0.29, -0.24], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.05, 4.37], [20.84, 4.25], [21.4, 3.94], [21.73, 3.46], [21.84, 2.86], [21.4, 1.84], [20.07, 1.49], [19.04, 1.49], [19.04, 4.37], [20.05, 4.37]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.08], [-0.14, 0.13], [-0.07, 0.18], [0, 0.22], [0.29, 0.24], [0.6, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.23, -0.08], [0.15, -0.14], [0.07, -0.18], [0, -0.44], [-0.29, -0.24], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.05, 4.37], [20.84, 4.25], [21.4, 3.94], [21.73, 3.46], [21.84, 2.86], [21.4, 1.84], [20.07, 1.49], [19.04, 1.49], [19.04, 4.37], [20.05, 4.37]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-0.22, 0.08], [-0.14, 0.13], [-0.07, 0.18], [0, 0.22], [0.29, 0.24], [0.6, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.23, -0.08], [0.15, -0.14], [0.07, -0.18], [0, -0.44], [-0.29, -0.24], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.05, 4.37], [20.84, 4.25], [21.4, 3.94], [21.73, 3.46], [21.84, 2.86], [21.4, 1.84], [20.07, 1.49], [19.04, 1.49], [19.04, 4.37], [20.05, 4.37]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.12, 0.22], [0, 0], [0.08, 0.04], [0.16, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.42, -0.12], [-0.27, -0.22], [-0.13, -0.31], [0, -0.37], [0.09, -0.26], [0.16, -0.21], [0.24, -0.16], [0.3, -0.09], [-0.09, -0.08], [-0.07, -0.11], [0, 0]], "o": [[0, 0], [-0.28, 0], [0, 0], [-0.07, -0.1], [-0.08, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.59, 0], [0.42, 0.12], [0.27, 0.22], [0.13, 0.3], [0, 0.29], [-0.08, 0.26], [-0.16, 0.21], [-0.24, 0.16], [0.1, 0.06], [0.09, 0.08], [0, 0], [0, 0]], "v": [[24.2, 8.92], [22.74, 8.92], [22.14, 8.59], [20.32, 5.81], [20.09, 5.59], [19.73, 5.53], [19.04, 5.53], [19.04, 8.92], [17.43, 8.92], [17.43, 0.24], [20.07, 0.24], [21.58, 0.43], [22.62, 0.94], [23.22, 1.73], [23.41, 2.74], [23.28, 3.56], [22.91, 4.27], [22.31, 4.83], [21.5, 5.21], [21.79, 5.42], [22.03, 5.71], [24.2, 8.92]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.12, 0.22], [0, 0], [0.08, 0.04], [0.16, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.42, -0.12], [-0.27, -0.22], [-0.13, -0.31], [0, -0.37], [0.09, -0.26], [0.16, -0.21], [0.24, -0.16], [0.3, -0.09], [-0.09, -0.08], [-0.07, -0.11], [0, 0]], "o": [[0, 0], [-0.28, 0], [0, 0], [-0.07, -0.1], [-0.08, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.59, 0], [0.42, 0.12], [0.27, 0.22], [0.13, 0.3], [0, 0.29], [-0.08, 0.26], [-0.16, 0.21], [-0.24, 0.16], [0.1, 0.06], [0.09, 0.08], [0, 0], [0, 0]], "v": [[24.2, 8.92], [22.74, 8.92], [22.14, 8.59], [20.32, 5.81], [20.09, 5.59], [19.73, 5.53], [19.04, 5.53], [19.04, 8.92], [17.43, 8.92], [17.43, 0.24], [20.07, 0.24], [21.58, 0.43], [22.62, 0.94], [23.22, 1.73], [23.41, 2.74], [23.28, 3.56], [22.91, 4.27], [22.31, 4.83], [21.5, 5.21], [21.79, 5.42], [22.03, 5.71], [24.2, 8.92]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.12, 0.22], [0, 0], [0.08, 0.04], [0.16, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.42, -0.12], [-0.27, -0.22], [-0.13, -0.31], [0, -0.37], [0.09, -0.26], [0.16, -0.21], [0.24, -0.16], [0.3, -0.09], [-0.09, -0.08], [-0.07, -0.11], [0, 0]], "o": [[0, 0], [-0.28, 0], [0, 0], [-0.07, -0.1], [-0.08, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.59, 0], [0.42, 0.12], [0.27, 0.22], [0.13, 0.3], [0, 0.29], [-0.08, 0.26], [-0.16, 0.21], [-0.24, 0.16], [0.1, 0.06], [0.09, 0.08], [0, 0], [0, 0]], "v": [[24.2, 8.92], [22.74, 8.92], [22.14, 8.59], [20.32, 5.81], [20.09, 5.59], [19.73, 5.53], [19.04, 5.53], [19.04, 8.92], [17.43, 8.92], [17.43, 0.24], [20.07, 0.24], [21.58, 0.43], [22.62, 0.94], [23.22, 1.73], [23.41, 2.74], [23.28, 3.56], [22.91, 4.27], [22.31, 4.83], [21.5, 5.21], [21.79, 5.42], [22.03, 5.71], [24.2, 8.92]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.12, 0.22], [0, 0], [0.08, 0.04], [0.16, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.42, -0.12], [-0.27, -0.22], [-0.13, -0.31], [0, -0.37], [0.09, -0.26], [0.16, -0.21], [0.24, -0.16], [0.3, -0.09], [-0.09, -0.08], [-0.07, -0.11], [0, 0]], "o": [[0, 0], [-0.28, 0], [0, 0], [-0.07, -0.1], [-0.08, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.59, 0], [0.42, 0.12], [0.27, 0.22], [0.13, 0.3], [0, 0.29], [-0.08, 0.26], [-0.16, 0.21], [-0.24, 0.16], [0.1, 0.06], [0.09, 0.08], [0, 0], [0, 0]], "v": [[24.2, 8.92], [22.74, 8.92], [22.14, 8.59], [20.32, 5.81], [20.09, 5.59], [19.73, 5.53], [19.04, 5.53], [19.04, 8.92], [17.43, 8.92], [17.43, 0.24], [20.07, 0.24], [21.58, 0.43], [22.62, 0.94], [23.22, 1.73], [23.41, 2.74], [23.28, 3.56], [22.91, 4.27], [22.31, 4.83], [21.5, 5.21], [21.79, 5.42], [22.03, 5.71], [24.2, 8.92]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.12, 0.22], [0, 0], [0.08, 0.04], [0.16, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.42, -0.12], [-0.27, -0.22], [-0.13, -0.31], [0, -0.37], [0.09, -0.26], [0.16, -0.21], [0.24, -0.16], [0.3, -0.09], [-0.09, -0.08], [-0.07, -0.11], [0, 0]], "o": [[0, 0], [-0.28, 0], [0, 0], [-0.07, -0.1], [-0.08, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.59, 0], [0.42, 0.12], [0.27, 0.22], [0.13, 0.3], [0, 0.29], [-0.08, 0.26], [-0.16, 0.21], [-0.24, 0.16], [0.1, 0.06], [0.09, 0.08], [0, 0], [0, 0]], "v": [[24.2, 8.92], [22.74, 8.92], [22.14, 8.59], [20.32, 5.81], [20.09, 5.59], [19.73, 5.53], [19.04, 5.53], [19.04, 8.92], [17.43, 8.92], [17.43, 0.24], [20.07, 0.24], [21.58, 0.43], [22.62, 0.94], [23.22, 1.73], [23.41, 2.74], [23.28, 3.56], [22.91, 4.27], [22.31, 4.83], [21.5, 5.21], [21.79, 5.42], [22.03, 5.71], [24.2, 8.92]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.12, 0.22], [0, 0], [0.08, 0.04], [0.16, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.42, -0.12], [-0.27, -0.22], [-0.13, -0.31], [0, -0.37], [0.09, -0.26], [0.16, -0.21], [0.24, -0.16], [0.3, -0.09], [-0.09, -0.08], [-0.07, -0.11], [0, 0]], "o": [[0, 0], [-0.28, 0], [0, 0], [-0.07, -0.1], [-0.08, -0.04], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.59, 0], [0.42, 0.12], [0.27, 0.22], [0.13, 0.3], [0, 0.29], [-0.08, 0.26], [-0.16, 0.21], [-0.24, 0.16], [0.1, 0.06], [0.09, 0.08], [0, 0], [0, 0]], "v": [[24.2, 8.92], [22.74, 8.92], [22.14, 8.59], [20.32, 5.81], [20.09, 5.59], [19.73, 5.53], [19.04, 5.53], [19.04, 8.92], [17.43, 8.92], [17.43, 0.24], [20.07, 0.24], [21.58, 0.43], [22.62, 0.94], [23.22, 1.73], [23.41, 2.74], [23.28, 3.56], [22.91, 4.27], [22.31, 4.83], [21.5, 5.21], [21.79, 5.42], [22.03, 5.71], [24.2, 8.92]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.29, -0.06], [0.18, -0.08], [0.08, -0.11], [0, -0.13], [-0.15, -0.11], [-0.25, 0], [-0.22, 0.11], [-0.21, 0.22], [0, 0]], "o": [[-0.43, 0.02], [-0.29, 0.05], [-0.18, 0.08], [-0.08, 0.11], [0, 0.26], [0.16, 0.11], [0.3, 0], [0.22, -0.11], [0, 0], [0, 0]], "v": [[28.06, 6.29], [26.98, 6.4], [26.28, 6.61], [25.9, 6.9], [25.79, 7.27], [26.01, 7.82], [26.62, 7.99], [27.41, 7.83], [28.06, 7.33], [28.06, 6.29]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.29, -0.06], [0.18, -0.08], [0.08, -0.11], [0, -0.13], [-0.15, -0.11], [-0.25, 0], [-0.22, 0.11], [-0.21, 0.22], [0, 0]], "o": [[-0.43, 0.02], [-0.29, 0.05], [-0.18, 0.08], [-0.08, 0.11], [0, 0.26], [0.16, 0.11], [0.3, 0], [0.22, -0.11], [0, 0], [0, 0]], "v": [[28.06, 6.29], [26.98, 6.4], [26.28, 6.61], [25.9, 6.9], [25.79, 7.27], [26.01, 7.82], [26.62, 7.99], [27.41, 7.83], [28.06, 7.33], [28.06, 6.29]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.29, -0.06], [0.18, -0.08], [0.08, -0.11], [0, -0.13], [-0.15, -0.11], [-0.25, 0], [-0.22, 0.11], [-0.21, 0.22], [0, 0]], "o": [[-0.43, 0.02], [-0.29, 0.05], [-0.18, 0.08], [-0.08, 0.11], [0, 0.26], [0.16, 0.11], [0.3, 0], [0.22, -0.11], [0, 0], [0, 0]], "v": [[28.06, 6.29], [26.98, 6.4], [26.28, 6.61], [25.9, 6.9], [25.79, 7.27], [26.01, 7.82], [26.62, 7.99], [27.41, 7.83], [28.06, 7.33], [28.06, 6.29]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.29, -0.06], [0.18, -0.08], [0.08, -0.11], [0, -0.13], [-0.15, -0.11], [-0.25, 0], [-0.22, 0.11], [-0.21, 0.22], [0, 0]], "o": [[-0.43, 0.02], [-0.29, 0.05], [-0.18, 0.08], [-0.08, 0.11], [0, 0.26], [0.16, 0.11], [0.3, 0], [0.22, -0.11], [0, 0], [0, 0]], "v": [[28.06, 6.29], [26.98, 6.4], [26.28, 6.61], [25.9, 6.9], [25.79, 7.27], [26.01, 7.82], [26.62, 7.99], [27.41, 7.83], [28.06, 7.33], [28.06, 6.29]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.29, -0.06], [0.18, -0.08], [0.08, -0.11], [0, -0.13], [-0.15, -0.11], [-0.25, 0], [-0.22, 0.11], [-0.21, 0.22], [0, 0]], "o": [[-0.43, 0.02], [-0.29, 0.05], [-0.18, 0.08], [-0.08, 0.11], [0, 0.26], [0.16, 0.11], [0.3, 0], [0.22, -0.11], [0, 0], [0, 0]], "v": [[28.06, 6.29], [26.98, 6.4], [26.28, 6.61], [25.9, 6.9], [25.79, 7.27], [26.01, 7.82], [26.62, 7.99], [27.41, 7.83], [28.06, 7.33], [28.06, 6.29]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0.29, -0.06], [0.18, -0.08], [0.08, -0.11], [0, -0.13], [-0.15, -0.11], [-0.25, 0], [-0.22, 0.11], [-0.21, 0.22], [0, 0]], "o": [[-0.43, 0.02], [-0.29, 0.05], [-0.18, 0.08], [-0.08, 0.11], [0, 0.26], [0.16, 0.11], [0.3, 0], [0.22, -0.11], [0, 0], [0, 0]], "v": [[28.06, 6.29], [26.98, 6.4], [26.28, 6.61], [25.9, 6.9], [25.79, 7.27], [26.01, 7.82], [26.62, 7.99], [27.41, 7.83], [28.06, 7.33], [28.06, 6.29]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1, 0], [-0.28, -0.12], [-0.2, -0.21], [-0.1, -0.29], [0, -0.35], [0, 0], [0, 0], [0.08, 0.04], [0.04, 0.13], [0, 0], [0.15, -0.11], [0.16, -0.07], [0.18, -0.04], [0.22, 0], [0.22, 0.07], [0.16, 0.14], [0.09, 0.21], [0, 0.28], [-0.05, 0.15], [-0.12, 0.14], [-0.19, 0.12], [-0.27, 0.09], [-0.36, 0.06], [-0.47, 0.01], [0, 0], [0.18, 0.2], [0.33, 0], [0.16, -0.06], [0.12, -0.07], [0.1, -0.06], [0.12, 0], [0.07, 0.05], [0.04, 0.07], [0, 0]], "o": [[0.71, -0.65], [0.36, 0], [0.28, 0.12], [0.2, 0.21], [0.1, 0.29], [0, 0], [0, 0], [-0.14, 0], [-0.08, -0.04], [0, 0], [-0.16, 0.14], [-0.15, 0.1], [-0.16, 0.07], [-0.18, 0.04], [-0.26, 0], [-0.22, -0.07], [-0.16, -0.14], [-0.09, -0.21], [0, -0.16], [0.05, -0.16], [0.12, -0.14], [0.19, -0.12], [0.28, -0.09], [0.36, -0.06], [0, 0], [0, -0.41], [-0.18, -0.2], [-0.24, 0], [-0.16, 0.06], [-0.12, 0.07], [-0.1, 0.06], [-0.1, 0], [-0.07, -0.06], [0, 0], [0, 0]], "v": [[24.66, 3.62], [27.22, 2.65], [28.19, 2.83], [28.91, 3.32], [29.35, 4.07], [29.51, 5.03], [29.51, 8.92], [28.83, 8.92], [28.51, 8.86], [28.33, 8.6], [28.2, 8.15], [27.74, 8.53], [27.28, 8.79], [26.76, 8.95], [26.16, 9.01], [25.44, 8.91], [24.87, 8.59], [24.5, 8.07], [24.37, 7.34], [24.45, 6.88], [24.7, 6.44], [25.16, 6.04], [25.85, 5.72], [26.81, 5.5], [28.06, 5.39], [28.06, 5.03], [27.8, 4.12], [27.03, 3.82], [26.43, 3.9], [26.02, 4.09], [25.69, 4.28], [25.37, 4.36], [25.1, 4.28], [24.93, 4.09], [24.66, 3.62]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1, 0], [-0.28, -0.12], [-0.2, -0.21], [-0.1, -0.29], [0, -0.35], [0, 0], [0, 0], [0.08, 0.04], [0.04, 0.13], [0, 0], [0.15, -0.11], [0.16, -0.07], [0.18, -0.04], [0.22, 0], [0.22, 0.07], [0.16, 0.14], [0.09, 0.21], [0, 0.28], [-0.05, 0.15], [-0.12, 0.14], [-0.19, 0.12], [-0.27, 0.09], [-0.36, 0.06], [-0.47, 0.01], [0, 0], [0.18, 0.2], [0.33, 0], [0.16, -0.06], [0.12, -0.07], [0.1, -0.06], [0.12, 0], [0.07, 0.05], [0.04, 0.07], [0, 0]], "o": [[0.71, -0.65], [0.36, 0], [0.28, 0.12], [0.2, 0.21], [0.1, 0.29], [0, 0], [0, 0], [-0.14, 0], [-0.08, -0.04], [0, 0], [-0.16, 0.14], [-0.15, 0.1], [-0.16, 0.07], [-0.18, 0.04], [-0.26, 0], [-0.22, -0.07], [-0.16, -0.14], [-0.09, -0.21], [0, -0.16], [0.05, -0.16], [0.12, -0.14], [0.19, -0.12], [0.28, -0.09], [0.36, -0.06], [0, 0], [0, -0.41], [-0.18, -0.2], [-0.24, 0], [-0.16, 0.06], [-0.12, 0.07], [-0.1, 0.06], [-0.1, 0], [-0.07, -0.06], [0, 0], [0, 0]], "v": [[24.66, 3.62], [27.22, 2.65], [28.19, 2.83], [28.91, 3.32], [29.35, 4.07], [29.51, 5.03], [29.51, 8.92], [28.83, 8.92], [28.51, 8.86], [28.33, 8.6], [28.2, 8.15], [27.74, 8.53], [27.28, 8.79], [26.76, 8.95], [26.16, 9.01], [25.44, 8.91], [24.87, 8.59], [24.5, 8.07], [24.37, 7.34], [24.45, 6.88], [24.7, 6.44], [25.16, 6.04], [25.85, 5.72], [26.81, 5.5], [28.06, 5.39], [28.06, 5.03], [27.8, 4.12], [27.03, 3.82], [26.43, 3.9], [26.02, 4.09], [25.69, 4.28], [25.37, 4.36], [25.1, 4.28], [24.93, 4.09], [24.66, 3.62]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1, 0], [-0.28, -0.12], [-0.2, -0.21], [-0.1, -0.29], [0, -0.35], [0, 0], [0, 0], [0.08, 0.04], [0.04, 0.13], [0, 0], [0.15, -0.11], [0.16, -0.07], [0.18, -0.04], [0.22, 0], [0.22, 0.07], [0.16, 0.14], [0.09, 0.21], [0, 0.28], [-0.05, 0.15], [-0.12, 0.14], [-0.19, 0.12], [-0.27, 0.09], [-0.36, 0.06], [-0.47, 0.01], [0, 0], [0.18, 0.2], [0.33, 0], [0.16, -0.06], [0.12, -0.07], [0.1, -0.06], [0.12, 0], [0.07, 0.05], [0.04, 0.07], [0, 0]], "o": [[0.71, -0.65], [0.36, 0], [0.28, 0.12], [0.2, 0.21], [0.1, 0.29], [0, 0], [0, 0], [-0.14, 0], [-0.08, -0.04], [0, 0], [-0.16, 0.14], [-0.15, 0.1], [-0.16, 0.07], [-0.18, 0.04], [-0.26, 0], [-0.22, -0.07], [-0.16, -0.14], [-0.09, -0.21], [0, -0.16], [0.05, -0.16], [0.12, -0.14], [0.19, -0.12], [0.28, -0.09], [0.36, -0.06], [0, 0], [0, -0.41], [-0.18, -0.2], [-0.24, 0], [-0.16, 0.06], [-0.12, 0.07], [-0.1, 0.06], [-0.1, 0], [-0.07, -0.06], [0, 0], [0, 0]], "v": [[24.66, 3.62], [27.22, 2.65], [28.19, 2.83], [28.91, 3.32], [29.35, 4.07], [29.51, 5.03], [29.51, 8.92], [28.83, 8.92], [28.51, 8.86], [28.33, 8.6], [28.2, 8.15], [27.74, 8.53], [27.28, 8.79], [26.76, 8.95], [26.16, 9.01], [25.44, 8.91], [24.87, 8.59], [24.5, 8.07], [24.37, 7.34], [24.45, 6.88], [24.7, 6.44], [25.16, 6.04], [25.85, 5.72], [26.81, 5.5], [28.06, 5.39], [28.06, 5.03], [27.8, 4.12], [27.03, 3.82], [26.43, 3.9], [26.02, 4.09], [25.69, 4.28], [25.37, 4.36], [25.1, 4.28], [24.93, 4.09], [24.66, 3.62]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1, 0], [-0.28, -0.12], [-0.2, -0.21], [-0.1, -0.29], [0, -0.35], [0, 0], [0, 0], [0.08, 0.04], [0.04, 0.13], [0, 0], [0.15, -0.11], [0.16, -0.07], [0.18, -0.04], [0.22, 0], [0.22, 0.07], [0.16, 0.14], [0.09, 0.21], [0, 0.28], [-0.05, 0.15], [-0.12, 0.14], [-0.19, 0.12], [-0.27, 0.09], [-0.36, 0.06], [-0.47, 0.01], [0, 0], [0.18, 0.2], [0.33, 0], [0.16, -0.06], [0.12, -0.07], [0.1, -0.06], [0.12, 0], [0.07, 0.05], [0.04, 0.07], [0, 0]], "o": [[0.71, -0.65], [0.36, 0], [0.28, 0.12], [0.2, 0.21], [0.1, 0.29], [0, 0], [0, 0], [-0.14, 0], [-0.08, -0.04], [0, 0], [-0.16, 0.14], [-0.15, 0.1], [-0.16, 0.07], [-0.18, 0.04], [-0.26, 0], [-0.22, -0.07], [-0.16, -0.14], [-0.09, -0.21], [0, -0.16], [0.05, -0.16], [0.12, -0.14], [0.19, -0.12], [0.28, -0.09], [0.36, -0.06], [0, 0], [0, -0.41], [-0.18, -0.2], [-0.24, 0], [-0.16, 0.06], [-0.12, 0.07], [-0.1, 0.06], [-0.1, 0], [-0.07, -0.06], [0, 0], [0, 0]], "v": [[24.66, 3.62], [27.22, 2.65], [28.19, 2.83], [28.91, 3.32], [29.35, 4.07], [29.51, 5.03], [29.51, 8.92], [28.83, 8.92], [28.51, 8.86], [28.33, 8.6], [28.2, 8.15], [27.74, 8.53], [27.28, 8.79], [26.76, 8.95], [26.16, 9.01], [25.44, 8.91], [24.87, 8.59], [24.5, 8.07], [24.37, 7.34], [24.45, 6.88], [24.7, 6.44], [25.16, 6.04], [25.85, 5.72], [26.81, 5.5], [28.06, 5.39], [28.06, 5.03], [27.8, 4.12], [27.03, 3.82], [26.43, 3.9], [26.02, 4.09], [25.69, 4.28], [25.37, 4.36], [25.1, 4.28], [24.93, 4.09], [24.66, 3.62]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1, 0], [-0.28, -0.12], [-0.2, -0.21], [-0.1, -0.29], [0, -0.35], [0, 0], [0, 0], [0.08, 0.04], [0.04, 0.13], [0, 0], [0.15, -0.11], [0.16, -0.07], [0.18, -0.04], [0.22, 0], [0.22, 0.07], [0.16, 0.14], [0.09, 0.21], [0, 0.28], [-0.05, 0.15], [-0.12, 0.14], [-0.19, 0.12], [-0.27, 0.09], [-0.36, 0.06], [-0.47, 0.01], [0, 0], [0.18, 0.2], [0.33, 0], [0.16, -0.06], [0.12, -0.07], [0.1, -0.06], [0.12, 0], [0.07, 0.05], [0.04, 0.07], [0, 0]], "o": [[0.71, -0.65], [0.36, 0], [0.28, 0.12], [0.2, 0.21], [0.1, 0.29], [0, 0], [0, 0], [-0.14, 0], [-0.08, -0.04], [0, 0], [-0.16, 0.14], [-0.15, 0.1], [-0.16, 0.07], [-0.18, 0.04], [-0.26, 0], [-0.22, -0.07], [-0.16, -0.14], [-0.09, -0.21], [0, -0.16], [0.05, -0.16], [0.12, -0.14], [0.19, -0.12], [0.28, -0.09], [0.36, -0.06], [0, 0], [0, -0.41], [-0.18, -0.2], [-0.24, 0], [-0.16, 0.06], [-0.12, 0.07], [-0.1, 0.06], [-0.1, 0], [-0.07, -0.06], [0, 0], [0, 0]], "v": [[24.66, 3.62], [27.22, 2.65], [28.19, 2.83], [28.91, 3.32], [29.35, 4.07], [29.51, 5.03], [29.51, 8.92], [28.83, 8.92], [28.51, 8.86], [28.33, 8.6], [28.2, 8.15], [27.74, 8.53], [27.28, 8.79], [26.76, 8.95], [26.16, 9.01], [25.44, 8.91], [24.87, 8.59], [24.5, 8.07], [24.37, 7.34], [24.45, 6.88], [24.7, 6.44], [25.16, 6.04], [25.85, 5.72], [26.81, 5.5], [28.06, 5.39], [28.06, 5.03], [27.8, 4.12], [27.03, 3.82], [26.43, 3.9], [26.02, 4.09], [25.69, 4.28], [25.37, 4.36], [25.1, 4.28], [24.93, 4.09], [24.66, 3.62]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-1, 0], [-0.28, -0.12], [-0.2, -0.21], [-0.1, -0.29], [0, -0.35], [0, 0], [0, 0], [0.08, 0.04], [0.04, 0.13], [0, 0], [0.15, -0.11], [0.16, -0.07], [0.18, -0.04], [0.22, 0], [0.22, 0.07], [0.16, 0.14], [0.09, 0.21], [0, 0.28], [-0.05, 0.15], [-0.12, 0.14], [-0.19, 0.12], [-0.27, 0.09], [-0.36, 0.06], [-0.47, 0.01], [0, 0], [0.18, 0.2], [0.33, 0], [0.16, -0.06], [0.12, -0.07], [0.1, -0.06], [0.12, 0], [0.07, 0.05], [0.04, 0.07], [0, 0]], "o": [[0.71, -0.65], [0.36, 0], [0.28, 0.12], [0.2, 0.21], [0.1, 0.29], [0, 0], [0, 0], [-0.14, 0], [-0.08, -0.04], [0, 0], [-0.16, 0.14], [-0.15, 0.1], [-0.16, 0.07], [-0.18, 0.04], [-0.26, 0], [-0.22, -0.07], [-0.16, -0.14], [-0.09, -0.21], [0, -0.16], [0.05, -0.16], [0.12, -0.14], [0.19, -0.12], [0.28, -0.09], [0.36, -0.06], [0, 0], [0, -0.41], [-0.18, -0.2], [-0.24, 0], [-0.16, 0.06], [-0.12, 0.07], [-0.1, 0.06], [-0.1, 0], [-0.07, -0.06], [0, 0], [0, 0]], "v": [[24.66, 3.62], [27.22, 2.65], [28.19, 2.83], [28.91, 3.32], [29.35, 4.07], [29.51, 5.03], [29.51, 8.92], [28.83, 8.92], [28.51, 8.86], [28.33, 8.6], [28.2, 8.15], [27.74, 8.53], [27.28, 8.79], [26.76, 8.95], [26.16, 9.01], [25.44, 8.91], [24.87, 8.59], [24.5, 8.07], [24.37, 7.34], [24.45, 6.88], [24.7, 6.44], [25.16, 6.04], [25.85, 5.72], [26.81, 5.5], [28.06, 5.39], [28.06, 5.03], [27.8, 4.12], [27.03, 3.82], [26.43, 3.9], [26.02, 4.09], [25.69, 4.28], [25.37, 4.36], [25.1, 4.28], [24.93, 4.09], [24.66, 3.62]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.44, 2.76], [32.44, 8.92], [30.95, 8.92], [30.95, 2.76], [32.44, 2.76]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.44, 2.76], [32.44, 8.92], [30.95, 8.92], [30.95, 2.76], [32.44, 2.76]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.44, 2.76], [32.44, 8.92], [30.95, 8.92], [30.95, 2.76], [32.44, 2.76]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.44, 2.76], [32.44, 8.92], [30.95, 8.92], [30.95, 2.76], [32.44, 2.76]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.44, 2.76], [32.44, 8.92], [30.95, 8.92], [30.95, 2.76], [32.44, 2.76]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.44, 2.76], [32.44, 8.92], [30.95, 8.92], [30.95, 2.76], [32.44, 2.76]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, -0.11], [0.09, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.08, 0.08], [0.05, 0.11], [0, 0.13], [-0.05, 0.12], [-0.08, 0.08], [-0.11, 0.05], [-0.13, 0], [-0.12, -0.05], [-0.08, -0.08], [-0.05, -0.12], [0, -0.13]], "o": [[0, 0.13], [-0.05, 0.11], [-0.08, 0.08], [-0.12, 0.05], [-0.13, 0], [-0.11, -0.05], [-0.08, -0.08], [-0.05, -0.11], [0, -0.13], [0.05, -0.12], [0.08, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.09, 0.08], [0.05, 0.12], [0, 0]], "v": [[32.64, 0.97], [32.56, 1.33], [32.35, 1.62], [32.05, 1.82], [31.68, 1.9], [31.32, 1.82], [31.02, 1.62], [30.82, 1.33], [30.75, 0.97], [30.82, 0.59], [31.02, 0.29], [31.32, 0.1], [31.68, 0.02], [32.05, 0.1], [32.35, 0.29], [32.56, 0.59], [32.64, 0.97]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, -0.11], [0.09, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.08, 0.08], [0.05, 0.11], [0, 0.13], [-0.05, 0.12], [-0.08, 0.08], [-0.11, 0.05], [-0.13, 0], [-0.12, -0.05], [-0.08, -0.08], [-0.05, -0.12], [0, -0.13]], "o": [[0, 0.13], [-0.05, 0.11], [-0.08, 0.08], [-0.12, 0.05], [-0.13, 0], [-0.11, -0.05], [-0.08, -0.08], [-0.05, -0.11], [0, -0.13], [0.05, -0.12], [0.08, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.09, 0.08], [0.05, 0.12], [0, 0]], "v": [[32.64, 0.97], [32.56, 1.33], [32.35, 1.62], [32.05, 1.82], [31.68, 1.9], [31.32, 1.82], [31.02, 1.62], [30.82, 1.33], [30.75, 0.97], [30.82, 0.59], [31.02, 0.29], [31.32, 0.1], [31.68, 0.02], [32.05, 0.1], [32.35, 0.29], [32.56, 0.59], [32.64, 0.97]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, -0.11], [0.09, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.08, 0.08], [0.05, 0.11], [0, 0.13], [-0.05, 0.12], [-0.08, 0.08], [-0.11, 0.05], [-0.13, 0], [-0.12, -0.05], [-0.08, -0.08], [-0.05, -0.12], [0, -0.13]], "o": [[0, 0.13], [-0.05, 0.11], [-0.08, 0.08], [-0.12, 0.05], [-0.13, 0], [-0.11, -0.05], [-0.08, -0.08], [-0.05, -0.11], [0, -0.13], [0.05, -0.12], [0.08, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.09, 0.08], [0.05, 0.12], [0, 0]], "v": [[32.64, 0.97], [32.56, 1.33], [32.35, 1.62], [32.05, 1.82], [31.68, 1.9], [31.32, 1.82], [31.02, 1.62], [30.82, 1.33], [30.75, 0.97], [30.82, 0.59], [31.02, 0.29], [31.32, 0.1], [31.68, 0.02], [32.05, 0.1], [32.35, 0.29], [32.56, 0.59], [32.64, 0.97]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, -0.11], [0.09, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.08, 0.08], [0.05, 0.11], [0, 0.13], [-0.05, 0.12], [-0.08, 0.08], [-0.11, 0.05], [-0.13, 0], [-0.12, -0.05], [-0.08, -0.08], [-0.05, -0.12], [0, -0.13]], "o": [[0, 0.13], [-0.05, 0.11], [-0.08, 0.08], [-0.12, 0.05], [-0.13, 0], [-0.11, -0.05], [-0.08, -0.08], [-0.05, -0.11], [0, -0.13], [0.05, -0.12], [0.08, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.09, 0.08], [0.05, 0.12], [0, 0]], "v": [[32.64, 0.97], [32.56, 1.33], [32.35, 1.62], [32.05, 1.82], [31.68, 1.9], [31.32, 1.82], [31.02, 1.62], [30.82, 1.33], [30.75, 0.97], [30.82, 0.59], [31.02, 0.29], [31.32, 0.1], [31.68, 0.02], [32.05, 0.1], [32.35, 0.29], [32.56, 0.59], [32.64, 0.97]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.05, -0.11], [0.09, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.08, 0.08], [0.05, 0.11], [0, 0.13], [-0.05, 0.12], [-0.08, 0.08], [-0.11, 0.05], [-0.13, 0], [-0.12, -0.05], [-0.08, -0.08], [-0.05, -0.12], [0, -0.13]], "o": [[0, 0.13], [-0.05, 0.11], [-0.08, 0.08], [-0.12, 0.05], [-0.13, 0], [-0.11, -0.05], [-0.08, -0.08], [-0.05, -0.11], [0, -0.13], [0.05, -0.12], [0.08, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.09, 0.08], [0.05, 0.12], [0, 0]], "v": [[32.64, 0.97], [32.56, 1.33], [32.35, 1.62], [32.05, 1.82], [31.68, 1.9], [31.32, 1.82], [31.02, 1.62], [30.82, 1.33], [30.75, 0.97], [30.82, 0.59], [31.02, 0.29], [31.32, 0.1], [31.68, 0.02], [32.05, 0.1], [32.35, 0.29], [32.56, 0.59], [32.64, 0.97]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0.05, -0.11], [0.09, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.08, 0.08], [0.05, 0.11], [0, 0.13], [-0.05, 0.12], [-0.08, 0.08], [-0.11, 0.05], [-0.13, 0], [-0.12, -0.05], [-0.08, -0.08], [-0.05, -0.12], [0, -0.13]], "o": [[0, 0.13], [-0.05, 0.11], [-0.08, 0.08], [-0.12, 0.05], [-0.13, 0], [-0.11, -0.05], [-0.08, -0.08], [-0.05, -0.11], [0, -0.13], [0.05, -0.12], [0.08, -0.08], [0.12, -0.05], [0.13, 0], [0.12, 0.05], [0.09, 0.08], [0.05, 0.12], [0, 0]], "v": [[32.64, 0.97], [32.56, 1.33], [32.35, 1.62], [32.05, 1.82], [31.68, 1.9], [31.32, 1.82], [31.02, 1.62], [30.82, 1.33], [30.75, 0.97], [30.82, 0.59], [31.02, 0.29], [31.32, 0.1], [31.68, 0.02], [32.05, 0.1], [32.35, 0.29], [32.56, 0.59], [32.64, 0.97]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.45, 0], [35.45, 8.92], [33.97, 8.92], [33.97, 0], [35.45, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.45, 0], [35.45, 8.92], [33.97, 8.92], [33.97, 0], [35.45, 0]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.45, 0], [35.45, 8.92], [33.97, 8.92], [33.97, 0], [35.45, 0]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.45, 0], [35.45, 8.92], [33.97, 8.92], [33.97, 0], [35.45, 0]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.45, 0], [35.45, 8.92], [33.97, 8.92], [33.97, 0], [35.45, 0]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.45, 0], [35.45, 8.92], [33.97, 8.92], [33.97, 0], [35.45, 0]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.5, -0.15], [0.57, 0], [0.58, 0.22], [0.41, 0.39], [0.22, 0.54], [0, 0.64], [-0.21, 0.54], [-0.39, 0.39], [-0.56, 0.22], [-0.69, 0], [-0.3, -0.06], [-0.26, -0.1], [-0.22, -0.14], [-0.18, -0.17], [0, 0], [0.12, -0.03], [0.14, 0.08], [0.13, 0.08], [0.16, 0.06], [0.2, 0.04], [0.26, 0], [0.35, -0.14], [0.24, -0.27], [0.13, -0.38], [0, -0.46], [-0.14, -0.39], [-0.26, -0.27], [-0.36, -0.14], [-0.45, 0], [-0.25, 0.07], [-0.24, 0.12], [0, 0], [0, 0], [0.06, 0.06], [0, 0.08], [0, 0], [0, 0]], "o": [[0, 0], [-0.44, 0.32], [-0.5, 0.15], [-0.71, 0], [-0.57, -0.22], [-0.4, -0.39], [-0.22, -0.54], [0, -0.65], [0.21, -0.54], [0.39, -0.39], [0.56, -0.22], [0.35, 0], [0.31, 0.06], [0.26, 0.1], [0.22, 0.14], [0, 0], [-0.07, 0.12], [-0.12, 0.02], [-0.13, -0.08], [-0.13, -0.08], [-0.16, -0.06], [-0.2, -0.04], [-0.43, 0], [-0.34, 0.14], [-0.24, 0.27], [-0.13, 0.38], [0, 0.5], [0.14, 0.39], [0.26, 0.27], [0.36, 0.14], [0.32, 0], [0.25, -0.07], [0, 0], [0, 0], [-0.1, 0], [-0.06, -0.06], [0, 0], [0, 0], [0, 0]], "v": [[44.22, 4.57], [44.22, 8.08], [42.81, 8.79], [41.21, 9.01], [39.29, 8.68], [37.82, 7.76], [36.88, 6.36], [36.55, 4.58], [36.87, 2.78], [37.77, 1.38], [39.19, 0.47], [41.06, 0.14], [42.05, 0.23], [42.9, 0.46], [43.62, 0.82], [44.21, 1.28], [43.75, 2.01], [43.47, 2.23], [43.09, 2.14], [42.69, 1.91], [42.25, 1.71], [41.7, 1.57], [41.01, 1.52], [39.84, 1.73], [38.96, 2.35], [38.4, 3.32], [38.2, 4.58], [38.41, 5.91], [39.01, 6.9], [39.95, 7.52], [41.16, 7.73], [42.02, 7.63], [42.76, 7.35], [42.76, 5.78], [41.67, 5.78], [41.42, 5.69], [41.34, 5.48], [41.34, 4.57], [44.22, 4.57]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.5, -0.15], [0.57, 0], [0.58, 0.22], [0.41, 0.39], [0.22, 0.54], [0, 0.64], [-0.21, 0.54], [-0.39, 0.39], [-0.56, 0.22], [-0.69, 0], [-0.3, -0.06], [-0.26, -0.1], [-0.22, -0.14], [-0.18, -0.17], [0, 0], [0.12, -0.03], [0.14, 0.08], [0.13, 0.08], [0.16, 0.06], [0.2, 0.04], [0.26, 0], [0.35, -0.14], [0.24, -0.27], [0.13, -0.38], [0, -0.46], [-0.14, -0.39], [-0.26, -0.27], [-0.36, -0.14], [-0.45, 0], [-0.25, 0.07], [-0.24, 0.12], [0, 0], [0, 0], [0.06, 0.06], [0, 0.08], [0, 0], [0, 0]], "o": [[0, 0], [-0.44, 0.32], [-0.5, 0.15], [-0.71, 0], [-0.57, -0.22], [-0.4, -0.39], [-0.22, -0.54], [0, -0.65], [0.21, -0.54], [0.39, -0.39], [0.56, -0.22], [0.35, 0], [0.31, 0.06], [0.26, 0.1], [0.22, 0.14], [0, 0], [-0.07, 0.12], [-0.12, 0.02], [-0.13, -0.08], [-0.13, -0.08], [-0.16, -0.06], [-0.2, -0.04], [-0.43, 0], [-0.34, 0.14], [-0.24, 0.27], [-0.13, 0.38], [0, 0.5], [0.14, 0.39], [0.26, 0.27], [0.36, 0.14], [0.32, 0], [0.25, -0.07], [0, 0], [0, 0], [-0.1, 0], [-0.06, -0.06], [0, 0], [0, 0], [0, 0]], "v": [[44.22, 4.57], [44.22, 8.08], [42.81, 8.79], [41.21, 9.01], [39.29, 8.68], [37.82, 7.76], [36.88, 6.36], [36.55, 4.58], [36.87, 2.78], [37.77, 1.38], [39.19, 0.47], [41.06, 0.14], [42.05, 0.23], [42.9, 0.46], [43.62, 0.82], [44.21, 1.28], [43.75, 2.01], [43.47, 2.23], [43.09, 2.14], [42.69, 1.91], [42.25, 1.71], [41.7, 1.57], [41.01, 1.52], [39.84, 1.73], [38.96, 2.35], [38.4, 3.32], [38.2, 4.58], [38.41, 5.91], [39.01, 6.9], [39.95, 7.52], [41.16, 7.73], [42.02, 7.63], [42.76, 7.35], [42.76, 5.78], [41.67, 5.78], [41.42, 5.69], [41.34, 5.48], [41.34, 4.57], [44.22, 4.57]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.5, -0.15], [0.57, 0], [0.58, 0.22], [0.41, 0.39], [0.22, 0.54], [0, 0.64], [-0.21, 0.54], [-0.39, 0.39], [-0.56, 0.22], [-0.69, 0], [-0.3, -0.06], [-0.26, -0.1], [-0.22, -0.14], [-0.18, -0.17], [0, 0], [0.12, -0.03], [0.14, 0.08], [0.13, 0.08], [0.16, 0.06], [0.2, 0.04], [0.26, 0], [0.35, -0.14], [0.24, -0.27], [0.13, -0.38], [0, -0.46], [-0.14, -0.39], [-0.26, -0.27], [-0.36, -0.14], [-0.45, 0], [-0.25, 0.07], [-0.24, 0.12], [0, 0], [0, 0], [0.06, 0.06], [0, 0.08], [0, 0], [0, 0]], "o": [[0, 0], [-0.44, 0.32], [-0.5, 0.15], [-0.71, 0], [-0.57, -0.22], [-0.4, -0.39], [-0.22, -0.54], [0, -0.65], [0.21, -0.54], [0.39, -0.39], [0.56, -0.22], [0.35, 0], [0.31, 0.06], [0.26, 0.1], [0.22, 0.14], [0, 0], [-0.07, 0.12], [-0.12, 0.02], [-0.13, -0.08], [-0.13, -0.08], [-0.16, -0.06], [-0.2, -0.04], [-0.43, 0], [-0.34, 0.14], [-0.24, 0.27], [-0.13, 0.38], [0, 0.5], [0.14, 0.39], [0.26, 0.27], [0.36, 0.14], [0.32, 0], [0.25, -0.07], [0, 0], [0, 0], [-0.1, 0], [-0.06, -0.06], [0, 0], [0, 0], [0, 0]], "v": [[44.22, 4.57], [44.22, 8.08], [42.81, 8.79], [41.21, 9.01], [39.29, 8.68], [37.82, 7.76], [36.88, 6.36], [36.55, 4.58], [36.87, 2.78], [37.77, 1.38], [39.19, 0.47], [41.06, 0.14], [42.05, 0.23], [42.9, 0.46], [43.62, 0.82], [44.21, 1.28], [43.75, 2.01], [43.47, 2.23], [43.09, 2.14], [42.69, 1.91], [42.25, 1.71], [41.7, 1.57], [41.01, 1.52], [39.84, 1.73], [38.96, 2.35], [38.4, 3.32], [38.2, 4.58], [38.41, 5.91], [39.01, 6.9], [39.95, 7.52], [41.16, 7.73], [42.02, 7.63], [42.76, 7.35], [42.76, 5.78], [41.67, 5.78], [41.42, 5.69], [41.34, 5.48], [41.34, 4.57], [44.22, 4.57]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.5, -0.15], [0.57, 0], [0.58, 0.22], [0.41, 0.39], [0.22, 0.54], [0, 0.64], [-0.21, 0.54], [-0.39, 0.39], [-0.56, 0.22], [-0.69, 0], [-0.3, -0.06], [-0.26, -0.1], [-0.22, -0.14], [-0.18, -0.17], [0, 0], [0.12, -0.03], [0.14, 0.08], [0.13, 0.08], [0.16, 0.06], [0.2, 0.04], [0.26, 0], [0.35, -0.14], [0.24, -0.27], [0.13, -0.38], [0, -0.46], [-0.14, -0.39], [-0.26, -0.27], [-0.36, -0.14], [-0.45, 0], [-0.25, 0.07], [-0.24, 0.12], [0, 0], [0, 0], [0.06, 0.06], [0, 0.08], [0, 0], [0, 0]], "o": [[0, 0], [-0.44, 0.32], [-0.5, 0.15], [-0.71, 0], [-0.57, -0.22], [-0.4, -0.39], [-0.22, -0.54], [0, -0.65], [0.21, -0.54], [0.39, -0.39], [0.56, -0.22], [0.35, 0], [0.31, 0.06], [0.26, 0.1], [0.22, 0.14], [0, 0], [-0.07, 0.12], [-0.12, 0.02], [-0.13, -0.08], [-0.13, -0.08], [-0.16, -0.06], [-0.2, -0.04], [-0.43, 0], [-0.34, 0.14], [-0.24, 0.27], [-0.13, 0.38], [0, 0.5], [0.14, 0.39], [0.26, 0.27], [0.36, 0.14], [0.32, 0], [0.25, -0.07], [0, 0], [0, 0], [-0.1, 0], [-0.06, -0.06], [0, 0], [0, 0], [0, 0]], "v": [[44.22, 4.57], [44.22, 8.08], [42.81, 8.79], [41.21, 9.01], [39.29, 8.68], [37.82, 7.76], [36.88, 6.36], [36.55, 4.58], [36.87, 2.78], [37.77, 1.38], [39.19, 0.47], [41.06, 0.14], [42.05, 0.23], [42.9, 0.46], [43.62, 0.82], [44.21, 1.28], [43.75, 2.01], [43.47, 2.23], [43.09, 2.14], [42.69, 1.91], [42.25, 1.71], [41.7, 1.57], [41.01, 1.52], [39.84, 1.73], [38.96, 2.35], [38.4, 3.32], [38.2, 4.58], [38.41, 5.91], [39.01, 6.9], [39.95, 7.52], [41.16, 7.73], [42.02, 7.63], [42.76, 7.35], [42.76, 5.78], [41.67, 5.78], [41.42, 5.69], [41.34, 5.48], [41.34, 4.57], [44.22, 4.57]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.5, -0.15], [0.57, 0], [0.58, 0.22], [0.41, 0.39], [0.22, 0.54], [0, 0.64], [-0.21, 0.54], [-0.39, 0.39], [-0.56, 0.22], [-0.69, 0], [-0.3, -0.06], [-0.26, -0.1], [-0.22, -0.14], [-0.18, -0.17], [0, 0], [0.12, -0.03], [0.14, 0.08], [0.13, 0.08], [0.16, 0.06], [0.2, 0.04], [0.26, 0], [0.35, -0.14], [0.24, -0.27], [0.13, -0.38], [0, -0.46], [-0.14, -0.39], [-0.26, -0.27], [-0.36, -0.14], [-0.45, 0], [-0.25, 0.07], [-0.24, 0.12], [0, 0], [0, 0], [0.06, 0.06], [0, 0.08], [0, 0], [0, 0]], "o": [[0, 0], [-0.44, 0.32], [-0.5, 0.15], [-0.71, 0], [-0.57, -0.22], [-0.4, -0.39], [-0.22, -0.54], [0, -0.65], [0.21, -0.54], [0.39, -0.39], [0.56, -0.22], [0.35, 0], [0.31, 0.06], [0.26, 0.1], [0.22, 0.14], [0, 0], [-0.07, 0.12], [-0.12, 0.02], [-0.13, -0.08], [-0.13, -0.08], [-0.16, -0.06], [-0.2, -0.04], [-0.43, 0], [-0.34, 0.14], [-0.24, 0.27], [-0.13, 0.38], [0, 0.5], [0.14, 0.39], [0.26, 0.27], [0.36, 0.14], [0.32, 0], [0.25, -0.07], [0, 0], [0, 0], [-0.1, 0], [-0.06, -0.06], [0, 0], [0, 0], [0, 0]], "v": [[44.22, 4.57], [44.22, 8.08], [42.81, 8.79], [41.21, 9.01], [39.29, 8.68], [37.82, 7.76], [36.88, 6.36], [36.55, 4.58], [36.87, 2.78], [37.77, 1.38], [39.19, 0.47], [41.06, 0.14], [42.05, 0.23], [42.9, 0.46], [43.62, 0.82], [44.21, 1.28], [43.75, 2.01], [43.47, 2.23], [43.09, 2.14], [42.69, 1.91], [42.25, 1.71], [41.7, 1.57], [41.01, 1.52], [39.84, 1.73], [38.96, 2.35], [38.4, 3.32], [38.2, 4.58], [38.41, 5.91], [39.01, 6.9], [39.95, 7.52], [41.16, 7.73], [42.02, 7.63], [42.76, 7.35], [42.76, 5.78], [41.67, 5.78], [41.42, 5.69], [41.34, 5.48], [41.34, 4.57], [44.22, 4.57]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.5, -0.15], [0.57, 0], [0.58, 0.22], [0.41, 0.39], [0.22, 0.54], [0, 0.64], [-0.21, 0.54], [-0.39, 0.39], [-0.56, 0.22], [-0.69, 0], [-0.3, -0.06], [-0.26, -0.1], [-0.22, -0.14], [-0.18, -0.17], [0, 0], [0.12, -0.03], [0.14, 0.08], [0.13, 0.08], [0.16, 0.06], [0.2, 0.04], [0.26, 0], [0.35, -0.14], [0.24, -0.27], [0.13, -0.38], [0, -0.46], [-0.14, -0.39], [-0.26, -0.27], [-0.36, -0.14], [-0.45, 0], [-0.25, 0.07], [-0.24, 0.12], [0, 0], [0, 0], [0.06, 0.06], [0, 0.08], [0, 0], [0, 0]], "o": [[0, 0], [-0.44, 0.32], [-0.5, 0.15], [-0.71, 0], [-0.57, -0.22], [-0.4, -0.39], [-0.22, -0.54], [0, -0.65], [0.21, -0.54], [0.39, -0.39], [0.56, -0.22], [0.35, 0], [0.31, 0.06], [0.26, 0.1], [0.22, 0.14], [0, 0], [-0.07, 0.12], [-0.12, 0.02], [-0.13, -0.08], [-0.13, -0.08], [-0.16, -0.06], [-0.2, -0.04], [-0.43, 0], [-0.34, 0.14], [-0.24, 0.27], [-0.13, 0.38], [0, 0.5], [0.14, 0.39], [0.26, 0.27], [0.36, 0.14], [0.32, 0], [0.25, -0.07], [0, 0], [0, 0], [-0.1, 0], [-0.06, -0.06], [0, 0], [0, 0], [0, 0]], "v": [[44.22, 4.57], [44.22, 8.08], [42.81, 8.79], [41.21, 9.01], [39.29, 8.68], [37.82, 7.76], [36.88, 6.36], [36.55, 4.58], [36.87, 2.78], [37.77, 1.38], [39.19, 0.47], [41.06, 0.14], [42.05, 0.23], [42.9, 0.46], [43.62, 0.82], [44.21, 1.28], [43.75, 2.01], [43.47, 2.23], [43.09, 2.14], [42.69, 1.91], [42.25, 1.71], [41.7, 1.57], [41.01, 1.52], [39.84, 1.73], [38.96, 2.35], [38.4, 3.32], [38.2, 4.58], [38.41, 5.91], [39.01, 6.9], [39.95, 7.52], [41.16, 7.73], [42.02, 7.63], [42.76, 7.35], [42.76, 5.78], [41.67, 5.78], [41.42, 5.69], [41.34, 5.48], [41.34, 4.57], [44.22, 4.57]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.07], [-0.14, 0.14], [-0.07, 0.2], [0, 0.24], [0.07, 0.19], [0.14, 0.13], [0.22, 0.07], [0.3, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.22, -0.08], [0.15, -0.14], [0.07, -0.2], [0, -0.23], [-0.07, -0.19], [-0.14, -0.13], [-0.22, -0.07], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 4.6], [49.37, 4.49], [49.91, 4.18], [50.24, 3.67], [50.35, 3.01], [50.24, 2.38], [49.92, 1.9], [49.37, 1.6], [48.59, 1.49], [47.39, 1.49], [47.39, 4.6], [48.59, 4.6]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.07], [-0.14, 0.14], [-0.07, 0.2], [0, 0.24], [0.07, 0.19], [0.14, 0.13], [0.22, 0.07], [0.3, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.22, -0.08], [0.15, -0.14], [0.07, -0.2], [0, -0.23], [-0.07, -0.19], [-0.14, -0.13], [-0.22, -0.07], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 4.6], [49.37, 4.49], [49.91, 4.18], [50.24, 3.67], [50.35, 3.01], [50.24, 2.38], [49.92, 1.9], [49.37, 1.6], [48.59, 1.49], [47.39, 1.49], [47.39, 4.6], [48.59, 4.6]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.07], [-0.14, 0.14], [-0.07, 0.2], [0, 0.24], [0.07, 0.19], [0.14, 0.13], [0.22, 0.07], [0.3, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.22, -0.08], [0.15, -0.14], [0.07, -0.2], [0, -0.23], [-0.07, -0.19], [-0.14, -0.13], [-0.22, -0.07], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 4.6], [49.37, 4.49], [49.91, 4.18], [50.24, 3.67], [50.35, 3.01], [50.24, 2.38], [49.92, 1.9], [49.37, 1.6], [48.59, 1.49], [47.39, 1.49], [47.39, 4.6], [48.59, 4.6]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.07], [-0.14, 0.14], [-0.07, 0.2], [0, 0.24], [0.07, 0.19], [0.14, 0.13], [0.22, 0.07], [0.3, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.22, -0.08], [0.15, -0.14], [0.07, -0.2], [0, -0.23], [-0.07, -0.19], [-0.14, -0.13], [-0.22, -0.07], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 4.6], [49.37, 4.49], [49.91, 4.18], [50.24, 3.67], [50.35, 3.01], [50.24, 2.38], [49.92, 1.9], [49.37, 1.6], [48.59, 1.49], [47.39, 1.49], [47.39, 4.6], [48.59, 4.6]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.22, 0.07], [-0.14, 0.14], [-0.07, 0.2], [0, 0.24], [0.07, 0.19], [0.14, 0.13], [0.22, 0.07], [0.3, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.22, -0.08], [0.15, -0.14], [0.07, -0.2], [0, -0.23], [-0.07, -0.19], [-0.14, -0.13], [-0.22, -0.07], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 4.6], [49.37, 4.49], [49.91, 4.18], [50.24, 3.67], [50.35, 3.01], [50.24, 2.38], [49.92, 1.9], [49.37, 1.6], [48.59, 1.49], [47.39, 1.49], [47.39, 4.6], [48.59, 4.6]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-0.22, 0.07], [-0.14, 0.14], [-0.07, 0.2], [0, 0.24], [0.07, 0.19], [0.14, 0.13], [0.22, 0.07], [0.3, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.3, 0], [0.22, -0.08], [0.15, -0.14], [0.07, -0.2], [0, -0.23], [-0.07, -0.19], [-0.14, -0.13], [-0.22, -0.07], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 4.6], [49.37, 4.49], [49.91, 4.18], [50.24, 3.67], [50.35, 3.01], [50.24, 2.38], [49.92, 1.9], [49.37, 1.6], [48.59, 1.49], [47.39, 1.49], [47.39, 4.6], [48.59, 4.6]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.42, -0.14], [-0.28, -0.24], [-0.13, -0.34], [0, -0.4], [0.14, -0.35], [0.28, -0.25], [0.42, -0.14], [0.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.58, 0], [0.42, 0.14], [0.28, 0.24], [0.14, 0.34], [0, 0.42], [-0.14, 0.35], [-0.28, 0.25], [-0.42, 0.14], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 0.24], [50.1, 0.44], [51.15, 1.01], [51.76, 1.89], [51.97, 3.01], [51.76, 4.16], [51.13, 5.06], [50.07, 5.65], [48.59, 5.86], [47.39, 5.86], [47.39, 8.92], [45.77, 8.92], [45.77, 0.24], [48.59, 0.24]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.42, -0.14], [-0.28, -0.24], [-0.13, -0.34], [0, -0.4], [0.14, -0.35], [0.28, -0.25], [0.42, -0.14], [0.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.58, 0], [0.42, 0.14], [0.28, 0.24], [0.14, 0.34], [0, 0.42], [-0.14, 0.35], [-0.28, 0.25], [-0.42, 0.14], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 0.24], [50.1, 0.44], [51.15, 1.01], [51.76, 1.89], [51.97, 3.01], [51.76, 4.16], [51.13, 5.06], [50.07, 5.65], [48.59, 5.86], [47.39, 5.86], [47.39, 8.92], [45.77, 8.92], [45.77, 0.24], [48.59, 0.24]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.42, -0.14], [-0.28, -0.24], [-0.13, -0.34], [0, -0.4], [0.14, -0.35], [0.28, -0.25], [0.42, -0.14], [0.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.58, 0], [0.42, 0.14], [0.28, 0.24], [0.14, 0.34], [0, 0.42], [-0.14, 0.35], [-0.28, 0.25], [-0.42, 0.14], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 0.24], [50.1, 0.44], [51.15, 1.01], [51.76, 1.89], [51.97, 3.01], [51.76, 4.16], [51.13, 5.06], [50.07, 5.65], [48.59, 5.86], [47.39, 5.86], [47.39, 8.92], [45.77, 8.92], [45.77, 0.24], [48.59, 0.24]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.42, -0.14], [-0.28, -0.24], [-0.13, -0.34], [0, -0.4], [0.14, -0.35], [0.28, -0.25], [0.42, -0.14], [0.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.58, 0], [0.42, 0.14], [0.28, 0.24], [0.14, 0.34], [0, 0.42], [-0.14, 0.35], [-0.28, 0.25], [-0.42, 0.14], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 0.24], [50.1, 0.44], [51.15, 1.01], [51.76, 1.89], [51.97, 3.01], [51.76, 4.16], [51.13, 5.06], [50.07, 5.65], [48.59, 5.86], [47.39, 5.86], [47.39, 8.92], [45.77, 8.92], [45.77, 0.24], [48.59, 0.24]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.42, -0.14], [-0.28, -0.24], [-0.13, -0.34], [0, -0.4], [0.14, -0.35], [0.28, -0.25], [0.42, -0.14], [0.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.58, 0], [0.42, 0.14], [0.28, 0.24], [0.14, 0.34], [0, 0.42], [-0.14, 0.35], [-0.28, 0.25], [-0.42, 0.14], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 0.24], [50.1, 0.44], [51.15, 1.01], [51.76, 1.89], [51.97, 3.01], [51.76, 4.16], [51.13, 5.06], [50.07, 5.65], [48.59, 5.86], [47.39, 5.86], [47.39, 8.92], [45.77, 8.92], [45.77, 0.24], [48.59, 0.24]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-0.42, -0.14], [-0.28, -0.24], [-0.13, -0.34], [0, -0.4], [0.14, -0.35], [0.28, -0.25], [0.42, -0.14], [0.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.58, 0], [0.42, 0.14], [0.28, 0.24], [0.14, 0.34], [0, 0.42], [-0.14, 0.35], [-0.28, 0.25], [-0.42, 0.14], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.59, 0.24], [50.1, 0.44], [51.15, 1.01], [51.76, 1.89], [51.97, 3.01], [51.76, 4.16], [51.13, 5.06], [50.07, 5.65], [48.59, 5.86], [47.39, 5.86], [47.39, 8.92], [45.77, 8.92], [45.77, 0.24], [48.59, 0.24]]}], "t": 185}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.26, 1.57], [56.65, 1.57], [56.65, 8.92], [55.03, 8.92], [55.03, 1.57], [52.42, 1.57], [52.42, 0.24], [59.26, 0.24], [59.26, 1.57]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.26, 1.57], [56.65, 1.57], [56.65, 8.92], [55.03, 8.92], [55.03, 1.57], [52.42, 1.57], [52.42, 0.24], [59.26, 0.24], [59.26, 1.57]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.26, 1.57], [56.65, 1.57], [56.65, 8.92], [55.03, 8.92], [55.03, 1.57], [52.42, 1.57], [52.42, 0.24], [59.26, 0.24], [59.26, 1.57]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.26, 1.57], [56.65, 1.57], [56.65, 8.92], [55.03, 8.92], [55.03, 1.57], [52.42, 1.57], [52.42, 0.24], [59.26, 0.24], [59.26, 1.57]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.26, 1.57], [56.65, 1.57], [56.65, 8.92], [55.03, 8.92], [55.03, 1.57], [52.42, 1.57], [52.42, 0.24], [59.26, 0.24], [59.26, 1.57]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.26, 1.57], [56.65, 1.57], [56.65, 8.92], [55.03, 8.92], [55.03, 1.57], [52.42, 1.57], [52.42, 0.24], [59.26, 0.24], [59.26, 1.57]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5145, 0.3565, 0.9834], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5145, 0.3565, 0.9834], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5145, 0.3565, 0.9834], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5145, 0.3565, 0.9834], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5145, 0.3565, 0.9834], "t": 140}, {"s": [0.5145, 0.3565, 0.9834], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 2}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.12, 0.18], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.12, 0.18], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.12, 0.18], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.12, 0.18], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.12, 0.18], "t": 140}, {"s": [3.12, 0.18], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.13, 14.15], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.13, 14.15], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.81, 14.15], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.81, 14.15], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.13, 14.15], "t": 140}, {"s": [10.13, 14.15], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.24, 0], [6.17, 0.22], [0.07, 0.36], [0, 0.13], [6.24, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.24, 0], [6.17, 0.22], [0.07, 0.36], [0, 0.13], [6.24, 0]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.24, 0], [6.17, 0.22], [0.07, 0.36], [0, 0.13], [6.24, 0]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.24, 0], [6.17, 0.22], [0.07, 0.36], [0, 0.13], [6.24, 0]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.24, 0], [6.17, 0.22], [0.07, 0.36], [0, 0.13], [6.24, 0]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.24, 0], [6.17, 0.22], [0.07, 0.36], [0, 0.13], [6.24, 0]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 140}, {"s": [0.8471, 0.8706, 0.9098], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 3}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.22, 3.02], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.22, 3.02], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.22, 3.02], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.22, 3.02], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.22, 3.02], "t": 140}, {"s": [3.22, 3.02], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.13, 14.52], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.13, 14.52], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.81, 14.52], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.81, 14.52], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.13, 14.52], "t": 140}, {"s": [10.13, 14.52], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.01], [0, 0], [0.12, -0.41], [0.77, -0.34], [0.37, -0.01], [0.36, 1.15], [0.14, 0.45], [0, 0], [0, 0.01], [-0.36, 0.5], [-0.23, 0.14], [-0.34, 0.01], [0, 0], [-0.26, -0.13], [-0.17, -0.24], [0.17, -0.58]], "o": [[0, 0.01], [0, 0], [-0.12, 0.41], [-0.26, 0.85], [-0.34, 0.15], [-1.19, 0.04], [-0.14, -0.45], [0, 0], [0, -0.01], [-0.18, -0.58], [0.16, -0.22], [0.29, -0.17], [0, 0], [0.29, 0], [0.26, 0.13], [0.35, 0.48], [0, 0]], "v": [[6.35, 2.44], [6.34, 2.47], [6.27, 2.7], [5.9, 3.94], [4.26, 5.8], [3.19, 6.05], [0.59, 4.18], [0.17, 2.83], [0.1, 2.61], [0.09, 2.57], [0.38, 0.85], [0.96, 0.31], [1.92, 0.04], [4.57, 0], [5.41, 0.19], [6.07, 0.75], [6.35, 2.44]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.01], [0, 0], [0.12, -0.41], [0.77, -0.34], [0.37, -0.01], [0.36, 1.15], [0.14, 0.45], [0, 0], [0, 0.01], [-0.36, 0.5], [-0.23, 0.14], [-0.34, 0.01], [0, 0], [-0.26, -0.13], [-0.17, -0.24], [0.17, -0.58]], "o": [[0, 0.01], [0, 0], [-0.12, 0.41], [-0.26, 0.85], [-0.34, 0.15], [-1.19, 0.04], [-0.14, -0.45], [0, 0], [0, -0.01], [-0.18, -0.58], [0.16, -0.22], [0.29, -0.17], [0, 0], [0.29, 0], [0.26, 0.13], [0.35, 0.48], [0, 0]], "v": [[6.35, 2.44], [6.34, 2.47], [6.27, 2.7], [5.9, 3.94], [4.26, 5.8], [3.19, 6.05], [0.59, 4.18], [0.17, 2.83], [0.1, 2.61], [0.09, 2.57], [0.38, 0.85], [0.96, 0.31], [1.92, 0.04], [4.57, 0], [5.41, 0.19], [6.07, 0.75], [6.35, 2.44]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.01], [0, 0], [0.12, -0.41], [0.77, -0.34], [0.37, -0.01], [0.36, 1.15], [0.14, 0.45], [0, 0], [0, 0.01], [-0.36, 0.5], [-0.23, 0.14], [-0.34, 0.01], [0, 0], [-0.26, -0.13], [-0.17, -0.24], [0.17, -0.58]], "o": [[0, 0.01], [0, 0], [-0.12, 0.41], [-0.26, 0.85], [-0.34, 0.15], [-1.19, 0.04], [-0.14, -0.45], [0, 0], [0, -0.01], [-0.18, -0.58], [0.16, -0.22], [0.29, -0.17], [0, 0], [0.29, 0], [0.26, 0.13], [0.35, 0.48], [0, 0]], "v": [[6.35, 2.44], [6.34, 2.47], [6.27, 2.7], [5.9, 3.94], [4.26, 5.8], [3.19, 6.05], [0.59, 4.18], [0.17, 2.83], [0.1, 2.61], [0.09, 2.57], [0.38, 0.85], [0.96, 0.31], [1.92, 0.04], [4.57, 0], [5.41, 0.19], [6.07, 0.75], [6.35, 2.44]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.01], [0, 0], [0.12, -0.41], [0.77, -0.34], [0.37, -0.01], [0.36, 1.15], [0.14, 0.45], [0, 0], [0, 0.01], [-0.36, 0.5], [-0.23, 0.14], [-0.34, 0.01], [0, 0], [-0.26, -0.13], [-0.17, -0.24], [0.17, -0.58]], "o": [[0, 0.01], [0, 0], [-0.12, 0.41], [-0.26, 0.85], [-0.34, 0.15], [-1.19, 0.04], [-0.14, -0.45], [0, 0], [0, -0.01], [-0.18, -0.58], [0.16, -0.22], [0.29, -0.17], [0, 0], [0.29, 0], [0.26, 0.13], [0.35, 0.48], [0, 0]], "v": [[6.35, 2.44], [6.34, 2.47], [6.27, 2.7], [5.9, 3.94], [4.26, 5.8], [3.19, 6.05], [0.59, 4.18], [0.17, 2.83], [0.1, 2.61], [0.09, 2.57], [0.38, 0.85], [0.96, 0.31], [1.92, 0.04], [4.57, 0], [5.41, 0.19], [6.07, 0.75], [6.35, 2.44]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.01], [0, 0], [0.12, -0.41], [0.77, -0.34], [0.37, -0.01], [0.36, 1.15], [0.14, 0.45], [0, 0], [0, 0.01], [-0.36, 0.5], [-0.23, 0.14], [-0.34, 0.01], [0, 0], [-0.26, -0.13], [-0.17, -0.24], [0.17, -0.58]], "o": [[0, 0.01], [0, 0], [-0.12, 0.41], [-0.26, 0.85], [-0.34, 0.15], [-1.19, 0.04], [-0.14, -0.45], [0, 0], [0, -0.01], [-0.18, -0.58], [0.16, -0.22], [0.29, -0.17], [0, 0], [0.29, 0], [0.26, 0.13], [0.35, 0.48], [0, 0]], "v": [[6.35, 2.44], [6.34, 2.47], [6.27, 2.7], [5.9, 3.94], [4.26, 5.8], [3.19, 6.05], [0.59, 4.18], [0.17, 2.83], [0.1, 2.61], [0.09, 2.57], [0.38, 0.85], [0.96, 0.31], [1.92, 0.04], [4.57, 0], [5.41, 0.19], [6.07, 0.75], [6.35, 2.44]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, -0.01], [0, 0], [0.12, -0.41], [0.77, -0.34], [0.37, -0.01], [0.36, 1.15], [0.14, 0.45], [0, 0], [0, 0.01], [-0.36, 0.5], [-0.23, 0.14], [-0.34, 0.01], [0, 0], [-0.26, -0.13], [-0.17, -0.24], [0.17, -0.58]], "o": [[0, 0.01], [0, 0], [-0.12, 0.41], [-0.26, 0.85], [-0.34, 0.15], [-1.19, 0.04], [-0.14, -0.45], [0, 0], [0, -0.01], [-0.18, -0.58], [0.16, -0.22], [0.29, -0.17], [0, 0], [0.29, 0], [0.26, 0.13], [0.35, 0.48], [0, 0]], "v": [[6.35, 2.44], [6.34, 2.47], [6.27, 2.7], [5.9, 3.94], [4.26, 5.8], [3.19, 6.05], [0.59, 4.18], [0.17, 2.83], [0.1, 2.61], [0.09, 2.57], [0.38, 0.85], [0.96, 0.31], [1.92, 0.04], [4.57, 0], [5.41, 0.19], [6.07, 0.75], [6.35, 2.44]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 140}, {"s": [1, 1, 1], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 4}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.15], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.15], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.15], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.15], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.15], "t": 140}, {"s": [0.28, 1.15], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [14.58, 7.62], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [14.58, 7.62], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [14.27, 7.62], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [14.27, 7.62], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [14.58, 7.62], "t": 140}, {"s": [14.58, 7.62], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.26], [0, 0], [-0.16, 0], [0, -0.26], [0, 0], [0.16, 0]], "o": [[-0.16, 0], [0, 0], [0, -0.26], [0.16, 0], [0, 0], [0, 0.26], [0, 0]], "v": [[0.28, 2.31], [0, 1.84], [0, 0.47], [0.28, 0], [0.57, 0.47], [0.57, 1.84], [0.28, 2.31]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.26], [0, 0], [-0.16, 0], [0, -0.26], [0, 0], [0.16, 0]], "o": [[-0.16, 0], [0, 0], [0, -0.26], [0.16, 0], [0, 0], [0, 0.26], [0, 0]], "v": [[0.28, 2.31], [0, 1.84], [0, 0.47], [0.28, 0], [0.57, 0.47], [0.57, 1.84], [0.28, 2.31]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.26], [0, 0], [-0.16, 0], [0, -0.26], [0, 0], [0.16, 0]], "o": [[-0.16, 0], [0, 0], [0, -0.26], [0.16, 0], [0, 0], [0, 0.26], [0, 0]], "v": [[0.28, 2.31], [0, 1.84], [0, 0.47], [0.28, 0], [0.57, 0.47], [0.57, 1.84], [0.28, 2.31]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.26], [0, 0], [-0.16, 0], [0, -0.26], [0, 0], [0.16, 0]], "o": [[-0.16, 0], [0, 0], [0, -0.26], [0.16, 0], [0, 0], [0, 0.26], [0, 0]], "v": [[0.28, 2.31], [0, 1.84], [0, 0.47], [0.28, 0], [0.57, 0.47], [0.57, 1.84], [0.28, 2.31]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.26], [0, 0], [-0.16, 0], [0, -0.26], [0, 0], [0.16, 0]], "o": [[-0.16, 0], [0, 0], [0, -0.26], [0.16, 0], [0, 0], [0, 0.26], [0, 0]], "v": [[0.28, 2.31], [0, 1.84], [0, 0.47], [0.28, 0], [0.57, 0.47], [0.57, 1.84], [0.28, 2.31]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0.26], [0, 0], [-0.16, 0], [0, -0.26], [0, 0], [0.16, 0]], "o": [[-0.16, 0], [0, 0], [0, -0.26], [0.16, 0], [0, 0], [0, 0.26], [0, 0]], "v": [[0.28, 2.31], [0, 1.84], [0, 0.47], [0.28, 0], [0.57, 0.47], [0.57, 1.84], [0.28, 2.31]]}], "t": 185}]}}, {"ty": "gf", "bm": 0, "hd": false, "nm": "", "e": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.568444013595581, 1.153730034828186], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.568444013595581, 1.153730034828186], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.568444013595581, 1.153730034828186], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.568444013595581, 1.153730034828186], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.568444013595581, 1.153730034828186], "t": 140}, {"s": [0.568444013595581, 1.153730034828186], "t": 185}]}, "g": {"p": 2, "k": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 140}, {"s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 185}]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.153730034828186], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.153730034828186], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.153730034828186], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.153730034828186], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.153730034828186], "t": 140}, {"s": [0, 1.153730034828186], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 5}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.19], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.19], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.19], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.19], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.28, 1.19], "t": 140}, {"s": [0.28, 1.19], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [5.6, 7.63], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [5.6, 7.63], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [5.28, 7.63], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [5.28, 7.63], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [5.6, 7.63], "t": 140}, {"s": [5.6, 7.63], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.04, -0.08], [0.1, 0], [0, 0.27], [0, 0], [-0.17, 0], [-0.05, -0.06], [-0.01, -0.02], [0, -0.13]], "o": [[0, 0], [0, 0.11], [-0.05, 0.12], [-0.17, 0], [0, 0], [0, -0.27], [0.07, 0], [0.01, 0.02], [0.05, 0.09], [0, 0]], "v": [[0.56, 0.48], [0.56, 1.89], [0.52, 2.17], [0.29, 2.37], [0, 1.89], [0, 0.48], [0.3, 0], [0.47, 0.09], [0.49, 0.14], [0.56, 0.48]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.04, -0.08], [0.1, 0], [0, 0.27], [0, 0], [-0.17, 0], [-0.05, -0.06], [-0.01, -0.02], [0, -0.13]], "o": [[0, 0], [0, 0.11], [-0.05, 0.12], [-0.17, 0], [0, 0], [0, -0.27], [0.07, 0], [0.01, 0.02], [0.05, 0.09], [0, 0]], "v": [[0.56, 0.48], [0.56, 1.89], [0.52, 2.17], [0.29, 2.37], [0, 1.89], [0, 0.48], [0.3, 0], [0.47, 0.09], [0.49, 0.14], [0.56, 0.48]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.04, -0.08], [0.1, 0], [0, 0.27], [0, 0], [-0.17, 0], [-0.05, -0.06], [-0.01, -0.02], [0, -0.13]], "o": [[0, 0], [0, 0.11], [-0.05, 0.12], [-0.17, 0], [0, 0], [0, -0.27], [0.07, 0], [0.01, 0.02], [0.05, 0.09], [0, 0]], "v": [[0.56, 0.48], [0.56, 1.89], [0.52, 2.17], [0.29, 2.37], [0, 1.89], [0, 0.48], [0.3, 0], [0.47, 0.09], [0.49, 0.14], [0.56, 0.48]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.04, -0.08], [0.1, 0], [0, 0.27], [0, 0], [-0.17, 0], [-0.05, -0.06], [-0.01, -0.02], [0, -0.13]], "o": [[0, 0], [0, 0.11], [-0.05, 0.12], [-0.17, 0], [0, 0], [0, -0.27], [0.07, 0], [0.01, 0.02], [0.05, 0.09], [0, 0]], "v": [[0.56, 0.48], [0.56, 1.89], [0.52, 2.17], [0.29, 2.37], [0, 1.89], [0, 0.48], [0.3, 0], [0.47, 0.09], [0.49, 0.14], [0.56, 0.48]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.04, -0.08], [0.1, 0], [0, 0.27], [0, 0], [-0.17, 0], [-0.05, -0.06], [-0.01, -0.02], [0, -0.13]], "o": [[0, 0], [0, 0.11], [-0.05, 0.12], [-0.17, 0], [0, 0], [0, -0.27], [0.07, 0], [0.01, 0.02], [0.05, 0.09], [0, 0]], "v": [[0.56, 0.48], [0.56, 1.89], [0.52, 2.17], [0.29, 2.37], [0, 1.89], [0, 0.48], [0.3, 0], [0.47, 0.09], [0.49, 0.14], [0.56, 0.48]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.04, -0.08], [0.1, 0], [0, 0.27], [0, 0], [-0.17, 0], [-0.05, -0.06], [-0.01, -0.02], [0, -0.13]], "o": [[0, 0], [0, 0.11], [-0.05, 0.12], [-0.17, 0], [0, 0], [0, -0.27], [0.07, 0], [0.01, 0.02], [0.05, 0.09], [0, 0]], "v": [[0.56, 0.48], [0.56, 1.89], [0.52, 2.17], [0.29, 2.37], [0, 1.89], [0, 0.48], [0.3, 0], [0.47, 0.09], [0.49, 0.14], [0.56, 0.48]]}], "t": 185}]}}, {"ty": "gf", "bm": 0, "hd": false, "nm": "", "e": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5638930201530457, 1.1858899593353271], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5638930201530457, 1.1858899593353271], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5638930201530457, 1.1858899593353271], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5638930201530457, 1.1858899593353271], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5638930201530457, 1.1858899593353271], "t": 140}, {"s": [0.5638930201530457, 1.1858899593353271], "t": 185}]}, "g": {"p": 2, "k": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 140}, {"s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 185}]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.1858899593353271], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.1858899593353271], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.1858899593353271], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.1858899593353271], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 1.1858899593353271], "t": 140}, {"s": [0, 1.1858899593353271], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 6}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.54, 0.31], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.54, 0.31], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.54, 0.31], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.54, 0.31], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.54, 0.31], "t": 140}, {"s": [0.54, 0.31], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [11.56, 7.44], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [11.56, 7.44], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [11.25, 7.44], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [11.25, 7.44], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [11.56, 7.44], "t": 140}, {"s": [11.56, 7.44], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.01, 0.61], [1.08, 0.42], [0.54, 0], [0, 0.42], [0.07, 0.62], [1.01, 0.61]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.01, 0.61], [1.08, 0.42], [0.54, 0], [0, 0.42], [0.07, 0.62], [1.01, 0.61]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.01, 0.61], [1.08, 0.42], [0.54, 0], [0, 0.42], [0.07, 0.62], [1.01, 0.61]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.01, 0.61], [1.08, 0.42], [0.54, 0], [0, 0.42], [0.07, 0.62], [1.01, 0.61]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.01, 0.61], [1.08, 0.42], [0.54, 0], [0, 0.42], [0.07, 0.62], [1.01, 0.61]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.01, 0.61], [1.08, 0.42], [0.54, 0], [0, 0.42], [0.07, 0.62], [1.01, 0.61]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 140}, {"s": [0.8471, 0.8706, 0.9098], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 7}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.55, 0.31], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.55, 0.31], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.55, 0.31], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.55, 0.31], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.55, 0.31], "t": 140}, {"s": [0.55, 0.31], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.83, 7.44], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.83, 7.44], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.52, 7.44], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.52, 7.44], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.83, 7.44], "t": 140}, {"s": [8.83, 7.44], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.03, 0.62], [1.09, 0.42], [0.55, 0], [0, 0.42], [0.07, 0.62], [1.03, 0.62]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.03, 0.62], [1.09, 0.42], [0.55, 0], [0, 0.42], [0.07, 0.62], [1.03, 0.62]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.03, 0.62], [1.09, 0.42], [0.55, 0], [0, 0.42], [0.07, 0.62], [1.03, 0.62]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.03, 0.62], [1.09, 0.42], [0.55, 0], [0, 0.42], [0.07, 0.62], [1.03, 0.62]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.03, 0.62], [1.09, 0.42], [0.55, 0], [0, 0.42], [0.07, 0.62], [1.03, 0.62]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0.07], [0.3, 0], [0, -0.23], [-0.04, -0.06], [0, 0]], "o": [[0.04, -0.06], [0, -0.23], [-0.3, 0], [0, 0.07], [0, 0], [0, 0]], "v": [[1.03, 0.62], [1.09, 0.42], [0.55, 0], [0, 0.42], [0.07, 0.62], [1.03, 0.62]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 140}, {"s": [0.8471, 0.8706, 0.9098], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 8}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.2, 1.07], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.2, 1.07], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.2, 1.07], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.2, 1.07], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.2, 1.07], "t": 140}, {"s": [3.2, 1.07], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.18, 7.63], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.18, 7.63], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.86, 7.63], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.86, 7.63], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.18, 7.63], "t": 140}, {"s": [10.18, 7.63], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.59], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.57, 0]], "o": [[0, 0], [-0.59, 0], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.58], [0, 0]], "v": [[5.36, 2.12], [1.06, 2.14], [0, 1.07], [0, 0.68], [0.68, 0], [5.74, 0.01], [6.39, 0.68], [6.39, 1.06], [5.36, 2.12]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.59], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.57, 0]], "o": [[0, 0], [-0.59, 0], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.58], [0, 0]], "v": [[5.36, 2.12], [1.06, 2.14], [0, 1.07], [0, 0.68], [0.68, 0], [5.74, 0.01], [6.39, 0.68], [6.39, 1.06], [5.36, 2.12]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.59], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.57, 0]], "o": [[0, 0], [-0.59, 0], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.58], [0, 0]], "v": [[5.36, 2.12], [1.06, 2.14], [0, 1.07], [0, 0.68], [0.68, 0], [5.74, 0.01], [6.39, 0.68], [6.39, 1.06], [5.36, 2.12]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.59], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.57, 0]], "o": [[0, 0], [-0.59, 0], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.58], [0, 0]], "v": [[5.36, 2.12], [1.06, 2.14], [0, 1.07], [0, 0.68], [0.68, 0], [5.74, 0.01], [6.39, 0.68], [6.39, 1.06], [5.36, 2.12]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.59], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.57, 0]], "o": [[0, 0], [-0.59, 0], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.58], [0, 0]], "v": [[5.36, 2.12], [1.06, 2.14], [0, 1.07], [0, 0.68], [0.68, 0], [5.74, 0.01], [6.39, 0.68], [6.39, 1.06], [5.36, 2.12]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.59], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.57, 0]], "o": [[0, 0], [-0.59, 0], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.58], [0, 0]], "v": [[5.36, 2.12], [1.06, 2.14], [0, 1.07], [0, 0.68], [0.68, 0], [5.74, 0.01], [6.39, 0.68], [6.39, 1.06], [5.36, 2.12]]}], "t": 185}]}}, {"ty": "gf", "bm": 0, "hd": false, "nm": "", "e": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [6.39015007019043, 1.0705900192260742], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [6.39015007019043, 1.0705900192260742], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [6.39015007019043, 1.0705900192260742], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [6.39015007019043, 1.0705900192260742], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [6.39015007019043, 1.0705900192260742], "t": 140}, {"s": [6.39015007019043, 1.0705900192260742], "t": 185}]}, "g": {"p": 2, "k": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 140}, {"s": [0, 0.26670589626069163, 0.29415687501664256, 0.5490588504099378, 1, 0.14905882954831215, 0.14905882954831215, 0.30984314859147166], "t": 185}]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.00020229400251992047, 1.0705900192260742], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.00020229400251992047, 1.0705900192260742], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.00020229400251992047, 1.0705900192260742], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.00020229400251992047, 1.0705900192260742], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.00020229400251992047, 1.0705900192260742], "t": 140}, {"s": [0.00020229400251992047, 1.0705900192260742], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 9}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.72, 1.46], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.72, 1.46], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.72, 1.46], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.72, 1.46], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.72, 1.46], "t": 140}, {"s": [1.72, 1.46], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.15, 8.65], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.15, 8.65], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.83, 8.65], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.83, 8.65], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.15, 8.65], "t": 140}, {"s": [10.15, 8.65], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.81], [-0.8, 0], [0, 0], [0, -0.8], [0.79, -0.01]], "o": [[0, 0], [-0.8, 0.01], [0, -0.81], [0, 0], [0.79, 0], [0, 0.8], [0, 0]], "v": [[2.02, 2.91], [1.45, 2.92], [0, 1.47], [1.45, 0], [2.02, 0], [3.45, 1.45], [2.02, 2.91]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.81], [-0.8, 0], [0, 0], [0, -0.8], [0.79, -0.01]], "o": [[0, 0], [-0.8, 0.01], [0, -0.81], [0, 0], [0.79, 0], [0, 0.8], [0, 0]], "v": [[2.02, 2.91], [1.45, 2.92], [0, 1.47], [1.45, 0], [2.02, 0], [3.45, 1.45], [2.02, 2.91]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.81], [-0.8, 0], [0, 0], [0, -0.8], [0.79, -0.01]], "o": [[0, 0], [-0.8, 0.01], [0, -0.81], [0, 0], [0.79, 0], [0, 0.8], [0, 0]], "v": [[2.02, 2.91], [1.45, 2.92], [0, 1.47], [1.45, 0], [2.02, 0], [3.45, 1.45], [2.02, 2.91]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.81], [-0.8, 0], [0, 0], [0, -0.8], [0.79, -0.01]], "o": [[0, 0], [-0.8, 0.01], [0, -0.81], [0, 0], [0.79, 0], [0, 0.8], [0, 0]], "v": [[2.02, 2.91], [1.45, 2.92], [0, 1.47], [1.45, 0], [2.02, 0], [3.45, 1.45], [2.02, 2.91]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.81], [-0.8, 0], [0, 0], [0, -0.8], [0.79, -0.01]], "o": [[0, 0], [-0.8, 0.01], [0, -0.81], [0, 0], [0.79, 0], [0, 0.8], [0, 0]], "v": [[2.02, 2.91], [1.45, 2.92], [0, 1.47], [1.45, 0], [2.02, 0], [3.45, 1.45], [2.02, 2.91]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.81], [-0.8, 0], [0, 0], [0, -0.8], [0.79, -0.01]], "o": [[0, 0], [-0.8, 0.01], [0, -0.81], [0, 0], [0.79, 0], [0, 0.8], [0, 0]], "v": [[2.02, 2.91], [1.45, 2.92], [0, 1.47], [1.45, 0], [2.02, 0], [3.45, 1.45], [2.02, 2.91]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 140}, {"s": [0.8471, 0.8706, 0.9098], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 10}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.49, 1.53], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.49, 1.53], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.49, 1.53], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.49, 1.53], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3.49, 1.53], "t": 140}, {"s": [3.49, 1.53], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.19, 7.85], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.19, 7.85], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.88, 7.85], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.88, 7.85], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.19, 7.85], "t": 140}, {"s": [10.19, 7.85], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.83], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.8, -0.01]], "o": [[0, 0], [-0.83, 0.01], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.82], [0, 0]], "v": [[5.53, 3.04], [1.5, 3.06], [0, 1.56], [0, 0.68], [0.68, 0], [6.33, 0.01], [6.98, 0.68], [6.98, 1.55], [5.53, 3.04]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.83], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.8, -0.01]], "o": [[0, 0], [-0.83, 0.01], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.82], [0, 0]], "v": [[5.53, 3.04], [1.5, 3.06], [0, 1.56], [0, 0.68], [0.68, 0], [6.33, 0.01], [6.98, 0.68], [6.98, 1.55], [5.53, 3.04]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.83], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.8, -0.01]], "o": [[0, 0], [-0.83, 0.01], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.82], [0, 0]], "v": [[5.53, 3.04], [1.5, 3.06], [0, 1.56], [0, 0.68], [0.68, 0], [6.33, 0.01], [6.98, 0.68], [6.98, 1.55], [5.53, 3.04]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.83], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.8, -0.01]], "o": [[0, 0], [-0.83, 0.01], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.82], [0, 0]], "v": [[5.53, 3.04], [1.5, 3.06], [0, 1.56], [0, 0.68], [0.68, 0], [6.33, 0.01], [6.98, 0.68], [6.98, 1.55], [5.53, 3.04]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.83], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.8, -0.01]], "o": [[0, 0], [-0.83, 0.01], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.82], [0, 0]], "v": [[5.53, 3.04], [1.5, 3.06], [0, 1.56], [0, 0.68], [0.68, 0], [6.33, 0.01], [6.98, 0.68], [6.98, 1.55], [5.53, 3.04]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0.83], [0, 0], [-0.37, 0], [0, 0], [0, -0.37], [0, 0], [0.8, -0.01]], "o": [[0, 0], [-0.83, 0.01], [0, 0], [0, -0.38], [0, 0], [0.36, 0], [0, 0], [0, 0.82], [0, 0]], "v": [[5.53, 3.04], [1.5, 3.06], [0, 1.56], [0, 0.68], [0.68, 0], [6.33, 0.01], [6.98, 0.68], [6.98, 1.55], [5.53, 3.04]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.8471, 0.8706, 0.9098], "t": 140}, {"s": [0.8471, 0.8706, 0.9098], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 11}, {"ty": 4, "nm": "Group 1244832884 / Vector", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [4.48, 3.49], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [4.48, 3.49], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [4.48, 3.49], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [4.48, 3.49], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [4.48, 3.49], "t": 140}, {"s": [4.48, 3.49], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.1, 7.49], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.1, 7.49], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.78, 7.49], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [9.78, 7.49], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10.1, 7.49], "t": 140}, {"s": [10.1, 7.49], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.15, -0.35], [1.24, -0.38], [0.53, -0.01], [0.61, 1.4], [-0.25, 0.68], [-1.53, 0.31], [-0.4, 0], [-0.52, -1.45], [0, -0.32]], "o": [[0, 0.38], [-0.42, 0.98], [-0.51, 0.16], [-1.99, 0.02], [-0.29, -0.66], [0.43, -1.18], [0.4, -0.08], [2.04, 0.02], [0.11, 0.3], [0, 0]], "v": [[8.97, 3.48], [8.74, 4.58], [6.12, 6.74], [4.55, 6.98], [0.24, 4.62], [0.17, 2.54], [3.34, 0.12], [4.55, 0], [8.8, 2.55], [8.97, 3.48]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.15, -0.35], [1.24, -0.38], [0.53, -0.01], [0.61, 1.4], [-0.25, 0.68], [-1.53, 0.31], [-0.4, 0], [-0.52, -1.45], [0, -0.32]], "o": [[0, 0.38], [-0.42, 0.98], [-0.51, 0.16], [-1.99, 0.02], [-0.29, -0.66], [0.43, -1.18], [0.4, -0.08], [2.04, 0.02], [0.11, 0.3], [0, 0]], "v": [[8.97, 3.48], [8.74, 4.58], [6.12, 6.74], [4.55, 6.98], [0.24, 4.62], [0.17, 2.54], [3.34, 0.12], [4.55, 0], [8.8, 2.55], [8.97, 3.48]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.15, -0.35], [1.24, -0.38], [0.53, -0.01], [0.61, 1.4], [-0.25, 0.68], [-1.53, 0.31], [-0.4, 0], [-0.52, -1.45], [0, -0.32]], "o": [[0, 0.38], [-0.42, 0.98], [-0.51, 0.16], [-1.99, 0.02], [-0.29, -0.66], [0.43, -1.18], [0.4, -0.08], [2.04, 0.02], [0.11, 0.3], [0, 0]], "v": [[8.97, 3.48], [8.74, 4.58], [6.12, 6.74], [4.55, 6.98], [0.24, 4.62], [0.17, 2.54], [3.34, 0.12], [4.55, 0], [8.8, 2.55], [8.97, 3.48]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.15, -0.35], [1.24, -0.38], [0.53, -0.01], [0.61, 1.4], [-0.25, 0.68], [-1.53, 0.31], [-0.4, 0], [-0.52, -1.45], [0, -0.32]], "o": [[0, 0.38], [-0.42, 0.98], [-0.51, 0.16], [-1.99, 0.02], [-0.29, -0.66], [0.43, -1.18], [0.4, -0.08], [2.04, 0.02], [0.11, 0.3], [0, 0]], "v": [[8.97, 3.48], [8.74, 4.58], [6.12, 6.74], [4.55, 6.98], [0.24, 4.62], [0.17, 2.54], [3.34, 0.12], [4.55, 0], [8.8, 2.55], [8.97, 3.48]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0.15, -0.35], [1.24, -0.38], [0.53, -0.01], [0.61, 1.4], [-0.25, 0.68], [-1.53, 0.31], [-0.4, 0], [-0.52, -1.45], [0, -0.32]], "o": [[0, 0.38], [-0.42, 0.98], [-0.51, 0.16], [-1.99, 0.02], [-0.29, -0.66], [0.43, -1.18], [0.4, -0.08], [2.04, 0.02], [0.11, 0.3], [0, 0]], "v": [[8.97, 3.48], [8.74, 4.58], [6.12, 6.74], [4.55, 6.98], [0.24, 4.62], [0.17, 2.54], [3.34, 0.12], [4.55, 0], [8.8, 2.55], [8.97, 3.48]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0.15, -0.35], [1.24, -0.38], [0.53, -0.01], [0.61, 1.4], [-0.25, 0.68], [-1.53, 0.31], [-0.4, 0], [-0.52, -1.45], [0, -0.32]], "o": [[0, 0.38], [-0.42, 0.98], [-0.51, 0.16], [-1.99, 0.02], [-0.29, -0.66], [0.43, -1.18], [0.4, -0.08], [2.04, 0.02], [0.11, 0.3], [0, 0]], "v": [[8.97, 3.48], [8.74, 4.58], [6.12, 6.74], [4.55, 6.98], [0.24, 4.62], [0.17, 2.54], [3.34, 0.12], [4.55, 0], [8.8, 2.55], [8.97, 3.48]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 140}, {"s": [1, 1, 1], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 12}, {"ty": 4, "nm": "Rectangle 18350", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8, 8], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8, 8], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8, 8], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8, 8], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8, 8], "t": 140}, {"s": [8, 8], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10, 10], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10, 10], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10, 10], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10, 10], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [10, 10], "t": 140}, {"s": [10, 10], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [9.37, 0], [16, 6.63], [16, 9.37], [9.37, 16], [6.63, 16], [0, 9.37]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [9.37, 0], [16, 6.63], [16, 9.37], [9.37, 16], [6.63, 16], [0, 9.37]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [9.37, 0], [16, 6.63], [16, 9.37], [9.37, 16], [6.63, 16], [0, 9.37]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [9.37, 0], [16, 6.63], [16, 9.37], [9.37, 16], [6.63, 16], [0, 9.37]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [9.37, 0], [16, 6.63], [16, 9.37], [9.37, 16], [6.63, 16], [0, 9.37]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [9.37, 0], [16, 6.63], [16, 9.37], [9.37, 16], [6.63, 16], [0, 9.37]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 140}, {"s": [0.5922, 0.4745, 0.9412], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 13}, {"ty": 4, "nm": "Group 1244832885 / Rectangle 18349", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17, 1], "t": 140}, {"s": [17, 1], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-61, 12], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-61, 12], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [44, 12], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [44, 12], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-61, 12], "t": 140}, {"s": [-61, 12], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [33.45, 0], [34, 0.55], [34, 1.45], [33.45, 2], [0.55, 2], [0, 1.45]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [33.45, 0], [34, 0.55], [34, 1.45], [33.45, 2], [0.55, 2], [0, 1.45]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [33.45, 0], [34, 0.55], [34, 1.45], [33.45, 2], [0.55, 2], [0, 1.45]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [33.45, 0], [34, 0.55], [34, 1.45], [33.45, 2], [0.55, 2], [0, 1.45]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [33.45, 0], [34, 0.55], [34, 1.45], [33.45, 2], [0.55, 2], [0, 1.45]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [33.45, 0], [34, 0.55], [34, 1.45], [33.45, 2], [0.55, 2], [0, 1.45]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 140}, {"s": [0.847, 0.847, 0.847], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 14}, {"ty": 4, "nm": "Group 1244832885 / Rectangle 18348", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [23.5, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [23.5, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [23.5, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [23.5, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [23.5, 1], "t": 140}, {"s": [23.5, 1], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-54.5, 8], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-54.5, 8], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [50.5, 8], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [50.5, 8], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-54.5, 8], "t": 140}, {"s": [-54.5, 8], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [46.45, 0], [47, 0.55], [47, 1.45], [46.45, 2], [0.55, 2], [0, 1.45]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [46.45, 0], [47, 0.55], [47, 1.45], [46.45, 2], [0.55, 2], [0, 1.45]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [46.45, 0], [47, 0.55], [47, 1.45], [46.45, 2], [0.55, 2], [0, 1.45]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [46.45, 0], [47, 0.55], [47, 1.45], [46.45, 2], [0.55, 2], [0, 1.45]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [46.45, 0], [47, 0.55], [47, 1.45], [46.45, 2], [0.55, 2], [0, 1.45]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3]], "o": [[0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0]], "v": [[0, 0.55], [0.55, 0], [46.45, 0], [47, 0.55], [47, 1.45], [46.45, 2], [0.55, 2], [0, 1.45]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.847, 0.847, 0.847], "t": 140}, {"s": [0.847, 0.847, 0.847], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 15}, {"ty": 4, "nm": "Group 1244832885 / Rectangle 18347", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 6], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 6], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [28.5, 6], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [28.5, 6], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [29, 6], "t": 140}, {"s": [29, 6], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-55, 10], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-55, 10], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [50.5, 10], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [50.5, 10], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-55, 10], "t": 140}, {"s": [-55, 10], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1.22, 0], [0, 0], [0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22]], "o": [[0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22], [0, 0], [-1.22, 0], [0, 0]], "v": [[0, 2.21], [2.21, 0], [55.79, 0], [58, 2.21], [58, 9.79], [55.79, 12], [2.21, 12], [0, 9.79]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1.22, 0], [0, 0], [0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22]], "o": [[0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22], [0, 0], [-1.22, 0], [0, 0]], "v": [[0, 2.21], [2.21, 0], [55.79, 0], [58, 2.21], [58, 9.79], [55.79, 12], [2.21, 12], [0, 9.79]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1.22, 0], [0, 0], [0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22]], "o": [[0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22], [0, 0], [-1.22, 0], [0, 0]], "v": [[0, 2.21], [2.21, 0], [54.79, 0], [57, 2.21], [57, 9.79], [54.79, 12], [2.21, 12], [0, 9.79]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1.22, 0], [0, 0], [0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22]], "o": [[0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22], [0, 0], [-1.22, 0], [0, 0]], "v": [[0, 2.21], [2.21, 0], [54.79, 0], [57, 2.21], [57, 9.79], [54.79, 12], [2.21, 12], [0, 9.79]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-1.22, 0], [0, 0], [0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22]], "o": [[0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22], [0, 0], [-1.22, 0], [0, 0]], "v": [[0, 2.21], [2.21, 0], [55.79, 0], [58, 2.21], [58, 9.79], [55.79, 12], [2.21, 12], [0, 9.79]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-1.22, 0], [0, 0], [0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22]], "o": [[0, -1.22], [0, 0], [1.22, 0], [0, 0], [0, 1.22], [0, 0], [-1.22, 0], [0, 0]], "v": [[0, 2.21], [2.21, 0], [55.79, 0], [58, 2.21], [58, 9.79], [55.79, 12], [2.21, 12], [0, 9.79]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 140}, {"s": [1, 1, 1], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 16}, {"ty": 4, "nm": "Group 1244832885 / Vector 3151", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3, 3.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3, 3.5], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3, 3.5], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3, 3.5], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [3, 3.5], "t": 140}, {"s": [3, 3.5], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-85, 10.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-85, 10.5], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [21, 10.5], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [21, 10.5], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-85, 10.5], "t": 140}, {"s": [-85, 10.5], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6, 0], [0, 0], [6, 7], [6, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6, 0], [0, 0], [6, 7], [6, 0]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6, 0], [0, 0], [6, 7], [6, 0]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6, 0], [0, 0], [6, 7], [6, 0]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6, 0], [0, 0], [6, 7], [6, 0]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6, 0], [0, 0], [6, 7], [6, 0]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 140}, {"s": [1, 1, 1], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 17}, {"ty": 4, "nm": "Group 1244832885 / Rectangle 18350", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [46.5, 8], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [46.5, 8], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [40, 8], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [40, 8], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [46.5, 8], "t": 140}, {"s": [46.5, 8], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-50.5, 10], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-50.5, 10], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [-50.5, 10], "t": 140}, {"s": [-50.5, 10], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-2.74, 0], [0, 0], [0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74]], "o": [[0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74], [0, 0], [-2.74, 0], [0, 0]], "v": [[0, 4.97], [4.97, 0], [88.03, 0], [93, 4.97], [93, 11.03], [88.03, 16], [4.97, 16], [0, 11.03]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-2.74, 0], [0, 0], [0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74]], "o": [[0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74], [0, 0], [-2.74, 0], [0, 0]], "v": [[0, 4.97], [4.97, 0], [88.03, 0], [93, 4.97], [93, 11.03], [88.03, 16], [4.97, 16], [0, 11.03]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-2.74, 0], [0, 0], [0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74]], "o": [[0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74], [0, 0], [-2.74, 0], [0, 0]], "v": [[0, 4.97], [4.97, 0], [75.03, 0], [80, 4.97], [80, 11.03], [75.03, 16], [4.97, 16], [0, 11.03]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-2.74, 0], [0, 0], [0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74]], "o": [[0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74], [0, 0], [-2.74, 0], [0, 0]], "v": [[0, 4.97], [4.97, 0], [75.03, 0], [80, 4.97], [80, 11.03], [75.03, 16], [4.97, 16], [0, 11.03]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-2.74, 0], [0, 0], [0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74]], "o": [[0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74], [0, 0], [-2.74, 0], [0, 0]], "v": [[0, 4.97], [4.97, 0], [88.03, 0], [93, 4.97], [93, 11.03], [88.03, 16], [4.97, 16], [0, 11.03]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-2.74, 0], [0, 0], [0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74]], "o": [[0, -2.74], [0, 0], [2.74, 0], [0, 0], [0, 2.74], [0, 0], [-2.74, 0], [0, 0]], "v": [[0, 4.97], [4.97, 0], [88.03, 0], [93, 4.97], [93, 11.03], [88.03, 16], [4.97, 16], [0, 11.03]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.5922, 0.4745, 0.9412], "t": 140}, {"s": [0.5922, 0.4745, 0.9412], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 18}, {"ty": 4, "nm": "Rectangle 18351", "sr": 1, "st": 0, "op": 186, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 140}, {"s": [42, 10], "t": 185}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 140}, {"s": [100, 100], "t": 185}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [42, 10], "t": 140}, {"s": [42, 10], "t": 185}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 140}, {"s": [0], "t": 185}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [77.37, 0], [84, 6.63], [84, 13.37], [77.37, 20], [6.63, 20], [0, 13.37]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [77.37, 0], [84, 6.63], [84, 13.37], [77.37, 20], [6.63, 20], [0, 13.37]]}], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [77.37, 0], [84, 6.63], [84, 13.37], [77.37, 20], [6.63, 20], [0, 13.37]]}], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [77.37, 0], [84, 6.63], [84, 13.37], [77.37, 20], [6.63, 20], [0, 13.37]]}], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [77.37, 0], [84, 6.63], [84, 13.37], [77.37, 20], [6.63, 20], [0, 13.37]]}], "t": 140}, {"s": [{"c": true, "i": [[0, 0], [-3.66, 0], [0, 0], [0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66]], "o": [[0, -3.66], [0, 0], [3.66, 0], [0, 0], [0, 3.66], [0, 0], [-3.66, 0], [0, 0]], "v": [[0, 6.63], [6.63, 0], [77.37, 0], [84, 6.63], [84, 13.37], [77.37, 20], [6.63, 20], [0, 13.37]]}], "t": 185}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 140}, {"s": [1, 1, 1], "t": 185}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 45}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 70}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 115}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 140}, {"s": [100], "t": 185}]}}], "ind": 19}], "v": "5.7.0", "fr": 30, "op": 185.00491619110107, "ip": 0, "assets": []}