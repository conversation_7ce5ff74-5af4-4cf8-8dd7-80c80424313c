import React, { useState, useEffect } from 'react';
import LottieView from 'lottie-react-native';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import useConfigStore from '../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys, labels } from '../../../configStore/Common/constants';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import {
  getBusRailsPremiumConfig,
  getRailsMealsRISPokus,
  showNewRailsBusCommonLanding,
} from 'apps/rails/src/RailsAbConfig';
import { isPremiumUser as checkPremiumUser } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {
  getBookingLOBS,
  getAdsAb,
  onHardBackPress,
  getRisOptions,
} from 'apps/rails/src/pages/RailsBusHomePage/Common/utils';
import { RailsBusPremiumLanding } from '../Components/RailsBusPremiumLanding';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {
  TouchableOpacity,
  BackHandler,
  FlatList,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Dimensions,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { SEARCH_CONTEXT } from '../../../RailsConfig';
import { removeDataFromStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { getTGSCarouselTag, getRisIconPaletteVersion,getRisIconGPTConfig } from '../../../RailsAbConfig';
import { onPnrStatusClicked } from '../Common/utils';
import Carousel from 'react-native-looped-carousel';
import { connect } from 'react-redux';
import { Actions } from '../../../navigation';
import styles from '../Styles/commonCss';
import RailsNewLandingHeader from '../../Common/RailsNewLandingHeader';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import { trackPageVisits, trackCovidInfoClicked, trackClickEventProp61} from '../Analytics';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  getDataFromAsynStorage,
  RAILS_BUS_COMMON_LANDING,
  removeDataFromAsyncStorage,
  RAILS_CONFIG_DATA,
  removeFCKey,
  RIS_ON_CLICK_GPT_BANNER,
  RIS_ON_CLICK_GPT_LOTTIE,
  NEW_LANDING_OMINITURE_TRUE,
  NEW_LANDING_OMINITURE_FALSE,
  SCREENER_LOADING,
  SCREENER_INTERACTIVE,
  RIS_DETAILS_MEALS_ORDER_NOW_CLICKED,
} from '../../../Utils/RailsConstant';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import {
  TRAIN_BUS_COMMON_LANDING,
  TRAIN_BUS_COMMON_LANDING_SNACKBAR,
  TRAIN_BUS_COMMON_LANDING_ADFEED2,
  TRAIN_BUS_COMMON_LANDING_ADFEED1,
} from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import CovidInfoCard from '@mmt/legacy-commons/Common/Components/CovidInfoCard';
import { isPhonePePlatform } from '../../../Utils/RisUtils';
import { RisOptions, LtsCard ,RISGpt} from '../Components';
import { constants } from '../Common';
import { omnitureKeys } from '../../RisLiveTrainStatusForm/Common/index';
import { RailsBusCommonLandingProps } from '../Interfaces';
import { useFocusEffect } from '@mmt/navigation';
import { getAllTypesAdsAb } from '../../Common/utils';
import { trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { logCommonLandingPageLoad } from '../../../PdtAnalytics/PdtAnalyticsV2/PdtRailsCommonLanding';
import blueArrow from '@mmt/legacy-assets/src/RightArrowBlue.webp';
import newBlueArrow from '@mmt/legacy-assets/src/arrow-right-new.webp';

import {
  fetchSectionOrder,
  upComingPnrs,
} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import { getPokusConfigWaitingPromise } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { _label } from '../../../vernacular/AppLanguage';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import RisIconPalette from '../Components/RisIconPalette/RisIconPalette';
import TgsCard from '../Components/TgsCard/TgsCard';
import { isEmpty } from 'lodash';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { PDT_JOURNEY_ID_KEY } from '../../../PdtAnalytics/PdtAnalyticsV2/CommonPdtConstants';
import {
  removeEventFromEvar47or97Variable,
  trackGenericEvar47or97Event,
  updateEvar47or97Variable,
} from 'apps/rails/src/railsAnalytics';
import SponsoredAds from 'apps/rails/src/configStore/Landing/SponsoredAds';
import RailsMealEntryPoint from '../../RIS/MealsOnTrain/Components/RailsMealEntryPoint';
import MealTgTogether from '../../RIS/MealTgTogether/MealTgTogether';
import { trackMealsOmnitureLoadEvent } from 'apps/rails/src/RisAnalytics/RisAnalytics';
import NewBookingCard from '../Components/NewBookingCard/NewBookingCard';
import NewTile from '../Components/NewTile/NewTile';
import TgsCardNewLanding from '../Components/TgsCardNewLanding/TgsCardNewLanding';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';

const tgsBanner = ASSETS.landingTgBannner;
import gptRisLottie from 'apps/rails/src/pages/RailsBusHomePage/Pages/railGPT.json';
import newTrainIcon from '@mmt/legacy-assets/src/rails/Train-new.webp';
import newBusIcon from '@mmt/legacy-assets/src/rails/Bus.webp';
interface TextContent {
  textString1: string;
  textString2: string;
}
interface RailsGPT {
  url?: string;
  headerText?: string;
  textContent?: TextContent
}

const RailsBusCommonLanding = (props: RailsBusCommonLandingProps) => {

  const [showAds, setShowAds] = useState(false);
  const [isPremiumUser, setIsPremiumUser] = useState(false);
  const [isNewLanding, setIsNewLanding] = useState(false);
  const [loader, setLoader] = useState(false);
  const [risGptConfig, setRISGptConfig] = useState(0);
  const [showDiffTypeOfAds, setShowDiffTypeOfAds] = useState({
    multi_banner: 0,
    snackbar: 0,
    adfeed: 0,
    interstitial: 0,
  });
  const [railsMealPokus, setRailsMealPokus] = useState(false);
  const [railsBusCommonLanding, setRailsBusCommonLanding] = useState(props.railsBusCommonLanding);
  const bookingLOBSMap = getBookingLOBS(railsBusCommonLanding || false);
  const risDataMap = getRisOptions(props.risOptions, railsBusCommonLanding || false);
  const newRisDataMap = getRisOptions(props.risOptions, railsBusCommonLanding || false, true);
  const showAd = railsBusCommonLanding && showAds;
  const [isFocused, setIsFocused] = useState(false);
  const screenWidth = Dimensions.get('window').width;
  const newCardWidth = screenWidth * 0.43;

  //Reason to ignore : We have a return statement at the top, ideally all hooks should be
  //declared before any return, right now not modifying exsting code.

  const railsRISGptJSON: RailsGPT = useConfigStore(configKeys.RAILS_RIS_GPT);


  const { updateState } = useScreenProfiler();

  const [showSponsoredAd1, setShowSponsoredAd1] = useState(false);
  const [showSponsoredAd2, setShowSponsoredAd2] = useState(false);
  const [showSponsoredAd3, setShowSponsoredAd3] = useState(false);

  const handleSponsoredLabel = (val: boolean, adNumber: number) => {
    switch (adNumber) {
      case 1:
        setShowSponsoredAd1(val);
        break;
      case 2:
        setShowSponsoredAd2(val);
        break;
      case 3:
        setShowSponsoredAd3(val);
        break;
    }
  };

  const { status } = props;
  const { data } = constants;
  // const { getBookingLOBS, getAdsAb, onHardBackPress, getRisOptions } = utils;

  /* for PIP had to add custom back behavior. If there is a navigation back stack then it will just fallback
   * to home screen. else it will relaunch the application (required if PIP mode is switched).
   * TODO: Avoid splash screen when relaunched.
   */
  const onHardBackPressed = () => {
    if (Platform.OS === 'android') {
      RailsModule.onRailsBusCommonBackPressed();
    } else {
      onHardBackPress();
    }
  };

  useEffect(() => {
    if (Platform.OS === 'android') {
      GenericModule?.showFloatingNotification?.(data?.PAGE_NAME);
    }
  }, [data?.PAGE_NAME]);

  useEffect(() => {
    async function setPremiumUser() {
      try {
        setLoader(true);
        await getPokusConfigWaitingPromise(1000);
        removeEventFromEvar47or97Variable('p1');
        removeEventFromEvar47or97Variable('p2');
        const railsBusPremiumPokus = await getBusRailsPremiumConfig();
        const isPremiumUser = (await checkPremiumUser(null)) ?? false;
        if (railsBusPremiumPokus === 1) {
          updateEvar47or97Variable('p1');
        } else {
          updateEvar47or97Variable('p2');
        }
        setIsPremiumUser(isPremiumUser && railsBusPremiumPokus === 2);
        setLoader(false);
      } catch (e) {
        setLoader(false);
        console.log('failed to fetch premium user', e);
      }
    }
    setPremiumUser();
  }, []);


  useEffect(() => {
    const setABData = async () => {
      await getPokusConfigWaitingPromise(1000);
      await removeFCKey();
      await logCommonLandingPageLoad();
      const showAds = Platform.OS !== 'web' ? await getAdsAb() : false;
      const AdsAb = await getAllTypesAdsAb();
      setShowDiffTypeOfAds(AdsAb);
      const railsBusCommonLanding = await getDataFromAsynStorage(RAILS_BUS_COMMON_LANDING);
      const railsBusPremiumPokus = await getBusRailsPremiumConfig();
      const isPremiumUser = (await checkPremiumUser(null)) ?? false;
      if (!isPremiumUser && railsBusPremiumPokus === 1) {
        trackAdLoad('mob:funnel:railsris:railsrislanding', AdsAb.trackingPayload);
        trackPageVisits(data.PAGE_NAME, {});
      }
      setShowAds(showAds);
      if (railsBusCommonLanding) {
        setRailsBusCommonLanding(railsBusCommonLanding);
      }
    };
    setABData();
   const risGptConfigVal =  getRisIconGPTConfig();
   setRISGptConfig(risGptConfigVal);
    props.fetchSections();
    removeDataFromAsyncStorage(RAILS_CONFIG_DATA);
    removeDataFromAsyncStorage(PDT_JOURNEY_ID_KEY);
  }, []);



  useFocusEffect(
    React.useCallback(() => {
      removeDataFromStorage(SEARCH_CONTEXT);
      BackHandler.addEventListener('hardwareBackPress', onHardBackPressed);
      setIsFocused(true);

      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onHardBackPressed);
        setIsFocused(false);
      };
    }, []),
  );

  useEffect(() => {
    const pokusCall = async () => {
      await getPokusConfigWaitingPromise(1000);
      const railsMealPokusValue = await getRailsMealsRISPokus();
      const isNewCommonLandingPokusValue = await showNewRailsBusCommonLanding();
      if (isNewCommonLandingPokusValue) {
        setIsNewLanding(isNewCommonLandingPokusValue);
        trackGenericEvar47or97Event(data.PAGE_NAME, NEW_LANDING_OMINITURE_TRUE);
      } else {
        trackGenericEvar47or97Event(data.PAGE_NAME, NEW_LANDING_OMINITURE_FALSE);
      }
      if (railsMealPokusValue) {
        props.getUpComingPnrs();
      }
      setRailsMealPokus(railsMealPokusValue);
      if (railsMealPokusValue) {
        trackMealsOmnitureLoadEvent('ris_details_meals_shown');
      }
    };
    pokusCall();
  }, []);

  const showGPTLottie = risGptConfig === 1 && railsRISGptJSON && !isEmpty(railsRISGptJSON);
  const showGPTBanner = risGptConfig === 2 && railsRISGptJSON && !isEmpty(railsRISGptJSON);

  const bookingCard = (config: { label: string; icon: unknown; onClick: () => void }) => (
    <Card>
      <TouchableRipple onPress={config.onClick}>
        <View style={[styles.bookingContainer, { height: 45 }]}>
          <Image source={config.icon} style={{ height: 32, width: 32 }} />
          <Text style={[styles.header, fontStyle('bold'), { marginLeft: 15 }]}>
            {_label(config.label)}
          </Text>
          <Image style={{ width: 16, height: 12 }} source={blueArrow} resizeMode="contain" />
        </View>
      </TouchableRipple>
    </Card>
  );

  const risOptionView = (topMargin: number) => {
    /* Pokus Controlled */

    let risHeaderIcons;
    let risIconPaletteIcons;

    const risIconPaletteVersion = getRisIconPaletteVersion();

    if (risIconPaletteVersion?.toString() === '1') {
      risHeaderIcons = risDataMap;
    } else if (risIconPaletteVersion?.toString() === '2') {
      risHeaderIcons = risDataMap.slice(0, 2);
      risIconPaletteIcons = risDataMap.slice(2);
    }

    const isRisPage = props?.isRIS?.toLowerCase() === 'true';

    const openGPTView = () => {
      if (showGPTBanner) {
        trackClickEventProp61(data.PAGE_NAME, RIS_ON_CLICK_GPT_BANNER);
      }
      else {
        trackClickEventProp61(data.PAGE_NAME, RIS_ON_CLICK_GPT_LOTTIE);
      }
      Actions.openGPTWebView({
        url: railsRISGptJSON?.url,
        headerText: railsRISGptJSON?.headerText,
        headerIcon: backIcon,
        sharedCookiesEnabled:true,
      });
    };

    if (status === 'NOT_DONE') {
      return (
        <View style={{ flex: 1, justifyContent: 'center' }} testID="ris_options_list_loading">
          <Spinner size={30} color="#008b8b" />
        </View>
      );
    }

    return (
      <>
        <View style={[styles.form, { marginTop: topMargin }]} testID="ris_options_list_container">
          <View style={styles.lottieContainer} testID="ris_options_list_heading_container">
            <Text
              style={[styles.headingStyle, fontStyle('regular')]}
              testID="ris_options_list_heading_text"
            >
              {_label(data.RIS_TITLE)}
            </Text>
            {showGPTLottie && (
              <TouchableRipple onPress={openGPTView} testID="ris_options_list_heading_container">
                <LottieView
                  style={styles.lottieView}
                  source={gptRisLottie}
                  autoPlay={true}
                  loop={false}
                />
              </TouchableRipple>
            )}
          </View>
          <FlatList
            data={risHeaderIcons}
            numColumns={2}
            contentContainerStyle={{ paddingHorizontal: 11 }}
            renderItem={({ item, index }) => (
              <RisOptions
                icon={item.icon}
                iconText={item.text}
                onClick={() => {
                  item.onClick(isRisPage);
                }}
                key={index}
              />
            )}
            testID="ris_options_list_list"
          />
          {showGPTBanner && (
            <RISGpt textContent={railsRISGptJSON?.textContent} openGPTView={openGPTView} />
          )}
        </View>
        {risIconPaletteVersion.toString() === '2' && (
          <RisIconPalette risData={risIconPaletteIcons} isNewLanding={false} />
        )}
      </>
    );
  };

  const newRisOptionView = () => {
    const risHeaderIcons = newRisDataMap.slice(0, 2);
    const risIconPaletteIcons = newRisDataMap.slice(2);

    const isRisPage = props?.isRIS?.toLowerCase() === 'true';

    if (status === 'NOT_DONE') {
      return (
        <View style={{ flex: 1, justifyContent: 'center' }} testID="ris_options_list_loading">
          <Spinner size={30} color="#008b8b" />
        </View>
      );
    }

    return (
      <>
        <View testID="ris_options_list_container">
          <View style={styles.newHeadingContainer} testID="ris_options_list_heading_container">
            <Text
              style={[styles.newHeading, fontStyle('bold')]}
              testID="ris_options_list_heading_text"
            >
              {data.NEW_RIS_TITLE}
            </Text>
          </View>
          <View style={styles.newBottomTile} testID="ris_options_list_bottom_tile">
            <NewTile onClick={()=>risHeaderIcons[0].onClick(isRisPage)} 
            label={risHeaderIcons[0].text} 
            icon={risHeaderIcons[0].icon} 
            width={newCardWidth} />
            <NewTile onClick={()=>risHeaderIcons[1].onClick(isRisPage)} 
            label={risHeaderIcons[1].text} 
            icon={risHeaderIcons[1].icon} 
            width={newCardWidth} />
          </View>
          <RisIconPalette risData={risIconPaletteIcons} isNewLanding={true} />
        </View>
      </>
    );
  };

  const bookingOptionsView = () => (
    <>
      <View
        style={[styles.form, { paddingHorizontal: 14, paddingBottom: 16 }]}
        testID="booking_options_list"
      >
        {bookingCard(bookingLOBSMap.train)}
        {Platform.OS !== 'web' && railsBusCommonLanding && bookingCard(bookingLOBSMap.bus)}
      </View>
      <View style={localStyles.separator} />
    </>
  );

  const newBookingOptionView = () => (
    <>
      <View style={styles.newForm} testID="new_booking_option_view_container">
        <NewBookingCard onClick={bookingLOBSMap.train.onClick} label={'Train'} icon={newTrainIcon} arrowIcon={newBlueArrow} width={newCardWidth} />
        {Platform.OS !== 'web' &&
          railsBusCommonLanding &&
          <NewBookingCard onClick={bookingLOBSMap.bus.onClick} label={'Bus'} icon={newBusIcon} arrowIcon={newBlueArrow} width={newCardWidth} />
          }
      </View>
      <View style={localStyles.newSeparator} />
    </>
  );

  // Temporarily handling redirection to PNR page due to IRCTC's concern with TG
  const navigateTgsToPnrPage = () => {
    onPnrStatusClicked(!!railsBusCommonLanding, !!props.isRIS);
  };

  const trackClickEvent = () => {
    trackClickEventProp61(data.PAGE_NAME, RIS_DETAILS_MEALS_ORDER_NOW_CLICKED);
  };

  const renderCards = (isRIS: string) => (
    <>
      {isRIS?.toLowerCase() === 'true' ? risOptionView(10) : bookingOptionsView()}
      <TgsCard onPress={navigateTgsToPnrPage} />
      {railsMealPokus && isFocused && <RailsMealEntryPoint onBookNowClicked={trackClickEvent} />}
      <View style={localStyles.separator} />
      {isRIS?.toLowerCase() === 'true' ? bookingOptionsView() : risOptionView(0)}
    </>
  );

  const renderNewCards = (isRIS: string) => (
    <>
      {isRIS?.toLowerCase() === 'true' ? newRisOptionView() : newBookingOptionView()}
      {!railsMealPokus && <TgsCardNewLanding onPress={navigateTgsToPnrPage} withMeal={false} />}
      {railsMealPokus && (
        <MealTgTogether onTgPress={navigateTgsToPnrPage} onMealPress={trackClickEvent} />
      )}
      {isRIS?.toLowerCase() === 'true' ? newBookingOptionView() : newRisOptionView()}
      <View style={localStyles.newGreySeparator} />
    </>
  );

  const tgsCarouselOrder = getTGSCarouselTag();
  const onPnrClicked = () => {
    Actions.railsPNRStatusLanding({
      isFromNewLanding: true,
      railsBusCommonLanding: true,
    });
  };
  const tgsCarousalComponents = [
    <TouchableOpacity key="TGS" onPress={onPnrClicked} testID="tgs_carousel_item">
      <View style={localStyles.tgsBannerContainer}>
        <Image source={tgsBanner} style={localStyles.tgsBanner} resizeMode="contain" />
      </View>
    </TouchableOpacity>,
    <LtsCard
      key="LTS"
      pageName={data.PAGE_NAME}
      tryNow
      componentLocation="RISLP"
      omnitureKey={omnitureKeys.LTSoffline_card_click_RISLP}
    />,
  ];

  if (loader) {
    updateState(SCREENER_LOADING);
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Spinner size={30} color="#008cff" />
      </View>
    );
  }
  updateState(SCREENER_INTERACTIVE);

  if (isPremiumUser) {
    const tripDetails = {
      from: props?.from,
      to: props?.to,
    };
    return (
      <>
        <ScrollView style={localStyles.container} testID="premium_landing_container">
          <RailsBusPremiumLanding
            defaultLob={props.defaultLob}
            tripDetails={tripDetails}
            title={'trains_and_bus'}
            onIconClick={onHardBackPressed}
            buildFlavor={props.build_flavor}
          />
        </ScrollView>
      </>
    );
  }
  if (isNewLanding) {
    return (
      <>
        <ScrollView style={localStyles.container}>
          <RailsNewLandingHeader
            title={'trains_and_bus'}
            onIconClick={onHardBackPressed}
            buildFlavor={props.build_flavor}
            isNewLanding={isNewLanding}
          />
          {renderNewCards(props.isRIS || 'false')}
          {Platform.OS !== 'ios' ? (
            <Carousel autoplay={false} style={localStyles.carousel}>
              {tgsCarouselOrder.split('|').map((x: string) => {
                return tgsCarousalComponents.find((y: { key: string }) => y.key === x);
              })}
            </Carousel>
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={localStyles.iosCarousel}
            >
              {tgsCarouselOrder.split('|').map((x: string) => {
                return tgsCarousalComponents.find((y: { key: string }) => y.key === x);
              })}
            </ScrollView>
          )}
          {!!showDiffTypeOfAds.adfeed && (
            <>
              <View style={styles.adfeed2Container}>
                <View>{getAdsCard(Platform.OS, TRAIN_BUS_COMMON_LANDING_ADFEED2)}</View>
              </View>
              <View style={localStyles.separator} />
            </>
          )}
          {!isPhonePePlatform() && showAd && !!showDiffTypeOfAds.multi_banner && (
            <View style={showSponsoredAd1 ? styles.multiAdContainer : styles.noAdContainer}>
              {showSponsoredAd1 && (
                <Text style={[styles.adsHeading, fontStyle('latoBlack')]}>
                  {_label(labels.SPONSORED, { uppercase: true })}
                </Text>
              )}
              <SponsoredAds
                os={Platform.OS.toString()}
                pages={TRAIN_BUS_COMMON_LANDING}
                showSponsoredLabel={(val: boolean) => handleSponsoredLabel(val, 1)}
              />
            </View>
          )}
          {!!showDiffTypeOfAds.adfeed && (
            <>
              <View style={localStyles.separator} />
              <View style={{ marginBottom: 30 }}>
                <View style={showSponsoredAd2 ? styles.multiAdContainer : styles.noAdContainer}>
                  {showSponsoredAd2 && (
                    <Text style={[styles.adsHeading, fontStyle('latoBlack')]}>
                      {_label(labels.SPONSORED, { uppercase: true })}
                    </Text>
                  )}
                  <SponsoredAds
                    os={Platform.OS.toString()}
                    pages={TRAIN_BUS_COMMON_LANDING_ADFEED1}
                    showSponsoredLabel={(val: boolean) => handleSponsoredLabel(val, 2)}
                  />
                </View>
                <View style={localStyles.separator} />

                <View style={showSponsoredAd3 ? styles.multiAdContainer : styles.noAdContainer}>
                  {showSponsoredAd3 && (
                    <Text style={[styles.adsHeading, fontStyle('latoBlack')]}>
                      {_label(labels.TOP_DEALS, { uppercase: true })}
                    </Text>
                  )}
                  <SponsoredAds
                    os={Platform.OS.toString()}
                    pages={TRAIN_BUS_COMMON_LANDING_ADFEED2}
                    showSponsoredLabel={(val: boolean) => handleSponsoredLabel(val, 3)}
                  />
                </View>
              </View>
            </>
          )}
          <View style={localStyles.covidInfoContainer}>
            <CovidInfoCard
              cosmosScope="raillanding"
              trackClickEvent={trackCovidInfoClicked}
              openWebView={Actions.openWebView}
            />
          </View>
        </ScrollView>
        {!!showDiffTypeOfAds.snackbar && (
          <View style={localStyles.snackBarContainer}>
            <View>{getAdsCard(Platform.OS, TRAIN_BUS_COMMON_LANDING_SNACKBAR)}</View>
          </View>
        )}
      </>
    );
  }
  return (
    <>
      <ScrollView style={localStyles.container} testID="common_landing_container">
        <RailsNewLandingHeader
          title={'trains_and_bus'}
          onIconClick={onHardBackPressed}
          buildFlavor={props.build_flavor}
        />
        {renderCards(props.isRIS || 'false')}
        {Platform.OS !== 'ios' ? (
          <Carousel autoplay={false} style={localStyles.carousel}>
            {tgsCarouselOrder.split('|').map((x: string) => {
              return tgsCarousalComponents.find((y: { key: string }) => y.key === x);
            })}
          </Carousel>
        ) : (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={localStyles.iosCarousel}
          >
            {tgsCarouselOrder.split('|').map((x: string) => {
              return tgsCarousalComponents.find((y: { key: string }) => y.key === x);
            })}
          </ScrollView>
        )}
        {!!showDiffTypeOfAds.adfeed && (
          <>
            <View style={styles.adfeed2Container}>
              <View>{getAdsCard(Platform.OS, TRAIN_BUS_COMMON_LANDING_ADFEED2)}</View>
            </View>
            <View style={localStyles.separator} />
          </>
        )}
        {!isPhonePePlatform() && showAd && !!showDiffTypeOfAds.multi_banner && (
          <View style={showSponsoredAd1 ? styles.multiAdContainer : styles.noAdContainer}>
            {showSponsoredAd1 &&
              <Text style={[styles.adsHeading, fontStyle('latoBlack')]}>
                {_label(labels.SPONSORED, { uppercase: true })}
              </Text>
            }
            <SponsoredAds
              os={Platform.OS.toString()}
              pages={TRAIN_BUS_COMMON_LANDING}
              showSponsoredLabel={(val: boolean) => handleSponsoredLabel(val, 1)}
            />
          </View>
        )}
        {!!showDiffTypeOfAds.adfeed && (
          <>
            <View style={localStyles.separator} />
            <View style={{ marginBottom: 30 }}>
              <View style={showSponsoredAd2 ? styles.multiAdContainer : styles.noAdContainer}>
                {showSponsoredAd2 &&
                  <Text style={[styles.adsHeading, fontStyle('latoBlack')]}>
                    {_label(labels.SPONSORED, { uppercase: true })}
                  </Text>
                }
                <SponsoredAds
                  os={Platform.OS.toString()}
                  pages={TRAIN_BUS_COMMON_LANDING_ADFEED1}
                  showSponsoredLabel={(val: boolean) => handleSponsoredLabel(val, 2)}
                />
              </View>
              <View style={localStyles.separator} />

              <View style={showSponsoredAd3 ? styles.multiAdContainer : styles.noAdContainer}>
                {showSponsoredAd3 &&
                  <Text style={[styles.adsHeading, fontStyle('latoBlack')]}>
                    {_label(labels.TOP_DEALS, { uppercase: true })}
                  </Text>
                }
                <SponsoredAds
                  os={Platform.OS.toString()}
                  pages={TRAIN_BUS_COMMON_LANDING_ADFEED2}
                  showSponsoredLabel={(val: boolean) => handleSponsoredLabel(val, 3)}
                />
              </View>
            </View>
          </>
        )}
        <View style={localStyles.covidInfoContainer}>
          <CovidInfoCard
            cosmosScope="raillanding"
            trackClickEvent={trackCovidInfoClicked}
            openWebView={Actions.openWebView}
          />
        </View>
      </ScrollView>
      {!!showDiffTypeOfAds.snackbar && (
        <View style={localStyles.snackBarContainer}>
          <View>{getAdsCard(Platform.OS, TRAIN_BUS_COMMON_LANDING_SNACKBAR)}</View>
        </View>
      )}
    </>
  );
};

const mapStateToProps = ({ railsLanding, railsVernacular }) => {
  return {
    risOptions: railsLanding?.risOptions,
    status: railsVernacular.status,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchSections: () => {
      dispatch(fetchSectionOrder());
    },
    getUpComingPnrs: () => {
      dispatch(upComingPnrs());
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(RailsBusCommonLanding);

const localStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  separator: {
    width: '100%',
    height: 5,
    backgroundColor: colors.grayBg,
  },
  newGreySeparator: {
    width: '100%',
    height: 16,
    backgroundColor: colors.grayBg,
  },
  newSeparator: {
    width: '100%',
    height: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray,
  },
  fullPlaceholder: {
    flex: 1,
    backgroundColor: colors.grayBg,
  },
  adStyle: {
    margin: 16,
    alignSelf: 'center',
    flexDirection: 'row',
  },
  covidInfoContainer: {
    padding: 0,
    paddingTop: 10,
    backgroundColor: colors.white,
  },
  tgsBanner: {
    height: 145,
    width: Dimensions.get('screen').width - 30,
    marginBottom: 'auto',
  },
  carousel: {
    height: 148,
    marginTop: 15,
    paddingLeft: 15,
    paddingRight: 15,
  },
  iosCarousel: {
    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  tgsBannerContainer: {
    height: 145,
    marginRight: Platform.select({ ios: 16 }),
  },
  snackBarContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
