import React, {Component} from 'react';
import {
  FlatList,
  Text,
  View,
  TouchableHighlight,
} from 'react-native';
import isEqual from 'lodash/isEqual';
import times from 'lodash/times';
import fecha from 'fecha';
import { styles } from './DatePicker.styles';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { getDaysNamesShort, getMonthNamesShort } from '../../vernacular/calendarUtil';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import { AVAILABILITY_COLORS, getFromToDate, API_POST_METHOD } from '../CommonCalendar/Utils/CalenderUtils';
import fetch2 from '../../fetch2';
import { hideAvailabilityMarkers } from '../../RailsAbConfig';
import { AvailabilityMarkers } from '../../RailsAbConstants';
import PropTypes from 'prop-types';

class Calendar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startIndex: 0,
      selectedDate: props.selectedDate,
      ...this._getDatesState(),
      availability: {},
    };
  }

  shouldComponentUpdate(nextProps, nextState) {
    return !isEqual(nextState, this.state);
  }

  componentDidMount() {
    const showAvailabilityMarkers = hideAvailabilityMarkers() === AvailabilityMarkers.SHOWN;
    if (showAvailabilityMarkers) {
      const { srcCode: source, destCode: destination } = this.props;
      if (source && destination) {
        const { fromDate, toDate } = getFromToDate(this.props.maxDays);
        const { url } = AVAILABILITY_COLORS;
        const body = {
          source,
          destination,
          from_date: fromDate,
          to_date: toDate,
        };
        fetch2(url, {
          method: API_POST_METHOD,
          headers: AVAILABILITY_COLORS.headers,
          body: JSON.stringify(body),
        })
          .then((res) => res.json())
          .then((data) => {
            const availability = data.availabilityByDate || {};
            this.setState({ availability });
            return data;
          })
          .catch((err) => {
            console.error(err.message);
            throw err;
          });
      }
    }
  }

  UNSAFE_componentWillReceiveProps (nextProps) {
    this.setState({
      selectedDate: nextProps.selectedDate,
    });
  }

  _getDatesState() {
    const {selectedDate, maxDays} = this.props;

    const date = new Date(this.props.minDate.getTime());
    date.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);
    const startTime = selectedDate.getTime();

    const oneDayMs = 24 * 60 * 60 * 1000;
    let startIndex = 0;
    const minDateTime = date.getTime();
    const dates = times(maxDays, (i) => {
      const time = minDateTime + (i * oneDayMs);
      if (startTime === time) {startIndex = i;}
      date.setTime(time);
      return ({
        date: date.getDate(),
        day: getDaysNamesShort()[date.getDay()],
        month: date.getMonth(),
        year: date.getFullYear(),
        ts: date.getTime(),
      });
    });
    return {
      dates,
      selectedMonth: dates[startIndex].month,
      selectedYear: dates[startIndex].year,
      startIndex,
    };
  }


  _getItemLayout = (data, index) => ({length: 72, offset: 72 * index, index});

  _onDateSelected = (date) => {
    this.props.changeDate(new Date(date.ts));
  };

  _getAvlStatus = ( item ) => {
    const currDate = new Date(item.ts);
    const dateStr = fecha.format(currDate, 'YYYYMMDD');
    const avlColor = this.state.availability[dateStr];
    const  avlStatus = AVAILABILITY_COLORS.intensityShort[avlColor] || '--';
    return {
      avlColor,
      avlStatus,
    };
  };

  _renderDate = ({item}) => {
    const isSelected = item.ts === this.state.selectedDate.getTime();
    const MONTHS = getMonthNamesShort();
    const { avlColor, avlStatus } = this._getAvlStatus(item);
    const showAvailabilityMarkers = ( hideAvailabilityMarkers() === AvailabilityMarkers.SHOWN);
    return (
      <View style={styles.dateContainer} testID={`calendar_${this.state.selectedDate}`}>
        {
          item.date === 1 && !(this.state.selectedMonth === item.month) &&
            <View style={styles.monthContainer}>
              <View style={styles.monthHeader}>
                <Text style={[styles.month, fontStyle('bold'), getLineHeight(12)]}>{MONTHS[item.month].toUpperCase()}</Text>
              </View>
            </View>
        }
        <TouchableHighlight
          onPress={() => this._onDateSelected(item)}
          activeOpacity={0.6}
          underlayColor={colors.grey3}
        >
          <View style={isSelected ? styles.dateBoxSelected : styles.dateBox}>
            {showAvailabilityMarkers && avlColor && (
              <View style={[styles.avlDot, { backgroundColor: avlColor }]} />
            )}
            <Text
              style={[
                isSelected ? styles.dateSelected : styles.date,
                fontStyle('medium'),
                getLineHeight(12),
              ]}
            >
              {item.date}, {item.day}
            </Text>
            {showAvailabilityMarkers && avlStatus && (
              <Text
                style={[isSelected ? styles.avlTextSelected : styles.avlText, fontStyle('medium')]}
              >
                {avlStatus}
              </Text>
            )}
          </View>
        </TouchableHighlight>
      </View>
    );
  };

  _onScroll = ({nativeEvent}) => {
    this._scrollX = nativeEvent.contentOffset.x;
    if (this._scrollX < 0) {
      return;
    }
    const scrollIndex = Math.round(this._scrollX / 72);
    if (this._lastIndex !== scrollIndex) {
      if (this._lastIndex &&
        this.state.dates[this._lastIndex].month !== this.state.dates[scrollIndex].month) {
        this.setState({
          selectedMonth: this.state.dates[scrollIndex].month,
          selectedYear: this.state.dates[scrollIndex].year,
        });
      }
      this._lastIndex = scrollIndex;
    }
  };

  render() {
    const MONTHS = getMonthNamesShort();
    return (
      <View style={{ flexDirection: 'row', backgroundColor: colors.white,marginVertical:5 }} testID={this.props?.id}>
        <View
          style={{
            height: 48,
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          testID={`${this.props?.id || 'calendar'}_month`}
        >
          <View style={styles.monthHeader}>
            <Text
              testID={`${MONTHS[this.state?.selectedMonth].toUpperCase()}_month_header_text`}
              style={[styles.month, fontStyle('bold'), getLineHeight(12)]}
            >
              {MONTHS[this.state.selectedMonth].toUpperCase()}
            </Text>
          </View>
        </View>
        <FlatList
          ref={(e) => {this._calendarScroll = e;}}
          onLayout={({nativeEvent}) => {this._visibleItems = Math.round(nativeEvent.layout.width / 72);}}
          horizontal
          getItemLayout={this._getItemLayout}
          showsHorizontalScrollIndicator={false}
          onScroll={this._onScroll}
          data={this.state.dates}
          renderItem={this._renderDate}
          extraData={this.state.selectedDate}
          keyExtractor={i => `${i.ts}`}
          initialScrollIndex={this.state.startIndex}
          testID="calendar_dates_flatlist"
        />
      </View>
    );
  }
}

Calendar.propTypes = {
  id: PropTypes.string,
  changeDate: PropTypes.func,
  selectedDate: PropTypes.instanceOf(Date),
  minDate: PropTypes.instanceOf(Date),
  maxDays: PropTypes.instanceOf(Date),
  destCode: PropTypes.string,
  srcCode: PropTypes.string,
};


export default Calendar;
