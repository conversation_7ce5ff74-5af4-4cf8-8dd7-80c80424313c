
import React from 'react';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {View,  BackHandler, SafeAreaView } from 'react-native';
import { Actions } from '../../navigation';
import fecha from 'fecha';
import CosmosPageHeader from '@mmt/legacy-commons/Common/Components/Header/CosmosPageHeader';
import Card from '@mmt/legacy-commons/Common/Components/Card/index';
import {ACTION_DATE_SELECTED} from '../RailsLandingPage/Store/RailsLandingActionTypes';
import {setDepartureDate} from '../NewListing/RailsListingActions';
import {
  DATE_CHANGE_ERR_PAGE_CONST,
  LISTING_PAGE_CONST,
  PNR_RETURN_TICKETS,
  YEAR_MONTH_DATE_WITH_SEPERATOR,
} from '../../Utils/RailsConstant';
import CommonCalendar from '../CommonCalendar/CommonCalendar';
import { _label } from '../../vernacular/AppLanguage';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { MAX_DAYS } from 'apps/rails/src/Utils/RailsConstant';

class MmtCalendar extends React.Component {
  constructor(props) {
    super(props);
    let selectedDate = props?.selectedDate;
    try {
      this._selectedDate = fecha.format(selectedDate, YEAR_MONTH_DATE_WITH_SEPERATOR);
    } catch (error) {
      console.log('Error in formatting date: ',error);
      selectedDate = new Date();
      this._selectedDate = fecha.format(selectedDate, YEAR_MONTH_DATE_WITH_SEPERATOR);
    }

    this.state = {
      selectedDate: this._selectedDate,
      selectedDateObj: props.selectedDate,
      id: 'calendar',
      arpWindow: MAX_DAYS,
    };
  }

  async componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this._onBack);
    const ARP_WINDOW = await getConfigStore(configKeys.RAILS_ARP_WINDOW_FOR_CALENDAR, false);
    let arpWindowDays = ARP_WINDOW.irctc_arp_window;
    arpWindowDays = typeof arpWindowDays === 'string' ? Number(arpWindowDays) : arpWindowDays;

    this.setState({ arpWindow: arpWindowDays });
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this._onBack);
  }

  _onBack = () => {
    Actions.pop();
    return true;
  };

  _onDone = () => {
    if (this.props.fromPage === DATE_CHANGE_ERR_PAGE_CONST) {
      this.props.onDoneFromAutoSearch(this.state.selectedDateObj, this.props.searchProps);
      return;
    }

    if (this.props.fromPage === PNR_RETURN_TICKETS) {
      const { srcCodeParam, destCodeParam } = this.props;
      const originStation = {
        code: srcCodeParam,
      };
      const destinationStation = {
        code: destCodeParam,
      };
      const searchProps = {
        originStation,
        destinationStation,
      };
      this.props.onDoneFromPNRReturn(this.state.selectedDateObj, searchProps);
      return;
    }

    if (this.props.fromPage === LISTING_PAGE_CONST) {
      this.props.onDoneFromListing(this.state.selectedDateObj);
    }

    this.props.onDone(this.state.selectedDateObj);

    Actions.pop();
  };

  _onBackPress = () => {
    if (this.props.fromPage === PNR_RETURN_TICKETS) {
      Actions.pop();
      return;
    }
    this._onDone();
  };

  _handleDateSelection = (date) => {
    const dateStr = fecha.format(date, 'YYYY-MM-DD');
    this.setState({
      selectedDate: dateStr,
      selectedDateObj: date,
    }, this._onDone);
  };

  render() {
    const { srcCode, srcCodeParam, destCode, destCodeParam, departureDate } = this.props;
    return (
      <SafeAreaView style={{flex: 1}}>
      <View style={{position: 'absolute', height: '100%'}} testID={this.state?.id}>
        <Card style={{
          marginVertical: 0,
          marginHorizontal: 0,
          padding: 0,
        }}
        >
          <CosmosPageHeader
            title=""
            boldTitle={_label('select_date')}
              onIconClick={this._onBackPress}
          />
        </Card>
           <CommonCalendar
            source={srcCode ?? srcCodeParam}
            destination={destCode ?? destCodeParam}
            departureDate={departureDate}
            onDateClick={this._handleDateSelection}
            arpDays={this.state.arpWindow}
          />
        <View style={{
          alignSelf: 'center',
          position: 'absolute',
          bottom: 24,
        }}
        />
      </View>
      </SafeAreaView>);
  }
}

MmtCalendar.propTypes = {
  labels: PropTypes.object.isRequired,
  selectedDate: PropTypes.instanceOf(Date).isRequired,
  searchProps: PropTypes.object,
  onDone: PropTypes.func.isRequired,
  srcCode: PropTypes.string,
  srcCodeParam: PropTypes.string,
  destCode: PropTypes.string,
  destCodeParam: PropTypes.string,
  departureDate: PropTypes.instanceOf(Date),
  fromPage: PropTypes.string,
  onDoneFromAutoSearch: PropTypes.func,
  onDoneFromPNRReturn: PropTypes.func,
  onDoneFromListing: PropTypes.func,
};

const mapDispatchToProps = dispatch => ({
  onDone: (selectedDate) => {
    dispatch({
      type: ACTION_DATE_SELECTED,
      data: selectedDate,
    });
  },
  onDoneFromListing: date => dispatch(setDepartureDate(date)),
  onDoneFromAutoSearch: (date, searchProps) => {
    dispatch(setDepartureDate(date));
    Actions.pop();
    Actions.railsListing({
      ...searchProps,
      departureDate: date,
      fromPage: 'landing',
      type: 'replace',
    });
  },
  onDoneFromPNRReturn: (date, searchProps) => {
    dispatch(setDepartureDate(date));
    Actions.pop();
    Actions.railsListing({
      ...searchProps,
      departureDate: date,
      fromPage: 'PNR',
      type: 'replace',
    });
  },
});

const mapStateToProps = ({ railsLanding }) => {
  const { originStation, destinationStation, departureDate } = railsLanding;

  return {
    srcCode: originStation?.code,
    destCode: destinationStation?.code,
    departureDate,
  };
};

MmtCalendar.whyDidYouRender = {
  logOnDifferentValues: true,
  customName: 'RailCalendar render track',
};
export default connect(mapStateToProps, mapDispatchToProps)(MmtCalendar);
