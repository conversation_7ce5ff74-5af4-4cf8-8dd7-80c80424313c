import { StyleSheet } from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
    monthContainer: {
      height: 48,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 4,
    },
    monthHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.black,
      height: 39,
    },
    month: {
      color: colors.white,
      fontSize: 12,
      transform: [{rotate: '-90deg'}],
    },
    year: {
    },
    dateContainer: {
      marginLeft: 4,
      alignItems: 'center',
      flexDirection: 'row',
    },
    dateBox: {
      marginHorizontal: 0,
      borderWidth: 1,
      borderColor: colors.lightGrey,
      height: 40,
      alignItems: 'center',
      borderRadius: 4,
    },
    dateBoxSelected: {
      marginHorizontal: 0,
      borderWidth: 1,
      borderColor: colors.azure,
      height: 40,
      alignItems: 'center',
      borderRadius: 4,
      backgroundColor: colors.azure,
    },
    date: {
      color: colors.black,
      top: 10,
      fontSize: 12,
      width: 66,
      textAlign: 'center',
      lineHeight: 16,
    },
    dateSelected: {
      color: colors.white,
      top: 10,
      fontSize: 12,
      width: 66,
      textAlign: 'center',
      lineHeight: 16,
    },
    day: {
      color: colors.lighterTextColor,
      fontSize: 12,
      paddingVertical: 8,
      textAlign: 'center',
      alignItems: 'center',
    },
    avlDot: {
      position: 'absolute',
      width:4,
      height:4,
      borderRadius: 4,
      top:6,
      right:6,
    },
    avlText: {
      position: 'absolute',
      top:24,
      color: colors.lightTextColor,
      fontSize: 8,
    },
    avlTextSelected: {
      position: 'absolute',
      top:24,
      color: colors.white,
      fontSize: 8,
    },
});
