
import { irctcAccountUserNameAPI } from '@mmt/rails/src/Utils/RailsConstant';
export const ACTION_UPDATE_USERNAME = '@rails/ACTION_UPDATE_USERNAME';
export const ACTION_UPDATE_VERIFICATION_FLOW = '@rails/ACTION_UPDATE_VERIFICATION_FLOW';
export const ACTION_UPDATE_AADHAR_FLOW = '@rails/ACTION_UPDATE_AADHAR_FLOW';

export const updateUsername = irctcUserName => async (dispatch) => {

  dispatch({
    type: ACTION_UPDATE_USERNAME,
    data: {
      irctcUserName,
    },
  });

};

export const updateVerificationFlow = (verificationFlow = {}) => async (dispatch) => {
  dispatch({
    type: ACTION_UPDATE_VERIFICATION_FLOW,
    data: {
      verificationFlow,
    },
  });
};

export const updateConfigKeyResponseForAadharVerification = (response) => async (dispatch) => {
  dispatch({
    type: ACTION_UPDATE_AADHAR_FLOW,
    data: {
      aadharConfigKeyResponse: response,
    },
  });
};

export const validateUsernameField = (text) => {
  const regex = /^[a-zA-Z0-9_]*$/;
  return regex.test(text);
};

export const initUserFlowDetails = () => async (dispatch) => {
  const irctcUserName = await irctcAccountUserNameAPI();
  dispatch(updateUsername(irctcUserName));
};
