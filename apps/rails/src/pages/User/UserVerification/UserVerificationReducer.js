import {
  ACTION_UPDATE_USERNAME,
  ACTION_UPDATE_VERIFICATION_FLOW,
  ACTION_UPDATE_AADHAR_FLOW,
} from './UserVerificationActions';

const initialState = {
  irctcUserName: null,
  verificationFlow: {},
};

export default (state = initialState, action) => {
  switch (action.type) {
    case ACTION_UPDATE_USERNAME: {
      const {
        irctcUserName,
      } = action.data;

      return {
        ...state,
        irctcUserName,
      };
    }
    case ACTION_UPDATE_VERIFICATION_FLOW:
    case ACTION_UPDATE_AADHAR_FLOW: {
      return {
        ...state,
        ...action.data,
      };
    }
    default: {
      return state;
    }
  }
};
