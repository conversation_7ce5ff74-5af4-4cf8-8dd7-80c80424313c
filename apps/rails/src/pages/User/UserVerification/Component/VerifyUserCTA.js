import React from 'react';
import PropTypes from 'prop-types';
import {View} from 'react-native';
import styles from '../../Common/UserVerificationCSS';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import DisabledCTAButton from '../../Common/DisabledCTAButton';
import LoadingCTAButton from '../../Common/LoadingCTAButton';
import CtaButton from '../../Common/CtaButton';

const verifyUserCTADefaultPropTextStyle = [];

// textStyle prop must be an ARRAY  [  styles , objects]
const VerifyUserCTA = ({
  enableCTA,
  isLoading,
  label,
  verifyUserId,
  textStyle = verifyUserCTADefaultPropTextStyle,
  btnStyle = null,
}) => (
  <View style={[styles.paddingTop40, {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  }]}
  >
    {!enableCTA && !isLoading &&
    <DisabledCTAButton
      label={label}
      btnStyle={{
        borderWidth: 0,
        ...btnStyle,
      }}
      textStyle={[{color: colors.white},...textStyle]}
    />
    }

    {enableCTA && !isLoading &&
    <CtaButton
      label={label}
      onPress={() => verifyUserId()}
      btnStyle={{
        borderWidth: 0,
        ...btnStyle,
      }}
      textStyle={[{color: colors.white} , ...textStyle]}
    />
    }

    {isLoading &&
    <View style={{marginRight: 9}}>
      <LoadingCTAButton
        btnStyle={{
          borderWidth: 0,
        }}
      />
    </View>
    }
  </View>
);

VerifyUserCTA.propTypes = {
  enableCTA: PropTypes.bool.isRequired,
  isLoading: PropTypes.bool.isRequired,
  verifyUserId: PropTypes.func.isRequired,
  label: PropTypes.string.isRequired,
  textStyle: PropTypes.object,
  btnStyle: PropTypes.object,
};

VerifyUserCTA.navigationOptions = {
  header: null,
};

export default VerifyUserCTA;
