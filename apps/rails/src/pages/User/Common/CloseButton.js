import React from 'react';
import PropTypes from 'prop-types';
import {Image, View} from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import styles from './UserVerificationCSS';
import icon from '@mmt/legacy-assets/src/ic_close_black.webp';

const CloseButton = ({ onPress = null }) => (
  <TouchableRipple
    onPress={(onPress)}
  >
    <View style={styles.closeButtonContainer}>
      <Image style={styles.closeButtonIcon} source={icon} />
    </View>
  </TouchableRipple>
);

CloseButton.propTypes = {
  onPress: PropTypes.func,
};

export default CloseButton;
