import React from 'react';
import PropTypes from 'prop-types';
import {ActivityIndicator, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';

const loadingCTAButtonDefaultPropColor = ['#53B2FE', '#065AF3'];

const LoadingCTAButton = ({
  btnStyle = null, color = loadingCTAButtonDefaultPropColor,
}) => (
  <View>
    <LinearGradient
      colors={color}
      start={{
        x: 0.0,
        y: 0.0,
      }}
      end={{
        x: 1.0,
        y: 0.0,
      }}
      style={[styles.cta, btnStyle]}
    >
      <ActivityIndicator
        styleAttr="Inverse"
        color="white"
        style={{margin: 4}}
        size="large"
      />
    </LinearGradient>
  </View>
);

LoadingCTAButton.propTypes = {
  btnStyle: PropTypes.object,
  color: PropTypes.array,
};

const styles = ({
  cta: {
    overflow: 'hidden',
    backgroundColor: colors.azure,
    paddingHorizontal: normalisePx(20),
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    width: 44,
    borderRadius: 96,
    borderWidth: 0,
  },
  text: {
    width: 20,
    height: 20,
  },
});

export default LoadingCTAButton;
