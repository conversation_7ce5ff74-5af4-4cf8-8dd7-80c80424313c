import React from 'react';
import PropTypes from 'prop-types';
import {Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors, normalisePx} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

const disabledCTAButtonDefaultPropColor = [colors.disabledButton, colors.disabledButton];
const disabledCTAButtonDefaultPropTextStyle = [];

// textStyle prop is an array of styles
const DisabledCTAButton = ({
  label,
  btnStyle = null,
  color = disabledCTAButtonDefaultPropColor,
  textStyle = disabledCTAButtonDefaultPropTextStyle,
}) => (
  <View>
    <LinearGradient
      colors={color}
      start={{
        x: 0.0,
        y: 0.0,
      }}
      end={{
        x: 1.0,
        y: 0.0,
      }}
      style={[styles.cta, btnStyle]}
    >
      <Text style={[styles.text, fontStyle('bold'), getLineHeight(16), ...textStyle]}>{label.toUpperCase()}</Text>
    </LinearGradient>
  </View>
);

DisabledCTAButton.propTypes = {
  btnStyle: PropTypes.object,
  color: PropTypes.array,
  textStyle: PropTypes.array,
  label: PropTypes.string.isRequired,
};

const styles = ({
  cta: {
    overflow: 'hidden',
    backgroundColor: colors.azure,
    paddingHorizontal: normalisePx(20),
    paddingVertical: normalisePx(12),
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    borderRadius: 96,
    borderWidth: 1,
  },
  text: {
    fontSize: 16,
    backgroundColor: colors.transparent,
    color: colors.white,
    paddingHorizontal: normalisePx(30),
  },
});

export default DisabledCTAButton;
