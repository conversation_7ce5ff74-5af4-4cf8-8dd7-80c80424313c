import React from 'react';
import { Text, TextInput, View } from 'react-native';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import styleConst from './UserVerificationCSS';
import { isIos } from '../../../Utils/device';

export const TextInputTypeActive = ({
  label, styles, isError, ...textInputProps
}) => (
  <View style={styles}>
    <Text style={isError ? styleConst.errorTextLabel : styleConst.activeTextLabel}>{label}</Text>
    <TextInput
      {...textInputProps}
      selectionColor="#008cff"
      underlineColorAndroid={isError ? 'red' : '#008cff'}
      enablesReturnKeyAutomatically
      editable
      ref={(e) => {
        textInputProps.innerRef && textInputProps.innerRef(e);
      }}
      returnKeyType="done"
      style={styleConst.activeTextValue}
      autoCapitalize="none"
    />
    { isIos() &&
      <View style={[styleConst.defaultLine, styleConst.paddingTop7]}/>
    }
  </View>
);

TextInputTypeActive.propTypes = {
  ...TextInput.PropTypes,
  label: PropTypes.string.isRequired,
  styles: PropTypes.object,
  isError: PropTypes.bool,
};

TextInputTypeActive.defaultProps = {
  styles: {},
  isError: false,
};

export const TextInputTypeStatic = ({label, value, styles}) => (
  <View style={styles}>
    <Text style={styleConst.staticTextLabel}>{label}</Text>
    <Text style={[styleConst.staticTextValue, styleConst.paddingTop6]}>{value}</Text>
  </View>
);

TextInputTypeStatic.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  styles: PropTypes.object,

};
TextInputTypeStatic.defaultProps = {
  styles: {},
};

export const TextInputTypeError = ({
  label, value, description, styles,
}) => (
  <View style={styles}>
    <Text style={styleConst.errorTextLabel}>{label}</Text>
    <View>
      <Text style={[styleConst.errorTextValue, styleConst.paddingTop7]}>{value}</Text>
      <View style={[styleConst.errorLine, styleConst.paddingTop7]} />
    </View>
    <Text style={[styleConst.errorTextDesc, styleConst.paddingTop6]}>{description}</Text>
  </View>
);

TextInputTypeError.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  styles: PropTypes.object,
};
TextInputTypeError.defaultProps = {
  styles: {},
};

export const TextInputTypeErrorWithoutDesc = ({
  label, value, styles,
}) => (
  <View style={styles}>
    <Text style={styleConst.errorTextLabel}>{label}</Text>
    <Text style={[styleConst.errorTextValue, styleConst.paddingTop7]}>{value}</Text>
  </View>
);

TextInputTypeErrorWithoutDesc.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  styles: PropTypes.object,

};
TextInputTypeErrorWithoutDesc.defaultProps = {
  styles: {},
};

export const TextInputTypeDefault = ({
  label, value, styles, onClick,
}) => (
  <View style={styles}>
    <Text style={styleConst.staticTextLabel}>{label}</Text>
    <TouchableRipple onPress={onClick}>
      <Text style={[styleConst.staticTextValue, styleConst.paddingTop6]}>{value}</Text>
    </TouchableRipple>
  </View>
);

TextInputTypeDefault.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  styles: PropTypes.object,

};
TextInputTypeDefault.defaultProps = {
  styles: {},
};

