
import React from 'react';
import PropTypes from 'prop-types';
import { Image, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors, normalisePx } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import whiteArrow from '@mmt/legacy-assets/src/ic-cta-arrownext-white.webp';

const ctaButtonDefaultPropColor = ['#53B2FE', '#065AF3'];
const ctaButtonDefaultPropTextStyle = [];

// textStyle prop is an array of styles
const CtaButton = (props) => {
  props = {
    ...props,
    btnStyle: typeof props.btnStyle === 'undefined' ? null : props.btnStyle,
    color: typeof props.color === 'undefined' ? ctaButtonDefaultPropColor : props.color,
    textStyle:
      typeof props.textStyle === 'undefined' ? ctaButtonDefaultPropTextStyle : props.textStyle,
    withNextArrow: typeof props.withNextArrow === 'undefined' ? false : props.withNextArrow,
  };

  const {
    label, onPress, btnStyle, color, textStyle, withNextArrow,
  } = props;
  const resolvedTextStyle = Array.isArray(textStyle) ? textStyle : textStyle ? [textStyle] : [];
  const resolvedBtnStyle = Array.isArray(btnStyle) ? btnStyle : btnStyle ? [btnStyle] : [];
  const labelText = typeof label === 'string' ? label : String(label || '');
  const resolvedColorsRaw = Array.isArray(color)
    ? color
    : typeof color === 'string' && color
    ? [color]
    : [];
  const resolvedColors = resolvedColorsRaw.filter(Boolean);
  if (resolvedColors.length === 0) {
    resolvedColors.push('#53B2FE', '#065AF3');
  }
  return (
    <TouchableRipple feedbackColor={colors.transparent} onPress={onPress}>
      <View>
        <LinearGradient
          colors={resolvedColors}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 0.0 }}
          style={[styles.cta, ...resolvedBtnStyle]}
        >
          <View style={{ flexDirection: 'row' }}>
            <Text style={[styles.text, fontStyle('bold'), getLineHeight(16), ...resolvedTextStyle]}>
              {labelText.toUpperCase()}
            </Text>
            {withNextArrow &&
              <Image
                style={{
                  width: 12,
                  height: 9,
                  marginTop: 3,
                }}
                source={whiteArrow}
                resizeMode="contain"
              />
            }
          </View>
        </LinearGradient>
      </View>
    </TouchableRipple>
  );
};

CtaButton.propTypes = {
  btnStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  color: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),
  textStyle: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  label: PropTypes.string.isRequired,
  onPress: PropTypes.func.isRequired,
  withNextArrow: PropTypes.bool,
};

const styles = ({
  cta: {
    overflow: 'hidden',
    backgroundColor: colors.azure,
    // paddingHorizontal: normalisePx(20),
    // paddingVertical: normalisePx(5),
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    borderRadius: 96,
    borderWidth: 1,
  },
  text: {
    fontSize: 16,
    backgroundColor: colors.transparent,
    color: colors.white,
    paddingHorizontal: normalisePx(30),
  },
});

export default CtaButton;
