import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../../vernacular/VernacularUtils';

export const textStyle = {
  getCommonHeaderTitleFontStyle : () => {
      return fontStyle('bold');
  },
  getLatoBlackTextFontStyle : (forceFontCode = undefined) => {
      return fontStyle('black', forceFontCode);
  },
  getLatoRegularTextFontStyle : (forceFontCode = undefined) => {
      return fontStyle('regular',forceFontCode);
  },
  getLatoBoldTextFontStyle : (forceFontCode = undefined) => {
    return fontStyle('bold', forceFontCode);
  },
  getDefaultPlaceHolderFontStyle : () => {
    return textStyle.getLatoBoldTextFontStyle();
  },
  getActiveTextLabelFontStyle : () => {
    return textStyle.getLatoRegularTextFontStyle();
  },
  getActiveTextValueFontStyle : () => {
    return textStyle.getLatoBoldTextFontStyle();
  },
  getStaticTextLabelFontStyle : () => {
    return textStyle.getLatoRegularTextFontStyle();
  },
  getStaticTextValueFontStyle : () => {
    return textStyle.getLatoBoldTextFontStyle();
  },
  getErrorTextLabelFontStyle : () => {
      return textStyle.getLatoRegularTextFontStyle();
  },
  getErrorTextValueFontStyle : () => {
      return textStyle.getLatoBoldTextFontStyle();
  },
  getErrorTextDescFontStyle : () => {
      return textStyle.getLatoRegularTextFontStyle();
  },
};

const styles = {
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  commonHeaderTitle: {
    color: colors.black,
    fontSize: 24,
    lineHeight: 30,
    letterSpacing: 0,
  },
  userFlowContainer: {
    marginTop: 16,
    marginLeft: 16,
    marginRight: 16,
  },
  marginHorizontal24: {
    marginLeft: 24,
    marginRight: 24,
  },
  closeButtonContainer: {
    flexDirection: 'row-reverse',
  },
  closeButtonIcon: {
    marginRight: 16,
    marginBottom: 16,
    marginTop: 16,
    width: 14,
    height: 14,
  },
  latoBlackText: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.lighterTextColor,
  },
  latoBoldText: {
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.lighterTextColor,
  },
  latoRegularText: {
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0,
    color: colors.lighterTextColor,
  },
  verifyUserForm: {
    marginTop: 64,
    marginLeft: 16,
    marginRight: 16,
  },
  paddingTop20: {
    paddingTop: 20,
  },
  paddingTop40: {
    paddingTop: 40,
  },
  paddingTop18: {
    paddingTop: 18,
  },
  paddingTop14: {
    paddingTop: 14,
  },
  paddingTop120: {
    paddingTop: 120,
  },
  paddingTop35: {
    paddingTop: 35,
  },
  paddingTop16: {
    paddingTop: 16,
  },
  paddingTop80: {
    paddingTop: 80,
  },
  paddingTop7: {
    paddingTop: 7,
  },
  paddingTop8: {
    paddingTop: 8,
  },
  paddingTop6: {
    paddingTop: 6,
  },
  paddingTop25: {
    paddingTop: 25,
  },
  paddingTop30: {
    paddingTop: 30,
  },
  paddingTop124: {
    paddingTop: 124,
  },
  defaultPlaceHolder: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.black04,
  },
  defaultLine: {
    width: '100%',
    borderBottomColor: '#c2c2c2',
    borderBottomWidth: 2,
  },
  errorLine: {
    width: '100%',
    borderBottomColor: 'red',
    borderBottomWidth: 2,
  },
  activeTextLabel: {
    fontSize: 10,
    lineHeight: 14,
    letterSpacing: 0,
    color: colors.azure,
  },
  activeTextValue: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.black04,
  },
  staticTextLabel: {
    fontSize: 10,
    lineHeight: 14,
    letterSpacing: 0,
    color: colors.lightTextColor,
  },
  staticTextValue: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.black04,
  },
  flex1Row: {
    flex: 1,
    flexDirection: 'row',
  },
  flex1: {
    flex: 1,
  },
  latoBoldText12: {
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0,
    color: colors.lighterTextColor,
  },
  alignItemsCenter: {
    alignItems: 'center',
  },
  latoRegularText16: {
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.lighterTextColor,
  },
  latoRegularText14: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.black,
  },
  latoBoldText14: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.lighterTextColor,
  },
  errorTextLabel: {
    fontSize: 10,
    lineHeight: 14,
    letterSpacing: 0,
    color: colors.red,
  },
  errorTextValue: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.defaultTextColor,
  },
  errorTextDesc: {
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0,
    color: colors.red,
  },
  marginTop56: {
    marginTop: 56,
  },
  headerStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 56,
  },
  marginTop16: {
    marginTop: 16,
  },
  marginTop8: {
    marginTop: 8,
  },
  paddingtop8: {
    paddingTop: 8,
  },
  latoRegularBlack04Text14: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.black04,
  },
  paddingTop68: {
    paddingTop: 68,
  },
  latoRegularBlackText16: {
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.black,
  },
  latoRegularCyanText14: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
    color: colors.cyan,
  },
};
export default styles;
