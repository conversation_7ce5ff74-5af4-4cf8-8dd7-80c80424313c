import React from 'react';
import {Text, View} from 'react-native';
import PropTypes from 'prop-types';
import styles from './UserVerificationCSS';
import CloseButton from './CloseButton';

const CommonHeader = ({
  title,
  titleStyle = null,
  showCloseButton = true,
  onPress = null,
  titleTextStyle = null,
}) => (
  <View style={styles.flex1}>
    {showCloseButton &&
    <CloseButton onPress={onPress} />
    }
    <View style={titleStyle}>
      <Text style={[styles.commonHeaderTitle, titleTextStyle]}>{title}</Text>
    </View>
  </View>
);

CommonHeader.propTypes = {
  title: PropTypes.string.isRequired,
  titleStyle: PropTypes.object,
  showCloseButton: PropTypes.bool,
  onPress: PropTypes.func,
  titleTextStyle: PropTypes.object,
};

export default CommonHeader;
