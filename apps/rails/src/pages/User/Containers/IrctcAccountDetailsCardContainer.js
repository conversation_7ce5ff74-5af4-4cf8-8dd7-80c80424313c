import {connect} from 'react-redux';
import {IrctcAccountDetailsCard} from '../../irctc/IrctcAccountDetailsCard';
import {
} from '../UserVerification/UserVerificationActions';
import {
  captureRef,
  setShowIrctcProfileCompletionFlow,
} from '../../TravelerDetails/TravelerDetailsActions';
import { logTravellerGenericClickEvents } from '../../Review/RailsReviewActions';
import {updateUsername} from '../../User/UserVerification/UserVerificationActions';

const getLabels = texts => ({
  irctcPassword: texts.trv_irctc_password,
  accDetails: texts.trv_irctc_account_details,
  timeoutReminder: texts.trv_timeout_reminder,
  irctcUsername: texts.trv_irctc_username,
  verified: texts.trv_verified,
  notRequested: texts.trv_not_requested,
  requestNewPassword: texts.trv_request_new_password,
  reservationChoice: texts.trv_reservation_choice,
  change: texts.trv_change,
  continue: texts.trv_continue,
  createNewAcc:texts.trv_create_new_irctc_account,
  forgotUserName:texts.trv_forgot_irctc_username,
  userName:texts.trv_username,
  enterIrctcUserName:texts.user_enter_irctc_username,
  newPassword:texts.ns_get_new_password,
  strongRecommendation:texts.trv_strong_recommendation,
});

const mapStateToProps = (state, ownProps) => {
  const {
    railsUserVerification: {
      irctcUserName,
      aadharConfigKeyResponse = null,
      verificationFlow = {},
    } = {},
    railsVernacular: {
      texts,
    },
    railsTraveler: {
      showIRCTCUsernameError,
      showProfileIncompleteFlow,
      irctcProfileCompletedFromWebView,
      showIrctcComponentLoader,
    },
    railsListing: { travelplexChatConfig },
  } = state || {};

  return {
    ...ownProps,
    irctcUserName,
    showIRCTCUsernameError,
    showProfileIncompleteFlow,
    irctcProfileCompletedFromWebView,
    showIrctcComponentLoader,
    labels: getLabels(texts),
    aadharConfigKeyResponse,
    verificationFlow,
    travelplexChatConfig,
  };
};

const mapDispatchToProps = dispatch => ({
  captureRef: (componentName, myRef) => dispatch(captureRef(componentName, myRef)),
  logTravellerGenericClickEvents: (eventValue) =>
    dispatch(logTravellerGenericClickEvents(eventValue)),
  setUserNameToRedux: (userName) => {
    dispatch(updateUsername(userName));
  },
  setShowIrctcProfileCompletionFlow: (value) => {
    dispatch(setShowIrctcProfileCompletionFlow(value));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(IrctcAccountDetailsCard);
