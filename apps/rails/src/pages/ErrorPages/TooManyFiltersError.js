import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, Text, View, Image, PixelRatio} from 'react-native';
import ContainerView from '@mmt/legacy-commons/Common/Components/ContainerView';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';

import tooManyFilterImage from '@mmt/legacy-assets/src/ic_filters.webp';
class TooManyFiltersError extends Component {
  render() {
    return (
      <ContainerView style={styles.container} id={this.props?.id}>

        <View style={styles.noInternet}>
          <Image source={tooManyFilterImage} style={styles.noInternetImage} />
        </View>
        <View style={styles.tooManyFiltersContainer}>
          <View>
            <Text style={[styles.noInternetTitle, fontStyle('bold'), getLineHeight(24)]}>{_label('to_many_filters_applied')}</Text>
            <Text style={[styles.noInternetSubtitle, fontStyle('regular'), getLineHeight(14), {marginTop: 2}]}>{_label('could_not_find_trains')}</Text>
          </View>
          <View style={styles.footerButtonsContainer}>
            <View style={styles.textContainer}>
              <TouchableRipple onPress={this.props.onChangeFiltersClicked}>
                <Text style={[styles.filterTextStyle, fontStyle('bold'), getLineHeight(18)]}>{_label('change_filters', { uppercase: true })}</Text>
              </TouchableRipple>
            </View>
            <View style={{flex: 1}}>
              <TouchableRipple onPress={this.props.onClearAllFiltersClicked}>
                <Text style={[styles.filterTextStyle, fontStyle('bold'), getLineHeight(18)]}>{_label('clear_all_filters', { uppercase: true })}</Text>
              </TouchableRipple>
            </View>
          </View>
        </View>
      </ContainerView>
    );
  }
}

TooManyFiltersError.propTypes = {
  onChangeFiltersClicked: PropTypes.func.isRequired,
  onClearAllFiltersClicked: PropTypes.func.isRequired,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  noInternet: {
    flex: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noInternetImage: {
    height: '80%',
    marginHorizontal: PixelRatio.getPixelSizeForLayoutSize(24),
    marginVertical: PixelRatio.getPixelSizeForLayoutSize(8),
    width: '70%',
    resizeMode: 'contain',
  },
  noInternetTitle: {
    marginTop: 24,
    marginHorizontal: 24,
    color: colors.black,
    fontSize: 24,
    lineHeight: 28,
    textAlign: 'center',
  },
  noInternetSubtitle: {
    marginTop: 8,
    marginHorizontal: 24,
    color: colors.lightTextColor,
    fontSize: 14,
    lineHeight: 21,
    textAlign: 'center',
  },
  tooManyFiltersContainer: {
    flex: 3,
    alignItems: 'center',
    // backgroundColor: 'grey',
    flexDirection: 'column',
    width: '100%',
  },
  footerButtonsContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-around',
    width: '100%',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterTextStyle: {
    fontSize: 18,
    color: colors.azure,
  },
});

export default TooManyFiltersError;
