import React, {Component} from 'react';
import PropTypes from 'prop-types';
import noInternetImage from '@mmt/legacy-assets/src/ic_servererror.webp';
import {StyleSheet, Text, View, Image} from 'react-native';
import ContainerView from '@mmt/legacy-commons/Common/Components/ContainerView';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import { Actions } from '../../navigation';

class CustomizedSomethingWentWrong extends Component {
  render() {
    return (
      <ContainerView style={styles.container}>

        <View style={styles.noInternet}>
          <Image source={noInternetImage} style={styles.noInternetImage} />
        </View>
        <View style={styles.serverErrorFooterContainer}>
          <View>
            <Text style={[styles.noInternetTitle, fontStyle('bold'), getLineHeight(24)]}>{this.props.header}</Text>
            <View>
              <Text style={[styles.noInternetSubtitle, fontStyle('regular'), getLineHeight(14)]}>{this.props.subHeader}</Text>
            </View>
          </View>
          <View style={styles.footerButtonsContainer}>
            <TouchableRipple onPress={() => Actions.rails()}>
              <View style={styles.buttonStyle}>
                <Text style={[styles.footerTextStyle, fontStyle('bold'), getLineHeight(18)]}>{this.props.buttonText}</Text>
              </View>
            </TouchableRipple>
          </View>
        </View>
      </ContainerView>
    );
  }
}

CustomizedSomethingWentWrong.propTypes = {
  onRefresh: PropTypes.func.isRequired,
  header: PropTypes.string.isRequired,
  subHeader: PropTypes.string.isRequired,
  buttonText: PropTypes.string.isRequired,
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  noInternet: {
    flex: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noInternetImage: {
    height: '80%',
    marginHorizontal: 24,
    marginVertical: 8,
    width: '70%',
    resizeMode: 'contain',
  },
  noInternetTitle: {
    marginTop: 24,
    marginHorizontal: 24,
    color: colors.black,
    fontSize: 24,
    lineHeight: 28,
    textAlign: 'center',
  },
  noInternetSubtitle: {
    marginTop: 8,
    marginHorizontal: 24,
    color: colors.lightTextColor,
    fontSize: 14,
    lineHeight: 21,
    textAlign: 'center',
  },
  serverErrorFooterContainer: {
    flex: 3,
    alignItems: 'center',
    flexDirection: 'column',
    width: '100%',
  },
  footerButtonsContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  buttonStyle: {
    alignSelf: 'flex-start',
    marginTop: 24,
  },
  footerTextStyle: {
    fontSize: 18,
    color: colors.azure,
  },
});

export default CustomizedSomethingWentWrong;
