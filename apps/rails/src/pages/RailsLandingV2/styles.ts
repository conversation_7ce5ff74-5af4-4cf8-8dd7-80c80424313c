import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  railsLandingBorderBackground: {
    backgroundColor: colors.white,
    borderTopWidth: 2,
    borderBottomWidth: 2,
    borderColor: colors.grayBg,
  },
  railsOfferCardsContainer: {
    backgroundColor: colors.white,
    borderTopWidth: 2,
    borderBottomWidth: 2,
    borderColor: colors.grayBg,
    paddingTop: 20,
  },
  topBarContainer: {
    display: 'flex',
    flexDirection: 'row',
    gap: 20,
    alignItems: 'center',
    padding: 16,
  },
  crossButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  crossIcon: {
    width: 14,
    height: 14,
    resizeMode: 'contain',
  },
  logo: {
    width: 91,
    height: 29,
    resizeMode: 'contain',
  },
});
