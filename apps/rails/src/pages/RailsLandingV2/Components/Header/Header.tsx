import React from 'react';
import { View, Text, Image } from 'react-native';
import { styles } from './styles';

interface HeaderProps {
  data: {
    title: string;
    subTitle: string;
    imageUrl: string;
    fallBackTitle: string;
  };
  srcCity: string;
  destCity: string;
}

const Header: React.FC<HeaderProps> = ({ data, srcCity, destCity }) => {
  const { title, subTitle, imageUrl, fallBackTitle } = data;
  return (
    <View style={styles.container}>
      {imageUrl && (
        <Image
          source={{ uri: imageUrl }}
          style={styles.backgroundImage}
          resizeMode="cover"
          testID="header_background_image"
        />
      )}
      <View style={styles.contentContainer} testID="header_content_container">
        <Text style={styles.title} testID="header_title">
          {`${
            srcCity && destCity
              ? title?.replace('{sourceCity}', srcCity).replace('{destinationCity}', destCity)
              : fallBackTitle
          }`?.replaceAll('-', ' ')}
        </Text>
        <Text style={styles.subTitle} testID="header_sub_title">
          {subTitle}
        </Text>
      </View>
    </View>
  );
};

export default Header;
