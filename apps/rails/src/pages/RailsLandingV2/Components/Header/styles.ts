import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet, Dimensions } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  backgroundImage: {
    width: '100%',
    height: screenWidth * 0.5,
    resizeMode: 'contain',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 0,
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
  title: {
    fontWeight: '700',
    fontSize: 22,
    lineHeight: 26,
    color: colors.black,
  },
  subTitle: {
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 17,
    color: colors.textGrey,
  },
});
