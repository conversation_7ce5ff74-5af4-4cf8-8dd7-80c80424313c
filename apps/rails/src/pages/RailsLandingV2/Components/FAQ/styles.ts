import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    paddingVertical: 32,
    backgroundColor: colors.white,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.black,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  faqContainer: {
    paddingHorizontal: 16,
  },
  faqItem: {
    paddingVertical: 16,
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: colors.greyLight2,
  },
  questionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  question: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.black,
    flex: 1,
    paddingRight: 16,
  },
  expandIcon: {
    height: 20,
    width: 20,
  },
  rotate: {
    transform: [{ rotate: '180deg' }],
  },
  answerContainer: {
    marginTop: 12,
  },
  answer: {
    fontSize: 14,
    color: colors.textGrey,
    lineHeight: 20,
  },
});
