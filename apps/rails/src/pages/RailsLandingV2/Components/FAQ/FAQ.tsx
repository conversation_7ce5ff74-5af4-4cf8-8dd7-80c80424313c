import React, { useState } from 'react';
import { View, Text, Image } from 'react-native';
import { styles } from './styles';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { trackSemLandingPageClickEvent } from '@mmt/rails/src/railsAnalytics';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQProps {
  title: string;
  faqs: FAQItem[];
}

const FAQ: React.FC<FAQProps> = ({ title, faqs }) => {
  const [expandedIndexes, setExpandedIndexes] = useState<number[]>([]);

  const toggleExpand = (index: number) => {
    const isAlreadyExpanded = expandedIndexes.includes(index);
    if (isAlreadyExpanded) {
      setExpandedIndexes((prevIndexes) => prevIndexes.filter((i) => i !== index));
    } else {
      setExpandedIndexes((prevIndexes) => [...prevIndexes, index]);
      trackSemLandingPageClickEvent(`${RAIL_EVENTS.SEM_LANDING.FAQ_EXPAND_CLICK}${index}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.faqContainer}>
        {faqs.map((faq, index) => (
          <View
            key={index}
            style={[styles.faqItem, index !== faqs.length - 1 && styles.borderBottom]}
          >
            <TouchableRipple onPress={() => toggleExpand(index)}>
              <View style={styles.questionContainer}>
                <Text style={styles.question}>{faq.question}</Text>
                <Image
                  style={[styles.expandIcon, expandedIndexes.includes(index) && styles.rotate]}
                  source={ASSETS.downArrow}
                />
              </View>
            </TouchableRipple>
            {expandedIndexes.includes(index) && (
              <View style={styles.answerContainer}>
                <Text style={styles.answer}>{faq.answer}</Text>
              </View>
            )}
          </View>
        ))}
      </View>
    </View>
  );
};

export default FAQ;
