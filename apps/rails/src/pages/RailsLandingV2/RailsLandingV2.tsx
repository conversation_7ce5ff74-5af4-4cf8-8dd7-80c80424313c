import React, { useEffect } from 'react';
import { View, FlatList, BackHandler, SafeAreaView, TouchableOpacity, Image } from 'react-native';
import Header from './Components/Header/Header';
import FeaturesCarousel from 'apps/rails/src/configStore/Landing/FeatureCarousel';
import FAQ from './Components/FAQ/FAQ';
import { styles } from './styles';
import { Actions } from '../../navigation';
import { connect } from 'react-redux';
import {
  initRailsLanding,
  searchTrains,
  trackSearchEvent,
  setSemLandingOverlayVisibility,
} from '../RailsLandingPage/Store/RailsLandingPageActions';
import RailsSearchForm from '../RailsLandingPage/Components/RailsSearchForm/RailsSearchForm';
import { LANDING_PDT_CLICK_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { trackClickEvent } from '../RailsLandingPage/Analytics/railsLandingPageAnalytics';
import { isLandingDataFilled } from '../RailsLandingPage/Shared/utils';
import {
  logLandingGenericClickEvent,
  logSemLandingPagePdtEvent,
} from '../../PdtAnalytics/PdtAnalyticsV2/PdtRailsLanding';
import RailsOffersContainer from '../RailsLandingPage/Components/Containers/RailsOffersContainer';
import isEmpty from 'lodash/isEmpty';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { _label } from '../../vernacular/AppLanguage';
import ASSETS from '../../Utils/Assets/RailsAssets';
import {
  trackEvar71Event,
  trackLandingPageLoadV2,
  updateEvar99Variable,
} from '../../railsAnalytics';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
const PAGE_ID = 'SEM_LANDING';

function RailsLandingV2(props: unknown) {
  const { originStation, destinationStation, departureDate, showSemLandingOverlay } = props;

  const hasFields = originStation !== null && destinationStation !== null;

  const onSearchClicked = async (from: unknown, to: unknown, date: unknown) => {
    trackClickEvent('mob_rail_landing_search_click');
    if (isLandingDataFilled(from, to, date)) {
      trackSearchEvent(from, to, date);
      props.setSemLandingOverlayVisibility(false);
      props.onSearchClicked();
    }
    props.logLandingGenericClickEvent(LANDING_PDT_CLICK_EVENTS.SEARCH_CLICK);
  };

  const onOffersItemClick = (offer: unknown, index: number) => {
    const { url } = offer;
    trackClickEvent(`mob_rail_landing_offer_card_clicked_position_${index + 1}`);
    if (url) {
      Actions.openWebView({
        url,
        headerText: _label('offer_details'),
        headerIcon: backIcon,
      });
    }
  };

  const renderComponent = (component: unknown) => {
    switch (component.type) {
      case 'HEADER':
        return (
          <Header
            data={component.data}
            srcCity={props?.from_city || ''}
            destCity={props?.to_city || ''}
          />
        );
      case 'SEARCH_FORM':
        return (
          <RailsSearchForm
            hasFields={hasFields}
            searchLabel={props.searchLabel}
            onSearchClicked={() => {
              onSearchClicked(originStation, destinationStation, departureDate);
            }}
            cardElevation={0}
            id={`${PAGE_ID}_searchForm`}
          />
        );
      case 'FEATURES':
        return (
          <FeaturesCarousel
            data={props.rtbLandingData}
            autoScroll={true}
            railsLandingBorderBackground={styles.railsLandingBorderBackground}
            id={`${PAGE_ID}_featuresCarousel`}
            actionsData={props.rtbLandingActionsData}
          />
        );
      case 'OFFERS':
        return !isEmpty(props.offers) ? (
          <View style={styles.railsOfferCardsContainer} testID={`${PAGE_ID}_offers`}>
            <RailsOffersContainer
              offers={props.offers}
              headerText={_label('offers')}
              onItemClick={onOffersItemClick}
              id={`${PAGE_ID}_offers`}
            />
          </View>
        ) : (
          <></>
        );
      case 'FAQ':
        return <FAQ title={component.data.title} faqs={component.data.faqs} />;
      default:
        return null;
    }
  };

  const onBack = () => {
    // If opened from listing wrapper (has closeOverlayFunction), use new behavior
    if (props.closeOverlayFunction) {
      props.setSemLandingOverlayVisibility(false);
      props.closeOverlayFunction();
    } else {
      // If opened from landing page or other flows, use old behavior
      Actions.railsBusCommonLanding(undefined, 'replace');
      return true;
    }
  };

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', onBack);
    updateEvar99Variable(RAIL_EVENTS.SEM_LANDING.SEM_LANDING_EVAR);
    trackLandingPageLoadV2();
    trackEvar71Event(RAIL_EVENTS.SEM_LANDING.PAGE_NAME, RAIL_EVENTS.SEM_LANDING.SOURCE_EVENT);
    props?.logSemLandingPagePdtEvent?.('PAGE_LOAD');

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onBack);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const renderItem = ({ item, index }: { item: unknown; index: number }) => {
    return <View key={index}>{renderComponent(item)}</View>;
  };

  if (!showSemLandingOverlay) {
    return null;
  }

  return (
    <>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.topBarContainer}>
          <TouchableOpacity style={styles.crossButton} onPress={onBack}>
            <Image source={ASSETS.greyCrossIcon} style={styles.crossIcon} />
          </TouchableOpacity>
          <Image source={ASSETS.mmtLogo} style={styles.logo} />
        </View>
        <FlatList
          data={props.semLandingPageConfig?.components || []}
          renderItem={renderItem}
          keyExtractor={(_, index) => `component-${index}`}
          showsVerticalScrollIndicator={false}
        />
      </SafeAreaView>
    </>
  );
}

const mapStateToProps = (state: unknown, ownProps: unknown) => {
  const {
    railsLanding: {
      rtbLandingData,
      rtbLandingActionsData,
      offers,
      originStation,
      destinationStation,
      departureDate,
      showSemLandingOverlay,
    },
  } = state || {};
  return {
    ...ownProps,
    rtbLandingData,
    rtbLandingActionsData,
    offers,
    searchLabel: 'search',
    originStation,
    destinationStation,
    departureDate,
    showSemLandingOverlay,
  };
};

const mapDispatchToProps = (dispatch: unknown) => ({
  initRailsLanding: (data: unknown) => dispatch(initRailsLanding(data)),
  onSearchClicked: () => dispatch(searchTrains),
  setSemLandingOverlayVisibility: (showOverlay: boolean) =>
    dispatch(setSemLandingOverlayVisibility(showOverlay)),
  logLandingGenericClickEvent: (event: string) => dispatch(logLandingGenericClickEvent(event)),
  logSemLandingPagePdtEvent: (event: string) => dispatch(logSemLandingPagePdtEvent(event)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsLandingV2);
