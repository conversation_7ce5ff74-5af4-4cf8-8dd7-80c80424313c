import React from 'react';
import { Text, View, Image } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import LinearGradient from 'react-native-linear-gradient';
import { _label } from '../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import { tgsSearchButtonstyles } from './TGSStylesSheet.styles';
import whatsAppImage from '@mmt/legacy-assets/src/ic_whatsapp.webp';
import PropTypes from 'prop-types';

export default function TGSSearchButton(props) {
    const { disabled, label, onPress, loading,whatsappIcon } = props;
    if (disabled) {
    return (
      <View
        style={[
          tgsSearchButtonstyles.button,
          tgsSearchButtonstyles.disabledButton,
          props.buttonStyle,
        ]}
        testID="tgs_search_button_disabled"
      >
        <Text style={[tgsSearchButtonstyles.label, fontStyle('black')]}>
          {_label(label, { uppercase: true })}
        </Text>
      </View>
    );
    }

    const Loader = () => (
    <View style={tgsSearchButtonstyles.loader}>
      <Spinner size={30} color={colors.white} />
    </View>
    );

    if (whatsappIcon) {
        return (
            <TouchableRipple onPress={onPress}>
                <LinearGradient
                    colors={[colors.lightBlue, colors.darkBlue]}
                    start={{ x: 0.0, y: 0.0 }}
                    end={{ x: 1.0, y: 0.0 }}
                    style={[tgsSearchButtonstyles.buttonWhatsApp, props.buttonStyle]}
                >
                    <View style={tgsSearchButtonstyles.flexDirectionWhatsApp}>
                        <Text style={tgsSearchButtonstyles.labelWhatsApp}>{_label(label, { uppercase: true })} <Image source={whatsAppImage} resizeMode="contain" style={tgsSearchButtonstyles.whatsAppimage} /></Text>
                    </View>
                </LinearGradient>
            </TouchableRipple>
        );
    }

    const renderContent = () => {
        if (loading) {
            return (<Loader />);
        }

        return (
            <View style={tgsSearchButtonstyles.flexDirection}>
                <Text style={[tgsSearchButtonstyles.label, fontStyle('black'), getLineHeight(16)]}>{_label(label, { uppercase: true })}</Text>
            </View>);
    };

    return (
        <TouchableRipple onPress={onPress}>
            <LinearGradient
                colors={[colors.lightBlue, colors.darkBlue]}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 0.0 }}
                style={[tgsSearchButtonstyles.button, props.buttonStyle]}
            >
                <View>
                    {renderContent()}
                </View>
            </LinearGradient>
        </TouchableRipple>
    );
}

TGSSearchButton.propTypes = {
    disabled: PropTypes.bool,
    label: PropTypes.string,
    onPress: PropTypes.func,
    loading: PropTypes.bool,
    whatsappIcon: PropTypes.bool,
    buttonStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  };
