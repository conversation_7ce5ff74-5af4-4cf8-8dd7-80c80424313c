import React from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';
import { getTGSTandCWhatsappTag} from '@mmt/rails/src/RailsAbConfig';
import {configKeys} from '@mmt/rails/src/configStore/Common/constants';
import { Actions } from '@mmt/rails/src/navigation/index';
import TandCHeader from './TandCHeader';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';

interface Props {
    header: string;
    callbackUrl: string;
    callbackFunc: () => void;
    headerText: string;
    url: string;
}

interface State {
    canGoBack: boolean;
    whatsAppEnable:boolean;
    whatsAppMessage:string;
}

class TGSTandCPageWebView extends React.Component<Props, State> {
    _webViewRef: {
        callbackFunc: () => void;
        goBack: () => void;
        stopLoading: () => void;
    };

    constructor(props) {
        super(props);
        this.state = {
            canGoBack: false,
            whatsAppEnable:false,
            whatsAppMessage:'',
        };
    }

    async whatsAppEnable() {
        const whatsAppEnable = await getTGSTandCWhatsappTag();
        if (whatsAppEnable) {
      const response = await getConfigStore(configKeys.RAILS_WHATSAPP_TANDC);
      if (Object.keys(response).length !== 0) {
        this.setState({ whatsAppEnable: true, whatsAppMessage: response });
            }
        }
    }

    async componentDidMount() {
        await this.whatsAppEnable();
    }

    onHardBackPress = () => {
        const { canGoBack } = this.state;
        if (canGoBack && this._webViewRef !== null) {
            this._webViewRef.goBack();
            return canGoBack;
        } else {
            Actions.pop();
            return true;
        }
    };

    _checkIfCallbackUrl = url => url.indexOf(this.props.callbackUrl) > -1;

    _onShouldStartLoadWithRequest = (request) => {
        console.log('request', request);

        if (this._checkIfCallbackUrl(request.url)) {
            if (this._webViewRef !== null) {
                this._webViewRef.stopLoading();
                this.props.callbackFunc(request.url);
                return false;
            }
        }
        return true;
    };

    _onNavigationStateChange = (navState) => {
        this.setState({
            canGoBack: navState.canGoBack,
        });
        this._onShouldStartLoadWithRequest(navState);
    };

    render() {
        return (
            <View style={{
                flex: 1,
            }}
            >
                <TandCHeader whatsAppEnable={this.state.whatsAppEnable} whatsAppMessage={this.state.whatsAppMessage} />
                <WebView
                    ref={(e) => {
                        this._webViewRef = e;
                    }}
                    onNavigationStateChange={this._onNavigationStateChange}
                    onShouldStartLoadWithRequest={this._onShouldStartLoadWithRequest}
                    source={{ uri: 'https://www.makemytrip.com/railways/pnr/tAndCPage?isApp=true' }}
                    style={{ flex: 1 }}
                    javaScriptEnabled
                />
            </View>
        );
    }
}

export default TGSTandCPageWebView;

