import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, Mask, G } from 'react-native-svg';

export const LeftArrow = (props) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={25}
      viewBox="0 0 24 25"
      fill="none"
      {...props}
    >
      <Path
        d="M14 6.5l-6 6 6 6"
        stroke="#fff"
        strokeWidth={3}
        strokeLinecap="round"
      />
    </Svg>
  );
};

export const PlayButton = (props) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={40}
      height={45}
      viewBox="0 0 40 45"
      fill="none"
      {...props}
    >
      <Path
        opacity={0.5}
        d="M37 17.304c4 2.31 4 8.083 0 10.392L9.25 43.718c-4 2.309-9-.578-9-5.197V6.48c0-4.62 5-7.506 9-5.197L37 17.304z"
        fill="#fff"
      />
    </Svg>
  );
};

export const  PriceRise = (props)=>{
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={26}
      viewBox="0 0 24 26"
      fill="none"
      {...props}
    >
      <Path
        d="M10.68 2.352a1.846 1.846 0 012.64 0l.93.95a1.846 1.846 0 001.63.53l1.31-.223a1.846 1.846 0 012.137 1.553l.193 1.315c.087.6.464 1.118 1.008 1.387l1.19.59c.93.46 1.298 1.594.817 2.512l-.617 1.177a1.846 1.846 0 000 1.714l.617 1.177a1.846 1.846 0 01-.816 2.512l-1.192.59c-.543.269-.92.787-1.007 1.387l-.193 1.315a1.846 1.846 0 01-2.137 1.553l-1.31-.224a1.846 1.846 0 00-1.63.53l-.93.95a1.846 1.846 0 01-2.64 0l-.93-.95a1.846 1.846 0 00-1.63-.53l-1.31.224a1.846 1.846 0 01-2.137-1.553l-.193-1.315a1.846 1.846 0 00-1.008-1.387l-1.19-.59a1.846 1.846 0 01-.817-2.512l.617-1.177a1.846 1.846 0 000-1.714l-.617-1.177a1.846 1.846 0 01.816-2.512l1.191-.59c.544-.269.92-.787 1.008-1.387l.193-1.315A1.846 1.846 0 016.81 3.609l1.31.224a1.846 1.846 0 001.63-.53l.93-.95z"
        stroke="url(#paint0_linear_4142_8821)"
        strokeWidth={1.84615}
      />
      <Path
        d="M6.267 17l-.934-.933 4.934-4.967 2.666 2.667 3.467-3.434h-1.733V9h4v4h-1.334v-1.733l-4.4 4.4L10.267 13l-4 4z"
        fill="url(#paint1_linear_4142_8821)"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_4142_8821"
          x1={0}
          y1={25}
          x2={24}
          y2={1}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F3D452" />
          <Stop offset={1} stopColor="#F09819" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_4142_8821"
          x1={5.33325}
          y1={17}
          x2={19.2778}
          y2={15.7344}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F3D452" />
          <Stop offset={1} stopColor="#F09819" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
};

export const  RuppeeSymbol = (props) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={26}
      viewBox="0 0 24 26"
      fill="none"
      {...props}
    >
      <Path
        d="M10.68 2.352a1.846 1.846 0 012.64 0l.93.95a1.846 1.846 0 001.63.53l1.31-.223a1.846 1.846 0 012.137 1.553l.193 1.315c.087.6.464 1.118 1.008 1.387l1.19.59c.93.46 1.298 1.594.817 2.512l-.617 1.177a1.846 1.846 0 000 1.714l.617 1.177a1.846 1.846 0 01-.816 2.512l-1.192.59c-.543.269-.92.787-1.007 1.387l-.193 1.315a1.846 1.846 0 01-2.137 1.553l-1.31-.224a1.846 1.846 0 00-1.63.53l-.93.95a1.846 1.846 0 01-2.64 0l-.93-.95a1.846 1.846 0 00-1.63-.53l-1.31.224a1.846 1.846 0 01-2.137-1.553l-.193-1.315a1.846 1.846 0 00-1.008-1.387l-1.19-.59a1.846 1.846 0 01-.817-2.512l.617-1.177a1.846 1.846 0 000-1.714l-.617-1.177a1.846 1.846 0 01.816-2.512l1.191-.59c.544-.269.92-.787 1.008-1.387l.193-1.315A1.846 1.846 0 016.81 3.609l1.31.224a1.846 1.846 0 001.63-.53l.93-.95z"
        stroke="url(#paint0_linear_4142_8954)"
        strokeWidth={1.84615}
      />
      <Path
        d="M15.327 9.566h-2.614a1.994 1.994 0 01.982 1.374h1.632v1.24h-1.64a2.703 2.703 0 01-.716 1.3c-.36.37-.837.647-1.432.835a1.777 1.777 0 01.65.598l2.872 4.054h-1.55c-.301 0-.52-.118-.658-.355l-2.489-3.581a.818.818 0 00-.273-.266c-.098-.054-.246-.081-.443-.081H8.326v-1.182H9.76c.62 0 1.117-.12 1.492-.362.379-.246.632-.566.76-.96H8.32v-1.24h3.737a1.602 1.602 0 00-.731-.975c-.37-.231-.877-.347-1.522-.347H8.326V8.333h7.001v1.233z"
        fill="url(#paint1_linear_4142_8954)"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_4142_8954"
          x1={0}
          y1={25}
          x2={24}
          y2={1}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F3D452" />
          <Stop offset={1} stopColor="#F09819" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_4142_8954"
          x1={7.77588}
          y1={19.6591}
          x2={16.2003}
          y2={19.3448}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F3D452" />
          <Stop offset={1} stopColor="#F09819" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
};


export const Lcons = (props) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={132}
      height={24}
      viewBox="0 0 132 24"
      fill="none"
      {...props}
    >
      <Path
        d="M5.389 15.389h1.666v1.667H5.39v-1.667zM16.944 15.389h1.667v1.667h-1.667v-1.667z"
        fill="#9C3200"
        stroke="#9C3200"
      />
      <Path
        d="M4.667 10.283h.444c.238 0 .443.135.545.333h-.99a.167.167 0 010-.333zM19.333 10.283h-.444a.611.611 0 00-.545.333h.99a.167.167 0 000-.333z"
        fill="#125BD3"
        stroke="#9C3200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.11 11.99l.964-1.011 1.301-3.983a.8.8 0 01.76-.552h7.729a.8.8 0 01.76.552l1.302 3.982.964 1.013a.8.8 0 01.22.551v2.97a.8.8 0 01-.8.8H5.69a.8.8 0 01-.8-.8v-2.97a.8.8 0 01.22-.551z"
        fill="#F4A200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.111 10.648l.738-2.95a.8.8 0 01.776-.606h6.75a.8.8 0 01.777.606l.737 2.95H7.111zM7.077 12.91l1.402.401c.505.145.854.607.854 1.133H7.111a.889.889 0 01-.889-.888.67.67 0 01.855-.645zM16.923 12.91l-1.402.401a1.178 1.178 0 00-.854 1.133h2.222a.889.889 0 00.889-.888.67.67 0 00-.855-.645z"
        fill="#FFFEF0"
      />
      <Path
        d="M44.55 16.881l-2.375 3.187M51.45 16.881l2.375 3.187"
        stroke="#9C3200"
        strokeWidth={0.766584}
        strokeLinecap="round"
      />
      <Path
        d="M44.474 4.432h6.899c.147 0 .267.12.267.267v.65h-7.433v-.65c0-.148.12-.267.267-.267z"
        fill="#125BD3"
        stroke="#9C3200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M43.4 4.89h9.2c.846 0 1.532.687 1.532 1.533v9.2c0 .846-.686 1.533-1.533 1.533H43.4a1.533 1.533 0 01-1.533-1.533v-9.2c0-.846.687-1.533 1.533-1.533z"
        fill="#F4A200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M43.4 6.807h3.775c.212 0 .383.171.383.383v3.62a.383.383 0 01-.383.383H43.4a.383.383 0 01-.383-.384V7.19c0-.212.172-.383.383-.383zM48.767 6.807h3.775c.211 0 .383.171.383.383v3.62a.383.383 0 01-.383.383h-3.775a.383.383 0 01-.383-.384V7.19c0-.212.171-.383.383-.383z"
        fill="#FFFEF0"
      />
      <Path
        d="M43.707 15.622a1 1 0 100-2 1 1 0 000 2zM52.14 15.622a1 1 0 100-2 1 1 0 000 2zM50.223 13.972h-4.6v1.299h4.6v-1.299z"
        fill="#FFFEF0"
      />
      <Path
        d="M42.494 19.421h10.819M43.707 17.922h8.432"
        stroke="#9C3200"
        strokeWidth={0.766584}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M86.588 12.162h8.088c.179 0 .324.145.324.323 0 .19-.131.354-.316.397l-8.096 1.868v-2.588z"
        fill="#F4A200"
      />
      <Path
        d="M80.912 12.662h-6.326l6.326 1.46v-1.46z"
        fill="#F4A200"
        stroke="#F4A200"
      />
      <Path
        d="M83.853 8.132h.718l-.718.166v-.166z"
        fill="#125BD3"
        stroke="#9C3200"
      />
      <Path
        d="M84.147 8.132h-.718l.718.166v-.166z"
        fill="#125BD3"
        stroke="#9C3200"
      />
      <Path
        d="M83.308 11.338L84 7.65l.692 3.69h-1.383z"
        fill="#125BD3"
        stroke="#9C3200"
      />
      <Path
        d="M84 17.338a3.882 3.882 0 100-7.764 3.882 3.882 0 000 7.764z"
        fill="#F4A200"
      />
      <Path
        d="M77.53 15.397a1.294 1.294 0 100-2.588 1.294 1.294 0 000 2.588zM90.47 15.397a1.294 1.294 0 100-2.588 1.294 1.294 0 000 2.588z"
        fill="#FFFEF0"
        stroke="#9C3200"
        strokeWidth={0.555556}
      />
      <Path
        d="M82.561 11.992a3.433 3.433 0 002.878 0"
        stroke="#FFFEF0"
        strokeWidth={1.11111}
        strokeLinecap="round"
      />
      <Path
        d="M113.968 17.195h1.45v1.45h-1.45v-1.45zM124.583 17.195h1.449v1.45h-1.449v-1.45z"
        fill="#125BD3"
        stroke="#9C3200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M114.285 4.856h11.43c.451 0 .817.365.817.816v11.431a.817.817 0 01-.817.816h-11.43a.817.817 0 01-.817-.816V5.673c0-.452.366-.817.817-.817z"
        fill="#F4A200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M115.101 6.08h9.798c.225 0 .408.183.408.409v5.715a.408.408 0 01-.408.409h-9.798a.408.408 0 01-.408-.409V6.49c0-.226.183-.408.408-.408z"
        fill="#FFFEF0"
      />
      <Path
        d="M112.968 10.858a.316.316 0 01-.224-.303V8.922c0-.143.094-.263.224-.303v2.24zM127.032 10.858c.13-.04.225-.16.225-.303V8.922a.317.317 0 00-.225-.303v2.24z"
        fill="#125BD3"
        stroke="#9C3200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M115.509 14.654h.817c.676 0 1.225.548 1.225 1.224v.409h-2.042a.816.816 0 110-1.633zM124.491 14.654h-.817c-.676 0-1.225.548-1.225 1.224v.409h2.042a.816.816 0 100-1.633z"
        fill="#FFFEF0"
      />
    </Svg>
  );
};

export const TgTrainIcon = (props) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={12}
      height={14}
      viewBox="0 0 12 14"
      fill="none"
      {...props}
    >
      <Path
        d="M3.187 10.98l-1.936 2.598M8.813 10.98l1.936 2.598"
        stroke="#9C3200"
        strokeWidth={0.651788}
        strokeLinecap="round"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.125.422H8.75c.345 0 .625.28.625.625v.938H2.5v-.938c0-.345.28-.625.625-.625z"
        fill="#9C3200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.25 1.203h7.5c.69 0 1.25.56 1.25 1.25v7.5c0 .69-.56 1.25-1.25 1.25h-7.5c-.69 0-1.25-.56-1.25-1.25v-7.5c0-.69.56-1.25 1.25-1.25z"
        fill="#F4A200"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.25 2.766h3.077c.172 0 .312.14.312.312V6.03c0 .173-.14.313-.312.313H2.25a.312.312 0 01-.313-.313V3.08c0-.173.14-.313.313-.313zM6.625 2.766h3.078c.172 0 .312.14.312.312V6.03c0 .173-.14.313-.312.313H6.625a.312.312 0 01-.312-.313V3.08c0-.173.14-.313.312-.313z"
        fill="#FEF4E1"
      />
      <Path
        d="M2.5 9.953a.815.815 0 100-1.63.815.815 0 000 1.63zM9.375 9.953a.815.815 0 100-1.631.815.815 0 000 1.63zM7.813 8.608h-3.75v1.059h3.75v-1.06z"
        fill="#FEF4E1"
      />
      <Path
        d="M1.51 13.05h8.821M2.5 11.828h6.875"
        stroke="#9C3200"
        strokeWidth={0.651788}
      />
    </Svg>
  );
};

export const TgsIcon = (props) => {
  return (
    <Svg
      width={39}
      height={26}
      viewBox="0 0 39 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M29.035 2.452a1.986 1.986 0 011.477.412c.404.318.688.792.755 1.345l.505 4.142a4.23 4.23 0 00-1.936 1.573 4.442 4.442 0 00-.718 3.024 4.427 4.427 0 001.423 2.76c.626.565 1.408.94 2.256 1.055l.41 3.368a2.023 2.023 0 01-.41 1.489 1.993 1.993 0 01-1.333.76L6.525 25.466a1.986 1.986 0 01-1.477-.413 2.015 2.015 0 01-.755-1.345l-.568-4.664a4.3 4.3 0 001.463-1.606c.407-.772.593-1.68.478-2.623a4.44 4.44 0 00-1.093-2.428 4.278 4.278 0 00-1.806-1.203l-.414-3.398a2.023 2.023 0 01.41-1.488 1.993 1.993 0 011.334-.76l24.938-3.086z"
        fill="#2F0959"
        stroke="#2F0959"
        strokeWidth={0.683667}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M27.927 1.554a1.986 1.986 0 011.477.413c.404.318.687.791.755 1.344l.504 4.142a4.23 4.23 0 00-1.936 1.573 4.442 4.442 0 00-.718 3.025 4.426 4.426 0 001.423 2.76 4.21 4.21 0 002.256 1.054l.41 3.369a2.023 2.023 0 01-.409 1.488 1.993 1.993 0 01-1.334.76L5.417 24.569a1.985 1.985 0 01-1.477-.413 2.015 2.015 0 01-.755-1.344l-.445-3.645a4.3 4.3 0 001.464-1.606c.407-.772.592-1.68.478-2.623a4.44 4.44 0 00-1.094-2.428 4.278 4.278 0 00-1.806-1.203l-.538-4.418a2.023 2.023 0 01.41-1.488 1.993 1.993 0 011.334-.76l24.939-3.086z"
        fill="#fff"
        stroke="#2F0959"
        strokeWidth={0.683667}
      />
      <Mask
        id="a"
        style={{
          maskType: 'alpha',
        }}
        maskUnits="userSpaceOnUse"
        x={1}
        y={2}
        width={24}
        height={23}
      >
        <Path
          d="M22.053 2.758l2.36 19.37-18.023 2.23a2.675 2.675 0 01-2.975-2.343l-.445-3.65c1.087-.631 1.748-2.054 1.559-3.608-.19-1.553-1.172-2.773-2.379-3.121l-.445-3.65A2.686 2.686 0 014.03 4.988l18.023-2.23z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#a)">
        <Path
          clipRule="evenodd"
          d="M27.927 1.554a1.986 1.986 0 011.477.413c.404.318.687.791.755 1.344l.504 4.142a4.23 4.23 0 00-1.936 1.573 4.442 4.442 0 00-.718 3.025 4.426 4.426 0 001.423 2.76 4.21 4.21 0 002.256 1.054l.41 3.369a2.023 2.023 0 01-.409 1.488 1.993 1.993 0 01-1.334.76L5.417 24.569a1.985 1.985 0 01-1.477-.413 2.015 2.015 0 01-.755-1.344l-.445-3.645a4.3 4.3 0 001.464-1.606c.407-.772.592-1.68.478-2.623a4.44 4.44 0 00-1.094-2.428 4.278 4.278 0 00-1.806-1.203l-.538-4.418a2.023 2.023 0 01.41-1.488 1.993 1.993 0 011.334-.76l24.939-3.086z"
          stroke="#2F0959"
          strokeWidth={0.683667}
        />
        <Path
          d="M11.975 15.51a.702.702 0 00.453-.262.754.754 0 00.135-.52.735.735 0 00-.255-.47.692.692 0 00-.956.118.753.753 0 00-.135.52.752.752 0 ***********.7 0 00.503.143zM16.015 15.01a.702.702 0 00.453-.262.754.754 0 00.135-.52.736.736 0 00-.256-.47.692.692 0 00-.956.118.753.753 0 00-.135.519.751.751 0 00.256.471.7.7 0 00.503.143z"
          fill="#2F0959"
        />
        <Path
          d="M18.94 18.895l.534.434a.663.663 0 00.952-.116.72.72 0 00-.119-.987l-2.252-1.83c.466-.507.714-1.216.653-1.72-.076-.624-.319-1.811-.72-3.53a151.734 151.734 0 00-.498-2.061c-.253-1.092-1.334-1.872-2.41-1.74l-4.039.5C9.966 7.98 9.105 9 9.122 10.108c0 0-.003.97.012 2.134.022 1.766.072 2.977.148 3.601.062.505.474 1.136 1.05 1.513l-1.153 1.533-.012.016-.584.776a.721.721 0 00.121.986.666.666 0 00.952-.118l.418-.556 8.866-1.097zm-7.691-.464l.594-.791 4.819-.596.766.622-6.18.765zm-.749-5.572l6.31-.78c.302 1.323.491 2.271.55 2.752.002.193-.283.745-.588.783l-5.386.667c-.306.038-.714-.428-.757-.605-.06-.492-.104-1.459-.129-2.817zm-.021-2.751c-.007-.413.336-.819.732-.868l4.04-.5c.396-.049.826.261.922.675 0 .003.135.538.314 1.287l-6.005.743c-.005-.77-.003-1.326-.003-1.337z"
          fill="#2F0959"
        />
        <Path
          d="M22.315 2.46l2.318 19.024"
          stroke="#2F0959"
          strokeWidth={1.16326}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeDasharray="1.94 1.94"
        />
      </G>
      <Path
        d="M37.678 10.595a.574.574 0 00-.21-.368l-1.073-.787.244-1.33a.595.595 0 00-.088-.41.485.485 0 00-.355-.206l-1.297-.129-.435-1.273a.473.473 0 00-.278-.307.442.442 0 00-.405.01l-1.187.568-.997-.889a.514.514 0 00-.395-.122.538.538 0 00-.355.215l-.753 1.105-1.288-.261a.54.54 0 00-.395.088.503.503 0 00-.197.366L28.1 8.207l-1.228.44a.461.461 0 00-.295.287.49.49 0 00.013.419l.556 1.232-.852 1.025a.552.552 0 00-.115.408.574.574 0 00.21.368l1.072.788-.243 1.33c-.02.148.01.288.088.41.09.12.214.197.355.206l1.297.128.434 1.274c.042.14.146.258.279.307a.441.441 0 00.404-.01l1.187-.568.997.889c.112.09.245.14.395.121a.539.539 0 00.355-.214l.753-1.105 1.288.26a.54.54 0 00.395-.088.502.502 0 00.197-.365l.116-1.342 1.227-.44a.462.462 0 00.295-.287.54.54 0 00-.013-.419l-.556-1.232.852-1.025a.576.576 0 00.116-.409z"
        fill="#33D18F"
        stroke="#136F48"
        strokeWidth={0.412073}
      />
      <Path
        d="M36.325 11.682a.525.525 0 00-.07.56l.555 1.233-1.227.44a.526.526 0 00-.34.45l-.129 1.33-1.288-.261a.507.507 0 00-.507.207l-.754 1.105-.996-.888a.536.536 0 00-.396-.122.61.61 0 00-.147.044l-1.162.565-.434-1.274a.524.524 0 00-.436-.353l-1.298-.129.244-1.33a.55.55 0 00-.204-.527l-1.072-.787.852-1.026a.525.525 0 00.07-.56l-.552-1.206 1.228-.441a.526.526 0 00.338-.45l.116-1.341 1.288.261a.507.507 0 00.508-.207l.753-1.105.996.888a.483.483 0 00.543.078l1.187-.568.434 1.274c.061.19.23.326.437.353l1.272.132-.244 1.33a.55.55 0 00.204.527l1.072.787-.84 1.011z"
        fill="url(#paint0_linear_4526_14688)"
      />
      <Path
        d="M33.094 9.218l-1.857 2.463-.802-.65a.74.74 0 00-.554-.168.695.695 0 00-.496.298.796.796 0 00-.159.572.741.741 0 00.291.516l1.394 1.128a.732.732 0 00.553.168.714.714 0 00.497-.298l2.315-3.072a.815.815 0 00-.134-1.1c-.311-.264-.786-.205-1.048.143z"
        fill="#fff"
      />
      <Path
        d="M33.895 9.854l-2.316 3.073a.238.238 0 01-.35.043l-1.406-1.127a.261.261 0 01-.097-.172.262.262 0 01.053-.19.245.245 0 01.166-.1.244.244 0 01.184.056l1 .81c.112.09.275.07.35-.044l2.01-2.666a.238.238 0 01.35-.043c.**************.056.36z"
        fill="#fff"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_4526_14688"
          x1={27.6076}
          y1={17.3596}
          x2={36.6306}
          y2={5.56864}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#43E1A8" />
          <Stop offset={1} stopColor="#219393" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
};

export const TRIP_GURANTEE_BENEFITS = 'Benefits of Trip Guarantee';
export const VIDEO_URL = 'https://juggler.makemytrip.com/juggler/stream/key/638d5d8e-6b36-423b-9025-9715ad2a93b3/master.m3u8';
export const TRIP_GURANTEE = 'Trip Guarantee';
export const WAITLISTED_TICKET = 'Got a Waitlisted Ticket? Don’t Worry!';
export const REFUND_3X = 'Get 3X refund if your ticket is not confirmed';
export const ENTER_YOUR_PNR = 'Enter your PNR and guarantee your trip!';
export const USE3X_REFUND = 'Use this 3X refund amount and upgrade to:';
export const REFUND_USERS = '₹10 crore+ refund issued to Trip Guarantee users!';
export const CHECK_PNR = 'Check PNR Status';
export const ENTER_PNR = 'Enter your 10 digit PNR Number';
export const TGS_MMT_ID = 'TGS_MMT_ID';
export const TandCText = 'Terms & Conditions';
export const TandCTextShort = 'T&C';
export const TGWIDGET_RAC_TEXT = 'Only the trip guarantee premium amount will be refunded in case of RAC and partially confirmed tickets.';
export const MMT_HIGHLIGHTS_FALLBACK_TEXT = 'Trusted by 1M+ customers';
export const TG_DETAILS = 'Trip Guarantee Details';

export const tgLandingIconlabels = {
    ASSURED_TRIPS : '3l_assured_trips',
    TRAVEL : 'travel_assured_with',
    WAIT_LIST_TICKET : 'waiting_list_ticket',
};

export const errorMessageMapping = (pnr: string) =>
(
  {
    '3007': `This <d>PNR ${pnr}</d> is already covered with Trip Guarantee. If you wish to buy again, please try with another PNR`,
    '3008': 'Oops! You have already bought Trip Guarantee once in last 15 days.Please try again later',
    '2101': `Oops! Your <d>PNR ${pnr}</d> is not eligible for Trip Guarantee`,
    '2102': `Oops! Your <d>PNR ${pnr}</d> is not eligible due to partial confirmation status`,
    '2103': `Oops! Your <d>PNR ${pnr}</d> is not eligible for Trip Guarantee because your tickets are confirmed`,
    '2106': `Oops! Your <d>PNR ${pnr}</d> is not eligible due to partial RAC status`,
    'booker':`Oops! Your <d>PNR ${pnr}</d> is not eligible for Trip Guarantee`,
    'default': 'We are not able to fetch your eligibility status at the moment. Please retry.',
    'wrongPnr':`This <d>PNR ${pnr}</d> is either flushed or not generated`,
  }
);
