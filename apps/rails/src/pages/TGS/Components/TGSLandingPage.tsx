import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    TextInput,
    Keyboard,
    Animated,
    Image,
    Platform,
    NativeSyntheticEvent,
    NativeScrollEvent,
} from 'react-native';
import { trackTGSInEligibility, trackTGSeligibility } from 'apps/rails/src/RisAnalytics/RisAnalytics.js';
import { getPnrDetails } from '../../RIS/PnrStatus/RailInfoAction';
import { tgsLandingPageStyles } from './TGSStylesSheet.styles';
import { omnitureMapping, errorCode_3007, errorCode_3008, errorCode_2101, errorCode_2102, errorCode_2103, errorCode_2106, refundWaysTextStyle } from '../../RIS/PnrStatus/TGSUtils/Constants';
import { trackPageVisits, trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { Left<PERSON>rrow, TRIP_GURANTEE, WAITLISTED_TICKET, USE3X_REFUND, REFUND_3X, ENTER_YOUR_PNR } from './TGSConstants';
import TGSVideoContainer from './TGSVideoContainer';
import TgsCarousel from './TGSCarousel';
import TGSTestimonalFlow from './TGSTestimonalFlow';
import { Actions } from '../../../navigation';
import { populateApClassAndPriceFromPnrStatusApi, validatePnr,tgsTrackPageDetails,removeTGSOmniture } from '../../../Utils/RisUtils';
import TGSSearchButton from './TGSSearchButton';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import { _label } from '../../../vernacular/AppLanguage';
import { refundWays, getTextStyle } from '../../TravelerDetails/Components/AssuredConfirmation/AssuredConfirmationUtils';
import SpendRefundToBook from '../../TravelerDetails/Components/AssuredConfirmation/SpendRefundToBook';
import LinearGradient from 'react-native-linear-gradient';
import TGSLandingPageErrorHandling from './TGSLandingPageErrorHandling';
import { removeDataFromAsyncStorage,UNIQUE_PNR_PAGENAMES } from 'apps/rails/src/Utils/RailsConstant';
import { USER_PNR_CONTEXT } from '@mmt/legacy-commons/AppState/LocalStorage';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

const tgIcon = ASSETS.tgsLandingIcon;
const TGSLandingPage = () => {
    const [pnrNumber, setPnrNumber] = useState<string>('');
    const [focusValue, setFocusVal] = useState<boolean>(false);
    const [error, setError] = useState<unknown>({});
    const [loading, setLoading] = useState<boolean>(false);
    const scrollY = new Animated.Value(0);
    const translateYComponentWaitListedText = scrollY.interpolate({
        inputRange: [0, 50],
        outputRange: [50, 0],
        extrapolate: 'clamp',
    });

    const translateYComponentRefundAmount = scrollY.interpolate({
        inputRange: [0, 110],
        outputRange: [110, 0],
        extrapolate: 'clamp',
    });


    const onChangeHandler = (text: string) => {
        const validText = validatePnr(text);
        if (validText.length === 10) {
            Keyboard.dismiss();
        }
        setPnrNumber(validText);
    };

    useEffect(() => {
        trackPageVisits(omnitureMapping.TGS_LANDING_PAGE, {});
        removeDataFromAsyncStorage(USER_PNR_CONTEXT);
        removeTGSOmniture();
        tgsTrackPageDetails(omnitureMapping.TGS_LANDING_PAGE,UNIQUE_PNR_PAGENAMES.TGS_LANDING_PAGE);
    }, []);

    const onSubmitClicked = () => {

        trackClickEventProp61(omnitureMapping.TGS_LANDING_PAGE, omnitureMapping.TGS_LANDING_SEARCH_CLICKED);
        getPnrDetails(pnrNumber).then((pnrDetailInfo) => {
            const { TgsEligibility: { isTgEligible: isTgEligible,
                                      success: tgsSuccess,
                                      message: tgsMessage,
                                      response: tgsResponse,
                                      error: error }, IsBookedOnMMT } = pnrDetailInfo;
            populateApClassAndPriceFromPnrStatusApi(pnrDetailInfo, omnitureMapping.TGS_LANDING_PAGE);

            if (isTgEligible && IsBookedOnMMT) {
                trackTGSeligibility(omnitureMapping.TGS_LANDING_PAGE, omnitureMapping.TGS_BOOKER_ELIGIBLE);
            }
            else if (isTgEligible && !IsBookedOnMMT) {
                trackTGSeligibility(omnitureMapping.TGS_LANDING_PAGE, omnitureMapping.TGS_NON_BOOKER_ELIGIBLE);
            }
            else if (!isTgEligible && IsBookedOnMMT) {
                trackTGSeligibility(omnitureMapping.TGS_LANDING_PAGE, omnitureMapping.TGS_BOOKER_INELIGIBLE);
            }
            else if (tgsSuccess) {
                trackTGSeligibility(omnitureMapping.TGS_LANDING_PAGE, omnitureMapping.TGS_NON_BOOKER_INELIGIBLE);
            }

            if (isTgEligible) {
                Actions.tgsTravellersPage({ pnrNumber: pnrNumber,
                                            totalVoucherAmount:tgsResponse?.totalVoucherAmount,
                                            totalTicketFare:tgsResponse?.totalTicketFare});
            }

            const tgsEligibilityErrorCode = error?.errorCode;

            if (tgsEligibilityErrorCode) {

                switch (tgsEligibilityErrorCode) {
                    case errorCode_2101:
                        trackTGSInEligibility(omnitureMapping.TGS_LANDING_PAGE,
                                              omnitureMapping.TGS_INELIGIBLE_DS_OR_TATKAL);
                        break;
                    case errorCode_2102:
                        trackTGSInEligibility(omnitureMapping.TGS_LANDING_PAGE,
                                              omnitureMapping.TGS_INELIGIBLE_PARTIAL_CNF);
                        break;
                    case errorCode_2103:
                        trackTGSInEligibility(omnitureMapping.TGS_LANDING_PAGE,
                                              omnitureMapping.TGS_INELIGIBLE_CNF);
                        break;
                    case errorCode_3007:
                        trackTGSInEligibility(omnitureMapping.TGS_LANDING_PAGE,
                                              omnitureMapping.TGS_INELIGIBLE_GUARDRAIL_BOUGHT_RECENTLY);
                        break;
                    case errorCode_3008:
                        trackTGSInEligibility(omnitureMapping.TGS_LANDING_PAGE,
                                              omnitureMapping.TGS_INELIGIBLE_GUARDRAIL_ALREADY_BOUGHT);
                        break;
                    case errorCode_2106:
                        trackTGSInEligibility(omnitureMapping.TGS_LANDING_PAGE,
                                              omnitureMapping.TGS_INELIGIBLE_RAC);
                        break;
                }
            }
            if (Object.keys(tgsResponse).length === 0 && tgsMessage === '') {
                trackTGSInEligibility(omnitureMapping.TGS_LANDING_PAGE, omnitureMapping.TGS_INELIGIBLE_RIS_ERROR);
                if (error?.errorDescription === 'This pnr is a booker') {
                    setError({ errorCode: 'booker' });
                }
                else {
                    setError({ errorCode: 'wrongPnr' });
                }
            }
            else {
                setError(error);
            }
            setLoading(false);
        }).catch(() => {
            setLoading(false);
        });
    };

    const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        scrollY.setValue(event.nativeEvent.contentOffset.y);
    };

    const onBack = () => {
        trackClickEventProp61(omnitureMapping.TGS_LANDING_PAGE, omnitureMapping.TGS_LANDING_BACK_BTN);
        Actions.pop();
    };

    const submitButtonDisabled = () => pnrNumber.length !== 10;

    const onCtaPressed = () => {
        setLoading(true);
        onSubmitClicked();
    };

    return (
            <Animated.ScrollView
                onScroll={handleScroll}
                stickyHeaderIndices={[0]}
            >
                <View>
                    <LinearGradient
                        useAngle={true} angle={350.44}
                        colors={['#7C7792', '#1F1C2C']}
                        start={{ x: 0.0, y: 0.0 }}
                        end={{ x: 1.0, y: 0.0 }}
                        style={tgsLandingPageStyles.gradientContainer}
                    >
                        <View style={tgsLandingPageStyles.tgsWidetViewCss}>
                            <TouchableRipple onPress={onBack} style={Platform.OS === 'ios' ? tgsLandingPageStyles.leftArrowIos : tgsLandingPageStyles.leftArrow}>
                                <View style={Platform.OS === 'ios' ? tgsLandingPageStyles.leftArrowContainerIos : tgsLandingPageStyles.leftArrowContainer}>
                                    <LeftArrow height={34} width={34} />
                                </View>
                            </TouchableRipple>
                            <View style={tgsLandingPageStyles.tgsWidgetCss}>
                                <Image source={tgIcon} style={tgsLandingPageStyles.tgsWidgetImage} resizeMode="contain" />
                            </View>
                            <View style={tgsLandingPageStyles.availabilityContainerStyle}>
                                <Text style={[fontStyle('black'), tgsLandingPageStyles.tripGuranteeText]}>{TRIP_GURANTEE}</Text>
                            </View>
                        </View>
                        <Animated.View
                            style={{
                                height: translateYComponentWaitListedText,
                            }}
                        >
                            <Text style={[fontStyle('bold'), tgsLandingPageStyles.waitListedTicket]}>
                                {WAITLISTED_TICKET}
                            </Text>
                            <Text style={[fontStyle('semiBold'), tgsLandingPageStyles.textAlignCenter]}>{REFUND_3X}</Text>
                        </Animated.View>
                        {Object.keys(error).length === 0 ? <View style={tgsLandingPageStyles.pnrContainer}>
                            <Text style={[tgsLandingPageStyles.pnrText, fontStyle('semiBold')]}>{ENTER_YOUR_PNR}</Text>
                            <TextInput
                                placeholder={'Eg.: 8947502345'}
                                onChangeText={onChangeHandler}
                                keyboardType="phone-pad"
                                maxLength={10}
                                onFocus={() => setFocusVal(true)}
                                onBlur={() => setFocusVal(false)}
                                value={pnrNumber}
                                style={[tgsLandingPageStyles.inputText, 
                                    { borderColor: focusValue ? colors.primary : colors.lightGrey }]}
                            />
                            <View>
                                <TGSSearchButton loading={loading}
                                                 label={'Submit'}
                                                 onPress={onCtaPressed}
                                                 disabled={submitButtonDisabled()}
                                                 buttonStyle={tgsLandingPageStyles.buttonStyle}
                                 />
                            </View>
                        </View> : <TGSLandingPageErrorHandling pnr={pnrNumber} 
                        setError={setError} errorCode={error?.errorCode} />}
                    </LinearGradient>
                </View>
                <Animated.View
                    style={{
                        height: translateYComponentRefundAmount,
                    }}
                >
                    <LinearGradient
                        useAngle={true} angle={350.44}
                        colors={['#7C7792', '#1F1C2C']}
                        start={{ x: 0.0, y: 0.0 }}
                        end={{ x: 1.0, y: 0.0 }}
                        style={tgsLandingPageStyles.animationGradientStyle}
                    >
                        <Text style={[tgsLandingPageStyles.textAlignCenterAmount, fontStyle('semiBold')]}>{USE3X_REFUND}</Text>
                        <View style={tgsLandingPageStyles.flexDirectionRow}>
                            {refundWays.map((item, index) => {
                                return <SpendRefundToBook
                                    icon={item.icon}
                                    text={_label(item.text)}
                                    dot={item.dot}
                                    key={index}
                                    width={item.width}
                                    height={item.height}
                                    refundWaysTextStyle={[...getTextStyle(refundWaysTextStyle)]}
                                />;
                            })}
                        </View>
                    </LinearGradient>
                </Animated.View>
                <View style={tgsLandingPageStyles.tgsView}>
                    <TGSVideoContainer />
                    <TGSTestimonalFlow />
                    <TgsCarousel />
                </View>
            </Animated.ScrollView>
    );
};

export default TGSLandingPage;
