import React from 'react';
import HTM<PERSON>View from 'react-native-htmlview';
import { View } from 'react-native';
import { errorMessageMapping } from './TGSConstants';
import { tgsErrorContainerStyles, tgsErrorContainerTextStyles } from './TGSStylesSheet.styles';
import TGSSearchButton from './TGSSearchButton';
import PropTypes from 'prop-types';

const TGSLandingPageErrorHandling = ({ errorCode, setError, pnr }) => {

    const onRetryClicked = () => {
        setError({});
    };
    const errorMapping = errorMessageMapping(pnr);
    const buttonLabel = () => {
        if (!errorMapping[errorCode] || errorCode === 'default') {
            return 'Retry';
        }
        return 'Try Another PNR';
    };

    return (<View style={tgsErrorContainerStyles.pnrContainer}>
        <HTMLView stylesheet={tgsErrorContainerTextStyles} value={`<p><q>${errorMapping[errorCode] ?? errorMapping.wrongPnr}</q></p>`} />
        <View>
            <TGSSearchButton label={buttonLabel()}
                             onPress={onRetryClicked}
                             buttonStyle={tgsErrorContainerStyles.buttonStyle} />
        </View>
    </View>);
};

TGSLandingPageErrorHandling.propTypes = {
    errorCode: PropTypes.string,
    setError: PropTypes.func,
    pnr: PropTypes.string,
  };

export default TGSLandingPageErrorHandling;
