import React from 'react';
import { View, Text, Image, ScrollView } from 'react-native';
import { tgsCarouselStyles } from './TGSStylesSheet.styles';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import { TRIP_GURANTEE_BENEFITS } from './TGSConstants';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

const { refundBanner } = ASSETS;
// const useRefundBanner = ASSETS.useRefundBanner;
const { upgradeBanner } = ASSETS;
const bxsOffer = '@mmt/legacy-assets/src/TGS/bxs_offer.webp';

export const TGSScrollView = () => (
  <ScrollView
    horizontal
    style={tgsCarouselStyles.flatList}
    showsHorizontalScrollIndicator={false}
  >
    <View style={tgsCarouselStyles.imageContainer}>
      <Image source={refundBanner} style={tgsCarouselStyles.imageStyle} />
    </View>
    {/* Commenting to not show refund banner to temporarily handle IRCTC's concern with TG */}
    {/* <View style={tgsCarouselStyles.imageContainer}>
      <Image source={useRefundBanner} style={tgsCarouselStyles.imageStyle} />
    </View> */}
    <View style={tgsCarouselStyles.imageContainer}>
      <Image source={upgradeBanner} style={tgsCarouselStyles.imageStyle} />
    </View>
  </ScrollView>
);

function TgsCarousel() {
  return (
    <View style={tgsCarouselStyles.container}>
      <View style={tgsCarouselStyles.bxsOfferContainer}>
        <Image source={bxsOffer} style={tgsCarouselStyles.bxsOffer} />
        <Text style={[fontStyle('bold'), tgsCarouselStyles.blackColor]}>{TRIP_GURANTEE_BENEFITS}</Text>
      </View>
      <TGSScrollView />
    </View>
  );
}


export default TgsCarousel;
