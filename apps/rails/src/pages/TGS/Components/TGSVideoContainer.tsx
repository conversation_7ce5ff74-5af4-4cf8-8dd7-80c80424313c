import React, { useState } from 'react';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { View, Text, TouchableWithoutFeedback, Image } from 'react-native';
import { tgsVideoContainerStyles } from './TGSStylesSheet.styles';
import LinearGradient from 'react-native-linear-gradient';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import Video from 'react-native-video';
import { PlayButton, VIDEO_URL, REFUND_USERS } from './TGSConstants';

import pauseButton from '@mmt/hubble/src/RNVPlayer/icons/ic_pause.webp';
const TGSVideoContainer = () => {
    const [paused, setPaused] = useState(true);
    const [muted, setMuted] = useState(true);
    const [firstLoad, setFirstLoad] = useState(true);
    const [hideControls, setHideControls] = useState(false);
    const [timer, setTimer] = useState(0);
    const volumeIcon = muted ? require('@mmt/legacy-assets/src/img/volume_off.webp') : require('@mmt/legacy-assets/src/img/volume_on.webp');

    const hideVideoControls = () => {
        setHideControls(false);
        hideConrolsAfterTimeOut();
    };

    const hideConrolsAfterTimeOut = () => {
         clearTimeout(timer);
        const timerValue = setTimeout(() => { setHideControls(true); }, 2000);
        setTimer(timerValue);
    };

    const playButton = () => {
        setPaused(!paused);
        hideConrolsAfterTimeOut();
        if (firstLoad) {
            setMuted(false);
            setFirstLoad(false);
        }
    };

    const muteButton = () => {
        hideConrolsAfterTimeOut();
        setMuted(!muted);};

    return (<View style={tgsVideoContainerStyles.container}>
        <View style={tgsVideoContainerStyles.videoPlayerContainer}>
            <TouchableWithoutFeedback onPress={hideVideoControls}>
                <Video
                    source={{uri:VIDEO_URL}}
                    paused={paused}
                    style={tgsVideoContainerStyles.videoScreen}
                    repeat={true}
                    muted={muted}
                    resizeMode="cover"
                />
            </TouchableWithoutFeedback>
            <LinearGradient
                colors={[colors.lightBlue, colors.darkBlue]}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 0.0 }}
                style={tgsVideoContainerStyles.gradientStyle}
            >
                <Text style={[fontStyle('bold'), {
                    letterSpacing: 0.5,
                    color: colors.white,
                    textAlign: 'center',
                }]}>{REFUND_USERS}</Text>
            </LinearGradient>
            {!hideControls && <TouchableWithoutFeedback style={tgsVideoContainerStyles.playButton} onPress={playButton}>
                {paused ? <PlayButton height={40} width={40} style={tgsVideoContainerStyles.playButton} /> :
                    <Image source={pauseButton} style={tgsVideoContainerStyles.playButton} />}
            </TouchableWithoutFeedback>
            }
            {!hideControls && <TouchableWithoutFeedback onPress={muteButton} style={tgsVideoContainerStyles.volumeIcon}>
                <Image style={tgsVideoContainerStyles.volumeIcon} source={volumeIcon} />
            </TouchableWithoutFeedback>
            }
        </View>
    </View>);

};

export default TGSVideoContainer;
