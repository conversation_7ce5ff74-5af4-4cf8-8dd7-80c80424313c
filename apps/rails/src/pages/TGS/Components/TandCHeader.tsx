import React from 'react';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { View, Image, Text, StyleSheet, Linking } from 'react-native';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import shareContents from '@mmt/legacy-commons/Common/utils/ShareUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { Actions } from '@mmt/rails/src/navigation/index';
import { TG_DETAILS } from '@mmt/rails/src/pages/TGS/Components/TGSConstants';

import backButton from '@mmt/legacy-assets/src/black_grey.webp';
import whatsapp from '@mmt/legacy-assets/src/ic_whatsapp.webp';

interface Props {
    whatsAppMessage: string;
    whatsAppEnable: boolean;
}

const TandCHeader = (props: Props) => {
    const onwhatsAppSubmit = async () => {
        const whatsAppUrl = `whatsapp://send/?text=${props?.whatsAppMessage}`;
        return Linking.canOpenURL(whatsAppUrl)
            .then((supported) => {
                if (!supported) {
                    shareContents('TGS Share Message', `${props?.whatsAppMessage}`, null);

                }
                return Linking.openURL(whatsAppUrl);
            });

    };
    return (
        (<View>
            <View style={styles.container}>
                <View style={styles.container1}>
                    <TouchableRipple onPress={() => Actions.pop()}>
                        <Image source={backButton} style={styles.backButton} resizeMode={'contain'} />
                    </TouchableRipple>
                    <Text style={[styles.textHeader, fontStyle('black')]}>{TG_DETAILS}</Text>
                </View>
                {props?.whatsAppEnable &&
                    < TouchableRipple onPress={onwhatsAppSubmit} style={styles.whatsappIcon}>
                        <Image source={whatsapp} style={styles.whatsappIcon} />
                    </TouchableRipple>
                }
            </View>
        </View >)

    );
};

const styles = StyleSheet.create({
    container: {
        justifyContent: 'space-between',
        backgroundColor: colors.blue8,
        height: 60,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        borderColor: colors.cardBorderColor,
    },
    container1: { flexDirection: 'row', alignItems: 'center' },
    textHeader: {
        fontSize: 16,
        color: colors.white,
        lineHeight: 24,
    },
    whatsappIcon: {
        height: 25,
        width: 25,
        marginRight: 20,
    },
    backButton: {
        height: 24,
        width: 24,
        marginLeft: 15,
        marginRight: 10,
    },
});
export default TandCHeader;
