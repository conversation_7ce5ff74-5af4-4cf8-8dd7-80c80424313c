import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import React, { ReactElement } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';

interface BottomSheetModal {
  toggleModalState: () => void;
  children: ReactElement;
  extraContainerStyle?: object;
  shouldShowKeyboardAvoidingView?: boolean;
}

const TGSBottomSheet = ({
  toggleModalState,
  children,
  extraContainerStyle = {},
}: BottomSheetModal) => {

    return (
    <Modal
      animationType="slide"
      transparent
      visible={true}
      onRequestClose={toggleModalState}
      testID="tgs_bottomsheet_modal"
    >
      <View
        style={{
          position: 'absolute',
          bottom: 0,
          top: 0,
          right: 0,
          left: 0,
          flex: 1,
          backgroundColor: colors.black55,
          justifyContent: 'flex-end',
        }}
        testID="tgs_bottomsheet_background_wrapper"
      >
        <TouchableWithoutFeedback
          onPress={toggleModalState}
          testID="tgs_bottomsheet_background_wrapper_touchable"
          >
          <View style={styles.backgroundWrapper} />
        </TouchableWithoutFeedback>
        <View style={[styles.container, extraContainerStyle]} testID="tgs_bottomsheet_container">
          {children}
          </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: colors.black55,
  },
  backgroundWrapper: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
});

export default TGSBottomSheet;
