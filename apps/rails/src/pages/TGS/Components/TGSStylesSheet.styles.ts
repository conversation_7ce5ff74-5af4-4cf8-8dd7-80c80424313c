import {
    StyleSheet,
} from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { getScreenWidth } from 'apps/rails/src/pages/Review/AvailabilityStatusChange/AvailabilityChangeUtils';

export const tgsLandingPageStyles = StyleSheet.create({
    container:{height: 'auto'},
    buttonStyle: {
        marginHorizontal: 0,
    },
    tgBanner: {
        height: 300,
        width: '100%',
    },
    leftArrow: {
        position: 'absolute',
        left:'2%',
    },
    leftArrowContainer:{
        position: 'absolute',
        left:'2%',
        width:45,
        height:45,
        alignItems:'center',
        justifyContent:'center',
    },
    leftArrowContainerIos:{
    },
    leftArrowIos:{
        position:'absolute',
        left:'2%',
        width:45,
        height:45,
        alignItems:'center',
        justifyContent:'center',
    },
    gradientContainer: {
        paddingTop: 20,
        flex: 1,
        justifyContent: 'flex-start',
        paddingBottom: 10,
    },
    pnrGradientContainer: {
        padding: 10,
        flex: 1,
        marginBottom: 10,
        paddingBottom: 10,
        justifyContent: 'flex-start',
    },
    tgsWidgetCss: {
        flexBasis: '20%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    tgsWidetViewCss: {
        flexDirection: 'row',
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
    availabilityContainerStyle: {
        flexBasis: '50%',
        alignSelf: 'center',
    },
    flexDirectionRow: {
        flexDirection: 'row',
        marginBottom: 10,
        justifyContent: 'space-evenly',
    },
    tripGuranteeText: {
        fontWeight: '700',
        fontSize: 24,
        lineHeight: 28.75,
        color: colors.white,
    },
    waitListedTicket: {
        textAlign: 'center',
        fontSize: 18,
        color: colors.yellow8,
        marginBottom: 4,
    },
    textAlignCenter: {
        textAlign: 'center',
        fontSize: 14,
        color: colors.white,
        marginBottom: 10,
    },
    textAlignCenterAmount: {
        textAlign: 'center',
        fontSize: 14,
        color: colors.white,
        marginVertical: 5,
    },
    pnrContainer: {
        borderRadius: 10,
        backgroundColor: colors.white,
        marginLeft: 12,
        marginRight: 12,
        paddingTop: 20,
        paddingBottom: 16,
        paddingRight: 17,
        paddingLeft: 17,
    },
    pnrText: {
        fontSize: 18,
        color: colors.defaultTextColor,
        marginBottom: 12,
        textAlign: 'center',
    },
    inputText: {
        height: 32,
        borderRadius: 4,
        borderWidth: 1,
        borderStyle: 'solid',
        paddingLeft: 8,
        backgroundColor: colors.grayBg,
        marginBottom: 12,
        paddingVertical: 0,
    },
    tgsWidgetImage: {
        height: 54,
        width: 54,
    },
    pnrBottomSection: {
        marginRight: 10,
        marginLeft: 10,
        backgroundColor: colors.white,
        marginBottom: 10,
        borderRadius: 8,
    },
    pnrImage: {
        marginTop: -25,
        marginLeft: '4%',
        marginRight: '4%',
    },
    pnrScrollView: {
        marginTop: -25,
        marginLeft: '2.5%',
        marginRight: '2.5%',
    },
    pnrTestimonalFlow: {
        marginTop: -80,
        marginBottom: -25,
    },
    tgsView: {
        position: 'relative',
        top: -24,
    },
    animationGradientStyle:
    {
        padding: 10,
        paddingBottom: 20,
  },
  marB20: {
    marginBottom: 20,
    },
});

export const newPnrLandingStyles = StyleSheet.create({
    tgBanner: {
        height: 'auto',
        width: '92%',
        marginHorizontal: '4.44%',
    },
    gradientContainer: {
        paddingTop: 20,
        flex: 1,
        justifyContent: 'flex-start',
        backgroundColor: colors.white,
        paddingBottom: 5,
    },
    tgsWidetViewCss: {
        flexDirection: 'row',
        marginBottom: 12,
        alignItems: 'center',
        height: 56,
    },
    tripGuranteeText: {
        fontWeight: '700',
        fontSize: 16,
        lineHeight: 28.75,
        color: colors.black,
        marginLeft: '15.5%',
    },
    pnrContainer: {
        borderRadius: 16,
        borderColor: colors.lightSilver,
        borderWidth: 1,
        marginHorizontal: '4.44%',
        paddingVertical: 16,
        paddingHorizontal: 12,
    },
    pnrText: {
        fontSize: 16,
        color: colors.defaultTextColor,
        marginBottom: 12,
        textAlign: 'center',
    },
    inputText: {
        height: 44,
        borderStyle: 'solid',
        paddingTop: 12,
        textAlign: 'center',
    },
    pnrTgBannerContainer: {
        height: 'auto',
        backgroundColor: colors.green,
        borderRadius: 16,
        borderColor: colors.lightSilver,
    },
    pnrAdFeed1Container: {
        backgroundColor: colors.white,
        paddingVertical: 19,
    },
    pnrAdFeed2Container: {
        backgroundColor: colors.white,
        paddingVertical: 16,
    },
    adsSeparator: {
        marginTop: 16,
        width: '100%',
        height: 4,
        backgroundColor: colors.grayBg,
    },
    adsHeading: {
    fontSize: 18,
        color: colors.black,
        lineHeight: 20,
        letterSpacing: 0.23,
        marginBottom: 11,
        marginHorizontal: '4.44%',
    },
    marginHorizontal16: {
        marginHorizontal: '4.44%',
    },
    button: {
        height: 42,
        borderRadius: 8,
        justifyContent: 'center',
        marginHorizontal: 0,
    },
    tgsPnrView: {
        backgroundColor: colors.white,
        position: 'relative',
    },
    pnrSearchTgsPnrView: {
        backgroundColor: colors.white,
        position: 'relative',
        paddingVertical: 0,
    },
    tgBannerHeading: {
        display: 'flex',
        flexDirection: 'row',
        paddingVertical: 8,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        alignItems: 'center',
        backgroundColor: colors.lightBlue30,
    },
    recentSearchContainer: {
    },
    image: {
        height: 16,
        width: 16,
    },
    videoContainer: {
        zIndex: 9999,
        position: 'absolute',
        bottom: 0,
        marginHorizontal: '4.44%',
        marginBottom: 16,
        borderRadius:16,
        overflow: 'hidden',
    },
    whyBookWithUsText: {
        fontFamily: fonts.bold,
        marginLeft: 6,
        fontWeight: '900',
        fontSize: 14,
    },
    bookWithUsHeading: {
        paddingLeft: 10,
        marginLeft: 6,
        marginBottom:4,
        marginTop: 10,
        fontSize: 14,
        fontWeight: '900',
        fontFamily: fonts.bold,
        color: colors.black,
        lineHeight: 17,
    },
    feedbackContainer: {
        marginLeft: -10,
    },
  marB20: { marginBottom: 20 },
});

export const tgsCarouselStyles = StyleSheet.create({
    carousalHeight: {
        height: 127,
    },
    blackColor: {
        color: colors.black,
    },
    bxsOfferContainer: {
        flexDirection: 'row',
        marginBottom: 15,
    },
    container: {
        marginRight: 10,
        marginLeft: 10,
        backgroundColor: colors.white,
    },
    imageStyle: {
        height: 120,
        width: 232,
    },
    imageContainer: {
        paddingHorizontal: 6,
    },
    bxsOffer: {
        height: 20,
        width: 20,
        marginRight: 4,
    },
    flatList: {
        marginBottom: '5%',
    },
});

export const tgsVideoContainerStyles = StyleSheet.create({
    container: {
        height: 400,
        borderRadius: 8,
        paddingTop: 14,
        backgroundColor: colors.white,
        position: 'relative',
        top: -18,
        marginBottom: -220,
    },
    videoPlayerContainer: {
        marginRight: 14,
        marginLeft: 14,
    },
    videoScreen: {
        height: 211,
        borderRadius: 7,
        zIndex: 111,
    },
    gradientStyle: {
        paddingVertical: 7,
        borderRadius: 6,
        position: 'relative',
        top: -4,
    },
    playButton: {
        position: 'relative',
        bottom: '50%',
        left: '45%',
        zIndex: 411,
    },
    volumeIcon: {
        height: 20,
        width: 20,
        position: 'relative',
        bottom: '35%',
        left: '90%',
        zIndex: 411,
    },
});

export const tgsTestimonalFlow = StyleSheet.create({
    container: {
        marginRight: 10,
        marginLeft: 10,
        marginBottom: 15,
        marginTop: 58,
    },
    imageStyle: {
        height: 275,
    },
    carousel: {
        height: 180,
        marginTop: 80,
        marginLeft: 5,
        marginRight: 5,
    },
    imageStyleEng: {
        height: getScreenWidth() < 400 ? 150 : 163,
        width: getScreenWidth() < 400 ? 148 : 158,
        resizeMode: 'contain',
    },
    imageStyleHindi: {
        height: getScreenWidth() < 400 ? 150 : 163,
        width: getScreenWidth() < 400 ? 148 : 158,
        resizeMode: 'contain',
    },
    imageStyleTamil: {
        height: getScreenWidth() < 400 ? 150 : 163,
        width: getScreenWidth() < 400 ? 185 : 195,
    },
    imageContainer: {
        paddingHorizontal: 6,
    },
    bxsOffer: {
        height: 20,
        width: 20,
    },
    flatList: {
        marginTop: getScreenWidth() < 400 ? '28%' : '24%',
        marginRight: 5,
        marginLeft: 5,
    },
});

export const tgsErrorContainerStyles = StyleSheet.create({
    buttonStyle: {
        marginHorizontal: 0,
    },
    pnrContainer: {
        borderRadius: 10,
        backgroundColor: colors.white,
        borderWidth: 1,
        marginLeft: 12,
        marginRight: 12,
        paddingTop: 20,
        paddingBottom: 16,
        paddingRight: 17,
        paddingLeft: 17,
    },
});


export const tgsErrorContainerTextStyles = StyleSheet.create({
    q: {},
    p: {
        fontSize: 16,
        color: colors.defaultTextColor,
        marginBottom: 12,
    },
    d: {
        fontWeight: '500',
        fontSize: 18,
    },

});

export const tgsSearchButtonstyles = StyleSheet.create({
    disabledButton: {
        backgroundColor: colors.disabledBtnBg,
    },
    button: {
        marginHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 4,
        alignItems: 'center',
    },
    buttonWhatsApp: {
        marginHorizontal: 16,
        paddingVertical: 8,
        paddingBottom:12,
        borderRadius: 4,
        alignItems: 'center',
    },
    label: {
        color: colors.white,
        fontSize: 16,
    },
    labelWhatsApp: {
        color: colors.white,
        fontSize: 20,
        fontWeight:'800',
        fontFamily:'Lato-Bold',
    },
    flexDirectionWhatsApp:{
        flexDirection: 'row',
        justifyContent:'center',
        alignItems:'center',
    },
    loader: {
        alignItems: 'center',
        justifyContent: 'center',
        height: 7,
        padding: 10,
    },
    whatsAppimage: {
        height: 25,
        width: 25,
        marginLeft: 5,
    },
    flexDirection: {
        flexDirection: 'row',
    },
});

export const tgsPnrStyles = StyleSheet.create({
    pnrAnimation:{ paddingBottom: 20 },
    emptyPnrAnimation:{ paddingBottom: 20 },
    tgsPnrView: { backgroundColor: colors.grey11, position: 'relative'},
});
