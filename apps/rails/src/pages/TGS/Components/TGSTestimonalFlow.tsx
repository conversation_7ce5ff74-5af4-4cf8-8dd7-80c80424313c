import React from 'react';
import { View, Image, ImageBackground, ScrollView } from 'react-native';
import { tgsTestimonalFlow } from './TGSStylesSheet.styles';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

const tgsImage = ASSETS.testimonyComponent;
const testimonyEng = ASSETS.testimony1;
const testimonyHindi = ASSETS.testimony2;
const testimonyTamil = ASSETS.testimony3;

const TGSTestimonalFlow = () => {
    return (
        <View style={tgsTestimonalFlow.container}>
            <ImageBackground source={tgsImage} resizeMode={'contain'} style={tgsTestimonalFlow.imageStyle} >
                <ScrollView
                    horizontal
                    style={tgsTestimonalFlow.flatList}
                    showsHorizontalScrollIndicator={false}
                >
                    <View style={tgsTestimonalFlow.imageContainer}>
                        <Image source={testimonyEng} style={tgsTestimonalFlow.imageStyleEng} />
                    </View>
                    <View style={tgsTestimonalFlow.imageContainer}>
                        <Image source={testimonyHindi} style={tgsTestimonalFlow.imageStyleHindi} />
                    </View>
                    <View style={tgsTestimonalFlow.imageContainer}>
                        <Image source={testimonyTamil} style={tgsTestimonalFlow.imageStyleTamil} />
                    </View>

                </ScrollView>
            </ImageBackground>
        </View>);
};

export default TGSTestimonalFlow;
