import React, { useEffect, useState } from 'react';
import { BackHandler, Linking, Switch, Text, View } from 'react-native';
import Header from '@mmt/legacy-commons/Common/Components/Header/Header';
import { showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import {
  ENV_SWITCH,
  RAILS_API_ENV,
  RAILS_API_ENV_SELECTION,
  getDataFromAsynStorage,
  setDataToAsyncStorage,
} from 'apps/rails/src/Utils/RailsConstant';
import { Actions } from 'apps/rails/src/navigation';
import { RectangularGradientButton } from 'apps/rails/src/pages/Common/Buttons';
import EnvOption from 'apps/rails/src/pages/EnvSwitchPage/EnvOption';
import { EndPoint } from 'apps/rails/src/pages/EnvSwitchPage/types';
import { colors } from 'packages/legacy-commons/Styles/globalStyles';
import RailsEnvVariables from '../../Utils/RailsEnvVariables';
import { styles } from './styles';

const RailsEnvSwitchPage = () => {
  const [selectedPWAEndPoint, setPWAEndPoint] = useState<EndPoint>(
    RailsEnvVariables.PWA_ENDPOINTS[0],
  );
  const [selectedNodeEndPoint, setNodeEndPoint] = useState<EndPoint>(
    RailsEnvVariables.NODE_ENDPOINTS[0],
  );
  const [selectedCustomPWAEndPoint, setCustomPWAEndPoint] = useState<string | null>(null);
  const [selectedCustomNodeEndPoint, setCustomNodeEndPoint] = useState<string | null>(null);
  const [showCustomEndpoint, setShowCustomEndpoint] = useState(false);
  const [disableButton, setDisableButton] = useState(false);

  useEffect(() => {
    const setSelectedAPIEnv = async () => {
      const isCustomEndpointSelected = await getDataFromAsynStorage(RAILS_API_ENV_SELECTION);
      setShowCustomEndpoint(isCustomEndpointSelected ?? false);
      const selectedAPI = await getDataFromAsynStorage(RAILS_API_ENV);
      if (isCustomEndpointSelected) {
        const { pwa, node } = selectedAPI || {};
        if (pwa) {
          setCustomPWAEndPoint(pwa);
          RailsEnvVariables.setPWABaseUrl(pwa);
        }
        if (node) {
          setCustomNodeEndPoint(node);
          RailsEnvVariables.setNodeBaseUrl(node);
        }
      } else {
        const selectedPWA = RailsEnvVariables.PWA_ENDPOINTS.find(
          (endpoint) => endpoint.value === selectedAPI.pwa,
        );
        selectedPWA && setPWAEndPoint(selectedPWA);
        const selectedNode = RailsEnvVariables.NODE_ENDPOINTS.find(
          (endpoint) => endpoint.value === selectedAPI.node,
        );
        selectedNode && setNodeEndPoint(selectedNode);
        selectedPWA && RailsEnvVariables.setPWABaseUrl(selectedPWA.value);
        selectedNode && RailsEnvVariables.setNodeBaseUrl(selectedNode.value);
      }
    };
    setSelectedAPIEnv();
  }, []);

  const saveEndPoints = () => {
    let pwaEndPoint = showCustomEndpoint ? selectedCustomPWAEndPoint : selectedPWAEndPoint.value;
    let nodeEndPoint = showCustomEndpoint ? selectedCustomNodeEndPoint : selectedNodeEndPoint.value;

    if (!pwaEndPoint) {
      pwaEndPoint = RailsEnvVariables.PWA_ENDPOINTS[0].value;
    }
    if (!nodeEndPoint) {
      nodeEndPoint = RailsEnvVariables.NODE_ENDPOINTS[0].value;
    }
    const envObj = {
      pwa: pwaEndPoint,
      node: nodeEndPoint,
    };
    setDataToAsyncStorage(RAILS_API_ENV, envObj);
    setDataToAsyncStorage(RAILS_API_ENV_SELECTION, showCustomEndpoint);
    RailsEnvVariables.setPWABaseUrl(pwaEndPoint);
    RailsEnvVariables.setNodeBaseUrl(nodeEndPoint);
    showLongToast(`Set ${pwaEndPoint} and ${nodeEndPoint}`);
    BackHandler.exitApp();
    Linking.openURL('mmyt://rails/?page=railsBusCommonLanding');
  };

  const toggleSwitch = () => {
    setShowCustomEndpoint((previousState) => !previousState);
  };

  const onError = (disable: boolean) => setDisableButton(disable);

  return (
    <View style={styles.container}>
      <Header
        title={ENV_SWITCH.TITLE}
        onBackPress={() => {
          Actions.pop();
        }}
        type="Elevated"
      />
      <View style={styles.switcherRow}>
        <Text style={styles.switcherText}>{ENV_SWITCH.ENTER_IP}</Text>
        <Switch
          trackColor={{ false: colors.lightGrey7, true: colors.azureSemiTrans }}
          thumbColor={showCustomEndpoint ? colors.primary : colors.grey10}
          onValueChange={toggleSwitch}
          value={showCustomEndpoint}
        />
      </View>
      <View style={styles.switchContainer}>
        <EnvOption
          label={ENV_SWITCH.SELECT_PWA}
          inputPlaceholder={ENV_SWITCH.PLACEHOLDER}
          setEndPoint={setPWAEndPoint}
          setCustomEndPoint={setCustomPWAEndPoint}
          data={RailsEnvVariables.PWA_ENDPOINTS}
          selectedEndpointLabel={selectedPWAEndPoint.label}
          selectedCustomEndPoint={selectedCustomPWAEndPoint}
          showCustomEndpoint={showCustomEndpoint}
          onError={onError}
        />
        <EnvOption
          label={ENV_SWITCH.SELECT_NODE}
          inputPlaceholder={ENV_SWITCH.PLACEHOLDER}
          setEndPoint={setNodeEndPoint}
          setCustomEndPoint={setCustomNodeEndPoint}
          data={RailsEnvVariables.NODE_ENDPOINTS}
          selectedEndpointLabel={selectedNodeEndPoint.label}
          selectedCustomEndPoint={selectedCustomNodeEndPoint}
          showCustomEndpoint={showCustomEndpoint}
          onError={onError}
        />
        <RectangularGradientButton
          gradientStyle={styles.gradientStyle}
          label={ENV_SWITCH.APPLY}
          onPress={saveEndPoints}
          disabled={!showCustomEndpoint ? showCustomEndpoint : disableButton}
        />
        <Text style={styles.noteText}>{ENV_SWITCH.WIFI_NOTE}</Text>
      </View>
    </View>
  );
};

export default RailsEnvSwitchPage;
