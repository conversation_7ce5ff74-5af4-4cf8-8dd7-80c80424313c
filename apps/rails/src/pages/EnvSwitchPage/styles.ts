import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  switchContainer: {
    marginHorizontal: 24,
  },
  label: {
    color: colors.black,
    fontSize: 14,
    fontWeight: '400',
    paddingTop: 24,
    paddingBottom: 10,
  },
  gradientStyle: {
    height: 46,
    marginTop: 24,
  },
  switcherText: {
    color: colors.black,
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
  },
  noteText: {
    color: colors.defaultTextColor,
    fontSize: 12,
    fontWeight: '400',
    marginTop: 34,
  },
  textInputContainer: {
    borderRadius: 8,
    borderColor: colors.lightGrey,
    borderWidth: 1,
    padding: 5,
    height: 50,
    marginBottom: 0,
  },
  textInputText: {
    color: colors.black,
    fontSize: 14,
  },
  switcherRow: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginTop: 8,
    paddingVertical: 8,
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  errorLabel: {
    marginTop: 5,
    marginBottom: 0,
  },
});
