import React, { useState } from 'react';
import { Text, View } from 'react-native';
import SimpleTextInput from '@mmt/legacy-commons/Common/Components/Inputs/SimpleTextInput';
import { ENV_SWITCH } from 'apps/rails/src/Utils/RailsConstant';
import RailsSingleSelectDropDown from 'apps/rails/src/pages/Common/RailsSingleSelectDropDown';
import { isIpv4 } from 'apps/rails/src/pages/EnvSwitchPage/utils';
import { styles } from './styles';

interface Props {
  data: { label: string; value: string }[];
  showCustomEndpoint: boolean;
  label: string;
  selectedEndpointLabel: string;
  selectedCustomEndPoint: string | null;
  inputPlaceholder: string;
  setEndPoint: (item: { label: string; value: string }) => void;
  setCustomEndPoint(text: string): unknown;
  onError(disable: boolean): unknown;
}

const EnvOption = (props: Props) => {
  const [endpointError, setEndPointError] = useState<string | null>(null);
  const onTextChange = (text: string): void => {
    props.setCustomEndPoint(text);
    if (isIpv4(text) || !text) {
      setEndPointError(null);
      props.onError(false);
    } else {
      setEndPointError(ENV_SWITCH.ENTER_VALID_IP);
      props.onError(true);
    }
  };

  return (
    <View>
      <Text style={styles.label}>{props.label}</Text>
      {!props.showCustomEndpoint ? (
        <RailsSingleSelectDropDown
          showValue
          data={props.data}
          label={props.selectedEndpointLabel}
          onSelect={props.setEndPoint}
        />
      ) : (
        <SimpleTextInput
          maxLength={300}
          multiline
          value={props.selectedCustomEndPoint}
          inputStyle={styles.textInputContainer}
          placeholder={props.inputPlaceholder}
          onChangeText={onTextChange}
          numberOfLines={1}
          keyboardType={'numbers-and-punctuation'}
          error={endpointError}
          errorContainerStyle={styles.errorLabel}
        />
      )}
    </View>
  );
};

export default EnvOption;
