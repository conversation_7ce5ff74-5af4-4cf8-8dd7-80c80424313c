import {View, Image, Text, ScrollView, StyleSheet} from 'react-native';
import React from 'react';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import { Actions } from '../../navigation';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {trackRailsCreateNewAccountSuccessPageLoad} from '../../railsAnalytics';
import {STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING} from '../../Utils/UserRepository';
import {_label} from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import MultiStyleVernacText from '../../vernacular/MultiStyleVernacText';
import ASSETS from '../../Utils/Assets/RailsAssets';
import closeIcon from '@mmt/legacy-assets/src/ic-headerclose-grey.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
export default class RailsCreateAccountSuccess extends React.Component {
  constructor(props) {
    super(props);
    if (!this.props.userName) {
      Actions.pop();
    }
    trackRailsCreateNewAccountSuccessPageLoad();
  }

  onVerifyClicked = async () => {
    Actions.railsOtpVerification({
      verify: STATUS_MOBILE_EMAIL_OTP_VERIFICATION_PENDING,
      focus: 'email',
      userName: this.props.userName,
      otpType: 'B',
      from: this.props.from,
      probableTo: 'travelers',
      nextPageProps: this.props.nextPageProps,
      type: 'replace',
    });
  };

  render() {
    return (
      <ScrollView>
        <View style={styles.closeIconContainer}>
          <TouchableRipple onPress={() => { Actions.pop(); }}>
            <Image style={styles.closeIcon} source={closeIcon} />
          </TouchableRipple>
        </View>
        <View style={{width:'90%',marginRight:'auto',marginLeft:'auto'}}>
          <MultiStyleVernacText
              content = {_label('irctc_account_created')}
              params = {{
                username : this.props.userName,
              }}
              defaultContentStyle = {styles.accountCreatedText}
              textStyles = {{
                username : [styles.userNameText, fontStyle('black'), getLineHeight(16)],
              }}
          />
        </View>
        <View style={styles.tickImageContainer}>
          <Image style={styles.tickImage} source={ASSETS.icFpTick} />
        </View>
        <View style={styles.bottomTextContainer}>
          <Text style={[styles.viewTrainsText, fontStyle('bold'), getLineHeight(12)]}>
            {_label('irctc_account_verify_persuade')}
          </Text>
          <Text style={[styles.contactText, fontStyle('black'), getLineHeight(24)]}>
            {_label('irctc_account_verify_steps')}
          </Text>
        </View>
        <TouchableRipple onPress={this.onVerifyClicked}>
          <View style={{marginTop: 40}}>
            <LinearGradient
              style={styles.linearGrad}
              colors={['#53B2FE', '#065AF3']}
              start={{x: 0.0, y: 0.0}}
              end={{x: 1.0, y: 0.0}}
            >
              <View >
                <Text style={[styles.verifyNowText, fontStyle('bold'), getLineHeight(16)]}>{_label('verify_now',{uppercase : true})}</Text>
              </View>
            </LinearGradient>
          </View>
        </TouchableRipple>
      </ScrollView>
    );
  }
}

RailsCreateAccountSuccess.propTypes = {
  userName: PropTypes.string.isRequired,
  from: PropTypes.string,
  probableTo: PropTypes.string,
  nextPageProps: PropTypes.object,
};

RailsCreateAccountSuccess.defaultProps = {
  from: null,
  probableTo: null,
};

const styles = StyleSheet.create({
  closeIconContainer: {
    flex: 1, flexDirection: 'row', marginLeft: 16, marginVertical: 32, alignItems: 'center',
  },
  closeIcon: {width: 24, height: 24},
  accountCreatedText: {
    fontSize: 16,  fontWeight: 'bold', color: colors.black,textAlign:'center',
  },
  userNameText: {
    fontSize: 16, color: colors.black, fontWeight: 'bold',textAlign:'center',
  },
  tickImageContainer: {marginVertical: 64, alignItems: 'center', justifyContent: 'center'},
  tickImage: {width: 102, height: 102},
  bottomTextContainer: {alignItems: 'center', justifyContent: 'center'},
  viewTrainsText: {
    fontSize: 12,  color: colors.defaultTextColor, fontWeight: 'bold',
  },
  contactText: {
    fontSize: 24,  color: colors.black, fontWeight: 'bold', marginVertical: 4,textAlign:'center',width:'90%',
  },
  linearGrad: {
    borderRadius: 96,
    height: 54,
    marginHorizontal: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verifyNowText: {
    color: colors.white, fontSize: 16, fontWeight: 'bold',
  },
});
