import React, {Component} from 'react';
import {
  Image,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { Actions } from '../../navigation';
import sumBy from 'lodash/sumBy';
import isEmpty from 'lodash/isEmpty';
import CheckBox from 'react-native-checkbox';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import SimpleHeaderForPopup from '@mmt/legacy-commons/Common/Components/Header/SimpleHeaderForPopup';
import {RailsDropDownOverlayContainer} from './Components/IrctcFormDropDown';
import AddressInfo from './Components/IrctcAddressInfo';
import {
  fillCreateAccountData,
  onCreateAccountClick,
  toggleOfficeCheckBox,
  captchaAndSessionId,
} from '../RailsLandingPage/Store/RailsLandingPageActions';
import OfficeAddressInfo from './Components/IrctcOfficeAddressInfo';
import PersonalInfo from './Components/IrctcPersonalInfo';
import AccountDetails from './Components/IrctcAccountDetails';
import {trackRailsCreateNewAccountPageLoad} from '../../railsAnalytics';
import {_label} from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';
import { ErrorContainer } from 'apps/rails/src/pages/RailsLandingPage/Pages/ErrorContainer';
import Captcha from './Components/Captcha';
import BookNowTravellers from 'apps/rails/src/pages/TravelerDetails/BookNowTravellers';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { PAGE_NOT_LOADING } from '../../Utils/RailsConstant';

import CB_ENABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-active.webp';
import CB_DISABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-inactive.webp';
import formImage from '@mmt/legacy-assets/src/form-createnewaccount.webp';

export class KeyboardAvoidingViewWrapper extends Component {
  render() {
    if (isIos()) {
      return (
        <KeyboardAvoidingView style={{flex: 1}} behavior="padding">
          {this.props.children}
        </KeyboardAvoidingView>);
    }
    return (
      <KeyboardAvoidingView style={{flex: 1}}>
        {this.props.children}
      </KeyboardAvoidingView>
    );
  }
}

KeyboardAvoidingViewWrapper.propTypes = {
  children: PropTypes.element.isRequired,
};

const LinearGradientHeader = ({formPercentage}) => (
  <View style={styles.formHeaderContainer}>
    <LinearGradient
      colors={['#dafef4', '#d5ebf3']}
      start={{x: 0.0, y: 0.0}}
      end={{x: 1.0, y: 0.0}}
    >
      <View style={lgStyles.container}>
        <View style={lgStyles.imageContainer}>
          <Image source={formImage} style={lgStyles.image} />
        </View>
        <View style={lgStyles.textContainer}>
          <Text style={[lgStyles.textHeader, fontStyle('black'), getLineHeight(16)]}>
            { _label('form_pecentage_already_filled',
                undefined,{
                        amountFilled : formPercentage,
            })}
          </Text>
        </View>
      </View>
    </LinearGradient>
  </View>
);

const lgStyles = StyleSheet.create({
  container: {
    margin: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageContainer: {
    width: 50,
    height: 50,
  },
  image: {width: 48, height: 48},
  textContainer: {marginLeft: 20, justifyContent: 'center'},
  textHeader: {
    fontSize: 16,  color: colors.black, backgroundColor: colors.transparent,
  },
});

LinearGradientHeader.propTypes = {
  formPercentage: PropTypes.number.isRequired,
  formFieldsRemaining: PropTypes.number.isRequired,
};


class IrctcNewAccoutCreate extends React.Component {
  constructor(props) {
    super(props);
    if (!this.props.from) {
      Actions.pop();
    }
    this.state = {
      securityQuestionList: [],
      occupationList: [],
    };
    trackRailsCreateNewAccountPageLoad();
  }

  async UNSAFE_componentWillMount () {
    this.props.captchaAndSessionId();
    this.props.fillCreateAccountData();
  }

  _onCreateNewAccountClick = () => {
    this.props.onCreateAccountClick(this.props);
  };

  render() {
    const {securityQuestionList, occupationList, userDataLoader, captchaDowntimeError} = this.props;

    if (captchaDowntimeError) {
      /* eslint-disable */
      let customizedErrorResponse = { ...errorResponse };
      customizedErrorResponse.errorDetails.errorMessage = captchaDowntimeError;
      return (
        <ErrorContainer
          errorResponsefromAPI={customizedErrorResponse}
          errorImg={PAGE_NOT_LOADING}
          ctaPressed={() => Actions.pop()}
        />
      );
    }

    return (
      <RailsDropDownOverlayContainer style={{flex: 1}}>
        <SimpleHeaderForPopup
          size = "normal"
          title = {_label('create_new_account',{capitalize : true})}
          onPressCloseHandler = {() => { Actions.pop(); }}
          backImage = "true"
        />
        <LinearGradientHeader
          formPercentage={this.props.formPercentage}
          formFieldsRemaining={this.props.formFieldsRemaining}
        />
        <KeyboardAvoidingViewWrapper>
          <ScrollView keyboardShouldPersistTaps="handled" ref={(ref) => { this._scrollView = ref; }}>
            <View style={styles.sectionHeader}>
              <AccountDetails securityQuestionList={securityQuestionList} />
            </View>

            <View style={styles.divider} />

            <View style={styles.sectionHeader}>
              <PersonalInfo occupationList={occupationList} />
            </View>

            <View style={styles.divider} />

            <View style={styles.sectionHeader}>
              <AddressInfo />
            </View>

            <View style={styles.divider} />

            <View style={styles.sectionHeader}>
              <Captcha />
            </View>
            <View style={styles.divider} />

            <View style={styles.checkBoxHeader}>
              <CheckBox
                ref = {(ref) => { this._checkSomeElse = ref; }}
                label = {_label('copy_address')}
                checked = {this.props.officeCheckBoxEnabled}
                labelStyle = {{fontSize: 12}}
                onChange = {this.props.toggleOfficeCheckBox}
                checkedImage = {CB_ENABLED_IMAGE}
                uncheckedImage = {CB_DISABLED_IMAGE}
                containerStyle = {{marginBottom: 20}}
              />
            </View>
            {
              !this.props.officeCheckBoxEnabled &&
              <View style={styles.officeAddressContainer}>
                <OfficeAddressInfo />
              </View>
            }
            <BookNowTravellers
              BookNowButtonEnabled={true}
              onBookNowClicked={() => this._onCreateNewAccountClick()}
              buttonText={_label('create_account',{uppercase: true})}
              showLoader={userDataLoader}
            />
          </ScrollView>
        </KeyboardAvoidingViewWrapper>
      </RailsDropDownOverlayContainer>
    );
  }
}

IrctcNewAccoutCreate.propTypes = {
  formPercentage: PropTypes.number.isRequired,
  formFieldsRemaining: PropTypes.number.isRequired,
  toggleOfficeCheckBox: PropTypes.func.isRequired,
  onCreateAccountClick: PropTypes.func.isRequired,
  officeCheckBoxEnabled: PropTypes.bool.isRequired,
  from: PropTypes.string,
  probableTo: PropTypes.string,
  captchaAndSessionId: PropTypes.func,
  fillCreateAccountData: PropTypes.func,
  securityQuestionList: PropTypes.array,
  occupationList: PropTypes.array,
  userDataLoader: PropTypes.bool,
  captchaDowntimeError: PropTypes.string,
};

IrctcNewAccoutCreate.defaultProps = {
  from: null,
  probableTo: null,
};

const mapStateToProps = (state) => {
  const {railsLanding: {userAccount, securityQuestionList, occupationList}} = state;
  const { officeCheckBoxEnabled, userDataLoader,captchaDowntimeError } = userAccount;
  const officeFields = ['enterOfficeAddress','enterOfficeMobileNumber','enterOfficePinCode','selectOfficeCity','selectOfficeState','selectOfficePostOffice','officeCountry'];
  const userAccountKeyList = Object.keys(userAccount);
  const totalFields = officeCheckBoxEnabled ? 21 : 28;
  const noOfFilledFields = sumBy(userAccountKeyList, (item) => {
    if (officeCheckBoxEnabled && officeFields.includes(item)){
      return 0;
    }
    if (isEmpty(userAccount[item]) || isEmpty(userAccount[item].text)) {
      return 0;
    }
    return 1;
  });
  const formPercentage = Math.min(100,
    Math.floor(((noOfFilledFields > 22 && officeCheckBoxEnabled ? noOfFilledFields - 7 : noOfFilledFields)
    / totalFields) * 100));
  const formFieldsRemaining = Math.max(0,(totalFields - noOfFilledFields));
  return {
    captchaDowntimeError,
    userDataLoader,
    officeCheckBoxEnabled,
    formPercentage,
    formFieldsRemaining,
    securityQuestionList,
    occupationList,
  };
};

const mapDispatchToProps = dispatch => ({
  toggleOfficeCheckBox: (state) => { dispatch(toggleOfficeCheckBox(state)); },
  onCreateAccountClick: (props) => { dispatch(onCreateAccountClick(props)); },
  fillCreateAccountData: () => { dispatch(fillCreateAccountData); },
  captchaAndSessionId: () => {dispatch(captchaAndSessionId); },
});

export default connect(mapStateToProps, mapDispatchToProps)(IrctcNewAccoutCreate);

const styles = StyleSheet.create({
  sectionHeader: {
    marginVertical: 16,
    paddingHorizontal: 16,
  },
  divider: {
    height: 20,
    backgroundColor: colors.grayBg,
  },
  checkBoxHeader: {
    paddingHorizontal: 16,
    marginTop: 16,
  },
  formHeaderContainer: {
    height: 85,
  },
  officeAddressContainer: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
});
