import React from 'react';
import isEmpty from 'lodash/isEmpty';
import {connect} from 'react-redux';
import {Text, View} from 'react-native';
import fecha from 'fecha';
import getFormObject from '../IrctcFormObject';
import {addTextData} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import {today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {_label} from '../../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';

const minDate = today();
minDate.setFullYear(minDate.getFullYear() - 100);
const maxDate = today();
maxDate.setFullYear(maxDate.getFullYear() - 18);

const maxDate1 = today();
maxDate.setFullYear(maxDate.getFullYear() - 10);

class CalendarWrapper extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      error: false,
      errorMessage: '',
      dob: null,
    };
  }

  UNSAFE_componentWillMount () {
    let dob;
    let error;
    let errorMessage;
    const {fieldObject} = this.props;
    if (isEmpty(fieldObject) || isEmpty(fieldObject.text)) {
      dob = '';
    } else {
      dob = this.props.fieldObject.text;
    }
    if (!isEmpty(fieldObject) && fieldObject.error === true) {
      error = true;
      errorMessage = fieldObject.errorMessage;
    } else {
      error = false;
      errorMessage = '';
    }
    this.setState({dob, error, errorMessage});
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const {fieldObject} = nextProps;
    if (!isEmpty(fieldObject)) {
      if (fieldObject.error === true) {
        this.setState({error: true, errorMessage: fieldObject.errorMessage});
      } else {
        this.setState({error: false, errorMessage: ''});
      }
    }
  }

  onBlur = () => {
    let errorMessage = null;
    let error: false;
    const date = this._dateRef.value;
    const dateObject = fecha.parse(date, 'YYYY-MM-DD');
    if (dateObject < minDate || dateObject > maxDate) {
      error = true;
      errorMessage = _label('invalid_dob',
                        undefined,{
                                minDate : fecha.format(minDate, 'DD MMM YYYY'),
                                maxDate : fecha.format(maxDate, 'DD MMM YYYY'),
                      });
    }
    this.setState({dob: fecha.format(dateObject, 'DD/MM/YYYY'), error, errorMessage});
    this.props.addTextData({
      currentObject: this.props.currentObject,
      selectedValue: fecha.format(dateObject, 'DD/MM/YYYY'),
    });
  };

  render() {
    return (
      <div>
        <p style={{
color: colors.defaultTextColor, fontSize: 14,  ...fontStyle('bold'), marginBottom: 12, ... getLineHeight(14),
}}
        >{_label('date_of_birth')}
        </p>
        <input
          type = "date"
          min = {fecha.format(minDate, 'YYYY-MM-DD')}
          max = {fecha.format(maxDate1, 'YYYY-MM-DD')}
          onBlur = {this.onBlur}
          ref = {ref => this._dateRef = ref}
          style = {{
            backgroundColor: colors.transparent,
            borderWidth: 1,
            borderColor: colors.azure,
            borderRadius: 6,
            width: '90%',
            padding: 12,
            marginBottom: 12,
          }}
        />
        {
          this.state.error &&
          <View style={{marginTop: 8}}>
            <Text style={{color: colors.red, fontSize: 12, ...fontStyle('regular'), ...getLineHeight(12)}}>
              {this.state.errorMessage}
            </Text>
          </View>
        }
      </div>
    );
  }
}

const mapStateToProps = (state, ownProps) => {
  const {id} = ownProps;
  const currentObject = getFormObject()[id];
  const {railsLanding: {userAccount}} = state;
  const fieldObject = userAccount[id];
  return {
    ...ownProps,
    currentObject,
    fieldObject,
  };
};

const mapDispatchToProps = dispatch => ({
  addTextData: (data) => { dispatch(addTextData(data)); },
});

export default connect(mapStateToProps, mapDispatchToProps)(CalendarWrapper);

CalendarWrapper.propTypes = {
  fieldObject: PropTypes.shape({
    text: PropTypes.string,
    error: PropTypes.bool,
    errorMessage: PropTypes.string,
  }),
  currentObject: PropTypes.object,
  addTextData: PropTypes.func,
};
