import React from 'react';
import isEmpty from 'lodash/isEmpty';
import isUndefined from 'lodash/isUndefined';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {Image, Keyboard, StyleSheet, Text, TextInput, View} from 'react-native';
import getFormObject from '../IrctcFormObject';
import {addTextData, setCaptchaToNull} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import {fontStyle,getLineHeight} from '../../../vernacular/VernacularUtils';
import { getDataFromAsynStorage, CREATE_ACCOUNT_CLICK } from 'apps/rails/src/Utils/RailsConstant';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

import icGreenTick from '@mmt/legacy-assets/src/green_tick.webp';
import icGrayTick from '@mmt/legacy-assets/src/ic-status-tickgraylarge.webp';
class CustomTextField extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      text: '',
      showList: false,
      validList: null,
      error: false,
      errorMessage: '',
    };
  }

  UNSAFE_componentWillMount() {
    let text;
    let error;
    let errorMessage;
    const {
      currentObject: { validationList },
      fieldObject,
    } = this.props;
    if (isEmpty(fieldObject) || isEmpty(fieldObject.text)) {
      text = '';
    } else {
      text = this.props.fieldObject.text;
    }
    if (!isEmpty(fieldObject) && fieldObject.error === true) {
      error = true;
      errorMessage = fieldObject.errorMessage;
    } else {
      error = false;
      errorMessage = '';
    }
    const validList = !isEmpty(validationList)
      ? validationList.map((item) => item.validationFunction(this.state.text))
      : [];
    this.setState({
      validList,
      text,
      error,
      errorMessage,
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { fieldObject } = nextProps;
    let text;
    if (isEmpty(fieldObject) || isEmpty(fieldObject.text)) {
      text = '';
    } else {
      text = fieldObject.text;
    }
    if (!isEmpty(fieldObject)) {
      if (fieldObject.error) {
        this.setState({ text, error: true, errorMessage: fieldObject.errorMessage });
      }
    }
  }

  async componentDidUpdate(prevProps) {
    if (prevProps.emptyText !== this.props.emptyText) {
      this.setState({ text: '', error: false, errorMessage: '' });
      this.props.setCaptchaToNull();
    }
    const createAccountClick = await getDataFromAsynStorage(CREATE_ACCOUNT_CLICK);
    if (prevProps.currentObject !== this.props.currentObject && createAccountClick) {
      let text;
      let error;
      let errorMessage;
      const {
        currentObject: { validationList },
        fieldObject,
      } = this.props;
      text = this.state.text;

      if (!isEmpty(fieldObject) && fieldObject.error === true) {
        error = true;
        errorMessage = fieldObject.errorMessage;
      } else {
        error = false;
        errorMessage = '';
      }
      const errorHandling = [];
      const validList = !isEmpty(validationList)
        ? validationList.map((item) => {
            if (!item.validationFunction(this.state.text)) {
              errorHandling.push({
                error: item.validationFunction(this.state.text),
                errorDescription: item.description,
              });
            }
            return item.validationFunction(this.state.text);
          })
        : [];
      if (errorHandling.length !== 0) {
        this.setState({
          validList,
          text,
          error: true,
          errorMessage: errorHandling[0].errorDescription,
        });
      } else {
        this.setState({
          validList,
          text,
          error,
          errorMessage,
        });
      }
    }
  }

  _handleInputChange = (text) => {
    const currentObject = this.props.currentObject;
    const { validationList } = currentObject;
    const validList = !isEmpty(validationList) ?
      validationList.map(item => item.validationFunction(text)) : [];
    this.setState({text, validList});
    if (currentObject.id === 'enterCaptcha' || currentObject.id === 'enterMobileNumber') {
        this.props.addTextData({
          currentObject: this.props.currentObject,
          selectedValue: text,
        });
     }
  };

  render() {
    const {
      headerText, validationList, keyboardType, maxLength,
    } = this.props.currentObject;
    return (
      <View style={{marginBottom: 16}}>
        <Text style={[styles.headerText, fontStyle('bold'), getLineHeight(14)]}>
          {headerText}
        </Text>
        <View style={this.state.error ? styles.errorContainer : styles.validContainer}>
          <View style={{flexDirection: 'row', flex: 1}}>
            <View style={{flex: 1}}>
              <TextInput
                ref={(ref) => { this._ref = ref; }}
                underlineColorAndroid="transparent"
                style={{padding: 8}}
                maxLength={maxLength}
                onChangeText={(text) => {this._handleInputChange(text);}}
                onFocus={() => {
              let showList = false;
              if (!isEmpty(validationList)) {
                showList = true;
              }
              this.setState({showList, error: false});
            }}
                onBlur={() => {
                  if (this.props.currentObject.id === 'enterEmail') {
                    this.props.addTextData({
                      currentObject: this.props.currentObject,
                      selectedValue: this.state.text.toLowerCase(),
                    });
                  } else {
                    this.props.addTextData({
                      currentObject: this.props.currentObject,
                      selectedValue: this.state.text,
                    });
                  }
              if (!isEmpty(validationList)) {
                this.setState({showList: false});
              }
              if (!isUndefined(this.props.onBlur)) {
                this.props.onBlur(this.state.text, this.props.currentObject);
              }
            }}
                value={this.state.text}
                keyboardType={keyboardType}
                returnKeyType={keyboardType === 'numeric' ? 'done' : 'next'}
                keyboardShouldPersistTaps="handled"
                autoCapitalize="none"
                onSubmitEditing={() => {
              Keyboard.dismiss();
            }}
              />
            </View>
            <View style={{height: 40, width: 2}} />
          </View>
        </View>
        <View style={{marginTop: 8}}>
          {
            this.state.error &&
            <Text style={[styles.errorMessageText, fontStyle('regular'), getLineHeight(12)]}>
              {this.state.errorMessage}
            </Text>
          }
          { this.state.showList && !isEmpty(validationList) &&
          validationList.map((item, index) => (
            <View key={index} style={styles.imageContainer}>
              {
                this.state.validList[index] ?
                  <Image
                    style={styles.greenImage}
                    source={icGreenTick}
                  /> :
                  <Image
                    style={styles.greyImage}
                    source={icGrayTick}
                  />
              }
              <Text style={[styles.descriptionText, fontStyle('regular'), getLineHeight(14)]}>
                {item.description}
              </Text>
            </View>
          ))
          }
        </View>

      </View>
    );
  }
}

CustomTextField.propTypes = {
  fieldObject: PropTypes.shape({
    text: PropTypes.string,
    error: PropTypes.bool,
    errorMessage: PropTypes.string,
  }),
  currentObject: PropTypes.shape({
    id: PropTypes.string.isRequired,
    headerText: PropTypes.string.isRequired,
    nextId: PropTypes.string.isRequired,
    keyboardType: PropTypes.string.isRequired,
    errorMessage: PropTypes.shape({
      emptyErrorMessage: PropTypes.string.isRequired,
      allNotValidErrorMessage: PropTypes.string,
    }),
    maxLength: PropTypes.number.isRequired,
    validationList: PropTypes.arrayOf(PropTypes.shape({
      description: PropTypes.string.isRequired,
      validationFunction: PropTypes.func.isRequired,
    })),
  }),
  addTextData: PropTypes.func.isRequired,
  emptyText: PropTypes.bool,
  setCaptchaToNull: PropTypes.func,
  onBlur: PropTypes.func,
};

CustomTextField.defaultProps = {
  fieldObject: null,
  currentObject: {
    validationList: null,
    errorMessage: {
      allNotValidErrorMessage: '',
    },
  },
  onBlur: undefined,
};

const styles = StyleSheet.create({
  headerText: {
    color: colors.defaultTextColor, fontSize: 14, fontWeight: 'bold',marginBottom: 12,
  },
  errorContainer: {
    borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.red,
  },
  validContainer: {
    borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.azure,
  },
  errorMessageText: {color: colors.red, fontSize: 12},
  imageContainer: {flexDirection: 'row', alignItems: 'center', marginBottom: 10},
  greenImage: {
    height: 20,
    width: 20,
    marginRight: 10,
  },
  greyImage: {
    height: 24,
    width: 24,
    marginRight: 10,
  },
  descriptionText: {
    fontSize: 14, color: colors.lightTextColor, flex: 1,
  },
});

const mapStateToProps = (state, ownProps) => {
  const {id} = ownProps;
  const currentObject = getFormObject()[id];
  const {railsLanding: {userAccount}} = state;
  const fieldObject = userAccount[id];
  return {
    ...ownProps,
    currentObject,
    fieldObject,
  };
};

const mapDispatchToProps = (dispatch) => ({
  addTextData: (data) => {
    dispatch(addTextData(data));
  },
  setCaptchaToNull: () => {
    dispatch(setCaptchaToNull());
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(CustomTextField);
