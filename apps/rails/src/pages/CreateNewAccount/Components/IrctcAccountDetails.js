import React from 'react';
import PropTypes from 'prop-types';
import {Image, Text, View, StyleSheet} from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import CustomTextField from './IrctcFormInput';
import PasswordTextField from './IrctcPasswordInput';
import DropDownTextFieldNew from './IrctcDropdownWrapper';
import getFormObject from '../IrctcFormObject';
import {_label} from '../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';

export default class AccountDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      itemsVisibility: true,
    };
  }

  render() {
    return (
      <View>
        <TouchableRipple onPress={() => {
          this.setState({itemsVisibility: !this.state.itemsVisibility});
        }}
        >
          <View style={styles.container}>
            <Text style={styles.accountText}>{_label('account_details', {capitalize : true})}</Text>
            <View style={styles.imageContainer}>
              <Image
                source={this.state.itemsVisibility ? arrowUp : arrowDown}
                style={styles.image}
              />
            </View>
          </View>
        </TouchableRipple>
        {this.state.itemsVisibility &&
        <Card style={{marginHorizontal: 0, marginVertical: 0}}>
          <View style={styles.bottomContainer}>
            <CustomTextField id="userName" />
            <PasswordTextField id="password" />
            <DropDownTextFieldNew id="securityQuestion" dropDownList={this.props.securityQuestionList} />
            <PasswordTextField id="securityAnswer" />
            <DropDownTextFieldNew id="selectLanguage" dropDownList={getFormObject().selectLanguage.dropDownList} />
          </View>
        </Card>
        }
      </View>
    );
  }
}

AccountDetails.propTypes = {
  securityQuestionList: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  })),
};

AccountDetails.defaultProps = {
  securityQuestionList: [],
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 10, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center',
  },
  accountText: {fontWeight: 'normal', fontSize: 22, color: colors.defaultTextColor},
  imageContainer: {padding: 8, alignItems: 'center', justifyContent: 'center'},
  image: {width: 24, height: 24},
  bottomContainer: {marginHorizontal: 16, marginTop: 16},
});
