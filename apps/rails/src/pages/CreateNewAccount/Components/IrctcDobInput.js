import React from 'react';
import { StyleSheet, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import fecha from 'fecha';
import isEmpty from 'lodash/isEmpty';
import DatePicker from 'react-native-datepicker';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {addDays, today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {openDatePicker} from '@mmt/legacy-commons/Native/BusRailsCalendarModule';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import getFormObject from '../IrctcFormObject';
import {addTextData} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import {_label} from '../../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../../vernacular/VernacularUtils';
import { isAndroid, isIos } from '../../../Utils/device';

const getMinDate = () => {
  const minDate = today();
  return (minDate.setFullYear(minDate.getFullYear() - 100));
};
const getMaxDate = () => {
  const maxDate = today();
  return maxDate.setFullYear(maxDate.getFullYear() - 18);
};

class CalendarWrapper extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      error: false,
      errorMessage: '',
      dob: null,
    };
  }

  UNSAFE_componentWillMount () {
    let dob;
    let error;
    let errorMessage;
    const {fieldObject} = this.props;
    if (isEmpty(fieldObject) || isEmpty(fieldObject.text)) {
      dob = '';
    } else {
      dob = this.props.fieldObject.text;
    }
    if (!isEmpty(fieldObject) && fieldObject.error === true) {
      error = true;
      errorMessage = fieldObject.errorMessage;
    } else {
      error = false;
      errorMessage = '';
    }
    this.setState({dob, error, errorMessage});
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const {fieldObject} = nextProps;
    if (!isEmpty(fieldObject)) {
      if (fieldObject.error === true) {
        this.setState({error: true, errorMessage: fieldObject.errorMessage});
      } else {
        this.setState({error: false, errorMessage: ''});
      }
    }
  }

  _openDOBCalendar = async () => {
    const minDate = today();
    minDate.setFullYear(minDate.getFullYear() - 100);
    const minDateStr = fecha.format(minDate, 'DD/MM/YYYY');

    const maxDate = today();
    maxDate.setFullYear(maxDate.getFullYear() - 18);
    const maxDateStr = fecha.format(maxDate, 'DD/MM/YYYY');

    let {dob} = this.state;
    if (isEmpty(dob)) {
      dob = addDays(maxDate, -1);
    }
    if (isEmpty(dob)) {
      const date = addDays(maxDate, -1);
      dob = fecha.format(date, 'DD/MM/YYYY');
    }
    const newDobStr = await openDatePicker({
      minDate: minDateStr,
      maxDate: maxDateStr,
      selectedDate: dob,
      title: _label('select_dob'),
    });
    this.setState({
      dob: newDobStr,
    });
    this.props.addTextData({currentObject: this.props.currentObject, selectedValue: newDobStr});
  };

  render() {
    let dd;
    let mm;
    let yyyy;
    let dateEmpty = true;
    if (isEmpty(this.state.dob)) {
      dd = '';
      mm = '';
      yyyy = '';
    } else {
      dateEmpty = false;
      [dd, mm, yyyy] = this.state.dob.split('/');
    }
    return (
      <View style={{marginBottom: 16}}>
        <Text style={[{
          color: colors.defaultTextColor, fontSize: 14, fontWeight: 'bold', marginBottom: 12,
        }, fontStyle('bold'), getLineHeight(14)]}
        >
          {_label('date_of_birth')}
        </Text>

        <TouchableRipple onPress={() => { if (isAndroid()) { this._openDOBCalendar(); } }}>
          <View style={this.state.error ? styles.errorContainer : styles.activeContainer}>
            <View style={{borderRadius: 5, flexDirection: 'row', flex: 1}}>
              <View style={styles.textContainer}>
                <Text style={dateEmpty ? styles.unselectedText : styles.selectedText}>
                  {isEmpty(dd) ? 'DD' : dd}
                </Text>
              </View>
              <View style={styles.mmTextContainer}>
                <Text style={dateEmpty ? styles.unselectedText : styles.selectedText}>
                  {isEmpty(mm) ? 'MM' : mm}
                </Text>
              </View>
              <View style={styles.yyTextContainer}>
                <Text style={dateEmpty ? [styles.unselectedText, fontStyle('regular'), getLineHeight(14)] : [styles.selectedText,fontStyle('black') , getLineHeight(14)]}>
                  {isEmpty(yyyy) ? 'YYYY' : yyyy}
                </Text>
              </View>
              <View style={{height: 40, width: 2}} />
              {isIos() &&
              <DatePicker
                mode="date"
                hideText
                format="DD/MM/YYYY"
                minDate={fecha.format(getMinDate(), 'DD/MM/YYYY')}
                maxDate={fecha.format(getMaxDate(), 'DD/MM/YYYY')}
                confirmBtnText = {_label('confirm')}
                cancelBtnText = {_label('cancel')}
                placeholder=""
                showIcon={false}
                style={{
                  position: 'absolute', bottom: 0, top: 0, left: 0, width: '100%',
                }}
                customStyles={{
                  ...dateStyle.datePickerStyle,
                  placeholderText : {
                    ...dateStyle.datePickerStyle.placeholderText ,
                    ...fontStyle('bold'),
                    ...getLineHeight(14),
                  },
                }}
                onDateChange={(date) => {
                this.setState({dob: date});
                this.props.addTextData({
                  currentObject: this.props.currentObject,
                  selectedValue: date,
                });
              }}
              />
            }
            </View>
          </View>
        </TouchableRipple>
        {
          this.state.error &&
          <View style={{marginTop: 8}}>
            <Text style={[styles.errorText, fontStyle('regular'), getLineHeight(12)]}>
              {this.state.errorMessage}
            </Text>
          </View>
        }
      </View>
    );
  }
}

CalendarWrapper.propTypes = {
  addTextData: PropTypes.func.isRequired,
  fieldObject: PropTypes.shape({
    id: PropTypes.string,
    text: PropTypes.string,
    error: PropTypes.bool,
    errorMessage: PropTypes.string,
  }),
  currentObject: PropTypes.shape({
    id: PropTypes.string.isRequired,
    nextId: PropTypes.string.isRequired,
    errorMessage: PropTypes.shape({
      emptyErrorMessage: PropTypes.string.isRequired,
    }),
    headerText: PropTypes.string.isRequired,
  }).isRequired,
};

CalendarWrapper.defaultProps = {
  fieldObject: null,
};


const mapStateToProps = (state, ownProps) => {
  const {id} = ownProps;
  const currentObject = getFormObject()[id];
  const {railsLanding: {userAccount}} = state;
  const fieldObject = userAccount[id];
  return {
    ...ownProps,
    currentObject,
    fieldObject,
  };
};

const mapDispatchToProps = dispatch => ({
  addTextData: (data) => { dispatch(addTextData(data)); },
});

export default connect(mapStateToProps, mapDispatchToProps)(CalendarWrapper);

const styles = StyleSheet.create({
  errorContainer: {
    borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.red,
  },
  activeContainer: {
    borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.azure,
  },
  textContainer: {
    alignItems: 'center', justifyContent: 'center', borderRadius: 5, flex: 1,
  },
  unselectedText: {
    color: colors.mediumGrey, fontSize: 14,
  },
  selectedText: {color: colors.black, fontSize: 14},
  errorText: {color: colors.red, fontSize: 12},
  mmTextContainer: {
    flex: 1, justifyContent: 'center', alignItems: 'center', borderRightWidth: StyleSheet.hairlineWidth * 1, borderLeftWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.primary,
  },
  yyTextContainer: {
    borderRadius: 5, justifyContent: 'center', flex: 1, alignItems: 'center',
  },
});

const dateStyle = {
  datePickerStyle: {
    dateInput:
      {borderWidth: 0, alignItems: 'flex-start'},
    placeholderText: {
      fontSize: 14,
      lineHeight: 18,
      letterSpacing: 0,
      color: colors.black04,
    },
  },
  datePickerContainer: {
    flexDirection: 'row',
    flex: 1,
    borderBottomWidth: 2,
    borderColor: colors.gradientLightGrey,
  },
};
