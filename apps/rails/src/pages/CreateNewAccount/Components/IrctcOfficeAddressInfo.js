import React, {Component} from 'react';
import {View} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import CustomTextField from './IrctcFormInput';
import DropDownTextFieldNew from './IrctcDropdownWrapper';
import getFormObject from '../IrctcFormObject';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {setPinCodeData} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import railsConfig from '../../../RailsConfig';
import fetch2 from '../../../fetch2';
import { getDefaultPostHeaders } from '../../NewListing/RailsListingActions';
import {_label} from '../../../vernacular/AppLanguage';

class OfficeAddressInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      cityList: [],
      stateList: [],
      postOfficeList: [],
    };
  }

  onPinCodeBlur = async (text, currentObject) => {
    let error = false;
    let errorMessage = '';
    let pinCodeData = null;
    if (currentObject.id === 'enterOfficePinCode') {
      let response;
      try {
        const requestBody = {pinCode: text};
        const res = await fetch2(railsConfig.getAddressFromPinCode, {
          method: 'POST',
          headers: getDefaultPostHeaders(),
          body: JSON.stringify(requestBody),
        });
        response = await res.json();
      } catch (e) {
        console.log('error in onPincodeblur', e);
        showShortToast(_label('something_went_wrong') + ' Error Code: ' + e?.errorCode );
        return;
      }

      if (!isEmpty(response.errorMessage)) {
        error = true;
        errorMessage = _label('please_fill_valid_pincode');
        pinCodeData = {pinCode: {text, error, errorMessage}};
      } else {
        const {city, state, postOfficeList: newPostOfficeList} = response;
        const newCityList = [city];
        const newStateList = [state];
        const cityList = newCityList.map((item, index) => ({id: index.toString(), text: item}));
        const stateList = newStateList.map((item, index) => ({id: index.toString(), text: item}));
        const postOfficeList = newPostOfficeList.map((item, index) => ({id: index.toString(), text: item}));
        this.setState({cityList, stateList, postOfficeList});
        pinCodeData = {
          pinCode: {text},
          city: cityList[0],
          postOffice: postOfficeList[0],
          state: stateList[0],
          isOffice: true,
        };
      }
      this.props.setPinCodeData(currentObject, pinCodeData);
    }
  };

  render() {
    return (
      <View>
        <Card style={{marginHorizontal: 0, marginVertical: 0}}>
          <View style={{marginHorizontal: 16, marginTop: 16}}>
            <CustomTextField id="enterOfficeAddress" />
            <CustomTextField id="enterOfficeMobileNumber" />
            <CustomTextField id="enterOfficePinCode" onBlur={this.onPinCodeBlur} />
            <DropDownTextFieldNew id="selectOfficeCity" dropDownList={this.state.cityList} />
            <DropDownTextFieldNew id="selectOfficeState" dropDownList={this.state.stateList} />
            <DropDownTextFieldNew id="selectOfficePostOffice" dropDownList={this.state.postOfficeList} />
            <DropDownTextFieldNew id="officeCountry" dropDownList={getFormObject().country.dropDownList} />
          </View>
        </Card>
      </View>
    );
  }
}

OfficeAddressInfo.propTypes = {
  setPinCodeData: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => {
  const {railsLanding: {userAccount: {enterPinCode}}} = state;
  return {
    enterPinCode,
  };
};

const mapDispatchToProps = dispatch => ({
  setPinCodeData: (currentObject, pinCodeData) => {
    dispatch(setPinCodeData(currentObject, pinCodeData));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(OfficeAddressInfo);
