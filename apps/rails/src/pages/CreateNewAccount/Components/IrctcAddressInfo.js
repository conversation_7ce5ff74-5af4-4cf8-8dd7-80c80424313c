import React, {Component} from 'react';
import {Image, Text, View, StyleSheet} from 'react-native';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import CustomTextField from './IrctcFormInput';
import DropDownTextFieldNew from './IrctcDropdownWrapper';
import getFormObject from '../IrctcFormObject';
import {setPinCodeData} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import railsConfig from '../../../RailsConfig';
import fetch2 from '../../../fetch2';
import { getDefaultPostHeaders } from '../../NewListing/RailsListingActions';
import {_label} from '../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';

let openOnce = true;

class AddressInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      itemsVisibility: false,
      cityList: [],
      stateList: [],
      postOfficeList: [],
    };
  }

  componentDidUpdate() {
    if (openOnce) {
      if (this.props.autoOpen) {
        this.setState({itemsVisibility: true});
        openOnce = false;
      }
    }
  }

  onPinCodeBlur = async (text, currentObject) => {
    let error = false;
    let errorMessage = '';
    let pinCodeData = null;
    if (currentObject.id === 'enterPinCode') {
      let response;
      try {
        const requestBody = {pinCode: text};
        const res = await fetch2(railsConfig.getAddressFromPinCode, {
          method: 'POST',
          headers: getDefaultPostHeaders(),
          body: JSON.stringify(requestBody),
        });
        response = await res.json();
      } catch (e) {
        console.log('error in onpincodeblure', e);
        showShortToast(_label('something_went_wrong') + ' Error Code: ' + e?.errorCode );
        return;
      }

      if (!isEmpty(response.errorDetails)) {
        error = true;
        errorMessage = _label('please_fill_valid_pincode');
        pinCodeData = {pinCode: {text, error, errorMessage}};
      } else {
        const {city, state, postOfficeList: newPostOfficeList} = response;
        const newCityList = [city];
        const newStateList = [state];
        const cityList = newCityList.map((item, index) => ({id: index.toString(), text: item}));
        const stateList = newStateList.map((item, index) => ({id: index.toString(), text: item}));
        const postOfficeList = newPostOfficeList.map((item, index) => ({id: index.toString(), text: item}));
        this.setState({cityList, stateList, postOfficeList});
        pinCodeData = {
          pinCode: {text},
          city: cityList[0],
          postOffice: postOfficeList[0],
          state: stateList[0],
          isOffice: false,
        };
      }
      this.props.setPinCodeData(currentObject, pinCodeData);
    }
  };

  render() {
    return (
      <View>
        <TouchableRipple onPress={() => {
          this.setState({itemsVisibility: !this.state.itemsVisibility});
        }}
        >
          <View style={styles.addressContainer}>
            <Text style={styles.addressText}>{_label('address_info', {capitalize : true})}</Text>
            <View style={styles.imageContainer}>
              <Image
                source={this.state.itemsVisibility ? arrowUp : arrowDown}
                style={styles.image}
              />
            </View>
          </View>
        </TouchableRipple>
        {this.state.itemsVisibility &&
        <Card style={{marginHorizontal: 0, marginVertical: 0}}>
          <View style={styles.bottomContainer}>
            <CustomTextField id="enterResidentialAddress" />
            <CustomTextField id="enterResidentialMobileNumber" />
            <CustomTextField id="enterPinCode" onBlur={this.onPinCodeBlur} />
            <DropDownTextFieldNew id="selectCity" dropDownList={this.state.cityList} />
            <DropDownTextFieldNew id="selectState" dropDownList={this.state.stateList} />
            <DropDownTextFieldNew id="selectPostOffice" dropDownList={this.state.postOfficeList} />
            <DropDownTextFieldNew id="country" dropDownList={getFormObject().country.dropDownList} />
          </View>
        </Card>
        }
      </View>
    );
  }
}

AddressInfo.propTypes = {
  autoOpen: PropTypes.bool.isRequired,
  setPinCodeData: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  addressContainer: {
    marginBottom: 10, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center',
  },
  addressText: {fontWeight: 'normal', fontSize: 22, color: colors.defaultTextColor},
  imageContainer: {padding: 8, alignItems: 'center', justifyContent: 'center'},
  image: {width: 24, height: 24},
  bottomContainer: {marginHorizontal: 16, marginTop: 16},
});

const mapStateToProps = (state) => {
  const {railsLanding: {userAccount: {enterPinCode, selectMaritalStatus}}} = state;
  return {
    enterPinCode,
    autoOpen: !isEmpty(selectMaritalStatus),
  };
};

const mapDispatchToProps = dispatch => ({
  setPinCodeData: (currentObject, pinCodeData) => {
    dispatch(setPinCodeData(currentObject, pinCodeData));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(AddressInfo);
