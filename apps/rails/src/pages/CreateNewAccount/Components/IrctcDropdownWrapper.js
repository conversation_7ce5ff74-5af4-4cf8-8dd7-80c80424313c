import {Text, View} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import React, {Component} from 'react';
import RailsDropDown from './IrctcFormDropDown';
import getFormObject from '../IrctcFormObject';
import {addUserData} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import {fontStyle,getLineHeight} from '../../../vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

class DropDownTextFieldNew extends Component {
  componentDidMount() {
    if (this.props.dropDownList.length === 1) {
      this.props.addUserData({
        currentObject: this.props.currentObject,
        selectedValue: this.props.dropDownList[0],
      });
    }
  }

  componentDidUpdate() {
    if (this.props.dropDownList.length === 1) {
      if (isEmpty(this.props.currentSelection)) {
        this.props.addUserData({
          currentObject: this.props.currentObject,
          selectedObject: this.props.dropDownList[0],
        });
      }
    }
  }

  render() {
    const {
      currentObject, dropDownList, currentSelection, error, errorMessage,
    } = this.props;
    return (
      <View style={{marginBottom: 16}}>
        <Text style={[{color: colors.defaultTextColor, fontSize: 14, fontWeight: 'bold', marginBottom: 12}, fontStyle('bold'), getLineHeight(14)]}>
          {currentObject.headerText}
        </Text>
        <RailsDropDown
          label={currentObject.label}
          selected={currentSelection}
          options={dropDownList}
          onSelect={(option) => {
            this.props.addUserData({currentObject, selectedObject: option});
          }}
          headerInfo={currentObject.headerInfo}
          header={currentObject.headerText}
          error={error}
          errorMessage={errorMessage}
        />
      </View>
    );
  }
}

DropDownTextFieldNew.propTypes = {
  dropDownList: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  })),
  error: PropTypes.bool,
  errorMessage: PropTypes.string,
  addUserData: PropTypes.func.isRequired,
  currentSelection: PropTypes.string,
  currentObject: PropTypes.shape({
    id: PropTypes.string.isRequired,
    nextId: PropTypes.string.isRequired,
    errorMessage: PropTypes.shape({
      emptyErrorMessage: PropTypes.string.isRequired,
    }).isRequired,
    headerText: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    dropDownList: PropTypes.array,
    headerInfo: PropTypes.string,
  }).isRequired,

};

DropDownTextFieldNew.defaultProps = {
  dropDownList: [],
  error: false,
  errorMessage: '',
  currentSelection: '',
};

const mapStateToProps = (state, ownProps) => {
  const {id} = ownProps;
  const currentObject = getFormObject()[id];
  const {railsLanding: {userAccount}} = state;
  const currentSelectionObject = userAccount[id];
  const focus = {userAccount};
  const currentSelection = isEmpty(currentSelectionObject) ? '' : currentSelectionObject.text;
  const error = isEmpty(currentSelectionObject) ? false : currentSelectionObject.error;
  const errorMessage = isEmpty(currentSelectionObject) ? '' : currentSelectionObject.errorMessage;
  return {
    ...ownProps,
    currentObject,
    currentSelection,
    error,
    errorMessage,
    focus,
  };
};

const mapDispatchToProps = dispatch => ({
  addUserData: (data) => { dispatch(addUserData(data)); },
});

export default connect(mapStateToProps, mapDispatchToProps)(DropDownTextFieldNew);
