import React, {Component} from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import {connect} from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import CustomTextField from './IrctcFormInput';
import DropDownTextFieldNew from './IrctcDropdownWrapper';
import CalendarWrapper from './IrctcDobInput';
import getFormObject from '../IrctcFormObject';
import {_label} from '../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';

let openOnce = true;

class PersonalInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      itemsVisibility: false,
    };
  }

  componentDidUpdate() {
    if (openOnce) {
      if (this.props.autoOpen) {
        this.setState({itemsVisibility: true});
        openOnce = false;
      }
    }
  }

  render() {
    const formObject = getFormObject();
    return (
      <View>
        <TouchableRipple onPress={() => {
          this.setState({itemsVisibility: !this.state.itemsVisibility});
        }}
        >
          <View style={styles.addressContainer}>
            <Text style={styles.addressText}>{_label('personal_info', {capitalize : true})}</Text>
            <View style={styles.imageContainer}>
              <Image
                source={this.state.itemsVisibility ? arrowUp : arrowDown}
                style={styles.image}
              />
            </View>
          </View>
        </TouchableRipple>
        {this.state.itemsVisibility &&
        <Card style={{marginHorizontal: 0, marginVertical: 0}}>
          <View style={styles.bottomContainer}>
            <CustomTextField id="enterFirstName" />
            <CustomTextField id="enterLastName" />
            <DropDownTextFieldNew id="selectGender" dropDownList={formObject.selectGender.dropDownList} />
            <CustomTextField id="enterEmail" />
            <CustomTextField id="enterMobileNumber" />
            <CalendarWrapper id="enterDob" />
            <DropDownTextFieldNew id="selectOccupation" dropDownList={this.props.occupationList} />
            <DropDownTextFieldNew id="selectMaritalStatus" dropDownList={formObject.selectMaritalStatus.dropDownList} />
            <DropDownTextFieldNew id="selectNationality" dropDownList={formObject.selectNationality.dropDownList} />
            <Text style={styles.foreignNationalsHelpText}>{_label('foreign_nationals_help_text')}</Text>
          </View>
        </Card>
        }
      </View>
    );
  }
}

PersonalInfo.propTypes = {
  occupationList: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  })),
  autoOpen: PropTypes.bool.isRequired,
};

PersonalInfo.defaultProps = {
  occupationList: [],
};

const styles = StyleSheet.create({
  addressContainer: {
    marginBottom: 10, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center',
  },
  addressText: {fontWeight: 'normal', fontSize: 22, color: colors.defaultTextColor},
  imageContainer: {padding: 8, alignItems: 'center', justifyContent: 'center'},
  image: {width: 24, height: 24},
  bottomContainer: {marginHorizontal: 16, marginTop: 16},
  foreignNationalsHelpText: {
    fontSize: 12,
    marginBottom: 10,
  },
});

const mapStateToProps = (state) => {
  const {railsLanding: {userAccount}} = state;
  return {
    autoOpen: !isEmpty(userAccount.selectLanguage),
  };
};

export default connect(mapStateToProps, null)(PersonalInfo);
