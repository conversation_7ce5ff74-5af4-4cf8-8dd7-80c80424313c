import React, { Component, createContext } from 'react';
import {
  BackHandler,
  Dimensions,
  FlatList,
  Text,
  View,
  TouchableWithoutFeedback,
  StyleSheet,
  Keyboard,
  Image,
} from 'react-native';
import PropTypes from 'prop-types';
import debounce from 'lodash/debounce';
import isEmpty from 'lodash/isEmpty';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle,getLineHeight} from '../../../vernacular/VernacularUtils';
import { isMweb,isIos} from '../../../Utils/device';

import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';

const IrctcDropdownContext = createContext({
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  showDropDown: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onOptionSelected: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  hideDropDown: () => {},
});

class RailsDropDown extends Component {
  _captureRef = (ref) => {
    this._ref = ref;
  };

  render() {
    const { showDropDown, hideDropDown, onOptionSelected } = this.context || {};
    const {
    onSelect, options, overlayMode, error, errorMessage, maxHeight, disabled,
    } = this.props;
    return (
      <View nativeID="irctc_form_drop_down" ref={this._captureRef} style={{opacity: disabled ? 0.3 : 1,  borderRadius: 5}}>
        <View>
          <TouchableRipple disabled={disabled} onPress={() => {
            Keyboard.dismiss();
            if (overlayMode) {
              return;
            }
            this._ref && this._ref.measureInWindow((x, y, w, h) => {
              const position = {
                x, y, w, h,
              };
              showDropDown(this.props, position, onSelect);
            });
          }}
          >
            <View style={{borderRadius: 5}}>
            { !overlayMode &&
            <View style={this.props.error ? {flex: 1, flexDirection: 'row', borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.red} : {flex: 1, flexDirection: 'row', borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.primary}}>
              <Text style={{padding: 12, fontSize: 14, ...fontStyle('regular'), ...getLineHeight(14) , color: colors.defaultTextColor, flex: 1}}>
                {isEmpty(this.props.selected) ? this.props.label : this.props.selected}
              </Text>
              <View style={{height: 40, width: 40, alignSelf: 'center', alignItems: 'center', justifyContent: 'center'}}>
                <Image
                  source={arrowDown}
                  style={{
                    height: 24, width: 24,
                  }}
                />
              </View>
            </View>
            }
            </View>
          </TouchableRipple>
          { !overlayMode && error &&
            <View style={{marginTop: 8}}>
              <Text style={{color: colors.red, fontSize: 12, ...fontStyle('regular'), ...getLineHeight(12)}}>
                {errorMessage}
              </Text>
            </View>
          }
        </View>
        {overlayMode &&
          <View style={{backgroundColor: colors.white, borderRadius: 5, paddingHorizontal: 16, paddingTop: 16}}>
            { !isEmpty(this.props.headerInfo) &&
              <Text style={{
                fontSize: 12,
                ...fontStyle('italic'),
                ... getLineHeight(12),
                fontStyle: 'italic',
                color: colors.grey29,
                marginBottom: 16,
              }}>{this.props.headerInfo}</Text>
            }
            <Text style={{fontSize: 10, ...fontStyle('regular'), color: colors.defaultTextColor, marginBottom: 8, ...getLineHeight(10)}}>{this.props.header.toUpperCase()}</Text>
            <View style={{maxHeight: maxHeight}}>
        <FlatList
          data={options}
          style={{backgroundColor: colors.white, borderRadius: 5}}
          keyboardShouldPersistTaps="handled"
          keyExtractor={item => item.id}
          renderItem={({item}) => {
            return (
              <TouchableWithoutFeedback
                onPress={(e) => {
                  if (isMweb()) {
                    e.preventDefault();
                  }
                  onOptionSelected(item);
                  hideDropDown();
                }}
                underlayColor="white"
                activeOpacity={0.5}
              >

                <View key={item.id} style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  paddingVertical: 16,
                  paddingHorizontal: 2,
                  margin: 0,
                  backgroundColor: colors.white,
                  borderBottomWidth: StyleSheet.hairlineWidth,
                  borderBottomColor: colors.lightGrey14,
                }}>
                  <Text style={{fontSize: 14, ...fontStyle('bold'), color: colors.defaultTextColor, fontWeight: 'bold', ...getLineHeight(14)}}>{item.text}</Text>
                </View>
              </TouchableWithoutFeedback>
            );
          }}
        />
            </View>
          </View>
        }
      </View>
    );
  }
}


RailsDropDown.contextType = IrctcDropdownContext;
RailsDropDown.propTypes = {
  adjustPosition: PropTypes.bool,
  fixedPosition: PropTypes.object,
  overlayMode: PropTypes.bool,
  label: PropTypes.string.isRequired,
  selected: PropTypes.string,
  defaultText: PropTypes.string,
  onSelect: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  })).isRequired,
  header: PropTypes.string,
  headerInfo: PropTypes.string,
  error: PropTypes.bool,
  errorMessage: PropTypes.string,
  disabled: PropTypes.bool,
  maxHeight: PropTypes.number,
};
RailsDropDown.defaultProps = {
  adjustPosition: true,
  fixedPosition: null,
  overlayMode: false,
  selected: '',
};
export default RailsDropDown;


export class RailsDropDownOverlayContainer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDropDown: false,
    };
    this._repositionDropdownDebounced = debounce(this._repositionDropdown, 300);
  }


  onBackPress = () => {
    if (this.state.showDropDown) {
      this._hideDropDown();
    }
    return true;
  };

  handleBackPress = () => {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  };

  getContextValue = () => ({
    showDropDown: (dropDownProps, position, onSelectHandler) => {
      this.setState({
        showDropDown: true,
        dropDownProps,
        position,
        onSelectHandler,
      });
      this.handleBackPress();
    },
    onOptionSelected: (option) => {
      this.state.onSelectHandler(option);
    },
    hideDropDown: this._hideDropDown,
  });

  _hideDropDown = (e) => {
    if (e && isMweb()) {
      e.preventDefault();
    }
    this.setState({
      showDropDown: false,
    });
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  };


  _repositionDropdown = (layout, position) => {
    this._height = Dimensions.get('screen').height;
    const diff = position.y + layout.height - this._height + 24;
    if (diff > 0) {
      this.setState({
        repositioning: true,
        position: {
          ...position,
          y: position.y - diff - 24,
        },
      });
    }
  };


  _onLayout = ({nativeEvent: {layout}}) => {
    if (!this.state.dropDownProps.adjustPosition) {return;}
    console.log('layout', layout);
    const {position} = this.state;
    this._repositionDropdown(layout, position);
  };


  render() {
    this._height = Dimensions.get('screen').height;
    const {position, dropDownProps, showDropDown} = this.state;
    const {style = {}} = this.props;
    let marginTop = 0;
    let maxHeight = 0;
    if (showDropDown && position) {
      const {adjustPosition, fixedPosition} = dropDownProps;
      if (isEmpty(fixedPosition)) {
        marginTop = isIos() ? position.y - 24 : position.y;
      } else {
        marginTop = fixedPosition.y;
      }
      maxHeight = (adjustPosition ? this._height : this._height - marginTop - 24);
    }
    return (
      <IrctcDropdownContext.Provider value={this.getContextValue()}>
        <View style={[{ flex: 1 }, style]}>
          {this.props.children}
        {showDropDown &&
        <TouchableWithoutFeedback onPress={this._hideDropDown}>
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            ...getPlatformElevation(4),
            backgroundColor: colors.black,
          }}
          >
            <View
              onLayout={this._onLayout}
              style={{
                marginTop: marginTop,
                marginLeft: position.x,
                width: position.w,
                maxHeight: maxHeight,
              }}
            >
              <RailsDropDown {...dropDownProps} overlayMode maxHeight={maxHeight}/>
            </View>
          </View>
        </TouchableWithoutFeedback>
        }
        </View>
      </IrctcDropdownContext.Provider>
    );
  }
}

RailsDropDownOverlayContainer.propTypes = {
  style: PropTypes.any,
  children: PropTypes.node,
};


