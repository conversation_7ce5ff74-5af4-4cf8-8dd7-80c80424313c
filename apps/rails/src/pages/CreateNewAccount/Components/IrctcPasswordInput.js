import React, {Component} from 'react';
import isEmpty from 'lodash/isEmpty';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {Image, Keyboard, StyleSheet, Text, TextInput, View} from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import getFormObject from '../IrctcFormObject';
import {addTextData} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { CREATE_ACCOUNT_CLICK, getDataFromAsynStorage } from 'apps/rails/src/Utils/RailsConstant';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

import passwordShow from '@mmt/legacy-assets/src/ic-show.webp';
import passwordHide from '@mmt/legacy-assets/src/ic-hide.webp';
import icGreenTick from '@mmt/legacy-assets/src/green_tick.webp';
import icGrayTick from '@mmt/legacy-assets/src/ic-status-tickgraylarge.webp';

class PasswordTextField extends Component {
  constructor(props) {
    super(props);
    this.state = {
      secureText: true,
      text: '',
      showList: false,
      validList: null,
      error: false,
      errorMessage: '',
    };
  }

  UNSAFE_componentWillMount () {
    let text;
    let error;
    let errorMessage;
    const {
      currentObject: { validationList },
      fieldObject,
    } = this.props;
    if (isEmpty(fieldObject) || isEmpty(fieldObject.text)) {
      text = '';
    } else {
      text = this.props.fieldObject.text;
    }
    if (!isEmpty(fieldObject) && fieldObject.error === true) {
      error = true;
      errorMessage = fieldObject.errorMessage;
    } else {
      error = false;
      errorMessage = '';
    }
    const validList = !isEmpty(validationList)
      ? validationList.map((item) => item.validationFunction(this.state.text))
      : [];
    this.setState({
      validList,
      text,
      error,
      errorMessage,
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { fieldObject } = nextProps;
    if (!isEmpty(fieldObject) && fieldObject.error === true) {
      this.setState({ error: true, errorMessage: fieldObject.errorMessage });
    }
  }

  async componentDidUpdate(prevProps) {
    const createAccountClick = await getDataFromAsynStorage(CREATE_ACCOUNT_CLICK);
    if (prevProps.currentObject !== this.props.currentObject && createAccountClick) {
      let text;
      let error;
      let errorMessage;
      const {
        currentObject: { validationList },
        fieldObject,
      } = this.props;
      text = this.props.fieldObject?.text ?? '';
      if (!isEmpty(fieldObject) && fieldObject.error === true) {
        error = true;
        errorMessage = fieldObject.errorMessage;
      } else {
        error = false;
        errorMessage = '';
      }
      const errorHandling = [];
      const validList = !isEmpty(validationList)
        ? validationList.map((item) => {
            if (!item.validationFunction(this.state.text)) {
              errorHandling.push({
                error: item.validationFunction(this.state.text),
                errorDescription: item.description,
              });
            }
            return item.validationFunction(this.state.text);
          })
        : [];

      if (errorHandling.length !== 0) {
        this.setState({
          validList,
          text,
          error: true,
          errorMessage: errorHandling[0].errorDescription,
        });
      } else {
        this.setState({
          validList,
          text,
          error,
          errorMessage,
        });
      }
    }
  }

  render() {
    const { headerText, validationList, keyboardType, maxLength } = this.props.currentObject;
    return (
      <View style={{marginBottom: 16}}>
        <Text style={[styles.headerText, fontStyle('bold'), getLineHeight(14)]}>
          {headerText}
        </Text>
        <View style={this.state.error ? styles.errorContainer : styles.validContainer}>
          <View style={{flexDirection: 'row', flex: 1}}>
            <View style={{flex: 1}}>
              <TextInput
                ref={(ref) => { this._ref = ref; }}
                underlineColorAndroid="transparent"
                style={{padding: 8}}
                value={this.state.text}
                maxLength={maxLength}
                secureTextEntry={this.state.secureText}
                onChangeText={(text) => {
                  const validList = !isEmpty(validationList) ?
                    validationList.map(item => item.validationFunction(text)) : [];
                  this.setState({text, validList});
                }}
                onFocus={() => {
                  let showList = false;
                  if (!isEmpty(validationList)) {
                    showList = true;
                  }
                  this.setState({showList, error: false});
                }}
                onBlur={() => {
                  this.props.addTextData({
                    currentObject: this.props.currentObject,
                    selectedValue: this.state.text,
                  });
                  if (!isEmpty(validationList)) {
                    this.setState({showList: false});
                  }
                }}
                keyboardType={keyboardType}
                returnKeyType={keyboardType === 'numeric' ? 'done' : 'next'}
                keyboardShouldPersistTaps="handled"
                onSubmitEditing={() => {
                  Keyboard.dismiss();
                }}
              />
            </View>
            <TouchableRipple onPress={() => {
              this.setState({secureText: !this.state.secureText});
            }}
            >
              <View style={styles.imageContainer}>
                <Image
                  source={this.state.secureText ? passwordHide : passwordShow}
                  style={styles.passwordImage}
                />
              </View>
            </TouchableRipple>
          </View>
        </View>
        <View style={{marginTop: 8}}>
          {
            this.state.error &&
            <Text style={[styles.errorMessageText, fontStyle('regular'), getLineHeight(12)]}>
              {this.state.errorMessage}
            </Text>
          }
          {this.state.showList && !isEmpty(validationList) &&
          validationList.map((item, index) => (
            <View key={index} style={styles.validationContainer}>
              {
                this.state.validList[index] ?
                  <Image
                    style={styles.greenImage}
                    source={icGreenTick}
                  /> :
                  <Image
                    style={styles.greyImage}
                    source={icGrayTick}
                  />
              }
              <Text style={[styles.descriptionText, fontStyle('regular'), getLineHeight(14)]}>{item.description}
              </Text>
            </View>
          ))
          }
        </View>
      </View>
    );
  }
}

PasswordTextField.propTypes = {
  fieldObject: PropTypes.shape({
    text: PropTypes.string,
    error: PropTypes.bool,
    errorMessage: PropTypes.string,
  }),
  currentObject: PropTypes.shape({
    id: PropTypes.string.isRequired,
    headerText: PropTypes.string.isRequired,
    nextId: PropTypes.string.isRequired,
    keyboardType: PropTypes.string.isRequired,
    errorMessage: PropTypes.shape({
      emptyErrorMessage: PropTypes.string.isRequired,
      allNotValidErrorMessage: PropTypes.string,
    }),
    maxLength: PropTypes.number.isRequired,
    validationList: PropTypes.arrayOf(PropTypes.shape({
      description: PropTypes.string.isRequired,
      validationFunction: PropTypes.func.isRequired,
    })),
  }),
  addTextData: PropTypes.func.isRequired,
};

PasswordTextField.defaultProps = {
  fieldObject: null,
  currentObject: {
    validationList: null,
    errorMessage: {
      allNotValidErrorMessage: '',
    },
  },
};

const styles = StyleSheet.create({
  headerText: {
    color: colors.defaultTextColor, fontSize: 14, fontWeight: 'bold',marginBottom: 12,
  },
  errorContainer: {
    borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.red,
  },
  validContainer: {
    borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1, borderColor: colors.azure,
  },
  imageContainer: {
    height: 40, width: 40, alignSelf: 'center', alignItems: 'center', justifyContent: 'center',
  },
  passwordImage: {
    height: 24, width: 24,
  },
  errorMessageText: {color: colors.red, fontSize: 12},
  validationContainer: {flexDirection: 'row', alignItems: 'center', marginBottom: 10},
  greenImage: {
    height: 20,
    width: 20,
    marginRight: 10,
  },
  greyImage: {
    height: 24,
    width: 24,
    marginRight: 10,
  },
  descriptionText: {
    fontSize: 14,  color: colors.lightTextColor, flex: 1,
  },
});

const mapStateToProps = (state, ownProps) => {
  const {id} = ownProps;
  const currentObject = getFormObject()[id];
  const {railsLanding: {userAccount}} = state;
  const fieldObject = userAccount[id];
  return {
    ...ownProps,
    currentObject,
    fieldObject,
  };
};

const mapDispatchToProps = dispatch => ({
  addTextData: (data) => { dispatch(addTextData(data)); },
});

export default connect(mapStateToProps, mapDispatchToProps)(PasswordTextField);
