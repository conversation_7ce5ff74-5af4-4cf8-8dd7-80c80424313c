import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { View, Text, Image, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';
import CustomTextField from './IrctcFormInput';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { captchaAndSessionId } from 'apps/rails/src/pages/RailsLandingPage/Store/RailsLandingPageActions';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { trackClickEventProp61 } from '../../RailsBusHomePage/Analytics';
import { IRCTC_CAPTCHA_RETRY_EVENT, IRCTC_CREATE_ACCOUNT_EVENT } from 'apps/rails/src/Utils/RailsConstant';
import PropTypes from 'prop-types';

import reload from '@mmt/legacy-assets/src/reload_captcha.webp';

const Loader = () => (
  <View
    style={{
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
    }}
  >
    <ActivityIndicator size="small" color={colors.white} />
  </View>
);

const Captcha = (props) => {
  const [toggle, setToggle] = useState(true);
  useEffect(() => {
    setToggle(!toggle);
  }, [props.captchaQuestion]);

  const handleRetryCaptchaClick = () => {
    trackClickEventProp61(IRCTC_CREATE_ACCOUNT_EVENT,IRCTC_CAPTCHA_RETRY_EVENT);
    props.captchaAndSessionId();
  };

  return (
    <View>
      <Text style={styles.text}>Enter CAPTCHA</Text>
      <View style={styles.imageContainer}>
        {props.captchaLoading ? (
          <Loader />
        ) : (
          <View style={styles.spaceBetweenImages}>
            {!props.captchaQuestion ? (
              <View style={styles.captchaErrorContainer}>
                <Text style={styles.captchaError}>{_label('captcha_could_not_load')}</Text>
              </View>
            ) : (
              <Image
                resizeMode="contain"
                style={styles.image}
                source={{ uri: `data:image/jpeg;base64,${props.captchaQuestion}` }}
              />
            )}
            <TouchableOpacity
              style={styles.reloadImage}
              onPress={handleRetryCaptchaClick}
            >
              <Image resizeMode="contain" style={styles.reloadImage} source={reload} />
            </TouchableOpacity>
          </View>
        )}
      </View>
      <CustomTextField emptyText={toggle} id="enterCaptcha" />
      <Text style={styles.captchaText}>{`*${_label('captcha_description')}`}</Text>
    </View>
  );
};

const mapStateToProps = (state) => {
  const {
    railsLanding: {
      userAccount: { captchaQuestion, captchaLoading },
    },
  } = state;
  return {
    captchaQuestion,
    captchaLoading,
  };
};

const styles = StyleSheet.create({
  imageContainer: {
    backgroundColor: colors.deepRoyalBlue,
    height: 60,
    width: '100%',
    paddingLeft: 15,
    paddingRight: 20,
    justifyContent: 'center',
  },
  image: {
    height: 61,
    width: '41%',
  },
  text: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 5,
    fontFamily: 'Lato',
  },
  captchaText: {
    position: 'relative',
    top: -16,
    fontWeight: '500',
    fontFamily: 'Lato',
    fontSize: 10,
    lineHeight: 14,
  },
  reloadImage: {
    height: 30,
    width: 30,
  },
  spaceBetweenImages: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  captchaError: {
    fontFamily: 'Lato',
    fontSize: 12,
    color: colors.white,
  },
  captchaErrorContainer: {
    marginTop: 'auto',
    marginBottom: 'auto',
  },
});
const mapDispatchToProps = (dispatch) => ({
  captchaAndSessionId: () => {
    dispatch(captchaAndSessionId);
  },
});
export default connect(mapStateToProps, mapDispatchToProps)(Captcha);

Captcha.propTypes = {
  captchaAndSessionId: PropTypes.func,
  captchaLoading: PropTypes.bool,
  captchaQuestion: PropTypes.string,
};
