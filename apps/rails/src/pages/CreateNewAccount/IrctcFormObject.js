import isEmpty from 'lodash/isEmpty';
import fecha from 'fecha';
import {isValidEmail, isValidMobile} from '@mmt/legacy-commons/Helpers/validationHelpers';
import {today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {_label} from '../../vernacular/AppLanguage';

const minDate = today();
minDate.setFullYear(minDate.getFullYear() - 100);
const maxDate = today();
maxDate.setFullYear(maxDate.getFullYear() - 18);

const getFormObject = () => ({
  userName: {
    id: 'userName',
    headerText: _label('username',{capitalize : true}),
    nextId: 'password',
    keyboardType: 'default',
    errorMessage: {
      emptyErrorMessage: _label('enter_username',{sentenceCase : true}),
      allNotValidErrorMessage: _label('enter_valid_username',{sentenceCase : true}),
    },
    maxLength: 30,
    validationList: [
      {
        description: _label('username_length'),
        validationFunction: text => (text.length >= 3),
      },
      {
        description: _label('username_case_sensitivity'),
        validationFunction: text => !text.match(/[^a-zA-Z0-9]/i),
      },
    ],
  },
  password: {
    id: 'password',
    headerText: _label('password',{capitalize : true}),
    keyboardType: 'default',
    errorMessage: {
      emptyErrorMessage: _label('enter_password',{sentenceCase : true}),
      allNotValidErrorMessage: _label('enter_valid_password',{sentenceCase : true}),
    },
    nextId: 'securityQuestion',
    maxLength: 15,
    validationList: [
      {
        description: _label('password_length_validation'),
        validationFunction: text => (text.length >= 8),
      },
      {
        description: _label('small_alphabet_validation'),
        validationFunction: text => (/[a-z]/.test(text)),
      },
      {
        description: _label('capital_alphabet_validation'),
        validationFunction: text => (/[A-Z]/.test(text)),
      },
      {
        description: _label('digit_validation'),
        validationFunction: text => (/[0-9]/.test(text)),
      },
    ],
  },
  securityQuestion: {
    id: 'securityQuestion',
    nextId: 'securityAnswer',
    errorMessage: {
      emptyErrorMessage: _label('select_security_question'),
    },
    headerInfo: _label('security_question_need'),
    headerText: _label('security_question'),
    label: _label('select_security_question'),
    dropDownList: [
      {
        id: 'b',
        text: 'b',
      },
      {
        id: 'ban',
        text: 'ban',
      },
      {
        id: 'bana',
        text: 'bana',
      },
      {
        id: 'ba',
        text: 'ba',
      },
      {
        id: 'banan',
        text: 'banan',
      },
      {
        id: 'banana',
        text: 'banana',
      },
    ],
  },
  securityAnswer: {
    id: 'securityAnswer',
    errorMessage: {
      emptyErrorMessage: _label('enter_security_answer'),
      allNotValidErrorMessage: _label('enter_valid_security_answer'),
    },
    nextId: 'selectLanguage',
    keyboardType: 'default',
    maxLength: 20,
    headerText: _label('security_answer',{capitalize : true}),
    validationList: [
      {
        description: _label('security_answer_length'),
        validationFunction: text => (text.length > 3 && text.length <= 20),
      },
      {
        description: _label('alphanumeric_validation'),
        validationFunction: text => text.match(/^[a-z0-9]+$/i) !== null,
      },
    ],
  },
  selectLanguage: {
    id: 'selectLanguage',
    errorMessage: {
      emptyErrorMessage: _label('select_language'),
    },
    nextId: 'securityAnswer',
    headerText: _label('preferred_language',{capitalize : true}),
    label: _label('select_language'),
    dropDownList: [
      {id: 'en', text: 'English'},
      {id: 'hi', text: 'Hindi'},
    ],
  },
  enterFirstName: {
    id: 'enterFirstName',
    maxLength: 30,
    errorMessage: {
      emptyErrorMessage: _label('enter_first_name'),
      validationErrorMessage: _label('first_name_case'),
    },
    nextId: 'enterLastName',
    keyboardType: 'default',
    headerText: _label('first_name',{capitalize : true}),
    onNextValidation: text => text.match(/^[a-z]+$/i) !== null,
  },
  enterLastName: {
    id: 'enterLastName',
    maxLength: 30,
    errorMessage: {
      emptyErrorMessage: _label('enter_last_name'),
    },
    nextId: 'selectGender',
    keyboardType: 'default',
    headerText: _label('last_name',{capitalize : true}),
  },
  selectGender: {
    id: 'selectGender',
    nextId: 'enterEmail',
    errorMessage: {
      emptyErrorMessage: _label('select_gender'),
    },
    headerText: _label('gender',{capitalize : true}),
    label: _label('select_gender'),
    dropDownList: [
      {
        id: 'M',
        text: 'Male',
      },
      {
        id: 'F',
        text: 'Female',
      },
    ],
  },
  enterEmail: {
    id: 'enterEmail',
    errorMessage: {
      emptyErrorMessage: _label('enter_email',{sentenceCase : true}),
      validationErrorMessage: _label('enter_valid_email',{sentenceCase : true}),
    },
    maxLength: 70,
    nextId: 'enterMobileNumber',
    keyboardType: 'email-address',
    headerText: _label('email_address',{capitalize : true}),
    onNextValidation: text => (isValidEmail(text)),
  },
  enterMobileNumber: {
    maxLength: 10,
    id: 'enterMobileNumber',
    errorMessage: {
      emptyErrorMessage: _label('enter_mobile_number',{sentenceCase : true}),
      validationErrorMessage: _label('enter_valid_mobile_number',{sentenceCase : true}),
    },
    onNextValidation: text => (isValidMobile(text)),
    nextId: 'enterDob',
    keyboardType: 'numeric',
    headerText: _label('mobile_number',{capitalize : true}),
  },
  enterDob: {
    id: 'enterDob',
    nextId: 'selectOccupation',
    errorMessage: {
      emptyErrorMessage: _label('enter_dob',{sentenceCase : true}),
      validationErrorMessage: _label('invalid_dob',
                                  undefined, {
                                          minDate : fecha.format(minDate, 'DD MMM YYYY'),
                                          maxDate : fecha.format(maxDate, 'DD MMM YYYY'),
                                  }),
    },
    onNextValidation: (text) => {
      const date = fecha.parse(text, 'DD/MM/YYYY');
      if (date < minDate || date > maxDate) {
        return false;
      }
      return true;
    },
    headerText: _label('date_of_birth'),
  },
  selectOccupation: {
    id: 'selectOccupation',
    nextId: 'selectMaritalStatus',
    errorMessage: {
      emptyErrorMessage: _label('select_occupation'),
    },
    headerText: _label('occupation',{capitalize : true}),
    label: _label('select_occupation'),
    dropDownList: [
      {id: '1', text: 'occupation 1'},
      {id: '2', text: 'occupation 2'},
      {id: '3', text: 'occupation 3'},
      {id: '4', text: 'occupation 4'},
      {id: '5', text: 'occupation 5'},
      {id: '6', text: 'occupation 6'},
    ],
  },
  selectMaritalStatus: {
    id: 'selectMaritalStatus',
    nextId: 'selectNationality',
    errorMessage: {
      emptyErrorMessage: _label('select_marital_status'),
    },
    headerText: _label('marital_status'),
    label: _label('select_status'),
    dropDownList: [
      {id: 'M', text: 'Married'},
      {id: 'U', text: 'Unmarried'},
    ],
  },
  selectNationality: {
    id: 'selectNationality',
    nextId: 'enterResidentialAddess',
    errorMessage: {
      emptyErrorMessage: _label('select_nationality',{sentenceCase : true}),
    },
    headerText: _label('nationality',{capitalize : true}),
    label: _label('select_nationality',{capitalize : true}),
    dropDownList: [
      {id: '94', text: 'India'},
    ],
  },
  enterResidentialAddress: {
    maxLength: 200,
    id: 'enterResidentialAddress',
    nextId: 'enterResidentialMobileNumber',
    errorMessage: {
      emptyErrorMessage: _label('enter_residential_address',{sentenceCase : true}),
    },
    keyboardType: 'default',
    headerText: _label('residential_address',{capitalize : true}),
  },
  enterCaptcha:{
    maxLength: 10,
    id: 'enterCaptcha',
    errorMessage: {
      emptyErrorMessage: _label('enter_captcha',{sentenceCase : true}),
    },
    keyboardType: 'default',
  },
  enterResidentialMobileNumber: {
    maxLength: 10,
    id: 'enterResidentialMobileNumber',
    errorMessage: {
      emptyErrorMessage: _label('enter_phone_number'),
      validationErrorMessage: _label('enter_valid_mobile_number'),
    },
    onNextValidation: text => (isValidMobile(text)),
    keyboardType: 'numeric',
    nextId: 'enterPinCode',
    headerText: _label('phone_number',{capitalize : true}),
  },
  enterPinCode: {
    maxLength: 6,
    id: 'enterPinCode',
    errorMessage: {
      emptyErrorMessage: _label('enter_pin_code',{sentenceCase : true}),
    },
    keyboardType: 'numeric',
    nextId: 'selectCity',
    headerText: _label('pin_code',{capitalize : true}),
  },
  selectCity: {
    id: 'selectCity',
    errorMessage: {
      emptyErrorMessage: _label('select_city',{sentenceCase : true}),
    },
    nextId: 'selectState',
    headerText: _label('city_or_town'),
    label: _label('select_city',{sentenceCase : true}),
    dropDownList: [
      {id: '1', text: 'City 1'},
      {id: '2', text: 'City 2'},
    ],
  },
  selectState: {
    id: 'selectState',
    nextId: 'selectPostOffice',
    errorMessage: {
      emptyErrorMessage: _label('select_state',{sentenceCase : true}),
    },
    headerText: _label('state',{capitalize : true}),
    label: _label('select_state',{sentenceCase : true}),
    dropDownList: [
      {id: '1', text: 'State 1'},
      {id: '2', text: 'State 2'},
    ],
  },
  selectPostOffice: {
    id: 'selectPostOffice',
    nextId: 'country',
    errorMessage: {
      emptyErrorMessage: _label('select_post_office',{sentenceCase : true}),
    },
    headerText: _label('post_office',{capitalize : true}),
    label: _label('select_post_office',{sentenceCase : true}),
    dropDownList: [
      {id: '1', text: 'Post Office 1'},
      {id: '2', text: 'Post Office 2'},
    ],
  },
  country: {
    id: 'country',
    nextId: 'enterOfficeAddress',
    errorMessage: {
      emptyErrorMessage: _label('select_country',{sentenceCase : true}),
    },
    headerText: _label('country',{capitalize : true}),
    label: _label('select_country',{sentenceCase : true}),
    dropDownList: [
      {id: '94', text: 'India'},
    ],
  },
  enterOfficeAddress: {
    office: 'true',
    maxLength: 200,
    id: 'enterOfficeAddress',
    errorMessage: {
      emptyErrorMessage: _label('enter_office_address',{sentenceCase : true}),
    },
    headerText: _label('office_address',{capitalize : true}),
    keyboardType: 'default',
    nextId: 'enterOfficeMobileNumber',

  },
  enterOfficeMobileNumber: {
    maxLength: 10,
    office: 'true',
    id: 'enterOfficeMobileNumber',
    onNextValidation: text => (isValidMobile(text)),
    errorMessage: {
      emptyErrorMessage: _label('enter_phone_number',{sentenceCase : true}),
      validationErrorMessage: _label('enter_valid_mobile_number',{sentenceCase : true}),
    },
    headerText: _label('phone_number',{capitalize : true}),
    keyboardType: 'numeric',
    nextId: 'enterOfficeAddress',
  },
  enterOfficePinCode: {
    maxLength: 6,
    office: 'true',
    id: 'enterOfficePinCode',
    errorMessage: {
      emptyErrorMessage: _label('enter_pin_code',{sentenceCase : true}),
    },
    headerText: _label('pin_code',{capitalize : true}),
    keyboardType: 'numeric',
    nextId: 'selectOfficeCity',
  },
  selectOfficeCity: {
    office: 'true',
    id: 'selectOfficeCity',
    errorMessage: {
      emptyErrorMessage: _label('select_city',{sentenceCase : true}),
    },
    nextId: 'selectOfficeState',
    headerText: _label('city_or_town'),
    label: _label('select_city',{sentenceCase : true}),
    dropDownList: [
      {id: '1', text: 'City 1'},
      {id: '2', text: 'City 2'},
    ],
  },
  selectOfficeState: {
    office: 'true',
    id: 'selectOfficeState',
    errorMessage: {
      emptyErrorMessage: _label('select_state',{sentenceCase : true}),
    },
    nextId: 'selectOfficePostOffice',
    headerText: _label('state',{capitalize : true}),
    label: _label('select_state',{sentenceCase : true}),
    dropDownList: [
      {id: '1', text: 'State 1'},
      {id: '2', text: 'State 2'},
    ],
  },
  selectOfficePostOffice: {
    office: 'true',
    id: 'selectOfficePostOffice',
    nextId: 'officeCountry',
    errorMessage: {
      emptyErrorMessage: _label('select_post_office',{sentenceCase : true}),
    },
    headerText: _label('post_office'),
    label: _label('select_post_office',{sentenceCase : true}),
    dropDownList: [
      {id: '1', text: 'Post Office 1'},
      {id: '2', text: 'Post Office 2'},
    ],
  },
  officeCountry: {
    office: 'true',
    id: 'officeCountry',
    nextId: 'enterOfficeAddress',
    errorMessage: {
      emptyErrorMessage: _label('select_country',{sentenceCase : true}),
    },
    headerText: _label('country',{capitalize:true}),
    label: _label('select_country',{sentenceCase : true}),
    dropDownList: [
      {id: '1', text: 'Country 1'},
      {id: '2', text: 'Country 2'},
    ],
  },
});

export const fieldsWithoutOfficeAddress = () => Object.keys(getFormObject()).filter(item =>
  (isEmpty(getFormObject()[item].office)));
export const fieldsWithOfficeAddress = () => Object.keys(getFormObject());

export default getFormObject;
