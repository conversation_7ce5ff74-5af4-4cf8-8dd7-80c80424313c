import React, {Component} from 'react';
import isEmpty from 'lodash/isEmpty';
import isUndefined from 'lodash/isUndefined';
import {connect} from 'react-redux';
import { Actions } from '../../navigation';
import * as PropTypes from 'prop-types';
import {View, Text, ScrollView, Image, Clipboard, StyleSheet, TouchableWithoutFeedback, ActivityIndicator} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import {getNewVoucherClick} from '../NewListing/RailsListingActions';
import {voucherState} from '../../Utils/RailsConstant';
import {trackNewListingClickEvent} from '../../railsAnalytics';
import giftIconOpen from '@mmt/legacy-assets/src/confetti_gift.webp';
import giftIconSuccess from '@mmt/legacy-assets/src/confetti_gift_success.webp';
import giftIconClose from '@mmt/legacy-assets/src/flat_gift.webp';
import copyIcon from '@mmt/legacy-assets/src/ic_copy_blue.webp';
import hotelImage from '@mmt/legacy-assets/src/ic-hotel.webp';
import rightArrow from '@mmt/legacy-assets/src/ic-arrowfwd-blue.webp';
import clock from '@mmt/legacy-assets/src/ic-clock-stroke.webp';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';

const addZero = (i) => {
  if (i < 10) {
    i = `0${i}`;
  }
  return i;
};

const RatingLinearGradient = ({rating}) => (
  <LinearGradient
    style={ratingLgStyles.container}
    colors={['#00d2ff', '#3a7bd5']}
    start={{x: 0.0, y: 0.0}}
    end={{x: 1.0, y: 0.0}}
  >
    <Text style={[ratingLgStyles.ratingText, fontStyle('semiBold'), getLineHeight(10)]}>
      {rating}/5
    </Text>
  </LinearGradient>
);

const ratingLgStyles = StyleSheet.create({
  container: {
    borderRadius: 10, alignItems: 'center', justifyContent: 'center',
  },
  ratingText: {
    fontSize: 10, color: colors.white, backgroundColor: colors.transparent, marginHorizontal: 7, marginVertical: 2,
  },
});

const ViewAllCard = ({deeplink}) => (
  <TouchableRipple onPress={() => {
    trackNewListingClickEvent('mob_rail_listing_v2_hotel_view_all_clicked');
    GenericModule.openDeepLink(deeplink);
  }}
  >
    <View>
      <Card style={{
        marginRight: 16, marginLeft: 2, marginVertical: 2, width: 132, borderRadius: 4, height: 160,
      }}
      >
        <View style={{marginTop: 30, marginLeft: 16}}>
          <Image source={hotelImage} style={{width: 36, height: 36, marginBottom: 12}} />
          <Text style={[viewAllCardStyles.headerText, fontStyle('bold'), getLineHeight(16)]}
          >{_label('view')}
          </Text>
          <Text style={[viewAllCardStyles.subHeaderText, fontStyle('bold'), getLineHeight(16)]}
          >{_label('all_hotels')}
          </Text>
          <Image source={rightArrow} style={{width: 27, height: 6}} />
        </View>
      </Card>
    </View>
  </TouchableRipple>
);

const viewAllCardStyles = StyleSheet.create({
  headerText: { color: colors.azure, fontSize: 16, marginBottom: 4 },
  subHeaderText: { color: colors.azure, fontSize: 16, marginBottom: 12 },
});

const HotelCard = ({hotel, deeplink}) => (
  <TouchableRipple onPress={() => {
    trackNewListingClickEvent('mob_rail_listing_v2_hotel_clicked');
    GenericModule.openDeepLink(deeplink);
  }}
  >
    <View>
      <Card style={{
        marginHorizontal: 2, marginVertical: 2, minWidth: 132, borderRadius: 4,
      }}
      >
        <View style={hotelCardStyles.hotelImageContainer}>
          <Image source={{uri: `https:${hotel.mainImages[0]}`}} resizeMode="stretch" style={hotelCardStyles.hotelImage} />
        </View>
        <View style={hotelCardStyles.ratingContainer}>
          <RatingLinearGradient rating={hotel.flyfishReviewSummary.MMT.cumulativeRating} />
        </View>
        <View style={hotelCardStyles.fareContainer}>
          <Text style={[hotelCardStyles.fareText, fontStyle('bold'), getLineHeight(16)]}>
            {numAppendedWithRuppeeSymbol(hotel.displayFare.slashedPrice.value)}
          </Text>
          <Text style={[hotelCardStyles.hotelName, fontStyle('medium'), getLineHeight(12)]}>{hotel.name}
          </Text>
          <Text style={[hotelCardStyles.hotelArea, fontStyle('regular'), getLineHeight(12)]}>{hotel.address.line2}</Text>
        </View>
      </Card>
    </View>
  </TouchableRipple>
);

const hotelCardStyles = StyleSheet.create({
  hotelImageContainer: {
    height: 80, backgroundColor: colors.grey, borderTopLeftRadius: 4, borderTopRightRadius: 4,
  },
  hotelImage: {flex: 1, width: '100%'},
  ratingContainer: {position: 'absolute', top: 72, right: 4},
  fareContainer: {
    backgroundColor: colors.transparent, marginHorizontal: 8, marginTop: 12,
    marginBottom: 10, borderBottomLeftRadius: 4, borderBottomRightRadius: 4,
  },
  fareText: {
    fontSize: 16,color: colors.black, marginBottom: 6,
  },
  hotelName: {
    fontSize: 12, color: colors.black, marginBottom: 6,
  },
  hotelArea: {fontSize: 12, color: colors.defaultTextColor},
});


const GiftCardLinearGradient = ({onClick, loading}) =>
  (
    <TouchableRipple onPress={onClick}>
      <LinearGradient
        style={[giftCardLgStyles.lg, {height: 68}]}
        colors={[colors.seance, colors.governorBay]}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
      >
        {
          loading &&
          <View style={giftCardLgStyles.bottomContainer}>
            <View style={{paddingHorizontal: 24, justifyContent: 'center'}}>
              <ActivityIndicator
                styleAttr="Inverse"
                color="white"
                size="small"
              />
            </View>
             <View>
              <Text style={[giftCardLgStyles.text, fontStyle('bold'), getLineHeight(16)]}>
                {_label('unlocking_offer')}
              </Text>
            </View>
          </View>
        }
        {
          !loading &&
          <View style={{flexDirection: 'row', flex: 1}}>
            <View style={{justifyContent: 'flex-end'}}>
              <Image source={giftIconClose} style={giftCardLgStyles.image} />
            </View>
            <View style={{justifyContent: 'center'}}>
              <Text style={[giftCardLgStyles.text, fontStyle('bold'), getLineHeight(16)]}>
                {_label('tap_to_win')}
              </Text>
              <Text style={[giftCardLgStyles.subText, fontStyle('bold'), getLineHeight(12)]}>
                {_label('unlock_special_offer')}
              </Text>
            </View>
          </View>
        }
      </LinearGradient>
    </TouchableRipple>
  );

const VoucherCodeContainer = ({voucherCode, copyVoucherCode}) => (
  <TouchableWithoutFeedback onPress={copyVoucherCode}>
    <View style={voucherCodeStyle.container}>
      <View style={voucherCodeStyle.subContainer}>
        <View>
          <Text style={[voucherCodeStyle.voucherCode, fontStyle('bold'), getLineHeight(12)]}>
          {_label('voucher_code', { uppercase: true })}
          </Text>
          <Text style={[voucherCodeStyle.voucherCodeText, fontStyle('black'), getLineHeight(16)]}>{voucherCode}</Text>
        </View>
        <CopyCode />
      </View>
    </View>
  </TouchableWithoutFeedback>
);

const voucherCodeStyle = StyleSheet.create({
  container: {flexDirection: 'row'},
  subContainer: {
    backgroundColor: colors.hawkesBlue,
    flex: 1,
    marginTop: 16,
    justifyContent: 'space-between',
    marginHorizontal: 8,
    borderRadius: 4,
    paddingBottom: 8,
    paddingTop: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  voucherCode: {color: colors.defaultTextColor, fontSize: 12},
  voucherCodeText: {color: colors.black, fontSize: 16},
});

const giftCardLgStyles = StyleSheet.create({
  lg: {
    borderRadius: 4, justifyContent: 'center',
  },
  bottomContainer: {flexDirection: 'row'},
  image: {width: 91, height: 42, resizeMode: 'contain'},
  text: {fontSize: 16, color: colors.white},
  subText: {fontSize: 12, color: colors.white, opacity: 0.8, paddingTop: 6},
});

class LinearGradientWithTimer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: new Date(),
      endDate: new Date(this.props.endTime),
    };
  }

  componentDidMount() {
    this.timerId = setInterval(() => {
      this.setState({startDate: new Date(this.state.startDate.getTime() + 1000)});
    }, 1000);
  }

  componentWillUnmount() {
    clearInterval(this.timerId);
  }

  _goToHotelCouponPage = () => {
    Actions.railsHotelsCouponSuccessPage();
  };

  render() {
    const {startDate, endDate} = this.state;
    const difference = endDate - startDate;
    let seconds = Math.floor(difference / 1000);
    let minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    seconds %= 60;
    minutes %= 60;
    return (
      <View style={lgWithTimer.container}>
        <LinearGradient
          style={lgWithTimer.lg}
          colors={[colors.orange, colors.radicalRed]}
          start={{x: 0.0, y: 0.0}}
          end={{x: 1.0, y: 0.0}}
        >
          <View style={lgWithTimer.bottomContainer}>
            <Image source={clock} style={lgWithTimer.image} />
            <Text style={[lgWithTimer.timerText, fontStyle('bold'), getLineHeight(14)]}>
              {difference > 0 ? `${addZero(hours)}h : ${addZero(minutes)}m : ${addZero(seconds)}s` : _label('expired')}
            </Text>
          </View>
        </LinearGradient>
      </View>
    );
  }
}

const lgWithTimer = StyleSheet.create({
  container: {flexDirection: 'row'},
  lg: {
    borderRadius: 34, justifyContent: 'center', paddingVertical: 4,
  },
  bottomContainer: {flexDirection: 'row', alignItems: 'center'},
  image: {width: 15, height: 15, marginLeft: 8},
  timerText: {
    fontSize: 14,
    color: colors.white,
    backgroundColor: colors.transparent,
    marginRight: 8,
    marginLeft: 8,
  },
});

export const CopyCode = () => (
  <View style={{alignItems: 'center'}}>
    <View>
      <Image source={copyIcon} style={{width: 24, height: 24}} />
    </View>
    <Text style={[{color: colors.azure, fontSize: 10}, fontStyle('bold'), getLineHeight(10)]}>{_label('copy')}</Text>
  </View>
);

const VoucherCodeWithTimer = ({voucherObject}) => (
  <View>
    <View style={voucherWithTimer.lgContainer}>
      <LinearGradientWithTimer
        issueTime={voucherObject.voucher.voucherIssueTime}
        endTime={voucherObject.voucher.voucherExpirationTime}
      />
    </View>
    <LinearGradient
      style={{borderRadius: 4}}
      colors={[colors.seance, colors.governorBay]}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 0}}
    >
      <View style={voucherWithTimer.bottomContainer}>
        <View style={{flexDirection: 'row', paddingBottom: 8}}>
          <Image source={giftIconOpen} style={voucherWithTimer.image} />
          <View style={{paddingLeft: 12, justifyContent: 'center'}}>
            <Text style={[voucherWithTimer.offText, fontStyle('bold'), getLineHeight(16)]}>
              {_label('OFF_on_hotel_booking', undefined, {
                discount: voucherObject.voucher.discountPercentage,
              })}
            </Text>
            <Text style={[voucherWithTimer.offSubText, fontStyle('bold'), getLineHeight(12)]}>
              {_label('book_to_reveal_voucher')}
            </Text>
          </View>
        </View>
        <View style={voucherWithTimer.voucherCodeContainer}>
          <Text style={[voucherWithTimer.voucherText, fontStyle('bold'), getLineHeight(12)]}>
            {_label('voucher_code', { uppercase: true })}
          </Text>
          <Text style={[voucherWithTimer.voucherCodeText, fontStyle('black'), getLineHeight(16)]}>
            {voucherObject.voucher.voucherCode.toUpperCase()}
          </Text>
        </View>
      </View>
    </LinearGradient>
  </View>
);

const voucherWithTimer = StyleSheet.create({
  lgContainer: {
    position: 'absolute',
    right: 12,
    top: -12,
    elevation: 1,
    zIndex: 1,
  },
  bottomContainer: {flex: 1, marginHorizontal: 12, marginVertical: 12},
  voucherCodeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: colors.white,
    padding: 4,
    marginTop: 8,
    borderRadius: 4,
  },
  image: {width: 69, height: 46, resizeMode: 'contain'},
  offText: {paddingTop: 7, fontSize: 16,  color: colors.white},
  offSubText: {paddingTop: 5, fontSize: 12,  color: colors.white, opacity: 0.8},
  voucherText: {fontSize: 12, color: colors.lightTextColor},
  voucherCodeText: {fontSize: 16, color: colors.black},
});

const ShowVoucherCodeWithTimer = ({copyVoucherCode, voucherObject, openTnc}) => (
  <View>
    <View style={showVoucherWithTimer.lgContainer}>
      <LinearGradientWithTimer
        issueTime={voucherObject.voucher.voucherIssueTime}
        endTime={voucherObject.voucher.voucherExpirationTime}
      />
    </View>
    <View>
      <LinearGradient
        style={{borderRadius: 4}}
        colors={[colors.seance, colors.governorBay]}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
      >
        <View style={showVoucherWithTimer.bottomContainer}>
          <View style={{flexDirection: 'row'}}>
            <Image source={giftIconSuccess} style={showVoucherWithTimer.image} />
            <View style={{paddingLeft: 12, justifyContent: 'center'}}>
              <Text style={[showVoucherWithTimer.offText, fontStyle('light'), getLineHeight(22)]}>
                {`${_label('congratulations')}!`}
              </Text>
              <Text style={[showVoucherWithTimer.offSubText, fontStyle('bold'), getLineHeight(14)]}>
                {_label('off_on_hotels', undefined, {
                  discount: voucherObject.voucher.discountPercentage,
                })}
              </Text>
            </View>
          </View>
          <VoucherCodeContainer
            voucherCode={voucherObject.voucher.voucherCode.toUpperCase()}
            copyVoucherCode={copyVoucherCode}
          />
        </View>
      </LinearGradient>
      <View style={showVoucherWithTimer.tnContainer}>
        <Text style={[{fontSize: 10, color: colors.defaultTextColor}, fontStyle('regular'), getLineHeight(10)]}>
          {_label('discount_will_be_sent')}
        </Text>
        <TouchableRipple onPress={openTnc}>
          <Text style={[{fontSize: 10, color: colors.azure}, fontStyle('bold'), getLineHeight(10)]}>
            {_label('view_tnc')}
          </Text>
        </TouchableRipple>
      </View>
    </View>
  </View>
);

const showVoucherWithTimer = StyleSheet.create({
  lgContainer: {
    position: 'absolute',
    right: 12,
    top: -12,
    elevation: 1,
    zIndex: 1,
  },
  tnContainer:{
    backgroundColor: colors.white,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  bottomContainer: {flex: 1, marginHorizontal: 12, marginVertical: 12},
  image: {width: 86, height: 58, resizeMode: 'contain'},
  offText: {paddingVertical: 3, fontSize: 22,color: colors.white},
  offSubText: {paddingVertical: 2, fontSize: 14, color: colors.white},
});

class VoucherCodeWrapper extends Component {
  copyVoucherCode = () => {
    Clipboard.setString(this.props.voucherObject.voucher.voucherCode);
    showShortToast( );
    trackNewListingClickEvent('mob_rail_listing_v2_voucher_copy_clicked');
  };

  onHotelTncClick = () => {
    trackNewListingClickEvent('mob_rail_listing_v2_voucher_view_t&c_clicked');
    Actions.openWebView({
      headerText: _label('voucher_tnc'),
      url: 'https://promos.makemytrip.com/dh-railusers-15022019.html',
      headerIcon: backIcon,
    });
  };

  render() {
    const {
      voucherObject, onNewVoucherClick, showVoucher, loading,
    } = this.props;
    if (isEmpty(voucherObject)) {
      return null;
    } else if (!isEmpty(voucherObject.error)) {
      return null;
    } else if (isEmpty(voucherObject.voucherStatus)) {
      return null;
    } else if (voucherObject.voucherStatus === voucherState.ACTIVE) {
      return showVoucher ?
        <ShowVoucherCodeWithTimer
          copyVoucherCode={this.copyVoucherCode}
          voucherObject={voucherObject}
          openTnc={this.onHotelTncClick}
        />
        :
        <VoucherCodeWithTimer
          voucherObject={voucherObject}
        />;
    } else if (voucherObject.voucherStatus === voucherState.EXPIRED) {
      return null;
    }
    return (
      !showVoucher ? <GiftCardLinearGradient onClick={onNewVoucherClick} loading={loading} /> : null
    );
  }
}

class HotelsCrossSell extends React.Component {
  render() {
    const {
      hotelsError, voucherError, showVoucher, voucherLoading,
    } = this.props;
    if (!showVoucher && !voucherError) {
      return (
        <View testID={this.props?.id}>
          <View style={[hotelsStyle.container, {backgroundColor: colors.grayBg}]}>
            <VoucherCodeWrapper
              voucherObject={this.props.voucherObject}
              onNewVoucherClick={this.props.getNewVoucherClick}
              showVoucher={showVoucher}
              loading={voucherLoading}
            />
          </View>
          {
            this.props.showMargin &&
            <View style={{backgroundColor: colors.grayBg, height: 20}} />
          }
        </View>
      );
    }
    if (showVoucher) {
      return (
        <View>
          {
            !voucherError &&
            <View>
              <View style={hotelsStyle.container}>
                <VoucherCodeWrapper
                  voucherObject={this.props.voucherObject}
                  onNewVoucherClick={this.props.getNewVoucherClick}
                  showVoucher={showVoucher}
                  loading={voucherLoading}
                />
              </View>
              {
                this.props.showMargin &&
                <View style={{backgroundColor: colors.white, height: 20}} />
              }
            </View>
          }
          {
            !hotelsError &&
            <View style={hotelsStyle.container}>
              <View>
                <Text style={[hotelsStyle.heading, fontStyle('light'), getLineHeight(22)]}>
                  {this.props.heading}
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{marginTop: 12}}>
                  {
                    this.props.hotelsList.map(item => (
                      <View key={item.id} style={{marginRight: 8}}>
                        <HotelCard hotel={item} deeplink={this.props.cardDetails.viewMore.deepLink} />
                      </View>))
                  }
                  <View>
                    <ViewAllCard deeplink={this.props.cardDetails.viewMore.deepLink} />
                  </View>
                </ScrollView>
              </View>
              {
                this.props.showMargin &&
                <View style={{backgroundColor: colors.white, height: 20}} />
              }
            </View>
          }
        </View>
      );
    }
    return null;
  }
}

const hotelsStyle = StyleSheet.create({
  container: {
    paddingTop: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    paddingBottom: 8,
  },
  heading: {
    fontSize: 22,
    color: colors.black,
  },
});

const mapStateToProps = (state) => {
  const {railsListing: {hotelsData, voucherObject, voucherLoading}} = state;
  let hotelsError = true;
  let voucherError = true;
  try {
    const {hotelsApiError} = hotelsData;
    if (!isEmpty(voucherObject) &&
      isEmpty(voucherObject.error) &&
      voucherObject.voucherStatus !== voucherState.EXPIRED) {
      voucherError = false;
    }
    if (hotelsApiError) {
      return {
        hotelsError: hotelsApiError,
        voucherObject,
        voucherError,
        voucherLoading,
      };
    }
    const {cardData: {Cross_Sell_Personalised: {data: {heading, cardDetails}}}} = hotelsData;
    const lastHotelsCard = cardDetails[cardDetails.length - 1];
    const hotelsList = lastHotelsCard.hotels;
    const validHotelsList = hotelsList.filter(item => (
      !isUndefined(item.flyfishReviewSummary.MMT.cumulativeRating) &&
      !isUndefined(item.displayFare.slashedPrice.value) &&
      !isUndefined(item.address.line2)));
    hotelsError = !!(isEmpty(validHotelsList) ||
      isEmpty(lastHotelsCard.viewMore.deepLink));
    return {
      heading,
      cardDetails: lastHotelsCard,
      hotelsList: validHotelsList,
      hotelsError,
      voucherObject,
      voucherError,
      voucherLoading,
    };
  } catch (e) {
    console.log('error is,', e);
    return {
      hotelsError,
      voucherObject,
      voucherError,
      voucherLoading,
    };
  }
};

const mapDispatchToProps = dispatch => ({
  getNewVoucherClick: () => { dispatch(getNewVoucherClick); },
});

export default connect(mapStateToProps, mapDispatchToProps)(HotelsCrossSell);

RatingLinearGradient.propTypes = {
  rating: PropTypes.number.isRequired,
};

HotelCard.propTypes = {
  deeplink: PropTypes.string.isRequired,
  hotel: PropTypes.object.isRequired,
};

GiftCardLinearGradient.propTypes = {
  onClick: PropTypes.func.isRequired,
  loading: PropTypes.bool,
};

LinearGradientWithTimer.propTypes = {
  endTime: PropTypes.number.isRequired,
  issueTime: PropTypes.number.isRequired,
};

VoucherCodeWithTimer.propTypes = {
  voucherObject: PropTypes.object.isRequired,
};

ShowVoucherCodeWithTimer.propTypes = {
  copyVoucherCode: PropTypes.func.isRequired,
  voucherObject: PropTypes.object.isRequired,
  openTnc: PropTypes.func,
};

VoucherCodeWrapper.propTypes = {
  voucherObject: PropTypes.object,
  onNewVoucherClick: PropTypes.func.isRequired,
  showVoucher: PropTypes.bool,
  loading: PropTypes.bool,
};

VoucherCodeWrapper.defaultProps = {
  voucherObject: {},
};

HotelsCrossSell.propTypes = {
  cardDetails: PropTypes.object,
  getNewVoucherClick: PropTypes.func.isRequired,
  heading: PropTypes.string,
  hotelsError: PropTypes.bool.isRequired,
  hotelsList: PropTypes.array,
  voucherError: PropTypes.bool.isRequired,
  voucherObject: PropTypes.object,
  showMargin: PropTypes.bool,
  showVoucher: PropTypes.bool,
  voucherLoading: PropTypes.bool,
  id: PropTypes.string,
};

HotelsCrossSell.defaultProps = {
  cardDetails: {},
  voucherObject: {},
  heading: '',
  hotelsList: [],
  showMargin: false,
};

VoucherCodeContainer.propTypes = {
  voucherCode: PropTypes.string,
  copyVoucherCode: PropTypes.func,
};

ViewAllCard.propTypes = {
  deeplink: PropTypes.string,
};
