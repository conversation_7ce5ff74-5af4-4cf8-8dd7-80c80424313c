import React, { Component } from 'react';
import { Image, Text, View } from 'react-native';
import { Actions } from '../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { trackNewListingClickEvent } from '../../railsAnalytics';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';

const addZero = (i) => i < 10 ? `0${i}` : i;

import giftIconOpen from '@mmt/legacy-assets/src/circle_gift.webp';
class HotelCrossSellVoucher extends Component {

  constructor(props) {
    super(props);
    this.state = {
      startDate: new Date(),
      endDate: new Date(props.voucherObject.voucher.voucherExpirationTime),
    };
  }

  componentDidMount() {
    this.timerId = setInterval(() => {
      this.setState({startDate: new Date(this.state.startDate.getTime() + 1000)});
    }, 1000);
  }

  componentWillUnmount() {
    clearInterval(this.timerId);
  }

  onHotelTncClick = () => {
    trackNewListingClickEvent('mob_rail_listing_v2_voucher_view_t&c_clicked');
    Actions.openWebView({
      headerText: _label('voucher_tnc'),
      url: 'https://promos.makemytrip.com/dh-railusers-15022019.html',
      headerIcon: backIcon,
    });
  };

  render() {
    let { voucherObject } = this.props;
    const {startDate, endDate} = this.state;
    const difference = endDate - startDate;
    let seconds = Math.floor(difference / 1000);
    let minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    seconds %= 60;
    minutes %= 60;
    return (
      <View style={{ backgroundColor: colors.white }} testID={this.props?.id}>
        <View style={{justifyContent: 'center', paddingHorizontal: 32, marginTop: 66}}>
          <Text style={{ fontSize: 14, ...fontStyle('bold'), ...getLineHeight(14),color: colors.defaultTextColor }}>
            {_label('unlocked_discount_on_hotel_booking')}
          </Text>
          <Text style={{ fontSize: 32, ...fontStyle('black'), ...getLineHeight(32), color: colors.black, paddingTop: 5 }}>{_label('num_off', undefined, { num: voucherObject.voucher.discountPercentage })}</Text>
          <Text style={{ fontSize: 12, ...fontStyle('bold'), ...getLineHeight(12), color: colors.red, paddingTop: 10 }}>
            { `Valid for ${difference > 0 ? `${addZero(hours)}h : ${addZero(minutes)}m : ${addZero(seconds)}s` : _label('expired')}`}
          </Text>
        </View>
        <View style={{ justifyContent: 'center', backgroundColor: colors.hawkesBlue, marginTop: 30, paddingVertical: 12, paddingHorizontal: 32}}>
          <View>
            <Text style={{ fontSize: 12, ...fontStyle('bold'), ...getLineHeight(12), color: colors.lightTextColor }}>{_label('voucher_code')}</Text>
            <Text style={{ fontSize: 24, ...fontStyle('black'), ...getLineHeight(24), color: colors.black, paddingTop: 5 }}>{voucherObject.voucher.voucherCode.toUpperCase()}</Text>
          </View>
        </View>
        <View>
        <View style={{paddingVertical: 12, paddingHorizontal: 32 }}>
          <Text style={{ fontSize: 10, ...fontStyle('regular'), ...getLineHeight(10), color: colors.defaultTextColor }}>{_label('reveal_voucher_code')}</Text>
        </View>
        <View style={{alignItems: 'flex-end'}}>
          <TouchableRipple onPress={this.onHotelTncClick}>
            <Text style={{ fontSize: 10, ...fontStyle('regular'), ...getLineHeight(10), color: colors.azure, marginTop:8, padding: 16}}>{_label('view_tnc')}</Text>
          </TouchableRipple>
        </View>
        </View>
        <LinearGradient
          style={{position: 'absolute', width: 96, height: 96, borderRadius: 48, left: 36, top: -48, justifyContent: 'flex-end',alignItems: 'center'}}
          colors={[colors.seance, colors.governorBay]}
          start={{x: 0, y: 1}}
          end={{x: 1, y: 0}}
        >
          <Image source={giftIconOpen} style={{width:96, height: 96, resizeMode: 'contain'}}/>
        </LinearGradient>
      </View>
    );
  }
}

export default HotelCrossSellVoucher;

HotelCrossSellVoucher.propTypes = {
  voucherObject: PropTypes.object,
  'voucherObject.voucher': PropTypes.object,
  'voucherObject.voucher.voucherExpirationTime': PropTypes.number,
  'voucherObject.voucher.discountPercentage': PropTypes.number,
  'voucherObject.voucher.voucherCode': PropTypes.string,
  id: PropTypes.string,
};
