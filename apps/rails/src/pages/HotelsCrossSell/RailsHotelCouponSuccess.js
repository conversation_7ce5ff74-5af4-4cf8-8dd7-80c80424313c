import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import {connect} from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import LottieView from 'lottie-react-native';
import { Actions } from '../../navigation';
import React from 'react';
import {
  ActivityIndicator,
  Animated,
  Clipboard,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import {CopyCode} from './HotelsCrossSell';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import {HOTELS_LANDING_DEEPLINK} from '../../RailsConfig';
import {voucherState} from '../../Utils/RailsConstant';
import {trackCongratsPageClickEvents, trackCongratsPageLoad} from '../../railsAnalytics';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';

import giftImage from '@mmt/legacy-assets/src/ic_giftboxlarge_plus_confetti.webp';
import hotelImage from '@mmt/legacy-assets/src/ic_hotel.webp';
import closeIcon from '@mmt/legacy-assets/src/close-white.webp';
import lottieJson from '@mmt/legacy-assets/src/LottieAnimations/confettie/data.json';
const SearchHotelLinearGradient = () => (
  <TouchableRipple onPress={() => {
    trackCongratsPageClickEvents('mob_rail_voucher_congrats_search_hotels_clicked');
    GenericModule.openDeepLink(HOTELS_LANDING_DEEPLINK);
  }}
  >
    <View>
      <LinearGradient
        style={searchHotelLg.lg}
        colors={['#53B2FE', '#065AF3']}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
      >
        <View >
          <Text style={[searchHotelLg.hotelText, fontStyle('black'), getLineHeight(12)]}>
            {_label('search_hotels', { uppercase: true })}
          </Text>
        </View>
      </LinearGradient>
    </View>
  </TouchableRipple>
);

const searchHotelLg = StyleSheet.create({
  lg: {
    borderRadius: 96,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  hotelText: {
    fontSize: 12,
    color: colors.white,
    backgroundColor: colors.transparent,
  },
});

const CongratulationsContainer = () => (
  <View style={congratsStyle.container}>
    <Text style={[congratsStyle.congratsText, fontStyle('black'), getLineHeight(32)]}>
      {`${_label('congratulations')}!`}
    </Text>
    <Text style={[congratsStyle.bookHotelText, fontStyle('bold'), getLineHeight(14)]}>
      {_label('hotel_at_discounted_rate')}
    </Text>
  </View>
);

const congratsStyle = StyleSheet.create({
  container: {paddingTop: 8, alignItems: 'center'},
  congratsText: {
    fontSize: 32,  color: colors.white, backgroundColor: colors.transparent,
  },
  bookHotelText: {
    fontSize: 14, color: colors.white, backgroundColor: colors.transparent, marginTop: 8,
  },
});

const VoucherCodeContainer = ({voucherCode, copyVoucherCode}) => (
  <TouchableWithoutFeedback onPress={copyVoucherCode}>
    <View style={voucherCodeStyle.container}>
      <View style={voucherCodeStyle.subContainer}>
        <View>
          <Text style={[voucherCodeStyle.voucherCode, fontStyle('bold'), getLineHeight(12)]}>{_label('coupon_code', { uppercase: true })}</Text>
          <Text style={[voucherCodeStyle.voucherCodeText, fontStyle('bold'), getLineHeight(16)]}>{voucherCode}</Text>
        </View>
        <CopyCode />
      </View>
    </View>
  </TouchableWithoutFeedback>
);

const voucherCodeStyle = StyleSheet.create({
  container: {flexDirection: 'row'},
  subContainer: {
    backgroundColor: colors.hawkesBlue, marginTop: 16, flex: 1, justifyContent: 'space-between', marginHorizontal: 22, borderRadius: 6, padding: 12, flexDirection: 'row',
  },
  voucherCode: {color: colors.lightTextColor, fontSize: 12},
  voucherCodeText: {color: colors.black, fontSize: 16},
});

class TimerComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: new Date().getTime(),
      endDate: this.props.endTime,
    };
  }

  componentDidMount() {
    this.timerId = setInterval(() => {
      this.setState({startDate: new Date()});
    }, 1000);
  }

  componentWillUnmount() {
    clearInterval(this.timerId);
  }

  render() {
    const {startDate, endDate} = this.state;
    const difference = endDate - startDate;
    let seconds = Math.floor(difference / 1000);
    let minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    seconds %= 60;
    minutes %= 60;
    return (
      <View>
        <Text style={{color: colors.red, fontSize: 12, ...fontStyle('bold'), ...getLineHeight(12)}}>
          Valid for {`${hours}h : ${minutes}m : ${seconds}s`}
        </Text>
      </View>
    );
  }
}

class GiftImageAnimation extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      animatedValue: new Animated.Value(0),
    };
  }
  componentDidMount() {
    Animated.loop(Animated.sequence([
      Animated.timing(this.state.animatedValue, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(this.state.animatedValue, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ])).start();
  }

  render() {
    return (
      <Animated.Image
        source={giftImage}
        style={{
          height: 64,
width: 84,
backgroundColor: colors.transparent,
          transform: [
            {
              scaleX: this.state.animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 1.2],
              }),
            },
            {
              scaleY: this.state.animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 1.2],
              }),
            },
          ],
        }}
      />
    );
  }
}

class ConfettieView extends React.PureComponent {
  componentDidMount() {
    if (!isEmpty(this._animationRef)) {
      this._animationRef.play();
    }
  }
  render() {
    return (
      <LottieView
        ref={(animation) => {
          this._animationRef = animation;
        }}
        source={lottieJson}
        speed={1}
      />
    );
  }
}

class RailsHotelVoucherSuccess extends React.Component {
  constructor(props) {
    super(props);
    this.counterFlag = true;
    this.state = {
      percentageCounter: 0,
    };
    trackCongratsPageLoad();
  }

  onHotelTncClick = () => {
    trackCongratsPageClickEvents('mob_rail_voucher_congrats_view_t&c_clicked');
    Actions.openWebView({
      headerText: _label('voucher_tnc'),
      url: 'https://promos.makemytrip.com/dh-railusers-15022019.html',
      headerIcon: backIcon,
    });
  };

  copyVoucherCode = () => {
    Clipboard.setString(this.props.voucherObject.voucher.voucherCode);
    showShortToast(_label('coupon_copied_to_clipboard'));
    trackCongratsPageClickEvents('mob_rail_voucher_congrats_copy_clicked');
  };

  componentDidUpdate() {
    if (this.counterFlag) {
      this.counterFlag = false;
      this._percentageCounter = setInterval(() => {
        if (this.state.percentageCounter === this.props.voucherObject.voucher.discountPercentage) {
          clearInterval(this._percentageCounter);
        } else {
          this.setState(state => ({percentageCounter: state.percentageCounter + 1}));
        }
      }, 40);
    }
  }


  componentDidMount() {
    if (this.counterFlag) {
      if (!this.props.loading) {
        this.counterFlag = false;
        this._percentageCounter = setInterval(() => {
          if (this.state.percentageCounter === this.props.voucherObject.voucher.discountPercentage) {
            clearInterval(this._percentageCounter);
          } else {
            this.setState(state => ({percentageCounter: state.percentageCounter + 1}));
          }
        }, 40);
      }
    }
  }

  render() {
    const {voucherObject, loading, onCloseClick} = this.props;
    if (loading) {
      return (
        <View style={{flex: 1}}>
          <LinearGradient
            style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}
            colors={['#862ca7', '#3b0696']}
            start={{x: 0.0, y: 0.0}}
            end={{x: 0.0, y: 1.0}}
          >
            <ActivityIndicator size="large" color={colors.white} />
          </LinearGradient>
        </View>
      );
    }
    return (
      <View style={{flex: 1}}>
        <LinearGradient
          style={styles.lgContainer}
          colors={['#862ca7', '#3b0696']}
          start={{x: 0.0, y: 0.0}}
          end={{x: 0.0, y: 1.0}}
        >
          <View style={styles.confettieContainer}>
            <ConfettieView />
          </View>
          <ScrollView style={{flex: 1, width: '100%'}}>
            <TouchableRipple onPress={onCloseClick}>
              <Image
                style={styles.closeImage}
                source={closeIcon}
              />
            </TouchableRipple>
            <View style={{alignSelf: 'center'}}>
              <GiftImageAnimation />
            </View>
            <CongratulationsContainer />
            <View style={styles.bottomContainer}>
              <View style={styles.hotelImageContainer}>
                <Image
                  source={hotelImage}
                  style={{width: 56, height: 56}}
                />
              </View>
              <Card style={{
backgroundColor: colors.white, flex: 1, borderRadius: 10, height: 290, flexDirection: 'row',
}}
              >
                <View style={{flex: 1}}>
                  <View style={styles.discountContainer}>
                    <Text style={[styles.offText, fontStyle('bold'), getLineHeight(32)]}>
                      {this.state.percentageCounter}% off
                    </Text>
                    <Text style={[styles.discountText, fontStyle('bold'), getLineHeight(14)]}>
                      {
                        _label('flat_discount_on_hotels', undefined, {
                          discount: numAppendedWithRuppeeSymbol(voucherObject.voucher.discountAmount),
                        })
                      }
                    </Text>
                  </View>
                  <View style={styles.tncContainer}>
                    <TimerComponent
                      issueTime={voucherObject.voucher.voucherIssueTime}
                      endTime={voucherObject.voucher.voucherExpirationTime}
                    />
                    <TouchableRipple onPress={this.onHotelTncClick}>
                      <Text style={[styles.tncText, fontStyle('bold'), getLineHeight(12)]}>
                        {_label('view_tnc')}
                      </Text>
                    </TouchableRipple>
                    <VoucherCodeContainer
                      voucherCode={voucherObject.voucher.voucherCode}
                      copyVoucherCode={this.copyVoucherCode}
                    />
                  </View>
                </View>
                <View style={styles.leftCircle} />
                <View style={styles.rightCircle} />
              </Card>
              <View style={styles.searchHotelContainer}>
                <SearchHotelLinearGradient />
              </View>
            </View>
            <TouchableRipple onPress={() => {
              trackCongratsPageClickEvents('mob_rail_voucher_congrats_back_to_trains_clicked');
              onCloseClick();
            }}
            >
              <View style={{alignItems: 'center'}}>
                <Text style={[styles.backToTrainsText, fontStyle('bold'), getLineHeight(14)]}>
                  {_label('back_to_trains', { uppercase: true })}
                </Text>
              </View>
            </TouchableRipple>
          </ScrollView>
        </LinearGradient>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  lgContainer: {paddingTop: 20, flex: 1, alignItems: 'center'},
  closeImage: {height: 32, width: 32, marginLeft: 20},
  confettieContainer: {
    top: 0, width: 400, height: 400, position: 'absolute',
  },
  bottomContainer: {
    borderRadius: 10,
    marginTop: 32,
    marginBottom: 38,
    marginHorizontal: 45,
    paddingTop: 28,
    paddingBottom: 12,
  },
  hotelImageContainer: {
    elevation: 2, zIndex: 2, position: 'absolute', alignSelf: 'center', top: 0,
  },
  discountContainer: {
    flex: 1, alignItems: 'center', justifyContent: 'center', backgroundColor: colors.transparent,
  },
  offText: { fontSize: 32, color: colors.black},
  discountText: { fontSize: 14, color: colors.black},
  tncText: {
    color: colors.azure, fontSize: 12,  marginTop: 4,
  },
  tncContainer: {flex: 1, alignItems: 'center'},
  leftCircle: {
    backgroundColor: colors.violetBlue, height: 30, width: 30, position: 'absolute', alignSelf: 'center', left: -15, borderRadius: 30,
  },
  rightCircle: {
    backgroundColor: colors.violetBlue, height: 30, width: 30, position: 'absolute', alignSelf: 'center', right: -15, borderRadius: 30,
  },
  searchHotelContainer: {
    position: 'absolute', bottom: 0, alignSelf: 'center', elevation: 2, zIndex: 2,
  },
  backToTrainsText: {
    color: colors.white, fontSize: 14,  backgroundColor: colors.transparent,
  },
});

const mapStateToProps = (state) => {
  const {railsListing: {voucherObject}} = state;
  const loading = isEmpty(voucherObject) || voucherObject.voucherStatus === voucherState.NOT_ISSUED;
  return {
    voucherObject,
    loading,
  };
};

const mapDispatchToProps = () => ({
  onCloseClick: () => { Actions.pop(); },
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsHotelVoucherSuccess);

VoucherCodeContainer.propTypes = {
  copyVoucherCode: PropTypes.func.isRequired,
  voucherCode: PropTypes.string.isRequired,
};

TimerComponent.propTypes = {
  endTime: PropTypes.number.isRequired,
  issueTime: PropTypes.number.isRequired,
};

RailsHotelVoucherSuccess.propTypes = {
  voucherObject: PropTypes.shape({
    voucher: PropTypes.shape({
      discountAmount: PropTypes.number,
      voucherIssueTime: PropTypes.number,
      voucherExpirationTime: PropTypes.number,
      voucherCode: PropTypes.string,
      discountPercentage: PropTypes.number,
    }),
  }),
  loading: PropTypes.bool,
  onCloseClick: PropTypes.func,
};
