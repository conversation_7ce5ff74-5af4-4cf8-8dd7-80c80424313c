import React from 'react';
import { styles } from '../Styles/styles';
import { View } from 'react-native';

const CustomDashedLine: React.FC = React.memo(() => {
  const dashes = new Array(30).fill(0);
  return (
    <View style={styles.dashedLineContainer}>
      {dashes.map((_, index) => (
        <View key={index} style={styles.dash} />
      ))}
    </View>
  );
});

CustomDashedLine.displayName = 'CustomDashedLine';
export default CustomDashedLine;
