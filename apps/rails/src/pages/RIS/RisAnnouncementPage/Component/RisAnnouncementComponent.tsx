import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
  BackHandler,
  ListRenderItem,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import NewTrainScheduleScreen from 'apps/rails/src/pages/RIS/TrainSchedule/Components/NewTrainScheduleScreen';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { styles } from '../Styles/styles';
import Loader from '@mmt/rails/src/pages/Common/Loader';
import RailsNewLandingHeader from '@mmt/rails/src/pages/Common/RailsNewLandingHeader';
import { Actions } from '../../../../navigation/railsNavigation';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import CustomDashedLine from './CustomDashLine';
import { data } from '../../../RailsBusHomePage/Common/constants';
import {
  NEW_TRAINS,
  SPECIAL_TRAINS,
  VIEW_AVAILABILITY_TEXT,
  VIEW_ROUTE_TEXT,
  RUNNING_PERIOD_TEXT,
  RUNNING_DATES_TEXT,
  CANCELLED_FOR,
  VIEW_AVAILABLE_TRAINS_TEXT,
  MOB_RAIL_IRCTC_UPDATES_VIEW_AVAILABILITY_CLICKED,
  MOB_RAIL_IRCTC_UPDATES_VIEW_TRAINS_CLICKED,
  MOB_RAIL_IRCTC_UPDATES_VIEW_ROUTE_CLICKED,
  MOB_RAIL_IRCTC_UPDATES_NEW_TRAINS_TAB_CLICKED,
  MOB_RAIL_IRCTC_UPDATES_CANCELLED_TRAINS_TAB_CLICKED,
  NEW_AND_SPECIAL_TRAINS,
  CANCELLED_TRAINS,
  NEW_AND_SPECIAL_TRAINS_LABEL,
  CANCELLED_TRAINS_LABEL,
} from '../../../../Utils/RailsConstant';
import { SEO_SEAT_AVAILABILITY_URL } from '../../../../RailsConfig';
import RisAnnoucementErrorScreen from './RisAnnoucementErrorScreen';
import { formatDate, formatDateDDMMYYYY, trimText } from '../../RisCommonUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  IRCTC_UPDATES_SUCCESSFULL_PAGE,
  RIS_IRCTC_UPDATES_CANCELLED_TRAIN_EMPTY,
  RIS_IRCTC_UPDATES_NEWTRAINS_EMPTY,
  trackOmnitureTrainCallout,
} from '@mmt/rails/src/RisAnalytics/RisAnalytics';

import CALENDAR_ICON from '@mmt/legacy-assets/src/rails/ic_calendar.webp';
import ARROW_FORWARD_ICON from '@mmt/legacy-assets/src/rails/arrow_forward.webp';
import BottomSheetModalTrain from '../../../Common/BottomSheetModalTrain';
const TABS = [
  {
    id: NEW_AND_SPECIAL_TRAINS,
    label: NEW_AND_SPECIAL_TRAINS_LABEL,
  },
  {
    id: CANCELLED_TRAINS,
    label: CANCELLED_TRAINS_LABEL,
  },
];
interface Announcement {
  trainName: string;
  trainNo: string;
  srcStnName: string;
  dstStnName: string;
  src: string;
  dst: string;
  startDate: string;
  endDate: string;
  isSpecial: boolean;
  runningDates: string[];
  cncldStDt: string;
  cncldEndDt: string;
}

interface RisAnnouncementComponentProps {
  specialTrains: Announcement[];
  cancelledTrains: Announcement[];
  risAnnouncementsLoading: boolean;
  fetchRISAnnounceMentsData: () => void;
  displayTrainScheduleBottomSheet: boolean;
  toggleTrainScheduleBottomSheet: () => void;
}

const RisAnnouncementComponent: React.FC<RisAnnouncementComponentProps> = ({
  specialTrains,
  cancelledTrains,
  risAnnouncementsLoading,
  fetchRISAnnounceMentsData,
  toggleTrainScheduleBottomSheet,
  displayTrainScheduleBottomSheet,
}) => {
  const [activeTab, setActiveTab] = useState(TABS[0].id);
  const [showNewAndSpecialTrains, setShowNewAndSpecialTrains] = useState(true);
  const [showCancelledTrains, setShowCancelledTrains] = useState(false);
  const [visibleSpecialTrains, setVisibleSpecialTrains] = useState([]);
  const [visibleCancelledTrains, setVisibleCancelledTrains] = useState([]);
  const ITEMS_TO_RENDER = 20;

  useEffect(() => {
    setVisibleSpecialTrains(specialTrains?.slice(0, ITEMS_TO_RENDER));
  }, [specialTrains]);

  useEffect(() => {
    setVisibleCancelledTrains(cancelledTrains?.slice(0, ITEMS_TO_RENDER));
  }, [cancelledTrains]);

  useEffect(() => {
    fetchRISAnnounceMentsData();
    BackHandler.addEventListener('hardwareBackPress', onHardBackPress);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onHardBackPress);
      toggleTrainScheduleBottomSheet(false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadMoreSpecialTrains = () => {
    if (visibleSpecialTrains?.length === specialTrains?.length) {
      return;
    }

    const nextBatch = specialTrains.slice(
      visibleSpecialTrains.length,
      visibleSpecialTrains.length + ITEMS_TO_RENDER,
    );
    setVisibleSpecialTrains([...visibleSpecialTrains, ...nextBatch]);
  };

  const renderFooter = () => {
    return (
      <View style={{ paddingVertical: 20 }}>
        <ActivityIndicator animating size="large" />
      </View>
    );
  };

  const loadMoreCancelledTrains = () => {
    if (visibleCancelledTrains?.length === cancelledTrains?.length) {
      return;
    }

    const nextBatch = cancelledTrains.slice(
      visibleCancelledTrains.length,
      visibleCancelledTrains.length + ITEMS_TO_RENDER,
    );
    setVisibleSpecialTrains([...visibleCancelledTrains, ...nextBatch]);
  };

  const onHardBackPress = () => {
    Actions.pop();
    return true;
  };

  const viewAvailabilityCalendar = (trainNo: string) => {
    trackClickEventProp61(
      IRCTC_UPDATES_SUCCESSFULL_PAGE,
      MOB_RAIL_IRCTC_UPDATES_VIEW_AVAILABILITY_CLICKED,
    );
    Actions.openAvailabilityCalendarWebView({
      url: `${SEO_SEAT_AVAILABILITY_URL}/${trainNo}`,
      headerText: data.AVLCAL,
      headerIcon: backIcon,
    });
  };

  const openListingPage = (src: string, dst: string, departureDate: string) => {
    trackClickEventProp61(
      IRCTC_UPDATES_SUCCESSFULL_PAGE,
      MOB_RAIL_IRCTC_UPDATES_VIEW_TRAINS_CLICKED,
    );
    const formattedDate = formatDateDDMMYYYY(departureDate);
    Actions.railsListing({
      originStation: { code: src },
      destinationStation: { code: dst },
      departureDate: formattedDate,
    });
  };

  if (risAnnouncementsLoading) {
    return <Loader />;
  }

  if (!specialTrains?.length && !cancelledTrains?.length) {
    return <RisAnnoucementErrorScreen isShownHeader={true} />;
  }

  const viewRouteClicked = (trainNo: string) => {
    trackClickEventProp61(
      IRCTC_UPDATES_SUCCESSFULL_PAGE,
      MOB_RAIL_IRCTC_UPDATES_VIEW_ROUTE_CLICKED,
    );
    toggleTrainScheduleBottomSheet(true, trainNo);
  };
  const onPress = (tabId: string) => {
    if (tabId === NEW_AND_SPECIAL_TRAINS) {
      trackClickEventProp61(
        IRCTC_UPDATES_SUCCESSFULL_PAGE,
        MOB_RAIL_IRCTC_UPDATES_NEW_TRAINS_TAB_CLICKED,
      );
      if (specialTrains?.length === 0) {
        trackOmnitureTrainCallout(
          IRCTC_UPDATES_SUCCESSFULL_PAGE,
          RIS_IRCTC_UPDATES_NEWTRAINS_EMPTY,
        );
      }
      setShowNewAndSpecialTrains(true);
      setShowCancelledTrains(false);
    }
    if (tabId === CANCELLED_TRAINS) {
      trackClickEventProp61(
        IRCTC_UPDATES_SUCCESSFULL_PAGE,
        MOB_RAIL_IRCTC_UPDATES_CANCELLED_TRAINS_TAB_CLICKED,
      );
      if (cancelledTrains?.length === 0) {
        trackOmnitureTrainCallout(
          IRCTC_UPDATES_SUCCESSFULL_PAGE,
          RIS_IRCTC_UPDATES_CANCELLED_TRAIN_EMPTY,
        );
      }
      setShowCancelledTrains(true);
      setShowNewAndSpecialTrains(false);
    }
    setActiveTab(tabId);
  };

  const renderAnnouncementsCancelledTrains: ListRenderItem<Announcement> = ({
    item: announcement,
    index,
  }) => {
    return (
      <View key={`${announcement?.trainName}-${index}`} style={styles.announcementCard}>
        <View style={styles.trainInfo}>
          <Text style={[fontStyle('black'), styles.trainName]}>
            {announcement?.trainName}
            <Text> </Text>
            <Text style={[fontStyle('regular'), styles.trainNumber]}>
              (#{announcement?.trainNo})
            </Text>
          </Text>
        </View>
        <View style={styles.stationInfo}>
          <Text style={[fontStyle('bold'), styles.cancelledStationName, styles.marginRight8]}>
            {trimText(announcement?.srcStnName)}
          </Text>
          <CustomDashedLine />
          <Text style={[fontStyle('bold'), styles.cancelledStationName, styles.marginLeft8]}>
            {trimText(announcement?.dstStnName)}
          </Text>
        </View>
        <View style={styles.stationCodeInfo}>
          <Text style={styles.stationCodeInfoText}>({trimText(announcement?.src)})</Text>
          <Text style={styles.stationCodeInfoText}>({trimText(announcement?.dst)})</Text>
        </View>
        <View style={styles.calenderIconContainer}>
          <Image style={styles.calenderIcon} source={CALENDAR_ICON} resizeMode="contain" />
          <Text style={[fontStyle('regular'), styles.runningPeriod]}>{CANCELLED_FOR}</Text>
          <Text style={[fontStyle('bold'), styles.dates]}>
            {`  ${formatDate(announcement?.cncldStDt)}`}
            {announcement?.cncldStDt !== announcement?.cncldEndDt &&
              ` - ${formatDate(announcement?.cncldEndDt)}`}
          </Text>
        </View>
        <View style={styles.availabilityContainer}>
          <TouchableOpacity
            onPress={() =>
              openListingPage(announcement?.src, announcement?.dst, announcement?.cncldStDt)
            }
            style={styles.calenderIconContainer}
          >
            <Text style={[fontStyle('bold'), styles.viewAvailability]}>
              {VIEW_AVAILABLE_TRAINS_TEXT}
            </Text>
            <Image style={styles.calenderIcon} source={ARROW_FORWARD_ICON} resizeMode="contain" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderAnnouncementsNewAndSpecialTrains: ListRenderItem<Announcement> = ({
    item: announcement,
    index,
  }) => {
    return (
      <View key={`${announcement.trainName}-${index}`} style={styles.announcementCard}>
        <View style={styles.trainInfo}>
          <Text style={[fontStyle('black'), styles.trainName]}>
            {announcement?.trainName}
            <Text> </Text>
            <Text style={[fontStyle('regular'), styles.trainNumber]}>
              (#{announcement?.trainNo})
            </Text>
          </Text>
          {announcement?.isSpecial ? (
            <View style={styles.specialTrains}>
              <Text style={styles.specialTrainsText}>{SPECIAL_TRAINS}</Text>
            </View>
          ) : (
            <View style={styles.newTrains}>
              <Text style={styles.newTrainsText}>{NEW_TRAINS}</Text>
            </View>
          )}
        </View>
        <View style={styles.stationInfo}>
          <Text style={[fontStyle('bold'), styles.stationName, styles.marginRight8]}>
            {trimText(announcement?.srcStnName)}
          </Text>
          <CustomDashedLine />
          <Text style={[fontStyle('bold'), styles.stationName, styles.marginLeft8]}>
            {trimText(announcement?.dstStnName)}
          </Text>
        </View>
        <View style={styles.stationCodeInfo}>
          <Text style={styles.stationCodeInfoText}>({trimText(announcement?.src)})</Text>
          <Text style={styles.stationCodeInfoText}>({trimText(announcement?.dst)})</Text>
        </View>
        <View style={styles.calenderIconContainer}>
          <Image style={styles.calenderIcon} source={CALENDAR_ICON} resizeMode="contain" />
          {announcement?.isSpecial ? (
            <Text style={[fontStyle('regular'), styles.runningPeriod]}>{RUNNING_PERIOD_TEXT}</Text>
          ) : (
            <Text style={[fontStyle('regular'), styles.runningPeriod]}>{RUNNING_DATES_TEXT}</Text>
          )}
          <Text style={[fontStyle('bold'), styles.dates]}>
            {`  ${formatDate(announcement?.startDate)}`} -{' '}
            {formatDate(announcement?.runningDates?.[announcement?.runningDates?.length - 1])}
          </Text>
        </View>
        <View style={styles.availabilityContainer}>
          <TouchableOpacity
            onPress={() => viewAvailabilityCalendar(announcement?.trainNo)}
            style={styles.calenderIconContainer}
          >
            <Text style={[fontStyle('bold'), styles.viewAvailability]}>
              {VIEW_AVAILABILITY_TEXT}
            </Text>
            <Image style={styles.calenderIcon} source={ARROW_FORWARD_ICON} resizeMode="contain" />
          </TouchableOpacity>
          <Text
            style={[fontStyle('bold'), styles.viewRoute]}
            onPress={() => {
              viewRouteClicked(announcement?.trainNo);
            }}
          >
            {VIEW_ROUTE_TEXT}
          </Text>
        </View>
      </View>
    );
  };
  const keyExtractor = (item: unknown) =>
    `${item?.trainName?.toString()}_${Math.random().toString(36).substr(2, 9)}`;

  return (
    <View style={styles.container}>
      <RailsNewLandingHeader
        title={label('irctc_title_announcements')}
        onIconClick={onHardBackPress}
      />
      <View style={styles.tabs}>
        {TABS.map((tab) => (
          <TouchableRipple
            key={tab.id}
            style={
              Platform.OS === 'ios' && [
                styles.tab,
                activeTab === tab.id ? styles.activeTab : {},
                tab.id === TABS[0].id ? styles.tabRails : {},
              ]
            }
            onPress={() => onPress(tab.id)}
          >
            <View
              style={
                Platform.OS !== 'ios' && [
                  styles.tab,
                  activeTab === tab.id ? styles.activeTab : {},
                  tab.id === TABS[0].id ? styles.tabRails : {},
                ]
              }
            >
              <View style={styles.flexDirectionRow}>
                <Text
                  style={
                    activeTab === tab.id
                      ? [styles.activeTabText, fontStyle('bold')]
                      : styles.tabText
                  }
                >
                  {tab.label}
                </Text>
              </View>
            </View>
          </TouchableRipple>
        ))}
      </View>
      {showNewAndSpecialTrains &&
        (specialTrains?.length > 0 ? (
          <FlatList
            data={specialTrains}
            renderItem={renderAnnouncementsNewAndSpecialTrains}
            keyExtractor={keyExtractor}
            initialNumToRender={ITEMS_TO_RENDER}
            maxToRenderPerBatch={ITEMS_TO_RENDER}
            windowSize={3}
            contentContainerStyle={styles.content}
            ListFooterComponent={
              visibleSpecialTrains.length < specialTrains.length ? renderFooter : null
            }
            onEndReached={loadMoreSpecialTrains}
            onEndReachedThreshold={0.5}
          />
        ) : (
          <RisAnnoucementErrorScreen isShownHeader={false} />
        ))}
      {showCancelledTrains &&
        (cancelledTrains?.length > 0 ? (
          <FlatList
            data={cancelledTrains}
            renderItem={renderAnnouncementsCancelledTrains}
            keyExtractor={keyExtractor}
            initialNumToRender={ITEMS_TO_RENDER}
            maxToRenderPerBatch={ITEMS_TO_RENDER}
            windowSize={3}
            contentContainerStyle={styles.content}
            ListFooterComponent={
              visibleCancelledTrains.length < cancelledTrains.length ? renderFooter : null
            }
            onEndReached={loadMoreCancelledTrains}
            onEndReachedThreshold={0.5}
          />
        ) : (
          <RisAnnoucementErrorScreen isShownHeader={false} />
        ))}
      {displayTrainScheduleBottomSheet && (
        <BottomSheetModalTrain
          onTouchOutside={() => {
            toggleTrainScheduleBottomSheet(false);
          }}
          additionalContainerStyle={styles.bottomSheetContainer}
          testID="ris_announcement_bottomsheet_modal"
        >
          <View style={styles.trainScheduleContainer}>
            <NewTrainScheduleScreen />
          </View>
        </BottomSheetModalTrain>
      )}
    </View>
  );
};

export default RisAnnouncementComponent;
