import React, { useEffect } from 'react';
import { View, Text, Image, BackHandler } from 'react-native';
import { styles } from '../Styles/styles';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import PageNotLoading from '@mmt/legacy-assets/src/PageNotLoading.webp';
import RailsNewLandingHeader from '../../../Common/RailsNewLandingHeader';
import {
  IRCTC_ERROR_SUB_TITLE,
  IRCTC_ERROR_TITLE,
} from '../../../../Utils/RailsConstant';
import { label } from '@mmt/legacy-commons/Common/Components/Vernacular';
import { Actions } from '../../../../navigation/railsNavigation';

interface RisAnnoucementErrorScreenProps {
  isShownHeader?: boolean;
}
const RisAnnoucementErrorScreen = ({ isShownHeader = false }: RisAnnoucementErrorScreenProps) => {
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', onHardBackPress);

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onHardBackPress);
    };
  }, []);
  const onHardBackPress = () => {
    Actions.pop();
    return true;
  };

  return (
    <>
      {isShownHeader && (
        <RailsNewLandingHeader
          title={label('irctc_title_announcements')}
          onIconClick={onHardBackPress}
        />
      )}
      <View
        style={
          !isShownHeader
            ? styles.newErrorFlowContainerForSinglePageError
            : styles.newErrorFlowContainer
        }
      >
        <Image
          source={PageNotLoading}
          width={192}
          height={192}
          resizeMode="contain"
          style={styles.pageNotLoading}
        />
        <Text style={[styles.newErrorFlowTitle, fontStyle('black')]}>{IRCTC_ERROR_TITLE}</Text>
        <Text style={[styles.newErrorFlowSubtitle, fontStyle('regular')]}>
          {IRCTC_ERROR_SUB_TITLE}
        </Text>
      </View>
    </>
  );
};

export default RisAnnoucementErrorScreen;
