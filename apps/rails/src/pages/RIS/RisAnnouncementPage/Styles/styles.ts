import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.grayBg,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  announcementCard: {
    marginTop: 5,
    marginBottom: 5,
    padding: 12,
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
  },
  loader: {
    paddingVertical: 20,
  },
  trainInfo: {
    marginBottom: 8,
    flexDirection: 'row',
  },
  trainName: {
    color: colors.black,
    fontSize: 14,
    overflow: 'hidden',
    fontWeight: '900',
  },
  stationName: {
    color: colors.textGrey,
    fontSize: 14,
    overflow: 'hidden',
    fontWeight: '700',
  },
  cancelledStationName: {
    color: colors.black,
    fontSize: 14,
    overflow: 'hidden',
    fontWeight: '900',
  },
  marginLeft8: {
    marginLeft: 8,
  },
  marginRight8: {
    marginRight: 8,
  },
  trainNumber: {
    color: colors.defaultTextColor,
  },
  stationInfo: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stationCodeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  stationCodeInfoText: {
    color: colors.greyText1,
  },
  calenderIcon: {
    height: 20,
    width: 20,
  },
  calenderIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  runningPeriod: {
    color: colors.textGrey,
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400',
  },
  dates: {
    fontSize: 12,
    color: colors.black,
    fontWeight: '700',
  },
  viewRoute: {
    marginLeft: 'auto',
    color: colors.primary,
  },
  viewAvailability: {
    color: colors.primary,
  },
  availabilityContainer: {
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  specialTrains: {
    backgroundColor: colors.creamWhite,
    padding: 10,
    paddingBottom: 5,
    paddingRight: 25,
    borderRadius: 16,
    position: 'absolute',
    top: -19,
    right: -28,
    zIndex: 0,
  },
  newTrains: {
    backgroundColor: colors.lightSilver,
    padding: 10,
    paddingBottom: 4,
    paddingRight: 25,
    borderRadius: 16,
    position: 'absolute',
    top: -19,
    right: -28,
    zIndex: 0,
  },
  specialTrainsText: {
    color: colors.brown2,
    fontSize: 12,
    fontWeight: '400',
  },
  newTrainsText: {
    color: colors.greyText1,
    fontWeight: '400',
    fontSize: 12,
  },
  dashedLineContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 4,
    overflow: 'hidden',
  },
  dash: {
    width: 7,
    height: 2,
    backgroundColor: colors.lightGray,
    marginHorizontal: 3,
  },
  pageNotLoading: {
    marginBottom: 40,
  },
  newErrorFlowContainer: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    paddingHorizontal: 80,
    marginTop: 150,
  },
  newErrorFlowContainerForSinglePageError: {
    backgroundColor: colors.white,
    alignItems: 'center',
    paddingHorizontal: 80,
    paddingTop: 150,
    paddingBottom: 300,
  },
  newErrorFlowTitle: {
    textAlign: 'center',
    marginTop: 12,
    fontSize: 20,
    color: colors.black,
  },
  newErrorFlowSubtitle: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    lineHeight: 16.8,
    color: colors.textGrey,
  },
  trainScheduleContainer: {
    height: '85%',
    backgroundColor: colors.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    overflow: 'hidden',
  },
  bottomSheetContainer: {
    zIndex: 9999,
  },
  tabs: {
    flexDirection: 'row',
    marginHorizontal: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginVertical: 12,
  },
  tab: {
    alignItems: 'center',
    paddingVertical: 7,
    flex: 1,
  },
  activeTab: {
    alignItems: 'center',
    borderColor: colors.azure,
    borderWidth: 1,
    borderRadius: 12,
    backgroundColor: colors.lighterBlue,
    zIndex: 1000,
  },
  activeTabText: {
    color: colors.azure,
    borderColor: colors.azure,
    fontSize: 13,
    fontWeight: '900',
  },
  tabText: {
    color: colors.defaultTextColor,
    borderColor: colors.lightGray,
    fontSize: 13,
    fontWeight: '400',
  },
  flexDirectionRow: {
    flexDirection: 'row',
  },
  tabRails: {
    position: 'relative',
  },
  image: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
});
