import { connect } from 'react-redux';
import RisAnnouncementComponent from './Component/RisAnnouncementComponent';
import { fetchRISAnnounceMentsData } from '../../RailsLandingPage/Store/RailsLandingPageActions';
import { toggleTrainScheduleBottomSheet } from '../../NewListing/RailsListingActions';

const mapStateToProps = ({
  railInfoReducer: {
    risAnnouncements: {
      newAndSpecialTrains: { trains: specialTrains = [] } = {},
      cancelledTrains: { trains: cancelledTrains = [] } = {},
    } = {},
    risAnnouncementsLoading = false,
  } = {},
  railsListing: { displayTrainScheduleBottomSheet },
}) => {
  return {
    specialTrains, // List of special trains
    cancelledTrains,
    risAnnouncementsLoading,
    displayTrainScheduleBottomSheet,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRISAnnounceMentsData: () => {
      dispatch(fetchRISAnnounceMentsData());
    },
    toggleTrainScheduleBottomSheet: (
      showBottomSheet = true,
      trainNumber = '',
      isCnfOptionTrain = false,
    ) => {
      dispatch(toggleTrainScheduleBottomSheet(showBottomSheet, trainNumber, isCnfOptionTrain));
    },
  };
};
export default connect(mapStateToProps, mapDispatchToProps)(RisAnnouncementComponent);
