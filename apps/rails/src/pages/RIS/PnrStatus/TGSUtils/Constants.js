import React from 'react';
import {
    StyleSheet,
} from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {isValidName} from '../../../TravelerDetails/TravelerDetailsUtils';
import { _label } from '../../../../vernacular/AppLanguage';
import { isOnlyEnglishCharacters } from '../../../../vernacular/VernacularUtils';
import _trim from 'lodash/trim';
import {VoucherActivation,PartiallyConfirmed,VoucherValidity} from './TGSAssets';

export const refundWaysTextStyle = {
    'text': '',
    'color': '#ffffff',
    'fontFamily': 'bold',
    'fontSize': '13',
    'backgroundColor': [],
    'nonBreakableText': false,
};

export const PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY = 'PNR_TRACKING_DETAILS';
export const MMT_RAILS_TGS_KEY = 'MMT_RAILS_TGS_KEY';
export const PASSENGER_NAME = 'Passenger Name';
export const TICKET_STATUS = 'Ticket Status';
export const TRAVELLER_PAGE = 'Traveller Page';
export const TICKET_DETAILS = 'Ticket Details';
export const TICKET_DETAILS_TEXT = 'Select where you booked your train ticket from out of the following';
export const BOOK_TICKET_DETAILS = 'Where did you book your ticket from?';
export const IRCTC = 'IRCTC';
export const OTHERS = 'OTHERS';
export const CONFIRM_AND_PAY = 'Confirm and Pay';
export const NAME = 'Name';
export const PHONE_NUMBER = 'Phone Number';
export const TRAVELLER_DETAILS = 'Travellers Details';
export const EMAIL_ID = 'Email ID';
export const FULL_NAME_PLACEHOLDER = 'Enter Full Name';
export const PHONE_NUMBER_PLACEHOLDER = 'Enter Phone Number';
export const EMAIL_PLACEHOLDER = 'Eg. <EMAIL>';
export const JOURNEY_DETAILS = 'Journey Details';
export const SHOW_DETAILS = 'Show details';
export const SHOW_LESS = 'Show Less';
export const RAIL_INFORMATION_SERVICES = 'Rail Information Services';
export const SHARE_WITH_FRIENDS = 'Share with your friends!';
export const TELL_YOUR_FRIENDS = 'Tell your friends about Trip Guarantee!';
export const BUTTON_TEXT = 'Share on WhatsApp';
export const CONTINUE = 'CONTINUE';
export const NEW_PLACEHOLDERS = {
    name: 'FULL NAME',
    mobile: 'MOBILE NUMBER',
    email: 'EMAIL ID',
};
export const SEE_ALL_PASSENGERS = 'See All Passengers';
export const HIDE = 'Hide';
export const PASSENGER = 'Passenger';
export const TESTIMONIAL_HEADING = 'Industry-first Rail Travel assurance';
export const PNR_STATUS = 'PNR Status';

export const commonStyles = StyleSheet.create({
    genericContainer: {
        backgroundColor: colors.white,
        padding: 8,
        paddingLeft: 20,
        paddingRight: 20,
        marginRight: 'auto',
        marginLeft: 'auto',
        width: '100%',
        display: 'flex',
        marginBottom: 10,
        color:colors.black,
    },
    firstRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 10,
    },
    TextRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 5,
    },
    heading: {
        fontWeight: '900',
        fontSize: 22,
        lineHeight: 26,
        fontFamily: 'Lato',
        color:colors.black,
    },
    platformText: {
        fontFamily: 'Lato',
        fontWeight: '600',
        fontSize: 14,
        lineHeight: 24,
        marginLeft: 14,
    },
    tgsTicketDetailsRow: {
        height: 40,
        borderRadius: 4,
        borderWidth: 0.5,
        marginBottom: 4,
        marginTop: 3,
        paddingRight: 5,
        paddingLeft: 5,
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
    },
    tgsTicketDetailsImageStyle: {
        marginLeft: 'auto',
        width: 20,
        height: 20,
    },
});

export const termsAndConditionsImg = (tncImg) => {
 const TERMS_AND_CONDITIONS_IMG = {
    voucherActivation : <VoucherActivation  style={tncImg} />,
    partiallyConfirmed : <PartiallyConfirmed style={tncImg} />,
    voucherValidity : <VoucherValidity style={tncImg} />,
};
return TERMS_AND_CONDITIONS_IMG;
};

export const BOOKING_ID = 'BOOKING ID';
export const VOUCHER_CODE = 'Voucher Code';
export const TERMS_AND_CONDITIONS = 'Terms & Conditions';
export const TERMS_AND_CONDITIONS_SHORT = 'T&C';
export const MANAGE_BOOKING = 'MANAGE BOOKING';
export const PNR = 'PNR';
const pnrStatusRisKey = 'CPS';
const trainScheduleRisKey = 'STS';
const liveTrainStatusRisKey = 'LTS';
const trainAvailabilityRisKey = 'CSA';
const liveStationRisKey = 'LSS';
const coachPositionRisKey = 'CPP';
const foodInTrainsRisKey = 'FIT';
export const DEFAULT_RIS_OPTIONS_FOR_WEB = [liveTrainStatusRisKey,pnrStatusRisKey, 
                                            trainScheduleRisKey,foodInTrainsRisKey, 
                                            liveStationRisKey, coachPositionRisKey,
                                            trainAvailabilityRisKey];
export const SHOW_MORE = 'Show more information services';
export const SEE_LESS = 'See less';
export const journeyDetailsKey = 'JOURNEY_DETAILS';
export const travellerDetailsKey = 'TRAVELLER_DETAILS';
export const ticketDetailsKey = 'TICKET_DETAILS';
export const priceBreakUpKey = 'PRICE_BREAKUP';
export const railsTGSHeaderUrl = 'rails-tgs.makemytrip.com';
export const TGS_TRAVELLER_PAGE = 'https://rails-tgs.makemytrip.com/api/tgs/tgsTravellerDetails/v1';
export const TGS_THANK_YOU_PAGE = 'https://rails-tgs.makemytrip.com/api/tgs/thankYou/v1';
export const TGS_REVIEW_PAYMENT_API = 'https://rails-tgs.makemytrip.com/api/tgs/review/v1';
export const RADIO_BUTTON_1 = 'RadioButton1';
export const RADIO_BUTTON_2 = 'RadioButton2';
export const timeouts =  {
    TGS: 10000,
};
export const travellerFieldEmptyError = 'Please fill all the traveller details to proceed further!';
export const travellerFieldMobileError = 'Please fill the correct mobile number to proceed further!';
export const  travellerFieldNameError = 'Please fill the correct name to proceed further!';
export const travellerFieldEmailError = 'Please fill the correct email to proceed further!';
export const ticketContainerError = 'Please select the Ticket Details';
export const tgsButtonClicked = 'tgs_sumit_button_clicked';
export const TGS_PAYMENT_PAGE = 'TGS Payment Page';
export const TGS_T_AND_C = 'T&C';
export const refund1xText = '1X refund';
export const refund2xText = '2X refund';
export const yourTicketFare = 'Your Ticket Fare';
export const get3xRefund = 'Get 3X Refund of';
export const payOnly = 'Pay only';


export const splitText = (val) => {
    if (val)
    {
    return [val.split('|')[0], val.split('|')[1]];
    }
    return [null,null];
};

export const getTgFare = ( totalPrice ) => {
    if (totalPrice) {
        return totalPrice.split('|')[1].replace('₹', '').trim();
    }
    return null;
};

export const getTicketFare = (ticketFare, paxCount) => {
    if (ticketFare) {
        const integerRegex = /\d+/g;
        const farePerPerson = ticketFare.match(integerRegex)[0];
        const totalFare = farePerPerson * paxCount;
        return `₹${totalFare}`;
    }
    return null;
};

export const getRefundFare = (refundFare, paxCount) => {
    if (refundFare) {
        const integerRegex = /\d+/g;
        const refundPerPerson = refundFare.match(integerRegex)[1];
        const totalRefund = refundPerPerson * paxCount;
        return `₹${totalRefund}`;
    }
    return null;
};

const FEEDBACK_NAMES = {
    firstUser: 'Prakash Nair',
    secondUser: 'Aman Singh',
};

const FEEDBACK_CONTENT = {
    firstUser: 'Had to take my Banking exam in Pune but ticket remained in waiting List. Trip Guarantee helped me go to Pune!',
    secondUser: 'Had to visit my village for immediate family commitment. I reached home on time all thanks to Trip Guarantee!',
};

export const getFeedbackData = () => {
    const feedbackData = [
        {
            name: FEEDBACK_NAMES.firstUser,
            content: FEEDBACK_CONTENT.firstUser,
        },
        {
            name: FEEDBACK_NAMES.secondUser,
            content: FEEDBACK_CONTENT.secondUser,
        },
    ];
    return feedbackData;
};


export const omnitureMapping = {
    TGSWIDGET_ON_PNR_PAGE_SHOWN:'rails:tgs:widget:shown',
    TGSWIDGET_ON_PNR_PAGE_CLICKED:'rails_tgs_widget_pnr_clicked',
    TGSWIDGET_ON_PNR_PAGE_CLICKED_NEW:'rails_tgs_widget_pnr_clicked_new',
    TGSWIDGET_ON_PNR_PAGE_CLICKED_NEW_BOTTOMSHEET:'rails_tgs_widget_pnr_clicked_new_bottomsheet',
    TGS_TRAVELLERS_OTHERS:'rails_tgs_travellers_bookingsource_others',
    TGS_TRAVELLERS_IRCTC:'rails_tgs_travellers_bookingsource_irctc',
    TGS_TRAVELLERS_JOURNEY_CLICKED:'rails_tgs_travellers_journeydetails_clicked',
    TGS_TRAVELLERS_JOURNEY_SHOW_LESS_CLICKED:'rails_tgs_travellers_journeydetails_showless_clicked',
    TGS_THANK_YOU_PAGE_COPY_CLICKED:'rails_tgs_thankyousuccess_copy_clicked',
    TGS_THANKYOU_SUCCESS:'rails:tgs:thankyou:success',
    TGS_THANKYOU_RIS:'rails_tgs_thankyousuccess_ris_',
    TGS_THANKYOU_T_AND_C:'rails_tgs_thankyousucces_termsandconditions',
    TGS_TRAVELLERS_OTHER_OPTIONS:'rails_tgs_travellers_bookingsource_others_',
    TGS_TRAVELLERS_BOTTOMSHEET_DISMISS:'rails_tgs_travellers_bookingsource_bottomsheet_dismissed',
    TGS_TRAVELLERS_PAGE:'rails:tgs:travellers',
    TGS_LANDING_PAGE:'rails:tgs:landingpage',
    TGS_CONFIRM_PAY_CLICKED:'rails_tgs_travellers_confirmnpay_clicked',
    TGS_TRAVELLER_BACK_BUTTON_CLICKED:'rails_tgs_travellers_backbutton_clicked',
    TGS_TRAVELLER_T_AND_C:'rails_tgs_travellers_tnc_clicked',
    TGS_THANK_YOU_MANGE_BOOKING:'rails_tgs_thankyousuccess_managebooking',
    TGS_NON_BOOKER_ELIGIBLE:'rails:tgs:pnr:nonbooker:eligible',
    TGS_NON_BOOKER_INELIGIBLE:'rails:tgs:pnr:nonbooker:ineligible',
    TGS_BOOKER_ELIGIBLE:'rails:tgs:pnr:booker:eligible',
    TGS_BOOKER_INELIGIBLE:'rails:tgs:pnr:booker:ineligible',
    TGS_THANKYOU_RIS_SHOW_MORE:'rails_tgs_thankyousucces_ris_showmore',
    TGS_INELIGIBLE_CNF:'rails:tgs:ineligible:confirmed',
    TGS_INELIGIBLE_RAC:'rails:tgs:ineligible:RAC',
    TGS_INELIGIBLE_PARTIAL_CNF:'rails:tgs:ineligible:PartialCNForRAC',
    TGS_INELIGIBLE_RIS_ERROR:'rails:tgs:ineligible:notfetchable',
    TGS_INELIGIBLE_GUARDRAIL_BOUGHT_RECENTLY:'rail:tgs:ineligible:tgsboughtin15days',
    TGS_INELIGIBLE_GUARDRAIL_ALREADY_BOUGHT :'rails:tgs:ineligible:PNRalreadybought',
    TGS_INELIGIBLE_DS_OR_TATKAL:'rails:tgs:ineligible:DSorTatkalorAP0',
    TGS_LANDING_SEARCH_CLICKED:'rails_tgs_landing_searchclicked',
    TGS_LANDING_BACK_BTN:'rails_tgs_landing_backbtn',
    TGS_THANKYOU_SHARE_CLICKED:'rails_tgs_share_thankyou_share_clicked',
    TGS_THANKYOU_SHARE_CLICKED_BOTTOMSHEET:'rails_tgs_share_thankyou_share_clicked_bottomsheet',
    TGS_TRAVELLERS_SHARE_CLICKED:'rails_tgs_share_travellers_share_clicked',
    TGS_THANKYOU_BOOTOMSHEET:'rails:tgs:share:thankyou:bottomsheet',
    TRAINS_BUS_CROSS_SELL_VISIBLE:'rails:busCarousel_CrossSell_Component_Visible_True',
    TRAINS_BUS_CROSS_SELL_NOT_VISIBLE: 'rails:busCarousel_CrossSell_Component_Visible_False',
    TGS_LANDING_PAGE_UNIQUE:'rails:tgs:landingpage:uniquepnr',
    TGS_TRAVELLERS_PAGE_UNIQUE:'rails:tgs:travellers:uniquepnr',
    TGS_THANKYOU_SUCCESS_UNIQUE:'rails:tgs:thankyou:success:uniquepnr',
    RIS_LANDING_PNR_DETAILS_SUCCESS_UNIQUE: 'mob:funnel:ris:pnr:pnrdetails:uniquepnr',
    PNR_STATUS_TRAIN_CALLOUT:'mob:funnel:ris:pnr:pnrdetails:traincallout',
    PNR_STATUS_TRAIN_CALLOUT_CLICKED:'mob:funnel:ris:pnr:pnrdetails:traincallout_clicked',
    PNR_LANDING_VIDEO_EXPAND_CLICKED: 'mob:funnel:ris:pnr:pnrlanding:video_expand_clicked',
    TGS_TRAVELLER_SEE_MORE_PAX_CLICKED: 'rails_tgs_travellers_see_more_pax_clicked',
    TGS_PNR_STATUS_PAGE_BUS_SHOWN_TO_NON_BOOKERS: 'rails:pnr:nonbooker:bus:shown',
    TGS_PNR_STATUS_PAGE_ALTERNATE_ROUTE_SHOWN_TO_NON_BOOKERS: 'rails:pnr:nonbooker:alternate:route:shown',
    TGS_PNR_STATUS_PAGE_NEW_USESR_OFFER_SHOWN_TO_NON_BOOKERS: 'rails:pnr:nonbooker:new:offer:shown',
    TGS_INELIGIBLE_RIS_ERROR_FOR_BOOKERS:'rails:tgs:bookers:nonfetchable',
    TGS_PNR_STATUS_PAGE_BOOKERS_TGS_ELIGIBLE: 'rails:tgs:pnr:booker:eligible',
    TGS_PNR_STATUS_PAGE_BOOKERS_TGS_NOT_ELIGIBLE: 'rails:tgs:pnr:booker:ineligible',
    TGS_PNR_LANDING_PAGE_BANNER_CLICKED:'tgs_pnr_landing_page_banner_clicked',
};

 const isNameLengthInRange = travelerName =>
  travelerName.length >= 3 &&
  travelerName.length <= 50;

export function validateName(name) {
    if (!_trim(name)) {
        return {
            error: travellerFieldNameError,
            valid: false,
        };
    }
    else if (!isOnlyEnglishCharacters(name)) {
        return {
            error: _label('type_in_english'),
            valid: false,
        };
    }
    else if (!isValidName(name)) {
        return {
            error: _label('no_numeric'),
            valid: false,
        };
    }
    else if (!isNameLengthInRange(name)) {
        return {
            error: name.length < 3 ? _label('min_allowed_chars') : 'Name is too long, maximum 50 chars allowed',
            valid: false,
        };
    }

    return {
        error: '',
        valid: true,
    };
}

export const errorResponse = {
    'errorCode': '404',
    'errorDetails': {
    'displayMode': 'FULL_PAGE',
    'errorTitle': 'Uh Oh!',
    'errorMessage': 'We are unable to serve you at the moment.\nWe’ll be back shortly!',
    'intent': 'WARNING',
    'autoDismissDelay': 0,
    'cta': {
        'buttonText': 'Go back',
        'theme': 'PRIMARY',
        'action': 'RETRY',
        'linkType': 'repeat_same_action_which_returned_error',
        'retryCount': 1,
    },
    'receivedAt': 1658065905,
    },
};

export const TGS_Traveller_Container = 'TGS_Traveller_Container';

export const TGS_Tickets_Container = 'TGS_Tickets_Container';

export const RisTGSErrorResponse = {
    'containerBg': [
        '#928DAB',
        '#1F1C2C',
    ],
    'bannerTag': {
        'backgroundColor': '#FF654C',
        'bannerTagTextStyle': '{ "color": "#FFFFFF", "fontSize": 12 }',
        'bannerTagText': '2 lakh+ Assured trips',
    },
    'pnrContainer': {
        'backgroundColor': '#FFFFFF',
        'pnrTextStyle': '{"color":"#000000","fontSize":16}',
        'pnrText': 'PNR %s',
        'ticketEligibilityText': "<p>Your PNR is <s>not eligible</s> for Trip Guarantee\n due to 'Partial Confirmation'</p>",
        'ticketEligibilityStyleSheet': '{"p":{"color":"#4A4A4A","fontWeight":"500","fontStyle":"Lato"},"s":{"color":"#7e1887","fontWeight":"500","fontStyle":"Lato"}}',
    },
    'availabilityContainer': {
        'availabilityText': '<a>Trip Guarantee</a>|<d>Get 3X refund on your train ticket price</d><am> if your\n</am>|<am>waitlisted ticket doesn’t get confirmed</am>',
        'availabilityStyleSheet': '{"a":{"color":"white","fontWeight":"700","fontSize":16},"d":{"fontWeight":"800","fontSize":12,"color":"#FFEB81"},"am":{"fontSize":12,"color":"#FFFFFF"}}',
    },
    'buttonContainer': {
        'buttonText': 'Retry',
        'buttonTextColor': '#ffffff',
        'buttonColor': [
            '#065af3',
            '#53b2fe',
        ],
    },
};

export const disclaimer = 'You are advised to verify the details before making any decision based on the information provided. Neither MakeMyTrip nor IRCTC will be responsible for any liability occurring due to this information.';

export const errorCode_3007 = 3007;

export const errorCode_3008 = 3008;

export const errorCode_2101 = 2101;
export const errorCode_2102 = 2102;
export const errorCode_2103 = 2103;
export const errorCode_2106 = 2106;

const removeParam = ['fontStyle','whiteSpace'];

function recursively(obj,result = {},parentKey = null) {
    let keys = Object.keys(obj);
    keys.forEach((key) => {
        if (!removeParam.includes(key)) {
            if (Array.isArray(obj[key])) {
                result[key] = obj[key];
            } else if (typeof obj[key] === 'object') {
               recursively(obj[key], result,key);
            } else {
              if (parentKey !== null)
              {
                if (!result[parentKey])
                {result[parentKey] = {};}

                result[parentKey][key] = obj[key];
              }
              else
              {
                result[key] = obj[key];
              }
            }
            }
    });

    return result;
}

export const parseTGSResponse = (response) => {
   return  recursively(response);
};

export const ticketOptions = [
    {
        id: 'Ixigo',
        displayName:'Ixigo',
        image: require('@mmt/legacy-assets/src/rails/Ixigo.webp'),
    },
    {
        id: 'Confirmtkt',
        displayName:'Confirmtkt',
        image: require('@mmt/legacy-assets/src/rails/Confirmtkt.webp'),
    },
    {
        id: 'PayTM',
        displayName:'PayTM',
        image: require('@mmt/legacy-assets/src/rails/Paytm.webp'),
    },
    {
        id: 'EaseMyTrip',
        displayName:'EaseMyTrip',
        image: require('@mmt/legacy-assets/src/rails/EaseMyTrip.webp'),
    },
    {
        id: 'Trainman',
        displayName:'Trainman',
        image: require('@mmt/legacy-assets/src/rails/Trainman.webp'),
    },
    {
        id: 'RailYatri',
        displayName:'RailYatri',
        image: require('@mmt/legacy-assets/src/rails/RailYatri.webp'),
    },
    {
        id: 'Amazon',
        displayName:'Amazon',
        image: require('@mmt/legacy-assets/src/rails/Amazon.webp'),
    },
    {
        id: 'OfflineAgent',
        displayName:'Offline agent',
        image: require('@mmt/legacy-assets/src/rails/Agent.webp'),
    },
];


export const configKeyMapping = {
  headline: 'Box 2 Headline',
  WL_Tag: 'WL Tag',
  TG_Headline: 'TG Headline',
  Price_Tag: 'Price Tag',
  TG_Headline_Subtask: 'TG Headline Subtext',
  Box1_HeadLine: 'Box 1 Headline',
  Box1_Text: 'Box 1 Text',
  Box2_Text: 'Box 2 Text',
  Box3_Text1: 'Box 3 Text 1',
  Box3_Text2: 'Box 3 Text 2',
  CTA: 'CTA',
};

export const useThis = 'Use this to book:';
export const waitListedText = 'if your waitlisted ticket is not confirmed';
export const cheapTickets = 'Book CHEAPEST Train Ticket on MMT!';
export const discountCode = 'Use discount code ';
