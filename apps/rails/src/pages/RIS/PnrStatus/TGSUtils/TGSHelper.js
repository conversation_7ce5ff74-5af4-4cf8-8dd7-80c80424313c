import {Platform} from 'react-native';
import { getRailsTGSAppFunnelNativeOrMweb } from '../../../../RailsAbConfig';
import {
    TGS_TRAVELLER_PAGE,
    TGS_THANK_YOU_PAGE,
    timeouts,
    TGS_REVIEW_PAYMENT_API,
} from './Constants';
import fetch2 from '../../../../fetch2';
import {TGS_MMT_ID} from 'apps/rails/src/pages/TGS/Components/TGSConstants';
import { DATE_MONTH_YEAR_FORMAT, DOUBLE_DIGIT_DAY_WITH_MONTH_FORMAT, setDataToAsyncStorage } from 'apps/rails/src/Utils/RailsConstant';
import { getDefaultPostHeaders } from '../../../NewListing/RailsListingActions';
import fecha from 'fecha';
import {
    trackPNRAdvancePurchase,
    trackPNRPrice,
    trackPNRSourceDestination,
    trackPnrId,
} from 'apps/rails/src/RisAnalytics/RisAnalytics';

export async function tgsAPIRequest(promise) {
    const timeout = new Promise((resolve) => {
        setTimeout(() => {
            resolve({error:'API call timeout'});
        }, timeouts.TGS);
    });
    return await Promise.race([promise, timeout]);
}

export async function tgsTravellerAPIRequestPromise(pnrNumber) {
    try {

        let url = TGS_TRAVELLER_PAGE + `?pnrNumber=${pnrNumber}`;

        const response = await fetch2(url);
        const jsonResponse = await response.json();
        return jsonResponse;
    }
    catch (e) {
        return e;
    }
}

export async function tgsThankYouAPIRequestPromise(mmtIdNumber) {
    try {
        let url = TGS_THANK_YOU_PAGE;

        const requestBody = {
            mmtId: mmtIdNumber,
        };
        let headers = { ...getDefaultPostHeaders() };
        const response = await fetch2(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestBody),
        });
        const jsonResponse = await response.json();

        return jsonResponse;
    }
    catch (e) {
        return e;
    }
}


export async function tgsReviewAPIRequestPromise(requestDetails,pnrNumber) {
    try {
        let url = TGS_REVIEW_PAYMENT_API;
        const railsTGSApp = getRailsTGSAppFunnelNativeOrMweb();
        const requestBody = {
            source: railsTGSApp === 1 ? 'RNW' : 'RN',
            mobileNumber: requestDetails.mobileNumber,
            email: requestDetails.email,
            bookMode: railsTGSApp === 1 ? 'P' : Platform.OS === 'android' ? 'A' : 'I',
            bookerName: requestDetails.bookerName,
            pnrNumber: pnrNumber,
            bookingSource: requestDetails.bookingSource,
            organisation: 'MakeMyTrip',
            confirmationGuaranteeDetail: {
                railsConfirmationGuaranteeOption: 1,
                cnfmGuaranteeOpted: true,
            },
        };
        let headers = { ...getDefaultPostHeaders() };
        const response = await fetch2(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestBody),
        });
        const jsonResponse = await response.json();
        if (Platform.OS === 'ios') {
            const { mmtBookingId } = jsonResponse?.response;
            await setDataToAsyncStorage(TGS_MMT_ID, mmtBookingId);
        }
        return jsonResponse;
    }
    catch (e) {
        return e;
    }
}

export const getDateAndPnrNumberFromTravellerResponse = ( travellerResponse ) => {
    const {sourceDestinationAndDateInfo, journeyDetailsContainer} = travellerResponse?.response;
    const pnrNumber =  journeyDetailsContainer.pnr.split('|')[1];
    const journeyDate = sourceDestinationAndDateInfo.split('|')[1].trim();
    const parsedJourneyDate = fecha.parse(journeyDate, DOUBLE_DIGIT_DAY_WITH_MONTH_FORMAT);
    const formattedJourneyDate = fecha.format(parsedJourneyDate, DATE_MONTH_YEAR_FORMAT);
    return {
        pnrNumber,
        formattedJourneyDate,
    };
};

export const trackPnrDetails = (pageName, pnrDetails) => {
    const {
        advancePurchase,
        ticketPrice,
        originDest,
        pnrId,
    } = pnrDetails || {};
    trackPNRAdvancePurchase(pageName, advancePurchase);
    trackPNRPrice(pageName, ticketPrice);
    trackPNRSourceDestination(pageName, originDest);
    trackPnrId(pageName, pnrId);
};
