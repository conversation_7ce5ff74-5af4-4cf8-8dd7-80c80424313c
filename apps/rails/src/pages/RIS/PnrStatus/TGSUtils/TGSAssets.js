import * as React from 'react';
import Svg, {
    Path,
    Rect,
    Ellipse,
    Circle,
    Defs,
    LinearGradient,
    Stop,
    Mask,
    G,
    ClipPath,
  } from 'react-native-svg';

export const DownArrow = (props) => (
    <Svg
        xmlns="http://www.w3.org/2000/svg"
        width={11}
        height={7}
        viewBox="0 0 11 7"
        fill="none"
    testId="down_arrow_icon"
        {...props}
    >
        <Path
            d="M1 1l4.5 4L10 1"
            stroke="#008CFF"
            strokeWidth={1.5}
            strokeLinecap="round"
        />
    </Svg>
);

export const UpArrow = (props) => (
    <Svg
        xmlns="http://www.w3.org/2000/svg"
        width={11}
        height={7}
        viewBox="0 0 11 7"
        fill="none"
    testId="up_arrow_icon"
    {...props}
    >
        <Path
            d="M10 6L5.5 2 1 6"
            stroke="#008CFF"
            strokeWidth={1.5}
            strokeLinecap="round"
        />
    </Svg>
);

export const CopyIcon = (props) => {
    return (
      <Svg
        width={8}
        height={11}
        viewBox="0 0 8 11"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <Path
          d="M.751 1.313L.75 2.375v5.252a1.625 1.625 0 001.625 1.625h4.308a1.125 1.125 0 01-1.061.75H2.375A2.375 2.375 0 010 7.627V2.375c0-.49.314-.907.751-1.062zM6.875 0A1.125 1.125 0 018 1.125v6.5A1.125 1.125 0 016.875 8.75h-4.5A1.125 1.125 0 011.25 7.625v-6.5A1.125 1.125 0 012.375 0h4.5zm0 .75h-4.5A.375.375 0 002 1.125v6.5c0 .207.168.375.375.375h4.5a.375.375 0 00.375-.375v-6.5A.375.375 0 006.875.75z"
          fill="#fff"
        />
      </Svg>
    );
  };

export const BagsIcon = (props) => {
    return (
        <Svg
            width={40}
            height={36}
            viewBox="0 0 40 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M9.919 13.898l6.319 1.812-3.982 13.886a3.224 3.224 0 01-3.988 2.21l-3.22-.922a3.224 3.224 0 01-2.21-3.989L5.93 16.108a3.224 3.224 0 013.988-2.21z"
                fill="url(#paint0_linear_380_2939)"
                stroke="#3B385E"
                strokeWidth={1.61222}
            />
            <Path
                d="M9.178 16.842a.806.806 0 10-1.55-.444l1.55.444zM4.51 27.267a.806.806 0 101.55.444l-1.55-.444zM15.541 7.02a.806.806 0 10-.444 1.55l.445-1.55zm2.78 1.636l.775.222.222-.775-.775-.222-.222.775zm-7.03 21.592a.806.806 0 001.55.444l-1.55-.444zm-3.663-13.85L4.511 27.267l1.55.444 3.117-10.869-1.55-.444zm7.47-7.828l3 .86.445-1.55-3.001-.86-.445 1.55zm2.448-.137L11.29 30.248l1.55.444 6.255-21.814-1.55-.445z"
                fill="#3B385E"
            />
            <Ellipse
                cx={2.98189}
                cy={32.6644}
                rx={1.15273}
                ry={1.13984}
                transform="rotate(16 2.982 32.664)"
                fill="#3B385E"
            />
            <Ellipse
                cx={9.07613}
                cy={34.4115}
                rx={1.15273}
                ry={1.13984}
                transform="rotate(16 9.076 34.411)"
                fill="#3B385E"
            />
            <Path
                d="M20.925 9.86h8.964v18.063a3.225 3.225 0 01-3.225 3.224h-5.74a3.224 3.224 0 01-3.224-3.224V13.085a3.224 3.224 0 013.225-3.225z"
                fill="#fff"
                stroke="#3B385E"
                strokeWidth={1.61222}
            />
            <Path
                d="M26.077 1h3.883v27.335"
                stroke="#3B385E"
                strokeWidth={1.61222}
                strokeLinecap="round"
            />
            <Ellipse
                cx={19.8511}
                cy={33.8922}
                rx={1.43407}
                ry={1.37361}
                fill="#3B385E"
            />
            <Ellipse
                cx={27.7378}
                cy={33.8922}
                rx={1.43407}
                ry={1.37361}
                fill="#3B385E"
            />
            <Path
                d="M3.182 9.23a.806.806 0 00-1.14 1.14l1.14-1.14zm1.279 3.559a.806.806 0 001.14-1.14l-1.14 1.14zM7.139 8.74a.806.806 0 10-1.53.51l1.53-.51zm-.993 2.122a.806.806 0 101.53-.51l-1.53.51zm-4.785 1.442a.806.806 0 10-.722 1.442l.722-1.442zm.89 2.248a.806.806 0 00.722-1.442l-.721 1.442zm-.209-4.181l2.419 2.418 1.14-1.14-2.419-2.418-1.14 1.14zM5.61 9.25l.536 1.612 1.53-.51L7.14 8.74l-1.53.51zM.64 13.746l1.612.806.72-1.442-1.611-.806-.722 1.442z"
                fill="#3B385E"
            />
            <Circle
                cx={32.0353}
                cy={14.2347}
                r={6.85192}
                fill="url(#paint1_linear_380_2939)"
                stroke="#3B385E"
                strokeWidth={1.61222}
            />
            <Path
                d="M29.457 14.435l1.572 1.571 3.668-3.667"
                stroke="#3B385E"
                strokeWidth={1.61222}
                strokeLinecap="round"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_380_2939"
                    x1={25.8417}
                    y1={-7.36858}
                    x2={-4.21013}
                    y2={-10.1755}
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#FFC800" />
                    <Stop offset={1} stopColor="#FF9500" />
                </LinearGradient>
                <LinearGradient
                    id="paint1_linear_380_2939"
                    x1={30.8475}
                    y1={1.46202}
                    x2={19.006}
                    y2={14.5408}
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#4AFFCD" />
                    <Stop offset={1} stopColor="#7DBFCC" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};

export const TrainsIcon = (props) => {
    return (
        <Svg
            width={57}
            height={38}
            viewBox="0 0 57 38"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M42.315 2.878a2.954 2.954 0 012.177.6 2.922 2.922 0 011.113 1.955l.743 6.023a6.217 6.217 0 00-2.853 2.288 6.394 6.394 0 00-1.058 4.399 6.41 6.41 0 002.097 4.013 6.24 6.24 0 003.325 1.534l.605 4.899c.1.804-.139 1.572-.604 2.164a2.946 2.946 0 01-1.966 1.106L9.143 36.346a2.954 2.954 0 01-2.177-.6 2.922 2.922 0 01-1.112-1.955l-.839-6.783a6.286 6.286 0 002.157-2.337c.6-1.122.874-2.443.704-3.814a6.417 6.417 0 00-1.61-3.531 6.312 6.312 0 00-2.662-1.748l-.61-4.943c-.1-.804.139-1.573.604-2.164a2.946 2.946 0 011.966-1.106l36.751-4.487z"
                fill="#2F0959"
                stroke="#2F0959"
                strokeWidth={1.95644}
            />
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M40.681 1.572a2.954 2.954 0 012.177.6 2.922 2.922 0 011.113 1.955l.743 6.024a6.217 6.217 0 00-2.852 2.287 6.394 6.394 0 00-1.059 4.4A6.41 6.41 0 0042.9 20.85a6.24 6.24 0 003.325 1.533l.605 4.9c.1.804-.139 1.572-.604 2.164a2.946 2.946 0 01-1.966 1.106L7.51 35.04a2.954 2.954 0 01-2.177-.6 2.922 2.922 0 01-1.112-1.956l-.655-5.3A6.287 6.287 0 005.72 24.85c.6-1.123.874-2.444.704-3.815a6.418 6.418 0 00-1.61-3.531 6.31 6.31 0 00-2.662-1.748L1.36 9.33c-.1-.805.139-1.573.604-2.165A2.946 2.946 0 013.93 6.06l36.752-4.487z"
                fill="#fff"
                stroke="#2F0959"
                strokeWidth={1.95644}
            />
            <Mask
                id="a"
                style={{
                    maskType: 'alpha',
                }}
                maskUnits="userSpaceOnUse"
                x={2}
                y={3}
                width={34}
                height={32}
            >
                <Path
                    d="M32.026 3.323l3.478 28.17-26.56 3.243c-2.157.264-4.12-1.262-4.386-3.407l-.654-5.308c1.602-.919 2.575-2.988 2.296-5.247-.279-2.26-1.727-4.034-3.505-4.54l-.656-5.307c-.265-2.145 1.269-4.098 3.426-4.36l26.56-3.244z"
                    fill="#fff"
                />
            </Mask>
            <G mask="url(#a)">
                <Path
                    clipRule="evenodd"
                    d="M40.681 1.572a2.954 2.954 0 012.177.6 2.922 2.922 0 011.113 1.955l.743 6.024a6.217 6.217 0 00-2.852 2.287 6.394 6.394 0 00-1.059 4.4A6.41 6.41 0 0042.9 20.85a6.24 6.24 0 003.325 1.533l.605 4.9c.1.804-.139 1.572-.604 2.164a2.946 2.946 0 01-1.966 1.106L7.51 35.04a2.954 2.954 0 01-2.177-.6 2.922 2.922 0 01-1.112-1.956l-.655-5.3A6.287 6.287 0 005.72 24.85c.6-1.123.874-2.444.704-3.815a6.418 6.418 0 00-1.61-3.531 6.31 6.31 0 00-2.662-1.748L1.36 9.33c-.1-.805.139-1.573.604-2.165A2.946 2.946 0 013.93 6.06l36.752-4.487z"
                    stroke="#2F0959"
                    strokeWidth={1.95644}
                />
                <Path
                    d="M17.174 21.867c.258-.032.502-.175.668-.38.155-.215.231-.492.199-.756a1.066 1.066 0 00-.377-.685c-.414-.33-1.088-.248-1.409.172a1.084 1.084 0 00-.199.755c.033.264.174.515.377.685.21.16.483.24.74.209zM23.127 21.14c.258-.032.502-.175.668-.38.155-.215.231-.492.199-.755a1.066 1.066 0 00-.377-.685c-.414-.331-1.088-.249-1.409.171a1.084 1.084 0 00-.199.756c.033.264.175.514.377.685.211.16.483.24.74.208z"
                    fill="#2F0959"
                />
                <Path
                    d="M27.438 26.792l.787.63a.987.987 0 001.403-.169 1.038 1.038 0 00-.175-1.434l-3.32-2.661c.687-.738 1.052-1.77.962-2.502-.112-.908-.47-2.634-1.061-5.134-.389-1.638-.734-2.997-.734-2.997-.373-1.588-1.965-2.723-3.55-2.53l-5.953.728c-1.584.193-2.853 1.677-2.828 3.289 0 0-.005 1.409.017 3.103.033 2.568.107 4.329.219 5.237.09.734.698 1.651 1.547 2.2l-1.699 2.23a3.767 3.767 0 00-.018.023l-.86 1.127a1.04 1.04 0 00.178 1.436.984.984 0 001.403-.172l.616-.81 13.066-1.594zm-11.335-.676l.876-1.15 7.1-.867 1.13.905-9.106 1.112zM15 18.014l9.298-1.135c.445 1.924.724 3.303.81 4.003.004.28-.416 1.083-.866 1.138l-7.937.97c-.451.055-1.053-.623-1.116-.88-.088-.715-.153-2.122-.19-4.096zm-.032-4.001c-.01-.602.495-1.191 1.08-1.263L22 12.023c.584-.07 1.217.38 1.358.983 0 .003.2.781.463 1.87l-8.849 1.08c-.007-1.12-.005-1.927-.005-1.943z"
                    fill="#2F0959"
                />
                <Path
                    d="M32.411 2.888l3.417 27.667"
                    stroke="#2F0959"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="3.03 3.03"
                />
            </G>
            <Path
                d="M55.051 14.72a.833.833 0 00-.309-.535l-1.58-1.145.358-1.935a.856.856 0 00-.13-.596.717.717 0 00-.522-.3l-1.912-.186-.64-1.852a.692.692 0 00-.41-.447.658.658 0 00-.597.015l-1.75.826-1.468-1.293a.765.765 0 00-.583-.177.795.795 0 00-.522.312l-1.11 1.607-1.898-.38a.805.805 0 00-.583.129.728.728 0 00-.29.532l-.17 1.951-1.81.641a.676.676 0 00-.434.416.704.704 0 00.02.61l.819 1.791-1.256 1.491a.795.795 0 00-.17.594.832.832 0 00.31.536l1.58 1.144-.359 1.936a.857.857 0 00.13.596.717.717 0 00.523.299l1.912.187.64 1.852a.692.692 0 00.41.447c.198.09.403.084.597-.016l1.749-.825 1.468 1.292a.765.765 0 00.583.177.795.795 0 00.523-.312l1.11-1.607 1.898.38c.21.032.41-.012.583-.128a.728.728 0 00.29-.533l.17-1.95 1.809-.642a.676.676 0 00.435-.416c.069-.2.077-.43-.02-.61l-.819-1.79 1.256-1.492a.83.83 0 00.17-.594z"
                fill="#33D18F"
                stroke="#136F48"
                strokeWidth={1.17923}
            />
            <Path
                d="M53.059 16.3a.755.755 0 00-.105.816l.82 1.792-1.81.641c-.27.09-.462.343-.499.654l-.19 1.934-1.899-.38c-.288-.06-.571.07-.748.302l-1.11 1.607-1.469-1.293a.797.797 0 00-.583-.177c-.074.01-.145.037-.216.065l-1.713.82-.64-1.851a.77.77 0 00-.643-.514l-1.911-.187.359-1.936a.794.794 0 00-.3-.766l-1.581-1.145 1.255-1.49a.755.755 0 00.105-.816l-.815-1.754 1.81-.641c.269-.09.462-.343.499-.653l.17-1.951 1.898.38c.288.06.571-.07.748-.302l1.11-1.607 1.47 1.292c.23.201.534.24.798.113l1.75-.825.64 1.851a.77.77 0 00.643.514l1.874.192-.359 1.936a.794.794 0 00.3.765l1.582 1.145-1.24 1.47z"
                fill="url(#paint0_linear_380_2906)"
            />
            <Path
                d="M48.297 12.716L45.56 16.3l-1.182-.945a1.1 1.1 0 00-.816-.244 1.027 1.027 0 00-.731.433c-.178.232-.272.53-.234.831.037.301.182.57.429.75l2.054 1.642c.249.198.539.278.815.244.276-.034.557-.183.732-.434l3.412-4.467c.37-.485.282-1.2-.197-1.6-.459-.384-1.159-.298-1.545.207z"
                fill="#fff"
            />
            <Path
                d="M49.477 13.643l-3.412 4.467a.353.353 0 01-.516.064l-2.072-1.64a.379.379 0 01-.143-.25.378.378 0 01.078-.276.362.362 0 01.244-.145.363.363 0 01.272.082l1.472 1.177c.166.132.406.103.516-.063l2.962-3.878a.353.353 0 01.516-.063c.184.13.212.356.083.525z"
                fill="#fff"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_380_2906"
                    x1={40.2118}
                    y1={24.5576}
                    x2={53.2872}
                    y2={7.24281}
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#43E1A8" />
                    <Stop offset={1} stopColor="#219393" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};

export const DoubleRupee = (props) =>
(
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={54}
      height={24}
      viewBox="0 0 54 24"
      fill="none"
      {...props}
    >
      <Rect
        x={0.45752}
        width={52.5513}
        height={24}
        rx={12}
        fill="url(#paint0_linear_3881_8451)"
      />
      <Path
        d="M23.43 7.656h-3.339c.328.21.583.451.766.726.183.269.314.563.393.884h2.18v1.866h-2.19a3.538 3.538 0 01-.903 1.62c-.452.472-1.045.838-1.778 1.1.203.092.39.213.56.364.177.15.334.33.471.54L23.145 20H20.76c-.445 0-.773-.177-.982-.53l-2.986-4.547a1.033 1.033 0 00-.373-.364c-.13-.072-.327-.108-.589-.108h-1.846v-1.787h1.944c.747 0 1.342-.14 1.788-.422.451-.282.769-.652.952-1.11h-4.694V9.266h4.753a1.988 1.988 0 00-.923-1.12c-.452-.274-1.064-.412-1.837-.412h-1.983V5.8h9.447v1.856zm16.15 0h-3.339c.328.21.583.451.766.726.184.269.315.563.393.884h2.18v1.866h-2.19a3.539 3.539 0 01-.903 1.62c-.452.472-1.044.838-1.778 1.1.203.092.39.213.56.364.177.15.334.33.471.54L39.295 20H36.91c-.445 0-.773-.177-.982-.53l-2.985-4.547a1.033 1.033 0 00-.374-.364c-.13-.072-.327-.108-.589-.108h-1.846v-1.787h1.944c.747 0 1.343-.14 1.788-.422.451-.282.769-.652.952-1.11h-4.694V9.266h4.753a1.987 1.987 0 00-.923-1.12c-.452-.274-1.064-.412-1.836-.412h-1.984V5.8h9.447v1.856z"
        fill="#fff"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_3881_8451"
          x1={0.45752}
          y1={24}
          x2={55.0939}
          y2={17.4842}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#FCDD5F" />
          <Stop offset={1} stopColor="#F2A330" />
        </LinearGradient>
      </Defs>
    </Svg>
  );

export const Rupee = (props) =>  (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={38}
      height={24}
      viewBox="0 0 38 24"
      fill="none"
      {...props}
    >
      <Rect
        x={0.918945}
        width={36.5513}
        height={24}
        rx={12}
        fill="url(#paint0_linear_3881_8449)"
      />
      <Path
        d="M23.967 7.656h-3.34c.328.21.583.451.767.726.183.269.314.563.392.884h2.18v1.866h-2.19a3.538 3.538 0 01-.903 1.62c-.452.472-1.044.838-1.777 1.1.203.092.39.213.56.364.176.15.333.33.47.54L23.683 20h-2.387c-.445 0-.772-.177-.982-.53l-2.985-4.547a1.033 1.033 0 00-.373-.364c-.131-.072-.328-.108-.59-.108H14.52v-1.787h1.945c.746 0 1.342-.14 1.787-.422.452-.282.77-.652.953-1.11h-4.695V9.266h4.754a1.988 1.988 0 00-.924-1.12c-.451-.274-1.063-.412-1.836-.412h-1.984V5.8h9.448v1.856z"
        fill="#fff"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_3881_8449"
          x1={0.918945}
          y1={24}
          x2={39.1976}
          y2={20.8248}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#FCDD5F" />
          <Stop offset={1} stopColor="#F2A330" />
        </LinearGradient>
      </Defs>
    </Svg>
  );


export const VoucherIcon = (props) => (
    <Svg
        width={16}
        height={10}
        viewBox="0 0 16 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <Path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M.5 1.085C.5.841.698.643.943.643h14.114c.245 0 .443.198.443.442v2.216c-.878.063-1.568.727-1.568 1.537 0 .81.69 1.475 1.568 1.538v1.968a.443.443 0 01-.443.443H.943A.443.443 0 01.5 8.344V6.376c.**************.136.005.942 0 1.705-.69 1.705-1.543 0-.852-.763-1.542-1.705-1.542A1.91 1.91 0 00.5 3.3V1.085z"
            fill="url(#paint0_linear_1219_230)"
        />
        <Path
            d="M15.5 3.3l.024.332.308-.022V3.3H15.5zm0 3.076h.332v-.31l-.308-.021-.024.33zm-15 0l.024-.331-.356-.026v.357H.5zM.5 3.3H.168v.356l.356-.025L.5 3.3zM.943.31a.775.775 0 00-.775.774h.664c0-.06.05-.11.11-.11V.31zm14.114 0H.943v.664h14.114V.31zm.775.774a.775.775 0 00-.775-.775v.665c.061 0 .11.05.11.11h.665zm0 2.216V1.085h-.664v2.216h.664zm-1.568 1.537c0-.607.526-1.154 1.26-1.206l-.048-.663c-1.021.074-1.876.856-1.876 1.87h.664zm1.26 1.207c-.734-.053-1.26-.6-1.26-1.207H13.6c0 1.013.855 1.796 1.876 1.87l.048-.663zm.308 2.299V6.376h-.664v1.968h.664zm-.775.775a.775.775 0 00.775-.775h-.664a.11.11 0 01-.11.11v.665zm-14.114 0h14.114v-.664H.943v.664zm-.775-.775c0 .428.347.775.775.775v-.664a.11.11 0 01-.11-.111H.167zm0-1.968v1.968h.664V6.376H.168zm.468-.327c-.038 0-.075-.002-.112-.004l-.048.662c.***************.16.006v-.664zm1.373-1.21c0 .637-.583 1.21-1.373 1.21v.664c1.093 0 2.037-.809 2.037-1.875H2.01zM.636 3.627c.79 0 1.373.572 1.373 1.21h.664c0-1.066-.944-1.874-2.037-1.874v.664zm-.112.004c.037-.003.074-.004.112-.004v-.664a2.24 2.24 0 00-.16.005l.048.663zM.168 1.085v2.216h.664V1.085H.168z"
            fill="#008CFF"
        />
        <Path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M.943.643a.443.443 0 00-.443.442v2.216a1.91 1.91 0 01.136-.005c.942 0 1.705.69 1.705 1.542 0 .852-.763 1.543-1.705 1.543-.046 0-.091-.002-.136-.005v1.968c0 .245.198.443.443.443h4.093V.643H.943z"
            fill="#fff"
        />
        <Path
            d="M.5 3.3H.168v.357l.356-.025L.5 3.3zm0 3.076l.024-.331-.356-.026v.357H.5zm4.536 2.41v.333h.333v-.332h-.333zm0-8.143h.333V.31h-.333v.333zm-4.204.442c0-.06.05-.11.11-.11V.31a.775.775 0 00-.774.775h.664zm0 2.216V1.085H.168v2.216h.664zm-.308.33c.037-.002.074-.003.112-.003v-.664a2.24 2.24 0 00-.16.005l.048.663zm.112-.003c.79 0 1.373.572 1.373 1.21h.664c0-1.066-.944-1.874-2.037-1.874v.664zm1.373 1.21c0 .638-.583 1.21-1.373 1.21v.665c1.093 0 2.037-.809 2.037-1.875H2.01zM.636 6.048c-.038 0-.075 0-.112-.003l-.048.662c.***************.16.006v-.664zm.196 2.296V6.376H.168v1.968h.664zm.11.11a.11.11 0 01-.11-.11H.168c0 .428.347.775.775.775v-.664zm4.094 0H.943v.665h4.093v-.664zM4.704.644v8.144h.665V.643h-.665zM.943.975h4.093V.31H.943v.665z"
            fill="#008CFF"
        />
        <Ellipse
            cx={8.56033}
            cy={3.19573}
            rx={0.711207}
            ry={0.702089}
            fill="#fff"
            stroke="#008CFF"
            strokeWidth={0.664286}
        />
        <Ellipse
            cx={10.9309}
            cy={6.47202}
            rx={0.711207}
            ry={0.702089}
            fill="#fff"
            stroke="#008CFF"
            strokeWidth={0.664286}
        />
        <Path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.829 2.802L7.688 6.795l4.14-3.993z"
            fill="#3B385E"
        />
        <Path
            d="M11.829 2.802L7.688 6.795"
            stroke="#008CFF"
            strokeWidth={0.664286}
            strokeLinecap="round"
        />
        <Defs>
            <LinearGradient
                id="paint0_linear_1219_230"
                x1={6.69994}
                y1={-2.87609}
                x2={1.10003}
                y2={8.51551}
                gradientUnits="userSpaceOnUse"
            >
                <Stop stopColor="#4AFFCD" />
                <Stop offset={1} stopColor="#7DBFCC" />
            </LinearGradient>
        </Defs>
    </Svg>
);

export const VoucherCodeLogo = (props) => (
    <Svg
      width={90}
      height={9}
      viewBox="0 0 90 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M8.016 9H6.408a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258L1.932 8.724a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084H.018l2.88-4.464L.132.324H1.74c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3L6.054.546a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542L5.148 4.47 8.016 9zm9.05 0H15.46a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084H9.069l2.88-4.464L9.183.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3l1.812-2.85a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542l-2.79 4.146L17.067 9zm9.052 0H24.51a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084H18.12L21 4.536 18.234.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3l1.812-2.85A.51.51 0 0124.3.378a.34.34 0 01.198-.054h1.542L23.25 4.47 26.118 9zm9.05 0H33.56a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084H27.17l2.88-4.464L27.284.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3l1.812-2.85a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542L32.3 4.47 35.168 9zm9.051 0h-1.608a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084h-1.506l2.88-4.464L36.335.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3l1.812-2.85a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542l-2.79 4.146L44.22 9zm9.05 0h-1.607a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084h-1.506l2.88-4.464L45.386.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3l1.812-2.85a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542l-2.79 4.146L53.27 9zm9.052 0h-1.608a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084h-1.506l2.88-4.464L54.437.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3l1.812-2.85a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542l-2.79 4.146L62.32 9zm9.05 0h-1.608a.411.411 0 01-.27-.084.716.716 0 01-.155-.192l-2.01-3.33c-.032.1-.07.186-.115.258l-1.925 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084h-1.506l2.88-4.464L63.486.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.145.144l1.974 3.18c.04-.1.088-.2.144-.3l1.811-2.85a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542l-2.79 4.146L71.371 9zm9.051 0h-1.608a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.252 1.252 0 01-.168.192.337.337 0 01-.24.084h-1.506l2.88-4.464L72.538.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3L78.46.546a.512.512 0 01.144-.168.34.34 0 01.198-.054h1.542l-2.79 4.146L80.422 9zm9.051 0h-1.608a.411.411 0 01-.27-.084.716.716 0 01-.156-.192l-2.01-3.33c-.032.1-.07.186-.114.258l-1.926 3.072a1.256 1.256 0 01-.168.192.338.338 0 01-.24.084h-1.506l2.88-4.464L81.589.324h1.608c.112 0 .192.016.24.048a.41.41 0 01.144.144l1.974 3.18c.04-.1.088-.2.144-.3l1.812-2.85a.51.51 0 01.144-.168.34.34 0 01.198-.054h1.542l-2.79 4.146L89.473 9z"
        fill="url(#paint0_linear_380_2995)"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_380_2995"
          x1={0}
          y1={11}
          x2={87.4285}
          y2={-20.9707}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F3D452" />
          <Stop offset={1} stopColor="#F09819" />
        </LinearGradient>
      </Defs>
    </Svg>
);

export const VoucherActivation = (props) => {
    return (
      <Svg
        width={16}
        height={17}
        viewBox="0 0 16 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <Path
          d="M10.911 5.733v.59c0 .174-.02.314-.06.42a2.224 2.224 0 01-.11.27l-2.64 5.515a1.015 1.015 0 01-.27.325c-.113.09-.267.135-.46.135h-1l2.705-5.385a3.63 3.63 0 01.205-.345c.07-.103.148-.201.235-.295H6.171a.313.313 0 01-.22-.095.297.297 0 01-.095-.215v-.92h5.055z"
          fill="#4A4A4A"
        />
        <Path
          d="M8.113 1.815c0-.16.13-.292.292-.286a7.593 7.593 0 015.061 2.207 7.676 7.676 0 012.26 5.358 7.677 7.677 0 01-2.142 5.407 7.595 7.595 0 01-5.012 2.318.283.283 0 01-.298-.28l-.013-.591a.302.302 0 01.285-.305 6.43 6.43 0 004.198-1.959 6.5 6.5 0 001.813-4.577 6.497 6.497 0 00-1.912-4.536 6.427 6.427 0 00-4.24-1.866.302.302 0 01-.292-.298v-.592zM3.301 14.725a.283.283 0 01-.409.024 7.648 7.648 0 01-1.488-1.954.283.283 0 01.129-.386l.526-.26a.302.302 0 01.397.132 6.474 6.474 0 001.208 1.584.302.302 0 01.023.415l-.386.445zM1.12 11.353a.283.283 0 01-.36-.194 7.696 7.696 0 01-.246-2.45.283.283 0 01.314-.261l.583.058c.16.016.276.159.267.32-.036.668.031 1.339.2 1.987a.302.302 0 01-.199.366l-.56.174zM.994 7.453a.283.283 0 01-.21-.35 7.674 7.674 0 011.034-2.23.283.283 0 01.405-.066l.472.35a.302.302 0 01.066.411 6.496 6.496 0 00-.839 1.81.302.302 0 01-.358.213l-.57-.138zM7.004 16.455a.283.283 0 01-.332.239 7.586 7.586 0 01-2.298-.848.283.283 0 01-.098-.396l.308-.503a.302.302 0 01.406-.102 6.421 6.421 0 001.86.686c.158.032.267.18.243.34l-.09.584z"
          fill="#4A4A4A"
        />
        <Path
          d="M9.918.843L7.559 2.055l2.036 1.583"
          stroke="#4A4A4A"
          strokeWidth={0.895636}
          strokeLinecap="round"
        />
      </Svg>
    );
  };

  export const PartiallyConfirmed = (props) => {
    return (
      <Svg
        width={15}
        height={14}
        viewBox="0 0 15 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <Path
          d="M6.8 4.9h1.4V3.5H6.8m.7 9.1A5.607 5.607 0 011.9 7c0-3.087 2.513-5.6 5.6-5.6 3.087 0 5.6 2.513 5.6 5.6 0 3.087-2.513 5.6-5.6 5.6zM7.5 0a7 7 0 100 14 7 7 0 000-14zm-.7 10.5h1.4V6.3H6.8v4.2z"
          fill="#4A4A4A"
        />
      </Svg>
    );
  };


  export const VoucherValidity = (props) =>  (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={17}
      height={16}
      viewBox="0 0 17 16"
      fill="none"
      {...props}
    >
      <G clipPath="url(#clip0_912_5664)">
        <Path
          d="M10.1 12.267c0 .236-.126.49-.411.705-.286.214-.704.362-1.189.362-.485 0-.903-.148-1.188-.362-.285-.214-.412-.469-.412-.705s.127-.49.412-.705c.285-.214.703-.362 1.188-.362s.903.148 1.189.362c.285.214.411.469.411.705z"
          stroke="#4A4A4A"
          strokeWidth={1.06667}
        />
        <Path
          d="M12.233 12.267c0 1.702-1.602 3.2-3.733 3.2-2.131 0-3.733-1.498-3.733-3.2 0-1.703 1.602-3.2 3.733-3.2 2.13 0 3.733 1.497 3.733 3.2z"
          stroke="#4A4A4A"
          strokeWidth={1.06667}
        />
        <Path
          d="M7.68 11.752h0a.984.984 0 001.64 0h0c.565-.845 1.053-1.56 1.471-2.176.768-1.127 1.305-1.917 1.67-2.541.286-.492.482-.908.603-1.33.122-.425.162-.834.162-1.305 0-2.67-2.103-4.86-4.726-4.86-2.623 0-4.726 2.19-4.726 4.86 0 .471.04.88.162 1.305.121.422.317.838.604 1.33.364.624.901 1.414 1.668 2.54.42.616.907 1.332 1.472 2.177zM9.65 4.4c0 .778-.557 1.206-1.15 1.206-.315 0-.6-.104-.802-.29-.195-.181-.347-.47-.347-.916 0-.446.152-.735.347-.915.202-.187.487-.29.802-.29.593 0 1.15.427 1.15 1.205z"
          fill="#4A4A4A"
          stroke="#fff"
          strokeWidth={0.918523}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_912_5664">
          <Path fill="#fff" transform="translate(.5)" d="M0 0H16V16H0z" />
        </ClipPath>
      </Defs>
    </Svg>

);
