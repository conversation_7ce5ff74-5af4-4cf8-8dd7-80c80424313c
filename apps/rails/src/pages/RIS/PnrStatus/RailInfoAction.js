import { Platform } from 'react-native';
import { Actions } from '../../../navigation';
import shareContents from '@mmt/legacy-commons/Common/utils/ShareUtils';
import fetch2 from '../../../fetch2';
import {
  getConfigStore,
  getStoredConfigStore,
} from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import fecha from 'fecha';
import { isEmpty } from 'lodash';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import TrainDetailsCard from './PnrStatusUtils/TrainDetailsCard';
import { PassengerList } from './PnrStatusUtils/PassengerList';
import { handleDeeplink } from '@mmt/legacy-commons/Common/utils/ReactDeeplinkHandler';
import { railsConfig } from '../../../RailsConfig';
import { addDays } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { getUserDetails, isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { getDeviceDetails } from '../../../Utils/CommonUtils';
import {
  INPUT_CITY_SELECTED,
  PNR_STATUS_FOOD_IN_TRAIN_LOAD,
  removeDataFromAsyncStorage,
} from '../../../Utils/RailsConstant';
import { getPokusConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import {
  RIS_LANDING_PNR_DETAILS_SUCCESS,
  trackMealsOmnitureLoadEvent,
} from '../../../RisAnalytics/RisAnalytics';
import { botmanSensorHeader } from '@mmt/legacy-commons/Native/NetworkModule';
import ViewState from '../../../Utils/ViewState';
import BusSharedModuleHolder from '@mmt/bus-shared/src';
import { populateApClassAndPriceFromPnrStatusApi } from 'apps/rails/src/Utils/RisUtils';
import { RAILS_LANDING_PNR_PAGE_NAME } from 'apps/rails/src/Utils/RailsLandingTrackingHelper';
import { getAlternateAvailabilityResponse } from '../../NewListing/Components/ConfirmedOptions/ConfirmedOptionsUtils';
import {
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY } from './TGSUtils/Constants';
import { fetchCrossSellData, fetchLtsCrossSellData } from '../Utils/RISAPIUtil';
import { logRisPdtEvent } from '../../../PdtAnalytics/PdtAnalyticsV2/RIS';
import { RIS_EVENT_VALUES } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS/RisPdtConstants';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import {
  getRailsListingDeeplinkFromLtsDetails,
  getRailsListingDeeplinkFromPnrDetails,
} from '../RisCommonUtils';
import { CROSS_SELL_TYPES } from './Components/PNRCrossSells/types';
import { getPNRPageConfig, getRailsMealsPnrDetailsPokus } from 'apps/rails/src/RailsAbConfig';
import { PDT_PNR_JOURNEY_ID_KEY } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/CommonPdtConstants';
import { INTERACTED_WITH_MEALS_PNR } from '../MealsOnTrain/Constants/MealsConstants';
import { getMealsNewFlow, showMealsWebView } from '@mmt/rails/src/RailsAbConfig';

export const ACTIONS_RAILS_PNR_STATUS_INIT = 'ACTIONS_RAILS_PNR_STATUS_INIT';
export const ACTIONS_RAILS_PNR_ALTERNATE_TRAINS = 'ACTIONS_RAILS_ALTERNATE_TRAINS';
export const ACTIONS_RAILS_PNR_NO_ALTERNATE_TRAINS = 'ACTIONS_RAILS_PNR_NO_ALTERNATE_TRAINS';
export const ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL = 'ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL';
export const ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL_ERROR = 'ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL_ERROR';
export const ACTIONS_RAILS_PNR_LISTING_SET_NO_NETWORK = 'ACTIONS_RAILS_PNR_LISTING_SET_NO_NETWORK';
export const ACTIONS_RAILS_PNR_LISTING_SET_BOTTOM_LOADER = 'ACTIONS_RAILS_PNR_LISTING_SET_BOTTOM_LOADER';
export const ACTIONS_RAILS_PNR_LISTING_SET_ERROR = 'ACTIONS_RAILS_PNR_LISTING_SET_ERROR';
export const ACTIONS_RAILS_PNR_RECENT_SEARCH = 'ACTIONS_RAILS_PNR_RECENT_SEARCH';
export const ACTIONS_RAILS_PNR_ALERT_SET = 'ACTIONS_RAILS_PNR_ALERT_SET';
export const ACTIONS_RAILS_PNR_ALERT_NOT_SET = 'ACTIONS_RAILS_PNR_ALERT_NOT_SET';
export const ACTIONS_RAILS_INFO_COACH_POSITION = 'ACTIONS_RAILS_INFO_COACH_POSITION';
export const ACTIONS_RAILS_INFO_COACH_ERROR = 'ACTIONS_RAILS_INFO_COACH_ERROR';
export const ACTIONS_RAILS_INFO_PNR_ALERT_LOADER = 'ACTIONS_RAILS_INFO_PNR_ALERT_LOADER';
export const ACTIONS_RAILS_INFO_ALT_TRAINS_LOADER = 'ACTIONS_RAILS_INFO_ALT_TRAINS_LOADER';
export const ACTION_RIS_LIVE_STATION_FORM_ORIGIN_SELECTED = 'ACTION_RIS_LIVE_STATION_FORM_ORIGIN_SELECTED';
export const ACTIONS_RAILS_INFO_ALT_BUS_LOADER = 'ACTIONS_RAILS_INFO_ALT_BUS_LOADER';
export const ACTIONS_RAILS_PNR_CROSS_SELL_BUS = 'ACTIONS_RAILS_PNR_CROSS_SELL_BUS';
export const ACTIONS_RAILS_PNR_ALTERNATE_TRAINS_NEW = 'ACTIONS_RAILS_PNR_ALTERNATE_TRAINS_NEW';
export const ACTIONS_RAILS_PNR_TRAIN_TRACKING_DETIALS = 'ACTIONS_RAILS_PNR_TRAIN_TRACKING_DETIALS';
export const ACTIONS_RAILS_PNR_CROSS_SELL_DATA = 'ACTIONS_RAILS_PNR_CROSS_SELL_DATA';
export const ACTIONS_RAILS_PNR_CROSS_SELL_DATA_TGS = 'ACTIONS_RAILS_PNR_CROSS_SELL_DATA_TGS';
export const ACTIONS_RAILS_UPCOMING_PNR = 'ACTIONS_RAILS_UPCOMING_PNR';
export const ACTIONS_RAILS_RIS_ANNOUNCMENTS = 'ACTIONS_RAILS_RIS_ANNOUNCMENTS';


const sendHotelDetails = hotelCrossSellData => async (dispatch) => {
  dispatch({
    type: ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL,
    data: {
      hotelCrossSellData,
      showHotelLoader: false,
      isHotelError: false,
    },
  });
};

const isMealAvailableForPnr = (mealDetails) => {
  const mealsWebView = showMealsWebView();
  const showNewMeals = getMealsNewFlow();
  const isZomato = mealsWebView || showNewMeals;
  return isZomato ? mealDetails?.ZomatoAvailable : mealDetails?.Available;
};

export const initPnrStatus = pnr => async (dispatch) => {
  try {
    removeDataFromAsyncStorage(PDT_PNR_JOURNEY_ID_KEY);
    setDataInStorage(INTERACTED_WITH_MEALS_PNR, false);
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      dispatch({
        type: ACTIONS_RAILS_PNR_LISTING_SET_NO_NETWORK,
        data: { showLoader: false, pnrViewState: ViewState.NO_INTERNET, pnrNumber: pnr },
      });
      return;
    }
    dispatch({
      type: ACTIONS_RAILS_PNR_LISTING_SET_BOTTOM_LOADER,
      data: { showLoader: true, pnrViewState: ViewState.LOADING },
    });
    const pnrDetailInfo = await getPnrDetails(pnr);
    const railsMealPokusValue = await getRailsMealsPnrDetailsPokus();
    if (railsMealPokusValue && isMealAvailableForPnr(pnrDetailInfo?.MealDetails)) {
      trackMealsOmnitureLoadEvent(PNR_STATUS_FOOD_IN_TRAIN_LOAD);
    }
    if (!isEmpty(pnrDetailInfo)) {
      dispatch(sendSuccess(pnrDetailInfo, pnr));
    } else {
      dispatch({
        type: ACTIONS_RAILS_PNR_LISTING_SET_ERROR,
        data: { showLoader: false, pnrViewState: ViewState.ERROR, pnrNumber: pnr },
      });
      return;
    }
    const isOldPnrPage = getPNRPageConfig();
    if (isOldPnrPage && pnrDetailInfo.CheckAlternateAvailability) {
      try {
        const originStationCode = pnrDetailInfo?.StationDetails?.BoardingPoint?.code;
        const destinationStationCode = pnrDetailInfo?.StationDetails?.ReservationUpto?.code;
        const departureDate = pnrDetailInfo?.PnrDetails?.SourceDoj?.FormattedDate;
        const [day, month, year] = departureDate.split('-');
        const departureDateObj = new Date(year, month - 1, day);
        const trainNumber = pnrDetailInfo?.TrainDetails?.Train?.Number;
        dispatch(
          getNewAlternateTrains({
            originStationCode,
            destinationStationCode,
            departureDateObj,
            trainNumber,
          }),
        );
      } catch (error) {
        console.log('unable to get confirmed options: ', error);
      }
      dispatch(getAlternateTrains(pnr));
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { PnrDetails: { PnrCurrentStatus = '' } } = pnrDetailInfo;
    populateApClassAndPriceFromPnrStatusApi(pnrDetailInfo, RAILS_LANDING_PNR_PAGE_NAME);
    if (!isOldPnrPage && pnrDetailInfo.PnrDetailsEncrypted) {
      dispatch(
        getCrossSellData(
          pnrDetailInfo,
          pnrDetailInfo.PnrDetailsEncrypted,
          pnrDetailInfo.ConnectorPnrResponseEncoded,
        ),
      );
    } else {
      logRisPdtEvent({
        eventValue: RIS_EVENT_VALUES.PNR_PAGE_RENDERED,
        pnrDetails: pnrDetailInfo,
      });
    }

    let defaultPokusRISBusCrossSell = false;
    if (Platform.OS === 'web') {
      defaultPokusRISBusCrossSell = true;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const showRISBusCrossSell = await getPokusConfig(
      PokusLobs.BUS,
      'busRISpnrCrossSell',
      defaultPokusRISBusCrossSell,
    );
    if (isOldPnrPage) {
      try {
        const originStation = {
          cityName: pnrDetailInfo.CityDetails.SourceCity.Name,
          code: pnrDetailInfo.CityDetails.SourceCity.Code,
        };
        const destinationStation = {
          cityName: pnrDetailInfo.CityDetails.DestinationCity.Name,
          code: pnrDetailInfo.CityDetails.DestinationCity.Code,
        };
        const hotelCrossSellData = await getHotelsList(originStation, destinationStation);
        if (hotelCrossSellData) {
          dispatch(sendHotelDetails(hotelCrossSellData));
        }
      } catch (error) {
        console.log(error);
      }
    }
  } catch (error) {
    dispatch({
      type: ACTIONS_RAILS_PNR_LISTING_SET_ERROR,
      data: { showLoader: false, pnrViewState: ViewState.ERROR, pnrNumber: pnr },
    });
  }
};

export const getLtsCrossSellData =
  (startingStation, endingStation, returnDate) => async (dispatch) => {
    try {
      const pnrMasterConfig = await getConfigStore(configKeys.RAILS_PNR_MASTER_CONFIG);
      const crossLtsSellResponse = await fetchLtsCrossSellData(
        startingStation.Station.code,
        endingStation.Station.code,
      );
      if (crossLtsSellResponse.success) {
        const ltsCrossSellDataObj = {
          viewAllTrainsDeepLink: getRailsListingDeeplinkFromLtsDetails(
            startingStation,
            endingStation,
            returnDate,
          ),
          searchParams: {
            sourceName: startingStation?.Station?.name,
            destinationName: endingStation?.Station?.name,
            sourceCode: startingStation?.Station?.code,
            destinationCode: endingStation?.Station?.code,
            date: new Date(returnDate),
          },
          type: CROSS_SELL_TYPES.RETURN_TICKETS,
          data: crossLtsSellResponse?.returnTripAvailabilityResponse?.availabilityByDate,
          coupon: crossLtsSellResponse?.returnTripAvailabilityResponse?.coupon,
          configData: pnrMasterConfig.crossSells?.returnTicketOptions,
        };
        dispatch({
          type: ACTIONS_RAILS_PNR_CROSS_SELL_DATA,
          data: ltsCrossSellDataObj,
        });
      }
    } catch (error) {
      dispatch({
        type: ACTIONS_RAILS_PNR_CROSS_SELL_DATA,
        data: null,
      });
      console.error('Error in fetching cross sell data: ', error);
    }
  };

const getCrossSellData =
  (pnrDetailInfo, pnrDetailsEncrypted, connectorPnrResponseEncoded) => async (dispatch) => {
    try {
      const pnrMasterConfig = getStoredConfigStore(configKeys.RAILS_PNR_MASTER_CONFIG);
      const crossSellResponse = await fetchCrossSellData(
        pnrDetailsEncrypted,
        connectorPnrResponseEncoded,
      );
      if (crossSellResponse.success) {
        if (crossSellResponse?.tgsEligibilityRisResponse?.tgsEligibilityResponse?.isTgEligible) {
          dispatch({
            type: ACTIONS_RAILS_PNR_CROSS_SELL_DATA_TGS,
            data: crossSellResponse?.tgsEligibilityRisResponse?.tgsEligibilityResponse,
          });
          return;
        }
        let crossSellDataObj = {
          viewAllTrainsDeepLink: getRailsListingDeeplinkFromPnrDetails(pnrDetailInfo, false),
        };
        const { TrainDetails, CityDetails: { SourceCity, DestinationCity } = {} } = pnrDetailInfo;
        if (
          crossSellResponse?.alternateAvailabilityRisResponse?.alternateAvailabilityResponse
            ?.alternateAvailabilityList?.length
        ) {
          const searchParams = {
            sourceName: SourceCity?.Name,
            destinationName: DestinationCity?.Name,
            sourceCode: SourceCity?.Code,
            destinationCode: DestinationCity?.Code,
            date: new Date(TrainDetails?.TrainStartingDate),
          };
          crossSellDataObj = {
            ...crossSellDataObj,
            searchParams,
            type: CROSS_SELL_TYPES.CONFIRMED_OPTIONS,
            data: crossSellResponse?.alternateAvailabilityRisResponse?.alternateAvailabilityResponse
              ?.alternateAvailabilityList[0],
            coupon: crossSellResponse?.alternateAvailabilityRisResponse?.coupon,
            configData: pnrMasterConfig.crossSells?.confirmedOptions,
          };
        } else if (
          crossSellResponse?.returnTripAvailabilityResponse?.availabilityByDate?.length
        ) {
          const searchParams = {
            sourceName: DestinationCity?.Name,
            destinationName: SourceCity?.Name,
            sourceCode: DestinationCity?.Code,
            destinationCode: SourceCity?.Code,
            date: new Date(TrainDetails?.TrainStartingDate),
          };
          crossSellDataObj = {
            ...crossSellDataObj,
            searchParams,
            type: CROSS_SELL_TYPES.RETURN_TICKETS,
            data: crossSellResponse?.returnTripAvailabilityResponse?.availabilityByDate,
            coupon: crossSellResponse?.returnTripAvailabilityResponse?.coupon,
            configData: pnrMasterConfig.crossSells?.returnTicketOptions,
          };
        } else if (crossSellResponse?.busSearchV3RisResponse?.busSearchV3Response?.edges?.length) {
          const { cityDetails } =
            crossSellResponse?.busSearchV3RisResponse?.busSearchV3Response || {};
          const searchParams = {
            sourceName: cityDetails?.sourceCityName,
            destinationName: cityDetails?.destinationCityName,
            sourceCode: cityDetails?.sourceCityCode,
            destinationCode: cityDetails?.destinationCityCode,
            date: new Date(TrainDetails?.TrainStartingDate),
          };
          crossSellDataObj = {
            ...crossSellDataObj,
            searchParams,
            type: CROSS_SELL_TYPES.BUS,
            data: crossSellResponse?.busSearchV3RisResponse?.busSearchV3Response?.edges,
            coupon: crossSellResponse?.busSearchV3RisResponse?.busSearchV3Response?.coupon,
            configData: pnrMasterConfig.crossSells?.busOptions,
          };
        }
        dispatch({
          type: ACTIONS_RAILS_PNR_CROSS_SELL_DATA,
          data: crossSellDataObj,
        });
        // Log PNR and cross sell details to pdt
        logRisPdtEvent({
          eventValue: RIS_EVENT_VALUES.PNR_PAGE_RENDERED,
          pnrDetails: pnrDetailInfo,
          pnrCrossSell: crossSellDataObj,
        });
      } else {
        // Log only PNR details to pdt
        logRisPdtEvent({
          eventValue: RIS_EVENT_VALUES.PNR_PAGE_RENDERED,
          pnrDetails: pnrDetailInfo,
        });
      }
    } catch (error) {
      // Log only PNR details to pdt
      logRisPdtEvent({
        eventValue: RIS_EVENT_VALUES.PNR_PAGE_RENDERED,
        pnrDetails: pnrDetailInfo,
      });
      console.error('Error in fetching cross sell data: ', error);
    }
};

export const deeepLinkHandling = deeplink => () => {
  handleDeeplink(deeplink);
};

export const onOriginClicked = (dispatch, getState) => {
  const { sourceStationCode, destinationStationCode } = getState().liveStationReducer;
  Actions.railsCityPicker({
    selectedCity: sourceStationCode,
    pairedCity: destinationStationCode,
    inputCitySelected: INPUT_CITY_SELECTED.source,
    actionType: ACTION_RIS_LIVE_STATION_FORM_ORIGIN_SELECTED,
  });
};

export const onSharePress = trainInfo => () => {
  const title = 'PNR Status';
  const {
    trainName,
    trainNumber,
    platform,
    departStation,
    departStationCode,
    arrivalStation,
    arrivalStationCode,
    isChartPrepared,
    pnrNumber,
  } = trainInfo;
  const PNR_SEARCH_URL = 'https://www.makemytrip.com/rails/pnrsearch?pnr=';
  const chartStatus = isChartPrepared ? 'Chart Prepared' : 'Chart not Prepared';
  const url = PNR_SEARCH_URL + pnrNumber;
  const message = ` PNR Number - ${pnrNumber}

  Train Number - ${trainNumber}

  Train Name - ${trainName}

  From - ${departStation}(${departStationCode})
  ${platform ? `\nPlatform - ${platform}\n` : ''}
  To - ${arrivalStation}(${arrivalStationCode})

  ${chartStatus}

  For more information, Please visit ${url}`;
  if (url || message) {
    shareContents(title, message, url);
  }
};

export const onPnrAlertClicked = (pnr, enabled) => async (dispatch) => {
  const data = {};
  try {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      data.pnrAlertState = ViewState.NO_INTERNET;
      dispatch({
        type: ACTIONS_RAILS_PNR_ALERT_NOT_SET,
        data,
      });
      return;
    }
    dispatch({
      type: ACTIONS_RAILS_INFO_PNR_ALERT_LOADER,
      data: { pnrAlertState: ViewState.LOADING, showLoader: true },
    });
    const pnrAlertInfo = await getPnrAlert(pnr, enabled);
    if (!pnrAlertInfo) {
      data.pnrAlertState = ViewState.ERROR;
      data.enabled = false;
      dispatch({
        type: ACTIONS_RAILS_PNR_ALERT_NOT_SET,
        data,
      });
      return;
    }
    dispatch(sendPnrAlert(pnrAlertInfo, enabled));
  } catch (error) {
    data.pnrAlertState = ViewState.ERROR;
    data.enabled = false;
    dispatch({
      type: ACTIONS_RAILS_PNR_ALERT_NOT_SET,
      data,
    });
  }
};


export const getAlternateTrains = pnr => async (dispatch) => {
  try {
    dispatch({
      type: ACTIONS_RAILS_INFO_ALT_TRAINS_LOADER,
      data: { showAltLoader: true },
    });
    const alternateTrains = await getAlternateTrainDetails(pnr);
    if (!alternateTrains) {
      dispatch({
        type: ACTIONS_RAILS_PNR_NO_ALTERNATE_TRAINS,
        data: {
          showAlternateTrains: false,
        },
      });
      return;
    }
    dispatch(sendAlternateTrains(alternateTrains));
  } catch (error) {
    console.log(error);
  }
};

export const getNewAlternateTrains = ({
  originStationCode,
  destinationStationCode,
  departureDateObj,
  trainNumber = '',
  }) => async (dispatch) => {
    try {
      dispatch({
        type: ACTIONS_RAILS_INFO_ALT_TRAINS_LOADER,
        data: { showAltLoader: true },
      });
      const {data, status} = await getAlternateAvailabilityResponse(originStationCode,
        destinationStationCode, departureDateObj);
      let alternateAvailabilityList = '';
      if (status === 200) {
        if (data && !data?.error) {
          alternateAvailabilityList = data?.alternateAvailabilityList[0];
          if (trainNumber) {
            alternateAvailabilityList =
              data?.alternateAvailabilityList.filter(item => item.trainNumber === trainNumber)[0] ||
              alternateAvailabilityList;
          }
        }
      } else {
        dispatch({
          type: ACTIONS_RAILS_PNR_NO_ALTERNATE_TRAINS,
          data: {
            showAlternateTrains: false,
          },
        });
      }
      dispatch({
        type: ACTIONS_RAILS_PNR_ALTERNATE_TRAINS_NEW,
        data: {
          alternateAvailabilityList,
        },
      });
    } catch (error) {
      console.log('Error in fetching Alternate Trains: ', error);
    }

};

export const getAlternateBus = () => async (dispatch, getState) => {
  const {
    pnrDetailInfo: {
      StationDetails,
      PnrDetails,
    },
  } = getState().railInfoReducer;

  const journeyDetails = {
    source: StationDetails.BoardingPoint.code,
    destination: StationDetails.ReservationUpto.code,
    date_of_journey: PnrDetails.SourceDoj.FormattedDate,
  };
  try {
    dispatch({
      type: ACTIONS_RAILS_INFO_ALT_BUS_LOADER,
      data: true,
    });
    const alternateBuses = await getAlternateBusDetails(journeyDetails, 5);
    if (!alternateBuses) {
      dispatch({
        type: ACTIONS_RAILS_INFO_ALT_BUS_LOADER,
        data: false,
      });
      return;
    }
    if (alternateBuses && alternateBuses.buses.length > 0) {
      const busSharedResourceProvider = BusSharedModuleHolder.get();
      if (!busSharedResourceProvider) {
        throw new Error('Bus-Shared module not bootstrapped');
      }
      const { trackRisBusCrossSell } = busSharedResourceProvider.getBusSharedTracker();
      trackRisBusCrossSell(RIS_LANDING_PNR_DETAILS_SUCCESS, 'Mob_Bus_RIS_PNR_Bus_Loaded');
    }
    dispatch(sendAlternateBus(alternateBuses));
  } catch (error) {
    console.log(error);
  }
};
async function getPnrAlert(pnr, enabled) {
  try {
    let actions;
    if (!enabled) {
      actions = 'SUBSCRIBE';
    } else {
      actions = 'UNSUBSCRIBE';
    }
    const url = 'https://railsinfo-services.makemytrip.com/api/rails/pnr/alert/v1';
    const body = {
      pnrID: `${pnr}`,
      action: actions,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    const header = await _getPnrDetailHeaders();
    const requestBody = JSON.stringify(body);
    const response = await fetch2(url, {
      method: 'POST',
      headers: header,
      body: requestBody,
    });
    if (!response) {
      return null;
    }
    const jsonData = await response.json();
    return jsonData;
  } catch (e) {
    return null;
  }
}

const _getChannelCode = () => {
  let channelCode;
  if (Platform.OS === 'web') {
    channelCode = 'PWA';
  } else if (Platform.OS === 'android') {
    channelCode = 'ANDROID';
  } else if (Platform.OS === 'ios') {
    channelCode = 'IOS';
  } else {
    channelCode = Platform.OS;
  }
  return channelCode;
};

export async function getPnrDetails(pnr) {
  try {
    const url = 'https://railsinfo-services.makemytrip.com/api/rails/pnr/currentstatus/v1';
    const body = {
      pnrID: `${pnr}`,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    const header = await _getPnrDetailHeaders();
    let railsTGSHeaders = {};
     const tgsEligibilityNeeded = await  getPokusConfig(PokusLobs.RAIL,'rail_tgs_ONOFFswitch_PNRdetail', false);
      railsTGSHeaders = { 'TgsEligibilityNeeded': tgsEligibilityNeeded };

    const response = await fetch2(url, {
      method: 'POST',
      headers: { ...header, ...railsTGSHeaders },
      body: JSON.stringify(body),
    });

    if (!response) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
}

async function getAlternateTrainDetails(pnr) {
  try {
    const url = 'https://railsinfo-services.makemytrip.com/api/rails/train/alternateavailability/v1';
    const body = {
      pnrID: `${pnr}`,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    const header = {
      'Content-Type': 'application/json',
      'cache-control': 'no-cache',
    };
    const response = await fetch2(url, {
      method: 'POST',
      headers: header,
      body: JSON.stringify(body),
    });
    if (!response) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
}

export const sendAlternateTrains = alternateTrains => (dispatch) => {
  const {
    AlternateAvailabilityTitle: headerTitle,
    AlternateAvailabilitySubTitle: subheaderTitle,
    AlternateAvailabilityList: trainList,
    ViewAllTrainsDeeplink: deeplink,
    LastUpdatedOn: lastUpdated,
    TotalAvailabilityOptions: totalOption,
    showAlternateTrains = true,
  } = alternateTrains;
  dispatch({
    type: ACTIONS_RAILS_PNR_ALTERNATE_TRAINS,
    data: {
      headerTitle,
      subheaderTitle,
      trainList,
      deeplink,
      lastUpdated,
      totalOption,
      showAlternateTrains,
    },
  });
};


export async function getAlternateBusDetails(journeyDetails, numberOfBus) {
  const { source, destination, date_of_journey } = journeyDetails;
  try {
    const url = 'https://mbus.makemytrip.com/api/mobile/search/getAlternateRoutes';
    const getChannel = _getChannelCode();
    const body = {
      source,
      destination,
      location_code_category: 'RailsStationCodes',
      no_of_buses: numberOfBus,
      date_of_journey,
      channel: getChannel === 'PWA' ? 'MWEB' : getChannel,
    };
    const header = {
      'Content-Type': 'application/json',
      'cache-control': 'no-cache',
    };
    const response = await fetch2(url, {
      method: 'POST',
      headers: header,
      body: JSON.stringify(body),
    });
    if (!response) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
}
export const sendAlternateBus = alternateBuses => (dispatch) => {
  dispatch({
    type: ACTIONS_RAILS_PNR_CROSS_SELL_BUS,
    data: alternateBuses,
  });
};

export const sendPnrAlert = (pnrAlertInfo, enabled) => (dispatch) => {
  const { Success, Message } = { ...pnrAlertInfo };
  if (Success) {
    dispatch({
      type: ACTIONS_RAILS_PNR_ALERT_SET,
      data: {
        showLoader: false,
        pnrAlertSet: true,
        pnrAlertState: ViewState.SHOW_DETAIL,
        enabled,
        message: Message,
      },
    });
    return;
  }
  dispatch({
    type: ACTIONS_RAILS_PNR_ALERT_NOT_SET,
    data: {
      pnrAlertState: ViewState.ERROR,
      enabled: false,
    },
  });
};

export const sendSuccess = (pnrDetailInfo, pnr) => (dispatch) => {
  const {
    TrainDetails, StationDetails, PnrDetails, PassengerDetails, Disclaimer,
    PnrSearchCoupon, PnrLoginPersuasionCoupon = {},TgsEligibility,IsBookedOnMMT = false,
  } = { ...pnrDetailInfo };
  dispatch(getTrackingDetails(pnrDetailInfo));
  const trainInfo = new TrainDetailsCard(TrainDetails, StationDetails, PnrDetails);
  const passengerList = new PassengerList(PnrDetails, PassengerDetails);
  const pnrNumber = pnr;
  const travelStartDate = pnrDetailInfo.PnrDetails.SourceDoj.FormattedDate;
  const travelEndDate = pnrDetailInfo.PnrDetails.DestinationDoj.FormattedDate;
  const destCityName = pnrDetailInfo.CityDetails.DestinationCity.Name;
  const recentPnr = {
    pnrNumber,
    travelStartDate,
    travelEndDate,
    destCityName,
  };
  const data = {};
  data.pnrDetailInfo = pnrDetailInfo;
  data.trainInfo = trainInfo;
  data.passengerList = passengerList;
  data.showLoader = false;
  data.pnrViewState = ViewState.SHOW_DETAIL;
  data.disclaimer = Disclaimer;
  data.coachPosition = StationDetails.CoachPosition;
  data.pnrNumber = pnr;
  data.pnrAlertState = null;
  data.showAlternateTrains = false;
  data.hotelCrossSellData = null;
  data.enabledPnr = PnrDetails.AlertSet;
  data.enabled = PnrDetails.AlertSet;
  data.pnrSearchCoupon = PnrSearchCoupon;
  data.pnrLoginPersuasionCoupon = PnrLoginPersuasionCoupon;
  data.tgsEligibility = TgsEligibility;
  data.IsBookedOnMMT = IsBookedOnMMT;
  if (data.enabled) {
    data.message = 'An alert has been set successfully for this PNR';
    data.pnrAlertState = ViewState.SHOW_DETAIL;
  }

  dispatch({
    type: ACTIONS_RAILS_PNR_STATUS_INIT,
    data: { ...data, recentPnr },
  });
};


const getTrackingDetails = (pnrDetailInfo) => () => {
  const {
    TrainDetails, PnrDetails, CityDetails,
  } = { ...pnrDetailInfo };
  const advancePurchase = calculateAdvancePurchase(TrainDetails?.TrainStartingDate);
  const ticketPrice = PnrDetails?.FareDetails?.ticketFare;
  const originDest = `${CityDetails?.SourceCity?.Code} to ${CityDetails?.DestinationCity?.Code}`;
  const pnrId = PnrDetails?.Pnr;

  const trackingDetails = {
    advancePurchase,
    ticketPrice,
    originDest,
    pnrId,
  };
  setDataInStorage(PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY, trackingDetails);
};

const checkIfSameDay = (date1, date2) => {
  if (date1?.getYear() === date2?.getYear()
      && date1?.getMonth() === date2?.getMonth()
      && date1?.getDate() === date2?.getDate()) {
    return true;
  }
  return false;
};

const calculateAdvancePurchase = (pnrTrainStartingDate) => {
  const today = new Date();
  const trainArrivalDate = new Date(pnrTrainStartingDate);
  let differenceInDays;
  const isSameDay = checkIfSameDay(today, trainArrivalDate);
  if (isSameDay) {
    differenceInDays = 0;
  } else {
    const diffTime = Math.abs(trainArrivalDate.getTime() - today.getTime());
    differenceInDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return differenceInDays;
};

const getRequestForHotelsCrossSell = ({
  uniqueIds, originStation, destinationStation,
}) => {
  const header = {
    'Content-Type': 'application/json',
    deviceid: 'aaaaaaaaaaaaaaaa',
    uuid: 'MMMMMMMMMMM',
    ver: '7.5.1',
    vid: 'vid',
  };
  const request = {
    userEvent: {
      bookingDetails: {
        aggregateBookingStatus: 'CONFIRMED',
        bookingDateTime: fecha.format(addDays(new Date(), -2), 'YYYY-MM-DDTHH:mm:ss'),
        bookingID: '0000000000',
        bookingStatus: '0',
        coachDetails: [],
        flightSegment: [],
        hotelDetails: [],
        maxArrDate: fecha.format(addDays(new Date(), 2), 'YYYY-MM-DDTHH:mm:ss'),
        minDeptDate: fecha.format(addDays(new Date(), 2), 'YYYY-MM-DDTHH:mm:ss'),
        railDetails: [
          {
            destinationCityName: destinationStation.cityName,
            originCityName: originStation.cityName,
            passengers: [],
            destination: destinationStation.code,
            endDateTime: fecha.format(addDays(new Date(), 2), 'DD/MM/YYYY HH:mm:ss'),
            origin: originStation.code,
            productCode: 'TRAIN',
            startDateTime: fecha.format(addDays(new Date(), 2), 'DD/MM/YYYY HH:mm:ss'),
            totalPax: 2,
          },
        ],
      },
      bookingDevice: 'ANDROID',
      bookingId: '0000000000',
      idContext: 'MOB',
      pageContext: [
        'ris_thankyou',
      ],
      _cs: 'p5x2PPfV2IvWnPf0eXimkSLvpcyGdTWp5t6xnqF8ocxC603NJXQOXJ91AVL+gJ1ALKTgUCYh78FP\nZEKmOSpwKA==',
      pageName: 'mob:funnel:ris:pnr:pnrdetails',
      requestType: 'B2CAgent',
      visitorId: 'vid',

    },
  };
  const newRequest = isEmpty(uniqueIds) ? request :
    {
        ...request,
        userEvent: {
          ...request.userEvent,
        uniqueIds,
      },
      };

  return {
    header,
    request: newRequest,
  };
};

export const fetchCoachList = trainNumber => async (dispatch) => {
  try {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      dispatch({
        type: ACTIONS_RAILS_PNR_LISTING_SET_NO_NETWORK,
        data: { showLoader: false, coachViewState: ViewState.NO_INTERNET },
      });
      return;
    }
    dispatch({
      type: ACTIONS_RAILS_PNR_LISTING_SET_BOTTOM_LOADER,
      data: { showLoader: true, coachViewState: ViewState.LOADING },
    });
    const apiResponse = await fetchCoachPosition(trainNumber);
    if (apiResponse.Error) {
      dispatch({
        type: ACTIONS_RAILS_INFO_COACH_ERROR,
        data: {
          coachList: null,
          coachViewState: ViewState.ERROR,
          coachViewErrorMsg: apiResponse.Error.message,
        },
      });
    } else {
      const coachList = await convertToCoachList(apiResponse);
      const {
        Train: {
          Name: trainName,
        },
      } = apiResponse;
      dispatch({
        type: ACTIONS_RAILS_INFO_COACH_POSITION,
        data: { coachList, trainName, coachViewState: ViewState.SHOW_DETAIL },
      });
    }
  } catch (error) {
    console.log(error);
  }
};

const fetchCoachPosition = async (trainNumber) => {
  try {
    const url = 'https://railsinfo-services.makemytrip.com/api/rails/train/coachposition/v1';
    const body = {
      trainNumber: `${trainNumber}`,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    const header = {
      'Content-Type': 'appliation/json',
      'cache-control': 'no-cache',
    };
    const response = await fetch2(url, {
      method: 'POST',
      headers: header,
      body: JSON.stringify(body),
    });
    if (!response) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

const convertToCoachList = (data) => {
  const coaches = [];
  data.Coaches.map(item => (coaches.push(item.Name)));
  return coaches;
};

export const getHotelsList = async (originStation, destinationStation) => {
  let retry = 0;
  let response = {};
  while (retry < 2) {
    const { header, request } = getRequestForHotelsCrossSell({
      uniqueIds: null,
      originStation,
      destinationStation,
    });
    try {
      let url = railsConfig.hotelsCrossSellUrl;
      if (Platform.OS === 'web') {
        url = railsConfig.hotelsCrossSellUrl.replace('cp3o', 'mapi');
      }
      const res = await fetch2(url, {
        method: 'POST',
        body: JSON.stringify(request),
        headers: header,
      });
      response = await res.json();
    } catch (e) {
      console.log('Error is', e);
    }
    if (!isEmpty(response.cardData)) {
      return response;
    }
    const { header: newHeader, request: newRequest } = getRequestForHotelsCrossSell({
      uniqueIds: response.uniqueIds,
      originStation,
      destinationStation,
    });
    try {
      const res = await fetch2(railsConfig.hotelsCrossSellUrl, {
        method: 'POST',
        body: JSON.stringify(newRequest),
        headers: newHeader,
      });
      response = await res.json();
    } catch (e) {
      console.log('Error is', e);
    }
    if (retry === 1 && !isEmpty(response.cardData)) {
      return response;
    }
    retry += 1;
  }
  throw new Error('did not receive proper response from hotels api');
};

async function _getPnrDetailHeaders() {
  const isLoggedIn = await isUserLoggedIn();
  const deviceDetails = await getDeviceDetails();
  const botmanHeaders = await botmanSensorHeader();
  let deviceid = null;
  const { OS } = Platform;
  if (OS === 'ios' || OS === 'android') {
    deviceid = deviceDetails.deviceId;
  }
  const header = {
    'Content-Type': 'appliation/json',
    'cache-control': 'no-cache',
    deviceid,
    ...botmanHeaders,
  };
  if (isLoggedIn) {
    const { mmtAuth } = await getUserDetails();
    return {
      ...header,
      'mmt-auth': mmtAuth,
    };
  } else {
    return header;
  }
}

export const resetPnrStatus = (dispatch) => {
  dispatch({
    type: ACTIONS_RAILS_PNR_LISTING_SET_BOTTOM_LOADER,
    data: { showLoader: true, pnrViewState: ViewState.LOADING },
  });
};

