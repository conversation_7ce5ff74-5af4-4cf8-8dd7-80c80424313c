
import {getArrivalDeptTime, getPnrStatusJourneyDateFormat, getPnrStatusDateFormat} from '../../../../Utils/RisUtils';

export default class TrainDetailsCard {
  trainName = null;
  trainNumber = null;
  travelClass = null;
  travelQuota = null;
  platform = '';
  arrivalTime = '';
  arrivalDate = '';
  deptTime = '';
  deptDate = '';
  departStation = null;
  departCity = null;
  departStationCode = null;
  departExpectedPlatformNumber = null;
  arrivalStation = null;
  arrivalCity = null;
  arrivalStationCode = null;
  arrivalExpectedPlatformNumber = null;
  isChartPrepared = false;
  expectedChartPreparedTime = null;
  journeyDate = null;
  duration = null;
  pnrNumber = null;
  trainCancelledFlag = false;
  trainStatusMsg = '';

  constructor(trainDetails, stationDetails, pnrDetails) {
    this.trainName = ((trainDetails || {}).Train || {}).Name;
    this.trainNumber = ((trainDetails || {}).Train || {}).Number;
    this.travelClass = (pnrDetails || {}).Class;
    this.travelQuota = (pnrDetails || {}).Quota;
    this.platform = (stationDetails || {}).ExpectedPlatformNo;
    this.arrivalTime = getArrivalDeptTime(((trainDetails || {}).DepartureDetails || {}).ArrivalTime);
    this.deptTime = getArrivalDeptTime(((trainDetails || {}).DepartureDetails || {}).DepartureTime);
    this.arrivalDate = getPnrStatusDateFormat(((pnrDetails || {}).DestinationDoj || {}).FormattedDate);
    this.deptDate = getPnrStatusDateFormat(pnrDetails?.SourceDoj?.FormattedDate);
    this.departStation = ((stationDetails || {}).BoardingPoint || {}).name;
    this.arrivalStation = ((stationDetails || {}).ReservationUpto || {}).name;
    this.departStationCode = ((stationDetails || {}).BoardingPoint || {}).code;
    this.arrivalStationCode = ((stationDetails || {}).ReservationUpto || {}).code;
    this.departExpectedPlatformNumber = stationDetails?.BoardingPoint?.expectedPlatformNumber;
    this.arrivalExpectedPlatformNumber = stationDetails?.ReservationUpto?.expectedPlatformNumber;
    this.isChartPrepared = (trainDetails || {}).ChartPrepared;
    this.expectedChartPreparedTime = trainDetails?.ExpectedChartPreparedTime;
    this.journeyDate = getPnrStatusJourneyDateFormat(((pnrDetails || {}).SourceDoj || {}).FormattedDate);
    this.duration = (((trainDetails || {}).DepartureDetails || {}).Duration);
    this.pnrNumber = (pnrDetails.Pnr || {});
    this.trainCancelledFlag = (trainDetails || {}).TrainCancelledFlag;
    this.trainStatusMsg = this.trainCancelledFlag ? trainDetails.Message : '';
  }
}
