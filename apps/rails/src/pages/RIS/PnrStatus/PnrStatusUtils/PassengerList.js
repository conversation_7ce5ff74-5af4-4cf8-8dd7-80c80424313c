import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { BerthText } from '../../../Constants/Berth';


export class PassengerList {
  passenger = [];
  pnrAlertSet = false;
  isPnrAlertEligible = false;

  constructor(pnrDetails, passengerList) {
    passengerList.PassengerStatus.forEach((passengers) => {
      this.passenger.push(new TicketStatus(pnrDetails, passengers));
    });
    this.isPnrAlertEligible = pnrDetails.AlertEligible;
    this.pnrAlertSet = pnrDetails.AlertSet;
  }
}


export class TicketStatus {
  passengerName = null;
  currentStatus = null;
  bookingStatus = null;
  pnrNumber = null;
  isConfirmed = false;
  pnrStatus = null;
  predictionPercentage = null;
  ticketStatus = null;
  colorCode = colors.black;
  currentBerthCode = null;

  constructor(pnrDetails, passengers) {
    this.pnrNumber = passengers.Pnr;
    this.passengerName = `Passenger ${passengers.Number}`;
    this.bookingStatus = this.getBookinStatus(passengers.BookingStatus || passengers.BookingStatusNew);
    this.currentStatus = this.getCurrentStatus(
      passengers.CurrentStatus,
      passengers.CurrentBerthCode,
    );
    this.pnrStatus = passengers.CurrentStatusNew;
    this.isConfirmed = this.getConfirmationStatus(passengers.CurrentStatusNew);
    this.predictionPercentage = passengers.Prediction;
    this.ticketStatus = passengers.ConfirmTktStatus;
    this.colorCode = this.getBookingStatusColor(passengers.CurrentStatusNew);
    this.currentBerthCode = BerthText[passengers.CurrentBerthCode];
  }


  getConfirmationStatus(currentStatusNew) {
    if (currentStatusNew === 'CNF') {
      return true;
    }
    return false;
  }


  getBookinStatus(bookingStatus) {
    return bookingStatus.replace(/\s+/g, '/');
  }


  getCurrentStatus(currentStatus) {
    return currentStatus.replace(/\s+/g, '-');
  }

  getBookingStatusColor(currentStatusNew) {
    //Write a switch statement to return color for train booking status
    switch (currentStatusNew) {
      case 'CNF':
      case 'RAC':
        return colors.lightGreen16;
      case 'WL':
        return colors.lightYello;
      default:
        return colors.black;
    }
  }
}
