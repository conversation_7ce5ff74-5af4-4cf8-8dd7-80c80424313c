import React from 'react';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {isEmpty} from 'lodash';
import {deeepLinkHandling} from '../RailInfoAction';
import {View, Text, FlatList, Image, StyleSheet, SafeAreaView, Platform} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {getArrivalDeptTime, getPnrStatusDateFormat} from '../../../../Utils/RisUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {getTrainName} from '../../../Common/JourneyDetailsCard';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import PnrBusCrossSell from './PnrBusCrossSell';

import arrow from '@mmt/legacy-assets/src/ic_arrow_long_blue.webp';


const AlternateAvailabilityTrainsContainer = ({
  trainList,
  deeplink,
  totalOption,
  handlecardClick,
  onCloseTGSBottomSheet,
}) => {

  const _renderCounterItem = ({item}) => {
    if (!item.isLastItem) {
      return (
        <TouchableRipple onPress={() => {
            GenericModule.openDeepLink(item.TrainDetails.TravellerPageDeeplink);
            onCloseTGSBottomSheet();
          }}
          testID="alternate_availability_trains_container_touchable_ripple"
        >
          <View
            elevation={2}
            style={alternateTrainsStyles.cardStyle}
            testID="alternate_availability_trains_container_view"
          >
            <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text
                style={alternateTrainsStyles.trainNameStyle}
                testID="alternate_availability_trains_container_view_text"
              >
                {getTrainName(item?.TrainDetails?.Train?.Name?.toLowerCase())}
              </Text>
              <Text style={alternateTrainsStyles.trainNumberStyle}>
                #{item.TrainDetails.Train.Number}
              </Text>
            </View>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 6 }}
              testID="alternate_availability_trains_container_view_time_date_style"
            >
              <Text style={alternateTrainsStyles.timeDateStyle}>
                {getArrivalDeptTime(item.TrainDetails.BoardingStnDetails.DepartureTime)},
                {getPnrStatusDateFormat(item.TrainDetails.BoardingStnDetails.DepartureDate)}
              </Text>
              <Text
                style={alternateTrainsStyles.trainNumberStyle}
                testID="alternate_availability_trains_container_view_time_date_style_text"
              >
                {item.TrainDetails.TravelDuration}
              </Text>
            </View>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 12 }}
              testID="alternate_availability_trains_container_view_time_date_style_two"
            >
              <Text style={alternateTrainsStyles.timeDateStyle}>
                {item.TrainDetails.AvailabilityDetails.ClassType} |{' '}
                {item.TrainDetails.AvailabilityDetails.Quota}
              </Text>
              <Text
                style={alternateTrainsStyles.trainFareStyle}
                testID="alternate_availability_trains_container_view_time_date_style_three"
              >{`\u20B9 ${_getFare(item)}`}</Text>
            </View>
            {item.TrainDetails.AvailabilityDetails.AvailablityStatus && (
              <Text style={alternateTrainsStyles.availableStyle}>
                AVAILABLE {item.TrainDetails.AvailabilityDetails.AvailablityStatus}
              </Text>
            )}
          </View>
        </TouchableRipple>
      );
    } else if (item.listSize >= 3) {
      return (
        <TouchableRipple
          onPress={() => {
            item.onClick(item.deeplinkUrl);
            onCloseTGSBottomSheet();
          }}
          testID="alternate_availability_trains_container_touchable_ripple_two"
        >
          <View
            style={[
              alternateTrainsStyles.cardStyle,
              { justifyContent: 'center', alignItems: 'center' },
            ]}
            elevation={3}
            testID="alternate_availability_trains_container_view_two"
          >
            <Text
              style={{
                color: colors.azure,
                fontFamily: fonts.bold,
                fontSize: 16,
                justifyContent: 'center',
              }}
              testID="alternate_availability_trains_container_view_two_text"
            >
              View All Trains
            </Text>
          </View>
        </TouchableRipple>
      );
    }
  };

  if (trainList && trainList.length > 0) {
    return (
      <View>
        <View style={{ flexDirection: 'row' }}>
          <Text style={alternateTrainsStyles.trainsStyle}>{totalOption}</Text>
          <Text style={alternateTrainsStyles.trainsStyle}> Trains from </Text>
          <Text style={alternateTrainsStyles.trainsStyle}>{`\u20B9 ${_getFare(
            trainList[0],
          )}`}</Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 8,
          }}
        >
          <Text style={alternateTrainsStyles.departCityStyle}>
            {trainList[0]?.TrainDetails?.BoardingStnDetails?.Station?.name}
          </Text>
          <Image source={arrow} style={alternateTrainsStyles.justifyContentCenter} />
          <Text style={alternateTrainsStyles.arrivalCityStyle}>
            {trainList[0]?.TrainDetails?.DropStnDetails?.Station?.name}
          </Text>
          <View style={alternateTrainsStyles.verticalBar} />
          <Text style={alternateTrainsStyles.trainFareStyle}>
            {getPnrStatusDateFormat(trainList[0].TrainDetails.BoardingStnDetails.DepartureDate)}
          </Text>
        </View>
        {!isEmpty(trainList) && (
          <View
            style={{ flexDirection: 'row' }}
            testID="alternate_availability_trains_container_view_three"
          >
            <FlatList
              horizontal
              data={[
                ...trainList,
                {
                  isLastItem: true,
                  totalAvailableOptions: totalOption,
                  deeplinkUrl: deeplink,
                  onClick: handlecardClick,
                  listSize: trainList.length,
                },
              ]}
              showsHorizontalScrollIndicator={false}
              renderItem={_renderCounterItem}
              keyExtractor={(item, index) => `list-item-${index}`}
              testID="alternate_availability_trains_container_flat_list"
            />
          </View>
        )}
      </View>
    );
  }
  return null;
};

const _getFare = (trains) => trains.TrainDetails.AvailabilityDetails.TotalFare;

class AlternateJourney extends React.Component {
  render() {
    const {
      headerTitle,
      subheaderTitle,
      trainList,
      deeplink,
      lastUpdated,
      totalOption,
      handlecardClick,
      showAlternateTrains,
      travellerDeeplink,
      alternateBusesDetails,
      onCloseTGSBottomSheet,
    } = this.props;
    if (
      ((trainList && trainList.length > 0) ||
            (alternateBusesDetails && alternateBusesDetails !== null &&
            alternateBusesDetails.buses && alternateBusesDetails.buses.length > 0))) {
      return (
        <SafeAreaView style={{ flex: 1 }} testID="alternate_journey_container">
          <View>
            <View
              style={alternateTrainsStyles.containerStyle}
              testID="alternate_journey_container_view"
            >
              <Text
                style={alternateTrainsStyles.headerStyle}
                testID="alternate_journey_container_view_text"
              >
                {headerTitle}
              </Text>
              <Text
                style={alternateTrainsStyles.subHeaderStyle}
                testID="alternate_journey_container_view_text_two"
              >
                {subheaderTitle}
              </Text>
              {trainList && trainList.length > 0 && Platform.OS !== 'web' && <AlternateAvailabilityTrainsContainer
                trainList={trainList}
                deeplink={deeplink}
                lastUpdated={lastUpdated}
                totalOption={totalOption}
                handlecardClick={handlecardClick}
                onCloseTGSBottomSheet={onCloseTGSBottomSheet}
                showAlternateTrains={showAlternateTrains}
                travellerDeeplink={travellerDeeplink}
              />}
              <PnrBusCrossSell trainList={trainList} />
            </View>
          </View>
        </SafeAreaView>
      );
    }

    return null;
  }
}

AlternateAvailabilityTrainsContainer.propTypes = {  
  trainList: PropTypes.array,
  deeplink: PropTypes.string,
  totalOption: PropTypes.string,
  handlecardClick: PropTypes.func,
  onCloseTGSBottomSheet: PropTypes.func,
  trainList: PropTypes.array,
}

AlternateJourney.propTypes = {
  handlecardClick: PropTypes.func,
  onCloseTGSBottomSheet:PropTypes.func,
  trainList: PropTypes.array,
  deeplink: PropTypes.string,
  totalOption: PropTypes.string,
  lastUpdated: PropTypes.string,
  handlecardClick: PropTypes.func,
  onCloseTGSBottomSheet: PropTypes.func,
  showAlternateTrains: PropTypes.bool,
  travellerDeeplink: PropTypes.string,
  alternateBusesDetails: PropTypes.object,
  subheaderTitle: PropTypes.string,
  headerTitle: PropTypes.string,
};
const alternateTrainsStyles = StyleSheet.create({
  containerStyle: {
    backgroundColor: colors.goldenYellow3,
    paddingLeft: 16,
    paddingBottom: 13,
    borderColor: colors.datePickerColor,
    borderWidth: 0.5,
    marginTop: 10,
  },
  headerStyle: {
    fontFamily: fonts.light,
    fontSize: 22,
    color: colors.black,
    justifyContent: 'center',
    marginTop: 16,
  },
  subHeaderStyle: {
    fontFamily: fonts.bold,
    fontSize: 12,
    color: colors.black,
    marginTop: 8,
    marginRight: 16,
    width: '100%',
    marginBottom: 15,
  },
  trainsStyle: {
    fontSize: 14,
    fontFamily: fonts.bold,
    color: colors.azureSemi,
    marginTop: 15,
  },
  departCityStyle: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.black,
    marginRight: 8,
  },
  arrivalCityStyle: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.black,
    marginLeft: 8,
    marginRight: 8,
  },
  trainNameStyle: {
    fontSize: 12,
    fontFamily: fonts.black,
    color: colors.black,
    width: 120,
    height: 30,
  },
  trainNumberStyle: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
  },
  trainFareStyle: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.black,
  },
  timeDateStyle: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.black,
  },
  lastUpdatedStyle: {
    fontFamily: fonts.bold,
    fontSize: 10,
    color: colors.black,
  },
  availableStyle: {
    fontFamily: fonts.regular,
    fontSize: 10,
    color: colors.lightGreen4,
    marginTop: 5,
  },
  cardStyle: {
    width: 182,
    height: 103,
    marginTop: 15,
    marginBottom: 10,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 3,
    backgroundColor: colors.white,
  },
  verticalBar: {
    backgroundColor: colors.black,
    height: 21,
    width: 1,
    alignSelf: 'center',
    marginHorizontal: 3,
  },
  horizontalBar: {
    width: 'auto',
    height: 10,
    backgroundColor: colors.grey11,
    marginHorizontal: 16,
  },
  justifyContentCenter: {
    justifyContent: 'center',
  }
});

const mapStatesToProps = (state, ownProps) => {
  let {headerTitle, subheaderTitle} = state.railInfoReducer;
  const {
    trainList,
    deeplink,
    lastUpdated,
    totalOption,
    showAltLoader,
    showAlternateTrains,
    alternateBusesDetails,
  } = state.railInfoReducer;

  if ((!headerTitle || headerTitle === '') && alternateBusesDetails && alternateBusesDetails.buses && alternateBusesDetails.buses.length > 0) {
    headerTitle = 'Get a Confirmed Seat';
    subheaderTitle = 'Book alternate confirmed options';
  }
  return {
    ...ownProps,
    headerTitle,
    subheaderTitle,
    trainList,
    deeplink,
    lastUpdated,
    totalOption,
    showAltLoader,
    showAlternateTrains,
    alternateBusesDetails,
  };
};
const mapDispatchToProps = dispatch => ({
  handlecardClick: deeplink => dispatch(deeepLinkHandling(deeplink)),
});
export default connect(mapStatesToProps, mapDispatchToProps)(AlternateJourney);

