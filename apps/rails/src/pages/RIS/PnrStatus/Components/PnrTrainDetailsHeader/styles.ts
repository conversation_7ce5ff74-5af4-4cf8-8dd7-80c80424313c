import { StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  container: {
    marginTop: 10,
    marginHorizontal: 16,
  },
  trainNumber: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.greyText1,
    marginBottom: 6,
  },
  nameAndDate: {
    fontSize: 14,
    fontFamily: fonts.bold,
    fontWeight: '900',
    color: colors.black,
    marginBottom: 2,
  },
  selectedClass: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.greyText1,
    textAlign: 'center',
    marginTop: -1,
  },
  classWrapper: {
    paddingHorizontal: 4,
    borderRadius: 4,
    borderColor: colors.lightGray,
    borderWidth: 1,
    marginLeft: 10,
    justifyContent: 'center',
  },
  stationNameContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  stationNameText: {
    maxWidth: '48%',
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.defaultTextColor,
  },
  stationDetailContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 8,
  },
  stationDetailsText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.defaultTextColor,
  },
  platformText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.lighterBlue2,
  },
  platformTextContainer: {
    backgroundColor: colors.lighterBlue,
    paddingHorizontal: 6,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  separator: {
    width: 16,
    height: 1,
    backgroundColor: colors.grey30,
  },
  durationText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.lightShade,
    marginHorizontal: 6,
  },
  rowView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  trainNameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: colors.defaultTextColor,
  },
  duration: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  trainHeaderContainer: {
    marginTop: 4,
    marginBottom: 3,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  chartPrepared: {
    backgroundColor: colors.lightGreen18,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 18,
    paddingVertical: 2,
  },
  chartPreparedText: {
    fontSize: 12,
    color: colors.lightGreen16,
  },
  chartNotPrepared: {
    backgroundColor: colors.lighterBlue,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 18,
    paddingVertical: 2,
  },
  chartNotPreparedText: {
    fontSize: 12,
    color: colors.defaultTextColor,
  },
});

export const optionsStyles = StyleSheet.create({
  risContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    marginHorizontal: 12,
    justifyContent: 'space-between',
    columnGap: 8,
    flex: 1,
    flexWrap: 'wrap',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    borderColor: colors.lightGrey,
    borderWidth: 1,
    borderRadius: 16,
    padding: 6,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  imgContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.grey5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    width: 32,
    height: 32,
  },
  fullFlex: {
    flex: 1,
  },
  textStyle: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '600',
    color: colors.azureBlue2,
    marginLeft: 4,
    flex: 1,
  },
});
