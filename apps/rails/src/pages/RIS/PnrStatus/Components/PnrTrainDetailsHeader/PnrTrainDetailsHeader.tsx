import React from 'react';
import { Text, View } from 'react-native';
import { TrainInfo } from 'apps/rails/src/pages/Types/TrainInfoTypes';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { getTrainName } from '../../../../Common/JourneyDetailsCard';
import styles from './styles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';

interface Props {
  trainInfo: TrainInfo;
}

export const PnrTrainDetailsHeader = (props: Props) => {
  const { trainInfo } = props;

  return (
    <View style={styles.container} testId="pnr_train_details_header_container">
      <View
        style={styles.trainNameContainer}
        testId="pnr_train_details_header_container_train_name_container"
      >
        <View
          style={styles.rowContainer}
          testId="pnr_train_details_header_container_train_name_container_row_container"
        >
          {trainInfo?.trainName && (
            <Text
              style={[styles.nameAndDate, fontStyle('bold'), getLineHeight(16)]}
              testId="pnr_train_details_header_container_train_name_container_text"
            >
              {getTrainName(trainInfo?.trainName?.toLowerCase())}
            </Text>
          )}
          <View
            style={styles.classWrapper}
            testId="pnr_train_details_header_container_train_name_container_class_wrapper"
          >
            <Text
              style={[styles.selectedClass, fontStyle('bold'), getLineHeight(12)]}
              testId="pnr_train_details_header_container_train_name_container_text_text"
            >
              {trainInfo.travelClass} {trainInfo.travelQuota ? `| ${trainInfo.travelQuota}` : ''}
            </Text>
          </View>
        </View>
        <Text
          style={[styles.trainNumber, fontStyle('bold'), getLineHeight(16)]}
          testId="pnr_train_details_header_container_train_name_container_text_text_text"
        >{`#${trainInfo?.trainNumber}`}</Text>
      </View>
      <View
        style={styles.stationNameContainer}
        testId="pnr_train_details_header_container_station_name_container"
      >
        <Text
          style={[styles.stationNameText, fontStyle('bold'), getLineHeight(14.4)]}
          testId="pnr_train_details_header_container_station_name_container_text"
        >
          {`${trainInfo.departStation}`}
        </Text>
        <Text
          style={[styles.stationNameText, fontStyle('bold'), getLineHeight(14.4)]}
          testId="pnr_train_details_header_container_station_name_container_text_text"
        >
          {`${trainInfo.arrivalStation}`}
        </Text>
      </View>
      <View
        style={styles.stationDetailContainer}
        testId="pnr_train_details_header_container_station_detail_container"
      >
        <View
          style={styles.rowView}
          testId="pnr_train_details_header_container_station_detail_container_row_view"
        >
          <Text
            style={[styles.stationDetailsText, fontStyle('medium'), getLineHeight(14.4)]}
            testId="pnr_train_details_header_container_station_detail_container_row_view_text"
          >
            {trainInfo.departStationCode}
          </Text>
          {trainInfo.departExpectedPlatformNumber && (
            <View
              style={styles.platformTextContainer}
              testId="pnr_train_details_header_container_station_detail_container_row_view_platform_text_container"
            >
              <Text
                style={[styles.platformText, fontStyle('bold'), getLineHeight(14.4)]}
                testId="pnr_train_details_header_container_station_detail_container_row_view_platform_text_container_text"
              >
                {`PF ${trainInfo.departExpectedPlatformNumber}`}
              </Text>
            </View>
          )}
        </View>
        <View
          style={styles.rowView}
          testId="pnr_train_details_header_container_station_detail_container_row_view_two"
        >
          <View style={styles.separator} />
          <Text
            style={[styles.durationText, fontStyle('medium'), getLineHeight(16)]}
            testId="pnr_train_details_header_container_station_detail_container_row_view_two_text"
          >
            {trainInfo.duration}
          </Text>
          <View style={styles.separator} />
        </View>
        <View
          style={styles.rowView}
          testId="pnr_train_details_header_container_station_detail_container_row_view_three"
        >
          {trainInfo.arrivalExpectedPlatformNumber && (
            <View
              style={styles.platformTextContainer}
              testId="pnr_train_details_header_container_station_detail_container_row_view_three_platform_text_container"
            >
              <Text
                style={[styles.platformText, fontStyle('bold'), getLineHeight(14.4)]}
                testId="pnr_train_details_header_container_station_detail_container_row_view_three_platform_text_container_text"
              >
                {`PF ${trainInfo.arrivalExpectedPlatformNumber}`}
              </Text>
            </View>
          )}
          <Text
            style={[styles.stationDetailsText, fontStyle('medium'), getLineHeight(14.4)]}
            testId="pnr_train_details_header_container_station_detail_container_row_view_three_text"
          >
            {trainInfo.arrivalStationCode}
          </Text>
        </View>
      </View>
      <View
        style={styles.stationDetailContainer}
        testId="pnr_train_details_header_container_station_detail_container_two"
      >
        <View style={styles.rowContainer}>
          <Text
            style={[styles.timeText, fontStyle('medium'), getLineHeight(14.4)]}
            testId="pnr_train_details_header_container_station_detail_container_two_row_view_text"
          >
            {`${trainInfo.deptTime}, `}
          </Text>
          <Text
            style={[styles.timeText, fontStyle('medium'), getLineHeight(14.4)]}
            testId="pnr_train_details_header_container_station_detail_container_two_row_view_text_text"
          >
            {trainInfo.deptDate}
          </Text>
        </View>
        <View style={styles.rowContainer}>
          <Text
            style={[styles.timeText, fontStyle('medium'), getLineHeight(14.4)]}
            testId="pnr_train_details_header_container_station_detail_container_two_row_view_text_text_text"
          >
            {`${trainInfo.arrivalTime}, `}
          </Text>
          <Text
            style={[styles.timeText, fontStyle('medium'), getLineHeight(14.4)]}
            testId="pnr_train_details_header_container_station_detail_container_two_row_view_text_text_text_text"
          >
            {trainInfo.arrivalDate}
          </Text>
        </View>
      </View>
      <View style={trainInfo.isChartPrepared ? styles.chartPrepared : styles.chartNotPrepared}>
        {trainInfo.isChartPrepared ? (
          <Text
            style={[styles.chartPreparedText, fontStyle('bold')]}
            testId="pnr_train_details_header_container_station_detail_container_two_row_view_text_text_text_text_text"
          >
            {_label('pnr_chart_prepared')}
          </Text>
        ) : trainInfo.expectedChartPreparedTime ? (
          <Text
            style={[styles.chartNotPreparedText, fontStyle('medium')]}
            testId="pnr_train_details_header_container_station_detail_container_two_row_view_text_text_text_text_text_text"
          >
            {`${_label('pnr_chart_will_be_prepared')} ${trainInfo.expectedChartPreparedTime}`}
          </Text>
        ) : (
          <Text
            style={[styles.chartNotPreparedText, fontStyle('bold')]}
            testId="pnr_train_details_header_container_station_detail_container_two_row_view_text_text_text_text_text_text_text"
          >
            {_label('chart_not_prepared')}
          </Text>
        )}
      </View>
    </View>
  );
};
