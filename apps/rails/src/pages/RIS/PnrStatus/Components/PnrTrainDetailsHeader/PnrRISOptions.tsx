import React from 'react';
import { Image, Platform, Text, View } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { optionsStyles } from './styles';
import { openLiveTrainStatus } from 'apps/rails/src/pages/RIS/RisCommonUtils';
import { Actions } from 'apps/rails/src/navigation';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  PNR_DETAILS_LTS_CLICKED,
  PNR_DETAILS_SCHEDULE_CLICKED,
  RIS_LANDING_PNR_DETAILS_SUCCESS,
} from 'apps/rails/src/RisAnalytics/RisAnalytics';

interface Props {
  trainInfo: unknown;
}

interface OptionProps {
  icon: unknown;
  iconText: unknown;
  onClick: unknown;
}

const Options = (props: OptionProps) => {
  const { icon, iconText, onClick } = props;
  const handleOnPress = () => onClick();
  return (
    <View style={optionsStyles.fullFlex} testId="pnr_ris_options_container_full_flex">
      <TouchableRipple onPress={handleOnPress} style={optionsStyles.contentContainer}>
        <View
          style={Platform.OS === 'ios' ? optionsStyles.content : optionsStyles.contentContainer}
          testId="pnr_ris_options_container_full_flex_content_container"
        >
          <Image
            resizeMode="contain"
            style={optionsStyles.img}
            source={icon}
            testId="pnr_ris_options_container_full_flex_content_container_image"
          />
          <Text
            style={[optionsStyles.textStyle, fontStyle('semiBold')]}
            testId="pnr_ris_options_container_full_flex_content_container_text"
          >
            {iconText}
          </Text>
        </View>
      </TouchableRipple>
    </View>
  );
};

const PnrRISOptions = (props: Props) => {
  const { trainInfo } = props;
  const openTrainStatus = () => {
    if (trainInfo) {
      trackClickEventProp61(RIS_LANDING_PNR_DETAILS_SUCCESS, PNR_DETAILS_LTS_CLICKED);
      openLiveTrainStatus({ trainDetails: trainInfo });
    }
  };

  const openTrainRoute = () => {
    const { trainInfo } = props;
    const trainNumToSearch = trainInfo.trainNumber;
    trackClickEventProp61(RIS_LANDING_PNR_DETAILS_SUCCESS, PNR_DETAILS_SCHEDULE_CLICKED);
    Actions.railsTrainSchedule({ trainNumToSearch });
  };

  return (
    <View style={optionsStyles.risContainer} testId="pnr_ris_options_container">
      <Options
        icon={ASSETS.ic_train_schedule}
        iconText={'Train Schedule'}
        onClick={openTrainRoute}
      />
      <Options icon={ASSETS.icTrainLTS} iconText={'Live Train Status'} onClick={openTrainStatus} />
    </View>
  );
};

export default PnrRISOptions;
