
import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
    containerBackground: {
        backgroundColor: colors.white,
        paddingHorizontal: 16,
        paddingBottom: 18,
    },
    container: {
        backgroundColor: colors.transparent,
    },
    borderContainer: {
        borderColor: colors.lightSilver,
        borderRadius: 16,
        borderWidth: 1,
        marginHorizontal: 16,
        overflow: 'hidden',
    },
    headerContainer: {
        marginLeft: 16,
        marginVertical: 13.5,
    },
    headerText: {
        color: colors.black,
        marginBottom: 4,
    },
    subHeaderText: {
        color: colors.textGrey,
    },
    flexRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    flexDisplay: {
        flex: 1,
    },
    viewAllContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 10,
    },
    btnImg: {
        width: 16,
        height: 16,
        marginLeft: 4,
    },
    boldImg: {
        width: 12,
        height: 12,
        marginRight: 4,
    },
    azureText: {
        color: colors.azure,
    },
    seperatorLine: {
        backgroundColor: colors.lightGrey,
        height: 1,
    },
    wrapFlex: {
        flexWrap: 'wrap',
    },
    banner: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        backgroundColor: colors.lightGreen18,
        borderBottomRightRadius: 35,
        paddingHorizontal: 14,
        paddingVertical: 2,
        marginBottom: 8,
        borderColor: colors.lightGray,
        borderBottomWidth: 1,
        borderRightWidth: 1,
        alignItems: 'center',
    },
    whiteText: {
        color: colors.white,
    },
    greyText: {
        color: colors.greyText1,
    },
    blackText: {
        color: colors.black,
    },
    purpleGreyText: {
        color: colors.purpleyGrey,
    },
    trainDescContainer: {
        paddingHorizontal: 16,
        marginBottom: 14,
    },
    trainDetailsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: 17,
        marginBottom: 4,
    },
    journeyDetails: {
        color: colors.purpleyGrey,
        marginBottom: 10,
        marginTop: 4,
    },
    cnfOptionsDesc: {
        backgroundColor: colors.lighterBlue,
        paddingHorizontal: 10.5,
        paddingVertical: 4,
    borderRadius: 8,
    },
    cnfDescText: {
        flexWrap: 'wrap',
        color: colors.textGrey,
    },
    availabilityCardList: {
        marginBottom: 12,
        marginLeft: 16,
    },
    availabilityCardContainer: {
        paddingHorizontal: 10,
        paddingVertical: 10,
        width: 130,
        borderWidth: 1,
        borderColor: colors.lightSilver,
        borderRadius: 16,
        marginRight: 8,
    },
    classContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    availabilityText: {
        color: colors.lightGreen16,
        marginBottom: 8,
    },
    lastUpdatedText: {
        flexWrap: 'wrap',
        color: colors.purpleyGrey,
    },
});
