
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import React, { useCallback } from 'react';
import { View, Text, FlatList, Image, TouchableOpacity } from 'react-native';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { getFormattedStnName } from '../../../../NewListing/Components/ConfirmedOptions/ConfirmedOptionsUtils';
import { styles } from './AlternateTriansNew.styles';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  RIS_LANDING_PNR_DETAILS_SUCCESS,
} from '../../../../../RisAnalytics/RisAnalytics';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { openTravellerPage } from 'apps/rails/src/pages/RIS/RisCommonUtils';
import { CROSS_SELL_CLICK } from '../../../Utils/RISOmnitureConstants';
import { CrossSellCoupons, CrossSellInlineHeader } from '../PNRCrossSells/Components';
import { logRisPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS';
import { RIS_EVENT_VALUES } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS/RisPdtConstants';
import PropTypes from 'prop-types';

const TrainCards = ({
  crossSellData,
  alternateAvailabilityList,
  pnrDetailInfo,
  onClosePressed,
  isFromBottomSheet,
}) => {
  const { trainNumber, classAvailabilityList, frmStnName, frmStnCode, toStnName, toStnCode } =
    alternateAvailabilityList;

  const trainData = classAvailabilityList.map(({ tbsAvailability }) => tbsAvailability);
  const { PnrDetails: { Pnr } = {} } = pnrDetailInfo || {};

  const logConfirmedOptionsToPdt = () => {
    try {
      // Sending complete cross sell data to pdt as there is only one confirmed option
      // available at a time in current api response
      const returnTicketPdtData = {
        ...crossSellData,
        isFromBottomSheet,
      };
      logRisPdtEvent({
        eventValue: RIS_EVENT_VALUES.CONFIRMED_OPTIONS,
        pnrCrossSell: returnTicketPdtData,
      });
    } catch (error) {}
  };

  const handleAvailabilityCardClicked = (item) => {
    const { availablityDate, classType, quota } = item;
    trackClickEventProp61(
      RIS_LANDING_PNR_DETAILS_SUCCESS,
      CROSS_SELL_CLICK.CONFIRMED_OPTIONS[isFromBottomSheet ? 'BS_CARD' : 'INLINE_CARD'],
    );
    onClosePressed && onClosePressed();
    const travelDate = availablityDate?.split('-').reverse().join('');
    openTravellerPage(
      frmStnCode,
      toStnCode,
      travelDate,
      trainNumber,
      classType,
      quota,
      frmStnName,
      toStnName,
      Pnr,
    );
    logConfirmedOptionsToPdt();
  };

  const trainItem = useCallback(({ item }) => (
      <TouchableOpacity
        style={styles.availabilityCardContainer}
        onPress={() => handleAvailabilityCardClicked(item)}
        testID="alternate_trains_new_touchable_opacity"
      >
        <View style={styles.classContainer} testID="alternate_trains_new_class_container">
          <Text
            style={[fontStyle('black'), getLineHeight(14), styles.blackText]}
            testID="alternate_trains_new_class_container_text"
          >
            {item.classType}
          </Text>
          <Text
            style={[fontStyle('black'), getLineHeight(14), styles.blackText]}
            testID="alternate_trains_new_class_container_text_price"
          >{`₹${item.totalFare}`}</Text>
      </View>
        <Text
          style={[fontStyle('black'), getLineHeight(14), styles.availabilityText]}
          testID="alternate_trains_new_availability_text"
        >
        {item.prettyPrintingAvailablityStatus || item.availablityStatus}
      </Text>
        <View testID="alternate_trains_new_last_updated_text_container">
          <Text
            style={[fontStyle('regular'), getLineHeight(12), styles.lastUpdatedText]}
            testID="alternate_trains_new_last_updated_text"
          >
          {item.lastUpdatedOn}
        </Text>
      </View>
    </TouchableOpacity>
  ), []);

  return (
    <View style={styles.availabilityCardList}>
      <FlatList
        horizontal
        data={trainData}
        showsHorizontalScrollIndicator={false}
        renderItem={trainItem}
        testID="alternate_trains_new_flat_list"
      />
    </View>
  );
};

export const AlternateTrainsNew = ({
  isFromBottomSheet = false,
  crossSellData,
  pnrDetailInfo = {},
  onClosePressed,
}) => {
  const { data: alternateAvailabilityList, coupon } = crossSellData || {};

  if (!alternateAvailabilityList?.classAvailabilityList?.length) {
    return null;
  }

  const {
    trainName,
    trainNumber,
    classAvailabilityList,
    frmStnName: boardStnName,
    toStnName: dropStnName,
  } = alternateAvailabilityList;

  const { frmStnName, toStnName, frmStnCode, toStnCode } = classAvailabilityList[0];

  const originStationName = getFormattedStnName(frmStnName);
  const destinationStationName = getFormattedStnName(toStnName);

  return (
    <View style={[styles.container, isFromBottomSheet && styles.borderContainer]}>
      {!isFromBottomSheet && <CrossSellInlineHeader crossSellData={crossSellData} />}
      {!isFromBottomSheet && (
        <View style={styles.seperatorLine} testID="alternate_trains_new_seperator_line" />
      )}
      <View style={styles.wrapFlex} testID="alternate_trains_new_wrap_flex">
        <View style={styles.banner} testID="alternate_trains_new_banner">
          <Image
            source={ASSETS.confirmedOptionBolt}
            resizeMode="contain"
            style={styles.boldImg}
            testID="alternate_trains_new_bold_img"
          />
          <Text
            style={[fontStyle('black'), getLineHeight(13), styles.greyText]}
            testID="alternate_trains_new_grey_text"
          >
            {_label('confirmed_options')}
          </Text>
        </View>
      </View>
      <View style={styles.trainDescContainer} testID="alternate_trains_new_train_desc_container">
        <View
          style={styles.trainDetailsContainer}
          testID="alternate_trains_new_train_details_container"
        >
          <Text
            style={[fontStyle('black'), getLineHeight(16), styles.blackText]}
            testID="alternate_trains_new_train_name"
          >
            {trainName}
          </Text>
          <Text
            style={[fontStyle('regular'), getLineHeight(16), styles.purpleGreyText]}
          >{`#${trainNumber}`}</Text>
        </View>
        <Text
          style={[fontStyle('regular'), getLineHeight(14), styles.journeyDetails]}
          testID="alternate_trains_new_journey_details"
        >
          {`${originStationName} (${frmStnCode}) to ${destinationStationName} (${toStnCode})`}
        </Text>
        <View style={styles.cnfOptionsDesc} testID="alternate_trains_new_cnf_options_desc">
          <Text
            style={[fontStyle('regular'), getLineHeight(14), styles.cnfDescText]}
            testID="alternate_trains_new_cnf_desc_text"
          >
            <Text>{_label('book_from')}</Text>
            <Text
              style={fontStyle('black')}
              testID="alternate_trains_new_cnf_desc_text_text"
            >{` ${originStationName}`}</Text>
            <Text>{` ${_label('to', { lowercase: true })}`}</Text>
            <Text
              style={fontStyle('black')}
              testID="alternate_trains_new_cnf_desc_text_text_text"
            >{` ${destinationStationName}.`}</Text>
            <Text>{` ${_label('board_at')}`}</Text>
            <Text
              style={fontStyle('black')}
              testID="alternate_trains_new_cnf_desc_text_text_text_text"
            >{` ${boardStnName}`}</Text>
            <Text>{` ${_label('and')} ${_label('get_down_at', { lowercase: true })} `}</Text>
            <Text
              style={fontStyle('black')}
              testID="alternate_trains_new_cnf_desc_text_text_text_text_text"
            >
              {dropStnName}
            </Text>
          </Text>
        </View>
      </View>
      <TrainCards
        crossSellData={crossSellData}
        alternateAvailabilityList={alternateAvailabilityList}
        pnrDetailInfo={pnrDetailInfo}
        onClosePressed={onClosePressed}
        isFromBottomSheet={isFromBottomSheet}
      />
      <CrossSellCoupons coupon={coupon} />
    </View>
  );
};

TrainCards.propTypes = {
  crossSellData: PropTypes.any,
  alternateAvailabilityList: PropTypes.object,
  pnrDetailInfo: PropTypes.object,
  onClosePressed: PropTypes.func,
  isFromBottomSheet: PropTypes.bool,
};

AlternateTrainsNew.propTypes = {
  isFromBottomSheet: PropTypes.bool,
  crossSellData: PropTypes.any,
  pnrDetailInfo: PropTypes.object,
  onClosePressed: PropTypes.func,
  alternateAvailabilityList: PropTypes.object,
};


export default AlternateTrainsNew;
