import React from 'react';
import { View, Text, Platform, StyleSheet, BackHandler, Image } from 'react-native';
import { Actions } from '../../../../navigation';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import PropTypes from 'prop-types';


const CoachPositionHeader = (props) => {
  const { trainNumber, trainName, navigation } = props;
  return (<View
    style={{
      flexDirection: 'row',
      height: 64,
      marginBottom: 2,
    }}
    elevation={5}
  >
    <TouchableRipple onPress={() => {
      if (navigation && navigation.canGoBack()) {
        Actions.pop();
      } else if (Platform.OS === 'ios') {
        ViewControllerModule.popViewController();
      } else {
        BackHandler.exitApp();
      }
    }}
    >
      <View style={pnrStyles.backIconContainer}>
        <Image style={pnrStyles.backIconStyle} source={backIcon} />
      </View>
    </TouchableRipple>
    <View style={{justifyContent: 'center'}}>
      <Text style={pnrStyles.headerTextStyle}>Coach Positions</Text>
      <Text style={pnrStyles.pnrNumberStyle}>{trainNumber}<Text
        style={pnrStyles.trainNumberStyle}
      > {trainName}
      </Text>
      </Text>
    </View>
  </View>
  );
};

const pnrStyles = StyleSheet.create({
  containerStyle: {
    backgroundColor: colors.grey11,
  },
  headerStyle: {
    flexDirection: 'row',
    height: 64,
    borderBottomWidth: 2,
    borderColor: colors.greyBookedSeat,
  },
  backIconStyle: {
    width: 18,
    height: 16,
    justifyContent: 'center',
  },
  backIconContainer: {
    width: 48,
    height: 48,
    marginTop: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTextStyle: {
    marginLeft: 12,
    marginTop: 13,
    fontFamily: fonts.bold,
    fontSize: 16,
    color: colors.black,
  },
  pnrNumberStyle: {
    marginLeft: 12,
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.black,
  },
  trainNumberStyle: {
    color: colors.lightTextColor,
  },
  warningContainer: {
    marginHorizontal: 16,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: colors.red,
    padding: 5,
  },
  warningText: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  },
  warningHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  importantText: {
    fontFamily: fonts.bold,
    fontSize: 14,
    color: colors.black,
  },
  closeButton: {
    height: 24,
    width: 24,
    marginTop: -5,
  },
});

CoachPositionHeader.propTypes = {
  trainNumber: PropTypes.string,
  trainName: PropTypes.string,
  navigation: PropTypes.object,
};

export default CoachPositionHeader;
