import React from 'react';
import { connect } from 'react-redux';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import { DownArrow, UpArrow } from '../TGSUtils/TGSAssets';
import { Actions } from 'apps/rails/src/navigation';
import PropTypes from 'prop-types';
import TrainCallOut from './TrainCallOut';
import { getTrainName } from '../../../Common/JourneyDetailsCard';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { trainHeader } from 'apps/rails/src/pages/RIS/PnrStatus/Components/TrainDetailsNew';
import {View, Text, Image, StyleSheet, Platform, DeviceEventEmitter, TouchableWithoutFeedback} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import ViewState from '../../../../Utils/ViewState';
import { isUserLoggedIn, performLogin } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { onPnrAlertClicked } from '../RailInfoAction';
import HTMLView from 'react-native-htmlview';
import { _label } from '../../../../vernacular/AppLanguage';
import { LOGIN_EVENT } from '@mmt/legacy-commons/Common/constants/AppConstants';
import { trackTGSInEligibility, RIS_LANDING_PNR_DETAILS_SUCCESS, PNR_DETAILS_SUBSCRIPTION_CLICKED} from 'apps/rails/src/RisAnalytics/RisAnalytics';
import { omnitureMapping } from '../TGSUtils/Constants';
import { PnrRISOptions, PnrTrainDetailsHeader } from './PnrTrainDetailsHeader';
import PNRWhatsappAlert from './PNRWhatsappAlert';
import { trackClickEventProp61 } from '../../../RailsBusHomePage/Analytics';

import greenTick from '@mmt/legacy-assets/src/green_tick.webp';
import redCross from '@mmt/legacy-assets/src/red_tick_cross.webp';
import userIcon from '@mmt/legacy-assets/src/user_icon.webp';
import arrowBlueFilled from '@mmt/legacy-assets/src/ArrowBlueFilled.webp';
import tgsIcon from '@mmt/legacy-assets/src/TGS/TG.webp';

let loginEventListener;
/* eslint-disable */
class PnrTicketStatusNew extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            // eslint-disable-next-line react/prop-types
            enabled: this.props.data ? this.props.data.enabled : false,
            isPassengerListExtended: false,
        };
    }

    UNSAFE_componentWillMount() {
        const { enabled, showTrainCallOut } = this.props;
        this.setState({ enabled });
        if (showTrainCallOut){
            trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, 
                omnitureMapping.TGS_PNR_STATUS_PAGE_NEW_USESR_OFFER_SHOWN_TO_NON_BOOKERS);
        }
    }

    updateUserLoggedInStatus = async () => {
        const loginStatus = await isUserLoggedIn();
        this.setState({ isUserLoggedIn: loginStatus });
    };

    async componentDidMount() {
        this.updateUserLoggedInStatus();
        loginEventListener =  DeviceEventEmitter?.addListener(LOGIN_EVENT, this.updateUserLoggedInStatus);
    }

    componentWillUnmount() {
    loginEventListener?.remove();
    }

    renderProbability = () => {
        if (this.props.PnrConfirmationProbabilityText.length > 0) {
            return (
                <View style={pnrTicketStyle.infoContainer} testId="pnr_ticket_status_new_info_container">
                    {this.props.PnrConfirmationProbabilityText.map((el) => 
                    <HTMLView stylesheet={infostyles} value={el} testId="pnr_ticket_status_new_info_container_html_view" />)}
                </View>
            );
        }
        return null;
    };

    renderProbabilityPersuasion = () => {
        return (
            <>
                <View style={[pnrTicketStyle.infoContainer, pnrTicketStyle.nonloginInfoContainer]} testId="pnr_ticket_status_new_info_container">
                    <View style={{ width: '70%' }} testId="pnr_ticket_status_new_info_container_view">
                        <Text style={pnrTicketStyle.nonloginInfo} testId="pnr_ticket_status_new_info_container_text">Your ticket has 
                            <Text style={pnrTicketStyle.blurText} testId="pnr_ticket_status_new_info_container_text_xx"> xx </Text>
                            % chance of getting confirmed</Text>
                    </View>
                    <View  testId="pnr_ticket_status_new_info_container_login_button">
                        <TouchableRipple onPress={() => performLogin()}>
                            <Text style={pnrTicketStyle.loginBtn} testId="pnr_ticket_status_new_info_container_login_button_text">LOGIN</Text>
                        </TouchableRipple>
                    </View>
                </View>
                <View style={pnrTicketStyle.persuasionContainer} testId="pnr_ticket_status_new_persuasion_container">
                    <View testId="pnr_ticket_status_new_persuasion_container_user_icon">
                        <Image source={userIcon} style={pnrTicketStyle.userIcon} testId="pnr_ticket_status_new_persuasion_container_user_icon_image" />
                    </View>
                    <View testId="pnr_ticket_status_new_persuasion_container_text">
                        <Text style={pnrTicketStyle.persuasionText} testId="pnr_ticket_status_new_persuasion_container_text_text">
                            Mr. Raghav got a confirmed ticket despite 85% confirmation chance. 
                            Login to access Makemytrip’s accurate prediction.
                        </Text>
                    </View>
                </View>
            </>
        );
    };

    renderStatus = (tgPremiumAmount, onSubmit) => {
        return (
            <View testId="pnr_ticket_status_new_status_container">
                  <View style={pnrTicketStyle.ticketWaitlistContainer} testId="pnr_ticket_status_new_status_container_waitlist_container">
                <Text style={[pnrTicketStyle.ticketWaitlist, fontStyle('semiBold')]} testId="pnr_ticket_status_new_status_container_waitlist_container_text">
                    Your Ticket is Waitlisted
                </Text>
                </View>
                <TouchableRipple style={pnrTicketStyle.tgsComponentContainer} onPress={onSubmit} testId="pnr_ticket_status_new_status_container_tgs_component_container">
                    <View style={Platform.OS === 'ios' ? pnrTicketStyle.tgsComponent : pnrTicketStyle.tgsComponentAndroid} testId="pnr_ticket_status_new_status_container_tgs_component_container_view">
                        <View testId="pnr_ticket_status_new_status_container_tgs_component_container_view_image">
                            <Image
                                source={tgsIcon}
                                resizeMode="contain"
                                style={pnrTicketStyle.waitListIcon} testId="pnr_ticket_status_new_status_container_tgs_component_container_view_image_image" />
                        </View>
                        <View style={{ width: '50%' }} testId="pnr_ticket_status_new_status_container_tgs_component_container_view_text">
                            <Text testId="pnr_ticket_status_new_status_container_tgs_component_container_view_text_text">
                                Get <Text style={pnrTicketStyle.tgsTextStyle}>CONFIRMED</Text> 
                                Ticket Ya <Text style={pnrTicketStyle.tgsTextStyle}>3 GUNA Refund!</Text>{'\n'}
                                <Text style={{ color: colors.lightBlue }} testId="pnr_ticket_status_new_status_container_tgs_component_container_view_text_text_text">Only at 
                                    <Text style={[{ textDecorationLine: 'line-through' }, fontStyle('regular')]}>
                                        ₹{Math.floor(tgPremiumAmount * 1.5)}</Text>
                                        <Text style={[{ color: colors.darkBlue }, fontStyle('bold')]} testId="pnr_ticket_status_new_status_container_tgs_component_container_view_text_text_text_text"> ₹{tgPremiumAmount}</Text>
                                        </Text>
                            </Text>
                        </View>
                        <View testId="pnr_ticket_status_new_status_container_tgs_component_container_view_arrow">
                            <Image source={arrowBlueFilled} resizeMode="contain" style={pnrTicketStyle.arrowBlueFilled} testId="pnr_ticket_status_new_status_container_tgs_component_container_view_arrow_image" />
                        </View>
                    </View>
                </TouchableRipple>
            </View >
        );
    };

    checkRenderingOfProbabilityInfo = (PnrConfirmationProbabilityText, enableLoginPersuasion) => {
        if (PnrConfirmationProbabilityText.length > 0 && enableLoginPersuasion) {
            return this.state.isUserLoggedIn ? this.renderProbability() : this.renderProbabilityPersuasion();
        }
        return null;
    };

    renderPassengersSectionTwo = () => {
        const { isPassengerListExtended } = this.state;
        return (
            <View style={pnrTicketStyle.tncView} testId="pnr_ticket_status_new_passengers_section_two_container">
                <View style={pnrTicketStyle.pnrTicketStyleView} testId="pnr_ticket_status_new_passengers_section_two_container">
                    <TouchableWithoutFeedback onPress={() => { 
                        this.setState({ isPassengerListExtended: !isPassengerListExtended }); }} 
                        style={pnrTicketStyle.tncHeaderContainer}>
                        <View style={pnrTicketStyle.tncHeaderContainer} testId="pnr_ticket_status_new_passengers_section_two_container_header_container">
                            {isPassengerListExtended ? <Text style={pnrTicketStyle.tnc} testId="pnr_ticket_status_new_passengers_section_two_container_header_container_text">
                                {_label('hide', { defaultLang: true })}</Text> : 
                                <Text style={pnrTicketStyle.tnc} testId="pnr_ticket_status_new_passengers_section_two_container_header_container_text_text">{_label('see_all_passengers', 
                                { defaultLang: true })}</Text>}
                            {isPassengerListExtended ? <UpArrow style={pnrTicketStyle.downArrow} />
                             : <DownArrow style={pnrTicketStyle.downArrow} />}
                        </View>
                    </TouchableWithoutFeedback>
                </View>
            </View>
        );
    };

    renderPassengers = (passengers, lastElement) => (
        <>
            <View style={pnrTicketStyle.passengerRowStyle} testId="pnr_ticket_status_new_passengers_section_two_container_passenger_row_container">
        <View testId="pnr_ticket_status_new_passengers_section_two_container_passenger_row_container_text_container">
          <Text style={[pnrTicketStyle.passengerNameStyle, fontStyle('medium')]} testId="pnr_ticket_status_new_passengers_section_two_container_passenger_row_container_text">
            {passengers.passengerName}
          </Text>
        </View>
        <View>
          <Text
            style={[
              pnrTicketStyle.bookingStatusStyle,
              fontStyle('bold'),
              { color: passengers.colorCode },
            ]}
            testId="pnr_ticket_status_new_passengers_section_two_container_passenger_row_container_text_text"
          >
            {passengers.currentStatus}
            {passengers.currentBerthCode && (
              <Text style={[pnrTicketStyle.currentStatusStyle, fontStyle('medium')]} testId="pnr_ticket_status_new_passengers_section_two_container_passenger_row_container_text_text_text">
                {` (${_label(passengers.currentBerthCode)})`}
              </Text>
            )}
          </Text>
        </View>
            </View>
            {!lastElement && <View style={pnrTicketStyle.horizontalBar} />}
            {this.state.isPassengerListExtended && lastElement && <View style={pnrTicketStyle.horizontalBar} />}
        </>);

    redirectToListing = () => {
        const { trainInfo } = this.props;
        let date = new Date(trainInfo?.journeyDate);
        const originStation = {
            code: trainInfo?.departStationCode,
        };
        const destinationStation = {
            code: trainInfo?.arrivalStationCode,
        };
        Actions.railsListing({
            originStation: destinationStation,
            destinationStation: originStation,
            departureDate: date,
            source: 'PNR',
        });
    };

    render() {
        const {
            enableLoginPersuasion, pnrNumber, pnrAlertSet, isPnrAlertEligible, pnrAlertState, message, PnrConfirmationProbabilityText = [], passengerList: { passenger: list = [] } = {},
            isTgEligible, tgPremiumAmount, onTGSSubmit, trainInfo, showTrainCallOut } = this.props;

        const passengersList = [{ itemZero: true }, ...list];
        const { isPassengerListExtended, isUserLoggedIn } = this.state;
        const passengersListSectionOne = passengersList?.filter((_, idx) => idx < 4);
        const passengersListSectionTwo = passengersList?.filter((_, idx) => idx > 3);
        return (
            <View style={pnrTicketStyle.containerStyle} testId="pnr_ticket_status_new_container_container">
                {isTgEligible ?
                    <View style={pnrTicketStyle.container} testId="pnr_ticket_status_new_container_container_view">
                        {passengersListSectionOne?.map((passengers, idx) => (
                            (passengers.itemZero ?
                                <View style={trainHeader.container} testId="pnr_ticket_status_new_container_container_view_train_header_container">
                                    <View style={pnrTicketStyle.trainNameContainer} testId="pnr_ticket_status_new_container_container_view_train_header_container_train_name_container">
                                        {trainInfo.trainName && <Text 
                                            style={trainHeader.nameAndDate} testId="pnr_ticket_status_new_container_container_view_train_header_container_train_name_container_text">
                                            {getTrainName(trainInfo?.trainName.toLowerCase())}
                                            </Text>}
                                        <Text style={trainHeader.nameAndDate} testId="pnr_ticket_status_new_container_container_view_train_header_container_train_name_container_text_text">{trainInfo.deptDate}</Text>
                                    </View>
                                    <View style={pnrTicketStyle.trainHeaderContainer} testId="pnr_ticket_status_new_container_container_view_train_header_container_train_header_container">
                                        <Text style={trainHeader.selectedClass} testId="pnr_ticket_status_new_container_container_view_train_header_container_train_header_container_text">{trainInfo.travelClass} {trainInfo.travelQuota ? `| ${trainInfo.travelQuota}` : ''}</Text>
                                        {trainInfo.isChartPrepared &&
                                            <Text style={trainHeader.chartPreparedStyle} testId="pnr_ticket_status_new_container_container_view_train_header_container_train_header_container_text_text">Chart Prepared</Text>
                                        }
                                        {!trainInfo.isChartPrepared &&
                                            <Text style={trainHeader.chartNotPreparedStyle} testId="pnr_ticket_status_new_container_container_view_train_header_container_train_header_container_text_text_text">Chart Not Prepared</Text>
                                        }
                                    </View>
                                    {this.renderStatus(tgPremiumAmount, onTGSSubmit)}
                                </View>
                                :
                                this.renderPassengers(passengers, idx === passengersListSectionOne?.length - 1)
                            )))}
                        {isPassengerListExtended &&
                            passengersListSectionTwo?.map((passengers, idx) => 
                                (this.renderPassengers(passengers, idx === passengersListSectionTwo.length - 1)))
                        }
                        {passengersListSectionTwo?.length !== 0 ?
                            this.renderPassengersSectionTwo() : <View style={{ marginVertical: 10 }} />}
                    </View> :
                    <View style={pnrTicketStyle.container} testId="pnr_ticket_status_new_container_container_view_two">
                        {passengersListSectionOne.map((passengers, idx) => (
                            (passengers.itemZero ?
                                <PnrTrainDetailsHeader trainInfo={trainInfo} />
                                :
                                this.renderPassengers(passengers, idx === passengersListSectionOne?.length - 1)
                            )))}
                        {isPassengerListExtended &&
                            passengersListSectionTwo?.map((passengers, idx) => 
                                (this.renderPassengers(passengers, idx === passengersListSectionTwo.length - 1)))
                        }
            {passengersListSectionTwo?.length !== 0 && this.renderPassengersSectionTwo()}
                        {showTrainCallOut && <TrainCallOut redirectToListing={this.redirectToListing} />}
                        <PnrRISOptions trainInfo={trainInfo} />
                    </View>
                }
                {this.checkRenderingOfProbabilityInfo(PnrConfirmationProbabilityText, enableLoginPersuasion)}
                {isPnrAlertEligible && ((Platform.OS !== 'web') && isUserLoggedIn) &&
                    <View testId="pnr_ticket_status_new_container_container_view_two_alert_container">
                        <View style={pnrTicketStyle.alertContainer} testId="pnr_ticket_status_new_container_container_view_two_alert_container_view">
              <PNRWhatsappAlert
                onValueChange={() => this._pnrAlertSwitchClicked(pnrNumber)}
                isEnabled={this.state.enabled}
              />
                        </View>
                        <View testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two">
                            {pnrAlertState === ViewState.LOADING &&
                                <View style={pnrTicketStyle.activityIndicatorStyle} testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two_activity_indicator_style">
                                    <Spinner size={18} color="#008cff" />
                                </View>}

                            {pnrAlertState === ViewState.SHOW_DETAIL &&
                                <View style={pnrTicketStyle.pnrAlertSuccessStyle} testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two_pnr_alert_success_style">
                                    <Image
                                        style={{
                                            width: 16,
                                            height: 16,
                                            marginRight: 7,
                                        }}
                                        source={greenTick}
                                        testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two_image"
                                    />
                                    <Text style={pnrTicketStyle.alertSuccessText} testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two_text">{message}</Text>
                                </View>}
                            {!pnrAlertSet && pnrAlertState === ViewState.ERROR &&
                                <View style={pnrTicketStyle.pnrAlertSuccessStyle} testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two_pnr_alert_success_style_two">
                                    <Image
                                        style={{
                                            width: 16,
                                            height: 16,
                                            marginRight: 7,
                                        }}
                                        source={redCross}
                                        testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two_image_two"
                                    />
                                    <Text style={pnrTicketStyle.alertFailedText} testId="pnr_ticket_status_new_container_container_view_two_alert_container_view_two_text_two">
                                        Sorry we are unable to set the alert for
                                        your PNR
                                        currently. Please try again after some.
                                    </Text>
                                </View>}
                        </View>
                    </View>}
            </View>
        );
    }


    greenGradient = ['#219393', '#43e1a8'];
    redGradient = ['#9f0404', '#9f0404'];
    yellowGradient = ['#f89d19', '#f3c852'];
    defaultGradient = ['#bdbdbd', '#bdbdbd'];


    _gradientColorFor = (state) => {
        const status = state.toLowerCase();

        if (status.includes('confirm') === true) {
            return this.greenGradient;
        }
        if (status.includes('no chance') === true) {
            return this.redGradient;
        }
        if (status.includes('probable') === true) {
            return this.yellowGradient;
        }
        return this.defaultGradient;
    };

    _pnrAlertSwitchClicked = (pnr) => {
        this.props.onPnrAlertClicked(pnr, this.state.enabled);
        const enabled = !this.state.enabled;
        trackClickEventProp61(RIS_LANDING_PNR_DETAILS_SUCCESS, PNR_DETAILS_SUBSCRIPTION_CLICKED);
        this.setState({ enabled });
    };

    _getStatusText = (ticketStatus, predictionMsg) => {
        if (ticketStatus.includes('confirm') === true) {
            return 'CONFIRMED';
        }
        return predictionMsg;
    };
}

PnrTicketStatusNew.propTypes = {
    pnrAlertSet: PropTypes.bool,
    isLoading: PropTypes.bool,
    isPnrAlertEligible: PropTypes.bool,
    onPnrAlertClicked: PropTypes.func.isRequired,
    isTgEligible: PropTypes.bool.isRequired,
    enabled: PropTypes.bool,
    showTrainCallOut: PropTypes.bool,
    PnrConfirmationProbabilityText: PropTypes.array,
    trainInfo: PropTypes.object,
    passengerList: PropTypes.object,
    enableLoginPersuasion: PropTypes.bool,
    pnrNumber: PropTypes.string,
    pnrAlertState: PropTypes.string,
    message: PropTypes.string,
    tgPremiumAmount: PropTypes.number,
    onTGSSubmit: PropTypes.func,

};

PnrTicketStatusNew.defaultProps = {
    pnrAlertSet: false,
    isLoading: true,
};


const infostyles = StyleSheet.create({
    p: {
        color: colors.deepBlue,
        fontSize: 12,
        lineHeight: 12,
        letterSpacing: 0.18,
        fontFamily: fonts.regular,
        width: '100%',
        paddingBottom: 5,
    },
    b: {
        fontFamily: fonts.bold,
    },
});

const pnrTicketStyle = StyleSheet.create({
    container: {
        borderColor: colors.lightSilver,
        borderWidth: 1,
        marginHorizontal: 16,
        borderRadius: 16,
    marginTop: 1,
        marginBottom: 10,
    },
    arrowBlueFilled: { width: 20, height: 25 },
    waitListIcon: {
        width: 80,
        height: 60,
    },
    trainNameContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    trainHeaderContainer: {
        marginTop: 4,
        marginBottom: 3,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    tnc: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '500',
        fontSize: 14,
        lineHeight: 16,
        marginRight: 5,
        color: colors.azure,
    },
    downArrow: { height: 6, width: 13, marginLeft: 4 },
    tncView: {
        marginLeft: 24,
        marginBottom: 10,
        marginRight: 'auto',
        marginTop: 10,
        width: '88%',
    },
    tncHeaderContainer: {
        flexDirection: 'row',
        marginBottom: 10,
        alignItems: 'center',
    },
    containerStyle: {
        marginTop: 0,
        backgroundColor: colors.white,
        borderColor: colors.lightGrey,
    },
    horizontalBar: {
        width: 'auto',
        height: 1,
        backgroundColor: colors.grey11,
        marginHorizontal: 16,
    },
    passengerNameStyle: {
        width: 93,
        color: colors.black,
        letterSpacing: 0,
        lineHeight: 20,
    fontSize: 14,
    },

    currentStatusStyle: {
        fontFamily: fonts.black,
        fontSize: 12,
        color: colors.black,

    },
    bookingStatusStyle: {
        fontFamily: fonts.regular,
    fontSize: 14,
        color: colors.black,
        marginLeft: 20,
        marginRight: 16,
    },
    passengerRowStyle: {
    marginLeft: 12,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    },
    alertContainer: {
    marginTop: 10,
        marginLeft: 16,
        marginRight: 16,
        marginBottom: 15,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    activityIndicatorStyle: {
        marginTop: 10,
        marginLeft: 16,
        marginBottom: 10,
        height: 16,
        width: 16,
    },

    pnrAlertSuccessStyle: {
        flexDirection: 'row',
        marginLeft: 16,
        marginRight: 16,
        marginBottom: 20,
        paddingLeft: 5,
    },

    alertSuccessText: {
        color: colors.lightSeaGreenTextColor,
        fontSize: 12,
        fontFamily: fonts.regular,
    },

    alertFailedText: {
        color: colors.red,
        fontSize: 12,
        fontFamily: fonts.regular,
        marginLeft: 5,
        marginRight: 16,
    },
    infoContainer: {
        marginLeft: 16,
        marginRight: 16,
        marginBottom: 15,
        paddingLeft: 12,
        paddingRight: 12,
        paddingTop: 5,
        flexDirection: 'column',
        justifyContent: 'space-between',
        backgroundColor: colors.lightGreen8,
        borderRadius: 4.5,
        borderColor: '#3326b5a9',
    },
    nonloginInfoContainer: {
        flexDirection: 'row',
    },
    nonloginInfo: {
        color: colors.deepBlue,
        fontSize: 14,
        lineHeight: 18,
        letterSpacing: 0.22,
        fontFamily: fonts.bold,
        width: '100%',
        paddingBottom: 10,
    },
    blurText: {
        color: colors.transparent,
        textShadowColor: 'rgba(0,0,0,0.25)',
        textShadowOffset: {
            width: 0,
            height: 0,
        },
        textShadowRadius: 5,
        fontSize: 14,
    },
    loginBtn: {
        color: colors.primary,
        fontSize: 16,
        lineHeight: 18,
        paddingTop: 10,
    },
    persuasionContainer: {
        display: 'flex',
        flexDirection: 'row',
        marginTop: 15,
        marginLeft: 16,
        marginRight: 16,
        marginBottom: 15,
    },
    persuasionText: {
        fontSize: 12,
        lineHeight: 18,
        letterSpacing: 0.18,
        color: '#3c3761',
        marginLeft: 10,
    },
    userIcon: {
        width: 24,
        height: 24,
    },
    pnrTicketStyleView: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    ticketWaitlist: {
        textAlign: 'center',
        color: colors.white,
    },
    ticketWaitlistContainer:{
        borderRadius: 4,
        marginTop: 4,
        padding: 3,
        backgroundColor: '#F4A200',
    },
    tgsComponent: {
        borderRadius: 12,
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 5,
        flexDirection: 'row',
        borderWidth: 0.4,
        borderColor: colors.lightSilver,
        shadowColor: colors.black,
        shadowOpacity: 0.26,
        shadowOffset: { width: 0, height: 2 },
        shadowRadius: 10,
        elevation: 3,
        backgroundColor: colors.white,
        width:'100%',
        paddingRight:10,
    },
    tgsComponentAndroid: {
        borderRadius: 12,
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 5,
        flexDirection: 'row',
        borderWidth: 0.4,
        borderColor: colors.lightSilver,
        shadowColor: colors.black,
        shadowOpacity: 0.26,
        shadowOffset: { width: 0, height: 2 },
        shadowRadius: 10,
        elevation: 3,
        backgroundColor: colors.white,
        width:'100%',
        paddingRight:10,
        marginTop:8,
    },
    tgsTextStyle: {
        fontFamily: fonts.bold,
        color: colors.black,
    },
    tgsComponentContainer:{
        marginTop: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width:'100%',
    },
});

const mapStatesToProps = (state, ownProps) => {
    const {
        pnrAlertState,
        trainInfo,
        passengerList = [],
        coachPosition,
        pnrAlertSet,
        message,
        enabled,
    } = state.railInfoReducer;
    return {
        ...ownProps,
        pnrAlertState,
        trainInfo,
        passengerList,
        coachPosition,
        pnrAlertSet,
        message,
        enabled,

    };
};

const mapDispatchToProps = dispatch => ({
    onPnrAlertClicked: (pnr, enabled) => dispatch(onPnrAlertClicked(pnr, enabled)),
});

export default connect(mapStatesToProps, mapDispatchToProps)(PnrTicketStatusNew);
