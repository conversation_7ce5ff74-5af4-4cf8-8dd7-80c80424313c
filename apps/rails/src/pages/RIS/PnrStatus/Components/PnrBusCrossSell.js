import React from 'react';
import { connect } from 'react-redux';
import {
  View,
  Text,
  Image,
  StyleSheet,
  PixelRatio,
  FlatList,
  SafeAreaView,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { isEmpty } from 'lodash';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {
  getPnrStatusDateFormat,
  convertEpochToTimeFormat,
  isDateChanged,
} from '../../../../Utils/RisUtils';
import { deeepLinkHandling } from '../RailInfoAction';
import TouchableOpacity from '@mmt/legacy-commons/Common/Components/TouchableOpacity';
import { getQueryParamsFromUrl } from '@mmt/legacy-commons/Helpers/misc';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from '../../../../RisAnalytics/RisAnalytics';
import { getQueryParamsFromDeepLink } from '../../../../Utils/CommonUtils';
import BusSharedModuleHolder from '@mmt/bus-shared/src';

import arrowImage from '@mmt/legacy-assets/src/ic_arrow_long_blue.webp';
import { navigation } from '@mmt/navigation';
import PropTypes from 'prop-types';

const PnrBusCrossSell = ({ alternateBusesDetails, trainList }) => {
  if (
    alternateBusesDetails &&
    alternateBusesDetails !== null &&
    alternateBusesDetails.buses &&
    alternateBusesDetails.buses.length > 0
  ) {
    const {
      minPrice,
      noOfBuses,
      fromCity,
      toCity,
      fromCityCode,
      toCityCode,
      dateOfJourney,
      url,
      buses,
      handlecardClick,
    } = alternateBusesDetails;
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <View>
          {trainList && trainList.length > 0 && (
            <View style={styles.horizontalBar} />
          )}
          <Text style={styles.subTitle}>
            {noOfBuses} Buses from {`\u20B9${minPrice}`}
          </Text>
          <View style={styles.destinationContainer}>
            <Text style={styles.destinationText}>{fromCity}</Text>
            <Image style={styles.arrowImage} source={arrowImage} />
            <Text style={styles.destinationText}>{toCity}</Text>
            <View style={styles.verticalBar} />
            <Text style={styles.destinationText}>
              {getPnrStatusDateFormat(dateOfJourney)}
            </Text>
          </View>
          <View style={{ flexDirection: 'row' }}>
            <FlatList
              horizontal
              data={[
                ...buses,
                {
                  isLastItem: true,
                  listingUrl: url,
                  id: '123431234',
                  onClick: handlecardClick,
                  listSize: buses.length,
                  fromCity,
                  toCity,
                  dateOfJourney,
                  fromCityCode,
                  toCityCode,
                },
              ]}
              showsHorizontalScrollIndicator={false}
              renderItem={renderItem}
              keyExtractor={(item) => `list-item-${item.id}`}
            />
          </View>
        </View>
      </SafeAreaView>
    );
  }
  return null;
};

const renderItem = ({ item }) => {
  if (!item.isLastItem) {
    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          const busSharedResourceProvider = BusSharedModuleHolder.get();
          if (!busSharedResourceProvider) {
            throw new Error('Bus-Shared module not bootstrapped');
          }
          let seatmapParameter = {};
          const navigation = busSharedResourceProvider.getBusSharedNavigation();
          const { trackRisBusCrossSell } = busSharedResourceProvider.getBusSharedTracker();

          if (Platform.OS !== 'web') {
            seatmapParameter = getQueryParamsFromDeepLink(item.redirection);
            const { tripKey, cmp, source = 'RISxSell' } = seatmapParameter;
            navigation.openSeatBooking({
              tripKey,
              cmp,
              source,
            });
          } else if (Platform.OS === 'web' && !isEmpty(window)) {
            seatmapParameter = getQueryParamsFromUrl(item.redirection);
            window.location.href = item.redirection;
          }
          trackRisBusCrossSell(
            RIS_LANDING_PNR_DETAILS_SUCCESS,
            'Mob_Bus_RIS_PNR_Bus_Click',
          );
        }}
      >
        <BusCard bus={item} />
      </TouchableOpacity>
    );
  } else if (item.listSize >= 5) {
    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          // const _listingURl = `${item.listingUrl}&source=RISxSell`;
          // Linking.openURL(_listingURl);
          const listingUrlObj = getQueryParamsFromUrl(item.listingUrl);
          const { from, from_code, to,to_code,departure,cmp } = listingUrlObj || {};
          navigation.openBusListing({
            from,
            to,
            departureDate:departure,
            from_code,
            to_code,
            source:'RISxSell',
            cmp,
          });
        }}
      >
        <ViewAllCard />
      </TouchableOpacity>
    );
  }
  return null;
};

const BusCard = ({
  bus: {
    operatorName,
    busType,
    minFare,
    availableSeats,
    duration,
    arrivalTime,
    departureTime,
  },
}) => (
  <Card style={styles.cardStyle}>
    <Text style={styles.cardTitle} ellipsizeMode="tail" numberOfLines={1}>
      {operatorName}
    </Text>
    <Text
      style={styles.busCardRegularText}
      ellipsizeMode="tail"
      numberOfLines={1}
    >
      {busType}
    </Text>
    <View style={styles.timeSection}>
      <Text style={styles.busCardRegularText}>
        {convertEpochToTimeFormat(departureTime)}{' '}
      </Text>
      <View style={styles.durationText}>
        <Line />
        <Text style={[styles.busCardRegularText, { color: colors.grey }]}>
          {' '}
          {duration}{' '}
        </Text>
        <Line />
      </View>
      <Text style={styles.busCardRegularText}>
        {' '}
        {convertEpochToTimeFormat(arrivalTime)}
      </Text>
      {isDateChanged(departureTime, duration) && (
        <Text style={styles.dayChanged}> +1 day</Text>
      )}
    </View>
    <View style={styles.bottomContainer}>
      <Text style={styles.availableText}>
        AVAILABLE{' '}
        <Text style={{ fontFamily: fonts.black }}>{availableSeats}</Text>
      </Text>
      <Text style={styles.farePrice}> {`\u20B9${minFare}`}</Text>
    </View>
  </Card>
);

const ViewAllCard = () => (
  <Card style={styles.cardStyle}>
    <Text style={styles.seeMoreText}>Want to see more?</Text>
    <View style={{ alignSelf: 'flex-end' }}>
      <LinearGradient
        colors={['#53B2FE', '#065AF3']}
        start={{ x: 0.0, y: 1.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={{
          height: 26,
          borderRadius: 34,
          width: 104,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Text style={styles.buttonText}>VIEW All</Text>
      </LinearGradient>
    </View>
  </Card>
);

const Line = () => (
  <View
    style={{
      width: 8,
    }}
  >
    <View
      style={{
        borderColor: colors.lightTextColor,
        height: 1,
        borderWidth: 1 / PixelRatio.getPixelSizeForLayoutSize(1),
      }}
    />
  </View>
);

const styles = StyleSheet.create({
  cardStyle: {
    width: 184,
    height: 99,
    marginTop: 15,
    marginBottom: 16,
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 3,
    backgroundColor: colors.white,
    marginLeft: 0,
  },
  subTitle: {
    fontSize: 14,
    lineHeight: 18,
    fontFamily: fonts.bold,
    color: colors.azureSemi,
    marginBottom: 2,
  },
  destinationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  destinationText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.black,
    lineHeight: 16,
  },
  arrowImage: {
    width: 26,
    height: 7,
    marginHorizontal: 9,
  },
  seeMoreText: {
    fontSize: 10,
    fontFamily: fonts.bold,
    marginTop: 36,
    marginBottom: 30,
    marginLeft: 8,
    lineHeight: 14,
    color: colors.black,
  },
  farePrice: {
    fontFamily: fonts.black,
    fontSize: 16,
    lineHeight: 22,
    color: colors.black,
  },
  verticalBar: {
    backgroundColor: colors.black,
    height: 16,
    width: 1,
    alignSelf: 'center',
    marginHorizontal: 5,
  },
  horizontalBar: {
    width: 'auto',
    height: 1,
    backgroundColor: colors.greyBookedSeat,
    marginVertical: 15,
  },
  cardTitle: {
    fontFamily: fonts.black,
    fontSize: 12,
    lineHeight: 15,
    color: colors.black,
    maxWidth: 160,
  },
  busCardRegularText: {
    fontFamily: fonts.regular,
    fontSize: 12,
    lineHeight: 15,
    color: colors.black,
    maxWidth: 160,
  },
  timeSection: {
    flexDirection: 'row',
    marginTop: 8,
    alignItems: 'center',
  },
  durationText: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  availableText: {
    fontSize: 10,
    lineHeight: 15.8,
    color: colors.lightGreen4,
  },
  buttonText: {
    fontFamily: fonts.black,
    color: colors.white,
    fontSize: 12,
    lineHeight: 16,
  },
  dayChanged: {
    fontSize: 10,
    alignSelf: 'flex-start',
    color: colors.lightTextColor,
  },
});

const mapStatesToProps = (state) => {
  const { alternateBusesDetails, trainList } = state.railInfoReducer;

  return {
    alternateBusesDetails,
    trainList,
  };
};
const mapDispatchToProps = (dispatch) => ({
  handlecardClick: (deeplink) => dispatch(deeepLinkHandling(deeplink)),
});

PnrBusCrossSell.propTypes = {
  alternateBusesDetails: PropTypes.object,
  trainList: PropTypes.array,
};

BusCard.propTypes = {
  bus: PropTypes.object,
};

export default connect(mapStatesToProps, mapDispatchToProps)(PnrBusCrossSell);
