/* eslint-disable */
import React from 'react';
import { useFocusEffect } from '@react-navigation/native';
import PropTypes from 'prop-types';

function FocusEffect({ children, onFocusOut = () => {
  // do nothing
}, onFocusIn = () => {
  // do nothing
} }) {
  useFocusEffect(
    React.useCallback(() => {
      onFocusIn();
      return () => {
        onFocusOut();
      };
    }, []),
  );

  return <>{children}</>;
}

FocusEffect.propTypes = {
  children: PropTypes.node,
  onFocusOut: PropTypes.func,
  onFocusIn: PropTypes.func,
};

export default FocusEffect;
