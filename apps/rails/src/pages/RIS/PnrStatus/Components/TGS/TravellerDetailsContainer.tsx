/* eslint-disable */
import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
} from 'react-native';
import {
    commonStyles, NAME, PHONE_NUMBER, TRAVELLER_DETAILS,
    EMAIL_ID, FULL_NAME_PLACEHOLDER, PHONE_NUMBER_PLACEHOLDER,
    EMAIL_PLACEHOLDER, travellerFieldEmptyError,
    TGS_Traveller_Container, validateName,
} from '../../TGSUtils/Constants';
import {travellerDetailsStyles} from './TGSContainer.styles';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { validateMobile, validateEmail } from '@mmt/legacy-commons/Common/Components/ContactDetails/contactDetails.utils';
import { TravellerDetailsInterface, TravellerDetailsProps } from '../../TGSCommonInterfaces';

const TravellerDetailsContainer = ({ captureRef, travellerFields, setTravellerFields, 
  error, setError, tgsEmailInputMandatory }: TravellerDetailsProps) => {
    const [focusValues, setFocusValues] = useState({ name: false, mobile: false, email: false });
    const [displayError, setDisplayError] = useState(false);

    useEffect(() => {
        travellersError();
    }, [travellerFields]);

    const setFocusVal = (param: string, val: boolean) => 
      setFocusValues((prev: TravellerDetailsInterface) => ({ ...prev, [param]: val }));

    const travellersError = () => {
        const { valid: isValidName, error: nameError } = validateName(travellerFields.name);
        const { valid: isValidMobileNumber, error: mobileError } = validateMobile(travellerFields.mobile);
        const { valid: isValidEmail, error: emailError } = validateEmail(travellerFields.email);

        if (travellerFields.mobile === '' || (tgsEmailInputMandatory && travellerFields.email === '') || travellerFields.name === '') {
            setError(travellerFieldEmptyError);
        }
        else if (!isValidName) {
            setError(nameError);
        }
        else if (!isValidMobileNumber) {
            setError(mobileError);
        }
        else if ((tgsEmailInputMandatory || travellerFields.email.length !== 0) && !isValidEmail) {
            setError(emailError);
        }
        else {
            setError('');
        }
    };

    const onChangeHandler = (name: string, text: string) => {
        setTravellerFields((prev: TravellerDetailsInterface) => ({ ...prev, [name]: text }));
        setDisplayError(true);
    };

    return (
      <View
        ref={(ref: any) => captureRef(TGS_Traveller_Container, ref)}
        style={commonStyles.genericContainer}
      >
        <View style={commonStyles.firstRow}>
          <Text style={commonStyles.heading}>{TRAVELLER_DETAILS}</Text>
        </View>
        <View style={travellerDetailsStyles.row}>
          <Text style={travellerDetailsStyles.input}>{NAME}</Text>
          <Text style={[travellerDetailsStyles.input, { marginLeft: 'auto' }]}>{PHONE_NUMBER}</Text>
        </View>
        <View style={[travellerDetailsStyles.row, { marginBottom: 12,flex:1 }]}>
            <TextInput
              placeholder={FULL_NAME_PLACEHOLDER}
              onChangeText={(text: string) => onChangeHandler('name', text)}
              value={travellerFields.name}
              onFocus={() => setFocusVal('name', true)}
              onBlur={() => setFocusVal('name', false)}
              style={[
                travellerDetailsStyles.inputText,
                { borderColor: focusValues.name ? 'rgb(0, 140, 255)' : colors.lightGrey },
              ]}
            />
          <TextInput
            placeholder={PHONE_NUMBER_PLACEHOLDER}
            onChangeText={(text: string) => onChangeHandler('mobile', text)}
            keyboardType="phone-pad"
            maxLength={10}
            onFocus={() => setFocusVal('mobile', true)}
            onBlur={() => setFocusVal('mobile', false)}
            value={travellerFields.mobile}
            style={[
              travellerDetailsStyles.inputText,
              {
                marginLeft: 'auto',
                borderColor: focusValues.mobile ? 'rgb(0, 140, 255)' : colors.lightGrey,
              },
            ]}
          />
        </View>
        {tgsEmailInputMandatory && (
          <>
            <Text style={{ marginBottom: 3 }}>{EMAIL_ID}</Text>
            <View  style={{ marginBottom: 8,flex:1 }}>
              <TextInput
                style={[
                  travellerDetailsStyles.email,
                  { borderColor: focusValues.email ? 'rgb(0, 140, 255)' : colors.lightGrey },
                ]}
                placeholder={EMAIL_PLACEHOLDER}
                onFocus={() => setFocusVal('email', true)}
                onBlur={() => setFocusVal('email', false)}
                onChangeText={(text: string) => onChangeHandler('email', text)}
                value={travellerFields.email}
              />
            </View>
          </>
        )}
        {error.length !== 0 && displayError && (
          <View style={travellerDetailsStyles.errorContainer}>
            <Text style={travellerDetailsStyles.error}>{error}</Text>
          </View>
        )}
      </View>
    );
};

export default TravellerDetailsContainer;
