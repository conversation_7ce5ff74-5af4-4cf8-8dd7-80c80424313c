import {
    StyleSheet,
    Platform,
} from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const priceBreakUpStyles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 5,
    },
    tripGuaranteePremiumStyle: {
        lineHeight: 15,
        color: colors.black,
    },
    totalPriceStyle: {
        fontSize: 16,
        lineHeight: 17,
    },
    blackColor: {
        color: colors.black,
    },
});

export const tgsJourneyStyles = StyleSheet.create({
    showDetails: {
        fontWeight: '700',
        fontSize: 14,
        lineHeight: 17,
        color: colors.azure,
    },
    textColumn1: {
        fontSize: 14,
        lineHeight: 17,
        color: colors.black,
    },
    textRow3: {
        fontSize: 14,
        lineHeight: 17,
        color: colors.defaultTextColor,
    },
    textColumn1LastRow: {
        fontWeight: '400',
        fontSize: 12,
        lineHeight: 17,
        color: colors.greyLight,
    },
    arrowStyle: {
        height: 10,
        width: 10,
        marginLeft: 6,
    },
    flexDirectionWithCenterAligned:
    {
        flexDirection: 'row',
        alignItems: 'center',
    },
    blackColor: { color: colors.black },
},
);

export const tgsComponentStyles = StyleSheet.create({
    buttonContainer: {
        marginBottom: 3,
    },
    textContainer: {
        borderRadius: 4,
        paddingVertical: 14,
        paddingHorizontal: 30,
        minWidth: 240,
        justifyContent: 'center',
    },
    buttonText: {
        fontSize: 16,
        lineHeight: 17,
        letterSpacing: 0.5,
        textAlign: 'center',
        color: colors.white,
        backgroundColor: colors.transparent,
        fontWeight:'700',
    },
    paymentInfo: {
        backgroundColor: colors.lightWhite2,
        flexDirection: 'column',
        borderRadius: 4,
        padding: 8,
        marginBottom: 10,
    },
    container: {
        display: 'flex',
        flexDirection: 'column',
        width: '99%',
        justifyContent: 'space-between',
        borderRadius: 5,
    },
    bannerTag: {
        position: 'relative',
        height: 19,
        left: '59%',
        top: -19,
        width: 145,
        display: 'flex',
        backgroundColor: colors.orange3,
        borderRadius: 11,
        justifyContent: 'center',
        alignItems: 'center',
    },
    bannerTagTextStyle: {
        fontSize: 12,
        lineHeight: 14,
        display: 'flex',
        alignItems: 'center',
        color: colors.white,
        paddingLeft: 7,
        paddingRight: 4,
    },
    gradientContainer: {
        padding: 10,
        borderRadius: 9,
        borderTopWidth: 0,
        flex: 1,
        width: '96%',
        marginLeft: 'auto',
        marginRight: 'auto',
        marginTop: 5,
    },
    pnrContainer: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        width: '100%',
        borderRadius: 5,
        marginBottom: 10,
    },
    pnrText: {
        fontSize: 16,
        lineHeight: 19,
        color: colors.black,
        marginRight: 5,
    },
    pnrTextContainer: {
        marginVertical: 5,
        flexDirection: 'row',
    },
    descriptionTextStyle: {
        fontWeight: '800',
        fontSize: 12,
        color: colors.yellow6,
    },
    availabilityText: {
        color: colors.white,
        fontWeight: '700',
        fontSize: 16,
    },
    flexDirectionRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    tgsWidgetCss: {
        flexBasis: '20%',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 9,
    },
    tgsWidgetImage: {
        height: 54,
        width: 54,
    },
    tgsWidetViewCss: {
        flexDirection: 'row',
        marginBottom: 10,
        alignItems: 'center',
    },
    totalTicketAmountTextCss: {
        marginBottom: 7,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    spendRefundToBookCss: {
        flexDirection: 'row',
        marginBottom: 10,
        justifyContent: 'space-evenly',
    },
    marginAuto: {
        marginRight: 'auto',
        marginLeft: 'auto',
    },
    flexBasisEigthy:{
        flexBasis:'80%',
        marginBottom:20,
        height:20,
    },
},
);

export const tgsTicketDetailsRowComponentStyles = StyleSheet.create({
    row: {
        height: 40,
        borderRadius: 4,
        borderWidth: 0.5,
        marginBottom: 4,
        marginTop: 3,
        paddingRight: 5,
        paddingLeft: 5,
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
    },
    imageStyle: {
        marginLeft: 'auto',
        width: 20,
        height: 20,
    },
    imageContainer: {
        flexBasis: '10%',
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export const tgsTravellersPageStyles = StyleSheet.create({
    refundContainerText: {
        color: colors.defaultTextColor,
        fontSize: 14,
        paddingTop: 5,
        paddingLeft: 6,
        paddingRight: 6,
    },
    refundContainer: {
        paddingBottom: 10,
        borderRadius: 4,
        borderColor: colors.lightGray,
        borderStyle: 'solid',
        borderWidth: 0.5,
        flexBasis: '49%',
    },
    refundContainerGradient: {
        borderTopRightRadius: 4,
        borderTopLeftRadius: 4,
        height: 24,
        padding: 5,
    },
    tripGuaranteeBenefit: {
        marginTop: 8,
        padding: 6,
        borderRadius: 3,
        marginBottom: 8,
        justifyContent: 'center',
    },
    table: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 8,
        marginBottom: 8,
    },
    passengerContainerText: {
        color: colors.purpleyGrey,
    },
    passengerText: {
        color: colors.black,
    },
    passengerContainer: {
        borderRadius: 4,
        borderColor: colors.lightGray,
        borderStyle: 'solid',
        borderWidth: 0.5,
    },
    textContainer: {
        borderRadius: 4,
        paddingVertical: 14,
        paddingHorizontal: 30,
        minWidth: 240,
        justifyContent: 'center',
    },
    paymentInfo: {
        backgroundColor: colors.white,
        flexDirection: 'column',
        borderRadius: 4,
        padding: 8,
        marginBottom: 10,
        borderColor: colors.lightGray,
        borderStyle: 'solid',
        borderWidth: 0.5,
    },
    container: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        borderRadius: 5,
    },
    bannerTag: {
        position: 'absolute',
        width: 145,
        left: '62%',
        top: -32,
        display: 'flex',
        backgroundColor: colors.orange3,
        borderRadius: 11,
        justifyContent: 'center',
        alignItems: 'center',
    },
    bannerTagTextStyle: {
        fontSize: 12,
        lineHeight: 14,
        display: 'flex',
        alignItems: 'center',
        color: colors.white,
        padding: 5,
    },
    gradientContainer: {
        padding: 20,
        paddingBottom: 0,
        borderTopRightRadius: 10,
        borderTopLeftRadius: 10,
        width: '100%',
        marginTop: 12,
    },
    pnrContainer: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 59,
        backgroundColor: colors.white,
        borderRadius: 5,
        marginBottom: 10,
    },
    pnrText: {
        fontStyle: 'normal',
        fontSize: 16,
        lineHeight: 19,
        color: colors.black,
        marginBottom: 4,
    },
    availabilityText: {
        marginBottom: 5,
    },
    flexDirectionRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    paymentTicketFare: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: 30,
    },
    tgsWidgetCss: {
        flexBasis: '20%',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 9,
    },
    refundText1xstyle: {
        fontSize: 12,
        color: colors.white,
    },
    passengerTextContainer: {
        flexBasis: '50%',
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
    },
    tncContainerLink: {
        color: colors.azure,
        fontSize: 12,
        marginLeft: 4,
    }
    , refundText2xstyle: {
        fontSize: 12,
        color: colors.white,
    },
    tgsWidgetContainer: {
        flexDirection: 'row',
    },
    refundText3x: {
        marginBottom: 7,
    },
    refundTextText: {
        color: colors.black,
    },
    tgsWidgetImage: {
        height: 54,
        width: 54,
    },
},
);


export const ticketDetailsBottomSheetStyles = StyleSheet.create({
    container: {
        height: 388,
        backgroundColor: colors.white,
        padding: 20,
    },
    subContainer: {
        padding: 4,
        justifyContent: 'space-between',
        flexDirection: 'row',
    },
    heading: {
        fontWeight: '700',
        fontSize: 30,
        marginBottom: 20,
        fontFamily: 'Lato',
    },
    text: {
        fontWeight: '400',
        fontSize: 16,
        lineHeight: 24,
        color: colors.textGrey,
        marginBottom: 20,
    },
    imageStyle: {
        width: 30,
        height: 30,
    },
    platformContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    platformBox: {
        flexBasis: '48%',
    },
    item: {
        height: 45,
        flexDirection: 'row',
        borderWidth: 0.5,
        borderStyle: 'solid',
        borderRadius: 5,
        alignItems: 'center',
        marginBottom: 10,
        paddingLeft: 11,
    },
    image: { height: 18, width: 18, marginTop: 12 },
});

export const travellerDetailsStyles = StyleSheet.create({
    row: {
        flexDirection: 'row',
    },
    input: {
        width: '48%',
        marginBottom: 3,
    },
    inputText: {
        width: '48%',
        height: 32,
        borderRadius: 4,
        borderWidth: 1,
        borderStyle: 'solid',
        paddingLeft: 8,
        paddingVertical: 0,
    },
    email: {
        height: 32,
        borderRadius: 4,
        borderWidth: 1,
        borderStyle: 'solid',
        paddingLeft: 8,
        paddingVertical: 0,
    },
    error: {
        color: colors.red,
    },
    errorContainer: {
        marginTop: 4,
        backgroundColor: colors.lightPink,
        borderRadius: 3,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 20,
        padding: 4,
    },
});

export const ticketDetailsStyles = StyleSheet.create({
    irctcLogo: {
        width: 30,
        height: 30,
    },
    othersLogo: {
        width: 30,
        height: 30,
    },
});

export const thankyouPageComponentStyles = StyleSheet.create({
    tnc: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '500',
        fontSize: 14,
        lineHeight: 16,
        alignItems: 'center',
        color: colors.azure,
    },
    DownArrow: { height: 6, width: 13, marginTop: 6, marginLeft: 4 },
    tncContainer: {
        flexDirection: 'row',
        marginBottom: 10,
        alignItems: 'center',
        width: '90%',
    },
    tncView: {
        marginLeft: 24,
        marginBottom: 10,
        marginRight: 'auto',
        marginTop: 10,
    },
    tncHeaderContainer: {
        flexDirection: 'row',
        marginBottom: 10,
    },
    tncText: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 10,
        lineHeight: 14,
        color: colors.defaultTextColor,
        marginLeft: 5,
    },
    tncImg: {
        height: 15,
        width: 15,
        resizeMode: 'contain',
    },
    rupeeIcon: {
        position:'absolute',
        top:-3,
        left:12,
        resizeMode: 'contain',
        zIndex: 5,
    },
    refundBox: {
        flex: 1,
        marginBottom: 10,
        borderRadius: 4,
    },
    innerRefundBox: {
        alignItems: 'center', margin: 2, backgroundColor: colors.floralWhite,
        borderWidth: 1.5,
        borderColor: colors.goldenYellow,
        borderRadius:4,
    },
    refundText: {
        marginHorizontal: 15, paddingVertical: 10, marginTop: 3, alignContent: 'center', justifyContent: 'center',
    },
    voucherBox: {
        flexDirection: 'row',
        flex: 1,
        alignItems: 'center',
        marginLeft: 24,
        marginRight: 24,
        marginBottom: 10,
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
    },
    innerVoucherBox: {
        flexDirection: 'row',
        alignItems: 'center',
        margin: 20,
        marginTop:10,
        marginBottom:10,
        backgroundColor: colors.white,
        borderWidth: 1.5,
        borderColor: colors.goldenYellow,
        borderRadius:4,
    },
    voucherIconContainer: { flexBasis: '13%', marginLeft: 15 },
    voucherIcon: {
        resizeMode: 'contain',
        aspectRatio:2.5,
    },
    VcodeText: {
        fontWeight: '600',
        fontSize: 14,
        lineHeight: 16,
        paddingVertical: 6,
        flexBasis: '40%',
        color: colors.defaultTextColor,
    },
    voucherMsgBg: {
        backgroundColor: colors.lightGreen12,
        marginLeft: 'auto',
        marginRight: 'auto',
        borderRadius:4,
        width:'90%',
    },
    voucherMsgText: {
        paddingHorizontal: 6,
        paddingVertical: 3,
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '500',
        fontSize: 12,
        lineHeight: 14,
        color: colors.green,
        textAlign: 'center',
    },
    voucherLogo: {width: 110, marginLeft: 14 },
    refundCardsContainer: {
        flexDirection: 'row',
        justifyContent:
            'space-between',
        marginTop: 15,
        marginLeft: 20,
        marginRight: 20,
    },
    refundCardContainer:
    {
        width: '48%',
    },
});


export const thankyouPageStyles = StyleSheet.create({
    separator: {
        height: 10,
        backgroundColor: colors.grayBg,
    },
    trainsIconContainer: {
        justifyContent: 'center', alignItems: 'center', flexBasis: '100%',
    },
    headingText: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '900',
        fontSize: 18,
        lineHeight: 22,
        textAlign: 'center',
        color: colors.white,
        marginTop: 10,
        marginBottom: 5,
    },
    confirmationContainer: { alignItems: 'center' },
    bookindIdContainer: {
        flexDirection: 'row', justifyContent: 'center', marginBottom: 10,
    },
    bookingId: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 14,
        lineHeight: 15,
        textAlign: 'center',
        color: colors.white,
    },
    BookingIdText: { fontWeight: '700', marginHorizontal: 2 },
    backIcon: {
        height: 16, width: 18, marginTop: 30, marginLeft: 20,
    },
    copyIcon: {
        height: 14,
        width: 10,
         marginTop: 1,
          marginLeft: 2,
    },
    trainsIcon: {
        marginTop: -5,
        marginBottom: 5,
        aspectRatio:1.54,
    },
    button: {
        backgroundColor: colors.white,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 20,
        height: 36,
        width: 223,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    blueText: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '900',
        fontSize: 14,
        lineHeight: 16,
        color: colors.azure,
        textAlign: 'center',
        padding: 2,
    },
    pnrView: {
        backgroundColor: colors.white,
        justifyContent:'space-around',
        borderTopRightRadius: 4,
        borderTopLeftRadius: 4,
        marginTop: -4,
        flexDirection: 'row',
        shadowColor: colors.black,
        shadowOpacity: 0.8,
        shadowRadius: 2,
        shadowOffset: {
            height: 1,
            width: 1,
        },
    },
    pnrTextContainer: {
        marginLeft: 24, marginTop: 18, marginBottom: 18, flexBasis: '70%',
    },
    pnrText: {
        marginTop: 5,
        fontSize: 20,
        lineHeight: 19.2,
        color: colors.black,
    },
    trainDateAndInfo: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 14,
        lineHeight: 20,
        color: colors.greyLight,
        marginTop: 3,
    },
    voucherView: {
        backgroundColor: colors.white,
        marginTop: 14,
    },
    trainsIconContainerSmall: {
        marginLeft: 24, marginBottom: 10, flexBasis: '10%',
    },
    trainsIconSmall: {
        height: 40, width: 40, resizeMode: 'contain',
    },
    flexDirectionRow: { flexDirection: 'row' },
    policyText: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 14,
        lineHeight: 14,
        color: colors.defaultTextColor,
        marginTop: 5,
        width: '80%',
    },
    buttonContainer: {
        marginTop: 10,
        marginBottom: 30,
    },
    homeContainer: {
        marginRight: 'auto',
        alignSelf: 'center',
        marginVertical: 15,
        marginLeft: 20,
    },
    trainBanner: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width:'98%',
    },
    bottomSheetContainer: {
        backgroundColor: colors.white,
        padding: 7,
        paddingTop:20,
        borderRadius:7,
    },
    headingContainer:
    {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
    subHeadingContainer: {
        textAlign: 'center',
        color: colors.black,
        fontSize: 20,
        lineHeight: 24,
    },
    imageContainer: {
        height: 13,
        width: 13,
        position: 'absolute',
        left: '92%',
        marginTop:20,
        marginBottom:20,
    },
    image: {
        height: 13,
        width: 13,
    },
    message: {
        textAlign: 'center',
        marginTop: 5,
        color: colors.textGrey,
    },
    whatsAppBanner: {
        height:300,
        width: '100%',
        marginLeft: 'auto',
        marginRight: 'auto',
        marginTop: 10,
    },
    whatsAppCTAButtonBottom: {
        marginBottom: Platform.select({ ios: 15 }),
    },
});

export const tgsTripGuaranteeComponentStyles = StyleSheet.create({
    flexDirection: {
        flexDirection: 'row',
        justifyContent:'space-between',
        alignItems:'center',
        marginBottom:5,
    },
    container:{

    },
    image: {
        width: 50,
        height: 32,
    },
    heading: {
        color: colors.white,
        lineHeight:32,
        fontSize:24,
    },
    ruppeText: {
        color: colors.white,
        backgroundColor: colors.green12,
        paddingTop: 2,
        paddingBottom: 2,
        paddingRight: 4,
        paddingLeft: 4,
        borderRadius:4,

    },
    text: {
        color: colors.white,
        fontSize:16,
        lineHeight:22,
        marginBottom:10,
    },
});

export const tgsRefundContainerStyles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        marginBottom: '4%',
        justifyContent: 'space-between',
    },
    refundContainerOne: {
        width: '45%',
        borderWidth: 1.5,
        borderColor: colors.yello,
        borderRadius:6,
        backgroundColor: colors.lightWhite2,
        padding: 5,
        paddingLeft:10,
    },
    refundContainerTwo: {
        width: '50%',
        borderWidth: 1.5,
        borderColor: colors.yello,
        backgroundColor: colors.lightWhite2,
        padding: 5,
        paddingLeft:10,
        borderRadius:6,
    },
    heading: {
        color: colors.black,
        marginBottom: 8,
        lineHeight:24,
    },
    text: {
        fontSize: 14,
        color: colors.black,
    },
    imageDimensions: {
        marginTop:'2%',
        height: 24,
        width: '80%',
        marginLeft:'auto',
        marginRight:'auto',
    },
    marginBottom:
    {
        marginBottom: '4%',
    },
});
