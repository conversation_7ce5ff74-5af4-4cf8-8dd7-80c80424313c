
import React from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
} from 'react-native';
import {ticketDetailsBottomSheetStyles} from './TGSContainer.styles';
import { TicketDetailsBottomSheetInterface } from '../../TGSCommonInterfaces';
import {
    ticketOptions, commonStyles,TICKET_DETAILS, TICKET_DETAILS_TEXT,
    omnitureMapping,
} from '../../TGSUtils/Constants';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import closeIcon from '@mmt/legacy-assets/src/close-black.webp';

const TicketDetailsBottomSheet = ({ setPlatformVal, platformVal, 
    setModalVal, handleTouchOutside }: TicketDetailsBottomSheetInterface) => {
    return (<View style={ticketDetailsBottomSheetStyles.container}>
        <View style={ticketDetailsBottomSheetStyles.subContainer}>
            <Text style={ticketDetailsBottomSheetStyles.heading}>{TICKET_DETAILS}</Text>
            <TouchableOpacity onPress={handleTouchOutside}>
                <Image style={ticketDetailsBottomSheetStyles.image} source={closeIcon} />
            </TouchableOpacity>
        </View>
        <Text style={ticketDetailsBottomSheetStyles.text}>{TICKET_DETAILS_TEXT}</Text>
        <View style={ticketDetailsBottomSheetStyles.platformContainer}>
            {ticketOptions.map((ticketOption) => {
                return (
                    <TouchableOpacity style={ticketDetailsBottomSheetStyles.platformBox} 
                    key={ticketOption.id} onPressOut={(event) => {
                        if (event) {
                            event.preventDefault();
                        }
                        trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE,
                            `${omnitureMapping.TGS_TRAVELLERS_OTHER_OPTIONS}${ticketOption.id}`);
                        setPlatformVal(`${ticketOption.displayName}`); setModalVal(false);
                    }}>
                        <View style={[ticketDetailsBottomSheetStyles.item, { 
                            borderColor: platformVal === ticketOption.displayName ? colors.azure : colors.lightGray }]}>
                            <Image source={ticketOption.image} style={ticketDetailsBottomSheetStyles.imageStyle} />
                            <Text style={commonStyles.platformText}>{ticketOption.displayName}</Text>
                        </View>
                    </TouchableOpacity>);
            })}
        </View>
    </View>);
};

export default TicketDetailsBottomSheet;
