import React from 'react';
import {
    View,
    Text,
} from 'react-native';
import {priceBreakUpStyles} from './TGSContainer.styles';
import { commonStyles, splitText } from '../../TGSUtils/Constants';
import { PriceBreakupProps } from '../../TGSCommonInterfaces';
import {fontStyle} from '../../../../../vernacular/VernacularUtils';

const PriceBreakupContainer = ({ 
    priceBreakUpContainer: { 
        tripGuaranteePremium, 
        discount, 
        serviceCharge, 
        totalPrice 
    } }: PriceBreakupProps) => {
    const [tripGuaranteePremiumText, tripGuaranteePremiumValue] = splitText(tripGuaranteePremium);
    const [serviceChargeText, serviceChargeValue] = splitText(serviceCharge);
    const [discountText, discountValue] = splitText(discount);
    const [totalPriceText, totalPriceTextValue] = splitText(totalPrice);

    return (<View style={[commonStyles.genericContainer, { paddingBottom: 9, marginBottom: 0 }]}>
        <View style={commonStyles.firstRow}>
            <Text style={commonStyles.heading}>Price Breakup</Text>
        </View>
        <View style={priceBreakUpStyles.row}>
            <Text style={[priceBreakUpStyles.tripGuaranteePremiumStyle,fontStyle('bold')]}>{tripGuaranteePremiumText}</Text>
            <Text style={[priceBreakUpStyles.tripGuaranteePremiumStyle,fontStyle('bold')]}>{tripGuaranteePremiumValue}</Text>
        </View>
        {serviceCharge &&
            <View style={priceBreakUpStyles.row}>
                <Text style={[fontStyle('regular'),priceBreakUpStyles.blackColor]}>{serviceChargeText}</Text>
                <Text style={[fontStyle('regular'),priceBreakUpStyles.blackColor]}>{serviceChargeValue}</Text>
            </View>
        }
        {
            discount &&
            <View style={priceBreakUpStyles.row}>
                <Text style={[fontStyle('regular'),priceBreakUpStyles.blackColor]}>{discountText}</Text>
                <Text style={[fontStyle('regular'),priceBreakUpStyles.blackColor]}>{discountValue}</Text>
            </View>
        }
        <View style={priceBreakUpStyles.row}>
            <Text style={[fontStyle('bold'),priceBreakUpStyles.blackColor]}>{totalPriceText}</Text>
            <Text style={[fontStyle('bold'),priceBreakUpStyles.blackColor]}>{totalPriceTextValue}</Text>
        </View>
    </View>);
};

export default PriceBreakupContainer;
