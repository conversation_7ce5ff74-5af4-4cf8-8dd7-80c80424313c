import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableWithoutFeedback,
} from 'react-native';
import { thankyouPageComponentStyles } from './TGSContainer.styles';
import HTMLView from 'react-native-htmlview';
import { TermsAndConditionsProps, RefundCardsProps, VoucherCardProps } from '../../TGSCommonInterfaces';
import {
    termsAndConditionsImg,
    VOUCHER_CODE,
    TERMS_AND_CONDITIONS,
    omnitureMapping,
} from '../../TGSUtils/Constants';
import { DoubleRupee, UpArrow, DownArrow, Rupee, VoucherIcon, VoucherCodeLogo } from '../../TGSUtils/TGSAssets';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

export const TermsAndConditions = ({termsAndConditions}:TermsAndConditionsProps) => {
    const [isExtended, setIsExtended] = useState(false);
    const tncArray = Object.entries(termsAndConditions);

      return (
          <View style={thankyouPageComponentStyles.tncView}>
              <TouchableWithoutFeedback onPress={() => {
                trackClickEventProp61(
                    omnitureMapping.TGS_THANKYOU_SUCCESS,
                    omnitureMapping.TGS_THANKYOU_T_AND_C); 
                    setIsExtended(!isExtended);}} 
                style={thankyouPageComponentStyles.tncHeaderContainer}>
              <View style={thankyouPageComponentStyles.tncHeaderContainer}>
                  <Text style={thankyouPageComponentStyles.tnc}>{TERMS_AND_CONDITIONS}</Text>
                 { isExtended ? 
                 <UpArrow style={thankyouPageComponentStyles.DownArrow}/> :  
                 <DownArrow style={thankyouPageComponentStyles.DownArrow}/>}
                 </View>
              </TouchableWithoutFeedback>
              {isExtended && tncArray.map(([name, value],index) => (
                  <View key={index}  style={thankyouPageComponentStyles.tncContainer}>
                      {termsAndConditionsImg(thankyouPageComponentStyles.tncImg)[name] }
                      <Text style={thankyouPageComponentStyles.tncText}>{value}</Text>
                  </View>
              ))
              }
          </View>
      );
  };

const RefundCards = ({data,dataStyle, doubleRupeeSymbol}:RefundCardsProps) => {
    return (
            <View style={thankyouPageComponentStyles.refundCardContainer}>
            {doubleRupeeSymbol ? <DoubleRupee height={13} 
            style={thankyouPageComponentStyles.rupeeIcon} /> : 
            <Rupee height={13} style={thankyouPageComponentStyles.rupeeIcon} />}
                <View style={thankyouPageComponentStyles.innerRefundBox}>
                    <View style={thankyouPageComponentStyles.refundText}>
                        <HTMLView  value={data} stylesheet={StyleSheet.create(JSON.parse(dataStyle))} />
                    </View>
                </View>
            </View>
    );
};

export const VoucherCard = ({voucherMsg ,voucherCodeStyle, refundText}:VoucherCardProps) =>{
    const voucherMsgBg = {backgroundColor:voucherCodeStyle.backgroundColor};
    const voucherMsgText = {color:voucherCodeStyle.color};
    return (
        <View>
                <View style={thankyouPageComponentStyles.innerVoucherBox}>
                    <View style={thankyouPageComponentStyles.voucherIconContainer}>
                        <VoucherIcon height={12}  style={thankyouPageComponentStyles.voucherIcon} />
                    </View>
                    <Text style={thankyouPageComponentStyles.VcodeText}>{VOUCHER_CODE}</Text>
                    <VoucherCodeLogo height={16} style={thankyouPageComponentStyles.voucherLogo}/>
                </View>

            <View style={[thankyouPageComponentStyles.voucherMsgBg, voucherMsgBg ]}>
                <Text style={[thankyouPageComponentStyles.voucherMsgText, voucherMsgText ]}>{voucherMsg}</Text>
            </View>

            <View style ={thankyouPageComponentStyles.refundCardsContainer}>
                <RefundCards data={refundText.refundTextColumn1} dataStyle={refundText.refundTextStyle}/>
                <RefundCards doubleRupeeSymbol 
                data={refundText.refundTextColumn2} 
                dataStyle={refundText.refundTextStyle}/>
            </View>
        </View>
    );
};

