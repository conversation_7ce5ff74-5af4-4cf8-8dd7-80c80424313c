import React from 'react';
import {
    View,
    Text,
    TouchableOpacity,
} from 'react-native';
import {ticketDetailsStyles} from './TGSContainer.styles';
import { TicketDetailsContainerInterface } from '../../TGSCommonInterfaces';
import {
    commonStyles,ticketOptions,
    TICKET_DETAILS,
    BOOK_TICKET_DETAILS, IRCTC, OTHERS, RADIO_BUTTON_1, RADIO_BUTTON_2
    , omnitureMapping
    , TGS_Tickets_Container,
} from '../../TGSUtils/Constants';
import TGSTicketDetailsRowComponent from './TGSTicketDetailsRowComponent';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import othersLogo from '@mmt/legacy-assets/src/rails/Others.webp';
import IRCTCLogo from '@mmt/legacy-assets/src/rails/IRCTC.webp';

const TicketDetailsContainer = ({ setModalVal, platformVal, 
    setRadioButton, radioButton, captureRef }: TicketDetailsContainerInterface) => {
    const logo = platformVal === OTHERS ? othersLogo : ticketOptions.find(o => o.displayName === platformVal)?.image;

    const handleOnpressRadioButton2 = () => {
        setRadioButton(RADIO_BUTTON_2);
        setModalVal(true);
        trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE, omnitureMapping.TGS_TRAVELLERS_OTHERS);
    };

    return (<View ref={(ref) => captureRef(TGS_Tickets_Container, ref)} style={commonStyles.genericContainer}>
        <View style={commonStyles.firstRow}>
            <Text style={commonStyles.heading}>{TICKET_DETAILS}</Text>
        </View>
        <View>
            <Text>{BOOK_TICKET_DETAILS}</Text>
        </View>
        <TouchableOpacity onPress={() => { 
            setRadioButton(RADIO_BUTTON_1); 
            trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE, omnitureMapping.TGS_TRAVELLERS_IRCTC); 
            }}>
            <TGSTicketDetailsRowComponent 
            radioButton={radioButton} 
            logoName={IRCTC} 
            buttonName={RADIO_BUTTON_1} 
            logo={IRCTCLogo} 
            logoStyle={ticketDetailsStyles.irctcLogo} />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => handleOnpressRadioButton2()}>
            <TGSTicketDetailsRowComponent 
            radioButton={radioButton} 
            logoName={platformVal} 
            logo={logo} 
            buttonName={RADIO_BUTTON_2} 
            logoStyle={ticketDetailsStyles.othersLogo} />
        </TouchableOpacity>
    </View >);
};

export default TicketDetailsContainer;
