import React , {useState} from 'react';
import {
    View,
    Text,
    Image,
    StyleSheet,
    FlatList,
    TouchableWithoutFeedback,
  } from 'react-native';
import {
    DEFAULT_RIS_OPTIONS_FOR_WEB, SHOW_MORE, SEE_LESS,
    RAIL_INFORMATION_SERVICES, omnitureMapping,
} from '../../TGSUtils/Constants';
import {   DownArrow, UpArrow} from '../../TGSUtils/TGSAssets';
import { _label } from '../../../../../vernacular/AppLanguage';
import { utils } from '../../../../RailsBusHomePage/Common';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {RisOptionCardProps} from '../../TGSCommonInterfaces';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

const OptionCard = (props: RisOptionCardProps) => {
    const { icon, iconText, onClick } = props;

    const handleOnPress = () => {
   onClick();
   trackClickEventProp61(omnitureMapping.TGS_THANKYOU_SUCCESS,`${omnitureMapping.TGS_THANKYOU_RIS}${iconText}`);
    };

    return (
        <View style={styles.optionCardContainer}>
            <TouchableWithoutFeedback onPress={() => handleOnPress()} 
            style={[styles.contentContainer,{backgroundColor:colors.black}]}>
                <View style={styles.contentContainer}>
                    <View style={styles.imgContainer}>
                        <Image style={styles.img} source={icon} />
                    </View>
                    <View style={styles.textContainer}>
                        <Text style={styles.textStyle}> {_label(iconText)} </Text>
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </View>
    );
  };

const RisOptions = () =>{
    const [open , setOpen] = useState<boolean>(false);
    const {getRisOptions } = utils;
    const risDataMap = getRisOptions( 
        open ? DEFAULT_RIS_OPTIONS_FOR_WEB : 
        DEFAULT_RIS_OPTIONS_FOR_WEB.slice(0,4) , false
    );
    return (
        <>
            <View style={{marginBottom:11}}>

                <Text style={[styles.headingStyle, {fontWeight: 'bold' }]}>
                    {RAIL_INFORMATION_SERVICES}
                </Text>

                <View style={styles.listContainer}>
                    <FlatList
                        data={risDataMap}
                        numColumns={4}
                        contentContainerStyle={{paddingVertical:20}}
                        ItemSeparatorComponent={
                            () => <View style={styles.itemSeperator}/>
                        }
                        renderItem={({ item, index }) => (
                            <View style={{marginRight:'auto',marginLeft:'auto'}}>
                            <OptionCard icon={item.icon} iconText={item.text} onClick={item.onClick} key={index} />
                            </View>
                        )}
                    />
                    <View>
                        <TouchableWithoutFeedback onPress={() => { !open ? trackClickEventProp61(omnitureMapping.TGS_THANKYOU_SUCCESS, omnitureMapping.TGS_THANKYOU_RIS_SHOW_MORE) : ''; setOpen(!open); }} style={[styles.showMoreContainer,open ? {marginHorizontal:130} : {marginHorizontal:55}]}>
                            <View elevation={3} style={[styles.showMoreContainer , !open ? {width:'59%'} : {width:81}]}>
                                <Text style={styles.showMoreText}>{open ? SEE_LESS : SHOW_MORE}</Text>
                                {open ? <UpArrow style={styles.UpArrow} /> : <DownArrow style={styles.DownArrow} /> }
                            </View>
                    </TouchableWithoutFeedback>
                    </View>
                </View>

            </View>
        {risDataMap && <View style={styles.fullPlaceholder} />}
        <View style={styles.separator} />
      </>
    );
};
const styles = StyleSheet.create({
    fullPlaceholder: {
        flex: 1,
        backgroundColor: colors.grayBg,
    },
    separator: {
        marginTop:5,
        width: '100%',
        height: 10,
        backgroundColor: colors.grayBg,
    },
    headingStyle: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '700',
        fontSize: 16,
        lineHeight: 17,
        color: colors.black,
        marginTop:16,
        marginLeft:16,
        marginBottom:14,
    },
    showMoreText: {
        fontFamily: 'Lato',
        fontStyle: 'normal',
        fontWeight: '700',
        fontSize: 12,
        lineHeight: 15,
        textAlign: 'center',
        color: colors.azure,
        marginVertical:2,
    },
    showMoreContainer : {
        flexDirection : 'row',
        backgroundColor:colors.white,
        shadowOffset: { width: 0, height: 1 },
         shadowOpacity: 0.2,
          shadowRadius: 4,
        alignItems:'center',
        justifyContent:'center',
        marginLeft: 'auto',
        marginRight: 'auto',
        borderRadius:12,
    },
    DownArrow: { height: 6, width: 13, marginTop: 3, marginLeft: 7 },
    UpArrow : {height : 8 , width : 16 , marginTop:3 , marginLeft:4 },
    optionCardContainer : {},
    contentContainer : { flexDirection: 'column', alignItems: 'center', justifyContent: 'center' },
    imgContainer: {width: 40, height: 40, borderRadius:20  , backgroundColor:colors.grey5 , alignItems: 'center', justifyContent: 'center' },
    img : { width: 30, height: 30, borderRadius: 15, backgroundColor: colors.grey5, padding: 2, overflow: 'hidden' },
    textContainer: { width: 84 },
    textStyle :{ textAlign: 'center', fontSize: 12, lineHeight: 13, fontWeight: '500', marginTop: 10 } ,
    listContainer : {justifyContent: 'space-around', marginLeft:16 , marginRight:24 , shadowColor: colors.gray4, shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.2, shadowRadius: 4},
    itemSeperator : {height:25 , backgroundColor:colors.white},
});

export default RisOptions;
