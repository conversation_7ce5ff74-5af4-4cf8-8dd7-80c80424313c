import React from 'react';
import {
    View,
    Text,
    Image,
} from 'react-native';
import {tgsTicketDetailsRowComponentStyles} from './TGSContainer.styles';
import { commonStyles, BlueTick, EmptyEllipse } from '../../TGSUtils/Constants';
import { TGSTicketDetailsRowComponentInterface } from '../../TGSCommonInterfaces';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

const TGSTicketDetailsRowComponent = ({ logoName, logo, 
    buttonName, logoStyle, radioButton }: TGSTicketDetailsRowComponentInterface) => {

    return (
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
            }}>
                <View style={[tgsTicketDetailsRowComponentStyles.row, { borderColor: radioButton !== buttonName ? colors.lightGray : colors.azure, justifyContent: 'center' }]}>
                    <View style={tgsTicketDetailsRowComponentStyles.imageContainer}>
                        <Image style={logoStyle} source={logo} />
                    </View>
                    <Text style={[commonStyles.platformText, { marginLeft: 4 }]}>
                        {logoName}
                    </Text>
                    {radioButton === buttonName ? 
                    <Image style={tgsTicketDetailsRowComponentStyles.imageStyle} 
                    source={BlueTick} /> : 
                    <Image style={tgsTicketDetailsRowComponentStyles.imageStyle} 
                    source={EmptyEllipse} />}
                </View>
            </View>);
};

export default TGSTicketDetailsRowComponent;
