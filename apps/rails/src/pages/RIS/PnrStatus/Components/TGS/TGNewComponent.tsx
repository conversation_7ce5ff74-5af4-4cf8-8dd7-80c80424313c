import { View, Text, StyleSheet, Image } from 'react-native';
import React from 'react';
import fecha from 'fecha';
import { isEmpty } from 'lodash';
import Button from '@RN_UI_Lib/Button';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import TGStaticComponent from 'apps/rails/src/pages/RisPnrForm/Components/TGStaticComponent';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { Actions } from 'apps/rails/src/navigation';
import { TRAIN_LABEL, STUCK_IN_TEXT, WAITING_LIST_TEXT } from 'apps/rails/src/Utils/RailsConstant';

import tgWaitingList from '@mmt/legacy-assets/src/TG_Ani.webp';
import closeButton from '@mmt/legacy-assets/src/b2bFilterCross.webp';
import PropTypes from 'prop-types';

const TGNewComponent = ({
    bottomSheet,
    onClose,
    isWaitlist,
    showClose,
    cityDetails,
    travelDate,
    tgMarketingData,
}) => {
    const bookNextTrip = useConfigStore(configKeys.RAILS_TG_MARKETING_NEXTTRIP);
    const bookSameTrip = useConfigStore(configKeys.RAILS_TG_MARKETING_SAMETRIP);

    const { SourceCity, DestinationCity } = cityDetails;
    const { metaTgEligible, tgMarketingVoucher} = tgMarketingData;

    let displayMessage = '';
    displayMessage = !isEmpty(bookSameTrip) && bookSameTrip;
    if (!metaTgEligible && isWaitlist) {
        displayMessage = !isEmpty(bookNextTrip) && bookNextTrip;
    }

    const openDeepLink = () => {
        onSubmit();

        const date = fecha.parse(travelDate, 'DD-MM-YYYY');
        Actions.railsListing({
            originStation: { code: SourceCity.Code, stationName: SourceCity.Name },
            destinationStation: { code: DestinationCity.Code, stationName: DestinationCity.Name },
            departureDate: date,
            fromPage: 'pnrStatus',
          });
    };

    const onSubmit = () => {
        if (bottomSheet) {
            onClose();
        }
    };

    const viewTrainsButton = (
        <Button
            name={TRAIN_LABEL}
            onPress={openDeepLink}
            solidBtn={showClose ? true : false}
            btnCustTxtStyle={showClose ? styles.modalButtonText : styles.customButtonText}
            btnCustStyle={showClose ? styles.modalButton : styles.customButton}
            btnActiveOpacity={0}
        />
    );

    return (
        <View style={styles.container}>
            <View style={styles.bannerContainer}>
                <Image source={tgWaitingList} resizeMode="contain" style={styles.tgWaitListImage} />
                <Text style={styles.bannerText}>{STUCK_IN_TEXT}
                    <Text style={styles.bannerText2}>{WAITING_LIST_TEXT}</Text>
                    ?
                </Text>
                {showClose && (<TouchableRipple onPress={onSubmit}>
                    <Image style={styles.closeButton} source={closeButton}/>
                </TouchableRipple>)}
            </View>
            {displayMessage && <Text style={styles.displayMessage}>{displayMessage}</Text>}
            <TGStaticComponent couponCode={tgMarketingVoucher}/>
            <View style={styles.button}>
                {viewTrainsButton}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        height: 468,
        backgroundColor: colors.white,
        borderTopLeftRadius: 15,
        borderTopRightRadius: 15,
    },
    button: {
        width: '100%',
        marginTop: 15,
        flex:1,
        height: 30,
    marginBottom: 7,
    },
    customButtonText: {
        color: colors.darkBlue,
        fontFamily: fonts.regular,
        fontWeight: 'bold',
        fontSize: 16,
    },
    modalButtonText: {
        color: colors.white,
        fontFamily: fonts.regular,
        fontWeight: 'bold',
        fontSize: 16,
    },
    customButton: {
        marginLeft: 11,
        marginRight: 10,
        borderColor: colors.darkBlue,
        fontWeight: 'bold',
        fontSize: 16,
        width: '93%',
        paddingVertical: 10,
        fontFamily: fonts.regular,
        height: 'auto',
    },
    modalButton: {
        marginLeft: 11,
        marginRight: 10,
        marginBottom: 15,
        borderColor: colors.darkBlue,
        fontWeight: 'bold',
        fontSize: 16,
        width: '93%',
        paddingVertical: 15,
        fontFamily: fonts.bold,
        height: 'auto',
    },
    closeButton: {
        height: 15,
        width: 15,
        margin: 15,
    },
    tgWaitListImage: {
        height: 55,
        width: '100%',
        flex: 2,
    },
    bannerContainer: {
        marginTop: 5,
        justifyContent: 'center',
        alignItems: 'flex-start',
        flexDirection: 'row',
        marginBottom: -15,
    },
    bannerText: {
        flex: 4,
        marginTop: 15,
        marginLeft: -13,
        fontFamily: fonts.regular,
        fontWeight: 'bold',
        fontSize: 17,
        color: colors.black,
    paddingBottom: 3,
    },
    bannerText2: {
        fontFamily: fonts.regular,
        fontWeight: 'bold',
        fontSize: 17,
        color: colors.lightYello,
    },
    displayMessage: {
        flex: 1,
        marginTop: 10,
        marginHorizontal: 20,
        marginBottom: 3,
    paddingBottom: 3,
        fontFamily: fonts.regular,
        fontWeight: '400',
        fontSize: 16,
        color: colors.black,
    },
});
TGNewComponent.propTypes = {
    bottomSheet: PropTypes.bool,
    onClose: PropTypes.func,
    isWaitlist: PropTypes.bool,
    showClose: PropTypes.bool,
    cityDetails: PropTypes.shape({
        SourceCity: PropTypes.shape({
            Code: PropTypes.string,
            Name: PropTypes.string,
        }),
        DestinationCity: PropTypes.shape({
            Code: PropTypes.string,
            Name: PropTypes.string,
        }),
    }),
    travelDate: PropTypes.string,
    tgMarketingData: PropTypes.shape({
        metaTgEligible: PropTypes.bool,
        tgMarketingVoucher: PropTypes.string,
    }),
};

export default TGNewComponent;
