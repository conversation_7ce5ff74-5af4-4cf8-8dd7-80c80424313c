import React from 'react';
import { View, Text, Image, StyleSheet, TouchableHighlight } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  SHARE_WITH_FRIENDS,
  TELL_YOUR_FRIENDS,
} from 'apps/rails/src/pages/RIS/PnrStatus/TGSUtils/Constants';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

const { whatsAppShare } = ASSETS;

interface TgsThankyouShare {
    onShare: () => void;
}

const TGSThankyouShareComponent = (props: TgsThankyouShare) => {
    return (<View style={styles.container}>
        <View style={styles.textContainer}>
            <Text style={styles.bold} >
                {SHARE_WITH_FRIENDS}
            </Text>
            <Text style={styles.lightText}>
                {TELL_YOUR_FRIENDS}
            </Text>
        </View>
        <TouchableHighlight onPress={props.onShare} style={styles.imageContainer} underlayColor={colors.transparent}>
            <Image source={whatsAppShare} style={styles.image}  resizeMode="contain" />
        </TouchableHighlight>
    </View>);
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        flexWrap: 'wrap',
    },
    bold: {
        color: colors.black,
        fontWeight: '700',
        lineHeight: 17,
        marginHorizontal:8,
    },
    lightText: {
        color: colors.greyLight,
        fontWeight: '400',
        lineHeight: 17,
        marginHorizontal:8,
    },
    image: {
        height: 30,
        width: '100%',
    },
    textContainer: {
        margin: 5,
        marginRight:'4%',
    },
    imageContainer: {
        margin: 5,
        width: '23%',
    },
});

export default TGSThankyouShareComponent;
