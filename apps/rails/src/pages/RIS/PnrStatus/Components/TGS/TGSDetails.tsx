import React from 'react';
import { View, Text, Image, StyleSheet,Platform } from 'react-native';
import HTMLView from 'react-native-htmlview';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import {useThis,waitListedText} from 'apps/rails/src/pages/RIS/PnrStatus/TGSUtils/Constants';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

import mmtImage from 'packages/legacy-assets/src/TGS/mmt_Image.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
const { lobIcons } = ASSETS;
interface Props {
    refundAmountValue: string;
}

const TGSDetails = ({ refundAmountValue }: Props) => {
    return (<View>
        <View style={styles.textContainer}>
            <Text style={[styles.textStyle, fontStyle('regular')]}>
                Get <Image style={styles.imageStyle} source={mmtImage} resizeMode="contain" /> Voucher of <HTMLView stylesheet={styles} value={`<a>₹ ${refundAmountValue}<a>`} /> {waitListedText}</Text>
        </View>
        <View>
            <Text style={styles.textStyle2}>
                {useThis}
            </Text>
        </View>
        <Image resizeMode="contain" source={lobIcons} style={styles.imageLob} />
    </View>);
};


const styles = StyleSheet.create({
    textStyle: {
        textAlign: 'center',
        marginTop: 15,
        color: colors.black,
        alignItems: 'center',
        fontFamily: 'Lato',
        fontSize: 14,
        lineHeight: 28,
    },
    textStyle2: {
        textAlign: 'center',
        marginTop: 7,
        color: colors.black,
        alignItems: 'center',
        fontFamily: 'Lato',
        fontSize: 14,
    },
    a: {
        color: colors.white,
        backgroundColor: colors.lightGreen4,
        paddingHorizontal: 4,
        paddingVertical:2,
        borderRadius: 4,
        fontFamily: 'Lato-Bold',
        position: 'relative',
        top: Platform.select({ios:2,android:5}),
        borderWidth:1,
        borderColor: colors.white,
        overflow: 'hidden',
    },
    imageLob: {
        width: '80%',
        marginLeft: 'auto',
        marginRight: 'auto',
        height:65,
    },
    textContainer: {
        flexDirection: 'row',
        width: '74%',
        alignItems: 'center',
        marginLeft: 'auto',
        marginRight: 'auto',
    },
    imageStyle: {
        height: 25,
        width: 25,
    },
});
export default TGSDetails;
