import React from 'react';
import { View, Text } from 'react-native';
import { RuppeeSymbol, PriceRise } from '../../../../TGS/Components/TGSConstants';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import { configKeyMapping } from '../../TGSUtils/Constants';
import {tgsRefundContainerStyles} from './TGSContainer.styles';
// import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

// const lobIcons = ASSETS.lobIcons;

interface TgsRefundContainerProps {
    tgsTextData?: Record<string, string>;
  }

  const TgsRefundContainer: React.FC<TgsRefundContainerProps> = ({ tgsTextData }) => {
    return (<View style={tgsRefundContainerStyles.container}>
        <View style={tgsRefundContainerStyles.refundContainerOne}>
            <View  style={tgsRefundContainerStyles.marginBottom}>
                <RuppeeSymbol />
            </View>
            <Text style={[tgsRefundContainerStyles.heading, fontStyle('bold')]}>{tgsTextData?.[configKeyMapping.Box1_HeadLine]}</Text>
            <Text style={tgsRefundContainerStyles.text}>{tgsTextData?.[configKeyMapping.Box1_Text]}</Text>
        </View>
        <View style={tgsRefundContainerStyles.refundContainerTwo}>
            <View style={tgsRefundContainerStyles.marginBottom}>
                <PriceRise />
            </View>
            <Text style={[tgsRefundContainerStyles.heading, fontStyle('bold')]}>{tgsTextData?.[configKeyMapping.headline]}</Text>
            <Text style={tgsRefundContainerStyles.text}>{tgsTextData?.[configKeyMapping.Box2_Text]}</Text>
        </View>
    </View>);
};


export default TgsRefundContainer;