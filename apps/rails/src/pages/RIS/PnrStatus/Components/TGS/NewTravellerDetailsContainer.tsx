import React, { useState, useEffect } from 'react';
import { View, Text, TextInput } from 'react-native';
import {
  TRAVELLER_DETAILS,
  travellerFieldEmptyError,
  TGS_Traveller_Container,
  validateName,
  NEW_PLACEHOLDERS,
} from '../../TGSUtils/Constants';
import { travellerDetailsStyles } from './TGSContainer.styles';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  validateMobile,
  validateEmail,
} from '@mmt/legacy-commons/Common/Components/ContactDetails/contactDetails.utils';
import { TravellerDetailsInterface, TravellerDetailsProps } from '../../TGSCommonInterfaces';
import styles from './styles/NewTravellerDetails.styles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';

const NewTravellerDetailsContainer = ({
  captureRef,
  travellerFields,
  setTravellerFields,
  error,
  setError,
  tgsEmailInputMandatory,
}: TravellerDetailsProps) => {
  const [focusValues, setFocusValues] = useState({ name: false, mobile: false, email: false });
  const [displayError, setDisplayError] = useState(false);

  useEffect(() => {
    travellersError();
  }, [travellerFields]);

  const setFocusVal = (param: string, val: boolean) =>
    setFocusValues((prev: TravellerDetailsInterface) => ({ ...prev, [param]: val }));

  const travellersError = () => {
    const { valid: isValidName, error: nameError } = validateName(travellerFields.name);
    const { valid: isValidMobileNumber, error: mobileError } = validateMobile(
      travellerFields.mobile,
    );
    const { valid: isValidEmail, error: emailError } = validateEmail(travellerFields.email);

    if (
      travellerFields.mobile === '' ||
      (tgsEmailInputMandatory && travellerFields.email === '') ||
      travellerFields.name === ''
    ) {
      setError(travellerFieldEmptyError);
    } else if (!isValidName) {
      setError(nameError);
    } else if (!isValidMobileNumber) {
      setError(mobileError);
    } else if ((tgsEmailInputMandatory || travellerFields.email.length !== 0) && !isValidEmail) {
      setError(emailError);
    } else {
      setError('');
    }
  };

  const onChangeHandler = (name: string, text: string) => {
    setTravellerFields((prev: TravellerDetailsInterface) => ({ ...prev, [name]: text }));
    setDisplayError(true);
  };

  return (
    <View ref={(ref: unknown) => captureRef(TGS_Traveller_Container, ref)} style={styles.container}>
      <Text style={[styles.heading, getLineHeight(20), fontStyle('black')]}>
        {TRAVELLER_DETAILS}
      </Text>
      <View style={styles.name}>
        <TextInput
          placeholder={NEW_PLACEHOLDERS.name}
          placeholderTextColor={colors.lightTextColor}
          onChangeText={(text: string) => onChangeHandler('name', text)}
          value={travellerFields.name}
          onFocus={() => setFocusVal('name', true)}
          onBlur={() => setFocusVal('name', false)}
          style={[
            styles.inputText,
            { borderColor: focusValues.name ? colors.blueColour : colors.lightGrey },
          ]}
        />
        <TextInput
          placeholder={NEW_PLACEHOLDERS.mobile}
          placeholderTextColor={colors.lightTextColor}
          onChangeText={(text: string) => onChangeHandler('mobile', text)}
          keyboardType="phone-pad"
          maxLength={10}
          onFocus={() => setFocusVal('mobile', true)}
          onBlur={() => setFocusVal('mobile', false)}
          value={travellerFields.mobile}
          style={[
            styles.inputText,
            {
              marginLeft: 'auto',
              borderColor: focusValues.mobile ? colors.blueColour : colors.lightGrey,
            },
          ]}
        />
      </View>
      {tgsEmailInputMandatory && (
        <>
          <View style={styles.email}>
            <TextInput
              style={[
                styles.emailText,
                { borderColor: focusValues.email ? colors.blueColour : colors.lightGrey },
              ]}
              placeholder={NEW_PLACEHOLDERS.email}
              placeholderTextColor={colors.lightTextColor}
              onFocus={() => setFocusVal('email', true)}
              onBlur={() => setFocusVal('email', false)}
              onChangeText={(text: string) => onChangeHandler('email', text)}
              value={travellerFields.email}
            />
          </View>
        </>
      )}
      {error.length !== 0 && displayError && (
        <View style={travellerDetailsStyles.errorContainer}>
          <Text style={travellerDetailsStyles.error}>{error}</Text>
        </View>
      )}
    </View>
  );
};

export default NewTravellerDetailsContainer;
