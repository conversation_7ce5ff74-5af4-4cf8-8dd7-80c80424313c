/* eslint-disable */
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    outerContainer: {
       backgroundColor: colors.white,
    },
    container: {
        height: 'auto',
        marginHorizontal: '5.55%',
    },
    ticketFareContainer: {
        marginTop: 16,
        height: 92,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: colors.lightSilver,
        paddingHorizontal: 12,
        paddingVertical: 10,
    },
    ticketFareBox: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    ticketFareText: {
        color: '#303030',
    },
    ticketRefund: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    ticketRefundAmt: {
        color: colors.lightGreen4,
    },
    greenTickImg: {
        height: 14,
        width: 14,
        marginLeft: 5,
    },
    ticketRefundText: {
        color: '#303030',
        marginTop: 6,
    },
    availTgContainer: {
        marginTop: 8,
        backgroundColor: '#CFFFEB',
        borderRadius: 8,
        height: 24,
        justifyContent: 'center',
        alignItems: 'center',
    },
    availTgText: {
        color: '#059B5C',
    },
    refundQ: {
        marginTop:14,
    },
    refundAContainer: {
        marginTop: 8,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    refundA: {
        borderRadius: 12,
        borderWidth: 1,
        borderColor: colors.lightSilver,
        height: 65,
        width: '48%',
        paddingHorizontal: 8,
        paddingVertical: 8,
    },
    refundText1xstyle: {
        color: colors.textGrey,
    },
    refundTextDesc: {
        color: colors.textGrey,
        marginTop: 6,
    },
    tncContainer: {
        marginTop: 12,
        marginHorizontal: '3.1%',
    },
    tncContainerDesc: {
        color: '#858585',
        textAlign: 'center',
    },
    tncContainerLink: {
        color: colors.azure,
    },
    feedbackHeading: {
        color: colors.black,
        marginTop: 16,
    },
    feedbackContainer: {
        marginTop: 12,
        width: '100%',
        height: 84,
        marginLeft: '5.55%',
        paddingRight: '6%',
        marginBottom: 16,
    },
    feedbackItem: {
        width: 212,
        height: '100%',
        backgroundColor: colors.grayBg,
        marginRight: 10,
        borderRadius: 12,
        paddingHorizontal: 10,
        paddingVertical: 10,
    },
    testimonalHeadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    testimonalHeadingText: {
        marginLeft: 10,
        color: colors.textGrey,
    },
    testimonalImg: {
        height: 24,
        width: 24,
    },
    testimonalContent: {
        color: colors.textGrey,
    },
    seperator: {
        width: '100%',
        height: 10,
        backgroundColor: colors.grayBg,
    },
    tgBannerImg: {
        width: '100%',
        height: 130,
    },
});

export default styles;
