import React from 'react';
import { View, Image, Text } from 'react-native';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import { configKeyMapping } from '../../TGSUtils/Constants';
import { tgsTripGuaranteeComponentStyles } from './TGSContainer.styles';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { DISCOUNT_PLACEHOLDER } from 'apps/rails/src/Utils/RailsConstant';
import PropTypes from 'prop-types';


const tgIcon = ASSETS.tgsLandingIcon;

const TGSTripGuaranteeComponent = ({ tgsTextData,tgPremiumAmount }) => {
    let priceTag = tgsTextData?.[configKeyMapping.Price_Tag];
  priceTag = priceTag.replace(DISCOUNT_PLACEHOLDER, tgPremiumAmount);
    return (<View style={tgsTripGuaranteeComponentStyles.container}>
        <View style={tgsTripGuaranteeComponentStyles.flexDirection}>
            <Image source={tgIcon} style={tgsTripGuaranteeComponentStyles.image} resizeMode="contain"/>
            <Text style={[tgsTripGuaranteeComponentStyles.heading, fontStyle('bold')]}>{tgsTextData?.[configKeyMapping.TG_Headline]}</Text>
            <Text style={[tgsTripGuaranteeComponentStyles.ruppeText, fontStyle('regular')]}>{priceTag}</Text>
        </View>
        <View>
            <Text style={[tgsTripGuaranteeComponentStyles.text, fontStyle('regular')]}>
                {tgsTextData?.[configKeyMapping.TG_Headline_Subtask]}
            </Text>
        </View>
    </View>);
};

TGSTripGuaranteeComponent.propTypes = {
    tgsTextData: PropTypes.object,
    tgPremiumAmount: PropTypes.number,
};

export default TGSTripGuaranteeComponent;
