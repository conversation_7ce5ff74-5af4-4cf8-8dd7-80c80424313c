import React from 'react';
import {
    View,
    StyleSheet,
    Text,
    TouchableOpacity,
    Image,
} from 'react-native';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import { tgsComponentStyles } from './TGSContainer.styles';
import TgsRefundContainer from './TgsRefundContainer';
import SpendRefundToBook from '../../../../TravelerDetails/Components/AssuredConfirmation/SpendRefundToBook';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from '../../../../../RisAnalytics/RisAnalytics';
import { splitText, omnitureMapping, parseTGSResponse, configKeyMapping,refundWaysTextStyle } from '../../TGSUtils/Constants';
import { refundWays, getTextStyle } from '../../../../TravelerDetails/Components/AssuredConfirmation/AssuredConfirmationUtils';
import HTMLView from 'react-native-htmlview';
import LinearGradient from 'react-native-linear-gradient';
import TGSTripGuaranteeComponent from './TGSTripGuaranteeComponent';
import { _label } from '../../../../../vernacular/AppLanguage';
import { TGSRISPageDetailsProps } from '../../TGSCommonInterfaces';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import { tgsLogin } from '../NewPnrStatusPage';
import { DISCOUNT_PLACEHOLDER } from 'apps/rails/src/Utils/RailsConstant';

import cancel from '@mmt/legacy-assets/src/TGS/cancel.webp';
import illustration from '@mmt/legacy-assets/src/TGS/Illustration_Placeholder.webp';

import tgIcon from '@mmt/legacy-assets/src/rails/TG_icon.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

/* eslint-disable */
export const TgsAvailabilityBanner = ({ availabilityContainer,refundWaysIcon }) => {
    return (
        <View>
            <View style={tgsComponentStyles.tgsWidetViewCss}>
                <View style={tgsComponentStyles.tgsWidgetCss}>
                    <Image source={tgIcon}  style={tgsComponentStyles.tgsWidgetImage} />
                </View>
                <View style={tgsComponentStyles.flexBasisEigthy}>
                    {availabilityContainer.availabilityText.split('|').map((x, idx) => (<HTMLView key={idx}  value={`<p>${x}</p>`} stylesheet={StyleSheet.create(JSON.parse(availabilityContainer.availabilityStyleSheet))} />))}
                </View>
            </View>
            {refundWaysIcon &&
                <View style={tgsComponentStyles.spendRefundToBookCss}>
                    {refundWays.map((item, index) => {
                        return <SpendRefundToBook
                            icon={item.icon}
                            text={_label(item.text)}
                            dot={item.dot}
                            key={index}
                            width={item.width}
                            height={item.height}
                            refundWaysTextStyle={[...getTextStyle(refundWaysTextStyle)]}
                        />;
                    })}
                </View>
            }
        </View>);
};

const TGSComponent = ({ 
    tgsEligibility: { 
    containerBg, pnrContainer, buttonContainer, 
    paymentContainer,tgPremiumAmount,
    totalVoucherAmount,totalTicketFare 
}, userLoggedIn, pnrNumber, bottomSheet, onClose, tgsTextData }: TGSRISPageDetailsProps) => {
    const [refundAmountText, refundAmountValue] = splitText(paymentContainer.refundAmount);
    const [totalTicketAmountText, totalTicketAmountValue] = splitText(paymentContainer.totalTicketAmount);

    const pnrContainerEligibilityStyleSheet = JSON.parse(pnrContainer.ticketEligibilityStyleSheet);
    pnrContainerEligibilityStyleSheet.p.textAlign = 'center';

    let ctaText = tgsTextData?.[configKeyMapping.CTA];
  ctaText = ctaText.replace(DISCOUNT_PLACEHOLDER, tgPremiumAmount);

    const onSubmit = () => {
        if (bottomSheet) {
            onClose();
        }
        trackClickEventProp61(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGSWIDGET_ON_PNR_PAGE_CLICKED);
        tgsLogin(pnrNumber,totalVoucherAmount,totalTicketFare,userLoggedIn);
    };

    const totalTicketAmountStyleSheet = parseTGSResponse(JSON.parse(paymentContainer.totalTicketAmountStyleSheet));
    if (totalTicketAmountStyleSheet?.s?.color) {
        totalTicketAmountStyleSheet.s.color = 'black';
    }

    const refundAmountStyleSheet = parseTGSResponse(JSON.parse(paymentContainer.refundAmountStyleSheet));
    if (refundAmountStyleSheet?.p?.color) {
        refundAmountStyleSheet.p.color = 'black';
    }

    return (
        <View style={{ backgroundColor: bottomSheet ? colors.cardBorderColor : colors.white, marginTop: bottomSheet ? 0 : 9, height: bottomSheet ? 480 : 'auto' }}>
            <LinearGradient
                useAngle={true} angle={350.44}
                colors={containerBg}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 0.0 }}
                style={[tgsComponentStyles.gradientContainer, {  marginBottom:bottomSheet ? 0 : 10,width: bottomSheet ? '100%' : '96%', marginTop: bottomSheet ? 0 : 5 }]}
            >
                {bottomSheet &&
                    <View>
                        <TouchableRipple style={{ height: 25, width: 25, position: 'absolute', top: 10, left: '90%' }} onPress={onClose}>
                            <Image source={cancel} style={{ height: 25, width: 25, position: 'absolute', top: 10, left: '90%' }} />
                        </TouchableRipple>
                        <Image source={illustration} style={{ height: 110, width: 110, bottom: '70%', left: '4%' }} />
                    </View>}
                <View style={[tgsComponentStyles.container, { marginTop: bottomSheet ? -50 : 0 }]}>

                    <View style={tgsComponentStyles.pnrContainer}>
                        <Text style={[{ color: colors.white }, fontStyle('regular')]}>PNR {pnrNumber}</Text>
                        <LinearGradient
                            style={{ padding: 3, borderRadius: 4, marginLeft: 4 }}
                            colors={['#f3d452', '#f09819']}
                            start={{ x: 0.0, y: 0.0 }}
                            end={{ x: 1.0, y: 1.0 }}
                        >
                            <Text style={[{ color: colors.white, fontSize: 12 }, fontStyle('bold')]}>{tgsTextData?.[configKeyMapping.WL_Tag]}</Text>
                        </LinearGradient>
                    </View>
                    <View>
                        <TGSTripGuaranteeComponent tgsTextData={tgsTextData} tgPremiumAmount={tgPremiumAmount} />
                        <TgsRefundContainer tgsTextData={tgsTextData} />
                        <View style={[tgsComponentStyles.paymentInfo, { backgroundColor: colors.lightWhite2 }]}>
                            <View style={tgsComponentStyles.totalTicketAmountTextCss}>
                                <View>
                                    <HTMLView value={totalTicketAmountText} 
                                    stylesheet={StyleSheet.create(parseTGSResponse(totalTicketAmountStyleSheet))} />
                                </View>
                                <View>
                                    <HTMLView value={totalTicketAmountValue} 
                                    stylesheet={StyleSheet.create(parseTGSResponse(totalTicketAmountStyleSheet))} />
                                </View>
                            </View>
                            <View style={tgsComponentStyles.flexDirectionRow}>
                                <View>
                                    <HTMLView value={refundAmountText} 
                                    stylesheet={StyleSheet.create(refundAmountStyleSheet)} />
                                </View>
                                <View>
                                    <HTMLView value={refundAmountValue} 
                                    stylesheet={StyleSheet.create(refundAmountStyleSheet)} />
                                </View>
                            </View>
                        </View>
                        <View style={tgsComponentStyles.buttonContainer}>
                            <TouchableOpacity onPress={() => onSubmit()} activeOpacity={0.7}>
                                <LinearGradient
                                    start={{
                                        x: 0.0,
                                        y: 0.0,
                                    }}
                                    end={{
                                        x: 0.0,
                                        y: 1.0,
                                    }}
                                    colors={buttonContainer.buttonColor}
                                    style={tgsComponentStyles.textContainer}// button radius:5
                                >
                                    <Text style={[tgsComponentStyles.buttonText, { color: buttonContainer.buttonTextColor }, fontStyle('bold')]}>{ctaText}</Text>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </LinearGradient>
        </View>);
};

export default TGSComponent;
