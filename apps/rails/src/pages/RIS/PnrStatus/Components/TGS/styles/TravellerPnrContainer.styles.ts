import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: '4.44%',
        paddingVertical: 16,

    },
    pnrHeading: {
        color: colors.black,
        marginBottom: 16,
    },
    passengersContainer: {
        width: '100%',
        borderRadius: 16,
        paddingHorizontal: '4.88%',
        borderWidth: 1,
        borderColor: colors.lightSilver,
    },
    passengerDetails: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingTop: 14,
    },
    passengerText: {
        color: colors.black,
    },
    lineSeperator: {
        width: '100%',
        height: 1,
        backgroundColor: colors.grayBg,
        marginTop: 14,
    },
    showMoreContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    showMoreText: {
        color: colors.primary,
        marginVertical: 14,
    },
    showMoreImg: {
        width: 15,
        height: 17,
    },
});

export default styles;
