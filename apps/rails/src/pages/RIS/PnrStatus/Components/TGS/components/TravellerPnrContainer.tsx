import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import styles from '../styles/TravellerPnrContainer.styles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { HIDE, omnitureMapping, PASSENGER, PNR_STATUS, SEE_ALL_PASSENGERS } from '../../../TGSUtils/Constants';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import downArrow from '@mmt/legacy-assets/src/ic_arrow_blue_down.webp';
import upArrow from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import PropTypes from 'prop-types';

function TravellerPnrContainer({ travellerPassengerDetails }) {
  const [showAll, setShowAll] = useState(false);
  const paxCount = travellerPassengerDetails.length;
  const numOfPaxToBeShown = showAll ? paxCount : 2;
  const seePassengersObj = {
    img: downArrow,
    text: SEE_ALL_PASSENGERS,
  };
  const hidePassengersObj = {
    img: upArrow,
    text: HIDE,
  };
  const [textShown, setTextShown] = useState(seePassengersObj);

  const toggleShowAll = () => {
    if (!showAll) {
      trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE, omnitureMapping.TGS_TRAVELLER_SEE_MORE_PAX_CLICKED);
    }
    showAll ? setTextShown(seePassengersObj) : setTextShown(hidePassengersObj);
    setShowAll(!showAll);
  };

  const showlineSeperator = (index: number) => {
    return (index === 0 || (index === 1 && paxCount === 2) || index !== numOfPaxToBeShown - 1);
  };

  const PassengerDetails = [
    travellerPassengerDetails?.slice(0, numOfPaxToBeShown)?.map((value, index) => (
      <View key = {index} >
        <View style={styles.passengerDetails}>
          <Text style={[styles.passengerText, getLineHeight(14), fontStyle('black')]}>
            {`${PASSENGER} ${index + 1}`}
          </Text>
          <Text style={[styles.passengerText, getLineHeight(14), fontStyle('black')]}>
            {value.ticketStatus}
          </Text>
        </View>
        {showlineSeperator(index) && <View style={styles.lineSeperator} />}
      </View>
    )),
  ];

  return (
    <View style={styles.container}>
      <Text style={[styles.pnrHeading, getLineHeight(20), fontStyle('black')]}>{PNR_STATUS}</Text>
      <View style={styles.passengersContainer}>
        {PassengerDetails}
        {paxCount > 2 && (
          <TouchableOpacity style={styles.showMoreContainer} onPress={toggleShowAll}>
            <Text
              style={[styles.showMoreText, getLineHeight(12), fontStyle('bold')]}
              >
              {textShown.text}
            </Text>
            <Image source={textShown.img} resizeMode="contain" style={styles.showMoreImg}/>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

TravellerPnrContainer.propTypes = {
  travellerPassengerDetails: PropTypes.array,
};

export default TravellerPnrContainer;
