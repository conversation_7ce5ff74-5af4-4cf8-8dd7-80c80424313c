import React , {useState, useEffect} from 'react';
import {
    View,
    Text,
    Linking,
    TouchableOpacity,
    TouchableWithoutFeedback,
    ScrollView,
    BackHandler,
    Image,
    Platform,
  } from 'react-native';
  import {tgsTrackPageDetails} from 'apps/rails/src/Utils/RisUtils';
import TGSThankyouShareComponent from './TGSThankyouShareComponent';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { getTGSWhatsappTag} from 'apps/rails/src/RailsAbConfig';
  import shareContents from '@mmt/legacy-commons/Common/utils/ShareUtils';
  import { trackTGSInEligibility, trackPageVisits, RIS_TGS_THANK_YOU_SUCCESS_PAGE } from '../../../../../RisAnalytics/RisAnalytics';
  import * as Clipboard from 'react-native/Libraries/Components/Clipboard/Clipboard';
  import {thankyouPageStyles} from './TGSContainer.styles';
  import Disclaimer from '../../../TrainSchedule/Components/Disclaimer';
  import { Actions } from '../../../../../navigation/index';
  import { PAGE_NOT_LOADING,  getDataFromAsynStorage, UNIQUE_PNR_PAGENAMES} from '../../../../../Utils/RailsConstant';
  import {TGS_MMT_ID} from  'apps/rails/src/pages/TGS/Components/TGSConstants';
  import { ErrorContainer } from '../../../../RailsLandingPage/Pages/ErrorContainer';
  import TouchableRipple from '@mmt/ui/components/touchableRipple';
import Loader from '../../../../Common/Loader';
import LinearGradient from 'react-native-linear-gradient';
import { tgsThankYouAPIRequestPromise,tgsAPIRequest, trackPnrDetails } from '../../TGSUtils/TGSHelper';
import _isEmpty from 'lodash/isEmpty';
import Toast from '@mmt/legacy-commons/Common/Components/Toast2';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import {
    BOOKING_ID,
    MANAGE_BOOKING,
    PNR,
    omnitureMapping,
    errorResponse,
    disclaimer,
    BUTTON_TEXT,
    TELL_YOUR_FRIENDS,
    SHARE_WITH_FRIENDS,
    PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY,
} from '../../TGSUtils/Constants';
import {
    CopyIcon,
    BagsIcon,
    TrainsIcon,
} from '../../TGSUtils/TGSAssets';
import RisOptions from './RisOptions';
import {ThankYouPageDetails , bookingIdProps} from '../../TGSCommonInterfaces';
import {VoucherCard , TermsAndConditions} from './TgsPnrThankyouComponents';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import BookedBackIcon from '../TGSImageComponents/BookedBackIcon';
import TGSSearchButton from '../../../../TGS/Components/TGSSearchButton';
import { getDataFromStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import closeIcon from '@mmt/legacy-assets/src/close-black.webp';
import BottomSheetModalTrain from '@mmt/rails/src/pages/Common/BottomSheetModalTrain';

/* eslint-disable */

const copyId = (id:string) => {
    Clipboard.setString(id);
    trackClickEventProp61(omnitureMapping.TGS_THANKYOU_SUCCESS,omnitureMapping.TGS_THANK_YOU_PAGE_COPY_CLICKED);
    Toast.show('Booking ID Copied to Clipboard');
  };

const BookingIdText = ({bookingID}:bookingIdProps) =>{
    const booking_id = <Text style={thankyouPageStyles.bookingIdText}>{bookingID}</Text>;
    return (
        <Text style={[thankyouPageStyles.bookingId,fontStyle('regular')]}>{BOOKING_ID} <Text style={[thankyouPageStyles.bookingId,fontStyle('bold')]}>{booking_id} </Text></Text>
    );
};

const whatsappShareBanner = ASSETS.tgsShare;

export const onwhatsAppSubmit = async (message: string) => {
    const whatsAppUrl = `whatsapp://send/?text=${message}`;
    return Linking.canOpenURL(whatsAppUrl)
        .then((supported) => {
            if (!supported) {
                shareContents('TGS Share Message', `${message}`, null);

            }
            return Linking.openURL(whatsAppUrl);
        });

};

const TgsPnrThankYou = (props) =>{
    const [response, setResponse] = useState({});
    const [error, setError] = useState({});
    const [retryCount, setRetryCount] = useState(0);
    const [showModal, setBottomModal] = useState(false);
    const [whatsAppStatus, setWhatsAppStatus] = useState(false);

    const onHomeIconClicked = () => {
        Actions.railNewLanding({type: 'replace'});
        return true;
    };
  const whatsAppMessage = useConfigStore(configKeys.RAILS_TGS_WHATSAPP_MESSAGE);

    useEffect(() =>{
        const backHandler = BackHandler.addEventListener('hardwareBackPress', onHomeIconClicked);
        return () => backHandler.remove();
    },[]);

    useEffect(() => {
        (async () => {
            const whatsAppEnable = await getTGSWhatsappTag();
            if (whatsAppEnable) {
                setWhatsAppStatus(whatsAppEnable);
                setTimeout(() => { setBottomModal(true); }, 1000);
                trackTGSInEligibility(omnitureMapping.TGS_THANKYOU_SUCCESS, omnitureMapping.TGS_THANKYOU_BOOTOMSHEET);
            }
        })();
    }, []);


    useEffect(() => {
        async function apiCall() {
            let mmtBookingId = '';
            if (props?.mmtIdNumber) {
                mmtBookingId = props.mmtIdNumber;
            }
            else {
                if (Platform.OS === 'ios') {
                    mmtBookingId = await getDataFromAsynStorage(TGS_MMT_ID);
                }
                else if (props?.data) {
                    const paymentResponse = JSON.parse(props?.data?.PAYMENT_RESPONSE_VO);
                    mmtBookingId = paymentResponse?.bookingId;
                }
            }
            const res = await tgsAPIRequest(tgsThankYouAPIRequestPromise(mmtBookingId));
            if (!res.error)
            {
                tgsTrackPageDetails(omnitureMapping.TGS_THANKYOU_SUCCESS,UNIQUE_PNR_PAGENAMES.TGS_THANK_YOU);
                tgsTrackPageDetails(omnitureMapping.TGS_THANKYOU_SUCCESS_UNIQUE,UNIQUE_PNR_PAGENAMES.TGS_THANK_YOU);
            }
            setResponse(res.response);
            if (!res.error) {
              const trackingDetails = await getDataFromStorage(PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY);
              trackPnrDetails(RIS_TGS_THANK_YOU_SUCCESS_PAGE, trackingDetails);
            }
            setError(res.error);
        }
        trackPageVisits(omnitureMapping.TGS_THANKYOU_SUCCESS);
        apiCall();
    }, [retryCount]);

    const refreshCallback = () => {
        setRetryCount((prev) => prev + 1);
    };

    const closeBottomSheetModal = () => {
        setBottomModal(false);
    };
    const whatsAppCTABottomsheet =  () => {
        trackClickEventProp61(
            omnitureMapping.TGS_THANKYOU_SUCCESS, 
            omnitureMapping.TGS_THANKYOU_SHARE_CLICKED_BOTTOMSHEET);
        onwhatsAppSubmit(whatsAppMessage);
    };

    const whatsAppCTA =  () => {
        trackClickEventProp61(omnitureMapping.TGS_THANKYOU_SUCCESS, omnitureMapping.TGS_THANKYOU_SHARE_CLICKED);
        onwhatsAppSubmit(whatsAppMessage);
    };

    const whatsAppSegment = () => {
        if (whatsAppStatus) {
            return (
                <>
                    <TGSThankyouShareComponent onShare={whatsAppCTA} />
                    <View style={thankyouPageStyles.separator} />
                </>);
        }
        return <></>;
    };

    if (!_isEmpty(error))
    {
        return (
            <ErrorContainer
                refreshCallback={() => refreshCallback()}
                errorResponsefromAPI={errorResponse}
                errorImg = {PAGE_NOT_LOADING}
                ctaPressed={onHomeIconClicked}
            />
        );
    }

    if (_isEmpty(response) || Object.keys(response).length === 0)
     {return <Loader />;}

    const {containerBg , tripGuaranteeHeader , pnrNumber , trainDateAndInfo , policyText, voucherCodeMessage
        , voucherCodeStyle ,refundText, termsAndConditions}:ThankYouPageDetails = response;

    return (
        <>
        <ScrollView>

                <LinearGradient start={{ x: 1.0, y: 0.0 }} end={{ x: 0.0, y: 1.0 }} colors={containerBg}>
                   <View style={thankyouPageStyles.homeContainer}>
                    <TouchableRipple onPress={onHomeIconClicked}>
                        <BookedBackIcon height={30} width={30} />
                    </TouchableRipple>
                    </View>
                <View style={thankyouPageStyles.flexDirectionRow}>
                    <View style = {thankyouPageStyles.trainsIconContainer}>
                        <TrainsIcon height={45} width={60} style={thankyouPageStyles.trainsIcon}/>
                    </View>
                </View>
                <View style={thankyouPageStyles.confirmationContainer}>
                    <Text style={thankyouPageStyles.headingText}>{tripGuaranteeHeader.tripGuaranteeText}</Text>
                    <View style={thankyouPageStyles.bookindIdContainer}>
                        <BookingIdText bookingID={tripGuaranteeHeader.bookingID}/>
                        <TouchableOpacity onPress={()=>copyId(tripGuaranteeHeader.bookingID)}>
                            <CopyIcon width={15} height={15} style={thankyouPageStyles.copyIcon}/>
                        </TouchableOpacity>
                    </View>
                    <View style={thankyouPageStyles.buttonContainer}>
                    <TouchableWithoutFeedback onPress={()=>{  
                        trackClickEventProp61(
                            omnitureMapping.TGS_THANKYOU_SUCCESS,
                            omnitureMapping.TGS_THANK_YOU_MANGE_BOOKING); 
                        Linking.openURL(tripGuaranteeHeader.manageBookingURL); }}>
                        <View style ={thankyouPageStyles.button}>
                            <Text style={thankyouPageStyles.blueText}>{MANAGE_BOOKING}</Text>
                        </View>
                    </TouchableWithoutFeedback>
                    </View>
                </View>
            </LinearGradient>

            <View style={thankyouPageStyles.pnrView} elevation={5}>
                <View style={thankyouPageStyles.pnrTextContainer}>
                    <Text style={[thankyouPageStyles.pnrText,{fontWeight:'700'}]}>{PNR} {pnrNumber}</Text>
                    <Text style={thankyouPageStyles.trainDateAndInfo}>{trainDateAndInfo.replace('|',' • ')}</Text>
                </View>
                <View>
                    <BagsIcon height={80} width={40}/>
                </View>
            </View>

            <View style={thankyouPageStyles.voucherView}>
                <View style={thankyouPageStyles.trainBanner}>
                    <View style={thankyouPageStyles.trainsIconContainerSmall}>
                        <TrainsIcon width={40} style={thankyouPageStyles.trainsIconSmall} />
                    </View>
                    <Text style={thankyouPageStyles.policyText}>{policyText}</Text>
                </View>
                <VoucherCard 
                  voucherMsg={voucherCodeMessage} 
                  voucherCodeStyle = {voucherCodeStyle} 
                  refundText={refundText}
                />
            </View>

            <TermsAndConditions termsAndConditions = {termsAndConditions}/>
            <View style={thankyouPageStyles.separator} />
           {whatsAppSegment()}
            <RisOptions/>
         <View>
         <Disclaimer disclaimer={disclaimer} />
       </View>
       </ScrollView>
            {
                showModal && <BottomSheetModalTrain onTouchOutside={closeBottomSheetModal} testID="tgs_thankyou_bottomsheet_modal">
                    <View style={thankyouPageStyles.bottomSheetContainer} testID="tgs_thankyou_bottomsheet_container">
                        <View style={thankyouPageStyles.headingContainer}>
                            <Text style={[fontStyle('bold'), thankyouPageStyles.subHeadingContainer]}>{SHARE_WITH_FRIENDS}</Text>
                            <TouchableOpacity style={thankyouPageStyles.imageContainer} onPress={closeBottomSheetModal} testID="tgs_thankyou_bottomsheet_close_button">
                                <Image source={closeIcon} style={thankyouPageStyles.image} />
                            </TouchableOpacity>
                        </View>
                        <Text style={[fontStyle('regular'), thankyouPageStyles.message]}>{TELL_YOUR_FRIENDS}</Text>
                        <Image source={whatsappShareBanner} resizeMode="contain" style={thankyouPageStyles.whatsAppBanner} />
                        <View style={thankyouPageStyles.whatsAppCTAButtonBottom}>
                            <TGSSearchButton 
                                whatsappIcon={whatsAppStatus} 
                                onPress={whatsAppCTABottomsheet} 
                                label={BUTTON_TEXT} 
                            />
                        </View>
                    </View>
                </BottomSheetModalTrain>
            }
       </>
    );
};

export default TgsPnrThankYou;
