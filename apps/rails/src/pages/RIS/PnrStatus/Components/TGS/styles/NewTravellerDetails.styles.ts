import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: '5.55%',
        paddingVertical: 16,
    },
    heading: {
        color: colors.black,
    },
    name: {
        flexDirection: 'row',
        marginTop: 16,
    },
    email: {
        marginTop: 16,
    },
    inputText: {
        width: '48%',
        height: 32,
        borderRadius: 8,
        borderWidth: 1,
        borderStyle: 'solid',
        paddingLeft: 8,
        paddingVertical: 0,
        backgroundColor: colors.smokeWhite,
        fontSize: 14,
        fontWeight: '600',
    },
    emailText: {
        height: 32,
        borderRadius: 8,
        borderWidth: 1,
        borderStyle: 'solid',
        paddingLeft: 8,
        paddingVertical: 0,
        backgroundColor: colors.smokeWhite,
        fontSize: 14,
        fontWeight: '600',
    },
});

export default styles;
