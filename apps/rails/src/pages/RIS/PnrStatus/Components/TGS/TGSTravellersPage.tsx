/* eslint-disable */
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    Image,
    BackHandler,
} from 'react-native';
import {tgsTrackPageDetails} from 'apps/rails/src/Utils/RisUtils';
import { getRailsTGSAppFunnelNativeOrMweb } from '../../../../../RailsAbConfig';
import PaymentModule from '@mmt/legacy-commons/Native/PaymentModule';
import {tgsTravellersPageStyles} from './TGSContainer.styles';
import { captureRef, scrollToErrorComponent, refs, COMPONENT_TRAVELER_PAGE_SCROLL_VIEW } from '../../../../TravelerDetails/TravelerDetailsActions';
import { Actions } from '../../../../../navigation/index';
import Loader from '../../../../Common/Loader';
import { TGSTravellersPageResponseProps } from '../../TGSCommonInterfaces';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { getPokusConfig, AbConfigKeyMappings } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import _isEmpty from 'lodash/isEmpty';
import { getContactDetailsFromLoggedInUser } from '@mmt/legacy-commons/Common/Components/ContactDetails/contactDetailsUtils';
import {
    refundWaysTextStyle, TICKET_STATUS, PASSENGER_NAME, TRAVELLER_PAGE,
    commonStyles, OTHERS, CONFIRM_AND_PAY, journeyDetailsKey,
    travellerDetailsKey, ticketDetailsKey, priceBreakUpKey, splitText,
    RADIO_BUTTON_1, IRCTC,
    omnitureMapping,
    ticketContainerError,
    errorResponse,
    TGS_Traveller_Container,
    TGS_Tickets_Container,
    TGS_PAYMENT_PAGE,
    TGS_T_AND_C,
    refund2xText,
    refund1xText,
    getTgFare,
    CONTINUE,
    PASSENGER,
    PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY,
} from '../../TGSUtils/Constants';
import HTMLView from 'react-native-htmlview';
import LinearGradient from 'react-native-linear-gradient';
import RailInfoPageHeader from '../../../../Common/RailInfoPageHeader';
import { importedStyles } from '../../../../RisPnrForm/Styles/index';
import { refundWays, getTextStyle } from '../../../../TravelerDetails/Components/AssuredConfirmation/AssuredConfirmationUtils';
import SpendRefundToBook from '../../../../TravelerDetails/Components/AssuredConfirmation/SpendRefundToBook';
import { _label  } from '../../../../../vernacular/AppLanguage';
import PNRWaitingLogo from '../TGSImageComponents/PNRWaitingLogo';
import TGSCheckmark from '../TGSImageComponents/TGSCheckmark';
import TravellerDetailsContainer from './TravellerDetailsContainer';
import PriceBreakupContainer from './PriceBreakupContainer';
import TGSJourneyContainer from './TGSJourneyContainer';
import TicketDetailsBottomSheet from './TicketDetailsBottomSheet';
import TicketDetailsContainer from './TicketDetailsContainer';
import BookNowButton from '../../../../Common/BookNow';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import {
    tgsTravellerAPIRequestPromise,
    tgsReviewAPIRequestPromise,
    tgsAPIRequest,
    getDateAndPnrNumberFromTravellerResponse,
    trackPnrDetails,
} from '../../TGSUtils/TGSHelper';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { ErrorContainer } from '../../../../RailsLandingPage/Pages/ErrorContainer';
import { isUniquePnrVisit, PAGE_NOT_LOADING, UNIQUE_PNR_PAGENAMES } from 'apps/rails/src/Utils/RailsConstant';
import {
  trackPageVisits,
  RIS_LANDING_PNR_TRAVELLERS_DETAILS_PAGE,
} from '../../../../../RisAnalytics/RisAnalytics';
import NewTravellerDetailsContainer from './NewTravellerDetailsContainer';
import NewTgsTravellersView from './NewTgsTravellersView';
import {getPNRPageConfig} from 'apps/rails/src/RailsAbConfig';
import { getDataFromStorage } from '@mmt/legacy-commons/AppState/LocalStorage';

const initialValue = {
    responseDefault: {},
    apiErrorDefault: '',
    dispayTicketDetailsDefault: false,
    platformValDefault: OTHERS,
    showLoaderDefault: false,
    radioButtonDefault: null,
    travellerFieldsDefault: {
        name: '',
        mobile: '',
        email: '',
    },
    errorDefault: '',
};

import tgIcon from '@mmt/legacy-assets/src/rails/TG_icon.webp';
import BottomSheetModalTrain from '@mmt/rails/src/pages/Common/BottomSheetModalTrain';

const OldTgsTravellersView = ( {response, components} ) => {

    const {
        containerBg,
        paymentContainer,
        refundContainer,
        tripGuaranteeBenefit,
        travellerPassengerDetails,
        tncContainer,
        order,
        bannerTag,
        availabilityContainer,
    } = response;

    const [paymentTicketFareText, paymentTicketFareValue] = splitText(paymentContainer?.ticketFare);
    const [refundText, refundValue] = splitText(paymentContainer?.refund);

    const tandCSubmit = () => {
        trackClickEventProp61(
          omnitureMapping.TGS_TRAVELLERS_PAGE,
          omnitureMapping.TGS_TRAVELLER_T_AND_C,
        );
        Actions.tgsTandCPageWebView();
    };

    const GetComponents = [
      order?.split('|')?.map((x: string) => {
        return components?.find((y: { key: string }) => y.key === x);
      }),
    ];

    return (
        <View>
            <LinearGradient
                    colors={containerBg}
                    start={{ x: 0.0, y: 0.0 }}
                    end={{ x: 1.0, y: 0.0 }}
                    style={tgsTravellersPageStyles.gradientContainer}
                >
                    <View style={tgsTravellersPageStyles.container}>
                        <View
                            style={[tgsTravellersPageStyles.bannerTag, { backgroundColor: bannerTag.backgroundColor }]}
                        >
                            <Text style={[fontStyle('bold'), tgsTravellersPageStyles.bannerTagTextStyle, JSON.parse(bannerTag.bannerTagTextStyle)]} >{bannerTag.bannerTagText}</Text>
                        </View>
                        <View>
                            <View style={tgsTravellersPageStyles.tgsWidgetContainer}>
                                <View style={tgsTravellersPageStyles.tgsWidgetCss}>
                                <Image source={tgIcon} style={tgsTravellersPageStyles.tgsWidgetImage} />
                                </View>
                                <View style={{ flexBasis: '80%' }}>
                                    <Text style={fontStyle('bold')}>
                                        {availabilityContainer.availabilityText.split('|').map((x: string, idx: number) => (<View key={idx} style={{ marginBottom: 3 }}><HTMLView value={`<p>${x}</p>`} stylesheet={StyleSheet.create(JSON.parse(availabilityContainer.availabilityStyleSheet))} /></View>))}
                                    </Text>
                                </View>
                            </View>
                            <View style={[tgsTravellersPageStyles.flexDirectionRow, { marginBottom: 10, justifyContent: 'space-evenly' }]}>
                                {refundWays.map((item, index) => {
                                    return <SpendRefundToBook
                                        icon={item.icon}
                                        text={_label(item.text)}
                                        dot={item.dot}
                                        key={index}
                                        width={item.width}
                                        height={item.height}
                                        refundWaysTextStyle={[...getTextStyle(refundWaysTextStyle)]}
                                    />;
                                })}
                            </View>
                        </View>
                    </View>
                </LinearGradient>
            <View style={commonStyles.genericContainer}>
                {travellerPassengerDetails?.length > 0 &&
                    <View style={tgsTravellersPageStyles.passengerContainer}>
                        <View style={tgsTravellersPageStyles.table}>
                            <Text style={tgsTravellersPageStyles.passengerContainerText}>
                                {PASSENGER_NAME}
                            </Text>
                            <Text style={[tgsTravellersPageStyles.passengerContainerText, { flexBasis: '50%' }]}>
                                {TICKET_STATUS}
                            </Text>
                        </View>
                        {travellerPassengerDetails.map((x, idx: number) => (
                            <View key={idx} style={tgsTravellersPageStyles.table}>
                                <Text style={tgsTravellersPageStyles.passengerText}>
                                   {`${PASSENGER} ${idx + 1}`}
                                </Text>
                                <View style={tgsTravellersPageStyles.passengerTextContainer}>
                                    <Text style={tgsTravellersPageStyles.passengerText}>
                                        {x.ticketStatus}
                                    </Text>
                                    <PNRWaitingLogo />
                                </View>
                            </View>
                        ))}
                    </View>
                }
                <View style={[tgsTravellersPageStyles.tripGuaranteeBenefit, 
                    { backgroundColor: tripGuaranteeBenefit.backgroundColor }]}>
                    <HTMLView value={tripGuaranteeBenefit.tripGuaranteeBenefitText} 
                    stylesheet={StyleSheet.create(JSON.parse(tripGuaranteeBenefit.tripGuaranteeBenefitStyle))} />
                </View>
                <View style={[tgsTravellersPageStyles.paymentInfo, 
                    { backgroundColor: paymentContainer.backgroundColor }]}>
                    <View style={tgsTravellersPageStyles.paymentTicketFare}>
                        <View>
                            <HTMLView value={paymentTicketFareText} 
                            stylesheet={StyleSheet.create(JSON.parse(paymentContainer.ticketFareStyleSheet))} />
                        </View>
                        <View>
                            <HTMLView value={paymentTicketFareValue} 
                            stylesheet={StyleSheet.create(JSON.parse(paymentContainer.ticketFareStyleSheet))} />
                        </View>
                    </View>
                    <View style={tgsTravellersPageStyles.flexDirectionRow}>
                        <View>
                            <HTMLView value={refundText}
                            stylesheet={StyleSheet.create(JSON.parse(paymentContainer.refundStyleSheet))} />
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                            <HTMLView  value={refundValue} 
                            stylesheet={StyleSheet.create(JSON.parse(paymentContainer.refundStyleSheet))} />
                            <TGSCheckmark style={{ marginLeft: 3 }} />
                        </View>
                    </View>
                </View>
                <HTMLView style={tgsTravellersPageStyles.refundText3x} 
                value={refundContainer.refundText3x} 
                stylesheet={StyleSheet.create(JSON.parse(refundContainer.refundStyle3x))} />
                <View style={{ flexDirection: 'row' }}>
                    <View style={[tgsTravellersPageStyles.refundContainer, { marginRight: 'auto' }]}>
                        <LinearGradient
                            colors={containerBg}
                            start={{ x: 0.0, y: 0.0 }}
                            end={{ x: 1.0, y: 0.0 }}
                            style={tgsTravellersPageStyles.refundContainerGradient}
                        >
                            <Text style={[tgsTravellersPageStyles.refundText1xstyle, fontStyle('black')]}>{refund1xText}</Text>
                        </LinearGradient>
                        <Text style={[tgsTravellersPageStyles.refundContainerText, fontStyle('regular')]}>{refundContainer.refundText1x} </Text>
                    </View>
                    <View style={tgsTravellersPageStyles.refundContainer}>
                        <LinearGradient
                            colors={containerBg}
                            start={{ x: 0.0, y: 0.0 }}
                            end={{ x: 1.0, y: 0.0 }}
                            style={tgsTravellersPageStyles.refundContainerGradient}
                        >
                            <Text style={[tgsTravellersPageStyles.refundText2xstyle, fontStyle('black')]}>{refund2xText}</Text>
                        </LinearGradient>
                        <Text style={[tgsTravellersPageStyles.refundContainerText, fontStyle('regular')]}>{refundContainer.refundText2x} </Text>
                    </View>
                </View>
                <View style={{ marginTop: 15 }}>
                    <Text style={{
                        color: '#858585',
                        textAlign: 'center',
                    }}>
                        {tncContainer.tncContainerText}
                        <Text style={[tgsTravellersPageStyles.tncContainerLink, fontStyle('bold')]}
                            onPress={() => tandCSubmit()} >{TGS_T_AND_C}</Text>
                    </Text>
                </View>
            </View>
            {GetComponents}
        </View>

    );
};


const TGSTravellersPage = ({ captureRef, pnrNumber, deeplink,totalVoucherAmount,totalTicketFare }) => {
    const {
        responseDefault,
        apiErrorDefault,
        dispayTicketDetailsDefault,
        platformValDefault,
        showLoaderDefault,
        radioButtonDefault,
        travellerFieldsDefault,
        errorDefault,
    } = initialValue;

    const [response, setResponse] = useState(responseDefault);
    const [apiError, setApiError] = useState(apiErrorDefault);
    const [reviewApiError, setReviewApiError] = useState(apiErrorDefault);
    const [dispayTicketDetails, setDispayTicketDetails] = useState<boolean>(dispayTicketDetailsDefault);
    const [platformVal, setPlatformVal] = useState(platformValDefault);
    const [showLoader, setShowLoader] = useState<boolean>(showLoaderDefault);
    const [radioButton, setRadioButton] = useState(radioButtonDefault);
    const [travellerFields, setTravellerFields] = useState(travellerFieldsDefault);
    const [error, setError] = useState(errorDefault);
    const [retryCount, setRetryCount] = useState(0);

    const isOldTgsPage = getPNRPageConfig();

    const tgsBookingSource = getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.tgsBookingSource,
        false,
    );

    const tgsEmailInputMandatory = getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.tgsEmailInput,
        false,
    );

    useEffect(() => {
        async function apiCall() {
            setResponse(responseDefault);
            setApiError(apiErrorDefault);
            setReviewApiError(apiErrorDefault);
            setDispayTicketDetails(dispayTicketDetailsDefault);
            setPlatformVal(platformValDefault);
            setShowLoader(showLoaderDefault);
            setRadioButton(radioButtonDefault);
            setTravellerFields(travellerFieldsDefault);
            setError(errorDefault);

            const res = await tgsAPIRequest(tgsTravellerAPIRequestPromise(pnrNumber));
            setResponse(res.response);
            const userLogedData = await getContactDetailsFromLoggedInUser();
            if (userLogedData !== null)
                {setTravellerFields((prev) => ({ ...prev, ...userLogedData }));}
            if (res.error !== null) {
                setApiError(res.error);
            } else {
                const trackingDetails = await getDataFromStorage(PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY);
                trackPnrDetails(RIS_LANDING_PNR_TRAVELLERS_DETAILS_PAGE, trackingDetails);
                const {pnrNumber, formattedJourneyDate} = getDateAndPnrNumberFromTravellerResponse(res);
                const isNewUniqueVisit = 
                    await isUniquePnrVisit(UNIQUE_PNR_PAGENAMES.TGS_TRAVELLER, pnrNumber, formattedJourneyDate);
                if (isNewUniqueVisit) {
                    if (isOldTgsPage) {
                        tgsTrackPageDetails(omnitureMapping.TGS_TRAVELLERS_PAGE_UNIQUE, 
                            UNIQUE_PNR_PAGENAMES.TGS_TRAVELLER);
                    }
                    else {
                        tgsTrackPageDetails(omnitureMapping.TGS_TRAVELLERS_PAGE_UNIQUE, 
                            UNIQUE_PNR_PAGENAMES.NEW_TGS_TRAVELLER);
                    }
                }
            }
        }
        trackPageVisits(omnitureMapping.TGS_TRAVELLERS_PAGE);
        apiCall();
    }, [retryCount,isOldTgsPage]);

    const onBackClick = () => {
        trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE, omnitureMapping.TGS_TRAVELLER_BACK_BUTTON_CLICKED);
        if (deeplink) {
          Actions.railsPNRStatusLanding({ deeplink: deeplink});
        } else {
          Actions.pop();
        }
        return true;
    };

    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', onBackClick);
        return () => {
            BackHandler.removeEventListener('hardwareBackPress', onBackClick);
        };
    }, []);

    useEffect(() => {
        async function trackTGSPage() {
            if (isOldTgsPage) {
                tgsTrackPageDetails(omnitureMapping.TGS_TRAVELLERS_PAGE,UNIQUE_PNR_PAGENAMES.TGS_TRAVELLER);
            }
            else {
                tgsTrackPageDetails(omnitureMapping.TGS_TRAVELLERS_PAGE,UNIQUE_PNR_PAGENAMES.NEW_TGS_TRAVELLER);
            }
        }
        trackTGSPage();
    }, [isOldTgsPage]);

    const refreshCallback = () => {
        setRetryCount((prev) => prev + 1);
    };

    if (reviewApiError?.length !== 0) {
        return (
            <ErrorContainer
                refreshCallback={refreshCallback}
                errorResponsefromAPI={errorResponse}
                errorImg={PAGE_NOT_LOADING}
                ctaPressed={() => setReviewApiError(apiErrorDefault)}
            />
        );
    }

    if (apiError?.length !== 0) {
        return (
            <ErrorContainer
                refreshCallback={refreshCallback}
                errorResponsefromAPI={errorResponse}
                errorImg={PAGE_NOT_LOADING}
                ctaPressed={onBackClick}
            />
        );
    }

    if (_isEmpty(response) || Object.keys(response).length === 0)
        {return <Loader />;}

    const setModalVal = (val: boolean) => {
        setDispayTicketDetails(val);
    };

    const tgsThankyouCallback = (url) => {
        const mmtNumber = url.split('?mmtId=');
        Actions.tgsThankyouPage({ mmtIdNumber: mmtNumber[1] });
    };


    const {
        sourceDestinationAndDateInfo,
        journeyDetailsContainer,
        priceBreakUpContainer,
    }: TGSTravellersPageResponseProps = response;

    const components = [
        <TravellerDetailsContainer
            captureRef={captureRef}
            key={travellerDetailsKey}
            tgsEmailInputMandatory={tgsEmailInputMandatory}
            travellerFields={travellerFields}
            setTravellerFields={setTravellerFields}
            setError={setError}
            error={error} />,
        <TGSJourneyContainer key={journeyDetailsKey} journeyDetailsContainer={journeyDetailsContainer} />,
        <PriceBreakupContainer key={priceBreakUpKey} priceBreakUpContainer={priceBreakUpContainer} />,
    ];

    const NewTravellerComponent = [
        <NewTravellerDetailsContainer
            captureRef={captureRef}
            key={travellerDetailsKey}
            tgsEmailInputMandatory={tgsEmailInputMandatory}
            travellerFields={travellerFields}
            setTravellerFields={setTravellerFields}
            setError={setError}
            error={error} />,
    ];

    if (tgsBookingSource) {
        components.push(<TicketDetailsContainer
            key={ticketDetailsKey}
            captureRef={captureRef}
            platformVal={platformVal}
            setModalVal={setModalVal}
            setRadioButton={setRadioButton}
            radioButton={radioButton} />);
    }

    const onClickBooking = async () => {
        trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE, omnitureMapping.TGS_CONFIRM_PAY_CLICKED);
        if (error.length !== 0) {
            showShortToast(_label('fill_valid_traveller'));
            scrollToErrorComponent(TGS_Traveller_Container, refs);
        }
        else if (tgsBookingSource && !radioButton) {
            showShortToast(ticketContainerError);
            scrollToErrorComponent(TGS_Tickets_Container, refs);
        }
        else {
            const bookingSource = radioButton === RADIO_BUTTON_1 ? IRCTC : platformVal;
            const requestDetails = {
                mobileNumber: travellerFields.mobile,
                bookerName: travellerFields.name,
                bookingSource: bookingSource,
            };
            if (tgsEmailInputMandatory) {
                requestDetails.email = travellerFields.email;
            }
            setShowLoader(true);
            const res = await tgsAPIRequest(tgsReviewAPIRequestPromise(requestDetails,pnrNumber));
            setShowLoader(false);
            if (res?.error === null) {
                const railsTGSApp = getRailsTGSAppFunnelNativeOrMweb();
                if (railsTGSApp === 2) {
                    const response = res.response;
                    response.totalCollectibleAmount = 0;
                    const paymentTGS = {
                        lob: 'TGS', // DO NOT CHANGE THIS
                        request: {},
                        response: response,
                        bookingSummaryInfo: {},
                        backBehaviourConfig: undefined,
                    };
                    PaymentModule.openPaymentPage(paymentTGS);
                }
                else {
                    Actions.openTGSWebView({
                        tgsThankyouPageCallback: (url) => tgsThankyouCallback(url),
                        url: res.response.paymentInfo.checkoutUrl,
                        headerIcon: require('@mmt/legacy-assets/src/white_backarrow.webp'),
                        headerText: `${TGS_PAYMENT_PAGE}`,
                    });
                }
            }
            else {
                setReviewApiError(res.error);
            }
        }
    };

    const handleTouchOutside = () => {
        setModalVal(false);
        trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE,
            omnitureMapping.TGS_TRAVELLERS_BOTTOMSHEET_DISMISS);
        setRadioButton(null);
    };

    return (
        <>
        <RailInfoPageHeader  onIconClick={onBackClick} title={{ bold: TRAVELLER_PAGE }} subtitle={`${sourceDestinationAndDateInfo}`} />
            <ScrollView ref={(ref) =>
                captureRef(COMPONENT_TRAVELER_PAGE_SCROLL_VIEW, ref)
            }
                style={importedStyles.container}
            >
                { isOldTgsPage ?
                    <OldTgsTravellersView response={response} components={components}/> :
                    <NewTgsTravellersView
                    totalVoucherAmount={totalVoucherAmount}
                    response={response}
                    NewTravellerComponent={NewTravellerComponent}
                    totalTicketFare={totalTicketFare}
                    />
                }
            </ScrollView>
            {
                dispayTicketDetails &&
                < BottomSheetModalTrain
                    onTouchOutside={() => handleTouchOutside()}
                    hardwareBackButtonClose={() => handleTouchOutside()}
                >
                    <TicketDetailsBottomSheet platformVal={platformVal} 
                    setPlatformVal={setPlatformVal} setModalVal={setModalVal} 
                    handleTouchOutside={handleTouchOutside} />
                </BottomSheetModalTrain>}
            {
                !dispayTicketDetails &&
                <BookNowButton
                    onBookNowClicked={() => onClickBooking()}
                    totalCollectibleAmount={getTgFare(priceBreakUpContainer?.totalPrice)}
                    buttonText={ isOldTgsPage ? CONFIRM_AND_PAY : CONTINUE}
                    showLoader={showLoader}
                />
            }
        </>);
};

const mapStateToProps = (state, ownProps) => ({

});

const mapDisptachToProps = dispatch => ({
    captureRef: (componentName: string, myScrollRef: string) => dispatch(captureRef(componentName, myScrollRef)),
});

export default connect(mapStateToProps, mapDisptachToProps)(TGSTravellersPage);



