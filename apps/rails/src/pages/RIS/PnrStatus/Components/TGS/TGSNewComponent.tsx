import React from 'react';
import {
    View,
    StyleSheet,
    Text,
    Image,
} from 'react-native';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from '../../../../../RisAnalytics/RisAnalytics';
import { omnitureMapping } from '../../TGSUtils/Constants';
import TGSSearchButton from 'apps/rails/src/pages/TGS/Components/TGSSearchButton';
import { TGSRISPageDetailsProps } from '../../TGSCommonInterfaces';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import TGSDetails from './TGSDetails';
import { tgsLogin } from '../NewPnrStatusPage';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

const { tgsConfirmedBanner } = ASSETS;

const TGSNewComponent = ({ tgsEligibility: { 
    tgPremiumAmount,totalVoucherAmount,totalTicketFare }, bottomSheet, 
    onClose,pnrNumber,userLoggedIn }: TGSRISPageDetailsProps) => {
    const onSubmit = () => {
        if (bottomSheet) {
            onClose();
        }
        trackClickEventProp61(
            RIS_LANDING_PNR_DETAILS_SUCCESS, 
            omnitureMapping.TGSWIDGET_ON_PNR_PAGE_CLICKED_NEW_BOTTOMSHEET
        );
        tgsLogin(pnrNumber,totalVoucherAmount,totalTicketFare,userLoggedIn);
    };

    return (
        <View style={styles.container} >
            <View style={styles.topSection}>
                <Text style={[styles.topSectionText, fontStyle('bold')]}>Your ticket is waitlisted</Text>
            </View>
            <View style={styles.tgsConfirmedBannerContainer}>
                <Image source={tgsConfirmedBanner} resizeMode="contain" style={styles.tgsConfirmedBanner} />
            </View>
            <TGSDetails refundAmountValue={totalVoucherAmount} />
            <View style={styles.button}>
                <TGSSearchButton onPress={onSubmit} label={`BUY @₹${tgPremiumAmount}`} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        height: 308,
        backgroundColor: colors.white,
        borderTopLeftRadius: 15,
        borderTopRightRadius: 15,

    },
    topSection: {
        backgroundColor: colors.creamWhite,
        borderTopLeftRadius: 15,
        borderTopRightRadius: 15,
        height: 25,
        justifyContent:'center',
    },
    topSectionText: {
        color: colors.lightYello,
        textAlign: 'center',
    },
    tgsConfirmedBanner: {
        height: 52,
        width: '92%',
    },
    tgsConfirmedBannerContainer: {
        marginTop: 10,
    },
    button: {
        width: '90%',
        marginRight: 'auto',
        marginLeft: 'auto',
    },
});

export default TGSNewComponent;
