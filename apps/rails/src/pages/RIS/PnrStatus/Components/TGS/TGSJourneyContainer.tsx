import React, { useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
} from 'react-native';
import {tgsJourneyStyles} from './TGSContainer.styles';
import { DownArrow, UpArrow } from '../../TGSUtils/TGSAssets';
import { fontStyle } from '../../../../../vernacular/VernacularUtils';
import EllipsisText from '@mmt/ui/components/ellipsisText';
import { commonStyles, JOURNEY_DETAILS, SHOW_DETAILS, splitText, SHOW_LESS, omnitureMapping } from '../../TGSUtils/Constants';
import { JourneyDetailsProps } from '../../TGSCommonInterfaces';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

const TGSJourneyContainer = ({ 
    journeyDetailsContainer: { 
        trainName, pnr, trainSourceDestinationCode, 
        trainSourceDestinationStationName 
    } }: JourneyDetailsProps) => {
    const [displayUp, setDisplayUp] = useState<boolean>(true);
    const [trainNameText, trainNameValue] = splitText(trainName);
    const [pnrTextName, pnrValue] = splitText(pnr);
    const [trainSourceCity, trainDestinationCity] = splitText(trainSourceDestinationCode);
    const [trainSourceStationName, trainDestinationStationName] = splitText(trainSourceDestinationStationName);

    const handleOnPress = () => {
        if (displayUp) {
            trackClickEventProp61(omnitureMapping.TGS_TRAVELLERS_PAGE
                , omnitureMapping.TGS_TRAVELLERS_JOURNEY_CLICKED);
        }
        else {
            trackClickEventProp61(
                omnitureMapping.TGS_TRAVELLERS_PAGE, 
                omnitureMapping.TGS_TRAVELLERS_JOURNEY_SHOW_LESS_CLICKED
            );
        }
        setDisplayUp((prev: boolean) => !prev);
    };

    return (<View style={commonStyles.genericContainer}>
        <View style={[commonStyles.firstRow, { alignItems: 'center' }]}>
            <Text style={commonStyles.heading}>
                {JOURNEY_DETAILS}
            </Text>
            <TouchableOpacity onPress={() => handleOnPress()}>
                <View style={tgsJourneyStyles.flexDirectionWithCenterAligned}>
                    <Text style={tgsJourneyStyles.showDetails}>
                        {displayUp ? SHOW_DETAILS : SHOW_LESS}
                    </Text>
                    {
                        displayUp ?
                            <DownArrow style={tgsJourneyStyles.arrowStyle} /> :
                            <UpArrow style={tgsJourneyStyles.arrowStyle} />

                    }
                </View>
            </TouchableOpacity>
        </View>
        <View style={commonStyles.firstRow}>
            <EllipsisText content={trainNameText} contentLength={25} style={[tgsJourneyStyles.textColumn1, fontStyle('bold')]} />
            <Text style={[tgsJourneyStyles.textColumn1LastRow, fontStyle('regular')]}>
                {trainNameValue}
            </Text>
        </View>
        {!displayUp &&
            <>
                <View style={commonStyles.TextRow}>
                    <Text style={[tgsJourneyStyles.textColumn1, fontStyle('bold')]}>
                        {pnrTextName}
                    </Text>
                    <Text style={[fontStyle('regular'), tgsJourneyStyles.blackColor]}>
                        {pnrValue}
                    </Text>
                </View>
                <View style={commonStyles.TextRow}>
                    <EllipsisText content={trainSourceCity} contentLength={25} style={[tgsJourneyStyles.textRow3, fontStyle('bold')]} />
                    <EllipsisText content={trainDestinationCity} contentLength={25} style={[tgsJourneyStyles.textRow3, fontStyle('bold')]} />
                </View>
                <View style={commonStyles.TextRow}>
                    <EllipsisText content={trainSourceStationName} contentLength={25} style={[tgsJourneyStyles.textColumn1LastRow, fontStyle('regular')]} />
                    <EllipsisText content={trainDestinationStationName} contentLength={25} style={[tgsJourneyStyles.textColumn1LastRow, fontStyle('regular')]} />
                </View>
            </>
        }
    </View>);
};

export default TGSJourneyContainer;
