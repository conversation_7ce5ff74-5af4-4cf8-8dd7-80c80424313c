import React, { useCallback } from 'react';
import { View, Text, Image, StyleSheet, FlatList } from 'react-native';
import { Actions } from 'apps/rails/src/navigation/index';
import {
  omnitureMapping,
  TGS_T_AND_C,
  refund1xText,
  refund2xText,
  yourTicketFare,
  get3xRefund,
  getTgFare,
  payOnly,
  getFeedbackData,
  TESTIMONIAL_HEADING,
} from '../../TGSUtils/Constants';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import HTMLView from 'react-native-htmlview';
import styles from './styles/NewTgsTravellersView.styles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import TravellerPnrContainer from './components/TravellerPnrContainer';
import { TGSTravellersPageResponseProps } from '../../TGSCommonInterfaces';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

import greenTickIcon from '@mmt/legacy-assets/src/tg_green_tick.webp';
import testimonalIcon from '@mmt/legacy-assets/src/testimonal.webp';
import PropTypes from 'prop-types';
const tgBanner = ASSETS.tgTravellerBanner;

export const GetTgFeedbackContainer = () => {

  const feedbackData = getFeedbackData();

  const feedbackItem = useCallback(({ item }) => (
    <View style={styles.feedbackItem} key={item.name}>
      <View style={styles.testimonalHeadingContainer}>
        <Image style={styles.testimonalImg} source={testimonalIcon} />
        <Text style={[styles.testimonalHeadingText, getLineHeight(14), fontStyle('black')]}>
          {item.name}
        </Text>
      </View>
      <Text style={[styles.testimonalContent, getLineHeight(11), fontStyle('regular')]}>{item.content}</Text>
    </View>
  ), []);

  return (
    <View style={styles.feedbackContainer}>
      <FlatList
        horizontal
        data={feedbackData}
        showsHorizontalScrollIndicator={false}
        renderItem={feedbackItem}
      />
    </View>
  );
};

function NewTgsTravellersView({ response, NewTravellerComponent,totalVoucherAmount,totalTicketFare }) {
  const {
    refundContainer,
    travellerPassengerDetails,
    tncContainer,
    priceBreakUpContainer,
  }: TGSTravellersPageResponseProps = response;

  const totalTgFare = getTgFare(priceBreakUpContainer?.totalPrice);

  const tandCSubmit = () => {
    trackClickEventProp61(
      omnitureMapping.TGS_TRAVELLERS_PAGE,
      omnitureMapping.TGS_TRAVELLER_T_AND_C,
    );
    Actions.tgsTandCPageWebView();
  };

  return (
    <View style={styles.outerContainer}>
      <Image source={tgBanner} resizeMode="contain" style={styles.tgBannerImg} />
      <View style={styles.container}>
        <View style={styles.ticketFareContainer}>
          <View style={styles.ticketFareBox}>
            <Text style={[styles.ticketFareText, fontStyle('regular'), getLineHeight(14)]}>
              {yourTicketFare}
            </Text>
            <Text style={[styles.ticketFareText, fontStyle('regular'), getLineHeight(12)]}>
            ₹{totalTicketFare}
            </Text>
          </View>
          <View style={styles.ticketFareBox}>
            <Text style={[styles.ticketRefundText, fontStyle('black'), getLineHeight(14)]}>
              {get3xRefund}
            </Text>
            <View style={styles.ticketRefund}>
              <Text style={[styles.ticketRefundAmt, fontStyle('black'), getLineHeight(14)]}>
              ₹{totalVoucherAmount + totalTicketFare}
              </Text>
              <Image style={styles.greenTickImg} source={greenTickIcon} resizeMode="contain" />
            </View>
          </View>
          <View style={styles.availTgContainer}>
            <Text style={[styles.availTgText, fontStyle('regular'), getLineHeight(14)]}>
              <Text>{payOnly}</Text>
              <Text style={fontStyle('black')}>{` ₹${totalTgFare}`}</Text>
              <Text> to avail</Text>
              <Text style={fontStyle('black')}> Trip Guarantee</Text>
              <Text> benefit</Text>
            </Text>
          </View>
        </View>
        <HTMLView
          style={styles.refundQ}
          value={refundContainer.refundText3x}
          stylesheet={StyleSheet.create(JSON.parse(refundContainer.refundStyle3x))}
        />
        <View style={styles.refundAContainer}>
          <View style={styles.refundA}>
            <Text style={[styles.refundText1xstyle, fontStyle('black'), getLineHeight(14)]}>
              {refund1xText + ' (₹' + totalTicketFare + ')'}
            </Text>
            <Text style={[styles.refundTextDesc, fontStyle('regular'), getLineHeight(13)]}>
              {refundContainer.refundText1x}
            </Text>
          </View>
          <View style={styles.refundA}>
            <Text style={[styles.refundText1xstyle, fontStyle('black'), getLineHeight(14)]}>
              {refund2xText + ' (₹' + totalVoucherAmount + ')'}
            </Text>
            <Text style={[styles.refundTextDesc, fontStyle('regular'), getLineHeight(13)]}>
              {refundContainer.refundText2x}
            </Text>
          </View>
        </View>
        <View style={styles.tncContainer}>
          <Text style={[styles.tncContainerDesc, getLineHeight(12), fontStyle('medium')]}>
            {`${tncContainer.tncContainerText} `}
            <Text style={[styles.tncContainerLink, fontStyle('bold')]} onPress={tandCSubmit}>
              {TGS_T_AND_C}
            </Text>
          </Text>
        </View>
        <Text style={[styles.feedbackHeading, getLineHeight(14), fontStyle('black')]}>
          {TESTIMONIAL_HEADING}
        </Text>
      </View>
      <GetTgFeedbackContainer />
      <View style={styles.seperator} />
      {NewTravellerComponent}
      <View style={styles.seperator} />
      <TravellerPnrContainer travellerPassengerDetails={travellerPassengerDetails} />
    </View>
  );
}

NewTgsTravellersView.propTypes = {
  response: PropTypes.shape({
    paymentContainer: PropTypes.any, 
    refundContainer: PropTypes.shape({
      refundText3x: PropTypes.string,
      refundStyle3x: PropTypes.string,
      refundText1x: PropTypes.string,
      refundText2x: PropTypes.string,
    }),
    travellerPassengerDetails: PropTypes.any, 
    tncContainer: PropTypes.shape({
      tncContainerText: PropTypes.string,
    }),
    priceBreakUpContainer: PropTypes.shape({
      totalPrice: PropTypes.number,
    }),
  }),
  NewTravellerComponent: PropTypes.element,
  totalVoucherAmount: PropTypes.number,
  totalTicketFare: PropTypes.number,
};

export default NewTgsTravellersView;
