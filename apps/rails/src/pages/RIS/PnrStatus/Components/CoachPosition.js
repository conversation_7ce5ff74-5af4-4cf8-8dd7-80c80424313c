import React, { useState, useEffect } from 'react';
import {
  FlatList,
  ImageBackground,
  StyleSheet,
  Text,
  View,
  Platform,
  BackHandler,
} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { Actions } from '../../../../navigation';
import CoachPositionHeader from './CoachPositionHeader';
import {getAdsCard} from '@mmt/legacy-commons/adsConfig/adsCard';
import {RIS_COACH_RESULT} from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import {getAllTypesAdsAb} from '../../../Common/utils';
import { trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';



import engineImage from '@mmt/legacy-assets/src/icEnginePurple.webp';
import coachImage from '@mmt/legacy-assets/src/icCoachPurple.webp';
import PropTypes from 'prop-types';

const EngineCoachView = props => {
  const [showDiffTypeOfAds, setShowDiffTypeOfAds] = useState({multi_banner: 0, snackbar: 0, adfeed:0,interstitial: 0});

  useEffect(() => {
    getAdConfig();
  }, []);
  const getAdConfig = async ()=>{
    const AdsAb = await getAllTypesAdsAb();
    setShowDiffTypeOfAds(AdsAb);
    trackAdLoad('mob:funnel:ris:pwa:coachposition:coachpositiondtls',AdsAb.trackingPayload);
  };
  return (
  <View>
    {!!showDiffTypeOfAds.multi_banner && <View style={{margin: 16, flexDirection:'row', justifyContent: 'center'}}>
      {getAdsCard(Platform.OS, RIS_COACH_RESULT)}
    </View>}
    <View style={{
      flexDirection: 'row',
      marginVertical: 10,
      marginLeft: 38,
      alignItems: 'center',
    }}
    >
      <ImageBackground
        source={engineImage}
        style={{
          justifyContent: 'center',
          width: 56,
          height: 110,
        }}
      >
        <Text style={{
          alignSelf: 'center',
          color: colors.white,
        }}
        >{props.item}
        </Text>
      </ImageBackground>
      <Text style={{
        marginLeft: 20,
        fontFamily: fonts.bold,
        fontSize: 16,
        color: colors.black,
      }}
      >
        {getCoachName(props.item)}
      </Text>
    </View>
  </View>
 );
};

function CoachView(props: { index: unknown, item: unknown }) {
  return (<View style={{
    flexDirection: 'row',
    marginVertical: 10,
    marginLeft: 16,
    alignItems: 'center',
  }}
  >
    <Text style={{
      marginRight: 16,
      fontFamily: fonts.bold,
      fontSize: 12,
      color: colors.black,
    }}
    >
      {props.index}
    </Text>
    <ImageBackground
      source={coachImage}
      style={{
        width: 56,
        height: 80,
        justifyContent: 'center',
      }}
    >
      <Text style={{
        alignSelf: 'center',
        color: colors.white,
      }}
      >{props.item}
      </Text>
    </ImageBackground>
    <Text style={{
      marginLeft: 20,
      fontFamily: fonts.bold,
      fontSize: 16,
      color: colors.black,
    }}
    >
      {getCoachName(props.item)}
    </Text>
  </View>);
}

class CoachPosition extends React.PureComponent {
  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBack);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBack);
  }

  onBack = () => {
    if (this.props && this.props.navigation && this.props.navigation.canGoBack()) {
      Actions.pop();
    } else if (Platform.OS === 'ios') {
      ViewControllerModule.popViewController();
    } else {
      BackHandler.exitApp();
    }
    return true;
  };
  render() {
    const {trainNumber, trainName, coachList} = this.props;
    return (
      <View>
        <CoachPositionHeader trainNumber={trainNumber} trainName={trainName} navigation={this.props.navigation} />
        <View style={pnrStyles.warningContainer}>
          <Text style={pnrStyles.warningText}>
            Coach positions may vary for certain trains. Remember to re-check at station.
          </Text>
        </View>
        <View style={{
          backgroundColor: colors.grey11,
          paddingBottom: 90,
        }}
        >
          <FlatList
            vertical
            data={coachList}
            renderItem={({item, index}) =>
              (index === 0 ? <EngineCoachView item={item} /> : index < coachList.length - 1 ?
                <CoachView index={index} item={item} /> : <View />)}
            contentContainerStyle={{paddingBottom: 170}}
            keyExtractor={(item, index) => `list-item-${index}`}
          />
        </View>
      </View>
    );
  }
}

const getCoachName = (coach) => {
  const result = coach.replace(/[0-9]/g, '');
  return coachMap[result];
};

const coachMap = {
  S: 'Sleeper',
  SL: 'Sleeper',
  SLR: 'Sitting and Luggage',
  H: 'First Class AC',
  A: 'AC 2-Tier',
  B: 'AC 3-Tier',
  L: 'Engine',
  HA: 'First and Second AC',
  HB: 'First and Third AC',
  AB: 'Second and Third AC',
  D: 'Second Sitting / Sitting Coach',
  FC: 'First Class Non AC',
  F: 'First Class Non AC',
  EC: 'Executive Chair Car',
  CC: 'AC Chair Car',
  J: 'Garib Rath Chair Car',
  G: 'Garib Rath',
  GS: 'UnReserved',
  UR: 'UnReserved Second Class',
  PC: 'Pantry',
  EOG: 'Generator',
  E: 'Executive Chair Car',
  C: 'AC Chair Car',
  GEN: 'General Compartment',
  HCP: 'First Class AC',
  GN: 'UnReserved',
  GD: 'Garib Rath',
  EXT: 'Extra Coach',
  SE: 'Second Class Executive Chair Car',
  ES: 'Extra Sleeper Coach',
};


const pnrStyles = StyleSheet.create({
  containerStyle: {
    backgroundColor: colors.grey11,
  },
  headerStyle: {
    flexDirection: 'row',
    height: 64,
    borderBottomWidth: 2,
    borderColor: colors.greyBookedSeat,
  },
  backIconStyle: {
    width: 18,
    height: 16,
    justifyContent: 'center',
  },
  backIconContainer: {
    width: 48,
    height: 48,
    marginTop: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTextStyle: {
    marginLeft: 12,
    marginTop: 13,
    fontFamily: fonts.bold,
    fontSize: 16,
    color: colors.black,
  },
  pnrNumberStyle: {
    marginLeft: 12,
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.black,
  },
  trainNumberStyle: {
    color: colors.lightTextColor,
  },
  warningContainer: {
    marginHorizontal: 16,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: colors.red,
    padding: 5,
  },
  warningText: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  },
  warningHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  importantText: {
    fontFamily: fonts.bold,
    fontSize: 14,
    color: colors.black,
  },
  closeButton: {
    height: 24,
    width: 24,
    marginTop: -5,
  },
});

CoachPosition.propTypes = {
  trainNumber: PropTypes.string,
  trainName: PropTypes.string,
  coachList: PropTypes.array,
  navigation: PropTypes.object,
};

EngineCoachView.propTypes = {
  item: PropTypes.string,
};

export default CoachPosition;
