/* eslint-disable */
import React from 'react';
import {Image,View, Text, StyleSheet} from 'react-native';
import {WHY_BOOK_WITH_US_TITLE} from '../../../../Utils/RailsConstant';
import {getScreenWidth} from '../../../Review/AvailabilityStatusChange/AvailabilityChangeUtils';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {fontStyle} from '../../../../vernacular/VernacularUtils';
import { _label } from '../../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

const WhyBookWithUs = () => {
    return (
        <View style={WhyBookStyles.WhyBookWithUsContainer}>
            <Card style={zeroMargin} elevation={2}>
                <View style = {WhyBookStyles.WhyBookTitleContainer}>
                    <Text style = {[WhyBookStyles.WhyBookTitle, fontStyle('bold')]}>
                        {_label(WHY_BOOK_WITH_US_TITLE, { uppercase: true, defaultLang: true })}
                    </Text>
                </View>
                <View style = {WhyBookStyles.CardListContainer}>
                    {cardsArray.map((card, index) => <WhyBookWithUsItem key = {index} cardData = {card}/>)}
                </View>
            </Card>
        </View>
    );
};

const WhyBookWithUsItem = (props) => {
    const {cardData} = props;
    const {iconHandler , detailsText} = cardData;
    return (
        <View key = {key} style = {WhyBookStyles.CardStyle}>
            <Image
            source={iconHandler}
            style={ (iconHandler !== pnrAlertIcon) ? WhyBookStyles.IconStyle : WhyBookStyles.AlertIconStyle } />
            <View
             style = {(iconHandler !== pnrAlertIcon) ? WhyBookStyles.TextBoxStyle : WhyBookStyles.AlertTextBoxStyle}>
                <Text style = {[styles.textDetailsStyle, fontStyle('bold', 'en')]}>
                    {detailsText}
                </Text>
            </View>
        </View>
    );
};

const WhyBookStyles = StyleSheet.create({
    WhyBookWithUsContainer : {
        marginTop : 10,
        marginBottom : 10,
        marginLeft : 7,
        marginRight : 9,
        borderRadius : 4,
        elevation : 5,
        borderColor : colors.grey3,
        backgroundColor : colors.white
    },
    WhyBookTitleContainer : {
        marginTop : 12,
        marginLeft : 15,
        height : 22
    },
    WhyBookTitle : {
        fontSize : 18,
        color : colors.black,
    },
    CardListContainer : {
        marginLeft : 4,
        flexDirection : 'row',
        maxWidth : getScreenWidth() - 20,
        overflow : 'scroll',
    },
    IconStyle : {
        width : 40,
        height : 40,
        marginTop : 19,
        marginBottom : 9,
        marginHorizontal : 19,
    },
    AlertIconStyle : {
        width : 40,
        height : 40,
        marginTop : 19,
        marginBottom : 9,
        marginHorizontal : 28
    },

    TextBoxStyle : {
        width : 78,
    },
    AlertTextBoxStyle : {
        width : 98,
        height : 28
    },
    CardStyle : {
        marginRight : 2
    }
});

WhyBookWithUsItem.propTypes = {
    cardData : PropTypes.object
};

export default WhyBookWithUs;
