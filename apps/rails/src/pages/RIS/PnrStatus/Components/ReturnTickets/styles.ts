import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  arrowImage: {
    height: 20,
    marginHorizontal: 10,
    resizeMode: 'contain',
    width: 20,
  },
  borderContainer: {
    borderColor: colors.lightSilver,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 16,
    overflow: 'hidden',
    paddingTop: 10,
  },
  calendarContainer: {
    alignItems: 'center',
    backgroundColor: colors.lighterBlue,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    marginHorizontal: 10,
    marginTop: 8,
    minHeight: 24,
    padding: 3.5,
  },
  calendarIcon: {
    height: 18,
    marginRight: 6,
    resizeMode: 'contain',
    width: 18,
  },
  card: {
    borderColor: colors.lightSilver,
    borderRadius: 8,
    borderWidth: 1,
    flexGrow: 1,
    padding: 8,
    minWidth: 90,
  },
  cardContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  container: {
    backgroundColor: colors.transparent,
  },
  dateText: {
    color: colors.black,
  },
  flatListContainer: {
    marginTop: 6,
    marginBottom: 4,
    paddingHorizontal: 10,
    flexGrow: 1,
  },
  rowContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 6,
    paddingLeft: 10,
  },
  separator: {
    width: 8,
  },
  stationName: {
    color: colors.black,
    maxWidth: '45%',
  },
  statusText: {
    color: colors.black,
    marginTop: 8,
  },
  emptyStatusText: {
    textAlign: 'center',
  },
  viewCalendarText: {
    color: colors.primary,
  },
});
