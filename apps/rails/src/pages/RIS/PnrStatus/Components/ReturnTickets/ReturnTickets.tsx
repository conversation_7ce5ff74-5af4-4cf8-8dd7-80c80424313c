import React from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from 'apps/rails/src/RisAnalytics/RisAnalytics';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { getPnrDateFormat } from 'apps/rails/src/Utils/RisUtils';
import {
  getDateForReturnTicket,
  openCrossSell,
} from 'apps/rails/src/pages/RIS/RisCommonUtils';
import { CROSS_SELL_CLICK } from 'apps/rails/src/pages/RIS/Utils/RISOmnitureConstants';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { PNR_RETURN_TICKETS } from '../../../../../Utils/RailsConstant';
import { Actions } from '../../../../../navigation';
import { CrossSellCoupons, CrossSellInlineHeader } from '../PNRCrossSells/Components';
import { AvailabilityByDate, CrossSellDataObj } from '../PNRCrossSells/types';
import styles from './styles';
import { logRisPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS';
import { RIS_EVENT_VALUES } from '../../../../../PdtAnalytics/PdtAnalyticsV2/RIS/RisPdtConstants';

interface Props {
  isFromBottomSheet: boolean;
  crossSellData: CrossSellDataObj;
  onClosePressed?: () => void;
}

const ReturnTickets = (props: Props) => {
  const { crossSellData, isFromBottomSheet, onClosePressed } = props;
  const { data = {}, coupon = {}, searchParams = {} } = crossSellData || {};

  const logReturnTicketToPdt = async (item: AvailabilityByDate) => {
    try {
      const returnTicketPdtData = {
        ...crossSellData,
        data: [item],
        isFromBottomSheet,
      };
      logRisPdtEvent({
        eventValue: RIS_EVENT_VALUES.RUTURN_TICKETS,
        pnrCrossSell: returnTicketPdtData,
      });
    } catch (error) {}
  };

  const onCardClick = (item: AvailabilityByDate) => {
    try {
      const date = getDateForReturnTicket(item.date);
      if (date) {
        onClosePressed && onClosePressed();
        trackClickEventProp61(
          RIS_LANDING_PNR_DETAILS_SUCCESS,
          CROSS_SELL_CLICK.RETURN_TICKETS[isFromBottomSheet ? 'BS_CARD' : 'INLINE_CARD'],
        );
        openCrossSell(searchParams, crossSellData.type, date);
        logReturnTicketToPdt(item);
      }
    } catch (error) {}
  };

  const onViewCalendarClicked = () => {
    onClosePressed && onClosePressed();
    trackClickEventProp61(
      RIS_LANDING_PNR_DETAILS_SUCCESS,
      CROSS_SELL_CLICK.RETURN_TICKETS[isFromBottomSheet ? 'BS_CALENDAR' : 'INLINE_CALENDAR'],
    );
    Actions.railsCalendar({
      selectedDate: new Date(),
      srcCodeParam: searchParams?.sourceCode,
      destCodeParam: searchParams?.destinationCode,
      fromPage: PNR_RETURN_TICKETS,
    });
  };

  const renderItem = ({ item, index }: { item: AvailabilityByDate; index: number }) => {
    return (
      <>
        <TouchableOpacity
          style={styles.card}
          onPress={() => onCardClick(item)}
          testID="return_tickets_card"
        >
          <Text
            style={[fontStyle('black'), getLineHeight(14), styles.dateText]}
            testID="return_tickets_card_date_text"
          >
            {getPnrDateFormat(item.date)}
          </Text>
          <Text
            style={[
              fontStyle('black'),
              getLineHeight(14),
              styles.statusText,
              !item.statusText && styles.emptyStatusText,
              { color: item.colourCode },
            ]}
            testID="return_tickets_card_status_text"
          >
            {item.statusText || '----'}
          </Text>
        </TouchableOpacity>
        {index < data?.length - 1 && <View style={styles.separator} />}
      </>
    );
  };

  const keyExtractor = (item: AvailabilityByDate, index: number) => index.toString();
  const ItemSeparatorComponent = () => <View style={styles.separator} />;

  return (
    <View
      style={[styles.container, isFromBottomSheet && styles.borderContainer]}
      testID="return_tickets_container"
    >
      {!isFromBottomSheet && <CrossSellInlineHeader crossSellData={crossSellData} />}
      <View style={styles.rowContainer} testID="return_tickets_row_container">
        <Text
          style={[fontStyle('black'), getLineHeight(16), styles.stationName]}
          testID="return_tickets_row_container_source_name"
        >
          {searchParams?.sourceName}
        </Text>
        <Image source={ASSETS.ic_arrow_right} style={styles.arrowImage} />
        <Text
          style={[fontStyle('black'), getLineHeight(16), styles.stationName]}
          testID="return_tickets_row_container_destination_name"
        >
          {searchParams?.destinationName}
        </Text>
      </View>
      <FlatList
        data={data as AvailabilityByDate[]}
        contentContainerStyle={styles.flatListContainer}
        horizontal={data?.length > 3}
        numColumns={data?.length === 3 ? 3 : 1}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ItemSeparatorComponent={ItemSeparatorComponent}
        testID="return_tickets_flat_list"
      />
      <TouchableOpacity
        style={styles.calendarContainer}
        onPress={onViewCalendarClicked}
        testID="return_tickets_calendar_container"
      >
        <Image
          source={ASSETS.ic_calendar}
          style={styles.calendarIcon}
          testID="return_tickets_calendar_icon"
        />
        <Text
          style={[fontStyle('black'), getLineHeight(15), styles.viewCalendarText]}
          testID="return_tickets_calendar_text"
        >
          View Calendar
        </Text>
      </TouchableOpacity>
      <CrossSellCoupons coupon={coupon} />
    </View>
  );
};

export default ReturnTickets;
