import React from 'react';
import { View, Text, Switch, StyleSheet, Image } from 'react-native';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';

interface Props {
  isEnabled: boolean;
  onValueChange: () => void;
}

const PNRWhatsappAlert = ({ isEnabled, onValueChange }: Props) => {
  return (
    <View style={styles.container} testId="pnr_whatsapp_alert_container">
      <View style={styles.row} testId="pnr_whatsapp_alert_container_row">
        <Image
          source={ASSETS.ic_whatsapp}
          style={styles.image}
          testId="pnr_whatsapp_alert_container_row_image"
        />
        <Text
          style={[styles.text, fontStyle('medium'), getLineHeight(16)]}
          testId="pnr_whatsapp_alert_container_row_text"
        >
          {_label('pnr_updates_on_whastapp')}
        </Text>
        <Switch
          style={styles.switch}
          value={isEnabled}
          onValueChange={onValueChange}
          testId="pnr_whatsapp_alert_container_row_switch"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.lightGray,
    padding: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    width: 32,
    height: 32,
    marginRight: 10,
  },
  text: {
    flex: 1,
    fontSize: 14,
    color: colors.black,
  },
  switch: {
    marginLeft: 10,
  },
});

export default PNRWhatsappAlert;
