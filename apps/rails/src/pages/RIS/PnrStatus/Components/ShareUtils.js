import {Platform, Share} from 'react-native';

// At least one of URL or message is required.
const shareContents = (title, text, url) => {
  let message = text;
  if (Platform.OS === 'android') {
    message = `${text || ''}`;
    Share.share(
      { message, url, title },
      {
        // Android only:
        title,
        // iOS only:
        subject: title,
      },
    ).catch();
  }
};

export default shareContents;
