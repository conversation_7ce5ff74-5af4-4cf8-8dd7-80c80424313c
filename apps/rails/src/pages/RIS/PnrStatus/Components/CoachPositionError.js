/* eslint-disable */
import React from 'react';
import {Image, View, Text, StyleSheet, PixelRatio, Platform, BackHandler} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { Actions } from '../../../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';

import errorImage from '@mmt/legacy-assets/src/ic_flushed_pnr.webp';
import PropTypes from 'prop-types';

const handleBackClick = () => {
  const somethingPoped = Actions.pop();
  if (somethingPoped != null && !somethingPoped) {
    if (Platform.OS === 'ios') {
      ViewControllerModule.popViewController(getRootTag());
    } else {
      BackHandler.exitApp();
    }
  }
};

const CoachPositionError = props => (
  <View styles={styles.container} >
    <Image source={errorImage} style={styles.image} />
    <Text style={styles.title}>{props.errorText}</Text>
    <View style={styles.noInternetCta}>
      <TouchableRipple
        onPress={handleBackClick}
      >
        <View>
          <LinearGradient
            colors={['#53B2FE', '#065AF3']}
            start={{x: 0.0, y: 0.0}}
            end={{x: 1.0, y: 0.0}}
            style={styles.cta}
          >
            <Text style={styles.text}>CHANGE TRAIN</Text>
          </LinearGradient>
        </View>
      </TouchableRipple>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignSelf: 'center',
  },
  image: {
    alignSelf: 'center',
    width: 281,
    height: 246,
    marginTop: 100,
  },
  title: {
    marginTop: 20,
    marginHorizontal: 20,
    color: colors.darkGrey,
    fontSize: 20,
    fontFamily: fonts.bold,
    textAlign: 'center',
  },
  noInternetCta: {
    marginTop: 50,
    alignSelf: 'center',
  },
  cta: {
    width: 200,
    backgroundColor: colors.azure,
    height: PixelRatio.getPixelSizeForLayoutSize(16),
    alignItems: 'center',
    justifyContent: 'center',
    ...getPlatformElevation(4),
  },
  text: {
    color: colors.white,
    fontSize: PixelRatio.getFontScale() * 12,
    paddingHorizontal: 8,
    fontFamily: fonts.bold,
    backgroundColor: colors.transparent,
  },
});

CoachPositionError.propTypes = {
  errorText: PropTypes.string,
};

export default CoachPositionError;
