import React, {useEffect} from 'react';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import { View, BackHandler, Platform } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import isEmpty from 'lodash/isEmpty';
import CoachPosition from './CoachPosition';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fetchCoachList} from '../RailInfoAction';
import ViewState from '../../../../Utils/ViewState';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import CoachPositionHeader from './CoachPositionHeader';
import { Actions } from '../../../../navigation';
import {
  EVENT_RIS_PNR_COACH_POSITION,
  trackOmnitureLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import CoachPositionError from './CoachPositionError';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';

function CoachPositionList(props) {
  props = {
    ...props,
    coachList: typeof props.coachList === 'undefined' ? null : props.coachList,
  };

  const {
    trainNumber,
    trainName,
    fetchCoachList,
    coachList,
    coachViewState,
    coachViewErrorMsg,
    navigation,
  } = props;

  useEffect(() => {
    fetchCoachList(trainNumber);
  }, []);

  useEffect(() => {
    const onHardBackPress = () => {
      if (navigation && navigation.canGoBack()) {
        Actions.pop();
      } else if (Platform.OS === 'ios') {
        ViewControllerModule.popViewController();
      } else {
        BackHandler.exitApp();
      }
      return true;
    };
    BackHandler.addEventListener('hardwareBackPress', onHardBackPress);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onHardBackPress);
    };
  }, []);

  if (coachViewState === ViewState.LOADING) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Spinner size={30} color={colors.azure} />
      </View>
    );
  }

  if (coachViewState === ViewState.NO_INTERNET) {
    return (
      <View>
        <CoachPositionHeader trainName={trainName} trainNumber={trainNumber} navigation={props.navigation} />
        <NoInternetView onRetry={() => fetchCoachList(trainNumber)} />
      </View>
    );
  }

  if (!isEmpty(coachList)) {
    trackOmnitureLoadEvent(EVENT_RIS_PNR_COACH_POSITION);
    return (
      <View style={{flex: 1}}>
        <CoachPosition
          trainName={trainName}
          trainNumber={trainNumber}
          coachList={coachList}
          navigation={props.navigation}
        />
      </View>
    );
  }
  return (
    <View>
      <CoachPositionHeader
        trainName={trainName}
        trainNumber={trainNumber}
        navigation={props.navigation}
      />
      <CoachPositionError errorText={coachViewErrorMsg}/>
    </View>
  );
}


CoachPositionList.propTypes = {
  fetchCoachList: PropTypes.func,
  coachList: PropTypes.array,
  trainNumber: PropTypes.string,
  trainName: PropTypes.string,
  coachViewState: PropTypes.string,
  coachViewErrorMsg: PropTypes.string,
  navigation: PropTypes.object,
};

const mapStateToProps = (state) => {
  const {coachList, coachViewState, coachViewErrorMsg} = state.railInfoReducer;
  return {
    coachList,
    coachViewState,
    coachViewErrorMsg,
  };
};


const mapDispatchToProps = dispatch => ({
  fetchCoachList: trainNumber => dispatch(fetchCoachList(trainNumber)),
});

export default connect(mapStateToProps, mapDispatchToProps)(CoachPositionList);
