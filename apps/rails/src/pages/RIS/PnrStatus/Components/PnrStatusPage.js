import React from 'react';
import PropTypes from 'prop-types';
import { Actions } from '../../../../navigation';
import {
  trackClickEventProp61,
  trackPageLoad,
  trackAdLoad
} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  PNR_DETAILS_MEALS_BS_SHOWN,
  PNR_DETAILS_MEALS_BS_SHOWN_TIMER,
  PNR_DETAILS_MEALS_ORDER_NOW_CLICKED,
  showMealsBottomSheet,
  isUniquePnrVisit,
  UNIQUE_PNR_PAGENAMES
} from 'apps/rails/src/Utils/RailsConstant';
import FocusEffect from './FocusEffect';
import {
  getDataFromStorage,
  removeDataFromStorage,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import RailsIosSwipeGesture from 'apps/rails/src/pages/Common/RailsIosSwipeGesture';
import { getLocusIdFromStationDetails } from '../../../RailsLandingPage/Store/RailsLandingPageActions';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  Platform,
  BackHandler,
  DeviceEventEmitter,
  Animated,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import BusSharedModuleHolder from '@mmt/bus-shared/src';
import isEmpty from 'lodash/isEmpty';
import ViewState from '../../../../Utils/ViewState';
import TGSComponent from './TGS/TGSComponent';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import PnrHeader from './PnrHeader';
import PnrTicketStatus from './PnrTicketStatus';
import TrainDetails from './TrainDetails';
import AlternateJourney from './AlternateTrains';
import PnrCrossSellHotels from './PnrCrossSellHotels';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import PnrFlushedErrorView from './PnrFlushedErrorView';
import {
  trackTGSeligibility, trackTGSInEligibility,
  EVENT_RIS_PNR_COACH_POSITION,
  trackOmnitureClickEvent,
  trackOmnitureLoadEvent,
  trackTrainsBusCrossSell,
  RIS_LANDING_PNR_DETAILS_SUCCESS,
  trackMealsOmnitureLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import { trackPnrDetails } from '../TGSUtils/TGSHelper';

import {
  _getChannelCode,
  getNewPnrSearchHistory,
  getPnrRecentSearch,
  savePnrRecentSearch,
  tgsTrackPageDetails,
} from '../../../../Utils/RisUtils';
import { configKeys } from '../../../../configStore/Common/constants';
import { addWebEngageScriptToDOM } from '../../../../webEngageHelper';
import { pushPnrStatusIntoGtm } from '../../../../GtmConfig/gtmConfig';
import { RIS_PNR_RESULT } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import RailBookingCoupons from './railBookingCoupon/pnrCoupons';
import TGSBottomSheet from 'apps/rails/src/pages/TGS/Components/TGSBottomSheet';
import RailCouponView from './railBookingCoupon/railCouponView';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {
  AbConfigKeyMappings,
  getPokusConfig,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { LOGIN_EVENT } from '@mmt/legacy-commons/Common/constants/AppConstants';
import { openLiveTrainStatus } from '../../RisCommonUtils';
import {getAllTypesAdsAb} from '../../../Common/utils';
import fetch2 from '../../../../fetch2';
import {
  omnitureMapping,
  errorCode_3007,
  errorCode_3008,
  errorCode_2101,
  errorCode_2102,
  errorCode_2103,
  errorCode_2106,
  tgsButtonClicked,
  PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY,
} from '../TGSUtils/Constants';
import {
  getRailsMealsPnrDetailsPokus,
  showBusTrainCrossSell,
  getMealsNewFlow,
  showMealsWebView,
} from '@mmt/rails/src/RailsAbConfig';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import RailsMealEntryPoint from 'apps/rails/src/pages/RIS/MealsOnTrain/Components/RailsMealEntryPoint';
import MealBottomSheet from '../../MealsOnTrain/Components/MealBottomSheet';
import { INTERACTED_WITH_MEALS_PNR } from '../../MealsOnTrain/Constants/MealsConstants';
import BottomSheetModalTrain from '../../../Common/BottomSheetModalTrain';

const isMealAvailableForPnr = (mealDetails) => {
  const mealsWebView = showMealsWebView();
  const showNewMeals = getMealsNewFlow();
  const isZomato = mealsWebView || showNewMeals;
  return isZomato ? mealDetails?.ZomatoAvailable : mealDetails?.Available;
};

{/* Affding false to disable banner */ }
const enableCouponBanner = false;
let loginEventListener;
const config = {
  detectSwipeUp: false,
  detectSwipeDown: false,
  detectSwipeLeft: false,
};

class PnrStatusPage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showModal: false,
      mandateLogin: false,
      enableLoginPersuasion: false,
      userLoggedIn: false,
      showDiffTypeOfAds : {multi_banner: 0, snackbar: 0, adfeed:0,interstitial: 0},
      showTGSBottomSheet: false,
      showTGSComponent:false,
      tgsTextData: {},
      railsOriginLocusCode : null ,
      railsDestinationLocusCode : null,
      firstBackPress:false,
      tgsTimer: 0,
      railsMealPokus: false,
      isBottomSheetVisible: false,
      backButtonClickedCount: 0,
    };
    this.scrollView = undefined;
    this.scrollAnim = new Animated.Value(0);
  }

  async UNSAFE_componentWillMount() {
    let pnr = null;
    if (Platform.OS === 'web') {
      try {
        const url = new URL(window.location.href);
        pnr = url.searchParams.get('pnr');
      } catch {
        pnr = this.props.pnr;
      }
    }
    const response = await getConfigStore(configKeys.RAILS_TGS_WIDGET);
    if (Object.keys(response).length !== 0) {
      this.setState({ tgsTextData: response });
    }
   await  this.props.initPnrStatus(pnr || this.props.pnr);
   const {
     tgsEligibility: {
       isTgEligible: isTgEligible,
       success: tgsSuccess,
       error: tgsError,
       message: tgsMessage,
       response: tgsResponse,
     },
     IsBookedOnMMT,
     recentPnr,
   } = this.props;

   const trackingDetails = await getDataFromStorage(PNR_TRACKING_DETAILS_ASYNC_STORAGE_KEY);
   trackPnrDetails(RIS_LANDING_PNR_DETAILS_SUCCESS, trackingDetails);

    if (this.props?.pnrPage) {
      await tgsTrackPageDetails(RIS_LANDING_PNR_DETAILS_SUCCESS,UNIQUE_PNR_PAGENAMES.PNR_STATUS);
    }

    const { pnrNumber, travelEndDate } = recentPnr || {};
    const isNewUniqueVisit = await isUniquePnrVisit(
      UNIQUE_PNR_PAGENAMES.PNR_STATUS,
      pnrNumber,
      travelEndDate,
    );
    if (isNewUniqueVisit) {
      tgsTrackPageDetails(omnitureMapping.RIS_LANDING_PNR_DETAILS_SUCCESS_UNIQUE, 
        UNIQUE_PNR_PAGENAMES.PNR_STATUS);
      if (isTgEligible) {
        trackPageLoad(omnitureMapping.RIS_LANDING_PNR_DETAILS_SUCCESS_UNIQUE, 
          omnitureMapping.TGSWIDGET_ON_PNR_PAGE_SHOWN);
      }
    }

   if (isTgEligible) {
    trackPageLoad(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGSWIDGET_ON_PNR_PAGE_SHOWN);
  }

   if (isTgEligible && IsBookedOnMMT) {
     trackTGSeligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_BOOKER_ELIGIBLE);
     trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, 
      omnitureMapping.TGS_PNR_STATUS_PAGE_BOOKERS_TGS_ELIGIBLE);
   } else if (isTgEligible && !IsBookedOnMMT) {
     trackTGSeligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_NON_BOOKER_ELIGIBLE);
   } else if (!isTgEligible && IsBookedOnMMT) {
     trackTGSeligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_BOOKER_INELIGIBLE);
     trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, 
      omnitureMapping.TGS_PNR_STATUS_PAGE_BOOKERS_TGS_ELIGIBLE);
   } else if (tgsSuccess) {
     trackTGSeligibility(
       RIS_LANDING_PNR_DETAILS_SUCCESS,
       omnitureMapping.TGS_NON_BOOKER_INELIGIBLE,
     );
   }

   if (
     !(
       !this.props.tgsEligibility?.isTgEligible &&
       showBusTrainCrossSell() &&
       this.isWaitListed(this.props.passengerList)
     )
   ) {
     trackTrainsBusCrossSell(
       RIS_LANDING_PNR_DETAILS_SUCCESS,
       omnitureMapping.TRAINS_BUS_CROSS_SELL_NOT_VISIBLE,
     );
   }

    if (!this.props.tgsEligibility?.isTgEligible &&
      showBusTrainCrossSell() &&
      this.isWaitListed(this.props.passengerList)) {
      trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, 
        omnitureMapping.TGS_PNR_STATUS_PAGE_BUS_SHOWN_TO_NON_BOOKERS);
    }

    if (!IsBookedOnMMT && this.props.pnrViewState === ViewState.SHOW_DETAIL && 
      !this.props.tgsEligibility?.isTgEligible) {
      trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, 
        omnitureMapping.TGS_PNR_STATUS_PAGE_ALTERNATE_ROUTE_SHOWN_TO_NON_BOOKERS);
    }

   const tgsEligibilityErrorCode = tgsError?.errorCode;
   if (tgsEligibilityErrorCode) {

     switch (tgsEligibilityErrorCode) {
       case errorCode_2101:
         trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_INELIGIBLE_DS_OR_TATKAL);
         break;
       case errorCode_2102:
         trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_INELIGIBLE_PARTIAL_CNF);
         break;
       case errorCode_2103:
         trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_INELIGIBLE_CNF);
         break;
       case errorCode_3007:
         trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS,
          omnitureMapping.TGS_INELIGIBLE_GUARDRAIL_BOUGHT_RECENTLY);
         break;
       case errorCode_3008:
         trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS,omnitureMapping.TGS_INELIGIBLE_GUARDRAIL_ALREADY_BOUGHT);
         break;
       case errorCode_2106:
         trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_INELIGIBLE_RAC);
         break;
     }
   }

    if (tgsResponse && Object.keys(tgsResponse)?.length === 0 && tgsMessage) {
      IsBookedOnMMT
        ? trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_INELIGIBLE_RIS_ERROR_FOR_BOOKERS)
        : trackTGSInEligibility(RIS_LANDING_PNR_DETAILS_SUCCESS, omnitureMapping.TGS_INELIGIBLE_RIS_ERROR);
    }

    this.getAdConfig();
  }

  async componentDidUpdate(prevProps) {
    const { recentPnr,tgsEligibility:{isTgEligible:isTgEligible}  } = this.props;
    if (recentPnr) {
      const recentSearchPnr = await getPnrRecentSearch();
      const recentSearchPnrHistory = getNewPnrSearchHistory(recentPnr, recentSearchPnr || null);
      if (recentSearchPnrHistory) {
        await savePnrRecentSearch(recentSearchPnrHistory);
      }
    }

    if (this.props.trainInfo !== prevProps.trainInfo && showBusTrainCrossSell()) {
      if (this.props.trainInfo) {
        const locusResponseData = await getLocusIdFromStationDetails({
          from_station_code: this.props.trainInfo.departStationCode,
          to_station_code: this.props.trainInfo.arrivalStationCode,
        });
        if (locusResponseData?.success) {
          const { fromLocusCode, toLocusCode } = locusResponseData;
          this.railsDestinationLocusCode = toLocusCode;
          this.railsOriginLocusCode = fromLocusCode;
        }
      }
    }

    if (prevProps.tgsEligibility !== this.props.tgsEligibility) {
      if (isTgEligible) {
        const timer = setTimeout(() => {
          this.setState({ showTGSBottomSheet: true });
        }, 3000);
        this.setState({ tgsTimer: timer });
      }
    }
  }

  getAdConfig = async ()=>{
    const { pnrViewState } = this.props;
    const AdsAb = await getAllTypesAdsAb();
    this.setState({showDiffTypeOfAds:AdsAb});
    if (pnrViewState !== ViewState.ERROR) {
      trackAdLoad('mob:funnel:ris:pnr:pnrdetails', AdsAb.trackingPayload);
      trackOmnitureLoadEvent(RIS_LANDING_PNR_DETAILS_SUCCESS);
    }
  };

  updateUserLoggedInStatus = async () => {
    const logInStatus = await isUserLoggedIn();
    this.setState({
      userLoggedIn: logInStatus,
    });

    let tgsButtonClickedFlag = await getDataFromStorage(tgsButtonClicked);
    if (tgsButtonClickedFlag && logInStatus) {
      await removeDataFromStorage(tgsButtonClicked);
      Actions.tgsTravellersPage({ pnrNumber: this.props.pnrNumber });
    }
    else {
      await removeDataFromStorage(tgsButtonClicked);
    }
  };

  async componentDidMount() {
    await this.pokusCall();
    // eslint-disable-next-line
    loginEventListener = DeviceEventEmitter?.addListener(LOGIN_EVENT, this.updateUserLoggedInStatus);
    BackHandler.addEventListener('hardwareBackPress', this.onBackIconPressed);
    // false added to disable mandat feature as design changes, will do mid app release
    const enableMandateLoginPNR =  false && getPokusConfig(PokusLobs.RAIL, AbConfigKeyMappings.railsPNRBlocker, false);
    const enableLoginPersuasion =  getPokusConfig(PokusLobs.RAIL, AbConfigKeyMappings.enableLoginPersuasion, false);
    try {
      if (Platform.OS === 'web') {
        addWebEngageScriptToDOM();
        const { trainInfo } = this.props;
        if (!isEmpty(trainInfo)) {
          const payload = {
            source: trainInfo.departStationCode,
            sourceStation: trainInfo.departStation,
            destination: trainInfo.arrivalStationCode,
            destinationStation: trainInfo.arrivalStation,
            trainNumber: trainInfo.trainNumber,
            quota: trainInfo.travelQuota,
            class: trainInfo.travelClass,
            depDate: trainInfo.journeyDate,
            arrivalDate: trainInfo.arrivalDate,
          };
          await pushPnrStatusIntoGtm(payload);
        }
      }
      this.updateUserLoggedInStatus();
      const logInStatus = await isUserLoggedIn();

      setTimeout(() => {
        this.setState({
          showModal: !logInStatus,
          mandateLogin: enableMandateLoginPNR,
          enableLoginPersuasion,
        });
      }, 2000);
    } catch (e) {
      console.log(e);
    }
  }
  async pokusCall() {
    const railsMealPokusValue = await getRailsMealsPnrDetailsPokus();
    this.setState({ railsMealPokus: railsMealPokusValue });
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackIconPressed);
    loginEventListener?.remove();
  }

  setShowMealBottomSheet = (val) => {
    this.setState({ isBottomSheetVisible: val });
  };

  onBackIconPressed = async () => {
    const timer = await getDataFromStorage(PNR_DETAILS_MEALS_BS_SHOWN_TIMER);
    const interactedWithPNR = await getDataFromStorage(INTERACTED_WITH_MEALS_PNR);
    if (
      this.props.pnrViewState !== ViewState.ERROR &&
      this.state.railsMealPokus &&
      isMealAvailableForPnr(this.props?.pnrDetailInfo?.MealDetails) &&
      !this.state.isBottomSheetVisible &&
      this.state.backButtonClickedCount === 0 &&
      showMealsBottomSheet(timer) &&
      !interactedWithPNR
    ) {
      this.setState({ isBottomSheetVisible: true });
      trackMealsOmnitureLoadEvent(PNR_DETAILS_MEALS_BS_SHOWN);
      this.setState({ backButtonClickedCount: 1 });
      setDataInStorage(PNR_DETAILS_MEALS_BS_SHOWN_TIMER, Date.now());
      return;
    }
    const { mandateLogin, showModal, enableLoginPersuasion } = this.state;
    if ((mandateLogin || !isEmpty(this.props.pnrLoginPersuasionCoupon)) && showModal && enableLoginPersuasion) {
      this.setState({ showModal: false });
      return true;
    }
    if (!this.state.firstBackPress && this.props?.tgsEligibility?.isTgEligible)
    {
      this.setState({ showTGSBottomSheet: true ,firstBackPress:true});
      return;
    }
    this.props.resetPnrStatus();
    const { deeplink } = this.props;
    if (deeplink === 'true') {
      if (Platform.OS === 'ios') {
        ViewControllerModule.popViewController();
      }
      if (Platform.OS === 'android') {
        BackHandler.exitApp();
      }
    } else {
      // Actions.railsPNRStatusLanding({
      //   isFromNewLanding: true,
      //   railsBusCommonLanding: this.state.railsBusCommonLanding,
      // });
      Actions.pop();
    }
    return true;
  };

  _onRefreshTap = () => {
    this.props.initPnrStatus(this.props.pnr);
  };

  _onShareTap = () => {
    const { trainInfo } = this.props;
    if (trainInfo) {
      this.props.onShareButtonClicked(trainInfo);
    }
  };

  _openTrainStatus = () => {
    const { trainInfo } = this.props;
    if (trainInfo) {
      openLiveTrainStatus({ trainDetails: trainInfo });
    }
  };

  _openTrainRoute = async () => {
    const { trainInfo } = this.props;
    const trainNumToSearch = trainInfo.trainNumber;
    Actions.railsTrainSchedule({ trainNumToSearch });
  };

  _openCoachPosition = () => {
    const { coachPosition, trainInfo } = this.props;
    const coach = coachPosition.split(' ');
    trackOmnitureClickEvent(EVENT_RIS_PNR_COACH_POSITION, 'EVENT_RIS_PNR_COACH_POSITION');
    Actions.coachPosition({
      coachList: coach,
      trainName: trainInfo.trainName,
      trainNumber: trainInfo.trainNumber,
      fromPage: 'pnrStatus',
    });
  };

  openDeeplink = () => {
    if (Platform.OS === 'web') {
      const url = 'https://mbus.makemytrip.com/rnw/';
      window.location.href = url;
    }
  };

  sendCoupon = async (userData) => {
    const { email, mobile } = userData;
    try {
      const url =
        'https://railsinfo-services.makemytrip.com/api/rails/pnr/currentstatus/coupons/message';
      const body = {
        pnrNo: this.props.pnrNumber,
        phoneNumber: mobile,
        email,
        trackingParams: {
          affiliateCode: 'MMT001',
          channelCode: _getChannelCode(),
        },
      };
      const response = await fetch2(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain',
        },
        body: JSON.stringify(body),
      });
      if (!response) {
        return null;
      }
      const res = await response.json();
      if (res.status) {
        showShortToast('Success');
        // this.setState({ showModal: false });
        // this.scrollView && this.scrollView.scrollTo({ x: 0, y: 0 });
      }
    } catch (e) {
      this.setState({ showModal: false });
    }
  };

  _onClose = () => {
    const { mandateLogin, showModal, userLoggedIn } = this.state;
    if ((showModal && !mandateLogin) || userLoggedIn) {
      this.setState({ showModal: false });
      this.scrollView && this.scrollView.scrollTo({ x: 0, y: 0 });
      return true;
    }
  };

  _showModal = () => {
    this.scrollToBottom(true);
    this.setState({ showModal: true });
  };

  scrollToBottom = (modalState) => {
    if (modalState) {
      setTimeout(() => {
        if (!this.scrollView) {
          return;
        }
        this.scrollView.scrollToEnd({
          animated: true,
        });
      });
    }
  };

  isWaitListed = (passengerList) => {
    const waitlisting = ['GNWL','RLWL','PQWL','CKWL','RSWL','RQWL','RLGN', 'WL'];
    const wlPassenger = passengerList?.passenger.filter(item => waitlisting.includes(item.pnrStatus));
    if (wlPassenger && wlPassenger.length > 0) {
      return true;
    }
    return false;
  };

  /* eslint-disable */

  conditionCheckToRenderBottomSheet = (_pnrLoginPersuasionCoupon) => {
    const { showModal, mandateLogin, enableLoginPersuasion } = this.state;
    const { CouponHeader: couponHeader, 
      CouponText: couponSubHeader, 
      PnrConfirmationText: couponDetail, 
      PnrConfirmationProbabilityText = [] } = _pnrLoginPersuasionCoupon || {};

    if ((mandateLogin || !isEmpty(_pnrLoginPersuasionCoupon)) && showModal && enableLoginPersuasion) {
      this.scrollToBottom(true);
      return (
        <BottomSheetModalTrain
          onTouchOutside={this._onClose}
          additionalContainerStyle={{zIndex: 1, backgroundColor: colors.lightBlack4 }}
          testID="pnr_login_persuasion_bottomsheet_modal"
        >
          <RailBookingCoupons
            dismiss={this._onClose}
            heading={couponHeader}
            subheading={couponSubHeader}
            description={couponDetail}
            sendCoupon={this.sendCoupon}
            pnrNumber={this.props.trainInfo.pnrNumber}
            mandateLogin={mandateLogin}
            displayConfirmationProbabilityInfo={PnrConfirmationProbabilityText.length > 0}
          />
        </BottomSheetModalTrain>
      );
    }
    return null;
  };

  tgsContainer = (tgsEligibility) => {
    if (tgsEligibility?.isTgEligible) {
      if (!this.state.showTGSComponent || Object.keys(this.state.tgsTextData).length === 0) {
        return <></>;
      }
      return (<TGSComponent tgsTextData={this.state.tgsTextData} 
        performWebLogin={this.performWebLoginAndDivertToTravellersPage} 
        tgsEligibility={tgsEligibility.response} 
        userLoggedIn={this.state.userLoggedIn} 
        pnrNumber={this.props.pnrNumber} />);
    }
    return null;
  };
 closeHandler = () => {
    clearTimeout(this.state.tgsTimer);
    this.setState({ showTGSBottomSheet: false,showTGSComponent:true });
  };

  onFocusChange = () => {
    this.setState({ firstBackPress: false });
    this.closeHandler();
  };

  onDeepLinkClicked = () => {
    this.setState({deepLinkClicked:true});
    this.closeHandler();
  };

  trackClickEvent = () => {
    trackClickEventProp61(RIS_LANDING_PNR_DETAILS_SUCCESS, PNR_DETAILS_MEALS_ORDER_NOW_CLICKED);
  };

  render() {
    const {
      showLoader,
      trainInfo,
      passengerList,
      pnrViewState,
      disclaimer,
      pnrNumber,
      coachPosition,
      enabledPnr,
      pnrLoginPersuasionCoupon,
      tgsEligibility,
    } = this.props;

    const {
      enableLoginPersuasion,
      showDiffTypeOfAds,
      showTGSBottomSheet,
      railsMealPokus,
    } = this.state;
    // const { couponHeader, couponSubHeader, couponDetail, couponButtonText } = pnrSearchCoupon || {};
    const { PnrConfirmationText: couponDetail, PnrConfirmationProbabilityText = [] } =
      pnrLoginPersuasionCoupon || {};
    const busSharedModule = BusSharedModuleHolder.get();
    const BusTrainCrossSell = busSharedModule ? 
    busSharedModule.getBusSharedComponents().BusTrainCrossSellCarousel : null;
    return (
      <RailsIosSwipeGesture
      onSwipeRight={() => (Platform.OS === 'ios' ? this.onBackIconPressed() : null)}
      config={config}
      style={pnrStyles.fullFlex}
      >
      <FocusEffect onFocusOut={this.onFocusChange}>
        <SafeAreaView style={{ flex: 1 }} testID="pnr_status_page_container">
          {pnrViewState === ViewState.LOADING && showLoader && (
            <View style={{ justifyContent: 'center', flex: 1, alignItems: 'center' }} testID="pnr_status_page_loading_view">
                <Spinner size={30} color="#008b8b" />
            </View>
          )}
          <View style={{ justifyContent: 'center' }} elevation={5}>
            { }
            {!showLoader && pnrViewState && (
              <PnrHeader
                onBackIconPressed={this.onBackIconPressed}
                shareButtonClick={this._onShareTap}
                refreshButtonClick={this._onRefreshTap}
                pnrNumber={pnrNumber}
                viewState={pnrViewState}
              />
            )}
            {pnrViewState === ViewState.NO_INTERNET && (
              <NoInternetView onRetry={() => this.props.initPnrStatus(pnrNumber)} />
              )}
            {pnrViewState === ViewState.ERROR && <PnrFlushedErrorView pnrNumber={pnrNumber} />}
            <ScrollView
              ref={(e) => {
                this.scrollView = e;
              }}
              testID="pnr_status_page_scroll_view"
            >
              {pnrViewState === ViewState.SHOW_DETAIL && !showLoader && trainInfo && (
                <View style={pnrStyles.containerStyle} testID="pnr_status_page_container">
                  <PnrTicketStatus
                    passengerList={passengerList}
                    enabled={enabledPnr}
                    pnrNumber={trainInfo.pnrNumber}
                    isPnrAlertEligible={passengerList.isPnrAlertEligible}
                    PnrConfirmationProbabilityText={PnrConfirmationProbabilityText}
                    enableLoginPersuasion={enableLoginPersuasion}
                  />
                    {railsMealPokus && isMealAvailableForPnr(this.props?.pnrDetailInfo?.MealDetails) && (
                      <View style={pnrStyles.mealsEntryPoint} testID="pnr_status_page_meals_entry_point">
                        <RailsMealEntryPoint
                          onBookNowClicked={this.trackClickEvent}
                          pnrNumber={this.props.pnrNumber}
                        />
                      </View>
                    )}
                  {this.tgsContainer(tgsEligibility)}
                  {!tgsEligibility?.isTgEligible &&
                    tgsEligibility?.error?.errorCode !== errorCode_3007 &&
                    BusTrainCrossSell &&
                    this.railsDestinationLocusCode &&
                    this.railsOriginLocusCode &&
                    showBusTrainCrossSell() &&
                    this.isWaitListed(passengerList) && (
                      <BusTrainCrossSell
                        railsOriginLocusCode={this.railsOriginLocusCode}
                        railsDestinationLocusCode={this.railsDestinationLocusCode}
                        departureDate={trainInfo?.journeyDate}
                        deeplinkSource={'TrainsPnrStatusPage'}
                      />
                    )}
                  {enableCouponBanner && couponDetail && (
                    <RailCouponView description={couponDetail} onClick={this._showModal} />
                  )}
                  {!tgsEligibility?.isTgEligible && Platform.OS !== 'web' && (
                    <AlternateJourney onCloseTGSBottomSheet={this.onDeepLinkClicked} />
                  )}
                  <TrainDetails
                    trainInfo={trainInfo}
                      openLiveTrainStatus={this._openTrainStatus}
                      openTrainRoute={this._openTrainRoute}
                      seeCoachPosition={this._openCoachPosition}
                      coachPosition={coachPosition}
                    />
                    {!!showDiffTypeOfAds.multi_banner && (
                      <View style={pnrStyles.addsStyle}>
                        {getAdsCard(Platform.OS, RIS_PNR_RESULT)}
                      </View>
                    )}
                  <PnrCrossSellHotels />
                  <View>
                    <View>
                      <Text style={pnrStyles.disclaimerStyle}>Disclaimer</Text>
                    </View>
                    <View style={pnrStyles.disclaimerContainer}>
                      <Text style={pnrStyles.disclaimerText}>{disclaimer}</Text>
                    </View>
                  </View>
                </View>
              )}
            </ScrollView>
            {this.conditionCheckToRenderBottomSheet(pnrLoginPersuasionCoupon)}
            {tgsEligibility?.isTgEligible && showTGSBottomSheet && Object.keys(this.state.tgsTextData).length !== 0 && (
              <TGSBottomSheet
               toggleModalState={this.closeHandler}
              >
                <TGSComponent
                  tgsTextData={this.state.tgsTextData}
                  onClose={this.closeHandler}
                  bottomSheet={true}
                  performWebLogin={this.performWebLoginAndDivertToTravellersPage}
                  tgsEligibility={tgsEligibility.response}
                  userLoggedIn={this.state.userLoggedIn}
                  pnrNumber={this.props.pnrNumber}
                />
                  </TGSBottomSheet>
                )}
            </View>
            {railsMealPokus && this.state.isBottomSheetVisible && (
              <MealBottomSheet
                setShowMealBottomSheet={this.setShowMealBottomSheet}
                isPnr={true}
                pnrNumberFromPnrPage={this.props.pnrNumber}
              />
            )}
        </SafeAreaView>
      </FocusEffect>
     </RailsIosSwipeGesture>
    );
  }
}

PnrStatusPage.propTypes = {
  isChartPrepared: PropTypes.bool,
  initPnrStatus: PropTypes.func.isRequired,
  onBackIconPressed: PropTypes.func.isRequired,
  onPnrAlertClicked: PropTypes.func.isRequired,
  onShareButtonClicked: PropTypes.func.isRequired,
  handleDeepLink: PropTypes.func.isRequired,
  openTrainRoute: PropTypes.func.isRequired,
  tgsEligibility: PropTypes.object,
  resetPnrStatus: PropTypes.func.isRequired,
  pnr: PropTypes.string,
  IsBookedOnMMT: PropTypes.bool,
  recentPnr: PropTypes.object,
  pnrPage: PropTypes.bool,
  passengerList: PropTypes.object,
  pnrViewState: PropTypes.string,
  trainInfo: PropTypes.object,
  pnrDetailInfo: PropTypes.object,
  deeplink: PropTypes.string,
  pnrLoginPersuasionCoupon: PropTypes.object,
  coachPosition: PropTypes.string,
  showLoader: PropTypes.bool,
  disclaimer: PropTypes.string,
  enabledPnr: PropTypes.bool,
  pnrSearchCoupon: PropTypes.object,
  pnrLoginPersuasionCoupon: PropTypes.object,
  pnrNumber: PropTypes.string,
};

PnrStatusPage.defaultProps = {
  isChartPrepared: false,
  tgsEligibility:{},
};

const pnrStyles = StyleSheet.create({
  containerStyle: {
    backgroundColor: colors.grey11,
  },
  mealsEntryPoint: {
    marginLeft: 0,
    marginRight: 0,
    marginBottom: -9,
  },
  disclaimerStyle: {
    fontSize: 22,
    fontFamily: fonts.light,
    color: colors.defaultTextColor,
    marginTop: 15,
    marginBottom: 15,
    marginLeft: 16,
  },
  addsStyle: { margin: 16, flexDirection: 'row', justifyContent: 'center' },
  disclaimerText: {
    fontFamily: fonts.bold,
    fontSize: 12,
    color: colors.black,
  },
  disclaimerContainer: {
    height: 75,
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 30,
  },
  fullFlex: {
    flex: 1,
  },
});

export default PnrStatusPage;
