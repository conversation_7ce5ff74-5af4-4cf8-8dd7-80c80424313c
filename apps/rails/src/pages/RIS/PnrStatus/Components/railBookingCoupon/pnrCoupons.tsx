import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, Platform, DeviceEventEmitter } from 'react-native';
import { isEmpty } from 'lodash';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import HTMLView from 'react-native-htmlview';
import { ModalProps } from './railCouponView';
import giftBody from '@mmt/legacy-assets/src/giftCouponIcon.webp';
import tickBody from '@mmt/legacy-assets/src/tick_white.webp';
import KeyboardAvoidingView from './KeyboardAvoidingViewWrapper';
import { performLogin, isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { LOGIN_EVENT } from '@mmt/legacy-commons/Common/constants/AppConstants';
import { getContactDetailsFromLoggedInUser } from '@mmt/legacy-commons/Common/Components/ContactDetails/contactDetailsUtils';

let loginEventListener:unknown;

const RailBookingCoupons = (props: ModalProps) => {
  const { heading, subheading, description, sendCoupon,
     mandateLogin, dismiss, displayConfirmationProbabilityInfo } = props;
  const [userLoggedIn, setUserLoggedIn] = useState(false);
  const [userData, setUserData] = useState({});
  useEffect(() => {
    updateUserLoggedInStatus();
    // eslint-disable-next-line
    loginEventListener = DeviceEventEmitter?.addListener(LOGIN_EVENT, updateUserLoggedInStatus);
    return () => {
      loginEventListener?.remove();
    };
  }, []);

  const updateUserLoggedInStatus = async () => {
    const loginStatus = await isUserLoggedIn();
    if (loginStatus) {
      const userLogedData = await getContactDetailsFromLoggedInUser();
      setUserData(userLogedData);
    }
    setUserLoggedIn(loginStatus);
  };

  const loginOrSignUp = () => {
    performLogin();
  };

  const closeBottomsheet = () => {
    dismiss();
  };
  const mobileOrEmail = userData.mobile ? `phone <br/>number <b>${userData.mobile}</b>` : `email <b>${userData.email}</b>`;
  const loggedInHeading = "<p><h2 style=\"color:black\"><span style='color:#04C694'>₹ 50 coupon</span> for your <br/> next train booking sent</h2></p>";
  const loggedInInfo = `<p>The coupon has been sent to your ${mobileOrEmail}.<br/><br/>You can also opt in for PNR alerts to your phone.</p>`;

  const probablityInfoText = displayConfirmationProbabilityInfo ? 'Also get to know the probability of confirmation on your ticket' : '';
  const foreLoginHeader = "<p><h2 style=\"color:black\"><span style='color:#04C694'>Login/ Signup</span> to know <br/>your PNR status</h2></p>";
  const foreceloginInfo = `<p>First time user get <b>₹ 50 coupon</b> for the next train booking.${probablityInfoText}</p>`;

  const renderCouponSent = () => {
    return (
      <>
        <View testID="pnr_coupon_sent_container">
          <LinearGradient
            start={{ x: 0.0, y: 0.0 }} end={{ x: 1.0, y: 1.0 }}
            colors={['#ace143', '#219393']}
            style={styles.greenTick}
            testID="pnr_coupon_sent_container"
          >
            <Image
              source={tickBody}
              style={{ width: 43, height: 33 }}
              testID="pnr_coupon_sent_container"
            />
          </LinearGradient>
          <HTMLView
            value={''}
            stylesheet={headerstyles}
            testID="pnr_coupon_sent_container"
          />
          <View style={{ marginBottom: -50 }} />
          <HTMLView
            stylesheet={subHeaderStyle}
            value={loggedInHeading}
            testID="pnr_coupon_sent_container"
          />

          <View style={{ marginBottom: -20 }} />
          <HTMLView
            stylesheet={descriptionStyle}
            value={loggedInInfo}
          />
        </View>

      </>
    );
  };

  const renderMandateLogin = () => {
    return (
      <>
        <View>
          <HTMLView
            value={''}
            stylesheet={headerstyles}
          />
          <View style={{ marginBottom: -50 }} />
          <HTMLView
            stylesheet={subHeaderStyle}
            value={foreLoginHeader} />

          <View style={{ marginBottom: -20 }} />
          <HTMLView
            stylesheet={descriptionStyle}
            value={foreceloginInfo}
          />
        </View>
        <View style={[styles.containerButton, styles.fullwidth]}>
          <View>
            <TouchableRipple onPress={loginOrSignUp} style={{ marginBottom: 30 }}>
              <LinearGradient
                start={{ x: 0.0, y: 0.0 }} end={{ x: 1.0, y: 1.0 }}
                colors={['#53b2fe', '#065af3']}
                style={styles.mandateLogin}>
                <Text
                  style={styles.buttonText}>
                  {'Login / SignUp'}
                </Text>
              </LinearGradient>
            </TouchableRipple>
          </View>
        </View>
      </>
    );
  };

  const renderLoginPersuasion = () => {
    return (
      <>
        <View>
          <Image source={giftBody} style={{ width: 91, height: 91, marginTop: -72, marginBottom: 30 }} />
          {!isEmpty(heading) && <HTMLView
            value={heading}
            stylesheet={headerstyles}
          />}
          <View style={{ marginBottom: -50 }} />
          {!isEmpty(subheading) && <HTMLView
            stylesheet={subHeaderStyle}
            value={subheading} />}

          <View style={{ marginBottom: -20 }} />
          {!isEmpty(description) && <HTMLView
            stylesheet={descriptionStyle}
            value={description}
          />}
        </View>
        <View style={styles.containerButton}>
          <View>
            <TouchableRipple onPress={closeBottomsheet} style={{ width: '100%', marginBottom: 30 }}>
              <Text
                style={styles.dismissText}>
                {'Dismiss'}
              </Text>
            </TouchableRipple>
          </View>
          <View>
            <TouchableRipple onPress={loginOrSignUp} style={{ width: '100%', marginBottom: 30 }}>
              <LinearGradient
                start={{ x: 0.0, y: 0.0 }} end={{ x: 1.0, y: 1.0 }}
                colors={['#53b2fe', '#065af3']}
                style={styles.msgButton}>
                <Text
                  style={styles.buttonText}>
                  {'Login / SignUp'}
                </Text>
              </LinearGradient>
            </TouchableRipple>
          </View>
        </View>
      </>
    );
  };

  const checkMandateFlow = () => {
    if (mandateLogin && !userLoggedIn) {
      return renderMandateLogin();
    }
    if (!userLoggedIn) {
      return renderLoginPersuasion();
    }
    if (userLoggedIn && isEmpty(subheading)) {
      dismiss();
      return null;
    }
    sendCoupon(userData);
    return renderCouponSent();
  };
  return (
    <KeyboardAvoidingView>
      <View style={{ backgroundColor: colors.white, alignItems: 'flex-start', padding: 30, alignContent: 'center' }}>
        {checkMandateFlow()}
      </View>
    </KeyboardAvoidingView>
  );
};

const headerstyles = StyleSheet.create({
  h6: {
    color: colors.lightTextColor,
    fontFamily: fonts.bold,
    fontSize: 14,
    marginBottom: Platform.OS === 'web' ? 20 : 0,

  },
});

const subHeaderStyle = StyleSheet.create(
  {
    h2: {
      color: colors.black,
      fontFamily: fonts.black,
      fontSize: 28,
      marginBottom: Platform.OS === 'web' ? 30 : 0,
    },
    span: {
      color: colors.green13,
    },
  },
);

const descriptionStyle = StyleSheet.create(
  {
    b: {
      fontFamily: fonts.bold,
    },
    h2: {
      color: colors.black,
      fontFamily: fonts.black,
      fontSize: 16,
      marginTop: Platform.OS === 'web' ? 20 : 0,
    },
    span: {
      color: colors.black6,
    },
  },
);

const styles = StyleSheet.create({
  msgButton: {
    width: 188,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mandateLogin: {
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  greenTick: {
    marginTop: -72, marginBottom: 30,
    width: 90,
    height: 90,
    borderRadius: 50,
    overflow: 'hidden',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: fonts.black,
    textTransform: 'uppercase',
  },
  dismissText: {
    fontWeight:'500',
    color: colors.primary,
    letterSpacing: 0.32,
    fontSize: 16,
    fontFamily: fonts.black,
    backgroundColor: colors.transparent,
    textTransform: 'uppercase',
  },
  containerButton: {
    flexDirection: 'row',
    marginTop: 50,
    marginBottom: 31,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  fullwidth: {
    flex: 1,
  },
});

export default RailBookingCoupons;
