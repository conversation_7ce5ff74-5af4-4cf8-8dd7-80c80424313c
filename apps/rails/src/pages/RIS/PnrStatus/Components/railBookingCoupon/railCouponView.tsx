/* eslint-disable */
import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Platform } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import HTMLView from 'react-native-htmlview';

export interface ModalProps {
  mandateLogin?: boolean;
  pnrNumber?: string;
  heading: string;
  subheading: string;
  description: string;
  displayConfirmationProbabilityInfo: boolean;
  sendCoupon: (userdata: object) => void;
  dismiss: () => void;
  onClick: () => void;
}
import giftBody from '@mmt/legacy-assets/src/giftCouponIcon.webp';

const RailCouponView = (props: ModalProps) => {
  const { description } = props;
  return (
    <View
      style={{
        backgroundColor: colors.governorBay,
        height: 56,
        alignContent: 'center',
        marginTop: 10,
        marginHorizontal: 8,
        flexDirection: 'row',
      }}
      testID="rail_coupon_view_container"
    >
      <Image
        source={giftBody}
        style={couponStyle.imageStyle}
        testID="rail_coupon_view_container_image"
      />
      <View
        style={{ justifyContent: 'space-between', flexDirection: 'row', marginHorizontal: 8 }}
        testID="rail_coupon_view_container_view"
      >
        <HTMLView
          value={description}
          stylesheet={descriptionStyle}
          testID="rail_coupon_view_container_html_view"
        />
        <TouchableOpacity
          onPress={props.onClick}
          style={{ alignContent: 'flex-end' }}
          testID="rail_coupon_view_container_touchable_opacity"
        >
          <Text style={couponStyle.saveText} testID="rail_coupon_view_container_text">
            Save Coupon
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const descriptionStyle = StyleSheet.create({
  p: {
    color: colors.transparentColour,
    fontFamily: fonts.regular,
    width: 200,
    fontSize: 12,
    marginTop: 13,
    marginRight: 16,
  },
  b: {
    fontFamily: fonts.bold,
    color: colors.white,
    marginLeft: Platform.OS === 'web' ? -13 : 0,
  },
  h2: {
    color: colors.white,
    fontFamily: fonts.regular,
    fontSize: 12,
  },
  span: {
    color: colors.white,
  },
});

const couponStyle = StyleSheet.create({
  saveText: {
    color: colors.white,
    fontFamily: fonts.bold,
    fontSize: 12,
    width: 70,
    marginTop: 13,
    alignSelf: 'center',
    textTransform: 'uppercase',
  },

  imageStyle: {
    width: 36,
    height: 36,
    marginVertical: 10,
    marginLeft: 16,
    marginRight: 12,
  },
});

export default RailCouponView;
