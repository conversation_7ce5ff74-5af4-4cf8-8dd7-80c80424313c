import React from 'react';
import { Platform, KeyboardAvoidingView } from 'react-native';

interface KeyboardAvoidingViewWrapperProps {
  children: React.ReactChild | React.ReactChild[]
}

export default function KeyboardAvoidingViewWrapper(props: KeyboardAvoidingViewWrapperProps) {
  const { children } = props;
  if (Platform.OS === 'ios') {
    return (
      <KeyboardAvoidingView behavior="padding">
        {children}
      </KeyboardAvoidingView>
    );
  }
  return (
    <KeyboardAvoidingView style={{ marginBottom: 30 }}>
      {children}
    </KeyboardAvoidingView>
  );
}
