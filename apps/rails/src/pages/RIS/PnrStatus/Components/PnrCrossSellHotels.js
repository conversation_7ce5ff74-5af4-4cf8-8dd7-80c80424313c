import React from 'react';
import {connect} from 'react-redux';
import { View, Text, ScrollView, Image, StyleSheet } from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import {trackNewListingClickEvent} from '../../../../railsAnalytics';
import LinearGradient from 'react-native-linear-gradient';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';

import rightArrow from '@mmt/legacy-assets/src/ic-arrowfwd-blue.webp';
import hotelImage from '@mmt/legacy-assets/src/ic-hotel.webp';
import PropTypes from 'prop-types';

class PnrCrossSellHotels extends React.Component {
  render() {
    const {hotelCrossSellData} = this.props;
    if (!hotelCrossSellData) {
      return (<View />);
    }
    const {cardData: {Cross_Sell_Personalised: {data: {heading, cardDetails}}}} = hotelCrossSellData;
    const header = heading;
    const hotel = cardDetails[cardDetails.length - 1];
    const viewMoreDeeplink = hotel.viewMore.deepLink;
    const hotelList = [];
    hotel.hotels.forEach((hotel) => {
      hotelList.push(new HotelUtils(hotel));
    });
    return (
      <View style={hotelStyle.container}>
        {hotelCrossSellData &&
          <View>
            <View>
              <Text style={hotelStyle.heading}>
                {header}
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{marginTop: 12}}>
                {
                  hotelList.map(item => (
                    <View key={item.id} style={{marginRight: 8}}>
                      <HotelCard item={item} />
                    </View>))
                }
                <View>
                  <ViewAllCard viewMoreDeeplink={viewMoreDeeplink} />
                </View>
              </ScrollView>
            </View>
            <View style={{
              backgroundColor: colors.white,
              height: 20,
            }}
            />
          </View>
          }
      </View>);
  }
}

const HotelCard = ({item}) => {
  if (!item.isLastItem) {
    return (
      <TouchableRipple onPress={() => {
        trackNewListingClickEvent('mob_rail_listing_v2_hotel_clicked');
        GenericModule.openDeepLink(item.itemDeepLink);
      }}
      >
        <View>
          <Card style={{
            marginHorizontal: 2, marginVertical: 2, minWidth: 132, borderRadius: 4,
          }}
          >
            <View style={hotelsStyle.hotelImageContainer}>
              <Image source={{uri: item.hotelImageUri}} resizeMode="stretch" style={hotelsStyle.hotelImage} />
            </View>
            <View style={hotelsStyle.ratingContainer}>
              <RatingLinearGradient rating={item.cumulativeRating} />
            </View>
            <View style={hotelsStyle.fareContainer}>
              <RupeeText style={hotelsStyle.fareText}>
                {numAppendedWithRuppeeSymbol(item.slashedPrice)}
              </RupeeText>
              <Text style={hotelsStyle.hotelName}>{item.hotelName}
              </Text>
              <Text style={hotelsStyle.hotelArea}>{item.address}</Text>
            </View>
          </Card>
        </View>
      </TouchableRipple>
    );
  }
};

const ViewAllCard = ({viewMoreDeeplink}) => (
  <TouchableRipple onPress={() => {
    trackNewListingClickEvent('mob_rail_listing_v2_hotel_view_all_clicked');
    GenericModule.openDeepLink(viewMoreDeeplink);
  }}
  >
    <View>
      <Card style={{
        marginRight: 16, marginLeft: 2, marginVertical: 2, width: 132, borderRadius: 4, height: 160,
      }}
      >
        <View style={{marginTop: 30, marginLeft: 16}}>
          <Image source={hotelImage} style={{width: 36, height: 36, marginBottom: 12}} />
          <Text style={viewAllCardStyles.headerText}>View
          </Text>
          <Text style={viewAllCardStyles.subHeaderText}>All Hotels
          </Text>
          <Image source={rightArrow} style={{width: 27, height: 6}} />
        </View>
      </Card>
    </View>
  </TouchableRipple>
);

const RatingLinearGradient = ({rating}) => (
  <LinearGradient
    style={ratingLgStyles.container}
    colors={['#00d2ff', '#3a7bd5']}
    start={{x: 0.0, y: 0.0}}
    end={{x: 1.0, y: 0.0}}
  >
    <Text style={ratingLgStyles.ratingText}>
      {rating}/5
    </Text>
  </LinearGradient>
);


class HotelUtils {
  rating = null;
  totalReviewCount = null;
  hotelName = null;
  actualFare = null;
  slashedPrice = null;
  address = null;
  itemDeepLink = null;
  hotelImageUri = null;
  cumulativeRating = null;
  id = null;
  constructor(hotel) {
    this.rating = hotel.starRating;
    this.totalReviewCount = hotel.flyfishReviewSummary.MMT.totalReviewsCount;
    this.hotelName = hotel.name;
    this.actualFare = hotel.displayFare.actualPrice.value;
    this.slashedPrice = hotel.displayFare.slashedPrice.value;
    this.isPAHAvailable = hotel.isPAHAvailable;
    this.isFreeCancellation = hotel.isFreeCancellation;
    this.address = hotel.address.line2;
    this.itemDeepLink = hotel.appDeeplink;
    this.cumulativeRating = hotel.flyfishReviewSummary.MMT.cumulativeRating;
    this.hotelImageUri = `https:${hotel.mainImages[0]}`;
    this.id = hotel.id;
  }
}

const hotelsStyle = StyleSheet.create({
  rootContainerStyle: {
    marginTop: 16,
    marginLeft: 16,
    marginBottom: 16,
    backgroundColor: colors.white,
    height: 222,
  },
  mainHeaderStyle: {
    fontFamily: fonts.regular,
    fontSize: 22,
    color: colors.black,
  },
  hotelImageContainer: {
    height: 80, backgroundColor: colors.grey0, borderTopLeftRadius: 4, borderTopRightRadius: 4,
  },
  hotelImage: {flex: 1, width: '100%'},
  ratingContainer: {position: 'absolute', top: 72, right: 4},
  fareContainer: {
    backgroundColor: colors.transparent , marginHorizontal: 8, marginTop: 12, marginBottom: 10, 
    borderBottomLeftRadius: 4, borderBottomRightRadius: 4,
  },
  fareText: {
    fontSize: 16, fontFamily: fonts.bold, color: colors.black, marginBottom: 6,
  },
  hotelName: {
    fontSize: 12, fontFamily: fonts.medium, color: colors.black, marginBottom: 6,
  },
  hotelArea: {fontSize: 12, fontFamily: fonts.regular, color: colors.defaultTextColor},
});

const ratingLgStyles = StyleSheet.create({
  container: {
    borderRadius: 10, alignItems: 'center', justifyContent: 'center',
  },
  ratingText: {
    fontSize: 10, fontFamily: fonts.semiBold, color: colors.white, backgroundColor: colors.transparent, 
    marginHorizontal: 7, marginVertical: 2,
  },
});

const viewAllCardStyles = StyleSheet.create({
  headerText: {
    color: colors.azure, fontSize: 16, fontFamily: fonts.bold, marginBottom: 4,
  },
  subHeaderText: {
    color: colors.azure, fontSize: 16, fontFamily: fonts.bold, marginBottom: 12,
  },
});
const hotelStyle = StyleSheet.create({
  container: {
    paddingTop: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    paddingBottom: 8,
    marginTop: 10,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
  heading: {
    fontSize: 22,
    fontFamily: fonts.light,
    color: colors.black,
  },
});

const mapStatesToProps = (state, ownProps) => {
  const {
    hotelCrossSellData,
  } = state.railInfoReducer;
  return {
    ...ownProps,
    hotelCrossSellData,
  };
};

PnrCrossSellHotels.propTypes = {
  hotelCrossSellData: PropTypes.object,
};

HotelCard.propTypes = {
  item: PropTypes.object,
  rating: PropTypes.string,
};

ViewAllCard.propTypes = {
  viewMoreDeeplink: PropTypes.string,
};

RatingLinearGradient.propTypes = {
  rating: PropTypes.string,
};


export default connect(mapStatesToProps, null)(PnrCrossSellHotels);

