/* eslint-disable */
import React from 'react';
import { connect } from 'react-redux';
import {DownArrow,UpArrow} from '../TGSUtils/TGSAssets';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import LinearGradient from 'react-native-linear-gradient';
import {View, Text, Switch, Image, StyleSheet, Platform, DeviceEventEmitter, TouchableWithoutFeedback} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import ViewState from '../../../../Utils/ViewState';
import { isUserLoggedIn, performLogin } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { onPnrAlertClicked } from '../RailInfoAction';
import LottieView from 'lottie-react-native';
import HTMLView from 'react-native-htmlview';
import {fontStyle} from '../../../../vernacular/VernacularUtils';
import { _label } from '../../../../vernacular/AppLanguage';
import { LOGIN_EVENT } from '@mmt/legacy-commons/Common/constants/AppConstants';

import lottieJson from './railBookingCoupon/data';
import greenTick from '@mmt/legacy-assets/src/green_tick.webp';
import redCross from '@mmt/legacy-assets/src/red_tick_cross.webp';
import userIcon from '@mmt/legacy-assets/src/user_icon.webp';
let loginEventListener;
class PnrTicketStatus extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // eslint-disable-next-line react/prop-types
      enabled: this.props.data ? this.props.data.enabled : false,
      isPassengerListExtended:false,
    };
  }

  UNSAFE_componentWillMount() {
    const { enabled } = this.props;
    this.setState({ enabled });
  }

  updateUserLoggedInStatus = async () => {
    const loginStatus = await isUserLoggedIn();
    this.setState({ isUserLoggedIn: loginStatus });
  };

  async componentDidMount() {
    this.updateUserLoggedInStatus();
    loginEventListener =  DeviceEventEmitter?.addListener(LOGIN_EVENT, this.updateUserLoggedInStatus);
  }

  componentWillUnmount() {
    loginEventListener?.remove();
  }

  renderProbability = () => {
    if (this.props.PnrConfirmationProbabilityText.length > 0) {
      return (
        <View style={pnrTicketStyle.infoContainer}>
          {this.props.PnrConfirmationProbabilityText.map((el) => <HTMLView stylesheet={infostyles} value={el} />)}
        </View>
      );
    }
    return null;
  };

  renderProbabilityPersuasion = () => {
    return (
      <>
        <View style={[ pnrTicketStyle.infoContainer,pnrTicketStyle.nonloginInfoContainer]} testID="pnr_ticket_status_info_container">
          <View style={{ width: '70%' }} testID="pnr_ticket_status_info_container_view">
            <Text style={pnrTicketStyle.nonloginInfo} testID="pnr_ticket_status_info_container_text">
              Your ticket has <Text 
              style={pnrTicketStyle.blurText} testID="pnr_ticket_status_info_container_text_text"> xx </Text>% chance of getting confirmed</Text>
          </View>
          <View  >
            <TouchableRipple onPress={() => performLogin()} testID="pnr_ticket_status_info_container_login_button">
              <Text style={pnrTicketStyle.loginBtn} testID="pnr_ticket_status_info_container_login_button_text">LOGIN</Text>
            </TouchableRipple>
          </View>
        </View>
        <View style={pnrTicketStyle.persuasionContainer} testID="pnr_ticket_status_persuasion_container">
          <View testID="pnr_ticket_status_persuasion_container_view">
            <Image source={userIcon} style={pnrTicketStyle.userIcon} testID="pnr_ticket_status_persuasion_container_user_icon" />
          </View>
          <View testID="pnr_ticket_status_persuasion_container_view_text">
            <Text style={pnrTicketStyle.persuasionText} testID="pnr_ticket_status_persuasion_container_text">
              Mr. Raghav got a confirmed ticket despite 85% confirmation chance. 
              Login to access Makemytrip’s accurate prediction.
            </Text>
          </View>
        </View>
      </>
    );
  };

  renderStatus = (status) => {
    const waitlisting = ['GNWL','RLWL','PQWL','CKWL','RSWL','RQWL','RLGN', 'WL'];
    return (
      <LinearGradient
        style={pnrTicketStyle.statusbar}
        colors={waitlisting.includes(status) ? ['#f3d452','#f09819'] : ['#43e1a8','#28beb2']}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 1.0 }}
        testID="pnr_ticket_status_statusbar"
      >
        <Text style={{ fontSize: 10, fontFamily: fonts.bold, color: colors.white }} testID="pnr_ticket_status_statusbar_text">
          {waitlisting.includes(status) ? 'WL' : 'CNF'}
        </Text>
      </LinearGradient>
    );
  };

  checkRenderingOfProbabilityInfo = (PnrConfirmationProbabilityText,enableLoginPersuasion) =>{
    if (PnrConfirmationProbabilityText.length > 0 && enableLoginPersuasion){
      return  this.state.isUserLoggedIn ? this.renderProbability() : this.renderProbabilityPersuasion();
    }
    return null;
  };


  renderPassengersSectionTwo = (passengerCount) => {
    const { isPassengerListExtended } = this.state;
    return (
      <View style={pnrTicketStyle.tncView} testID="pnr_ticket_status_tnc_view">
          <View style={pnrTicketStyle.pnrTicketStyleView} testID="pnr_ticket_status_tnc_view_view">
        <TouchableWithoutFeedback onPress={() => 
        { this.setState({ isPassengerListExtended: !isPassengerListExtended }); }} 
        style={pnrTicketStyle.tncHeaderContainer}>
          <View style={pnrTicketStyle.tncHeaderContainer} testID="pnr_ticket_status_tnc_view_header_container">
          {isPassengerListExtended ?   <Text style={pnrTicketStyle.tnc}>
            {_label('hide', { defaultLang: true })}</Text> : 
            <Text style={pnrTicketStyle.tnc} testID="pnr_ticket_status_tnc_view_text">{_label('see_all', { defaultLang: true })}</Text>}
            {isPassengerListExtended ? 
            <UpArrow style={pnrTicketStyle.downArrow} /> : <DownArrow style={pnrTicketStyle.downArrow} />}
          </View>
          </TouchableWithoutFeedback>
          {!isPassengerListExtended &&
            <View testID="pnr_ticket_status_tnc_view_more_passengers_container">
              <Text style={{color:colors.grey29}} testID="pnr_ticket_status_tnc_view_more_passengers_text">+{passengerCount} 
                {_label('more_passengers', { defaultLang: true })}</Text>
            </View>
  }
          </View>
      </View>
    );
  };


  renderPassengers = (passengers) => (<View style={pnrTicketStyle.passengerRowStyle}>
    <View style={{ justifyContent: 'flex-start', flex: 1, width: '100%' }} testID="pnr_ticket_status_passenger_row_view">
      <Text style={[pnrTicketStyle.passengerNameStyle, fontStyle('regular')]} testID="pnr_ticket_status_passenger_row_text">{passengers.passengerName}</Text>
    </View>

    <View style={{ justifyContent: 'space-between', flex: 1, width: '100%', alignItems: 'center', flexDirection: 'row' }} testID="pnr_ticket_status_passenger_row_status_container">
      <Text style={[pnrTicketStyle.currentStatusStyle, fontStyle('black')]} testID="pnr_ticket_status_passenger_row_status_text">{passengers.currentStatus}</Text>
      {this.renderStatus(passengers.pnrStatus)}
      {(passengers.ticketStatus || passengers.predictionPercentage) &&
        <LinearGradient
          style={pnrTicketStyle.buttonConfirm}
          colors={this._gradientColorFor(passengers.ticketStatus)}
          start={{ x: 1.0, y: 0.0 }}
          end={{ x: 0.0, y: 0.0 }}
          testID="pnr_ticket_status_passenger_row_status_linear_gradient"
        >
          <Text style={[{ fontSize: 10, color: colors.white }, fontStyle('bold')]} testID="pnr_ticket_status_passenger_row_status_text_text">
            {this._getStatusText(passengers.ticketStatus.toLowerCase(), passengers.predictionPercentage)}
          </Text>
        </LinearGradient>}
    </View>
    <View style={{ justifyContent: 'flex-start', flex: 1, width: '100%' }} testID="pnr_ticket_status_passenger_row_booking_status_container">
      <Text style={[pnrTicketStyle.bookingStatusStyle, fontStyle('regular')]} testID="pnr_ticket_status_passenger_row_booking_status_text">{passengers.bookingStatus}</Text>
    </View>
  </View>);

  render() {
    const {
      enableLoginPersuasion,pnrNumber, pnrAlertSet, isPnrAlertEligible, 
      pnrAlertState, message, PnrConfirmationProbabilityText = [], passengerList: { passenger: list = [] } = {},
    } = this.props;
    const passengersList = [{ itemZero: true }, ...list];
    const { isPassengerListExtended,isUserLoggedIn } = this.state;
    const passengersListSectionOne = passengersList.filter((_,idx) => idx < 3);
    const passengersListSectionTwo = passengersList.filter((_,idx) => idx > 2);
    return (
      <View style={pnrTicketStyle.containerStyle} testID="pnr_ticket_status_container">
        <View>
          {passengersListSectionOne.map(passengers => (
            (passengers.itemZero ?
              <View testID="pnr_ticket_status_sub_header_container">
                <View style={pnrTicketStyle.subHeaderStyle} testID="pnr_ticket_status_sub_header_style">
                  <View style={{ justifyContent: 'flex-start', flex: 1, width: '100%' }} testID="pnr_ticket_status_sub_header_text_container">
                    <Text style={pnrTicketStyle.subHeaderTextStyle} testID="pnr_ticket_status_sub_header_text_container_text">Passenger{'\n'}Name</Text>
                  </View>
                  <View style={{ justifyContent: 'flex-start', flex: 1, width: '100%' }} testID="pnr_ticket_status_sub_header_text_container_two">
                    <Text style={pnrTicketStyle.subHeaderTextStyle} testID="pnr_ticket_status_sub_header_text_container_two_text"> Current{'\n'} Status</Text>
                  </View>
                  <View style={{ justifyContent: 'flex-start', flex: 1, width: '100%' }} testID="pnr_ticket_status_sub_header_text_container_three">
                    <Text style={[pnrTicketStyle.subHeaderTextStyle, { marginLeft: 30, marginRight: 16, textAlign:'center' }]} testID="pnr_ticket_status_sub_header_text_container_three_text">Status at{'\n'}Booking</Text>
                  </View>
                </View>
                <View style={pnrTicketStyle.horizontalBar} testID="pnr_ticket_status_horizontal_bar" />
              </View>
              :
              this.renderPassengers(passengers)
            )))}
                {isPassengerListExtended &&
            passengersListSectionTwo.map((passengers) => (this.renderPassengers(passengers)))
          }
          {passengersListSectionTwo.length !== 0 ?
            this.renderPassengersSectionTwo(passengersListSectionTwo.length) : <View style={{marginVertical:10}} />}
        </View>
        {this.checkRenderingOfProbabilityInfo(PnrConfirmationProbabilityText,enableLoginPersuasion)}
        <View style={pnrTicketStyle.horizontalBar} testID="pnr_ticket_status_horizontal_bar" />
       {isPnrAlertEligible && ((Platform.OS !== 'web') && isUserLoggedIn) &&
          <View testID="pnr_ticket_status_alert_container">
            <View style={pnrTicketStyle.alertContainer} testID="pnr_ticket_status_alert_container_view">
              <View style={{ flexDirection: 'row' }} testID="pnr_ticket_status_alert_container_view">
                <LottieView
                  source={lottieJson}
                  loop={false}
                  autoPlay={true}
                  progress={0}
                  style={{ width: 24, height: 24 }}
                  testID="pnr_ticket_status_alert_container_view_lottie_view"
                />
                <View>
                  <Text style={pnrTicketStyle.pnrAlertTextStyle} testID="pnr_ticket_status_alert_container_view_text">Turn on message alert for</Text>
                  <Text style={pnrTicketStyle.pnrNumberTextStyle} testID="pnr_ticket_status_alert_container_view_text_text">PNR {pnrNumber}</Text>
                </View>
              </View>
              <View style={{ paddingRight: 30 }} testID="pnr_ticket_status_alert_container_view_switch_container">
                <Switch
                  tintColor="#cccccc"
                  thumbColor={[(this.state.enabled ? '#008cff' : '#f5f5f5')]}
                  onTintColor="#d7edff"
                  onValueChange={() => this._pnrAlertSwitchClicked(pnrNumber)}
                  value={this.state.enabled}
                  testID="pnr_ticket_status_alert_container_view_switch"
                />
              </View>
            </View>
            <View testID="pnr_ticket_status_alert_container_view_two">
              {pnrAlertState === ViewState.LOADING &&
                <View style={pnrTicketStyle.activityIndicatorStyle} testID="pnr_ticket_status_alert_container_view_two_activity_indicator_style">
                  <Spinner size={18} color="#008cff" />
                </View>}

              {pnrAlertState === ViewState.SHOW_DETAIL &&
                <View style={pnrTicketStyle.pnrAlertSuccessStyle} testID="pnr_ticket_status_alert_container_view_two_success_style">
                  <Image
                    style={{
                      width: 16,
                      height: 16,
                      marginRight: 7,
                    }}
                    source={greenTick}
                    testID="pnr_ticket_status_alert_container_view_two_image"
                  />
                  <Text style={pnrTicketStyle.alertSuccessText} testID="pnr_ticket_status_alert_container_view_two_text">{message}</Text>
                </View>}
              {!pnrAlertSet && pnrAlertState === ViewState.ERROR &&
                <View style={pnrTicketStyle.pnrAlertSuccessStyle} testID="pnr_ticket_status_alert_container_view_two_success_style_two">
                  <Image
                    style={{
                      width: 16,
                      height: 16,
                      marginRight: 7,
                    }}
                    source={redCross}
                    testID="pnr_ticket_status_alert_container_view_two_image_two"
                  />
                  <Text style={pnrTicketStyle.alertFailedText} testID="pnr_ticket_status_alert_container_view_two_text_two">Sorry we are unable to set the alert for
                  your PNR
                  currently. Please try again after some.
              </Text>
                </View>}
            </View>
          </View>}
      </View>
    );
  }


  greenGradient = ['#219393', '#43e1a8'];
  redGradient = ['#9f0404', '#9f0404'];
  yellowGradient = ['#f89d19', '#f3c852'];
  defaultGradient = ['#bdbdbd', '#bdbdbd'];


  _gradientColorFor = (state) => {
    const status = state.toLowerCase();

    if (status.includes('confirm') === true) {
      return this.greenGradient;
    }
    if (status.includes('no chance') === true) {
      return this.redGradient;
    }
    if (status.includes('probable') === true) {
      return this.yellowGradient;
    }
    return this.defaultGradient;
  };

  _pnrAlertSwitchClicked = (pnr) => {
    this.props.onPnrAlertClicked(pnr, this.state.enabled);
    const enabled = !this.state.enabled;
    this.setState({ enabled });
  };

  _getStatusText = (ticketStatus, predictionMsg) => {
    if (ticketStatus.includes('confirm') === true) {
      return 'CONFIRMED';
    }
    return predictionMsg;
  };
}

PnrTicketStatus.propTypes = {
  pnrAlertSet: PropTypes.bool,
  isLoading: PropTypes.bool,
  isPnrAlertEligible: PropTypes.bool,
  onPnrAlertClicked: PropTypes.func.isRequired,
  enabled: PropTypes.bool,
  PnrConfirmationProbabilityText: PropTypes.array,
  pnrNumber: PropTypes.string,
  pnrAlertState: PropTypes.string,
  message: PropTypes.string,
  enableLoginPersuasion: PropTypes.bool,
  enabled: PropTypes.bool,
  passengerList: PropTypes.object,
};

PnrTicketStatus.defaultProps = {
  pnrAlertSet: false,
  isLoading: true,
};


const infostyles = StyleSheet.create({
  p: {
    color: colors.deepBlue,
    fontSize: 12,
    lineHeight: 12,
    letterSpacing: 0.18,
    fontFamily: fonts.regular,
    width: '100%',
    paddingBottom:5,
  },
  b: {
    fontFamily: fonts.bold,
  },
});

const pnrTicketStyle = StyleSheet.create({
  tnc:{
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 16,
    marginRight:5,
    color: colors.azure,
},
downArrow: { height: 6, width: 13, marginLeft: 4 },
tncContainer:{
    flexDirection:'row',
    marginBottom:5,
    alignItems:'center',
},
tncView: {
    marginLeft: 15,
    marginBottom: 10,
    marginRight:'auto',
    marginTop:10,
    width:'88%',
},
tncHeaderContainer:{
    flexDirection: 'row',
     marginBottom: 10,
     alignItems:'center',
},
  containerStyle: {
    marginTop: 10,
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
  headerStyle: {
    marginTop: 15,
    marginLeft: 16,
    fontFamily: fonts.light,
    fontSize: 22,
    color: colors.black,
  },
  buttonConfirm: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    height: 15,
    width: 77,
    marginTop: 5,
    padding: 2,
  },
  subHeaderTextStyle: {
    color: '#afafaf',
    fontFamily: fonts.bold,
    fontSize: 12,
  },
  horizontalBar: {
    width: 'auto',
    height: 1,
    backgroundColor: colors.grey11,
    marginHorizontal: 16,
  },
  passengerNameStyle: {
    width: 93,
    color: colors.black,
    letterSpacing: 0,
    lineHeight:14,
  },

  currentStatusStyle: {
    fontFamily: fonts.black,
    fontSize: 12,
    color: colors.black,

  },
  statusbar:{
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 13,
    marginTop: 5,
    padding: 7,
  },

  bookingStatusStyle: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.black,
    marginLeft: 20,
    marginRight: 16,
  },
  passengerRowStyle: {
    marginLeft: 16,
    marginTop: 15,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems:'center',
  },
  subHeaderStyle: {
    fontFamily: fonts.bold,
    marginTop: 15,
    marginBottom: 15,
    marginLeft: 16,
    marginRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  alertContainer: {
    marginTop: 15,
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  pnrAlertTextStyle: {
    width: 187,
    fontFamily: fonts.bold,
    fontSize: 14,
    marginLeft: 16,
    marginRight: 47,
    color: colors.deepBlue,
    justifyContent: 'center',
  },
  pnrNumberTextStyle: {
    fontFamily: fonts.black,
    color: colors.deepBlue,
    justifyContent: 'center',
    marginLeft: 16,
    fontSize: 14,
  },

  activityIndicatorStyle: {
    marginTop: 10,
    marginLeft: 16,
    marginBottom: 10,
    height: 16,
    width: 16,
  },

  pnrAlertSuccessStyle: {
    flexDirection: 'row',
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 20,
    paddingLeft: 5,
  },

  alertSuccessText: {
    color: colors.lightSeaGreenTextColor,
    fontSize: 12,
    fontFamily: fonts.regular,
  },

  alertFailedText: {
    color: colors.red,
    fontSize: 12,
    fontFamily: fonts.regular,
    marginLeft: 5,
    marginRight: 16,
  },
  info: {
    color: colors.deepBlue,
    fontSize: 12,
    lineHeight: 12,
    letterSpacing: 0.18,
    fontFamily: fonts.bold,
    width: '100%',
    paddingBottom: 5,

  }
  , infoContainer: {
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 15,
    paddingLeft: 12,
    paddingRight: 12,
    paddingTop: 5,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#f0fdfc',
    borderRadius: 4.5,
    borderColor: '#3326b5a9',
  },
  nonloginInfoContainer: {
    flexDirection: 'row',
  },
  nonloginInfo: {
    color: colors.deepBlue,
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0.22,
    fontFamily: fonts.bold,
    width: '100%',
    paddingBottom: 10,
  },
  blurText: {
    color: 'transparent',
    textShadowColor: 'rgba(0,0,0,0.25)',
    textShadowOffset: {
      width: 0,
      height: 0,
    },
    textShadowRadius: 5,
    fontSize: 14,
  },
  loginBtn: {
    color: colors.primary,
    fontSize: 16,
    lineHeight: 18,
    paddingTop: 10,
  },
  persuasionContainer: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 15,
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 15,
  },
  persuasionText: {
    fontSize: 12,
    lineHeight: 18,
    letterSpacing: 0.18,
    color: '#3c3761',
    marginLeft: 10,
  },
  userIcon: {
    width: 24,
    height: 24,
  },
  pnrTicketStyleView :{
    display:'flex',
    flexDirection:'row',
    justifyContent:'space-between',
  },
});

const mapStatesToProps = (state, ownProps) => {
  const {
    pnrAlertState,
    trainInfo,
    passengerList = [],
    coachPosition,
    pnrAlertSet,
    message,
    enabled,
  } = state.railInfoReducer;
  return {
    ...ownProps,
    pnrAlertState,
    trainInfo,
    passengerList,
    coachPosition,
    pnrAlertSet,
    message,
    enabled,

  };
};

const mapDispatchToProps = dispatch => ({
  onPnrAlertClicked: (pnr, enabled) => dispatch(onPnrAlertClicked(pnr, enabled)),
});

export default connect(mapStatesToProps, mapDispatchToProps)(PnrTicketStatus);
