/* eslint-disable */
import React from 'react';
import { View, Text, StyleSheet, Image, Platform } from 'react-native';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { getStnNameConcatenated } from '../../../NewListing/Components/NewTrainInfo';
import { getTrainName } from '../../../Common/JourneyDetailsCard';

import liveStationIcon from '@mmt/legacy-assets/src/ic_raillanding_livestation.webp';
import trainScheduleIcon from '@mmt/legacy-assets/src/ic_raillanding_trainschedule.webp';

OptionCard = (props) => {
    const { icon, iconText, onClick } = props;

    const handleOnPress = () => {
        onClick();
    };

    return (
        <View style={optionsStyles.optionCardContainer}>
            <TouchableRipple onPress={() => handleOnPress()} style={optionsStyles.contentContainer}>
                <View style={Platform.OS === 'ios' ? optionsStyles.content : optionsStyles.contentContainer}>
                    <View style={optionsStyles.imgContainer}>
                        <Image resizeMode="contain" style={optionsStyles.img} source={icon} />
                    </View>
                    <View style={[optionsStyles.textContainer, fontStyle('semiBold')]}>
                        <Text style={optionsStyles.textStyle}> {iconText} </Text>
                    </View>
                </View>
            </TouchableRipple>
        </View>
    );
};

OptionCard.propTypes = {
    icon: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    iconText: PropTypes.string,
    onClick: PropTypes.func,
};

OptionCard.displayName = 'OptionCard';

function TrainDetailsNew(props) {
    const {
        trainInfo, openLiveTrainStatus, openTrainRoute } = props;
    return (
        <View style={trainInfoStyles.container}>
            <View style={trainInfoStyles.rootTagStyle}>
                <Text style={[trainInfoStyles.trainDetailHeader, fontStyle('regular')]}>Train Details</Text>
                <View style={trainHeader.container}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                        <Text style={[trainHeader.name, fontStyle('semiBold')]}>{getTrainName(trainInfo.trainName.toLowerCase())}</Text>
                        <Text style={trainHeader.number}>{`#${trainInfo.trainNumber}`}</Text>
                    </View>
                </View>
                <View style={{ marginHorizontal: 16 }}>

                    <View style={styles.stationDetailContainer}>
                        <Text style={styles.stationDetailsText}>
                            {`${getStnNameConcatenated(trainInfo.departStation)} - ${trainInfo.departStationCode}`}
                        </Text>
                        <Text style={styles.stationDetailsText}>
                            {`${getStnNameConcatenated(trainInfo.arrivalStation)} - ${trainInfo.arrivalStationCode}`}
                        </Text>
                    </View>
                </View>
                <View style={{ flexDirection: 'row', paddingRight: 16, marginBottom: 15, flexWrap: 'wrap', justifyContent: 'space-between' }}>
                    <View style={{ width: '50%' }}>
                        <OptionCard icon={trainScheduleIcon} iconText={'Train Schedule'} onClick={openTrainRoute} />
                    </View>
                    <View style={{ width: '50%' }}>
                        <OptionCard icon={liveStationIcon} iconText={'Live Train Status'} onClick={openLiveTrainStatus} />
                    </View>
                </View>
            </View>
        </View>
    );
}

TrainDetailsNew.propTypes = {
    openLiveTrainStatus: PropTypes.func.isRequired,
    openTrainRoute: PropTypes.func.isRequired,
    seeCoachPosition: PropTypes.func.isRequired,
    trainInfo: PropTypes.object
};

const trainInfoStyles = StyleSheet.create({
    container: {
        backgroundColor: colors.white,
    },
    rootTagStyle: {
        justifyContent: 'space-between',
        marginTop: 10,
        marginBottom: 10,
        backgroundColor: colors.white,
        borderColor: colors.lightGrey,
        borderWidth: 0.5,
        borderColor: colors.lightGray,
        borderWidth: 1,
        marginHorizontal: 16,
        borderRadius: 16,
    },
    trainDetailHeader: {
        fontFamily: fonts.light,
        fontSize: 22,
        color: colors.black,
        marginTop: 15,
        marginLeft: 16,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

const styles = StyleSheet.create({
    stationDetailContainer: {
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginTop: 10,
    },
    stationDetailsText: {
        fontSize: 12,
        fontFamily: fonts.bold,
        color: colors.defaultTextColor,
    },
    separator: {
        width: 30,
        height: 1,
        borderColor: colors.lightGrey,
        borderWidth: 1,
    },
});

const optionsStyles = StyleSheet.create({
    optionCardContainer: {},
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        marginTop: 10,
        borderColor: colors.lightGrey,
        marginLeft: 10,
        borderWidth: 1,
        borderRadius: 16,
        padding: 6,
    },
    content:{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
    },
    imgContainer: { width: 40, height: 40, borderRadius: 20, backgroundColor: colors.grey5, alignItems: 'center', justifyContent: 'center' },
    img: { width: 25, height: 25, borderRadius: 15, backgroundColor: colors.grey5, overflow: 'hidden' },
    textContainer: {},
    textStyle: { textAlign: 'center', fontSize: 12, lineHeight: 17, fontWeight: '600', color: colors.azureBlue2 },
});



export const trainHeader = StyleSheet.create({
    container: { flexDirection: 'column', marginTop: 10, marginHorizontal: 16 },
    name: {
        fontSize: 14, fontFamily: fonts.bold, color: colors.black, marginBottom: 2,fontWeight:'900',
    },
    number: {
        fontSize: 16, fontFamily: fonts.regular, color: colors.lightTextColor, marginBottom: 6,
    },
});

export default TrainDetailsNew;
