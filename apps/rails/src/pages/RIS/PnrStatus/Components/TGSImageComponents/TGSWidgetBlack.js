import * as React from 'react';
import Svg, {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';

function TGSWidgetBlack(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={54}
      height={55}
      viewBox="0 0 54 55"
      fill="none"
      {...props}
    >
      <Circle opacity={0.270744} cx={27} cy={27.75} r={27} fill="#fff" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M40.497 14.22a2.525 2.525 0 011.86.513c.509.396.866.984.95 1.671l.637 5.148a5.313 5.313 0 00-2.439 1.956 5.465 5.465 0 00-.904 3.76 5.479 5.479 0 001.792 3.43 5.333 5.333 0 002.842 1.311l.517 4.187a2.49 2.49 0 01-.516 1.85 2.518 2.518 0 01-1.68.946l-31.413 3.835a2.525 2.525 0 01-1.86-.513 2.497 2.497 0 01-.951-1.671l-.717-5.798a5.373 5.373 0 001.844-1.997c.513-.96.746-2.088.602-3.26a5.485 5.485 0 00-1.377-3.018 5.394 5.394 0 00-2.275-1.495l-.521-4.224A2.49 2.49 0 017.404 19a2.518 2.518 0 011.68-.946l31.413-3.835z"
        fill="#2F0959"
        stroke="#2F0959"
        strokeWidth={1.67222}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.1 13.11a2.526 2.526 0 011.861.514c.51.395.866.983.951 1.67l.637 5.149a5.313 5.313 0 00-2.438 1.955 5.464 5.464 0 00-.904 3.76A5.48 5.48 0 0041 29.587a5.336 5.336 0 002.842 1.311l.518 4.187a2.49 2.49 0 01-.516 1.85c-.397.506-.988.861-1.68.945l-31.413 3.83a2.526 2.526 0 01-1.86-.514 2.498 2.498 0 01-.952-1.67l-.56-4.531a5.369 5.369 0 001.843-1.997 5.47 5.47 0 00.6-3.26 5.487 5.487 0 00-1.377-3.018 5.398 5.398 0 00-2.275-1.495l-.678-5.491a2.49 2.49 0 01.515-1.85c.398-.506.989-.86 1.68-.945l31.414-3.83z"
        fill="#fff"
        stroke="#2F0959"
        strokeWidth={1.67222}
      />
      <Mask
        id="a"
        style={{
          maskType: 'alpha',
        }}
        maskUnits="userSpaceOnUse"
        x={6}
        y={14}
        width={29}
        height={28}
      >
        <Path
          d="M31.702 14.605l2.977 24.077-22.703 2.768a3.36 3.36 0 01-3.748-2.912l-.56-4.537c1.369-.785 2.2-2.554 1.962-4.484-.24-1.931-1.477-3.448-2.997-3.88L6.072 21.1A3.347 3.347 0 019 17.373l22.702-2.768z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#a)">
        <Path
          clipRule="evenodd"
          d="M39.1 13.11a2.526 2.526 0 011.861.514c.51.395.866.983.951 1.67l.637 5.149a5.313 5.313 0 00-2.438 1.955 5.464 5.464 0 00-.904 3.76A5.48 5.48 0 0041 29.587a5.336 5.336 0 002.842 1.311l.518 4.187a2.49 2.49 0 01-.516 1.85c-.397.506-.988.861-1.68.945l-31.413 3.83a2.526 2.526 0 01-1.86-.514 2.498 2.498 0 01-.952-1.67l-.56-4.531a5.369 5.369 0 001.843-1.997 5.47 5.47 0 00.6-3.26 5.487 5.487 0 00-1.377-3.018 5.398 5.398 0 00-2.275-1.495l-.678-5.491a2.49 2.49 0 01.515-1.85c.398-.506.989-.86 1.68-.945l31.414-3.83z"
          stroke="#2F0959"
          strokeWidth={1.67222}
        />
        <Path
          d="M19.007 30.451c.22-.027.43-.15.571-.325a.928.928 0 00.17-.646.911.911 0 00-.322-.585c-.353-.283-.93-.212-1.204.147a.926.926 0 00-.17.645c.*************.322.586.18.136.413.205.633.178zM24.095 29.83c.22-.027.43-.15.571-.325a.928.928 0 00.17-.646.911.911 0 00-.322-.585c-.353-.283-.93-.213-1.204.147a.926.926 0 00-.17.645c.*************.322.586.18.136.413.205.633.178z"
          fill="#2F0959"
        />
        <Path
          d="M27.78 34.662l.673.54a.843.843 0 001.199-.145.888.888 0 00-.15-1.226l-2.836-2.276c.586-.63.898-1.512.82-2.138-.095-.776-.4-2.252-.907-4.389-.332-1.4-.627-2.562-.627-2.562-.32-1.357-1.68-2.327-3.034-2.162l-5.088.62c-1.354.166-2.438 1.434-2.417 2.812 0 0-.003 1.204.016 2.652.028 2.195.091 3.7.187 4.477.078.628.597 1.412 1.322 1.881l-1.451 1.905-.016.02-.734.964a.889.889 0 00.152 1.227.841.841 0 001.199-.147l.526-.691 11.166-1.362zm-9.687-.579l.749-.983 6.068-.74.966.774-7.783.95zm-.944-6.926l7.947-.97c.38 1.646.62 2.825.693 3.423.003.24-.355.926-.74.973l-6.783.827c-.386.047-.9-.532-.954-.752-.076-.61-.131-1.813-.163-3.5zm-.027-3.42c-.009-.514.423-1.018.922-1.08l5.088-.62c.5-.06 1.04.325 1.161.84 0 .003.17.668.396 1.6l-7.562.922c-.007-.958-.005-1.648-.005-1.662z"
          fill="#2F0959"
        />
        <Path
          d="M32.031 14.229l2.92 23.647"
          stroke="#2F0959"
          strokeWidth={1.5}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeDasharray="2.59 2.59"
        />
      </G>
      <Path
        d="M51.384 24.345a.712.712 0 00-.265-.458l-1.351-.979.306-1.654a.732.732 0 00-.11-.509.613.613 0 00-.448-.256l-1.634-.16-.547-1.583a.591.591 0 00-.35-.382.563.563 0 00-.51.013l-1.496.705-1.255-1.104a.654.654 0 00-.498-.152.68.68 0 00-.447.267l-.949 1.373-1.622-.325a.687.687 0 00-.498.11.622.622 0 00-.248.455l-.145 1.667-1.546.548a.578.578 0 00-.372.355.602.602 0 00.017.521l.7 1.532-1.073 1.274a.68.68 0 00-.145.507.711.711 0 00.265.458l1.351.979-.306 1.654a.732.732 0 00.11.51c.115.149.27.244.448.255l1.634.16.547 1.583c.053.173.183.32.35.382.17.077.346.072.51-.013l1.496-.705 1.255 1.104c.142.114.31.175.498.152a.68.68 0 00.447-.267l.949-1.373 1.622.325c.18.027.35-.01.498-.11a.622.622 0 00.247-.455l.146-1.667 1.546-.548a.578.578 0 00.372-.355c.059-.171.066-.368-.017-.521l-.7-1.532 1.073-1.274a.71.71 0 00.145-.507z"
        fill="#33D18F"
        stroke="#136F48"
        strokeWidth={1.00791}
      />
      <Path
        d="M49.68 25.693a.645.645 0 00-.09.697l.7 1.531-1.547.549a.655.655 0 00-.426.558l-.163 1.653-1.623-.324a.643.643 0 00-.64.257l-.948 1.374-1.255-1.105a.682.682 0 00-.499-.151c-.063.008-.123.031-.184.055l-1.464.701-.547-1.582a.658.658 0 00-.55-.44l-1.634-.16.307-1.654a.679.679 0 00-.256-.655l-1.352-.978 1.074-1.274a.645.645 0 00.09-.697l-.697-1.5 1.546-.547a.655.655 0 00.427-.559l.145-1.667 1.623.324a.643.643 0 00.64-.257l.948-1.374 1.255 1.105a.615.615 0 00.683.096l1.496-.706.547 1.583c.077.236.29.406.55.44l1.602.163-.307 1.655a.679.679 0 00.256.654l1.352.979-1.06 1.256z"
        fill="url(#paint0_linear_913_5767)"
      />
      <Path
        d="M45.61 22.63l-2.34 3.062-1.01-.808a.942.942 0 00-.697-.208.878.878 0 00-.626.37.98.98 0 00-.2.71.918.918 0 00.367.642l1.755 1.402a.902.902 0 001.323-.162l2.917-3.818c.316-.414.24-1.026-.17-1.368-.392-.327-.99-.254-1.32.178z"
        fill="#fff"
      />
      <Path
        d="M46.617 23.421l-2.916 3.819a.302.302 0 01-.441.054l-1.771-1.401a.324.324 0 01-.122-.214.323.323 0 01.066-.236.309.309 0 01.209-.124.31.31 0 01.232.07l1.259 1.006c.142.113.346.088.44-.054l2.532-3.315a.302.302 0 01.441-.053.306.306 0 01.071.448z"
        fill="#fff"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_913_5767"
          x1={38.6984}
          y1={32.7502}
          x2={49.8742}
          y2={17.9509}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#43E1A8" />
          <Stop offset={1} stopColor="#219393" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}

export default TGSWidgetBlack;
