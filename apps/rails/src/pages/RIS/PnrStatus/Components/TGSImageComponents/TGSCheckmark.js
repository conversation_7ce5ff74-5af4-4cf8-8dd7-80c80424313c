import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

function TGSCheckMark(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={17}
      height={17}
      viewBox="0 0 17 17"
      fill="none"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.794 6.64a1.573 1.573 0 00.261 1.833 1.571 1.571 0 01-.35 2.46 1.567 1.567 0 00-.763 1.684c.099.477-.03.973-.346 1.342a1.543 1.543 0 01-1.268.536 1.55 1.55 0 00-1.544 1.001 1.555 1.555 0 01-1.011.94 1.54 1.54 0 01-1.355-.24 1.54 1.54 0 00-1.836 0 1.54 1.54 0 01-1.354.24 1.555 1.555 0 01-1.011-.94 1.549 1.549 0 00-1.545-1 1.544 1.544 0 01-1.268-.537 1.573 1.573 0 01-.346-1.342 1.567 1.567 0 00-.763-1.684 1.57 1.57 0 01-.35-2.46c.474-.488.58-1.23.26-1.832a1.573 1.573 0 01-.042-1.386c.2-.444.595-.768 1.067-.875a1.559 1.559 0 001.202-1.4c.039-.485.3-.925.707-1.189.407-.263.912-.32 1.367-.154.637.233 1.35.022 1.761-.522C7.561.728 8.017.5 8.5.5c.483 0 .94.228 1.233.615.41.544 1.124.755 1.761.522.455-.167.96-.11 1.367.154.406.264.668.704.707 1.19A1.558 1.558 0 0014.77 4.38c.472.107.866.431 1.067.875.2.443.185.956-.043 1.385zM3.771 9l3.402 3.43 5.959-6.008-1.45-1.46-4.509 4.546-1.953-1.97L3.77 9z"
        fill="#33D18F"
      />
    </Svg>
  );
}

export default TGSCheckMark;
