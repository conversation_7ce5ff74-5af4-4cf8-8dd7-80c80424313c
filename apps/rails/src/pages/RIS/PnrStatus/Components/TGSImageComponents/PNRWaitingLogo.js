import * as React from 'react';
import Svg, { <PERSON>, Rect, Defs, LinearGradient, Stop } from 'react-native-svg';

function PNRWaitingLogo(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={71}
      height={14}
      viewBox="0 0 71 14"
      fill="none"
      {...props}
    >
      <Path
        d="M12.055 8.723l1.133-4.41h.593l-.066 1.136L12.504 10h-.617l.168-1.277zm-.848-4.41l.918 4.375.082 1.312h-.656l-1.317-5.688h.973zm3.86 4.363l.906-4.364h.976L15.633 10h-.656l.09-1.324zm-1.055-4.364l1.12 4.422.165 1.266h-.617l-1.192-4.55-.062-1.138h.586zm5.707.758l-1.7 4.93h-1.027l2.14-5.688h.657l-.07.758zM21.14 10l-1.703-4.93-.075-.758h.66L22.172 10H21.14zm-.082-2.11v.778h-3.094v-.777h3.094zm2.796-3.577V10h-.98V4.312h.98zm3.442 0V10h-.973V4.312h.973zm1.785 0v.78h-4.527v-.78h4.527zm1.797 0V10h-.98V4.312h.98zm5.73 0V10h-.98l-2.55-4.074V10h-.981V4.312h.98l2.559 4.083V4.313h.972zm5.535 2.78v2.173c-.08.106-.206.224-.378.351-.17.125-.395.233-.676.324-.281.092-.632.137-1.05.137-.358 0-.684-.06-.981-.18a2.147 2.147 0 01-.77-.535 2.45 2.45 0 01-.496-.855 3.525 3.525 0 01-.176-1.156v-.395c0-.43.054-.812.16-1.148a2.44 2.44 0 01.47-.86c.202-.234.447-.411.733-.531.29-.122.616-.184.98-.184.467 0 .852.079 1.157.235a1.7 1.7 0 01.711.64c.167.274.272.586.316.938h-.96A1.514 1.514 0 0041 5.516a.93.93 0 00-.383-.368c-.164-.09-.377-.136-.64-.136a1.32 1.32 0 00-.598.129 1.13 1.13 0 00-.43.379 1.86 1.86 0 00-.254.609c-.057.24-.086.513-.086.82v.403c0 .312.033.59.098.832.068.242.164.446.29.613.127.167.282.293.464.379.182.083.388.125.617.125.224 0 .408-.018.55-.055.144-.039.257-.085.34-.137.087-.054.153-.106.2-.156V7.824h-1.184v-.73h2.16zm6.641 2.13V10H45.93v-.777h2.855zm-2.582-4.91V10h-.98V4.312h.98zm4.387 0V10h-.98V4.312h.98zm4.21 4.222a.835.835 0 00-.054-.312.61.61 0 00-.184-.25 1.513 1.513 0 00-.374-.22 4.982 4.982 0 00-.61-.222 6.76 6.76 0 01-.75-.277 2.853 2.853 0 01-.594-.36c-.169-.137-.3-.295-.394-.472a1.324 1.324 0 01-.14-.621c0-.232.047-.443.144-.633.099-.19.238-.354.418-.492a2 2 0 01.644-.324c.248-.079.521-.118.82-.118.422 0 .786.079 1.09.235.308.156.543.366.707.629.167.263.25.553.25.87h-.972a.993.993 0 00-.121-.495.82.82 0 00-.36-.344 1.297 1.297 0 00-.605-.125 1.39 1.39 0 00-.578.105.77.77 0 00-.344.286.745.745 0 00-.113.406.59.59 0 00.074.293c.05.086.125.166.227.242.101.073.229.142.382.207.154.065.335.128.543.187.315.094.59.198.825.313.234.112.43.24.585.383a1.416 1.416 0 01.47 1.101c0 .24-.049.456-.145.649a1.4 1.4 0 01-.414.488c-.177.133-.391.236-.641.309-.247.07-.523.105-.828.105a3.04 3.04 0 01-.809-.11 2.384 2.384 0 01-.718-.331 1.716 1.716 0 01-.516-.563 1.589 1.589 0 01-.192-.793h.98c0 .185.032.343.095.473.065.13.155.237.27.32.114.08.247.14.398.18.153.039.317.059.492.059.229 0 .42-.033.574-.098a.757.757 0 00.352-.274.715.715 0 00.117-.406zm4.192-4.223V10h-.972V4.312h.972zm1.785 0v.782H56.25v-.782h4.527z"
        fill="url(#paint0_linear_380_2682)"
      />
      <Rect
        x={0.5}
        y={0.5}
        width={70}
        height={13}
        rx={6.5}
        stroke="url(#paint1_linear_380_2682)"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_380_2682"
          x1={2}
          y1={11}
          x2={60.4821}
          y2={-15.6748}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F3D452" />
          <Stop offset={1} stopColor="#F09819" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_380_2682"
          x1={0}
          y1={14}
          x2={69.5602}
          y2={-5.21282}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F3D452" />
          <Stop offset={1} stopColor="#F09819" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}

export default PNRWaitingLogo;
