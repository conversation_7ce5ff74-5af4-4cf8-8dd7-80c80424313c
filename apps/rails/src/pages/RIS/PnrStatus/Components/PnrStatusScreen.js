import {connect} from 'react-redux';
import {initPnrStatus, onSharePress, deeepLinkHandling, resetPnrStatus } from '../RailInfoAction';
import {getPNRPageConfig} from 'apps/rails/src/RailsAbConfig';
import PnrStatusPage from './PnrStatusPage';
import NewPnrStatusPage from './NewPnrStatusPage';

let PnrStatus;

const pnrPage = getPNRPageConfig();

if (pnrPage) {
  PnrStatus = PnrStatusPage;
}
else {
  PnrStatus = NewPnrStatusPage;
}

const mapStatesToProps = (state, ownProps) => {
  const {
    showLoader,
    pnrDetailInfo,
    trainInfo,
    passengerList,
    pnrViewState,
    disclaimer,
    isAlternate,
    coachPosition,
    pnrNumber,
    enabledPnr,
    recentPnr,
    pnrSearchCoupon,
    pnrLoginPersuasionCoupon,
    tgsEligibility,
    IsBookedOnMMT,
    alternateBusesDetails,
    trainList,
    crossSellData,
  } = state.railInfoReducer;
  return {
    ...ownProps,
    showLoader,
    pnrDetailInfo,
    trainInfo,
    passengerList,
    pnrViewState,
    disclaimer,
    isAlternate,
    coachPosition,
    pnrNumber,
    enabledPnr,
    recentPnr,
    pnrSearchCoupon,
    pnrLoginPersuasionCoupon,
    tgsEligibility,
    IsBookedOnMMT,
    alternateBusesDetails,
    trainList,
    pnrPage,
    crossSellData,
  };
};


const mapDispatchToProps = dispatch => ({
  initPnrStatus: pnr => dispatch(initPnrStatus(pnr)),
  onShareButtonClicked: trainInfo => dispatch(onSharePress(trainInfo)),
  handleDeepLink: deeplink => dispatch(deeepLinkHandling(deeplink)),
  resetPnrStatus: () => dispatch(resetPnrStatus),
});


export default connect(mapStatesToProps, mapDispatchToProps)(PnrStatus);
