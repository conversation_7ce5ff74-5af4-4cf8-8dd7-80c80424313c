import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  card: {
    borderColor: colors.grey4,
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 220,
    maxWidth: 250,
  },
  container: {
    backgroundColor: colors.transparent,
  },
  flatListContainer: {
    paddingLeft: 16,
    flexGrow: 1,
    marginBottom: 16,
    marginTop: 2,
    paddingRight: 12,
  },
  separator: {
    width: 12,
  },
  busType: {
    fontSize: 12,
    color: colors.black,
    flex: 1,
  },
  cityName: {
    fontSize: 12,
    color: colors.textGrey,
    marginTop: 4,
    marginBottom: 12,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  arrivalTime: {
    fontSize: 14,
    color: colors.black,
    fontWeight: '900',
  },
  departureTime: {
    fontSize: 12,
    color: colors.textGrey,
    marginTop: 2,
  },
  lineContainer: {
    marginHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  smallLine: {
    backgroundColor: colors.black,
    width: 6,
    height: 1,
  },
  dayChanged: {
    color: colors.grey29,
    fontSize: 10,
  },
  ratingCardStyle: {
    backgroundColor: colors.white,
    borderRadius: 4,
    height: 16,
    borderWidth: 0,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
    marginLeft: 16,
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
  overallRatingStyle: {
    fontSize: 10,
    backgroundColor: colors.transparent,
    color: colors.white,
    textAlign: 'center',
  },
  starIcon: {
    width: 10,
    height: 10,
    marginRight: 2,
  },
});
