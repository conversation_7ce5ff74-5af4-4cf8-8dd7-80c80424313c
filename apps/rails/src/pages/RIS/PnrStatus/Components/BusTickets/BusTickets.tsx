import React from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import {
  convertEpochToTimeFormat,
  getRatingColors,
  hasDayChanged,
} from 'apps/rails/src/Utils/RisUtils';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import LinearGradient from 'react-native-linear-gradient';
import { CrossSellInlineHeader } from '../PNRCrossSells/Components';
import { CrossSellDataObj, Edge } from '../PNRCrossSells/types';
import styles from './styles';
import { openCrossSell } from 'apps/rails/src/pages/RIS/RisCommonUtils';
import { trackClickEventProp61 } from 'apps/rails/src/pages/RailsBusHomePage/Analytics';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from 'apps/rails/src/RisAnalytics/RisAnalytics';
import { CROSS_SELL_CLICK } from 'apps/rails/src/pages/RIS/Utils/RISOmnitureConstants';
import { logRisPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS';
import { RIS_EVENT_VALUES } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS/RisPdtConstants';

interface Props {
  isFromBottomSheet: boolean;
  crossSellData: CrossSellDataObj;
  onClosePressed?: () => void;
}

const BusTickets = (props: Props) => {
  const { crossSellData, isFromBottomSheet, onClosePressed } = props;
  const { data, searchParams } = crossSellData || {};

  const logBusTicketToPdt = async (item: Edge) => {
    try {
      const returnTicketPdtData = {
        ...crossSellData,
        data: [item],
        isFromBottomSheet,
      };
      logRisPdtEvent({
        eventValue: RIS_EVENT_VALUES.BUS_TICKETS,
        pnrCrossSell: returnTicketPdtData,
      });
    } catch (error) {}
  };

  const onCardClick = (item: Edge) => {
    try {
      const { node: { journeyInfo: { departureTimeStamp } = {} } = {} } = item;
      onClosePressed && onClosePressed();
      trackClickEventProp61(
        RIS_LANDING_PNR_DETAILS_SUCCESS,
        CROSS_SELL_CLICK.BUS[isFromBottomSheet ? 'BS_CARD' : 'INLINE_CARD'],
      );
      openCrossSell(
        crossSellData.searchParams,
        crossSellData.type,
        new Date(departureTimeStamp * 1000),
      );
      logBusTicketToPdt(item);
    } catch (error) {}
  };

  const renderItem = ({ item }: { item: Edge }) => {
    const {
      node: { journeyInfo, fareDetails, extraInfo, operatorName },
    } = item;
    const dayChanged = hasDayChanged(
      journeyInfo.departureTimeStamp,
      journeyInfo.journeyTimeInMinutes,
    );
    return (
      <TouchableOpacity style={styles.card} onPress={() => onCardClick(item)}>
        <View style={styles.rowContainer}>
          <Text style={[fontStyle('black'), styles.busType]}>{operatorName}</Text>
          {extraInfo.overallRating > 0 && (
            <LinearGradient
              style={styles.ratingCardStyle}
              colors={getRatingColors(extraInfo.overallRating)}
              start={{ x: 1, y: 1 }}
              end={{ x: 0, y: 0 }}
            >
              <Image source={ASSETS.ic_star_white} style={styles.starIcon} />
              <Text style={[styles.overallRatingStyle, fontStyle('bold')]}>
                {extraInfo.overallRating}
              </Text>
            </LinearGradient>
          )}
        </View>
        <Text style={[fontStyle('medium'), styles.cityName]}>
          {`${journeyInfo.sourceCityName ?? searchParams?.sourceName} - ${
            journeyInfo.destinationCityName ?? searchParams?.destinationName
          }`}
        </Text>

        <View style={styles.rowContainer}>
          <View style={styles.rowContainer}>
            <Text style={[fontStyle('black'), styles.arrivalTime]}>
              {convertEpochToTimeFormat(journeyInfo.departureTimeStamp * 1000)}
            </Text>
            <View style={styles.lineContainer}>
              <View style={styles.smallLine} />
            </View>
            <Text style={[fontStyle('medium'), styles.departureTime]}>
              {convertEpochToTimeFormat(journeyInfo.arrivalTimeStamp * 1000)}
            </Text>
            {dayChanged && <Text style={[fontStyle('medium'), styles.dayChanged]}> +1 day</Text>}
          </View>
          <Text style={[fontStyle('bold'), styles.arrivalTime]}>₹{fareDetails.displayPrice}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const keyExtractor = (item: Edge, index: number) => index.toString();
  const ItemSeparatorComponent = () => <View style={styles.separator} />;

  return (
    <View style={styles.container}>
      {!isFromBottomSheet && <CrossSellInlineHeader crossSellData={crossSellData} />}
      <FlatList
        data={data as Edge[]}
        contentContainerStyle={styles.flatListContainer}
        horizontal={true}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ItemSeparatorComponent={ItemSeparatorComponent}
      />
    </View>
  );
};

export default BusTickets;
