import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {getStnNameConcatenated} from '../../../NewListing/Components/NewTrainInfo';
import {getTrainName} from '../../../Common/JourneyDetailsCard';
import {isEmpty} from 'lodash';

function TrainDetails(props) {
  const {
    trainInfo, openLiveTrainStatus, openTrainRoute, seeCoachPosition, coachPosition,
  } = props;
  return (
    <View style={trainInfoStyles.rootTagStyle}>
      <Text style={trainInfoStyles.trainDetailHeader}>Train Details</Text>
      <View style={trainHeader.container}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <Text style={trainHeader.name}>{getTrainName(trainInfo.trainName.toLowerCase())}</Text>
          <Text style={trainHeader.number}>{`#${trainInfo.trainNumber}`}</Text>
        </View>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <Text style={trainHeader.selectedClass}>{trainInfo.travelClass} | {trainInfo.travelQuota}</Text>
          {trainInfo.isChartPrepared &&
          <Text style={trainHeader.chartPreparedStyle}>Chart Prepared</Text>
          }
          {!trainInfo.isChartPrepared &&
          <Text style={trainHeader.chartNotPreparedStyle}>Chart Not Prepared</Text>
          }
        </View>
      </View>
      <View style={{marginHorizontal: 16, marginTop: 21}}>
        <View style={styles.timeContainer}>
          <View style={{flexDirection: 'row'}}>
            <Text style={styles.timeText}>{trainInfo.deptTime}, </Text>
            <Text style={styles.dayText}>{trainInfo.deptDate}</Text>
          </View>
          <View style={styles.separator} />
          <Text style={styles.duration}>{trainInfo.duration}</Text>
          <View style={styles.separator} />
          <View style={{flexDirection: 'row'}}>
            <Text style={styles.timeText}>{trainInfo.arrivalTime}, </Text>
            <Text style={styles.dayText}>{trainInfo.arrivalDate}</Text>
          </View>
        </View>

        <View style={styles.stationDetailContainer}>
          <Text style={styles.stationDetailsText}>
            {`${getStnNameConcatenated(trainInfo.departStation)} (${trainInfo.departStationCode})`}
          </Text>
          <Text style={styles.stationDetailsText}>
            {`${getStnNameConcatenated(trainInfo.arrivalStation)} (${trainInfo.arrivalStationCode})`}
          </Text>
        </View>
      </View>
      <View style={trainInfoStyles.horizontalBarLong} />
      <View style={{flexDirection: 'row', paddingRight: 16, marginBottom: 15}}>
        <TouchableRipple onPress={openTrainRoute}>
          <View style={[trainInfoStyles.buttonBlue, {marginLeft: 15}]} elevation={5}>
            <Text style={trainInfoStyles.buttonTextStyle}>
              VIEW ROUTE
            </Text>
          </View>
        </TouchableRipple>
        <TouchableRipple onPress={openLiveTrainStatus}>
          <View style={[trainInfoStyles.buttonBlue, {marginLeft: 9, width: 170}]} elevation={5}>
            <Text style={trainInfoStyles.buttonTextStyle}>
              LIVE TRAIN STATUS
            </Text>
          </View>
        </TouchableRipple>
      </View>
      {!isEmpty(coachPosition) &&
      <TouchableRipple onPress={seeCoachPosition}>
        <View style={[trainInfoStyles.buttonBlue, {width: 150, marginLeft: 16, marginBottom: 15}]} elevation={5}>
          <Text style={trainInfoStyles.buttonTextStyle}>
            COACH POSITION
          </Text>
        </View>
      </TouchableRipple>
      }
    </View>
  );
}

TrainDetails.propTypes = {
  openLiveTrainStatus: PropTypes.func.isRequired,
  openTrainRoute: PropTypes.func.isRequired,
  seeCoachPosition: PropTypes.func.isRequired,
  trainInfo: PropTypes.object,
  coachPosition: PropTypes.object,
};

const trainInfoStyles = StyleSheet.create({

  rootTagStyle: {
    justifyContent: 'space-between',
    marginTop: 10,
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
  buttonBlue: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 41,
    borderRadius: 50,
    backgroundColor: colors.grey11,
    borderColor: colors.grey,
    elevation: 5,
  },
  trainDetailHeader: {
    fontFamily: fonts.light,
    fontSize: 22,
    color: colors.black,
    marginTop: 15,
    marginLeft: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  horizontalBarLong: {
    width: 'auto',
    height: 1,
    backgroundColor: colors.grey11,
    marginTop: 15,
    marginBottom: 15,
    marginLeft: 16,
    marginRight: 16,
  },
  buttonTextStyle: {
    color: colors.azure,
    borderRadius: 50,
    fontSize: 12,
    fontFamily: fonts.bold,
    textAlign: 'center',
    paddingLeft: 15,
    paddingRight: 15,
  },

});

const regularLightText = {
  fontSize: 12,
  fontFamily: fonts.regular,
  color: colors.lightTextColor,
};

const styles = StyleSheet.create({
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    fontFamily: fonts.black,
    color: colors.black,
  },
  dayText: {
    ...regularLightText,
  },
  duration: {
    ...regularLightText,
  },
  stationDetailContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginTop: 10,
  },
  stationDetailsText: {
    ...regularLightText,
  },
  separator: {
    width: 30,
    height: 1,
    borderColor: colors.lightGrey,
    borderWidth: 1,
  },
});

export const trainHeader = StyleSheet.create({
  container: {flexDirection: 'column', marginTop: 10, marginHorizontal: 16},
  name: {
    fontSize: 16, fontFamily: fonts.black, color: colors.black, marginBottom: 2,
  },
  number: {
    fontSize: 16, fontFamily: fonts.regular, color: colors.lightTextColor, marginBottom: 6,
  },
  selectedClass: {
    fontSize: 12, fontFamily: fonts.regular, color: colors.lightTextColor, marginBottom: 6,
  },
  platformStyle: {
    height: 15,
    fontFamily: fonts.black,
    fontSize: 12,
    color: colors.red7,
  },
   chartNotPreparedStyle: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.bold,
  },
  chartPreparedStyle: {
    fontSize: 12,
    color: colors.lightGreen4,
    fontFamily: fonts.bold,
  },
});

export default TrainDetails;
