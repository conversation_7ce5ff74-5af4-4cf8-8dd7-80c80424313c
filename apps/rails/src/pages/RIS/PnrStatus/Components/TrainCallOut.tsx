import React from 'react';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { View, StyleSheet, Image, Text } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {cheapTickets,discountCode} from 'apps/rails/src/pages/RIS/PnrStatus/TGSUtils/Constants';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from '../../../../RisAnalytics/RisAnalytics';
import {omnitureMapping} from '../TGSUtils/Constants';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import offer from '@mmt/legacy-assets/src/TGS/coupon.webp';
import arrowBlueFilled from '@mmt/legacy-assets/src/ArrowBlueFilled.webp';
import { VIEW_TRAINS } from '@mmt/rails/src/Utils/RailsConstant';

interface Props {
    redirectToListing: () => void;
}

const TrainCallOut = ({ redirectToListing }: Props) => {
  const onSubmit = () => {
    redirectToListing();
    trackClickEventProp61(
      RIS_LANDING_PNR_DETAILS_SUCCESS,
      omnitureMapping.PNR_STATUS_TRAIN_CALLOUT_CLICKED,
    );
  };
  return (
    <View style={styles.container} testId="train_call_out_container">
      <View style={styles.imageContainer} testId="train_call_out_container_image_container">
        <Image
          style={styles.image}
          source={offer}
          resizeMode="contain"
          testId="train_call_out_container_image_container_image"
        />
      </View>
      <View>
        <Text style={[styles.bold, fontStyle('bold')]} testId="train_call_out_container_text">
          {cheapTickets}
        </Text>
        <Text testId="train_call_out_container_text_text">
          {discountCode}
          <Text style={fontStyle('bold')} testId="train_call_out_container_text_text_text_text">
            MYTRAIN
          </Text>
        </Text>
        <Text
          onPress={onSubmit}
          style={[styles.link, fontStyle('semiBold')]}
          testId="train_call_out_container_text_text_text"
        >
          {VIEW_TRAINS}
          <Image
            source={arrowBlueFilled}
            resizeMode="contain"
            style={styles.arrowImage}
            testId="train_call_out_container_text_text_text_image"
          />
        </Text>
      </View>
    </View>
  );
};


export default TrainCallOut;

export const styles = StyleSheet.create({
  container: {
    padding: 8,
    backgroundColor: colors.lightGreen17,
    margin: 8,
    borderRadius: 12,
    flexDirection: 'row',
    columnGap: 8,
  },
  imageContainer: {
    margin: 5,
  },
  image: {
    height: 30,
    width: 30,
  },
  bold: {
    fontSize: 14,
    color: colors.black,
  },
  link: {
    marginTop: 3,
    color: colors.azure,
  },
  arrowImage: {
    width: 14,
    height: 14,
  },
});
