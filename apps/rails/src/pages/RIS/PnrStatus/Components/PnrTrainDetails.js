import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {getTrainName} from '../../../Common/JourneyDetailsCard';
import cancelledTrains from '@mmt/legacy-assets/src/ic_cancelled.webp';
import PropTypes from 'prop-types';

function PnrTrainDetails(props) {
  const {trainInfo} = props;
  return (
    <View style={trainInfoStyles.rootTagStyle}>
      <View style={trainHeader.container}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <Text style={trainHeader.name}>{getTrainName(trainInfo.trainName.toLowerCase())}</Text>
          <Text style={trainHeader.journeyDate}>{trainInfo.journeyDate}</Text>
        </View>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <Text style={trainHeader.selectedClass}>{trainInfo.travelClass} | {trainInfo.travelQuota}</Text>
          {trainInfo.isChartPrepared &&
          <Text style={trainHeader.chartPreparedStyle}>Chart Prepared</Text>
          }
          {!trainInfo.isChartPrepared &&
          <Text style={trainHeader.chartNotPreparedStyle}>Chart Not Prepared</Text>
          }
        </View>
      </View>
      <View style={{marginHorizontal: 16, marginTop: 11, paddingBottom: 15}}>
        <View style={styles.timeContainer}>
          <View style={{flexDirection: 'row'}}>
            <Text style={styles.timeText}>{trainInfo.deptTime}, </Text>
            <Text style={styles.dayText}>{trainInfo.deptDate}</Text>
          </View>
          <View style={styles.separator} />
          <Text style={styles.duration}>{trainInfo.duration}</Text>
          <View style={styles.separator} />
          <View style={{flexDirection: 'row'}}>
            <Text style={styles.timeText}>{trainInfo.arrivalTime}, </Text>
            <Text style={styles.dayText}>{trainInfo.arrivalDate}</Text>
          </View>
        </View>
      </View>
      {trainInfo.trainCancelledFlag &&
      <View style={trainHeader.cancelledTrain}>
        <Image style={trainHeader.imageStyle} source={cancelledTrains} />
        <Text style={trainHeader.cancelledTrainText}>
          {trainInfo.trainStatusMsg}
        </Text>
      </View>}
    </View>
  );
}

const trainInfoStyles = StyleSheet.create({

  rootTagStyle: {
    justifyContent: 'space-between',
    paddingTop: 15,
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
  buttonTextStyle: {
    color: colors.azure,
    borderRadius: 50,
    fontSize: 16,
    fontFamily: fonts.bold,
    textAlign: 'center',
    paddingHorizontal: 15,
  },

});

const regularLightText = {
  fontSize: 12,
  fontFamily: fonts.regular,
  color: colors.lightTextColor,
};

const styles = StyleSheet.create({
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    fontFamily: fonts.black,
    color: colors.black,
  },
  dayText: {
    ...regularLightText,
  },
  duration: {
    ...regularLightText,
  },
  separator: {
    width: 30,
    height: 1,
    borderColor: colors.lightGrey,
    borderWidth: 1,
  },
});

const trainHeader = StyleSheet.create({
  container: {flexDirection: 'column', marginTop: 10, marginHorizontal: 16},
  name: {
    fontSize: 14, fontFamily: fonts.black, color: colors.black, marginBottom: 2,
  },
  journeyDate: {
    fontSize: 14, fontFamily: fonts.black, color: colors.black, marginBottom: 6,
  },
  selectedClass: {
    fontSize: 12, fontFamily: fonts.regular, color: colors.lightTextColor, marginBottom: 6,
  },
  chartNotPreparedStyle: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.bold,
  },
  chartPreparedStyle: {
    fontSize: 12,
    color: colors.lightGreen4,
    fontFamily: fonts.bold,
  },
  cancelledTrain: {
    flexDirection: 'row',
    backgroundColor: colors.lightPink,
    marginLeft: 8,
    marginRight: 8,
    marginTop: 10,
    marginBottom: 10,
    alignItems: 'flex-start',
    borderRadius:5,
  },
  cancelledTrainText: {
    width: 324,
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.regular,
    alignSelf: 'center',
    marginTop: 5,
    marginBottom: 5,
    marginLeft: 4,
    marginRight: 16,
  },
  imageStyle: {
    height: 26,
    width: 30,
    marginLeft: 8,
    justifyContent: 'center',
    alignSelf: 'center',
  },
});

PnrTrainDetails.propTypes = {
  trainInfo: PropTypes.shape({
    trainName: PropTypes.string,
    journeyDate: PropTypes.string,
    travelClass: PropTypes.string,
    travelQuota: PropTypes.string,
    isChartPrepared: PropTypes.bool,
    deptTime: PropTypes.string,
    deptDate: PropTypes.string,
    duration: PropTypes.string,
    arrivalTime: PropTypes.string,
    arrivalDate: PropTypes.string,
    trainCancelledFlag: PropTypes.bool,
    trainStatusMsg: PropTypes.string,
  }),
};

export default PnrTrainDetails;
