import React from 'react';
import PropTypes from 'prop-types';
import shareIcon from '@mmt/legacy-assets/src/share_pnr_icon.webp';
import {View, Text, Image, StyleSheet, Clipboard,Platform} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import ViewState from '../../../../Utils/ViewState';

import backIcon from '@mmt/legacy-assets/src/iconBackBlack.webp';

import refreshIconBlue from '@mmt/legacy-assets/src/refreshBlue.webp';

import copyIconBlue from '@mmt/legacy-assets/src/pnrStatusPageCopyButton.webp';

const PnrHeader = (props) => {
  const {
    onBackIconPressed, shareButtonClick, refreshButtonClick, pnrNumber, viewState,
  } = props;

  const copyPnr = () => {
    if (Platform.OS === 'web') {
      navigator.clipboard.writeText('Copy this text to clipboard');
    }
    else {
      Clipboard.setString(pnrNumber);
    }
  };

  return (
    <View style={pnrStyles.rootContainer} testId="pnr_header_container">
      <TouchableRipple onPress={onBackIconPressed} testId="pnr_header_back_icon_container">
        <View style={pnrStyles.backIconContainer} testId="pnr_header_back_icon_container">
          <Image
            style={pnrStyles.backIconStyle}
            source={backIcon}
            testId="pnr_header_back_icon_container"
          />
        </View>
      </TouchableRipple>
      <View style={{ justifyContent: 'center' }} testId="pnr_header_text_container">
        <Text style={pnrStyles.headerTextStyle} testId="pnr_header_text_container_pnr_status">
          PNR Status
        </Text>
        <Text style={pnrStyles.pnrNumberStyle} testId="pnr_header_text_container_pnr_number">
          PNR {pnrNumber}
        </Text>
      </View>
      <View style={{ flexDirection: 'row', height: '100%', marginLeft: 'auto' }}>
        {viewState !== ViewState.ERROR && viewState !== ViewState.NO_INTERNET && (
          <TouchableRipple onPress={copyPnr} testId="pnr_header_copy_icon_container">
            <View
              style={{ alignItems: 'center', justifyContent: 'center', height: '100%' }}
              testId="pnr_header_copy_icon_container"
            >
              <Image
                source={copyIconBlue}
                style={pnrStyles.copyIconStyle}
                testId="pnr_header_copy_icon_container"
              />
            </View>
          </TouchableRipple>
        )}
        {viewState !== ViewState.ERROR && viewState !== ViewState.NO_INTERNET && (
          <TouchableRipple onPress={refreshButtonClick} testId="pnr_header_refresh_icon_container">
            <View
              style={{ alignItems: 'center', justifyContent: 'center', height: '100%' }}
              testId="pnr_header_refresh_icon_container"
            >
              <Image
                source={refreshIconBlue}
                style={pnrStyles.refreshIconStyle}
                testId="pnr_header_refresh_icon_container"
              />
            </View>
          </TouchableRipple>
        )}
        {viewState !== ViewState.ERROR && viewState !== ViewState.NO_INTERNET && (
          <TouchableRipple onPress={shareButtonClick} testId="pnr_header_share_icon_container">
            <View
              style={{ alignItems: 'center', justifyContent: 'center', height: '100%' }}
              testId="pnr_header_share_icon_container"
            >
              <Image
                source={shareIcon}
                style={pnrStyles.shareIconStyle}
                testId="pnr_header_share_icon_container"
              />
            </View>
          </TouchableRipple>
        )}
      </View>
    </View>
  );
};

PnrHeader.propTypes = {
  onBackIconPressed: PropTypes.func.isRequired,
  shareButtonClick: PropTypes.func.isRequired,
  refreshButtonClick: PropTypes.func.isRequired,
  pnrNumber: PropTypes.string,
  viewState: PropTypes.string,
};

const pnrStyles = StyleSheet.create({
  rootContainer: {
    flexDirection: 'row',
    height: 64,
    borderBottomColor: colors.datePickerColor,
    borderBottomWidth: 0.5,
    borderTopWidth: 0,
    borderTopColor: colors.white,
  },
  backIconStyle: {
    width: 18,
    height: 16,
    justifyContent: 'center',
  },
  backIconContainer: {
    width: 48,
    height: 48,
    marginTop: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },

  copyIconStyle: {
    marginRight: 13,
    width: 24,
    height: 24,
  },

  headerTextStyle: {
    marginLeft: 12,
    marginTop: 13,
    fontFamily: fonts.bold,
    fontSize: 16,
    color: colors.black,
  },
  pnrNumberStyle: {
    marginLeft: 12,
    marginBottom: 10,
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.lightTextColor,
  },
  refreshIconStyle: {
    marginLeft : 13,
    marginRight: 13,
    width: 24,
    height: 24,
  },
  shareIconStyle: {
    marginLeft: 13,
    marginRight: 13,
    width: 16,
    height: 18,
    justifyContent: 'center',
    paddingTop: 2,
    paddingBottom: 3,
  },
});

export default PnrHeader;
