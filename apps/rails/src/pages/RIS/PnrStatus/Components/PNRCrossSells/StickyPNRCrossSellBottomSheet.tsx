import React, { useEffect } from 'react';
import { Platform, SafeAreaView, View } from 'react-native';
import {
  trackClickEventProp61,
  trackPageLoad,
} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from 'apps/rails/src/RisAnalytics/RisAnalytics';
import { getDateForReturnTicket, openCrossSell } from 'apps/rails/src/pages/RIS/RisCommonUtils';
import {
  CROSS_SELL_CLICK,
  getCrossSellTrackingKey,
} from 'apps/rails/src/pages/RIS/Utils/RISOmnitureConstants';
import { CrossSellHeader } from './Components';
import styles from './styles';
import { COMPONENT_TYPES, CROSS_SELL_TYPES, CrossSellDataObj } from './types';
import { logRisPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS';

interface Props {
  crossSellData: CrossSellDataObj;
  onClosePressed: () => void;
  visible: boolean;
}

const StickyPNRCrossSellBottomSheet = (props: Props) => {
  const { onClosePressed, visible, crossSellData } = props;

  const onViewButtonPress = () => {
    trackClickEventProp61(
      RIS_LANDING_PNR_DETAILS_SUCCESS,
      CROSS_SELL_CLICK[crossSellData.type as keyof typeof CROSS_SELL_CLICK].STICKY_VIEW_ALL,
    );
    onClosePressed();
    if (crossSellData.type === CROSS_SELL_TYPES.RETURN_TICKETS && crossSellData?.data?.length > 0) {
      const date = getDateForReturnTicket(crossSellData?.data[0].date);
      openCrossSell(crossSellData.searchParams, crossSellData.type, date);
      logRisPdtEvent({
        eventValue: `${crossSellData.type}_STICKY_VIEW_ALL`,
      });
      return;
    }
    openCrossSell(crossSellData.searchParams, crossSellData.type);
  };

  useEffect(() => {
    if (visible) {
      trackPageLoad(
        RIS_LANDING_PNR_DETAILS_SUCCESS,
        getCrossSellTrackingKey(crossSellData.type, COMPONENT_TYPES.STICKY_BOTTOM_SHEET),
      );
    }
  }, [crossSellData.type, visible]);

  if (!visible) {
    return null;
  }

  return (
    <SafeAreaView style={Platform.OS === 'android' ? styles.stickyContainer : {}}>
      <View style={styles.stickyContainer}>
        <CrossSellHeader
          onClosePressed={onClosePressed}
          isSticky={true}
          crossSellData={crossSellData}
          onViewButtonPress={onViewButtonPress}
        />
      </View>
    </SafeAreaView>
  );
};

export default StickyPNRCrossSellBottomSheet;
