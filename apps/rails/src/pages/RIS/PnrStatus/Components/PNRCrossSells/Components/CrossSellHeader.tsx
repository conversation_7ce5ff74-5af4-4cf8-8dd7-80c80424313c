import React from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { RectangularGradientButton } from 'apps/rails/src/pages/Common/Buttons';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import styles from '../styles';
import { CROSS_SELL_TYPES, CrossSellDataObj } from '../types';

interface CrossSellHeaderProps {
  onClosePressed: () => void;
  isSticky?: boolean;
  crossSellData: CrossSellDataObj;
  onViewButtonPress: () => void;
}

const CrossSellHeader = (props: CrossSellHeaderProps) => {
  const {
    onClosePressed,
    isSticky = true,
    crossSellData: { configData, type, searchParams } = {},
    onViewButtonPress,
  } = props;
  const urgencyText = isSticky ? configData?.urgencyText : configData?.bsUrgencyText;
  return (
    <View testID="pnr_cross_sell_bottomsheet_modal_content_container">
      {urgencyText && (
        <View
          style={[styles.closeContainer, styles.stickyBorder]}
          testID="pnr_cross_sell_bottomsheet_modal_content_container_close_container"
        >
          <Text
            style={[
              styles.message,
              fontStyle('bold'),
              getLineHeight(type === CROSS_SELL_TYPES.BUS ? 14 : 16),
            ]}
            testID="pnr_cross_sell_bottomsheet_modal_content_container_text"
          >
            {urgencyText}
          </Text>
          <TouchableOpacity
            style={styles.stickyCloseButton}
            onPress={onClosePressed}
            testID="pnr_cross_sell_bottomsheet_modal_content_container_close_button"
          >
            <Image
              source={ASSETS.ic_close}
              style={styles.closeIcon}
              testID="pnr_cross_sell_bottomsheet_modal_content_container_close_icon"
            />
          </TouchableOpacity>
        </View>
      )}
      <View
        style={[styles.pnrHeaderContent, !urgencyText && styles.border]}
        testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content"
      >
        <Image
          source={configData?.iconURl ? { uri: configData?.iconURl } : ASSETS.icYellowTrain}
          style={styles.crossSellIcon}
          testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content_icon"
        />
        <View
          style={styles.fullFlex}
          testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content_full_flex"
        >
          <Text
            style={[styles.title, fontStyle('bold'), getLineHeight(isSticky ? 16 : 21)]}
            testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content_full_flex_text"
          >
            {configData?.title}
          </Text>

          {isSticky && type !== CROSS_SELL_TYPES.BUS ? (
            <View
              style={styles.rowContainer}
              testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content_row_container"
            >
              <Text
                numberOfLines={1}
                style={[styles.description, fontStyle('regular')]}
                testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content_row_container_text"
              >
                {searchParams?.sourceCode}
              </Text>
              <Image
                source={ASSETS.ic_arrow_right_small}
                style={styles.arrowImage}
                testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content_row_container_icon"
              />
              <Text
                numberOfLines={1}
                style={[styles.description, fontStyle('regular')]}
                testID="pnr_cross_sell_bottomsheet_modal_content_container_header_content_row_container_text_text"
              >
                {searchParams?.destinationCode}
              </Text>
            </View>
          ) : (
            <Text
              numberOfLines={1}
              style={[
                styles.nonsDescription,
                fontStyle('regular'),
                getLineHeight(isSticky ? 14 : type === CROSS_SELL_TYPES.BUS ? 14 : 16),
              ]}
            >
              {configData?.description}
            </Text>
          )}
        </View>
        {(!urgencyText || isSticky) &&
          (!isSticky ? (
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClosePressed}
              testID="pnr_cross_sell_bottomsheet_modal_content_container_close_button"
            >
              <Image
                source={ASSETS.ic_close}
                style={styles.closeIcon}
                testID="pnr_cross_sell_bottomsheet_modal_content_container_close_icon"
              />
            </TouchableOpacity>
          ) : (
            <RectangularGradientButton
              gradientStyle={styles.gradientStyle}
              label={configData?.stickyCTA}
              labelSize={14}
              onPress={onViewButtonPress}
            />
          ))}
      </View>
    </View>
  );
};

export default CrossSellHeader;
