import { Platform, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  azureText: {
    color: colors.azure,
  },
  border: {
    alignItems: 'center',
    borderColor: colors.lightGrey,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderTopWidth: 1,
    elevation: 4,
    paddingBottom: 0,
    shadowColor: colors.black,
    shadowOffset: {
      height: -10,
      width: 0,
    },
    shadowRadius: 10,
  },
  stickyBorder: {
    alignItems: 'center',
    borderColor: colors.lightGrey,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderTopWidth: 1,
    elevation: 4,
    shadowColor: colors.black,
    shadowOffset: {
      height: -10,
      width: 0,
    },
    shadowRadius: 10,
  },
  paddingBottom16: {
    paddingBottom: 16,
  },
  borderCrossSellContainer: {
    borderColor: colors.lightSilver,
    borderRadius: 16,
    borderWidth: 1,
    marginHorizontal: 16,
    paddingBottom: 0,
    overflow: 'hidden',
    paddingTop: 16,
  },
  btnImg: {
    height: 15,
    marginLeft: 4,
    width: 15,
  },
  closeButton: {
    alignSelf: 'baseline',
    padding: 4,
    paddingBottom: 20,
    paddingLeft: 20,
  },
  closeContainer: {
    alignItems: 'center',
    backgroundColor: colors.creamWhite,
    flexDirection: 'row',
    paddingLeft: 16,
    paddingVertical: 4,
  },
  closeIcon: {
    height: 12,
    width: 12,
  },
  content: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  contentContainer: {
    backgroundColor: colors.modalgrey,
    marginTop: 'auto',
    ...Platform.select({
      ios: {
        marginBottom: 16,
      },
    }),
  },
  crossSellBtn: {
    borderRadius: 8,
    height: 44,
    paddingHorizontal: 10,
  },
  crossSellContainer: {
    backgroundColor: colors.white,
    paddingBottom: 12,
  },
  crossSellContent: {
    backgroundColor: colors.white,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  crossSellIcon: {
    height: 36,
    marginRight: 8,
    width: 36,
  },
  description: {
    color: colors.black,
    fontSize: 12,
    maxWidth: '50%',
  },
  nonsDescription: {
    color: colors.black,
    fontSize: 14,
    marginTop: 4,
  },
  flexRow: {
    flexDirection: 'row',
  },
  rowContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 2,
  },
  arrowImage: {
    marginHorizontal: 4,
    resizeMode: 'contain',
    width: 16,
    height: 8,
  },
  fullFlex: {
    flex: 1,
  },
  gradientStyle: {
    borderRadius: 8,
    height: 32,
    paddingHorizontal: 10,
  },
  header: {
    backgroundColor: colors.lightGrey,
    borderRadius: 2,
    height: 4,
    width: 40,
  },
  inlineHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inlineHeaderContent: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    paddingBottom: 16,
    paddingLeft: 10,
  },
  line: {
    backgroundColor: colors.lightGrey,
    height: 1,
    marginBottom: 12,
    width: '100%',
  },
  message: {
    color: colors.lightYello,
    flex: 1,
    fontSize: 14,
    textAlign: 'center',
  },
  modalBackground: {
    backgroundColor: colors.modalgrey,
    flex: 1,
  },
  pnrHeaderContent: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    paddingBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  stickyCloseButton: {
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  stickyContainer: {
    backgroundColor: colors.transparent,
    bottom: 0,
    flex: 1,
    left: 0,
    marginTop: 'auto',
    position: 'absolute',
    right: 0,
    ...Platform.select({
      ios: {
        paddingBottom: 16,
        backgroundColor: colors.white,
      },
    }),
  },
  title: {
    color: colors.black,
  },
  transparentBackground: {
    backgroundColor: colors.transparent,
    flex: 1,
  },
  viewAllContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    marginRight: 10,
  },
  offerCode: {
    color: colors.lightGreen16,
  },
  offerContainer: {
    alignItems: 'center',
    backgroundColor: colors.lightGreen17,
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
    paddingLeft: 16,
  },
  offerIc: {
    height: 18,
    marginRight: 6,
    resizeMode: 'contain',
    width: 18,
  },
  offerText: {
    color: colors.black,
  },
  copyButton: {
    paddingRight: 16,
    paddingVertical: 5,
  },
  copyIc: {
    height: 18,
    marginLeft: 6,
    resizeMode: 'contain',
    width: 18,
  },
});
