import React, { useEffect } from 'react';
import { View } from 'react-native';
import { trackPageLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from 'apps/rails/src/RisAnalytics/RisAnalytics';
import { getCrossSellTrackingKey } from 'apps/rails/src/pages/RIS/Utils/RISOmnitureConstants';
import AlternateTrainsNew from '../AlternateTrainsNew/AlternateTrainsNew';
import { ReturnTickets } from '../ReturnTickets';
import { BusTickets } from '../BusTickets';
import styles from './styles';
import { COMPONENT_TYPES, CROSS_SELL_TYPES, CrossSellDataObj } from './types';

interface Props {
  crossSellData: CrossSellDataObj;
  pnrDetailInfo: unknown;
  isFromBottomSheet: boolean;
  onViewButtonPress?: () => void;
  onClosePressed?: () => void;
}

const PNRCrossSellInline = (props: Props) => {
  const { crossSellData, pnrDetailInfo, isFromBottomSheet } = props;

  useEffect(() => {
    trackPageLoad(
      RIS_LANDING_PNR_DETAILS_SUCCESS,
      getCrossSellTrackingKey(crossSellData.type, COMPONENT_TYPES.INLINE),
    );
  }, [crossSellData.type]);

  const renderCrossSellComponent = () => {
    switch (crossSellData.type) {
      case CROSS_SELL_TYPES.CONFIRMED_OPTIONS:
        return (
          <AlternateTrainsNew
            isFromBottomSheet={props.isFromBottomSheet}
            crossSellData={crossSellData}
            pnrDetailInfo={pnrDetailInfo}
            onClosePressed={props.onClosePressed}
          />
        );
      case CROSS_SELL_TYPES.RETURN_TICKETS:
        return (
          <ReturnTickets
            isFromBottomSheet={props.isFromBottomSheet}
            crossSellData={crossSellData}
            onClosePressed={props.onClosePressed}
          />
        );
      case CROSS_SELL_TYPES.BUS:
        return (
          <BusTickets
            isFromBottomSheet={props.isFromBottomSheet}
            crossSellData={crossSellData}
            onClosePressed={props.onClosePressed}
          />
        );
      default:
        return null;
    }
  };

  return (
    <View
      style={[styles.crossSellContainer, !isFromBottomSheet && styles.borderCrossSellContainer]}
      testID="pnr_cross_sell_inline_container"
    >
      {renderCrossSellComponent()}
    </View>
  );
};

export default PNRCrossSellInline;
