import React from 'react';
import { Clipboard, Image, Text, TouchableOpacity, View } from 'react-native';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import styles from '../styles';
import { Coupon } from '../types';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';

interface CrossSellCouponsProps {
  coupon?: Coupon;
}

const CrossSellCoupons = (props: CrossSellCouponsProps) => {
  const { coupon } = props;

  if (!coupon?.code) {
    return null;
  }

  const handleCopy = () => {
    if (!coupon?.code) {
      return;
    }
    Clipboard.setString(coupon.code);
    showShortToast('Coupon code copied');
  };

  return (
    <View style={styles.offerContainer} testID="pnr_cross_sell_coupons_container">
      <Image
        style={styles.offerIc}
        source={ASSETS.ic_offer}
        resizeMode="contain"
        testID="pnr_cross_sell_coupons_icon"
      />
      { }
      <Text
        style={[fontStyle('medium'), getLineHeight(14), styles.offerText]}
        testID="pnr_cross_sell_coupons_text"
      >
        {_label('avail')}
        {/*eslint-disable-next-line*/}
        <Text style={[fontStyle('black')]} testID="pnr_cross_sell_coupons_amount_text">{` ₹${coupon.amount} `}</Text>
        {_label('off_using_code')}
        <Text
          style={[fontStyle('black'), styles.offerCode]}
          testID="pnr_cross_sell_coupons_code_text"
        >{` ${coupon.code}`}</Text>
      </Text>
      <TouchableOpacity
        style={styles.copyButton}
        onPress={handleCopy}
        testID="pnr_cross_sell_coupons_copy_button"
      >
        <Image
          style={styles.copyIc}
          source={ASSETS.ic_copy}
          resizeMode="contain"
          testID="pnr_cross_sell_coupons_copy_icon"
        />
      </TouchableOpacity>
    </View>
  );
};

export default CrossSellCoupons;
