import { useEffect, useState } from 'react';
import { VISIBLE_STATE } from './types';

const BOTTOM_SHEET_DELAY = 3000;

const useCrossSellData = () => {
  const [visibleState, setVisibleState] = useState<VISIBLE_STATE>(VISIBLE_STATE.NONE);
  const [delayStickyRender, setDelayStickyRender] = useState(true);

  useEffect(() => {
    const timeoutRef = setTimeout(() => {
      setVisibleState(VISIBLE_STATE.BOTTOM_SHEET);
    }, BOTTOM_SHEET_DELAY);
    const stickyTimeoutRef = setTimeout(() => {
      setDelayStickyRender(true);
    }, BOTTOM_SHEET_DELAY + 1000);
    return () => {
      clearTimeout(timeoutRef);
      clearTimeout(stickyTimeoutRef);
      setVisibleState(VISIBLE_STATE.NONE);
    };
  }, []);
  return { visibleState, delayStickyRender, setVisibleState };
};

export default useCrossSellData;
