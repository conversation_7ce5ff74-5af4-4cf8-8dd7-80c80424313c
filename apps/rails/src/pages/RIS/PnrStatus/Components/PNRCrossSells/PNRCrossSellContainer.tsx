import React from 'react';
import { connect } from 'react-redux';
import {
  PNRCrossSellBottomSheet,
  PNRCrossSellInline,
  StickyPNRCrossSellBottomSheet,
} from './index';
import { COMPONENT_TYPES, ComponentTypes, CrossSellDataObj, VISIBLE_STATE } from './types';
import useCrossSellData from './useCrossSellData';

interface Props {
  pnrDetailInfo: unknown;
  type?: ComponentTypes;
  crossSellData?: CrossSellDataObj;
}

const PNRCrossSellContainer = ({ pnrDetailInfo, type, crossSellData }: Props) => {
  const { visibleState, delayStickyRender, setVisibleState } = useCrossSellData();

  const onClosePressed = () => {
    if (visibleState === VISIBLE_STATE.BOTTOM_SHEET) {
      setVisibleState(VISIBLE_STATE.STICKY);
    } else {
      setVisibleState(VISIBLE_STATE.NONE);
    }
  };

  if (!crossSellData?.data) {
    return null;
  }

  if (type === COMPONENT_TYPES.INLINE) {
    return (
      <PNRCrossSellInline
        crossSellData={crossSellData}
        pnrDetailInfo={pnrDetailInfo}
        isFromBottomSheet={false}
      />
    );
  }

  return (
    <>
      {delayStickyRender && (
        <StickyPNRCrossSellBottomSheet
          crossSellData={crossSellData}
          onClosePressed={onClosePressed}
          visible={visibleState !== VISIBLE_STATE.NONE}
        />
      )}
      <PNRCrossSellBottomSheet
        crossSellData={crossSellData}
        onClosePressed={onClosePressed}
        visible={visibleState === VISIBLE_STATE.BOTTOM_SHEET}
        pnrDetailInfo={pnrDetailInfo}
      />
    </>
  );
};

const mapStateToProps = (state: unknown) => {
  const { crossSellData } = state.railInfoReducer;
  return { crossSellData };
};

export default connect(mapStateToProps, null)(PNRCrossSellContainer);
