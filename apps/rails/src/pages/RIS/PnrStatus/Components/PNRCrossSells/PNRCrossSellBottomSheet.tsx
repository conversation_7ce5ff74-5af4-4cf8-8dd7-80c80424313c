import React, { useEffect } from 'react';
import { Modal, TouchableOpacity, View } from 'react-native';
import {
  trackClickEventProp61,
  trackPageLoad,
} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { RIS_LANDING_PNR_DETAILS_SUCCESS } from 'apps/rails/src/RisAnalytics/RisAnalytics';
import { RectangularGradientButton } from 'apps/rails/src/pages/Common/Buttons';
import { getDateForReturnTicket, openCrossSell } from 'apps/rails/src/pages/RIS/RisCommonUtils';
import {
  CROSS_SELL_CLICK,
  getCrossSellTrackingKey,
} from 'apps/rails/src/pages/RIS/Utils/RISOmnitureConstants';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { CrossSellHeader } from './Components';
import { PNRCrossSellInline } from './index';
import styles from './styles';
import { COMPONENT_TYPES, CROSS_SELL_TYPES, CrossSellDataObj } from './types';
import { logRisPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/RIS';

interface Props {
  crossSellData: CrossSellDataObj;
  onClosePressed: () => void;
  visible: boolean;
  pnrDetailInfo?: unknown;
}

const PNRCrossSellBottomSheet = (props: Props) => {
  const { crossSellData, onClosePressed, visible, pnrDetailInfo } = props;

  const onViewButtonPress = () => {
    trackClickEventProp61(
      RIS_LANDING_PNR_DETAILS_SUCCESS,
      CROSS_SELL_CLICK[crossSellData.type as keyof typeof CROSS_SELL_CLICK].BS_VIEW_ALL,
    );
    onClosePressed();
    if (crossSellData.type === CROSS_SELL_TYPES.RETURN_TICKETS && crossSellData?.data?.length > 0) {
      const date = getDateForReturnTicket(crossSellData?.data[0].date); //Getting the first return train object train date
      openCrossSell(crossSellData.searchParams, crossSellData.type, date);
      logRisPdtEvent({
        eventValue: `${crossSellData.type}_BS_VIEW_ALL`,
      });
      return;
    }
    openCrossSell(crossSellData.searchParams, crossSellData.type);
  };

  useEffect(() => {
    trackPageLoad(
      RIS_LANDING_PNR_DETAILS_SUCCESS,
      getCrossSellTrackingKey(crossSellData.type, COMPONENT_TYPES.BOTTOM_SHEET),
    );
  }, [crossSellData?.type]);

  return (
    <Modal
      animationType="slide"
      visible={visible}
      transparent={true}
      onRequestClose={onClosePressed}
      statusBarTranslucent={true}
      testID="pnr_cross_sell_bottomsheet_modal"
    >
      <TouchableOpacity
        style={styles.modalBackground}
        onPressOut={onClosePressed}
        testID="pnr_cross_sell_bottomsheet_modal_background"
      />
      <View
        style={styles.contentContainer}
        testID="pnr_cross_sell_bottomsheet_modal_content_container"
      >
        <CrossSellHeader
          onClosePressed={onClosePressed}
          isSticky={false}
          crossSellData={crossSellData}
          onViewButtonPress={onViewButtonPress}
        />
        <PNRCrossSellInline
          crossSellData={crossSellData}
          pnrDetailInfo={pnrDetailInfo}
          isFromBottomSheet={true}
          onViewButtonPress={onViewButtonPress}
          onClosePressed={onClosePressed}
        />
        <View style={styles.crossSellContent}>
          <RectangularGradientButton
            gradientStyle={styles.crossSellBtn}
            label={crossSellData?.configData?.cta || _label('view_all')}
            onPress={onViewButtonPress}
          />
        </View>
      </View>
    </Modal>
  );
};

export default PNRCrossSellBottomSheet;
