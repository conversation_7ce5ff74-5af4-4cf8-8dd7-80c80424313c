/**** ConfigStore Response types ****/
export interface CrossSellsConfig {
  crossSells?: CrossSells;
}

export interface CrossSells {
  confirmedOptions: CrossSellOptions;
  returnTicketOptions: CrossSellOptions;
  busOptions: CrossSellOptions;
}

export interface CrossSellOptions {
  title: string;
  description: string;
  iconURl: string;
  urgencyText: string;
  bsUrgencyText: string;
  cta: string;
  stickyCTA: string;
}

/**** API Response types ****/
export interface CrossSellResponse {
  returnTripAvailabilityResponse?: ReturnTripAvailabilityResponse;
  alternateAvailabilityRisResponse?: AlternateAvailabilityRisResponse;
  busSearchV3RisResponse?: BusSearchV3RisResponse;
  success: boolean;
}

export interface ReturnTripAvailabilityResponse {
  availabilityByDate: AvailabilityByDate[];
  coupon: Coupon;
}

export interface AvailabilityByDate {
  date: string;
  statusText: string;
  colourCode: string;
}

export interface AlternateAvailabilityRisResponse {
  alternateAvailabilityResponse: AlternateAvailabilityResponse;
  coupon: Coupon;
}

export interface AlternateAvailabilityResponse {
  alternateAvailabilityMsg: string;
  alternateAvailabilityList: AlternateAvailabilityList[];
  quotaList: string[];
  lang: unknown;
  error: unknown;
}

export interface AlternateAvailabilityList {
  trainNumber: string;
  trainName: string;
  departureTime: string;
  departureDate: string;
  arrivalTime: string;
  arrivalDate: string;
  frmStnCode: string;
  frmStnName: string;
  toStnCode: string;
  toStnName: string;
  duration: number;
  distance: number;
  classAvailabilityList: ClassAvailabilityList[];
}

export interface ClassAvailabilityList {
  travelClass: string;
  frmStnCode: string;
  frmStnName: string;
  departureTime: string;
  departureDate: string;
  toStnCode: string;
  toStnName: string;
  arrivalTime: string;
  arrivalDate: string;
  distance: number;
  originalFare: number;
  fareDifference: number;
  tbsAvailability: TbsAvailability;
}

export interface TbsAvailability {
  lastUpdatedOn: string;
  lastUpdatedOnRaw: number;
  availablityDate: string;
  availablityStatus: string;
  prettyPrintingAvailablityStatus: string;
  availablityType: string;
  reason: unknown;
  reasonType: string;
  wlType: string;
  currentBkgFlag: string;
  classType: string;
  quota: string;
  totalFare: number;
  className: unknown;
  availablityScore: number;
  classWt: number;
  availAndClassScoreCombined: number;
  cumulativeQuotaScore: number;
  quotaScore: number;
  wzqScore: number;
  predictionPercentage: unknown;
  tgPremiumPercentage: unknown;
  fcPremiumMMT: number;
  isOutOfPolicy: boolean;
  outOfPolicyReason: unknown;
  blockBooking: boolean;
  freeCancellationAddonAllowed: boolean;
}

export interface BusSearchV3RisResponse {
  busSearchV3Response: BusSearchV3Response;
  coupon: Coupon;
}
export interface BusSearchV3Response {
  edges: Edge[];
  cityDetails: CityDetails;
  success: boolean;
}

export interface Edge {
  node: Node;
}

export interface Node {
  type: string;
  operatorName: string;
  attributes: Attributes;
  journeyInfo: JourneyInfo;
  fareDetails: FareDetails;
  extraInfo: ExtraInfo;
}

export interface Attributes {
  type: string;
}

export interface JourneyInfo {
  departureTimeStamp: number;
  arrivalTimeStamp: number;
  sourceCityName: string;
  destinationCityName: string;
  journeyTimeInMinutes: number;
}

export interface FareDetails {
  minPrice: number;
  maxPrice: number;
  displayPrice: number;
}

export interface ExtraInfo {
  overallRating: number;
}

export interface CityDetails {
  sourceCityName: string;
  sourceCityCode: string;
  destinationCityName: string;
  destinationCityCode: string;
}

export interface Coupon {
  code: string;
  amount: number;
}

type Data = AlternateAvailabilityList | AvailabilityByDate[] | Edge[] | undefined;

//**** Class types ****/
export interface CrossSellDataObj {
  searchParams: SearchParams;
  type: CrossSellTypes;
  viewAllTrainsDeepLink?: string;
  data?: Data;
  coupon?: Coupon;
  configData?: CrossSellOptions;
}

export interface SearchParams {
  sourceName: string;
  destinationName: string;
  sourceCode: string;
  destinationCode: boolean;
  date: Date;
}

export enum VISIBLE_STATE {
  STICKY,
  BOTTOM_SHEET,
  NONE,
}
export const COMPONENT_TYPES = {
  INLINE: 'INLINE',
  BOTTOM_SHEET: 'BOTTOM_SHEET',
  STICKY_BOTTOM_SHEET: 'STICKY_BOTTOM_SHEET',
};
export type ComponentTypes = (typeof COMPONENT_TYPES)[keyof typeof COMPONENT_TYPES];

export const CROSS_SELL_TYPES = {
  CONFIRMED_OPTIONS: 'CONFIRMED_OPTIONS',
  RETURN_TICKETS: 'RETURN_TICKETS',
  BUS: 'BUS',
};
export type CrossSellTypes = (typeof CROSS_SELL_TYPES)[keyof typeof CROSS_SELL_TYPES];
