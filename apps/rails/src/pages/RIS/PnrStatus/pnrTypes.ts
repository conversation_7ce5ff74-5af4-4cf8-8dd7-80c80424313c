export interface PnrDetailsInfo {
  BookingId: string;
  TrainDetails: TrainDetails;
  CityDetails: CityDetails;
  StationDetails: StationDetails;
  PnrDetails: PnrDetails;
  PassengerDetails: PassengerDetails;
  RatingDetails: RatingDetails;
  Disclaimer: string;
  CheckAlternateAvailability: boolean;
  IsBookedOnMMT: boolean;
  PnrSearchCoupon: PnrSearchCoupon;
  PnrLoginPersuasionCoupon: PnrLoginPersuasionCoupon;
  Source: string;
  TgsEligibility: TgsEligibility;
  DsPPnrCnfProbResponse: DsPpnrCnfProbResponse;
  LastUpdated: string;
}

export interface TrainDetails {
  Train: Train;
  DepartureDetails: DepartureDetails;
  TrainStartingDate: string;
  TrainCancelledFlag: boolean;
  ChartPrepared: boolean;
  Epoch: number;
}

export interface Train {
  Number: string;
  Name: string;
}

export interface DepartureDetails {}

export interface CityDetails {
  SourceCity: SourceCity;
  DestinationCity: DestinationCity;
}

export interface SourceCity {
  Name: string;
  Code: string;
}

export interface DestinationCity {
  Name: string;
  Code: string;
}

export interface StationDetails {
  BoardingPoint: BoardingPoint;
  ReservationUpto: ReservationUpto;
}

export interface BoardingPoint {
  name: string;
  code: string;
}

export interface ReservationUpto {
  name: string;
  code: string;
}

export interface PnrDetails {
  Pnr: string;
  Class: string;
  Text: string;
  PredictionDetails: PredictionDetails;
  DestinationDoj: DestinationDoj;
  SourceDoj: SourceDoj;
  PnrCurrentStatus: string;
  BookingDate: BookingDate;
  FareDetails: FareDetails;
  Quota: string;
  AlertSet: boolean;
  AlertEligible: boolean;
}

export interface PredictionDetails {
  PNR_PRED: string[];
  PNR_PRED_DISC: string;
  PNR_PRED_RAC: string;
}

export interface DestinationDoj {
  FormattedDate: string;
}

export interface SourceDoj {
  FormattedDate: string;
  Epoch: number;
}

export interface BookingDate {
  FormattedDate: string;
  Epoch: number;
}

export interface FareDetails {
  bookingFare: string;
  ticketFare: string;
}

export interface PassengerDetails {
  PassengerStatus: PassengerStatu[];
  Count: number;
}

export interface PassengerStatu {
  Pnr: string;
  Number: number;
  Berth: number;
  BookingStatus: string;
  CurrentStatus: string;
  BookingBerthNo: string;
  BookingStatusNew: string;
  CurrentBerthNo: string;
  CurrentStatusNew: string;
  ConfirmationProbability: ConfirmationProbability;
}

export interface ConfirmationProbability {}

export interface RatingDetails {}

export interface PnrSearchCoupon {
  couponHeader: string;
  couponSubHeader: string;
  couponDetail: string;
  couponButtonText: string;
}

export interface PnrLoginPersuasionCoupon {}

export interface TgsEligibility {
  message: string;
  responseFrom: string;
  response: Response;
  isTgEligible: boolean;
  success: boolean;
  error: Error;
  dsPredictionResponse: DsPredictionResponse;
}

export interface Response {
  metaTgEligible: boolean;
  tgMarketingVoucher: string;
}

export interface Error {
  errorCode: number;
  errorDescription: string;
}

export interface DsPredictionResponse {
  dsPredictionMap: unknown;
  errorMessage: string;
}

export interface DsPpnrCnfProbResponse {
  dsPredictionMap: unknown;
  errorMessage: string;
}
