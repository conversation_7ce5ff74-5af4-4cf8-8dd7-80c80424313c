import {
  ACTIONS_RAILS_PNR_STATUS_INIT,
  ACTIONS_RAILS_PNR_ALTERNATE_TRAINS,
  ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL,
  ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL_ERROR,
  ACTIONS_RAILS_PNR_LISTING_SET_BOTTOM_LOADER,
  ACTIONS_RAILS_PNR_NO_ALTERNATE_TRAINS,
  ACTIONS_RAILS_PNR_LISTING_SET_ERROR,
  ACTIONS_RAILS_PNR_ALERT_SET,
  ACTIONS_RAILS_PNR_ALERT_NOT_SET,
  ACTIONS_RAILS_PNR_RECENT_SEARCH,
  ACTIONS_RAILS_PNR_LISTING_SET_NO_NETWORK,
  ACTIONS_RAILS_INFO_COACH_POSITION,
  ACTIONS_RAILS_INFO_COACH_ERROR,
  ACTIONS_RAILS_INFO_PNR_ALERT_LOADER,
  ACTIONS_RAILS_INFO_ALT_TRAINS_LOADER,
  ACTION_RIS_LIVE_STATION_FORM_ORIGIN_SELECTED,
  ACTIONS_RAILS_INFO_ALT_BUS_LOADER,
  ACTIONS_RAILS_PNR_CROSS_SELL_BUS,
  ACTIONS_RAILS_PNR_ALTERNATE_TRAINS_NEW,
  ACTIONS_RAILS_PNR_CROSS_SELL_DATA,
  ACTIONS_RAILS_PNR_CROSS_SELL_DATA_TGS,
  ACTIONS_RAILS_UPCOMING_PNR,
  ACTIONS_RAILS_RIS_ANNOUNCMENTS,
} from './RailInfoAction';

const initialStates = {
  pnrViewState: null,
  pnrAlertState: null,
  alternateBusesDetails: null,
  crossSellData: null,
  upComingPnrs: {
    suggestedBookings: [],
  },
};

export default (state = initialStates, action) => {
  switch (action.type) {
    case ACTIONS_RAILS_PNR_STATUS_INIT: {
      const {
        pnrDetailInfo,
        trainInfo,
        passengerList,
        pnrViewState,
        showLoader,
        disclaimer,
        coachPosition,
        pnrNumber,
        pnrAlertState,
        showAlternateTrains,
        hotelCrossSellData,
        enabled,
        enabledPnr,
        message,
        recentPnr,
        pnrSearchCoupon,
        pnrLoginPersuasionCoupon,
        tgsEligibility,
        IsBookedOnMMT,
      } = action.data;
      return {
        ...state,
        pnrDetailInfo,
        trainInfo,
        passengerList,
        pnrViewState,
        showLoader,
        disclaimer,
        coachPosition,
        pnrNumber,
        pnrAlertState,
        showAlternateTrains,
        hotelCrossSellData,
        enabled,
        enabledPnr,
        message,
        recentPnr,
        pnrSearchCoupon,
        pnrLoginPersuasionCoupon,
        tgsEligibility,
        IsBookedOnMMT,
        crossSellData: null,
      };
    }
    case ACTIONS_RAILS_INFO_PNR_ALERT_LOADER: {
      const {pnrAlertState} = action.data;
      return {
        ...state,
        pnrAlertState,
      };
    }

    case ACTIONS_RAILS_INFO_ALT_TRAINS_LOADER: {
      const {showAltLoader} = action.data;
      return {
        ...state,
        showAltLoader,
      };
    }
    case ACTIONS_RAILS_PNR_LISTING_SET_BOTTOM_LOADER: {
      return {
        ...state,
        ...action.data,
      };
    }

    case ACTIONS_RAILS_PNR_LISTING_SET_NO_NETWORK: {
      const {
        pnrViewState, pnrNumber, showLoader, coachViewState,
      } = action.data;
      return {
        ...state,
        pnrViewState,
        pnrNumber,
        showLoader,
        coachViewState,
      };
    }

    case ACTIONS_RAILS_PNR_RECENT_SEARCH: {
      const {pnrDetail} = action.data;
      return {
        ...state,
        pnrDetail,
      };
    }

    case ACTION_RIS_LIVE_STATION_FORM_ORIGIN_SELECTED: {
      const {
        code, stationName, cityName, stateName,
      } = action.data;
      return {
        ...state,
        stationDetails: {
          code,
          stationName,
          cityName,
          stateName,
          destinationStationCode: undefined,
        },
      };
    }

    case ACTIONS_RAILS_PNR_ALERT_SET: {
      const {
        pnrAlertState, pnrAlertSet, message, enabled,
      } = action.data;
      return {
        ...state,
        pnrAlertState,
        pnrAlertSet,
        message,
        enabled,
      };
    }

    case ACTIONS_RAILS_PNR_ALERT_NOT_SET: {
      const {pnrAlertState, enabled} = action.data;
      return {
        ...state,
        pnrAlertState,
        enabled,
      };
    }

    case ACTIONS_RAILS_PNR_LISTING_SET_ERROR: {
      const {pnrViewState, pnrNumber, showLoader} = action.data;
      return {
        ...state,
        pnrViewState,
        pnrNumber,
        showLoader,
      };
    }
    case ACTIONS_RAILS_PNR_ALTERNATE_TRAINS: {
      const {
        headerTitle,
        subheaderTitle,
        trainList,
        deeplink,
        lastUpdated,
        totalOption,
        showAlternateTrains,
      } = action.data;
      return {
        ...state,
        headerTitle,
        subheaderTitle,
        trainList,
        deeplink,
        lastUpdated,
        totalOption,
        showAlternateTrains,
      };
    }
    case ACTIONS_RAILS_PNR_ALTERNATE_TRAINS_NEW: {
      const {
        alternateAvailabilityList,
      } = action.data;
      return {
        ...state,
        alternateAvailabilityList,
      };
    }
    case ACTIONS_RAILS_PNR_NO_ALTERNATE_TRAINS: {
      const {
        showAlternateTrains,
      } = action.data;
      return {
        showAlternateTrains,
      };
    }

    case ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL: {
      const {
        hotelCrossSellData,
      } = action.data;
      return {
        ...state,
        hotelCrossSellData,
      };
    }

    case ACTIONS_RAILS_PNR_HOTEL_CROSS_SELL_ERROR: {
      const {
        hotelCrossSellData,
      } = action.data;
      return {
        hotelCrossSellData,
      };
    }

    case ACTIONS_RAILS_INFO_COACH_POSITION: {
      const {coachList, trainName, coachViewState} = action.data;
      return {
        coachList,
        trainName,
        coachViewState,
      };
    }

    case ACTIONS_RAILS_INFO_COACH_ERROR: {
      const {coachList, coachViewState, coachViewErrorMsg} = action.data;
      return {
        coachList,
        coachViewState,
        coachViewErrorMsg,
      };
    }

    case ACTIONS_RAILS_INFO_ALT_BUS_LOADER: {
      return {
        ...state,
        showAlternateBus: action.data,
      };
    }

    case ACTIONS_RAILS_PNR_CROSS_SELL_BUS: {
      return {
        ...state,
        alternateBusesDetails: action.data,
      };
    }
    case ACTIONS_RAILS_PNR_CROSS_SELL_DATA: {
      return {
        ...state,
        crossSellData: action.data,
      };
    }
    case ACTIONS_RAILS_PNR_CROSS_SELL_DATA_TGS: {
      return {
        ...state,
        tgsEligibility: action.data,
      };
    }
    case ACTIONS_RAILS_UPCOMING_PNR: {
      return {
        ...state,
        upComingPnrs: action.data,
      };
    }
    case ACTIONS_RAILS_RIS_ANNOUNCMENTS: {
      return {
        ...state,
        risAnnouncements: action.data?.risAnnouncements,
        risAnnouncementsLoading: action.data?.risAnnouncementsLoading,
      };
    }
    default:
      return state;
  }
};

