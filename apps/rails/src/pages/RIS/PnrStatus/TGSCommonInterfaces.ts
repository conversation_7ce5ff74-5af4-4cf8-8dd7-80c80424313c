export interface tripGuranteeDetails {
    tripGuaranteeText: string;
    bookingID: string;
    manageBookingURL: string;
}

export interface voucherCodeStyleDetails {
    backgroundColor: string;
    color: string;
}

export interface refundTextDetails {
    refundTextColumn1: string;
    refundTextColumn2: string;
    refundTextStyle: string;
}

export interface TermsAndConditionsDetails {
    voucherActivation: string;
    partiallyConfirmed: string;
    voucherValidity: string;
}

export interface ThankYouPageDetails {
    containerBg: Array<string>;
    tripGuaranteeHeader: tripGuranteeDetails;
    pnrNumber: number;
    trainDateAndInfo: string;
    policyText: string;
    voucherCodeMessage: string;
    voucherCodeStyle: voucherCodeStyleDetails;
    refundText: refundTextDetails;
    termsAndConditions: TermsAndConditionsDetails;
}

export interface RisOptionCardProps {
    icon: number;
    iconText: string;
    onClick: () => void;
}
export interface VoucherCardProps {
    voucherMsg: string;
    voucherCodeStyle: voucherCodeStyleDetails;
    refundText: refundTextDetails;
}

export interface RefundCardsProps {
    data: string;
    dataStyle: string;
    doubleRupeeSymbol?: boolean;
}

export interface TermsAndConditionsProps {
    termsAndConditions: TermsAndConditionsDetails;
}

export interface bookingIdProps {
    bookingID: string;
}

interface BannerTagDetails {
    backgroundColor: string;
    bannerTagText: string;
    bannerTagTextStyle: string;
}

interface PnrContainerDetails {
    backgroundColor: string;
    pnrTextStyle: string;
    pnrText: string;
    ticketEligibilityText: string;
    ticketEligibilityStyleSheet: string;
}

interface AvailabilityContainerDetails {
    availabilityText: string;
    availabilityStyleSheet: string;
}
interface PaymentContainerDetails {
    backgroundColor: string;
    totalTicketAmount: string;
    totalTicketAmountStyleSheet: string;
    refundAmount: string;
    refundAmountStyleSheet: string;
}

interface TGSTravellersPaymentContainerDetails {
    backgroundColor: string;
    totalTicketAmount: string;
    ticketFareStyleSheet: string;
    ticketFare: string;
    refund: string;
    refundStyleSheet: string;
}

interface TGSTravellersRefundContainer {
    refundText1x: string;
    refundText2x: string;
    refundText3x: string;
    refundStyle3x: string;
}


interface ButtonContainerDetails {
    buttonColor: Array<string>;
    buttonTextColor: string;
    buttonText: string;
}

export interface TGSRISPageDetails {
    containerBg: Array<string>;
    bannerTag: BannerTagDetails;
    pnrContainer: PnrContainerDetails;
    availabilityContainer: AvailabilityContainerDetails;
    paymentContainer: PaymentContainerDetails;
    buttonContainer: ButtonContainerDetails;
    termsAndConditionsLink?: string;
    tncContainer?: unknown;
    tgPremiumAmount:string;
    totalVoucherAmount:string
}

export interface TGSRISPageDetailsProps {
    tgsEligibility: TGSRISPageDetails;
    performWebLogin: () => void;
    userLoggedIn: boolean;
    pnrNumber: string;
    IsBookedOnMMT: boolean;
    cta: string;
    refreshButtonClick: () => void;
    bottomSheet?:boolean;
    onClose:() => void;
    tgsTextData: unknown;
}

export interface JourneyDetails {
    trainSourceDestinationStationName: string;
    trainSourceDestinationCode: string;
    pnr: string;
    trainName: string;
}

export interface PriceBreakUp {
    tripGuaranteePremium: string;
    serviceCharge: string;
    discount: string;
    totalPrice: string;
}

export interface TicketDetailsBottomSheetInterface {
    setPlatformVal: () => void;
    platformVal: string;
    setModalVal: () => void;
    handleTouchOutside: () => void;
}


export interface TicketDetailsContainerInterface {
    setModalVal: () => void;
    platformVal: string;
    setRadioButton: () => void;
    radioButton: string;
    captureRef: () => void;
}

export interface TravellerDetailsInterface {
    name: string;
    mobile: string;
    email: string;
}

export interface TGSTicketDetailsRowComponentInterface {
    logoName: string;
    logo: HTMLImageElement;
    buttonName: string;
    logoStyle: unknown;
    radioButton: string;
}

export interface JourneyDetailsProps {
    journeyDetailsContainer: JourneyDetails
}

export interface PriceBreakupProps {
    priceBreakUpContainer: PriceBreakUp
}


export interface TravellerDetailsProps {
    travellerFields: TravellerDetailsInterface;
    setTravellerFields: () => void;
    error: string;
    setError: () => void;
    tgsEmailInputMandatory: boolean;
    captureRef: () => void;
}

export interface tncContainerProps {
    tncContainerText: string;
    tncContainerLink: string;
}

export interface TripGuaranteeBenefitProps {
    backgroundColor: string;
    tripGuaranteeBenefitText: string;
    tripGuaranteeBenefitStyle: string;
}
export interface TGSTravellersPageResponseProps {
    containerBg: Array<string>;
    bannerTag: BannerTagDetails;
    paymentContainer: TGSTravellersPaymentContainerDetails;
    availabilityContainer: AvailabilityContainerDetails;
    refundContainer: TGSTravellersRefundContainer;
    tripGuaranteeBenefit: TripGuaranteeBenefitProps;
    sourceDestinationAndDateInfo: string;
    travellerPassengerDetails: unknown;
    tncContainer: tncContainerProps;
    journeyDetailsContainer: JourneyDetails;
    order: string;
    priceBreakUpContainer: PriceBreakUp;
}
