import { DeepLinkParams, RequestDetails } from './RisCommonInterfaces';
import {
  AbConfigKeyMappings,
  getPokusConfig,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { Actions } from '../../navigation';
import { Platform } from 'react-native';
import { getDateDetails } from 'apps/rails/src/Utils/RisUtils';
import fecha from 'fecha';
import GenericModule from '@mmt/core/native/GenericModule';
import BusSharedModuleHolder from '@mmt/bus-shared/src';
import {
  SearchParams,
  CrossSellTypes,
  CROSS_SELL_TYPES,
} from 'apps/rails/src/pages/RIS/PnrStatus/Components/PNRCrossSells/types';
import { RAILS_DEEPLINK } from 'apps/rails/src/Utils/RailsConstant';
import { StoppingStation } from './LiveTrainStatus/interface';

export const openLiveTrainStatus = async (requestDetails: RequestDetails) => {
  const { trainDetails, stationDetails, dateDetails } = requestDetails;
  if (Platform.OS === 'web') {
    const url = `https://www.makemytrip.com/railways/liveStatus/?q1=${trainDetails.trainNumber}`; //
    window.location.href = url;
  } else {
    // LTS flow is in Native and RN
    // This pokus decide which to use
    const ltsFlow = await getPokusConfig(PokusLobs.RAIL, AbConfigKeyMappings.ltsRNFlow, 2);
    switch (ltsFlow) {
      case 1:
        RailsModule.openLiveTrainStatus({
          trainDetail: trainDetails,
          stationDetail: stationDetails,
          dateDetail: dateDetails,
        });
        break;

      case 2:
        Actions.railLiveTrainStatusRNLanding({
          requestDetails: {
            trainDetail: trainDetails,
            stationDetail: stationDetails,
            dateDetail: dateDetails,
          },
        });
    }
  }
};

export const openLiveTrainStatusFromDeeplink = async (deepLinkParams: DeepLinkParams) => {
  const dateOfJourney = fecha.parse(deepLinkParams.dateOfJourney, 'YYYYMMDD');
  const dateDetail = getDateDetails(dateOfJourney);
  Actions.railLiveTrainStatusRNLanding({
    requestDetails: {
      trainDetail: {
        trainNumber: deepLinkParams?.trainNumber,
      },
      dateDetail,
    },
  });
};

export const getRailsListingDeeplinkFromPnrDetails = (pnrDetails: unknown, isReturnTicket: boolean) => {
  const {
    PnrDetails,
    TrainDetails,
    CityDetails: { SourceCity, DestinationCity } = {},
  } = pnrDetails;
  const startCode = isReturnTicket ? DestinationCity?.Code : SourceCity?.Code;
  const endCode = isReturnTicket ? SourceCity?.Code : DestinationCity?.Code;
  const journeyDate = isReturnTicket
    ? SourceCity?.Code
    : TrainDetails?.TrainStartingDate?.replaceAll('-', '');
  return RAILS_DEEPLINK.LISTING.replace('{startCode}', startCode)
    .replace('{endCode}', endCode)
    .replace('{journeyDate}', journeyDate)
    .replace('pnr', PnrDetails.Pnr)
    .replace('alternate', 'true');
};

export const getRailsListingDeeplinkFromLtsDetails = (
  SourceCity: StoppingStation,
  DestinationCity: StoppingStation,
  returnDate: string,
) => {
  const startCode = SourceCity?.Station.code;
  const endCode = DestinationCity?.Station.code;
  const journeyDate = returnDate?.replaceAll('-', '');
  return RAILS_DEEPLINK.LISTING_FROM_LTS.replace('{startCode}', startCode)
    .replace('{endCode}', endCode)
    .replace('{journeyDate}', journeyDate);
};

export const getDateForReturnTicket = (date: string): Date | null => {
  try {
    return fecha.parse(date, 'YYYYMMDD');
  } catch (error) {
    return null;
  }
};

export const openCrossSell = (
  searchParams: SearchParams,
  type: CrossSellTypes,
  journeyDate: Date | null = null,
) => {
  const { date, destinationName, destinationCode, sourceName, sourceCode } = searchParams;
  const listingDate = journeyDate || date;
  if (type === CROSS_SELL_TYPES.BUS) {
    const busSharedResourceProvider = BusSharedModuleHolder.get();
    if (!busSharedResourceProvider || !listingDate) {
      return;
    }
    const navigation = busSharedResourceProvider.getBusSharedNavigation();
    const obj = {
      from: sourceName,
      to: destinationName,
      departureDate: listingDate,
      from_code: sourceCode,
      to_code: destinationCode,
      source: 'RISxSell',
    };
    navigation.openBusListing(obj);
    return;
  }

  const originStation = {
    code: sourceCode,
  };
  const destinationStation = {
    code: destinationCode,
  };
  if (type === CROSS_SELL_TYPES.RETURN_TICKETS && journeyDate === null) {
    listingDate.setDate(listingDate.getDate() + 3);
  }

  Actions.railsListing({
    originStation,
    destinationStation,
    departureDate: listingDate,
    source: 'PNR',
  });
};

export const openTravellerPage = (
  frmStnCode: unknown,
  toStnCode: unknown,
  travelDate: unknown,
  trainNumber: unknown,
  classType: unknown,
  quota: unknown,
  frmStnName: unknown,
  toStnName: unknown,
  Pnr: unknown,
) => {
  const travellerDeepLink = RAILS_DEEPLINK.TRAVELLER.replace('{frmStnCode}', frmStnCode)
    .replace('{toStnCode}', toStnCode)
    .replace('{travelDate}', travelDate)
    .replace('{trainNumber}', trainNumber)
    .replace('{classType}', classType)
    .replace('{quota}', quota)
    .replace('{alternate}', 'true')
    .replace('{boardingStationCode}', frmStnCode)
    .replace('{destStationCode}', toStnCode)
    .replace('{frmStnName}', frmStnName)
    .replace('{toStnName}', toStnName)
    .replace('{pnr}', Pnr);
  GenericModule.openDeepLink(travellerDeepLink);
};

export const trimText = (text: string) => {
  if (!text) {
    return '';
  }
  return text.length > 18 ? `${text.substring(0, 20)}..` : text;
};

export const formatDate = (dateString: string) => {
  if (!dateString) {
    return '';
  }
  const parsedDate = fecha.parse(dateString, 'YYYYMMDD');
  if (!parsedDate) {
    return '';
  }
  return fecha.format(parsedDate, 'MMM DD, YYYY');
};

export const formatDateDDMMYYYY = (date: string) => {
  try {
    return fecha.parse(date, 'YYYYMMDD');
  } catch (e) {
    return new Date();
  }
};
