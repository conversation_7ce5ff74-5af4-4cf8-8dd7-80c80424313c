import fetch2 from '@mmt/rails/src/fetch2';
import railsConfig from '@mmt/rails/src/RailsConfig';
import { MEALS_AVAILABILITY_INVALID_PNR, SOMETHING_WENT_WRONG } from '../Constants/MealsConstants';
import { getMealsNewFlow } from '@mmt/rails/src/RailsAbConfig';

export async function validatePnr(pnr: string) {
  try {
    const response = await fetch2(railsConfig.mealsAvailability, {
      method: 'POST',
      headers: {
        orgname: 'MakeMyTrip',
      },
      body: JSON.stringify({ pnr }),
    });
    const res = await response.json();
    
    const showNewMeals = getMealsNewFlow();
    const isAvailable = showNewMeals 
      ? res?.availabilityDetails?.zomatoAvailable
      : res?.availabilityDetails?.available;
      
    if (isAvailable) {
      return { isValid: true, errorMsg: '' };
    }
    const errorMessage =
      res?.errorDetails?.code === MEALS_AVAILABILITY_INVALID_PNR
        ? res?.errorDetails?.message
        : SOMETHING_WENT_WRONG;

    return { isValid: false, errorMsg: errorMessage };
  } catch (error) {
    return { isValid: false, errorMsg: SOMETHING_WENT_WRONG };
  }
}
