import React, { useState, useEffect, useCallback, useRef } from 'react';
import { BackHandler, View, StyleSheet } from 'react-native';
import { WebView, WebViewNavigation, WebViewMessageEvent } from 'react-native-webview';
import { getDataFromStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import {
  trackClickEventProp61,
  trackPageLoad,
} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { getUserDetails } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { getDeviceDetails } from '@mmt/legacy-commons/Helpers/genericHelper';
import MealsInTrainsHeader from '@mmt/legacy-commons/Common/Components/Header/MealsInTrainsHeader';
import useConfigStore from '@mmt/rails/src/configStore/Common/RailsConfigStoreUtils';
import {
  CLICK_EVENTS,
  EVENT_ACTIONS,
  MealsInTrainsWrapperProps,
  PAGE_TRACKING,
  PAGE_TRACKING_BY_ACTION,
  URL_PAGE_TRACKING,
  URL_PATTERNS,
  UserDetails,
  PNR_NUMBER,
  MealsInTrainsQueries,
  REL_FOOD_URL,
} from '../Constants/MealsInTrainsWebViewConstants';
import { configKeys } from '../../../../configStore/Common/constants';
import { Actions } from 'apps/rails/src/navigation/railsNavigation';
import { makemytrip_url } from '../Constants/MealsConstants';

const MealsInTrainsWrapper: React.FC<MealsInTrainsWrapperProps> = ({
  url,
  headerText,
  imgSrc,
  closeWebView,
}) => {
  const [canGoBack, setCanGoBack] = useState<boolean>(false);
  const [navURL, setNavURL] = useState<string>(url);
  const [hideWebView, setHideWebView] = useState<boolean>(false);
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [_deviceContext, setDeviceContext] = useState<unknown>(null);
  const [pnrNumber, setPnrNumber] = useState<string>('');
  const mealsInTrainsQueries: MealsInTrainsQueries = useConfigStore(
    configKeys.RAILS_MEALS_IN_TRAIN_QUERY_SELECTORS,
  );

  const webViewRef = useRef<WebView>(null);
  const isNavigatingForwardRef = useRef<boolean>(true);
  const previousURLRef = useRef<string>(url);

  const handleBackButtonClick = useCallback(() => {
    if (canGoBack) {
      isNavigatingForwardRef.current = false;
      webViewRef.current?.goBack();
      return true;
    }
    if (hideWebView) {
      return false;
    }
    setHideWebView(true);
    return true;
  }, [canGoBack, hideWebView]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => backHandler.remove();
  }, [handleBackButtonClick]);

  useEffect(() => {
    async function getPnrNumber() {
      const pnrNo = await getDataFromStorage(PNR_NUMBER);
      setPnrNumber(pnrNo);
    }
    getPnrNumber();
  }, []);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetailsInfo = await getUserDetails();
        const deviceData = await getDeviceDetails();
        setUserDetails(userDetailsInfo);
        setDeviceContext(deviceData);
      } catch (error) {
        console.error('Failed to fetch user details:', error);
      }
    };
    fetchUserData();
  }, []);

  useEffect(() => {
    if (hideWebView) {
      closeWebView();
    }
  }, [hideWebView, closeWebView]);

  useEffect(() => {

    if (!isNavigatingForwardRef.current) {
      isNavigatingForwardRef.current = true;
      return;
    }

    if (navURL.endsWith(REL_FOOD_URL)) {
      trackPageLoad(PAGE_TRACKING.HOME);
      return;
    }

    const trackingPage = Object.keys(URL_PAGE_TRACKING).find(
      (pattern) => navURL.includes(pattern) || pattern === '',
    );

    if (trackingPage) {
      trackPageLoad(URL_PAGE_TRACKING[trackingPage]);
    }
  }, [navURL]);

  useEffect(() => {
    setNavURL(url);
  }, [url]);

  const onNavigationStateChange = useCallback((navState: WebViewNavigation) => {
    if (navState?.url === makemytrip_url) {
      Actions.railsBusCommonLanding({ railsBusCommonLanding: true });
      return;
    }
    previousURLRef.current = navState.url;
    setNavURL(navState.url);
    setCanGoBack(navState.canGoBack);
  }, []);

  const injectJavaScriptCode = useCallback((code: string) => {
    webViewRef.current?.injectJavaScript(code);
  }, []);

  const onLoadEnd = useCallback(() => {
    injectJavaScriptCode(`
      (function() {
        var pnrElement = document.getElementById('${mealsInTrainsQueries.pnr}');
        if (pnrElement) {
          pnrElement.value = '${pnrNumber}';
        }
      })();
    `);

    if (navURL.includes(URL_PATTERNS.CHECKOUT)) {
      injectJavaScriptCode(`
        (function() {
          var passengerElement = document.getElementsByName('${
            mealsInTrainsQueries.passengerName
          }')[0];
          if (passengerElement) {
            passengerElement.value = '${userDetails?.firstName ?? ''} ${
        userDetails?.lastName ?? ''
      }';
          const inputEvent = new Event('input', { bubbles: true, cancelable: true });
          passengerElement.dispatchEvent(inputEvent);
          }

          var mobileElement = document.getElementsByName('${mealsInTrainsQueries.mobileNumber}')[0];
          if (mobileElement) {
          mobileElement.value = '${userDetails?.mobile?.mobileNumber ?? ''}';
          const inputEvent = new Event('input', { bubbles: true, cancelable: true });
          mobileElement.dispatchEvent(inputEvent);
          }

          var emailElement = document.getElementsByName('${mealsInTrainsQueries.email}')[0];
          if (emailElement) {
          emailElement.value = '${userDetails?.email ?? ''}';
          const inputEvent = new Event('input', { bubbles: true, cancelable: true });
          emailElement.dispatchEvent(inputEvent);
          }
        })();
      `);
    }

    injectJavaScriptCode(`
      (function() {
        var searchInput = document.querySelector('${mealsInTrainsQueries.searchInput}');
        if (searchInput) {
          searchInput.addEventListener('click', function() {
             window.ReactNativeWebView.postMessage('${CLICK_EVENTS.SEARCH_INPUT_CLICKED}');
          });
        }

        var orderNowElements = document.querySelectorAll('${mealsInTrainsQueries.orderNow}');
        if (orderNowElements.length > 0) {
          orderNowElements.forEach(function (orderNow) {
            orderNow.addEventListener('click', function () {
              window.ReactNativeWebView.postMessage('${CLICK_EVENTS.ORDER_NOW_CLICKED}');
            });
          });
        }
  
        var addButtons = document.querySelectorAll("${mealsInTrainsQueries.addButton}");
        if (addButtons.length > 0) {
          addButtons.forEach(function (addButton) {
            addButton.addEventListener("click", function () {
              window.ReactNativeWebView.postMessage("${CLICK_EVENTS.ADD_FIRST_CLICK}");
            });
          });
        }

        var proceedToNextButton = document.querySelector('${mealsInTrainsQueries.proceedToNextButton}');
        if (proceedToNextButton) {
          proceedToNextButton.addEventListener('click', function() {
           window.ReactNativeWebView.postMessage('${CLICK_EVENTS.PROCEED_TO_NEXT_CTA_CLICK}');
          });
        }

        var nextButton = document.querySelector('${mealsInTrainsQueries.nextButton}');
        if (nextButton) {
          nextButton.addEventListener('click', function() {
            window.ReactNativeWebView.postMessage('${CLICK_EVENTS.NEXT_CTA_CLICK}');
          });
        }

        var chkIsveg = document.getElementById('${mealsInTrainsQueries.chkIsVeg}');
        if (chkIsveg) {
          chkIsveg.addEventListener('click', function() {
          window.ReactNativeWebView.postMessage('${CLICK_EVENTS.CHK_IS_VEG_CLICKED}');
          });
        }


      })();
    `);
  }, [navURL, injectJavaScriptCode, mealsInTrainsQueries, pnrNumber, userDetails]);

  const onMessage = useCallback((event: WebViewMessageEvent) => {
    const message = event.nativeEvent.data;
    const eventAction = EVENT_ACTIONS[message];

    if (eventAction) {
      const pageTracking = PAGE_TRACKING_BY_ACTION[message] || PAGE_TRACKING.HOME;
      trackClickEventProp61(pageTracking, eventAction);
    }
  }, []);

  const onError = useCallback((syntheticEvent: WebViewMessageEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.error('WebView error: ', nativeEvent);
  }, []);

  return (
    <View style={styles.container}>
      <MealsInTrainsHeader
        headerText={headerText}
        imgSrc={imgSrc}
        backPressHandler={handleBackButtonClick}
      />
      <WebView
        ref={webViewRef}
        source={{ uri: url }}
        onNavigationStateChange={onNavigationStateChange}
        onLoadEnd={onLoadEnd}
        onMessage={onMessage}
        onError={onError}
        startInLoadingState
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default MealsInTrainsWrapper;
