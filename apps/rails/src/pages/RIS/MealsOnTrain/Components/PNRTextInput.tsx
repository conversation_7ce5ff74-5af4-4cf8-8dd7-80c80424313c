import { colors } from 'packages/legacy-commons/Styles/globalStyles';
import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  Easing,
  FlatList,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  BEGIN_ORDERING_YOUR_FOOD,
  ENTER_YOUR_TEN_DIGIT_PNR,
  PNR_IS_REQUIRED_TO_DELIVER_IN_THIS_TRAIN,
  PNR_NUMBER,
  TAP_TO_ADD_PNR,
  UPCOMING_JOURNEYS,
} from '../Constants/MealsConstants';
import { getMealsNewFlow } from '@mmt/rails/src/RailsAbConfig';

interface PNRTexInputProps {
  upComingPnrs: unknown;
  pnrNumber: unknown;
  setPnrNumber: (pnr: string) => void;
  isValidPnr: boolean;
  errorMsg: string;
}
const PNRTextInput = (props: PNRTexInputProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const animatedIsFocused = useRef(new Animated.Value(0)).current;
  const { upComingPnrs, pnrNumber, setPnrNumber, isValidPnr, errorMsg } = props;
  useEffect(() => {
    Animated.timing(animatedIsFocused, {
      toValue: isFocused || pnrNumber ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, pnrNumber, animatedIsFocused]);

  const handleInputChange = (text: string) => {
    const numericText = text.replace(/[^0-9]/g, '');
    setPnrNumber(numericText);
  };
  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };
  const handleItemPress = (pnr: string) => {
    setPnrNumber(pnr);
  };

  const isPnrLengthTen = pnrNumber.length === 10;
  const backgroundColor = isPnrLengthTen
    ? isValidPnr
      ? colors.transparent
      : colors.lightRed5
    : colors.transparent;
  const borderColor = isPnrLengthTen ? (isValidPnr ? colors.grey : colors.red) : colors.primary;
  const color = isPnrLengthTen ? colors.black : colors.primary;

  const labelStyle = {
    position: 'absolute',
    left: 9,
    fontWeight: '700',
    top: animatedIsFocused.interpolate({
      inputRange: [0, 1],
      outputRange: [48, 35],
      easing: Easing.ease,
    }),
    fontSize: animatedIsFocused.interpolate({
      inputRange: [0, 1],
      outputRange: [14, 12],
    }),
    color: borderColor,
    backgroundColor: colors.white,
    paddingHorizontal: 4,
  };
  const inputStyle = {
    ...styles.textInput,
    backgroundColor,
    borderColor,
    color,
    paddingTop: 16,
  };

  const showNewMeals = getMealsNewFlow();
  const pnrHeaderText = showNewMeals ? BEGIN_ORDERING_YOUR_FOOD : PNR_NUMBER;

  return (
    <View style={styles.container} testID="meal_bottomsheet_container">
      <Text style={styles.PNRNumber} testID="meal_bottomsheet_text">
        {pnrHeaderText}
      </Text>
      <Animated.Text style={labelStyle}>{ENTER_YOUR_TEN_DIGIT_PNR}</Animated.Text>
      <TextInput
        style={inputStyle}
        testID="meal_bottomsheet_text_input"
        placeholder=""
        keyboardType="numeric"
        maxLength={10}
        onChangeText={handleInputChange}
        value={pnrNumber}
        onFocus={handleFocus}
        onBlur={handleBlur}
      />
      <Text style={[styles.textStyle, errorMsg && { color: colors.red }]}>
        {errorMsg || PNR_IS_REQUIRED_TO_DELIVER_IN_THIS_TRAIN}
      </Text>
      {upComingPnrs?.suggestedBookings?.length !== 0 && (
        <View style={styles.upcoming}>
          <View style={styles.upcomingJourneysContainer}>
            <Text style={styles.upComingJourney}>{UPCOMING_JOURNEYS}</Text>
            <Text style={styles.tapToAddPnr}>{TAP_TO_ADD_PNR}</Text>
          </View>
          <LinearGradient
            colors={['rgba(0, 0, 0, 0.05)', 'transparent']}
            style={styles.shadow}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <View style={{ height: 50, width: 12 }} />
          </LinearGradient>
          <FlatList
            style={styles.flatList}
            data={upComingPnrs?.suggestedBookings}
            keyExtractor={(item) => item.pnrNumber}
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <TouchableOpacity onPress={() => handleItemPress(item.pnrNumber)}>
                <View
                  style={[
                    styles.itemContainer,
                    !isFocused && pnrNumber === item.pnrNumber && { borderColor: colors.primary },
                  ]}
                >
                  <View style={styles.stationAndDate}>
                    <Text style={styles.itemText}>{item.fromCity}</Text>
                    <Text style={styles.itemText}>to</Text>
                    <Text style={styles.itemText}>{item.toCity}</Text>
                    <Text style={styles.journeyDate}>{item.journeyDate}</Text>
                  </View>
                  <Text
                    style={[
                      styles.upComingJourney,
                      !isFocused && pnrNumber === item.pnrNumber && { color: colors.primary },
                    ]}
                  >
                    {item.pnrNumber}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 12,
    flex: 1,
  },
  PNRNumber: {
    fontFamily: 'Lato',
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 19.2,
    textAlign: 'left',
    color: colors.black,
    marginBottom: 14,
  },
  shadow: {
    marginLeft: 12,
    marginTop: 10,
  },
  textStyle: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.4,
    textAlign: 'left',
    color: colors.greyText1,
    marginTop: 4,
  },
  textInput: {
    fontFamily: 'Lato',
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 16.8,
    textAlign: 'left',
    color: colors.primary,
    height: 48,
    borderColor: colors.primary,
    borderWidth: 1,
    borderRadius: 10,
    paddingLeft: 12,
    paddingBottom: 0,
  },
  itemContainer: {
    padding: 9,
    borderColor: colors.lightSilver,
    borderWidth: 1,
    borderRadius: 10,
    marginLeft: 12,
  },
  itemText: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '400',
    padding: 0,
    margin: 0,
    lineHeight: 14.4,
    marginRight: 4,
  },
  journeyDate: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '400',
    padding: 0,
    margin: 0,
    lineHeight: 14.4,
    marginLeft: 4,
  },
  upcomingJourneysContainer: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 14.4,
    textAlign: 'left',
    backgroundColor: colors.white,
  },
  upcoming: {
    marginTop: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  stationAndDate: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 0,
  },
  flatList: {
    marginTop: 10,
    marginLeft: 0,
    flexGrow: 0,
  },
  upComingJourney: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '900',
    lineHeight: 14.4,
    color: colors.textGrey,
    marginTop: 5,
  },
  tapToAddPnr: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.4,
    color: colors.greyText1,
    marginTop: 5,
  },
});

export default PNRTextInput;
