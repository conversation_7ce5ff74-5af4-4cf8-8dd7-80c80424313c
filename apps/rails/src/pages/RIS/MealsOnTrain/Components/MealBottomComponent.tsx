import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { Image, Platform, StyleSheet, Text, View } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {
  INTERACTED_WITH_MEALS_LTS,
  NOW_ENJOY_FOOD_FROM_FAVOURITE_RESTAURANTS,
  ORDER_MEALS,
} from '../Constants/MealsConstants';
import { Actions } from '@mmt/rails/src/navigation/railsNavigation';
import { FOOD_ICON } from '@mmt/rails/src/Utils/RailsConstant';
import { setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { showMealsWebView } from '@mmt/rails/src/RailsAbConfig';
import crossIcon from '@mmt/legacy-assets/src/cross_tg.webp';

interface Props {
  closeMealsComponent: () => void;
  onOrderMealClicked: () => void;
}

const MealBottomComponent = ({ closeMealsComponent, onOrderMealClicked }: Props) => {
  const orderNowClicked = () => {
    setDataInStorage(INTERACTED_WITH_MEALS_LTS, true);

    const mealsWebView = showMealsWebView();
    if (mealsWebView) {
      Actions.openMealsFunnelWebView({
        pnr: '',
      });
    } else {
      Actions.openMealsPage();
    }

    onOrderMealClicked();
  };

  return (
    <>
      <View style={styles.container}>
        <Image style={styles.foodIcon} source={{ uri: FOOD_ICON }} />
        <Text style={[styles.descriptionStyle, fontStyle('regular')]}>
          {NOW_ENJOY_FOOD_FROM_FAVOURITE_RESTAURANTS}
        </Text>
        <TouchableRipple onPress={orderNowClicked}>
          <LinearGradient
            colors={[colors.lightBlue, colors.darkBlue]}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
            style={styles.button}
          >
            <Text style={[styles.label, fontStyle('bold')]}>{ORDER_MEALS}</Text>
          </LinearGradient>
        </TouchableRipple>

        <TouchableRipple onPress={closeMealsComponent}>
          <Image source={crossIcon} style={styles.crossIcon} />
        </TouchableRipple>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    paddingHorizontal: 0,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
    justifyContent: 'center',
    left: 0,
    right: 0,
    borderColor: colors.greyBookedSeat,
    marginBottom: -5,
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  foodIcon: {
    flex: 1,
    width: 24,
    height: 24,
    marginLeft: 16,
    resizeMode: 'contain',
  },
  descriptionStyle: {
    flex: 6,
    fontSize: 12,
    color: colors.black,
    padding: 16,
  },
  orderNowButtonContainer: {
    flex: 3,
    borderRadius: 8,
    padding: 1,
    width: 99,
    height: 32,
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    marginLeft: 8,
    marginBottom: 8,
  },
  orderNowButtonText: {
    fontFamily: 'Lato',
    fontSize: 12,
    lineHeight: 14.4,
    color: colors.white,
    textAlign: 'center',
  },
  crossIcon: {
    marginLeft: 7,
    width: 16,
    height: 16,
    marginRight: 16,
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 9,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  label: {
    color: colors.white,
    fontSize: 12,
  },
  black: {
    fontWeight: '900',
  },
});

export default MealBottomComponent;
