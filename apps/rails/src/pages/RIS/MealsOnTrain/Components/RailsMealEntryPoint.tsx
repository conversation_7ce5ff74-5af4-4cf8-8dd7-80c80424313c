import React, { useEffect, useState } from 'react';
import {
  Image,
  Text,
  View,
  StyleSheet,
  Platform,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import LottieView from 'lottie-react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import {
  getMealsNewFlow,
  showMealsStaticData,
  showMealsWebView,
} from '@mmt/rails/src/RailsAbConfig';
import {
  ENJOY_YOUR_FAVORITE,
  IN_TRAIN_DELIVERED_AT_YOUR_SEAT,
  ORDER_NOW,
  INTERACTED_WITH_MEALS_PNR,
  MEALS_IN_TRAINS,
} from '../Constants/MealsConstants';
import { Actions } from 'apps/rails/src/navigation/railsNavigation';
import { setDataInStorage } from 'packages/legacy-commons/AppState/LocalStorage';
import { PNR_NUMBER } from '../Constants/MealsInTrainsWebViewConstants';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { RELFOOD_URL, MEALS_STATIONS_URL } from 'apps/rails/src/Utils/RailsConstant';

interface RailsMealEntryPointProps {
  onBookNowClicked: () => void;
  pnrNumber?: string;
}

const RailsMealEntryPoint = ({ onBookNowClicked, pnrNumber }: RailsMealEntryPointProps) => {
  const configVal = useConfigStore(configKeys.RAILS_MEAL_CONFIG);
  const [animationData, setAnimationData] = useState(null);
  const [mealsDataPokus, setMealsDataPokus] = useState<boolean>(false);

  useEffect(() => {
    async function mealsStaticData() {
      const val = await showMealsStaticData();
      setMealsDataPokus(val);
    }
    mealsStaticData();
    const fetchAnimation = async () => {
      try {
        const response = await fetch(
          'https://go-assets.ibcdn.com/u/MMT/json/1727958230349-FoodLottie.json',
        );
        const data = await response.json();
        setAnimationData(data);
      } catch (error) {
        console.error('Error fetching animation data:', error);
      }
    };
    !mealsDataPokus && fetchAnimation();
  }, [mealsDataPokus]);

  const bookNow = () => {
    const mealsWebView = showMealsWebView();
    if (mealsWebView) {
      Actions.openMealsFunnelWebView({
        pnr: '',
      });
    } else {
      Actions.openMealsPage();
    }
  };

  const onPnrOrderMealClicked = () => {
    setDataInStorage(PNR_NUMBER, pnrNumber);
    setDataInStorage(INTERACTED_WITH_MEALS_PNR, true);
    const showNewMeals = getMealsNewFlow();
    const mealsWebView = showMealsWebView();

    if (mealsWebView || showNewMeals) {
      Actions.openMealsFunnelWebView({
        pnr: pnrNumber,
        url: `${MEALS_STATIONS_URL}?pnr=${pnrNumber}`,
      });
    } else {
      Actions.openMealsInTrainsWebView({
        headerText: MEALS_IN_TRAINS,
        url: `${(configVal as unknown)?.relFoodUrlPnrPage ?? RELFOOD_URL}${pnrNumber}`,
        imgSrc: require('@mmt/legacy-assets/src/white_backarrow.webp'),
      });
    }
  };

  const handleBookNowClick = () => {
    onBookNowClicked && onBookNowClicked();
    if (pnrNumber) {
      onPnrOrderMealClicked();
      return;
    }
    bookNow();
  };

  return (
    <TouchableOpacity
      onPress={handleBookNowClick}
      style={!mealsDataPokus && styles.food}
      testID="meals_on_trains_entry_point"
    >
      {mealsDataPokus ? (
        <FastImage
          source={ASSETS.railsFoodOnTrainsBanner}
          style={styles.foodLottieImage}
          resizeMode="contain"
          testID="meals_on_trains_entry_point_fast_image"
        />
      ) : (
        <>
          <View
            style={styles.backgroundStack}
            testID="meals_on_trains_entry_point_background_stack"
          >
            <Image
              source={ASSETS.foodVector}
              style={styles.backgroundLayer}
              testID="meals_on_trains_entry_point_background_stack_image"
            />
            <Image
              source={ASSETS.mealsVector}
              style={styles.backgroundLayer}
              testID="meals_on_trains_entry_point_background_stack_image_two"
            />
          </View>

          <View
            style={styles.contentContainer}
            testID="meals_on_trains_entry_point_content_container"
          >
            <View
              style={styles.foodRow}
              testID="meals_on_trains_entry_point_content_container_food_row"
            >
              <FastImage
                source={ASSETS.foodInTrain}
                style={styles.mealsOnTrainsIcon}
                resizeMode={FastImage.resizeMode.contain}
                testID="meals_on_trains_entry_point_content_container_food_row_fast_image"
              />
              <FastImage
                source={ASSETS.railsNewTag}
                style={styles.newIcon}
                resizeMode={FastImage.resizeMode.contain}
                testID="meals_on_trains_entry_point_content_container_food_row_fast_image_two"
              />
            </View>
            <View
              style={styles.enjoyFavoriteFood}
              testID="meals_on_trains_entry_point_content_container_enjoy_favorite_food"
            >
              <Text
                style={[styles.enjoyYourFavorite, fontStyle('bold')]}
                testID="meals_on_trains_entry_point_content_container_enjoy_favorite_food_text"
              >
                {ENJOY_YOUR_FAVORITE}
              </Text>
            </View>
            <View>
              <Text
                style={[styles.inTrainDeliveredAtYourSeat, fontStyle('regular')]}
                testID="meals_on_trains_entry_point_content_container_text"
              >
                {IN_TRAIN_DELIVERED_AT_YOUR_SEAT}
              </Text>
              <View
                style={styles.orderNowButtonContainer}
                testID="meals_on_trains_entry_point_content_container_order_now_button_container"
              >
                <Text
                  style={[styles.orderNowButtonText, fontStyle('black')]}
                  testID="meals_on_trains_entry_point_content_container_text_two"
                >
                  {ORDER_NOW}
                </Text>
              </View>
            </View>
          </View>
          {animationData && (
            <View style={styles.lottieWrapper} testID="meals_on_trains_lottie_wrapper">
              <LottieView
                style={styles.foodLottie}
                source={animationData}
                autoPlay={true}
                loop={true}
                testID="meals_on_trains_entry_point_lottie_wrapper_lottie_view"
              />
            </View>
          )}
        </>
      )}
    </TouchableOpacity>
  );
};
const { width: screenWidth } = Dimensions.get('window');
const dimension = screenWidth * 1.02;

const styles = StyleSheet.create({
  food: {
    alignSelf: 'stretch',
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 10,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.lightGray,
    overflow: 'hidden',
    zIndex: 1,
  },
  backgroundStack: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  backgroundLayer: {
    ...StyleSheet.absoluteFillObject,
    position: 'absolute',
    borderRadius: 16,
  },
  lottieWrapper: {
    position: 'absolute',
    zIndex: 30,
    width: '100%',
    height: '100%',
  },
  foodLottie: {
    position: 'absolute',
    left: Platform.select({
      ios: 0.149 * screenWidth,
      android: 0.24 * screenWidth,
    }),
    bottom: Platform.select({
      ios: -0.145 * screenWidth,
      android: -0.21 * screenWidth,
    }),
    width: dimension,
    height: dimension,
  },
  foodRow: {
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 4,
  },
  enjoyFavoriteFood: {
    flexDirection: 'row',
    marginTop: 8,
  },
  enjoyYourFavorite: {
    fontSize: 16,
    color: colors.defaultTextColor,
  },
  inTrainDeliveredAtYourSeat: {
    fontSize: 12,
    color: colors.defaultTextColor,
    zIndex: 4,
  },
  orderNowButtonContainer: {
    borderRadius: 8,
    width: 104,
    height: 26,
    borderWidth: 1,
    borderColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
    zIndex: -1,
    marginBottom: 12,
  },
  orderNowButtonText: {
    fontSize: 12,
    color: colors.primary,
    textAlign: 'center',
  },
  newIcon: {
    alignSelf: 'center',
    height: 11,
    width: 25,
    marginLeft: 4,
    marginTop: 0,
  },
  mealsOnTrainsIcon: {
    marginTop: 15,
    height: 28,
    width: 53,
    resizeMode: 'contain',
  },
  contentContainer: {
    zIndex: 2,
    width: '100%',
    paddingLeft: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  foodLottieImage: {
    height: 190,
    marginBottom: 16,
    marginHorizontal: 16,
  },
});

export default RailsMealEntryPoint;
