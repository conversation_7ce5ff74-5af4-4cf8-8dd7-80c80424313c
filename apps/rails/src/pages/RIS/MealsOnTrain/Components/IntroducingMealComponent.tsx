import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import {
  FOOD_ON_TRAIN,
  GET_FOOD_DELIVERED_AT_YOUR_SEAT,
  INTRODUCING,
  MEALS,
  ORDER_MEALS_AND_HAVE_YOUR_FOOD_DELIVERED_AT_YOUR_SEAT,
} from '../Constants/MealsConstants';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import { colors } from 'packages/legacy-commons/Styles/globalStyles';
import { FOOD_ICON } from 'apps/rails/src/Utils/RailsConstant';
import { getMealsNewFlow } from '@mmt/rails/src/RailsAbConfig';
const IntroducingMealComponent = () => {
  const showNewMeals = getMealsNewFlow();
  const title = !showNewMeals ? MEALS : FOOD_ON_TRAIN;
  const orderText = !showNewMeals ? ORDER_MEALS_AND_HAVE_YOUR_FOOD_DELIVERED_AT_YOUR_SEAT :
   GET_FOOD_DELIVERED_AT_YOUR_SEAT;

  return (
    <View style={styles.container} testID="meal_bottomsheet_container">
      <Image style={styles.foodIcon} source={{ uri: FOOD_ICON }} testID="meal_bottomsheet_image" />
      <Text style={[styles.introducingText, fontStyle('regular')]} testID="meal_bottomsheet_text">
        {INTRODUCING}
      </Text>
      <Text style={[styles.mealsText, fontStyle('bold')]} testID="meal_bottomsheet_text_two">
        {title}
      </Text>
      <Text style={[styles.orderText, fontStyle('regular')]} testID="meal_bottomsheet_text_three">
        {orderText}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 0,
    paddingBottom: 20,
  },
  foodIcon: {
    height: 80,
    width: 80,
    marginTop: 20,
  },
  introducingText: {
    color: colors.midnightBlue,
    fontSize: 12,
    lineHeight: 14.4,
    letterSpacing: 2,
    textAlign: 'center',
    marginTop: 16,
  },
  mealsText: {
    fontSize: 18,
    lineHeight: 28.29,
    textAlign: 'left',
    color: colors.black,
    marginTop: 8,
  },
  orderText: {
    fontSize: 14,
    lineHeight: 16.8,
    textAlign: 'center',
    color: colors.black,
    marginTop: 12,
  },
});
export default IntroducingMealComponent;
