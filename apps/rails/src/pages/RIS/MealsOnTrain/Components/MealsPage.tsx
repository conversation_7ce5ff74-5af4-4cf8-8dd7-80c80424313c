import React, { useEffect, useState } from 'react';
import {
  Image,
  Keyboard,
  Text,
  View,
  Dimensions,
  ScrollView,
  StyleSheet,
  Platform,
} from 'react-native';
import IntroducingMealComponent from './IntroducingMealComponent';
import { colors } from 'packages/legacy-commons/Styles/globalStyles';
import PNRTextInput from './PNRTextInput';
import { connect } from 'react-redux';
import WhyBookWithUs from './WhyBookWithUs';
import useConfigStore from '../../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../../configStore/Common/constants';
import {
  MEALS_AVAILABILITY_INVALID_PNR,
  MEALS_IN_TRAINS,
  MEALS_UNAVAILBILITY_MESSAGE,
  OK_GOT_IT,
  ORDER_NOW,
  SOMETHING_WENT_WRONG,
  WOOPS,
} from '../Constants/MealsConstants';
import backIcon from 'packages/legacy-assets/src/back_icon.webp';
import { Actions } from 'apps/rails/src/navigation/railsNavigation';
import { setDataInStorage } from 'packages/legacy-commons/AppState/LocalStorage';
import { PNR_NUMBER } from '../Constants/MealsInTrainsWebViewConstants';
import { trackClickEventProp61 } from 'packages/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  LTS_DETAILS_MEALS_ORDER_NOW_BS_CLICKED,
  RELFOOD_URL,
  MEALS_STATIONS_URL,
} from 'apps/rails/src/Utils/RailsConstant';
import { KeyboardAvoidingViewWrapper } from '../../../CreateNewAccount/CreateNewAccount';

import fetch2 from '@mmt/rails/src/fetch2';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import { getMealsNewFlow, showMealsWebView } from '@mmt/rails/src/RailsAbConfig';
import railsConfig from '@mmt/rails/src/RailsConfig';
import BottomSheetModalTrain from '../../../Common/BottomSheetModalTrain';

interface MealsPageProps {
  upComingPnrs: unknown;
  navigation?: unknown;
}
const { width: screenWidth } = Dimensions.get('window');
const MealsPage = (props: MealsPageProps) => {
  const { upComingPnrs } = props;
  const configVal = useConfigStore(configKeys.RAILS_MEAL_CONFIG);
  const [pnrNumber, setPnrNumber] = useState('');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [isValidPnr, setIsValidPnr] = useState(true);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [orderClicked, setOrderClicked] = useState(false);
  const [showModal, setBottomModal] = useState(false);
  const [unavailabilityMessage, setunavailabilityMessage] = useState('');

  const validatePnr = async (pnr: string) => {
    setLoading(true);
    const showNewMeals = getMealsNewFlow();
    const mealsWebView = showMealsWebView();
    if (!showNewMeals && !mealsWebView) {
      try {
        setIsValidPnr(true);
        return true;
      } finally {
        setLoading(false);
      }
    }
    try {
      const response: unknown = await fetch2(railsConfig.mealsAvailability, {
        method: 'POST',
        headers: {
          'orgname': 'MakeMyTrip',
        },
        body: JSON.stringify({ pnr }),
      });
      const res = await response.json();
      const isAvailable = showNewMeals
        ? res?.availabilityDetails?.zomatoAvailable
        : res?.availabilityDetails?.available;

      if (isAvailable) {
        setIsValidPnr(true);
        return true;
      }

      if (res?.errorDetails?.code === MEALS_AVAILABILITY_INVALID_PNR) {
        const errorMessage = res?.errorDetails?.message || SOMETHING_WENT_WRONG;
        setErrorMsg(errorMessage);
        setIsValidPnr(false);
        return false;
      } else {
        const unavailabilityMessage = res?.availabilityDetails?.unavailabilityMessage || MEALS_UNAVAILBILITY_MESSAGE;
        setunavailabilityMessage(unavailabilityMessage);
        setBottomModal(true);
        setPnrNumber('');
        setIsValidPnr(false);
        return false;
      }
    } catch (error) {
      console.error('Error validating PNR:', error);
      setIsValidPnr(false);
      setErrorMsg(SOMETHING_WENT_WRONG);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handlePnrChange = (pnr: string) => {
    setPnrNumber(pnr);
    if (orderClicked) {
      setIsValidPnr(true);
      setErrorMsg('');
      setOrderClicked(false);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const openMealsInTrainsWebView = (pnrNumber: string) => {
    trackClickEvent();
    setDataInStorage(PNR_NUMBER, pnrNumber);

    const showNewMeals = getMealsNewFlow();

    if (!showNewMeals) {
      Actions.openMealsInTrainsWebView({
        headerText: MEALS_IN_TRAINS,
        url: `${(configVal as unknown)?.relFoodUrlPnrPage ?? RELFOOD_URL}${pnrNumber}`,
        imgSrc: require('@mmt/legacy-assets/src/white_backarrow.webp'),
      });
      return;
    } else {
      Actions.openMealsFunnelWebView({
        pnr: pnrNumber,
      });
      return;
    }
  };

  const onOrderMealClicked = async () => {
    setOrderClicked(true);
    const isValid = await validatePnr(pnrNumber);
    if (!isValid) {
      return;
    }
    const showNewMeals = getMealsNewFlow();
    if (showNewMeals) {
      Actions.openMealsFunnelWebView({
        pnr: pnrNumber,
        url: `${MEALS_STATIONS_URL}?pnr=${pnrNumber}`,
      });
    } else {
      Actions.openMealsInTrainsWebView({
        headerText: MEALS_IN_TRAINS,
        url: `${(configVal as unknown)?.relFoodUrlPnrPage ?? RELFOOD_URL}${pnrNumber}`,
        imgSrc: require('@mmt/legacy-assets/src/white_backarrow.webp'),
      });
    }
  };

  const closeBottomSheetModal = () => {
    setBottomModal(false);
  };

  const trackClickEvent = () => {
    trackClickEventProp61('meals_page', LTS_DETAILS_MEALS_ORDER_NOW_BS_CLICKED);
  };
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const onBackIconClicked = () => {
    Actions.pop();
  };

  const renderBottomSheetContent = () => (
    <View style={styles.bottomSheetContent} testID="meals_page_bottomsheet_container">
      <TouchableRipple
        onPress={closeBottomSheetModal}
        style={styles.crossIconContainer}
        testID="meals_page_bottomsheet_close_button"
      >
        <Image style={styles.crossIcon} source={ASSETS.crossIcon} />
      </TouchableRipple>
      <Image source={ASSETS.fallbackUIImage} style={styles.image} />
      <Text style={styles.woopsText}>{WOOPS}</Text>
      <Text style={styles.errorMsg}>{unavailabilityMessage}</Text>
      <TouchableRipple onPress={closeBottomSheetModal} testID="meals_page_bottomsheet_ok_button">
        <Text style={styles.okButtonText}>{OK_GOT_IT}</Text>
      </TouchableRipple>
    </View>
  );

  return (
    <KeyboardAvoidingViewWrapper>
      <View style={styles.container}>
        <View style={styles.backIconContainer}>
          <TouchableRipple onPress={onBackIconClicked}>
            <Image style={styles.backIcon} source={backIcon} />
          </TouchableRipple>
        </View>
        <ScrollView
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
          nestedScrollEnabled={true}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={16}
        >
          <View style={styles.innerContainer}>
            <IntroducingMealComponent />
            <PNRTextInput
              upComingPnrs={upComingPnrs}
              pnrNumber={pnrNumber}
              setPnrNumber={handlePnrChange}
              isValidPnr={isValidPnr}
              errorMsg={errorMsg}
            />
            <View style={styles.whyBookWithUsContainer}>
              <WhyBookWithUs />
            </View>
          </View>
        </ScrollView>
        <View
          style={[
            styles.orderButtonWhiteContainer,
            keyboardVisible && styles.orderButtonWithKeyboard,
          ]}
        >
          <TouchableRipple
            style={styles.touchableRippleContainer}
            onPress={onOrderMealClicked}
            disabled={pnrNumber.length !== 10}
          >
            <View
              style={[
                styles.orderNowButtonContainer,
                pnrNumber.length !== 10 && styles.disabledButton,
              ]}
            >
              <Text
                style={[
                  styles.orderNowButtonText,
                  { color: loading ? colors.transparent : colors.white },
                ]}
              >
                {ORDER_NOW}
              </Text>
              {loading && (
                <View style={styles.spinnerContainer}>
                  <Spinner size={30} color={colors.white} />
                </View>
              )}
            </View>
          </TouchableRipple>
        </View>
      </View>
      {showModal && (
        <BottomSheetModalTrain
          onTouchOutside={closeBottomSheetModal}
          additionalContainerStyle={styles.bottomSheetContainer}
          testID="meals_page_bottomsheet_modal"
        >
          {renderBottomSheetContent()}
        </BottomSheetModalTrain>
      )}
    </KeyboardAvoidingViewWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  touchableRippleContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIconContainer: {
    position: 'absolute',
    top: 24,
    left: 5,
    zIndex: 1,
  },
  innerContainer: {
    flex: 1,
  },
  whyBookWithUsContainer: {
    paddingHorizontal: 1,
  },
  scrollContent: {
    flex: 1,
    paddingTop: 40,
  },
  scrollContentContainer: {
    paddingBottom: 40,
  },
  orderButtonWithKeyboard: {
  },
  orderButtonWhiteContainer: {
    backgroundColor: colors.white,
    paddingTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: -16,
    marginRight: -16,
    marginBottom: Platform.select({ ios: 0, android: 0 }),
    paddingBottom: Platform.select({ android: 20 }),
    width: screenWidth,
    borderColor: colors.greyText1,
    borderTopWidth: 0.5,
  },
  orderNowButtonContainer: {
    borderRadius: 8,
    height: 44,
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    width: '90%',
  },
  orderNowButtonText: {
    fontFamily: 'Lato',
    fontSize: 16,
    lineHeight: 19.2,
    color: colors.white,
    textAlign: 'center',
    fontWeight: '900',
  },
  spinnerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    zIndex: 50,
  },
  backIcon: {
    height: 20,
    width: 20,
  },
  disabledButton: {
    backgroundColor: colors.lightTextColor,
    borderColor: colors.lightTextColor,
  },
  bottomSheetContainer: {
    zIndex: 9999,
  },
  bottomSheetContent: {
    padding: 16,
    backgroundColor: colors.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    alignItems: 'center',
  },
  crossIconContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  crossIcon: {
    width: 24,
    height: 24,
  },
  image: {
    width: 135,
    height: 150,
    marginBottom: 16,
  },
  woopsText: {
    fontFamily: 'Lato',
    fontWeight: '900',
    fontSize: 20,
    lineHeight: 20,
    letterSpacing: 0,
    textAlign: 'center',
    marginBottom: 16,
  },
  errorMsg: {
    fontFamily: 'Lato',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0,
    textAlign: 'center',
    marginBottom: 50,
    color: colors.defaultTextColor,
  },
  okButtonText: {
    fontFamily: 'Lato',
    fontWeight: '700',
    fontSize: 18,
    lineHeight: 21.6,
    letterSpacing: 0,
    textAlign: 'center',
    color: colors.primary,
  },
});

const mapStateToProps = (state) => {
  return {
    upComingPnrs: state?.railInfoReducer?.upComingPnrs,
  };
};
export default connect(mapStateToProps, null)(MealsPage);
