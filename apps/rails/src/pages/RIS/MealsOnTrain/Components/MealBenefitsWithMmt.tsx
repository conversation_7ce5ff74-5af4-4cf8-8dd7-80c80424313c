import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import React from 'react';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { Image, Text, View } from 'react-native';

interface MealBenefitsWithMmtProps {
  title: string;
  description: string;
  icon: { uri: string };
  top: number;
  isLast?: boolean;
}

const MealBenefitsWithMmt = (props: MealBenefitsWithMmtProps) => {
  const { top, isLast } = props;

  const getContainerStyle = () => {
    if (top === 0) {
      return [styles.row, styles.rowTop, !isLast && styles.rowWithDivider];
    } else if (isLast) {
      return [styles.row, styles.rowBottom];
    }
    return [styles.row, styles.rowWithDivider];
  };

  return (
    <View style={getContainerStyle()} testID="meal_bottomsheet_container">
      <Image style={styles.icon} source={props.icon} testID="meal_bottomsheet_image" />
      <View style={styles.details} testID="meal_bottomsheet_details">
        <Text style={[styles.title, fontStyle('semiBold')]} testID="meal_bottomsheet_text">
          {props.title}
        </Text>
        <Text style={[styles.description, fontStyle('regular')]} testID="meal_bottomsheet_text_two">
          {props.description}
        </Text>
      </View>
    </View>
  );
};

const styles = {
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingVertical: 14,
    paddingHorizontal: 0,
    minHeight: 60,
  },
  rowTop: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  rowBottom: {
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  rowWithDivider: {
    borderBottomWidth: 1,
    borderBottomColor: colors.greyLight2,
  },
  icon: {
    height: 32,
    width: 32,
    marginLeft: 20,
    marginRight: 10,
  },
  details: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 16,
    color: colors.black7,
    marginBottom: 2,
  },
  description: {
    fontSize: 14,
    lineHeight: 18,
    color: '#757575',
  },
};

export default MealBenefitsWithMmt;
