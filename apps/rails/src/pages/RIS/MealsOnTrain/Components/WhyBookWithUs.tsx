import React from 'react';
import { Text, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import MealBenefitsWithMmt from './MealBenefitsWithMmt';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { WHY_BOOK_WITH_US } from '../Constants/MealsConstants';

const WhyBookWithUs = () => {
  const mealsConfig = useConfigStore(configKeys.RAILS_MEAL_CONFIG_ZOMATO);

  return (
    <View style={{ backgroundColor: colors.white }} testID="meal_bottomsheet_container">
      <View style={styles.textStyleContainer} testID="meal_bottomsheet_text_style_container">
        <Text
          style={[styles.textStyle, fontStyle('semiBold')]}
          testID="meal_bottomsheet_text_style_container_text"
        >
          {WHY_BOOK_WITH_US}
        </Text>
      </View>
      <View style={styles.card} testID="meal_bottomsheet_card">
        {mealsConfig?.mealsBenefits?.map((benefit, index) => (
          <MealBenefitsWithMmt
            key={index}
            title={benefit.title}
            description={benefit.subTitle}
            icon={{ uri: benefit.iconLink }}
            top={index}
            isLast={index === mealsConfig.mealsBenefits.length - 1}
          />
        ))}
      </View>
    </View>
  );
};

const styles = {
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 100,
    marginTop: 28,
    gap: 10,
    borderColor: colors.lightGray,
    borderWidth: 0.5,
    width: 171,
    height: 20,
    padding: 3,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 18,
    top: 8,
    zIndex: 1,
  },
  textStyleContainer: {
    backgroundColor: colors.white,
    borderRadius: 18,
    borderWidth: 0.5,
    borderStyle: 'solid',
    borderColor: colors.lightGray,
    width: 171,
    alignSelf: 'center',
    position: 'relative',
    top: 19,
    paddingVertical: 3,
    zIndex: 1,
  },
  textStyle: {
    fontSize: 12,
    lineHeight: 14.4,
    letterSpacing: 1.2,
    color: colors.black,
    textAlign: 'center',
  },
  benefitsContainer: {
    borderColor: colors.lightGray,
    borderWidth: 0.5,
    borderRadius: 18,
    padding: 10,
  },
  whiteBackground: {
    backgroundColor: colors.white,
    borderRadius: 16,
  },
  card: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 24,
    overflow: 'hidden',
    marginHorizontal: 12,
    marginTop: 8,
  },
};

export default WhyBookWithUs;
