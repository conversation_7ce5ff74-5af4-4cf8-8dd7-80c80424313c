import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
} from 'react-native';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import FastImage from 'react-native-fast-image';
import { Actions } from './../../../../navigation/railsNavigation';
import ASSETS from '../../../../Utils/Assets/RailsAssets';
import colors from '@mmt/ui/base/colors';
import arrowGrey from '@mmt/legacy-assets/src/arrowGrey.webp';
import MaskedView from '@react-native-community/masked-view';
import LinearGradient from 'react-native-linear-gradient';
import {GET_FOOD_DELIVERED_DIRECTLY_TO_YOUR_SEAT } from '../Constants/MealsConstants';
import { showMealsWebView } from '@mmt/rails/src/RailsAbConfig';

interface RailsMealEntryPointProps {
  onBookNowClicked: () => void;
}

const gradientColorsAndroid = ['#FF4500', '#FF0A54'];
const gradientColorsIos = ['#FF7F3F', '#FF3E5E'];

export const Gradient = (props: unknown) => {
  const gradientColors = Platform.OS === 'android' ? gradientColorsAndroid : gradientColorsIos;
  return (
    <MaskedView maskElement={<Text {...props} />}>
      <LinearGradient colors={gradientColors} start={{ x: 0, y: 0 }} end={{ x: 1, y: 1 }}>
        <Text {...props} style={[props.style, { opacity: 0 }]} />
      </LinearGradient>
    </MaskedView>
  );
};

const RailsMealEntryPointNewLanding = (props: RailsMealEntryPointProps) => {
  const { onBookNowClicked } = props;
  const screenWidth = Dimensions.get('window').width;
  const mealCardWidth = screenWidth * 0.43;

  const bookNow = () => {
    onBookNowClicked();

    const mealsWebView = showMealsWebView();
    if (mealsWebView) {
      Actions.openMealsFunnelWebView({
        pnr: '',
      });
    } else {
      Actions.openMealsPage();
    }
  };
  return (
    <TouchableOpacity onPress={bookNow} testID="meals_on_trains_entry_point">
      <View style={[styles.MealCard, { width: mealCardWidth }]}>
        <View style={styles.withMealTgIconContainer}>
          <View style={styles.mealsOnTrainsContainer}>
            <FastImage
              source={ASSETS.mealsOnTrains}
              style={styles.mealsOnTrainsIcon}
              resizeMode={FastImage.resizeMode.contain}
              testID="meals_on_trains_icon"
            />
            <FastImage
              source={ASSETS.railsNewTag}
              style={styles.newIcon}
              resizeMode={FastImage.resizeMode.contain}
            />
          </View>

          <View>
            <Image source={arrowGrey} style={styles.arrowIcon} />
          </View>
        </View>
        <View>
          <Text style={[styles.enjouYourFav, fontStyle('regular')]}>
            {GET_FOOD_DELIVERED_DIRECTLY_TO_YOUR_SEAT}
          </Text>
        </View>
        <Image source={ASSETS.foodIcon} style={styles.foodIcon} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  MealCard: {
    paddingTop: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    height: 152,
    position: 'relative',
    overflow: 'hidden',
  },
  enjouYourFav: {
    color: colors.defaultTextColor,
    fontSize: 14,
    paddingHorizontal: 14,
    marginTop: 4,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowIcon: {
    width: 20,
    height: 20,
    alignSelf: 'flex-start',
  },
  newIcon: {
    alignSelf: 'center',
    height: 13,
    width: 27,
    marginLeft: 4,
    marginBottom: 12,
  },
  mealsOnTrainsIcon: {
    height: 24,
    width: 67,
  },
  foodIcon: {
    width: 125,
    height: 85,
    borderBottomRightRadius: 16,
    marginLeft: 'auto',
    marginTop: 'auto',
    resizeMode: 'stretch',
  },
  withMealTgIconContainer: {
    marginLeft: 12,
    marginRight: 12,
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  withMealTgIcon: {
    height: 54,
    justifyContent: 'center',
  },
  foodLottieImage: {
    marginLeft: -6,
    height: '100%',
    width: '100%',
    objectFit: 'contain',
    overflow: 'visible',
  },
  mealsOnTrainsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default RailsMealEntryPointNewLanding;
