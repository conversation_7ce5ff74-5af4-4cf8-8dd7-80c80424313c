import TouchableRipple from 'packages/legacy-commons/Common/Components/TouchableRipple';
import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { INTERACTED_WITH_MEALS_LTS, ORDER_FOOD } from '../Constants/MealsConstants';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import { Actions } from 'apps/rails/src/navigation/railsNavigation';
import { FOOD_ICON } from 'apps/rails/src/Utils/RailsConstant';
import { setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { showMealsWebView } from '@mmt/rails/src/RailsAbConfig';

interface OrderMealProps {
  onOrderMealClicked: () => void;
}
const OrderMeal = ({ onOrderMealClicked }: OrderMealProps) => {
  const orderMealClicked = () => {
    setDataInStorage(INTERACTED_WITH_MEALS_LTS, true);

    const mealsWebView = showMealsWebView();
    if (mealsWebView) {
      Actions.openMealsFunnelWebView({
        pnr: '',
      });
    } else {
      Actions.openMealsPage();
    }

    onOrderMealClicked();
  };
  return (
    <TouchableRipple onPress={orderMealClicked} testID="meals_on_train_order_meal_touchable_ripple">
      <View style={styles.container} testID="meals_on_train_order_meal_container">
        <Image
          style={styles.foodIcon}
          source={{ uri: FOOD_ICON }}
          testID="meals_on_train_order_meal_image"
        />
        <Text
          style={[styles.orderMeal, fontStyle('black')]}
          testID="meals_on_train_order_meal_text"
        >
          {ORDER_FOOD}
        </Text>
      </View>
    </TouchableRipple>
  );
};

const styles = StyleSheet.create({
  orderMeal: {
    height: 20,
    fontSize: 12,
    color: colors.brown4,
    marginLeft: 3,
  },
  foodIcon: {
    height: 16,
    width: 16,
  },
  container: {
    flexDirection: 'row',
    marginLeft: 20,
    marginTop: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default OrderMeal;
