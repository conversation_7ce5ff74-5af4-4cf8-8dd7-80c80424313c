import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import TouchableRipple from 'packages/legacy-commons/Common/Components/TouchableRipple';
import IntroducingMealComponent from './IntroducingMealComponent';
import WhyBookWithUs from './WhyBookWithUs';
import { MEALS_IN_TRAINS, ORDER_NOW } from '../Constants/MealsConstants';
import { colors } from 'packages/legacy-commons/Styles/globalStyles';
import PNRTextInput from './PNRTextInput';
import { Actions } from 'apps/rails/src/navigation/railsNavigation';
import { setDataInStorage } from 'packages/legacy-commons/AppState/LocalStorage';
import { PNR_NUMBER } from '../Constants/MealsInTrainsWebViewConstants';
import { trackClickEventProp61 } from 'packages/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  LTS_DETAILS_MEALS_ORDER_NOW_BS_CLICKED,
  PNR_DETAILS_MEALS_ORDER_NOW_BS_CLICKED,
  RELFOOD_URL,
  MEALS_STATIONS_URL,
} from 'apps/rails/src/Utils/RailsConstant';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import {
  PAGE_RIS_LTS_INFO,
  RIS_LANDING_PNR_DETAILS_SUCCESS,
} from 'apps/rails/src/RisAnalytics/RisAnalytics';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { getMealsNewFlow, showMealsWebView } from '@mmt/rails/src/RailsAbConfig';
import BottomSheetModalTrain from '../../../Common/BottomSheetModalTrain';

interface MealBottomSheetProps {
  setShowMealBottomSheet: (show: boolean) => {
    // Function to set the visibility of the bottom sheet
  };
  upComingPnrs?: unknown;
  isPnr?: boolean;
  pnrNumberFromPnrPage?: string;
}
const { width: screenWidth } = Dimensions.get('window');
const MealBottomSheet = ({
  setShowMealBottomSheet,
  upComingPnrs,
  isPnr,
  pnrNumberFromPnrPage,
}: MealBottomSheetProps) => {
  const [showWhyBookWithUs, setShowWhyBookWithUs] = useState(true);
  const configVal = useConfigStore(configKeys.RAILS_MEAL_CONFIG);
  const [pnrNumber, setPnrNumber] = useState('');
  const [orderButtonClicked, setOrderButtonClicked] = useState(0);

  const onOrderMealClicked = () => {
    const showNewMeals = getMealsNewFlow();
    const mealsWebView = showMealsWebView();
    if (mealsWebView) {
      setDataInStorage(PNR_NUMBER, pnrNumber || '');
      trackClickEvent();
      Actions.openMealsFunnelWebView({
        pnr: pnrNumber || '',
      });
      closeMealBottomSheet();
      return;
    }

    setShowWhyBookWithUs(false);
    if (orderButtonClicked > 0) {
      setDataInStorage(PNR_NUMBER, pnrNumber);
      trackClickEvent();

      if (showNewMeals) {
        Actions.openMealsFunnelWebView({
          pnr: pnrNumber,
          url: `${MEALS_STATIONS_URL}?pnr=${pnrNumber}`,
        });
      } else {
        Actions.openMealsInTrainsWebView({
          headerText: MEALS_IN_TRAINS,
          url: `${(configVal as unknown)?.relFoodUrlPnrPage ?? RELFOOD_URL}${pnrNumber}`,
          imgSrc: require('@mmt/legacy-assets/src/white_backarrow.webp'),
        });
      }
      closeMealBottomSheet();
    }
    setOrderButtonClicked((prev) => prev + 1);
  };

  const closeMealBottomSheet = () => {
    setShowMealBottomSheet(false);
  };

  const onPnrOrderMealClicked = () => {
    trackClickEventForPnrPageBottomSheet();
    setDataInStorage(PNR_NUMBER, pnrNumberFromPnrPage);
    const showNewMeals = getMealsNewFlow();

    if (showNewMeals) {
      Actions.openMealsFunnelWebView({
        pnr: pnrNumberFromPnrPage,
        url: `${MEALS_STATIONS_URL}?pnr=${pnrNumberFromPnrPage}`,
      });
    } else {
      Actions.openMealsInTrainsWebView({
        headerText: MEALS_IN_TRAINS,
        url: `${(configVal as unknown)?.relFoodUrlPnrPage ?? RELFOOD_URL}${pnrNumberFromPnrPage}`,
        imgSrc: require('@mmt/legacy-assets/src/white_backarrow.webp'),
      });
    }
    closeMealBottomSheet();
  };

  const trackClickEvent = () => {
    trackClickEventProp61(PAGE_RIS_LTS_INFO, LTS_DETAILS_MEALS_ORDER_NOW_BS_CLICKED);
  };

  const trackClickEventForPnrPageBottomSheet = () => {
    trackClickEventProp61(RIS_LANDING_PNR_DETAILS_SUCCESS, PNR_DETAILS_MEALS_ORDER_NOW_BS_CLICKED);
  };


  return (
    <BottomSheetModalTrain
      onTouchOutside={closeMealBottomSheet}
      additionalContainerStyle={{ zIndex: 99 }}
      testID="meal_bottomsheet_modal"
    >
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -300}
      >
        <View style={styles.closeIconContainer} testID="meal_bottomsheet_close_icon_container">
          <TouchableRipple onPress={closeMealBottomSheet} testID="meal_bottomsheet_close_button">
            <Image
              style={styles.crossIcon}
              source={ASSETS.crossIcon}
              testID="meal_bottomsheet_close_icon_container_image"
            />
            </TouchableRipple>
        </View>
        <ScrollView
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
          testID="meal_bottomsheet_scroll_view"
        >
          <View style={styles.container} testID="meal_bottomsheet_container">
            <IntroducingMealComponent />
            {showWhyBookWithUs && <WhyBookWithUs />}
            <View>
              {!showWhyBookWithUs && (
                <PNRTextInput
                  upComingPnrs={upComingPnrs}
                  pnrNumber={pnrNumber}
                  setPnrNumber={setPnrNumber}
                  isValidPnr={true}
                  errorMsg=""
                />
              )}
            </View>
          </View>
        </ScrollView>
        <TouchableRipple
            onPress={isPnr ? onPnrOrderMealClicked : onOrderMealClicked}
            disabled={!isPnr && !showWhyBookWithUs && pnrNumber.length !== 10}
          testID="meal_bottomsheet_order_button"
        >
            <View
            style={[
              styles.orderNowButtonContainer,
              !showWhyBookWithUs && pnrNumber.length !== 10 && styles.disabledButton,
            ]}
            testID="meal_bottomsheet_order_button_container"
          >
            <Text
              style={[styles.orderNowButtonText, fontStyle('black')]}
              testID="meal_bottomsheet_order_button_text"
            >
              {ORDER_NOW}
            </Text>
            </View>
          </TouchableRipple>
      </KeyboardAvoidingView>
    </BottomSheetModalTrain>
  );
};

const styles = StyleSheet.create({
  keyboardContainer: {
    backgroundColor: colors.white,
    paddingTop: 24,
    paddingHorizontal: 16,
  },
  container: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    flex: 3,
  },
  crossIcon: {
    height: 28,
    width: 28,
  },
  closeIconContainer: {
    position: 'absolute',
    top: 17,
    right: 10,
    zIndex: 1,
  },
  scrollContent: {
  },
  scrollContentContainer: {
    paddingBottom: 30,
  },
  orderButtonWithKeyboard: {
    bottom: 0,
  },
  orderButtonWhiteContainer: {
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: Platform.select({ ios: 0, android: 20 }),
    flex: 1,
    width: screenWidth,
    borderTopColor: colors.greyText1,
    borderTopWidth: 0.2,
  },
  orderNowButtonContainer: {
    borderRadius: 8,
    height: 44,
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal: 16,
  },
  touchableRippleContainer: {

  },
  orderNowButtonText: {
    fontFamily: 'Lato',
    fontSize: 16,
    lineHeight: 19.2,
    color: colors.white,
    textAlign: 'center',
    fontWeight: '900',
  },
  disabledButton: {
    backgroundColor: colors.lightTextColor,
    borderColor: colors.lightTextColor,
  },
});

export default MealBottomSheet;
