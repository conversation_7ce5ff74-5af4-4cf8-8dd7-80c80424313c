export interface MealsInTrainsWrapperProps {
  url: string;
  headerText: string;
  imgSrc: string;
  closeWebView: () => void;
}

export interface AvailabilityCalendarWebViewWrapperProps {
  url: string;
  headerText: string;
  imgSrc: string;
  closeWebView: () => void;
}

export interface UserDetails {
  firstName: string;
  lastName: string;
  mobile: string;
  email: string;
}

export interface MealsInTrainsQueries {
  pnr?: string;
  passengerName?: string;
  mobileNumber?: string;
  email?: string;
  searchInput?: string;
  orderNow?: string;
  addButton?: string;
  proceedToNextButton?: string;
  nextButton?: string;
  chkIsVeg?: string;
}

export const PAGE_TRACKING: { [key: string]: string } = {
  HOME: 'rf_home_page',
  STATION_LIST: 'rf_station_list_page',
  MENU: 'rf_menu_page',
  CUSTOMER_DETAIL: 'rf_customer_detail_page',
  PAYMENT: 'rf_payment_page',
};

export const CLICK_EVENTS: { [key: string]: string } = {
  SEARCH_INPUT_CLICKED: 'SEARCH_INPUT_CLICKED',
  ADD_FIRST_CLICK: 'ADD_FIRST_CLICK',
  ORDER_NOW_CLICKED: 'ORDER_NOW_CLICKED',
  NEXT_CTA_CLICK: 'NEXT_CTA_CLICK',
  CHK_IS_VEG_CLICKED: 'CHK_IS_VEG_CLICKED',
  PROCEED_TO_NEXT_CTA_CLICK: 'PROCEED_TO_NEXT_CTA_CLICK',
  MMT_HOME_PAGE: 'MMT_HOME_PAGE',
};

export const URL_PATTERNS: { [key: string]: string } = {
  SEARCH: '/Search',
  MENU: '/Menu',
  CHECKOUT: '/CheckOut',
  CONFIRM_ORDER: '/ConfirmOrder/Index/',
};

export const EVENT_ACTIONS: { [key: string]: string } = {
  [CLICK_EVENTS.SEARCH_INPUT_CLICKED]: 'rf_home_page_search_clicked',
  [CLICK_EVENTS.ADD_FIRST_CLICK]: 'rf_menu_add_clicked',
  [CLICK_EVENTS.ORDER_NOW_CLICKED]: 'rf_station_list_order_now_clicked',
  [CLICK_EVENTS.NEXT_CTA_CLICK]: 'rf_menu_next_clicked',
  [CLICK_EVENTS.CHK_IS_VEG_CLICKED]: 'rf_menu_veg_toggle_clicked',
  [CLICK_EVENTS.PROCEED_TO_NEXT_CTA_CLICK]: 'rf_menu_proceed_next_clicked',
};

export const PAGE_TRACKING_BY_ACTION: { [key: string]: string } = {
  [CLICK_EVENTS.SEARCH_INPUT_CLICKED]: PAGE_TRACKING.HOME,
  [CLICK_EVENTS.ORDER_NOW_CLICKED]: PAGE_TRACKING.STATION_LIST,
  [CLICK_EVENTS.ADD_FIRST_CLICK]: PAGE_TRACKING.MENU,
  [CLICK_EVENTS.NEXT_CTA_CLICK]: PAGE_TRACKING.MENU,
  [CLICK_EVENTS.CHK_IS_VEG_CLICKED]: PAGE_TRACKING.MENU,
  [CLICK_EVENTS.PROCEED_TO_NEXT_CTA_CLICK]: PAGE_TRACKING.MENU,
};

export const URL_PAGE_TRACKING: { [key: string]: string } = {
  [URL_PATTERNS.SEARCH]: PAGE_TRACKING.STATION_LIST,
  [URL_PATTERNS.MENU]: PAGE_TRACKING.MENU,
  [URL_PATTERNS.CHECKOUT]: PAGE_TRACKING.CUSTOMER_DETAIL,
  [URL_PATTERNS.CONFIRM_ORDER]: PAGE_TRACKING.PAYMENT,
};

export const PNR_NUMBER = 'PNR_NUMBER';

export const REL_FOOD_URL = 'makemytrip.relfood.com';
