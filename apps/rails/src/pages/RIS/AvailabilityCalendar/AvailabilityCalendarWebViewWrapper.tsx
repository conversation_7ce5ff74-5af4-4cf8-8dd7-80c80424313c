import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  BackHandler,
  View,
  ActivityIndicator,
  StyleSheet,
  KeyboardAvoidingView,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { Actions } from 'apps/rails/src/navigation/railsNavigation';
import { AvailabilityCalendarWebViewWrapperProps } from '../MealsOnTrain/Constants/MealsInTrainsWebViewConstants';
import CommonHeader from 'packages/legacy-commons/Common/Components/Header/CommonHeader';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { RAILS_ROUTE_KEYS } from '@mmt/rails/src/navigation/railsPageKeys';

const AvailabilityCalendarWebViewWrapper: React.FC<AvailabilityCalendarWebViewWrapperProps> = ({
  url,
  headerText,
  headerIcon,
  loaderColor,
  closeWebView,
}) => {
  const [canGoBack, setCanGoBack] = useState(false);
  const [hideWebView, setHideWebView] = useState(false);
  const webViewRef = useRef<WebView>(null);
  const { startNextScreenTimer } = useScreenProfiler();
  const handleBackButtonClick = useCallback(() => {
    if (canGoBack && webViewRef.current) {
      webViewRef.current.goBack();
      return true;
    }
    if (hideWebView) {
      return false;
    }
    setHideWebView(true);
    return true;
  }, [canGoBack, hideWebView]);

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    const timer = setTimeout(() => {
      setHideWebView(false);
    }, 300);

    return () => {
      clearTimeout(timer);
      BackHandler.removeEventListener('hardwareBackPress', handleBackButtonClick);
    };
  }, [handleBackButtonClick]);

  useEffect(() => {
    if (hideWebView) {
      closeWebView();
    }
  }, [hideWebView, closeWebView]);

  const getAllQueryParamsFromUrl = (url) => {
    const urlSplits = decodeURIComponent(url).split('?');
    if (urlSplits.length < 2) {
      return {};
    }
    const pairs = urlSplits[1].split('&');
    const result = {};

    pairs.forEach((pair) => {
      const keyValueDelimiterIndex = pair.indexOf('=');
      const key = pair?.substring(0, keyValueDelimiterIndex);
      const value = pair.substring(keyValueDelimiterIndex + 1);
      result[key] = decodeURIComponent(value || '');
    });

    return JSON.parse(JSON.stringify(result));
  };

  const onNavigationStateChange = (navState: unknown) => {
    const url = navState?.url;
    const isMatch = url?.includes('railways/search/railsTravelerPage');
    if (isMatch) {
      const urlParams = getAllQueryParamsFromUrl(url);

      const originStation = {
        cityName: urlParams ? urlParams.originCityName : null,
        code: urlParams ? urlParams.from : null,
        stationName: urlParams ? urlParams.originStationName : null,
      };

      const destinationStation = {
        cityName: urlParams ? urlParams.destinationCityName : null,
        code: urlParams ? urlParams.to : null,
        stationName: urlParams ? urlParams.destinationStationName : null,
      };

      const departureDate = urlParams ? urlParams.departure : null;
      const trainNumber = urlParams ? urlParams.trainNumber : null;
      const classCode = urlParams ? urlParams.classCode : null;
      const quota = urlParams ? urlParams.quota : null;

      const deeplinkData = {
        from: originStation.code,
        to: destinationStation.code,
        trainNumber,
        classCode,
        quota,
        departure: departureDate,
        deeplink: true,
      };
      startNextScreenTimer(RAILS_ROUTE_KEYS.travelers, Date.now());
      Actions.railsTravellerDeeplink(deeplinkData, 'refresh');
      webViewRef?.current?.goBack();
      return;
    }

    setCanGoBack(navState.canGoBack);
  };

  const renderLoadingView = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={loaderColor || '#008cff'} />
    </View>
  );

  return (
    <View style={styles.flexContainer}>
      <CommonHeader
        headerText={headerText}
        imgSrc={headerIcon}
        backPressHandler={handleBackButtonClick}
      />
      {!hideWebView && (
        <KeyboardAvoidingView
          style={styles.container}
          behavior="padding"
          keyboardVerticalOffset={20}
          enabled
        >
          <WebView
            ref={webViewRef}
            source={{ uri: url }}
            onNavigationStateChange={onNavigationStateChange}
            startInLoadingState
            renderLoading={renderLoadingView}
          />
        </KeyboardAvoidingView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  flexContainer: { flex: 1, flexDirection: 'column' },
  container: { flex: 1, flexGrow: 1 },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AvailabilityCalendarWebViewWrapper;
