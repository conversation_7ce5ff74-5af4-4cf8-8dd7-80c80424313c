import { CrossSellDataObj } from '../PnrStatus/Components/PNRCrossSells/types';
import { DateDetails, TrainDetails, StationDetails } from '../RisCommonInterfaces';
export type { DateDetails, TrainDetails, StationDetails };

export interface RequestDetails {
  trainDetail: TrainDetails;
  stationDetail: StationDetails;
  dateDetail: DateDetails;
}

export interface OtherDetails {
  Delay: boolean;
  DistanceDetail: string;
  TimeDetail: string;
}

export interface SwitcherData {
  bold: boolean;
  value: string;
}

export interface IntermediateStationDetail {
  name: string;
  code: string;
}

export interface StationDetail extends IntermediateStationDetail {
  expectedPlatformNumber: string;
  platformNumber?: number;
}

export interface ArrivalDetail {
  arrived: boolean;
  actualArrivalTime: string;
  scheduledArrivalTime: string;
  actualArrivalDate: string;
  scheduledArrivalDate: string;
}
export interface DepartureDetail {
  departed: boolean;
  actualDepartureTime: string;
  scheduledDepartureTime: string;
  actualDepartureDate: string;
  scheduledDepartureDate: string;
}

export interface DayDetail {
  dayCount: number;
  scheduledDayCount: number;
  dayDifference: number;
}

export interface MetaDetail {
  StoppingStation: boolean;
  DivertedStation: boolean;
  CancelledStation: boolean;
}

export interface Location {
  latitude: number;
  longitude: number;
}

export interface IntermediateStation {
  Station: IntermediateStationDetail;
  ArrivalDetails: ArrivalDetail;
  DepartureDetails: DepartureDetail;
  Location: Location;
  StopNumber: number;
  Distance: string;
  Day: number;
}

export interface StoppingStation {
  IntermediateStations: IntermediateStation[];
  Station: StationDetail;
  HaltMinutes: number;
  ArrivalDetails: ArrivalDetail;
  DepartureDetails: DepartureDetail;
  DayDetails: DayDetail;
  JourneyDate: string;
  MetaDetails: MetaDetail;
  Distance: string;
  StopNumber: number;
  Location: Location;
}

export interface StationsSection {
  title: {
    date: string;
    sectionIndex: number;
  };
  data: StoppingStation[];
}

export interface CurrentStation {
  StoppingStation: StoppingStation;
  IntermediateStation?: IntermediateStation;
}

export interface DaysOfRun {
  Sun: boolean;
  Mon: boolean;
  Tue: boolean;
  Wed: boolean;
  Thu: boolean;
  Fri: boolean;
  Sat: boolean;
}

export interface TrainDetail {
  Number: string;
  Name: string;
  DistanceCovered: number;
  StartDate: string;
  StartDayDifference: string;
  StartDay: string;
  CurrentStation: IntermediateStationDetail;
  CancelledFromStation: unknown;
  CancelledToStation: unknown;
  Departed: boolean;
  Terminated: boolean;
  TotalJourney: string;
  TotalLateMinutes: number;
  Classes: string[];
  DaysOfRun: DaysOfRun;
  TrainAvgSpeed: number;
}

export interface OtherOption {
  offlineTracking: boolean;
  offlineError: boolean;
  showTrainSchedule: boolean;
  onlineError: boolean;
}

export interface StateProps {
  firstStationDetail: StoppingStation;
  lastStationDetail: StoppingStation;
  nextStationDetail: StoppingStation;
  currentStationDetail: CurrentStation;
  previousStationDetail: StoppingStation | IntermediateStation;
  trainDetails: TrainDetail;
  otherDetails: OtherDetails;
  disclaimer: string;
  lastUpdated: string;
  stationsSectionList: StationsSection[];
  showLoader: boolean;
  ltsViewState: string;
  ltsErrorMsg: string;
  otherOptions: OtherOption;
  lastAPIFetched: Date;
  cancellationAndDiversionInfo: CancellationAndDiversionInfo;
  pipModeStationList: [StoppingStation | IntermediateStation];
  ltsOfflineTrackingFetching: boolean;
  trainsLiveGPSSpeed: number;
  upComingPnrs: unknown;
}

export interface CancellationAndDiversionInfo {
  Cancelled:boolean;
  Diverted:boolean;
  Message:string;
  Tokens:string[];
}

export interface FeedbackData {
  feedback: string;
  timestamp: string;
  station: string;
  trainNumber: string;
}

export interface FeedBack {
  uuid?: string;
  phoneNumber?: string;
  deviceId?: string;
  data: FeedbackData[];
}

export interface StationLogData {
  stationCode: string;
  arrivalTime: string;
  source: string;
}

export interface LocationLogData {
  CT: {
    id: string;
    presentInData: boolean;
  };
  GPS: {
    lat: number;
    lng: number;
  };
  timeStamp: string;
}

export interface LogData {
  trainDetail: {
    trainNumber: string;
    trainStartDate: string;
  };
  stationData: StationLogData;
  locationData: LocationLogData;
}

export interface StationData {
  stnC: string; // stationCode
  aT: string; // arrivalTime DD-MM-YYYY HH:mm
  sr: string; //G,C,G&C
}

export interface LocationData {
  cT: {
    id: string; // cellTowerId optional, it can be absent
    pB: boolean; // cellTowerIdPresentInBundle
  };
  gps: {
    lat: number;
    long: number;
  }; // optional, it can be absent
  tS: string; // timeStamp DD-MM-YYYY HH:mm
}

export interface FinalLogData {
  tN: string; // trainNumber Required
  tSD: string; // trainStartDate DD-MM-YYYY Required
  // aV: string; // appVersion Required
  // dvid: string;  // deviceId Required
  // uuid: string; //UUID  optional
  stnD: StationData[];
  locD: LocationData[];
}

export interface StoppingStationOffsetCalculatioParameters {
  station: StoppingStation;
  currentStation: CurrentStation;
  offsetWindow: number;
  intermediatesExpanded: boolean;
}

export interface NativeCTandGPSData {
  lat: number;
  lng: CurrentStation;
  hasSpeed: boolean;
  speed: number;
  MNC: string;
  MCC: string;
  LAC: number;
  CID: number;
}

export interface LTS_MASTER_CONFIG {
  ltsDefaultSpeed: number;
  distanceCoveredDiffLimit: number;
  maxUserDistanceFromStation: number;
  maxHoursDiffFromArrival: number;
}

export interface ErrorLogData {
  refreshCount: number;
  algorithm: string;
  searchDetails: SearchDetails;
  ltsData: LtsData;
  cellTowerId?: string;
  isUserOnTrainByGPS: IsUserOnTrainByGps;
  userCurrentLocation?: UserCurrentLocation;
  failedReason: string;
  userDetails?: UserDetails;
  sessionId?: string;
  deviceDetails: DeviceDetails;
  scheduleApiResponse?: unknown;
}

export interface SearchDetails {
  trainNo?: string;
  doj?: string;
}

export interface LtsData {
  lastFetched?: string;
  lastUpdated?: string;
  currentStationCode?: string;
}

export interface IsUserOnTrainByGps {
  lastCurrentStation?: string;
  expectedCurrentStation?: string;
  userDistanceFromExpectedStation?: number;
  currentTimeStamp?: string;
  prevStationCheck: PrevStationCheck;
  distanceCheck: DistanceCheck;
  map?: string;
}

export interface PrevStationCheck {
  prevStationCode?: string;
  prevStationDeparture?: string;
  prevStationTimestampCheck: boolean;
}

export interface DistanceCheck {
  lastDistanceCovered?: string;
  expectedDistanceCovered?: string;
  distanceCoveredDiff?: number;
  trainSpeed?: number;
  timeDiffInHours?: number;
  probableDistanceCoveredDiff?: number;
  isDistanceValid: boolean;
}

export interface UserCurrentLocation {
  accuracy?: number;
  lng?: number;
  speed?: number;
  hasSpeed?: boolean;
  lat?: number;
}

export interface UserDetails {
  uuid?: string;
  mmtAuth?: string;
}

export interface DeviceDetails {
  deviceName?: string;
  networkType?: string;
  os?: string;
  networkSpeed?: string;
}

export interface railInfoReducer {
  crossSellData: CrossSellDataObj;
}
