import { connect } from 'react-redux';
import LiveTrainStatusPage from './LiveTrainStatusPage';
import { initTracking, clearOfflineError, clearStore, offlineTracking } from '../Util/LiveTrainStatusActions';
import { StateProps, RequestDetails, NativeCTandGPSData } from '../interface';
import { CrossSellDataObj } from '../../PnrStatus/Components/PNRCrossSells/types';

const mapStateToProps = ({
  liveTrainStatusReducer: {
    firstStationDetail,
    lastStationDetail,
    nextStationDetail,
    currentStationDetail,
    previousStationDetail,
    trainDetails,
    otherDetails,
    disclaimer,
    lastUpdated,
    stationsSectionList,
    showLoader,
    ltsViewState,
    ltsErrorMsg,
    otherOptions,
    lastAPIFetched,
    cancellationAndDiversionInfo,
    pipModeStationList,
    ltsOfflineTrackingFetching,
    trainsLiveGPSSpeed,
    dataSource,
  },
  railInfoReducer: { crossSellData, upComingPnrs },
}: {
  liveTrainStatusReducer: StateProps;
  railInfoReducer: { crossSellData: CrossSellDataObj; upComingPnrs: unknown };
}) => {
  return {
    firstStationDetail,
    lastStationDetail,
    nextStationDetail,
    currentStationDetail,
    previousStationDetail,
    trainDetails,
    otherDetails,
    disclaimer,
    lastUpdated,
    stationsSectionList,
    showLoader,
    ltsViewState,
    ltsErrorMsg,
    otherOptions,
    lastAPIFetched,
    cancellationAndDiversionInfo,
    pipModeStationList,
    ltsOfflineTrackingFetching,
    trainsLiveGPSSpeed,
    dataSource,
    ltsCrossSellData: crossSellData,
    upComingPnrs,
  };
};

const mapDispatchToProps = (dispatch) => ({
  initTracking: (
    requestDetails: RequestDetails,
    refresh: boolean,
    autoRefresh: boolean,
    refreshCount: number,
  ) => dispatch(initTracking(requestDetails, refresh, autoRefresh, refreshCount)),
  initOfflineTracking: (
    requestDetails: RequestDetails,
    refresh: boolean,
    autoRefresh: boolean,
    nativeCTGPSData: NativeCTandGPSData,
  ) => dispatch(offlineTracking(requestDetails, refresh, autoRefresh, false, nativeCTGPSData)),
  clearOfflineError: (updateOptions: unknown) => dispatch(clearOfflineError(updateOptions)),
  clearStore: () => dispatch(clearStore),
});

export default connect(mapStateToProps, mapDispatchToProps)(LiveTrainStatusPage);
