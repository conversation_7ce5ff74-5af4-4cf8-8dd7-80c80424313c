import React from 'react';
import { StyleSheet, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import LottieView from 'lottie-react-native';
import greenLottieIcon from '../LottieFiles/lts_green_current_station_lottie_icon.json';
import redLottieIcon from '../LottieFiles/lts_red_current_station_lottie_icon.json';

interface Props {
  index: number;
  delay: boolean;
  hideCircle: boolean;
  isLastStation: boolean;
}

export const LiveTrainStatusPIPListLine = ({ index, delay, hideCircle, isLastStation }: Props) => {
  const currentLottieIcon = delay ? redLottieIcon : greenLottieIcon;
  return (
    <View style={styles.listMarkerContainer}>
      {!isLastStation ? (
        <View style={[styles.verticalLine, index > 0 && styles.greyVerticalLine]} />
      ) : (
        <View style={styles.transparentVerticalLine} />
      )}
      {!hideCircle ? (
        <View style={[styles.circle, index > 1 && styles.greyCircle]} />
      ) : (
        index === 1 && (
          <View style={styles.lottieContainer}>
            <LottieView
              source={currentLottieIcon}
              loop={true}
              autoPlay={true}
              progress={0}
              style={styles.currentLottie}
            />
          </View>
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  verticalLine: {
    height: '100%',
    width: 4,
    backgroundColor: colors.lightBlue29,
  },
  greyVerticalLine: {
    backgroundColor: colors.lightGrey,
  },
  transparentVerticalLine: {
    width: 4,
    backgroundColor: colors.transparent,
  },
  circle: {
    width: 12,
    height: 12,
    left: -4,
    backgroundColor: colors.white,
    borderWidth: 3,
    borderColor: colors.lightBlue29,
    borderStyle: 'solid',
    borderRadius: 100,
    position: 'absolute',
  },
  greyCircle: {
    borderColor: colors.lightGrey,
  },
  lottieContainer: {
    position: 'absolute',
    left: -18,
    marginTop: -12,
  },
  currentLottie: {
    width: 40,
    height: 40,
  },
  listMarkerContainer: {
    height: 45,
  },
});
