import isEmpty from 'lodash/isEmpty';
import {
  getIntermediateStationIndexParams,
  getStoppingStationIndexParams,
  getTimeDiffInMins,
} from '../Util/OfflineTrackingHelper';
import type {
  CurrentStation,
  FeedbackData,
  IntermediateStation,
  RequestDetails,
  StationsSection,
  StoppingStation,
  TrainDetail,
  LocationLogData,
  StationLogData,
  LogData,
  StationData,
  LocationData,
  FinalLogData,
  StoppingStationOffsetCalculatioParameters,
} from '../interface';
import { getDateDetails, sendLtsFeedBack, loggingLts } from '../../../../Utils/RisUtils';
import { asyncStorageKeys, months } from '../constants';
import { getDataFromStorage, setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
// import Toast from 'src/Common/Components/Toast2';  // this can be used to make custom Toast UI /* eslint-disable */
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { checkForLocationPermission } from '@mmt/legacy-commons/Helpers/locationHelper';
import AbConfigKeyMappings, { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { getPokusConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import fecha from 'fecha';

export const ToastMessage = (message: string) => {
  showShortToast(message);
};

export const getDistanceCovered = (currentStationDetail: CurrentStation) => {
  return isEmpty(currentStationDetail?.IntermediateStation) ||
    isEmpty(currentStationDetail?.IntermediateStation?.Distance) ||
    parseInt(currentStationDetail?.IntermediateStation?.Distance || '0') === 0
    ? parseInt(currentStationDetail?.StoppingStation?.Distance)
    : parseInt(currentStationDetail?.IntermediateStation?.Distance);
};

export const formatDateTime = (dateTime: string) => {
  // Input format: DD-MM-YYYY HH:MM:SS
  // Output format: DD MMM YY
  if (!dateTime) {return '';}
  const dateArr = dateTime.split(' ')[0].split('-');
  const finalDate =
    dateArr[0] + ' ' + months[parseInt(dateArr[1]) - 1] + ' ' + dateArr[2].substring(2, 4);
  return finalDate;
};

export const isFutureDate = (date: string) => {
  if (isEmpty(date)) {return false;}
  const calculatedDate = getDateDetails(
    new Date(
      parseInt(date?.split('-')?.[2]),
      parseInt(date?.split('-')?.[1]) - 1,
      parseInt(date?.split('-')?.[0]),
    ),
  );
  return calculatedDate.dayDifference > 0;
};

export const createStartDateDetails = ({ StartDate }: TrainDetail) => {
  if (isEmpty(StartDate)) {
    return '';
  }
  const date = getDateDetails(
    new Date(
      parseInt(StartDate?.split('-')?.[2]),
      parseInt(StartDate?.split('-')?.[1]) - 1,
      parseInt(StartDate?.split('-')?.[0]),
    ),
  );
  return `${date?.dateTitle}, ${date?.dateFormattedSmall}`;
};

export const timeSince = (date?: Date) => {
  if (!date) {return undefined;}

  const seconds = Math.floor((Date.now() - date?.getTime()) / 1000);
  const ans = {
    value: seconds,
    stringValue: '',
  };

  let interval = Math.floor(seconds / 3600);
  if (interval > 1) {
    ans.stringValue = (interval <= 9 ? '0' : '') + interval + ' hrs ago';
    return ans;
  }
  interval = Math.floor(seconds / 60);
  if (interval === 0) {
    ans.stringValue = 'Just now';
    return ans;
  }
  ans.stringValue = (interval <= 9 ? '0' : '') + interval + ' min ago';
  return ans;
};

export const trainStatus = (arrived: boolean, departed: boolean) => {
  if (departed) {return 'Crossed';}
  if (arrived) {return 'Arrived';}
  return 'Arriving';
};

export const onSendFeedback = async (feedback: FeedbackData) => {
  // eslint-disable-next-line
  getDataFromStorage(asyncStorageKeys.FEEDBACK).then((feedBacks: FeedbackData[] = []) => {
    // eslint-disable-next-line
    setDataInStorage(asyncStorageKeys.FEEDBACK, [...(feedBacks || []), feedback]).then(() => {
      sendLtsFeedBack();
    });
  });
};

const shouldStationDataBeLogged = (lastStationData: StationData, stationData: StationLogData) => {
  return (
    !isEmpty(stationData) && // if stationData is not provided then, there is nothing to be inserted
    (isEmpty(lastStationData) || // if no stationData is previously stored then, stationData could be directly stored
      (!isEmpty(lastStationData) && stationData?.stationCode !== lastStationData.stnC)) // if last logged station is different than given station then, log it
  );
};

const shouldLocationDataBeLogged = (lastLocationData: LocationData, locationData: LocationLogData) => {
  return (
    !isEmpty(locationData) &&
    !isEmpty(locationData?.CT?.id) && // if locationData is not provided then, there is nothing to be inserted
    (isEmpty(lastLocationData) || // if no locationData is previously stored then, locationData could be directly stored
      (!isEmpty(lastLocationData) &&
        (locationData?.CT?.id !== lastLocationData?.cT?.id ||
          locationData?.GPS?.lat !== lastLocationData?.gps?.lat ||
          locationData?.GPS?.lng !== lastLocationData?.gps?.long))) // if last logged locationData is different than given locationData then, log it
  );
};

interface LastLog { stationData: StationData; locationData: LocationData }
const lastLog: LastLog = {} as LastLog;

export const sendLogs = async (logData: LogData) => {
  try {
    const logPokus = getPokusConfig(
      PokusLobs.RAIL,
      AbConfigKeyMappings.enableRailsLtsAccuracyLogging,
      false,
    );
    if (!logPokus) {return;}
    const { trainDetail, locationData, stationData } = logData ?? {};
    if (isEmpty(trainDetail) || isEmpty(stationData) || isEmpty(locationData)) {return;}
    const { trainNumber = '', trainStartDate = '' } = trainDetail ?? {};
    if (isEmpty(trainNumber) || isEmpty(trainStartDate)) {return;}
    getDataFromStorage(asyncStorageKeys.LOGGING)
      .then((finalLogDatas: FinalLogData[]) => {
        if (isEmpty(finalLogDatas)) {finalLogDatas = [];}
        const logDataIndex = finalLogDatas?.findIndex((logs) => {
          return logs?.tN === trainNumber && logs?.tSD === trainStartDate;
        });

        const finalLogData: FinalLogData =
          logDataIndex !== -1
            ? finalLogDatas[logDataIndex]
            : ({
                tN: trainNumber,
                tSD: trainStartDate,
                stnD: [],
                locD: [],
              } as FinalLogData);

        const lastStationData = finalLogData.stnD[finalLogData?.stnD?.length - 1];
        const stationLog = {
          stnC: stationData?.stationCode,
          aT: stationData?.arrivalTime,
          sr: stationData?.source,
        };
        if (shouldStationDataBeLogged(lastLog?.stationData ?? lastStationData, stationData)) {
          finalLogData.stnD.push(stationLog);
        }

        const lastLocationData = finalLogData.locD[finalLogData?.locD?.length - 1];
        const locationLog = {
          cT: {
            id: locationData?.CT?.id,
            pB: locationData?.CT?.presentInData,
          },
          gps: {
            lat: locationData?.GPS?.lat,
            long: locationData?.GPS?.lng,
          },
          tS: locationData?.timeStamp,
        };
        if (shouldLocationDataBeLogged(lastLog?.locationData ?? lastLocationData, locationData)) {
          finalLogData.locD.push(locationLog);
        }

        if (finalLogData.stnD.length > 0 || finalLogData.locD.length > 0) {
          if (logDataIndex !== -1) {finalLogDatas[logDataIndex] = finalLogData;}
          else {finalLogDatas.push(finalLogData);}
        }
        if (isEmpty(finalLogDatas)) {return Promise.reject(null);}
        lastLog.stationData = stationLog;
        lastLog.locationData = locationLog;

        return setDataInStorage(asyncStorageKeys.LOGGING, finalLogDatas);
      })
      .then(async () => {
        const hasNetwork = await isNetworkAvailable();
        if (hasNetwork) {(await loggingLts)();}
      })
      .catch((error) => {
        console.log('Error in sendLogs promises: ', error);
      });
  }
  catch (error) {
    console.log('Error in sendLogs: ', error);
  }
};

export const getDoubleDigit = (x: number) => {
  return x <= 9 ? `0${x}` : x;
};

export const getTimeStringFromDate = (d: Date) => fecha.format(d, 'DD-MM-YYYY HH:mm');

export const getISOTimeStringFromDate = (d: Date) => fecha.format(d, 'YYYY-MM-DDTHH:mm:ss.SSS');

export const convertMinToString = (interval: number) => {
  const hours = Math.floor(interval / 60);
  const minutes = interval % 60;
  const result = {
    value: interval,
    values: {
      hours,
      minutes,
    },
    stringValues: {
      hours: getDoubleDigit(hours),
      minutes: getDoubleDigit(minutes),
    },
    string: (hours !== 0 ? `${getDoubleDigit(hours)}hrs ` : '') + `${getDoubleDigit(minutes)}mins`,
    shortString: (hours !== 0 ? `${getDoubleDigit(hours)}h ` : '') + `${getDoubleDigit(minutes)}m`,
  };
  return result;
};

export const getTimeStringFromArithmetic = (timeStamp: string, addMinutes: number) => {
  // timeStamp format DD-MM-YYYY HH:MM
  const [date, month, year] = timeStamp
    .split(' ')[0]
    .split('-')
    .map((x) => parseInt(x));
  const [hours, minutes] = timeStamp
    .split(' ')[1]
    .split(':')
    .map((x) => parseInt(x));
  const newTimeStamp = new Date(
    new Date(year, month - 1, date, hours, minutes).getTime() + addMinutes * 60000,
  );
  return getTimeStringFromDate(newTimeStamp).split(' ');
};

export const getDateFromArithmetic = (timeStamp: string, monthIndex = 0) => {
  // timeStamp format DD-MM-YYYY HH:MM
  const [date, month, year] = timeStamp
    .split(' ')[0]
    .split('-')
    .map((x) => parseInt(x));
  const [hours, minutes] = timeStamp
    .split(' ')[1]
    .split(':')
    .map((x) => parseInt(x));
  return new Date(year, month - monthIndex, date, hours, minutes);
};

interface Station {
  ArrivalDetails: {
    arrived?: boolean;
    actualArrivalDate?: string;
    actualArrivalTime?: string;
    scheduledArrivalTime?: string;
    scheduledArrivalDate?: string;
  };
  DepartureDetails: {
    departed?: boolean;
    actualDepartureDate?: string;
    actualDepartureTime?: string;
    scheduledDepartureTime?: string;
    scheduledDepartureDate?: string;
  };
}

export const getTimeStringFromStationDetails = (station: Station) => {
  const {
    actualArrivalDate,
    actualArrivalTime,
    scheduledArrivalTime,
    scheduledArrivalDate,
  } = station?.ArrivalDetails;
  const {
    departed,
    actualDepartureDate,
    actualDepartureTime,
    scheduledDepartureTime,
    scheduledDepartureDate,
  } = station?.DepartureDetails;
  const arrivalTimeStamp = `${scheduledArrivalDate?.split(' ')?.[0]} ${scheduledArrivalTime}`;
  const departureTimeStamp = `${scheduledDepartureDate?.split(' ')?.[0]} ${scheduledDepartureTime}`;
  const actualArrivalTimeStamp =
    !isEmpty(actualArrivalDate) && !isEmpty(actualArrivalTime)
      ? `${actualArrivalDate?.split(' ')?.[0]} ${actualArrivalTime}`
      : arrivalTimeStamp;
  const actualDepartureTimeStamp =
    !isEmpty(actualDepartureDate) && !isEmpty(actualDepartureTime)
      ? `${actualDepartureDate?.split(' ')?.[0]} ${actualDepartureTime}`
      : departureTimeStamp;
  const stationTimeStamp = departed ? actualDepartureTimeStamp : actualArrivalTimeStamp;
  return {
    arrivalTimeStamp,
    departureTimeStamp,
    actualArrivalTimeStamp,
    actualDepartureTimeStamp,
    stationTimeStamp,
  };
};

const stationDetailsValidity = (station: Station) => {
  const {
    arrived,
    actualArrivalDate,
    actualArrivalTime,
  } = station?.ArrivalDetails;
  const {
    departed,
    actualDepartureDate,
    actualDepartureTime,
  } = station?.DepartureDetails;
  const isActualDeparture =
    departed && !isEmpty(actualDepartureDate) && !isEmpty(actualDepartureTime);
  const isActualArrival =
    arrived && !isEmpty(actualArrivalDate) && !isEmpty(actualArrivalTime);
  return { isActualArrival, isActualDeparture };
};

const getAllTimeStamps = (station: Station) => {
  const {
    actualArrivalDate,
    actualArrivalTime,
    scheduledArrivalTime,
    scheduledArrivalDate,
  } = station?.ArrivalDetails;
  const {
    actualDepartureDate,
    actualDepartureTime,
    scheduledDepartureTime,
    scheduledDepartureDate,
  } = station?.DepartureDetails;
  const actualDepartureTimeStamp = actualDepartureDate?.split(' ')[0].concat(' ', actualDepartureTime || '');
  const scheduledDepartureTimeStamp = scheduledDepartureDate?.split(' ')[0].concat(' ', scheduledDepartureTime || '');
  const actualArrivalTimeStamp = actualArrivalDate?.split(' ')[0].concat(' ', actualArrivalTime || '');
  const scheduledArrivalTimeStamp = scheduledArrivalDate?.split(' ')[0].concat(' ', scheduledArrivalTime || '');
  return {
    scheduledArrivalTimeStamp,
    actualArrivalTimeStamp,
    scheduledDepartureTimeStamp,
    actualDepartureTimeStamp,
  };
};

const getTimeStampsForDelay = (station: Station) => {
  const { isActualArrival, isActualDeparture } = stationDetailsValidity(station);
  const {
    scheduledArrivalTimeStamp,
    actualArrivalTimeStamp,
    scheduledDepartureTimeStamp,
    actualDepartureTimeStamp,
  } = getAllTimeStamps(station);
  const currentTimeStamp = getTimeStringFromDate(new Date());
  const [actualTimeStamp, scheduledTimeStamp] = isActualDeparture
    ? [actualDepartureTimeStamp, scheduledDepartureTimeStamp]
    : isActualArrival
    ? [actualArrivalTimeStamp, scheduledArrivalTimeStamp]
      : [currentTimeStamp, scheduledArrivalTimeStamp];
  return { actualTimeStamp, scheduledTimeStamp };
};

export const getDelay = (station: Station) => {
  try {
    if (isEmpty(station)) {return convertMinToString(0);}
    const { actualTimeStamp, scheduledTimeStamp } = getTimeStampsForDelay(station);
    const delay = getTimeDiffInMins(actualTimeStamp, scheduledTimeStamp);
    return convertMinToString(delay < 0 ? 0 : delay);
  } catch (error) {
    console.log('Error in calculating delay: ', error,  station);
    return convertMinToString(0);
  }
};

export const getStoppingStationOffset = ({ station, currentStation, 
  offsetWindow, intermediatesExpanded }: StoppingStationOffsetCalculatioParameters) => {
  const isStationCurrent = station.StopNumber === currentStation?.StoppingStation?.StopNumber;
  const isStationNotCurrentOrShowingIntermediateStations =
    !isStationCurrent ||
    (isStationCurrent &&
      intermediatesExpanded &&
      !isEmpty(currentStation?.IntermediateStation?.Station?.code));

  const trainAlreadyArrivedAtStation = station.ArrivalDetails?.arrived;

  const trainAlreadyDepartedStation =
    !isEmpty(currentStation?.IntermediateStation?.Station?.code) ||
    station.DepartureDetails?.departed;

  const offsetIfAlreadyDepartedWithNoIntermediate = (3 * (offsetWindow - 35)) / 4;

  const offsetByIntermediatePassed =
    ((2 * (currentStation?.IntermediateStation?.StopNumber ?? 0) -
      (currentStation?.IntermediateStation?.ArrivalDetails?.arrived ? 0 : 0)) *
      (offsetWindow - 40)) /
    (2 * station?.IntermediateStations?.length);

  const offsetIfAlreadyDepartedWithIntermediate =
    15 + (isEmpty(currentStation?.IntermediateStation?.Station?.code) ? 0 : offsetByIntermediatePassed);

  const offsetIfAlreadyDeparted =
    (station?.IntermediateStations?.length ?? 0) === 0
      ? offsetIfAlreadyDepartedWithNoIntermediate
      : offsetIfAlreadyDepartedWithIntermediate;


  if (isStationNotCurrentOrShowingIntermediateStations) {return offsetWindow;}
  else if (trainAlreadyDepartedStation) {return offsetIfAlreadyDeparted;}
  else if (trainAlreadyArrivedAtStation) {return 0;}
  return -20;
};

export const getTimeToStation = (station: Station) => {
  if (isEmpty(station)) {return convertMinToString(0);}
  const stationTimeStamp = getTimeStringFromStationDetails(station)?.stationTimeStamp;
  const currentTimeStamp = getTimeStringFromDate(new Date());
  if (isEmpty(stationTimeStamp)) {return convertMinToString(0);}
  const delay = getTimeDiffInMins(stationTimeStamp, currentTimeStamp);
  return convertMinToString(delay < 0 ? 0 : delay);
};

export const updateToCrossedStation = (station: StoppingStation | IntermediateStation) => {
  station.ArrivalDetails.arrived = true;
  station.DepartureDetails.departed = true;
  return station;
};

export const getPreviousStationDetail = (
  currentStation: CurrentStation,
  stationsSectionList: StationsSection[],
) => {
  const currentIntermediateStationCode = currentStation?.IntermediateStation?.Station?.code;
  const currentStoppingStationCode = currentStation?.StoppingStation?.Station?.code;
  if (!isEmpty(currentIntermediateStationCode)) {
    const { i = 0, j = 0, k = 0 } = getIntermediateStationIndexParams(
      stationsSectionList,
      currentStoppingStationCode,
      currentIntermediateStationCode,
    );
    if (k > 0)
      {return updateToCrossedStation(
        stationsSectionList?.[i]?.data?.[j]?.IntermediateStations?.[k - 1],
      );}
    return updateToCrossedStation(stationsSectionList?.[i]?.data?.[j]);
  }
  const { i = 0, j = 0 } = getStoppingStationIndexParams(
    stationsSectionList,
    currentStoppingStationCode,
  );
  const prevStation =
    j > 0 ? stationsSectionList?.[i]?.data?.[j - 1] : stationsSectionList?.[i]?.data?.[j];
  const prevStationIntermediates = prevStation?.IntermediateStations;
  return updateToCrossedStation(
    !isEmpty(prevStationIntermediates) && prevStationIntermediates.length > 0
      ? prevStationIntermediates?.[prevStationIntermediates.length - 1]
      : prevStation,
  );
};

export const checkForTracking = async (requestDetails: RequestDetails) => {
  let isInsideTrain = false;
  let canContinueToTracking = false;
  const lastSavedInfo = await getDataFromStorage(asyncStorageKeys.RIS_LTS_OPTIONS);
  if (
    !isEmpty(lastSavedInfo) &&
    lastSavedInfo?.trainNumber === requestDetails?.trainDetail?.trainNumber
  )
    {isInsideTrain = lastSavedInfo?.isInsideTrainStatus;}
  const hasNetwork = await isNetworkAvailable();

  try {
    if (!isInsideTrain) {canContinueToTracking = true;}
    else if (isInsideTrain || !hasNetwork) {
      const hasPerm = await checkForLocationPermission();
      if (hasPerm) {canContinueToTracking = true;}
    }
  } catch (error) {
    console.log('Error in Permission: ', error);
  } finally {
    return canContinueToTracking;
  }
};

export const getDateFromTimeStamp = (timeStamp: string) => {
  // Input Format: DD-MM-YYYY HH:mm:ss
  const [mDate, mTime] = timeStamp.split(' ');
  const [mDay, mMonth, mYear] = mDate.split('-');
  const [mHours, mMinutes, mSeconds, mMilliseconds] = mTime?.split(':') ?? [];
  return new Date(Number(mYear), Number(mMonth) - 1, Number(mDay), 
  Number(mHours ?? 0), Number(mMinutes ?? 0), Number(mSeconds ?? 0), 
  Number(mMilliseconds ?? 0));
};

export const getHash = (input: string) => {
  let hash = 0;
  const len = input.length;
  for (let i = 0; i < len; i++) {
    hash  = ((hash << 5) - hash) + input.charCodeAt(i);
    hash |= 0; // to 32bit integer
  }
  return hash;
};

export const getTimeDiffWithCurrentTime = (timeStamp: string) => {
  const actualArrivalDate = getDateFromTimeStamp(timeStamp);
  const currentDate = new Date();
  return (actualArrivalDate.getTime() - currentDate.getTime()) / 60000.0;
};

export const formatToIST = (timeStamp: string) => {
  try {
    try {
      return timeStamp?.replace('Z', '');
    } catch (error) {
      return timeStamp?.toLocaleString();
    }
  } catch (error) {
    return timeStamp;
  }
};
