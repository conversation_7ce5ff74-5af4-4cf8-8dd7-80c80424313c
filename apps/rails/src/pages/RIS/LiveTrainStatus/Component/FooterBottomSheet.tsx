import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableWithoutFeedback,
  LayoutRectangle,
  Animated,
  Platform,
} from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels, BOTTOMSHEET_HEIGHT } from '../constants';
import FooterCommonComponent from './FooterCommonComponent';
import { getDelay, getTimeToStation } from './commonUtils';
import { CurrentStation, StoppingStation, TrainDetail, IntermediateStation } from '../interface';

interface Props {
  firstStationDetail: StoppingStation;
  currentStationDetail: CurrentStation;
  prevStationDetail: StoppingStation | IntermediateStation;
  nextStationDetail: StoppingStation;
  lastStationDetail: StoppingStation;
  toggleFSVisibility: () => void;
  trainDetails: TrainDetail;
  onRefresh: (isManualRefresh?: boolean) => void;
  lastAPIFetched: Date;
  animatedBottomSheet: Animated.Value;
  trainsLiveGPSSpeed?: number;
}

const FooterBottomSheet: React.FC<Props> = ({
  firstStationDetail,
  currentStationDetail,
  prevStationDetail,
  nextStationDetail,
  lastStationDetail,
  toggleFSVisibility,
  trainDetails,
  onRefresh,
  lastAPIFetched,
  animatedBottomSheet,
  trainsLiveGPSSpeed,
}) => {
  const started = trainDetails?.Departed;
  const distanceCovered = trainDetails.DistanceCovered;
  const terminated = trainDetails?.Terminated;
  const noOfDays = lastStationDetail?.DayDetails?.scheduledDayCount;
  const stationStatus = ['Exp. Arr  ', 'Arrived at  ', 'Crossed at  '];
  const trainStatus = { started, terminated };

  const currentStation = currentStationDetail?.IntermediateStation?.Station?.code
    ? currentStationDetail?.IntermediateStation
    : currentStationDetail?.StoppingStation;
  const delayInMins = getDelay(currentStation);
  const delay = delayInMins.value !== 0;
  const remainingTimeVar = getTimeToStation(lastStationDetail);
  const [remainingTime, setRemainingTime] = useState(remainingTimeVar);
  const timeToNextStationVar = getTimeToStation(nextStationDetail);
  const [timeToNextStation, setTimeToNextStation] = useState(timeToNextStationVar);

  useEffect(() => {
    const timer = setInterval(() => {
      const timeToNextStationVar = getTimeToStation(nextStationDetail);
      setTimeToNextStation(timeToNextStationVar);
      const remainingTimeVar = getTimeToStation(lastStationDetail);
      setRemainingTime(remainingTimeVar);
    }, 30000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  const [componentWidth, setComponentWidth] = useState(0);
  function find_dimesions(layout: LayoutRectangle) {
    const { width } = layout;
    setComponentWidth(Math.floor(width));
  }

  const _onRefresh = (isManualRefresh?: boolean) => {
    onRefresh(isManualRefresh);
    toggleFSVisibility();
  };

  const totalDistance = lastStationDetail.Distance;
  const totalBarlength = componentWidth - (started && !terminated ? 85 : 70);
  const distanceCoveredWidth = Math.round(
    (distanceCovered * totalBarlength) / parseInt(totalDistance),
  );
  const remainingDistanceWidth = totalBarlength - distanceCoveredWidth;
  const stations: {
    title: string;
    detail: StoppingStation;
    alignment: 'right' | 'left';
  }[] = [
    !started || terminated
      ? { title: labels.ORIGIN_STATION, detail: firstStationDetail, alignment: 'left' }
      : {
          title: labels.CURRENT_STATION,
          detail: currentStationDetail?.StoppingStation,
          alignment: 'left',
        },
    !terminated
      ? { title: labels.NEXT_STATION, detail: nextStationDetail, alignment: 'right' }
      : { title: labels.LAST_STATION, detail: lastStationDetail, alignment: 'right' },
  ];

  const renderSeperator = () => <View style={styles.seperator} />;

  const renderStation = ({
    title,
    detail,
    alignment,
  }: {
    title: string;
    detail: StoppingStation;
    alignment: 'right' | 'left';
    }) => {
    const expectedPlatformNo =
      detail?.Station?.expectedPlatformNumber &&
      detail?.Station?.expectedPlatformNumber !== '0' &&
      detail?.Station?.expectedPlatformNumber !== '-'
        ? ` ${detail?.Station?.expectedPlatformNumber} `
        : ' -- ';
    const stationArrivalTimeStamp = {
      actual: detail?.ArrivalDetails?.actualArrivalDate + detail?.ArrivalDetails?.actualArrivalTime,
      scheduled:
        detail?.ArrivalDetails?.scheduledArrivalDate + detail?.ArrivalDetails?.scheduledArrivalTime,
    };
    const stationDepartureTimeStamp = {
      actual:
        detail?.DepartureDetails?.actualDepartureDate +
        detail?.DepartureDetails?.actualDepartureTime,
      scheduled:
        detail?.DepartureDetails?.scheduledDepartureDate +
        detail?.DepartureDetails?.scheduledDepartureTime,
    };
    const delay = !detail?.DepartureDetails?.departed
      ? stationArrivalTimeStamp.actual > stationArrivalTimeStamp.scheduled
      : stationDepartureTimeStamp.actual > stationDepartureTimeStamp.scheduled;
    return (
      <View style={styles.stationContainer} testID="footer_bottomsheet_station_container">
        <Text style={[styles.stationTitle, { textAlign: alignment }]}>{title}</Text>
        <Text
          style={[styles.stationName, styles.boldFamily, { textAlign: alignment }]}
          numberOfLines={1}
        >
          {detail?.Station?.name}
        </Text>
        <Text style={[styles.stationStatus, { textAlign: alignment }]}>
          {
            stationStatus[
              (detail?.ArrivalDetails?.arrived ? 1 : 0) +
                (detail?.DepartureDetails?.departed ? 1 : 0)
            ]
          }
          <Text style={[styles.boldFamily, { color: delay ? colors.red : colors.successGreen }]}>
            {detail?.DepartureDetails?.departed
              ? detail?.DepartureDetails?.actualDepartureTime
              : detail?.ArrivalDetails?.actualArrivalTime}
          </Text>
        </Text>
        <Text style={[styles.platform, { textAlign: alignment }]}>
          {labels.PLATFORM_BS}
          <Text style={styles.extraBoldText}>{expectedPlatformNo}</Text>
        </Text>
      </View>
    );
  };

  const renderCurrentCircle = () => (
    <View>
      {started && !terminated && (
        <React.Fragment>
          <View style={styles.downBranch} />
          <View
            style={[
              styles.currentCircle,
              { borderColor: delay ? colors.red : colors.successGreen },
            ]}
          />
          <View
            style={[
              styles.outerCurrentCircle,
              { borderColor: `${delay ? colors.red : colors.successGreen}4D` },
            ]}
          />
          <View
            style={[
              styles.delayIndicator,
              {
                backgroundColor: !delay ? colors.successGreen : colors.red,
                ...(distanceCovered / parseInt(totalDistance) > 0.5
                  ? { right: 7.5 }
                  : { left: 7.5 }),
              },
            ]}
          >
            <Text numberOfLines={1} style={styles.delayIndicatorText}>
              {!delay ? labels.ONTIME : `${delayInMins.string}${labels.DELAY}`}
            </Text>
          </View>
        </React.Fragment>
      )}
    </View>
  );

  return (
    <Animated.View style={{ transform: [{ translateY: animatedBottomSheet }] }}>
      <TouchableWithoutFeedback onPress={toggleFSVisibility}>
        <View
          style={styles.container}
          onLayout={(event) => {
            find_dimesions(event.nativeEvent.layout);
          }}
        >
          <FooterCommonComponent
            lastAPIFetched={lastAPIFetched}
            currentStationDetail={currentStationDetail}
            prevStationDetail={prevStationDetail}
            onRefresh={_onRefresh}
            trainStatus={trainStatus}
            trainsLiveGPSSpeed={trainsLiveGPSSpeed}
          />
          <View style={styles.stationsContainer}>
            {renderStation(stations[0])}
            {renderSeperator()}
            {renderStation(stations[1])}
            {started && !terminated && (
              <View style={styles.timeLeft}>
                <Text style={[styles.platform, { textAlign: 'right' }]}>
                  {labels.TIME_LEFT}
                  <Text style={[styles.extraBoldText, { color: colors.goldenYellow17 }]}>
                    {`  ${timeToNextStation.stringValues.hours}:${timeToNextStation.stringValues.minutes} m`}
                  </Text>
                </Text>
              </View>
            )}
          </View>
          <View style={styles.barContainer}>
            <View style={styles.barStationInfoContainer}>
              <Text style={[styles.barStationName, styles.boldFamily]} numberOfLines={1}>
                {`${firstStationDetail?.Station?.name}(${firstStationDetail?.Station?.code})`}
              </Text>
              <Text style={[styles.barStationName, styles.boldFamily]} numberOfLines={1}>
                {`${lastStationDetail?.Station?.name}(${lastStationDetail?.Station?.code})`}
              </Text>
            </View>
            <View style={styles.bar}>
              <View style={styles.barEndpoints} />
              <View style={[styles.travelledBar, { width: distanceCoveredWidth }]} />
              {renderCurrentCircle()}
              <View style={[styles.untravelledBar, { width: remainingDistanceWidth }]} />
              <View style={styles.barEndpoints} />
            </View>
            <View style={styles.barStationInfoContainer}>
              <Text style={styles.barStationTime}>
                {firstStationDetail?.ArrivalDetails?.scheduledArrivalTime}
              </Text>
              <Text style={styles.days}>
                {noOfDays > 1 && (
                  <Text style={styles.boldFamily}>
                    {`(+${noOfDays - 1}${noOfDays - 1 === 1 ? labels.DAY : labels.DAYS}) `}
                  </Text>
                )}
                {lastStationDetail?.ArrivalDetails?.scheduledArrivalTime}
              </Text>
            </View>
            {started && !terminated && (
              <Text style={styles.lastLine}>
                {labels.REMAINING}
                <Text style={styles.extraBoldText}>{` ${
                  parseInt(totalDistance) - distanceCovered
                }km `}</Text>
                {'in '}
                <Text style={styles.extraBoldText}>{remainingTime.shortString}</Text>
              </Text>
            )}
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Animated.View>
  );
};

export default FooterBottomSheet;

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.white,
    ...Platform.select({
      android: { height: BOTTOMSHEET_HEIGHT },
      ios: { height: BOTTOMSHEET_HEIGHT - 30 },
    }),
  },
  barContainer: {
    marginHorizontal: 20,
    paddingHorizontal: 15,
    paddingVertical: 18,
    height: 100,
    backgroundColor: colors.grayBg,
    borderRadius: 5,
  },
  barStationInfoContainer: {
    flexDirection: 'row',
    paddingBottom: 5,
    justifyContent: 'space-between',
  },
  bar: {
    flexDirection: 'row',
    marginTop: 6,
    marginBottom: 2,
  },
  barStationName: {
    color: colors.black,
    fontSize: 13,
    maxWidth: '50%',
  },
  boldFamily: { fontFamily: fonts.bold },
  barEndpoints: {
    width: 1,
    height: 8,
    backgroundColor: colors.blue6,
    alignSelf: 'center',
  },
  barStationTime: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  },
  days: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  },
  outerCurrentCircle: {
    borderWidth: 1,
    width: 24,
    height: 24,
    borderRadius: 12,
    position: 'absolute',
    top: -4.5,
    left: -4.5,
  },
  downBranch: {
    position: 'absolute',
    width: 1,
    height: 30,
    backgroundColor: colors.gray11,
    left: 7.5,
    top: 7.5,
  },
  currentCircle: {
    width: 15,
    height: 15,
    borderRadius: 7.5,
    borderWidth: 3.5,
    backgroundColor: colors.white,
  },
  delayIndicator: {
    position: 'absolute',
    paddingHorizontal: 6,
    paddingVertical: 2,
    top: 30,
    borderRadius: 8,
  },
  delayIndicatorText: {
    fontSize: 10,
    textAlign: 'center',
    fontStyle: 'italic',
    minWidth: 70,
    color: colors.white,
  },
  stationsContainer: {
    height: 160,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.lightGrey,
    borderTopWidth: 2,
    ...Platform.select({
      ios: { marginTop: 15 },
    }),
  },
  timeLeft: {
    position: 'absolute',
    right: 80,
    top: 95,
  },
  travelledBar: {
    backgroundColor: colors.successGreen,
    height: 5,
    alignSelf: 'center',
  },
  untravelledBar: {
    alignSelf: 'center',
    height: 5,
    backgroundColor: colors.grey19,
  },
  lastLine: {
    marginTop: 30,
    fontFamily: fonts.regular,
    color: colors.black,
    fontSize: 13,
    textAlign: 'center',
  },
  extraBoldText: { fontFamily: fonts.black },
  seperator: {
    alignSelf: 'center',
    backgroundColor: colors.grey19,
    height: 2,
    flex: 1,
  },
  stationContainer: {
    paddingHorizontal: 35,
    maxWidth: '50%',
  },
  stationTitle: {
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    fontSize: 12,
  },
  stationRightContainer: { marginLeft: 'auto' },
  stationName: {
    color: colors.black,
    fontSize: 15,
    marginVertical: 7,
  },
  platform: {
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    marginVertical: 7,
    fontSize: 13,
  },
  stationStatus: {
    fontFamily: fonts.regular,
    fontSize: 13,
    color: colors.darkGrey,
  },
});
