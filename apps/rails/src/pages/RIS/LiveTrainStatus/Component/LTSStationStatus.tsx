import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import isEmpty from 'lodash/isEmpty';
import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { labels } from '../constants';
import { CurrentStation, IntermediateStation, StoppingStation } from '../interface';
import { getDelay } from './commonUtils';
import redClockIcon from '@mmt/legacy-assets/src/lts_red_clock_icon.webp';
import greenClockIcon from '@mmt/legacy-assets/src/lts_green_clock_icon.webp';

interface Props {
  currentStationDetail: CurrentStation;
  prevStationDetail: StoppingStation | IntermediateStation;
  isInPIPMode: boolean;
  trainStatus: {
    started: boolean;
    terminated: boolean;
  };
}

const LTSStationStatus = ({
  currentStationDetail: { StoppingStation, IntermediateStation },
  trainStatus: { started, terminated },
  prevStationDetail,
  isInPIPMode = false,
}: Props) => {
  const stationStatuses = ['Approaching station ', 'Arrived at station ', 'Crossed station '];
  const delayInMins = getDelay(
    IntermediateStation?.Station?.code ? IntermediateStation : StoppingStation,
  );
  const delay = delayInMins?.value !== 0;
  const clockIcon = delay ? redClockIcon : greenClockIcon;

  const renderMessage = () => {
    const station: StoppingStation | IntermediateStation = isEmpty(IntermediateStation)
      ? StoppingStation
      : IntermediateStation;
    const isCancelled = station?.MetaDetails?.CancelledStation;
    const isDiverted = station?.MetaDetails?.DivertedStation;
    const isCancelledOrDiverted = isCancelled || isDiverted;

    const stationStatus = isCancelledOrDiverted
      ? isCancelled
        ? labels.TRAIN_CANCELLED
        : labels.TRAIN_DIVERTED
      : !started || terminated
      ? terminated
        ? labels.TRAIN_TERMINATED
        : labels.TRAIN_NOT_STARTED
      : stationStatuses[
          (station?.ArrivalDetails?.arrived ? 1 : 0) + (station?.DepartureDetails?.departed ? 1 : 0)
        ];
    const finalStation = stationStatus === stationStatuses[0] ? prevStationDetail : station;
    const isFinalStationCancelled = finalStation?.MetaDetails?.CancelledStation;
    const isFinalStationDiverted = finalStation?.MetaDetails?.DivertedStation;
    const finalStationStatus =
      isFinalStationCancelled || isFinalStationDiverted
        ? isFinalStationCancelled
          ? labels.TRAIN_CANCELLED
          : labels.TRAIN_DIVERTED
        : !started || terminated
        ? terminated
          ? labels.TRAIN_TERMINATED
          : labels.TRAIN_NOT_STARTED
        : stationStatuses[
            (finalStation?.ArrivalDetails?.arrived ? 1 : 0) +
              (finalStation?.DepartureDetails?.departed ? 1 : 0)
          ];
    const isIntermediate =
      isEmpty(finalStation?.MetaDetails) || finalStation?.MetaDetails?.StoppingStation === false;
    const stationName = isIntermediate
      ? `${finalStation?.Station?.name} (No-halt)`
      : finalStation?.Station?.name;

    const stationCode = isIntermediate
      ? `${finalStation?.Station?.code} (No-halt)`
      : finalStation?.Station?.code;

    const time = finalStation?.DepartureDetails?.departed
      ? finalStation?.DepartureDetails?.actualDepartureTime
      : finalStation?.ArrivalDetails?.actualArrivalTime;

    return (
      <View
        style={styles.textContainer}
        testID="train_list_sticky_header_first_container_status_container_text_container"
      >
        <Text
          style={[
            styles.normalMessage,
            { color: delay ? colors.red : colors.successGreen },
            isInPIPMode && styles.pipStationStatus,
          ]}
          numberOfLines={isInPIPMode ? 2 : 5}
          testID="train_list_sticky_header_first_container_status_container_text"
        >
          {finalStationStatus}
          <Text
            style={styles.boldMessage}
            testID={`train_list_sticky_header_first_container_status_container_text_bold_${stationCode}`}
          >
            {isInPIPMode ? stationCode : stationName}
          </Text>
          {isEmpty(time) ? (
            ''
          ) : (
            <Text>
              {' at '}
              <Text
                style={styles.boldMessage}
                testID={`train_list_sticky_header_first_container_status_container_text_bold_time_${time}`}
              >
                {time}
              </Text>
            </Text>
          )}
          {'.'}
          {isInPIPMode && (
            <Text
              style={styles.pipNormalMessage}
              testID={`train_list_sticky_header_first_container_status_container_text_pip_normal_message_${finalStation?.Station?.name}`}
            >{` (${finalStation?.Station?.name})`}</Text>
          )}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.innerContainer}>
      {!isInPIPMode && (
        <Image
          style={styles.clockImage}
          source={clockIcon}
          testID="train_list_sticky_header_first_container_status_container_clock_image"
        />
      )}
      {renderMessage()}
    </View>
  );
};

export default LTSStationStatus;

const styles = StyleSheet.create({
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 0,
  },
  clockImage: {
    width: 11,
    height: 13.5,
    marginRight: 8,
    marginLeft: 12,
    alignSelf: 'center',
  },
  textContainer: {
    flex: 3,
    alignSelf: 'center',
    marginRight: 5,
  },
  normalMessage: {
    fontFamily: fonts.medium,
    fontSize: 12,
    lineHeight: 16,
  },
  pipNormalMessage: {
    fontFamily: fonts.medium,
    fontSize: 9,
    lineHeight: 14,
    textTransform: 'capitalize',
  },
  boldMessage: {
    fontFamily: fonts.bold,
  },
  row: {
    flexDirection: 'row',
  },
  pipStationStatus: {
    fontSize: 9,
    lineHeight: 4,
  },
});
