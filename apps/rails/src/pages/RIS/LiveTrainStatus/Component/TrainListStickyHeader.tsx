import React, { useState } from 'react';
import H<PERSON><PERSON><PERSON>iew from 'react-native-htmlview';
import { View, Text, Image, StyleSheet, LayoutChangeEvent, Platform } from 'react-native';
import TextTicker from 'react-native-text-ticker';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {
  PAGE_RIS_LTS_INFO,
  trackOmnitureLTSClickEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import { labels, omnitureLabels, TRACKING_OPTIONS as options, trainListLabels } from '../constants';
import {
  CancellationAndDiversionInfo,
  CurrentStation,
  IntermediateStation,
  StoppingStation,
  TrainDetail,
} from '../interface';
import LTSStationStatus from './LTSStationStatus';

import downArrow from '@mmt/legacy-assets/src/lts_downArrow.webp';
const FIRST_ROW_HEIGHT = 50;

const cancelledStylesheet = StyleSheet.create({
  q: {},
  p: { fontFamily: fonts.medium, fontSize: 11, color: colors.white, lineHeight: 16 },
  s: { fontWeight: 'bold' },
});
const divertedStylesheet = StyleSheet.create({
  q: {},
  p: { fontFamily: fonts.medium, fontSize: 11, color: colors.darkBrown2, lineHeight: 16 },
  s: { fontWeight: 'bold', color: colors.darkBrown2 },
});

interface Props {
  toggleTOBSVisibility: () => {
    //
  };
  toggleSDBSVisibility: () => {
    //
  };
  outsideTrain: boolean;
  startDate: string;
  showTrackingOption: boolean;
  cancellationAndDiversionInfo: CancellationAndDiversionInfo;
  currentStationDetail: CurrentStation;
  prevStationDetail: StoppingStation | IntermediateStation;
  trainDetails: TrainDetail;
  isInsideTrain: boolean;
}

const TrainListStickyHeader = ({
  toggleTOBSVisibility,
  toggleSDBSVisibility,
  outsideTrain,
  startDate,
  showTrackingOption,
  cancellationAndDiversionInfo,
  currentStationDetail,
  prevStationDetail,
  trainDetails,
  isInsideTrain,
}: Props) => {
  const [containerWidth, setContainerWidth] = useState(0);

  const trainStatus = {
    started: trainDetails?.Departed,
    terminated: trainDetails?.Terminated,
  };

  const onStartDateClick = () => {
    toggleSDBSVisibility();
  };

  const onTrackingClick = () => {
    toggleTOBSVisibility();
    trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, omnitureLabels.LTS_OFFLINE_DROPDOWN_CLICKED);
  };

  const dateLabelText =
    showTrackingOption || startDate?.length > 20
      ? startDate + '          ' + startDate + '          ' + startDate
      : startDate;
  return (
    <View>
      {(cancellationAndDiversionInfo?.Diverted || cancellationAndDiversionInfo?.Cancelled) && (
        <View testID="train_list_sticky_header_error_modal_container">
          <View
            style={
              cancellationAndDiversionInfo.Cancelled
                ? styles.errorModalCancelled
                : styles.errorModalDiverted
            }
            testID="train_list_sticky_header_error_modal_view"
          >
            <HTMLView
              value={cancellationAndDiversionInfo?.Message}
              stylesheet={
                cancellationAndDiversionInfo.Cancelled ? cancelledStylesheet : divertedStylesheet
              }
              testID="train_list_sticky_header_error_modal_htmlview"
            />
          </View>
        </View>
      )}
      <View
        style={styles.firstContainer}
        onLayout={(event: LayoutChangeEvent) => {
          setContainerWidth(event.nativeEvent.layout.width);
        }}
        testID="train_list_sticky_header_first_container"
      >
        <TouchableRipple
          onPress={onStartDateClick}
          style={Platform.select({
            ios: { width: showTrackingOption ? '65%' : '100%' },
          })}
          testID="train_list_sticky_header_first_container_touchable_ripple"
        >
          <View
            style={[styles.firstRowStyle, styles.leftContainer, styles.startDateContainer]}
            testID="train_list_sticky_header_first_container_view"
          >
            <Text
              style={styles.startDateLabel}
              testID="train_list_sticky_header_first_container_text"
            >
              {labels.STARTING}
            </Text>
            <View
              style={styles.marqueeContainer}
              testID="train_list_sticky_header_first_container_marquee_container"
            >
              {showTrackingOption || dateLabelText?.length > 20 ? (
                <TextTicker
                  style={[
                    styles.containerValue,
                    {
                      width:
                        containerWidth === 0
                          ? 110
                          : containerWidth - (showTrackingOption ? 250 : 120),
                    },
                  ]}
                  duration={15000}
                  loop
                  bounce
                  repeatSpacer={10}
                  marqueeDelay={0}
                  testID="train_list_sticky_header_first_container_text_ticker"
                >
                  {dateLabelText}
                </TextTicker>
              ) : (
                <Text
                  style={[styles.containerValue, styles.dateLabelText]}
                  testID="train_list_sticky_header_first_container_text_ticker_text"
                >
                  {dateLabelText}
                </Text>
              )}

              <View
                style={styles.arrowContainer}
                testID="train_list_sticky_header_first_container_arrow_container"
              >
                <Image source={downArrow} style={styles.arrowIcon} />
              </View>
            </View>
          </View>
        </TouchableRipple>
        {showTrackingOption && (
          <TouchableRipple
            onPress={onTrackingClick}
            style={Platform.select({
              ios: { width: '35%' },
            })}
            testID="train_list_sticky_header_first_container_tracking_container_touchable_ripple"
          >
            <View
              style={[styles.firstRowStyle, styles.rightContainer]}
              testID="train_list_sticky_header_first_container_tracking_container_view"
            >
              <View
                style={styles.trackingContainer}
                testID="train_list_sticky_header_first_container_tracking_container_view_two"
              >
                <Image
                  source={options[outsideTrain ? 0 : 1].icon}
                  style={styles.trackingIcon}
                  testID="train_list_sticky_header_first_container_tracking_container_icon"
                />
                <Text
                  style={styles.containerValue}
                  testID="train_list_sticky_header_first_container_tracking_container_text"
                >
                  {options[outsideTrain ? 0 : 1].label}
                </Text>
              </View>
              <Image
                source={downArrow}
                style={styles.arrowIcon}
                testID="train_list_sticky_header_first_container_tracking_container_arrow_icon"
              />
            </View>
          </TouchableRipple>
        )}
      </View>
      {isInsideTrain && (
        <View
          style={styles.statusContainer}
          testID="train_list_sticky_header_first_container_status_container"
        >
          <LTSStationStatus
            currentStationDetail={currentStationDetail}
            prevStationDetail={prevStationDetail}
            trainStatus={trainStatus}
            isInPIPMode={false}
          />
        </View>
      )}
      <View
        style={styles.secondContainer}
        testID="train_list_sticky_header_first_container_status_container_view"
      >
        <View
          style={styles.trackingContainer}
          testID="train_list_sticky_header_first_container_status_container_tracking_container_view"
        >
          <Text
            style={[styles.trainListLabelsValue, styles.trainListLabelsStartValue]}
            testID="train_list_sticky_header_first_container_status_container_text_start_value"
          >
            {trainListLabels[0]}
          </Text>
          <Text
            style={styles.trainListLabelsValue}
            testID="train_list_sticky_header_first_container_status_container_text_end_value"
          >
            {trainListLabels[1]}
          </Text>
        </View>
        <View
          style={styles.trackingContainer}
          testID="train_list_sticky_header_first_container_status_container_tracking_container_view_two"
        >
          <Text
            style={styles.trainListLabelsValue}
            testID="train_list_sticky_header_first_container_status_container_text_middle_value"
          >
            {trainListLabels[2]}
          </Text>
          <Text
            style={[styles.trainListLabelsValue, styles.trainListLabelsEndValue]}
            testID="train_list_sticky_header_first_container_status_container_text_end_value_text"
          >
            {trainListLabels[3]}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  firstContainer: {
    marginHorizontal: 16,
    marginTop: 10,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  secondContainer: {
    width: '100%',
    height: 45,
    backgroundColor: colors.black,
    paddingHorizontal: 13,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  firstRowStyle: {
    height: FIRST_ROW_HEIGHT,
    borderColor: colors.lightGrey,
    borderWidth: 2,
    borderRadius: 5,
  },
  leftContainer: {
    justifyContent: 'center',
    paddingLeft: 8,
  },
  rightContainer: {
    marginLeft: 6,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  startDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  startDateLabel: {
    fontFamily: fonts.regular,
    fontSize: 14,
    color: colors.headerColor,
    marginRight: 10,
  },
  containerValue: {
    marginHorizontal: 10,
    color: colors.azure,
    fontFamily: fonts.bold,
    fontSize: 14,
  },
  marqueeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  arrowContainer: {
    width: 12,
    paddingRight: 16,
    paddingLeft: 10,
  },
  arrowIcon: {
    width: 12,
    height: 9,
    alignSelf: 'center',
  },
  trackingContainer: { flexDirection: 'row' },
  trackingIcon: { width: 16, height: 16 },
  trainListLabelsValue: {
    color: colors.white,
    fontFamily: fonts.black,
    fontSize: 14,
  },
  trainListLabelsStartValue: { marginRight: 36 },
  trainListLabelsEndValue: { marginLeft: 14 },
  errorModalCancelled: {
    position: 'absolute',
    width: '94%',
    backgroundColor: colors.red,
    minHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
    top: 170,
    left: 13,
    paddingLeft: 4,
    paddingRight: 5,
    borderRadius: 5,
  },
  errorModalDiverted: {
    position: 'absolute',
    width: '94%',
    backgroundColor: colors.dateLabelColor,
    minHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
    top: 170,
    left: 13,
    paddingLeft: 4,
    paddingRight: 5,
    borderRadius: 5,
  },
  dateLabelText: {
    width: '65%',
    textAlign: 'right',
  },
  statusContainer: {
    borderColor: colors.lightGrey,
    borderWidth: 2,
    borderRadius: 5,
    marginHorizontal: 16,
    marginBottom: 8,
    padding: 4,
  },
  clockImage: {
    width: 15,
    height: 18,
    marginRight: 8,
    marginLeft: 12,
    alignSelf: 'center',
  },
});

export default TrainListStickyHeader;
