import React from 'react';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { View, Text, Image, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import TrainIcon from '@mmt/legacy-assets/src/lts_white_trainIcon.webp';

interface Props {
  trainDetails: {
    Number: number;
    Name: string;
  };
}

const TrainHeader = (props: Props) => {
  const { trainDetails } = props;
  const trainNumber = trainDetails.Number;
  const trainName = trainDetails.Name;

  // In case of any change in the dimension of this component pls update dimensions.HEADER value in the constants.ts file accordingly as it is required for autoscroll feature
  return (
    <View style={styles.container}>
      <LinearGradient
        style={styles.iconGradient}
        colors={[colors.seance, colors.governorBay]}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.iconContainer}>
          <Image style={styles.icon} source={TrainIcon} />
        </View>
      </LinearGradient>
      <View style={styles.text}>
        <Text style={styles.trainNumber}>{'#' + trainNumber}</Text>
        <Text style={styles.trainName}>{trainName}</Text>
      </View>
    </View>
  );
};

export default TrainHeader;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingLeft: 16,
    paddingTop: 25,
    paddingBottom: 10,
  },
  iconGradient: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: { paddingRight: 2 },
  icon: { width: 21, height: 24 },
  text: { flex: 1, marginLeft: 10 },
  trainNumber: {
    fontFamily: fonts.black,
    color: colors.black,
    fontSize: 22,
  },
  trainName: {
    color: colors.defaultTextColor,
    fontSize: 16,
  },
});
