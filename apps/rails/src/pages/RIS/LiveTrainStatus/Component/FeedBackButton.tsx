import React from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels } from '../constants';

import feedBackIcon from '@mmt/legacy-assets/src/lts_feedback_icon.webp';

const FeedBackButton = () => (
  <View style={styles.container}>
    <Image style={styles.icon} source={feedBackIcon} />
    <Text style={styles.text}>{labels.FEEDBACK}</Text>
  </View>
);

export default FeedBackButton;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderColor: colors.green,
    borderWidth:1,
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 11,
    height: 10,
  },
  text: {
    fontFamily: fonts.bold,
    color: colors.green,
    fontSize: 12,
    marginLeft: 5,
  },
});
