/* eslint-disable */
import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  SectionList,
  ScrollView,
  SafeAreaView,
  BackHandler,
  Platform,
  StyleSheet,
  AppState,
  Animated,
  Easing,
  PermissionsAndroid,
  Linking,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import isEmpty from 'lodash/isEmpty';
import LinearGradient from 'react-native-linear-gradient';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import {
  AbConfigKeyMappings,
  getPokusConfig,
  getPokusConfigWaitingPromise,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule.js';
import { Actions } from '../../../../navigation/railsNavigation.ts';
import { isBooker } from '../../../../Utils/RisUtils';
import {
  trackOmnitureLTSClickEvent,
  trackOmnitureAlarmLoadEvent,
  trackOmnitureLTSVisitEvent,
  RIS_LTS_STATION_BOTTOMSHEET_DISMISSED,
  LTS_TRAIN_CANCELLED,
  LTS_TRAIN_PARTIALLY_CANCELLED,
  LTS_TRAIN_SCHEDULED,
  LTS_TRAIN_RUNNING,
  LTS_TRAIN_DIVERTED,
  RIS_LTS_STATION_ALARM_BOTTOMSHEET,
  PAGE_RIS_LTS_INFO,
  EVENT_RIS_LTS_REFRESH_CLICK,
  trackMealsOmnitureLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import LiveTrainStatusHeader from '@mmt/rails/src/pages/RIS/LiveTrainStatus/Component/LiveTrainStatusHeader';
import ErrorView from '@mmt/rails/src/pages/RIS/LiveTrainStatus/Component/ErrorView.tsx';
import DateLabel from './DateLabel';
import Station from './Station';
import Disclaimer from './Disclaimer';
import FooterMessage from './FooterMessage';
import DateBottomSheet from './DateBottomSheet';
import FooterBottomSheet from './FooterBottomSheet';
import TrackingOptionBottomSheet from './TrackingOptionBottomSheet';
import TrainListStickyHeader from './TrainListStickyHeader';
import Switcher from './Switcher';
import FeedBackBottomSheet from './FeedBackBottomSheet';
import StarBasedFeedbackBottomSheet from './StarBasedFeedbackBottomSheet';
import CTInfoBottomSheet from './CTInfoBottomSheet';
import AlarmSetterBottomSheet from './AlarmSetterBottomSheet';
import LocationPermissionPopup from './LocationPermission';
import {
  createStartDateDetails,
  onSendFeedback,
  formatDateTime,
  isFutureDate,
  checkForTracking,
} from './commonUtils';
import {
  dimensions,
  switcher,
  AppStates,
  viewStates,
  asyncStorageKeys,
  BOTTOMSHEET_HEIGHT,
  setIntervalTime,
  labels,
} from '../constants';
import { initializeLTS } from '../Util/LiveTrainStatusActions';
import {
  getDataFromStorage,
  setDataInStorage,
  removeDataFromStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import {
  requestForLocationPermission,
} from '@mmt/legacy-commons/Helpers/locationHelper';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  getIsLTSAlarmEnabled,
  getOfflineSwitchingLogic,
  getRailsMealsLtsDetailsPokus,
} from '../../../../RailsAbConfig';
import { useReferredState } from '../Hooks/useReferredState';
import { PNRCrossSellBottomSheet } from '../../PnrStatus/Components/PNRCrossSells';
import { trackClickEventProp61 } from 'packages/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  LTS_DETAILS_FOOD_IN_TRAIN_LOAD,
  LTS_DETAILS_MEALS_ORDER_NOW_STICKY_CLICKED,
  LTS_DETAILS_MEALS_BS_SHOWN_TIMER,
  showMealsBottomSheet,
} from 'apps/rails/src/Utils/RailsConstant';
import MealBottomComponent from '../../MealsOnTrain/Components/MealBottomComponent';
import MealBottomSheet from '../../MealsOnTrain/Components/MealBottomSheet';
import { INTERACTED_WITH_MEALS_LTS } from '../../MealsOnTrain/Constants/MealsConstants';
import PropTypes from 'prop-types';
import { getMealsNewFlow, showMealsWebView } from '@mmt/rails/src/RailsAbConfig';
import BottomSheetModalTrain from '../../../Common/BottomSheetModalTrain';

const isMealAvailableForStation = (station) => {
  const mealsWebView = showMealsWebView();
  const showNewMeals = getMealsNewFlow();
  const isZomato = mealsWebView || showNewMeals;
  return isZomato ? station?.ZomatoMealsAvailable : station?.MealsAvailable;
};

function LiveTrainStatusPage(props) {
  const {
    firstStationDetail,
    lastStationDetail,
    nextStationDetail,
    currentStationDetail,
    previousStationDetail,
    trainDetails,
    disclaimer,
    stationsSectionList,
    showLoader,
    ltsViewState,
    ltsErrorMsg,
    otherOptions,
    // lastAPIFetched,
    lastUpdated,
    cancellationAndDiversionInfo,
    trainsLiveGPSSpeed,
    dataSource,
    ltsCrossSellData,
    upComingPnrs,
  } = props;
  const scrollToRef = useRef(null);
  const timer = useRef(null);
  const timer2 = useRef(null);
  const userType = useRef(undefined);
  const autoRefresh = useRef(null);
  const countOfChangeInTrackingOption = useRef(0);
  const manualRefreshCount = useRef(0);
  const prevScrollOffset = useRef(0);
  const animatedBottomSheet = useRef(new Animated.Value(BOTTOMSHEET_HEIGHT)).current;
  const continueToBookingOnAppStateChange = useRef(false);
  const initTrackingConfig = useRef({});
  const timerId = useRef(null);
  const numberOfTimesBottomSheetShown = useRef(0);
  const [heightArr, setHeightArr] = useState([]);
  const [isFooterSheetVisible, setFooterSheetVisibility] = useState(false);
  const [isStartDateSelectVisible, setStartDateSelectVisibility] = useState(false);
  const [alarmVisibility, setAlarmVisibility] = useState(false);
  const [currentStationDetailsForAlarm, setCurrentStationDetailsForAlarm] = useState({});
  const [isInsideTrain, setInsideTrain] = useState(undefined);
  const [isTrackingOptionsVisible, setTrackingOptionsVisibility] = useState(false);
  const [isSwitcherVisible, setSwitcherVisibility] = useState(false);
  const [feedBackVisibility, feedBackVisibilityRef, setFeedBackVisibility] =
    useReferredState(false);
  const [stickHeader, setStickHeader] = useState(false);
  const [isOfflineTrackingEnabled, setIsOfflineTrackingEnabled] = useState(true);
  const [offlineErrorData, setOfflineErrorData] = useState(labels.LTS_OFFLINE_ERROR);
  const [appState, setAppState] = useState(AppStates.ACTIVE);
  const [locationRequestNeeded, setLocationRequestNeeded] = useState(false);
  const [locationRequestOnSettingsNeeded, setLocationRequestOnSettingsNeeded] = useState(false);
  const [ltsFeedbackPokus, setLtsFeedbackPokus] = useState(false);
  const [showMealsComponent, setShowMealsComponent] = useState(true);
  const [railsMealPokus, setRailsMealPokus] = useState(false);
  const [isShownFixedMealBottom, setIsShownFixedMealBottom] = useState(false);
  const [showMealBottomSheet, setShowMealBottomSheet] = useState(false);
  const [backButtonCount, setBackButtonCount] = useState(0);

  const isCTInfoEnabled = useRef(false);
  const [isCTInfoVisible, setCTInfoVisibility] = useState(false);
  const [keys, setKeys] = useState('1234');
  const [showReturnTicketBottomSheet, setShowReturnTicketBottomSheet] = useState(false);

  const toggleKeysForStationReRender = () => setKeys(keys.split('').reverse().join(''));

  let requestDetails = props.requestDetails;
  const isAlarmEnabled = getIsLTSAlarmEnabled();
  const [switcherData, setSwitcherData] = useState(switcher.DEFAULT_MOUNT);

  const updateIsInsideTrainStatus = async (isInsideTrainStatus) => {
    try {
      await setDataInStorage(asyncStorageKeys.RIS_LTS_OPTIONS, {
        trainNumber: requestDetails.trainDetail?.trainNumber,
        isInsideTrainStatus: isInsideTrainStatus,
      });
      setInsideTrain(isInsideTrainStatus);
    } catch (err) {
      console.log('Error in saving isInsideTrainInfo', err);
    }
  };

  useEffect(() => {
    async function pokusCall() {
      const railsMealPokusValue = await getRailsMealsLtsDetailsPokus();
      setRailsMealPokus(railsMealPokusValue);
      if (railsMealPokusValue) {
        stationsSectionList.map((section) => {
          section.data.map((station) => {
            if (isMealAvailableForStation(station)) {
              trackMealsOmnitureLoadEvent(LTS_DETAILS_FOOD_IN_TRAIN_LOAD);
              setIsShownFixedMealBottom(true);
            }
          });
        });
      }
    }
    pokusCall();
  }, [stationsSectionList]);

  useEffect(() => {
    if (cancellationAndDiversionInfo?.Diverted) {
      trackOmnitureLTSVisitEvent(LTS_TRAIN_DIVERTED);
    } else if (cancellationAndDiversionInfo?.Cancelled && !cancellationAndDiversionInfo.Tokens) {
      trackOmnitureLTSVisitEvent(LTS_TRAIN_CANCELLED);
    } else if (cancellationAndDiversionInfo?.Cancelled) {
      trackOmnitureLTSVisitEvent(LTS_TRAIN_PARTIALLY_CANCELLED);
    } else if (otherOptions?.showTrainSchedule) {
      trackOmnitureLTSVisitEvent(LTS_TRAIN_SCHEDULED);
    } else if (cancellationAndDiversionInfo) {
      trackOmnitureLTSVisitEvent(LTS_TRAIN_RUNNING);
    }
  }, [cancellationAndDiversionInfo, otherOptions?.showTrainSchedule]);

  const continueToTracking = () => {
    setLocationRequestNeeded(false);
    setLocationRequestOnSettingsNeeded(false);
    isInsideTrain !== undefined && setSwitcherVisibility(true);
    let refreshCount = 0;
    props.initTracking(
      requestDetails,
      initTrackingConfig.current?.refresh,
      initTrackingConfig.current?.autoRefresh,
      refreshCount,
    );
    clearInterval(autoRefresh.current);
    autoRefresh.current = setInterval(() => {
      refreshCount += 1;
      props.initTracking(requestDetails, false, true, refreshCount);
    }, setIntervalTime);
  };

  const requestPermissionRejected = () => {
    continueToTracking();
  };

  const requestPermission = async () => {
    setLocationRequestNeeded(false);
    const givenPerm = await requestForLocationPermission();
    if (givenPerm === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
      setLocationRequestOnSettingsNeeded(true);
      setSwitcherVisibility(false);
    } else {
      continueToTracking();
    }
  };

  const requestPermissionOnSettings = async () => {
    setLocationRequestOnSettingsNeeded(false);
    Linking.openSettings().then(() => {
      continueToBookingOnAppStateChange.current = true;
    });
  };

  useEffect(() => {
    const ltsFeedback = getPokusConfig(PokusLobs.RAIL, AbConfigKeyMappings.railsLtsFeedback, false);
    setLtsFeedbackPokus(ltsFeedback);
    manualRefreshCount.current = 0;
  }, []);

  useEffect(() => {
    if (appState === AppStates.ACTIVE && continueToBookingOnAppStateChange.current) {
      continueToTracking();
      continueToBookingOnAppStateChange.current = false;
    }
  }, [appState]);

  useEffect(() => {
    if (ltsViewState === viewStates.ERROR) {
      setTrackingOptionsVisibility(false);
      setSwitcherVisibility(false);
    }
  }, [ltsViewState]);

  useEffect(() => {
    if (ltsViewState === viewStates.ERROR) {
      setShowMealsComponent(false);
    }
  }, [showMealsComponent]);

  useEffect(() => {
    if (
      appState === AppStates.ACTIVE &&
      !locationRequestNeeded &&
      !locationRequestOnSettingsNeeded
    ) {
      clearInterval(autoRefresh.current);
      autoRefresh.current = setInterval(() => {
        props.initTracking(requestDetails, false, true);
      }, setIntervalTime);
    } else {
      clearInterval(autoRefresh.current);
    }
  }, [appState]);

  useEffect(() => {
    if (!isOfflineTrackingEnabled) {
      updateIsInsideTrainStatus(false).then(() => {
        setTrackingOptionsVisibility(false);
        if (isInsideTrain === true) {
          setSwitcherVisibility(false);
        }
      });
    }
  }, [isOfflineTrackingEnabled]);

  useEffect(() => {
    countOfChangeInTrackingOption.current += 1;
    if (isInsideTrain === false) {
      trackBooker();
    }
    if (isInsideTrain) {
      if (countOfChangeInTrackingOption.current <= 2) {
        setSwitcherData(switcher.SWITCH_TO_INSIDE_MOUNT);
      } else {
        setSwitcherData(switcher.SWITCH_TO_INSIDE);
      }
    } else if (isInsideTrain === false && !otherOptions?.offlineError) {
      if (countOfChangeInTrackingOption.current <= 2) {
        const switcherData = switcher.DEFAULT_MOUNT;
        setSwitcherData(switcherData);
      } else {
        setSwitcherData(switcher.SWITCH_TO_OUTSIDE);
      }
    }
    if (isInsideTrain) {
      // setSwitcherVisibility(true);     // After permissions is resolved, it will become true if isInsideTrain is not undefined
      setTrackingOptionsVisibility(false);
    } else {
      props.clearOfflineError({ offlineTracking: false });
    }
    clearInterval(autoRefresh.current);
    autoRefresh.current = setInterval(() => {
      props.initTracking(requestDetails, false, true);
    }, setIntervalTime);

    return () => {
      clearInterval(autoRefresh.current);
    };
  }, [isInsideTrain]);

  useEffect(() => {
    if (otherOptions?.offlineError) {
      updateIsInsideTrainStatus(false).then(() => {
        setSwitcherVisibility(false);
        setTrackingOptionsVisibility(false);
        setOfflineErrorData(
          otherOptions?.locationPermissionGranted
            ? labels.LTS_OFFLINE_ERROR
            : labels.LTS_OFFLINE_GPS_ERROR,
        );
        timer2.current = setTimeout(() => {
          props.clearOfflineError({ offlineError: false });
        }, 2000);
      });
    }
  }, [otherOptions?.offlineError]);

  useEffect(() => {
    if (otherOptions?.noNetwork && isOfflineTrackingEnabled) {
      updateIsInsideTrainStatus(true);
    }
  }, [otherOptions?.noNetwork, isOfflineTrackingEnabled]);

  useEffect(() => {
    if (otherOptions?.offlineTracking) {
      setDataInStorage(asyncStorageKeys.RAILS_FEEDBACK_BACK_BUTTON, true);
    }
  }, [otherOptions?.offlineTracking]);

  useEffect(() => {
    if (otherOptions?.offlineTracking && isInsideTrain === true) {
      trackBooker();
    }
    if (isInsideTrain && otherOptions?.offlineTracking) {
      setSwitcherVisibility(false);
    }
  }, [otherOptions?.offlineTracking, isInsideTrain]);

  useEffect(() => {
    if (isInsideTrain === false && !(otherOptions?.onlineTracking || otherOptions?.onlineError)) {
      const switcherData =
        countOfChangeInTrackingOption.current <= 2
          ? switcher.DEFAULT_MOUNT
          : switcher.SWITCH_TO_OUTSIDE;

      setSwitcherData(switcherData);
      setSwitcherVisibility(true);
    }
    if (isInsideTrain === false && (otherOptions?.onlineTracking || otherOptions?.onlineError)) {
      setSwitcherVisibility(false);
    }
  }, [otherOptions?.onlineTracking, otherOptions?.onlineError, isInsideTrain]);

  useEffect(() => {
    if (feedBackVisibility) {
      clearInterval(autoRefresh.current);
    }
  }, [feedBackVisibility]);

  useEffect(() => {
    const init = async () => {
      const lastSavedInfo = await getDataFromStorage(asyncStorageKeys.RIS_LTS_OPTIONS);
      if (
        !isEmpty(lastSavedInfo) &&
        !isEmpty(lastSavedInfo?.trainNumber) &&
        lastSavedInfo?.isInsideTrainStatus !== undefined &&
        lastSavedInfo?.trainNumber === requestDetails?.trainDetail?.trainNumber
      ) {
        setInsideTrain(lastSavedInfo?.isInsideTrainStatus);
      }

      if (isFutureDate(requestDetails?.dateDetail?.dateNumeric)) {
        setIsOfflineTrackingEnabled(false);
        await updateIsInsideTrainStatus(false);
      } else {
        setIsOfflineTrackingEnabled(true);
      }

      await getPokusConfigWaitingPromise(2000);
      const tlsV3EnabledPokus = await getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.tlsV3Enabled,
        false,
      );
      const isRisLtsPlotOfflineModesPokus = await getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.isRisLtsPlotCT,
        '{"GPS":1,"CT":1}',
      );
      isCTInfoEnabled.current = await getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.ctInfoEnabled,
        false,
      );
      const isRisLtsPlotOfflineModes = JSON.parse(isRisLtsPlotOfflineModesPokus);
      const isRisLtsPlotGPSPokus = isRisLtsPlotOfflineModes.GPS === 1;
      const isRisLtsPlotCTPokus = isRisLtsPlotOfflineModes.CT === 1 && Platform.OS === 'android';
      if (!(isRisLtsPlotGPSPokus || isRisLtsPlotCTPokus)) {
        setIsOfflineTrackingEnabled(false);
      }

      initializeLTS(requestDetails, tlsV3EnabledPokus, isRisLtsPlotGPSPokus, isRisLtsPlotCTPokus);
      checkForTracking(requestDetails).then((canContinueToTracking) => {
        initTrackingConfig.current = {
          refresh: false,
          autoRefresh: false,
        };
        if (canContinueToTracking) {
          continueToTracking();
        } else {
          if (Platform.OS === 'android') {
            setLocationRequestNeeded(true);
            setSwitcherVisibility(false);
            clearInterval(autoRefresh.current);
          } else {
            requestForLocationPermission().then(() => continueToTracking());
          }
        }
      });
    };
    init();
    const appStateSubscription = AppState.addEventListener('change', (nextAppState) =>
      setAppState(nextAppState),
    );
    BackHandler.addEventListener('hardwareBackPress', onFeedbackBackButtonPressed);

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onFeedbackBackButtonPressed);
      appStateSubscription.remove();
      clearInterval(autoRefresh.current);
      setSwitcherVisibility(false);
      clearTimeout(timer.current);
      clearTimeout(timer2.current);
      props.clearStore();
      animatedBottomSheet.stopAnimation();
      animatedBottomSheet.setValue(0);
    };
  }, []);

  const toggleSDBSVisibility = () => setStartDateSelectVisibility((prevState) => !prevState);

  const toggleFSVisibility = () => {
    if (!isFooterSheetVisible) {
      setFooterSheetVisibility((prevState) => !prevState);
    }
    Animated.timing(animatedBottomSheet, {
      duration: 300,
      toValue: isFooterSheetVisible ? BOTTOMSHEET_HEIGHT : 0,
      easing: Easing.linear,
      useNativeDriver: true,
    }).start(() => {
      if (isFooterSheetVisible) {
        setFooterSheetVisibility((prevState) => !prevState);
      }
    });
  };

  const toggleTOBSVisibility = () => setTrackingOptionsVisibility((prevState) => !prevState);

  const toggleFBBSVisibility = () => setFeedBackVisibility((prevState) => !prevState);

  const toggleAlarmVisibilityAndUpdateStationDetails = (
    estimatedArrivalDate = null,
    estimatedArrivalTime = null,
    currentStationName = null,
    currentStationCode = null,
  ) => {
    if (estimatedArrivalTime && currentStationName) {
      setCurrentStationDetailsForAlarm({
        estimatedArrivalDate: estimatedArrivalDate,
        estimatedArrivalTime: estimatedArrivalTime,
        currentStationName: currentStationName,
        currentStationCode: currentStationCode,
        trainNumber: trainDetails?.Number,
      });
    }
    if (!alarmVisibility) {
      trackOmnitureAlarmLoadEvent(RIS_LTS_STATION_ALARM_BOTTOMSHEET);
    } else {
      trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, RIS_LTS_STATION_BOTTOMSHEET_DISMISSED);
    }

    setAlarmVisibility((prevState) => !prevState);
  };

  const onFeedbackBottomSheetClose = () => {
    autoRefresh.current = setInterval(() => {
      props.initTracking(requestDetails, false, true);
    }, setIntervalTime);
    toggleFBBSVisibility();
  };

  const trackBooker = () => {
    const logic = getOfflineSwitchingLogic();
    if (isEmpty(userType.current)) {
      isBooker().then((isBooker) => {
        userType.current = isBooker ? 'booker' : 'non-booker';
        let param = `${userType.current}|${!isInsideTrain ? 'online' : `offline|${dataSource}`}`;
        param = param + `|LTS_${logic}`;
        trackOmnitureLTSVisitEvent(param);
      });
    } else {
      let param = `${userType.current}|${!isInsideTrain ? 'online' : `offline|${dataSource}`}`;
      param = param + `|LTS_${logic}`;
      trackOmnitureLTSVisitEvent(param);
    }
  };

  const onStartDateSelect = (dateDetail) => {
    requestDetails.dateDetail = dateDetail;
    toggleSDBSVisibility();
    setHeightArr([]);
    props.clearStore();
    setDataInStorage(asyncStorageKeys.INSIDE_MODE_SUCCESS, false);
    if (isFutureDate(requestDetails?.dateDetail?.dateNumeric)) {
      setIsOfflineTrackingEnabled(false);
      updateIsInsideTrainStatus(false).then(() => props.initTracking(requestDetails, false, false));
    } else {
      setIsOfflineTrackingEnabled(true);
      props.initTracking(requestDetails, false, false);
    }
  };

  const onTrackingOptionsSelect = (value) => {
    if ((isInsideTrain === undefined ? false : isInsideTrain) !== value) {
      updateIsInsideTrainStatus(value).then(() => {
        checkForTracking(requestDetails).then((canContinueToTracking) => {
          initTrackingConfig.current = {
            refresh: false,
            autoRefresh: true,
          };
          if (canContinueToTracking) {
            continueToTracking();
          } else {
            if (Platform.OS === 'android') {
              setLocationRequestNeeded(true);
              setSwitcherVisibility(false);
              clearInterval(autoRefresh.current);
            } else {
              requestForLocationPermission().then(() => continueToTracking());
            }
          }
        });
      });
    }
    if (isInsideTrain === undefined) {
      updateIsInsideTrainStatus(value);
    }
    toggleTOBSVisibility();
  };

  const updateHeightArr = (index, height) => {
    setHeightArr((prevArr) => {
      prevArr[index] = height;
      return prevArr;
    });
  };

  const processArr = (arr) => {
    let sum = 0;
    let maxIndex = currentStationDetail.StoppingStation.StopNumber - 2;
    if (currentStationDetail?.IntermediateStation) {
      maxIndex += 1;
    }
    const end = Math.min(arr.length - 1, maxIndex);
    for (let i = 0; i <= end; i += 1) {
      sum += arr[i] ?? 0;
    }
    return sum;
  };

  const triggerScroll = () => {
    timer.current = setTimeout(() => {
      if (scrollToRef?.current !== null) {
        if (
          currentStationDetail?.StoppingStation?.StopNumber > 1 ||
          (currentStationDetail?.StoppingStation?.StopNumber === 1 &&
            currentStationDetail?.IntermediateStation?.StopNumber > 1)
        ) {
          const offset =
            dimensions.HEADER +
            dimensions.SECTION_HEADER * currentStationDetail?.StoppingStation?.SectionIndex +
            processArr(heightArr) -
            100;
          scrollToRef.current.scrollTo({
            y: offset,
            animated: true,
          });
          offset !== 0 && setStickHeader(true);
        }
      }
    }, 150);
  };

  const onFeedbackBackButtonPressed = async () => {
    const timer = await getDataFromStorage(LTS_DETAILS_MEALS_BS_SHOWN_TIMER);
    const interactedWithLTS = await getDataFromStorage(INTERACTED_WITH_MEALS_LTS);
    if (
      isShownFixedMealBottom &&
      backButtonCount === 0 &&
      showMealsBottomSheet(timer) &&
      !interactedWithLTS
    ) {
      setShowMealBottomSheet(true);
      setDataInStorage(LTS_DETAILS_MEALS_BS_SHOWN_TIMER, Date.now());
      setBackButtonCount(1);
      return;
    }
    let feedbackButonFlag = await getDataFromStorage(asyncStorageKeys.RAILS_FEEDBACK_BACK_BUTTON);
    if (feedbackButonFlag && ltsViewState !== viewStates.ERROR) {
      setFeedBackVisibility(true);
      await removeDataFromStorage(asyncStorageKeys.RAILS_FEEDBACK_BACK_BUTTON);
    } else {
      onBackIconPressed();
    }
  };

  const onBackIconPressed = () => {
    const { deeplink } = props;
    if (deeplink) {
      Actions.railsBusCommonLanding(undefined, 'replace');
    } else {
      Actions.pop();
    }
    return true;
  };

  const _onScroll = (event) => {
    const offset = event.nativeEvent.contentOffset.y;
    if (offset > dimensions.HEADER) {
      setStickHeader(true);
    }
    if (offset <= dimensions.HEADER) {
      setStickHeader(false);
    }
    prevScrollOffset.current = offset;
  };

  const _onRefresh = (isManualRefresh) => {
    countOfChangeInTrackingOption.current += 1;
    manualRefreshCount.current += 1;
    props.initTracking(requestDetails, true, false);
    if (isManualRefresh) {
      trackOmnitureLTSClickEvent(
        PAGE_RIS_LTS_INFO,
        `${EVENT_RIS_LTS_REFRESH_CLICK}_${manualRefreshCount.current}`,
      );
    }
  };
  useEffect(() => setStickHeader(false), [ltsViewState]);

  const Loader = () => (
    <View style={{ flex: 1 }}>
      <View style={styles.loadingContainer}>
        <Spinner size={30} color="#008b8b" />
        <Text style={[styles.text, { marginTop: 25 }]}>{'Please wait while we fetch'}</Text>
        <Text style={[styles.text, styles.boldText]}>{'Live Train Status'}</Text>
      </View>
    </View>
  );
  const shouldRenderMealBottomComponent =
    railsMealPokus && isShownFixedMealBottom && showMealsComponent;

  const BottomComponents = () => (
    <React.Fragment>
      {shouldRenderMealBottomComponent && (
        <MealBottomComponent
          closeMealsComponent={closeMealsComponent}
          onOrderMealClicked={trackClickEvent}
        />
      )}
      {!otherOptions?.showTrainSchedule && (
        <FooterMessage
          onPress={toggleFSVisibility}
          lastAPIFetched={lastUpdated}
          onRefresh={_onRefresh}
          currentStationDetail={currentStationDetail}
          prevStationDetail={previousStationDetail}
          trainDetails={trainDetails}
          trainsLiveGPSSpeed={trainsLiveGPSSpeed}
          isInsideTrain={isInsideTrain}
        />
      )}

      {isStartDateSelectVisible && (
        <BottomSheetModalTrain
          onTouchOutside={toggleSDBSVisibility}
          additionalContainerStyle={{ zIndex: 1 }}
          testID="date_select_bottomsheet_modal"
        >
          <DateBottomSheet
            onStartDateSelect={onStartDateSelect}
            startDay={trainDetails?.StartDay}
            toggleSDBSVisibility={toggleSDBSVisibility}
          />
        </BottomSheetModalTrain>
      )}

      {isFooterSheetVisible && (
        <BottomSheetModalTrain
          onTouchOutside={toggleFSVisibility}
          additionalContainerStyle={{ zIndex: 1 }}
          testID="footer_bottomsheet_modal"
        >
          <FooterBottomSheet
            firstStationDetail={firstStationDetail}
            currentStationDetail={currentStationDetail}
            nextStationDetail={nextStationDetail}
            lastStationDetail={lastStationDetail}
            trainDetails={trainDetails}
            toggleFSVisibility={toggleFSVisibility}
            lastAPIFetched={lastUpdated}
            onRefresh={_onRefresh}
            prevStationDetail={previousStationDetail}
            animatedBottomSheet={animatedBottomSheet}
            trainsLiveGPSSpeed={trainsLiveGPSSpeed}
          />
        </BottomSheetModalTrain>
      )}

      {feedBackVisibility &&
        (ltsFeedbackPokus ? (
          <BottomSheetModalTrain
            onTouchOutside={onFeedbackBottomSheetClose}
            additionalContainerStyle={{ zIndex: 1 }}
            testID="feedback_bottomsheet_modal"
          >
            <StarBasedFeedbackBottomSheet
              onSendButtonClick={onSendFeedback}
              onCloseButtonClick={onFeedbackBottomSheetClose}
              trainDetails={trainDetails}
              currentStationDetail={currentStationDetail}
              stationsSectionList={stationsSectionList}
            />
          </BottomSheetModalTrain>
        ) : (
          <BottomSheetModalTrain
            onTouchOutside={toggleFBBSVisibility}
            additionalContainerStyle={{ zIndex: 1 }}
            testID="feedback_bottomsheet_modal"
          >
            <FeedBackBottomSheet
              onSendButtonClick={onSendFeedback}
              onCloseButtonClick={toggleFBBSVisibility}
              trainDetails={trainDetails}
              currentStationDetail={currentStationDetail}
            />
          </BottomSheetModalTrain>
        ))}
    </React.Fragment>
  );

  const renderItem = ({ item }) => (
    <Station
      key={keys}
      item={item}
      allStationList={stationsSectionList}
      firstStation={firstStationDetail}
      lastStation={lastStationDetail}
      trainDetails={trainDetails}
      currentStationDetail={currentStationDetail}
      updateHeightArr={updateHeightArr}
      triggerScroll={triggerScroll}
      yourStop={requestDetails?.stationDetail}
      otherOptions={otherOptions}
      onFeedBackButtonClick={() => toggleFBBSVisibility()}
      toggleAlarmVisibilityAndUpdateStationDetails={toggleAlarmVisibilityAndUpdateStationDetails}
      isAlarmEnabled={isAlarmEnabled}
      railsMealPokus={railsMealPokus}
    />
  );

  const renderSectionHeader = ({ section }) => {
    const { title } = section;
    return (
      <DateLabel
        value={formatDateTime(title.date)}
        currentStationSectionIndex={currentStationDetail?.StoppingStation?.SectionIndex}
        sectionIndex={title.sectionIndex}
        firstStation={stationsSectionList[title.sectionIndex - 1].data[0]}
        started={trainDetails?.Departed}
      />
    );
  };
  const keyExtractor = (item, index) => `${index}-${item?.Station?.code}`;

  useEffect(() => {
    if (!isFooterSheetVisible && numberOfTimesBottomSheetShown.current === 0) {
      timerId.current = setTimeout(() => {
        setShowReturnTicketBottomSheet(true);
        numberOfTimesBottomSheetShown.current += 1;
      }, 10000);
    }
    return () => {
      clearTimeout(timerId.current);
    };
  }, [isFooterSheetVisible]);

  function closeMealsComponent() {
    setShowMealsComponent(false);
  }

  const trackClickEvent = () => {
    trackClickEventProp61(PAGE_RIS_LTS_INFO, LTS_DETAILS_MEALS_ORDER_NOW_STICKY_CLICKED);
  };

  //******** FULL SCREEN MODE ********/
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View>
        <LiveTrainStatusHeader
          onBack={onFeedbackBackButtonPressed}
          trainNumber={trainDetails?.Number}
          trainName={trainDetails?.Name}
          toggleCTInfo={() =>
            isCTInfoEnabled.current && Platform.OS === 'android' && setCTInfoVisibility(true)
          }
        />
      </View>
      {ltsViewState === viewStates.LOADING && showLoader && <Loader />}
      {ltsViewState === viewStates.ERROR && (
        <View testID="live_train_status_page_error_view">
          <ErrorView
            displayText={ltsErrorMsg}
            onBackClicked={onBackIconPressed}
            onRefreshClicked={_onRefresh}
          />
        </View>
      )}
      {ltsViewState === viewStates.REFRESH &&
        !showLoader &&
        trainDetails?.Number === requestDetails.trainDetail.trainNumber && (
          <View style={{ flex: 1 }} testID="live_train_status_page_refresh_view">
            {stickHeader && (
              <TrainListStickyHeader
                cancellationAndDiversionInfo={cancellationAndDiversionInfo}
                outsideTrain={!isInsideTrain}
                startDate={createStartDateDetails(trainDetails)}
                toggleSDBSVisibility={toggleSDBSVisibility}
                toggleTOBSVisibility={toggleTOBSVisibility}
                showTrackingOption={isOfflineTrackingEnabled}
                currentStationDetail={currentStationDetail}
                prevStationDetail={previousStationDetail}
                trainDetails={trainDetails}
                isInsideTrain={isInsideTrain}
              />
            )}
            {!stickHeader && (
              <TrainListStickyHeader
                cancellationAndDiversionInfo={cancellationAndDiversionInfo}
                outsideTrain={!isInsideTrain}
                startDate={createStartDateDetails(trainDetails)}
                toggleSDBSVisibility={toggleSDBSVisibility}
                toggleTOBSVisibility={toggleTOBSVisibility}
                showTrackingOption={isOfflineTrackingEnabled}
                currentStationDetail={currentStationDetail}
                prevStationDetail={previousStationDetail}
                trainDetails={trainDetails}
                isInsideTrain={isInsideTrain}
              />
            )}
            <Loader />
            <BottomComponents />
          </View>
        )}
      {ltsViewState === viewStates.SHOW_DETAIL &&
        !showLoader &&
        trainDetails?.Number === requestDetails.trainDetail.trainNumber && (
          <View style={{ flex: 1 }}>
            {!otherOptions?.showTrainSchedule && (
              <LinearGradient
                style={styles.gradientContainer}
                colors={['rgba(180,180,180,84)', 'rgba(255,255,255,84)']}
                start={{ x: 0.0, y: 1.0 }}
                end={{ x: 0.0, y: 0.0 }}
                testID="live_train_status_page_linear_gradient_container"
              />
            )}
            {stickHeader && (
              <TrainListStickyHeader
                cancellationAndDiversionInfo={cancellationAndDiversionInfo}
                outsideTrain={!isInsideTrain}
                startDate={createStartDateDetails(trainDetails)}
                toggleSDBSVisibility={toggleSDBSVisibility}
                toggleTOBSVisibility={toggleTOBSVisibility}
                showTrackingOption={isOfflineTrackingEnabled}
                currentStationDetail={currentStationDetail}
                prevStationDetail={previousStationDetail}
                trainDetails={trainDetails}
                isInsideTrain={isInsideTrain}
              />
            )}
            <ScrollView ref={scrollToRef} onScroll={_onScroll} scrollEventThrottle={1} testID="live_train_status_page_scrollview">
              {!stickHeader && (
                <>
                  <TrainListStickyHeader
                    cancellationAndDiversionInfo={cancellationAndDiversionInfo}
                    outsideTrain={!isInsideTrain}
                    startDate={createStartDateDetails(trainDetails)}
                    toggleSDBSVisibility={toggleSDBSVisibility}
                    toggleTOBSVisibility={toggleTOBSVisibility}
                    showTrackingOption={isOfflineTrackingEnabled}
                    currentStationDetail={currentStationDetail}
                    prevStationDetail={previousStationDetail}
                    trainDetails={trainDetails}
                    isInsideTrain={isInsideTrain}
                  />
                </>
              )}
              <View style={styles.trainListContainer}>
                <SectionList
                  sections={stationsSectionList}
                  renderItem={renderItem}
                  renderSectionHeader={renderSectionHeader}
                  keyExtractor={keyExtractor}
                  testID="live_train_status_page_section_list"
                />
                <Disclaimer disclaimer={disclaimer} />
              </View>
            </ScrollView>
            <BottomComponents />
          </View>
        )}
      {alarmVisibility && (
        <BottomSheetModalTrain
          onTouchOutside={toggleAlarmVisibilityAndUpdateStationDetails}
          additionalContainerStyle={{ zIndex: 1 }}
          testID="alarm_setter_bottomsheet_modal"
        >
          <AlarmSetterBottomSheet
            toggleKeysForStationReRender={toggleKeysForStationReRender}
            currentStationDetailsForAlarm={currentStationDetailsForAlarm}
            toggleAlarmVisibilityAndUpdateStationDetails={
              toggleAlarmVisibilityAndUpdateStationDetails
            }
          />
        </BottomSheetModalTrain>
      )}

      {isTrackingOptionsVisible && (
        <BottomSheetModalTrain
          onTouchOutside={toggleTOBSVisibility}
          additionalContainerStyle={{ zIndex: 1 }}
          testID="tracking_option_bottomsheet_modal"
        >
          <TrackingOptionBottomSheet
            onSelect={onTrackingOptionsSelect}
            selected={isInsideTrain}
            toggleTOBSVisibility={toggleTOBSVisibility}
          />
        </BottomSheetModalTrain>
      )}
      {isSwitcherVisible && (
        <BottomSheetModalTrain
          onTouchOutside={() => setSwitcherVisibility(false)}
          additionalContainerStyle={{ zIndex: 1 }}
          testID="switcher_bottomsheet_modal"
        >
          <Switcher closeModal={() => setSwitcherVisibility(false)} data={switcherData} />
        </BottomSheetModalTrain>
      )}

      {locationRequestNeeded && (
        <LocationPermissionPopup
          message={labels.LOCATION_POPUP}
          primaryButton={labels.LOCATION_POPUP_PRIM_BTN}
          onPermGranted={requestPermission}
          onPermRejected={requestPermissionRejected}
        />
      )}
      {locationRequestOnSettingsNeeded && (
        <LocationPermissionPopup
          message={labels.LOCATION_POPUP_SETTINGS}
          primaryButton={labels.LOCATION_POPUP_SETTINGS_PRIM_BTN}
          onPermGranted={requestPermissionOnSettings}
          onPermRejected={requestPermissionRejected}
        />
      )}
      {isCTInfoVisible && (
        <BottomSheetModalTrain
          onTouchOutside={() => setCTInfoVisibility(false)}
          additionalContainerStyle={{ zIndex: 1 }}
          testID="ct_info_bottomsheet_modal"
        >
          <CTInfoBottomSheet closeModal={() => setCTInfoVisibility(false)} />
        </BottomSheetModalTrain>
      )}
      {!showMealBottomSheet &&
        !isShownFixedMealBottom &&
        showReturnTicketBottomSheet &&
        !isFooterSheetVisible &&
        ltsCrossSellData && (
          <PNRCrossSellBottomSheet
            crossSellData={ltsCrossSellData}
            onClosePressed={() => setShowReturnTicketBottomSheet(false)}
            visible={showReturnTicketBottomSheet}
          />
        )}
      {showMealBottomSheet && isShownFixedMealBottom && (
        <MealBottomSheet
          setShowMealBottomSheet={setShowMealBottomSheet}
          upComingPnrs={upComingPnrs}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    justifyContent: 'center',
    flex: 1,
    alignItems: 'center',
  },
  trainListContainer: {
    marginTop: 25,
    marginBottom: 20,
    flex: 1,
  },
  text: {
    fontFamily: fonts.medium,
    fontSize: 17,
    color: colors.defaultTextColor,
    alignSelf: 'center',
    textAlign: 'center',
  },
  boldText: {
    fontFamily: fonts.bold,
    fontSize: 19,
    lineHeight: 30,
    color: colors.black,
  },
  gradientContainer: {
    position: 'absolute',
    bottom: Platform.select({ android: 60, ios: 45 }),
    width: '100%',
    height: 40,
  },
  errorModal: {
    position: 'relative',
    width: '90%',
    backgroundColor: colors.red,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
    top: 30,
    left: 30,
  },
});

LiveTrainStatusPage.propTypes = {
  firstStationDetail: PropTypes.object,
  requestDetails: PropTypes.object,
  initTracking: PropTypes.func,
  clearStore: PropTypes.func,
  clearOfflineError: PropTypes.func,
  deeplink: PropTypes.bool,
  lastStationDetail: PropTypes.object,
  nextStationDetail: PropTypes.object,
  currentStationDetail: PropTypes.object,
  previousStationDetail: PropTypes.object,
  trainDetails: PropTypes.object,
  disclaimer: PropTypes.string,
  stationsSectionList: PropTypes.array,
  showLoader: PropTypes.bool,
  ltsViewState: PropTypes.string,
  ltsErrorMsg: PropTypes.string,
  otherOptions: PropTypes.object,
  lastUpdated: PropTypes.string,
  cancellationAndDiversionInfo: PropTypes.object,
  trainsLiveGPSSpeed: PropTypes.object,
  dataSource: PropTypes.string,
  ltsCrossSellData: PropTypes.object,
  upComingPnrs: PropTypes.array,
  isOfflineErrorVisible: PropTypes.bool,
  feedBackVisibilityRef: PropTypes.object,
  offlineErrorData: PropTypes.string,
};

export default LiveTrainStatusPage;
