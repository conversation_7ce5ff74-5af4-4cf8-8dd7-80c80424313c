import React, { useState } from 'react';
import { View, Image, Text, FlatList } from 'react-native';
import { getTrainStatusDates } from '../../../../Utils/RisUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels } from '../constants';
import { bottomSheetStyles } from './TrackingOptionBottomSheet';
import LinearGradient from 'react-native-linear-gradient';
import { DateDetails as DateListInterface } from '../interface';
import {
  EVENT_RIS_LTS_DATE_CHANGE_CLICK,
  PAGE_RIS_LTS_INFO,
  trackOmnitureLTSClickEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';

import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';

interface Props {
  onStartDateSelect: (date: DateListInterface | undefined) => void;
  startDay: string | undefined;
  toggleSDBSVisibility: () => void;
}

const DateBottomSheet = ({ onStartDateSelect, startDay, toggleSDBSVisibility }: Props) => {
  const dateList: DateListInterface[] = getTrainStatusDates();
  const intialSelectedDate = dateList.find((date) => startDay?.includes(date.dateTitle));
  const [selectedDate, selectDate] = useState<DateListInterface | undefined>(intialSelectedDate);

  const _renderItem = ({ item: date }: { item: DateListInterface }) => {
    return (
      <TouchableRipple onPress={() => selectDate(date)} testID="date_bottomsheet_item">
        <View style={bottomSheetStyles.optionContainer}>
          <View style={{ flexDirection: 'row' }} testID="date_bottomsheet_item_container">
            <RadioButton
              isSelected={selectedDate?.dateTitle === date?.dateTitle}
              onPress={() => selectDate(date)}
              radioSize={20}
              customStyle={bottomSheetStyles.radioContainer}
              testID="date_bottomsheet_item_radio_button"
            />
            <View
              style={bottomSheetStyles.optionViewContainer}
              testID="date_bottomsheet_item_view_container"
            >
              <Text
                style={[
                  bottomSheetStyles.optionTitle,
                  selectedDate !== date
                    ? bottomSheetStyles.boldText
                    : bottomSheetStyles.selectedOptionTitle,
                ]}
                testID="date_bottomsheet_item_text"
              >
                {date.dateTitle}
              </Text>
              <Text style={bottomSheetStyles.optionDesc} testID="date_bottomsheet_item_text_two">
                {date.dateFormatted}
              </Text>
            </View>
          </View>
        </View>
      </TouchableRipple>
    );
  };

  const _renderHeader = () => (
    <View style={bottomSheetStyles.headerContainer} testID="date_bottomsheet_header_container">
      <Text style={bottomSheetStyles.headerText} testID="date_bottomsheet_header_text">
        {labels.DATEBOTTOMSHEET_TITLE}
      </Text>
      <TouchableRipple
        onPress={() => toggleSDBSVisibility()}
        testID="date_bottomsheet_header_close_button"
      >
        <View
          style={bottomSheetStyles.headerIconContainer}
          testID="date_bottomsheet_header_icon_container"
        >
          <Image
            source={closeIcon}
            style={bottomSheetStyles.headerIcon}
            testID="date_bottomsheet_header_icon"
          />
        </View>
      </TouchableRipple>
    </View>
  );

  return (
    <View style={bottomSheetStyles.container} testID="date_bottomsheet_container">
      <View
        style={bottomSheetStyles.optionListContainer}
        testID="date_bottomsheet_option_list_container"
      >
        <FlatList
          data={dateList}
          renderItem={_renderItem}
          ListHeaderComponent={_renderHeader}
          extraData={selectedDate}
          keyExtractor={(item) => item.date.toString()}
          testID="date_bottomsheet_list"
        />
      </View>
      <View style={{ flex: 1 }} testID="date_bottomsheet_flex_one" />
      <TouchableRipple
        style={bottomSheetStyles.proceed}
        testID="date_bottomsheet_proceed_button"
        onPress={() => {
          onStartDateSelect(selectedDate);
          trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, EVENT_RIS_LTS_DATE_CHANGE_CLICK);
        }}
      >
        <LinearGradient
          colors={[colors.lightBlue, colors.darkBlue]}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 1.0 }}
          style={bottomSheetStyles.gradientStyle}
          testID="date_bottomsheet_gradient_style"
        >
          <Text style={bottomSheetStyles.proceedText} testID="date_bottomsheet_proceed_text">
            {labels.PROCEED}
          </Text>
        </LinearGradient>
      </TouchableRipple>
    </View>
  );
};

export default DateBottomSheet;
