import React, { useState, useEffect } from 'react';
import { View, Text, Image, StyleSheet, FlatList, Platform } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import RadioButton from './RadioButton';
import {
  labels,
  TRACKING_OPTIONS as options,
  TRACKING_OPTIONS_INTERFACE as OptionsInterface,
} from '../constants';
import {
  BS_RIS_LTS_TRACKING_MODE,
  LTS_INSIDE_TRAIN_CLICKED,
  LTS_OUTSIDE_TRAIN_CLICKED,
  trackOmnitureLTSClickEvent,
  trackOmnitureLTSLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';

import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';

interface Props {
  selected: boolean;
  toggleTOBSVisibility: () => void;
  onSelect: (insideTrain: boolean) => void;
}

const TrackingOptionBottomSheet = ({ selected, toggleTOBSVisibility, onSelect }: Props) => {
  const [insideTrain, setInsideTrain] = useState(selected || false);

  useEffect(() => {
    trackOmnitureLTSLoadEvent(BS_RIS_LTS_TRACKING_MODE, '');
  }, []);

  const onProceedPressed = () => {
    onSelect(insideTrain);
    trackOmnitureLTSClickEvent(BS_RIS_LTS_TRACKING_MODE,insideTrain ? 
      LTS_INSIDE_TRAIN_CLICKED : LTS_OUTSIDE_TRAIN_CLICKED);
  };

  const _renderItem = ({ item: option }: { item: OptionsInterface }) => {
    return (
      <TouchableRipple
        onPress={() => setInsideTrain(option.value)}
        testID="tracking_option_bottomsheet_option_list_item"
      >
        <View
          style={bottomSheetStyles.optionContainer}
          testID="tracking_option_bottomsheet_option_list_item_container"
        >
          <View
            style={{ flexDirection: 'row' }}
            testID="tracking_option_bottomsheet_option_list_item_container_two"
          >
            <RadioButton
              checked={insideTrain === option.value}
              dimension={18}
              style={bottomSheetStyles.radioContainer}
            />
            <View
              style={bottomSheetStyles.optionViewContainer}
              testID="tracking_option_bottomsheet_option_list_item_view_container"
            >
              <Text
                style={[
                  bottomSheetStyles.optionTitle,
                  insideTrain !== option.value
                    ? bottomSheetStyles.boldText
                    : bottomSheetStyles.selectedOptionTitle,
                ]}
                testID="tracking_option_bottomsheet_option_list_item_text"
              >
                {option.title}
              </Text>
              <Text
                style={bottomSheetStyles.optionDesc}
                testID="tracking_option_bottomsheet_option_list_item_text_two"
              >
                {option.desc}
              </Text>
            </View>
          </View>
          <Image
            source={option.icon}
            style={bottomSheetStyles.optionIcon}
            testID="tracking_option_bottomsheet_option_list_item_icon"
          />
        </View>
      </TouchableRipple>
    );
  };

  const _renderHeader = () => (
    <View
      style={bottomSheetStyles.headerContainer}
      testID="tracking_option_bottomsheet_header_container"
    >
      <Text style={bottomSheetStyles.headerText} testID="tracking_option_bottomsheet_header_text">
        {labels.TRACKING_OPTION_BOTTOMSHEET_TITLE}
      </Text>
      <TouchableRipple
        onPress={() => toggleTOBSVisibility()}
        testID="tracking_option_bottomsheet_header_close_button"
      >
        <View
          style={bottomSheetStyles.headerIconContainer}
          testID="tracking_option_bottomsheet_header_icon_container"
        >
          <Image
            source={closeIcon}
            style={bottomSheetStyles.headerIcon}
            testID="tracking_option_bottomsheet_header_icon"
          />
        </View>
      </TouchableRipple>
    </View>
  );

  const _renderFooter = () => <View style={bottomSheetStyles.footerGap} />;

  return (
    <View style={bottomSheetStyles.container} testID="tracking_option_bottomsheet_container">
      <View
        style={bottomSheetStyles.optionListContainer}
        testID="tracking_option_bottomsheet_option_list_container"
      >
        <FlatList
          data={options}
          renderItem={_renderItem}
          ListHeaderComponent={_renderHeader}
          ListFooterComponent={_renderFooter}
          extraData={insideTrain}
          testID="tracking_option_bottomsheet_list"
        />
      </View>
      <View style={{ flex: 1 }} />
      <TouchableRipple
        style={bottomSheetStyles.proceed}
        onPress={onProceedPressed}
        testID="tracking_option_bottomsheet_proceed_button"
      >
        <LinearGradient
          colors={[colors.lightBlue, colors.darkBlue]}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 1.0 }}
          style={bottomSheetStyles.gradientStyle}
          testID="tracking_option_bottomsheet_gradient_style"
        >
          <Text
            style={bottomSheetStyles.proceedText}
            testID="tracking_option_bottomsheet_proceed_text"
          >
            {labels.PROCEED}
          </Text>
        </LinearGradient>
      </TouchableRipple>
    </View>
  );
};

export default TrackingOptionBottomSheet;

export const bottomSheetStyles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 20,
    backgroundColor: colors.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  headerText: {
    fontFamily: fonts.bold,
    color: colors.black,
    fontSize: 21,
  },
  headerIconContainer: {
    height: 21,
    width: 25,
    paddingVertical: 2,
  },
  footerGap: {
    height: 10,
  },
  headerIcon: {
    width: 17,
    height: 17,
    marginRight: 8,
  },
  optionListContainer: {
    marginTop: 4,
    marginLeft: 10,
  },
  optionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  radioContainer: {
    marginLeft: 2,
    marginRight: 10,
    ...Platform.select({
      ios: {
        marginTop: 4,
      },
      android: {
        marginTop: 2,
      },
    }),
  },
  optionViewContainer: { flexShrink: 10 },
  optionTitle: {
    fontSize: 18,
  },
  optionDesc: {
    fontFamily: fonts.regular,
    fontSize: 16,
    lineHeight: 26,
    color: colors.defaultTextColor,
  },
  optionIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
    ...Platform.select({
      ios: {
        marginTop: 5,
      },
      android: {
        marginTop: 3,
      },
    }),
  },
  boldText: {
    fontFamily: fonts.bold,
    color: colors.black,
  },
  selectedOptionTitle: {
    fontFamily: fonts.black,
    color: colors.azure,
  },
  proceed: {
    marginTop: 'auto',
  },
  gradientStyle: {
    borderRadius: 8,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6,
  },
  proceedText: {
    fontFamily: fonts.black,
    color: colors.white,
    fontSize: 19,
    letterSpacing: 0.31,
  },
});
