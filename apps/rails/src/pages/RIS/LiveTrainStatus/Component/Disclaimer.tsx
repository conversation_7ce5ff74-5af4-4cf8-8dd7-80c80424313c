import isEmpty from 'lodash/isEmpty';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels } from '../constants';

const Disclaimer = ({ disclaimer }: { disclaimer: string }) => {
  return (
    <React.Fragment>
      {isEmpty(disclaimer) ? (
        null
      ) : (
        <View
          style={DisclaimerStyle.container}
          testID="live_train_status_page_disclaimer_container"
        >
          <Text style={DisclaimerStyle.header} testID="live_train_status_page_disclaimer_header">
            {labels.DISCLAIMER}
          </Text>
          <Text style={DisclaimerStyle.text} testID="live_train_status_page_disclaimer_text">
            {disclaimer}
          </Text>
        </View>
      )}
    </React.Fragment>
  );
};

const DisclaimerStyle = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 10,
  },
  header: {
    paddingTop: 25,
    paddingBottom: 25,
    paddingLeft: 15,
    fontSize: 22,
    fontFamily: fonts.bold,
    color: colors.black,
  },
  text: {
    paddingBottom: 25,
    paddingLeft: 15,
    fontSize: 14,
    paddingRight: 20,
  },
});

export default Disclaimer;
