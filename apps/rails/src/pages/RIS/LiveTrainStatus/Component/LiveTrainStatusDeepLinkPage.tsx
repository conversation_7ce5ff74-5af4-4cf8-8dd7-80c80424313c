import { getQueryParamsFromUrl } from '@mmt/legacy-commons/Helpers/misc';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { Actions } from 'apps/rails/src/navigation';
import React, { useEffect } from 'react';
import { ActivityIndicator, StyleSheet, View, Platform } from 'react-native';
import { openLiveTrainStatusFromDeeplink } from '../../RisCommonUtils';

const getQueryParamsFromProps = (props) => {
  return {
    dateOfJourney: props.dateOfJourney,
    trainNumber: props.trainNumber,
    page: props.page,
    railsBusCommonLanding: props.railsBusCommonLanding,
    deeplink: props.deeplink,
    isFromNewLanding: props.isFromNewLanding,
  };
};

interface LiveTrainStatusDeepLinkPageProps {
  deep_link_intent_url: string;
  page: string;
}

const LiveTrainStatusDeepLinkPage = (props: LiveTrainStatusDeepLinkPageProps) => {
  useEffect(() => {
    try {
      if (props.deep_link_intent_url || props.page === 'railLiveTrainStatusDeepLink') {
        const deepLinkParams =
          Platform.OS === 'ios'
            ? getQueryParamsFromProps(props)
            : props.deep_link_intent_url
            ? getQueryParamsFromUrl(props.deep_link_intent_url)
            : {};
        if (deepLinkParams.trainNumber) {
          openLiveTrainStatusFromDeeplink(deepLinkParams);
        } else {
          Actions.railsBusCommonLanding(undefined, 'replace');
        }
      } else {
        Actions.railsBusCommonLanding(undefined, 'replace');
      }
    } catch (error) {}
  }, []);

  return (
    <View style={styles.container}>
      <ActivityIndicator color={colors.red} />
    </View>
  );
};

export default LiveTrainStatusDeepLinkPage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
