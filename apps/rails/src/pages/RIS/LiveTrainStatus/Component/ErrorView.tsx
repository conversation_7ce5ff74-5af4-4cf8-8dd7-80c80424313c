import React, { useEffect } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {
  PAGE_RIS_LTS_UNSTABLE_INTERNET,
  PAGE_RIS_LTS_NOT_RUN_DATE,
  PAGE_RIS_LTS_TECHNICAL_ISSUE,
  PAGE_RIS_LTS_ERROR,
  trackOmnitureLTSLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import { labels } from '../constants';

import errorImage from '@mmt/legacy-assets/src/lts_error_icon.webp';

interface Props {
  displayText: string;
  onBackClicked: () => void;
  onRefreshClicked: () => void;
}

const ErrorView = ({ displayText, onBackClicked, onRefreshClicked }: Props) => {
  useEffect(() => {
    const event =
      displayText === labels.DEFAULT_ERROR_MSG
        ? PAGE_RIS_LTS_UNSTABLE_INTERNET
        : displayText.includes('technical issue')
        ? PAGE_RIS_LTS_TECHNICAL_ISSUE
        : displayText.includes('date')
        ? PAGE_RIS_LTS_NOT_RUN_DATE
        : PAGE_RIS_LTS_ERROR;
    trackOmnitureLTSLoadEvent(event, displayText);
  }, [displayText]);
  const onClick = () => {
    (displayText !== labels.DEFAULT_ERROR_MSG ? onBackClicked : onRefreshClicked)();
  };
  return (
    <View style={styles.container} testID="live_train_status_page_error_view_container">
      <View style={{ alignItems: 'center' }}>
        <Image
          source={errorImage}
          style={styles.noInternetImage}
          testID="live_train_status_page_error_view_no_internet_image"
        />
        <Text
          style={styles.noInternetTitle}
          testID="live_train_status_page_error_view_no_internet_title"
        >
          {'Oops!'}
        </Text>
        <Text
          style={styles.noInternetSubtitle}
          testID="live_train_status_page_error_view_no_internet_subtitle"
        >
          {displayText}
        </Text>
      </View>
      <View style={styles.noInternetCta} testID="live_train_status_page_error_view_no_internet_cta">
        <TouchableRipple
          onPress={onClick}
          testID="live_train_status_page_error_view_no_internet_cta_touchable_ripple"
        >
          <View>
            <LinearGradient
              colors={[colors.darkBlue, colors.lightBlue]}
              start={{ x: 0.0, y: 1.0 }}
              end={{ x: 1.0, y: 0.0 }}
              style={styles.cta}
              testID="live_train_status_page_error_view_no_internet_cta_linear_gradient"
            >
              <Text
                style={styles.text}
                testID="live_train_status_page_error_view_no_internet_cta_text"
              >
                {displayText !== labels.DEFAULT_ERROR_MSG
                  ? labels.ERRORVIEW_BUTTON_TEXT_BACK
                  : labels.ERRORVIEW_BUTTON_TEXT_REFRESH}
              </Text>
            </LinearGradient>
          </View>
        </TouchableRipple>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    height: '100%',
  },
  noInternetImage: {
    width: 231,
    height: 159,
    marginTop: 100,
  },
  noInternetCta: {
    paddingHorizontal: 10,
    marginBottom: 60,
  },
  cta: {
    borderRadius: 5,
    height: 50,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  noInternetTitle: {
    marginTop: 55,
    color: colors.black,
    fontSize: 22,
    fontFamily: fonts.bold,
    textAlign: 'center',
  },
  noInternetSubtitle: {
    marginTop: 15,
    maxWidth: 300,
    color: colors.lightBlack3,
    fontSize: 18,
    lineHeight: 23,
    fontFamily: fonts.regular,
    textAlign: 'center',
  },
  text: {
    fontFamily: fonts.black,
    color: colors.white,
    fontSize: 19,
    letterSpacing: 0.31,
  },
});

export default ErrorView;
