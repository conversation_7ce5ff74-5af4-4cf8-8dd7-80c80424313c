import React, { useState, useEffect } from 'react';
import { View, Text, Image } from 'react-native';
import { getCellId } from '../Util/LiveTrainStatusActions';
import { bottomSheetStyles } from './TrackingOptionBottomSheet';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';

const CTInfoBottomSheet = ({ closeModal }: { closeModal: () => void }) => {
  const [data, setData] = useState({});
  useEffect(() => {
    const init = async () => {
      const cellId = await getCellId();
      setData(cellId);
    };
    init();
  }, []);
  return (
    <View
      style={[bottomSheetStyles.container, { height: 200 }]}
      testID="ct_info_bottomsheet_container"
    >
      <View style={bottomSheetStyles.headerContainer} testID="ct_info_bottomsheet_header_container">
        <Text style={bottomSheetStyles.headerText} testID="ct_info_bottomsheet_header_text">
          {'CT Info'}
        </Text>
        <TouchableRipple onPress={() => closeModal()} testID="ct_info_bottomsheet_close_button">
          <View
            style={bottomSheetStyles.headerIconContainer}
            testID="ct_info_bottomsheet_header_icon_container"
          >
            <Image
              source={closeIcon}
              style={bottomSheetStyles.headerIcon}
              testID="ct_info_bottomsheet_header_icon"
            />
          </View>
        </TouchableRipple>
      </View>

      {Object.keys(data).map((key) => (
        <Text
          key={key}
          style={{ color: colors.black, fontFamily: fonts.black, fontSize: 16 }}
          testID="ct_info_bottomsheet_text"
        >
          {key}
          {': '}
          {data?.[key]}
        </Text>
      ))}
    </View>
  );
};

export default CTInfoBottomSheet;
