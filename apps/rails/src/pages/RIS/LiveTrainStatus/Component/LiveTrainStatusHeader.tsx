import React from 'react';
import { View, Text, Image, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import {
  EVENT_RIS_LTS_BACK_CLICK,
  PAGE_RIS_LTS_INFO,
  trackOmnitureLTSClickEvent,
} from '../../../../RisAnalytics/RisAnalytics';

interface Props {
  onBack: () => void;
  toggleCTInfo: () => void;
  showTrainNumber: boolean;
  trainNumber: string;
  trainName: string;
}

const LiveTrainStatusHeader = ({ onBack, toggleCTInfo, trainNumber, trainName }: Props) => (
  <TouchableWithoutFeedback
    onPress={toggleCTInfo}
    testID="live_train_status_page_header_container_touchable_without_feedback"
  >
    <View style={styles.container} testID="live_train_status_page_header_container">
      <View
        style={styles.headerContainer}
        testID="live_train_status_page_header_container_header_container"
      >
        <TouchableRipple onPress={() => {
          onBack();
            trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, EVENT_RIS_LTS_BACK_CLICK);
          }}
          testID="live_train_status_page_header_container_back_icon_container"
        >
          <View
            style={styles.backIconContainer}
            testID="live_train_status_page_header_container_back_icon_container_view"
          >
            <Image
              style={styles.backIconStyle}
              source={backIcon}
              testID="live_train_status_page_header_container_back_icon_container_image"
            />
          </View>
        </TouchableRipple>
        <Text
          style={styles.titleStyle}
          testID="live_train_status_page_header_container_title_style"
        >
          {trainName}
        </Text>
      </View>
      {trainNumber && (
        <Text
          style={styles.trainNumber}
          testID="live_train_status_page_header_container_train_number"
        >{`# ${trainNumber}`}</Text>
      )}
    </View>
  </TouchableWithoutFeedback>
);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 40,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backIconContainer: {
    paddingLeft: 16,
    paddingRight: 20,
    paddingVertical: 10,
    height: '100%',
    justifyContent: 'center',
  },
  backIconStyle: {
    width: 16,
    height: 14,
  },
  titleStyle: {
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.black,
    marginVertical: 10,
  },
  trainNumber: {
    marginRight: 16,
    fontFamily: fonts.bold,
    color: colors.defaultTextColor,
    fontSize: 14,
    marginVertical: 10,
  },
});

export default LiveTrainStatusHeader;
