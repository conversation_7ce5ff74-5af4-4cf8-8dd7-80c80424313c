/* eslint-disable */
import React, { useState, useEffect, useRef } from 'react';
import {
  getDataFromStorage,
  removeDataFromStorage,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { showShortToast } from '@mmt/core/helpers/toast';
import {
  trackOmnitureLTSClickEvent,
  PAGE_RIS_LTS_INFO,
  RIS_LTS_STATION_APPLY_ALARM,
  RIS_LTS_STATION_REMOVE_ALARM,
} from '../../../../RisAnalytics/RisAnalytics';
import {
  View, Text, StyleSheet, Image, Animated, Easing, TouchableOpacity,
} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import IntermediateStations from './IntermediateStations';
import LottieView from 'lottie-react-native';
import greenLottieIcon from '../LottieFiles/lts_green_current_station_lottie_icon.json';
import redLottieIcon from '../LottieFiles/lts_red_current_station_lottie_icon.json';
import { labels, asyncStorageKeys, dimensions, alarmMinimumTimeDiff } from '../constants';
import FeedBackButton from './FeedBackButton';
import {
  getDateFromTimeStamp,
  getDelay,
  getHash,
  getStoppingStationOffset,
  getTimeDiffWithCurrentTime,
} from './commonUtils';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { trackClickEventProp61 } from 'packages/legacy-commons/Common/utils/OmnitureTrackerUtils';
import OrderMeal from '@mmt/rails/src/pages/RIS/MealsOnTrain/Components/OrderMeal';
import { LTS_DETAILS_MEALS_ORDER_NOW_CLICKED } from 'apps/rails/src/Utils/RailsConstant';
import { getMealsNewFlow, showMealsWebView } from '@mmt/rails/src/RailsAbConfig';

import upArrow from '@mmt/legacy-assets/src/lts_upArrow.webp';
import downArrow from '@mmt/legacy-assets/src/lts_downArrow.webp';
import greenTrainIcon from '@mmt/legacy-assets/src/lts_green_train_icon.webp';
import redTrainIcon from '@mmt/legacy-assets/src/lts_red_train_icon.webp';
import ltsDelayIndicatorRedStart from '@mmt/legacy-assets/src/lts_delayIndicator_red_start.webp';
import ltsDelayIndicatorRedEnd from '@mmt/legacy-assets/src/lts_delayIndicator_red_end.webp';
import ltsDelayIndicatorGreenStart from '@mmt/legacy-assets/src/lts_delayIndicator_green_start.webp';
import ltsDelayIndicatorGreenEnd from '@mmt/legacy-assets/src/lts_delayIndicator_green_end.webp';
import alarmClock from '@mmt/legacy-assets/src/alarm_clock.webp';
import PropTypes from 'prop-types';

const isMealAvailableForStation = (station) => {
  const mealsWebView = showMealsWebView();
  const showNewMeals = getMealsNewFlow();
  const isZomato = mealsWebView || showNewMeals;
  return isZomato ? station?.ZomatoMealsAvailable : station?.MealsAvailable;
};

const Station = (props) => {
  const {
    firstStation,
    lastStation,
    trainDetails,
    currentStationDetail,
    item,
    otherOptions,
    onFeedBackButtonClick,
    isAlarmEnabled,
    railsMealPokus,
  } = props;
  const [showIntermediateStations, setShowIntermediateStations] = useState(false);
  const [incorrectLocationVisibility, setIncorrectLocationVisibility] = useState(false);
  const [alarmSelect, setAlarmSelect] = useState(false);
  const [offsetWindow, setOffsetWindow] = useState(
    item?.Station?.code === lastStation?.Station?.code
      ? dimensions.LAST_STOPPING_STATION.DEFAULT_OFFSET_WINDOW
      : isEmpty(item?.IntermediateStations)
      ? dimensions.NORMAL_STOPPING_STATION.DEFAULT_OFFSET_WINDOW.NO_INTERMEDIATES
      : dimensions.NORMAL_STOPPING_STATION.DEFAULT_OFFSET_WINDOW.WITH_INTERMEDIATES,
  );
  const delayIndicatorWidth = useRef(new Animated.Value(0));
  const delayIndicatorImageStartWidth = useRef(new Animated.Value(0));
  const delayIndicatorImageEndWidth = useRef(new Animated.Value(0));

  useEffect(() => {
    if (item?.IntermediateStations?.length > 0) {
      setOffsetWindow(dimensions.NORMAL_STOPPING_STATION.DEFAULT_OFFSET_WINDOW.WITH_INTERMEDIATES);
    }
  }, [item?.IntermediateStations?.length ?? 0]);

  const stationCode = item?.Station?.code;
  const stationName = item?.Station?.name;
  const stopNumber = item.StopNumber;
  const distance = item.Distance;
  const expectedPlatformNo =
    item?.Station?.expectedPlatformNumber &&
    item?.Station?.expectedPlatformNumber !== '0' &&
    item?.Station?.expectedPlatformNumber !== '-'
      ? ` ${item?.Station?.expectedPlatformNumber} `
      : ' -- ';
  const scheduledArrivalDate = item.ArrivalDetails.scheduledArrivalDate;
  const scheduledArrivalTime = item.ArrivalDetails.scheduledArrivalTime;
  const actualArrivalDate = item.ArrivalDetails.actualArrivalDate;
  const actualArrivalTime = item.ArrivalDetails.actualArrivalTime;
  const scheduledDepartureDate = item.DepartureDetails?.scheduledDepartureDate;
  const scheduledDepartureTime = item.DepartureDetails?.scheduledDepartureTime;
  const actualDepartureDate = item.DepartureDetails?.actualDepartureDate;
  const actualDepartureTime = item.DepartureDetails?.actualDepartureTime;
  const haltMinutes = item?.HaltMinutes;
  const cancelled = item.MetaDetails?.CancelledStation;
  const diverted = item.MetaDetails?.DivertedStation;
  const delayInMins = getDelay(
    currentStationDetail?.IntermediateStation?.Station?.code
      ? currentStationDetail?.IntermediateStation
      : currentStationDetail?.StoppingStation,
  );
  const delay = delayInMins.value !== 0;
  const currentLottieIcon = delay ? redLottieIcon : greenLottieIcon;
  const currentIcon = delay ? redTrainIcon : greenTrainIcon;

  const currentStationStopNumber = currentStationDetail?.StoppingStation?.StopNumber;
  const started = trainDetails?.Departed;
  const terminated = trainDetails?.Terminated;

  const lastStationCode = lastStation?.Station?.code;
  const firstStationCode = firstStation?.Station?.code;

  const currentStoppingStationCheck = stopNumber === currentStationStopNumber;

  const stoppedTrainCheck = !started || terminated;

  function find_dimesions(layout) {
    const { height } = layout;
    if (stopNumber < currentStationStopNumber) {
      props.updateHeightArr(stopNumber - 1, height);
      props.triggerScroll();
    }
    if (currentStoppingStationCheck) {
      props.triggerScroll();
    }
    setOffsetWindow(height);
  }

  const trackClickEvent = () => {
    trackClickEventProp61(PAGE_RIS_LTS_INFO, LTS_DETAILS_MEALS_ORDER_NOW_CLICKED);
  };

  const offsetCalculationParameters = {
    station: item,
    currentStation: currentStationDetail,
    offsetWindow,
    intermediatesExpanded: showIntermediateStations,
  };
  const offset = getStoppingStationOffset(offsetCalculationParameters);

  const leftBorderStyle1 = diverted
    ? styles.leftBorderYellow
    : cancelled
    ? styles.leftBorderRed
    : !started
    ? styles.leftBorderGrey
    : stopNumber <= currentStationStopNumber
    ? styles.leftBorderGreen
    : styles.leftBorderGrey;

  let leftBorderStyle2 = diverted
    ? styles.leftBorderYellow
    : cancelled
    ? styles.leftBorderRed
    : stopNumber < currentStationStopNumber
    ? styles.leftBorderGreen
    : styles.leftBorderGrey;

  const circleIconStyle = StyleSheet.flatten([
    diverted
      ? styles.leftBorderYellow
      : cancelled
      ? styles.leftBorderRed
      : stopNumber <= currentStationStopNumber
      ? styles.leftBorderGreen
      : styles.leftBorderGrey,
    styles.whiteBackground,
  ]);

  const delayAnimation = (delayAnimationStart) => {
    const delayTimer1 = setTimeout(() => {
      Animated.timing(delayIndicatorImageStartWidth.current, {
        toValue: 1,
        duration: 100,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => {
        Animated.timing(delayIndicatorWidth.current, {
          toValue: 1,
          duration: 200,
          easing: Easing.linear,
          useNativeDriver: true,
        }).start(() => {
          Animated.timing(delayIndicatorImageEndWidth.current, {
            toValue: 1,
            duration: 100,
            easing: Easing.linear,
            useNativeDriver: true,
          }).start();
        });
      });
    }, delayAnimationStart);

    const delayTimer2 = setTimeout(() => {
      Animated.timing(delayIndicatorImageEndWidth.current, {
        toValue: 0,
        duration: 100,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => {
        Animated.timing(delayIndicatorWidth.current, {
          toValue: 0,
          duration: 200,
          easing: Easing.linear,
          useNativeDriver: true,
        }).start(() => {
          Animated.timing(delayIndicatorImageStartWidth.current, {
            toValue: 0,
            duration: 100,
            easing: Easing.linear,
            useNativeDriver: true,
          }).start();
        });
      });
    }, 4000 + delayAnimationStart);

    return [delayTimer1, delayTimer2];
  };

  useEffect(() => {
    async function getAlarms() {
      const key = `${asyncStorageKeys.ALARM}${trainDetails?.Number}_${stationName}`;
      const alarmValue = (await getDataFromStorage(key)) ?? false;
      if (alarmValue) {
        var alarmDate = new Date(alarmValue);
        var currentDate = new Date();
        if (alarmDate > currentDate) {
          const actualArrivalTimeStamp = actualArrivalDate
            ?.split(' ')[0]
            .concat(' ', actualArrivalTime || '');
          const stationArrivalDate = getDateFromTimeStamp(actualArrivalTimeStamp);
          const reminderTime = (await getDataFromStorage(key + '_reminderTime')) ?? false;
          stationArrivalDate.setMinutes(stationArrivalDate.getMinutes() - Number(reminderTime));
          if (alarmDate.getTime() !== stationArrivalDate.getTime()) {
            //Updating the alarm with new schedule
            await removeAlarm();
            RailsModule.scheduleAlarm(
              stationArrivalDate.valueOf(),
              getHash(stationCode),
              labels.ALARM_TITLE,
              labels.ALARM_CONTENT.replace('{trainNo}', trainDetails?.Number)
                .replace('{stationName}', stationName)
                .replace('{reminderTime}', reminderTime),
            );
            setDataInStorage(key, stationArrivalDate.valueOf());
          }
          setAlarmSelect(true);
        } else {
          removeAlarm(key);
          setAlarmSelect(false);
        }
      } else {
        setAlarmSelect(false);
      }
    }
    isAlarmEnabled && getAlarms();
  }, [actualArrivalTime]);

  useEffect(() => {
    setShowIntermediateStations(false);

    const cleanup = () => {
      delayIndicatorImageStartWidth.current.stopAnimation();
      delayIndicatorWidth.current.stopAnimation();
      delayIndicatorImageEndWidth.current.stopAnimation();
    };

    return cleanup;
  }, []);

  useEffect(() => {
    let delayTimer1, delayTimer2, autoDelay;
    if (
      otherOptions?.offlineTracking &&
      (!currentStationDetail?.IntermediateStation?.Station?.code ||
        (currentStationDetail?.IntermediateStation?.Station?.code && !showIntermediateStations)) &&
      currentStoppingStationCheck
    ) {
      setIncorrectLocationVisibility(true);
    } else {
      setIncorrectLocationVisibility(false);
    }

    if (
      (!currentStationDetail?.IntermediateStation?.Station?.code ||
        (currentStationDetail?.IntermediateStation?.Station?.code && !showIntermediateStations)) &&
      currentStoppingStationCheck
    ) {
      [delayTimer1, delayTimer2] = delayAnimation(1800);
      autoDelay = setInterval(() => {
        [delayTimer1, delayTimer2] = delayAnimation(1800);
      }, 10400);
    }

    if (
      currentStoppingStationCheck &&
      currentStationDetail?.IntermediateStation?.Station?.code &&
      !showIntermediateStations
    ) {
      props.updateHeightArr(stopNumber - 1, 0);
      props.triggerScroll();
    }

    return () => {
      if (delayTimer1) {
        clearTimeout(delayTimer1);
      }
      if (delayTimer2) {
        clearTimeout(delayTimer2);
      }
      if (autoDelay) {
        clearInterval(autoDelay);
      }
    };
  }, [
    otherOptions?.offlineTracking,
    currentStationDetail?.StoppingStation?.Station?.code,
    currentStationDetail?.IntermediateStation?.Station?.code,
    showIntermediateStations,
  ]);

  useEffect(() => {
    if (currentStationDetail?.IntermediateStation?.Station?.code && currentStoppingStationCheck) {
      setShowIntermediateStations(true);
    }
  }, [
    currentStationDetail?.StoppingStation?.Station?.code,
    currentStationDetail?.IntermediateStation?.Station?.code,
  ]);

  const checkForFirstorLastStation = (stationCode) => {
    return stationCode === firstStationCode || stationCode === lastStationCode ? true : false;
  };

  const widthOfIndicator = !delay ? 52 : delayInMins.value >= 60 ? 112 : 92;
  const DelayIndicator = () => (
    <View style={styles.delayIndicatorTextContainer} testID="live_train_status_page_station_delay_indicator_container">
      <Animated.View
        style={[
          styles.delayIndicatorText,
          {
            backgroundColor: !delay ? 'rgba(14, 172, 159, 0.85)' : 'rgba(235, 32, 38, 0.85)',
            width: widthOfIndicator,
            transform: [
              { translateX: -(widthOfIndicator / 2) },
              { scaleX: delayIndicatorWidth.current },
              { translateX: widthOfIndicator / 2 },
            ],
          },
        ]}
        testID="live_train_status_page_station_delay_indicator_view"
      >
        {!delay ? (
          <Text numberOfLines={1} style={styles.delayText} testID="live_train_status_page_station_delay_indicator_text_ontime">
            {labels.ONTIME}
          </Text>
        ) : (
          <Text numberOfLines={1} style={styles.delayText} testID="live_train_status_page_station_delay_indicator_text_late">
            {labels.LATE[0]}
            <Text style={styles.boldFamily} testID="live_train_status_page_station_delay_indicator_text_late_short_string">{delayInMins.shortString}</Text>
            {labels.LATE[1]}
          </Text>
        )}
      </Animated.View>
      <Animated.Image
        source={!delay ? ltsDelayIndicatorGreenEnd : ltsDelayIndicatorRedEnd}
        style={{
          width: 12,
          transform: [
            { translateX: -6 },
            { scaleX: delayIndicatorImageEndWidth.current },
            { translateX: 6 },
          ],
          height: 22.7,
          left: -1,
        }}
        testID="live_train_status_page_station_delay_indicator_image"
      />
    </View>
  );

  const removeAlarm = async () => {
    await removeDataFromStorage(`${asyncStorageKeys.ALARM}${trainDetails?.Number}_${stationName}`);
    RailsModule.cancelAlarm(getHash(stationCode));
  };

  const alarmClockIcon = () => {
    if (parseInt(distance) !== 0) {
      return (
        <View
          style={[styles.alarmClockContainer, alarmSelect && styles.selectedAlarmClockContainer]}
          testID="live_train_status_page_station_alarm_clock_container"
        >
          <Image source={alarmClock} style={styles.alarmClock} testID="live_train_status_page_station_alarm_clock_image" />
        </View>
      );
    }
    return null;
  };

  const handleAlarmOnPress = async () => {
    if (alarmSelect) {
      trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, RIS_LTS_STATION_REMOVE_ALARM);
      showShortToast(`${labels.REMOVE_STATION_ALARM}`);
      setAlarmSelect(false);
      await removeAlarm();
    } else {
      trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, RIS_LTS_STATION_APPLY_ALARM);
      props.toggleAlarmVisibilityAndUpdateStationDetails(
        actualArrivalDate,
        actualArrivalTime,
        stationName,
        stationCode,
      );
    }
  };

  var canShowAlarmIcon =
    isAlarmEnabled && actualArrivalTime && stopNumber > currentStationStopNumber;

  if (canShowAlarmIcon) {
    const actualArrivalTimeStamp = actualArrivalDate
      ?.split(' ')[0]
      .concat(' ', actualArrivalTime || '');
    if (actualArrivalTimeStamp) {
      const timeDiff = getTimeDiffWithCurrentTime(actualArrivalTimeStamp);
      canShowAlarmIcon = timeDiff >= alarmMinimumTimeDiff;
    } else {
      canShowAlarmIcon = false;
    }
  }

  const IntermediateStationsDropDownText = () => {
    if (diverted) {
      return (
        <TouchableRipple onPress={() => setShowIntermediateStations((prevState) => !prevState)} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted">
          <View style={styles.showIntermediateStationsContainer} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted_view">
            {showIntermediateStations && (
              <>
                <Text style={styles.intermediateOptionTextDiverted} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted_text">
                  {labels.SHOW_INTERMEDIATE_DIVERTED}
                </Text>
                <Text style={[styles.boldFamily, { color: colors.yellow }]} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted_text_bold">
                  {'(' + item.IntermediateStations.length + ')'}
                </Text>
              </>
            )}
            {!showIntermediateStations && (
              <>
                <Text style={styles.intermediateOptionTextDiverted} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted_text">
                  {labels.SHOW_INTERMEDIATE_DIVERTED}
                </Text>
                <Text style={[styles.boldFamily, { color: colors.yellow }]} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted_text_bold">
                  {'(' + item.IntermediateStations.length + ')'}
                </Text>
              </>
            )}
            <View style={styles.greyBar} />
            {showIntermediateStations && <Image source={upArrow} style={styles.arrowIcon} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted_arrow_icon" />}
            {!showIntermediateStations && <Image source={downArrow} style={styles.arrowIcon} testID="live_train_status_page_station_intermediate_stations_dropdown_text_diverted_arrow_icon" />}
          </View>
        </TouchableRipple>
      );
    }

    return (
      <TouchableRipple onPress={() => setShowIntermediateStations((prevState) => !prevState)} testID="live_train_status_page_station_intermediate_stations_dropdown_text_cancelled">
        <View style={styles.showIntermediateStationsContainer}>
          {showIntermediateStations && (
            <Text style={styles.intermediateOptionText} testID="labels_intermediate_stations_dropdown_text_cancelled_text">{labels.HIDE_INTERMEDIATE}</Text>
          )}
          {!showIntermediateStations && (
            <Text style={styles.intermediateOptionText} testID="labels_intermediate_stations_dropdown_text_cancelled_text">
              <Text style={styles.boldFamily}>{item.IntermediateStations.length}</Text>
              {labels.SHOW_INTERMEDIATE}
            </Text>
          )}
          <View style={styles.greyBar} />
          {showIntermediateStations && <Image source={upArrow} style={styles.arrowIcon} testID="labels_intermediate_stations_dropdown_text_cancelled_arrow_up_icon" />}
          {!showIntermediateStations && <Image source={downArrow} style={styles.arrowIcon} testID="labels_intermediate_stations_dropdown_text_cancelled_arrow_down_icon" />}
        </View>
      </TouchableRipple>
    );
  };

  return (
    <View testID="live_train_status_page_station_container">
      <View
        style={[
          styles.container,
          !isEmpty(stationCode) &&
            !isEmpty(props.yourStop?.stationCode) &&
            stationCode === props.yourStop?.stationCode &&
            styles.yourStopContainer,
        ]}
        onLayout={(event) => {
          find_dimesions(event.nativeEvent.layout);
        }}
        testID="live_train_status_page_station_container_left_box"
      >
        <View style={styles.leftBox} testID="live_train_status_page_station_container_left_box_view">
          <Text
            numberOfLines={1}
            style={[
              styles.firstLineLeft,
              currentStoppingStationCheck
                ? { color: delay ? colors.red : colors.successGreen }
                : { color: stopNumber > currentStationStopNumber ? colors.textGrey : colors.black },
              { fontFamily: stopNumber <= currentStationStopNumber ? fonts.black : fonts.bold },
            ]}
            testID={`live_train_status_page_station_container_left_box_text_${stationCode}`}
          >
            {stationCode}
          </Text>
          <Text style={styles.secondLineLeft} testID={`live_train_status_page_station_container_left_box_text_${distance}`}>{parseInt(distance)} km</Text>
          {(alarmSelect || canShowAlarmIcon) && (
            <TouchableOpacity onPress={handleAlarmOnPress} testID="live_train_status_page_station_container_left_box_alarm_clock_button">{alarmClockIcon()}</TouchableOpacity>
          )}
        </View>
        <View style={styles.rightBoxContainer} testID="live_train_status_page_station_container_right_box_container">
          <View
            style={[
              styles.trackContainer,
              stationCode === lastStationCode ? styles.leftBorderWhite : leftBorderStyle2,
            ]}
            testID="live_train_status_page_station_container_right_box_track_container"
          >
            <View
              style={[
                { height: offset },
                styles.coloredTrack,
                stationCode === lastStationCode ? styles.leftBorderWhite : leftBorderStyle1,
              ]}
              testID="live_train_status_page_station_container_right_box_track_container_view"
            />
          </View>
          <View style={styles.flex1}>
            <View style={[styles.circleIcon, circleIconStyle]} />
            {currentStoppingStationCheck &&
              !currentStationDetail?.IntermediateStation?.Station?.code &&
              stoppedTrainCheck && (
                <Image source={currentIcon} style={[styles.currentImage, { top: -4 }]} testID="live_train_status_page_station_container_right_box_track_container_image" />
              )}
            {!isEmpty(stationCode) &&
              !isEmpty(props.yourStop?.stationCode) &&
              stationCode === props.yourStop?.stationCode && (
                <View style={styles.yourStopInnerContainer} testID="live_train_status_page_station_container_right_box_track_container_your_stop_inner_container">
                  <Text style={styles.yourStopText} testID={`live_train_status_page_station_container_right_box_track_container_your_stop_inner_container_text_${labels?.YOUR_STOP}`}>{labels.YOUR_STOP}</Text>
                </View>
              )}
            {stationCode === firstStationCode && (
              <View style={styles.startPointText} testID="live_train_status_page_station_container_right_box_track_container_start_point_text">
                <Text style={styles.pointLabel} testID={`live_train_status_page_station_container_right_box_track_container_start_point_text_${labels?.STARTING_POINT}`}>{labels.STARTING_POINT}</Text>
              </View>
            )}

            <View style={styles.firstSecondLineRightContainer} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container">
              <View style={styles.flex1} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_view">
                <View style={styles.notCurrentStationName} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_not_current_station_name">
                  <Text
                    numberOfLines={1}
                    style={[
                      styles.firstLineRight,
                      {
                        color: currentStoppingStationCheck
                          ? delay
                            ? colors.red
                            : colors.successGreen
                          : stopNumber > currentStationStopNumber
                          ? colors.textGrey
                          : colors.black,
                      },
                      {
                        fontFamily:
                          stopNumber <= currentStationStopNumber ? fonts.black : fonts.bold,
                      },
                    ]}
                    testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_not_current_station_name_text_${stationName}`}
                  >
                    {stationName}
                  </Text>
                </View>
              </View>
              <View style={styles.arrdepDetails}>
                <Text
                  style={[
                    styles.arrdepDetailsLabel,
                    {
                      color: stopNumber > currentStationStopNumber ? colors.textGrey : '#292929',
                    },
                  ]}
                  testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_arrdep_details_label_${labels.ARR}`}
                >
                  {labels.ARR}
                </Text>
                <Text
                  style={[
                    styles.arrdepDetailsExpt,
                    {
                      color:
                        actualArrivalDate + actualArrivalTime >
                        scheduledArrivalDate + scheduledArrivalTime
                          ? colors.red
                          : colors.successGreen,
                    },
                  ]}
                  testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_arrdep_details_expt_${actualArrivalTime}`}
                >
                  {stationCode !== firstStationCode ? actualArrivalTime : '-'}
                </Text>
                <Text style={styles.arrdepDetailsSchd} testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_arrdep_details_schd_${scheduledArrivalTime}`}>
                  {stationCode !== firstStationCode ? scheduledArrivalTime : '-'}
                </Text>
              </View>
            </View>

            <View style={styles.firstSecondLineRightContainer}>
              {diverted ? (
                <Text style={styles.platformDiverted} testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_platform_diverted_text_${labels.DIVERTED}`}>Diverted</Text>
              ) : cancelled ? (
                <Text style={styles.platformCancelled} testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_platform_cancelled_text_${labels.CANCELLED}`}>Cancelled</Text>
              ) : (
                <Text style={styles.platform} testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_platform_text_${labels.PLATFORM}`}>
                  {labels.PLATFORM}
                  <Text style={styles.boldText}>{expectedPlatformNo}</Text>
                </Text>
              )}
              {stationCode !== lastStationCode && (
                <View style={styles.arrdepDetails}>
                  <Text
                    style={[
                      styles.arrdepDetailsLabel,
                      {
                        color: stopNumber > currentStationStopNumber ? colors.textGrey : '#292929',
                      },
                    ]}
                    testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_arrdep_details_label_${labels.DEP}`}
                  >
                    {labels.DEP}
                  </Text>
                  <Text
                    style={[
                      styles.arrdepDetailsExpt,
                      {
                        color:
                          actualDepartureDate + actualDepartureTime >
                          scheduledDepartureDate + scheduledDepartureTime
                            ? colors.red
                            : colors.successGreen,
                      },
                    ]}
                    testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_arrdep_details_expt_${actualDepartureTime}`}
                  >
                    {actualDepartureTime}
                  </Text>
                  <Text style={styles.arrdepDetailsSchd} testID={`live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_arrdep_details_schd_${scheduledDepartureTime}`}>{scheduledDepartureTime}</Text>
                </View>
              )}
            </View>

            <View
              style={
                railsMealPokus && stopNumber >= currentStationStopNumber && isMealAvailableForStation(item)
                  ? styles.orderMealAndStopContainer
                  : {}
              }
              testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_order_meal_and_stop_container"
            >
              {railsMealPokus && stopNumber >= currentStationStopNumber && isMealAvailableForStation(item) && (
                <OrderMeal onOrderMealClicked={trackClickEvent} />
              )}
              {!checkForFirstorLastStation(stationCode) && (
                <View testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_stop_container">
                  <Text style={styles.stop} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_stop_container_text">
                    {haltMinutes !== undefined ? labels.STOP : ''}
                    <Text style={styles.boldFamily} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_stop_container_text_bold">
                      {haltMinutes !== undefined ? haltMinutes + 'm' : ''}
                    </Text>
                  </Text>
                </View>
              )}
            </View>
            {stationCode === lastStationCode && (
              <View style={styles.endPointText} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_end_point_text">
                <Text style={styles.pointLabel} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_end_point_text_text">{labels.ENDING_POINT}</Text>
              </View>
            )}

            <View style={styles.feedbackContainer} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_feedback_container">
              {incorrectLocationVisibility && (
                <TouchableRipple onPress={onFeedBackButtonClick} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_feedback_container_touchable_ripple">
                  <View testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_feedback_container_view">
                    <FeedBackButton />
                  </View>
                </TouchableRipple>
              )}
            </View>

            {item.IntermediateStations.length > 0 && (
              <View style={styles.intermediateOptionContainer} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_intermediate_option_container">
                <IntermediateStationsDropDownText />
              </View>
            )}
            {currentStoppingStationCheck &&
              (!currentStationDetail?.IntermediateStation?.Station?.code ||
                (currentStationDetail?.IntermediateStation?.Station?.code &&
                  !showIntermediateStations)) &&
              !stoppedTrainCheck && (
                <View style={[styles.currentLottieContainer, { top: -26 + offset }]} testID='live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_current_lottie_container'>
                  <Animated.Image
                    testID='live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_current_lottie_image'
                    style={[
                      styles.delayIndicatorImage,
                      {
                        width: 12,
                        transform: [
                          { translateX: -6 },
                          { scaleX: delayIndicatorImageStartWidth.current },
                          { translateX: 6 },
                        ],
                      },
                    ]}
                    source={!delay ? ltsDelayIndicatorGreenStart : ltsDelayIndicatorRedStart}
                  />
                  <LottieView
                    source={currentLottieIcon}
                    loop={true}
                    autoPlay={true}
                    progress={0}
                    style={styles.currentLottie}
                    testID='live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_current_lottie_lottie_view'
                  />
                  <DelayIndicator />
                </View>
              )}
          </View>
        </View>
      </View>
      {showIntermediateStations && (
        <IntermediateStations
          stopNumber={stopNumber}
          cancelled={cancelled}
          diverted={diverted}
          currentStationStopNumber={currentStationStopNumber}
          IntermediateStations={item.IntermediateStations}
          currentIntermediateStopNumber={currentStationDetail?.IntermediateStation?.StopNumber}
          updateHeightArr={props.updateHeightArr}
          triggerScroll={props.triggerScroll}
          otherOptions={otherOptions}
          onFeedBackButtonClick={onFeedBackButtonClick}
          hideIntermediateStations={() => setShowIntermediateStations(false)}
        />
      )}

      {showIntermediateStations && (
        <View style={styles.container} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_intermediate_stations_container">
          <View style={styles.leftBottomContainer} testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_intermediate_stations_container_left_bottom_container" />
          <View
            style={[
              styles.flex1,
              styles.rightBoxSpacing,
              stationCode === lastStationCode ? styles.leftBorderWhite : leftBorderStyle2,
              styles.whiteBackground,
            ]}
            testID="live_train_status_page_station_container_right_box_track_container_first_second_line_right_container_intermediate_stations_container_right_box_spacing"
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingRight: 12,
    paddingLeft: 2,
  },
  yourStopContainer: {
    backgroundColor: colors.lighterBlue,
    borderRadius: 10,
  },
  stop: {
    textAlign: 'right',
    flexDirection: 'row',
    marginVertical: 10,
    fontFamily: fonts.regular,
    fontSize: 14,
    color: colors.defaultTextColor,
    justifyContent: 'space-between',
  },
  delayIndicatorImage: {
    position: 'absolute',
    left: 47,
    height: 22.7,
    width: 12,
    top: 2,
  },
  delayIndicatorTextContainer: {
    flexDirection: 'row',
    position: 'absolute',
    left: 58.7,
    top: 2,
  },
  delayIndicatorText: {
    justifyContent: 'center',
    height: 19,
  },
  delayText: {
    fontStyle: 'italic',
    fontFamily: fonts.medium,
    fontSize: 10,
    color: colors.white,
    paddingVertical: 3,
  },
  normalContainer: { backgroundColor: colors.white },
  leftBox: {
    width: 80,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  boldFamily: {
    fontFamily: fonts.bold,
    flexDirection: 'row',
    alignItems: 'center',
  },
  orderMealAndStopContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  flex1: { flex: 1 },
  rightBoxContainer: { flexDirection: 'row', flex: 1 },
  rightBoxSpacing: {
    borderLeftWidth: 6,
    height: 10,
    width: 0,
  },
  trackContainer: {
    width: 6,
    height: '100%',
    left: -1.5,
    marginTop: 1.5,
    marginBottom: -1.5,
  },
  coloredTrack: {
    width: 6,
    position: 'absolute',
  },
  leftBorderGrey: { borderColor: colors.lightGrey, backgroundColor: colors.lightGrey },
  leftBorderWhite: { borderColor: colors.white, backgroundColor: colors.white },
  leftBorderRed: { borderColor: colors.red, backgroundColor: colors.red },
  leftBorderGreen: { borderColor: colors.successGreen, backgroundColor: colors.successGreen },
  leftBorderYellow: { borderColor: colors.yellow, backgroundColor: colors.yellow },
  whiteBackground: { backgroundColor: colors.white },
  circleIcon: {
    position: 'absolute',
    left: -11.5,
    width: 14,
    height: 14,
    backgroundColor: colors.white,
    borderWidth: 3,
    borderStyle: 'solid',
    borderRadius: 100,
  },
  currentImage: {
    position: 'absolute',
    width: 30,
    height: 30,
    left: -19.5,
  },
  currentLottieContainer: {
    position: 'absolute',
    left: -39.5,
  },
  currentLottie: {
    width: 70,
    height: 70,
  },
  yourStopInnerContainer: {
    position: 'absolute',
    top: -18,
    paddingHorizontal: 5,
    borderRadius: 10,
    backgroundColor: colors.lighterBlue,
    paddingVertical: 2,
    right: 5,
  },
  yourStopText: {
    fontFamily: fonts.black,
    fontSize: 10,
    color: colors.azure,
  },
  firstLineLeft: {
    marginRight: 20,
    color: colors.black,
    fontFamily: fonts.black,
    fontSize: 14,
  },
  secondLineLeft: {
    marginRight: 20,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    marginTop: 8,
    fontSize: 14,
    marginBottom: 5,
  },
  pointLabel: {
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    fontSize: 14,
  },
  firstSecondLineRightContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  notCurrentStationName: {
    marginLeft: 20,
  },
  arrdepDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrdepDetailsLabel: {
    fontFamily: fonts.bold,
    fontSize: 13,
  },
  arrdepDetailsExpt: {
    fontFamily: fonts.black,
    fontSize: 14,
    marginLeft: 10,
    width: 38,
    textAlign: 'center',
  },
  arrdepDetailsSchd: {
    fontFamily: fonts.regular,
    fontSize: 14,
    color: colors.defaultTextColor,
    marginLeft: 12,
    width: 38,
    textAlign: 'center',
  },
  firstLineRight: {
    color: colors.black,
    fontFamily: fonts.black,
    fontSize: 14,
    marginRight: 10,
  },
  platform: {
    marginLeft: 20,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    fontSize: 14,
  },
  platformCancelled: {
    marginLeft: 20,
    color: colors.red,
    fontFamily: fonts.bold,
    fontSize: 14,
  },
  platformDiverted: {
    marginLeft: 20,
    color: colors.yellow,
    fontFamily: fonts.bold,
    fontSize: 14,
  },
  fifthLineRight: {
    paddingLeft: 20,
    paddingTop: 5,
    color: colors.azure,
    fontFamily: fonts.regular,
    fontSize: 12,
  },
  regularText: {
    fontFamily: fonts.regular,
    fontSize: 14,
  },
  boldText: {
    fontFamily: fonts.black,
    fontSize: 14,
  },
  feedbackContainer: {
    paddingBottom: 10,
    alignItems: 'flex-end',
  },
  intermediateOptionContainer: {
    marginLeft: 20,
    marginTop: 5,
    marginBottom: 20,
  },
  showIntermediateStationsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 3,
  },
  intermediateOptionText: {
    fontFamily: fonts.medium,
    color: colors.azure,
    fontSize: 13,
  },
  intermediateOptionTextDiverted: {
    fontFamily: fonts.medium,
    color: colors.yellow,
    fontSize: 13,
    marginRight: 3,
  },
  intermediateOptionTextCancelled: {
    fontFamily: fonts.medium,
    color: colors.red,
    fontSize: 13,
    marginRight: 3,
  },
  greyBar: {
    alignSelf: 'center',
    height: 1,
    backgroundColor: colors.grey4,
    flex: 1,
    marginLeft: 16,
    marginRight: 10,
  },
  arrowIcon: {
    height: 9,
    width: 12,
  },
  leftBottomContainer: {
    height: '100%',
    width: 78.5,
  },
  dummyHeightToAvoidFlicker: {
    height: 21,
  },
  alarmClock: {
    height: 17,
    width: 17,
  },
  alarmClockContainer: {
    height: 25,
    width: 25,
    backgroundColor: colors.transparent,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 25 / 2,
  },
  selectedAlarmClockContainer: {
    backgroundColor: colors.yellow,
  },
  endPointText: {
    marginBottom: 8,
    marginLeft: 20,
    marginTop: 10,
  },
  startPointText: {
    position: 'absolute',
    left: 20,
    top: -25,
  },
});

Station.propTypes = {
  firstStation: PropTypes.object,
  lastStation: PropTypes.object,
  trainDetails: PropTypes.object,
  currentStationDetail: PropTypes.object,
  item: PropTypes.object,
  otherOptions: PropTypes.object,
  onFeedBackButtonClick: PropTypes.func,
  isAlarmEnabled: PropTypes.bool,
  railsMealPokus: PropTypes.bool,
  item: PropTypes.object,
  updateHeightArr: PropTypes.func,
  triggerScroll: PropTypes.func,
  yourStop: PropTypes.object,
};

export default Station;
