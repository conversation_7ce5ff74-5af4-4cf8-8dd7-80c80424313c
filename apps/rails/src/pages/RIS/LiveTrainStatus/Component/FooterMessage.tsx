import React from 'react';
import { View } from 'react-native';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import {
  EVENT_RIS_LTS_FOOTER_CLICK,
  PAGE_RIS_LTS_INFO,
  trackOmnitureLTSClickEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import FooterCommonComponent from './FooterCommonComponent';
import { CurrentStation, IntermediateStation, StoppingStation, TrainDetail } from '../interface';

interface Props {
  onPress?: () => void;
  lastAPIFetched: Date;
  currentStationDetail: CurrentStation;
  prevStationDetail: StoppingStation | IntermediateStation;
  trainDetails: TrainDetail;
  onRefresh?: (isManualRefresh?: boolean) => void;
  isInPIPMode: boolean;
  trainsLiveGPSSpeed: number;
  isInsideTrain: boolean;
}

const FooterMessage = ({
  onPress,
  lastAPIFetched,
  currentStationDetail,
  prevStationDetail,
  trainDetails,
  onRefresh,
  isInPIPMode = false,
  trainsLiveGPSSpeed,
  isInsideTrain,
}: Props) => {
  const onPressClicked = () => {
    onPress && onPress();
    trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, EVENT_RIS_LTS_FOOTER_CLICK);
  };
  const trainStatus = {
    started: trainDetails?.Departed,
    terminated: trainDetails?.Terminated,
  };

  return (
    <TouchableRipple onPress={onPressClicked} testID="footer_message_touchable_ripple">
      {/* Added this redundant View as without this TouchableRipple was losing its onClick callback functionality */}
      <View testID="footer_message_view">
        <FooterCommonComponent
          lastAPIFetched={lastAPIFetched}
          onRefresh={onRefresh}
          currentStationDetail={currentStationDetail}
          prevStationDetail={prevStationDetail}
          trainStatus={trainStatus}
          isInPIPMode={isInPIPMode}
          trainsLiveGPSSpeed={trainsLiveGPSSpeed}
          isInsideTrain={isInsideTrain}
        />
      </View>
    </TouchableRipple>
  );
};

export default FooterMessage;
