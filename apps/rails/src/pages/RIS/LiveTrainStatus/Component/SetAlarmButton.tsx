import React from 'react';
import selfStyles from '../../../RisLiveTrainStatusForm/Styles/selfStyles';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { View, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {trackOmnitureLTSClickEvent,RIS_LTS_STATION_OK_CLICKED,PAGE_RIS_LTS_INFO} from '../../../../RisAnalytics/RisAnalytics';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { labels } from '../constants';

interface Props {
    buttonText: string;
    callback: () => void;
}

const SetAlarmButton = (props: Props) => (
    <View style={selfStyles.touchableDoneV2}>
        <TouchableRipple style={selfStyles.doneButtonContainerV2} 
        onPress={() => {props.callback(); props.buttonText === labels.OKAY && 
        trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO,RIS_LTS_STATION_OK_CLICKED); }} testID="set_alarm_button">
            <LinearGradient
                style={selfStyles.doneButtonContainerV2}
                colors={[colors.lightBlue, colors.darkBlue]}
                start={{ x: 0.0, y: 0.5 }}
                end={{ x: 1.0, y: 0.5 }}
            >
                <Text style={selfStyles.doneButtonV2}>{props.buttonText}</Text>
            </LinearGradient>
        </TouchableRipple>
    </View>
);

export default SetAlarmButton;
