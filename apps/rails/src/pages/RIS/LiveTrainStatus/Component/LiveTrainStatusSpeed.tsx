import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { CurrentStation } from 'apps/rails/src/pages/RIS/LiveTrainStatus/interface';
import { getTrainRunningStatus } from 'apps/rails/src/pages/RIS/LiveTrainStatus/Util/OfflineTrackingHelper';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { SPEED_STATUS } from 'apps/rails/src/Utils/RailsConstant';

interface Props {
  delayInMins: unknown;
  delay: boolean;
  trainsLiveGPSSpeed: number;
  currentStationDetail: CurrentStation;
}

const TrainStatusMap: Record<string, string>  = {
  ON_TIME: _label('on_time'),
  DELAY: _label('delay'),
  PLANNED_HALT: _label('planned_halt'),
  UNPLANNED_HALT: _label('unplanned_halt'),
  NA: '...',
};

export const LiveTrainStatusSpeed = (props: Props) => {
  const { delay, delayInMins, trainsLiveGPSSpeed = -1, currentStationDetail } = props;
  const { StoppingStation, IntermediateStation } = currentStationDetail || {};
  const currentStation = IntermediateStation || StoppingStation;
  const trainRunningStatus = getTrainRunningStatus(delay, trainsLiveGPSSpeed, currentStation);
  return (
    <>
      <View style={styles.container} testID="footer_message_speed_container">
        <View style={styles.row}>
          <Text style={styles.labelText} testID="footer_message_speed_container_label_text">
            {_label('train_speed')}
          </Text>
          <Text style={styles.valueText} testID="footer_message_speed_container_value_text">
            {trainsLiveGPSSpeed < 0 ? '...' : `${trainsLiveGPSSpeed?.toFixed(1)} Km/Hr`}
          </Text>
        </View>
        <View style={styles.row} testID="footer_message_speed_container_row">
          <Text style={styles.labelText} testID="footer_message_speed_container_label_text_two">
            {_label('status')}
          </Text>
          <Text
            style={[
              styles.valueText,
              trainRunningStatus === SPEED_STATUS.ON_TIME ? styles.onTimeText : styles.delayText,
            ]}
            testID="footer_message_speed_container_value_text_two"
          >
            {`${trainRunningStatus === SPEED_STATUS.DELAY ? delayInMins?.value + ' ' : ''}${
              TrainStatusMap[trainRunningStatus]
            }`}
          </Text>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginLeft: 18,
  },
  row: {
    flexDirection: 'row',
  },
  labelText: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.defaultTextColor,
    marginBottom: 4,
  },
  valueText: {
    fontFamily: fonts.bold,
    fontSize: 12,
    color: colors.azure,
    marginLeft: 14,
  },
  noValueText: {
    fontFamily: fonts.bold,
    fontSize: 12,
    color: colors.lightTextColor,
    marginLeft: 14,
  },
  onTimeText: {
    color: colors.lightGreen4,
  },
  delayText: {
    color: colors.red,
  },
});
