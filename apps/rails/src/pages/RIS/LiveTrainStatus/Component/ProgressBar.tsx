import React, { useRef, useEffect } from 'react';
import { Animated, Easing, View } from 'react-native';

interface Props {
  width: number;
  height: number;
  progressedColor: string;
  unprogressedColor: string;
  borderRadius: number;
}

const ProgressBar = ({
  width,
  height,
  progressedColor,
  unprogressedColor,
  borderRadius,
}: Props) => {
  const progressedBarWidth = useRef(new Animated.Value(0)).current;
  const currentWidth = useRef(0);

  useEffect(() => {
    animate();

    return () => {
      progressedBarWidth.stopAnimation();
      progressedBarWidth.setValue(0);
    };
  }, []);

  const animate = () => {
    const _toValue = currentWidth.current + (width - currentWidth.current) / 2;
    currentWidth.current = _toValue;
    Animated.timing(progressedBarWidth, {
      toValue: _toValue,
      duration: 1000,
      easing: Easing.linear,
      useNativeDriver: false,
    }).start((o) => {
      if (o.finished) {animate();}
    });
  };

  return (
    <View
      style={{
        width: width,
        height: height,
        backgroundColor: unprogressedColor,
        borderRadius: borderRadius,
      }}
    >
      <Animated.View
        style={{
          width: progressedBarWidth,
          height: height,
          backgroundColor: progressedColor,
          borderRadius: borderRadius,
        }}
      />
    </View>
  );
};

export default ProgressBar;
