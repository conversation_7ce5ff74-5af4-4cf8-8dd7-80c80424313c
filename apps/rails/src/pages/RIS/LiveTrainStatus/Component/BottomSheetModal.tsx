import React, { PropsWithChildren } from 'react';
import {
  StyleSheet,
  View,
  TouchableWithoutFeedback,
  Platform,
  ViewStyle,
  GestureResponderEvent,
} from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface Props {
  onTouchOutside: () => void;
  additionalContainerStyle?: ViewStyle;
}

const BottomSheetModal: React.FC<PropsWithChildren<Props>> = ({
  additionalContainerStyle = {},
  onTouchOutside = () => {
    // Default function does nothing
  },
  children,
}) => {
  const _onTouchOutside = (e: GestureResponderEvent) => {
    if (Platform.OS === 'web') {
      e.preventDefault();
    }
    onTouchOutside();
  };
  return (
    <View style={[styles.container, additionalContainerStyle]}>
      <TouchableWithoutFeedback onPress={_onTouchOutside}>
        <View style={{ flex: 1 }} />
      </TouchableWithoutFeedback>
      <View>{children}</View>
    </View>
  );
};

const getElevation = () => {
  if (Platform.OS === 'ios') {
    return getPlatformElevation(100);
  }
  return getPlatformElevation(1000);
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
    flex: 1,
    backgroundColor: colors.lightBlack4,
    ...getElevation(),
  },
});

export default BottomSheetModal;
