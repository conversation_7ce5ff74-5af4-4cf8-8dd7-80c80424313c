/* eslint-disable */
import React, { useEffect, useState } from 'react';
import { setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { View, Text, Image, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { labels, asyncStorageKeys, alarmData } from '../constants';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import SetAlarmButton from './SetAlarmButton';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import {trackOmnitureLTSClickEvent,PAGE_RIS_LTS_INFO, RIS_LTS_STATION_ALARM_TIME,trackOmnitureAlarmLoadEvent,RIS_LTS_STATION_ALARM_SUCCESS_BOTTOMSHEET,RIS_LTS_STATION_BOTTOMSHEET_SET_ALARM_CLICKED} from '../../../../RisAnalytics/RisAnalytics';
import {
  getDateFromTimeStamp,
  getHash,
  getTimeDiffWithCurrentTime,
} from './commonUtils';

import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';
import alarmClock from '@mmt/legacy-assets/src/alarm_clock.webp';

interface StationDetails {
  estimatedArrivalDate: string;
  estimatedArrivalTime: string;
  currentStationName: string;
  trainNumber: string;
  currentStationCode: string;
}

interface Props {
  toggleAlarmVisibilityAndUpdateStationDetails: () => void;
  currentStationDetailsForAlarm: StationDetails;
  toggleKeysForStationReRender: () => void;
}

const AlarmSetterBottomSheet = ({
  toggleAlarmVisibilityAndUpdateStationDetails,
  currentStationDetailsForAlarm,
  toggleKeysForStationReRender,
}: Props) => {
  const {
    estimatedArrivalDate,
    estimatedArrivalTime,
    currentStationName,
    currentStationCode,
    trainNumber,
  } = currentStationDetailsForAlarm || {};
  const [selectedId, setSelectedId] = useState(0);
  const [stationAlarm, setStationAlarm] = useState(false);

  const actualArrivalTimeStamp = estimatedArrivalDate
    ?.split(' ')[0]
    .concat(' ', estimatedArrivalTime || '');
  const timeDiff = getTimeDiffWithCurrentTime(actualArrivalTimeStamp);

  useEffect(()=>{
    trackOmnitureAlarmLoadEvent(RIS_LTS_STATION_ALARM_SUCCESS_BOTTOMSHEET);
  },[]);

  const _renderHeader = () => (
    <View style={bottomSheetStyles.headerContainer} testID="alarm_setter_bottomsheet_header_container">
      <Text style={bottomSheetStyles.headerText}>{labels.ALARM_CLOCK_BOTTOMSHEET_TITLE}</Text>
      <TouchableRipple onPress={() => toggleAlarmVisibilityAndUpdateStationDetails()} testID="alarm_setter_bottomsheet_close_button">
        <View style={bottomSheetStyles.headerIconContainer}>
          <Image source={closeIcon} style={bottomSheetStyles.headerIcon} />
        </View>
      </TouchableRipple>
    </View>
  );

  const handleOnClick = async () => {
    try {
      trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO,RIS_LTS_STATION_BOTTOMSHEET_SET_ALARM_CLICKED);
      const alarmDate = getDateFromTimeStamp(actualArrivalTimeStamp);
      alarmDate.setMinutes(alarmDate.getMinutes() - Number(alarmData[selectedId]));
      trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO,`${RIS_LTS_STATION_ALARM_TIME}${Number(alarmData[selectedId])}`);
      RailsModule.scheduleAlarm(
        alarmDate.valueOf(),
        getHash(currentStationCode),
        labels.ALARM_TITLE,
        labels.ALARM_CONTENT.replace('{trainNo}', trainNumber)
          .replace('{stationName}', currentStationName)
          .replace('{reminderTime}', alarmData[selectedId]),
      );
      setStationAlarm(true);
      const key = `${asyncStorageKeys.ALARM}${trainNumber}_${currentStationName}`;
      setDataInStorage(key, alarmDate.valueOf());
      setDataInStorage(key + '_reminderTime', alarmData[selectedId]);
      toggleKeysForStationReRender();
    } catch (error) {
      console.error('Error while setting alarm ', error);
    }
  };

  const _renderAlarmList = ({ item, index }: { item: string; index: number }) => {
    if (timeDiff <= Number(item)) {return null;}
    return (
      <View testID="alarm_setter_bottomsheet_alarm_list_item">
        <Card elevation={3} style={bottomSheetStyles.cardCss}>
          <TouchableOpacity onPress={() => setSelectedId(index)} activeOpacity={1} testID="alarm_setter_bottomsheet_alarm_list_item_button">
            <View
              style={
                selectedId === index
                  ? bottomSheetStyles.cardViewAzure
                  : bottomSheetStyles.cardViewWhite
              }
              testID="alarm_setter_bottomsheet_alarm_list_item_view"
            >
              <Text
                style={
                  selectedId !== index
                    ? bottomSheetStyles.cardViewTextBlack
                    : bottomSheetStyles.cardViewTextWhite
                }
                testID="alarm_setter_bottomsheet_alarm_list_item_text"
              >
                {item}
              </Text>
              <Text
                style={
                  selectedId !== index
                    ? bottomSheetStyles.minutesLightColor
                    : bottomSheetStyles.minutesWhiteColor
                }
              >
                {labels.MINUTES}
              </Text>
            </View>
          </TouchableOpacity>
        </Card>
      </View>
    );
  };

  const keyExtractor = (item: string, index: number) => `${index}-${item}`;
  const DisplayAlarmDetails = () => {
    return (
      <View style={bottomSheetStyles.container} testID="alarm_setter_bottomsheet_display_alarm_details_container">
        <View style={bottomSheetStyles.alarmSetContainer}>
          <Text style={[bottomSheetStyles.headerText, bottomSheetStyles.centerText]}>
            {labels.ALARM_SET}
          </Text>
          <Image source={alarmClock} style={bottomSheetStyles.alarmClock} />
          <View>
            <Text style={bottomSheetStyles.alarmClockAlert}>
              {labels.ALARM_ALERT}
              <Text
                style={bottomSheetStyles.alarmClockAlertBold}
              >{`${alarmData[selectedId]}`}</Text>
              {labels.ALARM_ALERT_BEFORE}
            </Text>
            <Text style={bottomSheetStyles.alarmClockAlert}>
              {labels.TRAIN_REACHES}{' '}
              <Text style={bottomSheetStyles.alarmClockAlertBold}>{`${currentStationName}`}</Text>
            </Text>
          </View>
          <SetAlarmButton
            buttonText={labels.OKAY}
            callback={toggleAlarmVisibilityAndUpdateStationDetails}
          />
        </View>
      </View>
    );
  };

  const SetAlarmDetails = () => {
    return (
      <View style={bottomSheetStyles.container}>
        <View style={bottomSheetStyles.optionListContainer}>{_renderHeader()}</View>
        <View>
          <Text style={bottomSheetStyles.stopText}>{labels.YOUR_STOP_MSG}</Text>
        </View>
        <View style={bottomSheetStyles.stationView}>
          <View style={bottomSheetStyles.stationContainer}>
            <Text numberOfLines={1} ellipsizeMode="tail" style={bottomSheetStyles.stationTitle}>
              {currentStationName}
            </Text>
          </View>
          <View style={bottomSheetStyles.row}>
            <Text style={[bottomSheetStyles.stationTitle, bottomSheetStyles.font12]}>ETA :</Text>
            <View style={bottomSheetStyles.etaCssTextPadding}>
              <Text style={bottomSheetStyles.etaCss}>{estimatedArrivalTime}</Text>
            </View>
          </View>
        </View>
        <View style={bottomSheetStyles.alertMeBeforeContainer}>
          <Text style={bottomSheetStyles.stopText}>{labels.ALERT_ME_BEFORE}</Text>
        </View>
        <View>
          <FlatList
            data={alarmData}
            horizontal
            showsHorizontalScrollIndicator={false}
            renderItem={_renderAlarmList}
            keyExtractor={keyExtractor}
          />
        </View>
        <View style={bottomSheetStyles.fullFlex} />
        <SetAlarmButton buttonText={labels.SET_ALARM} callback={handleOnClick} />
      </View>
    );
  };

  return stationAlarm ? <DisplayAlarmDetails /> : <SetAlarmDetails />;
};

export default AlarmSetterBottomSheet;

export const bottomSheetStyles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 20,
    backgroundColor: colors.white,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    paddingLeft: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontFamily: fonts.bold,
    color: colors.black,
    fontSize: 21,
  },
  centerText: {
    textAlign: 'center',
  },
  headerIconContainer: {
    height: 21,
    width: 25,
    paddingVertical: 2,
  },
  headerIcon: {
    width: 17,
    height: 17,
    marginRight: 8,
  },
  optionListContainer: {
    marginTop: 4,
  },
  alarmSetContainer: {
    marginTop: 18,
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  stopText: {
    color: colors.lightTextColor,
    fontSize: 14,
    fontFamily: fonts.medium,
    marginBottom: 5,
  },
  stationView: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
    borderBottomWidth: 1,
    paddingBottom: 8,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: colors.white,
    borderBottomColor: colors.greyBookedSeat,
  },
  stationTitle: {
    color: colors.black,
    fontSize: 16,
    fontFamily: fonts.bold,
    lineHeight: 18,
  },
  font12: {
    fontSize: 12,
  },
  alarmItem: {
    backgroundColor: colors.white,
    marginVertical: 18,
    marginHorizontal: 16,
    textAlign: 'center',
    padding: 5,
    shadowRadius: 1,
    height: 200,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowColor: colors.brown3,
  },
  time: {
    fontSize: 18,
    lineHeight: 13,
  },
  minutesLightColor: {
    fontSize: 10,
    lineHeight: 13,
    color: colors.lightTextColor,
  },
  minutesWhiteColor: {
    fontSize: 10,
    lineHeight: 13,
    color: colors.white,
  },
  alarmClock: {
    height: 69,
    width: 69,
    marginTop: 16,
    marginBottom: 23,
    alignSelf: 'center',
  },
  alarmClockAlert: {
    fontSize: 16,
    lineHeight: 22,
    color: colors.black4,
    fontFamily: fonts.semiBold,
    textAlign: 'center',
  },
  alarmClockAlertBold: {
    fontSize: 16,
    lineHeight: 22,
    color: colors.black,
    fontFamily: fonts.bold,
  },
  cardCss: {
    height: 50,
    width: 70,
    marginRight: 8,
  },
  cardViewAzure: {
    height: 50,
    width: 70,
    borderWidth: 0.5,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: colors.azure,
    backgroundColor: colors.azure,
    borderRadius: 3,
  },
  cardViewWhite: {
    height: 50,
    width: 70,
    borderWidth: 0.5,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: colors.white,
    backgroundColor: colors.white,
    borderRadius: 3,
  },
  cardViewTextBlack: {
    fontFamily: fonts.bold,
    fontSize: 18,
    alignSelf: 'center',
    color: colors.black,
  },
  cardViewTextWhite: {
    fontFamily: fonts.bold,
    fontSize: 18,
    alignSelf: 'center',
    color: colors.white,
  },
  etaCss: {
    fontSize: 12,
    color: colors.easternBlue,
  },
  etaCssTextPadding: {
    paddingTop: 1,
    paddingLeft: 5,
    display: 'flex',
  },
  stationContainer: {
    width: '70%',
  },
  row: {
    flexDirection: 'row',
  },
  alertMeBeforeContainer: {
    marginBottom: 5,
  },
  fullFlex: {
    flex: 1,
  },
});
