import React from 'react';
import { View, Text, Image, TouchableWithoutFeedback, StyleSheet } from 'react-native';
import downArrow from '@mmt/legacy-assets/src/down_arrow_blue.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
// eslint-disable-next-line
import { labels } from '@mmt/rails/src/pages/RIS/LiveTrainStatus/constants';
import { isEmpty } from 'lodash';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';

const StartDate = (props) => {
  if (isEmpty(props?.value) || props?.value?.includes('undefined')) {
    return null;
  }
  return (
    <TouchableWithoutFeedback onPress={props.onPress}>
      <View style={styles.container}>
        <View style={styles.innerContainer}>
          <Text style={[styles.leftText, fontStyle('black')]}>{labels.STARTING}</Text>
          <View style={styles.innerContainer}>
            <Text style={[styles.rightText, fontStyle('bold')]}>{props.value}</Text>
            <Image source={downArrow} style={styles.arrowIcon} />
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

StartDate.propTypes = {
  value: PropTypes.string,
  onPress: PropTypes.func,
};

export default StartDate;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginTop: 5,
    marginBottom: 10,
    borderWidth: 2,
    borderRadius: 5,
    padding: 5,
    borderColor: colors.lightGrey,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  innerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftText: {
    fontSize: 14,
    color: colors.black,
  },
  rightText: {
    paddingHorizontal: 10,
    color: colors.azure,
  },
  arrowIcon: {
    width: 12,
    height: 9,
    alignSelf: 'center',
  },
});
