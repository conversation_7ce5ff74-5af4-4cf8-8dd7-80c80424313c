import React, { useEffect, useState, useRef } from 'react';
import { View, Image, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView,  LayoutAnimation } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels } from '../constants';
import { bottomSheetStyles } from './TrackingOptionBottomSheet';
import LinearGradient from 'react-native-linear-gradient';
import { CurrentStation, FeedbackData, TrainDetail } from '../interface';
import CheckBox from 'react-native-checkbox';
import { getTimeStringFromDate } from './commonUtils';


import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';
import stopIcon from '@mmt/legacy-assets/src/ltsStopIcon.webp';
import coloredStar from '@mmt/legacy-assets/src/BaseStar.webp';
import train from '@mmt/legacy-assets/src/FeedbackTrainIcon.webp';
import emptyStar1 from '@mmt/legacy-assets/src/emptyStar1.webp';
import emptyStar2 from '@mmt/legacy-assets/src/emptyStar2.webp';
import emptyStar3 from '@mmt/legacy-assets/src/emptyStar3.webp';
import emptyStar4 from '@mmt/legacy-assets/src/emptyStar4.webp';
import emptyStar5 from '@mmt/legacy-assets/src/emptyStar5.webp';
import fillStar1 from '@mmt/legacy-assets/src/Star1fill.webp';
import fillStar2 from '@mmt/legacy-assets/src/Star2fill.webp';
import fillStar3 from '@mmt/legacy-assets/src/Star3fill.webp';
import fillStar4 from '@mmt/legacy-assets/src/Star4fill.webp';
import fillStar5 from '@mmt/legacy-assets/src/Star5fill.webp';
import groupIcon from '@mmt/legacy-assets/src/rails/group_icon.webp';
const unfilledStars: unknown[] = [emptyStar1, emptyStar2, emptyStar3, emptyStar4, emptyStar5];
const filledStars: unknown[] = [fillStar1, fillStar2, fillStar3, fillStar4, fillStar5];
const LTS_TEXT = 'Live Train Status';
const THANKS_FEEDBACK = 'Thank you for your feedback!';
import CB_ENABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-active.webp';
import CB_DISABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-inactive.webp';


const FEEDBACKS =
  [
    {
      'Question': 'What went Wrong?',
      'Answers': ['Switching to online', 'Arrival time wrong', 'Train not clean', 'Wrong delay info', 'Refresh not working', 'Very slow loading', 'Departure time incorrect'],
    },
    {
      'Question': 'What went Wrong?',
      'Answers': ['Switching to online', 'Arrival time wrong', 'Train not clean', 'Wrong delay info', 'Refresh not working', 'Very slow loading', 'Departure time incorrect'],
    },
    {
      'Question': 'What went Wrong?',
      'Answers': ['Switching to online', 'Arrival time wrong', 'Train not clean', 'Wrong delay info', 'Refresh not working', 'Very slow loading', 'Departure time incorrect'],
    },
    {
      'Question': 'What can be improved?',
      'Answers': ['Delay information', 'Frequent switch to online', 'Platform number', 'Improve Design', 'Departure timings', 'Need more station info', 'Loading takes time'],
    },
    {
      'Question': 'What did you like?',
      'Answers': ['Good design', 'Accurate arrival times', 'Perfect functioning without internet', 'Smooth experience', 'Accurate delay info', 'Correct Departure timings'],
    },
  ];
const initialFeedback: unknown = { rating: 0 };

FEEDBACKS.forEach((val: unknown) => val.Answers.forEach((element: unknown) => {
  initialFeedback[element] = false;
}),
);

interface Props {
  onSendButtonClick: (feedback: FeedbackData) => void;
  onCloseButtonClick: () => void;
  trainDetails: TrainDetail;
  currentStationDetail: CurrentStation;
  stationsSectionList: unknown;
}

const StarBasedFeedbackBottomSheet = ({
  onSendButtonClick,
  onCloseButtonClick,
  trainDetails,
  currentStationDetail,
  stationsSectionList }: Props) => {

  const [feedback, setFeedback] = useState(initialFeedback);
  const [page, setPage] = useState(1);
  const [textFeedback, setTextFeedback] = useState<string>('');
  const [stationData, setStationData] = useState({});

  const feedbackRef = useRef(feedback);
  const textFeedbackRef = useRef(textFeedback);
  const stationDataRef = useRef(stationData);

  useEffect(() => {
    textFeedbackRef.current = textFeedback;
    feedbackRef.current = feedback;
    stationDataRef.current = stationData;
  }, [feedback, textFeedback, stationData]);

  useEffect(() => {
    return () => {
      if (feedbackRef.current.rating !== 0) {
        onSendButtonClick(feedbackToText(feedbackRef.current, textFeedbackRef.current, stationDataRef.current));
      }
    };
  }, []);

  const onChange = (textFeedBack: string) => {
    setTextFeedback(textFeedBack);
  };

  const handleChange = (stationData: unknown) => {
    setStationData((prevState: unknown) => {
      if (prevState[stationData.stnCode] === undefined) {
        return {
          ...prevState,
          ...{ [stationData.stnCode]: stationData },
        };
      }
      else {
        return {
          ...prevState,
          ...{ [stationData.stnCode]: undefined },
        };
      }
    });
  };

  const filterStationData = () => {

    let finalArr:unknown = [];
    for (let i = 0; i < stationsSectionList.length; i++) {
      const temp = stationsSectionList[i].data.map((val: unknown) => ({
        'stnCode': val.Station.code,
        stnName: val?.Station?.name,
        'haltMinutes': val.HaltMinutes,
        'arr': val.ArrivalDetails.actualArrivalTime,
        'dep': val.DepartureDetails.actualDepartureTime,
        'late_arrival': val.ArrivalDetails.actualArrivalTime > val.ArrivalDetails.scheduledArrivalTime,
        'late_departure': val.DepartureDetails.actualDepartureTime > val.DepartureDetails.scheduledDepartureTime,
      }));
      finalArr = [...finalArr,...temp];
    }
    return finalArr;
  };

  const feedbackToText = (feedBack: unknown, textFeedBack: unknown, stationFeedBack: unknown) => {
    const result: unknown = { 'Rating': feedBack.rating };
    const feedbackReasons: unknown = [];
    const stationInfo: unknown = [];

    Object.keys(feedBack).forEach((key) => {
      if (feedBack[key] === true) {
        feedbackReasons.push(key);
      }
    });
    result.Reasons = feedbackReasons;

    Object.keys(stationFeedBack).forEach((key) => {
      if (stationFeedBack[key] !== undefined) {
        stationInfo.push(stationFeedBack[key]);
      }
    });
    result.StationInfo = stationInfo;
    result.Comment = textFeedBack;

    const currentStation = currentStationDetail?.IntermediateStation?.Station?.code
      ? currentStationDetail?.IntermediateStation
      : currentStationDetail?.StoppingStation;
    const timestamp = getTimeStringFromDate(new Date());

    const feedBackData = {
      feedback: JSON.stringify(result),
      timestamp: timestamp,
      station: `${currentStation?.Station?.code} - ${currentStation?.Station?.name}`,
      trainNumber: trainDetails?.Number,
    };
    return feedBackData;
  };

  const RenderTopIcons = () => (
    <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: '2%' }}>
      <Image source={train} style={styles.topIcon} />
      <TouchableRipple onPress={() => onCloseButtonClick()}>
        <View style={bottomSheetStyles.headerIconContainer}>
          <Image source={closeIcon} style={styles.headerIcon} />
        </View>
      </TouchableRipple>
    </View>
  );

  const RenderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={bottomSheetStyles.headerText}>{labels.FEEDBACK_TITLE}</Text>
    </View>
  );

  const RenderBody = () => (
    <View>
      <Text style={styles.feedbackTitle}>How accurate was the <Text style={{ color: colors.successGreen, fontWeight: '500' }}>{LTS_TEXT}</Text> Shown to you?</Text>
    </View>
  );

  const RenderBodyPg2 = () => (
    <View>
      <Text style={[styles.feedbackTitle, { marginTop: '3%', marginLeft: '3%' }]}>Select stations where you faced accuracy issues.</Text>
    </View>
  );


  const _TextfeedBack = () => (
    <View>
      <Text style={styles.textFeedbackTitle}>{labels.FEEDBACK_TEXT_TITLE}</Text>
    <TextInput
      style={styles.input}
      onChangeText={onChange}
      value={textFeedback}
      placeholder={labels.FEEDBACK_DEFAULT}
      multiline={true}
      placeholderTextColor={colors.darkGrey2}
    />
    </View>
  );

  const _onPress = () => {
    setPage(3);
  };

  const _onExitPage1 = (rating:number) => {
    if (rating === 5){
      setPage(3);
    }
    else {
      setPage(2);
    }
  };

  if (page === 1) {
    return (
      <View style={bottomSheetStyles.container}>
        <View
          style={bottomSheetStyles.optionListContainer}
          testID="star_based_feedback_bottomsheet_option_list_container"
        >
          <RenderTopIcons />
          <RenderHeader />
          <RenderBody />
          <View>
            <View style={setStarBg(feedback.rating)}>
              {FEEDBACKS.map((star, starNo) => (
                <TouchableRipple
                  key={starNo + 1}
                  onPress={() => {
                    LayoutAnimation.configureNext({
                      duration: 300,
                      update: { type: LayoutAnimation.Types.easeInEaseOut },
                    });
                    setFeedback({ ...initialFeedback, ...{ rating: starNo + 1 } });
                  }}
                >
                  <Image
                    source={getStars(feedback.rating, starNo + 1)}
                    style={setStarStyle(feedback.rating)}
                  />
                </TouchableRipple>
              ))}
            </View>
            {feedback.rating !== 0 && (
              <View style={styles.feedbackContainer}>
                <Text style={styles.feedbackHeader}>{FEEDBACKS[feedback.rating - 1].Question}</Text>
                <View style={styles.feedbackBody}>
                  {FEEDBACKS[feedback.rating - 1].Answers.map((title) => (
                    <TouchableOpacity
                      key={title}
                      style={styles.head}
                      onPress={() =>
                        setFeedback((prevState: unknown) => {
                          return {
                            ...prevState,
                            ...{ [title]: !feedback[title as keyof typeof feedback] },
                          };
                        })
                      }
                      testID="star_based_feedback_bottomsheet_option_list_item"
                    >
                      <Text
                        style={[
                          styles.feedbackValuesBox,
                          feedback[title as keyof typeof feedback]
                            ? styles.valueSelected
                            : styles.valueUnselected,
                        ]}
                      >
                        {title}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
            {feedback.rating !== 0 && (
              <TouchableRipple onPress={() => _onExitPage1(feedback.rating)}>
                <LinearGradient
                  colors={[colors.lightBlue, colors.darkBlue]}
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 1.0 }}
                  style={bottomSheetStyles.gradientStyle}
                >
                  <Text style={bottomSheetStyles.proceedText}>
                    {proceedTextLabel(feedback.rating)}
                  </Text>
                </LinearGradient>
              </TouchableRipple>
            )}
          </View>
        </View>
      </View>
    );
  }
  if (page === 2) {
    return (
      <View style={styles.container}>

        <RenderTopIcons />
        <RenderBodyPg2 />

        <ScrollView style={styles.stationListContainer}>
          {filterStationData().map((station: unknown) => (
            <View key = {station?.stnCode}style={styles.station}>
              <View style={{ flexDirection: 'column', width: '75%' }}>
                <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'flex-end', marginBottom: 5 }}>
                  <Text style={{ flexBasis: '16%', textAlign: 'center', fontWeight: '700', fontSize: 14, color: colors.black }}>{station.stnCode}</Text>
                  <Text style={{ flexBasis: '55%', textAlign: 'center', fontWeight: '700', fontSize: 14, color: colors.black }}>{station.stnName}</Text>
                  <Text style={{ flexBasis: '15%', textAlign: 'center', fontWeight: '700', color: colors.blackLight }}>Arr.</Text>
                  <Text style={[setTimeColor(station.late_arrival), { fontWeight: '900' }]}>{station.arr}</Text>
                </View>

                <View style={{ flexDirection: 'row', flex: 1, justifyContent: 'flex-end' }}>
                  <View style={styles.stationStop} >
                    <Image source={stopIcon} style={styles.stopIcon} />
                    <Text style={{ marginLeft: 4 }}>Stop</Text>
                    <Text style={{ marginLeft: 4, fontWeight: '700' }}>{station.haltMinutes}</Text>
                    <Text style={{ marginLeft: 1, fontWeight: '700' }}>m</Text>
                  </View>
                  <Text style={{ flexBasis: '14%', textAlign: 'center', fontWeight: '700', color: colors.blackLight }}>Dep.</Text>
                  <Text style={[setTimeColor(station.late_departure), { fontWeight: '900' }]}>{station.dep}</Text>
                </View>
              </View>
              <View style={{ marginTop: 10 }}>
                <CheckBox label="" onChange={() => handleChange(station)} checkedImage={CB_ENABLED_IMAGE} uncheckedImage={CB_DISABLED_IMAGE} />
              </View>
            </View>

          ))
          }
        </ScrollView>

        {_TextfeedBack()}
        <TouchableRipple style={bottomSheetStyles.proceed} onPress={() => _onPress()}>
          <LinearGradient
            colors={[colors.lightBlue, colors.darkBlue]}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 1.0 }}
            style={bottomSheetStyles.gradientStyle}
          >
            <Text style={bottomSheetStyles.proceedText}>SUBMIT</Text>
          </LinearGradient>
        </TouchableRipple>

      </View>
    );
  }

  if (page === 3) {
    return (
      <View style={[styles.container, {justifyContent: 'center'}]}>
        <Image source={groupIcon} style={styles.groupIcon} />
        <Text style={styles.feedbackText}>{THANKS_FEEDBACK}</Text>
      </View>
    );
  }
};


export default StarBasedFeedbackBottomSheet;

const getStars = (rating: number, star: number) => {
  if (star === rating) {
    return filledStars[star - 1];
  }
  else if (star < rating) {
    return coloredStar;
  }
  else if (star > rating) {
    return unfilledStars[star - 1];
  }
};

const setStarStyle = (rating: number) => {
  if (rating !== 0) {
    return styles.star_small;
  }
  else {
    return styles.star_big;
  }
};

const setStarBg = (rating: number) => {
  if (rating !== 0) {
    return styles.stars_bg_small;
  }
  else {
    return styles.stars_bg_big;
  }
};

const setTimeColor = (isLate: boolean) => {
  if (isLate) {
    return { color: colors.successGreen };
  } else {
    return { color: colors.red };
  }
};

const proceedTextLabel = (rating:number) =>{
  if (rating === 5){
    return 'SUBMIT';
  } else {
    return 'NEXT';
  }
};

const styles = StyleSheet.create({
  feedbackTitle: {
    position: 'relative',
    fontWeight: '400',
    fontSize: 17,
    lineHeight: 22,
    color: colors.black,
  },
  stars_bg_big: {
    marginTop: '15%',
    backgroundColor: colors.yellowishGradient,
    borderRadius: 28,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    justifyContent: 'space-evenly',
    height: 56,
    width: 280,
    alignSelf: 'center',

  },
  stars_bg_small: {
    marginTop: '4%',
    marginBottom: '3%',
    backgroundColor: colors.yellowishGradient,
    borderRadius: 18,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    justifyContent: 'space-evenly',
    height: '15%',
    width: '70%',
    alignSelf: 'center',
  },
  star_big: {
    width: 40,
    height: 40,
    top: 0,
    left: 0,
    opacity: 0.75,
    borderRadius: 1,
  },
  star_small: {
    width: 28,
    height: 28,
    top: 0,
    left: 0,
    opacity: 0.75,
    borderRadius: 0.64,
  },
  head: {
    flexGrow: 1,
  },
  feedbackHeader: {
    position: 'relative',
    alignSelf: 'center',
    fontFamily: 'Lato',
    fontWeight: 'bold',
    fontSize: 14,
    lineHeight: 15,
    textAlign: 'center',
    color: colors.black,
    marginBottom: '5%',
  },
  feedbackBody: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: -5,
  },
  feedbackValuesBox: {
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontSize: 12,
    fontWeight: 'bold',
    backgroundColor: colors.grayBg,
    borderRadius: 12,
    margin: '2%',
    lineHeight: 25,
    color: colors.grey29,
    textAlign: 'center',
  },
  feedbackContainer: {
    marginBottom: '8%',
  },
  valueSelected: {
    color: colors.primary,
    borderColor: colors.primary,
    borderWidth: 1,
  },
  valueUnselected: {
    color: colors.grey29,
  },
  topIcon: {
    position: 'relative',
    zIndex: 10,
    marginTop: -60,
  },
  headerIcons: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  input: {
    height: 80,
    width: '95%',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.greyBookedSeat,
    fontFamily: fonts.regular,
    fontSize: 14,
    backgroundColor: colors.grayBg,
    padding: 14,
    marginRight: 0,
    marginLeft: '3%',
    marginBottom: 20,
    alignItems: 'center',
    textAlignVertical: 'top',
  },
  textFeedbackTitle: {
    fontWeight: '700',
    fontSize: 15,
    color: colors.black,
    margin: '2%',
    textAlign: 'left',
  },
  container: {
    paddingHorizontal: 12,
    paddingVertical: 20,
    backgroundColor: colors.white,
    height: 500,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
  },
  stationListContainer: {
    width: '95%',
    height: 100,
    backgroundColor: colors.white,
    borderRadius: 4,
    borderColor: colors.lightSilver,
    marginTop: '1%',
    marginBottom: '1%',
    overflow: 'scroll',
    borderWidth: 1,
    alignSelf: 'center',
  },
  station: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'stretch',
    marginBottom: 20,
    marginTop: 10,
  },
  stopIcon: {
    alignItems: 'center',
  },
  stationStop: {
    flexDirection: 'row',
    alignItems: 'center',
    flexBasis: '41%',
  },
  thankyou: {
    width: '90%',
    height: '90%',
    alignSelf: 'center',
    marginTop:'5%',
  },
  headerIcon: {
    width: 17,
    height: 17,
    marginRight: 8,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  groupIcon: {
    width: 86,
    height: 86,
    alignSelf: 'center',
  },
  feedbackText: {
    fontFamily: 'Lato',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 20,
    color: colors.black,
    marginTop: 32,
    textAlign: 'center',
  },
});
