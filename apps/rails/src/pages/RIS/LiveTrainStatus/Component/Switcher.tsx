import React, { useEffect } from 'react';
import { View, Text, Image, StyleSheet, FlatList } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import HorizontalProgressView from './ProgressBar';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { bottomSheetStyles } from './TrackingOptionBottomSheet';
import { SwitcherData } from '../interface';
import { switcher } from '../constants';
import { BS_RIS_LTS_OFFLINE_MODE, BS_RIS_LTS_ONLINE_MODE, BS_RIS_LTS_TOO_FAR_TRACK, trackOmnitureLTSLoadEvent } from '../../../../RisAnalytics/RisAnalytics';

import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';

interface Props {
  closeModal: () => void;
  data: SwitcherData[][];
}

const __renderItem = ({ item }: { item: SwitcherData }) => (
  <Text style={[styles.text, item.bold && styles.boldText]}>{item.value}</Text>
);

const _renderItem = ({ item }: { item: SwitcherData[] }) => (
  <FlatList data={item} renderItem={__renderItem} />
);

const _renderSeperator = () => <View style={{ height: 20 }} />;

const Switcher = ({ closeModal, data }: Props) => {
  useEffect(() => {
    if (data === switcher.PERMISSION_ERROR){
      return;
    }
    trackOmnitureLTSLoadEvent(
      data === switcher.OFFLINE_ERROR
        ? BS_RIS_LTS_TOO_FAR_TRACK
        : data === switcher.SWITCH_TO_INSIDE || data === switcher.SWITCH_TO_INSIDE_MOUNT
        ? BS_RIS_LTS_OFFLINE_MODE
        : BS_RIS_LTS_ONLINE_MODE,
      '',
    );
  }, [data]);
  return (
    <View style={bottomSheetStyles.container}>
      <View style={styles.headerContainer}>
        <Text style={bottomSheetStyles.headerText}>{}</Text>
        <TouchableRipple onPress={() => closeModal()} testID="switcher_bottomsheet_close_button">
          <View style={bottomSheetStyles.headerIconContainer}>
            <Image source={closeIcon} style={bottomSheetStyles.headerIcon} />
          </View>
        </TouchableRipple>
      </View>
      <View style={styles.flatListContainer} testID="switcher_bottomsheet_flat_list_container">
        <FlatList
          data={data}
          renderItem={_renderItem}
          ItemSeparatorComponent={_renderSeperator}
          showsVerticalScrollIndicator={false}
        />
      </View>
      <View
        style={styles.progressiveViewContainer}
        testID="switcher_bottomsheet_progressive_view_container"
      >
        <HorizontalProgressView
          width={80}
          height={5}
          progressedColor={colors.azure}
          unprogressedColor={colors.lightGrey}
          borderRadius={10}
        />
      </View>
    </View>
  );
};

export default Switcher;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    right: 16,
    top: 16,
  },
  offlineErrorIcon: {
    width: 194,
    height: 110,
    alignSelf: 'center',
    marginBottom: 35,
  },
  text: {
    fontFamily: fonts.medium,
    fontSize: 17,
    color: colors.defaultTextColor,
    alignSelf: 'center',
    textAlign: 'center',
  },
  boldText: {
    fontFamily: fonts.bold,
    fontSize: 19,
    lineHeight: 30,
    color: colors.black,
  },
  progressiveViewContainer: {
    marginTop: 32,
    alignSelf: 'center',
  },
  flatListContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
});
