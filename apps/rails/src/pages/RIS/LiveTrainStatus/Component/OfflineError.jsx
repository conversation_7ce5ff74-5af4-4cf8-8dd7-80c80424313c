/* eslint-disable */
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels } from '@mmt/rails/src/pages/RIS/LiveTrainStatus/constants';
import { fontStyle } from '../../../../vernacular/VernacularUtils';

const OfflineError = () => {
  return (
    <View style={styles.container}>
      <Text style={[styles.errorText, fontStyle('regular')]}>{labels.OFFLINE_ERROR}</Text>
    </View>
  );
};

export default OfflineError;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.red,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 30,
    paddingVertical: 8,
    marginBottom: 5,
  },
  errorText: {
    fontSize: 12,
    color: colors.white,
  },
});
