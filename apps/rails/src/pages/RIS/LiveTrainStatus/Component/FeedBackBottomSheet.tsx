import React, { useState } from 'react';
import { View, Image, Text, TextInput, StyleSheet } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels } from '../constants';
import { bottomSheetStyles } from './TrackingOptionBottomSheet';
import LinearGradient from 'react-native-linear-gradient';
import { CurrentStation, FeedbackData, TrainDetail } from '../interface';
import { getTimeStringFromDate } from './commonUtils';
import {
  EVENT_RIS_LTS_FEEDBACK_SUBMIT_CLICK,
  PAGE_RIS_LTS_INFO,
  trackOmnitureLTSClickEvent,
} from '../../../../RisAnalytics/RisAnalytics';

import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';

interface Props {
  onSendButtonClick: (feedback: FeedbackData) => void;
  onCloseButtonClick: () => void;
  trainDetails: TrainDetail;
  currentStationDetail: CurrentStation;
}

const FeedBackBottomSheet = ({
  onSendButtonClick,
  onCloseButtonClick,
  trainDetails,
  currentStationDetail,
}: Props) => {
  const [feedBack, setFeedback] = useState<string | undefined>();
  const onChange = (feedBack: string) => {
    setFeedback(() => feedBack);
  };

  const _renderHeader = () => (
    <View style={bottomSheetStyles.headerContainer}>
      <Text style={bottomSheetStyles.headerText}>{labels.FEEDBACK_TITLE}</Text>
      <TouchableRipple
        onPress={() => onCloseButtonClick()}
        testID="feedback_bottomsheet_close_button"
      >
        <View style={bottomSheetStyles.headerIconContainer}>
          <Image source={closeIcon} style={bottomSheetStyles.headerIcon} />
        </View>
      </TouchableRipple>
    </View>
  );

  const _feedBack = () => (
    <TextInput
      style={styles.input}
      onChangeText={onChange}
      value={feedBack}
      placeholder={labels.FEEDBACK_DEFAULT}
      multiline={true}
      placeholderTextColor={colors.darkGrey2}
    />
  );

  const _onPress = (feedBack: string = '') => {
    const currentStation = currentStationDetail?.IntermediateStation?.Station?.code
      ? currentStationDetail?.IntermediateStation
      : currentStationDetail?.StoppingStation;
    const timestamp = getTimeStringFromDate(new Date());
    const feedBackData = {
      feedback: feedBack,
      timestamp: timestamp,
      station: `${currentStation?.Station?.code} - ${currentStation?.Station?.name}`,
      trainNumber: trainDetails?.Number,
    };
    onSendButtonClick(feedBackData);
    trackOmnitureLTSClickEvent(PAGE_RIS_LTS_INFO, EVENT_RIS_LTS_FEEDBACK_SUBMIT_CLICK);
    onCloseButtonClick();
  };

  return (
    <View style={bottomSheetStyles.container} testID="feedback_bottomsheet_container">
      <View
        style={bottomSheetStyles.optionListContainer}
        testID="feedback_bottomsheet_option_list_container"
      >
        {_renderHeader()}
        {_feedBack()}
      </View>
      <TouchableRipple
        style={bottomSheetStyles.proceed}
        onPress={() => _onPress(feedBack)}
        testID="feedback_bottomsheet_proceed_button"
      >
        <LinearGradient
          colors={[colors.lightBlue, colors.darkBlue]}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 1.0 }}
          style={bottomSheetStyles.gradientStyle}
        >
          <Text style={bottomSheetStyles.proceedText}>{labels.FEEDBACK_SUBMIT}</Text>
        </LinearGradient>
      </TouchableRipple>
    </View>
  );
};

export default FeedBackBottomSheet;

const styles = StyleSheet.create({
  input: {
    height: 240,
    borderRadius: 5,
    fontFamily: fonts.regular,
    fontSize: 14,
    backgroundColor: colors.smokeWhite,
    padding: 14,
    marginRight: 10,
    marginBottom: 20,
    alignItems: 'flex-start',
    textAlignVertical: 'top',
  },
});
