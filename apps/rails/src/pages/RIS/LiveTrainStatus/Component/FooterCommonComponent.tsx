import React from 'react';
import { View, Text, Image, StyleSheet, Platform } from 'react-native';
import { CurrentStation, IntermediateStation, StoppingStation } from '../interface';
import { getDelay, timeSince } from './commonUtils';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { labels } from '../constants';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import isEmpty from 'lodash/isEmpty';
import { LiveTrainStatusSpeed } from 'apps/rails/src/pages/RIS/LiveTrainStatus/Component/LiveTrainStatusSpeed';
import redClockIcon from '@mmt/legacy-assets/src/lts_red_clock_icon.webp';
import greenClockIcon from '@mmt/legacy-assets/src/lts_green_clock_icon.webp';
import refreshIcon from '@mmt/legacy-assets/src/lts_refresh_icon.webp';
interface Props {
  lastAPIFetched?: Date;
  currentStationDetail: CurrentStation;
  prevStationDetail: StoppingStation | IntermediateStation;
  trainStatus: {
    started: boolean;
    terminated: boolean;
  };
  isInPIPMode: boolean;
  onRefresh?: (isManualRefresh?: boolean) => void;
  trainsLiveGPSSpeed?: number;
  isInsideTrain: boolean;
}
const NA: string = 'NA';

const FooterCommonComponent = ({
  lastAPIFetched,
  currentStationDetail,
  prevStationDetail,
  trainStatus,
  onRefresh,
  isInPIPMode,
  trainsLiveGPSSpeed,
  isInsideTrain,
}: Props) => {
  const { StoppingStation, IntermediateStation } = currentStationDetail || {};
  const { started, terminated } = trainStatus || {};
  const timeSinceLastFetched = timeSince(
    !isEmpty(lastAPIFetched) ? new Date(lastAPIFetched) : lastAPIFetched,
  );
  const stationStatuses = ['Approaching station ', 'Arrived at station ', 'Crossed station '];
  const delayInMins = getDelay(
    IntermediateStation?.Station?.code ? IntermediateStation : StoppingStation,
  );
  const delay = delayInMins?.value !== 0;
  const clockIcon = delay ? redClockIcon : greenClockIcon;

  const renderMessage = () => {
    const station: StoppingStation | IntermediateStation = isEmpty(IntermediateStation)
      ? StoppingStation
      : IntermediateStation;
    const isCancelled = station?.MetaDetails?.CancelledStation;
    const isDiverted = station?.MetaDetails?.DivertedStation;
    const isCancelledOrDiverted = isCancelled || isDiverted;

    const stationStatus = isCancelledOrDiverted
      ? isCancelled
        ? labels.TRAIN_CANCELLED
        : labels.TRAIN_DIVERTED
      : !started || terminated
      ? terminated
        ? labels.TRAIN_TERMINATED
        : labels.TRAIN_NOT_STARTED
      : stationStatuses[
          (station?.ArrivalDetails?.arrived ? 1 : 0) + (station?.DepartureDetails?.departed ? 1 : 0)
        ];
    const finalStation = stationStatus === stationStatuses[0] ? prevStationDetail : station;
    const isFinalStationCancelled = finalStation?.MetaDetails?.CancelledStation;
    const isFinalStationDiverted = finalStation?.MetaDetails?.DivertedStation;
    const finalStationStatus =
      isFinalStationCancelled || isFinalStationDiverted
        ? isFinalStationCancelled
          ? labels.TRAIN_CANCELLED
          : labels.TRAIN_DIVERTED
        : !started || terminated
        ? terminated
          ? labels.TRAIN_TERMINATED
          : labels.TRAIN_NOT_STARTED
        : stationStatuses[
            (finalStation?.ArrivalDetails?.arrived ? 1 : 0) +
              (finalStation?.DepartureDetails?.departed ? 1 : 0)
          ];
    const isIntermediate =
      isEmpty(finalStation?.MetaDetails) || finalStation?.MetaDetails?.StoppingStation === false;
    const stationName = isIntermediate
      ? `${finalStation?.Station?.name} (No-halt)`
      : finalStation?.Station?.name;

    const stationCode = isIntermediate
      ? `${finalStation?.Station?.code} (No-halt)`
      : finalStation?.Station?.code;

    const time = finalStation?.DepartureDetails?.departed
      ? finalStation?.DepartureDetails?.actualDepartureTime
      : finalStation?.ArrivalDetails?.actualArrivalTime;


    return (
      <View style={styles.textContainer} testID="footer_message_text_container">
        <Text
          style={[
            styles.normalMessage,
            { color: delay ? colors.red : colors.successGreen },
            isInPIPMode && styles.pipStationStatus,
          ]}
          numberOfLines={isInPIPMode ? 2 : 5}
          testID="footer_message_text_container_text"
        >
          {finalStationStatus}
          <Text style={styles.boldMessage} testID="footer_message_text_container_text_bold">
            {isInPIPMode ? stationCode : stationName}
          </Text>
          {isEmpty(time) ? (
            ''
          ) : (
            <Text>
              {' at '}
              <Text
                style={styles.boldMessage}
                testID="footer_message_text_container_text_bold_time"
              >
                {time}
              </Text>
            </Text>
          )}
          {'.'}
          {isInPIPMode && (
            <Text
              style={styles.pipNormalMessage}
              testID="footer_message_text_container_text_pip_normal_message"
            >{` (${finalStation?.Station?.name})`}</Text>
          )}
        </Text>
        {!terminated && (
          <Text style={styles.normalText} testID="footer_message_text_container_text_normal_text">
            {labels.LAST_UPDATED} {timeSinceLastFetched?.stringValue || NA}
          </Text>
        )}
      </View>
    );
  };

  const onRefreshBtnClicked = () => {
    onRefresh && onRefresh(true);
  };
  return (
    <View style={[styles.container, !isInPIPMode && styles.nonPipContainerAdjust]}>
      {!isInPIPMode && (
        <View
          style={[styles.bar, { backgroundColor: delay ? colors.palePink2 : colors.successGreen }]}
          testID="footer_message_bar"
        />
      )}
      <View style={styles.innerContainer} testID="footer_message_inner_container">
        {isInPIPMode || !isInsideTrain ? (
          <>
            {!isInPIPMode && (
              <Image
                style={styles.clockImage}
                source={clockIcon}
                testID="footer_message_clock_image"
              />
            )}
            {renderMessage()}
          </>
        ) : (
          <LiveTrainStatusSpeed
            delay={delay}
            delayInMins={delayInMins}
            trainsLiveGPSSpeed={trainsLiveGPSSpeed}
            currentStationDetail={currentStationDetail}
          />
        )}
        {!isInPIPMode && !terminated && (
          <TouchableRipple
            onPress={onRefreshBtnClicked}
            testID="footer_message_touchable_ripple_refresh"
          >
            {!isInPIPMode && (
              <View style={styles.row} testID="footer_message_row">
                <Image
                  source={refreshIcon}
                  style={styles.arrowIcon}
                  testID="footer_message_arrow_icon"
                />
              </View>
            )}
          </TouchableRipple>
        )}
      </View>
    </View>
  );
};

export default FooterCommonComponent;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    ...Platform.select({
      ios: {
        paddingTop: 20,
        height: 55,
      },
    }),
    borderColor: colors.lightGrey,
    justifyContent: 'center',
  },
  nonPipContainerAdjust: {
    height: Platform.select({ android: 80 }),
  },
  bar: {
    width: '30%',
    height: 6,
    alignSelf: 'center',
    backgroundColor: colors.palePink2,
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
    position: 'absolute',
    top: 0,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clockImage: {
    width: 15,
    height: 18,
    marginRight: 8,
    marginLeft: 12,
    alignSelf: 'center',
  },
  textContainer: {
    flex: 3,
    alignSelf: 'center',
    marginRight: 5,
  },
  normalMessage: {
    fontFamily: fonts.medium,
    fontSize: 12,
    lineHeight: 16,
  },
  pipNormalMessage: {
    fontFamily: fonts.medium,
    fontSize: 9,
    lineHeight: 14,
    textTransform: 'capitalize',
  },
  boldMessage: {
    fontFamily: fonts.bold,
  },
  arrowIcon: {
    width: 28,
    height: 28,
    marginRight: 16,
    marginLeft: 8,
    alignSelf: 'center',
  },
  divider: {
    marginHorizontal: 12,
    width: 2,
    backgroundColor: colors.gray7,
    height: '100%',
  },
  boldText: {
    fontFamily: fonts.bold,
    fontSize: 14,
    marginBottom: 2,
    color: colors.black,
    textAlign: 'right',
  },
  normalText: {
    fontFamily: fonts.regular,
    fontSize: 10,
    textAlign: 'left',
    color: colors.lightGrey1,
  },
  pipStationStatus: {
    fontSize: 9,
    lineHeight: 4,
  },
  timeContainer: {
    width: 70,
    alignSelf: 'center',
  },
  row: {
    flexDirection: 'row',
  },
});
