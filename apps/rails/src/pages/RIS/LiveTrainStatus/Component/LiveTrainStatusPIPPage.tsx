/* eslint-disable */
import React, { useEffect, useRef, useState } from 'react';
import {
  FlatList,
  ListRenderItem,
  NativeEventEmitter,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { getDelay } from './commonUtils';
import FooterMessage from './FooterMessage';
import { labels, viewStates } from '../constants';
import {
  CurrentStation,
  IntermediateStation,
  NativeCTandGPSData,
  OtherOption,
  StoppingStation,
  TrainDetail,
} from '../interface';
import { RequestDetails } from '../../RisCommonInterfaces';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { LiveTrainStatusPIPHeader } from './LiveTrainStatusPIPHeader';
import { LiveTrainStatusPIPStation } from './LiveTrainStatusPIPStation';
import greenLottieIcon from '../LottieFiles/lts_green_current_station_lottie_icon.json';
import redLottieIcon from '../LottieFiles/lts_red_current_station_lottie_icon.json';
import LottieView from 'lottie-react-native';
import {
  PAGE_RIS_LTS_PIP_LOAD,
  RIS_LTS_PIP_MID_VIEW_EXPANDED,
  trackOmnitureLTSPipLoadEvent,
  trackOmnitureLTSPipViewEvent,
} from '../../../../RisAnalytics/RisAnalytics';
const RAILS_CELL_TOWER_GPS_DATA_UPDATED = 'RAILS_CELL_TOWER_GPS_DATA_UPDATED';

interface LiveTrainStatusPIPProps {
  currentStationDetail: CurrentStation;
  lastUpdated: Date;
  previousStationDetail: StoppingStation | IntermediateStation;
  trainDetails: TrainDetail;
  otherOptions: OtherOption;
  showLoader: boolean;
  ltsViewState: string;
  trainStatus: string;
  pipStationList: [StoppingStation | IntermediateStation];
  requestDetails: RequestDetails;
  firstStationDetail: StoppingStation;
  initOfflineTracking: (
    requestDetails: RequestDetails,
    refresh: boolean,
    autoRefresh: boolean,
    nativeCTGPSData: NativeCTandGPSData,
  ) => void;
  isOfflineErrorVisible: boolean;
}

export const LiveTrainStatusPIPPage = (props: LiveTrainStatusPIPProps) => {
  const {
    currentStationDetail,
    lastUpdated,
    previousStationDetail,
    trainDetails,
    otherOptions,
    showLoader,
    ltsViewState,
    pipStationList,
    requestDetails,
    firstStationDetail,
    initOfflineTracking,
  } = props;

  const pipFlatListRef = useRef<FlatList<unknown>>(null);
  const [pipViewInTransition, setPIPViewInTransition] = useState(true);

  const { StoppingStation, IntermediateStation } = currentStationDetail || {};
  const delayInMins = getDelay(
    IntermediateStation?.Station?.code ? IntermediateStation : StoppingStation,
  );
  const delay = delayInMins.value !== 0;

  //Listens callback from native
  useEffect(() => {
    try {
      const eventEmitter = new NativeEventEmitter(RailsModule);
      eventEmitter.removeListener(RAILS_CELL_TOWER_GPS_DATA_UPDATED, () => {});
      const subscription = eventEmitter.addListener(RAILS_CELL_TOWER_GPS_DATA_UPDATED, (event) => {
        initOfflineTracking(requestDetails, false, true, event);
      });
      trackOmnitureLTSPipLoadEvent(PAGE_RIS_LTS_PIP_LOAD);
      return () => {
        subscription?.remove();
      };
    } catch (error) {
      console.error('Error in PIP listener ', error);
    }
  }, []);

  const onLayout = async (event: unknown) => {
    const pipViewHeight = event?.nativeEvent?.layout?.height;
    if (pipViewHeight > 250 && pipViewHeight < 300) {
      trackOmnitureLTSPipViewEvent(PAGE_RIS_LTS_PIP_LOAD, RIS_LTS_PIP_MID_VIEW_EXPANDED);
    }
    setPIPViewInTransition(pipViewHeight > 300);
  };

  if (pipViewInTransition || showLoader || ltsViewState !== viewStates.SHOW_DETAIL) {
    return (
      <View style={styles.fullFlex} onLayout={onLayout}>
        {!pipViewInTransition && (
          <View style={styles.loadingContainer}>
            <Spinner size={20} color="#008b8b" />
            <Text style={styles.loaderText}>{labels.FETCHING}</Text>
          </View>
        )}
      </View>
    );
  }

  // if (!showLoader && isOfflineErrorVisible) {
  //   return (
  //     <View style={styles.fullFlex}>
  //       <View style={styles.loadingContainer}>
  //         <Text numberOfLines={3} style={[styles.loaderText]}>
  //           {labels.PIP_OFFLINE_ERROR}
  //         </Text>
  //       </View>
  //     </View>
  //   );
  // }

  const keyExtractor = (item: StoppingStation | IntermediateStation, index: number) =>
    item?.Station?.code || `pip-${index}`;
  const renderLandingComponents: ListRenderItem<StoppingStation | IntermediateStation> = ({
    item,
    index,
  }) => (
    <LiveTrainStatusPIPStation
      station={item}
      index={index}
      delay={delay}
      firstStationDetail={firstStationDetail}
      currentStationDetail={currentStationDetail}
      triggerScroll={triggerScroll}
      isLastStation={index === pipStationList.length - 2} //Why 2, bcz we have one empty obj appended at the end
    />
  );
  const renderListHeaderComponent = () => (
    <LiveTrainStatusPIPHeader delay={delay} delayInMins={delayInMins} />
  );
  const renderListFooterComponent = () => (
    <View style={styles.footerContainer}>
      <FooterMessage
        lastAPIFetched={lastUpdated}
        currentStationDetail={currentStationDetail}
        prevStationDetail={previousStationDetail}
        trainDetails={trainDetails}
        isInPIPMode={true}
      />
    </View>
  );

  const renderHeaderComponents = () => <View style={styles.topVerticalLine} />;

  const triggerScroll = () => {
    if (pipFlatListRef?.current !== null) {
      pipFlatListRef.current.scrollToOffset({ offset: 20, animated: true });
    }
  };
  const currentLottieIcon = delay ? redLottieIcon : greenLottieIcon;
  return (
    <View style={styles.pipContainer} onLayout={onLayout}>
      {renderListHeaderComponent()}
      <View style={styles.listContainer}>
        <FlatList
          ref={pipFlatListRef}
          data={pipStationList}
          keyExtractor={keyExtractor}
          renderItem={renderLandingComponents}
          ListHeaderComponent={renderHeaderComponents}
          showsVerticalScrollIndicator={false}
        />
        <View style={styles.lottieContainer}>
          <View style={styles.verticalLine} />
          <LottieView
            source={currentLottieIcon}
            loop={true}
            autoPlay={true}
            progress={0}
            style={styles.currentLottie}
          />
        </View>
      </View>
      {!otherOptions?.showTrainSchedule && renderListFooterComponent()}
    </View>
  );
};

const styles = StyleSheet.create({
  fullFlex: {
    flex: 1,
  },
  pipContainer: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'space-between',
  },
  listContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    flex: 1,
    alignItems: 'center',
  },
  loaderText: {
    fontFamily: fonts.medium,
    fontSize: 12,
    color: colors.defaultTextColor,
    alignSelf: 'center',
    textAlign: 'center',
    marginTop: 16,
  },
  footerContainer: {
    width: '100%',
    justifyContent: 'flex-end',
    paddingHorizontal: 8,
    paddingTop: 4,
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: -20,
    },
    shadowOpacity: 0.8,
    shadowRadius: 6.0,
    marginBottom: -2,
    elevation: 24,
    borderColor: colors.lightGray,
    borderTopWidth: 0.2,
  },
  lottieContainer: {
    position: 'absolute',
    marginTop: 38,
  },
  currentLottie: {
    width: 40,
    height: 40,
  },
  verticalLine: {
    height: 16,
    width: 4,
    backgroundColor: colors.lightBlue29,
    marginLeft: 18,
    marginTop: 4,
    position: 'absolute',
  },
  topVerticalLine: {
    height: 6,
    width: 4,
    backgroundColor: colors.lightBlue29,
    marginLeft: 18,
  },
});
