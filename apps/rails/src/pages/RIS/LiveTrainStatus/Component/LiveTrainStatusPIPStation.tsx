import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { labels } from '../constants';
import {
  CurrentStation,
  IntermediateStation,
  StoppingStation,
} from '../interface';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { LiveTrainStatusPIPListLine } from './LiveTrainStatusPIPListLine';

interface Props {
  station: StoppingStation | IntermediateStation;
  firstStationDetail: StoppingStation;
  currentStationDetail: CurrentStation;
  index: number;
  delay: boolean;
  isLastStation: boolean;
  triggerScroll: () => void;
}

export const LiveTrainStatusPIPStation = (props: Props) => {
  const {
    station,
    index,
    delay,
    firstStationDetail,
    currentStationDetail,
    isLastStation,
    triggerScroll,
  } = props;
  const { StoppingStation, IntermediateStation } = currentStationDetail || {};
  const { Station, ArrivalDetails, DepartureDetails } = station || {};
  const currentStation = IntermediateStation || StoppingStation;

  //Time
  const actualArrivalTime = ArrivalDetails?.actualArrivalTime;
  const actualDepartureTime = DepartureDetails?.actualDepartureTime;

  //Station status
  const isFirstStation = firstStationDetail?.Station?.code === Station?.code;
  const isCurrentStation = currentStation?.Station?.code === Station?.code;
  const isDepartedFromStation = DepartureDetails?.departed;
  if (isCurrentStation && isDepartedFromStation) {
    triggerScroll();
  }
  return (
    <View style={styles.stationContainer}>
      {Station?.name && (
        <LiveTrainStatusPIPListLine
          index={index}
          delay={delay}
          hideCircle={false}
          isLastStation={isLastStation}
        />
      )}
      <View style={styles.stationDetailsContainer}>
        {Station?.name && (
          <>
            <Text style={styles.stationName} numberOfLines={1}>
              {Station?.name}
            </Text>
            <Text style={styles.arrival}>{`${labels.ARR} : ${
              isFirstStation ? labels.EMPTY_TIME : actualArrivalTime || labels.EMPTY_TIME
            }`}</Text>
            <Text style={styles.departure}>{`${labels.DEP} : ${
              actualDepartureTime || labels.EMPTY_TIME
            }`}</Text>
          </>
        )}
        <View style={styles.space} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  stationContainer: {
    marginLeft: 18,
    flexDirection: 'row',
  },
  stationDetailsContainer: {
    marginLeft: 16,
  },
  stationName: {
    marginRight: 14,
    fontFamily: fonts.bold,
    fontSize: 10,
    color: colors.black,
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  arrival: {
    marginRight: 14,
    fontFamily: fonts.medium,
    fontSize: 8,
    color: colors.red,
  },
  departure: {
    marginRight: 14,
    fontFamily: fonts.medium,
    fontSize: 8,
    color: colors.successGreen,
  },
  space: {
    marginBottom: 10,
  },
});
