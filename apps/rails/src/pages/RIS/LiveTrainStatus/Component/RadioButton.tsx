import React from 'react';
import { Image, ImageStyle } from 'react-native';

import checkedRadioButton from '@mmt/legacy-assets/src/lts_radio_checked_icon.webp';
import uncheckedRadioButton from '@mmt/legacy-assets/src/lts_radio_unchecked_icon.webp';

interface Props {
  checked: boolean;
  dimension?: number;
  style?: ImageStyle;
}

const RadioButton = ({ checked, dimension = 24, style = {} }: Props) => (
  <Image
    source={checked ? checkedRadioButton : uncheckedRadioButton}
    style={[{ width: dimension, height: dimension }, style]}
    testID="tracking_option_bottomsheet_option_list_item_radio_button_image"
  />
);

export default RadioButton;
