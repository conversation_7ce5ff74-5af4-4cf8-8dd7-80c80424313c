import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { labels } from '../constants';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

interface Props {
  delayInMins: unknown;
  delay: boolean;
}

export const LiveTrainStatusPIPHeader = ({ delay, delayInMins }: Props) => {
  return (
    <>
      <View style={[styles.railStatusContainer, delay && styles.delayBackground]}>
        {!delay ? (
          <Text numberOfLines={1} style={styles.headerText}>
            {labels.ONTIME}
          </Text>
        ) : (
          <Text numberOfLines={1} style={styles.headerText}>
            {labels.LATE[0]}
            <Text style={styles.headerText}>{delayInMins.shortString}</Text>
            {labels.LATE[1]}
          </Text>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  railStatusContainer: {
    height: 20,
    borderRadius: 12,
    backgroundColor: colors.lightBlue29,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 2,
  },
  headerText: {
    fontFamily: fonts.regular,
    fontSize: 10,
    color: colors.white,
  },
  verticalLine: {
    height: 6,
    width: 4,
    backgroundColor: colors.lightBlue29,
    marginLeft: 18,
  },
  delayBackground: {
    backgroundColor: colors.red,
  },
});
