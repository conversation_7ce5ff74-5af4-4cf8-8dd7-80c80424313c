/* eslint-disable */
import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import LottieView from 'lottie-react-native';
import isEmpty from 'lodash/isEmpty';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.android';
import { labels, dimensions } from '@mmt/rails/src/pages/RIS/LiveTrainStatus/constants';
import FeedBackButton from '@mmt/rails/src/pages/RIS/LiveTrainStatus/Component/FeedBackButton';
import greenLottieIcon from '../LottieFiles/lts_green_current_station_lottie_icon.json';
import redLottieIcon from '../LottieFiles/lts_red_current_station_lottie_icon.json';
import { getDelay } from '@mmt/rails/src/pages/RIS/LiveTrainStatus/Component/commonUtils';

import ltsDelayIndicatorRedStart from '@mmt/legacy-assets/src/lts_delayIndicator_red_start.webp';
import ltsDelayIndicatorRedEnd from '@mmt/legacy-assets/src/lts_delayIndicator_red_end.webp';
import ltsDelayIndicatorGreenStart from '@mmt/legacy-assets/src/lts_delayIndicator_green_start.webp';
import ltsDelayIndicatorGreenEnd from '@mmt/legacy-assets/src/lts_delayIndicator_green_end.webp';
import PropTypes from 'prop-types';

const IntermediateStations = (props) => {
  const {
    stopNumber,
    cancelled,
    diverted,
    currentStationStopNumber,
    IntermediateStations,
    otherOptions,
    onFeedBackButtonClick,
    currentIntermediateStopNumber,
    hideIntermediateStations,
  } = props;
  const currentStoppingStationCheck = stopNumber === currentStationStopNumber;

  const leftBorderStyle = diverted
    ? styles.leftBorderYellow
    : cancelled
    ? styles.leftBorderRed
    : stopNumber < currentStationStopNumber
    ? styles.leftBorderGreen
    : styles.leftBorderGrey;

  const leftBorderStyleCircle = diverted
    ? styles.leftBorderYellow
    : cancelled
    ? styles.leftBorderRed
    : stopNumber < currentStationStopNumber
    ? styles.leftBorderGreen
    : styles.leftBorderGrey;

  useEffect(() => {
    if (currentStoppingStationCheck && currentIntermediateStopNumber) {
      if (currentIntermediateStopNumber > 2) {
        props.updateHeightArr(stopNumber - 1, 94 * (currentIntermediateStopNumber - 1));
        props.triggerScroll();
      }
    }
  }, []);

  return (
    <>
      {IntermediateStations.map((intermediateStation, index) => {
        const [incorrectLocationVisibility, setIncorrectLocationVisibility] = useState(false);
        const [offsetWindow, setOffsetWindow] = useState(
          index === IntermediateStations.length - 1
            ? dimensions.LAST_INTERMEDIATE_STATION.DEFAULT_OFFSET_WINDOW
            : dimensions.NORMAL_INTERMEDIATE_STATION.DEFAULT_OFFSET_WINDOW,
        );
        const intermediateStopNumber = intermediateStation.StopNumber;
        const currentIntermediateStationCheck =
          intermediateStopNumber === currentIntermediateStopNumber;
        const {
          arrived,
          actualArrivalDate = intermediateStation?.ArrivalDetails?.scheduledArrivalDate || '',
          actualArrivalTime,
          scheduledArrivalDate,
          scheduledArrivalTime,
        } = intermediateStation?.ArrivalDetails;
        const {
          departed,
          scheduledDepartureDate,
          scheduledDepartureTime,
          actualDepartureDate = intermediateStation?.DepartureDetails?.scheduledDepartureDate || '',
          actualDepartureTime,
        } = intermediateStation?.DepartureDetails;
        const delayIndicatorWidth = useRef(new Animated.Value(0));
        const delayIndicatorImageStartWidth = useRef(new Animated.Value(0));
        const delayIndicatorImageEndWidth = useRef(new Animated.Value(0));
        const currentIntermediateStation = currentIntermediateStationCheck
          ? intermediateStation
          : undefined;
        const delayInMins = getDelay(currentIntermediateStation);
        const delay = delayInMins.value !== 0;
        const currentLottieIcon = delay ? redLottieIcon : greenLottieIcon;

        useEffect(() => {
          const cleanup = () => {
            delayIndicatorImageStartWidth.current.stopAnimation();
            delayIndicatorWidth.current.stopAnimation();
            delayIndicatorImageEndWidth.current.stopAnimation();
          };

          return cleanup;
        }, []);

        useEffect(() => {
          if (
            otherOptions?.offlineTracking &&
            currentStoppingStationCheck &&
            currentIntermediateStationCheck
          ) {
            setIncorrectLocationVisibility(true);
          } else {
            setIncorrectLocationVisibility(false);
          }
        }, [
          currentStationStopNumber,
          currentIntermediateStopNumber,
          otherOptions?.offlineTracking,
        ]);

        function find_dimesions(layout) {
          const { height } = layout;
          setOffsetWindow(height);
        }

        const delayAnimation = (delayAnimationStart) => {
          const delayTimer1 = setTimeout(() => {
            Animated.timing(delayIndicatorImageStartWidth.current, {
              toValue: 1,
              duration: 100,
              easing: Easing.linear,
              useNativeDriver: true,
            }).start(() => {
              Animated.timing(delayIndicatorWidth.current, {
                toValue: 1,
                duration: 200,
                easing: Easing.linear,
                useNativeDriver: true,
              }).start(() => {
                Animated.timing(delayIndicatorImageEndWidth.current, {
                  toValue: 1,
                  duration: 100,
                  easing: Easing.linear,
                  useNativeDriver: true,
                }).start();
              });
            });
          }, delayAnimationStart);

          const delayTimer2 = setTimeout(() => {
            Animated.timing(delayIndicatorImageEndWidth.current, {
              toValue: 0,
              duration: 100,
              easing: Easing.linear,
              useNativeDriver: true,
            }).start(() => {
              Animated.timing(delayIndicatorWidth.current, {
                toValue: 0,
                duration: 200,
                easing: Easing.linear,
                useNativeDriver: true,
              }).start(() => {
                Animated.timing(delayIndicatorImageStartWidth.current, {
                  toValue: 0,
                  duration: 100,
                  easing: Easing.linear,
                  useNativeDriver: true,
                }).start();
              });
            });
          }, 4000 + delayAnimationStart);

          return [delayTimer1, delayTimer2];
        };

        useEffect(() => {
          let delayTimer1, delayTimer2, autoDelay;
          if (currentIntermediateStationCheck && currentStoppingStationCheck) {
            [delayTimer1, delayTimer2] = delayAnimation(1800);
            autoDelay = setInterval(() => {
              [delayTimer1, delayTimer2] = delayAnimation(1800);
            }, 10400);
          }

          return () => {
            if (delayTimer1) {
              clearTimeout(delayTimer1);
            }
            if (delayTimer2) {
              clearTimeout(delayTimer2);
            }
            if (autoDelay) {
              clearInterval(autoDelay);
            }
          };
        }, [currentIntermediateStopNumber]);

        const widthOfIndicator = !delay
          ? dimensions.DELAY_INDICATOR_WIDTH.ONTIME
          : delayInMins.value >= 60
          ? dimensions.DELAY_INDICATOR_WIDTH.DELAY_MORE_THAN_60
          : dimensions.DELAY_INDICATOR_WIDTH.DELAY_LESS_THAN_60;
        const DelayIndicator = () => (
          <View style={styles.delayIndicatorTextContainer} testID="live_train_status_page_intermediate_stations_right_box_delay_indicator_text_container">
            <Animated.View
              style={[
                styles.delayIndicatorText,
                {
                  backgroundColor: !delay ? 'rgba(14, 172, 159, 0.85)' : 'rgba(235, 32, 38, 0.85)',
                  width: widthOfIndicator,
                  transform: [
                    { translateX: -(widthOfIndicator / 2) },
                    { scaleX: delayIndicatorWidth.current },
                    { translateX: widthOfIndicator / 2 },
                  ],
                },
              ]}
              testID="live_train_status_page_intermediate_stations_right_box_delay_indicator_text_view"
            >
              {!delay ? (
                <Text numberOfLines={1} style={styles.delayText} testID="live_train_status_page_intermediate_stations_right_box_delay_indicator_text_ontime">
                  {labels.ONTIME}
                </Text>
              ) : (
                <Text numberOfLines={1} style={styles.delayText} testID="live_train_status_page_intermediate_stations_right_box_delay_indicator_text_late">
                  {labels.LATE[0]}
                  <Text style={styles.boldFamily} testID="live_train_status_page_intermediate_stations_right_box_delay_indicator_text_late_short_string">{delayInMins.shortString}</Text>
                  {labels.LATE[1]}
                </Text>
              )}
            </Animated.View>
            <Animated.Image
              source={!delay ? ltsDelayIndicatorGreenEnd : ltsDelayIndicatorRedEnd}
              style={{
                width: 12,
                transform: [
                  { translateX: -6 },
                  { scaleX: delayIndicatorImageEndWidth.current },
                  { translateX: 6 },
                ],
                height: 22.7,
                left: -1,
              }}
              testID="live_train_status_page_intermediate_stations_right_box_delay_indicator_image"
            />
          </View>
        );

        const offset =
          stopNumber === currentStationStopNumber &&
          currentIntermediateStopNumber !== undefined &&
          intermediateStopNumber === currentIntermediateStopNumber
            ? !arrived
              ? -25
              : departed
              ? offsetWindow - (index === IntermediateStations.length - 1 ? 79 : 49)
              : 0
            : offsetWindow - 20;

        const leftBorderStyleForCurrentStoppingStation1 = diverted
          ? styles.leftBorderYellow
          : cancelled
          ? styles.leftBorderRed
          : intermediateStation.StopNumber <= currentIntermediateStopNumber
          ? styles.leftBorderGreen
          : styles.leftBorderGrey;

        const leftBorderStyleForCurrentStoppingStation2 = diverted
          ? styles.leftBorderYellow
          : cancelled
          ? styles.leftBorderRed
          : intermediateStation.StopNumber < currentIntermediateStopNumber
          ? styles.leftBorderGreen
          : styles.leftBorderGrey;

        const leftBorderStyleCircleForCurrentStoppingStation = diverted
          ? styles.leftBorderYellow
          : cancelled
          ? styles.leftBorderRed
          : intermediateStation.StopNumber <= currentIntermediateStopNumber
          ? styles.leftBorderGreen
          : styles.leftBorderGrey;

        return (
          <TouchableRipple
            onPress={hideIntermediateStations}
            onLayout={(event) => {
              find_dimesions(event.nativeEvent.layout);
            }}
            testID="live_train_status_page_intermediate_stations_touchable_ripple"
          >
            <View style={styles.container} key={index.toString()} testID="live_train_status_page_intermediate_stations_container">
              <View style={styles.leftBox} testID="live_train_status_page_intermediate_stations_left_box">
                <Text
                  numberOfLines={1}
                  style={[
                    styles.firstLineLeft,
                    {
                      color:
                        stopNumber > currentStationStopNumber ||
                        (stopNumber === currentStationStopNumber &&
                          (!currentIntermediateStopNumber ||
                            intermediateStopNumber > currentIntermediateStopNumber))
                          ? colors.textGrey
                          : currentStoppingStationCheck && currentIntermediateStationCheck
                          ? delay
                            ? colors.red
                            : colors.successGreen
                          : colors.black,
                    },
                    {
                      fontFamily:
                        stopNumber > currentStationStopNumber ||
                        (stopNumber === currentStationStopNumber &&
                          (!currentIntermediateStopNumber ||
                            intermediateStopNumber > currentIntermediateStopNumber))
                          ? fonts.bold
                          : fonts.black,
                    },
                  ]}
                  testID={`live_train_status_page_intermediate_stations_left_box_text_${intermediateStation?.Station?.code}`}
                >
                  {intermediateStation?.Station?.code}
                </Text>
                <Text style={styles.secondLineLeft} testID={`live_train_status_page_intermediate_stations_left_box_text_${intermediateStation?.Distance}`}>
                  {parseInt(intermediateStation?.Distance)} km
                </Text>
              </View>

              <View style={styles.rightBoxContainer} testID="live_train_status_page_intermediate_stations_right_box_container">
                <View
                  style={[
                    styles.rightBoxSpacing,
                    { left: -1.5, marginTop: -0.5, marginBottom: -1.5 },
                    currentStoppingStationCheck
                      ? leftBorderStyleForCurrentStoppingStation1
                      : leftBorderStyle,
                    styles.grayBackground,
                  ]}
                  testID="live_train_status_page_intermediate_stations_right_box_spacing"
                />
                <View style={{ flexDirection: 'row' }} testID="live_train_status_page_intermediate_stations_right_box_view">
                  <View style={[styles.trackContainer, leftBorderStyle]}>
                    <View
                      style={[
                        { height: offset },
                        styles.coloredTrack,
                        currentStoppingStationCheck
                          ? leftBorderStyleForCurrentStoppingStation1
                          : leftBorderStyle,
                      ]}
                      testID="live_train_status_page_intermediate_stations_right_box_track_container_view"
                    />
                  </View>
                  <View style={{ flex: 1 }}>
                    <View
                      style={[
                        currentStoppingStationCheck
                          ? leftBorderStyleCircleForCurrentStoppingStation
                          : leftBorderStyleCircle,
                        styles.circleIcon,
                      ]}
                      testID="live_train_status_page_intermediate_stations_right_box_track_container_view"
                    />
                    <View>
                      <Text
                        numberOfLines={1}
                        style={[
                          styles.firstLineRight,
                          {
                            color:
                              stopNumber > currentStationStopNumber ||
                              (stopNumber === currentStationStopNumber &&
                                (!currentIntermediateStopNumber ||
                                  intermediateStopNumber > currentIntermediateStopNumber))
                                ? colors.textGrey
                                : currentStoppingStationCheck && currentIntermediateStationCheck
                                ? delay
                                  ? colors.red
                                  : colors.successGreen
                                : colors.black,
                          },
                          {
                            fontFamily:
                              stopNumber > currentStationStopNumber ||
                              (stopNumber === currentStationStopNumber &&
                                (!currentIntermediateStopNumber ||
                                  intermediateStopNumber > currentIntermediateStopNumber))
                                ? fonts.bold
                                : fonts.black,
                          },
                        ]}
                        testID={`live_train_status_page_intermediate_stations_right_box_text_${intermediateStation?.Station?.name}`}
                      >
                        {intermediateStation?.Station?.name}
                      </Text>
                    </View>
                    <View style={styles.secondLineRightContainer}>
                      <Text
                        style={
                          diverted
                            ? styles.noStoppageLabelDiverted
                            : cancelled
                            ? styles.noStoppageLabelCancelled
                            : styles.noStoppageLabel
                        }
                        testID={`live_train_status_page_intermediate_stations_right_box_text_${labels.NO_STOPPAGE}`}
                      >
                        {labels.NO_STOPPAGE}
                      </Text>
                      <View style={styles.arrdepDetails} testID="live_train_status_page_intermediate_stations_right_box_arrdep_details_view">
                        <Text
                          testID={`live_train_status_page_intermediate_stations_right_box_text_${labels.ARR}`}
                          style={[
                            styles.arrdepDetailsExpt,
                            {
                              color: departed
                                ? isEmpty(actualDepartureDate)
                                  ? actualDepartureTime > scheduledDepartureTime
                                  : actualDepartureDate + actualDepartureTime >
                                    scheduledDepartureDate + scheduledDepartureTime
                                : isEmpty(actualArrivalDate)
                                ? actualArrivalTime > scheduledArrivalTime
                                : actualArrivalDate + actualArrivalTime >
                                  scheduledArrivalDate + scheduledArrivalTime
                                ? colors.red
                                : colors.successGreen,
                            },
                          ]}
                        >
                          {departed ? actualDepartureTime : actualArrivalTime || ''}
                        </Text>
                        <Text style={styles.arrdepDetailsSchd} testID="live_train_status_page_intermediate_stations_right_box_text">
                          {departed ? scheduledDepartureTime : scheduledArrivalTime || ''}
                        </Text>
                      </View>
                    </View>

                    {incorrectLocationVisibility && (
                      <View style={styles.feedbackContainer} testID="live_train_status_page_intermediate_stations_right_box_feedback_container">
                        <TouchableRipple onPress={onFeedBackButtonClick} testID="live_train_status_page_intermediate_stations_right_box_feedback_container_touchable_ripple">
                          <View testID="live_train_status_page_intermediate_stations_right_box_feedback_container_view">
                            <FeedBackButton />
                          </View>
                        </TouchableRipple>
                      </View>
                    )}
                    <View
                      style={[
                        {
                          left: -1.5,
                          height:
                            index === IntermediateStations.length - 1
                              ? 70
                              : incorrectLocationVisibility
                              ? 20
                              : 30,
                        },
                        currentStoppingStationCheck
                          ? leftBorderStyleForCurrentStoppingStation2
                          : leftBorderStyle,
                        styles.grayBackground,
                      ]}
                      testID="live_train_status_page_intermediate_stations_right_box_view"
                    />
                    {currentStoppingStationCheck && currentIntermediateStationCheck && (
                      <View style={[styles.currentLottieContainer, { top: -26 + offset }]} testID="live_train_status_page_intermediate_stations_right_box_current_lottie_container">
                        <Animated.Image
                          testID="live_train_status_page_intermediate_stations_right_box_current_lottie_image"
                          style={[
                            styles.delayIndicatorImage,
                            {
                              width: 12,
                              transform: [
                                { translateX: -6 },
                                { scaleX: delayIndicatorImageStartWidth.current },
                                { translateX: 6 },
                              ],
                            },
                          ]}
                          source={!delay ? ltsDelayIndicatorGreenStart : ltsDelayIndicatorRedStart}
                        />
                        <LottieView
                          source={currentLottieIcon}
                          loop={true}
                          autoPlay={true}
                          progress={0}
                          style={styles.currentLottie}
                          testID="live_train_status_page_intermediate_stations_right_box_current_lottie_lottie_view"
                        />
                        <DelayIndicator />
                      </View>
                    )}
                  </View>
                </View>
              </View>
            </View>
          </TouchableRipple>
        );
      })}
    </>
  );
};

IntermediateStations.propTypes = {
  stopNumber: PropTypes.number,
  cancelled: PropTypes.bool,
  diverted: PropTypes.bool,
  currentStationStopNumber: PropTypes.number,
  IntermediateStations: PropTypes.array,
  otherOptions: PropTypes.object,
  onFeedBackButtonClick: PropTypes.func,
  currentIntermediateStopNumber: PropTypes.number,
  hideIntermediateStations: PropTypes.func,
  updateHeightArr: PropTypes.func,
  triggerScroll: PropTypes.func,
};

export default IntermediateStations;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.grayBg,
    paddingRight: 12,
    paddingLeft: 2,
  },
  leftBox: {
    width: 80,
    flexDirection: 'column',
    alignItems: 'flex-end',
    paddingTop: 19,
  },
  firstLineLeft: {
    marginRight: 20,
    color: colors.black,
    fontFamily: fonts.black,
    fontSize: 14,
  },
  secondLineLeft: {
    marginRight: 20,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    marginTop: 8,
    fontSize: 14,
  },
  rightBoxContainer: { flex: 3 },
  feedbackContainer: { alignItems: 'flex-end', paddingTop: 14 },
  rightBoxSpacing: {
    borderLeftWidth: 6,
    height: 20,
    width: 0,
  },
  trackContainer: {
    width: 6,
    height: '100%',
    left: -1.5,
    marginTop: 1.5,
    marginBottom: -1.5,
  },
  coloredTrack: {
    width: 6,
    position: 'absolute',
  },
  rightBoxBorder: { borderLeftWidth: 6 },
  leftBorderGrey: { borderColor: colors.lightGrey, backgroundColor: colors.lightGrey },
  leftBorderWhite: { borderColor: colors.white, backgroundColor: colors.white },
  leftBorderRed: { borderColor: colors.red, backgroundColor: colors.red },
  leftBorderGreen: { borderColor: colors.successGreen, backgroundColor: colors.successGreen },
  leftBorderYellow: { borderColor: colors.yellow, backgroundColor: colors.yellow },
  grayBackground: { backgroundColor: colors.grayBg },
  iconStyle: {
    left: -6,
    flexDirection: 'row',
  },
  circleIcon: {
    position: 'absolute',
    left: -11.5,
    width: 14,
    height: 14,
    backgroundColor: colors.white,
    borderWidth: 3,
    borderStyle: 'solid',
    borderRadius: 100,
  },
  currentLottieContainer: {
    position: 'absolute',
    left: -39.5,
  },
  currentLottie: {
    width: 70,
    height: 70,
  },
  firstLineRight: {
    color: colors.black,
    fontFamily: fonts.bold,
    fontSize: 14,
    marginLeft: 20,
  },
  secondLineRightContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    marginLeft: 20,
  },
  noStoppageLabel: {
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    fontSize: 14,
  },
  noStoppageLabelDiverted: {
    color: colors.yellow,
    fontFamily: fonts.regular,
    fontSize: 14,
  },
  noStoppageLabelCancelled: {
    color: colors.red,
    fontFamily: fonts.regular,
    fontSize: 14,
  },
  arrdepDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrdepDetailsExpt: {
    fontFamily: fonts.black,
    fontSize: 14,
    width: 38,
    textAlign: 'center',
  },
  arrdepDetailsSchd: {
    fontFamily: fonts.regular,
    fontSize: 14,
    color: colors.defaultTextColor,
    marginLeft: 12,
    width: 38,
  },
  delayIndicatorTextContainer: {
    flexDirection: 'row',
    position: 'absolute',
    left: 51.7,
    top: -1,
  },
  delayIndicatorText: {
    justifyContent: 'center',
    height: 19,
  },
  delayText: {
    fontStyle: 'italic',
    fontFamily: fonts.medium,
    fontSize: 10,
    color: colors.white,
    paddingVertical: 3,
  },
  boldFamily: { fontFamily: fonts.bold },
  delayIndicatorImage: {
    position: 'absolute',
    left: 40,
    height: 23,
    width: 12,
    top: -1,
  },
});
