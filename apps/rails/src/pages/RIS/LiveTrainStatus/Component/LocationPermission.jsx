/* eslint-disable */
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { View, TouchableOpacity, Text, Image, StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  LTS_GPS_BOTTOM_SHEET_SHOWN,
  LTS_OFFLINE_SWITCH_GPS_NO,
  LTS_OFFLINE_SWITCH_GPS_YES,
  trackOmnitureLTSClickEvent,
  trackOmnitureLTSLoadEvent,
} from '@mmt/rails/src/RisAnalytics/RisAnalytics';

import locationIcon from '@mmt/legacy-assets/src/ic_location_perm.webp';
import BottomSheetModalTrain from '../../../Common/BottomSheetModalTrain';

const LocationPermission = ({ onPermGranted, onPermRejected, message, primaryButton }) => {
  useEffect(() => {
    trackOmnitureLTSLoadEvent(LTS_GPS_BOTTOM_SHEET_SHOWN, '');
  }, []);

  const permissionRejected = () => {
    trackOmnitureLTSClickEvent(LTS_GPS_BOTTOM_SHEET_SHOWN, LTS_OFFLINE_SWITCH_GPS_NO);
    onPermRejected();
  };

  const permissionGranted = () => {
    trackOmnitureLTSClickEvent(LTS_GPS_BOTTOM_SHEET_SHOWN, LTS_OFFLINE_SWITCH_GPS_YES);
    onPermGranted();
  };

  return (
    <BottomSheetModalTrain onTouchOutside={permissionRejected} testID="location_permission_bottomsheet_container">
      <View style={styles.locationRoot} testID="location_permission_bottomsheet_root">
        <View style={styles.locationSettingsContent}>
          <Text style={styles.locationSettingsHeader} testID="location_permission_bottomsheet_header">{'Location Permission'}</Text>
          <Text style={styles.locationSettingsSubHeader} testID="location_permission_bottomsheet_subheader">
            {'Allow Makemytrip access to device location?'}
          </Text>
          <View style={styles.rowContainer} testID="location_permission_bottomsheet_row_container">
            <Image style={styles.locationSettingsLocationIcon} source={locationIcon} testID="location_permission_bottomsheet_location_icon" />
            <Text style={styles.locationSettingsRationaleText} testID="location_permission_bottomsheet_rationale_text">{message}</Text>
          </View>
          <View style={styles.locationSettingsActionsContainer} testID="location_permission_bottomsheet_actions_container">
            <TouchableOpacity onPress={permissionRejected} testID="location_permission_bottomsheet_not_now_button">
              <Text style={styles.locationSettingsActionButton} testID="location_permission_bottomsheet_not_now_button_text">{'NOT NOW'}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={permissionGranted} testID="location_permission_bottomsheet_allow_button">
              <Text
                style={[
                  styles.locationSettingsActionButton,
                  styles.locationSettingsActionButtonPrimary,
                ]}
              >
                {primaryButton}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </BottomSheetModalTrain>
  );
};

LocationPermission.propTypes = {
  onPermRejected: PropTypes.func.isRequired,
  onPermGranted: PropTypes.func.isRequired,
  message: PropTypes.string,
  primaryButton: PropTypes.string,
};

const styles = StyleSheet.create({
  locationRoot: {
    backgroundColor: colors.white,
  },
  locationSettingsContent: {
    margin: 24,
  },
  locationSettingsHeader: {
    fontSize: 34,
    fontFamily: fonts.light,
    color: colors.black,
  },
  locationSettingsSubHeader: {
    fontSize: 18,
    marginTop: 16,
    lineHeight: 20,
    marginBottom: 16,
    fontFamily: fonts.light,
    color: colors.black,
  },
  rowContainer: {
    flexDirection: 'row',
    marginRight: 24,
    alignItems: 'center',
  },
  locationSettingsLocationIcon: {
    width: 40,
    resizeMode: 'contain',
  },
  locationSettingsRationaleText: {
    fontSize: 16,
    lineHeight: 18,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    padding: 8,
  },
  locationSettingsActionsContainer: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'space-between',
  },
  locationSettingsActionButton: {
    color: colors.azure,
    fontSize: 16,
    fontFamily: fonts.medium,
    lineHeight: 18,
  },
  locationSettingsActionButtonPrimary: {
    fontFamily: fonts.bold,
  },
});

export default LocationPermission;
