import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { StoppingStation } from '../interface';

interface Props {
  value: string;
  currentStationSectionIndex: number;
  sectionIndex: number;
  firstStation: StoppingStation;
  started: boolean;
}

const DateLabel: React.FC<Props> = ({
  value,
  currentStationSectionIndex,
  sectionIndex,
  firstStation,
  started,
}) => {
  const cancelled = firstStation?.MetaDetails?.CancelledStation;
  const diverted = firstStation?.MetaDetails?.DivertedStation;

  // In case of any change in the dimension of this component pls update dimensions.SECTION_HEADER value in constants.ts file accordingly as it is required for autoscroll feature
  return (
    <View style={styles.container}>
      <View style={styles.leftBox} />
      <View
        style={[
          styles.rightBox,
          {
            borderLeftColor: cancelled
              ? colors.red
              : diverted
              ? colors.yellow
              : !started
              ? colors.lightGrey
              : sectionIndex <= currentStationSectionIndex
              ? colors.successGreen
              : colors.lightGrey,
          },
        ]}
      >
        <View style={styles.textPosition}>
          <Text style={styles.textStyle}>{value}</Text>
        </View>
        <Text style={{ paddingLeft: 20 }} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 12,
  },
  textPosition: {
    left: -36,
    zIndex: 1,
    width: 64,
    height: 18,
    backgroundColor: colors.black,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textStyle: {
    color: colors.white,
    fontSize: 12,
    fontFamily: fonts.bold,
  },
  leftBox: {
    width: 68.5,
    paddingVertical: 35,
  },
  rightBox: {
    flex: 1,
    borderLeftWidth: 6,
  },
});
export default DateLabel;
