import {
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_SET_LOADER,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT_INTERMEDIATE,
  ACTIONS_RAIL_LIVE_TRAIN_STATUS_OFFLINE_TRACKING,
  ACTIONS_RAIL_LIVE_TRAIN_STATUS_TRAIN_SCHEDULE,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_EMPTY,
  ACTIONS_RAIL_LTS_OFFLINE_TRACKING_FETCHING,
} from '../constants';

const initialState = {
  firstStationDetail: {},
  lastStationDetail: {},
  nextStationDetail: {},
  currentStationDetail: {},
  trainDetails: {},
  otherDetails: {},
  disclaimer: '',
  lastUpdated: '',
  stationsSectionList: [],
  pipModeStationList: [],
  showLoader: null,
  ltsViewState: null,
  ltsErrorMsg: '',
  otherOptions: {},
  ltsOfflineTrackingFetching: false,
};

//TODO: Try to move it to action
const getPIPData = (
  previousStationDetail,
  currentStationDetail,
  nextStationDetail,
) => {
  const { IntermediateStation, StoppingStation } = currentStationDetail || {};
  if (!StoppingStation) {
    return [];
  }
  let nextStations = [];
  //Finding the next intermediate station
  if (IntermediateStation?.StopNumber && StoppingStation?.IntermediateStations?.length > 0) {
    const nextIntermediateStations = StoppingStation.IntermediateStations.filter(
      (intermediateStation) => intermediateStation.StopNumber > IntermediateStation?.StopNumber,
    );
    if (nextIntermediateStations) {
      nextStations.push.apply(nextStations, nextIntermediateStations);
    }
  } else if (StoppingStation?.IntermediateStations?.length > 0) {
    nextStations.push.apply(nextStations, StoppingStation.IntermediateStations);
  }
  if (!nextStationDetail?.DepartureDetails?.departed) {
    nextStations.push(nextStationDetail);
    nextStations.push.apply(nextStations, nextStationDetail.IntermediateStations);
  }
  //TODO: Find the next to next station
  // if (nextStations?.length > 0 && stationsSectionList?.length > 0) {
  //   const nextToNextStation = stationsSectionList[0].data.find(
  //     (stations) => stations.StopNumber > nextStations[0].StopNumber,
  //   );
  //   if (nextToNextStation) {
  //     nextStations.push(nextToNextStation);
  //   }
  // }
  nextStations = nextStations.slice(0, 4);
  nextStations.push({}); //Pushing empty to have a space at the bottom for auto scrolling

  return [
    previousStationDetail,
    IntermediateStation || StoppingStation,
    ...nextStations,
  ];
};

export default (state = initialState, action) => {
  const { data } = action;
  switch (action.type) {
    case ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT: {
      return { ...state, ...action.data };
    }
    case ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT_INTERMEDIATE: {
      return { ...state, ...action.data };
    }
    case ACTIONS_RAILS_LIVE_TRAIN_STATUS_SET_LOADER: {
      return { ...action.data, stationsSectionList: [] };
    }
    case ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR: {
      return { ...state, ...action.data };
    }
    case ACTIONS_RAIL_LIVE_TRAIN_STATUS_OFFLINE_TRACKING: {
      const pipModeStationList = getPIPData(
        data.previousStationDetail,
        data.currentStationDetail,
        data.nextStationDetail,
      );
      return { ...state, ...action.data, pipModeStationList };
    }
    case ACTIONS_RAIL_LIVE_TRAIN_STATUS_TRAIN_SCHEDULE: {
      return { ...state, ...action.data };
    }
    case ACTIONS_RAILS_LIVE_TRAIN_STATUS_EMPTY: {
      return { ...initialState };
    }
    case ACTIONS_RAIL_LTS_OFFLINE_TRACKING_FETCHING: {
      return { ...state, ...action.data };
    }
    default:
      return state;
  }
};
