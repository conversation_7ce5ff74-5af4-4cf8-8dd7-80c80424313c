import {
  URL,
  LIVE_TRAIN_STATUS_REQUEST_URL_INTERMEDIATE_STATIONS_V2,
  LIVE_TRAIN_STATUS_REQUEST_URL_INTERMEDIATE_STATIONS,
  LIVE_TRAIN_STATUS_REQUEST_URL_V2,
  LIVE_TRAIN_STATUS_REQUEST_URL_V3,
  timeouts,
} from '../constants';
import { Platform } from 'react-native';
import isEmpty from 'lodash/isEmpty';
import fetch2 from '../../../../fetch2';
import { responseTransformer } from './ResponseTransformer';
import { AbConfigKeyMappings, getPokusConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';

export async function stoppingStationsAPIRequest(requestDetails, tlsV3Enabled) {
  const timeout = new Promise((resolve) => {
    setTimeout(() => resolve(null), timeouts.LIVESTATUS);
  });
  return await Promise.race([
    stoppingStationsAPIRequestPromise(requestDetails, tlsV3Enabled),
    timeout,
  ]);
}
export async function stoppingStationsAPIRequestPromise(requestDetails, tlsV3Enabled) {
  try {
    const completeURL = tlsV3Enabled ? LIVE_TRAIN_STATUS_REQUEST_URL_V3 : LIVE_TRAIN_STATUS_REQUEST_URL_V2;
    const trainNumber = requestDetails.trainDetail.trainNumber;
    const stationCode = requestDetails?.stationDetail?.stationCode;
    const dateOfJourney = requestDetails?.dateDetail?.dateNumeric;
    const body = {
      trainNumber,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    if (stationCode) {
      body.stationCode = stationCode;
    }
    if (dateOfJourney) {
      body.dateOfJourney = dateOfJourney;
    }
    const header = { 'Content-Type': 'appliation/json' };
    const response = await fetch2(completeURL, {
      method: 'POST',
      headers: header,
      body: JSON.stringify(body),
    });
    const jsonResponse = await response.json();
    if (isEmpty(jsonResponse) || Object.keys(jsonResponse).length === 0) {
      return null;
    }
    return jsonResponse;
  } catch (e) {
    return e;
  }
}

// to format raw json into a proper list
export const formatLTS = (haltingStations, currentStationCode) => {
  const trainLiveStatus = [];
  let currentStationStopNumber = 0;
  let currStation = {};
  for (let i = 0, j = 0; i < haltingStations.length; i += 1) {
    if (haltingStations[i].MetaDetails.StoppingStation) {
      const stationCode = haltingStations[i].Station.code;
      trainLiveStatus[j] = haltingStations[i];
      trainLiveStatus[j].StopNumber = j + 1;
      if (!isEmpty(currentStationCode) && stationCode === currentStationCode) {
        currentStationStopNumber = j + 1;
        currStation.StoppingStation = trainLiveStatus[j];
      }
      trainLiveStatus[j].Location = haltingStations[i]?.Location ?? {};
      if (!trainLiveStatus[j].IntermediateStations) {
        trainLiveStatus[j].IntermediateStations = [];
      }
      j += 1;
    }
  }
  const [trainLiveStatusSection, currentStationSectionIndex] = formatLTSSection(
    trainLiveStatus,
    currentStationStopNumber,
  );
  if (!isEmpty(currStation)) {currStation.StoppingStation.SectionIndex = currentStationSectionIndex;}
  return [trainLiveStatusSection, currStation];
};

// convert list of stations to Section List
const formatLTSSection = (lts, currentStationStopNumber) => {
  let date = lts[0].ArrivalDetails.scheduledArrivalDate;
  let index = 0;
  let currentStationSectionIndex = 0;
  const ltsSection = [];
  ltsSection.push({ title: { date, sectionIndex: index + 1 }, data: [] });
  for (let i = 0; i < lts.length; i += 1) {
    if (lts[i].ArrivalDetails.scheduledArrivalDate !== date) {
      date = lts[i].ArrivalDetails.scheduledArrivalDate;
      index += 1;
      ltsSection.push({ title: { date, sectionIndex: index + 1 }, data: [] });
    }
    ltsSection[index].data.push(lts[i]);
    if (lts[i].StopNumber === currentStationStopNumber) {
      currentStationSectionIndex = index + 1;
    }
  }
  return [ltsSection, currentStationSectionIndex];
};

export async function intermediateStationsAPIRequest(requestDetails, trainStartDate) {
  const timeout = new Promise((resolve) => {
    setTimeout(() => {
      resolve(null);
    }, timeouts.SCHEDULEINTERMEDIATE);
  });
  return await Promise.race([
    intermediateStationsAPIRequestPromise(requestDetails, trainStartDate),
    timeout,
  ]);
}

export async function intermediateStationsAPIRequestPromise(requestDetails, trainStartDate) {
  try {
    let intermediateAPIV2 = getPokusConfig(PokusLobs.RAIL, AbConfigKeyMappings.enableRailsLTSIntermediateAPIV2, false);
    const completeURL = URL.concat(intermediateAPIV2 ? LIVE_TRAIN_STATUS_REQUEST_URL_INTERMEDIATE_STATIONS_V2 : 
      LIVE_TRAIN_STATUS_REQUEST_URL_INTERMEDIATE_STATIONS);
    const trainNumber = requestDetails.trainDetail.trainNumber;
    const body = {
      trainNumber,
      trainStartDate,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    const header = { 'Content-Type': 'appliation/json' };
    const response = await fetch2(completeURL, {
      method: 'POST',
      headers: header,
      body: JSON.stringify(body),
    });
    const jsonResponse = await response.json();
    if (isEmpty(jsonResponse) || Object.keys(jsonResponse).length === 0) {
      return null;
    }
    if (intermediateAPIV2) {
      const response = responseTransformer(jsonResponse);
      response.TrainDetails = {};
      response.TrainDetails.Number = requestDetails?.trainDetail?.trainNumber;
      response.TrainDetails.Name = requestDetails?.trainDetail?.trainName;
      return response;
    }
    return jsonResponse;
  } catch (e) {
    return e;
  }
}

// insert intermediate stations in SectionList
export const formatLTSSectionWithIntermediateStation = (
  stationsSectionList,
  intermediateStations,
) => {
  const finalSectionList = [...stationsSectionList];
  for (let i = 0; i < finalSectionList.length; i += 1) {
    for (let j = 0; j < finalSectionList[i].data.length; j += 1) {
      const station = intermediateStations?.find(
        (st) => st.Station.code === finalSectionList[i].data[j].Station.code,
      );
      if (!isEmpty(station)) {
        finalSectionList[i].data[j].Location = station.Location;
        finalSectionList[i].data[j].Day = station.Day;
        finalSectionList[i].data[j].IntermediateStations = station.IntermediateStations;
      }
    }
  }
  return finalSectionList;
};

export const _getChannelCode = () => {
  let channelCode;
  if (Platform.OS === 'web') {
    channelCode = 'PWA';
  } else if (Platform.OS === 'android') {
    channelCode = 'ANDROID';
  } else if (Platform.OS === 'ios') {
    channelCode = 'IOS';
  } else {
    channelCode = Platform.OS;
  }
  return channelCode;
};
