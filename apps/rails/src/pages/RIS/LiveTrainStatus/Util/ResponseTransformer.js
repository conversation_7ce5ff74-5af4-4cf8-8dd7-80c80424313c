const keyMap = {
    DOfR: 'DaysOfRun',
    Nu: 'Number',
    Sts: 'Stations',
    ISts: 'IntermediateStations',
    St: 'Station',
    na: 'name',
    co: 'code',
    DDe: 'DepartureDetails',
    ADe: 'ArrivalDetails',
    Lo: 'Location',
    SpNu: 'StopNumber',
    Di: 'Distance',
    Dy: 'Day',
    DiC: 'DistanceCovered',
    CToSt: 'CancelledToStation',
    CFSt: 'CancelledFromStation',
    Te: 'Terminated',
    a: 'arrived',
    D: 'Departed',
    SDt: 'StartDate',
    SDy: 'StartDay',
    LUpd: 'LastUpdated',
    Dis: 'Disclaimer',
    MDe: 'MetaDetails',
    CuSt: 'CurrentStation',
    Sun: 'Sun',
    Mon: 'Mon',
    Tue: 'Tue',
    Wed: 'Wed',
    Thu: 'Thu',
    Fri: 'Fri',
    Sat: 'Sat',
    longitude: 'longitude',
    latitude: 'latitude',
    scheduledArrivalDate: 'scheduledArrivalDate',
    scheduledArrivalTime: 'scheduledArrivalTime',
    scheduledDepartureDate: 'scheduledDepartureDate',
    scheduledDepartureTime: 'scheduledDepartureTime',
    TAvgS: 'TrainAvgSpeed',
};

export const responseTransformer = (jsonResponse) => {
    let response = recursively(jsonResponse);
    return response;
};

const daysConversion = (daysOfRun) => {
    // This variable comes as a number from the API. It will be parsed to binary form to get the train running days.
    // After conversion First bit will be Sunday and last bit will be Saturday.
    let days = daysOfRun.toString(2);
    return {
        Sun: days[0] === '0' ? false : true,
        Mon: days[1] === '0' ? false : true,
        Tue: days[2] === '0' ? false : true,
        Wed: days[3] === '0' ? false : true,
        Thu: days[4] === '0' ? false : true,
        Fri: days[5] === '0' ? false : true,
        Sat: days[6] === '0' ? false : true,
    };
};

const locationConversion = (location) => {
    return {
        latitude: parseFloat(location.split('_')[0]),
        longitude: parseFloat(location.split('_')[1]),
    };
};

const arrivalDetails = (timeStamp) => {
    let result = {};
    let arr = timeStamp.split(' ');
    if (arr.length === 2) {
        result.scheduledArrivalDate = arr[0];
        result.scheduledArrivalTime = arr[1];
    } else {
        result.scheduledArrivalTime = arr[0];
    }

    return result;
};

const departureDetails = (timeStamp) => {
    let result = {};
    let arr = timeStamp.split(' ');
    if (arr.length === 2) {
        result.scheduledDepartureDate = arr[0];
        result.scheduledDepartureTime = arr[1];
    } else {
        result.scheduledDepartureTime = arr[0];
    }

    return result;
};

function recursively(obj, result = {}, parentKey = null) {
    let keys = Object.keys(obj);
    keys.forEach((key) => {
        if (key === 'DOfR') {
            obj[key] = daysConversion(obj[key]);
        } else if (key === 'Lo') {
            obj[key] = locationConversion(obj[key]);
        } else if (key === 'ADe') {
            obj[key] = arrivalDetails(obj[key]);
        } else if (key === 'DDe') {
            obj[key] = departureDetails(obj[key]);
        }

        if (Array.isArray(obj[key])) {
            result[keyMap[key]] = [];
            obj[key].forEach((x) => {
                result[keyMap[key]].push(recursively(x, {}));
            });
        } else if (typeof obj[key] === 'object') {
            recursively(obj[key], result, key);
        } else {
            if (parentKey !== null) {
                if (!result[keyMap[parentKey]]) {
                    result[keyMap[parentKey]] = {};
                }

                result[keyMap[parentKey]][keyMap[key]] = obj[key];
            } else {
                result[keyMap[key]] = obj[key];
            }
        }
    });

    return result;
}
