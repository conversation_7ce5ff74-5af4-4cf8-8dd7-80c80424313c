import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import {
  getTimeStringFromStationDetails,
  getTimeStringFromDate,
  getTimeStringFromArithmetic,
  getDelay,
  getDistanceCovered,
  getDateFromTimeStamp,
  getISOTimeStringFromDate,
  getDateFromArithmetic,
} from '../Component/commonUtils';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { Platform } from 'react-native';
import {
  getCanReportError,
  getOfflineSwitchingLogic,
  getIsDynamicSpeedComputationEnabled,
} from '../../../../RailsAbConfig';
import { ALGORITHM, fireBaseErrorType } from '../constants';
import { SPEED_STATUS } from 'apps/rails/src/Utils/RailsConstant';
import {
  getCurrentLatLngWithPerm,
} from '@mmt/legacy-commons/Helpers/locationHelper';
import {
  LTS_OFFLINE_SWITCH_FAIL_CTE,
  LTS_OFFLINE_SWITCH_FAIL_CTCSF,
  LTS_OFFLINE_SWITCH_FAIL_CTTCF,
  LTS_OFFLINE_SWITCH_FAIL_GPSE,
  LTS_OFFLINE_SWITCH_FAIL_GPSUF,
  LTS_OFFLINE_SWITCH_FAIL_GPSTLM,
  LTS_OFFLINE_SWITCH_FAIL_GPSTCF,
  LTS_OFFLINE_SWITCH_FAIL_GPSDNV,
} from 'apps/rails/src/RisAnalytics/RisAnalytics';
import { getStoredConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { GMAP } from 'apps/rails/src/RailsConfig';
import { isUserOnTrainByGPSV2 } from './OfflineTrackingV2Helper';

let lastCurrentLocation = {};

//This function will get the location from native and gives the previous speed if current speed is 0. (one time)
export const getCurrentLatLng = async (nativeCTGPSData, enableLtsImprovementChanges) => {
  try {
    let currentLocation = nativeCTGPSData ? nativeCTGPSData : 
    await getCurrentLatLngWithPerm(enableLtsImprovementChanges); // lat lng
    if (!currentLocation?.lng){
      return currentLocation;
    }
    if (lastCurrentLocation?.hasSpeed && !currentLocation?.hasSpeed) {
      currentLocation.speed = lastCurrentLocation.speed;
      lastCurrentLocation = currentLocation;
      currentLocation.hasSpeed = true;
    } else {
      lastCurrentLocation = currentLocation;
    }
    return currentLocation;
  } catch (e) {
    return undefined;
  }
};

export const cellTowerMappingAPIRequest = async (trainNumber) => {
  try {
    const init = await RailsModule.initializeDataBaseLTS();
    if (!init) {return undefined;}
    const jsonStringResponse = await RailsModule.checkAndInsertLTSData(trainNumber);
    const jsonResponse = JSON.parse(jsonStringResponse);
    return jsonResponse?.TowersData;
  } catch (e) {
    return undefined;
  }
};

export const getFirstNextLastStationDetails = (stationsSectionList, currentStationDetail) => {
  let i,
    j,
    firstStationDetail,
    lastStationDetail,
    nextStationDetail,
    currStationFound = false,
    firstStationFound = false,
    nextStationFound = false,
    lastStationFound = false;
  for (i = 0; i < stationsSectionList.length; i += 1) {
    for (j = 0; j < stationsSectionList[i].data.length; j += 1) {
      if (
        !(
          stationsSectionList[i]?.data[j]?.MetaDetails?.CancelledStation ||
          stationsSectionList[i]?.data[j]?.MetaDetails?.DivertedStation
        )
      ) {
        firstStationDetail = stationsSectionList[i].data[j];
        firstStationFound = true;
        break;
      }
    }
    if (firstStationFound) {break;}
  }
  if (!firstStationFound) {firstStationDetail = stationsSectionList[0].data[0];}
  const currStationCode =
    currentStationDetail?.StoppingStation?.Station?.code || firstStationDetail?.Station?.code;
  if (isEmpty(currStationCode)) {nextStationDetail = firstStationDetail;}
  else {
    for (i = 0; i < stationsSectionList.length; i += 1) {
      for (j = 0; j < stationsSectionList[i].data.length; j += 1) {
        if (
          !(
            stationsSectionList[i]?.data[j]?.MetaDetails?.CancelledStation ||
            stationsSectionList[i]?.data[j]?.MetaDetails?.DivertedStation
          )
        ) {
          if (currStationFound) {
            nextStationDetail = stationsSectionList[i].data[j];
            nextStationFound = true;
            break;
          }
          if (
            !currStationFound &&
            stationsSectionList[i]?.data[j]?.Station?.code === currStationCode
          )
            {currStationFound = true;}
        }
      }
      if (nextStationFound) {break;}
    }
  }
  if (!nextStationFound) {nextStationDetail = firstStationDetail;}
  for (i = stationsSectionList.length - 1; i >= 0; i -= 1) {
    for (j = stationsSectionList[i].data.length - 1; j >= 0; j -= 1) {
      if (
        !(
          stationsSectionList[i]?.data[j]?.MetaDetails?.CancelledStation ||
          stationsSectionList[i]?.data[j]?.MetaDetails?.DivertedStation
        )
      ) {
        lastStationDetail = stationsSectionList[i].data[j];
        lastStationFound = true;
        break;
      }
    }
    if (lastStationFound) {break;}
  }
  if (!lastStationFound) {lastStationDetail =
    stationsSectionList[stationsSectionList.length - 1].data[
      stationsSectionList[stationsSectionList.length - 1].data.length - 1
    ];}
  return [firstStationDetail, nextStationDetail, lastStationDetail];
};

export const extractStationDataForLogging = data => {
  try {
    const currentIntermediateStationCode = get(data, 'currentStationDetail.IntermediateStation.Station.code');
    const currentIntermediateStationArrival = get(data, 'currentStationDetail.IntermediateStation.ArrivalDetails.actualArrivalTime', '');
    const currentStoppingStationCode = get(data, 'currentStationDetail.StoppingStation.Station.code');
    const currentStoppingStationArrival = get(data, 'currentStationDetail.StoppingStation.ArrivalDetails.actualArrivalTime', '');
    const currentStoppingStation = data?.currentStationDetail?.StoppingStation;

    const checkIfTrainIsInArrivedStatus = currentIntermediateStationCode
      ? !currentIntermediateStationArrival
      : currentStoppingStation?.ArrivalDetails?.arrived &&
        !currentStoppingStation?.DepartureDetails?.departed;

    if (!checkIfTrainIsInArrivedStatus) {return {};}

    const currentStationCode = currentIntermediateStationCode ?? currentStoppingStationCode;
    const stationArrivalTime = currentIntermediateStationCode
      ? currentIntermediateStationArrival
      : currentStoppingStationArrival;

    return {
      stationCode: currentStationCode,
      arrivalTime: stationArrivalTime,
    };
  }
  catch (error) {
    console.log('Error in extractStationDataForLogging: ', error);
    return {};
  }
};

export const nextStationTimeStampCheck = (
  expectedCurrentStationCode,
  currentTimeStamp,
  stationsSectionList,
) => {
  const stationList = convertToStationList(stationsSectionList);
  const expectedCurrentStationIndex = searchStationList(stationList, expectedCurrentStationCode);
  let expectedNextStoppingStationDetail;
  for (let i = expectedCurrentStationIndex + 1; i < stationList.length; i += 1) {
    if (stationList[i].Stopping) {
      expectedNextStoppingStationDetail = getStoppingStation(
        stationsSectionList,
        stationList[i].Code,
      );
      break;
    }
  }
  if (!expectedNextStoppingStationDetail) {return true;}
  const expectedNextStoppingStationTimeStamp = getTimeStringFromStationDetails(
    expectedNextStoppingStationDetail,
  );
  if (currentTimeStamp < expectedNextStoppingStationTimeStamp.arrivalTimeStamp) {return true;}
  return false;
};


//Find the previous stopping station and validate if current time is greater then previous station departure time.
export const previousStationTimeStampCheck = (
  expectedCurrentStationCode,
  currentTimeStamp,
  stationsSectionList,
  errorObj = {},
) => {
  const stationList = convertToStationList(stationsSectionList);
  const expectedCurrentStationIndex = searchStationList(stationList, expectedCurrentStationCode);
  let expectedPreviousStoppingStationDetail;
  //Finding the previous stopping station by searching in reverse order from current station,
  //whichever station has Stopping true will take that as previous stopping station.
  for (let i = expectedCurrentStationIndex - 1; i >= 0; i -= 1) {
    if (stationList[i].Stopping) {
      expectedPreviousStoppingStationDetail = getStoppingStation(
        stationsSectionList,
        stationList[i].Code,
      );
      break;
    }
  }
  if (!expectedPreviousStoppingStationDetail) {return true;}
  const expectedPreviousStoppingStationTimeStamp = getTimeStringFromStationDetails(
    expectedPreviousStoppingStationDetail,
  );

  if (currentTimeStamp >= expectedPreviousStoppingStationTimeStamp.departureTimeStamp) {return true;}
  try {
    const validDateObj = getDateFromArithmetic(
      expectedPreviousStoppingStationTimeStamp.departureTimeStamp,
    );
    const prevStationDepartureIso = getISOTimeStringFromDate(validDateObj);
    errorObj.prevStationCheck = {
      prevStationCode: expectedPreviousStoppingStationDetail?.Station?.code,
      prevStationDeparture: prevStationDepartureIso,
    };
  } catch (error) {
    //Ignore
  }
  return false;
};

export const lastCurrentStationDetailCheck = (
  expectedCurrentStationCode,
  lastCurrentStationDetail,
  stationsSectionList,
) => {
  const stationList = convertToStationList(stationsSectionList);
  const expectedCurrentStationIndex = searchStationList(stationList, expectedCurrentStationCode);
  if (isEmpty(lastCurrentStationDetail)) {return true;}
  const lastcurrIndex = lastCurrentStationDetail?.IntermediateStation
    ? searchStationList(stationList, lastCurrentStationDetail.IntermediateStation.Station.code)
    : searchStationList(stationList, lastCurrentStationDetail.StoppingStation.Station.code);
  if (expectedCurrentStationIndex >= lastcurrIndex) {return true;}
  return false;
};

export const checkTimeStampValidity = (expectedCurrentStationCode, delay, stationsSectionList) => {
  const _delay = isEmpty(delay) ? 0 : delay;
  const currentTimeStampWithDelay = getTimeStringFromDate(
    new Date(Date.now() - _delay * 60 * 1000),
  ); // DD-MM-YYYY HH:mm
  const currentTimeStamp = getTimeStringFromDate(new Date());
  const isNextStationTimeStampCheck = nextStationTimeStampCheck(
    expectedCurrentStationCode,
    currentTimeStampWithDelay,
    stationsSectionList,
  );
  const isPreviousStationTimeStampCheck = previousStationTimeStampCheck(
    expectedCurrentStationCode,
    currentTimeStamp,
    stationsSectionList,
  );
  if (
    isPreviousStationTimeStampCheck &&
    (isEmpty(delay) || (!isEmpty(delay) && isNextStationTimeStampCheck))
  )
    {return true;}
  return false;
};

export const isUserOnTrainByCT = (
  expectedCurrentStationCode,
  lastCurrentStationDetail,
  stationsSectionList,
  delay,
) => {
  const isLastCurrentStationDetailCheck = lastCurrentStationDetailCheck(
    expectedCurrentStationCode,
    lastCurrentStationDetail,
    stationsSectionList,
  );
  const isTimeStampCheck = checkTimeStampValidity(
    expectedCurrentStationCode,
    delay,
    stationsSectionList,
  );
  if (isLastCurrentStationDetailCheck && isTimeStampCheck) {
    const stationList = convertToStationList(stationsSectionList);
    const expectedCurrentStationIndex = searchStationList(stationList, expectedCurrentStationCode);
    const currentStationDetail = findOfflineCurrentStation(
      stationList,
      expectedCurrentStationIndex,
      stationsSectionList,
    );
    if (isEmpty(currentStationDetail)){
      return { isUserOnTrain: false, errorTracking: LTS_OFFLINE_SWITCH_FAIL_CTE };
    }
    return {
      isUserOnTrain: true,
      currentStationDetail,
    };
  }
  let errorTracking = '';
  if (!isLastCurrentStationDetailCheck){
    errorTracking = LTS_OFFLINE_SWITCH_FAIL_CTCSF;
  } else {
    errorTracking = LTS_OFFLINE_SWITCH_FAIL_CTTCF;
  }
  return { isUserOnTrain: false , errorTracking};
};

const isDistanceValidGPS = (
  currentLocation,
  lastCurrentStationDetail,
  stationsSectionList,
  trainAvgSpeed,
  errorObj = {},
) => {
  if (isEmpty(lastCurrentStationDetail)) {return true;}
  const stationList = convertToStationList(stationsSectionList);
  const lastCurrIndex = !isEmpty(lastCurrentStationDetail?.IntermediateStation?.Station?.code)
    ? searchStationList(stationList, lastCurrentStationDetail?.IntermediateStation?.Station?.code)
    : searchStationList(stationList, lastCurrentStationDetail?.StoppingStation?.Station?.code);
  const fMinIndex = calculateDistance(currentLocation, stationList, lastCurrIndex);
  const ltsMasterConfig = getStoredConfigStore(configKeys.RAILS_LTS_MASTER_CONFIG);
  const lastCurrentStation = stationList[lastCurrIndex];
  const expectedCurrentStation = stationList[lastCurrIndex + fMinIndex];
  const trainSpeed = getTrainSpeed(
    currentLocation,
    trainAvgSpeed ?? ltsMasterConfig?.ltsDefaultSpeed,
  );
  const lastDistanceCovered = lastCurrentStation?.Distance;
  const expectedDistanceCovered = expectedCurrentStation?.Distance;
  const distanceCoveredDiff = Math.abs(expectedDistanceCovered - lastDistanceCovered);
  const haltMinutes = stationList.reduce(
    (accumulatedValue, currentStation, currentStationIndex) => {
      if (currentStationIndex >= lastCurrIndex && currentStationIndex < lastCurrIndex + fMinIndex)
        {accumulatedValue += currentStation?.HaltMinutes;}
      return accumulatedValue;
    },
    0,
  );
  const { latitude: lastLat, longitude: lastLng } = lastCurrentStation?.Location || {};
  const { latitude: expLat, longitude: expLng } = expectedCurrentStation?.Location || {};
  const { lat: curLat, lng: curLng } = currentLocation || {};
  const lastTimeStamp =
    lastCurrentStationDetail?.StoppingStation?.ArrivalDetails?.actualArrivalDate?.split(' ')[0] +
    ' ' +
    lastCurrentStationDetail?.StoppingStation?.ArrivalDetails?.actualArrivalTime +
    ':00';
  const _timeDiff = (getTimeDiff(lastTimeStamp) - haltMinutes) / 60;
  const timeDiff = _timeDiff < 0 ? 0 : _timeDiff;
  const probableDistanceCoveredinDiff = trainSpeed * timeDiff;
  const isDistanceValid =
    Math.abs(probableDistanceCoveredinDiff - distanceCoveredDiff) <=
      ltsMasterConfig?.distanceCoveredDiffLimit ?? 50;
  const MAP_URL = `${GMAP}&origin=${lastLat},${lastLng}&destination=${expLat},${expLng}&waypoints=${curLat},${curLng}`;
  errorObj.distanceCheck = {};
  if (!isDistanceValid) {
    try {
      errorObj.distanceCheck = {
        lastDistanceCovered,
        expectedDistanceCovered,
        distanceCoveredDiff,
        trainSpeed,
        timeDiffInHours: timeDiff,
        probableDistanceCoveredDiff: probableDistanceCoveredinDiff,
      };
    } catch (error) {
      //ignore
    }
  }
  errorObj.map = MAP_URL;
  return isDistanceValid;
};

export const isUserOnTrainByGPS = (
  currentLocation,
  lastCurrentStationDetail,
  stationsSectionList,
  delay,
  trainAvgSpeed,
) => {
  const ltsMasterConfig = getStoredConfigStore(configKeys.RAILS_LTS_MASTER_CONFIG);
  const offlineSwitchingLogic = getOfflineSwitchingLogic();
  if (offlineSwitchingLogic === ALGORITHM.V1) {
    return isUserOnTrainByGPSV2(
      currentLocation,
      lastCurrentStationDetail,
      stationsSectionList,
      ltsMasterConfig,
    );
  }
  const errorObj = {};
  const stationList = convertToStationList(stationsSectionList);
  const lastCurrIndex = isEmpty(lastCurrentStationDetail)
    ? 0
    : !isEmpty(lastCurrentStationDetail?.IntermediateStation?.Station?.code)
    ? searchStationList(stationList, lastCurrentStationDetail?.IntermediateStation?.Station?.code)
    : searchStationList(stationList, lastCurrentStationDetail?.StoppingStation?.Station?.code);
  //Creating distance list of user location to stations location.
  //TODO: Consider departure time for calculateDistance;
  const [distanceArray, fMinIndex] = calculateDistance(currentLocation, stationList, lastCurrIndex);
  //Whichever station distance is near to user, will consider that as current station.
  const exptCurrIndex = lastCurrIndex + fMinIndex;
  const expectedCurrentStation = stationList[exptCurrIndex];
  const userDistanceFromExpectedStation = distanceArray[fMinIndex];
  const lastCurrentStation = stationList[lastCurrIndex];
  errorObj.lastCurrentStation = lastCurrentStation?.Code;
  errorObj.expectedCurrentStation = expectedCurrentStation?.Code;
  errorObj.userDistanceFromExpectedStation = userDistanceFromExpectedStation;
  const currentTimeStamp = getTimeStringFromDate(new Date());
  try {
    const getISOTimeStringFromDates = getISOTimeStringFromDate(new Date());
    errorObj.currentTimeStamp = getISOTimeStringFromDates;
  } catch (error) {
    //ignore error
  }
  //1. Find the previous stopping station and validate if current time
  //   is greater then previous station departure time.
  //TODO: Relax time
  const isPreviousStationTimeStampCheck = previousStationTimeStampCheck(
    expectedCurrentStation?.Code,
    currentTimeStamp,
    stationsSectionList,
    errorObj,
  );
  errorObj.prevStationCheck = {
    ...errorObj?.prevStationCheck,
    prevStationTimestampCheck: isPreviousStationTimeStampCheck,
  };
  //2. Calculate the probable distance covered from last stopping station arrival time,
  //   if two station diff minus probable distance is more then 50 km then distance is not valid.
  //TODO: 120 km avg speed from BE
  const isDistanceValid = isDistanceValidGPS(
    currentLocation,
    lastCurrentStationDetail,
    stationsSectionList,
    trainAvgSpeed,
    errorObj,
  );
  errorObj.distanceCheck = { ...errorObj?.distanceCheck, isDistanceValid };
  const maxUserDistanceFromStation = ltsMasterConfig?.maxUserDistanceFromStation ?? 10000;
  //TODO: Either find a max dist or remove

  if (
    isPreviousStationTimeStampCheck &&
    (isEmpty(delay) || isDistanceValid) &&
    userDistanceFromExpectedStation <= maxUserDistanceFromStation
  ) {
    const currentStationDetail = findOfflineCurrentStation(
      stationList,
      exptCurrIndex,
      stationsSectionList,
    );
    if (isEmpty(currentStationDetail)) {
      return { isUserOnTrain: false, errorTracking: LTS_OFFLINE_SWITCH_FAIL_GPSE };
    }
    return {
      isUserOnTrain: true,
      currentStationDetail,
      userDistanceFromExpectedStation,
    };
  }
  let errorTracking = '';
 if (!isPreviousStationTimeStampCheck){
    errorTracking = LTS_OFFLINE_SWITCH_FAIL_GPSTCF;
  } else if ((!isEmpty(delay) || !isDistanceValid)){
    errorTracking = LTS_OFFLINE_SWITCH_FAIL_GPSDNV;
  } else if (userDistanceFromExpectedStation >= maxUserDistanceFromStation) {
    errorTracking = LTS_OFFLINE_SWITCH_FAIL_GPSUF;
    if (!expectedCurrentStation?.Location?.latitude){
      errorTracking = LTS_OFFLINE_SWITCH_FAIL_GPSTLM;
    }
  }
  return { isUserOnTrain: false, errorTracking, isUserOnTrainByGPSerror: errorObj };
};

export const updateDetails = (
  currentStationDetailParam,
  atStation,
  stationsSectionListParam,
  trainDetailsParam,
  userDistanceFromExpectedStation,
  currentLocation,
) => {
  const currentStationDetail = { ...currentStationDetailParam },
    stationsSectionList = [...stationsSectionListParam],
    trainDetails = { ...trainDetailsParam };
  let delay = 0;
  const [firstStationDetail, lastStationDetail] = getFirstNextLastStationDetails(
    stationsSectionList,
    currentStationDetail,
  );
  const currentTimeStamp = getTimeStringFromDate(new Date()).split(' ');
  if (currentStationDetail?.IntermediateStation) {
    const { i, j, k } = getIntermediateStationIndexParams(
      stationsSectionList,
      currentStationDetail?.StoppingStation?.Station?.code,
      currentStationDetail?.IntermediateStation?.Station?.code,
    );
    const station = stationsSectionList[i]?.data[j]?.IntermediateStations[k];
    if (atStation && !station?.ArrivalDetails?.arrived) {
      station.ArrivalDetails.arrived = true;
      station.ArrivalDetails.actualArrivalDate = currentTimeStamp[0] + ' 00:00:00';
      station.ArrivalDetails.actualArrivalTime = currentTimeStamp[1];
      station.DepartureDetails.departed = false;
      station.DepartureDetails.actualDepartureDate = `${currentTimeStamp[0]} 00:00:00`;
      station.DepartureDetails.actualDepartureTime = currentTimeStamp[1];
    } else if (!atStation && station?.ArrivalDetails?.arrived && !station?.DepartureDetails?.departed) {
      station.DepartureDetails.departed = true;
      station.DepartureDetails.actualDepartureDate = currentTimeStamp[0] + ' 00:00:00';
      station.DepartureDetails.actualDepartureTime = currentTimeStamp[1];
    }
    stationsSectionList[i].data[j].IntermediateStations[k] = station;
    currentStationDetail.IntermediateStation = station;
    trainDetails.Departed = true;
  } else {
    const { i, j } = getStoppingStationIndexParams(
      stationsSectionList,
      currentStationDetail?.StoppingStation?.Station?.code,
    );
    const station = stationsSectionList[i]?.data[j];
    if (firstStationDetail?.Station?.code !== station?.Station?.code) {trainDetails.Departed = true;}
    if (atStation && !station?.ArrivalDetails?.arrived) {
      const currTime = getTimeStringFromDate(new Date());
      const expCurrArr = getTimeStringFromStationDetails(station).arrivalTimeStamp;
      if (
        !(currTime < expCurrArr && firstStationDetail?.Station?.code === station?.Station?.code)
      ) {
        station.ArrivalDetails.arrived = true;
        station.ArrivalDetails.actualArrivalDate = currentTimeStamp[0] + ' 00:00:00';
        station.ArrivalDetails.actualArrivalTime = currentTimeStamp[1];
        const departureTimeStamp = getTimeStringFromArithmetic(
          currentTimeStamp.join(' '),
          station?.HaltMinutes,
        );
        station.DepartureDetails.departed = false;
        station.DepartureDetails.actualDepartureDate = departureTimeStamp[0] + ' 00:00:00';
        station.DepartureDetails.actualDepartureTime = departureTimeStamp[1];
        delay = getTimeDiffInMins(currTime, expCurrArr);
        delay = delay < 0 ? 0 : delay;
        trainDetails.Departed = true;
        if (lastStationDetail?.Station?.code === station?.Station?.code)
          {trainDetails.Terminated = true;}
      }
    } else if (!atStation && !station?.ArrivalDetails?.arrived) {
      const trainSpeed = getTrainSpeed(currentLocation, trainDetailsParam.TrainAvgSpeed);
      const metersCoveredInaMinute = parseInt(trainSpeed * 16.667); // 60kmph => 16.667 meters per second
      const remainingTimeInMins = parseInt(userDistanceFromExpectedStation || 0) / metersCoveredInaMinute;
      //const remainingTimeInMins = parseInt(userDistanceFromExpectedStation || 0) / 1000; // 60kmph => 1000m in 1min
      const expectedTimeOfArrival = getTimeStringFromDate(
        new Date(Date.now() + remainingTimeInMins * 60 * 1000),
      ).split(' ');
      const expectedTimeOfDeparture = getTimeStringFromArithmetic(
        expectedTimeOfArrival.join(' '),
        station?.HaltMinutes,
      );
      station.ArrivalDetails.arrived = false;
      station.ArrivalDetails.actualArrivalDate = expectedTimeOfArrival[0] + ' 00:00:00';
      station.ArrivalDetails.actualArrivalTime = expectedTimeOfArrival[1];
      station.DepartureDetails.departed = false;
      station.DepartureDetails.actualDepartureDate = expectedTimeOfDeparture[0] + ' 00:00:00';
      station.DepartureDetails.actualDepartureTime = expectedTimeOfDeparture[1];
    } else if (
      station?.ArrivalDetails?.arrived &&
      !atStation &&
      !station?.DepartureDetails?.departed
    ) {
      const currTime = getTimeStringFromDate(new Date());
      const expCurrDept = getTimeStringFromStationDetails(station).departureTimeStamp;
      if (!(currTime >= expCurrDept)) {
        const errorMessage = `currTime:${currTime} expCurrDept:${expCurrDept} trainNumber:${trainDetails.trainNumber}`;
        logErrorToFirebase(fireBaseErrorType.UPDATE_ERROR, 105, errorMessage);
        return { isUserOnTrain: false };
      } else if (lastStationDetail?.Station?.code !== station?.Station?.code) {
        station.DepartureDetails.departed = true;
        station.DepartureDetails.actualDepartureDate = currentTimeStamp[0] + ' 00:00:00';
        station.DepartureDetails.actualDepartureTime = currentTimeStamp[1];
        trainDetails.Departed = true;
        delay = getTimeDiffInMins(currTime, expCurrDept);
        delay = delay < 0 ? 0 : delay;
      }
    }
    stationsSectionList[i].data[j] = station;
    currentStationDetail.StoppingStation = stationsSectionList[i].data[j];
    trainDetails.CurrentStation = currentStationDetail?.StoppingStation?.Station;
  }
  const currentStation = currentStationDetail?.IntermediateStation?.Station?.code
    ? currentStationDetail?.IntermediateStation
    : currentStationDetail?.StoppingStation;
  const delayInMins = getDelay(currentStation);
  const currentStationIndex = getStoppingStationIndexParams(
    stationsSectionList,
    currentStationDetail?.StoppingStation?.Station?.code,
  );
  for (let section = 0; section < stationsSectionList?.length; section += 1) {
    for (let station = 0; station < stationsSectionList?.[section]?.data?.length; station += 1) {
      if (
        section > currentStationIndex?.i ||
        (section === currentStationIndex?.i && station > currentStationIndex?.j)
      ) {
        const cstation = stationsSectionList[section].data[station];
        const expectedTimeOfArrival = getTimeStringFromArithmetic(
          [
            cstation?.ArrivalDetails?.scheduledArrivalDate?.split(' ')?.[0],
            cstation?.ArrivalDetails?.scheduledArrivalTime,
          ]?.join(' '),
          delayInMins.value,
        );
        const expectedTimeOfDeparture = getTimeStringFromArithmetic(
          [
            cstation?.DepartureDetails?.scheduledDepartureDate?.split(' ')?.[0],
            cstation?.DepartureDetails?.scheduledDepartureTime,
          ]?.join(' '),
          delayInMins.value,
        );
        cstation.ArrivalDetails.actualArrivalDate = `${expectedTimeOfArrival[0]} 00:00:00`;
        cstation.ArrivalDetails.actualArrivalTime = expectedTimeOfArrival[1];
        cstation.DepartureDetails.actualDepartureDate = expectedTimeOfDeparture[0] + ' 00:00:00';
        cstation.DepartureDetails.actualDepartureTime = expectedTimeOfDeparture[1];
        stationsSectionList[section].data[station] = cstation;
      }
    }
  }
  trainDetails.DistanceCovered = getDistanceCovered(currentStationDetail);
  return {
    isUserOnTrain: true,
    stationsSectionList,
    currentStationDetail,
    trainDetails,
    delay,
  };
};

const getDate = (date, addDay) => {
  return (
    getTimeStringFromDate(
      new Date(getDateFromTimeStamp(date).getTime() + addDay * 24 * 60 * 60 * 1000),
    ).split(' ')[0] + ' 00:00:00'
  );
};

export const getSchedule = (requestDetails, trainDetails, stationsSectionList) => {
  let finalTrainDetails = { ...trainDetails };
  finalTrainDetails.Departed = false;
  finalTrainDetails.Terminated = false;
  finalTrainDetails.StartDate =
    (!isEmpty(requestDetails?.dateDetail)
      ? requestDetails?.dateDetail?.dateNumeric
      : getTimeStringFromDate(new Date()).split(' ')[0]) + ' 00:00:00';
  finalTrainDetails.StartDay = !isEmpty(requestDetails?.dateDetail)
    ? requestDetails?.dateDetail?.dateTitle
    : 'Today';
  finalTrainDetails.distanceCovered = 0;
  let finalSectionList = [...stationsSectionList];
  for (let i = 0; i < finalSectionList.length; i += 1) {
    finalSectionList[i].title.date = getDate(finalTrainDetails.StartDate, i);
    for (let j = 0; j < finalSectionList[i].data.length; j += 1) {
      let station = finalSectionList[i].data[j];
      station.MetaDetails.CancelledStation = false;
      station.MetaDetails.DivertedStation = false;
      station.ArrivalDetails = {
        ...station.ArrivalDetails,
        arrived: false,
        scheduledArrivalDate: getDate(
          finalTrainDetails.StartDate,
          station.DayDetails.scheduledDayCount - 1,
        ),
        actualArrivalDate: getDate(
          finalTrainDetails.StartDate,
          station.DayDetails.scheduledDayCount - 1,
        ),
        actualArrivalTime: station.ArrivalDetails.scheduledArrivalTime,
      };
      station.DepartureDetails = {
        ...station.DepartureDetails,
        departed: false,
        scheduledDepartureDate: getDate(
          finalTrainDetails.StartDate,
          station.DayDetails.scheduledDayCount - 1,
        ),
        actualDepartureDate: getDate(
          finalTrainDetails.StartDate,
          station.DayDetails.scheduledDayCount - 1,
        ),
        actualDepartureTime: station.DepartureDetails.scheduledDepartureTime,
      };
      for (let k = 0; k < station.IntermediateStations.length; k += 1) {
        let intermediateStation = station.IntermediateStations[k];
        intermediateStation.ArrivalDetails = {
          ...intermediateStation.ArrivalDetails,
          arrived: false,
          scheduledArrivalDate: getDate(finalTrainDetails.StartDate, intermediateStation.Day - 1),
          actualArrivalTime: undefined,
          actualArrivalDate: undefined,
        };
        intermediateStation.DepartureDetails = {
          ...intermediateStation.DepartureDetails,
          departed: false,
          scheduledDepartureDate: getDate(finalTrainDetails.StartDate, intermediateStation.Day - 1),
          actualDepartureTime: undefined,
          actualDepartureDate: undefined,
        };
        station.IntermediateStations[k] = intermediateStation;
      }
      finalSectionList[i].data[j] = station;
    }
  }
  return {
    trainDetails: finalTrainDetails,
    stationsSectionList: finalSectionList,
  };
};

export const convertToStationList = (stationsSectionList) => {
  let stationList = [];
  for (let i = 0; i < stationsSectionList.length; i += 1) {
    for (let j = 0; j < stationsSectionList[i].data.length; j += 1) {
      if (
        !(
          stationsSectionList[i].data[j].MetaDetails.CancelledStation ||
          stationsSectionList[i].data[j].MetaDetails.DivertedStation
        )
      ) {
        stationList.push(extractUsefulData(stationsSectionList[i].data[j]));
        for (let k = 0; k < stationsSectionList[i].data[j].IntermediateStations.length; k += 1) {
          let station = extractUsefulData(stationsSectionList[i].data[j].IntermediateStations[k]);
          station.StoppingStation = stationsSectionList[i].data[j].Station.code;
          if (!station?.Distance || station?.Distance === 0.0) {
            station.Distance = stationsSectionList[i]?.data[j]?.Distance;
          }
          stationList.push(station);
        }
      }
    }
  }
  return stationList;
};

export const extractUsefulData = (data) => {
  let station = {};
  station.Code = data?.Station?.code;
  const arrivalDateAndTime = `${data?.ArrivalDetails?.actualArrivalDate?.split(' ')?.[0]} ${
    data?.ArrivalDetails?.actualArrivalTime
  }`;
  station.ArrivalDateTime = arrivalDateAndTime;
  station.Distance = data?.Distance;
  station.Location = data?.Location;
  station.Stopping = data?.MetaDetails?.StoppingStation ? true : false;
  station.HaltMinutes = data?.HaltMinutes || 0;
  return station;
};

export const searchStationList = (stationList, currentStationCode) => {
  let currIndex = 0;
  for (let i = 0; i < stationList.length; i++) {
    if (stationList[i].Code === currentStationCode) {
      currIndex = i;
      break;
    }
  }
  return currIndex;
};

export const calculateDistance = (userLocation, stationList, currIndex) => {
  let distanceArray = [];
  for (let i = currIndex; i < stationList.length; i += 1) {
    distanceArray.push(distanceBetween(stationList[i].Location, userLocation));
  }
  const fMinIndex = minimumDistance(distanceArray);
  return [distanceArray, fMinIndex];
};

export const distanceBetween = (trainLocation, userLocation) => {
  const earthRadius = 3958.75;
  const trainLatitude = (Math.PI * trainLocation.latitude) / 180;
  const userLatitude = (Math.PI * userLocation.lat) / 180;
  const latDiff = (Math.PI * (trainLocation.latitude - userLocation.lat)) / 180;
  const lngDiff = (Math.PI * (trainLocation.longitude - userLocation.lng)) / 180;
  const a =
    Math.sin(latDiff / 2) ** 2 +
    Math.cos(userLatitude) * Math.cos(trainLatitude) * (Math.sin(lngDiff / 2) ** 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = 1609 * earthRadius * c;
  if (!distance) {return Number.MAX_SAFE_INTEGER;}
  return distance;
};

export const minimumDistance = (distanceArray) => {
  let fMin = {
    distance: distanceArray[0],
    index: 0,
  };
  for (let i = 0; i < distanceArray.length; i += 1) {
    if (fMin.distance > distanceArray[i]) {
      fMin.distance = distanceArray[i];
      fMin.index = i;
    }
  }
  return fMin.index;
};

export const getTimeDiff = (lastUpdated) => {
  const currTime = new Date();
  const a = new Date(
    currTime.getFullYear(),
    currTime.getMonth(),
    currTime.getDate(),
    currTime.getHours(),
    currTime.getMinutes(),
    currTime.getSeconds(),
    0,
  );
  const lastTime = lastUpdated.split(' ');
  const ltDate = lastTime[0].split('-');
  const ltTime = lastTime[1].split(':');
  const b = new Date(ltDate[2], ltDate[1] - 1, ltDate[0], ltTime[0], ltTime[1], ltTime[2], 0);
  return (a.getTime() - b.getTime()) / 60000.0;
};

export const getTimeDiffInMins = (a, b) => {
  const aT = a.split(' ');
  const aTD = aT[0].split('-');
  const aTT = aT[1].split(':');
  const aDate = new Date(aTD[2], aTD[1] - 1, aTD[0], aTT[0], aTT[1], 0, 0);

  const bT = b.split(' ');
  const bTD = bT[0].split('-');
  const bTT = bT[1].split(':');
  const bDate = new Date(bTD[2], bTD[1] - 1, bTD[0], bTT[0], bTT[1], 0, 0);

  const mins = (aDate.getTime() - bDate.getTime()) / 60000;
  return mins;
};

export const findOfflineCurrentStation = (stationList, index, stationsSectionList) => {
  if (isEmpty(stationList) || stationList.length === 0 || isEmpty(stationsSectionList)) {return null;}
  const station = stationList[index];
  let currentStationDetail = {};
  if (station?.Stopping) {
    currentStationDetail.StoppingStation = getStoppingStation(stationsSectionList, station.Code);
  } else {
    currentStationDetail.StoppingStation = getStoppingStation(
      stationsSectionList,
      station.StoppingStation,
    );
    currentStationDetail.IntermediateStation = getIntermediateStation(
      stationsSectionList,
      station.StoppingStation,
      station.Code,
    );
  }
  return currentStationDetail;
};

export const getStoppingStationIndexParams = (stationsSectionList, code) => {
  let i, j;
  for (i = 0; i < stationsSectionList.length; i += 1) {
    for (j = 0; j < stationsSectionList[i].data.length; j += 1) {
      if (stationsSectionList[i].data[j].Station.code === code) {
        return { i, j };
      }
    }
  }
};

export const getIntermediateStationIndexParams = (
  stationsSectionList,
  stoppingStationCode,
  intermediateStationCode,
) => {
  for (let i = 0; i < stationsSectionList.length; i += 1) {
    for (let j = 0; j < stationsSectionList[i].data.length; j += 1) {
      if (stationsSectionList[i].data[j].Station.code === stoppingStationCode) {
        for (let k = 0; k < stationsSectionList[i].data[j].IntermediateStations.length; k += 1) {
          if (
            stationsSectionList[i].data[j].IntermediateStations[k].Station.code ===
            intermediateStationCode
          ) {
            return { i, j, k };
          }
        }
      }
    }
  }
  return { undefined, undefined, undefined };
};

export const getStoppingStation = (stationsSectionList, code) => {
  const { i, j } = getStoppingStationIndexParams(stationsSectionList, code);
  let station = stationsSectionList?.[i]?.data?.[j] || {};
  station.SectionIndex = i + 1;
  return station;
};

export const getIntermediateStation = (
  stationsSectionList,
  stoppingStationCode,
  intermediateStationCode,
) => {
  const { i, j, k } = getIntermediateStationIndexParams(
    stationsSectionList,
    stoppingStationCode,
    intermediateStationCode,
  );
  return stationsSectionList?.[i]?.data?.[j]?.IntermediateStations?.[k] || {};
};

const getTrainSpeed = (currentLocation, trainAvgSpeed = 60) => {
  const enableDynamicSpeedComputation = getIsDynamicSpeedComputationEnabled();
  if (!enableDynamicSpeedComputation && Platform.OS !== 'android'){
    return trainAvgSpeed;
  }

  const { hasSpeed, speed } = currentLocation || {};
  if (hasSpeed && speed > 0) {
    if (speed < 40 || speed > 200) {
      return trainAvgSpeed;
    } else {
      return speed;
    }
  } else {
    return trainAvgSpeed;
  }
};

export const logErrorToFirebase = (errorType, errorCode, errorMessage) => {
  try {
    if (getCanReportError(true)) {
      RailsModule.logFirebaseError(`${errorType}:${errorCode} ${errorMessage}`);
    }
  } catch (error) {
    console.error('Error while logErrorToFirebase');
  }
};

export const getTrainRunningStatus = (delay,speed,currentStation) => {
  if (
    currentStation?.ArrivalDetails?.arrived &&
    !currentStation?.DepartureDetails?.departed &&
    speed === 0
  ) {
    return SPEED_STATUS.PLANNED_HALT;
  } else if (speed === 0) {
    return SPEED_STATUS.UNPLANNED_HALT;
  } else if (delay) {
    return SPEED_STATUS.DELAY;
  } else if (!delay) {
    return SPEED_STATUS.ON_TIME;
  } else {
    return SPEED_STATUS.NA;
  }
};
