import { getDeviceDetails } from '@mmt/legacy-commons/Helpers/genericHelper';
import { getUserDetails } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

import {
  getCurrentInternetConnectionAndApproxSpeed,
  isNetworkAvailable,
} from '@mmt/legacy-commons/Common/utils/AppUtils';
import { getSessionID } from 'apps/rails/src/Utils/CommonUtils';
import { getDataFromAsynStorage, setDataToAsyncStorage } from 'apps/rails/src/Utils/RailsConstant';
import { getStoredConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import fetch2 from 'apps/rails/src/fetch2';
import {
  LTS_ERROR_LOGGING_URL,
  asyncStorageKeys,
  fireBaseErrorType,
} from 'apps/rails/src/pages/RIS/LiveTrainStatus/constants';
import { ErrorLogData } from 'apps/rails/src/pages/RIS/LiveTrainStatus/interface';
import isEmpty from 'lodash/isEmpty';
import { logErrorToFirebase } from './OfflineTrackingHelper';
import { getIfEnableLTSErrorLogging } from 'apps/rails/src/RailsAbConfig';

const createErrorBody = async (errorLogData: ErrorLogData) => {
  const errorLogBody = { ...errorLogData };
  const userDetails = await getUserDetails();
  const sessionId = await getSessionID();
  if (!isEmpty(userDetails)) {
    errorLogBody.userDetails = {
      uuid: userDetails.uuid,
      mmtAuth: userDetails.mmtAuth,
    };
  }
  errorLogBody.sessionId = sessionId;
  const deviceDetails = await getDeviceDetails();
  const internetInfo = await getCurrentInternetConnectionAndApproxSpeed();
  if (!isEmpty(deviceDetails)) {
    errorLogBody.deviceDetails = {
      deviceName: deviceDetails.deviceName,
      deviceId: deviceDetails.deviceId,
      networkType: deviceDetails.networkType,
      os: deviceDetails.os,
      networkSpeed: internetInfo,
    };
  }
  return errorLogBody;
};

export const logLTSErrorsToAPI = async () => {
  try {
    const hasNetwork = await isNetworkAvailable();
    const errorLogDataList = await getDataFromAsynStorage(asyncStorageKeys.ERROR_LOGGING);
    if (errorLogDataList?.length > 10) {
      errorLogDataList.splice(errorLogDataList.length - 10, 10);
      setDataToAsyncStorage(asyncStorageKeys.ERROR_LOGGING, errorLogDataList);
    }
    if (hasNetwork) {
      if (isEmpty(errorLogDataList)) {
        return;
      }
      const header = {
        'Content-Type': 'application/json',
      };
      const response = await fetch2(LTS_ERROR_LOGGING_URL, {
        method: 'POST',
        headers: header,
        body: JSON.stringify(errorLogDataList),
      });
      if (response?.ok && response?.status === 200) {
        setDataToAsyncStorage(asyncStorageKeys.ERROR_LOGGING, []);
      }
      return response;
    }
  } catch (error) {
    console.error('Error in logLTSErrorsToAPI',error);
    return null;
  }
};

export const logLTSErrors = async (errorLogData: ErrorLogData) => {
  if (getIfEnableLTSErrorLogging()) {
    const errorLogBody = await createErrorBody(errorLogData);
    getDataFromAsynStorage(asyncStorageKeys.ERROR_LOGGING)
      .then((errorLogDataList: ErrorLogData[]) => {
        if (isEmpty(errorLogDataList)) {
          errorLogDataList = [];
        }
        const ltsMasterConfig = getStoredConfigStore(configKeys.RAILS_LTS_MASTER_CONFIG);
        const limit = ltsMasterConfig?.errorUploadBatchLimit ?? 5;
        if (errorLogDataList.length >= limit) {
          errorLogDataList.shift();
        }
        errorLogDataList.push(errorLogBody);
        return setDataToAsyncStorage(asyncStorageKeys.ERROR_LOGGING, errorLogDataList);
      })
      .then(async () => {
        const hasNetwork = await isNetworkAvailable();
        if (hasNetwork) {
          (await logLTSErrorsToAPI)();
        }
        delete errorLogBody.scheduleApiResponse;
        logErrorToFirebase(fireBaseErrorType.OVERALL_ERROR, 1001, JSON.stringify(errorLogBody));
        return true;
      })
      .catch((error) => {
        console.error('Error in logLTSErrors : ', error);
      });
  }
};
