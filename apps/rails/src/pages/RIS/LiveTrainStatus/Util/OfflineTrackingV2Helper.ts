import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { getDateFromArithmetic } from '../Component/commonUtils';
import {
  CurrentStation,
  IntermediateStation,
  LTS_MASTER_CONFIG,
  StationsSection,
  StoppingStation,
  UserCurrentLocation,
} from '../interface';
import {
  convertToStationList,
  distanceBetween,
  extractUsefulData,
  findOfflineCurrentStation,
  minimumDistance,
  searchStationList,
} from './OfflineTrackingHelper';
import isEmpty from 'lodash/isEmpty';

export const calculateDistance = (
  userLocation: UserCurrentLocation,
  stationList: [StoppingStation | IntermediateStation],
  currIndex: number,
) => {
  const distanceArray = [];
  // let prevDistance = distanceBetween(stationList[currIndex].Location, userLocation);
  for (let i = currIndex; i < stationList.length; i += 1) {
    const distance = distanceBetween(stationList[i].Location, userLocation);
    // if (distance - prevDistance && stationList[i].Location.latitude >) {
    //     break; //Breaking as all next station distance will be higher only.
    // }
    distanceArray.push(distance);
    // prevDistance = distance;
  }
  const fMinIndex = minimumDistance(distanceArray);
  return [distanceArray, fMinIndex];
};

//Arrival time of train should not be more then 1/2 hr from current time.
export const validateTime = (station, hourDiff = 1) => {
  const arrivalDateTime = getDateFromArithmetic(station.ArrivalDateTime, 1);
  if (arrivalDateTime.getTime() < new Date().getTime()) {
    return true;
  }
  const diff = Math.abs(arrivalDateTime.getTime() - new Date().getTime()) / 36e5;
  if (__DEV__ && diff > hourDiff) {
    showShortToast('Time validation failed');
  }
  return diff <= hourDiff;
};

export const isUserOnTrainByGPSV2 = (
  currentLocation: UserCurrentLocation,
  lastCurrentStationDetail: CurrentStation,
  stationsSectionList: StationsSection[],
  ltsMasterConfig: LTS_MASTER_CONFIG,
) => {
  let isUserOnTrain = false;
  try {
    const stationList = convertToStationList(stationsSectionList);
    const lastCurrIndex = isEmpty(lastCurrentStationDetail)
      ? 0
      : !isEmpty(lastCurrentStationDetail?.IntermediateStation?.Station?.code)
      ? searchStationList(stationList, lastCurrentStationDetail?.IntermediateStation?.Station?.code)
      : searchStationList(stationList, lastCurrentStationDetail?.StoppingStation?.Station?.code);
    const [distanceArray] = calculateDistance(currentLocation, stationList, lastCurrIndex);
    const curvedDiff = 4;
    for (let i = lastCurrIndex; i < distanceArray.length + lastCurrIndex; i += 1) {
      if (!stationList[i].Stopping) {
        continue;
      }
      const stationsDistance = stationList[i].Distance - stationList[i ? i - 1 : 0].Distance;
      const dI = i - lastCurrIndex;
      const userDistance = distanceArray[dI] + distanceArray[dI ? dI - 1 : 0];
      const userDistanceInKm = userDistance / 1000;
      if (userDistanceInKm <= stationsDistance + curvedDiff) {
        if (validateTime(stationList[i], ltsMasterConfig.maxHoursDiffFromArrival)) {
          const userDistanceFromExpectedStation = distanceArray[dI];
          const currentStationDetail = findOfflineCurrentStation(
            stationList,
            i,
            stationsSectionList,
          );
          isUserOnTrain = true;
          return {
            isUserOnTrain: true,
            currentStationDetail,
            userDistanceFromExpectedStation,
          };
        }
      }
    }
  } catch (error) {
    console.error('Error in isUserOnTrainByGPSV2');
  }
  return { isUserOnTrain, errorTracking: '', isUserOnTrainByGPSerror: {} };
};

export const convertToHaltingStationList = (stationsSectionList) => {
  const stationList = [];
  for (let i = 0; i < stationsSectionList.length; i += 1) {
    for (let j = 0; j < stationsSectionList[i].data.length; j += 1) {
      if (
        !(
          stationsSectionList[i].data[j].MetaDetails.CancelledStation ||
          stationsSectionList[i].data[j].MetaDetails.DivertedStation
        )
      ) {
        stationList.push(extractUsefulData(stationsSectionList[i].data[j]));
      }
    }
  }
  return stationList;
};
