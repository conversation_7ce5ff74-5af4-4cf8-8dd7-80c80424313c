import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import {
  checkForLocationPermission,
} from '@mmt/legacy-commons/Helpers/locationHelper';
import isEmpty from 'lodash/isEmpty';
import cloneDeep from 'lodash/cloneDeep';
import {
  stoppingStationsAPIRequest,
  formatLTS,
  intermediateStationsAPIRequest,
  formatLTSSectionWithIntermediateStation,
} from './OnlineTrackingHelper';
import { ACTIONS_RAILS_PNR_CROSS_SELL_DATA } from 'apps/rails/src/pages/RIS/PnrStatus/RailInfoAction';
import {
  cellTowerMappingAPIRequest,
  isUserOnTrainByCT,
  getFirstNextLastStationDetails,
  getSchedule,
  isUserOnTrainByGPS,
  updateDetails,
  getIntermediateStation,
  extractStationDataForLogging,
  logErrorToFirebase,
  getCurrentLatLng,
} from './OfflineTrackingHelper';
import {
  timeSince,
  getPreviousStationDetail,
  getDistanceCovered,
  isFutureDate,
  ToastMessage,
  getTimeStringFromStationDetails,
  getDateFromTimeStamp,
  getTimeStringFromDate,
  sendLogs,
  formatToIST,
} from '../Component/commonUtils';
import {
  PAGE_RIS_LTS_INFO,
  PAGE_RIS_LTS_ERROR,
  trackOmnitureLTSLoadEvent,
  LTS_OFFLINE_SWITCH_FAIL_GPSUNE,
  LTS_OFFLINE_SWITCH_FAIL_CTCNF,
  LTS_OFFLINE_SWITCH_FAIL_CTNV,
  LTS_OFFLINE_SWITCH_FAIL_CTUNE,
  trackOmnitureLTSVisitEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import {
  labels,
  asyncStorageKeys,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_SET_LOADER,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT_INTERMEDIATE,
  ACTIONS_RAIL_LIVE_TRAIN_STATUS_OFFLINE_TRACKING,
  ACTIONS_RAIL_LIVE_TRAIN_STATUS_TRAIN_SCHEDULE,
  ACTIONS_RAILS_LIVE_TRAIN_STATUS_EMPTY,
  omnitureLabels,
  viewStates,
  toastLabels,
  timeouts,
  ACTIONS_RAIL_LTS_OFFLINE_TRACKING_FETCHING,
  fireBaseErrorType,
  Source,
  ALGORITHM,
} from '../constants';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { getDataFromStorage, setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { getDateDetails, getTrainStatusDates } from '../../../../Utils/RisUtils';
import fecha from 'fecha';
import { getEnableLtsImprovementChanges } from 'apps/rails/src/RailsAbConfig';
import { logLTSErrors } from './LiveTrainStatusErrorLogger';
import { showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { getLtsCrossSellData } from '../../PnrStatus/RailInfoAction';
import { INTERACTED_WITH_MEALS_LTS } from '../../MealsOnTrain/Constants/MealsConstants';

let retryTimeOut;

let prevStationsSectionListOffline,
  prevTrainDetailsOffline,
  delayOffline,
  currentStationDetailOffline = {},
  cellTowerMap = {},
  tlsV3Enabled = false,
  isRisLtsPlotGPS = true,
  isRisLtsPlotCT = true,
  locationPermissionGranted = false;

const logData = {};
let errorLogData = {};

const resetCTOptions = () => {
  prevStationsSectionListOffline = [];
  prevTrainDetailsOffline = {};
  delayOffline = undefined;
  currentStationDetailOffline = {};
};

export const initializeLTS = async (
  requestDetails,
  tlsV3EnabledPokus,
  isRisLtsPlotGPSPokus,
  isRisLtsPlotCTPokus,
) => {
  tlsV3Enabled = tlsV3EnabledPokus;
  isRisLtsPlotGPS = isRisLtsPlotGPSPokus;
  isRisLtsPlotCT = isRisLtsPlotCTPokus;
  setDataInStorage(INTERACTED_WITH_MEALS_LTS, false);
  resetCTOptions();
  if (isRisLtsPlotCTPokus) {
    const cellTowerMapCT = await cellTowerMappingAPIRequest(requestDetails.trainDetail.trainNumber);
    cellTowerMap = cellTowerMapCT || {};
  }
};

export const clearOfflineError = (updateOptions) => (dispatch, getState) => {
  const prevOtherOptions = getState().liveTrainStatusReducer.otherOptions;
  const otherOptions = { ...prevOtherOptions, ...updateOptions };
  dispatch({
    type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
    data: { otherOptions },
  });
};

export const clearStore = (dispatch) => {
  resetCTOptions();
  dispatch({
    type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_EMPTY,
    data: {},
  });
};

export const initTracking = (requestDetails, refresh, autoRefresh, refreshCount = -1) => async (
  dispatch,
  getState,
) => {
  errorLogData = {};
    dispatch(removeRailsCrossSellData());
  errorLogData.refreshCount = refreshCount;
  errorLogData.algorithm = ALGORITHM.V0;
  const journeyDate = requestDetails?.dateDetail?.dateNumeric;
  errorLogData.searchDetails = {
    trainNo: requestDetails?.trainDetail?.trainNumber,
      doj: journeyDate?.split('-')?.reverse()?.join('-'),
  };
  let isInsideTrain = false;
  const lastSavedInfo = await getDataFromStorage(asyncStorageKeys.RIS_LTS_OPTIONS);
  if (
    !isEmpty(lastSavedInfo) &&
    lastSavedInfo?.trainNumber === requestDetails?.trainDetail?.trainNumber
  )
    {isInsideTrain = lastSavedInfo?.isInsideTrainStatus;}

  const hasNetwork = await isNetworkAvailable();
  if (!(autoRefresh || refresh)) {
    resetCTOptions();
    dispatch({
      type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_SET_LOADER, // this will erase any previous data
      data: { showLoader: true, ltsViewState: viewStates.LOADING },
    });
  }
  if (refresh) {
    dispatch({
      type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
      data: { showLoader: false, ltsViewState: viewStates.REFRESH },
    });
  }
  // if (isInsideTrain || !hasNetwork) {
  //   try {
  //     const hasPerm = await checkForLocationPermission();
  //     if (!hasPerm) {
  //       dispatch(errorHandlerForOfflineTracking(requestDetails, refresh, autoRefresh, false));
  //       return;
  //     }
  //   } catch (error) {
  //     console.log('Error in Permission: ', error);
  //     dispatch(errorHandlerForOfflineTracking(requestDetails, refresh, autoRefresh, false));
  //     return;
  //   }
  // }

  if (hasNetwork) {
    dispatch(onlineTracking(requestDetails, isInsideTrain, refresh, autoRefresh));
  } else {
    const otherOptions = autoRefresh ? { ...getState().liveTrainStatusReducer?.otherOptions } : {};
    otherOptions.noNetwork = true;
    otherOptions.onlineError = true;
    trackOmnitureLTSLoadEvent(PAGE_RIS_LTS_ERROR, omnitureLabels.NO_NETWORK_ERROR);
    dispatch({
      type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
      data: { otherOptions },
    });
    if (isFutureDate(requestDetails?.dateDetail?.dateNumeric))
      {dispatch(lastWay(requestDetails, refresh, autoRefresh, false));}
    else {
         // eslint-disable-next-line
      new Promise((resolve) => {
        setTimeout(() => resolve(null), 1000); // This is to show switcher for atleast 1 sec
      }).then(() => dispatch(offlineTracking(requestDetails, refresh, autoRefresh, false)));
    }
  }
};

export const offlineTracking = (
  requestDetails,
  refresh,
  autoRefresh,
  gotOnlineResponse,
  nativeCTGPSData = null,
) => async (dispatch) => {
  try {
    dispatch({
      type: ACTIONS_RAIL_LTS_OFFLINE_TRACKING_FETCHING,
      data: { ltsOfflineTrackingFetching: true },
    });
    const hasPerm = await checkForLocationPermission();
    locationPermissionGranted = hasPerm;
    if (!hasPerm) {
      dispatch(
        errorHandlerForOfflineTracking(requestDetails, refresh, autoRefresh, gotOnlineResponse),
      );
      return;
    }
  } catch (error) {
    console.log('Error in permission check: ', error);
    dispatch(
      errorHandlerForOfflineTracking(requestDetails, refresh, autoRefresh, gotOnlineResponse),
    );
    return;
  }
  const data = await getDataForSchedule(requestDetails);
  const {
    stationsSectionList: cachedStationsSectionList = {},
    currentStationDetail: cachedCurrentStationDetail = {},
    trainDetails: cachedTrainDetails = {},
    lastAPIFetched = new Date(),
    lastUpdated,
  } = data || {};

  if (isEmpty(cachedStationsSectionList)) {
    dispatch(
      errorHandlerForOfflineTracking(requestDetails, refresh, autoRefresh, gotOnlineResponse),
    );
    return;
  }
  try {
    errorLogData.ltsData = {
      lastFetched: formatToIST(lastAPIFetched),
      lastUpdated: formatToIST(lastUpdated),
      currentStationCode: cachedCurrentStationDetail?.StoppingStation?.Station?.code,
    };
    errorLogData.scheduleApiResponse = JSON.stringify(cachedStationsSectionList);
  } catch (error) {
    //ignore error
  }
  intializeOfflineParameters(
    cachedTrainDetails,
    requestDetails,
    cachedStationsSectionList,
    cachedCurrentStationDetail,
    lastAPIFetched,
  );
  const lastCurrentStationDetailCT = cloneDeep(currentStationDetailOffline);
  const stationsSectionListCT = cloneDeep(prevStationsSectionListOffline);
  const trainDetailsCT = cloneDeep(prevTrainDetailsOffline);
  const lastCurrentStationDetailGPS = cloneDeep(currentStationDetailOffline);
  const stationsSectionListGPS = cloneDeep(prevStationsSectionListOffline);
  const trainDetailsGPS = cloneDeep(prevTrainDetailsOffline);
  const promiseArray = [];
  logData.trainDetail = {};
  logData.locationData = {};
  logData.stationData = {};

  if (isRisLtsPlotGPS)
    {promiseArray.push(
      Promise.race([
        GPSTracking(
          requestDetails,
          data,
          stationsSectionListGPS,
          lastCurrentStationDetailGPS,
          trainDetailsGPS,
          nativeCTGPSData,
        ),
        new Promise((resolve) => {
          setTimeout(() => resolve({ isUserOnTrain: false }), timeouts.OFFLINE);
        }),
      ]),
    );}
  if (isRisLtsPlotCT)
    {promiseArray.push(
      Promise.race([
        CTTracking(
          requestDetails,
          data,
          stationsSectionListCT,
          lastCurrentStationDetailCT,
          trainDetailsCT,
        ),
        new Promise((resolve) => {
          setTimeout(() => resolve({ isUserOnTrain: false }), timeouts.OFFLINE);
        }),
      ]),
    );}
  Promise.all(promiseArray)
    .then((response) => {
      dispatch({
        type: ACTIONS_RAIL_LTS_OFFLINE_TRACKING_FETCHING,
        data: { ltsOfflineTrackingFetching: false },
      });
      let gpsData = {},
        ctData = {};
      if (isRisLtsPlotGPS && isRisLtsPlotCT) {
        gpsData = response[0];
        ctData = response[1];
      } else if (isRisLtsPlotGPS && !isRisLtsPlotCT) {gpsData = response[0];}
      else if (!isRisLtsPlotGPS && isRisLtsPlotCT) {ctData = response[0];}
      dispatch(
        integrateGpswithCT(
          gpsData,
          ctData,
          requestDetails,
          refresh,
          autoRefresh,
          gotOnlineResponse,
        ),
      );
    })
    .catch((error) => {
      console.log('Offline Tracking Failing: ', error);
      dispatch(
        errorHandlerForOfflineTracking(requestDetails, refresh, autoRefresh, gotOnlineResponse),
      );
    });
};

const integrateGpswithCT = (
  gpsData,
  cellTowerData,
  requestDetails,
  refresh,
  autoRefresh,
  gotOnlineResponse,
) => (dispatch) => {
  const isUserOnTrainByGps = gpsData?.isUserOnTrain;
  const isUserOnTrainByCT = cellTowerData?.isUserOnTrain;

  if ((isUserOnTrainByGps || isUserOnTrainByCT) && !(autoRefresh || refresh)) {
    trackOmnitureLTSLoadEvent(PAGE_RIS_LTS_INFO, omnitureLabels.OFFLINE_TRACKING_DONE);
  } else {
    trackOmnitureLTSVisitEventAsync(
      !isUserOnTrainByGps ? gpsData?.errorTracking : cellTowerData?.errorTracking,
    );
  }
  if (!isUserOnTrainByGps && isUserOnTrainByCT) {
    dispatch(dispatchOfflineDataToComponent(cellTowerData, Source.CELL_TOWER));
  } else if (isUserOnTrainByGps && !isUserOnTrainByCT) {
    dispatch(dispatchOfflineDataToComponent(gpsData, Source.GPS));
  } else if (isUserOnTrainByGps && isUserOnTrainByCT) {
    const gpsStationCode = isEmpty(gpsData?.currentStationDetail?.IntermediateStation)
      ? gpsData?.currentStationDetail?.StoppingStation?.Station?.code
      : gpsData?.currentStationDetail?.IntermediateStation?.Station?.code;
    const ctStationCode = isEmpty(cellTowerData?.currentStationDetail?.IntermediateStation)
      ? cellTowerData?.currentStationDetail?.StoppingStation?.Station?.code
      : cellTowerData?.currentStationDetail?.IntermediateStation?.Station?.code;
    if (gpsStationCode === ctStationCode) {
      dispatch(dispatchOfflineDataToComponent(cellTowerData, Source.GPS_CELL_TOWER));
    } else {
      dispatch(dispatchOfflineDataToComponent(gpsData, Source.GPS_CELL_TOWER));
    }
  } else {
    dispatch(
      retryOrThrowError(requestDetails, refresh, autoRefresh, gotOnlineResponse),
    );
  }
};

//This function wait for 3 minutes (3 times retry) before throwing inside switch error.
const retryOrThrowError = (requestDetails, refresh, autoRefresh, gotOnlineResponse) => async (
  dispatch,
) => {
  const wasInInsideMode = await getDataFromStorage(asyncStorageKeys.INSIDE_MODE_SUCCESS, false);
  if (wasInInsideMode) {
    retryTimeOut = setTimeout(() => {
      setDataInStorage(asyncStorageKeys.INSIDE_MODE_SUCCESS, false);
    }, 240000);
  } else {
    clearTimeout(retryTimeOut);
    dispatch(
      errorHandlerForOfflineTracking(requestDetails, refresh, autoRefresh, gotOnlineResponse),
    );
  }
};

const trackOmnitureLTSVisitEventAsync = async (trackingParams) => {
  const wasInInsideMode = await getDataFromStorage(asyncStorageKeys.INSIDE_MODE_SUCCESS, false);
  if (!wasInInsideMode) {
    trackOmnitureLTSVisitEvent(trackingParams);
  }
};

const dispatchOfflineDataToComponent = (ltsData, source) => (dispatch) => {
  const enableLtsImprovementChanges = getEnableLtsImprovementChanges();
  ltsData.dataSource = source;
  clearTimeout(retryTimeOut);
  if (enableLtsImprovementChanges){
    dispatch({
      type: ACTIONS_RAIL_LIVE_TRAIN_STATUS_OFFLINE_TRACKING,
      data: ltsData,
    });
  }
  logData.stationData = extractStationDataForLogging(ltsData);
  if (logData.stationData?.stationCode){
    logData.stationData.source = source;
  }
  logData.trainDetail = {
    trainNumber: ltsData?.trainDetails?.Number,
    trainStartDate: ltsData?.trainDetails?.StartDate?.split(' ')?.[0],
  };
  sendLogs(logData);
  prevStationsSectionListOffline = ltsData?.stationsSectionList;
  currentStationDetailOffline = ltsData?.currentStationDetail;
  prevTrainDetailsOffline = ltsData?.trainDetails;
  delayOffline = ltsData?.delay;
  if (!enableLtsImprovementChanges){
    dispatch({
      type: ACTIONS_RAIL_LIVE_TRAIN_STATUS_OFFLINE_TRACKING,
      data: ltsData,
    });
  }
  setDataInStorage(asyncStorageKeys.INSIDE_MODE_SUCCESS, true);
};

const intializeOfflineParameters = (
  trainDetails,
  requestDetails,
  cachedStationsSectionList,
  cachedCurrentStationDetail,
  lastAPIFetched,
) => {
  const dataStartDate =
    trainDetails?.StartDate?.split?.(' ')?.[0] ||
    getTrainStatusDates().find((d) => d.dayDifference === 0).dateNumeric;
  let requestStartDate = requestDetails?.dateDetail?.dateNumeric;
  if (requestStartDate === undefined) {
    requestStartDate = dataStartDate;
  }
  if (isEmpty(prevStationsSectionListOffline)) {
    if (requestStartDate !== dataStartDate) {
      const scheduleData = getSchedule(requestDetails, trainDetails, cachedStationsSectionList);
      prevStationsSectionListOffline = [...scheduleData.stationsSectionList];
      prevTrainDetailsOffline = { ...scheduleData.trainDetails };
      currentStationDetailOffline.StoppingStation =
        scheduleData.stationsSectionList?.[0]?.data?.[0];
      currentStationDetailOffline.StoppingStation.SectionIndex = 1;
      currentStationDetailOffline.StoppingStation.StopNumber = 1;
    } else {
      prevStationsSectionListOffline = cachedStationsSectionList;
      prevTrainDetailsOffline = trainDetails;
    }
  }
  if (isEmpty(currentStationDetailOffline) && timeSince(new Date(lastAPIFetched)).value < 14400)
    {currentStationDetailOffline = cachedCurrentStationDetail;}
};

export const getCellId = async () => {
  try {
    const cellInfo = await RailsModule.readCellTowerData();
    return cellInfo;
  } catch (error) {
    console.log('Error in getting Cell ID: ', error);
    return {};
  }
};

export const getIsInPIPMode = async () => {
  try {
    const cellInfo = await RailsModule.isInPIPMode();
    return cellInfo;
  } catch (error) {
    console.log('Error in isInPIPMode : ', error);
    return {};
  }
};

export const getCellIdStatus = async (cellId, requestDetails) => {
  if (isEmpty(cellTowerMap)) {
    const cellTowerMapCT = await cellTowerMappingAPIRequest(requestDetails.trainDetail.trainNumber);
    cellTowerMap = cellTowerMapCT || {};
  }
  let response = {
    valid: false,
    stationTower: false,
  };
  if (isEmpty(cellTowerMap)) {return response;}
  const { MCC, MNC, LAC, CID } = cellId;
  const cellIdStatus =
    cellTowerMap?.[MCC.toString()]?.[MNC.toString()]?.[LAC.toString()]?.[CID.toString()];
  if (!isEmpty(cellIdStatus)) {
    const stationCode = cellIdStatus.split('_')[0];
    response = {
      valid: true,
      stationTower: cellIdStatus.split('_')[1] === '1',
      stationCode,
    };
  }
  return response;
};

const CTTracking = async (
  requestDetails = {},
  data = {},
  stationsSectionListCT = [],
  lastCurrentStationDetail = {},
  trainDetails = {},
) => {
  let cellID = {};
  try {
    const { disclaimer = '', lastAPIFetched = new Date() } = data || {};
    if (isEmpty(stationsSectionListCT)) {return { isUserOnTrain: false };}

    cellID = await getCellId();
    if (isEmpty(cellID)) {
      return { isUserOnTrain: false, errorTracking: LTS_OFFLINE_SWITCH_FAIL_CTCNF };
    }
    const cellIdStatus = await getCellIdStatus(cellID, requestDetails);
    logData.locationData.CT = {
      id: `${cellID?.MCC}%${cellID?.MNC}%${cellID?.LAC}%${cellID?.CID}`,
      presentInData: cellIdStatus?.valid ?? false,
    };
    errorLogData.cellTowerId = `${cellID?.MCC}%${cellID?.MNC}%${cellID?.LAC}%${cellID?.CID}`;
    logData.locationData.timeStamp = getTimeStringFromDate(new Date());
    if (cellIdStatus?.valid) {
      const { isUserOnTrain, currentStationDetail, errorTracking } = isUserOnTrainByCT(
        cellIdStatus?.stationCode,
        lastCurrentStationDetail,
        stationsSectionListCT,
        delayOffline,
        trainDetails?.Number,
      );
      if (isUserOnTrain) {
        const {
          isUserOnTrain: updatedIsUserOnTrain,
          stationsSectionList: updatedStationsSectionList,
          currentStationDetail: updatedCurrentStationDetail,
          trainDetails: updatedTrainDetails,
          delay,
        } = updateDetails(
          currentStationDetail,
          cellIdStatus?.stationTower,
          stationsSectionListCT,
          trainDetails,
        );

        if (!updatedIsUserOnTrain) {return { isUserOnTrain: false };}
        const [
          firstStationDetail,
          nextStationDetail,
          lastStationDetail,
        ] = getFirstNextLastStationDetails(updatedStationsSectionList, updatedCurrentStationDetail);

        return {
          isUserOnTrain: updatedIsUserOnTrain,
          firstStationDetail,
          lastStationDetail,
          nextStationDetail,
          trainDetails: updatedTrainDetails,
          disclaimer,
          stationsSectionList: updatedStationsSectionList,
          showLoader: false,
          ltsViewState: viewStates.SHOW_DETAIL,
          otherOptions: { offlineTracking: true },
          lastUpdated: new Date(),
          lastAPIFetched,
          currentStationDetail: updatedCurrentStationDetail,
          previousStationDetail: getPreviousStationDetail(
            updatedCurrentStationDetail,
            updatedStationsSectionList,
          ),
          delay: delay,
        };
      } else {return { isUserOnTrain: false, errorTracking };}
    } else {return { isUserOnTrain: false, errorTracking: LTS_OFFLINE_SWITCH_FAIL_CTNV };}
  } catch (error) {
    console.log('CTTacking Failing: ', error);
    return { isUserOnTrain: false, errorTracking: LTS_OFFLINE_SWITCH_FAIL_CTUNE};
  }
};

const onlineTracking = (requestDetails, isInsideTrain, refresh, autoRefresh) => async (
  dispatch,
  getState,
) => {
  try {
    // This variable is used to retrigger UI render from api response even if it is autorefresh/refresh call
    // This will be used to render api response after train schedule is served.
    let triggerForceRender = false;
    // api call for stopping stations

    const stoppingStationsAPIResponse = await stoppingStationsAPIRequest(
      requestDetails,
      tlsV3Enabled,
    );
    if (
      isEmpty(stoppingStationsAPIResponse) ||
      stoppingStationsAPIResponse === {} ||
      stoppingStationsAPIResponse?.Error
    ) {
      trackOmnitureLTSLoadEvent(
        PAGE_RIS_LTS_ERROR,
        stoppingStationsAPIResponse?.Error?.message || labels.DEFAULT_ERROR_MSG,
      );
      const otherOptions = autoRefresh
        ? { ...getState().liveTrainStatusReducer?.otherOptions }
        : {};
      // otherOptions.onlineError = true;
      dispatch({
        type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
        data: {
          ltsErrorMsg: stoppingStationsAPIResponse?.Error?.message || labels.DEFAULT_ERROR_MSG,
          otherOptions,
        },
      });
      if (isInsideTrain && !isFutureDate(requestDetails?.dateDetail?.dateNumeric))
        {dispatch(offlineTracking(requestDetails, refresh, autoRefresh, false));}
      else {dispatch(lastWay(requestDetails, refresh, autoRefresh, false));}
    } else {
      triggerForceRender =
        getState()?.liveTrainStatusReducer?.otherOptions?.showTrainSchedule ||
        isEmpty(getState()?.liveTrainStatusReducer?.stationsSectionList);
      const lastAPIFetched = new Date();
      let trainDetails = {},
        disclaimer = '',
        lastUpdated = '',
        stationsSectionList = [],
        currentStationDetail = {};

      trainDetails = stoppingStationsAPIResponse?.TrainDetails;
      disclaimer = stoppingStationsAPIResponse?.Disclaimer;
      lastUpdated = getDateFromTimeStamp(stoppingStationsAPIResponse?.LastUpdated);

      let currentHaltingStationCode =
        stoppingStationsAPIResponse?.MetaDetails?.CurrentStation?.Station?.code;
      if (isEmpty(currentHaltingStationCode)) {
        currentHaltingStationCode = trainDetails?.Terminated
          ? stoppingStationsAPIResponse?.Stations?.[stoppingStationsAPIResponse.Stations.length - 1]
              ?.Station?.code
          : stoppingStationsAPIResponse?.Stations?.[0]?.Station?.code;
      }

      let currStation = {};
      [stationsSectionList, currStation] = formatLTS(
        stoppingStationsAPIResponse.Stations,
        currentHaltingStationCode,
      ) || [[], {}];
      currentStationDetail = { ...currentStationDetail, ...currStation };
      const [
        firstStationDetail,
        nextStationDetail,
        lastStationDetail,
      ] = getFirstNextLastStationDetails(stationsSectionList, currentStationDetail);
      trainDetails.Terminated =
        currentStationDetail?.StoppingStation?.Station?.code === lastStationDetail?.Station?.code;
      let trainState;
      const TS = {
        NOTSTARTED: 'NotStarted',
        RUNNING: 'Running',
        TERMINATED: 'Terminated',
      };
      if (!trainDetails.Departed) {
        trainState = TS.NOTSTARTED;
      } else if (trainDetails.Departed && !trainDetails.Terminated) {
        trainState = TS.RUNNING;
      } else if (trainDetails.Terminated) {
        trainState = TS.TERMINATED;
      }
      const omnitureMsg = `${trainDetails.StartDay}|${trainState}`;
      if (!(autoRefresh || refresh)) {
        trackOmnitureLTSLoadEvent(PAGE_RIS_LTS_INFO, omnitureMsg);
      }
      trainDetails.DistanceCovered = getDistanceCovered(currentStationDetail);

      let cancellationAndDiversionInfo =
        stoppingStationsAPIResponse.MetaDetails.CancellationAndDiversionInfo;

      if (cancellationAndDiversionInfo.Tokens?.length > 0) {
        cancellationAndDiversionInfo.Tokens.forEach((x) => {
          cancellationAndDiversionInfo.Message = cancellationAndDiversionInfo.Message.replace(
            '%token',
            `<s>${x}</s>`,
          );
        });
      }

      cancellationAndDiversionInfo.Message = `<p><q>${cancellationAndDiversionInfo.Message}</q></p>`;
      if ((!autoRefresh || triggerForceRender) && !isInsideTrain) {
        dispatch({
          type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
          data: {
            firstStationDetail,
            lastStationDetail,
            nextStationDetail,
            currentStationDetail,
            cancellationAndDiversionInfo,
            previousStationDetail: getPreviousStationDetail(
              currentStationDetail,
              stationsSectionList,
            ),
            trainDetails,
            disclaimer,
            lastUpdated,
            stationsSectionList,
            showLoader: false,
            ltsViewState: viewStates.SHOW_DETAIL,
            otherOptions: { onlineTracking: true },
            lastAPIFetched,
          },
        });
      }
      // api call for intermediate stations
      const intermediateStationsAPIResponse = await intermediateStationsAPIRequest(
        requestDetails,
        trainDetails.StartDate.split(' ')[0],
      );
      if (
        isEmpty(intermediateStationsAPIResponse) ||
        intermediateStationsAPIResponse === {} ||
        intermediateStationsAPIResponse?.Error
      ) {
        if (autoRefresh && !isInsideTrain) {
          const otherOptions = autoRefresh
            ? { ...getState().liveTrainStatusReducer?.otherOptions }
            : {};
          otherOptions.onlineTracking = true;
          dispatch({
            type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
            data: {
              firstStationDetail,
              lastStationDetail,
              nextStationDetail,
              currentStationDetail,
              previousStationDetail: getPreviousStationDetail(
                currentStationDetail,
                stationsSectionList,
              ),
              trainDetails,
              disclaimer,
              lastUpdated,
              stationsSectionList,
              showLoader: false,
              ltsViewState: viewStates.SHOW_DETAIL,
              otherOptions: otherOptions,
              ltsErrorMsg:
                intermediateStationsAPIResponse?.Error?.message || labels.DEFAULT_ERROR_MSG,
              lastAPIFetched,
            },
          });
        }
        if (isInsideTrain && !isFutureDate(requestDetails?.dateDetail?.dateNumeric))
          {dispatch(offlineTracking(requestDetails, refresh, autoRefresh, false));}
      } else {
        trainDetails.DaysOfRun = intermediateStationsAPIResponse?.DaysOfRun;
        trainDetails.TrainAvgSpeed = intermediateStationsAPIResponse?.TrainAvgSpeed;
        stationsSectionList = formatLTSSectionWithIntermediateStation(
          stationsSectionList,
          intermediateStationsAPIResponse?.Stations,
        );
        const currentIntermediateStationCode =
          stoppingStationsAPIResponse?.MetaDetails?.CurrentStation?.CurrentIntermediateStation
            ?.code;
        if (!isEmpty(currentIntermediateStationCode)) {
          currentStationDetail.IntermediateStation = getIntermediateStation(
            stationsSectionList,
            currentHaltingStationCode,
            currentIntermediateStationCode,
          );
          if (!isEmpty(currentStationDetail.IntermediateStation)) {
            currentStationDetail.IntermediateStation.ArrivalDetails.arrived = true;
            currentStationDetail.IntermediateStation.DepartureDetails.departed = true;
          }
        }
        if ((!autoRefresh || triggerForceRender) && !isInsideTrain) {
          dispatch({
            type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT_INTERMEDIATE,
            data: {
              stationsSectionList,
              trainDetails,
              previousStationDetail: getPreviousStationDetail(
                currentStationDetail,
                stationsSectionList,
              ),
            },
          });
        }
        if (isEmpty(prevStationsSectionListOffline)) {
          prevStationsSectionListOffline = stationsSectionList;
          prevTrainDetailsOffline = trainDetails;
        }

        if (
          isEmpty(currentStationDetailOffline) ||
          currentStationDetailOffline?.StoppingStation?.StopNumber <=
            currentStationDetail?.StoppingStation?.StopNumber
        )
          {currentStationDetailOffline = currentStationDetail;}
        for (let i = 0; i < stationsSectionList.length; i += 1) {
          for (let j = 0; j < stationsSectionList[i].data.length; j += 1) {
            const station = stationsSectionList[i].data[j];
            if (station.StopNumber <= currentStationDetail?.StoppingStation?.StopNumber) {
              prevStationsSectionListOffline[i].data[j].ArrivalDetails = station.ArrivalDetails;
              prevStationsSectionListOffline[i].data[j].DepartureDetails = station.DepartureDetails;
            }
            if (station.StopNumber > currentStationDetailOffline?.StoppingStation?.StopNumber) {
              prevStationsSectionListOffline[i].data[j].ArrivalDetails = station.ArrivalDetails;
              prevStationsSectionListOffline[i].data[j].DepartureDetails = station.DepartureDetails;
            }
          }
        }

        const DB_KEY = asyncStorageKeys.TRAIN_INFO + trainDetails?.Number;
        let value = {};
        value.currentStationDetail = currentStationDetail;
        value.trainDetails = trainDetails;
        value.disclaimer = disclaimer;
        value.lastUpdated = lastUpdated;
        value.stationsSectionList = stationsSectionList;
        value.lastAPIFetched = lastAPIFetched;
        value.otherOptions = { onlineTracking: true };

        await setDataInStorage(DB_KEY, value);
        if (autoRefresh && !isInsideTrain) {
          const otherOptions = autoRefresh
            ? { ...getState().liveTrainStatusReducer?.otherOptions }
            : {};
          otherOptions.onlineTracking = true;
          dispatch({
            type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
            data: {
              firstStationDetail,
              lastStationDetail,
              nextStationDetail,
              currentStationDetail,
              previousStationDetail: getPreviousStationDetail(
                currentStationDetail,
                stationsSectionList,
              ),
              trainDetails,
              disclaimer,
              lastUpdated,
              stationsSectionList,
              showLoader: false,
              ltsViewState: viewStates.SHOW_DETAIL,
              otherOptions: otherOptions,
              lastAPIFetched,
            },
          });
        }
        if (isInsideTrain && !isFutureDate(requestDetails?.dateDetail?.dateNumeric))
          {dispatch(offlineTracking(requestDetails, refresh, autoRefresh, true));}
      }
        if (!autoRefresh && !refresh && !isInsideTrain) {
          dispatch(
            getLtsCrossSellData(
              lastStationDetail,
              firstStationDetail,
              requestDetails?.dateDetail?.dateNumeric,
            ),
          );
        }
    }
  } catch (error) {
      console.error('OnlineTracking Failng: ', error);
    if (isInsideTrain && !isFutureDate(requestDetails?.dateDetail?.dateNumeric))
      {dispatch(offlineTracking(requestDetails, refresh, autoRefresh, false));}
    else {dispatch(lastWay(requestDetails, refresh, autoRefresh, false));}
  }
};

const GPSTracking = async (
  data = {},
  stationsSectionListGPS = [],
  lastCurrentStationDetail = {},
  trainDetails = {},
  nativeCTGPSData,
) => {
  try {
    const { disclaimer = '', lastAPIFetched = new Date() } = data || {};
    if (isEmpty(stationsSectionListGPS)) {return { isUserOnTrain: false };}
    const enableLtsImprovementChanges = getEnableLtsImprovementChanges();
    const currentLocation = await getCurrentLatLng(nativeCTGPSData, enableLtsImprovementChanges); // lat lng
    if (isEmpty(currentLocation)) {
      return { isUserOnTrain: false };
    }
    logData.locationData.GPS = currentLocation;
    logData.locationData.timeStamp = getTimeStringFromDate(new Date());
    const {
      isUserOnTrain,
      currentStationDetail,
      userDistanceFromExpectedStation,
      errorTracking,
      isUserOnTrainByGPSerror,
    } = isUserOnTrainByGPS(
      currentLocation,
      lastCurrentStationDetail,
      stationsSectionListGPS,
      delayOffline,
      trainDetails?.TrainAvgSpeed,
    );
    errorLogData.isUserOnTrainByGPS = isUserOnTrainByGPSerror;
    errorLogData.userCurrentLocation = currentLocation;
    errorLogData.failedReason = errorTracking;
    if (isUserOnTrain) {
      const {
        isUserOnTrain: updatedIsUserOnTrain,
        stationsSectionList: updatedStationsSectionList,
        currentStationDetail: updatedCurrentStationDetail,
        trainDetails: updatedTrainDetails,
        delay,
      } = updateDetails(
        currentStationDetail,
        userDistanceFromExpectedStation <= 1000,
        stationsSectionListGPS,
        trainDetails,
        userDistanceFromExpectedStation,
        currentLocation,
      );

      if (!updatedIsUserOnTrain) {return { isUserOnTrain: false };}
      const [
        firstStationDetail,
        nextStationDetail,
        lastStationDetail,
      ] = getFirstNextLastStationDetails(updatedStationsSectionList, updatedCurrentStationDetail);

      return {
        isUserOnTrain: updatedIsUserOnTrain,
        firstStationDetail,
        lastStationDetail,
        nextStationDetail,
        trainDetails: updatedTrainDetails,
        disclaimer,
        stationsSectionList: updatedStationsSectionList,
        showLoader: false,
        ltsViewState: viewStates.SHOW_DETAIL,
        otherOptions: { offlineTracking: true },
        lastUpdated: new Date(),
        lastAPIFetched,
        currentStationDetail: updatedCurrentStationDetail,
        previousStationDetail: getPreviousStationDetail(
          updatedCurrentStationDetail,
          updatedStationsSectionList,
        ),
        delay: delay,
        trainsLiveGPSSpeed: currentLocation?.speed,
      };
    }
    return { isUserOnTrain: false, errorTracking };
  } catch (error) {
    console.error('GPSTracking Failing: ', error);
    const errorMessage = `error:${error} nativeCTGPSData:${JSON.stringify(
      nativeCTGPSData,
    )} trainNumber:${trainDetails?.Number}`;
    logErrorToFirebase(fireBaseErrorType.GPS_ERROR, 204, errorMessage);
    return { isUserOnTrain: false, errorTracking: LTS_OFFLINE_SWITCH_FAIL_GPSUNE };
  }
};

const errorHandlerForOfflineTracking = (
  requestDetails,
  refresh,
  autoRefresh,
  gotOnlineResponse,
) => async (dispatch) => {
  logData.trainDetail = {
    trainNumber: requestDetails?.trainDetail?.trainNumber,
    trainStartDate: requestDetails?.dateDetail?.dateNumeric,
  };
  sendLogs(logData);
  logLTSErrors(errorLogData);
  dispatch({
    type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
    data: { otherOptions: { offlineError: true, locationPermissionGranted } },
  });
  dispatch(lastWay(requestDetails, refresh, autoRefresh, gotOnlineResponse));
    const offlineErrorData = locationPermissionGranted
      ? labels.LTS_OFFLINE_ERROR
      : labels.LTS_OFFLINE_GPS_ERROR;
    showLongToast(offlineErrorData);
};

const lastWay = (requestDetails, refresh, autoRefresh, gotOnlineResponse) => async (
  dispatch,
  getState,
) => {
  try {
    const DB_KEY = asyncStorageKeys.TRAIN_INFO + requestDetails.trainDetail.trainNumber;
    const data = await getDataFromStorage(DB_KEY);
    if (getState().liveTrainStatusReducer?.ltsErrorMsg === 'Train is not running on given date.') {
      let viewState = autoRefresh || refresh ? viewStates.SHOW_DETAIL : viewStates.ERROR;
      if (
        !(
          getState().liveTrainStatusReducer?.trainDetails?.Number ===
          requestDetails.trainDetail.trainNumber
        )
      ) {
        viewState = viewStates.ERROR;
      }
      const otherOptionsVar = getState().liveTrainStatusReducer?.otherOptions;
      dispatch({
        type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR,
        data: {
          showLoader: false,
          ltsViewState: viewState,
          ltsErrorMsg: getState().liveTrainStatusReducer?.ltsErrorMsg || labels.DEFAULT_ERROR_MSG,
          otherOptions: otherOptionsVar?.offlineTracking
            ? otherOptionsVar
            : { ...otherOptionsVar, onlineError: true },
        },
      });
      return;
    } else if (data === null) {dispatch(showTrainSchedule(requestDetails, refresh, autoRefresh));}
    else {
      const {
        disclaimer,
        stationsSectionList,
        currentStationDetail,
        trainDetails,
        lastUpdated,
        lastAPIFetched,
        otherOptions,
      } = data;
      const [
        firstStationDetail,
        nextStationDetail,
        lastStationDetail,
      ] = getFirstNextLastStationDetails(stationsSectionList, currentStationDetail);
      const timeDiff = timeSince(new Date(lastAPIFetched)).value / 3600;
      const dataStartDate = trainDetails?.StartDate.split(' ')[0];
      let requestStartDate = requestDetails?.dateDetail?.dateNumeric;
      if (requestStartDate === undefined) {
        requestStartDate = dataStartDate;
      }
      if ((timeDiff <= 4 && requestStartDate === dataStartDate) || gotOnlineResponse) {
        trainDetails.DistanceCovered = getDistanceCovered(currentStationDetail);
        dispatch({
          type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_INIT,
          data: {
            firstStationDetail,
            lastStationDetail,
            nextStationDetail,
            currentStationDetail,
            previousStationDetail: getPreviousStationDetail(
              currentStationDetail,
              stationsSectionList,
            ),
            trainDetails,
            disclaimer,
            lastUpdated,
            stationsSectionList,
            showLoader: false,
            ltsViewState: viewStates.SHOW_DETAIL,
            otherOptions: { ...otherOptions, showTrainSchedule: false },
            lastAPIFetched,
          },
        });
        if (!gotOnlineResponse) {ToastMessage(toastLabels.FAIL_RETRY);}
      } else {
        dispatch(showTrainSchedule(requestDetails, refresh, autoRefresh));
      }
    }
  } catch (error) {
    console.log('Last Way failiing: ', error);
    dispatch({
      type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR,
      data: {
        showLoader: false,
        ltsViewState: viewStates.ERROR,
        ltsErrorMsg: getState().liveTrainStatusReducer?.ltsErrorMsg || labels.DEFAULT_ERROR_MSG,
      },
    });
  }
};

const getDataForSchedule = async (requestDetails) => {
  try {
    const DB_KEY = asyncStorageKeys.TRAIN_INFO + requestDetails.trainDetail.trainNumber;
    const cachedData = await getDataFromStorage(DB_KEY);
    if (!isEmpty(cachedData)) {return cachedData;}
    const scheduleApiData = await intermediateStationsAPIRequest(
      requestDetails,
      getDateDetails(requestDetails?.dateDetail?.date ?? Date.now()).dateNumeric,
    );
    if (isEmpty(scheduleApiData) || scheduleApiData === {} || scheduleApiData?.Error) {return null;}
    for (let i = 0; i < scheduleApiData.Stations.length; i += 1) {
      scheduleApiData.Stations[i].MetaDetails = { StoppingStation: true };
      scheduleApiData.Stations[i].DayDetails = {
        scheduledDayCount: scheduleApiData.Stations[i].Day,
      };
      const { arrivalTimeStamp, departureTimeStamp } = getTimeStringFromStationDetails(
        scheduleApiData.Stations[i],
      );
      const arrivalDate = getDateFromTimeStamp(arrivalTimeStamp);
      const departureDate = getDateFromTimeStamp(departureTimeStamp);
      scheduleApiData.Stations[i].HaltMinutes =
        (departureDate.getTime() - arrivalDate.getTime()) / 60000;
    }
    const [scheduleStationsList] = formatLTS(scheduleApiData.Stations) || [[]];
    const updateScheduleData = getSchedule(
      requestDetails,
      { ...scheduleApiData?.TrainDetails, DaysOfRun: scheduleApiData?.DaysOfRun },
      scheduleStationsList,
    );
    const preScheduleData = {
      disclaimer:
        scheduleApiData?.Disclaimer ??
        'These features have no affiliation with IRCTC. You are advised to verify the details once before making any decision, IRCTC or MakeMyTrip will not be responsible for any liability occurring due to this information.',
      stationsSectionList: updateScheduleData.stationsSectionList,
      trainDetails: updateScheduleData.trainDetails,
      otherOptions: { onlineTracking: true },
    };
    return preScheduleData;
  } catch (err) {
    console.log('Error in getDataForSchedule: ', err);
    return null;
  }
};

const checkScheduleRunning = (scheduleTrainDetails) => {
  try {
    const startDate = new Date(getDateFromTimeStamp(scheduleTrainDetails?.StartDate));
    const startDay = fecha.format(startDate, 'ddd');
    const running = scheduleTrainDetails?.DaysOfRun?.[startDay] ?? true;
    return running;
  } catch (err) {
    console.log('Error in checkScheduleRunning: ', err);
    return true;
  }
};

export const removeRailsCrossSellData = () => async (dispatch) => {
  dispatch({
    type: ACTIONS_RAILS_PNR_CROSS_SELL_DATA,
    data: null,
  });
};

const showTrainSchedule = (requestDetails, refresh, autoRefresh) => async (dispatch, getState) => {
  try {
    const data = await getDataForSchedule(requestDetails);
    if (data === null) {
      let viewState = autoRefresh || refresh ? viewStates.SHOW_DETAIL : viewStates.ERROR;
      if (
        !(
          getState().liveTrainStatusReducer?.trainDetails?.Number ===
          requestDetails.trainDetail.trainNumber
        )
      ) {
        viewState = viewStates.ERROR;
      }
      const otherOptionsVar = getState().liveTrainStatusReducer?.otherOptions;
      dispatch({
        type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR,
        data: {
          showLoader: false,
          ltsViewState: viewState,
          ltsErrorMsg: getState().liveTrainStatusReducer?.ltsErrorMsg || labels.DEFAULT_ERROR_MSG,
          otherOptions: otherOptionsVar?.offlineTracking
            ? otherOptionsVar
            : { ...otherOptionsVar, onlineError: true },
        },
      });
      return;
    }
    const { disclaimer, stationsSectionList, trainDetails, otherOptions } = data;
    const lastUpdated = '';
    let currentStationDetail = {};
    const {
      trainDetails: scheduleTrainDetails,
      stationsSectionList: scheduleStationsSectionList,
    } = getSchedule(requestDetails, trainDetails, stationsSectionList);
    const running = checkScheduleRunning(scheduleTrainDetails);
    if (running) {
      const [
        firstStationDetail,
        nextStationDetail,
        lastStationDetail,
      ] = getFirstNextLastStationDetails(scheduleStationsSectionList, undefined);
      currentStationDetail.StoppingStation = firstStationDetail;
      currentStationDetail.StoppingStation.SectionIndex = 1;
      currentStationDetail.StoppingStation.StopNumber = 1;
      const otherOptionsVar = { showTrainSchedule: true };
      trainDetails.DistanceCovered = getDistanceCovered(currentStationDetail);
      dispatch({
        type: ACTIONS_RAIL_LIVE_TRAIN_STATUS_TRAIN_SCHEDULE,
        data: {
          trainDetails: scheduleTrainDetails,
          lastUpdated,
          firstStationDetail,
          lastStationDetail,
          disclaimer,
          stationsSectionList: scheduleStationsSectionList,
          nextStationDetail,
          currentStationDetail,
          otherOptions: { ...otherOptions, ...otherOptionsVar },
          showLoader: false,
          ltsViewState: viewStates.SHOW_DETAIL,
        },
      });
      ToastMessage(toastLabels.FAIL_SCHEDULE);
    } else {
      const otherOptionsVar = getState().liveTrainStatusReducer?.otherOptions;
      dispatch({
        type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR,
        data: {
          showLoader: false,
          ltsViewState: viewStates.ERROR,
          ltsErrorMsg: 'Train is not running on given date.',
          otherOptions: otherOptionsVar?.offlineTracking
            ? otherOptionsVar
            : { ...otherOptionsVar, onlineError: true },
        },
      });
    }
  } catch (error) {
    console.log('Schedule Failing: ', error);
    dispatch({
      type: ACTIONS_RAILS_LIVE_TRAIN_STATUS_SHOW_ERROR,
      data: {
        showLoader: false,
        ltsViewState: viewStates.ERROR,
        ltsErrorMsg: getState().liveTrainStatusReducer?.ltsErrorMsg || labels.DEFAULT_ERROR_MSG,
        otherOptions: {},
      },
    });
  }
};
