import React, { useRef, useState } from 'react';

//Ref https://stackoverflow.com/questions/55265255/react-usestate-hook-event-handler-using-initial-state

export const useReferredState = <T>(
  initialValue: T,
): [T, React.MutableRefObject<T>, React.Dispatch<T>] => {
  const [state, setState] = useState<T>(initialValue);
  const reference = useRef<T>(state);
  const setReferredState = (value: unknown) => {
    reference.current = value;
    setState(value);
  };
  return [state, reference, setReferredState];
};
