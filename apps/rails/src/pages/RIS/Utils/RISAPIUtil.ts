import fetch2 from 'apps/rails/src/fetch2';

export const RIS_BASE_URL_PNR = 'https://rails-ris.makemytrip.com/api/';

const PNR = {
  CROSS_SELL_URL: `${RIS_BASE_URL_PNR}ris/pnr/cross-sells?pnrDetails=`,
};

const commonHeader = {
  Accept: 'application/json',
  'Content-Type': 'application/json',
};

export const fetchCrossSellData = async (
  pnrDetailsEncrypted: string,
  connectorPnrResponseEncoded: string,
) => {
  try {
    const additionalHeaders = {
      'connector-pnr-response': connectorPnrResponseEncoded,
    };
    const response = await fetch2(`${PNR.CROSS_SELL_URL}${pnrDetailsEncrypted}`, {
      headers: additionalHeaders,
    });
    return await response.json();
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
};

export const fetchLtsCrossSellData = async (
  startingStation: string,
  endingStation: string,
) => {
  try {
    const apiUrl = `${RIS_BASE_URL_PNR}ris/train/lts/cross-sells`;
    const requestBody = {
      returnTripInfo: {
        startingStation,
        endingStation,
      },
    };
    const response = await fetch2(apiUrl, {
      method: 'POST',
      headers: commonHeader,
      body: JSON.stringify(requestBody),
    });
    return await response.json();
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
};
