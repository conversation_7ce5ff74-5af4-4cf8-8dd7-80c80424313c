import { CrossSellTypes, ComponentTypes } from '../PnrStatus/Components/PNRCrossSells/types';
const CROSS_SELL_LOAD = {
  BOTTOM_SHEET: {
    RETURN_TICKETS: 'rails:pnr:return:train:trip:widget:bs:shown',
    CONFIRMED_OPTIONS: 'rails:pnr:route:extension:widget:bs:shown',
    BUS: 'rails:pnr:bus:widget:bs:shown ',
  },
  STICKY: {
    RETURN_TICKETS: 'rails:pnr:return:train:trip:widget:sticky:shown',
    CONFIRMED_OPTIONS: 'rails:pnr:route:extension:widget:sticky:shown',
    BUS: 'rails:pnr:bus:widget:sticky:shown',
  },
  INLINE: {
    RETURN_TICKETS: 'rails:pnr:return:train:trip:widget:shown',
    CONFIRMED_OPTIONS: 'rails:pnr:route:extension:widget:shown',
    BUS: 'rails:pnr:bus:widget:sticky:shown',
  },
};


export const CROSS_SELL_CLICK = {
  RETURN_TICKETS: {
    BS_CARD: 'mob_rail_pnr_train_return_trip_bs_date_card_clicked',
    BS_CALENDAR: 'mob_rail_pnr_train_return_trip_bs_view_calendar_clicked',
    BS_VIEW_ALL: 'mob_rail_pnr_train_return_trip_bs_view_trains_clicked',
    INLINE_CARD: 'mob_rail_pnr_train_return_trip_date_card_clicked',
    INLINE_CALENDAR: 'mob_rail_pnr_train_return_trip_view_calendar_clicked',
    INLINE_VIEW_ALL: 'mob_rail_pnr_train_return_trip_view_trains_clicked',
    STICKY_VIEW_ALL: 'mob_rail_pnr_train_return_trip_sticky_view_trains_clicked',
  },
  CONFIRMED_OPTIONS: {
    BS_CARD: 'mob_rail_pnr_route_extension_bs_class_card_clicked',
    BS_VIEW_ALL: 'mob_rail_pnr_route_extension_bs_view_trains_clicked',
    INLINE_CARD: 'mob_rail_pnr_route_extension_class_card_clicked',
    INLINE_VIEW_ALL: 'mob_rail_pnr_route_extension_view_trains_clicked',
    STICKY_VIEW_ALL: 'mob_rail_pnr_route_extension_sticky_view_trains_clicked',
  },
  BUS: {
    BS_CARD: 'mob_rail_pnr_bus_bs_bus_card_clicked',
    BS_VIEW_ALL: 'mob_rail_pnr_bus_bs_view_buses_clicked',
    INLINE_CARD: 'mob_rail_pnr_bus_bus_card_clicked',
    INLINE_VIEW_ALL: 'mob_rail_pnr_bus_view_buses_clicked',
    STICKY_VIEW_ALL: 'mob_rail_pnr_bus_sticky_view_buses_clicked',
  },
};

export const getCrossSellTrackingKey = (
  crossSellType: CrossSellTypes,
  componentType: ComponentTypes,
) => {
  let trackingKey = null;
  switch (componentType) {
    case 'BOTTOM_SHEET':
      trackingKey =
        CROSS_SELL_LOAD.BOTTOM_SHEET[crossSellType as keyof typeof CROSS_SELL_LOAD.BOTTOM_SHEET];
      break;
    case 'INLINE':
      trackingKey = CROSS_SELL_LOAD.STICKY[crossSellType as keyof typeof CROSS_SELL_LOAD.STICKY];
      break;
    case 'STICKY_BOTTOM_SHEET':
      trackingKey = CROSS_SELL_LOAD.INLINE[crossSellType as keyof typeof CROSS_SELL_LOAD.INLINE];
      break;
    default:
      break;
  }
  return trackingKey;
};
