import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  Platform,
  BackHandler,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import _ from 'lodash';
import { Actions } from '../../../../navigation/railsNavigation';
import { initLiveStationApi } from '../Util/liveStation.action';
import LiveStationHeader from './LiveStationHeader';
import ViewState from '../../../../Utils/ViewState';
import LiveStationErrorView from './LiveStationErrorView';
import LiveStationCard from './LiveStationCard';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import {
  RIS_LANDING_TRAIN_LIVE_STATION_SUCCESS,
  trackOmnitureLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';

class RailLiveStationList extends React.Component {
  constructor(props) {
    super(props);
    const { code } = props;
    this.state = {
      sourceStationCode: code,
    };
  }
  UNSAFE_componentWillMount() {
    const { code, fromStationCode, destinationStationCode = '' } = this.props;
    if (!fromStationCode && !destinationStationCode) {
      this.props.fetchLiveStationList(code, '');
    } else {
      this.props.fetchLiveStationList(code, destinationStationCode);
    }
  }

  componentDidMount() {
    trackOmnitureLoadEvent(RIS_LANDING_TRAIN_LIVE_STATION_SUCCESS);
    BackHandler.addEventListener('hardwareBackPress', this.onBackPressed);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPressed);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (
      nextProps.destinationStationCode &&
      this.props.destinationStationCode !== nextProps.destinationStationCode &&
      nextProps.destinationStationCode !== ' '
    ) {
      this.props.fetchLiveStationList(
        nextProps.sourceStationCode,
        nextProps.destinationStationCode,
      );
    }
  }

  onBackPressed = () => {
    const { deeplink } = this.props;
    if (deeplink === 'true') {
      if (Platform.OS === 'ios') {
        ViewControllerModule.popViewController();
      }
      if (Platform.OS === 'android') {
        BackHandler.exitApp();
      }
    } else {
      Actions.railLiveStationLanding({
        isFromNewLanding: true,
      });
    }
    return true;
  };

  render() {
    const {
      title,
      Disclaimer,
      trainList,
      liveStationViewState,
      showLiveStationLoader,
      sourceStationName,
      destinationStationCode = '',
      sourceStationCode,
      liveStationErrorMsg,
    } = this.props;

    return (
      <SafeAreaView style={{ flex: 1 }}>
        {liveStationViewState === ViewState.LOADING && showLiveStationLoader && (
          <View style={{ justifyContent: 'center', flex: 1, alignItems: 'center' }}>
            <Spinner size={30} color="#008b8b" />
          </View>
        )}

        {liveStationViewState === ViewState.NO_INTERNET && (
          <View>
            <LiveStationHeader
              sourceStationCode={this.props.code}
              onBackPressed={this.onBackPressed}
              liveStationViewState={ViewState.NO_INTERNET}
            />
            <NoInternetView
              onRetry={() =>
                this.props.fetchLiveStationList(sourceStationCode, destinationStationCode)
              }
            />
          </View>
        )}
        {!_.isEmpty(trainList) && liveStationViewState === ViewState.SHOW_DETAIL && (
          <ScrollView style={{ backgroundColor: colors.lightGrey }}>
            <View style={{ justifyContent: 'center' }}>
              <LiveStationHeader
                sourceStationCode={sourceStationCode}
                destinationStationCode={destinationStationCode}
                viewState={liveStationViewState}
                title={title}
                stationName={sourceStationName}
                onBackPressed={this.onBackPressed}
              />
              <View
                style={{
                  height: 2,
                  backgroundColor: colors.grey10,
                  width: '100%',
                }}
              />
              <LiveStationCard trainList={trainList} sourceStationCode={sourceStationCode} />
              {Disclaimer && (
                <View>
                  <Text style={liveStationStyle.disclaimerStyle}>Disclaimer:</Text>
                  <Text style={liveStationStyle.disclaimerText}>{Disclaimer}</Text>
                </View>
              )}
            </View>
          </ScrollView>
        )}
        {liveStationViewState === ViewState.ERROR && !showLiveStationLoader && (
          <View>
            <LiveStationHeader
              sourceStationCode={this.props.code}
              onBackPressed={this.onBackPressed}
              liveStationViewState={liveStationViewState}
            />
            <LiveStationErrorView errorText={liveStationErrorMsg} />
          </View>
        )}
      </SafeAreaView>
    );
  }
}

const liveStationStyle = StyleSheet.create({
  disclaimerStyle: {
    fontSize: 22,
    fontFamily: fonts.light,
    color: colors.defaultTextColor,
    marginTop: 15,
    marginBottom: 15,
    marginLeft: 16,
  },
  disclaimerText: {
    fontFamily: fonts.bold,
    fontSize: 12,
    color: colors.black,
    marginLeft: 16,
    paddingBottom: 16,
  },
  disclaimerContainer: {
    height: 75,
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 30,
  },
});

RailLiveStationList.propTypes = {
  fetchLiveStationList: PropTypes.func.isRequired,
  onBackPressed: PropTypes.func.isRequired,
  trainList: PropTypes.array,
  code: PropTypes.string,
  fromStationCode: PropTypes.string,
  destinationStationCode: PropTypes.string,
  sourceStationCode: PropTypes.string,
  deeplink: PropTypes.string,
  title: PropTypes.string,
  showLiveStationLoader: PropTypes.bool,
  liveStationErrorMsg: PropTypes.string,
  Disclaimer: PropTypes.string,
  liveStationViewState: PropTypes.string,
  sourceStationName: PropTypes.string,
};

RailLiveStationList.defaultProps = {
  trainList: [],
};

const mapStateToProps = (state) => {
  const {
    title,
    Disclaimer,
    trainList,
    liveStationViewState,
    sourceStationCode,
    destinationStationCode,
    showLiveStationLoader,
    sourceStationName,
    liveStationErrorMsg,
  } = state.liveStationReducer;
  return {
    title,
    Disclaimer,
    trainList,
    liveStationViewState,
    sourceStationCode,
    destinationStationCode,
    showLiveStationLoader,
    sourceStationName,
    liveStationErrorMsg,
  };
};

const mapDispatchToProps = (dispatch) => ({
  //onBackPressed: (railsBusCommonLanding) => dispatch(onBackPressed(railsBusCommonLanding)),
  fetchLiveStationList: (sourceStationCode, destinationStationCode) =>
    dispatch(initLiveStationApi(sourceStationCode, destinationStationCode)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RailLiveStationList);
