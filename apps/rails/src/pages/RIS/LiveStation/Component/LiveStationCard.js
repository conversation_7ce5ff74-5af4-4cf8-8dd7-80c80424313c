import React from 'react';
import {FlatList, View, StyleSheet, Platform} from 'react-native';
import LiveStationCardView from './LiveStationCardView';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {RIS_STATION_RESULT} from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import {getAdsCard} from '@mmt/legacy-commons/adsConfig/adsCard';

const LiveStationCard = (props) => {
  // eslint-disable-next-line react/prop-types
  const {trainList} = props;
  return (
    <View style={{backgroundColor: colors.white}}>
      <FlatList
        data={trainList}
        keyExtractor={item => item.trainNumber}
        renderItem={renderItem}
        keyboardShouldPersistTaps="handled"
      />
    </View>
  );
};


const renderItem = ({item, index}) => (
  <View>
    <LiveStationCardView train={item} />
    {(index > 0 && index === 1) && <View style={styles.adStyle}>
      {getAdsCard(Platform.OS, RIS_STATION_RESULT)}
    </View>}
  </View>
);

export default LiveStationCard;


const styles = StyleSheet.create({
  adStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    backgroundColor: colors.white,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
  },
});

