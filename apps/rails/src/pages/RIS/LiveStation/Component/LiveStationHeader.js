import React from 'react';
import PropTypes from 'prop-types';
import {View, Text, Image, StyleSheet, Platform} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import {initLiveStationApi, onOriginClicked} from '../Util/liveStation.action';
import {connect} from 'react-redux';
import ViewState from '../../../../Utils/ViewState';


import refreshIconBlue from '@mmt/legacy-assets/src/refreshBlue.webp';


function LiveStationHeader(props) {
  const {
    destinationStationCode, fetchLiveStationList, title, openStationPicker, 
    onBackPressed, sourceStationCode, liveStationViewState,
  } = props;

  return (
    <View style={{backgroundColor: colors.white}} elevation={5}>
      <View style={liveStationStyle.rootContainer}>
        <TouchableRipple onPress={onBackPressed}>
          <View style={liveStationStyle.backIconContainer}>
            <Image style={liveStationStyle.backIconStyle} source={backIcon} />
          </View>
        </TouchableRipple>
        <View style={{justifyContent: 'center'}}>
          <Text style={liveStationStyle.headerTextStyle}>Live Station Updates from {sourceStationCode}</Text>
        </View>
        {liveStationViewState === ViewState.SHOW_DETAIL &&
        <View style={{flexDirection: 'row', height: '100%', marginLeft: 'auto'}}>
          <TouchableRipple onPress={() => fetchLiveStationList(sourceStationCode, destinationStationCode)} >
            <View style={{height: '100%'}}>
              <Image source={refreshIconBlue} style={liveStationStyle.refreshIconStyle} />
            </View>
          </TouchableRipple>
        </View>}
      </View>
      {liveStationViewState === ViewState.SHOW_DETAIL &&
      <View>
        <View style={{
flex: 1, marginLeft: 16, marginRight: 16, paddingHorizontal: 30, marginBottom: 5,
}}
        >
          <Text style={liveStationStyle.searchSectionHeader}>DESTINATION STATION (Optional)</Text>
        </View>
        <View style={{flex: 1.5}}>
          <TouchableRipple
            onPress={() => openStationPicker()}
          >
            <View style={liveStationStyle.searchSection} elevation={8}>
              <Text style={liveStationStyle.searchSectionMessage}>Tap to Update Station</Text>
            </View>
          </TouchableRipple>
        </View>
        <View style={{flex: 1, width: '100%', padding: 16}}>
          <Text style={{
fontFamily: fonts.black, color: colors.black04, fontSize: 12, marginLeft: 30, marginRight: 16, paddingBottom: 5,
}}
          >{title}
          </Text>
        </View>
      </View>
        }
    </View>
  );
}

LiveStationHeader.propTypes = {
  openStationPicker: PropTypes.func,
  onBackPressed: PropTypes.func,
  fetchLiveStationList: PropTypes.func.isRequired,
  destinationStationCode: PropTypes.string,
  title: PropTypes.string,
  sourceStationCode: PropTypes.string,
  liveStationViewState: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

const liveStationStyle = StyleSheet.create({
  rootContainer: {
    height: 35,
    flexDirection: 'row',
    borderBottomColor: colors.datePickerColor,
    borderBottomWidth: 0.5,
    borderTopWidth: 0,
    marginTop: 12,
    borderTopColor: colors.white,
    backgroundColor: colors.white,
  },
  backIconStyle: {
    width: 22,
    height: 16,
    marginLeft: 16,
    justifyContent: 'center',
  },
  backIconContainer: {
    width: 40,
    height: 35,
  },
  headerTextStyle: {
    marginLeft: 12,
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.black,
    paddingBottom: 15,
  },
  pnrNumberStyle: {
    marginLeft: 12,
    marginBottom: 10,
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.lightTextColor,
  },
  refreshIconStyle: {
    marginRight: 13,
    width: 25,
    height: 25,
    justifyContent: 'center',
  },
  shareIconStyle: {
    marginLeft: 13,
    marginRight: 13,
    width: 16,
    height: 18,
    justifyContent: 'center',
    paddingTop: 2,
    paddingBottom: 3,
  },
  searchSection: {
    paddingHorizontal: 15,
    height: 30,
    backgroundColor: Platform.OS === 'android' ? colors.white : colors.grey2,
    borderColor: Platform.OS === 'web' ? colors.grey10 : colors.white,
    borderWidth: 1,
    borderRadius: 5,
    marginLeft: 46,
    marginRight: 46,
    justifyContent: 'center',
  },
  searchSectionMessage: {
    color: colors.grey6,
    fontSize: 16,
    fontFamily: fonts.regular,
    justifyContent: 'flex-start',
  },
  searchSectionHeader: {
    color: colors.grey,
    fontSize: 12,
    fontFamily: fonts.regular,
    justifyContent: 'flex-start',
  },

});

const mapStateToProps = (state) => {
  const {
    destinationStationCode, title, stationName, sourceStationCode, liveStationViewState,
  } = state.liveStationReducer;
  return {
    destinationStationCode, title, stationName, sourceStationCode, liveStationViewState,
  };
};

const mapDispatchToProps = dispatch => ({
  openStationPicker: () => dispatch(onOriginClicked),
  fetchLiveStationList: (sourceStationCode, destinationStationCode) => 
    dispatch(initLiveStationApi(sourceStationCode, destinationStationCode)),
});


export default connect(mapStateToProps, mapDispatchToProps)(LiveStationHeader);
