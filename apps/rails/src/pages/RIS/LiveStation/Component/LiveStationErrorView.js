import React from 'react';
import {Image, PixelRatio, StyleSheet, Text, View} from 'react-native';
import { Actions } from '../../../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';

import errorImage from '@mmt/legacy-assets/src/ic_flushed_pnr.webp';
import PropTypes from 'prop-types';

const handleBackClick = () => {
  Actions.railLiveStationLanding({isFromNewLanding: true});
};

const LiveStationErrorView = (props) =>

  (
    <View styles={styles.container}>
      <Image source={errorImage} style={styles.noInternetImage} />
      <Text style={styles.noInternetSubtitle}>{props.errorText}</Text>
      <View style={styles.noInternetCta}>
        <TouchableRipple
          onPress={handleBackClick}
        >
          <View>
            <LinearGradient
              colors={['#53B2FE', '#065AF3']}
              start={{x: 0.0, y: 0.0}}
              end={{x: 1.0, y: 0.0}}
              style={styles.cta}
            >
              <Text style={styles.text}>CHANGE STATION</Text>
            </LinearGradient>
          </View>
        </TouchableRipple>
      </View>
    </View>
  );
const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignSelf: 'center',
  },
  noInternetImage: {
    alignSelf: 'center',
    width: 250,
    height: 220,
    marginTop: 100,
  },
  noInternetTitle: {
    marginTop: 20,
    marginHorizontal: 20,
    color: colors.darkGrey,
    fontSize: 20,
    fontFamily: fonts.bold,
    textAlign: 'center',
  },
  noInternetSubtitle: {
    marginTop: 8,
    marginHorizontal: 24,
    color: colors.lightTextColor,
    fontSize: 14,
    fontFamily: fonts.regular,
    lineHeight: 21,
    textAlign: 'center',
  },
  noInternetCta: {
    marginTop: 50,
    alignSelf: 'center',
  },
  cta: {
    width: 200,
    backgroundColor: colors.azure,
    height: PixelRatio.getPixelSizeForLayoutSize(16),
    alignItems: 'center',
    justifyContent: 'center',
    ...getPlatformElevation(4),
  },
  text: {
    color: colors.white,
    fontSize: PixelRatio.getFontScale() * 12,
    paddingHorizontal: 8,
    fontFamily: fonts.bold,
    backgroundColor: colors.transparent,
  },
});

LiveStationErrorView.propTypes = {
  errorText: PropTypes.string,
};

export default LiveStationErrorView;
