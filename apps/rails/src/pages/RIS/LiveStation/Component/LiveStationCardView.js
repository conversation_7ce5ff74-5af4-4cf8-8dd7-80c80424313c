import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { openLiveTrainStatus } from '../../RisCommonUtils';
import PropTypes from 'prop-types';

export const LiveStationCardView = (props) => {
  const {train} = props;
  return (
    <View>
      <View style={trainStyle.container}>
        <View style={{flexDirection: 'row', marginBottom: 10}}>
          <View style={trainStyle.trainNoContainer}>
            <Text style={trainStyle.trainNo}>{train.trainNumber}</Text>
          </View>
          <View style={{flex: 4}}>
            <Text style={trainStyle.trainName}>{train.trainName} </Text>
          </View>
        </View>
        <View style={{flexDirection: 'row', marginBottom: 10}}>
          <Text style={trainStyle.platformStyle}>Expected Platform</Text>
          <Text style={trainStyle.platformStyle}> # {train.platformNo} </Text>
          {train.startStation &&
          <View style={trainStyle.sourceStyle}>
            <Text style={{color: colors.green14, fontFamily: fonts.bold, fontSize: 10}}>SOURCE</Text>
          </View>
          }
        </View>
        <TouchableOpacity style={{ marginBottom: 10 }} onPress={() => { openLiveTrainStatus({trainDetails: train}); }}>
          <Text style={{
            fontSize: 12, fontFamily: fonts.bold, lineHeight: 14, color: colors.primary,
          }}
          >SEE LIVE TRAIN STATUS
          </Text>
        </TouchableOpacity>
        <Card style={{borderRadius: 6, marginLeft: 0}}>
          <View style={trainStyle.innerCard}>
            <View style={{justifyContent: 'flex-start', flex: 1}}>
              <Text style={trainStyle.textStyle}>Arrival</Text>
            </View>
            {(train.actualArrivalTime === 'SRC') &&
            <View style={{justifyContent: 'flex-start', flex: 1}}>
              <Text style={[trainStyle.timeStyleArrival, {justifyContent: 'center'}]}>--</Text>
            </View>
            }
            {(train.actualArrivalTime !== 'SRC') &&
            <View style={{
              justifyContent: 'flex-start', flex: 2, flexDirection: 'row',
            }}
            >
              <Text style={trainStyle.timeStyleArrival}>Expt. {train.actualArrivalTime}</Text>
              <Text style={trainStyle.timeStyleSch}>Schd. {train.scheduledArrivalTime}</Text>
            </View>
            }
          </View>
          <View style={trainStyle.innerCard}>
            <View style={{justifyContent: 'flex-start', flex: 1}}>
              <Text style={trainStyle.textStyle}>Departure</Text>
            </View>
            {(train.actualDepartureTime === 'SRC') &&
            <View style={{justifyContent: 'flex-start', flex: 1}}>
              <Text style={trainStyle.timeStyleArrival}>--</Text>
            </View>}
            {(train.actualDepartureTime !== 'SRC') &&
            <View style={{
              justifyContent: 'flex-start', flex: 2, flexDirection: 'row',
            }}
            >
              <Text style={trainStyle.timeStyleArrival}>Expt. {train.actualDepartureTime}</Text>
              <Text style={trainStyle.timeStyleSch}>Schd. {train.scheduledDepartureTime}</Text>
            </View>}
          </View>
          <View style={trainStyle.innerCardLast}>
            <Text style={{fontSize: 12, fontFamily: fonts.black, color: colors.black}}>{train.delayString}</Text>
          </View>
        </Card>
      </View>
      <View style={{width: '100%', height: 2, backgroundColor: colors.grey10}} />
    </View>
  );
};

const trainStyle = StyleSheet.create({
  container: {
    borderBottomColor: colors.white,
    borderBottomWidth: 15,
    padding: 20,
  },
  trainNoContainer: {
    width: 60,
    height: 20,
    alignItems: 'center',
    backgroundColor: colors.black,
    marginRight: 8,
    borderRadius: 4,
  },
  trainNo: {
    color: colors.white,
    paddingBottom: 4,
    paddingTop: 3,
    paddingLeft: 7,
    paddingRight: 7,
    fontFamily: fonts.bold,
    fontSize: 12,
  },
  trainName: {
    fontFamily: fonts.bold,
    fontSize: 14,
    color: colors.black,
  },
  stationRef: {
    backgroundColor: colors.cruise,
    borderRadius: 10,
    color: colors.green,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 2,
    paddingBottom: 2,
  },
  innerCard: {
    borderBottomColor: colors.greyBookedSeat,
    borderBottomWidth: 2,
    paddingLeft: 20,
    paddingTop: 12,
    paddingBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  innerCardLast: {
    paddingLeft: 20,
    paddingTop: 16,
    paddingBottom: 16,
  },
  textStyle: {
    width: 95,
    fontSize: 12,
    fontFamily: fonts.black,
    color: colors.black,
  },
  timeStyleArrival: {
    width: 95,
    fontSize: 10,
    fontFamily: fonts.black,
    color: colors.green,
    marginRight: 16,
  },
  timeStyleSch: {
    width: 95,
    fontSize: 10,
    fontFamily: fonts.regular,
    color: colors.black,
    marginRight: 16,
  },
  sourceStyle: {
    borderRadius: 10,
    backgroundColor: colors.cruise,
    width: 60,
    height: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  platformStyle: {
    fontFamily: fonts.regular,
    fontSize: 12,
    marginTop: 2,
  },
});

LiveStationCardView.propTypes = {
  train: PropTypes.object,
};

export default LiveStationCardView;
