import * as ACTIONS from './liveStationAction.constant';
import ViewState from '../../../../Utils/ViewState';


const initialStates = {
  liveStationViewState: null,
  destinationStationCode: null,
};

export default (state = initialStates, action) => {
  switch (action.type) {
    case ACTIONS.ACTIONS_RAILS_LIVE_STATION_SHOW_DETAILS: {
      const {
        title, Disclaimer, trainList, sourceStationCode, destinationStationCode,
      } = action.data;
      return {
        ...state,
        title,
        Disclaimer,
        trainList,
        showLiveStationLoader: false,
        sourceStationCode,
        destinationStationCode,
        liveStationViewState: ViewState.SHOW_DETAIL,
      };
    }

      case ACTIONS.ACTIONS_RAILS_LIVE_STATION_INITIAL_STATE: {
          const {code,fromStationCode} = action.data;
          return {
              ...state,
              code,
              fromStationCode,
              destinationStationCode: fromStationCode,
          };
      }

    case ACTIONS.ACTIONS_RAILS_LIVE_STATION_INFO_SHOW_LOADER: {
      const {liveStationViewState} = action.data;
      return {
        ...state,
        title: null,
        trainList: null,
        showLiveStationLoader: true,
        liveStationViewState,
      };
    }
    case ACTIONS.ACTIONS_RAILS_LIVE_STATION_SET_NO_NETWORK: {
      const {
        liveStationViewState,
      } = action.data;
      return {
        ...state,
        title: null,
        trainList: null,
        liveStationViewState,
        destinationStationCode: undefined,
        sourceStationCode: undefined,
      };
    }
    case ACTIONS.ACTIONS_RAILS_INFO_SHOW_ERROR: {
      const {liveStationViewState, liveStationErrorMsg} = action.data;
      return {
        ...state,
        showLiveStationLoader: false,
        liveStationViewState,
        liveStationErrorMsg,
        destinationStationCode: undefined,
        sourceStationCode: undefined,
        title: null,
        trainList: null,
      };
    }

    case ACTIONS.ACTION_RIS_ON_ORIGIN_SELECTED: {
      const {
        code, stationName, cityName, stateName,
      } = action.data;
      const {sourceStationCode} = state;
      return {
        sourceStationCode,
        destinationStationCode: code,
        sourceStationName: stationName,
        stationDetails: {
          code,
          stationName,
          cityName,
          stateName,
        },
        fromStationCode: code,
        liveStationViewState: 'loading',
        code,
        title: null,
        disclaimer: null,
        trainList: null,
      };
    }

    case ACTIONS.ACTION_OPEN_TRAIN_PICKER: {
      const {trainDetail} = action.data;
      return {
        ...state,
        trainDetail,
      };
    }
    default:
      return state;
  }
};
