import { Platform } from 'react-native';
import { Actions } from '../../../../navigation';
import _ from 'lodash';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import ViewState from '../../../../Utils/ViewState';
import * as ACTIONS from './liveStationAction.constant';
import {INPUT_CITY_SELECTED} from '../../../../Utils/RailsConstant';
import fetch2 from '../../../../fetch2';


export const initLiveStationApi = (sourceStationCode, destinationStationCode) => async (dispatch) => {
  try {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      dispatch({
        type: ACTIONS.ACTIONS_RAILS_LIVE_STATION_SET_NO_NETWORK,
        data: {showLoader: false, liveStationViewState: ViewState.NO_INTERNET},
      });
      return;
    }
    dispatch({
      type: ACTIONS.ACTIONS_RAILS_LIVE_STATION_INFO_SHOW_LOADER,
      data: {
        showLiveStationLoader: true, liveStationViewState: ViewState.LOADING, 
        destinationStationCode: sourceStationCode, sourceStationCode: destinationStationCode,
      },
    });
    const liveStations = await getLiveStation(sourceStationCode, destinationStationCode);
    if (liveStations.Error) {
      dispatch({
        type: ACTIONS.ACTIONS_RAILS_INFO_SHOW_ERROR,
        data: {
          showLiveStationLoader: false, liveStationErrorMsg: liveStations.Error.message, 
          liveStationViewState: ViewState.ERROR, sourceStationCode, destinationStationCode,
        },
      });
    } else if (!_.isEmpty(liveStations)) {
      dispatch(sendSuccess(liveStations, sourceStationCode, destinationStationCode));
    }
  } catch (error) {
    console.log('Some error occured at processing the response for live station response');
  }
};

function _getTrainList(trains) {
  const trainList = [];
  trains.forEach((item) => {
    trainList.push({
      trainName: item.Train.Name,
      trainNumber: item.Train.Number,
      actualArrivalTime: item.ArrivalDetails.actualArrivalTime,
      scheduledArrivalTime: item.ArrivalDetails.scheduledArrivalTime,
      actualDepartureTime: item.DepartureDetails.actualDepartureTime,
      scheduledDepartureTime: item.DepartureDetails.scheduledDepartureTime,
      delayString: item.DelayString,
      platformNo: item.ExpectedPlatformNo === 'NULL' ? 'NA' : item.ExpectedPlatformNo,
      startStation: item.StartStation,
    });
  });
  return trainList;
}

const sendSuccess = (liveStations, sourceStationCode, destinationStationCode) => (dispatch) => {
  const {Title: title, Trains: trains, Disclaimer} = liveStations;
  const trainList = _getTrainList(trains);
  dispatch({
    type: ACTIONS.ACTIONS_RAILS_LIVE_STATION_SHOW_DETAILS,
    data: {
      trainList, title, Disclaimer, showLiveStationLoader: false, sourceStationCode, destinationStationCode,
    },
  });
};

export const getLiveStation = async (sourceStationCode, destinationStationCode) => {
  try {
    const url = 'https://railsinfo-services.makemytrip.com/api/rails/train/livestation/v1';
    const body = {
      sourceStationCode: `${sourceStationCode}`,
      destinationStationCode: `${destinationStationCode}`,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    const header = {
      'Content-Type': 'application/json',
      'cache-control': 'no-cache',
    };
    const response = await fetch2(url, {
      method: 'POST',
      headers: header,
      body: JSON.stringify(body),
    });
    if (!response) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

export const onOriginClicked = (dispatch, getState) => {
  const {sourceStationCode, destinationStationCode} = getState().liveStationReducer;
  Actions.railsCityPicker({
    selectedCity: sourceStationCode,
    pairedCity: destinationStationCode,
    inputCitySelected: INPUT_CITY_SELECTED.source,
    actionType: ACTIONS.ACTION_RIS_ON_ORIGIN_SELECTED,
  });
};


export const openTrainPicker = () => {
  Actions.railsTrainNumberPicker({
    actionType: ACTIONS.ACTION_OPEN_TRAIN_PICKER,
  });
};


const _getChannelCode = () => {
  let channelCode;
  if (Platform.OS === 'web') {
    channelCode = 'PWA';
  } else if (Platform.OS === 'android') {
    channelCode = 'ANDROID';
  } else if (Platform.OS === 'ios') {
    channelCode = 'IOS';
  } else {
    channelCode = Platform.OS;
  }
  return channelCode;
};

