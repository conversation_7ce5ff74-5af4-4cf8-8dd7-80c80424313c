import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  rootContainer: {
    flexDirection: 'row',
    height: 77,
    borderBottomColor: colors.lightGrey,
    borderBottomWidth: 2,
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  headerTextStyle: {
    color: colors.black,
  },
  subHeaderTextStyle: {
    color: colors.textGrey,
    marginTop: 4,
  },
  headerImg: {
    height: 40,
    width: 40,
  },
  headerContainer: {
    flex: 1,
    flexDirection: 'column',
    marginLeft: 12,
  },
  mainHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  closeContainer: {
    height: 30,
    width: 30,
    alignItems: 'center',
  },
  closeImg: {
    height: 16,
    width: 16,
  },
});
