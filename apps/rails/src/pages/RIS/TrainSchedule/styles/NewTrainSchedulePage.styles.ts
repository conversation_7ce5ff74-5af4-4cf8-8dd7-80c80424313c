import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  displayFlex: {
    flex: 1,
  },
  loaderView: {
    flexDirection: 'column',
    flex: 1,
  },
  loaderContainer: {
    justifyContent: 'center',
    flex: 1,
    alignItems: 'center',
  },
  buttonContainer: {
    backgroundColor: colors.white,
    height: 76,
    elevation: 4,
    borderTopWidth: 2,
    borderTopColor: colors.grayBg,
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  buttonOutline: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
  },
  buttonText: {
    color: colors.white,
  },
  itemSeperator: {
    width: 0,
    height: 0,
    backgroundColor: colors.green11,
  },
  sectionContainer: {
    marginTop: 10,
  },
});
