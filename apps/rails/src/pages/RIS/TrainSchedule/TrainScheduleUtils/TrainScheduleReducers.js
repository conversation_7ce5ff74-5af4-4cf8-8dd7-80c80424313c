import {
  ACTIONS_RAILS_TRAINS_SCHEDULE_INIT,
  ACTIONS_RAILS_TRAINS_SCHEDULE_SET_LOADER,
  ACTIONS_RAILS_TRAINS_SCHEDULE_NO_NETWORK,
  ACTIONS_RAILS_TRAINS_SCHEDULE_SHOW_ERROR,
} from './TrainScheduleActions';

const initialState = {
  tsViewState: null,
  showLoader: null,
  trainScheduleDetails: null,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case ACTIONS_RAILS_TRAINS_SCHEDULE_INIT: {
      const {
        trainScheduleDetails, schedule,
        trainNumber, trainName, trainClasses,
        daysOfRun, totalDuration, numberOfStops,
        disclaimer, showLoader, tsViewState,
      } = action.data;
      return {
        ...state,
        trainScheduleDetails,
        schedule,
        trainNumber,
        trainName,
        trainClasses,
        daysOfRun,
        totalDuration,
        numberOfStops,
        disclaimer,
        showLoader,
        tsViewState,
      };
    }
    case ACTIONS_RAILS_TRAINS_SCHEDULE_SET_LOADER: {
      return {...action.data};
    }

    case ACTIONS_RAILS_TRAINS_SCHEDULE_NO_NETWORK: {
      return {...state, ...action.data};
    }

    case ACTIONS_RAILS_TRAINS_SCHEDULE_SHOW_ERROR: {
      return {...action.data};
    }

    default:
      return state;
  }
};
