/* eslint-disable */
import { Actions } from '../../../../navigation';
import {Platform, BackHandler} from 'react-native';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import ViewState from '../../../../Utils/ViewState';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import fetch2 from '../../../../fetch2';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';
import { toggleTrainScheduleBottomSheet } from '../../../NewListing/RailsListingActions';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';

export const ACTIONS_RAILS_TRAINS_SCHEDULE_INIT = 'ACTIONS_RAILS_TRAINS_SCHEDULE_INIT';
export const ACTIONS_RAILS_TRAINS_SCHEDULE_SET_LOADER = 'ACTIONS_RAILS_TRAINS_SCHEDULE_SET_LOADER';
export const ACTIONS_RAILS_TRAINS_SCHEDULE_NO_NETWORK = 'ACTIONS_RAILS_TRAINS_SCHEDULE_NO_NETWORK';
export const ACTIONS_RAILS_TRAINS_SCHEDULE_SHOW_ERROR = 'ACTIONS_RAILS_TRAINS_SCHEDULE_SHOW_ERROR';

export const initTrainSchedule = trainNumber => async (dispatch) => {
  try {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      dispatch({
        type: ACTIONS_RAILS_TRAINS_SCHEDULE_NO_NETWORK,
        data: {showLoader: false, tsViewState: ViewState.NO_INTERNET},
      });
      return;
    }
    dispatch({
      type: ACTIONS_RAILS_TRAINS_SCHEDULE_SET_LOADER,
      data: {showLoader: true, tsViewState: ViewState.LOADING},
    });
    const trainScheduleDetails = await getTrainScheduleDetails(trainNumber);
    if (!trainScheduleDetails || trainScheduleDetails.Error) {
      const errorMsg = trainScheduleDetails?.Error?.message || _label('something_went_wrong_try_again_later');
      dispatch({
        type: ACTIONS_RAILS_TRAINS_SCHEDULE_SHOW_ERROR,
        data: {
          showLoader: false, tsViewState: ViewState.ERROR, tsErrorMsg: errorMsg,
        },
      });
    } else {
      const schedule = formatSchedule(trainScheduleDetails.Schedule) || [];
       
      const trainNumber = trainScheduleDetails.Train.Number;
      const trainName = trainScheduleDetails.Train.Name;
      const trainClasses = trainScheduleDetails.Train.Classes;
      const daysOfRun = trainScheduleDetails.DaysOfRun;
      const totalDuration = trainScheduleDetails.TotalDuration;
      const numberOfStops = trainScheduleDetails.NumberOfStops;
      const disclaimer = trainScheduleDetails.Disclaimer;
      dispatch({
        type: ACTIONS_RAILS_TRAINS_SCHEDULE_INIT,
        data: {
          trainScheduleDetails, schedule, trainNumber, trainName,
          trainClasses, daysOfRun, totalDuration, numberOfStops,
          disclaimer, showLoader: false, tsViewState: ViewState.SHOW_DETAIL,
        },
      });
    }
  } catch (error) {
    console.log('Error in train schedule response');
  }
};

const formatSchedule = (Section) => {
  let day = 0;
  const tempSchedule = [];
  for (let i = 0; i < Section.length; i += 1) {
    if (Section[i].Day === day) {
      tempSchedule[day - 1].data.push(Section[i]);
    } else {
      day += 1;
      tempSchedule.push({title: `Day ${day}`, data: []});
      tempSchedule[day - 1].data.push(Section[i]);
    }
  }
  return tempSchedule;
};

async function getTrainScheduleDetails(trainNumber) {
  try {
    const url = 'https://railsinfo-services.makemytrip.com/api/rails/train/schedule/v1';
    const body = {
      trainNumber,
      trackingParams: {
        affiliateCode: 'MMT001',
        channelCode: _getChannelCode(),
      },
    };
    const header = {'Content-Type': 'appliation/json'};
    const response = await fetch2(url, {
      method: 'POST',
      headers: header,
      body: JSON.stringify(body),
    });
    if (!response) {
      return null;
    }
    const jsonResponse = await response.json();
    return jsonResponse;
  } catch (e) {
    console.log(e);
    return null;
  }
}

const _getChannelCode = () => {
  let channelCode;
  if (Platform.OS === 'web') {
    channelCode = 'PWA';
  } else if (Platform.OS === 'android') {
    channelCode = 'ANDROID';
  } else if (Platform.OS === 'ios') {
    channelCode = 'IOS';
  } else {
    channelCode = Platform.OS;
  }
  return channelCode;
};

export const onBackIconPressed = () => (dispatch) => {
  goBack(dispatch);
};

export const onHardBackPressed = () => (dispatch) => {
  goBack(dispatch);
  return true;
};

const goBack = (dispatch) => {
  dispatch({
    type: ACTIONS_RAILS_TRAINS_SCHEDULE_SET_LOADER,
    data: {showLoader: true, tsViewState: ViewState.LOADING},
  });
  const somethingPoped = Actions.pop();
  if (somethingPoped != null && !somethingPoped) {
    if (Platform.OS === 'ios') {
      ViewControllerModule.popViewController(getRootTag());
    } else {
      BackHandler.exitApp();
    }
  }
};

export const closeBottomSheet = (dispatch) => {
  dispatch({
    type: ACTIONS_RAILS_TRAINS_SCHEDULE_SET_LOADER,
    data: {showLoader: true, tsViewState: ViewState.LOADING},
  });
  dispatch(toggleTrainScheduleBottomSheet( false ));
};


