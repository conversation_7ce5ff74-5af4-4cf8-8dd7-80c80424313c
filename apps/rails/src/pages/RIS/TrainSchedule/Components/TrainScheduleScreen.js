import {connect} from 'react-redux';
import TrainSchedulePage from './TrainSchedulePage';
import {initTrainSchedule, onBackIconPressed, onHardBackPressed} from '../TrainScheduleUtils/TrainScheduleActions';

const mapStateToProps = (state) => {
  const {
    schedule,
    trainNumber,
    trainName,
    trainClasses,
    daysOfRun,
    totalDuration,
    numberOfStops,
    tsViewState,
    tsErrorMsg,
    showLoader,
    disclaimer,
    trainScheduleDetails,
  } = state.trainScheduleReducer;

  return {
    schedule,
    trainNumber,
    trainName,
    trainClasses,
    daysOfRun,
    totalDuration,
    numberOfStops,
    tsViewState,
    tsErrorMsg,
    showLoader,
    disclaimer,
    trainScheduleDetails,
  };
};


const mapDispatchToProps = dispatch => ({
  initTrainSchedule: trainNumber => dispatch(initTrainSchedule(trainNumber)),
  onBackIconPressed: () => dispatch(onBackIconPressed()) ,
  onHardBackPressed: () => dispatch(onHardBackPressed()),
});


export default connect(mapStateToProps, mapDispatchToProps)(TrainSchedulePage);
