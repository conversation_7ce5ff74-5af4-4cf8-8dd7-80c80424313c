import React from 'react';
import { View, Text, Image } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import { showNewTrainSchedule } from '@mmt/rails/src/RailsAbConfig';
import { StationStyles } from './Station.Styles';
import { trainScheduleV2 } from '../../../../RailsAbConstants';
import PropTypes from 'prop-types';


const getStationDetails = (station) => {
  const {
    Station: { code: stationCode, name: stationName } = {},
    Distance: distance,
    ExpectedPlatformNo: expectedPlatformNo,
    ArrivalDetails: { scheduledArrivalTime: arrivalTime } = {},
    DepartureDetails: { scheduledDepartureTime: departureTime } = {},
    HaltMinutes: haltMinutes,
  } = station || {};

  return { stationCode, stationName, distance, expectedPlatformNo, arrivalTime, departureTime, haltMinutes };
};

const StationDetails = ({
  stationCode,
  stationName,
  distance,
  expectedPlatformNo,
  arrivalTime,
  departureTime,
  haltMinutes,
  isBoardingStation,
  isDestinationStation,
  isRouteStation,
  newTrainSchedule,
}) => (
  <View style={StationStyles.flexRow}>
    <View style={StationStyles.LeftBox}>
      <Text style={StationStyles.FirstLineLeft}>{stationCode}</Text>
      <Text style={StationStyles.SecondLineLeft}>{distance} {_label('km')}</Text>
    </View>
    <View
      style={[
        StationStyles.RightBox,
        {
          borderLeftWidth: 5,
          borderLeftColor:
            departureTime === 'Destination'
              ? colors.white
              : newTrainSchedule && isRouteStation && !isDestinationStation
              ? colors.lightGreen16
              : colors.lightGrey,
        },
      ]}
    >
      {isBoardingStation || isDestinationStation ? (
        <>
          {isBoardingStation && <View style={StationStyles.greyPart} />}
          <Image source={ASSETS?.checkCircleFilled} style={StationStyles.greenTick} />
        </>
      ) : (
        <View style={[StationStyles.Circle, { borderColor: newTrainSchedule ? 
          (isRouteStation ? colors.lightGreen16 : colors.grey) : colors.grey }]} />
      )}
      <View style={StationStyles.flexRow}>
        <Text style={StationStyles.FirstLineRight}>{stationName}</Text>
        {isBoardingStation && (
          <View style={StationStyles.LabelContainer}>
            <Text style={StationStyles.Label}>{_label('boarding')}</Text>
          </View>
        )}
        {isDestinationStation && (
          <View style={StationStyles.LabelContainer}>
            <Text style={StationStyles.Label}>{_label('get_down')}</Text>
          </View>
        )}
      </View>
      <Text style={StationStyles.SecondLineRight}>
        {_label('platform')}
        <Text>{' '}</Text>
        <Text style={{ fontFamily: fonts.black }}>#{expectedPlatformNo}</Text>
        <Text>{' | '}</Text>
        {haltMinutes !== '0' && `Stop ${haltMinutes}m`}
        {arrivalTime === 'Source' && 'Journey Start'}
        {departureTime === 'Destination' && 'Journey End'}
      </Text>

      <Text style={StationStyles.ThirdLineRight}>
        {arrivalTime !== 'Source' && `ARR. ${arrivalTime}`}{' '}
        {departureTime !== 'Destination' && `DEP. ${departureTime}`}
      </Text>
      {!isRouteStation && <View style={StationStyles.greyPart} />}
      </View>
  </View>
);

const Station = (props) => {
  const { item, isRouteStation, originStation, destinationStation } = props;
  const newTrainSchedule = showNewTrainSchedule() === trainScheduleV2.SHOWN;
  if (newTrainSchedule && isRouteStation && Array?.isArray(item?.stations)) {
    return (
      <View style={StationStyles.routeStationWrapper}>
        {item.stations.map((station, index) => {
          const {
            stationCode,
            stationName,
            distance,
            expectedPlatformNo,
            arrivalTime,
            departureTime,
            haltMinutes,
          } = getStationDetails(station);

          const isBoardingStation = originStation?.code === stationCode;
          const isDestinationStation = destinationStation?.code === stationCode;
          const stationIndex = item?.stations?.map((s) => s?.Station?.code).indexOf(stationCode);
          const destinationIndex = item?.stations
            ?.map((s) => s?.Station?.code)
            .indexOf(destinationStation?.code);
          const isBeforeDest = stationIndex < destinationIndex;

          return (
            <StationDetails
              key={index}
              stationCode={stationCode}
              stationName={stationName}
              distance={distance}
              expectedPlatformNo={expectedPlatformNo}
              arrivalTime={arrivalTime}
              departureTime={departureTime}
              haltMinutes={haltMinutes}
              isBoardingStation={isBoardingStation}
              isDestinationStation={isDestinationStation}
              isBeforeDest={isBeforeDest}
              isRouteStation={isRouteStation}
              newTrainSchedule={newTrainSchedule}
            />
          );
        })}
      </View>
    );
  }

  if (isRouteStation && Array?.isArray(item?.stations)) {
    return (
      <View>
        {item?.stations?.map((station, index) => {
          const stationDetails = getStationDetails(station);
          return (
            <StationDetails
              key={index}
              {...stationDetails}
              isRouteStation={isRouteStation}
              newTrainSchedule={newTrainSchedule}
            />
          );
        })}
      </View>
    );
  }

  const stationDetails = getStationDetails(item);

  return (
    <StationDetails
      {...stationDetails}
      isBoardingStation={originStation?.code === stationDetails?.stationCode}
      isDestinationStation={destinationStation?.code === stationDetails?.stationCode}
      isBeforeDest={false}
      isRouteStation={isRouteStation}
      newTrainSchedule={newTrainSchedule}
    />
  );
};

StationDetails.propTypes = {
  stationCode: PropTypes.string,
  stationName: PropTypes.string,
  distance: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  expectedPlatformNo: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  arrivalTime: PropTypes.string,
  departureTime: PropTypes.string,
  haltMinutes: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  isBoardingStation: PropTypes.bool,
  isDestinationStation: PropTypes.bool,
  isRouteStation: PropTypes.bool,
  newTrainSchedule: PropTypes.bool,
};

Station.propTypes = {
  item: PropTypes.object,
  isRouteStation: PropTypes.bool,
  originStation: PropTypes.shape({
    code: PropTypes.string,
  }),
  destinationStation: PropTypes.shape({
    code: PropTypes.string,
  }),
};


export default Station;
