import React from 'react';
import {
  View,
  Image,
  SectionList,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  BackHandler,
  Platform,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import PropTypes from 'prop-types';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import UpArrowBlue from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import DayLabel from './DayLabel';
import Station from './Station';

import TrainScheduleHeader from './TrainScheduleHeader';
import ViewState from '../../../../Utils/ViewState';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import TrainHeader from './TrainHeader';
import TrainInfoCard from './TrainInfoCard';
import Disclaimer from './Disclaimer';
import {
  RIS_LANDING_TRAIN_SCHEDULE_SUCCESS,
  trackOmnitureLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import ErrorView from './ErrorView';
import { RIS_SCHEDULE_RESULT } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import {getAllTypesAdsAb} from '../../../Common/utils';
import { trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
class TrainSchedulePage extends React.Component {
  constructor(props) {
    super(props);
    this._scrollViewRef = null;
    this.state = {
      showScrollToTop: false,
      showDiffTypeOfAds : {multi_banner: 0, snackbar: 0, adfeed:0,interstitial: 0},
    };
  }

  UNSAFE_componentWillMount() {
    this.props.initTrainSchedule(this.props.trainNumToSearch);
    this.getAdConfig();
  }
  getAdConfig = async ()=>{
    const AdsAb = await getAllTypesAdsAb();
    this.setState({showDiffTypeOfAds:AdsAb});
    trackAdLoad('mob:funnel:ris:pwa:schedule:scheduledtls',AdsAb.trackingPayload);
  };
  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.props.onHardBackPressed);
    trackOmnitureLoadEvent(RIS_LANDING_TRAIN_SCHEDULE_SUCCESS);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.props.onHardBackPressed);
  }

  _onScroll = (e) => {
    // console.log('e.nativeEvent.y', e.nativeEvent.contentOffset)
    const showScrollToTop = e.nativeEvent.contentOffset.y > 24;
    this.setState({ showScrollToTop });
  };

  render() {
    const {
      trainNumber,
      trainName,
      daysOfRun,
      trainClasses,
      schedule,
      totalDuration,
      numberOfStops,
      tsViewState,
      tsErrorMsg,
      showLoader,
      disclaimer,
      onBackIconPressed,
    } = this.props;
    return (
      <SafeAreaView style={{ flex: 1 }}>
        {tsViewState === ViewState.LOADING && showLoader && (
          <View style={{ flexDirection: 'column', flex: 1 }}>
            <View
              style={{
                justifyContent: 'center',
                flex: 1,
                alignItems: 'center',
              }}
            >
              <Spinner size={30} color="#008b8b" />
            </View>
          </View>
        )}

        <View>
          {!showLoader && (
            <TrainScheduleHeader
              title="Train Route and Schedule"
              onBackIconPressed={onBackIconPressed}
            />
          )}
          {tsViewState === ViewState.NO_INTERNET && (
            <NoInternetView
              onRetry={() => this.props.initTrainSchedule(this.props.trainNumToSearch)}
            />
          )}
          {tsViewState === ViewState.ERROR && (
            <ErrorView displayText={tsErrorMsg} onGoBackClicked={onBackIconPressed} />
          )}
          {tsViewState === ViewState.SHOW_DETAIL && !showLoader && trainNumber && (
            <ScrollView
              onScroll={this._onScroll}
              ref={(ref) => {
                this._scrollViewRef = ref;
              }}
            >
              {!!this.state.showDiffTypeOfAds.multi_banner && <View
                style={{
                  margin: 16,
                  flexDirection:'row', justifyContent: 'center',
                }}
              >
                {getAdsCard(Platform.OS, RIS_SCHEDULE_RESULT)}
              </View>}
              <TrainHeader trainNumber={trainNumber} trainName={trainName} />
              <TrainInfoCard
                daysOfRun={daysOfRun}
                trainClasses={trainClasses}
                totalDuration={totalDuration}
                numberOfStops={numberOfStops}
              />
              <View style={{ marginTop: 10 }}>
                <SectionList
                  sections={schedule}
                  ItemSeparatorComponent={() => (
                    <View style={{ width: 0, height: 0, backgroundColor: colors.green11 }} />
                  )}
                  renderItem={({ item }) => <Station item={item} />}
                  renderSectionHeader={({ section: { title } }) => <DayLabel value={title} />}
                />
              </View>
              <Disclaimer disclaimer={disclaimer} />
            </ScrollView>
          )}
          {this.state.showScrollToTop && (
            <View
              style={{
                position: 'absolute',
                bottom: 72,
                right: 24,
              }}
            >
              <TouchableOpacity
                onPress={() => {
                  if (this._scrollViewRef) {
                    this._scrollViewRef.scrollTo({ y: 0 });
                  }
                }}
              >
                <Card
                  elevation={4}
                  style={{
                    height: 40,
                    width: 40,
                    borderRadius: 20,
                    borderColor: colors.grey2,
                  }}
                >
                  <Image
                    style={{
                      width: 30,
                      height: 30,
                      position: 'absolute',
                      left: 5,
                      top: 5,
                    }}
                    source={UpArrowBlue}
                  />
                </Card>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </SafeAreaView>
    );
  }
}

TrainSchedulePage.propTypes = {
  initTrainSchedule: PropTypes.func.isRequired,
  trainNumToSearch: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onHardBackPressed: PropTypes.func,
  trainNumber: PropTypes.string,
  trainName: PropTypes.string,
  daysOfRun: PropTypes.array,
  trainClasses: PropTypes.array, 
  schedule: PropTypes.array,  
  totalDuration: PropTypes.string,      
  numberOfStops: PropTypes.number,
  tsViewState: PropTypes.oneOf([
    'LOADING',
    'NO_INTERNET',
    'ERROR',
    'SHOW_DETAIL',
  ]),
  tsErrorMsg: PropTypes.string,
  showLoader: PropTypes.bool,
  disclaimer: PropTypes.string,
  onBackIconPressed: PropTypes.func,
};
export default TrainSchedulePage;
