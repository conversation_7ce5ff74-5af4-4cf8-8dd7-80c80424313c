import Card from '@mmt/legacy-commons/Common/Components/Card';
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

const TrainInfoCard = (props) => {
  const {
    daysOfRun, trainClasses, totalDuration, numberOfStops,
  } = props;
  return (
    <View style={{
        padding: 20,
        fontSize: 14,
      }}
    >
      <Card elevation={4}>
        {daysOfRun && <View style={TrainInfoCardStyle.InnerCardStyle}>

          <Text style={{
            flex: 1,
            fontFamily: fonts.regular,
          }}
          >Runs On
          </Text>
          {daysOfRun &&
          <View style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
          >
            <Text
              style={daysOfRun.Mon ? TrainInfoCardStyle.DayHighlight : TrainInfoCardStyle.DayNoHighlight}
            >M
            </Text>
            <Text
              style={daysOfRun.Tue ? TrainInfoCardStyle.DayHighlight : TrainInfoCardStyle.DayNoHighlight}
            >T
            </Text>
            <Text
              style={daysOfRun.Wed ? TrainInfoCardStyle.DayHighlight : TrainInfoCardStyle.DayNoHighlight}
            >W
            </Text>
            <Text
              style={daysOfRun.Thu ? TrainInfoCardStyle.DayHighlight : TrainInfoCardStyle.DayNoHighlight}
            >T
            </Text>
            <Text
              style={daysOfRun.Fri ? TrainInfoCardStyle.DayHighlight : TrainInfoCardStyle.DayNoHighlight}
            >F
            </Text>
            <Text
              style={daysOfRun.Sat ? TrainInfoCardStyle.DayHighlight : TrainInfoCardStyle.DayNoHighlight}
            >S
            </Text>
            <Text
              style={daysOfRun.Sun ? TrainInfoCardStyle.DayHighlight : TrainInfoCardStyle.DayNoHighlight}
            >S
            </Text>
          </View>}

        </View> }
        {trainClasses && <View style={TrainInfoCardStyle.InnerCardStyle}>

          <Text style={{
            flex: 1,
            fontFamily: fonts.regular,
          }}
          >Classes
          </Text>
          <View style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'flex-end',
          }}
          >
            {trainClasses.map((value, index) => (
              <Text
                key={index}
                style={{
                  fontFamily: fonts.black,
                  color: colors.black,
                  marginLeft: 15,
                }}
              >
                {value}
              </Text>
            ))}
          </View>

        </View> }
        {totalDuration && <View style={TrainInfoCardStyle.InnerCardStyle}>

          <Text style={{fontFamily: fonts.regular}}>Total Duration</Text>
          <Text style={{fontFamily: fonts.black}}> {totalDuration}</Text>

        </View> }
        {numberOfStops && <View style={TrainInfoCardStyle.InnerCardStyle}>

          <Text style={{fontFamily: fonts.regular}}>Number Of Stops</Text>
          <Text style={{fontFamily: fonts.black}}>{numberOfStops}</Text>

        </View> }
      </Card>

    </View>);
};

TrainInfoCard.propTypes = {
  daysOfRun: PropTypes.object.isRequired,
  trainClasses: PropTypes.object.isRequired,
  totalDuration: PropTypes.string.isRequired,
  numberOfStops: PropTypes.number.isRequired,
};

const TrainInfoCardStyle = StyleSheet.create({
  InnerCardStyle: {
    borderBottomColor: colors.lightGrey,
    borderBottomWidth: 1,
    paddingBottom: 12,
    paddingTop: 12,
    paddingLeft: 8,
    paddingRight: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  DayHighlight: {
    color: colors.green,
    fontFamily: fonts.bold,
  },
  DayNoHighlight: {fontFamily: fonts.regular},
});

export default TrainInfoCard;
