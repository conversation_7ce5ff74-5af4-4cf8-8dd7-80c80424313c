import { connect } from 'react-redux';
import NewTrainSchedulePage from './NewTrainSchedulePage';
import { closeBottomSheet, initTrainSchedule } from '../TrainScheduleUtils/TrainScheduleActions';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { LISTING_TRAIN_SCHEDULE_DISMISS } from 'apps/rails/src/Utils/RailsConstant';
import { RIS_LANDING_TRAIN_SCHEDULE_SUCCESS } from 'apps/rails/src/RisAnalytics/RisAnalytics';

const mapStateToProps = (state) => {
  const {
    railsListing: { isCnfOptionsTrainSchedule = false } = {},
    trainScheduleReducer: {
      schedule,
    trainNumber,
    trainName,
    trainClasses,
    daysOfRun,
    totalDuration,
    numberOfStops,
    tsViewState,
    tsErrorMsg,
    showLoader,
    disclaimer,
    trainScheduleDetails,
    } = {},
  } = state;

  const trainNumberForSchedule = state.railsListing?.trainNumberForSchedule;
  const { alternateAvailabilityResponse, filteredTrainsList } = state?.railsListing || {};

  return {
    schedule,
    trainNumber,
    trainNumToSearch: trainNumberForSchedule,
    trainName,
    trainClasses,
    daysOfRun,
    totalDuration,
    numberOfStops,
    tsViewState,
    tsErrorMsg,
    showLoader,
    disclaimer,
    trainScheduleDetails,
    alternateAvailabilityResponse,
    isCnfOptionTrain: isCnfOptionsTrainSchedule,
    filteredTrainsList,
  };
};

const mapDispatchToProps = (dispatch) => ({
  initTrainSchedule: (trainNumber) => dispatch(initTrainSchedule(trainNumber)),
  closeTrainScheduleBottomSheet: () => {
    trackClickEventProp61(RIS_LANDING_TRAIN_SCHEDULE_SUCCESS, LISTING_TRAIN_SCHEDULE_DISMISS);
    dispatch(closeBottomSheet);
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(NewTrainSchedulePage);
