import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { styles } from '../styles/NewTrainScheduleHeader.styles';

import trainScheduleImg from '@mmt/legacy-assets/src/train_schedule_header.webp';
import crossImg from '@mmt/legacy-assets/src/close-black.webp';

interface TrainScheduleHeaderProps {
  trainDetails :{
    trainName: string,
    trainNumber: string
  },
  closeTrainScheduleBottomSheet : () => void
}

const TrainScheduleHeader = (props: TrainScheduleHeaderProps) => {
  const { trainDetails, closeTrainScheduleBottomSheet } = props;
  const trainName = trainDetails?.trainName;
  const trainNumber = trainDetails?.trainNumber;
  return (
    <View style={styles.rootContainer} testID="new_train_schedule_header_container">
      <Image source={trainScheduleImg} style={styles.headerImg} />
      <View style={styles.headerContainer}>
        <View style={styles.mainHeader}>
          <Text style={[styles.headerTextStyle, getLineHeight(18), fontStyle('black')]}>
            {trainNumber}
          </Text>
          <TouchableOpacity
            onPress={closeTrainScheduleBottomSheet}
            style={styles.closeContainer}
            testID="new_train_schedule_header_close_button"
          >
            <Image source={crossImg} style={styles.closeImg} />
          </TouchableOpacity>
        </View>
        <Text style={[styles.subHeaderTextStyle, getLineHeight(16), fontStyle('bold')]}>
          {trainName}
        </Text>
      </View>
    </View>
  );
};


export default TrainScheduleHeader;
