import React from 'react';
import PropTypes from 'prop-types';
import {View, Text, Image, StyleSheet} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import backIcon from '@mmt/legacy-assets/src/iconBackBlack.webp';

const TrainScheduleHeader = (props) => {
  const {
    onBackIconPressed,
    title,
  } = props;
  return (
    <View style={trnDetailsStyle.rootContainer}>
      <TouchableRipple onPress={onBackIconPressed}>
        <View style={trnDetailsStyle.backIconContainer}>
          <Image style={trnDetailsStyle.backIconStyle} source={backIcon} />
        </View>
      </TouchableRipple>
      <View>
        <Text style={trnDetailsStyle.headerTextStyle}>{title}</Text>

      </View>
    </View>
  );
};

TrainScheduleHeader.propTypes = {
  onBackIconPressed: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
};

const trnDetailsStyle = StyleSheet.create({
  rootContainer: {
    flexDirection: 'row',
    height: 64,
    borderBottomColor: colors.lightGrey,
    borderBottomWidth: 2,
    borderTopWidth: 0,
    borderTopColor: colors.white,
  },
  backIconStyle: {
    width: 18,
    height: 16,
    justifyContent: 'center',
  },
  backIconContainer: {
    width: 48,
    height: 48,
    marginTop: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTextStyle: {
    fontFamily: fonts.regular,
    fontSize: 18,
    color: colors.defaultTextColor,
    marginTop: 20,
  },
});

export default TrainScheduleHeader;
