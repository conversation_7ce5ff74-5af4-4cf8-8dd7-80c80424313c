import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

const DayLabel = props => (
  <View style={{flexDirection: 'row'}}>
    <View style={DayLabelStyle.LeftBox} />
    <View style={DayLabelStyle.RightBox}>
      <View style={DayLabelStyle.TextPosition}>
        <Text style={{color: colors.white, fontSize: 15, fontFamily: fonts.bold}}>{props.value}</Text>
      </View>
      <Text style={{paddingLeft: 20}} />
    </View>
  </View>);

DayLabel.propTypes = {
  value: PropTypes.string,
};

const DayLabelStyle = StyleSheet.create({
  TextPosition: {
    position: 'absolute',
    left: -38,
    top: -1,
    zIndex: 1,
    width: 70,
    height: 20,
    backgroundColor: colors.black,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  LeftBox: {
    flex: 1, height: 70,
  },
  RightBox: {
    flex: 3, height: 70, borderLeftWidth: 6, borderLeftColor: colors.greyBookedSeat,
  },
});
export default DayLabel;
