import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';


const Disclaimer = (props) => {
  const {disclaimer} = props;
  return (<View style={DisclaimerStyle.container}>
    <Text style={DisclaimerStyle.header}>Disclaimer</Text>
    <Text style={DisclaimerStyle.text}>{disclaimer}</Text>
  </View>
  );
};

const DisclaimerStyle = StyleSheet.create({
  container:{
    borderTopColor: colors.grayBg,
    borderTopWidth: 5,
    flex:1,
    marginBottom: 40},
  header: {
    paddingTop: 25, paddingBottom: 25, paddingLeft: 15, fontSize: 22 , fontFamily: fonts.bold, color: colors.black,
  },
  text:{
    paddingBottom: 25, paddingLeft: 15, fontSize: 14 , paddingRight: 20,
  },
});

Disclaimer.propTypes = {
  disclaimer: PropTypes.string.isRequired,
};

export default Disclaimer;
