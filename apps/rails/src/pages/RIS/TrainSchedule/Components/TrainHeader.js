import React from 'react';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
} from 'react-native';
import TrainDetailsIcon from '@mmt/legacy-assets/src/icTraindetailsPurple.webp';

const TrainHeader = (props) => {
  const {trainNumber, trainName} = props;
  return (
    <View style={{flexDirection: 'row', padding: 16}}>
      <Image
        style={{width: 42, height: 42}}
        source={TrainDetailsIcon}
      />
      <View style={{flex: 1, flexDirection: 'column', marginLeft: 10}}>
        <Text style={{
          color: colors.black, fontSize: 20, lineHeight: 20, fontWeight: 'bold', marginBottom: 4,
        }}
        >{trainNumber}
        </Text>
        <Text style={{fontSize: 14, lineHeight: 14, fontWeight: '400'}}>{trainNumber} {trainName}</Text>
      </View>

    </View>
  );
};

TrainHeader.propTypes = {
  trainNumber: PropTypes.string.isRequired,
  trainName: PropTypes.string.isRequired,
};

export default TrainHeader;
