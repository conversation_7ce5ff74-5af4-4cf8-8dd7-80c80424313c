import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { Platform } from 'react-native';

export const StationStyles = {
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    LeftBox: {
      flex: 1,
      height: 100,
      flexDirection: 'column',
      alignItems: 'flex-end',
      zIndex: 99,
    },
    RightBox: {
      flex: 3,
      height: 100,
    },
    LeftBorderGrey: {
      borderLeftWidth: 6,
      borderLeftColor: colors.lightGrey,
    },
    LeftBorderWhite: {
      borderLeftWidth: 6,
      borderLeftColor: colors.white,
    },
    greyPart: {
      position: 'absolute',
      ...Platform.select({
        android: {
          top: -21,
          left: -11,
        },
        ios: {
          top: -20,
          left: -11,
        },
      }),
      width: 5,
      height: 20,
      marginLeft:6,
      backgroundColor: colors.lightGrey,
    },
    Circle: {
      position: 'absolute',
      ...Platform.select({
        android: {
          top: -1,
          left: -11,
        },
        ios: {
          top: 0,
          left: -11,
        },
      }),
      width: 16,
      height: 16,
      backgroundColor: colors.white,
      borderWidth: 3,
      borderStyle: 'solid',
      borderRadius: 100,
    },
    greenTick: {
      position: 'absolute',
      zIndex: 2,
      ...Platform.select({
        android: {
          top: -1,
          left: -11,
        },
        ios: {
          top: 0,
          left: -11,
        },
      }),
      width: 17,
      height: 17,
      backgroundColor: colors.white,
    },
    MarginLeft5:{
      marginLeft: 5,
    },
    FirstLineLeft: {
      paddingRight: 20,
      color: colors.black,
      fontFamily: fonts.black,
      justifyContent: 'space-between',
    },
    SecondLineLeft: {
      paddingRight: 20,
      color: colors.defaultTextColor,
      fontFamily: fonts.regular,
      paddingTop: 5,
    },
    FirstLineRight: {
      paddingLeft: 20,
      color: colors.black,
      fontFamily: fonts.black,
    },
    SecondLineRight: {
      paddingLeft: 20,
      color: colors.defaultTextColor,
      fontFamily: fonts.regular,
      paddingTop: 5,
    },
    ThirdLineRight: {
      paddingLeft: 20,
      color: colors.easternBlue,
      fontFamily: fonts.black,
      paddingTop: 5,
    },
    routeStationWrapper: {
       borderColor: colors.lightGrey,
       borderWidth: 1,
       borderRadius: 16,
       zIndex: 1,
       marginRight:10,
       marginLeft: 25,
       boxSizing: 'border-box',
       paddingTop: 15,
       left: -7,
       paddingRight: 37,
       marginBottom: 15,
    },
    Label: {
      color: colors.greyText1,
      fontFamily: 'Lato',
      fontSize: 12,
      fontWeight: '400',
      lineHeight: 14.4,
      textAlign: 'center',
    },
    LabelContainer: {
      borderWidth: 1,
      borderColor: colors.greyText1,
      padding: 2,
      marginLeft: 10,
      borderRadius: 10,
      paddingHorizontal: 8,
    },
  };
