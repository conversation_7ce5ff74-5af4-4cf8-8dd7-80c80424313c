import React, { useEffect } from 'react';
import {
  View,
  Text,
  SectionList,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import DayLabel from './DayLabel';
import Station from './Station';
import NewTrainScheduleHeader from './NewTrainScheduleHeader';
import ViewState from '../../../../Utils/ViewState';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import TrainInfoCard from './TrainInfoCard';
import Disclaimer from './Disclaimer';
import LinearGradient from 'react-native-linear-gradient';
import { styles } from '../styles/NewTrainSchedulePage.styles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import {
  RIS_LANDING_TRAIN_SCHEDULE_SUCCESS,
  trackOmnitureLoadEvent,
} from '../../../../RisAnalytics/RisAnalytics';
import ErrorView from './ErrorView';
import RailsHeaderInfo from 'apps/rails/src/pages/Common/RailsHeaderInfo.js';
import { getAlteredStationName } from '../../../NewListing/Components/ConfirmedOptions/ConfirmedOptionsUtils';

interface NewTrainSchedulePageProps {
  initTrainSchedule: (trainNumber: string) => void
  closeTrainScheduleBottomSheet: () => void
  trainNumber: string,
  trainName: string,
  daysOfRun: object,
  trainClasses: [],
  schedule: [],
  totalDuration: string,
  numberOfStops: number,
  tsViewState: string,
  tsErrorMsg: string,
  showLoader: boolean,
  disclaimer: string,
  trainNumToSearch: string,
  alternateAvailabilityResponse: object,
  isCnfOptionTrain: boolean;
  filteredTrainsList: [];
  item: unknown;
}

interface SectionHeaderProps {
  section: {
    title: string;
    data: unknown[];
  };
}

interface StationItemProps {
  item: unknown;
  isRouteStation: boolean;
}

function NewTrainSchedulePage(props: NewTrainSchedulePageProps) {
  const {
    trainNumber,
    trainName,
    daysOfRun,
    trainClasses,
    schedule,
    totalDuration,
    numberOfStops,
    tsViewState,
    tsErrorMsg,
    showLoader,
    disclaimer,
    trainNumToSearch,
    closeTrainScheduleBottomSheet,
    alternateAvailabilityResponse,
    isCnfOptionTrain,
    filteredTrainsList,
  } = props;
  const matchedTrain = alternateAvailabilityResponse?.alternateAvailabilityList?.find(
    (train) => train?.trainNumber === trainNumber,
  );
  const StationCodes = (trainNumber) => {
    const train = filteredTrainsList?.find((train) => train?.trainNumber === trainNumber);
    return {
      boardingStationCode: train?.frmStnCode || null,
      destinationStationCode: train?.toStnCode || null,
    };
  };
  const { boardingStationCode, destinationStationCode } = StationCodes(trainNumber);
  interface WrappedSchedule {
    stations: Station[];
    isRouteStation: boolean;
  }
  interface WrappedScheduleType {
    stations: Station[];
    isRouteStation: boolean;
  }
  interface StationType {
    code: string;
    name: string;
    distance: number;
    ExpectedPlatformNo: string;
    scheduledArrivalTime: string;
    scheduledDepartureTime: string;
    haltMinutes: number;
  }

  const wrappedSchedule: WrappedSchedule[] = [];
  let startLogging = false;

  schedule?.forEach((day) => {
    const dayWrappedSchedule: WrappedScheduleType[] = [];
    let routeStations: StationType[] = [];
    day?.data?.forEach((station) => {
      if (station?.Station?.code === boardingStationCode) {
        startLogging = true;
      }
      if (startLogging) {
        routeStations?.push(station);
      } else {
        dayWrappedSchedule?.push({ ...station, isRouteStation: false });
      }
      if (station?.Station?.code === destinationStationCode) {
        startLogging = false;
        if (routeStations?.length > 0) {
          dayWrappedSchedule?.push({ stations: routeStations, isRouteStation: true });
          routeStations = [];
        }
      }
    });
    if (routeStations?.length > 0) {
      dayWrappedSchedule?.push({ stations: routeStations, isRouteStation: true });
    }
    wrappedSchedule?.push({ title: day?.title, data: dayWrappedSchedule });
  });

  useEffect(() => {
    props.initTrainSchedule(trainNumToSearch);
    trackOmnitureLoadEvent(RIS_LANDING_TRAIN_SCHEDULE_SUCCESS);
  }, []);

  const ItemSeparator = () => <View style={styles.itemSeperator} />;
  const StationItem = ({ item, isRouteStation }: StationItemProps) => {
    return (
      <Station
        destinationStation={{ code: destinationStationCode }}
        originStation={{ code: boardingStationCode }}
        item={item}
        isRouteStation={isRouteStation}
      />
    );
  };
  const SectionHeader = ({ section }: SectionHeaderProps) => <DayLabel value={section.title} />;

  let altStnlabel = '';
  if (matchedTrain?.frmStnName && matchedTrain?.toStnName) {
    altStnlabel = _label('cnf_listing_text', undefined, {
      originStnName: getAlteredStationName(matchedTrain?.classAvailabilityList[0]?.frmStnName),
      destinationStnName: getAlteredStationName(matchedTrain?.classAvailabilityList[0]?.toStnName),
      boardStnName: matchedTrain?.frmStnName,
      dropStnName: matchedTrain?.toStnName,
    });
  }

  return (
    <SafeAreaView style={styles.displayFlex} testID="new_train_schedule_page_container">
      {tsViewState === ViewState.LOADING && showLoader && (
        <View style={styles.loaderView}>
          <View style={styles.loaderContainer}>
            <Spinner size={30} color={colors.azureBlue5} />
          </View>
        </View>
      )}
      <View style={styles.displayFlex} testID="new_train_schedule_page_content_container">
        {tsViewState === ViewState.NO_INTERNET && (
          <NoInternetView onRetry={() => props?.initTrainSchedule(trainNumToSearch)} />
        )}
        {tsViewState === ViewState.ERROR && (
          <ErrorView displayText={tsErrorMsg} onGoBackClicked={closeTrainScheduleBottomSheet} />
        )}
        {tsViewState === ViewState.SHOW_DETAIL && !showLoader && trainNumToSearch && (
          <>
            <NewTrainScheduleHeader
              trainDetails={{ trainName, trainNumber }}
              closeTrainScheduleBottomSheet={closeTrainScheduleBottomSheet}
            />
            <ScrollView testID="new_train_schedule_page_scrollview">
              <TrainInfoCard
                daysOfRun={daysOfRun}
                trainClasses={trainClasses}
                totalDuration={totalDuration}
                numberOfStops={numberOfStops}
              />
              {isCnfOptionTrain && (
                <RailsHeaderInfo
                  style={{ marginTop: 20 }}
                  message={altStnlabel}
                  id={`${trainNumber}_alternateStation`}
                />
              )}
              <View style={styles.sectionContainer}>
                <SectionList
                  sections={wrappedSchedule}
                  ItemSeparatorComponent={ItemSeparator}
                  renderItem={({ item }) => (
                    <StationItem
                      item={item}
                      isRouteStation={item?.isRouteStation}
                    />
                  )}
                  renderSectionHeader={SectionHeader}
                />
              </View>
              <Disclaimer disclaimer={disclaimer} />
            </ScrollView>
          </>
        )}
      </View>
      {trainNumToSearch && (
        <TouchableOpacity
          style={styles.buttonContainer}
          onPress={closeTrainScheduleBottomSheet}
          testID="new_train_schedule_page_ok_button"
        >
          <LinearGradient
            colors={[colors.lightBlue, colors.darkBlue]}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
            style={styles.buttonOutline}
          >
            <Text style={[styles.buttonText, getLineHeight(16), fontStyle('regular')]}>
              {_label('okay')}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

export default NewTrainSchedulePage;
