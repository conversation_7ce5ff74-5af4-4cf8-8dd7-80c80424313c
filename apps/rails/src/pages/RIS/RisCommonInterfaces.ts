import type {
  StationDetails,
  TrainDetail as TrainDetails,
} from '../RisLiveTrainStatusForm/Interfaces';

export { TrainDetails, StationDetails };

export interface DateDetails {
  dateTitle: string;
  dateFormatted: string;
  dateFormattedMini: string;
  dateNumeric: string;
  dateFormattedSmall: string;
  date: number;
  dateDay: string;
}

export interface RequestDetails {
  trainDetails: TrainDetails;
  stationDetails?: StationDetails;
  dateDetails?: DateDetails;
}

export interface DeepLinkParams {
  dateOfJourney: string;
  trainNumber: string;
}
