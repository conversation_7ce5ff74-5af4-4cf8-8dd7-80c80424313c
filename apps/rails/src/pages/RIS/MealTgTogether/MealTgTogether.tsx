import React from 'react';
import { View } from 'react-native';
import { styles } from './MealTgTogether.styles';
import TgsCardNewLanding from '../../RailsBusHomePage/Components/TgsCardNewLanding/TgsCardNewLanding';
import RailsMealEntryPointNewLanding from '../MealsOnTrain/Components/RailsMealEntryPointNewLanding';

interface MealTgTogetherProps {
  onTgPress: () => void;
  onMealPress: () => void;
}

const MealTgTogether = (props: MealTgTogetherProps) => {
  const { onTgPress, onMealPress } = props;
  return (
    <View style={styles.container}>
      <RailsMealEntryPointNewLanding onBookNowClicked={onMealPress} />
      <TgsCardNewLanding onPress={onTgPress} withMeal={true} />
    </View>
  );
};

export default MealTgTogether;
