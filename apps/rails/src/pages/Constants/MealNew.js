
export const Meal = {
  NO: {
    key: 'D',
    value: 'no_food',
  },
};

export const getMealPreferenceOptions1 = (applicableFoodTypes) => {
  if (applicableFoodTypes) {
    const options = Object.entries(applicableFoodTypes).map(([key, text]) => ({
      id: key,
      text,
      obj: { key, value: text },
    }));

    options.forEach((meal) => {
      Meal[meal.text.toUpperCase().replace(/\s/g, '_') + '_FOOD'] = {
        key: meal.id,
        value: meal.text,
      };
    });

    return options.filter(
      (meal) =>
        !applicableFoodTypes ||
        !applicableFoodTypes.length ||
        applicableFoodTypes.includes(meal.id),
    );
  } else {
    return [];
  }
};

export const getMealObject = (mealKey, applicableFoodTypes) => {
  const Meal = getMealPreferenceOptions1(applicableFoodTypes);
  const mealItem = Meal.find((obj) => obj.id === mealKey);
  return mealItem ? mealItem.obj : null;
};

export default Meal;
