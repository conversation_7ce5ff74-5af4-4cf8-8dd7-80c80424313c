import map from 'lodash/map';

const SrCitizenConcession = Object.freeze({
  FULL_CONCESSION: {
    key: true,
    value: 'i_want_concession',
    displayText: 'availaing_sr_citizen_concession',
  },
  NO_CONCESSION: {
    key: false,
    value: 'i_dont_want_concession',
    displayText: 'not_availaing_sr_citizen_concession',
  },
});

export default SrCitizenConcession;

export const SrCitizenConcessionValues = [SrCitizenConcession.FULL_CONCESSION, SrCitizenConcession.NO_CONCESSION];

export const getSrCitizenObject =
    key => (key ? SrCitizenConcession.FULL_CONCESSION : SrCitizenConcession.NO_CONCESSION);

export const getSrCitizenOptions = () => {
  let options = [];
  try {
    options = map(SrCitizenConcession, cn => ({
      id: `${cn.key}`,
      text: cn.value,
      obj: cn,
    }));
  } catch (e) {
    console.log('Error in getSrCitizenOptions :', e);
  }
  return options;
};
