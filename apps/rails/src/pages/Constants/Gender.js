import isEmpty from 'lodash/isEmpty';
import { stringCompare } from '../TravelerDetails/TravelerDetailsUtils';

const Gender = Object.freeze({
  MALE: {
    key: 'MALE',
    value: 'M',
    displayText: 'male',
  },
  FEMALE: {
    key: 'FEMALE',
    value: 'F',
    displayText: 'female',
  },
  TRANSGENDER: {
    key: 'TRANSGENDER',
    value: 'T',
    displayText: 'transgender',
  },
  NOT_SPECIFIED: {
    key: 'NS',
    value: 'NS',
    displayText: 'not_specified',
  },
});

export const GenderList = Object.freeze([
  {
    key: 'MALE',
    value: 'M',
    displayText: 'male',
  },
  {
    key: 'FEMALE',
    value: 'F',
    displayText: 'female',
  },
  {
    key: 'TRANSGENDER',
    value: 'T',
    displayText: 'others',
  },
]);

export const getGenderConst = (gender) => {
  if (stringCompare(gender, Gender.FEMALE.key)) {
    return Gender.FEMALE;
  } else if (stringCompare(gender, Gender.MALE.key)) {
    return Gender.MALE;
  } else if (stringCompare(gender, Gender.TRANSGENDER.key)) {
    return Gender.TRANSGENDER;
  }
  return Gender.NOT_SPECIFIED;
};

export const getGenderfromValue = (gender) => {
  if (stringCompare(gender, Gender.FEMALE.value)) {
    return Gender.FEMALE;
  } else if (stringCompare(gender, Gender.MALE.value)) {
    return Gender.MALE;
  } else if (stringCompare(gender, Gender.TRANSGENDER.value)) {
    return Gender.TRANSGENDER;
  }
  return Gender.NOT_SPECIFIED;
};

export const isMale = gender => !isEmpty(gender) && stringCompare(Gender.MALE.key, gender.key);

export const isTransgender = gender => !isEmpty(gender) && stringCompare(Gender.TRANSGENDER.key, gender.key);

export const isFemale = (gender) =>
  !isEmpty(gender) && stringCompare(Gender.FEMALE.key, gender.key);

export const isGenderEqual = (gender1, gender2) => {
  if (isEmpty(gender1) || isEmpty(gender2)) {
    return false;
  }
  return stringCompare(gender1.key, gender2.key);
};

export default Gender;
