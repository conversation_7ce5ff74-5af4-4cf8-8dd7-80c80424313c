import find from 'lodash/find';
import {stringCompare} from '../TravelerDetails/TravelerDetailsUtils';

const Meal = Object.freeze({
  NO: {
    key: 'D',
    value: 'no_food',
  },
  VEG: {
    key: 'V',
    value: 'veg_food',
  },
  NON_VEG: {
    key: 'N',
    value: 'non_veg_food',
  },
});

export const getMealPreferenceOptions = (foodDetails) =>
  Object.values(Meal).map((meal) => ({
    id: meal.key,
    text: meal.value,
    obj: meal,
  })).filter((meal) => !foodDetails || foodDetails.includes(meal.id));

export const getMealObject = mealKey => find(Meal, value => stringCompare(value.key, mealKey));


export default Meal;

