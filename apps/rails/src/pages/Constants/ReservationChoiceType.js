import find from 'lodash/find';
import {stringCompare} from '../TravelerDetails/TravelerDetailsUtils';
import {NO_RESERVATION_CHOICE_CODE} from '../../Utils/RailsConstant';


const ReservationChoiceType = Object.freeze({
  BookIfAllBerthsAllottedInSameCoach: {
    key: 'all_berth_same_coach',
    value: '1',
    warning: 'all_berth_same_coach_warning',
    id: 'all_berth_same_coach',
  },
  BookIfAtLeastOneLowerBerthsAllotted: {
    key: 'at_least_one_lb',
    value: '2',
    warning: 'at_least_one_lb_warning',
    id: 'at_least_one_lb',
  },
  BookIfTwoLowerBerthsAllotted: {
    key: 'only_if_two_lb',
    value: '3',
    warning: 'only_if_two_lb_warning',
    id: 'only_if_two_lb',
  },
  None: {
    key: 'no_preferences',
    value: NO_RESERVATION_CHOICE_CODE,
    warning: '',
    id: 'no_preferences',
  },
  BookConfirmAndNone: {
    key: 'book_confirmed_berths',
    value: '4',
    warning: '',
    id: 'book_confirmed_berths',
  },
  BookConfirmAndBookIfAllBerthsAllottedInSameCoach: {
    key: 'same_coach_and_confirmed',
    value: '5',
    id: 'same_coach_and_confirmed',
  },
  BookConfirmAndBookIfAtLeastOneLowerBerthsAllotted: {
    key: 'one_lower_and_confirmed',
    value: '6',
    id: 'one_lower_and_confirmed',

  },
  BookConfirmAndBookIfTwoLowerBerthsAllotted: {
    key: 'two_lower_and_confirmed',
    value: '7',
    id: 'two_lower_and_confirmed',
  },
});

export const ReservationChoiceValuesDefault = [ReservationChoiceType.None,
  ReservationChoiceType.BookIfAllBerthsAllottedInSameCoach,
  ReservationChoiceType.BookIfAtLeastOneLowerBerthsAllotted,
  ReservationChoiceType.BookIfTwoLowerBerthsAllotted];

export const ReservationChoiceValuesWithoutLowerBerth = [ReservationChoiceType.None,
  ReservationChoiceType.BookIfAllBerthsAllottedInSameCoach];

export const getRsChoiceByValue = value => find(ReservationChoiceType, choice => stringCompare(choice.value, value));

export default ReservationChoiceType;
