/* eslint-disable */
import React, {Component} from 'react';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import isEmpty from 'lodash/isEmpty';
import fecha from 'fecha';
import { View} from 'react-native';
import RailsDropDown from '../Common/RailsDropdown';
/* eslint-disable max-len */
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker.js';
import {JourneyDetailsCard} from '../Common/JourneyDetailsCard';
import {addDays} from '@mmt/legacy-commons/Helpers/dateHelpers';
import AvailabilityStatusChange from '../Review/AvailabilityStatusChange';
import { _label } from '../../vernacular/AppLanguage';
import { AvailabilitySubscription } from '../TravelerDetails/Components/AvailabilitySubscription';

export default class TrainDetailsCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      options: this._getBoardingOptions(props.boardingStationList, props.selectedTrainInfo.departureDateAndTime),
      boardingTime: null,
    };
  }


  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showBoardingStationChange) {
      if (this.props.boardingStationList !== nextProps.boardingStationList) {
        this.setState({
          options: this._getBoardingOptions(nextProps.boardingStationList, nextProps.selectedTrainInfo.departureDateAndTime),
        });
      }
    }
  }


  componentDidMount() {
    if (this.props?.statusChangeAlert && !isEmpty(this.props.statusChangeAlert)) {
      trackClickEventProp61(
        RAIL_EVENTS.RAILS_TG_ELIGIBILITY_CHANGE.PAGE_NAME,
        RAIL_EVENTS.RAILS_TG_ELIGIBILITY_CHANGE.RAILS_TG_ELIGIBILITY_CHANGE,
      );
      this.props?.reviewAvailablityChange();
    }
  }

  _getBoardingOptions = (boardingStationList, departureDateAndTime) =>
    map(boardingStationList, (value) => {
      const time = fecha.format(fecha.parse(value.departureTime, 'HH:mm'), 'h:mm A');
      const day = departureDateAndTime ? fecha.format(addDays(departureDateAndTime, value.dayCount - boardingStationList[0].dayCount), 'D MMM') : '';
      const text = `${value.stationCode} (${value.stationName}) at ${time}, ${day}`;
      return ({
        id: value.stationCode,
        text,
        obj: value,
      });
    });

  render() {
    const {
      selectedPickupStation, selectedAvlStatus,selectedTrainInfo,
      selectedQuota, classValue, boardingStationList, showBoardingStationChange,
      onAvailabilityLayout,
    } = this.props;

    return (
      <View testID="train_details_card_container">
        <JourneyDetailsCard
          id={`${this.props?.id}_journeyDetails`}
          trainData={selectedTrainInfo}
          selectedAvlStatus={selectedAvlStatus}
          selectedQuota={selectedQuota}
          classValue={classValue}
          selectedTrainInfo={selectedTrainInfo}
          boardingStationList={boardingStationList}
          showBoardingStationChange={showBoardingStationChange}
          onAvailabilityLayout={onAvailabilityLayout}
          availabilityObject={this.props.availabilityObject}
          page={this.props.page}
          isUsingUpdatedAvailability={this.props.isUsingUpdatedAvailability}
        />
        <AvailabilityStatusChange statusChangeAlert = {this.props.statusChangeAlert}/>
        {this.props.showAvailSubscrnWidget && <AvailabilitySubscription />}
        {showBoardingStationChange && !isEmpty(this.state.options) && selectedPickupStation &&
        <RailsDropDown
          id={`${this.props?.id}_boardingStation`}
          adjustPosition={false}
          selected={selectedPickupStation.stationName}
          stationCode={selectedPickupStation.stationCode}
          options={this.state.options}
          selectedTrainInfo={selectedTrainInfo}
          boardingStationList={boardingStationList}
          selectedPickupStation={selectedPickupStation}
          onSelect={this.props.onBoardingStationClicked}
            changeLabel={_label('change')}
            logTravellerBoardingStationChangeClick={this.props?.logTravellerBoardingStationChangeClick}
        />
        }
      </View>
    );
  }
}
TrainDetailsCard.propTypes = {
  selectedTrainInfo: PropTypes.object.isRequired,
  showBoardingStationChange: PropTypes.bool,
  reviewSummaryData: PropTypes.object,
  selectedPickupStation: PropTypes.object,
  onBoardingStationClicked: PropTypes.func,
  boardingStationList: PropTypes.array,
  showPickChange: PropTypes.bool,
  durationFromBoardingStation: PropTypes.string,
  statusChangeAlert: PropTypes.object,
  reviewAvailablityChange: PropTypes.func,
  selectedAvlStatus: PropTypes.string,
  selectedQuota: PropTypes.string,
  classValue: PropTypes.string,
  onAvailabilityLayout: PropTypes.func,
  id: PropTypes.string,
  availabilityObject: PropTypes.object,
  page: PropTypes.string,
  showAvailSubscrnWidget: PropTypes.bool,
  logTravellerBoardingStationChangeClick: PropTypes.func,
};

TrainDetailsCard.defaultProps = {
  showBoardingStationChange: false,
  selectedPickupStation: null,
  onBoardingStationClicked: null,
  boardingStationList: [],
  showPickChange: false,
  durationFromBoardingStation: '',
};
