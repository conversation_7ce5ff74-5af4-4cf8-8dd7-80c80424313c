import terribleEmpty from '@mmt/legacy-assets/src/ic-1-empty.webp';
import terribleFilled from '@mmt/legacy-assets/src/ic-1-filled.webp';

import badEmpty from '@mmt/legacy-assets/src/ic-2-empty.webp';
import badFilled from '@mmt/legacy-assets/src/ic-2-filled.webp';

import okayEmpty from '@mmt/legacy-assets/src/ic-3-empty.webp';
import okayFilled from '@mmt/legacy-assets/src/ic-3-filled.webp';

import goodEmpty from '@mmt/legacy-assets/src/ic-4-empty.webp';
import goodFilled from '@mmt/legacy-assets/src/ic-4-filled.webp';

import greatEmpty from '@mmt/legacy-assets/src/ic-5-empty.webp';
import greatFilled from '@mmt/legacy-assets/src/ic-5-filled.webp';

const ratingConst = Object.freeze({
  TERRIBLE: {
    key: 1,
    emptyImg: terribleEmpty,
    filledImg: terribleFilled,
    value: 'Terrible',
  },
  BAD: {
    key: 2,
    emptyImg: badEmpty,
    filledImg: badFilled,
    value: 'Bad',
  },
  OKAY: {
    key: 3,
    emptyImg: okayEmpty,
    filledImg: okayFilled,
    value: 'Okay',
  },
  GOOD: {
    key: 4,
    emptyImg: goodEmpty,
    filledImg: goodFilled,
    value: 'Good',
  },
  GREAT: {
    key: 5,
    emptyImg: greatEmpty,
    filledImg: greatFilled,
    value: 'Great',
  },
});


export const getRatingConst = (rating) => {
  if (rating === ratingConst.TERRIBLE.key) {
    return ratingConst.TERRIBLE;
  } else if (rating === ratingConst.BAD.key) {
    return ratingConst.BAD;
  } else if (rating === ratingConst.OKAY.key) {
    return ratingConst.OKAY;
  } else if (rating === ratingConst.GOOD.key) {
    return ratingConst.GOOD;
  }
  return ratingConst.GREAT;
};
export default ratingConst;
