import map from 'lodash/map';
import { _label } from '../../vernacular/AppLanguage';

export const countries = Object.freeze({
  IN: {
    name: 'india',
    code: 'IN',
  },
});

export const getNationalityOptions = () => {
  let options = [];
  try {
    options = map(countries, cn => ({
      id: cn.code,
      text: _label(cn.name),
      obj: cn,
    }));
  } catch (e) {
    console.log('Error in getNationalityOptions :', e);
  }
  return options;
};
