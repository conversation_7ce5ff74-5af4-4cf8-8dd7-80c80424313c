import {stringCompare} from '../TravelerDetails/TravelerDetailsUtils';
import isEmpty from 'lodash/isEmpty';
import isNull from 'lodash/isNull';
import find from 'lodash/find';

const Berth = Object.freeze({
  UPPER_BERTH: {
    key: 'UB',
    value: 'upper',
  },
  LOWER_BERTH: {
    key: 'LB',
    value: 'lower',
  },
  SIDE_LOWER: {
    key: 'SL',
    value: 'side_lower',
  },
  SIDE_UPPER: {
    key: 'SU',
    value: 'side_upper',
  },
  MIDDLE_BERTH: {
    key: 'MB',
    value: 'middle',
  },
  WINDOW_SEAT: {
    key: 'WS',
    value: '$window',
  },
  SIDE_MIDDLE: {
    key: 'SM',
    value: 'side_middle',
  },
  CABIN: {
    key: 'CB',
    value: 'cabin',
  },
  COUPE: {
    key: 'CP',
    value: 'coupe',
  },
  NO_BERTH: {
    id: 'NONE',
    key: 'NONE',
    value: 'no_berth_preference',
    obj: {
      key: 'NONE',
      value: 'no_berth_preference',
    },
  },
  NO_BERTH_PREF: {
    id: 'NONE',
    text: 'no_berth_preference',
    key: 'NONE',
    obj: {
      key: 'NONE',
      value: 'no_berth_preference',
    },
  },
});

export const BerthText = Object.freeze({
  LB: 'lower_berth',
  MB: 'middle_berth',
  UB: 'upper_berth',
  SL: 'side_lower',
  SU: 'side_upper',
  NONE: 'no_berth_preference',
  WS: 'window_seat',
  SM: 'side_middle',
  CB: 'cabin',
  CP: 'coupe',
});

export const BerthList = Object.freeze([
  {
    key: 'LB',
    value: 'lower',
  },
  {
    key: 'MB',
    value: 'middle',
  },
  {
    key: 'UB',
    value: 'upper',
  },
  {
    key: 'SL',
    value: 'side_lower',
  },
  {
    key: 'SU',
    value: 'side_upper',
  },
  {
    key: '',
    value: 'none',
  },
]);

export const childBerthOptions = [
  {
    value: true,
    label: 'reserve_berth',
  },
  {
    value: false,
    label: 'dont_reserve_berth',
  },
];

export const isEqualsBerth = (berth1, berth2) => stringCompare(berth1.key, berth2.key);

export const getBerthType = (berthType) =>
  find(Berth, (value) => stringCompare(value.key, berthType)) ?? {
    key: berthType,
    value: berthType,
  };

export const getBerthPreferenceOptions = (applicableBerthTypes) => {
  let options = [];
  try {
    if (isEmpty(applicableBerthTypes) || isNull(applicableBerthTypes)) {
      return options;
    }
    for (let i = 0; i < applicableBerthTypes.length; i++) {
      const berth = getBerthType(applicableBerthTypes[i]);
      const ot = {
        id: berth.key,
        text: berth.value,
        obj: berth,
      };
      options.push(ot);
    }
  } catch (e) {
    console.log('Error in getBerthPreferenceOptions : ', e);
  }
  return options;
};

export default Berth;
