import {StyleSheet} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../../vernacular/VernacularUtils';

export const textStyle = {
    // currently all are same as regular for trainTravelBetweenText, title, option, optionSub
    getTitleFontStyle : () => {
      return fontStyle('regular');
    },
};
const StationChangeAlertStyle = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: 16,
    height: 300,

  },
  title: {
    color: colors.black,
    fontSize: 30,
    marginVertical: 8,
  },
  optionContainer: {
    flexDirection: 'row',
    padding: 8,
    marginVertical: 4,
    justifyContent: 'space-between',
  },
  option: {
    color: colors.black,
    fontSize: 16,
    marginLeft: 8,
  },
  optionSub: {
    color: colors.black,
    marginLeft: 8,
    fontSize: 14,
  },
  okGoAheadButton: {
    fontSize: 18,
    padding: 2,
    marginRight: 10,
    color: colors.azure,
  },

  trainTravelBetweenText: {
    fontSize: 12,
    color: colors.black,
  },


});

export default StationChangeAlertStyle;
