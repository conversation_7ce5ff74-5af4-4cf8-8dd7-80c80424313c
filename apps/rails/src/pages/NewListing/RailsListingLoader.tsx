import React from 'react';
import { View, Platform, ScrollView } from 'react-native';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';
const dummyLoaderPromise = Promise.resolve();

const placeholderBg = '#f1f1f1';

const Shimmer = (props) => {
  if (Platform.OS === 'web') {
    const {...propsWithoutReplace } = props;
    return <div className="placeholder-shimmer" {...propsWithoutReplace} />;
  }
  return null;
};

const classTypes = ['2SGN', 'SLGN', '3AGN', '2AGN', '1AGN'];

const RailsListingLoaderCard = () => (
  <Card
    style={{
      height: 240,
      overflow: 'hidden',
      paddingTop: 16,
      paddingBottom: 10,
      paddingHorizontal: 16,
      marginVertical: 5,
      marginHorizontal: 0,
    }}
  >
      <View>
        <View style={{ flexDirection: 'row', paddingVertical: 4, justifyContent: 'space-between' }}>
          <Shimmer
            style={{ height: 16, width: 130, backgroundColor: placeholderBg, borderRadius: 4 }}
            replace
            loader={dummyLoaderPromise}
          />
          <Shimmer
            style={{
              height: 16,
              width: 60,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
        </View>
        <View
          style={{
            flexDirection: 'row',
            marginTop: 12,
            paddingVertical: 2,
            justifyContent: 'space-between',
          }}
        >
          <Shimmer
            style={{
              height: 12,
              width: 64,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
          <Shimmer
            style={{
              height: 12,
              width: 56,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
          <Shimmer
            style={{
              height: 12,
              width: 65,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
        </View>
        <View style={{ flexDirection: 'row', marginTop: 10, justifyContent: 'space-between' }}>
          <Shimmer
            style={{
              height: 16,
              width: 130,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
          <Shimmer
            style={{
              height: 16,
              width: 130,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
        </View>
        <View style={{ marginTop: 10, marginBottom: 16 }}>
          <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
            {classTypes.map((classtype) => (
              <View key={classtype} style={{ marginLeft: 2, marginRight: 8 }}>
                <Shimmer
                  style={{
                    height: 90,
                    width: 150,
                    backgroundColor: placeholderBg,
                    borderRadius: 4,
                  }}
                  replace
                  loader={dummyLoaderPromise}
                />
              </View>
            ))}
          </ScrollView>
        </View>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 6 }}>
          <Shimmer
            style={{
              height: 12,
              width: 95,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
          <Shimmer
            style={{
              height: 12,
              width: 95,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
        </View>
      </View>
  </Card>
);

const RailsListingLoader = () => (
  <View>
    <RailsListingLoaderCard />
    <RailsListingLoaderCard />
    <RailsListingLoaderCard />
    <RailsListingLoaderCard />
    <RailsListingLoaderCard />
    <RailsListingLoaderCard />
    <RailsListingLoaderCard />
    <RailsListingLoaderCard />
  </View>
);

export const RailsListingLoaderWithHeader = () => (
  <View>
    <RailsListingHeaderLoader />
    <RailsListingLoader />
  </View>
);

export const RailsListingHeaderLoader = () => (
  <Card
    style={{
      height: 53,
      overflow: 'hidden',
      margin: 9,
      marginBottom: 12,
      width: '100%',
      backgroundColor: colors.grey13,
    }}
  >
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
        <View style={{ flexDirection: 'row' }}>
          <View
            style={{ height: '100%', width: 48, justifyContent: 'center', alignItems: 'center' }}
          >
            <Shimmer
              style={{
                height: 16,
                width: 16,
                backgroundColor: placeholderBg,
                borderRadius: 4,
              }}
              replace
              loader={dummyLoaderPromise}
            />
          </View>
          <View style={{ height: '100%', justifyContent: 'center', width: 160 }}>
            <Shimmer
              style={{
                height: 14,
                width: 160,
                backgroundColor: placeholderBg,
                borderRadius: 4,
              }}
              replace
              loader={dummyLoaderPromise}
            />
            <View style={{ height: 5 }} />
            <Shimmer
              style={{
                height: 11,
                width: 100,
                backgroundColor: placeholderBg,
                borderRadius: 4,
              }}
              replace
              loader={dummyLoaderPromise}
            />
          </View>
        </View>
        <View
          style={{
            height: '100%',
            width: 60,
            paddingTop: 10,
            paddingHorizontal: 20,
            marginLeft: 'auto',
          }}
        >
          <Shimmer
            style={{
              height: 35,
              width: 20,
              backgroundColor: placeholderBg,
              borderRadius: 4,
            }}
            replace
            loader={dummyLoaderPromise}
          />
        </View>
      </View>
  </Card>
);

export default RailsListingLoader;

Shimmer.propTypes = {
  replace: PropTypes.bool,
};
