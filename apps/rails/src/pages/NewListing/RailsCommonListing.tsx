import React, { useState, useEffect, useCallback } from 'react';
import RailsListingWrapper from './RailsListingWrapper';
import { connect } from 'react-redux';
import {
  railsListingSearchParamsChanged,
  initState,
  railsListingBackPress,
  initOmniture,
} from './RailsListingActions';
import { AbConfigKeyMappings, getPokusConfig, getPokusConfigWaitingPromise } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { getNecessaryStationDetails } from './Utils/RailListingUtils';
import RailsHeader from './Containers/RailsHeaderContainer';
import { BackHandler, View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../vernacular/AppLanguage';
import BusSharedModuleHolder from '@mmt/bus-shared/src';
import PropTypes from 'prop-types';
import {
  removeEventFromEvar99Variable,
  trackEvar71Event,
  trackGenericEvar99Event,
} from '../../railsAnalytics';
import { ARP_ALERT, ARP_ALERT_EVAR99, LISTING_PAGE_TRACKING_KEY } from '../../Utils/RailsConstant';

const RailsCommonListing = (props) => {
  const [crossSellEnabled, setCrossSellEnabled] = useState(false);
  const [init, setInit] = useState(false);
  const [showHeader, setShowHeader] = useState(false);
  const [params, setParams] = useState({
    ...{ ...props, ...{ openUniversalWidget: props?.openWidget, crossSellEnabled } },
  });
  useEffect(() => {
    // pokus
    const initAb = async () => {
      props?.initOmniture();
      await getPokusConfigWaitingPromise(1000);
      const crossSellPokus = await getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.railsBusTrainCrossSell,
        false,
      );
      const shouldSwitchToBusIfRailsIsPrimaryAndDESPokus = await getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings.desRedirection,
        true,
      );
      setParams({
        ...params,
        crossSellEnabled: crossSellPokus,
      });
      if (crossSellPokus) {
        const {
          originStation,
          destinationStation,
          departureDate,
        } = await getNecessaryStationDetails({
          ...props,
          ...{ crossSellEnabled: crossSellPokus },
        });
        props?.initState(originStation, destinationStation, departureDate, true);
        setShowHeader(true);
        props?.railsListingSearchParamsChanged(false);
        setParams({
          ...{
            ...props,
            ...{
              openUniversalWidget: props?.openWidget,
              crossSellEnabled: crossSellPokus,
              originStation,
              destinationStation,
              departureDate,
              shouldSwitchToBusIfRailsIsPrimaryAndDES: shouldSwitchToBusIfRailsIsPrimaryAndDESPokus,
            },
          },
        });
      }
      setCrossSellEnabled(crossSellPokus);
      setInit(true);
    };
    initAb();
    BackHandler.addEventListener('hardwareBackPress', onBack);

    if (props?.setAlert) {
      trackEvar71Event(LISTING_PAGE_TRACKING_KEY, ARP_ALERT);
      removeEventFromEvar99Variable(ARP_ALERT_EVAR99);
      trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY, ARP_ALERT_EVAR99);
    }
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onBack);
    };
  }, []);

  const onBack = () => {
    props?.setCrossSellPrimaryLob('bus');
    props?.railsListingBackPress();
    return true;
  };

  const translator = useCallback(_label, []);
  if (init) {
    const busSharedResourceProvider = BusSharedModuleHolder.get();
    if (!busSharedResourceProvider) {
      throw new Error('Bus-Shared module not bootstrapped');
    }
    const { BusTrainCrossSell } = busSharedResourceProvider.getBusSharedComponents();
    return crossSellEnabled ? (
      <BusTrainCrossSell
        routerState={params}
        primaryLob={'train'}
        translator={translator}
      />
    ) : (
      <View style={{ flex: 1 }}>
        <RailsListingWrapper {...params} />
      </View>
    );
  } else {
    if (showHeader) {
      return (
        <>
          <RailsHeader
            onBack={onBack}
            postPaymentBooking={props?.postPaymentBooking}
            page="newListing"
          />
          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <Spinner size={30} color={colors.azure} />
          </View>
        </>
      );
    } else {
      return (
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <Spinner size={30} color={colors.azure} />
        </View>
      );
    }
  }
};

RailsCommonListing.propTypes = {
  initOmniture: PropTypes.func,
  railsListingSearchParamsChanged: PropTypes.func,
  initState: PropTypes.func,
  railsListingBackPress: PropTypes.func,
  setCrossSellPrimaryLob: PropTypes.func,
  postPaymentBooking: PropTypes.bool,
  openWidget: PropTypes.bool,
  setAlert: PropTypes.bool,
};


const mapStateToProps = ({ railsListing: { originStation: railsOrigin, destinationStation: railsDestination } }) => {
  return { railsOrigin, railsDestination };
};
function getBusSharedModule() {
  const busSharedModule = BusSharedModuleHolder.get();
  if (busSharedModule === null) {
    throw 'Bus-Shared not bootstrapped';
  }
  return busSharedModule;
}
const mapDispatchToProps = (dispatch) => {
  const busSharedModule = getBusSharedModule();
  const {
      setCrossSellPrimaryLob,
  } = busSharedModule.getBusSharedListingActions();
  return {
    railsListingSearchParamsChanged: (noResolving: boolean) =>
      dispatch(railsListingSearchParamsChanged(noResolving)),
    initState: (origin: unknown, destination: unknown, departureDate: unknown, isLoading: boolean) =>
      dispatch(initState(origin, destination, departureDate, isLoading)),
    railsListingBackPress: () => dispatch(railsListingBackPress()),
    setCrossSellPrimaryLob: (value: string) => dispatch(setCrossSellPrimaryLob(value)),
    initOmniture: () => dispatch(initOmniture()),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(RailsCommonListing);
