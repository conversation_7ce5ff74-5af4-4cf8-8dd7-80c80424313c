import { MEAL_INFO_LOTTIE_URL } from '@mmt/rails/src/Utils/RailsConstant';

class LottieDataProvider {
  static animationData = null;
  static lottieUrl = MEAL_INFO_LOTTIE_URL;
  static async getLottieData() {
    try {
      if (!this.animationData) {
        await this.setLottieData();
      }
      return this.animationData;
    } catch (error) {
      console.error('Error in getLottieData:', error);
      return null;
    }
  }
  static async setLottieData() {
    try {
      const response = await fetch(this.lottieUrl);
      const data = await response.json();
      this.animationData = data;
    } catch (error) {
      console.error('Error fetching Lottie data:', error);
    }
  }
}
export default LottieDataProvider;
