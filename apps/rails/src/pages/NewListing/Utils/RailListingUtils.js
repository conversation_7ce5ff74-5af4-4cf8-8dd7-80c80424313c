import { NativeModules, Platform } from 'react-native';
import fecha from 'fecha';
import {
  getArrivalDate,
  getDepartureDate,
  getDepartureDateAndTime,
  getArrivalDateAndTime,
} from '../readers/newTrainInfo.reader';
import { getQueryParamsFromUrl, getDateFromURLFormat } from '@mmt/legacy-commons/Helpers/misc';
import { getStationDetailsFromLocus } from '../../RailsLandingPage/Store/RailsLandingPageActions';
import { filterAllStationsString } from '@mmt/legacy-commons/Common/utils/railsSharableUtils';
import { getHeaderSubtitle, getHeaderTitle } from '../../../Utils/HeaderContentUtil';
import isEmpty from 'lodash/isEmpty';
import { isUndefined } from 'lodash';


export const addMinutes = (date, minutes) => {
  return new Date(date.getTime() + minutes * 60000);
};

export const getImageSign = (originStation, destinationStation, currentTrain) => {
  const sources = [originStation.code, currentTrain.frmStnCode];
  const destinations = [destinationStation.code, currentTrain.toStnCode];

  const source = currentTrain.frmStnCode;
  const destination = currentTrain.toStnCode;


  let sign = '';

  for (let i = 0; i < sources.length; i += 1) {
    if (source === sources[i]) {
      sign = `${sign}S${i}`;
      break;
    }
  }

  for (let i = 0; i < destinations.length; i += 1) {
    if (destination === destinations[i]) {
      sign = `${sign}D${i}`;
      break;
    }
  }
  return sign;
};

export const convertMinsToHrsMins = (minutes) => {
  let roundedHrs = Math.floor(minutes / 60);
  let roundedMinutes = minutes % 60;
  roundedHrs = roundedHrs < 10 ? `0${roundedHrs}` : roundedHrs;
  roundedMinutes = roundedMinutes < 10 ? `0${roundedMinutes}` : roundedMinutes;
  return `${roundedHrs}h ${roundedMinutes}m`;
};

export const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

export const updateTimeInfoInSelectedTrain = (currentTrain, selectedDate) => {
  if (isUndefined(selectedDate) || isEmpty(selectedDate)) {
    return currentTrain;
  }
  let trainInfo = currentTrain;
  try {
    const hoursAndMinutes = currentTrain.departureTime.split(':');
    const hours = hoursAndMinutes[0];
    const minutes = hoursAndMinutes[1];

    const departureDateAndTime = fecha.parse(selectedDate, 'DD-MM-YYYY');
    departureDateAndTime.setHours(hours);
    departureDateAndTime.setMinutes(minutes);
    trainInfo.departureDateAndTime = departureDateAndTime;
    trainInfo.arrivalDateAndTime = addMinutes(trainInfo.departureDateAndTime, trainInfo.duration);
  } catch (e) {
    console.log('Error in extractDepartureDateAndTime : ', e);
  }
  return trainInfo;
};

export function getFormattedDates(trainData) { // returns in format of DD MMM e.g, 7 Aug
  try {
    let formattedDepartureDate;
    let formattedArrivalDate;
    if (getDepartureDateAndTime(trainData)) {
      formattedDepartureDate = fecha.format(getDepartureDateAndTime(trainData), 'D MMM');
      formattedArrivalDate = fecha.format(getArrivalDateAndTime(trainData), 'D MMM');
    } else {
      const [departureDate, departureMonth] = getDepartureDate(trainData).split('-');
      const [arrivalDate, arrivalMonth] = getArrivalDate(trainData).split('-');
      formattedDepartureDate = `${Number(departureDate)} ${months[Number(departureMonth) - 1]}`;
      formattedArrivalDate = `${Number(arrivalDate)} ${months[Number(arrivalMonth) - 1]}`;
    }

    return {
      formattedDepartureDate,
      formattedArrivalDate,
    };
  } catch (e) {
    console.log('Error in getFormattedDates : ', e);
    return {
      formattedDepartureDate: '',
      formattedArrivalDate: '',
    };
  }
}

function nativeLog(data) {
  if (NativeModules && NativeModules.GenericModule && NativeModules.GenericModule.logBreadCrumb && typeof NativeModules.GenericModule.logBreadCrumb === 'function') {
    const JS_VERSION = '24.0';
    const crumb = `RN ${data}`;
    console.log(crumb);
    NativeModules.GenericModule.logBreadCrumb(crumb, JS_VERSION);
  }
}

export function getValidDate(d) {
  nativeLog(`get valid date argument ${d}`);
  const parsedDate = d ? new Date(d) : new Date(); // if date is null then new Date(d) will return 1970 date. but we should show current day
  if (parsedDate.getDate()) {
    nativeLog(`get valid date return ${parsedDate}`);
    return parsedDate;
  }
  nativeLog(`get valid date return ${parsedDate}`);
  return new Date();
}

export function addDaysToDate(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export const getNecessaryStationDetails = async (data) => {
  let originStation = {}, destinationStation = {}, departureDate, locusData;
  const deeplinkCheck = (Platform.OS === 'ios' ? (data?.from_locus_id && data?.to_locus_id) : data?.deep_link_intent_url);
  if (deeplinkCheck && isEmpty(data?.originStation) && isEmpty(data?.destinationStation)) {
    let queryParams = getQueryParamsFromUrl(data?.deep_link_intent_url);
    const { from_locus_id, to_locus_id } = (Platform.OS === 'ios' ? data : queryParams);
    if (!isEmpty(from_locus_id) && !isEmpty(to_locus_id)) {
      try {
        locusData = await getStationDetailsFromLocus({ from_locus_id, to_locus_id });
        queryParams.from = locusData?.fromLobCode;
        queryParams.to = locusData?.toLobCode;

        queryParams.from_city = locusData?.fromStationName;
        queryParams.from_station = locusData?.fromStationName;
        queryParams.from_locus_id = locusData?.fromLocusCode;

        queryParams.to_city = locusData?.toStationName;
        queryParams.to_station = locusData?.toStationName;
        queryParams.to_locus_id = locusData?.toLocusCode;
      } catch (error) {
        return { stationDetailsFromLocusError: true, originStation, destinationStation, departureDate };
      }
    }
    const origin = {
      code: queryParams.from,
      cityName: queryParams.from_city,
      stationName: queryParams.from_station,
      locusCode: queryParams.from_locus_id,
    };
    const destination = {
      code: queryParams.to,
      cityName: queryParams.to_city,
      stationName: queryParams.to_station,
      locusCode: queryParams.to_locus_id,
    };
    const departureDateFromURL = queryParams.departure || data?.departure;
    let date = data?.departureDate;
    if (departureDateFromURL && !date) {
      date = getDateFromURLFormat(departureDateFromURL);
    }
    originStation = origin;
    destinationStation = destination;
    departureDate = date || addDaysToDate(new Date(), 1);
  } else if (isEmpty(data?.originStation) && isEmpty(data?.destinationStation)) {// && data?.from && data?.to){
    const origin = {
      code: data?.from,
    };
    const destination = {
      code: data?.to,
    };
    let date = data?.departureDate || data?.departure;
    if (date) {
      date = getDateFromURLFormat(date);
    }
    originStation = origin;
    destinationStation = destination;
    departureDate = date || (new Date());
  }
  if (!isEmpty(data?.originStation) && !isEmpty(data?.destinationStation)) {
    originStation = data?.originStation;
    destinationStation = data?.destinationStation;
    departureDate = getValidDate(data?.departureDate);
  }
  const [from_locus_id, to_locus_id] = [originStation?.locusCode, destinationStation?.locusCode];
  if (!isEmpty(from_locus_id) && !isEmpty(to_locus_id)) {
    try {
      locusData = await getStationDetailsFromLocus({ from_locus_id, to_locus_id });
      originStation.code = locusData?.fromLobCode;
      destinationStation.code = locusData?.toLobCode;
    } catch (error) {
      return { stationDetailsFromLocusError: true, originStation, destinationStation, departureDate };
    }
  }

  return { originStation, destinationStation, departureDate, locusData };
};

export const getRailsListingHeaderTitleAndSubTitle = (railsOrigin, railsDestination, railsDepartureDate) => {
  try {
    const title =
    isEmpty(railsOrigin) ||
      isEmpty(railsDestination) ||
      isEmpty(railsOrigin?.code) ||
      isEmpty(railsDestination?.code)
      ? ''
      : filterAllStationsString(getHeaderTitle(railsOrigin, railsDestination));

    const subTitle = !railsDepartureDate
      ? ''
      : getHeaderSubtitle(railsDepartureDate);
    return [title, subTitle];
  } catch (e) {
    console.error('Error:', e.message);
    return ['Test', 'test'];
  }
};
