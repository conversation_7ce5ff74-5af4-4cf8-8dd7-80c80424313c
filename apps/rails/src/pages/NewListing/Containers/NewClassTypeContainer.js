import { connect } from 'react-redux';
import {onNewClassTypeClicked} from '../RailsListingActions';
import ClassBar from '../Components/NewSelectClassType';

const mapStateToProps = (state, ownProps) => {
  const {trainData} = ownProps;
  const {railsListing: {trainsListObject, trainsList}} = state;
  const selectedTrainData = trainsList.find(item => (item.trainNumber === trainData.trainNumber));
  const {selectedClassType, selectedQuota, quotaClassRate} =
    trainsListObject[trainData.trainNumber];
  return {
    trainData: selectedTrainData,
    selectedClassType,
    selectedQuota,
    quotaClassRate,
  };
};

const mapDispatchToProps = dispatch => ({
  onClassTypeClicked: (classTypeSelected, trainData) => {
    dispatch(onNewClassTypeClicked(classTypeSelected, trainData));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(ClassBar);
