import { connect } from 'react-redux';
import { sortAndFilterDispatchToProps } from './utils';
import JourneyClassFilters from '../../Components/Filters/JourneyClassFilters';

function mapStateToProps (state) {
  const {
    railsListing: {
      sortFilterVersion,
      stagedFilterObject,
      stagedTrainsList,
    },
  } = state;

  return {
    version: sortFilterVersion,
    journeyClassFilter: stagedFilterObject.journeyClassFilter,
    noTrainsFlag: stagedTrainsList.length === 0,
  };
}

const JourneyClassFiltersContainer = connect(
  mapStateToProps,
  sortAndFilterDispatchToProps,
)(JourneyClassFilters);
export default JourneyClassFiltersContainer;
