import { connect } from 'react-redux';
import { fireOmnitureEventsNew, onClearSelectedFilter, setFiltersAndTrainsList } from '../../../RailsFilters/RailsFilterActions';
import BottomsheetHeader from '../../Components/Filters/BottomsheetHeader';
import { hasFiltersApplied } from './utils';

function mapStateToProps(state) {
  const { railsListing: {
    stagedTrainsList,
    stagedFilterObject,
  }} = state;
  return {
    foundTrainsCount: stagedTrainsList.length,
    noTrainsFlag: stagedTrainsList.length === 0,
    hasFilters: hasFiltersApplied(stagedFilterObject),
  };
}

function mapDispatchToProps(dispatch) {
  return {
    onDoneClick: (location) => {
      fireOmnitureEventsNew(location);
      dispatch(setFiltersAndTrainsList(location));
    },
    onClearSelectedFilter: (filterItem , location) => {
      fireOmnitureEventsNew(location);
      dispatch(onClearSelectedFilter(filterItem , location));
    },
  };
}

const BottomsheetHeaderContainer = connect(mapStateToProps, mapDispatchToProps)(BottomsheetHeader);
export default BottomsheetHeaderContainer;
