import { connect } from 'react-redux';
import { hasFiltersApplied, sortAndFilterDispatchToProps } from './utils';
import FilterBottomsheet from '../../Components/Filters/FilterBottomSheets';
import { TrainTypes } from '../../../Types/TrainTypes';

const formatTrainTypes = (trainTypeFilter) => {
  return {
    ...trainTypeFilter,
    filterTypesArray: [
      ...trainTypeFilter.filterTypesArray.filter(trainType => trainType !== TrainTypes.O.key),
      ...trainTypeFilter.filterTypesArray.filter(trainType => trainType === TrainTypes.O.key),
    ],
  };
};

function mapStateToProps (state) {
  const {
    railsListing: {
      sortFilterVersion,
      stagedFilterObject,
      stagedTrainsList,
    },
  } = state;

  const {
    journeyClassFilter,
    trainTypeFilter,
    departureTimeFilter,
    arrivalTimeFilter,
    fromStnFilter,
    toStnFilter,
    quickFilter,
    ticketTypeFilter,
    quotaFilter,
    availabilityFilter,
  } = stagedFilterObject;

  return {
    version: sortFilterVersion,
    noTrainsFlag: stagedTrainsList.length === 0,
    journeyClassFilter,
    trainTypeFilter: formatTrainTypes(trainTypeFilter),
    departureTimeFilter,
    arrivalTimeFilter,
    fromStnFilter,
    toStnFilter,
    quickFilter,
    ticketTypeFilter,
    quotaFilter,
    availabilityFilter,
    foundTrainsCount: stagedTrainsList.length,
    hasFilters: hasFiltersApplied(stagedFilterObject),
  };
}

const FilterBottomsheetContainer = connect(
  mapStateToProps,
  sortAndFilterDispatchToProps,
)(FilterBottomsheet);
export default FilterBottomsheetContainer;
