import { connect } from 'react-redux';
import { sortAndFilterDispatchToProps } from './utils';
import AvailabilityFilters from '../../Components/Filters/AvailabilityFilters';

function mapStateToProps (state) {
  const {
    railsListing: {
      sortFilterVersion,
      stagedFilterObject,
      stagedTrainsList,
    },
  } = state;

  const { availabilityFilter } = stagedFilterObject;
  return {
    version: sortFilterVersion,
    availabilityFilter,
    noTrainsFlag: stagedTrainsList.length === 0,
  };
}

const AvailabilityFiltersContainer = connect(
  mapStateToProps,
  sortAndFilterDispatchToProps,
)(AvailabilityFilters);
export default AvailabilityFiltersContainer;
