import { connect } from 'react-redux';
import { sortAndFilterDispatchToProps } from './utils';
import TimingFilters from '../../Components/Filters/TimingFilters';

function mapStateToProps (state) {
  const {
    railsListing: {
      sortFilterVersion,
      stagedFilterObject,
      stagedTrainsList,
    },
  } = state;

  return {
    version: sortFilterVersion,
    departureTimeFilter: stagedFilterObject.departureTimeFilter,
    arrivalTimeFilter: stagedFilterObject.arrivalTimeFilter,
    noTrainsFlag: stagedTrainsList.length === 0,
  };
}

const TimingFiltersContainer = connect(
  mapStateToProps,
  sortAndFilterDispatchToProps,
)(TimingFilters);
export default TimingFiltersContainer;
