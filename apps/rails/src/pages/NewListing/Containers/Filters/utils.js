import {
  fireOmnitureEventsNew,
  onFilterClick,
  onQuickFilterClick,
  setFiltersAndTrainsList,
  onFilterCloseClickV2,
  onClearAllFiltersClicked,
  onClearSelectedFilter,
  onListingQuickFilterClick,
} from '../../../RailsFilters/RailsFilterActions';
import { onSortTrainClicked } from '../../RailsListingActions';

export const hasFiltersApplied = (filterObject) => {
  return Object.keys(filterObject).some((filterKey) =>
    Object.keys(filterObject[filterKey].filterTypes).some(
      (filterTypeKey) => filterObject[filterKey].filterTypes[filterTypeKey].selected,
    ),
  );
};

export const sortAndFilterDispatchToProps = (dispatch) => ({
  onSortTrainClicked: (selectedSortParam) => {
    dispatch(onSortTrainClicked(selectedSortParam));
  },
  onFilterClick: (filterItem, filterTypeObject) => {
    const { id } = filterTypeObject;
    const event = `bottomSheet_${id}_${filterItem}`;
    fireOmnitureEventsNew(event);
    dispatch(onFilterClick(filterItem, filterTypeObject));
  },
  onQuickFilterClick: (filterItem, filterTypeObject) => {
    dispatch(onQuickFilterClick(filterItem, filterTypeObject));
  },
  onDoneClick: (location) => {
    dispatch(setFiltersAndTrainsList(location));
  },
  onCloseClick: () => {
    dispatch(onFilterCloseClickV2());
  },
  onClearAllFiltersClicked: () => {
    dispatch(onClearAllFiltersClicked());
  },
  onClearSelectedFilter: (filterItem, location) => {
    dispatch(onClearSelectedFilter(filterItem, location));
  },
  onListingQuickFilterClick: (filterItem, filterTypeObject) => {
    dispatch(onListingQuickFilterClick(filterItem, filterTypeObject));
  },
});
