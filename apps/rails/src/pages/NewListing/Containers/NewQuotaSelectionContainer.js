import { connect } from 'react-redux';
import { getQuota } from '../../Types/QuotaType';
import {onNewQuotaClicked} from '../RailsListingActions';
import QuotaBar from '../Components/NewQuotaSelection';

const mapStateToProps = (state, ownProps) => {
  const { trainData = {} } = ownProps;
  const {
    railsListing: { availableQuotaList = [], trainsListObject = {} },
  } = state;
  const selectedQuota = trainsListObject?.[trainData?.trainNumber]?.selectedQuota;

  const quotaList = availableQuotaList?.map((item) => getQuota(item));
  return {
    ...ownProps,
    quotaList,
    selectedQuota,
  };
};

const mapDispatchToProps = dispatch => ({
  onQuotaClicked: (quotaSelected, trainData) => {
    dispatch(onNewQuotaClicked(quotaSelected, trainData));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(QuotaBar);
