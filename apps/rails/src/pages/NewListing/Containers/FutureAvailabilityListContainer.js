import isEmpty from 'lodash/isEmpty';
import { connect } from 'react-redux';
import {dynamicPricingTrainTypes} from '../../../Utils/RailsConstant';
import {onAvailabilityDateClicked} from '../RailsListingActions';
import FutureAvailabilityList from '../Components/FutureAvailabilityList';

const mapStateToProps = (state, ownProps) => {
  const {trainData} = ownProps;
  const trainsListObject = state?.railsListing?.trainsListObject || {};
  const {
    availabilityResponse, selectedDate, isLoading, error, errorMessage,
  } = trainsListObject[trainData?.trainNumber] || {};
  const availablityList = isEmpty(availabilityResponse) ? [] : availabilityResponse?.avlDayList;
  let isDynamicPriced = false;
  const {trainType} = trainData;
  dynamicPricingTrainTypes.forEach((type) => {
    if (trainType.includes(type)) {
      isDynamicPriced = true;
    }
  });
  return {
    ...ownProps,
    availablityList,
    selectedDate,
    isLoading,
    error,
    errorMessage,
    isDynamicPriced,
  };
};

const mapDispatchToProps = dispatch => ({
  onDateClick: (selectedDate, trainData) => {
    dispatch(onAvailabilityDateClicked(selectedDate, trainData));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(FutureAvailabilityList);
