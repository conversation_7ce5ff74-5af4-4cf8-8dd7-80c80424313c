import {connect} from 'react-redux';
import {onFilterIconClick, onListingQuickFilterClick} from '../../RailsFilters/RailsFilterActions';
import RailFilterBar from '../Components/RailFilterBar';
import { QUOTA_FILTER_KEY } from '../../RailsFilters/railsFilter.utils';
import { logListingPdtClickEvents } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import { LISTING_PDT_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

const mapStateToProps = (state) => {
  const {railsListing: {filterObject}} = state;
  const {quickFilter:{filterTypesArray}} = filterObject;
  const filterCount = Object.values(filterObject)
    .filter(filter => Object.values(filter.filterTypes)
      // type.key !== QUOTA_FILTER_KEY is added to exclude the quota selected from the quick filters object
      .some(type => type.key !== QUOTA_FILTER_KEY && type.selected)).length;
  return {
    filterTypesArray,
    filterObject,
    filterCount,
  };
};

const mapDispatchToProps = dispatch => ({
  onListingQuickFilterClick: (filterItem, filterTypeObject) => {
    dispatch(onListingQuickFilterClick(filterItem, filterTypeObject));
  },
  onFilterIconClick: (pageData = null) => {
    dispatch(onFilterIconClick(pageData));
    dispatch(logListingPdtClickEvents(LISTING_PDT_CLICK_EVENTS.LISTING_SORT_AND_FILTER_ICON_CLICK));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(RailFilterBar);
