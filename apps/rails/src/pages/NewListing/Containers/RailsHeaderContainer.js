import React from 'react';
import {connect} from 'react-redux';
import { Platform, View, StyleSheet } from 'react-native';

import TripHeaderWithEditOption from '@mmt/legacy-commons/Common/Components/Header/TripHeaderWithEditOption';
import TripHeaderWithEditOptionIos from '@mmt/legacy-commons/Common/Components/Header/TripHeaderWithEditOptionIos';
import { showHideEditWidget } from '../RailsListingActions';
import { trackClickEvent, trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { RailsListingHeaderLoader } from '../RailsListingLoader';
import { getRailsListingHeaderTitleAndSubTitle } from '../Utils/RailListingUtils';
import LanguageSwitcher from '../../Common/LanguageSwitcher';
import { _label } from '../../../vernacular/AppLanguage';
import PropTypes from 'prop-types';

const RailsHeaderContainer = (props) => {
  const {
    showLanguageSwitcher,
    originStation,
    destinationStation,
  } = props;
  const originStationCode = originStation?.code;
  const destinationStationCode = destinationStation?.code;
  const shouldShowHeader = originStationCode && destinationStationCode;
  const check = (!props?.isEditClicked && !props?.showEditWidget && !props?.noHeader && ((props?.title !== '' && props?.subTitle !== '') || shouldShowHeader));
  if (check) {
    return (
      <View style={styles.container}>
        <View style={styles.flex8}>
          {
            Platform.OS === 'ios'
            ? <TripHeaderWithEditOptionIos {...props} />
            : <TripHeaderWithEditOption {...props} />
          }
        </View>
        { !!showLanguageSwitcher && <LanguageSwitcher /> }
      </View>
    );
  } else {
    return (<RailsListingHeaderLoader />);
  }
};

RailsHeaderContainer.propTypes = {
  showLanguageSwitcher: PropTypes.bool,
  originStation: PropTypes.shape({
    code: PropTypes.string,
  }),
  destinationStation: PropTypes.shape({
    code: PropTypes.string,
  }),
  isEditClicked: PropTypes.bool,
  showEditWidget: PropTypes.bool,
  noHeader: PropTypes.bool,
  title: PropTypes.string,
  subTitle: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'row',
    ...Platform.select({ ios: { zIndex: 9 } }),
  },
  flex8: {
    flex: 8,
  },
});

const mapStateToProps = (state, ownProps) => {
  const {
    railsListing: {
      originStation,
      destinationStation,
      departureDate,
      isEditClicked,
      showEditWidget,
      isLoading,
      settingListingDataForTravellers,
    },
  } = state;
  const [title, subTitle] = ownProps?.noHeader
    ? ['', '']
    : getRailsListingHeaderTitleAndSubTitle(
        originStation,
        destinationStation,
        departureDate,
      );
  return {
    listing_edit: _label('edit'),
    title,
    subTitle,
    originStation,
    destinationStation,
    departureDate,
    isEditClicked,
    showEditWidget,
    showEditIcon: !isLoading && !settingListingDataForTravellers,
    ...ownProps,
  };
};

const mapDispatchToProps = (dispatch) => ({
  showHideEditWidget: (data) => {
    trackClickEvent('mob_rail_listing_v2','edit_search_clicked');
    trackClickEventProp61('mob_rail_listing_v2','rail_date_modify_edit');
    return dispatch(showHideEditWidget(data));},
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsHeaderContainer);
