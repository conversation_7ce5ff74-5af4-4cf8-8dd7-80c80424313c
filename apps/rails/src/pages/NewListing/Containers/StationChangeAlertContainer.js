import { connect } from 'react-redux';
import { availabilityCardClicked, closeClassAvailability } from '../RailsListingActions';
import StationChangeAlert from '../Components/StationChangeAlert';
import { _label } from '../../../vernacular/AppLanguage';

const getLabels = ({originStnCode, destinationStnCode}, train = {}) => {
  const frmStnCode = train.boardingStation ? train.boardingStation.code : train.frmStnCode;
  const frmStnName = train.boardingStation ? train.boardingStation.name : train.frmStnName;
  const toStnCode = train.droppingStation ? train.droppingStation.code : train.toStnCode;
  const toStnName = train.droppingStation ? train.droppingStation.name : train.toStnName;
  return ({
    listing_station_change_title: _label('station_change'),
    listing_station_change_header: _label('station_change_header', undefined, {
      source: originStnCode,
      destination: destinationStnCode,
    }),
    listing_station_change_subheader: _label('station_change_sub_header', undefined, {
      sourceStnCode: frmStnCode,
      sourceStnName: frmStnName,
      destStnCode: toStnCode,
      destStnName: toStnName,
    }),
    listing_back: _label('back'),
    listing_go_ahead: _label('ok_go_ahead'),
  });
};

const mapStateToProps = (state) => {
  const {
    railsListing: {
      originStation, destinationStation, selectedTrainInfo, selectedClassType, selectedQuota,
    },
  } = state;
  const {
    frmStnCode,
    toStnCode,
    stationChangeSign,
    toStnName,
    frmStnName,
    boardingStation,
    droppingStation,
  } = selectedTrainInfo || {};
  return {
    selectedTrainInfo,
    selectedClassType,
    selectedQuota,
    originStation,
    destinationStation,
    frmStnCode,
    toStnCode,
    stationChangeSign,
    frmStnName,
    toStnName,
    boardingStation,
    droppingStation,
    labels: getLabels({
      originStnCode: originStation.code,
      destinationStnCode: destinationStation.code,
    }, selectedTrainInfo),
  };
};

const mapDispatchToProps = dispatch => ({
  onBackClick: () => { dispatch(closeClassAvailability()); },
  goToTravelersPage: (className, trainInfo, quotaCode) =>
    { dispatch(availabilityCardClicked(className, trainInfo, quotaCode, true)); },
});

export default connect(mapStateToProps, mapDispatchToProps)(StationChangeAlert);
