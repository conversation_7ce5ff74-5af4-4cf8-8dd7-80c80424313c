import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, TextStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import {  colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { Actions } from '../../../../navigation';
import { TextStyleBE } from '../../../../types/railofy.types';
import { trackRailofyKnowMoreClick } from '../../../../Utils/railofyUtils';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';
import { refundWays } from '../../../TravelerDetails/Components/AssuredConfirmation/AssuredConfirmationUtils';
import SpendRefundToBook from '../../../TravelerDetails/Components/AssuredConfirmation/SpendRefundToBook';
import { _label } from '../../../../vernacular/AppLanguage';

// const bannerImage = require('../../../../../../Assets/railofy_banner_icon.webp');

interface RailofyBannerProps {
  data: {
    additionalText: string;
    additionalTextURL: string;
    backgroundColor: string[];
    bannerImageUrl: string;
    bannerTag: TextStyleBE;
    description: TextStyleBE[];
    headerText: string;
  }
  id?: string;
}

export default function RailofyBanner(props: RailofyBannerProps) {
  const {
    data: {
      additionalText,
      additionalTextURL,
      backgroundColor,
      bannerImageUrl,
      bannerTag,
      description,
      headerText,
    },
  } = props;

  const onMoreDetailsPressed = () => {
    trackRailofyKnowMoreClick('mob_rail_listing_v2');
    Actions.openWebView({
      url: additionalTextURL,
      headerText: 'MakeMyTrip',
      headerIcon: backIcon,
    });
  };

  // ============================

  const containerDefaultBg = ['#c86dd7', '#3023ae'];
  const tagDefaultBg = ['#ff7f3f', '#ff3e5e'];

  const bannerTagTextStyle: TextStyle = {
    ...fontStyle(bannerTag.fontFamily || 'regular'),
    fontSize: Number(bannerTag.fontSize) || 10,
    color: bannerTag.color,
    letterSpacing: Number(bannerTag.letterSpacing) || 0,
  };

  // ========================

  return (
    <View style={styles.container} testID={props?.id}>
      <LinearGradient // positioned absolute
        colors={bannerTag.backgroundColor || tagDefaultBg}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.bannerTag}
      >
        <Text style={bannerTagTextStyle}>{bannerTag.text}</Text>
      </LinearGradient>
      <LinearGradient
        colors={backgroundColor || containerDefaultBg}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.gradientContainer}
      >
        <Image source={{ uri: bannerImageUrl }} style={styles.imageStyle} />
        {/* <Image source={bannerImage} style={styles.imageStyle} /> */}
        <View style={styles.detailsContainer}>
          <Text style={[styles.heading, fontStyle('black'), getLineHeight(16)]}>{headerText}</Text>
          <Text style={{marginTop: 5}}>
            {description.map((descTextObj) => {
              const {
                text,
                color,
                fontSize,
                fontFamily,
                letterSpacing,
                nonBreakableText,
              } = descTextObj;
              const descTextStyle: TextStyle = {
                ...fontStyle(fontFamily || 'regular'),
                ...getLineHeight(Number(fontSize || 10)),
                color,
                fontSize: Number(fontSize || 10),
                letterSpacing: Number(letterSpacing) || 0,
              };
              if (nonBreakableText) {
                return <Text key={text.replace(/ /g, '')} style={descTextStyle}>{text.split(' ').join('\u00A0')} </Text>;
              }
              return <Text key={text.replace(/ /g, '')}style={descTextStyle}>{text} </Text>; // don't remove the space after }
            })}
          </Text>
          <View style={{flexDirection : 'row', marginTop: 10, flexWrap: 'wrap'}}>
            {refundWays.map((item,index) => {
              return <SpendRefundToBook
                icon = {item.icon}
                text = {_label(item.text)}
                key = {index}
                width = {item.width}
                height = {item.height}
                refundWaysTextStyle={{
                  marginRight: 10,
                  color: colors.white,
                  letterSpacing: 0,
                  ...getLineHeight(12),
                  ...StyleSheet.flatten(fontStyle('bold')),
                  ...StyleSheet.flatten(getLineHeight(12)),
                }}
              />;
            })}
        </View>
        <TouchableOpacity activeOpacity={0.6} onPress={onMoreDetailsPressed} style={styles.button}>
            <Text style={[styles.linkText, fontStyle('black'), getLineHeight(11)]}>
              {additionalText}
            </Text>
          </TouchableOpacity>
      </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.grayBg,
    position: 'relative',
    padding: 10,
    paddingTop: 18,
  },
  bannerTag: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    height: 24,
    position: 'absolute',
    top: 8,
    right: 28,
    paddingHorizontal: 10,
    zIndex: 99999,
  },
  gradientContainer: {
    paddingTop: 24,
    paddingBottom: 15,
    paddingHorizontal: 10,
    borderColor: colors.lightGray,
    borderWidth: 1,
    flexDirection: 'row',
    borderRadius: 5,
  },
  imageStyle: {
    height: 65,
    width: 65,
    marginRight: 10,
    marginTop: 3,
  },
  detailsContainer: {
    flex: 1,
  },
  heading: {
    fontSize: 16,
    color: colors.white,
    letterSpacing: 0,
  },
  button: {
    paddingVertical: 5,
  },
  linkText: {
    color: colors.white,
    fontSize: 11,
  },
});
