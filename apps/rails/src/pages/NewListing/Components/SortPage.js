import React from 'react';
import { Image, Text, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import SortType from '../../Types/SortType';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import tickIcon from '@mmt/legacy-assets/src/tick.webp';
import PropTypes from 'prop-types';

const SortPage = ({ onSortClicked, selectedSortParam, id }) => {
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: colors.white,
        flexDirection: 'column',
        marginHorizontal: 20,
      }}
      testID={id}
    >
      <View style={sortStyles.sortType}>
        <TouchableRipple onPress={() => onSortClicked(SortType.DEFAULT)}>
          <View style={sortStyles.sortField}>
            <Text
              style={
                selectedSortParam === SortType.DEFAULT
                  ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                  : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
              }
            >
              {_label('availability_default')}
            </Text>
          </View>
        </TouchableRipple>
        <View style={sortStyles.sortValue}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            <TouchableRipple onPress={() => onSortClicked(SortType.DEFAULT)} testID={`${id}_available_first`}>
              <View style={sortStyles.sortButton}>
                <Text
                    style={
                      selectedSortParam === SortType.DEFAULT
                          ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                          : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                    }
                >
                  {_label('available_ticket_first')}
                </Text>
              </View>
            </TouchableRipple>
            {selectedSortParam === SortType.DEFAULT ? (
                <View>
                  <Image source={tickIcon} style={{ width: 12, height: 12 }} />
                </View>
            ) : (
                <View style={{ width: 12, height: 12 }} />
            )}
          </View>
        </View>
      </View>
      <View style={sortStyles.sortType}>
        <TouchableRipple onPress={() => onSortClicked(SortType.DurationFastest)}>
          <View style={sortStyles.sortField}>
            <Text
                style={
                  selectedSortParam === SortType.DurationFastest
                      ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                      : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                }
            >
              {_label('duration')}
            </Text>
          </View>
        </TouchableRipple>
        <View style={sortStyles.sortValue}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            <TouchableRipple onPress={() => onSortClicked(SortType.DurationFastest)} testID={`${id}_shortest_first`}>
              <View style={sortStyles.sortButton}>
                <Text
                    style={
                      selectedSortParam === SortType.DurationFastest
                          ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                          : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                    }
                >
                  {_label('shortest_first')}
                </Text>
              </View>
            </TouchableRipple>
            {selectedSortParam === SortType.DurationFastest ? (
              <View>
                <Image source={tickIcon} style={{ width: 12, height: 12 }} />
              </View>
            ) : (
              <View style={{ width: 12, height: 12 }} />
            )}
          </View>
        </View>
      </View>
      <View style={sortStyles.sortType}>
        <TouchableRipple onPress={() => onSortClicked(SortType.DepartureEarliest)}>
          <View style={sortStyles.sortField}>
            <Text
                style={
                  selectedSortParam === SortType.DepartureEarliest
                      ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                      : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                }
            >
              {_label('departure')}
            </Text>
          </View>
        </TouchableRipple>
        <View style={sortStyles.sortValue}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            <TouchableRipple onPress={() => onSortClicked(SortType.DepartureEarliest)} testID={`${id}_early_departures_first`}>
              <View style={sortStyles.sortButton}>
                <Text
                    style={
                      selectedSortParam === SortType.DepartureEarliest
                          ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                          : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                    }
                >
                  {_label('early_departure_first')}
                </Text>
              </View>
            </TouchableRipple>
            {selectedSortParam === SortType.DepartureEarliest ? (
              <View>
                <Image source={tickIcon} style={{ width: 12, height: 12 }} />
              </View>
            ) : (
              <View style={{ width: 12, height: 12 }} />
            )}
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            <TouchableRipple onPress={() => onSortClicked(SortType.DepartureLatest)} testID={`${id}_late_departures_first`}>
              <View style={sortStyles.sortButton}>
                <Text
                    style={
                      selectedSortParam === SortType.DepartureLatest
                          ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                          : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                    }
                >
                  {_label('late_departure_first')}
                </Text>
              </View>
            </TouchableRipple>
            {selectedSortParam === SortType.DepartureLatest ? (
              <View>
                <Image source={tickIcon} style={{ width: 12, height: 12 }} />
              </View>
            ) : (
              <View style={{ width: 12, height: 12 }} />
            )}
          </View>
        </View>
      </View>
      <View style={sortStyles.sortType}>
        <TouchableRipple onPress={() => onSortClicked(SortType.ArrivalEarliest)}>
          <View style={sortStyles.sortField}>
            <Text
                style={
                  selectedSortParam === SortType.ArrivalEarliest
                      ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                      : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                }
            >
              {_label('arrival')}
            </Text>
          </View>
        </TouchableRipple>
        <View style={sortStyles.sortValue}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            <TouchableRipple onPress={() => onSortClicked(SortType.ArrivalEarliest)} testID={`${id}_early_arrival_first`}>
              <View style={sortStyles.sortButton}>
                <Text
                    style={
                      selectedSortParam === SortType.ArrivalEarliest
                          ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                          : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                    }
                >
                  {_label('early_arrival_first')}
                </Text>
              </View>
            </TouchableRipple>
            {selectedSortParam === SortType.ArrivalEarliest ? (
              <View>
                <Image source={tickIcon} style={{ width: 12, height: 12 }} />
              </View>
            ) : (
              <View style={{ width: 12, height: 12 }} />
            )}
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            <TouchableRipple onPress={() => onSortClicked(SortType.ArrivalLatest)} testID={`${id}_early_arrival_first`}>
              <View style={sortStyles.sortButton}>
                <Text
                    style={
                      selectedSortParam === SortType.ArrivalLatest
                          ? [sortStyles.selectedSortText, textStyle.getSelectedSortTextFontStyle(), getLineHeight(14)]
                          : [sortStyles.sortText, textStyle.getSortTextFontStyle(), getLineHeight(14)]
                    }
                >
                  {_label('late_arrival_first')}
                </Text>
              </View>
            </TouchableRipple>
            {selectedSortParam === SortType.ArrivalLatest ? (
              <View>
                <Image source={tickIcon} style={{ width: 12, height: 12 }} />
              </View>
            ) : (
              <View style={{ width: 12, height: 12 }} />
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

SortPage.propTypes = {
  onSortClicked: PropTypes.func,
  selectedSortParam: PropTypes.any,
  id: PropTypes.string,
};

const textStyle = {
    getSortTextFontStyle : () => {
      return fontStyle('regular');
    },
    getSelectedSortTextFontStyle : () => {
      return fontStyle('regular');
    },
};
const sortStyles = {
  sortType: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  sortField: {
    paddingTop: 20,
    flex: 1,
  },
  sortValue: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-end',
    flex: 1,
  },
  sortText: {
    fontSize: 14,
    color: '#212121',
  },
  selectedSortText: {
    fontSize: 14,
    color: colors.azure,
  },
  sortButton: {
    padding: 20,
  },
};

export default SortPage;
