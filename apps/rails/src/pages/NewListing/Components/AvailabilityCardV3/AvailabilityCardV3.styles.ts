import { StyleSheet, Platform } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from 'packages/legacy-commons/Styles/getPlatformElevation';

export const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  classText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.black,
  },
  amountText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.black,
    marginLeft: 'auto',
  },
  tatkalGradieentContainer: {
    height: 16,
    width: 64,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  tatkalText: {
    fontSize: 10,
    color: colors.white,
  },
  availabilty: {
    marginBottom: 3,
  },
  availableTicket: {
    color: colors.cyan,
  },
  notAvailableTicket: {
    color: colors.lightTextColor,
  },
  waitlisted: {
    color: colors.yello,
  },
  railofyContainer: {
    marginTop: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  railofyIcon: {
    height: 10,
    width: 10,
    marginRight: 5,
    marginBottom: 1,
  },
  railofyText: {
    color: colors.purple,
  },
  freecancellationText: {
    marginTop: 2,
    color: colors.black54,
  },
  lastUpdatedOn: {
    color: colors.lightTextColor,
    position: 'absolute',
    bottom: 10,
    left: 8,
  },
  cardTouchable: {
    height: 90,
    minWidth: 152,
    backgroundColor: colors.white,
    ...getPlatformElevation(Platform.select({ android: 4, ios: 2 })),
    shadowRadius: 4,
    borderRadius: 4,
  },
  arrowTop: {
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 9,
    borderRightWidth: 9,
    borderBottomWidth: 8,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.white,
    marginTop: -3,
  },
  cardStyle: {
    flex: 1,
    padding: 8,
  },
  selectedCardStyle: {
    backgroundColor: colors.lighterBlue,
    borderColor: colors.azure,
    borderWidth: 1,
  },
  notAvailableCardStyle: {
    backgroundColor: colors.lightBlue33,
    borderColor: colors.lightBlue34,
    borderWidth: 1,
  },
  borderColor: {
    borderBottomColor: colors.lightSilver,
  },
  notAvailableArrow: {
    borderBottomColor: colors.greyLight2,
  },
  cardContainer: {
    flexDirection: 'column',
    flex: 1,
    marginVertical: 12,
    marginRight: 8,
    marginLeft: 2,
    marginBottom: 11,
    marginTop: 2,
    gap: 0,
  },
  availDepletContainer: {
    flexDirection: 'row',
    gap: 2,
    paddingVertical: 5,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.transparent,
  },
  availDepletIcon: {
    width: 10,
    height: 11,
    resizeMode: 'contain',
    alignSelf: 'center',
    marginBottom: 1,
  },
  availDepletText: {
    maxWidth: 160,
    fontSize: 11,
    fontWeight: '400',
    lineHeight: 11,
    flexWrap: 'wrap',
    flexShrink: 1,
    color: colors.black,
  },
});
