import React, { useCallback } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { styles } from './AvailabilityCardV3.styles';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { numAppendedWithRuppeeSymbol } from '@mmt/legacy-commons/Common/utils/NumberUtils';
import AVALABILITY_STATUS_VALUES from '../../../Types/AvailabilityStatusValues';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';
import { checkForWaitlistTG } from '../AvailabilityCard';
import { getRailsAvailDepletion, getRailsWlUrgencyPersuasion } from 'apps/rails/src/RailsAbConfig';
import { WlUrgencyPersuasion } from 'apps/rails/src/RailsAbConstants';
import { RAILS_ROUTE_KEYS } from '../../../../navigation/railsPageKeys';

import railofyIcon from '@mmt/legacy-assets/src/tick_purple.webp';
// =====================================================================================

interface AvailabilityDepletionProps {
  text: string;
  textColor: string;
  backgroundGradient: {
    start: string;
    end: string;
  };
  mainIconUrl: string;
  infoIconUrl: string | null;
  inventorySellOutInDays: number;
}

interface WlSeatUrgencyPersuasionProps {
  text: string;
  textColor: string;
  backgroundGradient: {
    start: string;
    end: string;
  };
  wlSeatsRemaining: number;
  omnitureEvent: string;
}

interface AvailabilityItem {
  availablityDate: string;
  availablityStatus: string;
  availablityType: '0' | '1' | '2' | '3' | '4' | '5' | '8224';
  className: string; // convert to enum
  classType: string; //convert to enum
  confirmationGuaranteeText: null | string;
  currentBkgFlag: string; // convert to enum
  displayPredictionFlag: boolean;
  freeCancellationText: null | string;
  lastUpdatedOn: string;
  predictionPercentage: string;
  prettyPrintingAvailablityStatus: string;
  quota: string; // convert to enum
  reason: ''; // string
  reasonType: string; // convert to enum
  totalFare: number;
  wlType: string; // convert to enum
  availabilityDepletion: AvailabilityDepletionProps | null;
  wlSeatUrgencyPersuasion: WlSeatUrgencyPersuasionProps | null;
}

interface AvailabilityCardV3Props {
  availabilityItem: AvailabilityItem;
  showGreenTGWL: boolean;
  isNewNearbyDates: boolean;
  onClick: () => void;
  showQuotaChip: boolean;
  quotaLabel: string;
  showFcStamps: boolean;
  isCardSelected: boolean;
  id?: string;
  isNotAvailableCard?: boolean;
}
interface CheckForWaitlistTGParams {
  isNewNearbyDates: boolean;
  showGreenTGWL: boolean;
  confirmationGuaranteeText: string | null;
}

export default function AvailabilityCardV3(props: AvailabilityCardV3Props) {
  const {
    availabilityItem,
    onClick,
    showQuotaChip,
    quotaLabel,
    showFcStamps,
    isCardSelected,
    showGreenTGWL,
    isNewNearbyDates,
    isNotAvailableCard = false,
  } = props;

  const { startNextScreenTimer } = useScreenProfiler();

  const clickCta = useCallback(() => {
    startNextScreenTimer(RAILS_ROUTE_KEYS.travelers, Date.now());
    onClick();
  }, [onClick, startNextScreenTimer]);

  const getAvailabilityStyle = () => {
    const { availablityType } = availabilityItem;
    if (availablityType === AVALABILITY_STATUS_VALUES.CNF.value
        || availablityType === AVALABILITY_STATUS_VALUES.RAC.value) {
      return [styles.availabilty, styles.availableTicket, getLineHeight(10), fontStyle('regular')];
    } else if (
      availablityType === AVALABILITY_STATUS_VALUES.NO_ROOM.value
      || availablityType === AVALABILITY_STATUS_VALUES.NT.value
      || availablityType?.startsWith(AVALABILITY_STATUS_VALUES.DEPARTED.value)
    ) {
      return [styles.availabilty,fontStyle('regular'), getLineHeight(10), styles.notAvailableTicket];
    } else {
      const params: CheckForWaitlistTGParams = {
        isNewNearbyDates: isNewNearbyDates,
        showGreenTGWL: showGreenTGWL,
        confirmationGuaranteeText: availabilityItem.confirmationGuaranteeText,
      };
      const checkforWaitlisted = checkForWaitlistTG(params);
      if (checkforWaitlisted) {
        return [
          styles.availabilty,
          fontStyle('regular'),
          getLineHeight(10),
          styles.availableTicket,
        ];
      }
        return [styles.availabilty, fontStyle('regular'), getLineHeight(10), styles.waitlisted];
    }
  };

  const availabilityDepletion = (getRailsAvailDepletion() === 2) ? availabilityItem?.availabilityDepletion : null;
  const wlSeatUrgencyPersuasion =
    getRailsWlUrgencyPersuasion() === WlUrgencyPersuasion.SHOWN
      ? availabilityItem?.wlSeatUrgencyPersuasion
      : null;

  return (
    <View style={{flexDirection: 'column', alignItems: 'center'}}>
      <View
        style={styles.cardContainer}
      >
        <TouchableOpacity
          onPress={clickCta}
          style={[
            styles.cardTouchable, 
            isCardSelected && styles.selectedCardStyle,
            isNotAvailableCard && styles.notAvailableCardStyle
          ]}
          activeOpacity={0.6}
        >
          <View style={styles.cardStyle} testID={props?.id}>
            <View style={styles.row}>
              <Text
                style={[styles.classText, fontStyle('black'), getLineHeight(12)]}
                testID={`${props?.id}_class`}
              >
                {availabilityItem.className}
              </Text>
              {showQuotaChip && (
                <LinearGradient
                  style={styles.tatkalGradieentContainer}
                  colors={[colors.goldenYellow, colors.yello]}
                  start={{ x: 0, y: 2 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text
                    style={[styles.tatkalText, fontStyle('bold'), getLineHeight(10)]}
                    testID={`${props?.id}_tatkal`}
                  >
                    {quotaLabel}
                  </Text>
                </LinearGradient>
              )}
              <RupeeText style={[styles.amountText, fontStyle('black'), getLineHeight(12)]}>
                {numAppendedWithRuppeeSymbol(availabilityItem.totalFare)}
              </RupeeText>
            </View>
            <Text style={getAvailabilityStyle()} testID={`${props?.id}_availabilityStatus`}>
              {availabilityItem.prettyPrintingAvailablityStatus ||
                availabilityItem.availablityStatus}
            </Text>
            {Boolean(availabilityItem.confirmationGuaranteeText) && (
              <View style={styles.railofyContainer} testID={`${props?.id}_TG`}>
                <Image source={railofyIcon} style={styles.railofyIcon} resizeMode="contain" />
                <Text style={[styles.railofyText, fontStyle('medium'), getLineHeight(12)]}>
                  {availabilityItem.confirmationGuaranteeText}
                </Text>
              </View>
            )}
            {showFcStamps && Boolean(availabilityItem.freeCancellationText) && (
              <Text
                style={[styles.freecancellationText, fontStyle('medium'), getLineHeight(12)]}
                testID={`${props?.id}_FC`}
              >
                {availabilityItem.freeCancellationText}
              </Text>
            )}
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={[styles.lastUpdatedOn, fontStyle('regular'), getLineHeight(12)]}
              testID={`${props?.id}_lastUpdatedStatus`}
            >
              {availabilityItem.lastUpdatedOn}
            </Text>
          </View>
        </TouchableOpacity>
        {availabilityDepletion && (
          <LinearGradient
            style={styles.availDepletContainer}
            colors={[
              availabilityDepletion?.backgroundGradient?.start || colors.white,
              availabilityDepletion?.backgroundGradient?.end || colors.white,
            ]}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
            >
              <Image
                source={{ uri: availabilityDepletion?.mainIconUrl }}
                style={styles.availDepletIcon}
              />
              <Text style={[styles.availDepletText, { color: availabilityDepletion?.textColor }]}>
                {availabilityDepletion?.text}
              </Text>
            </LinearGradient>
        )}
        {wlSeatUrgencyPersuasion && (
          <LinearGradient
            style={styles.availDepletContainer}
            colors={[
              wlSeatUrgencyPersuasion?.backgroundGradient?.start || '#ffb366',
              wlSeatUrgencyPersuasion?.backgroundGradient?.end || '#ff7f3f',
            ]}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
          >
            <Text style={[styles.availDepletText, { color: wlSeatUrgencyPersuasion?.textColor }]}>
              {wlSeatUrgencyPersuasion?.text}
            </Text>
          </LinearGradient>
        )}
      </View>
      <View style={[
        styles.arrowTop, 
        isCardSelected && styles.borderColor,
        isNotAvailableCard && styles.notAvailableArrow
      ]} />
    </View>
  );
}
