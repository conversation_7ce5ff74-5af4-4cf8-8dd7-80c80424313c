import {StyleSheet, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import React from 'react';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {getClassType} from '../../Types/ClassType';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';


export const NewUpdateAvailabilityCard = ({className, onClick, isTatkal}) => (
  <TouchableRipple onPress={onClick}>
    <View testID={this.props?.id}>
      <Card style={{
        justifyContent: 'space-between',
        height: 126,
        width: 170,
        marginHorizontal: 0,
        marginVertical: 12,
        marginRight: 12,
        paddingHorizontal: 16,
        paddingBottom: 10,
        marginLeft: 2,
        borderTopWidth: StyleSheet.hairlineWidth,
        borderColor: colors.lightGrey,
      }}
      >
        {
          isTatkal &&
          <LinearGradient
            style={styles.gradientContainer}
            colors={[colors.goldenYellow, colors.yello]}
            start={{
              x: 0,
              y: 2,
            }}
            end={{
              x: 1,
              y: 1,
            }}
          >
            <Text style={{ fontSize: 10, color: colors.white, ...fontStyle('bold'), ...getLineHeight(10) }}>{_label('tatkal', { uppercase: true })}</Text>
          </LinearGradient>
        }
        <Text style={[styles.classTypeText,
          fontStyle('regular'), getLineHeight(16),{
          paddingTop: 16,
        }]}>
          {_label(getClassType(className).value)}
        </Text>
        <View style={{paddingTop: 8}}>
          <LinearGradient
            style={{
              height: 14,
            }}
            colors={['#fafafa', '#f2f2f2']}
            start={{
              x: 0.0,
              y: 0.0,
            }}
            end={{
              x: 1.0,
              y: 0.0,
            }}
          />
        </View>
        <View style={{paddingTop: 8}}>
          <LinearGradient
            style={{
              height: 14,
            }}
            colors={['#fafafa', '#f2f2f2']}
            start={{
              x: 0.0,
              y: 0.0,
            }}
            end={{
              x: 1.0,
              y: 0.0,
            }}
          />
        </View>
        <Text style={[styles.tapText, fontStyle('regular'), getLineHeight(12), {paddingTop: 8}]}>
          {_label('tap_to_update')}
        </Text>
      </Card>
    </View>
  </TouchableRipple>
);

NewUpdateAvailabilityCard.propTypes = {
  className: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  isTatkal: PropTypes.bool,
};

const styles = StyleSheet.create({
  classTypeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.black,
  },
  tapText: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  gradientContainer: {
    height: 16,
    width: 64,
    borderRadius: 12,
    left: 12,
    top: -8,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
