import isEmpty from 'lodash/isEmpty';
import { connect } from 'react-redux';
import {
  onAvailabilityDateClickedV2,
  nearbyDatesCardClick,
  handleNearByDatesConfirmedOptionsClick,
  handleNearbyDatesTtuCardClick,
} from 'apps/rails/src/pages/NewListing/RailsListingActions';
import FutureAvailabilityListNew from '../FutureAvailabilityListNew';

const mapStateToProps = (state, ownProps) => {
  const {
    trainData,
    nearbyDatesInfo: {
      selectedNearbyCardsHashList,
    },
  } = ownProps;
  const {
    railsListing: { nearbyDatesTrainsListObject },
  } = state;
  let sectionIdentifier = '';
  if (trainData && trainData.uniqueCardId) {
    sectionIdentifier = trainData.uniqueCardId.split('_')[0] || '';
  }
  let selectedCardHash = null;
  if (sectionIdentifier) {
    selectedCardHash = selectedNearbyCardsHashList.find((item) => 
      item.includes(trainData?.trainNumber) && item.endsWith(`*${sectionIdentifier}`)
    );
  } else {
    selectedCardHash = selectedNearbyCardsHashList.find((item) => 
      item.includes(trainData?.trainNumber) && !item.includes('*searched') && !item.includes('*booked')
    );
  }
  
  const isLoading = nearbyDatesTrainsListObject[selectedCardHash]?.isLoading;
  const showRetryIcon = nearbyDatesTrainsListObject[selectedCardHash]?.showRetryIcon;
  const errorMessage = nearbyDatesTrainsListObject[selectedCardHash]?.errorMessage;
  const error = nearbyDatesTrainsListObject[selectedCardHash]?.error;
  const availabilityResponse = nearbyDatesTrainsListObject[selectedCardHash]?.availabilityResponse;
  const selectedDate = nearbyDatesTrainsListObject[selectedCardHash]?.selectedDate;
  const availablityList = isEmpty(availabilityResponse) ? [] : availabilityResponse.avlDayList;

  return {
    ...ownProps,
    availablityList,
    selectedDate,
    error,
    isLoading,
    showRetryIcon,
    errorMessage,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
  onDateClick: (selectedDate, trainData, selectedClassType, selectedQuota) => {
    dispatch(
      onAvailabilityDateClickedV2(
        selectedDate,
        trainData,
        selectedClassType,
        selectedQuota,
      ),
    );
  },
  onRetryClick: (trainData, selectedClassType, selectedQuota) => {
    dispatch(nearbyDatesCardClick(trainData, selectedClassType, selectedQuota));
  },
  onConfirmedOptionDateClick: (selectedDate, trainData, selectedClassType, selectedQuota) => {
    dispatch(
      handleNearByDatesConfirmedOptionsClick(
        selectedDate,
        trainData,
        selectedClassType,
        selectedQuota,
      ),
    );
  },
  ttuCardClick: (selectedDate, trainData, selectedClassType, selectedQuota) => {
    dispatch(
      handleNearbyDatesTtuCardClick(
        selectedDate,
        trainData,
        selectedClassType,
        selectedQuota,
      ),
    );
  }
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(FutureAvailabilityListNew);
