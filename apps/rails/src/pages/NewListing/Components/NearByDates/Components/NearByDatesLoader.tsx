import { StyleSheet, View } from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface LoaderItemProps {
    isLastItem: boolean,
}

function LoaderItem({ isLastItem }: LoaderItemProps) {
    return (
        <>
            <View style={styles.container}>
                <LinearGradient
                    colors={[colors.grayBg, colors.white]}
                    start={{ x: 0, y: 0.5 }}
                    end={{ x: 1, y: 0.5 }}
                    style={styles.datesContainer} />
                <View style={styles.cardsContainer}>
                    <LinearGradient
                        colors={[colors.grayBg, colors.white]}
                        start={{ x: 0, y: 0.5 }}
                        end={{ x: 1, y: 0.5 }}
                        style={styles.avlContainer} />
                    <LinearGradient
                        colors={[colors.grayBg, colors.white]}
                        start={{ x: 0, y: 0.5 }}
                        end={{ x: 1, y: 0.5 }}
                        style={styles.avlContainer} />
                </View>
            </View>
            {!isLastItem && <View style={styles.seperator} />}
        </>
    );
}

function NearByDatesLoader() {
    const numberOfItems = 6;

    return (
        <View style={styles.padding8}>
            {Array.from({ length: numberOfItems }).map((_, index) => (
                <LoaderItem isLastItem={index === numberOfItems - 1} key={index} />
            ))}
        </View>
    );
}

export default NearByDatesLoader;

const styles = StyleSheet.create({
    container: {
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        padding: 4,
    },
    datesContainer: {
        borderRadius: 4,
        height: 16,
        width: 64,
    },
    avlContainer: {
        borderRadius: 8,
        height: 38,
        width: 100,
    },
    seperator: {
        height: 1,
        backgroundColor: colors.grayBg,
        marginTop: 2,
    },
    cardsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: 223,
    },
    padding8: {
        padding: 8,
    },
});
