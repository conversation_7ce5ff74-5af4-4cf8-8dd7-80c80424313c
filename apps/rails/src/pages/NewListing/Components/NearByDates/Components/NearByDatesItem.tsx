import fecha from 'fecha';
import { Image, Text, View, TouchableOpacity } from 'react-native';
import React, { useState, useEffect } from 'react';
import { styles } from '../Styles/NearByDatesItem.styles';
import { getColorBasedOnAvailabilityType } from '../Utils/NearByDatesUtils';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { getRailsFcCalloutFlag, getNearbyDatesVariants } from 'apps/rails/src/RailsAbConfig';
import {
  trackEvar22Event,
  trackConfirmedOptionsClick,
  removeEventFromEvar47or97Variable,
} from '../../../../../railsAnalytics';
import { trackClickEventProp61 } from 'apps/rails/src/pages/RailsBusHomePage/Analytics';
import { LISTING_PAGE_TRACKING_KEY, NEARBY_DATES_EVENTS } from '../../../../../Utils/RailsConstant';
import { NearbyDatesItem } from '../types';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { RAILS_ROUTE_KEYS } from '@mmt/rails/src/navigation/railsPageKeys';

import railofyIcon from '@mmt/legacy-assets/src/tick_purple.webp';
import confirmedOptionsIcon from '@mmt/legacy-assets/src/confirmed_options_icon.webp';
import retryIcon from '@mmt/legacy-assets/src/nearby_dates_ttu_update_icon.webp';
import arrowRight from '@mmt/legacy-assets/src/arrow_blue_right.webp';

interface NearByDatesItemProps {
  selected: boolean;
  item: NearbyDatesItem;
  onAvlClick: unknown;
  onConfirmedOptionClick: unknown;
  ttuCardClick: unknown;
  isLastItem: boolean;
  listingAvailabilityType: string;
  id?: string;
  isConfirmedOptionsAvailable?: boolean;
}

const NearByDatesItem = ({
  selected,
  item,
  onAvlClick,
  onConfirmedOptionClick,
  isLastItem,
  listingAvailabilityType,
  ttuCardClick,
  id,
  isConfirmedOptionsAvailable,
}: NearByDatesItemProps) => {
  const [showFcStamp, setShowFcStamp] = useState(true);
  const { startNextScreenTimer } = useScreenProfiler();

  const nearbyDatesVariant = getNearbyDatesVariants();

  useEffect(() => {
    const showFcStamp = getRailsFcCalloutFlag();
    setShowFcStamp(showFcStamp);
    if (selected && item?.availablityType !== listingAvailabilityType) {
      trackEvar22Event(LISTING_PAGE_TRACKING_KEY, NEARBY_DATES_EVENTS.availabilitySwitch);
    }
  }, [item?.availablityType, listingAvailabilityType, selected]);


  try {
    const date = fecha.parse(item?.availablityDate, 'DD-MM-YYYY');
    const formattedDate = fecha.format(date, 'DD MMM, ddd');
    const availabilityStatus = item?.prettyPrintingAvailablityStatus || item?.availablityStatus;
    const { availabilityStyle, isNotBookable } = getColorBasedOnAvailabilityType(
      item?.availablityType,
      availabilityStatus,
    );

    const confirmedOptionData = item?.confirmedOption;
    const confirmedOptionPriceText = `@₹${confirmedOptionData?.totalFare}`;

    const handleConfirmedOptionClick = () => {
      startNextScreenTimer(RAILS_ROUTE_KEYS.travelers, Date.now());
      trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, NEARBY_DATES_EVENTS.confirmedOptionsClick);
      trackConfirmedOptionsClick();
      onConfirmedOptionClick(confirmedOptionData);
    };

    const handleAvlCardClick = () => {
      startNextScreenTimer(RAILS_ROUTE_KEYS.travelers, Date.now());
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        selected ? NEARBY_DATES_EVENTS.sameDateClick : NEARBY_DATES_EVENTS.otherDateClick,
      );
      removeEventFromEvar47or97Variable(
        `${RAIL_EVENTS.LISTING.RAIL_LISTING_CONFIRMED_OPTIONS_CLICKED}`,
      );
      onAvlClick(item);
    };

    const availabilityCard = availabilityStatus ? (
      <TouchableOpacity
        style={[styles.avlCard, isNotBookable && styles.notBookable]}
        disabled={isNotBookable}
        onPress={handleAvlCardClick}
      >
        <Text style={availabilityStyle}>{availabilityStatus}</Text>
        {availabilityStatus && item?.confirmationGuaranteeText && (
          <View style={styles.cardContainer}>
            <Image source={railofyIcon} style={styles.railofyIcon} />
            <Text style={[styles.railofyText, fontStyle('regular'), getLineHeight(10)]}>
              {item?.confirmationGuaranteeText}
            </Text>
          </View>
        )}
        {showFcStamp && item?.freeCancellationText && (
          <Text style={[styles.fcText, fontStyle('regular'), getLineHeight(10)]}>
            {item?.freeCancellationText}
          </Text>
        )}
      </TouchableOpacity>
    ) : (
      <TouchableOpacity style={styles.ttuCard} onPress={() => ttuCardClick(item)}>
        <Text style={availabilityStyle}>{_label('tap_to_update')}</Text>
        {!availabilityStatus && <Image source={retryIcon} style={styles.retryIcon} />}
      </TouchableOpacity>
    );

    const variantOne = (
      <View style={styles.cardsContainer}>
        {confirmedOptionData && (
          <TouchableOpacity style={styles.cnfOptionsContainer} onPress={handleConfirmedOptionClick}>
            <Text style={styles.stylesAvailable}>
              {confirmedOptionData.prettyPrintingAvailablityStatus ||
                confirmedOptionData.availablityStatus}
            </Text>
            <View style={styles.cardContainer}>
              <Image source={confirmedOptionsIcon} style={styles.railofyIcon} />
              <Text style={[styles.railofyText, fontStyle('regular'), getLineHeight(10)]}>
                {_label('confirmed_option')}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        {availabilityCard}
      </View>
    );

    const variantTwo = (
      <View style={styles.cardsContainer}>
        {availabilityCard}
        {confirmedOptionData ? (
          <TouchableOpacity style={styles.cnfOptionsContainer} onPress={handleConfirmedOptionClick}>
            <View style={styles.cardContainer}>
              <Text style={[styles.railofyText, fontStyle('regular'), getLineHeight(10)]}>
                {_label('confirmed_seat')}
              </Text>
            </View>
            <Text style={styles.confirmedOptionVariant3Fare}>
              {confirmedOptionPriceText}
              <Image source={arrowRight} style={styles.arrowContainer} />
            </Text>
          </TouchableOpacity>
        ) : isConfirmedOptionsAvailable ? (
          <View style={styles.emptyOption} />
        ) : null}
      </View>
    );

    const variantThree = (
      <View style={styles.cardsContainer}>
        {availabilityCard}
        {isConfirmedOptionsAvailable && !confirmedOptionData && <View style={styles.emptyOption} />}
        {confirmedOptionData && (
          <TouchableOpacity style={styles.cnfOptionsContainer} onPress={handleConfirmedOptionClick}>
            <Text style={styles.stylesAvailable}>
              {confirmedOptionData.prettyPrintingAvailablityStatus ||
                confirmedOptionData.availablityStatus}
            </Text>
            <View style={styles.cardContainer}>
              <Image source={confirmedOptionsIcon} style={styles.railofyIcon} />
              <Text style={[styles.railofyText, fontStyle('regular'), getLineHeight(10)]}>
                {_label('confirmed_option')}
              </Text>
            </View>
          </TouchableOpacity>
        )}
      </View>
    );

    const NearByDatesVariants = () => {
      switch (nearbyDatesVariant) {
        case 1:
          return variantOne;
        case 2:
          return variantTwo;
        case 3:
          return variantThree;
        default:
          return variantOne;
      }
    };

    return (
      <>
        <View style={[styles.container, selected && styles.selectedStyle]} testID={id}>
          <View>
            <Text
              style={
                selected
                  ? [styles.date, fontStyle('bold'), getLineHeight(14)]
                  : [styles.dateDeSelected, fontStyle('regular'), getLineHeight(14)]
              }
            >
              {formattedDate}
            </Text>
            {selected && (
              <Text style={[styles.yourSearch, fontStyle('bold'), getLineHeight(12)]}>
                {_label('your_search')}
              </Text>
            )}
          </View>
          <NearByDatesVariants />
        </View>
        {!isLastItem && <View style={styles.seperator} />}
      </>
    );
  } catch (e) {
    return null;
  }
};

export default NearByDatesItem;
