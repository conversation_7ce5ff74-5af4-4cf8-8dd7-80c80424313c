import { StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  loadContainer: {
    minHeight: 56,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  fourthVariantHeader: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
    backgroundColor: colors.lightGrey,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  dayLabel: {
    color: colors.greyText1,
    fontSize: 12,
    fontFamily: fonts.regular,
    marginLeft: 10,
  },
  availabilityLabel: {
    color: colors.greyText1,
    fontSize: 12,
    fontFamily: fonts.regular,
    marginLeft: 35,
  },
  availabilityLabel2: {
    color: colors.greyText1,
    fontSize: 12,
    fontFamily: fonts.regular,
    marginRight: 33,
  },
  confirmedLabel: {
    color: colors.greyText1,
    fontSize: 12,
    fontFamily: fonts.regular,
    paddingRight: 13,
    marginRight: 2,
  },
  infoGreyIcon: {
    height: 12,
    width: 12,
    marginBottom: -2,
    paddingLeft: 2,
  },
  errorMessage: {
    fontSize: 14,
    color: colors.defaultTextColor,
    marginHorizontal: 16,
  },
  dynamicContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginTop: 12,
    justifyContent: 'space-between',
  },
  dynamicImageContainer: {
    height: 24,
    width: 24,
  },
  dynamicImage: {
    width: 24,
    height: 24,
  },
  dynamicText: {
    fontSize: 12,
    color: colors.defaultTextColor,
    marginRight: 4,
  },
  dynamicPricingText: {
    fontSize: 12,
    color: colors.defaultTextColor,
  },
  listContainer: {
    minHeight: 50,
    padding: 4,
  },
  retryImgContainer: {
    height: 25,
    width: 25,
  },
  toolTipContainer: {
    position: 'absolute',
    width: '100%',
    marginHorizontal: 12,
    top: -112,
    right: -12,
    zIndex: 100,
  },
  toolTipInnerContainer: {
    borderRadius: 16,
    backgroundColor: colors.black,
    paddingTop: 10,
    paddingBottom: 15,
  },
  toolTipHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 11,
  },
  whiteText: {
    color: colors.white,
    paddingHorizontal: 20,
    fontSize: 14,
    fontWeight: 400,
  },
  arrowBottom: {
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 10,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderTopColor: colors.black,
    right: -280,
  },
  confirmedOptionsIcon: {
    width: 20,
    height: 20,
    marginLeft: 20,
  },
  confirmedOptionsText: {
    fontSize: 18,
    color: colors.white,
    paddingLeft: 5,
  },
  crossButton: {
    position: 'absolute',
    width: 45,
    height: 45,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
