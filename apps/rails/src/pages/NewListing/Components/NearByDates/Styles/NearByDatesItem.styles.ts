import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';

export const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
  },
  date: {
    fontSize: 16,
    color: colors.black,
  },
  dateDeSelected: {
    fontSize: 16,
    color: colors.defaultTextColor,
  },
  yourSearch: {
    fontSize: 12,
    color: colors.black,
    marginTop: 2,
  },
  predictionPercentage: {
    fontSize: 10,
    color: colors.lighterTextColor,
    marginTop: 1,
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
  },
  image: {
    height: 24,
    width: 24,
  },
  railofyIcon: {
    height: 12,
    width: 12,
    marginRight: 2,
    marginTop: 1,
  },
  railofyText: {
    color: colors.greyText1,
    fontSize: 10,
    height: 14,
  },
  fcText: {
    color: colors.greyText1,
    fontSize: 10,
    marginTop: 2,
  },
  stylesAvailable: {
    ...fontStyle('bold'),
    ...getLineHeight(12),
    color: colors.lightGreen16,
  },
  confirmedOptionVariant3Fare: {
    ...fontStyle('bold'),
    ...getLineHeight(12),
    color: colors.blue,
  },
  selectedStyle: {
    borderWidth: 1,
    borderColor: colors.azure,
    borderRadius: 12,
  },
  cardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cnfOptionsContainer: {
    borderRadius: 8,
    borderColor: colors.lightGray,
    borderWidth: 1,
    padding: 4,
    minHeight: 38,
    justifyContent: 'center',
    width: 115,
    marginRight: 10,
  },
  notBookable: {
    backgroundColor: colors.grayBg,
    padding: 6,
  },
  avlCard: {
    borderRadius: 8,
    borderColor: colors.lightGray,
    borderWidth: 1,
    padding: 4,
    minHeight: 38,
    justifyContent: 'center',
    width: 115,
    marginRight: 10,
  },
  emptyOption: {
    padding: 4,
    minHeight: 38,
    justifyContent: 'center',
    width: 125,
  },
  arrowContainer: {
    height: 10,
    width: 16,
    marginBottom: -1.5,
  },
  cardContainer: {
    flexDirection: 'row',
    marginTop: 2,
  },
  seperator: {
    height: 1,
    backgroundColor: colors.grayBg,
    marginTop: 2,
  },
  ttuCard: {
    borderRadius: 8,
    borderColor: colors.lightGray,
    borderWidth: 1,
    padding: 4,
    minHeight: 38,
    justifyContent: 'center',
    width: 115,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  retryIcon: {
    width: 15,
    height: 15,
    marginLeft: 2,
  },
});
