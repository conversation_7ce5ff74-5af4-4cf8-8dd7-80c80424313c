import fecha from 'fecha';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import AvailabilityStatusValues from 'apps/rails/src/pages/Types/AvailabilityStatusValues';
import { getDateObjectFromDateAndTime } from '@mmt/legacy-commons/Helpers/dateHelpers';
import railsConfig from 'apps/rails/src/RailsConfig';
import { DATE_MONTH_YEAR_FORMAT, YEAR_MONTH_DATE_FORMAT } from 'apps/rails/src/Utils/RailsConstant';

const DISABLED_CARD_MATCHING_STRINGS = [
  'not running',
  'train cancelled',
  'train departed',
  'booking not allowed',
  'class not exist',
  'not available',
];

const getIfCardIsDisabled = (availabilityStatus) => {
  const currentAvailabilityStatus = availabilityStatus?.toLowerCase();
  return DISABLED_CARD_MATCHING_STRINGS.some(
    (str) => str.toLowerCase() === currentAvailabilityStatus,
  );
};

export const getColorBasedOnAvailabilityType = (availabilityType, availabilityStatus) => {
  let isNotBookable = false;
  let availabilityStyle = {};

  const stylesAvailable = {
    ...fontStyle('bold'),
    ...getLineHeight(12),
    color: colors.lightGreen16,
  };
  const stylesNotAvailable = {
    color: colors.greyText1,
    ...fontStyle('regular'),
    ...getLineHeight(12),
  };
  const stylesWL = {
    ...fontStyle('bold'),
    ...getLineHeight(12),
    color: colors.lightYello,
  };

  isNotBookable = getIfCardIsDisabled(availabilityStatus);

  if (
    !availabilityStatus ||
    isNotBookable ||
    availabilityType === AvailabilityStatusValues.NO_ROOM.value ||
    availabilityType === AvailabilityStatusValues.NT.value ||
    availabilityType.startsWith(AvailabilityStatusValues.DEPARTED.value)
  ) {
    availabilityStyle = stylesNotAvailable;
  } else if (availabilityType === '1' || availabilityType === '2') {
    availabilityStyle = stylesAvailable;
  } else {
    availabilityStyle = stylesWL;
  }

  return { isNotBookable, availabilityStyle };
};

export const getSelectedCardHash = (
  trainNumber: string,
  selectedClassType: string,
  selectedQuota: string,
  sectionIdentifier?: string,
) => {
  if (sectionIdentifier) {
    return `${trainNumber}*${selectedClassType}*${selectedQuota}*${sectionIdentifier}`;
  }
  return `${trainNumber}*${selectedClassType}*${selectedQuota}`;
};

export const getNearbyDatesurl = ({
  fromCode,
  toCode,
  deptDate,
  trainNumber,
  selectedClassType,
  quotaSelected,
  fromStation,
  toStation,
  onlyConfirmedOptions,
}) => {
  return `${railsConfig.inventoryAvailabilities}/${fromCode}/${toCode}/${deptDate}/${trainNumber}/${selectedClassType}/${quotaSelected}/${fromStation}/${toStation}?onlyConfirmedOptions=${onlyConfirmedOptions}`;
};

export const getTrainDataForNearbyDatesConfirmedOption = (trainData, confirmedOptionData) => {
  return {
    ...trainData,
    frmStnCode: confirmedOptionData?.fromStationCode,
    frmStnName: confirmedOptionData?.fromStationName,
    toStnCode: confirmedOptionData?.toStationCode,
    toStnName: confirmedOptionData?.toStationName,
  };
};

export const getDataForConfirmedOptionsBottomSheet = (
  confirmedOption,
  trainData,
  selectedClassType,
  selectedQuota,
) => {
  const {
    trainNumber,
    trainName,
    departureTime,
    arrivalTime,
    frmStnCode,
    toStnCode,
    frmStnName,
    toStnName,
    distance,
    duration,
  } = trainData;

  const departureDate = confirmedOption?.availablityDate;
  const parsedBoardingDate = fecha.parse(confirmedOption?.availabilityKey, YEAR_MONTH_DATE_FORMAT);
  const boardingDate = fecha.format(parsedBoardingDate, DATE_MONTH_YEAR_FORMAT);
  const formattedBoardingDate = fecha.format(parsedBoardingDate, 'DD MMM');
  const parsedBoardingDateObj = getDateObjectFromDateAndTime(
    boardingDate,
    DATE_MONTH_YEAR_FORMAT,
    departureTime,
    'HH:mm',
  );
  const parsedDroppingDate = new Date(parsedBoardingDateObj.getTime() + duration * 60000);
  const droppingDate = fecha.format(parsedDroppingDate, DATE_MONTH_YEAR_FORMAT);
  const formattedDroppingDate = fecha.format(parsedDroppingDate, 'DD MMM');

  const departureDateObj = getDateObjectFromDateAndTime(
    confirmedOption?.availablityDate,
    DATE_MONTH_YEAR_FORMAT,
    departureTime,
    'HH:mm',
  );

  const arrivalDateObj = new Date(departureDateObj.getTime() + duration * 60000);
  const arrivalDate = fecha.format(arrivalDateObj, DATE_MONTH_YEAR_FORMAT);

  const confirmedOptionData = {
    ...confirmedOption,
    frmStnCode: confirmedOption?.fromStationCode,
    toStnCode: confirmedOption?.toStationCode,
    frmStnName: confirmedOption?.fromStationName,
    toStnName: confirmedOption?.toStationName,
    departureTime,
    departureDate,
    arrivalTime,
    arrivalDate,
    distance,
  };

  const nearbyDatesAlternateAvlData = {
    trainNumber,
    trainName,
    departureTime,
    departureDate,
    arrivalTime,
    arrivalDate,
    frmStnCode,
    toStnCode,
    frmStnName,
    toStnName,
    distance,
    duration,
    boardingDate,
    formattedBoardingDate,
    droppingDate,
    formattedDroppingDate,
    classAvailabilityList: [
      {
        travelClass: selectedClassType,
        frmStnCode: confirmedOption?.fromStationCode,
        toStnCode: confirmedOption?.toStationCode,
        frmStnName: confirmedOption?.fromStationName,
        toStnName: confirmedOption?.toStationName,
        tbsAvailability: {
          ...confirmedOption,
          classType: selectedClassType,
          quota: selectedQuota,
        },
      },
    ],
  };

  return { confirmedOptionData, nearbyDatesAlternateAvlData };
};

export const getClassNameAndQuotaFromHashList = (
  cardsHashList: [string],
  trainNumber: string,
  sectionIdentifier?: string,
) => {
  let currentCardHash;

  if (sectionIdentifier) {
    currentCardHash = cardsHashList.find((item) =>
      item.includes(trainNumber) && item.endsWith(`*${sectionIdentifier}`),
    );
  } else {
    currentCardHash = cardsHashList.find((item) =>
      item.includes(trainNumber) && !item.includes('*searched') && !item.includes('*booked'),
    ) || cardsHashList.find((item) => item.includes(trainNumber));
  }
  const [_selectedTrainNumber, selectedClassType, selectedQuota] = currentCardHash?.split('*');
  return { selectedClassType, selectedQuota };
};
