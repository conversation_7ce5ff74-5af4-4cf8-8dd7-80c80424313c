import { Text, View, Image, TouchableOpacity } from 'react-native';
import React, { useState, useEffect } from 'react';
import { isEmpty } from 'lodash';
import { styles } from './Styles/FutureAvailabilityList.styles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import NearByDatesItem from './Components/NearByDatesItem';
import NearByDatesLoader from './Components/NearByDatesLoader';
import { NearbyDatesInfo } from './types';
import { getClassNameAndQuotaFromHashList } from './Utils/NearByDatesUtils';
import { getNearbyDatesVariants } from 'apps/rails/src/RailsAbConfig';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import configFallbacks from 'apps/rails/src/configStore/Common/configDefaultFallbacks.json';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import {
  FROM_STATION_REPLACER,
  TO_STATION_REPLACER,
  LISTING_PAGE_TRACKING_KEY,
  NEARBY_DATES_EVENTS,
} from 'apps/rails/src/Utils/RailsConstant';
import { CrossButton } from 'apps/rails/src/pages/NewListing/Components/ConfirmedOptions/Assets/ConfirmedOptionsSvgComponents';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import retryIcon from '@mmt/legacy-assets/src/nearby_dates_retry_icon.webp';
import infoGreyIcon from '@mmt/legacy-assets/src/info_grey.webp';
import confirmedOptionsIcon from '@mmt/legacy-assets/src/confirmed_options_icon.webp';
import PropTypes from 'prop-types';

interface FutureAvailabilityListNewProps {
  selectedDate: object;
  availablityList: unknown[];
  isLoading: boolean;
  trainData: unknown;
  onDateClick: unknown;
  showRetryIcon: boolean;
  error: boolean;
  errorMessage: string;
  onLoad: unknown;
  nearbyDatesInfo: NearbyDatesInfo;
  id: string;
  onRetryClick: unknown;
  onConfirmedOptionDateClick: unknown;
  ttuCardClick: unknown;
}

const ToolTip = ({ text, closeTooltip }: { text: unknown; closeTooltip: unknown }) => {
  return (
    <View style={styles.toolTipContainer}>
      <View style={styles.toolTipInnerContainer}>
        <View style={styles.toolTipHeader}>
          <Image source={confirmedOptionsIcon} style={styles.confirmedOptionsIcon} />
          <Text style={[styles.confirmedOptionsText, fontStyle('regular')]}>
            {_label('mmt_confirmed')}
          </Text>
          <TouchableOpacity style={styles.crossButton} onPress={closeTooltip}>
            <CrossButton />
          </TouchableOpacity>
        </View>
        <Text style={[styles.whiteText, getLineHeight(14), fontStyle('regular')]}>{text}</Text>
      </View>
      <View style={styles.arrowBottom} />
    </View>
  );
};

const FutureAvailabilityListNew = ({
  selectedDate,
  availablityList,
  isLoading,
  trainData,
  onDateClick,
  error,
  showRetryIcon,
  errorMessage,
  onLoad,
  nearbyDatesInfo,
  onRetryClick,
  onConfirmedOptionDateClick,
  ttuCardClick,
  id,
}: FutureAvailabilityListNewProps) => {
  const [showToolTip, setShowToolTip] = useState(false);
  let toolTipText = useConfigStore(configKeys.RAILS_NEARBY_DATES_VARIANT_FOUR_TOOLTIP_TEXT);
  const mmtConfirmedText = `${_label('mmt_confirmed')}  `;

  if (isEmpty(toolTipText)) {
    toolTipText = configFallbacks?.rails_nearby_dates_variant_four_tooltip_text?.tooltipText;
  }

  const { selectedNearbyCardsHashList } = nearbyDatesInfo;
  const { trainNumber } = trainData;
  let sectionIdentifier = '';
  if (trainData && trainData.uniqueCardId) {
    sectionIdentifier = trainData.uniqueCardId.split('_')[0] || '';
  }
  const { selectedClassType, selectedQuota } = getClassNameAndQuotaFromHashList(
    selectedNearbyCardsHashList,
    trainNumber,
    sectionIdentifier,
  );

  const currentDayAvailabilityFromListing = trainData?.tbsAvailability?.find((avl) => {
    const className = avl?.className || avl?.classType;
    return className === selectedClassType;
  });

  const listingAvailabilityType = currentDayAvailabilityFromListing?.availablityType;
  const isConfirmedOptionsAvailable = availablityList?.some((item) => item.confirmedOption);

  const confirmedOptionToStn =
    availablityList?.length > 0 && availablityList[0]?.toStationName?.toUpperCase();
  const confirmedOptionFromStn =
    availablityList?.length > 0 && availablityList[0]?.fromStationName?.toUpperCase();

  const nearbyDatesVariant = getNearbyDatesVariants();

  const toolTipTextFormatted = toolTipText
    ?.replace(FROM_STATION_REPLACER, confirmedOptionFromStn)
    ?.replace(TO_STATION_REPLACER, confirmedOptionToStn);

  useEffect(() => {
    if (!isLoading) {
      setTimeout(() => {
        onLoad();
      }, 100);
    }
  }, [isLoading, onLoad]);

  const handleTooltipClicked = () => {
    if (!showToolTip) {
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        NEARBY_DATES_EVENTS.nearByDatesVariant4TooltipClicked,
      );
    }
    setShowToolTip(!showToolTip);
  };

  if (isLoading) {
    return <NearByDatesLoader />;
  }

  if (error) {
    return (
      <View style={styles.loadContainer} testID={`${id}_error`}>
        <Text style={[styles.errorMessage, fontStyle('regular'), getLineHeight(14)]}>
          {errorMessage}
        </Text>
        {showRetryIcon && (
          <TouchableOpacity
            onPress={() => {
              onRetryClick(trainData, selectedClassType, selectedQuota);
            }}
          >
            <Image source={retryIcon} style={styles.retryImgContainer} />
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <>
      {nearbyDatesVariant === 3 && (
        <View style={styles.fourthVariantHeader}>
          <Text style={styles.dayLabel}>{_label('day')}</Text>
          <Text
            style={
              isConfirmedOptionsAvailable ? styles.availabilityLabel : styles.availabilityLabel2
            }
          >
            {_label('availability_status')}
          </Text>
          {isConfirmedOptionsAvailable && (
            <TouchableOpacity onPress={handleTooltipClicked}>
              <Text style={styles.confirmedLabel}>
                {mmtConfirmedText}
                <Image source={infoGreyIcon} style={styles.infoGreyIcon} />
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
      <View style={styles.listContainer} testID={id}>
        {!isEmpty(availablityList) &&
          availablityList.map((item, index) => {
            let selected = false;
            let isLastItem = false;
            if (isEmpty(selectedDate)) {
              selected = index === 0;
            } else {
              selected = item.availablityDate === selectedDate.availablityDate;
            }
            if (index === availablityList.length - 1) {
              isLastItem = true;
            }
            return (
              <NearByDatesItem
                id={`${id}_${index}`}
                key={item.availablityDate}
                selected={selected}
                isLastItem={isLastItem}
                item={item}
                onAvlClick={(dateSelected) => {
                  const trainDataWithSection = {
                    ...trainData,
                    sectionIdentifier: sectionIdentifier,
                  };
                  onDateClick(dateSelected, trainDataWithSection, selectedClassType, selectedQuota);
                }}
                onConfirmedOptionClick={(dateSelected) => {
                  onConfirmedOptionDateClick(
                    dateSelected,
                    trainData,
                    selectedClassType,
                    selectedQuota,
                  );
                }}
                ttuCardClick={(dateSelected) => {
                  ttuCardClick(dateSelected, trainData, selectedClassType, selectedQuota);
                }}
                listingAvailabilityType={listingAvailabilityType}
                isConfirmedOptionsAvailable={isConfirmedOptionsAvailable}
              />
            );
          })}
      </View>
      {showToolTip && (
        <ToolTip text={toolTipTextFormatted} closeTooltip={() => setShowToolTip(false)} />
      )}
    </>
  );
};

ToolTip.propTypes = {
  text: PropTypes.string,
  closeTooltip: PropTypes.func,
};

export default FutureAvailabilityListNew;
