export interface NearbyDatesInfo {
  selectedNearbyCardsHashList: [string];
  selectedTrainsList: [string];
}
export interface NearbyDatesItem {
  travelAdvisoryDetails: object;
  insuranceCharge: number;
  totalCollectibleAmount: number;
  totalFare: number;
  baseFare: number;
  bkgCfg: object;
  availablityDate: string;
  availablityStatus: string;
  prettyPrintingAvailablityStatus: string;
  availablityType: string;
  reasonType: string;
  currentBkgFlag: string;
  freeCancellationText: string;
  confirmationGuaranteeText: string;
  fromStationName:string;
  fromStationCode:string;
  toStationName:string;
  toStationCode:string;
  isLoading: boolean;
  confirmedOption: NearbyDatesItem;
}
