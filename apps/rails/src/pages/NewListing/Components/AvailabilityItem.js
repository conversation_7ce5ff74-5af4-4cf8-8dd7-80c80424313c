import fecha from 'fecha';
import {Image, StyleSheet, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import React, {PureComponent} from 'react';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {getColorBasedOnAvailabilityType} from './AvailabilityCard';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { getGreenWaitlistedComponent, getRailsFcCalloutFlag, getNearbyDatesNew } from '../../../RailsAbConfig';

import rightArrow from '@mmt/legacy-assets/src/ic_arrow_right_grey.webp';
import railofyIcon from '@mmt/legacy-assets/src/tick_purple.webp';

export class AvailabilityItem extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showFcStamp: true,
      showGreenTGWL: false,
      isNewNearbyDates: false,
    };
  }

  componentDidMount() {
    const showFcStampLatest = getRailsFcCalloutFlag();
    const showGreenTGWLNew = getGreenWaitlistedComponent();
    const isNewNearbyDates = getNearbyDatesNew();
    this.setState({ showFcStamp: showFcStampLatest, showGreenTGWL: showGreenTGWLNew, isNewNearbyDates });
  }

  render() {
    const {selected, item, onClick} = this.props;
    const showFcStamp = this.state.showFcStamp;
    const showGreenTGWL = this.state.showGreenTGWL;
    const isNewNearbyDates = this.state.isNewNearbyDates;
    try {
      const date = fecha.parse(item.availablityDate, 'DD-MM-YYYY');
      const formattedDate = fecha.format(date, 'DD MMM, ddd');
      const availabilityStyle = getColorBasedOnAvailabilityType(item.availablityType, true,
        item.confirmationGuaranteeText, showGreenTGWL, isNewNearbyDates ); // isNearBy -> true
      // above is a bad (existing) approach but this will be changed when moving to new architecture
      return (
        <TouchableRipple onPress={() => onClick(item)} testID={this?.props?.id}>
          <View style={styles.container}>
            <View>
              <Text style={selected ? [styles.date, fontStyle('bold'), getLineHeight(16)] : [styles.dateDeSelected, fontStyle('regular'), getLineHeight(16)]}>{formattedDate}
              </Text>
              {selected &&
              <Text style={[styles.yourSearch, fontStyle('bold'), getLineHeight(12)]}>
                {_label('your_search')}
              </Text>
            }
            </View>
            <View style={styles.bottomContainer}>
              <View style={{ alignItems: 'flex-end' }}>
                <Text style={availabilityStyle}>
                  {item.prettyPrintingAvailablityStatus || item.availablityStatus}
                </Text>
                {item.displayPredictionFlag && item.predictionPercentage
                  && !item.confirmationGuaranteeText && !item.freeCancellationText && (
                  <Text style={[styles.predictionPercentage, fontStyle('black'), getLineHeight(10)]}>
                    {_label('num_chance', undefined, { num: item.predictionPercentage })}
                  </Text>
                )}
                {item.confirmationGuaranteeText && (
                  <View style={{flexDirection: 'row'}}>
                    <Image source={railofyIcon} style={styles.railofyIcon} />
                    <Text style={[styles.railofyText, fontStyle('regular'), getLineHeight(10)]}>
                      {item.confirmationGuaranteeText}
                    </Text>
                  </View>
                )}
                {showFcStamp && item.freeCancellationText && (
                  <Text style={[styles.fcText, fontStyle('regular'), getLineHeight(10)]}>
                    {item.freeCancellationText}
                  </Text>
                )}
              </View>

              <View style={styles.imageContainer}>
                <Image source={rightArrow} style={styles.image} />
              </View>
            </View>
          </View>
        </TouchableRipple>
      );
    } catch (e) {
      return null;
    }
  }
}

AvailabilityItem.propTypes = {
  item: PropTypes.shape({
    availablityDate: PropTypes.string.isRequired,
    availablityStatus: PropTypes.string.isRequired,
    availablityType: PropTypes.string,
    prettyPrintingAvailablityStatus: PropTypes.string,
    displayPredictionFlag: PropTypes.bool,
    predictionPercentage: PropTypes.number,
    confirmationGuaranteeText: PropTypes.string,
    freeCancellationText: PropTypes.string,
  }).isRequired,
  onClick: PropTypes.func.isRequired,
  selected: PropTypes.bool.isRequired,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: StyleSheet.hairlineWidth * 1,
    borderBottomColor: colors.lightGrey,
  },
  date: {
    fontSize: 16,  color: colors.defaultTextColor,
  },
  dateDeSelected: {
    fontSize: 16,  color: colors.defaultTextColor,
  },
  yourSearch: {
    fontSize: 12, color: colors.defaultTextColor,
  },
  predictionPercentage: {
    fontSize: 10, color: colors.lighterTextColor, marginTop: 1,
  },
  bottomContainer: {flexDirection: 'row', alignItems: 'center'},
  imageContainer: {alignItems: 'center', justifyContent: 'center', marginLeft: 12},
  image: {height: 24, width: 24},
  railofyIcon: {
    height: 10,
    width: 10,
    marginRight: 5,
    marginTop: 1,
  },
  railofyText: {
    color: colors.purple,
    fontSize: 10,
    height: 14,
  },
  fcText: {
    color: colors.black,
    fontSize: 10,
    marginTop: 2,
  },
});
