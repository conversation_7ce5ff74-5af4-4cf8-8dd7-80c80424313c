import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import { getClassType } from '../../Types/ClassType';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import _ from 'lodash';
import { getColorBasedOnAvailabilityType } from './AvailabilityCard';
import AvailabilityStatusValues from '../../Types/AvailabilityStatusValues';

export const NewAvailabilityCard =
({ availabilityItem, onClick, confirmationPrediction = null, isTatkal, id }) => {
  const confirmTktStyle = {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
  };
  const availabilityStyle = getColorBasedOnAvailabilityType(availabilityItem.availablityType);
  let predictionMessage = null;
  let predictionStyle = null;
  const availabilityType = availabilityItem.availablityType;
  if (availabilityType === AvailabilityStatusValues.NO_ROOM.value
    || availabilityType === AvailabilityStatusValues.NT.value
    || availabilityType.startsWith(AvailabilityStatusValues.DEPARTED.value)) {
    predictionMessage = '';
    predictionStyle = {
      ...confirmTktStyle,
      color: colors.lightTextColor,
    };
  } else if (!_.isEmpty(confirmationPrediction)) {
    predictionMessage = confirmationPrediction.Prediction;
    predictionStyle = {
      ...confirmTktStyle,
      color: confirmationPrediction.color,
    };
  }

  return (
    <TouchableRipple onPress={onClick}>
      <View testID={id}>
        <Card style={{
          justifyContent: 'space-between',
          height: 126,
          minWidth: 170,
          marginHorizontal: 0,
          marginVertical: 12,
          marginRight: 12,
          paddingHorizontal: 16,
          paddingBottom: 10,
          marginLeft: 2,
          borderTopWidth: StyleSheet.hairlineWidth,
          borderColor: colors.lightGrey,
        }}
        >
          <View style={{
            flexDirection: 'row',
            paddingTop: 16,
            alignItems: 'flex-end',
          }}>
            {
              isTatkal &&
              <LinearGradient
                style={styles.gradientContainer}
                colors={[colors.goldenYellow, colors.yello]}
                start={{
                  x: 0,
                  y: 2,
                }}
                end={{
                  x: 1,
                  y: 1,
                }}
              >
                <Text style={{ fontSize: 10, color: colors.white, fontFamily: fonts.bold }}>TATKAL</Text>
              </LinearGradient>
            }
            <Text style={styles.classText}>
              {getClassType(availabilityItem.className).value}
            </Text>
            <Text style={[styles.amountText, {paddingLeft: 8}]}>
              {`₹ ${Math.round(availabilityItem.totalFare)}`}
            </Text>
          </View>
          <Text style={[availabilityStyle, {paddingTop: 8}]}>
            {availabilityItem.prettyPrintingAvailablityStatus || availabilityItem.availablityStatus}
          </Text>

          <View style={{paddingTop: 8}}>
            {!_.isEmpty(confirmationPrediction) && !_.isEmpty(predictionMessage) &&
            <Text style={predictionStyle}>
              {predictionMessage}
            </Text>
            }

            {!_.isEmpty(confirmationPrediction) && _.isEmpty(predictionMessage) &&
            <LinearGradient
              style={{
                height: 14,
              }}
              colors={['white', 'white']}
              start={{
                x: 0.0,
                y: 0.0,
              }}
              end={{
                x: 1.0,
                y: 0.0,
              }}
            />
            }

            {_.isEmpty(confirmationPrediction) && _.isEmpty(predictionMessage) &&
            <LinearGradient
              style={{
                height: 14,
              }}
              colors={isTatkal || availabilityItem.availablityType !== '3' ? ['white', 'white'] : ['#fafafa', '#f2f2f2']}
              start={{
                x: 0.0,
                y: 0.0,
              }}
              end={{
                x: 1.0,
                y: 0.0,
              }}
            />
            }

            {_.isEmpty(confirmationPrediction) && !_.isEmpty(predictionMessage) &&
            <Text style={predictionStyle}>
              {predictionMessage}
            </Text>
            }

          </View>

          <Text style={[styles.lastUpdatedOn, {paddingTop: 8}]}>
            {availabilityItem.lastUpdatedOn}
          </Text>
        </Card>
      </View>
    </TouchableRipple>
  );
};

NewAvailabilityCard.propTypes = {
  availabilityItem: PropTypes.shape({
    availablityType: PropTypes.string,
    className: PropTypes.string,
    totalFare: PropTypes.number,
    prettyPrintingAvailablityStatus: PropTypes.string,
    availablityStatus: PropTypes.string,
    lastUpdatedOn: PropTypes.string,
  }),
  onClick: PropTypes.func.isRequired,
  showChancesLoader: PropTypes.bool,
  confirmationPrediction: PropTypes.shape({
    Prediction: PropTypes.string,
    color: PropTypes.string,
  }),
  isTatkal: PropTypes.bool,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  classText: {
    fontSize: 16,
    fontFamily: fonts.black,
    fontWeight: 'bold',
    color: colors.black,
  },
  amountText: {
    fontSize: 14,
    fontFamily: fonts.black,
    fontWeight: 'bold',
    color: colors.black,
  },
  lastUpdatedOn: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.lightTextColor,
  },
  gradientContainer: {
    height: 16,
    width: 64,
    borderRadius: 12,
    left: 12,
    top: -8,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
