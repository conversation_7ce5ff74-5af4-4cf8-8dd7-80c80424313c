import React, { useState } from 'react';
import { Image,StyleSheet, Text, View,TouchableWithoutFeedback } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import SortType from '../../Types/SortType';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle} from '../../../vernacular/VernacularUtils';
import { fireOmnitureEventsNew } from '../../RailsFilters/RailsFilterActions';
import RadioButton from '../../RIS/LiveTrainStatus/Component/RadioButton';
import LinearGradient from 'react-native-linear-gradient';
import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';
import PropTypes from 'prop-types';

const sortLabelArray = [
  _label('recommended_ticket_first'),
  _label('shortest_duration'),
  _label('early_departure_first'),
  _label('late_departure_first'),
  _label('early_arrival_first'),
  _label('late_arrival_first'),
];
const SortPageV2 = ({ onSortClicked, selectedSortParam, onDoneClick, onCloseClick,resetState }) => {
    const [sortType, setSortType] = useState(selectedSortParam);
    const onSortChange = (selectedParam:unknown) => {
      setSortType(selectedParam);
    };
    const applySort = (sort:unknown) =>{
        onSortClicked(sort);
        onDoneClick();
    };

    const sortClickHandler = (label : unknown) =>{
        switch (label) {
      case _label('recommended_ticket_first'):
                return SortType.DEFAULT;
            case _label('shortest_duration'):
                return SortType.DurationFastest;
            case _label('early_departure_first'):
                return SortType.DepartureEarliest;
            case _label('late_departure_first'):
                return SortType.DepartureLatest;
            case _label('early_arrival_first'):
                return SortType.ArrivalEarliest;
            case _label('late_arrival_first'):
                return SortType.ArrivalLatest;
          }
    };
    const isChecked = (label: unknown,selectedParam: unknown) =>{
        switch (label) {
      case _label('recommended_ticket_first'):
                return selectedParam === SortType.DEFAULT;
            case _label('shortest_duration'):
                return selectedParam === SortType.DurationFastest;
            case _label('early_departure_first'):
                return selectedParam === SortType.DepartureEarliest;
            case _label('late_departure_first'):
                return selectedParam === SortType.DepartureLatest;
            case _label('early_arrival_first'):
                return selectedParam === SortType.ArrivalEarliest;
            case _label('late_arrival_first'):
                return selectedParam === SortType.ArrivalLatest;
          }
    };
    return (
        <View style={{
            backgroundColor: colors.white,
            alignSelf: 'center',
            borderRadius: 4,
            paddingHorizontal: 34,
            marginHorizontal: 10,
            width: '100%',
        }}>
            <View style={{ flexDirection: 'row', marginVertical: 12, display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <View>
                    <Text style={{
                        ...fontStyle('bold'),
                        fontSize: 14,
                        color: colors.black,
                        lineHeight:20 }}
                    >
                        {_label('sort', { uppercase: true })}
                    </Text>
                </View>
                <TouchableRipple onPress={() => resetState()} onPressIn={() => { fireOmnitureEventsNew('Sort_Close'); onCloseClick(); }}>
                    <View>
                        <Image source={closeIcon} style={filterHeaderStyles.headerIcon} />
                    </View>
                </TouchableRipple>
            </View>
            <View style={{marginTop:'0%'}}>
                {sortLabelArray.map((sortLabel) => (
                    <TouchableWithoutFeedback key={'sort-option'} onPress={() => onSortChange(sortClickHandler(sortLabel))}>
                    <View style={sortStyles.sortType}>
                        {/* <TouchableWithoutFeedback onPress={() => onSortChange(sortClickHandler(sortLabel))}> */}
                        <View style={{ }}>
                            <Text style={[
                                {
                                    ...fontStyle('medium'),
                                    fontSize:12,
                                    color:colors.textGrey,
                                    lineHeight:16,
                                },
                                isChecked(sortLabel,sortType) ? {color:colors.primary} : {color:colors.textGrey}]}>
                                    {sortLabel}</Text>
                        </View>
                        {/* </TouchableWithoutFeedback> */}
                        <TouchableRipple onPress={() => onSortChange(sortClickHandler(sortLabel))}>
                            <View>
                                <RadioButton
                                    // checked={isChecked(sortLabel, selectedSortParam)} () => sortClickHandler(sortLabel)
                                    checked={isChecked(sortLabel,sortType)}
                                    dimension={14}
                                // style={bottomSheetStyles.radioContainer} onPressIn={()=>{onDoneClick()}}
                                />
                            </View>
                        </TouchableRipple>
                        {/* </TouchableWithoutFeedback> */}
                    </View>
                    </TouchableWithoutFeedback>
                ))}
            </View>
            <View>
            <TouchableRipple onPress = {()=>resetState()} onPressIn={()=>applySort(sortType)}>
                    <LinearGradient
                        style={{
                            borderRadius: 4,
                            height: 27,
                            marginTop: 15,
                            marginBottom: 8,
                            paddingBottom: 3,
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                        colors={['#53B2FE', '#065AF3']}
                        start={{ x: 0.0, y: 0.0 }}
                        end={{ x: 1.0, y: 0.0 }}
                    >
                        <Text style={{
                            fontSize: 12,
                            ...fontStyle('bold'),
                            color: colors.white,
                            backgroundColor: colors.transparent,
                            lineHeight:15,

                        }}
                        >{_label('apply')}
                        </Text>
                    </LinearGradient>
            </TouchableRipple>
            </View>
        </View>
    );
};

SortPageV2.propTypes = {
    onSortClicked: PropTypes.func,
    selectedSortParam: PropTypes.any,
    onDoneClick: PropTypes.func,
    onCloseClick: PropTypes.func,
    resetState: PropTypes.func,
  };

  const sortStyles = {
    sortType: {
      flexDirection: 'row',
      marginVertical: 5,
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    sortField: {
      paddingTop: 20,
    },
    sortValue: {
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'flex-end',
    },
    sortText: {
      fontSize: 14,
      color: '#212121',
    },
    selectedSortText: {
      fontSize: 14,
      color: colors.azure,
    },
    sortButton: {
      padding: 20,
    },
  };
const filterHeaderStyles = StyleSheet.create({
    container: { flexDirection: 'row', alignItems: 'center' },
    imageContainer: {
        height: 48, width: 48, justifyContent: 'center', alignItems: 'center',
    },
    image: { height: 24, width: 24 },
    textContainer: { justifyContent: 'center', marginLeft: 4 },
    filterText: { color: colors.defaultTextColor, fontSize: 18 },
    noOfTrains: { color: colors.lightTextColor, fontSize: 12 },
    clearAll: { fontSize: 16, color: colors.azure },
    headerIcon: {
        width: 10,
        height: 10,
        marginTop:4,
      },
});
export default SortPageV2;
