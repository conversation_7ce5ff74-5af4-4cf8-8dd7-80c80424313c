import React from 'react';
import { Actions } from '../../../navigation';
import {Image, PixelRatio, StyleSheet, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {DATE_CHANGE_ERR_PAGE_CONST} from '../../../Utils/RailsConstant';
import covidErrImg from '@mmt/legacy-assets/src/covid.webp';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
  },
  innerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorTitle: {
    marginHorizontal: 24,
    color: colors.black,
    fontSize: 24,
    lineHeight: 28,
    marginVertical: 16,
    textAlign: 'center',
  },
  errorSubTitle: {
    marginVertical: 8,
    marginHorizontal: 24,
    color: colors.lightTextColor,
    fontSize: 14,
    lineHeight: 21,
    textAlign: 'center',
  },
  actionBtn: {
    height: 40,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: PixelRatio.roundToNearestPixel(StyleSheet.hairlineWidth),
    borderRadius: 20,
    borderColor: colors.azure,
  },
});


function CovidErrorView(props) {
  const {
    errorViewData,
    searchProps,
  } = props;
  console.log('@rajesh props', props);
  const {
    title, //TODO put it in backend
  } = errorViewData;
  const changeDate = () => {
    Actions.railsCalendar({
      selectedDate: new Date(),
      fromPage: DATE_CHANGE_ERR_PAGE_CONST,
      searchProps,
    });
  };
  return (
    <View style={styles.innerContent} testID={props?.id}>
      <Image
        style={{
          height: 160,
          width: 240,
        }}
        source={covidErrImg}/>
      <Text style={[styles.errorTitle, fontStyle('bold'), getLineHeight(24)]}>Your Safety: Our Priority</Text>
      <Text style={[styles.errorSubTitle, fontStyle('regular'), getLineHeight(14)]}>{title}</Text>
      <TouchableRipple onPress={changeDate}>
        <Text style={{
          padding: 16,
          ...fontStyle('bold'),
          ...getLineHeight(14),
          color: colors.azure,
          fontSize: 14,
        }}>
          {_label('select_later_date', { uppercase: true })}
        </Text>
      </TouchableRipple>
    </View>
  );
}

CovidErrorView.propTypes = {
  errorViewData: PropTypes.object.isRequired,
  backHandler: PropTypes.object.isRequired,
  id: PropTypes.string,
  searchProps: PropTypes.shape({
    originStation: PropTypes.string,
    destinationStation: PropTypes.string,
    departureDate: PropTypes.string,
  }),
};

const stateToProps = (state) => {
  const {
    railsListing: {
      originStation, destinationStation, departureDate,
    },
  } = state;
  return {
    searchProps: {
      originStation,
      destinationStation,
      departureDate,
    },
  };
};

export default connect(stateToProps)(CovidErrorView);

