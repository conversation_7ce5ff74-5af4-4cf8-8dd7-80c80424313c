import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  Modal,
  TouchableWithoutFeedback,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import infoIcon from '@mmt/legacy-assets/src/info_grey.webp';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { LISTING_PAGE_TRACKING_KEY } from '@mmt/rails/src/Utils/RailsConstant';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';

interface SoldOutTrainsConfig {
  statusText?: string;
  toolTipText?: string;
  infoIconUrl?: string;
}

interface NonBookableBannerProps {
  seatsSoldoutConfig?: {
    soldOutTrainsConfig?: SoldOutTrainsConfig;
  };
}

const NonBookableBanner: React.FC<NonBookableBannerProps> = ({ seatsSoldoutConfig }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({
    iconCenterX: 0,
    iconY: 0,
    tooltipLeft: 0,
    arrowLeft: 0,
  });
  const infoIconRef = useRef(null);

  const trainSoldOutConfig = seatsSoldoutConfig?.soldOutTrainsConfig;
  const defaultText = _label('not_available');
  const hasTooltipText =
    trainSoldOutConfig?.toolTipText && trainSoldOutConfig.toolTipText.trim() !== '';

  const measureInfoIcon = () => {
    if (infoIconRef.current) {
      infoIconRef.current.measure((x, y, width, height, pageX, pageY) => {
        const iconCenterX = pageX + width / 2;
        const tooltipLeft = Math.min(iconCenterX - 135, Dimensions.get('window').width - 270 - 16);
        const arrowLeft = iconCenterX - tooltipLeft - 8;

        setTooltipPosition({
          iconCenterX,
          iconY: pageY,
          tooltipLeft: Math.max(16, tooltipLeft),
          arrowLeft: Math.max(8, arrowLeft),
        });
        setShowTooltip(true);
      });
    }
  };

  const toggleTooltip = () => {
    if (!showTooltip) {
      measureInfoIcon();
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_SOLD_OUT_BANNER_TOOLTIP_CLICKED,
      );
    } else {
      setShowTooltip(false);
    }
  };

  return (
    <>
      <View style={styles.noTrainContainer}>
        <View style={styles.statusContainer}>
          <Text style={[fontStyle('bold'), styles.trainSoldoutStatusText]}>
            {_label(trainSoldOutConfig?.statusText || defaultText)}
          </Text>
          {hasTooltipText && (
            <TouchableWithoutFeedback onPress={toggleTooltip}>
              <View ref={infoIconRef} collapsable={false} style={styles.infoIconWrapper}>
                <Image
                  source={
                    trainSoldOutConfig?.infoIconUrl
                      ? { uri: trainSoldOutConfig.infoIconUrl }
                      : infoIcon
                  }
                  style={styles.toolTipinfoIcon}
                />
              </View>
            </TouchableWithoutFeedback>
          )}
        </View>
      </View>

      <Modal visible={showTooltip} transparent animationType="none" onRequestClose={toggleTooltip}>
        <TouchableWithoutFeedback onPress={toggleTooltip}>
          <View style={styles.tooltipModalOverlay}>
            <View
              style={[
                styles.tooltipContainer,
                {
                  top: tooltipPosition.iconY - 73,
                  left: tooltipPosition.tooltipLeft,
                },
              ]}
            >
              <Text style={styles.tooltipText}>{trainSoldOutConfig?.toolTipText}</Text>
              <View
                style={[
                  styles.tooltipArrow,
                  {
                    left: tooltipPosition.arrowLeft,
                  },
                ]}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  noTrainContainer: {
    borderRadius: 4,
    backgroundColor: colors.grayBg,
    paddingVertical: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
    marginHorizontal: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trainSoldoutStatusText: {
    color: colors.greyText1,
  },
  infoIconWrapper: {
    marginLeft: 4,
  },
  toolTipinfoIcon: {
    marginTop: 2,
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  tooltipModalOverlay: {
    flex: 1,
    backgroundColor: colors.transparent,
  },
  tooltipContainer: {
    position: 'absolute',
    backgroundColor: colors.black,
    padding: 12,
    borderRadius: 8,
    width: 270,
  },
  tooltipArrow: {
    position: 'absolute',
    bottom: -8,
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderStyle: 'solid',
    backgroundColor: colors.transparent,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderTopColor: colors.black,
  },
  tooltipText: {
    ...fontStyle('regular'),
    fontSize: 14,
    lineHeight: 20,
    color: colors.white,
    textAlign: 'center',
  },
});

export default NonBookableBanner;
