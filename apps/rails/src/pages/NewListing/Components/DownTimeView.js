import React, { useState, useEffect } from 'react';
import Timer from '@mmt/rails/src/pages/TravelerDetails/Containers/Timer';
import { ScrollView, View, StyleSheet, Text, Image, Platform } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import ASSETS from '../../../Utils/Assets/RailsAssets';
import { RAIL_NO_TRAINS } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { isPhonePePlatform } from '../../../Utils/RisUtils';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import { getAllTypesAdsAb } from '../../Common/utils';
import { trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { getTimeDifference } from '@mmt/rails/src/Utils/RisUtils';

const DownTimeView = () => {
  const [showDiffTypeOfAds, setShowDiffTypeOfAds] = useState({
    multi_banner: 0,
    snackbar: 0,
    adfeed: 0,
    interstitial: 0,
  });
  useEffect(() => {
    getAdABConfig();
  }, []);

  const getAdABConfig = async () => {
    const AdsAb = await getAllTypesAdsAb();
    setShowDiffTypeOfAds(AdsAb);
    trackAdLoad('mob_rail_listing_v2_noTrainsFound_noTrainsOnOtherDates', AdsAb.trackingPayload);
  };

  return (
    <View style={styles.container}>
      <ScrollView>
        <Image style={styles.errorImg} source={ASSETS.maintenanceError} resizeMode="contain" />
        <Text style={[styles.title, fontStyle('bold')]}>{_label('irctc_error')}</Text>
        <Text style={styles.errorInfo}>
          {_label('back_in')}
          <Timer timerSeconds={getTimeDifference()} />
        </Text>
        <Text style={styles.errorInfoSubTitle}>
          {_label('please_come_back')}
          <Text style={[fontStyle('bold'), { color: colors.defaultTextColor }]}>
            {' '}
            12:20 AM{'\n'}
          </Text>
          <Text>{_label('apology')}</Text>
        </Text>
        <View style={{ margin: 16, alignSelf: 'center' }}>
          {!isPhonePePlatform() &&
            !!showDiffTypeOfAds.multi_banner &&
            getAdsCard(Platform.OS, RAIL_NO_TRAINS)}
        </View>
      </ScrollView>
    </View>
  );
};

export default DownTimeView;

const styles = StyleSheet.create({
  errorImg: {
    marginBottom: 40,
    marginRight: 'auto',
    marginLeft: 'auto',
    width: 250,
    height: 250,
  },
  title: {
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '700',
    fontSize: 20,
    marginBottom: 16,
    lineHeight: 22,
    color: colors.black,
    textAlign: 'center',
  },
  errorInfo: {
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 16,
    textAlign: 'center',
    marginBottom: 10,
    color: colors.defaultTextColor,
  },
  errorInfoSubTitle: {
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 60,
    color: colors.black,
  },
  container: {
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
});
