import PropTypes from 'prop-types';
import React from 'react';
import { View, Image, StyleSheet } from 'react-native';
import { _label } from '../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { trackFilterBarEvent } from '../../../railsAnalytics';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { QUOTA_FILTER_KEY } from '../../RailsFilters/railsFilter.utils';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { trackClickEventProp61 } from '../../RailsBusHomePage/Analytics';

import BottomBarFilter from '@Frontend_Ui_Lib_App/BottomBarFilter';

import filterIcon from '@mmt/legacy-assets/src/sort_filter.webp';
export default class RailFilterBar extends React.Component {
  handleQuickFilterClick = (filterItem, filterTypeObject) => {
    if (filterItem === QUOTA_FILTER_KEY) {
      trackClickEventProp61(
        RAIL_EVENTS.LISTING.MMT_RAIL_RN_LISTING_V2,
        RAIL_EVENTS.LISTING.RAIL_LISTING_QUICK_FILTER_QUOTA_TAP,
      );
      this.props.onFilterIconClick({ selectedTab: 'filter' });
    } else {
      this.props.onListingQuickFilterClick(filterItem, filterTypeObject);
    }
  };

  handleSortAndFilterClick = () => {
    trackFilterBarEvent('mob_rail_listing_quickfilter_sortfilter_click');
    this.props.onFilterIconClick();
  };

  render() {
    const { filterObject, filterTypesArray, filterCount } = this.props;
    const { quickFilter } = filterObject;

    return (
      <View testID={this.props?.id} style={styles.wrapperStyle}>

        {filterTypesArray?.length ? (
          <BottomBarFilter
            rightBtnClicker={this.handleSortAndFilterClick}
            rightBtnTestID={`${this.props?.id}_sortAndFilterButton`}
            rightBtnText={_label('sort_and_filter')}
            rightBtnIcon={<Image source={filterIcon} style={styles.filterImage} />}
            singleFilterData={filterTypesArray.map((item) => ({
              id: quickFilter.filterTypes[item].key,
              label: quickFilter.filterTypes[item].heading,
              isActive: quickFilter.filterTypes[item].selected,
              key: item,
            }))}
            customStyles={styles.customContainerStyle}
            customItemStyle={styles.customItemStyle}
            customFilterTextStyles={[fontStyle('bold'), getLineHeight(12)]}
            customRightBtnTextStyles={[styles.sortAndFilter, fontStyle('bold'), getLineHeight(10)]}
            customRoundBadgeTextStyles={[
              styles.filterCountText,
              fontStyle('bold'),
              getLineHeight(12),
            ]}
            itemClickHandler={(item) => {
              this.handleQuickFilterClick(item.key, quickFilter);
            }}
            activeNumberOfFilters={filterCount}
          />
        ) : (
          <></>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  wrapperStyle: { width: '100%', alignItems: 'center', paddingHorizontal: 16 },
  customContainerStyle: { maxWidth: '100%', marginBottom: 5 },
  customItemStyle: { maxWidth: 100, minWidth: 56 },
  filterCountText: {
    fontSize: 10,
    color: colors.black,
    textAlign: 'center',
    lineHeight: 14,
  },
  filterImage: {
    tintColor: colors.white,
    height: 25,
    width: 25,
  },
  sortAndFilter: {
    fontSize: 10,
    color: colors.white,
    lineHeight: 14,
    textAlign: 'center',
  },
});

RailFilterBar.propTypes = {
  filterTypesArray: PropTypes.array,
  onFilterIconClick: PropTypes.func.isRequired,
  onListingQuickFilterClick: PropTypes.func.isRequired,
  filterObject: PropTypes.shape({
    quickFilter: PropTypes.shape({
      filterTypes: PropTypes.objectOf(
        PropTypes.shape({
          key: PropTypes.string,
          heading: PropTypes.string,
          selected: PropTypes.bool,
        }),
      ),
    }),
  }),
  filterCount: PropTypes.number,
  id: PropTypes.string,
};

RailFilterBar.defaultProps = {
  filterTypesArray: [],
};

