import React, {Component} from 'react';
import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import {ScrollView} from 'react-native';
import Card from '@mmt/legacy-commons/Common/Components/Card/index';
import {getClassType} from '../../Types/ClassType';
import {ClassItem} from './ClassItem';


export default class ClassBar extends Component {
  render() {
    const {
      trainData, selectedQuota, selectedClassType, onClassTypeClicked, quotaClassRate,
    } = this.props;
    const {avlClasses} = trainData;

    return (
      <Card style={{marginHorizontal: 0, marginVertical: 0}}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} testID={this.props?.id}>
          {
            avlClasses.map((classType) => {
              const classTypeObject = getClassType(classType);
              if (isEmpty(quotaClassRate[selectedQuota.code][classType].rate)) {
                return (<ClassItem
                  key={classTypeObject.code}
                  itemID={classTypeObject.code}
                  id={`${this.props?.id}_${classTypeObject.code}`}
                  className={classTypeObject.value}
                  classRate=""
                  selected={selectedClassType === classType}
                  onSelect={(classTypeSelected) => {
                    onClassTypeClicked(classTypeSelected, trainData);
                  }}
                />);
              }
              return (
                <ClassItem
                  key={classTypeObject.code}
                  itemID={classTypeObject.code}
                  id={`${this.props?.id}_${classTypeObject.code}`}
                  className={classTypeObject.value}
                  classRate={quotaClassRate[selectedQuota.code][classType].rate}
                  selected={selectedClassType === classType}
                  onSelect={(classTypeSelected) => {
                    onClassTypeClicked(classTypeSelected, trainData);
                  }}
                />
              );
            })
          }
        </ScrollView>
      </Card>
    );
  }
}

ClassBar.propTypes = {
  onClassTypeClicked: PropTypes.func.isRequired,
  quotaClassRate: PropTypes.object.isRequired,
  selectedClassType: PropTypes.string.isRequired,
  selectedQuota: PropTypes.object.isRequired,
  trainData: PropTypes.object.isRequired,
  id: PropTypes.string,
};
