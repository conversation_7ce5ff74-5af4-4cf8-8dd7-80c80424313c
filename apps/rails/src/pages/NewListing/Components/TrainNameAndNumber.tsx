import React, { PureComponent} from 'react';
import { Image, StyleSheet, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import TrainRunningDaysChip from './TrainRunningDaysChip';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import isEmpty from 'lodash/isEmpty';
import { trackNewListingClickEvent } from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';

function getTrainName(trainName) {
  const trainNameLowerCase = trainName.toLowerCase();
  const exp = trainNameLowerCase.split(' ');
  let key = '';
  exp.forEach((ch) => {
    key += `${ch.charAt(0).toUpperCase() + ch.slice(1)} `;
  });
  return key;
}

class TrainNameAndNumber extends PureComponent {
  timer: number | null = null;
  constructor(props) {
    super(props);
    this.state = {
      showDynamicPriceToolTip: false,
    };
  }

  componentWillUnmount() {
    if (this.timer !== null) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  handleDynamicPriceIconClick = (val: boolean) => {
    if (this.timer !== null) {
      clearTimeout(this.timer);
    }
    this.setState({ showDynamicPriceToolTip: val });
    if (val) {
      this.timer = setTimeout(() => {
        this.setState({ showDynamicPriceToolTip: false });
      }, 3000);
      trackNewListingClickEvent(RAIL_EVENTS.LISTING.RAILS_DYNAMIC_PRICING_TOOLTIP_SHOWN);
    }
  };

    render() {
    const {
      trainName,
      trainNumber,
      showDaysOfRunChip,
      trainData,
      showDynamicPriceIcon = false,
      dynamicPriceConfig = {},
    } = this.props;
    return (
      <View
        style={trainHeader.container}
        testID={`train_name_and_number_container_${this.props?.id}`}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'flex-start', flexShrink: 1 }}>
          {showDynamicPriceIcon && !isEmpty(dynamicPriceConfig) && (
            <>
              <TouchableRipple
                onPress={() => this.handleDynamicPriceIconClick(true)}
                testID={`train_name_and_number_dynamic_price_icon_${this.props?.id}`}
              >
                <Image
                  source={{ uri: dynamicPriceConfig?.mainIconUrl }}
                  style={trainHeader.dynamicPriceMainIcon}
                  testID={`train_name_and_number_dynamic_price_icon_${this.props?.id}`}
                />
              </TouchableRipple>
              {this.state?.showDynamicPriceToolTip && (
                <View style={trainHeader.tooltipWrapper}>
                  <View
                    style={trainHeader.toolTipContainer}
                    testID={`train_name_and_number_dynamic_price_tooltip_container_${this.props?.id}`}
                  >
                    <Image
                      source={{ uri: dynamicPriceConfig?.toolTip?.iconUrl }}
                      style={trainHeader.dynamicPriceToolTipIcon}
                      testID={`train_name_and_number_dynamic_price_tooltip_icon_${this.props?.id}`}
                    />
                    <View style={trainHeader.flexColumn}>
                      <Text
                        style={trainHeader.toolTipTitle}
                        testID={`train_name_and_number_dynamic_price_tooltip_title_${this.props?.id}`}
                      >
                        {dynamicPriceConfig?.toolTip?.title}
                      </Text>
                      <Text
                        style={trainHeader.toolTipDescription}
                        testID={`train_name_and_number_dynamic_price_tooltip_description_${this.props?.id}`}
                      >
                        {dynamicPriceConfig?.toolTip?.description}
                      </Text>
                    </View>
                    <TouchableRipple
                      onPress={() => this.handleDynamicPriceIconClick(false)}
                      testID="train_cross_icon"
                    >
                      <Image source={ASSETS.whiteCrossIcon} style={trainHeader.whiteCrossIcon} />
                    </TouchableRipple>
                  </View>
                  <View style={trainHeader.arrowDown} />
                </View>
              )}
            </>
          )}
            <Text style={[trainHeader.name, fontStyle('black'), getLineHeight(16)]} numberOfLines={1} ellipsizeMode="tail" testID={`${this.props?.id}_trainName`}>
              {getTrainName(trainName)}
            </Text>
            {showDaysOfRunChip && <Text style={[trainHeader.number, fontStyle('regular')]} testID={`${this.props?.id}_trainNumber`}>{`(#${trainNumber})`}</Text>}
          </View>
          {!showDaysOfRunChip && <Text style={[trainHeader.number, fontStyle('regular')]} testID={`${this.props?.id}_trainNumber`}>{`#${trainNumber}`}</Text>}
          {showDaysOfRunChip && <TrainRunningDaysChip trainData={trainData} />}
        </View>
      );
    }
}

TrainNameAndNumber.propTypes = {
  trainName: PropTypes.string.isRequired,
  trainNumber: PropTypes.string.isRequired,
  id: PropTypes.string,
  showDaysOfRunChip: PropTypes.bool,
  trainData: PropTypes.object,
  showDynamicPriceIcon: PropTypes.bool,
  dynamicPriceConfig: PropTypes.shape({
    mainIconUrl: PropTypes.string,
    toolTip: PropTypes.shape({
      iconUrl: PropTypes.string,
      title: PropTypes.string,
      description: PropTypes.string,
    }),
  }),
};

const trainHeader = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 8,
  },
  name: {
    fontSize: 16,
    color: colors.black,
    flexShrink: 1,
  },
  number: {
    fontSize: 16,
    color: colors.lightTextColor,
    flexShrink: 0,
  },
  flexColumn: {
    flexDirection: 'column',
  },
  dynamicPriceMainIcon: {
    resizeMode: 'contain',
    height: 18,
    width: 18,
    alignSelf: 'center',
    marginRight: 2,
  },
  dynamicPriceToolTipIcon: {
    resizeMode: 'contain',
    height: 24,
    width: 24,
    alignSelf: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  toolTipContainer: {
    backgroundColor: colors.black,
    borderRadius: 8,
    flexDirection: 'row',
    padding: 8,
    gap: 8,
  },
  toolTipTitle: {
    color: colors.white,
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '400',
    opacity: 0.68,
  },
  toolTipDescription: {
    color: colors.white,
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '700',
  },
  whiteCrossIcon: {
    resizeMode: 'contain',
    height: 24,
    width: 24,
    alignSelf: 'center',
    marginLeft: 8,
  },
  tooltipWrapper: {
    top: -70,
    left: -10,
    position: 'absolute',
  },
  arrowDown: {
    left: 6,
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderTopWidth: 15,
    borderRightWidth: 13,
    borderBottomWidth: 0,
    borderLeftWidth: 13,
    borderTopColor: colors.black,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.transparent,
    borderLeftColor: colors.transparent,
  },
});

export default TrainNameAndNumber;
