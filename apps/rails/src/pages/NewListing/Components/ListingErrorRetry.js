import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { Text, View, StyleSheet, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { refreshListingPage } from '../../NewListing/RailsListingActions';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { RAILS_LISTING } from 'apps/rails/src/Utils/RailsConstant';
import PropTypes from 'prop-types';

import warningIcon from '@mmt/legacy-assets/src/rails/WarningSymbol.png';

const RAILS_LISTING_RETRY_COUNT = 'RAILS_LISTING_RETRY_COUNT';

const ListingErrorRetry = (props) => {
  const [isRetryDisabled] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetryFinished, setIsRetryFinished] = useState(false);

  const { errorDetails } = props;

  const { refreshListingPage } = props;

  const errorTitleMsg = _label('oops_something_went_wrong');
  const errorSubtitleMsg = _label('please_try_again_later');
  const goBackMsg = _label('go_back', { uppercase: true });
  const retry = _label('retry', { uppercase: true });

  useEffect(() => {
    retrieveCount();
  }, []);

  useEffect(() => {
    if (retryCount === 2) {
      setIsRetryFinished(true);
    }
  }, [retryCount]);

  const retrieveCount = async () => {
    try {
      const storedCount = await AsyncStorage.getItem(RAILS_LISTING_RETRY_COUNT);
      if (storedCount === null) {
        await AsyncStorage.setItem(RAILS_LISTING_RETRY_COUNT, retryCount);
      } else {setRetryCount(parseInt(storedCount));}
    } catch (error) {
      //
    }
  };

  const incrementCount = async () => {
    try {
      const newCount = retryCount + 1;
      await AsyncStorage.setItem(RAILS_LISTING_RETRY_COUNT, newCount.toString());
      setRetryCount(newCount);
      await retrieveCount();
      await refreshListingPage();
    } catch (error) {
      //
    }
  };

  const deleteRetryCount = async () => {
    await AsyncStorage.removeItem(RAILS_LISTING_RETRY_COUNT);
  };

  const handleGoBackButtonPress = () => {
    props?.onBack();
    deleteRetryCount();
    trackClickEventProp61(RAILS_LISTING, 'rail_error_goback');
  };

  const CustomButton = ({ onPress, title, isDisabled = false }) => (
    <TouchableRipple onPress={onPress} disabled={isDisabled}>
      <LinearGradient
        colors={[colors.lightBlue, colors.darkBlue]}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.button}
      >
        <Text style={styles.buttonText}>{title}</Text>
      </LinearGradient>
    </TouchableRipple>
  );

  CustomButton.propTypes = {
    onPress: PropTypes.func,
    title: PropTypes.string,
    isDisabled: PropTypes.bool,
  };

  const ButtonToBeDisplayed = () => {
    if (isRetryFinished) {
      return <CustomButton title={goBackMsg} onPress={handleGoBackButtonPress} />;
    }
    return (
      <CustomButton title={retry} onPress={() => incrementCount()} isDisabled={isRetryDisabled} />
    );
  };

  return (
    <View style={styles.container}>
      <Image source={warningIcon} style={styles.image} />
      <Text style={styles.title}>{isRetryFinished ? errorTitleMsg : errorDetails?.title}</Text>
      <Text style={styles.subtitle}>
        {isRetryFinished ? errorSubtitleMsg : errorDetails?.subtitle}
      </Text>
      <ButtonToBeDisplayed />
    </View>
  );
};

ListingErrorRetry.propTypes = {
  errorDetails: PropTypes.shape({
    title: PropTypes.string,
    subtitle: PropTypes.string,
  }),
  refreshListingPage: PropTypes.func,
  onBack: PropTypes.func,
};

const mapDispatchToProps = (dispatch) => ({
  refreshListingPage: () => dispatch(refreshListingPage()),
});

const mapStateToProps = (state) => {
  const {
    railsListing: { departureDate, originStation, destinationStation },
  } = state;

  return {
    departureDate,
    originStation,
    destinationStation,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ListingErrorRetry);

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    marginTop: -70,
  },
  image: {
    width: 45,
    height: 45,
    textAlign: 'center',
  },
  title: {
    fontSize: 15,
    lineHeight: 16,
    paddingTop: 15,
    paddingBottom: 7,
    fontWeight: '700',
    color: colors.black,
  },
  subtitle: {
    fontSize: 15,
    lineHeight: 16,
    paddingBottom: 20,
    color: colors.black,
  },
  button: {
    paddingVertical: 10,
    borderRadius: 10,
    width: 120,
    alignItems: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: 15,
    fontFamily: fonts.regular,
    fontWeight: '600',
  },
});
