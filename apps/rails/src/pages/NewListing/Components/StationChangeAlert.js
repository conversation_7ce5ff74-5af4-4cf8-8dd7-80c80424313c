import PropTypes from 'prop-types';
import React, {Component} from 'react';
import {Text, View, Image, Dimensions, StyleSheet} from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import StationChangeAlertStyle, {textStyle} from '../Styles/StattionChangeAlertStyle';
import StationChangeSignType from '../../Types/StationChangeSignType';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import RailsConfig from '../../../RailsConfig';
import { _label } from '../../../vernacular/AppLanguage';
import MultiStyleVernacText from '../../../vernacular/MultiStyleVernacText';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import ASSETS from '../../../Utils/Assets/RailsAssets';

const totalWidth = Dimensions.get('screen').width;
import stationChangeImage_S1D0 from '@mmt/legacy-assets/src/ic_origin_2x.webp';
import stationChangeImage_S0D1 from '@mmt/legacy-assets/src/ic_destination_2x.webp';
import yellowInfoIcon from '@mmt/legacy-assets/src/yellow_info_icon.webp';

const S1D0SrcCodeWidth = totalWidth / 4;
const S1D1DestCodeWith = (totalWidth / 2) + 40;
const S0D1DestCodeWith = (totalWidth / 2) + 28;

const DestCodeWith = (totalWidth / 15) - 20;

class StationChangeAlert extends Component {
  UNSAFE_componentWillMount() {
    if (RailsConfig.DEBUG_DANGEROUSLY) {
      setTimeout(() => {
        this.navigateToTravellerDetails();
      }, 1000);
    }
  }
  render() {
    const { labels, id } = this.props;
    return (
      <View testID="cluster_train_change_bottomsheet_container">
        <View style={styles.container} testID="cluster_train_change_bottomsheet_content">
          <Text style={[styles.stationChangeTitle, fontStyle('light'), getLineHeight(22)]}>
            {labels.listing_station_change_title}
          </Text>
          <View
            style={{ marginBottom: 6 }}
            testID="cluster_train_change_bottomsheet_header_container"
          >
            <Text style={[{ fontSize: 12, color: colors.lightTextColor}, fontStyle('regular'), getLineHeight(12)]}>
              {labels.listing_station_change_header}
            </Text>
          </View>

          <View
            style={{ marginBottom: 20 }}
            testID="cluster_train_change_bottomsheet_subheader_container"
          >
            <Text
              style={[
                StationChangeAlertStyle.trainTravelBetweenText,
                textStyle.getTitleFontStyle(),
              getLineHeight(12)]}>
              {labels.listing_station_change_subheader}
            </Text>
          </View>

          <View testID="cluster_train_change_bottomsheet_image_container">
            {this.getCorrectImageView()}
          </View>

          {this.getStationChangeAlertContent()}

          <View
            style={{ flexDirection: 'row', justifyContent: 'space-between' }}
            testID="cluster_train_change_bottomsheet_buttons_container"
          >
            {this.renderBackButton(labels, id)}
            {this.renderGoAheadButton(labels, id)}
          </View>
        </View>
      </View>
    );
  }

  getFromToStationText = (frmStnCode, frmStnName, toStnCode, toStnName) =>
    `${frmStnCode} (${frmStnName}) and ${toStnCode} (${toStnName})`;

  getCorrectImageView() {
    if (this.props.stationChangeSign !== StationChangeSignType.S0D0.code) {
      if (this.props.stationChangeSign === StationChangeSignType.S1D0.code) {
        return (
          <Image
            style={{width: '100%', height: 24, resizeMode: 'contain'}}
            source={stationChangeImage_S1D0}
            testID="cluster_train_change_bottomsheet_s1d0_image"
          />
        );
      } else if (this.props.stationChangeSign === StationChangeSignType.S0D1.code) {
        return (
          <Image
            style={{width: '100%', height: 24, resizeMode: 'contain'}}
            source={stationChangeImage_S0D1}
            testID="cluster_train_change_bottomsheet_s0d1_image"
          />
        );
      } else if (this.props.stationChangeSign === StationChangeSignType.S1D1.code) {
        return (
          <Image
            style={{ width: '100%', height: 24, resizeMode: 'contain' }}
            source={ASSETS.rectangleCopy}
            testID="cluster_train_change_bottomsheet_s1d1_image"
          />
        );
      }
    }
    return null;
  }

  renderGoAheadButton(labels, id) {
    return (
      <View testID="cluster_train_change_bottomsheet_go_ahead_container">
        <TouchableRipple
          onPress={() => {
        this.navigateToTravellerDetails();
      }}
          feedbackColor={colors.transparent}
          testID="cluster_train_change_bottomsheet_go_ahead_touchable"
        >
          <View testID={`${id}_goAheadButton`}>
            <Text style={[styles.alertButtons, fontStyle('bold'), getLineHeight(16)]}>{labels.listing_go_ahead}</Text>
          </View>
        </TouchableRipple>
      </View>
    );
  }

  renderBackButton(labels, id) {
    return (
      <View testID="cluster_train_change_bottomsheet_back_container">
        <TouchableRipple

          onPress={this.props.onBackClick}
          feedbackColor={colors.transparent}
          testID="cluster_train_change_bottomsheet_back_touchable"
        >
          <View testID={`${id}_backButton`}>
            <Text style={[styles.alertButtons, fontStyle('bold'), getLineHeight(16)]}>
              {labels.listing_back}
            </Text>
          </View>
        </TouchableRipple>
      </View>
    );
  }

  getStationChangeAlertContent = () => {
    const {
      selectedTrainInfo: { destinationDistance, sourceDistance } = {},
      frmStnCode,
      toStnCode,
      destinationStation = {},
      originStation = {},
    } = this.props;

    return (
      <View testID="cluster_train_change_bottomsheet_alert_content">
        <View
          style={{ flexDirection: 'row' }}
          testID="cluster_train_change_bottomsheet_stations_row"
        >
          <Text>{this.props.originStation.code}</Text>

          {this.props.stationChangeSign === StationChangeSignType.S1D0.code && (
            <View testID="cluster_train_change_bottomsheet_s1d0_container">
              <Text style={{ position: 'absolute', left: S1D0SrcCodeWidth }}>
                {this.props.frmStnCode}
              </Text>
            </View>
          )}

          {this.props.stationChangeSign === StationChangeSignType.S1D1.code && (
            <View testID="cluster_train_change_bottomsheet_s1d1_container">
              <Text style={{ position: 'absolute', left: S1D0SrcCodeWidth }}>
                {this.props.frmStnCode}
              </Text>
              <Text style={{ position: 'absolute', left: S1D1DestCodeWith }}>
                {this.props.toStnCode}
              </Text>
            </View>
          )}

          {this.props.stationChangeSign === StationChangeSignType.S0D1.code && (
            <Text style={{ position: 'absolute', left: S0D1DestCodeWith }}>
              {this.props.toStnCode}
            </Text>
          )}

          <Text style={{ position: 'absolute', right: DestCodeWith }}>
            {this.props.destinationStation.code}
          </Text>
        </View>
        {(Boolean(sourceDistance) || Boolean(destinationDistance)) && ( // - distance alert
          <View
            style={styles.clusterAlterContainer}
            testID="cluster_train_change_bottomsheet_distance_alert_container"
          >
            <Image
              source={yellowInfoIcon}
              style={styles.clusterAlertImage}
              testID="cluster_train_change_bottomsheet_distance_alert_icon"
            />
            <View
              style={{ marginTop: 2 }}
              testID="cluster_train_change_bottomsheet_distance_alert_content"
            >
              {Boolean(sourceDistance) && (
                <MultiStyleVernacText
                  content={_label('city_is_km_away_from_city')}
                  contentHorizontal
                  params={{
                    km: `${sourceDistance}km`,
                    foundCity: frmStnCode,
                    searchedCity: `${originStation.cityName} (${originStation.code})`,
                  }}
                  defaultContentStyle={[
                    styles.clusterParentText,
                    fontStyle('regular'),
                    getLineHeight(12),
                  ]}
                  textStyles={{
                    km: [styles.clusterKm, fontStyle('bold'), getLineHeight(12)],
                  }}
                />
                //   <Text style={[styles.clusterStationCode, fontStyle("black")]}>{frmStnCode}</Text>
                //   <Text> is {sourceDistance}km from </Text>
                //   <Text style={[styles.clusterStationCode, fontStyle("black")]}>{originStation.code}</Text>
                // </Text>
              )}
              {Boolean(destinationDistance) && (
                <MultiStyleVernacText
                  content={_label('city_is_km_away_from_city')}
                  contentHorizontal
                  params={{
                    km: `${destinationDistance}km`,
                    foundCity: toStnCode,
                    searchedCity: `${destinationStation.cityName} (${destinationStation.code})`,
                  }}
                  defaultContentStyle={[
                    styles.clusterParentText,
                    fontStyle('regular'),
                    getLineHeight(12),
                  ]}
                  textStyles={{
                    km: [styles.clusterKm, fontStyle('bold'), getLineHeight(12)],
                  }}
                />
                // <Text style={[styles.clusterParentText, fontStyle("regular")]}>
                //   <Text style={[styles.clusterStationCode, fontStyle("black")]}>{toStnCode}</Text>
                //   <Text> is {destinationDistance}km from </Text>
                //   <Text style={[styles.clusterStationCode, fontStyle("black")]}>{destinationStation.code}</Text>
                // </Text>
              )}
            </View>
          </View> // - distance alert
        )}
        {!sourceDistance && !destinationDistance && <View style={{ marginTop: 40 }} />}
      </View>
    );
  };

  navigateToTravellerDetails = () => {
    const {selectedTrainInfo, selectedClassType, selectedQuota} = this.props;
    this.props.onBackClick();
    this.props.goToTravelersPage(selectedClassType, selectedTrainInfo, selectedQuota.id);
  };
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: 16,
  },
  stationChangeTitle: {
    fontSize: 22,
    color: colors.black,
    marginBottom: 20,
  },
  alertButtons: {
    color: colors.azure,
    fontSize: 16,
  },
  clusterAlterContainer: {
    marginBottom: 40,
    marginTop: 20,
    paddingHorizontal: 8,
    borderRadius: 4,
    paddingVertical: 5,
    backgroundColor: colors.creamWhite,
    flexDirection: 'row',
    alignItems: 'center',
  },
  clusterAlertImage: {
    height: 16,
    width: 16,
    marginRight: 14,
    marginTop: 2,
  },
  clusterParentText: {
    color: colors.lightYello,
    flexShrink: 1,
  },
  clusterStationCode: {
  },
});

StationChangeAlert.propTypes = {

  stationChangeSign: PropTypes.string.isRequired,
  frmStnCode: PropTypes.string.isRequired,
  toStnCode: PropTypes.string.isRequired,
  destinationStation: PropTypes.object.isRequired,
  goToTravelersPage: PropTypes.func.isRequired,
  onBackClick: PropTypes.func.isRequired,
  originStation: PropTypes.object.isRequired,
  frmStnName: PropTypes.string.isRequired,
  toStnName: PropTypes.string.isRequired,
  boardingStation: PropTypes.object,
  droppingStation: PropTypes.object,
  labels: PropTypes.object.isRequired,
  selectedTrainInfo: PropTypes.object,
  selectedClassType: PropTypes.string,
  selectedQuota: PropTypes.shape({
    id: PropTypes.string.isRequired,
  }),
  id: PropTypes.string,
};

StationChangeAlert.defaultProps = {
  boardingStation: {},
  droppingStation: {},
  selectedTrainInfo: {},
};


StationChangeAlert.navigationOptions = {
  header: null,
};

export default StationChangeAlert;
