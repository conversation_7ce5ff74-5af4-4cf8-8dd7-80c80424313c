import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';

interface NotAvailableEducationConfig {
  title: string;
  subtitle: string;
}

const NotAvailableEducationCard: React.FC = () => {
  const notAvailableEducationConfig = useConfigStore(
    configKeys.RAILS_NOTAVAILABLE_EDUCATION_CONFIG,
  ) as NotAvailableEducationConfig;

  const renderStyledText = (text: string) => {
    if (!text) {
      return null;
    }
    const parts = text.split(/(\[.*?\])/);

    return parts.map((part, index) => {
      if (part.startsWith('[') && part.endsWith(']')) {
        const bracketText = part.slice(1, -1);
        return (
          <Text key={index} style={[styles.bracketText, fontStyle('bold')]}>
            {bracketText}
          </Text>
        );
      }
      return <Text key={index}>{part}</Text>;
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <Text style={[styles.title, fontStyle('bold'), getLineHeight(16)]}>
          {notAvailableEducationConfig?.title}
        </Text>
        <Text style={[styles.message, fontStyle('regular'), getLineHeight(14)]}>
          {renderStyledText(notAvailableEducationConfig?.subtitle)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 8,
    backgroundColor: colors.white,
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.cabsGreyBg,
    padding: 12,
  },
  title: {
    fontSize: 12,
    color: colors.black,
    marginBottom: 4,
  },
  message: {
    fontSize: 13,
    color: colors.lightGrey4,
    lineHeight: 18,
  },
  bracketText: {
    color: colors.textGrey,
  },
});

export default NotAvailableEducationCard;