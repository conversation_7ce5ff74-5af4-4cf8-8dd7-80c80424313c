import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import { NewTrainInfo } from './NewTrainInfo';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import AlternateAvailabilityCardList from './AlternateAvailabilityCardList';
import TrainNameAndNumber from './TrainNameAndNumber';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { getTrimmedStationName, getAlteredStationName } from '../Components/ConfirmedOptions/ConfirmedOptionsUtils';
import { ConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConstants';
import {
  removeEventFromEvar47or97Variable,
  updateEvar47or97Variable,
} from '@mmt/rails/src/railsAnalytics';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import { railsConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConfig';
import ConfirmedOptionsV2 from './ConfirmedOptionsV2';

const ConfirmOptionsBanner = () => {
  return (
    <View style={styles.cnfBanner} testID="confirmed_options_banner_container">
      <Text style={[styles.cnfTxt, getLineHeight(13)]} testID="confirmed_options_banner_text">
        {_label('confirmed_options')}
      </Text>
    </View>
  );
};

const ConfirmOptionsDesc = ({ sourceStationName, destinationStationName }) => {
  return (
    <View style={styles.cnfDescContainer} testID="confirmed_options_desc_container">
      <Text
        numberOfLines={2}
        style={[styles.cnfDescText, getLineHeight(14)]}
        testID="confirmed_options_desc_text"
      >
        <Text>{`${_label('cnf_desc')} ${_label('board_at')} `}</Text>
        <Text style={fontStyle('black')}>{getTrimmedStationName(sourceStationName, 15)}</Text>
        <Text>{` ${_label('and')} ${_label('get_down_at', { lowercase: true })} `}</Text>
        <Text style={fontStyle('black')}>{getTrimmedStationName(destinationStationName, 15)}</Text>
      </Text>
    </View>
  );
};

ConfirmOptionsDesc.propTypes = {
  sourceStationName: PropTypes.string,
  destinationStationName: PropTypes.string,
};

const ConfirmOptionsDescV2 = ({ originStnName, destinationStnName, boardStnName, dropStnName }) => {
  const cnfOptionsListingText = _label('cnf_listing_text', undefined, {
    originStnName: getAlteredStationName(originStnName),
    destinationStnName: getAlteredStationName(destinationStnName),
    boardStnName: getAlteredStationName(boardStnName),
    dropStnName: getAlteredStationName(dropStnName),
  });

  ConfirmOptionsDescV2.propTypes = {
    originStnName: PropTypes.string,
    destinationStnName: PropTypes.string,
    boardStnName: PropTypes.string,
    dropStnName: PropTypes.string,
  };

  return (
    <View style={styles.cnfDescContainerV2}>
      <Text style={[styles.cnfDescTextV2, getLineHeight(14.4)]}>
        {cnfOptionsListingText}
      </Text>
    </View>
  );
};

const AlternateTrainItem = (props) => {
  const {
    onAvailabilityCardClicked, trainData,
  } = props;
  const showConfirmedOptionsV2 = railsConfirmOptionsV2() === ConfirmOptionsV2.SHOWN;
  const hideConfirmedOptionsV2 = railsConfirmOptionsV2() === ConfirmOptionsV2.NOT_SHOWN;
  const showConfirmedOptionsUX_V2 = railsConfirmOptionsV2() === ConfirmOptionsV2.UX_V2;
  useEffect(() => {
    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_V1);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_V2);
    if (hideConfirmedOptionsV2) {
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_V1);
    } else if (showConfirmedOptionsV2) {
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_V2);
    } else if (showConfirmedOptionsUX_V2) {
      updateEvar47or97Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_V2_LISTING);
    }
  }, []);

  return (
    <View style={styles.container} testID={props?.id}>
      {!showConfirmedOptionsUX_V2 && <ConfirmOptionsBanner />}
      {showConfirmedOptionsUX_V2 && <ConfirmedOptionsV2 trainData={trainData} />}
      <View style={{
        paddingTop: 10,
        paddingBottom: 10,
        backgroundColor: colors.white,
      }}
      >
        <View style={{ marginHorizontal: 16 }}>
          <TrainNameAndNumber
            id={`${props?.id}_trainNameandNumber`}
            trainName={trainData.trainName}
            trainNumber={trainData.trainNumber}
          />
          <View style={{ marginTop: 12 }}>
            <NewTrainInfo trainData={trainData} id={`${props?.id}_trainInfo`} />
          </View>
          {hideConfirmedOptionsV2 &&
            <ConfirmOptionsDesc
              sourceStationName={trainData.frmStnName}
              destinationStationName={trainData.toStnName}
            />
          }
        </View>
        <View style={{
          marginHorizontal: 16,
          marginTop: 12,
        }}
        >
          <AlternateAvailabilityCardList
            id={`${props?.id}_trainAvailabilityCardList`}
            onAvailabilityCardClick={onAvailabilityCardClicked}
            trainData={props.trainData}
          />
          {showConfirmedOptionsV2 &&
            <ConfirmOptionsDescV2
              originStnName={trainData?.classAvailabilityList?.[0]?.frmStnName}
              destinationStnName={trainData?.classAvailabilityList?.[0]?.toStnName}
              boardStnName={trainData?.frmStnName}
              dropStnName={trainData?.toStnName}
            />
          }
        </View>
      </View>
    </View>
  );
};

export default AlternateTrainItem;

AlternateTrainItem.propTypes = {
  onAvailabilityCardClicked: PropTypes.func.isRequired,
  trainData: PropTypes.shape({
    trainName: PropTypes.string,
    trainNumber: PropTypes.string,
    frmStnName: PropTypes.string,
    toStnName: PropTypes.string,
    classAvailabilityList: PropTypes.arrayOf(
      PropTypes.shape({
        frmStnName: PropTypes.string,
        toStnName: PropTypes.string,
      }),
    ),
  }),
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
  },
  cnfBanner: {
    width: 139,
    height: 18,
    backgroundColor: colors.lightGreen4,
    borderBottomRightRadius: 30,
    justifyContent: 'center',
  },
  cnfTxt: {
    top: '1.18%',
    left: '12.2%',
    color: colors.white,
  },
  cnfDescContainer: {
    justifyContent: 'center',
    width: '100%',
    height: 40,
    backgroundColor: colors.mintGreen,
    borderRadius: 4,
    marginTop: 12,
  },
  cnfDescContainerV2: {
    justifyContent: 'center',
    width: '100%',
    backgroundColor: colors.lightGreen1,
    borderRadius: 10,
    marginTop: 12,
    paddingVertical: 10,
  },
  cnfDescText: {
    color: colors.jadeGreen1,
    marginLeft: 10,
    marginRight: 10,
  },
  cnfDescTextV2: {
    color: colors.textGrey,
    marginLeft: 16,
    marginRight: 10,
    fontFamily: 'lato',
    fontSize: 12,
  },
});
