import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { getLineHeight } from '@mmt/rails/src/vernacular/VernacularUtils';
import PropTypes from 'prop-types';
import {
  getConfirmedOptionsVersion,
  getFormattedStnName,
  GetImagebasedOnStationsHorizontal,
  getLabels,
  V1 as LONGER_ROUTE_AT_BOTH_ENDS,
  V2 as LONGER_ROUTE_AT_SOURCE,
  V3 as LONGER_ROUTE_AT_DESTINATION,
} from './ConfirmedOptions/ConfirmedOptionsUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import useConfigStore from '@mmt/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '@mmt/rails/src/configStore/Common/constants';
import { railsConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConfig';
import { ConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConstants';

const ConfirmOptionsBanner = () => {
  const cnfOptionsConfig = useConfigStore(configKeys.RAILS_CNF_OPTIONS_BANNER_CONFIG);
  const showConfirmedOptionsUX_V2 = railsConfirmOptionsV2() === ConfirmOptionsV2.UX_V2;
  const bannerText = showConfirmedOptionsUX_V2 ? cnfOptionsConfig?.name : _label('confirmed_seats');

  return (
    <View style={styles.cnfBanner}>
      <Text style={[styles.cnfTxt, getLineHeight(13)]}>{bannerText}</Text>
    </View>
  );
};

const ConfirmedOptionsV2 = (props) => {
  const { trainData } = props;
  const [cnfVersion, setCnfVersion] = React.useState(LONGER_ROUTE_AT_BOTH_ENDS);
  const originStnName = getFormattedStnName(trainData?.classAvailabilityList?.[0]?.frmStnName);
  const destinationStnName = getFormattedStnName(trainData?.classAvailabilityList?.[0]?.toStnName);
  const labels = getLabels({
    originStnName,
    destinationStnName,
    boardStnName: trainData?.frmStnName,
    dropStnName: trainData?.toStnName,
    cnfVersion,
  });
  const frmStnCode = trainData?.classAvailabilityList?.[0]?.frmStnCode;
  const toStnCode = trainData?.classAvailabilityList?.[0]?.toStnCode;
  const frmStnName = trainData?.frmStnCode;
  const toStnName = trainData?.toStnCode;

  useEffect(() => {
    const currVersion = getConfirmedOptionsVersion({
      originStnCode: frmStnCode,
      boardStnCode: frmStnName,
      dropStnCode: toStnName,
      destinationStnCode: toStnCode,
    });
    setCnfVersion(currVersion);
  }, [frmStnCode, frmStnName, toStnCode, toStnName]);

  const getDownTextStyle = () => {
    if (cnfVersion === LONGER_ROUTE_AT_SOURCE) {
      return {
        color: colors.black,
      };
    }
    return {
      color: colors.textGrey,
    };
  };
  return (
    <View style={styles.container} testID="confirmed_options_v2_container">
      <ConfirmOptionsBanner />
      <View style={styles.keyContainer} testID="confirmed_options_v2_key_container">
        {(cnfVersion === LONGER_ROUTE_AT_BOTH_ENDS || cnfVersion === LONGER_ROUTE_AT_SOURCE) && (
          <Text style={styles.originStn} testID="confirmed_options_v2_origin_stn">
            {labels.book_from}
          </Text>
        )}
        <Text style={styles.boardTxt} testID="confirmed_options_v2_board_txt">
          {labels.board_at}
        </Text>
        <Text
          style={[styles.getDownTxt, getDownTextStyle()]}
          testID="confirmed_options_v2_get_down_txt"
        >
          {labels.get_down_at}
        </Text>
        {(cnfVersion === LONGER_ROUTE_AT_BOTH_ENDS ||
          cnfVersion === LONGER_ROUTE_AT_DESTINATION) && (
          <Text style={styles.destinationStn} testID="confirmed_options_v2_destination_stn">{labels.book_upto}</Text>
        )}
      </View>
      <View style={styles.imgContainer} testID="confirmed_options_v2_img_container">
        {<GetImagebasedOnStationsHorizontal cnfVersion={cnfVersion} />}
      </View>
      <View style={styles.valueContainer} testID="confirmed_options_v2_value_container">
        {(cnfVersion === LONGER_ROUTE_AT_BOTH_ENDS || cnfVersion === LONGER_ROUTE_AT_SOURCE) && (
          <Text
            numberOfLines={2}
            style={styles.originStnName}
            testID="confirmed_options_v2_origin_stn_name"
          >
            {originStnName}
          </Text>
        )}
        <Text numberOfLines={2} style={styles.boardStn} testID="confirmed_options_v2_board_stn">
          {trainData?.frmStnName}
        </Text>
        <Text
          numberOfLines={2}
          style={[styles.dropStn, getDownTextStyle()]}
          testID="confirmed_options_v2_drop_stn"
        >
          {trainData?.toStnName}
        </Text>

        {(cnfVersion === LONGER_ROUTE_AT_BOTH_ENDS ||
          cnfVersion === LONGER_ROUTE_AT_DESTINATION) && (
          <Text
            numberOfLines={2}
            style={styles.destinationStnName}
            testID="confirmed_options_v2_destination_stn_name"
          >
            {destinationStnName}
          </Text>
        )}
      </View>
    </View>
  );
};

ConfirmedOptionsV2.propTypes = {
  trainData: PropTypes.shape({
    classAvailabilityList: PropTypes.arrayOf(
      PropTypes.shape({
        frmStnName: PropTypes.string,
        toStnName: PropTypes.string,
        frmStnCode: PropTypes.string,
        toStnCode: PropTypes.string,
      }),
    ),
    frmStnName: PropTypes.string,
    toStnName: PropTypes.string,
    frmStnCode: PropTypes.string,
    toStnCode: PropTypes.string,
  }),
};
const styles = StyleSheet.create({
  container: {
    position: 'relative',
    marginTop: 10,
    marginHorizontal: 10,
    backgroundColor: colors.lightGreen21,
    overflow: 'hidden',
    borderRadius: 8,
    flex: 1,
    paddingBottom: 10,
  },
  keyContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    marginTop: 8,
  },
  valueContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    marginTop: -6,
  },
  cnfBanner: {
    width: '35%',
    height: 18,
    backgroundColor: colors.lightGreen4,
    borderBottomRightRadius: 30,
    borderTopLeftRadius: 20,
    justifyContent: 'center',
  },
  cnfTxt: {
    top: '1.28%',
    left: '10.2%',
    color: colors.white,
    fontSize: 12,
    fontWeight: '700',
    fontFamily: 'Lato',
  },
  imgContainer: {
    position: 'relative',
    height: 20,
  },
  destinationStn: {
    fontFamily: 'Lato',
    fontWeight: '400',
    fontSize: 12,
    color: colors.greyText1,
    flexWrap: 'wrap',
    textAlign: 'center',
  },
  destinationStnName: {
    fontFamily: 'Lato',
    fontWeight: '400',
    fontSize: 12,
    color: colors.greyText1,
    width: '20%',
    flexWrap: 'wrap',
    textAlign: 'center',
  },
  originStnName: {
    fontFamily: 'Lato',
    fontWeight: '400',
    fontSize: 12,
    color: colors.greyText1,
    flexWrap: 'wrap',
    width: '20%',
    textAlign: 'center',
  },
  originStn: {
    fontFamily: 'Lato',
    fontWeight: '400',
    fontSize: 12,
    color: colors.greyText1,
    flexWrap: 'wrap',
    textAlign: 'center',
  },
  boardTxt: {
    color: colors.textGrey,
    fontSize: 12,
    fontWeight: '400',
    flexWrap: 'wrap',
    textAlign: 'center',
  },
  getDownTxt: {
    fontSize: 12,
    fontWeight: '400',
    flexWrap: 'wrap',
    color: colors.textGrey,
    textAlign: 'center',
  },
  boardStn: {
    flexWrap: 'wrap',
    color: colors.textGrey,
    width: '20%',
    alignContent: 'center',
    fontSize: 12,
    fontWeight: '400',
    textAlign: 'center',
  },
  dropStn: {
    flexWrap: 'wrap',
    width: '20%',
    alignContent: 'center',
    alignItems: 'center',
    fontSize: 12,
    textAlign: 'center',
  },
});
export default ConfirmedOptionsV2;
