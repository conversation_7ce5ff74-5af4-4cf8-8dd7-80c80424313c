import React from 'react';
import { StyleSheet, Text, View, Image } from 'react-native';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {getClassType} from '../../Types/ClassType';
import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../../vernacular/VernacularUtils';
import tripGuaranteeIcon from '@mmt/legacy-assets/src/tick_purple.webp';

const NewAvailabilityCard = ({ availabilityItem, id }) => {
  return (
    <Card style={cardStyle} elevation={2}>
      <View style={styles.row} testID={id}>
        <Text style={[styles.newClassText, fontStyle('black')]}>
          {getClassType(availabilityItem.classType).code}
        </Text>
        <Text style={[styles.newAmountText, fontStyle('black')]}>
          {numAppendedWithRuppeeSymbol(availabilityItem.totalFare)}
        </Text>
      </View>
      <Text style={styles.newAvailabilty}>
        {availabilityItem.prettyPrintingAvailablityStatus || availabilityItem.availablityStatus}
      </Text>
      {availabilityItem.confirmationGuaranteeText && (
        <View style={styles.confirmationGuaranteeContainer}>
          <Image source={tripGuaranteeIcon} style={styles.confirmationGuaranteeIcon} />
          <Text style={[styles.confirmationGuaranteeText, fontStyle('regular')]}>
            {availabilityItem.confirmationGuaranteeText}
          </Text>
        </View>
      )}
      <Text style={[styles.newLastUpdatedOn, fontStyle('regular')]}>
        {availabilityItem.lastUpdatedOn}
      </Text>
    </Card>
  );
};

NewAvailabilityCard.propTypes = {
  availabilityItem: PropTypes.shape({
    classType: PropTypes.string,
    totalFare: PropTypes.number,
    prettyPrintingAvailablityStatus: PropTypes.string,
    availablityStatus: PropTypes.string,
    lastUpdatedOn: PropTypes.string,
    confirmationGuaranteeText: PropTypes.string,
  }),
  id: PropTypes.string,
};

const AlternateAvailabilityCard = ({
  availabilityItem, onClick, id,
}) => {
  return (
    <TouchableRipple onPress={onClick} testID={`${id}_touchable_ripple`}>
      <View testID={id}>
        <NewAvailabilityCard availabilityItem={availabilityItem} id={id} />
      </View>
    </TouchableRipple>
  );
};

export default AlternateAvailabilityCard;

AlternateAvailabilityCard.propTypes = {
  availabilityItem: PropTypes.shape({
    classType: PropTypes.string,
    totalFare: PropTypes.number,
    prettyPrintingAvailablityStatus: PropTypes.string,
    availablityStatus: PropTypes.string,
    lastUpdatedOn: PropTypes.string,
    confirmationGuaranteeText: PropTypes.string,
  }),
  onClick: PropTypes.func,
  originalFare: PropTypes.number.isRequired,
  fareDifference: PropTypes.number.isRequired,
  id: PropTypes.string,
};

const cardStyle = {
  height: 90,
  minWidth: 152,
  marginHorizontal: 0,
  marginVertical: 12,
  padding: 8,
  marginRight: 8,
  marginLeft: 2,
  marginBottom: 2,
  borderWidth: StyleSheet.hairlineWidth,
  borderColor: '#e7e7e7',
  borderRadius: 4,
  marginTop: 0,
  position: 'relative',
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  newClassText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.black,
  },
  newAmountText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.black,
    marginLeft: 'auto',
  },
  newAvailabilty: {
    fontSize: 10,
    lineHeight: 16,
    color: colors.cyan,
  },
  newLastUpdatedOn: {
    fontSize: 10,
    color: colors.lightTextColor,
    position: 'absolute',
    bottom: 2,
    left: 8,
    lineHeight: 16,
  },
  lastUpdatedOn: {
    fontSize: 12,
    color: colors.lightTextColor,
    lineHeight: 18,
  },
  confirmationGuaranteeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  confirmationGuaranteeIcon: {
    width: 12,
    height: 12,
    marginRight: 4,
  },
  confirmationGuaranteeText: {
    fontSize: 10,
    color: colors.purple,
    lineHeight: 14,
  },
});
