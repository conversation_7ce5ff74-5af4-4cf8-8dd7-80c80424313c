import PropTypes from 'prop-types';
import React from 'react';
import {Image, PixelRatio, ScrollView, StyleSheet, Text, View,TouchableWithoutFeedback} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import isEmpty from 'lodash/isEmpty';
import isUndefined from 'lodash/isUndefined';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import {getFilterIcon} from './Filters/SvgComponents';

import morningIcon from '@mmt/legacy-assets/src/ic_morning.webp';
import afterNoonIcon from '@mmt/legacy-assets/src/ic_afternoon.webp';
import eveningIcon from '@mmt/legacy-assets/src/ic_evening.webp';
import nightIcon from '@mmt/legacy-assets/src/ic_night.webp';

export const FilterCard = ({
  heading,
  selected,
  subHeading = '',
  noOfTrains = undefined,
  onClick,
  version,
  OnFilterPage,
  id,
}) => {
  const cardStyle = selected ? filterCardStyle.cardSelected : filterCardStyle.cardDeSelected;
  const textStyle = selected ? [filterCardStyle.textSelected, fontStyle('regular'), getLineHeight(12)] : [filterCardStyle.textDeSelected, fontStyle('regular'), getLineHeight(12)];
  const subTextStyle = [fontStyle('bold'), getLineHeight(12),
    selected ?
    filterCardStyle.subTextSelected :
    filterCardStyle.subTextDeSelected];
  let headingText;
  if (isEmpty(subHeading)) {
    if (!isUndefined(noOfTrains) && version !== 'V2') {
      headingText = `${heading} (${noOfTrains})`;
    } else {
      headingText = heading;
    }
  } else if (!isUndefined(noOfTrains) && version !== 'V2') {
    headingText = ` - ${heading} (${noOfTrains})`;
  } else {
    headingText = ` - ${heading}`;
  }
  return (
    <TouchableWithoutFeedback onPress={onClick}>
      <View
        testID={`${id}_${heading}`}
        style={[cardStyle, version === 'V2' && OnFilterPage === false && filterCardStyle.halfWidthCard , version === 'V2' && OnFilterPage === true && filterCardStyle.halfWidthCardFilter ]}
      >
        <Text style={textStyle}>
          {
            !isEmpty(subHeading) &&
            <Text style={subTextStyle}>
              {subHeading}
            </Text>
          }
          {headingText}
        </Text>
      </View>
    </TouchableWithoutFeedback>
  );
};
export const FilterCardOnListing = ({
  heading, selected, onClick,
}) => {
  const textStyle = selected ? {
    ...filterCardStyle.textSelected,
    ...fontStyle('bold'),
    ...getLineHeight(12),
  } : {
    ...filterCardStyle.textSelected,
    ...fontStyle('regular'),
    ...getLineHeight(12),
  };
  return (
    <TouchableRipple onPress={onClick}>
      <View style={filterCardStyle.cardStyle}>
        {selected && <View style={filterCardStyle.circle} />}
        <Text numberOfLines={3} ellipsizeMode="tail" style={textStyle}>
          {heading}
        </Text>
      </View>
    </TouchableRipple>
  );
};

FilterCard.propTypes = {
  heading: PropTypes.string.isRequired,
  noOfTrains: PropTypes.number,
  onClick: PropTypes.func.isRequired,
  selected: PropTypes.bool.isRequired,
  subHeading: PropTypes.string,
  version: PropTypes.string,
  OnFilterPage: PropTypes.bool,
  id: PropTypes.string,
};

const filterCardStyle = {
  cardSelected: {
    backgroundColor: colors.azure,
    margin: 4,
    padding: 8,
    minHeight: 36,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    borderWidth: 1 / PixelRatio.getPixelSizeForLayoutSize(1),
    borderColor: 'transparent',
    ...getPlatformElevation(0.5),
  },
  cardDeSelected: {
    backgroundColor: colors.white,
    margin: 4,
    padding: 8,
    minHeight: 36,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    borderWidth: 1 / PixelRatio.getPixelSizeForLayoutSize(1),
    borderColor: '#e7e7e7',
    ...getPlatformElevation(2),
  },
  halfWidthCard: {
    width: '45%',
    margin: 8,
  },
  halfWidthCardFilter: {
    width: '40%',
    margin: 8,
  },
  circle: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.easternBlue,
    position: 'absolute',
    top: 5,
  },
  cardStyle: {
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textSelected: {
    color: colors.white,
    textAlign: 'center',
    paddingHorizontal: 10,
    maxWidth: 110,
  },
  textDeSelected: {
    color: colors.defaultTextColor,
    textAlign: 'center',
    paddingHorizontal: 10,
    maxWidth: 110,
  },
  subTextSelected: {
    fontSize: 12,
    color: colors.white,
    textAlign: 'center',
  },
  subTextDeSelected: {
    fontSize: 12,
    color: colors.defaultTextColor,
    textAlign: 'center',
  },
};

const tfCardStyles = StyleSheet.create({
  cardStyleV1: {
    paddingVertical: 10,
    alignItems: 'center',
    marginHorizontal: 4,
    borderRadius: 6,
  },
  cardStyleV2: {
    paddingVertical: 10,
    alignItems: 'center',
    marginHorizontal: 4,
    borderRadius: 6,
  },
  activeCardStyleV1: {

  },
  activeCardStyleV2: {
    backgroundColor: colors.primary,
  },
  titleV1: {
    fontSize: 10,
    marginHorizontal: 6,
    textAlign: 'center',
    color: colors.defaultTextColor,
  },
  titleV2: {
    fontSize: 10,
    marginHorizontal: 6,
    textAlign: 'center',
    color: colors.defaultTextColor,
  },
  subTitleV1: {
    fontSize: 9,
    lineHeight: 14,
    textAlign: 'center',
    color: colors.darkGrey2,
  },
  subTitleV2: {
    fontSize: 9,
    lineHeight: 14,
    textAlign: 'center',
    color: colors.darkGrey2,
  },
  activeTitleV1: {
    color: colors.azure,
  },
  activeImageV1: {

  },
  activeTitleV2: {
    color: colors.white,
  },
});

const TimeFilterCard = ({
  label, imageIcon, onClick, filterObject, noOfTrains, showTrainCount, version, iconType, id,
}) => {
  const {selected} = filterObject;
  const titleStyle = [tfCardStyles[`title${version}`], fontStyle('bold'), selected && tfCardStyles[`activeTitle${version}`]];
  const subTitleStyle = [tfCardStyles[`subTitle${version}`], fontStyle('medium'), selected && tfCardStyles[`activeTitle${version}`]];
  const imageStyle = [largeBoxStyles.image];
  if (selected) {
    // titleStyle.push(largeBoxStyles.activeTitle);
    // subTitleStyle.push(largeBoxStyles.activeTitle);
    imageStyle.push(largeBoxStyles.activeImage);
  }
  const trainCount = showTrainCount ? ` (${noOfTrains})` : '';
  return (
    <TouchableWithoutFeedback onPress={onClick}>
      <View style={{flex: 1}} testID={`${id}_${label}`}>
        <Card
          showBorder
          style={{
            ...StyleSheet.flatten(tfCardStyles[`cardStyle${version}`]),
            ...(selected ? StyleSheet.flatten(tfCardStyles[`activeCardStyle${version}`]) : {}),
          }}
        >
          {
            version === 'V2' ? getFilterIcon({
              element: iconType,
              elementType: 'timingFilter',
              isActive: selected,
            }) : <Image source={imageIcon} style={imageStyle} />
          }
          <Text style={titleStyle}>{`${label || filterObject.heading}${trainCount}`}</Text>
          <Text style={subTitleStyle}>{_label(filterObject.subHeading)}</Text>
        </Card>
      </View>
    </TouchableWithoutFeedback>
  );
};

TimeFilterCard.propTypes = {
  filterObject: PropTypes.object.isRequired,
  imageIcon: PropTypes.any.isRequired,
  noOfTrains: PropTypes.number.isRequired,
  onClick: PropTypes.func.isRequired,
  label: PropTypes.string,
  showTrainCount: PropTypes.bool,
  version: PropTypes.string,
  iconType: PropTypes.string,
  id: PropTypes.string,
};

const largeBoxStyles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  headerLabel: {
    fontSize: 14,
    color: colors.black,
    marginBottom: 12,
  },
  title: {
    fontSize: 10,
    marginHorizontal: 6,
    textAlign: 'center',
    color: colors.defaultTextColor,
  },
  activeTitle: {
    color: colors.azure,
  },
  subTitle: {
    fontSize: 9,
    lineHeight: 14,
    textAlign: 'center',
    color: colors.darkGrey2,
  },
  image: {
    width:28,
    height:28,
  },
  activeImage: {
    tintColor: colors.azure,
  },
});

const LargeBoxFilters = ({ filterType: filterObject, onFilterClick, headerLabel, version = 'V1', id }) => {
  const {
    nightFilter, morningFilter, afternoonFilter, eveningFilter,
  } = filterObject.filterTypes;
  return (
    <View style={largeBoxStyles.wrapper}>
      <Text style={[largeBoxStyles.headerLabel, fontStyle('bold'), getLineHeight(14)]}>
        {headerLabel || filterObject.heading}
      </Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          display: 'flex',
          flexDirection: 'row',
          width: '100%',
        }}
      >
        <TimeFilterCard
          id={id}
          imageIcon={morningIcon}
          iconType="morning"
          label={_label('morning')}
          filterObject={morningFilter}
          onClick={() => { onFilterClick('morningFilter', filterObject); }}
          noOfTrains={morningFilter.noOfTrains}
          showTrainCount={version !== 'V2'}
          version={version}
        />
        <TimeFilterCard
          id={id}
          imageIcon={afterNoonIcon}
          iconType="afternoon"
          label={_label('afternoon')}
          filterObject={afternoonFilter}
          onClick={() => { onFilterClick('afternoonFilter', filterObject); }}
          noOfTrains={afternoonFilter.noOfTrains}
          showTrainCount={version !== 'V2'}
          version={version}
        />
        <TimeFilterCard
          id={id}
          label={_label('evening')}
          iconType="evening"
          imageIcon={eveningIcon}
          filterObject={eveningFilter}
          onClick={() => { onFilterClick('eveningFilter', filterObject); }}
          noOfTrains={eveningFilter.noOfTrains}
          showTrainCount={version !== 'V2'}
          version={version}
        />
        <TimeFilterCard
          id={id}
          imageIcon={nightIcon}
          iconType="night"
          label={_label('night')}
          filterObject={nightFilter}
          onClick={() => { onFilterClick('nightFilter', filterObject); }}
          noOfTrains={nightFilter.noOfTrains}
          showTrainCount={version !== 'V2'}
          version={version}
        />
      </ScrollView>
    </View>
  );
};

LargeBoxFilters.propTypes = {
  onFilterClick: PropTypes.func.isRequired,
  filterType: PropTypes.object.isRequired,
  headerLabel: PropTypes.string,
  version: PropTypes.string,
  id: PropTypes.string,
};

const sbFilterStyles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
    justifyContent: 'space-between',
  },
});

const SmallBoxFilters = ({ filterType: filterObject = {}, onFilterClick, version, id }) => {
  const filterObjectArray = filterObject.filterTypesArray || [];
  return (
    <View style={sbFilterStyles.wrapper}>
      {
        filterObjectArray.map((item) => {
          return (
              <FilterCard
                id={id}
                key={filterObject.filterTypes[item].key}
                heading={filterObject.filterTypes[item].heading}
                subHeading={filterObject.filterTypes[item].subHeading}
                noOfTrains={filterObject.filterTypes[item].noOfTrains}
                selected={filterObject.filterTypes[item].selected}
                onClick={() => { onFilterClick(item, filterObject); }}
                version={version}
                OnFilterPage={false}
              />
          );
        })
      }
    </View>
  );
};

SmallBoxFilters.propTypes = {
  onFilterClick: PropTypes.func.isRequired,
  filterType: PropTypes.object.isRequired,
  headerLabel: PropTypes.string,
  version: PropTypes.string,
  id: PropTypes.string,
};

FilterCardOnListing.propTypes = {
  heading: PropTypes.string,
  selected: PropTypes.bool,
  subHeading: PropTypes.string,
  noOfTrains: PropTypes.number,
  onClick: PropTypes.func,
};

SmallBoxFiltersWithHeader.propTypes = {
  filterType: PropTypes.object,
  onFilterClick: PropTypes.func,
  headerLabel: PropTypes.string,
  version: PropTypes.string,
  id: PropTypes.string,
};


const SmallBoxFiltersWithHeader = ({ filterType: filterObject = {}, onFilterClick, headerLabel, version, id }) => {
    const filterObjectArray = filterObject.filterTypesArray || [];
    return (
      <View style={{ paddingHorizontal: 16, paddingVertical: 15 }}>
        <Text style={{
          fontSize: 14, lineHeight:20,  color: colors.black, marginBottom: 12, ...fontStyle('bold'),
        }}
        >
          {headerLabel || filterObject.heading}
        </Text>
        <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
          {
            filterObjectArray.map((item) => {
              return (
                <FilterCard
                  id={id}
                  key={filterObject.filterTypes[item].key}
                  heading={filterObject.filterTypes[item].heading}
                  subHeading={filterObject.filterTypes[item].subHeading}
                  noOfTrains={filterObject.filterTypes[item].noOfTrains}
                  selected={filterObject.filterTypes[item].selected}
                  onClick={() => { onFilterClick(item, filterObject); }}
                  version={version}
                  OnFilterPage={true}
                />
              );
            })
          }
        </View>
      </View>
    );
  };

const NoTrainsAlert = () => (
  <View style={{backgroundColor: colors.creamWhite, paddingHorizontal: 8, paddingVertical: 8}}>
    <Text style={{fontSize: 12,  color: colors.lightYello}}>
      { _label('too_many_filters') }
    </Text>
  </View>
);

export {SmallBoxFilters, LargeBoxFilters , SmallBoxFiltersWithHeader, NoTrainsAlert};
