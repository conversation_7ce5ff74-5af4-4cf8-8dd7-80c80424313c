import React, { useState } from 'react';
import { TouchableOpacity, View, Text, ActivityIndicator } from 'react-native';
import { styles } from './ScheduleTatkalListingCard.styles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface ScheduleTatkalListingCardProps {
  item: unknown;
  onScheduleTatkalCardClick: (item: unknown) => void;
  showLoader: boolean;
}

function ScheduleTatkalListingCard({
  item,
  onScheduleTatkalCardClick,
}: ScheduleTatkalListingCardProps) {
  const [showLoader, setShowLoader] = useState(false);

  const handleCardClick = async () => {
    if (showLoader) {
      return;
    }
    setShowLoader(true);
    await onScheduleTatkalCardClick(item);
    setShowLoader(false);
  };

  return (
    <View>
      <View style={styles.tatkalAlertCard}>
        <Text style={styles.tatkalAlertText}>{_label('tatkal_alert')}</Text>
      </View>
      <View style={styles.cardContainer}>
        <TouchableOpacity
            onPress={() => {
              handleCardClick();
            }}
          style={styles.cardTouchable}
          activeOpacity={0.6}
        >
          {showLoader && (
            <View style={styles.loaderStyle}>
              <ActivityIndicator color={colors.azure} />
            </View>
          )}
          {!showLoader && (
            <View style={styles.cardStyle}>
              <View style={styles.row}>
                <Text style={styles.classText}>{item?.classType}</Text>
              </View>
              <Text style={styles.reminderTextStyle}>{_label('set_remainder')}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

export default ScheduleTatkalListingCard;
