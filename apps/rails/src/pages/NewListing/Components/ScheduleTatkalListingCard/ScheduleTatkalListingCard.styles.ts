import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { Platform, StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: 'column',
    flex: 1,
    marginVertical: 12,
    marginRight: 8,
    marginLeft: 2,
    marginBottom: 11,
    marginTop: 2,
    gap: 0.5,
  },
  cardTouchable: {
    height: 90,
    minWidth: 152,
    backgroundColor: colors.white,
    ...getPlatformElevation(Platform.select({ android: 4, ios: 2 })),
    shadowRadius: 4,
    borderRadius: 4,
  },
  reminderTextStyle: {
    height: 14,
    fontFamily: 'Lato',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 14.4,
    color: colors.primary,
    alignItems: 'center',
    marginTop: 'auto',
    marginBottom: 8,
  },
  classText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.black,
  },
  cardStyle: {
    flex: 1,
    padding: 8,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  tatkalAlertText: {
    color: colors.brown2,
    fontSize: 10,
    fontWeight: '700',
    fontStyle: 'italic',
    fontFamily: 'Lato',
  },
  tatkalAlertCard: {
    top: -5,
    right: 15,
    position: 'absolute',
    borderRadius: 4,
    paddingHorizontal: 4,
    marginLeft: 'auto',
    zIndex: 999,
    elevation: 999,
    backgroundColor: colors.creamWhite,
  },
  loaderStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 50,
  },
});
