import {Image, Text, View, StyleSheet} from 'react-native';
import React from 'react';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import {_label} from '../../../vernacular/AppLanguage';
import blueTick from '@mmt/legacy-assets/src/blue_tick_3x.webp';

export const QuotaItem = ({
  selected, quotaName, onSelect, itemID, id,
}) => {
  const commonQuotaTextStyle = {fontSize: 12,  color: '#008cff'};
  const quotaTextStyle = [      fontStyle('regular','en') , getLineHeight(12), {
                                ...commonQuotaTextStyle,
                                ...(selected  ? { fontWeight: 'bold'} : {}),
                             }];
  return (
    <TouchableRipple
      testID={id}
      onPress={() => {
        onSelect(itemID);
      }}
    >
      <View style={styles.container}>
        {selected &&
        <Image source={blueTick} style={styles.image} />
        }
        <Text style={quotaTextStyle}>{_label(quotaName)} </Text>
      </View>
    </TouchableRipple>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row', paddingHorizontal: 12, alignItems: 'center', paddingVertical: 12,
  },
  image: {width: 12, height: 12, marginRight: 4},
});

QuotaItem.propTypes = {
  itemID: PropTypes.string.isRequired,
  onSelect: PropTypes.func.isRequired,
  quotaName: PropTypes.string.isRequired,
  selected: PropTypes.bool.isRequired,
  id: PropTypes.string,
};
