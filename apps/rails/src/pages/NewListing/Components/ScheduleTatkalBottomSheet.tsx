import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, Dimensions } from 'react-native';
import { connect } from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import {
  openScheduleTatkalTravelerPage,
  toggleScheduleTatakalBottomSheet,
} from '../RailsListingActionsV3';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { formatScheduleTatkalOpenTime } from 'apps/rails/src/pages/ScheduleTatkalTravelerPage/utils/ScheduleTatkalTravelerUtils';
import { SCHEDULE_TATKAL_EVENTS } from 'apps/rails/src/Utils/RailsConstant';
import { trackNewListingClickEvent } from 'apps/rails/src/railsAnalytics';
import PropTypes from 'prop-types';

const newBookingOpenImage = ASSETS.scheduleTatkalBookingOpen;
import crossIcon from '@mmt/legacy-assets/src/grey_cross.webp';
const tatkalRemainderImage = ASSETS.scheduleTatakalRemainder;

const Header = ({ closeBottomSheet }) => (
  <TouchableOpacity
    style={styles.crossButton}
    onPress={() => closeBottomSheet(false)}
    testID="schedule_tatkal_bottomsheet_close_button"
  >
    <Image source={crossIcon} style={styles.crossIcon} />
  </TouchableOpacity>
);

interface TatkalAlertBottomSheetProps {
  tatkalWindowOpenTimeEpoch: number;
  closeBottomSheet: () => void;
  openScheduleTatkalTravelerPageAction: () => void;
}

export function ScheduleTatkalBottomSheet({
  tatkalWindowOpenTimeEpoch,
  closeBottomSheet,
  openScheduleTatkalTravelerPageAction,
}: TatkalAlertBottomSheetProps) {
  const handleClick = () => {
    openScheduleTatkalTravelerPageAction();
    closeBottomSheet();
    trackNewListingClickEvent(SCHEDULE_TATKAL_EVENTS.listingScheduleTatkalContinueClick);
  };

  return (
    <View style={styles.bottomSheetContainer} testID="schedule_tatkal_bottomsheet_container">
      <View style={styles.headerContainer}>
        <View style={styles.introAlertContainer}>
          <Text style={[styles.introducingText, getLineHeight(18), fontStyle('regular')]}>
            {_label('introducing')}
          </Text>
          <Text style={[styles.tatkalAlert, getLineHeight(18)]}>
            {_label('tatkal_alert_header')}
          </Text>
        </View>
        <Text style={[styles.notifyText, getLineHeight(14), fontStyle('regular')]}>
          {_label('notify_when_tatkal_opens')}
        </Text>
        <Header closeBottomSheet={closeBottomSheet} />
      </View>

      <View style={styles.colorContainer}>
        <View style={styles.fixedContainer1}>
          <View style={styles.innerContainer}>
            <View style={styles.numberAndTextContainer}>
              <View style={styles.innerContainerTextBackground}>
                <Text style={[styles.innerContainerText, getLineHeight(16), fontStyle('bold')]}>
                  1
                </Text>
              </View>
              <View
                style={styles.textContainer}
                testID="schedule_tatkal_bottomsheet_text_container"
              >
                <Text style={[styles.fixedText, getLineHeight(16), fontStyle('regular')]}>
                  {_label('select_prefered_class_berth')}
                </Text>
                <Text
                  style={[styles.tatkalAlertCardsText, getLineHeight(14), fontStyle('regular')]}
                >
                  {_label('tatkal_alert_card_text')}
                </Text>
              </View>
            </View>
            <Image source={tatkalRemainderImage} style={styles.tatkalRemainderImage} />
          </View>
        </View>

        <View style={styles.fixedContainer2} testID="schedule_tatkal_bottomsheet_fixed_container_2">
          <View style={styles.innerContainer}>
            <View style={styles.numberAndTextContainer}>
              <View style={styles.innerContainerTextBackground}>
                <Text style={[styles.innerContainerText, getLineHeight(16), fontStyle('bold')]}>
                  2
                </Text>
              </View>
              <View style={styles.textContainer}>
                <Text style={[styles.fixedText, getLineHeight(16), fontStyle('regular')]}>
                  {_label('fill_your_details')}
                </Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.fixedContainer3}>
          <View style={styles.innerContainer}>
            <View style={styles.numberAndTextContainer}>
              <View style={styles.innerContainerTextBackground}>
                <Text style={[styles.innerContainerText, getLineHeight(16), fontStyle('bold')]}>
                  3
                </Text>
              </View>
              <View style={styles.textContainer}>
                <View style={styles.notificationContainer}>
                  <Text style={[styles.fixedText, getLineHeight(16), fontStyle('regular')]}>
                    {_label('get_notified_before_window_open')}
                  </Text>
                </View>
                <Text
                  style={[styles.tatkalAlertCardsText, getLineHeight(14), fontStyle('regular')]}
                >
                  {_label('quickly_complete_booking')}
                </Text>
              </View>
            </View>
            <Image source={newBookingOpenImage} style={styles.newBookingOpenImage} />
          </View>
        </View>
      </View>
      <View style={styles.bookingOpensContainer}>
        <Text style={[styles.bookingText, getLineHeight(12), fontStyle('semiBold')]}>
          {_label('tatkal_booking_opens_at')}
        </Text>
        <Text style={[styles.bookingTimeText, getLineHeight(12), fontStyle('semiBold')]}>
          {formatScheduleTatkalOpenTime(tatkalWindowOpenTimeEpoch)}
        </Text>
      </View>
      <View>
        <TouchableRipple onPress={handleClick} testID="schedule_tatkal_bottomsheet_button">
          <LinearGradient
            colors={[colors.lightBlue, colors.darkBlue]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.scheduleTatkalButton}
          >
            <Text style={[styles.scheduleTatkalButtonText, getLineHeight(16), fontStyle('bold')]}>
              {_label('schedule_tatakal', { uppercase: true })}
            </Text>
          </LinearGradient>
        </TouchableRipple>
      </View>
    </View>
  );
}

Header.propTypes = {
  closeBottomSheet: PropTypes.func,
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  bottomSheetContainer: {
    height: 498,
    padding: 20,
    backgroundColor: colors.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    justifyContent: 'flex-start',
  },
  headerContainer: {
    marginBottom: 4,
    marginLeft: 8,
  },
  introAlertContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  introducingText: {
    color: colors.textGrey,
    fontWeight: '400',
  },
  tatkalAlert: {
    fontStyle: 'italic',
    color: colors.black,
    marginLeft: 4,
    fontWeight: '600',
  },
  notifyText: {
    color: colors.textGrey,
    marginTop: 4,
  },
  crossButton: {
    position: 'absolute',
    bottom: 20,
    right: 0,
    zIndex: 10,
  },
  crossIcon: {
    width: 24,
    height: 24,
  },
  colorContainer: {
    width: width - 40,
    height: 280,
    backgroundColor: colors.lighterBlue,
    borderRadius: 20,
    borderColor: colors.lightBlue22,
    borderWidth: 1,
    marginTop: 15,
    marginLeft: 4,
    marginRight: 4,
    alignItems: 'center',
  },
  fixedContainer1: {
    width: width - 40,
    height: 102,
    padding: 12,
    marginBottom: 12,
  },
  fixedContainer2: {
    width: width - 40,
    padding: 12,
    marginTop: 12,
  },
  fixedContainer3: {
    width: width - 40,
    padding: 12,
    marginBottom: 4,
  },
  innerContainer: {
    alignItems: 'center',
  },
  numberAndTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  innerContainerTextBackground: {
    width: 26,
    height: 27,
    borderRadius: 8,
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 5,
    paddingRight: 5,
    backgroundColor: colors.lightBlue22,
  },
  innerContainerText: {
    textAlign: 'center',
    color: colors.textGrey,
    fontWeight: '900',
  },
  textContainer: {
    marginLeft: 12,
  },
  fixedText: {
    color: colors.black,
    fontWeight: '400',
  },
  notificationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  notificationTimeText: {
    color: colors.black,
    marginLeft: 4,
    fontWeight: '700',
  },
  tatkalAlertCardsText: {
    marginTop: 2,
    width: 266,
    color: colors.textGrey,
    fontWeight: '400',
  },
  tatkalRemainderImage: {
    width: 265,
    height: 67,
    borderRadius: 12,
    marginLeft: 35,
    marginTop: 25,
    alignSelf: 'flex-start',
    bottom: 15,
    borderWidth: 0.7,
    borderColor: colors.lightGray,
  },
  newBookingOpenImage: {
    width: 269,
    height: 46,
    borderRadius: 12.24,
    backgroundColor: colors.white,
    marginTop: 16,
    borderWidth: 0.5,
    borderColor: colors.lightGrey8,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scheduleTatkalButton: {
    height: 44,
    elevation: 4,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  scheduleTatkalButtonText: {
    color: colors.white,
    height: 19,
    fontWeight: '700',
  },

  bookingOpensContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    top: 55,
    marginBottom: 65,
  },
  bookingText: {
    color: colors.lightYello,
    fontWeight: '700',
  },
  bookingTimeText: {
    color: colors.lightYello,
    marginLeft: 4,
    fontWeight: '700',
  },
});

const mapStateToProps = (state) => {
  const { scheduleTatkalTraveler } = state;
  const { scheduleTatkalFormDetails } = scheduleTatkalTraveler;

  return {
    tatkalWindowOpenTimeEpoch: scheduleTatkalFormDetails?.tatkalWindowOpenTimeEpoch,
  };
};

const mapDispatchToProps = (dispatch) => ({
  closeBottomSheet: () => {
    dispatch(toggleScheduleTatakalBottomSheet(false));
  },
  openScheduleTatkalTravelerPageAction: () => {
    dispatch(openScheduleTatkalTravelerPage());
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(ScheduleTatkalBottomSheet);
