import {ScrollView, View, Text, StyleSheet, Image} from 'react-native';
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import AlternateAvailabilityCard from './AlternateAvailabilityCard';
import { getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { railsConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConfig';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import { ConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConstants';

const showConfirmedOptionsV2 = ( railsConfirmOptionsV2() === ConfirmOptionsV2.SHOWN);

export default class AlternateAvailabilityCardList extends PureComponent {
  handleCardClick = item => () => {
    const {trainData, onAvailabilityCardClick} = this.props;
    onAvailabilityCardClick(item, trainData);
  };

  render() {
    const {
      trainData,
    } = this.props;
    const {classAvailabilityList} = trainData;
    return (
      <View testID={this.props?.id}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          testID={`${this.props?.id}_scroll_view`}
        >
          {classAvailabilityList.map((item) => (
            <View
              key={item?.travelClass}
              style={styles.availabilityItem}
              testID={`${this.props?.id}_availability_item_${item?.travelClass}`}
            >
              {showConfirmedOptionsV2 ? (
                <View
                  style={styles.cnfoptionIconContainer}
                  testID={`${this.props?.id}_cnfoption_icon_container_${item?.travelClass}`}
                >
                  <Image
                    source={ASSETS.confirmedOptionsIcon}
                    style={styles.cnfoptionIcon}
                    testID={`${this.props?.id}_cnfoption_icon_${item?.travelClass}`}
                  />
                  <Text
                    style={[styles.cnfOptionText, getLineHeight(14.4)]}
                    testID={`${this.props?.id}_cnfoption_text_${item?.travelClass}`}
                  >
                    {_label('confirmed_options')}
                  </Text>
                </View>
              ) : null}
              <AlternateAvailabilityCard
                key={item?.travelClass}
                id={`${this.props?.id}_${item?.tbsAvailability?.quota}_${item?.tbsAvailability?.classType}`}
                availabilityItem={item?.tbsAvailability}
                originalFare={item?.originalFare}
                fareDifference={item?.fareDifference}
                onClick={this?.handleCardClick(item)}
              />
            </View>
          ))}
        </ScrollView>
      </View>
    );
  }
}

AlternateAvailabilityCardList.propTypes = {
  onAvailabilityCardClick: PropTypes.func.isRequired,
  trainData: PropTypes.object.isRequired,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  availabilityItem: {
    flexDirection: 'column',
    alignItems: 'center',
    marginRight: 10,
  },
  cnfoptionIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 5,
    marginLeft: -10,
    marginBottom: 7,
  },
  cnfoptionIcon: {
    width: 18,
    height: 18,
    marginRight: 5,
  },
  cnfOptionText: {
    fontSize: 12,
    color: colors.greyText1,
    fontFamily: 'lato',
  },
});
