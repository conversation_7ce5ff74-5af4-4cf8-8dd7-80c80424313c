import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import { getClassType } from '../../Types/ClassType';
import { numAppendedWithRuppeeSymbol } from '@mmt/legacy-commons/Common/utils/NumberUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import AvailabilityStatusValues from '../../Types/AvailabilityStatusValues';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

export const checkForWaitlistTG = ({
  isNewNearbyDates,
  showGreenTGWL,
  confirmationGuaranteeText,
}) => {
  if (!isNewNearbyDates && showGreenTGWL && confirmationGuaranteeText !== null) {
    return true;
  } else {
    return false;
  }
};

export const getColorBasedOnAvailabilityType = (availabilityType, isNearBy = false,
    confirmationGuaranteeText, showGreenTGWL, isNewNearbyDates) => {
  const stylesAvailable = {fontSize: 12, ...fontStyle('regular'), ...getLineHeight(12), color: colors.cyan};
  const stylesNotAvailable = {color: colors.lightTextColor, fontSize: 12, ...fontStyle('regular'), ...getLineHeight(12)};
  let stylesWL = {fontSize: 12, ...fontStyle('regular'), ...getLineHeight(12), color: colors.yello};
  const checkforWaitlisted = checkForWaitlistTG({
    isNewNearbyDates,
    showGreenTGWL,
    confirmationGuaranteeText,
  });
  if (checkforWaitlisted) {
    stylesWL = { fontSize: 12, ...fontStyle('regular'), ...getLineHeight(12), color: colors.cyan };
  }
  if (isNearBy) {
    stylesWL = { fontSize: 12, ...fontStyle('regular'), ...getLineHeight(12), color: colors.red7 };
    if (checkforWaitlisted) {
      stylesWL = {
        fontSize: 12,
        ...fontStyle('regular'),
        ...getLineHeight(12),
        color: colors.cyan,
      };
    }
  }
  if (availabilityType === '1' || availabilityType === '2') {
    return stylesAvailable;
  } else if (availabilityType === AvailabilityStatusValues.NO_ROOM.value
    || availabilityType === AvailabilityStatusValues.NT.value
    || availabilityType?.startsWith(AvailabilityStatusValues.DEPARTED.value)) {
    return stylesNotAvailable;
  } else {
    return stylesWL;
  }
};

export const AvailabilityCard = ({ availabilityItem, onClick, isTatkal, id }) => {
  const availabilityStyle = getColorBasedOnAvailabilityType(availabilityItem.availablityType);
  return (
      <TouchableRipple onPress={onClick}>
      <View testID={id}>
          <Card style={{
            justifyContent: 'space-between',
            height: 104,
            minWidth: 170,
            marginHorizontal: 0,
            marginVertical: 12,
            marginRight: 12,
            paddingHorizontal: 16,
            paddingVertical: 12,
            marginLeft: 2,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderColor: colors.lightGrey,
          }}
          >
            {
              isTatkal &&
              <LinearGradient
                style={styles.gradientContainer}
                colors={[colors.goldenYellow, colors.yello]}
                start={{
                  x: 0,
                  y: 2,
                }}
                end={{
                  x: 1,
                  y: 1,
                }}
              >
                <Text style={{ fontSize: 10, color: colors.white, ...fontStyle('bold') }}>{_label('tatkal', { uppercase: true })}</Text>
              </LinearGradient>
            }
            <Text style={[styles.classText, fontStyle('black'), getLineHeight(14)]}>
              {_label(getClassType(availabilityItem.className).value)}
            </Text>
            <RupeeText style={[styles.amountText, fontStyle('black'), getLineHeight(14)]}>
              {numAppendedWithRuppeeSymbol(availabilityItem.totalFare)}
            </RupeeText>
            <Text style={availabilityStyle}>
              {availabilityItem.prettyPrintingAvailablityStatus || availabilityItem.availablityStatus}
            </Text>
            <Text style={[styles.lastUpdatedOn, fontStyle('regular'), getLineHeight(12)]}>
              {availabilityItem.lastUpdatedOn}
            </Text>
          </Card>
        </View>
      </TouchableRipple>
  );
};

AvailabilityCard.propTypes = {
  availabilityItem: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
  isTatkal: PropTypes.bool,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  classText: {
    fontSize: 14, fontWeight: 'bold', color: colors.black,
  },
  amountText: {
    fontSize: 14,  fontWeight: 'bold', color: colors.black,
  },
  lastUpdatedOn: {
    fontSize: 12,  color: colors.lightTextColor,
  },
  gradientContainer: {
    height: 16,
    width: 64,
    borderRadius: 12,
    left: 12,
    top: -8,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
