import React from 'react';
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import isEmpty from 'lodash/isEmpty';
import { _label } from '../../../vernacular/AppLanguage';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import { SmallBoxFilters , LargeBoxFilters , SmallBoxFiltersWithHeader } from './SortAndFilterComponents';
import LinearGradient from 'react-native-linear-gradient';
import { fireOmnitureEventsNew } from '../../RailsFilters/RailsFilterActions';
import PropTypes from 'prop-types';
import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';

const JourneyClassFilters = ({
  journeyClassFilter,
  onFilterClick,
  onClearAllFiltersClicked,
  onDoneClick,
  onClearSelectedFilter,
  noTrainsFlag,
  resetState,
  version = 'V1',
}) => {
  console.log('VERSION', version);
    return (
        <View style={{width:'100%', backgroundColor: colors.white,alignSelf:'center' , borderRadius:4 , paddingHorizontal:'0%', paddingVertical: '0%' }}>
            {_renderTopIcons(_label('journey_class', { uppercase: true }), onDoneClick , onClearAllFiltersClicked, onClearSelectedFilter, journeyClassFilter, null, resetState, noTrainsFlag)}
            {noTrainsFlag && <NoTrainsAlert />}
            {!isEmpty(journeyClassFilter?.filterTypesArray) && (
                <React.Fragment>
                    {version === 'V1' && <View style={styles.separator} />}
                    <SmallBoxFilters
                        headerLabel={_label('journey_class', { uppercase: true })}
                        filterType={journeyClassFilter}
                        onFilterClick={onFilterClick}
                    />
                </React.Fragment>
            )}
        </View>
    );
};

JourneyClassFilters.propTypes = {
  journeyClassFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  onDoneClick: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  noTrainsFlag: PropTypes.bool,
  resetState: PropTypes.func,
  version: PropTypes.string,
};

const AvailabilityFilters = ({availabilityFilter, onFilterClick, onClearAllFiltersClicked,onDoneClick, onClearSelectedFilter,noTrainsFlag, resetState, version = 'V1'}) => {
    return (
        <View style={{width:'100%', backgroundColor: colors.white,alignSelf:'center' , borderRadius:4 , paddingHorizontal:'0%', paddingVertical: '0%' }}>
            {_renderTopIcons(_label('availability_filter', { uppercase: true }),onDoneClick, onClearAllFiltersClicked, onClearSelectedFilter, availabilityFilter, null, resetState, noTrainsFlag)}
            {!isEmpty(availabilityFilter?.filterTypesArray) && (
                <React.Fragment>
                    {version === 'V1' && <View style={styles.separator} />}
                    <SmallBoxFilters
                        headerLabel={_label('Availability')}
                        filterType={availabilityFilter}
                        onFilterClick={onFilterClick}
                    />
                </React.Fragment>
            )}
        </View>
    );
};

AvailabilityFilters.propTypes = {
  availabilityFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  onDoneClick: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  noTrainsFlag: PropTypes.bool,
  resetState: PropTypes.func,
  version: PropTypes.string,
};

const TimingFilters = ({labels, departureTimeFilter, arrivalTimeFilter, onFilterClick,
  onDoneClick, onClearAllFiltersClicked,onClearSelectedFilter, noTrainsFlag,resetState}) => {
    return (<View style={{width:'100%', backgroundColor: colors.white,alignSelf:'center' , borderRadius:4 , paddingHorizontal:'0%', paddingVertical: '0%' }}>

        {_renderTopIcons(_label('timing_filter', { uppercase: true }), onDoneClick, onClearAllFiltersClicked,onClearSelectedFilter, departureTimeFilter, arrivalTimeFilter, resetState, noTrainsFlag)}
        {noTrainsFlag && <NoTrainsAlert />}
        {!isEmpty(departureTimeFilter?.filterTypesArray) && (
            <React.Fragment>
                <LargeBoxFilters
                    labels={labels}
                    headerLabel={labels.departure_from_city}
                    filterType={departureTimeFilter}
                    onFilterClick={onFilterClick}
                />
            </React.Fragment>
        )}
        {!isEmpty(arrivalTimeFilter?.filterTypesArray) && (
            <React.Fragment>
                <LargeBoxFilters
                    labels={labels}
                    headerLabel={labels.arrival_in_city}
                    filterType={arrivalTimeFilter}
                    onFilterClick={onFilterClick}
                />
            </React.Fragment>
        )}
    </View>
    );
};

TimingFilters.propTypes = {
  labels: PropTypes.shape({
    departure_from_city: PropTypes.string,
    arrival_in_city: PropTypes.string,
  }),
  departureTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  arrivalTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  onDoneClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  noTrainsFlag: PropTypes.bool,
  resetState: PropTypes.func,
};

const _renderTopIcons = (
  heading,
  onDoneClick,
  onClearSelectedFilter,
  filter1,
  filter2,
  resetState,
  noTrainsFlag,
) => (
    <View style={{ flexDirection: 'row', marginHorizontal: 12, marginTop: '4%' }}>
        <View style={{ flex: 3 }}>
            <Text style={{
              ...fontStyle('bold'),
              fontSize: 14,
              lineHeight:20,
              color: colors.black,
            }}>{heading}</Text>
        </View>
        <View style={{
          flex: 1,
          flexDirection: 'row',
        }}>
          <TouchableRipple  onPress={ filter2 === null ? ()=>{onClearSelectedFilter(filter1,`bottomSheet_${heading}_Clear`);} : ()=>{onClearSelectedFilter(filter1,`bottomSheet_${heading}_Clear`); onClearSelectedFilter(filter2); } }>
                <View >
                    <Text style={[filterHeaderStyles.clearAll, fontStyle('bold')]}>{_label('CLEAR')}</Text>
                </View>
            </TouchableRipple>
            <TouchableRipple onPress={() => {resetState();}} onPressIn={() => {onDoneClick(`bottomSheet_${heading}_Done`);}} disabled={noTrainsFlag}>
                <View >
                    <Text style={ !noTrainsFlag ? [filterHeaderStyles.clearAll, fontStyle('bold')] : [filterHeaderStyles.disabled, fontStyle('regular')]}>{_label('DONE')}</Text>
                </View>
            </TouchableRipple>
        </View>
    </View>
);

_renderTopIcons.propTypes = {
  heading: PropTypes.string,
  onDoneClick: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  filter1: PropTypes.object,
  filter2: PropTypes.object,
  resetState: PropTypes.func,
  noTrainsFlag: PropTypes.bool,
};

const NoTrainsAlert = () => (
  <View style={{backgroundColor: colors.creamWhite, paddingHorizontal: 8, paddingVertical: 8}}>
    <Text style={{fontSize: 13, fontWeight:'bold',  color: colors.red , textAlign:'center' }}>
      { _label('Too many filters applied , No Trains Found!') }
    </Text>
  </View>
);

const FiltersPage = ({
    labels,
    noTrainsFlag,
    journeyClassFilter,
    trainTypeFilter,
    departureTimeFilter,
    arrivalTimeFilter,
    fromStnFilter,
    toStnFilter,
    onFilterClick,
    ticketTypeFilter,
    availabilityFilter,
    quotaFilter,
    onCloseClick,
    onDoneClick,
    onClearAllFiltersClicked,
    resetState,
  }) => (
    <View style={{ height:500, width:'100%', backgroundColor: colors.white,alignSelf:'center' , borderRadius:4 , paddingHorizontal:'0%', paddingVertical: '2%' }}>
        <View style={{ flexDirection: 'row' , justifyContent:'flex-end' , marginBottom:'4%' , marginTop:'2%'}}>
            <View style={{ marginLeft: '5%' , flexBasis:'50%' }}>
                <Text style={[styles.headerText, fontStyle('bold')]}>{_label('Filters')}</Text>
            </View>
            <View>
            <TouchableRipple onPress = {()=>resetState()} onPressIn={() => { fireOmnitureEventsNew('Filter_Close'); onCloseClick();}}>
                <View>
                    <Image source={closeIcon} style={styles.headerIcon} />
                </View>
            </TouchableRipple>
            </View>
      </View>
      <View style={styles.separator} />
      {noTrainsFlag && <NoTrainsAlert />}
      <ScrollView style={{ flex: 1 }}>
        {!isEmpty(quotaFilter?.filterTypesArray) && (
          <React.Fragment>
            <SmallBoxFiltersWithHeader
              headerLabel={_label('Quota Filter')}
              filterType={quotaFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}


        {!isEmpty(ticketTypeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <SmallBoxFiltersWithHeader
              headerLabel={_label('ticket_type_filter', { capitalize: true })}
              filterType={ticketTypeFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}

        {!isEmpty(toStnFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <SmallBoxFiltersWithHeader
              headerLabel={labels.stations_in_destination_city}
              filterType={toStnFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}

        {!isEmpty(fromStnFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <SmallBoxFiltersWithHeader
              filterType={fromStnFilter}
              onFilterClick={onFilterClick}
              headerLabel={labels.stations_in_origin_city}
            />
          </React.Fragment>
        )}

        {!isEmpty(journeyClassFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <SmallBoxFiltersWithHeader
              headerLabel={_label('journey_class_filter')}
              filterType={journeyClassFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}
        {!isEmpty(departureTimeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <LargeBoxFilters
              labels={labels}
              headerLabel={labels.departure_from_city}
              filterType={departureTimeFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}

        {!isEmpty(arrivalTimeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <LargeBoxFilters
              labels={labels}
              headerLabel={labels.arrival_in_city}
              filterType={arrivalTimeFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}

  {/** Remove the following two filters, this was done for dev purpose only */}
        {!isEmpty(availabilityFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <SmallBoxFiltersWithHeader
              headerLabel={_label('Availability')}
              filterType={availabilityFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}


        {!isEmpty(trainTypeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={styles.separator} />
            <SmallBoxFiltersWithHeader
              headerLabel={_label('train_types')}
              filterType={trainTypeFilter}
              onFilterClick={onFilterClick}
            />
          </React.Fragment>
        )}

      </ScrollView>

      <View style={{flexDirection:'row', justifyContent:'flex-end', marginTop:'3%'}}>

                <View style={{flexBasis:'50%',marginTop:'2%'}}>
                <TouchableRipple onPressIn={()=>{onClearAllFiltersClicked();}} onPress={() => {onDoneClick('clear_filterPage');}}>
                    <View >
                        <Text style={[filterHeaderStyles.clearAll, fontStyle('bold')]}>{_label('CLEAR')}</Text>
                    </View>
                </TouchableRipple>
                </View>

            <View style={{marginRight:'5%'}}>
                <TouchableRipple onPress={()=>resetState()} onPressIn={() => {onDoneClick('filterSheet_apply');}} disabled={noTrainsFlag} >
                    <LinearGradient
                        style={{
                            borderRadius: 4, width:140,height: 27, marginTop: '2%', marginBottom: '4%', alignItems: 'center', justifyContent: 'center',
                        }}
                        colors={ !noTrainsFlag ? ['#53B2FE', '#065AF3'] : ['#9B9B9B','#9B9B9B'] }
                        start={{ x: 0.0, y: 0.0 }}
                        end={{ x: 1.0, y: 0.0 }}
                    >
                        <Text style={{
                            fontSize: 12 , lineHeight:15, color: colors.white, backgroundColor: colors.transparent, ...fontStyle('bold'),
                        }}
                        >{_label('APPLY')}
                        </Text>
                    </LinearGradient>
                </TouchableRipple>
            </View>

      </View>
    </View>
  );


FiltersPage.propTypes = {
  labels: PropTypes.shape({
    stations_in_origin_city: PropTypes.string,
    stations_in_destination_city: PropTypes.string,
    departure_from_city: PropTypes.string,
    arrival_in_city: PropTypes.string,
  }),
  noTrainsFlag: PropTypes.bool,
  journeyClassFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  trainTypeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  departureTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  arrivalTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  fromStnFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  toStnFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  ticketTypeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  availabilityFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  quotaFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onCloseClick: PropTypes.func,
  onDoneClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  resetState: PropTypes.func,
};

const styles = {
    separator: {
      height: 1,
      backgroundColor: colors.grayBg,
    },
    headerIcon: {
        width: 10,
        height: 10,
        marginRight: '5%',
        marginTop:4,
      },
      headerText: {
        color: colors.black,
        fontSize: 16,
        lineHeight:20,
      },
  };

const filterHeaderStyles = StyleSheet.create({
    container: { flexDirection: 'row', alignItems: 'center' },
    imageContainer: {
        height: 48, width: 48, justifyContent: 'center', alignItems: 'center',
    },
    image: { height: 24, width: 24 },
    textContainer: { justifyContent: 'center', marginLeft: 4 },
    filterText: { color: colors.defaultTextColor, fontSize: 18 },
    noOfTrains: { color: colors.lightTextColor, fontSize: 12 },
    clearAll: {
      fontSize: 12,
      lineHeight:14.4,
      color: colors.azure,
      padding: 5,
      textAlign:'center',
    },
    disabled: {
      fontSize: 12,
      lineHeight:14.4,
      color: colors.grey29,
      padding: 5,
      textAlign:'center',
    },
});

export {JourneyClassFilters, AvailabilityFilters, TimingFilters, FiltersPage};
