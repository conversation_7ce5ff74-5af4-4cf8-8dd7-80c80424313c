import React, {Component} from 'react';
import isEmpty from 'lodash/isEmpty';
import {
  Animated,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import { connect } from 'react-redux';
import {NewTrainInfo} from './NewTrainInfo';
import QuotaBar from '../Containers/NewQuotaSelectionContainer';
import ClassBar from '../Containers/NewClassTypeContainer';
import FutureAvailabilityList from '../Containers/FutureAvailabilityListContainer';
import AvailabilityCardList from './NewAvailabilityCardList';
import NonBookableBanner from './NonBookableBanner';
import NotAvailableEducationCard from './NotAvailableEducationCard';
import {
  displayAvailDepletionListingBottomSheet,
  getViewDetailsTrackingData,
  toggleTrainScheduleBottomSheet,
  updateTatkalAlertTooltip,
  setNewTbsAvailability,
  clearNotAvailableCard,
} from '../RailsListingActions';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {
  removeEventFromEvar99Variable,
  trackGenericEvar99Event,
  trackNewListingEvent,
} from '../../../railsAnalytics';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { _label, transformString } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import ConfirmedOptionTrainCard from '../Components/ConfirmedOptions/ConfirmedOptionTrainCard';
import  TrainNameAndNumber  from './TrainNameAndNumber';
import  NextRunningTrainDetails  from './NextRunningTrainDetails';
import {
  getNearbyDatesNew,
  getRailsAvailDepletion,
  getRailsWlUrgencyPersuasion,
  showRailsDynamicPriceIcon,
  showNewTrainSchedule,
  getRailsListingMealInfo,
  showListingSoldoutBanner,
  getRailsListingRegretEduApps,
} from '../../../RailsAbConfig';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { RIS_LANDING_TRAIN_SCHEDULE_SUCCESS } from 'apps/rails/src/RisAnalytics/RisAnalytics';
import {
  LISTING_TRAIN_SCHEDULE,
  TBS_STATUSES_TO_HANDLE,
  LISTING_TRAIN_SCHEDULE_WITH_ODT,
  LISTING_PAGE_TRACKING_KEY,
  AVAILABILITY_STATUS,
  REGRET_EDUCATION_EVAR99,
  REGRET_EDUCATION_PDT_EVENT,
  WIDGET_SECTION_SEARCHED,
  WIDGET_SECTION_BOOKED,
} from '@mmt/rails/src/Utils/RailsConstant';
import FutureAvailabilityListNew from './NearByDates/Containers/FutureAvailabilityListContainerNew';
import { onScheduleTatkalCardClick, toggleScheduleTatakalBottomSheet } from '../RailsListingActionsV3';
import { logTrainCardPdtClickEvents, logListingPdtClickEvents } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import {
  LISTING_TRAINS_SOLD_OUT_BANNER,
  trainScheduleV2,
  WlUrgencyPersuasion,
  LISTING_REGRET_EDU_POKUS,
} from '../../../RailsAbConstants';
import MealInfo from './MealInfo';

import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';
import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import { configKeys } from '../../../configStore/Common/constants';
import { getConfigStore } from '../../../configStore/Common/RailsConfigStoreUtils';


class TrainItem extends Component {
  constructor(props) {
    super(props);
    this.state = {
      detailList: false,
      animation: new Animated.Value(0.01),
      minHeight: 200,
      height: 0,
      show: false,
      isTrainCancelledOrDeparted: false,
      cancelledOrDepartedTrainStatus: '',
      isNewNearbyDates: false,
      showAvailDepletV1: false,
      showAvailDepletV2: false,
      showDynamicPriceIcon: false,
      showRailsListingMealInfo: false,
      cardYPosition: 0,
      seatsSoldoutConfig: null,
      showSoldOutBanner: false,
      hasTrackedGreyedTrain: false,
      trackedRegretEducationCard: null,
    };
    this.availabilityCardListRef = React.createRef();
  }

  async componentDidMount() {
    this.getTrainAvailabilityStatus();
    const isNewNearbyDates = this.props.disableNearbydates ? false : getNearbyDatesNew();
    const showRailsListingMealInfo = getRailsListingMealInfo() === 1;
    const showAvailDepletV1 = (getRailsAvailDepletion() === 1);
    const showAvailDepletV2 = (getRailsAvailDepletion() === 2);
    const showWlUrgencyPersuasion = getRailsWlUrgencyPersuasion() === WlUrgencyPersuasion.SHOWN;
    const newTrainSchedule = (showNewTrainSchedule() === trainScheduleV2.SHOWN);
    const showDynamicPriceIcon =
      showRailsDynamicPriceIcon() === 1 && this.props?.trainData?.isDynamicTrain;
    
    const showSoldOutBanner = showListingSoldoutBanner() === LISTING_TRAINS_SOLD_OUT_BANNER.SHOWN;
    const seatsSoldoutConfig = showSoldOutBanner ? 
    await getConfigStore(configKeys.RAILS_LISTING_SOLD_OUT_CONFIG) : null;

    this.setState({
      isNewNearbyDates,
      showAvailDepletV1,
      showAvailDepletV2,
      showWlUrgencyPersuasion,
      showDynamicPriceIcon,
      newTrainSchedule,
      showRailsListingMealInfo,
      seatsSoldoutConfig,
      showSoldOutBanner,
    });
  }

  UNSAFE_componentWillReceiveProps(){
      this.getTrainAvailabilityStatus();
  }

  getTrainAvailabilityStatus(){
    const statusToHandle = TBS_STATUSES_TO_HANDLE;
    const availabiltyStatusArray = this.props.trainData?.tbsAvailability ?? [];
    if (availabiltyStatusArray?.length === 0)
    {
      return;
    }
    for (let eachCard of availabiltyStatusArray){
      if (statusToHandle.includes(eachCard?.availablityStatus?.toLowerCase())){
        this.setState({
          isTrainCancelledOrDeparted: true,
          cancelledOrDepartedTrainStatus: eachCard?.availablityStatus,
        });
        return;
      }
    }
  }

  areAllClassesNonBookable() {
    const availabiltyStatusArray = this.props.trainData?.tbsAvailability ?? [];
    if (availabiltyStatusArray?.length === 0) {
      return false;
    }
    const nonBookableStatuses = [
      'booking not allowed',
      'not available',
      'class not exist',
      'regret'
    ];
    const allNonBookable = availabiltyStatusArray.every(card => {
      const status = card?.availablityStatus?.toLowerCase() || '';
      const isNonBookable = nonBookableStatuses.some(nonBookable => status.includes(nonBookable));
      return isNonBookable;
    });
  
    if (allNonBookable) {
      if (!this.state.hasTrackedGreyedTrain) {
        trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY, RAIL_EVENTS.LISTING.RAILS_TRAINS_SOLD_OUT_BANNER);
        this.setState({ hasTrackedGreyedTrain: true });
      }

      if (this.state.isNewNearbyDates) {
        const { trainData, toggleNearbyDatesList } = this.props;
        const { selectedNearbyCardsHashList = [] } = this.props.nearbyDatesInfo || {};
        
        const isTrainSelected = selectedNearbyCardsHashList.some(hash => 
          hash.startsWith(`${trainData?.trainNumber}*`)
        );

        if (isTrainSelected && toggleNearbyDatesList) {
          const activeHash = selectedNearbyCardsHashList.find(hash => 
            hash.startsWith(`${trainData?.trainNumber}*`)
          );
          const [, activeClassType, activeQuota, sectionIdentifier = ''] = (activeHash || '').split('*');
          
          toggleNearbyDatesList(false, trainData?.trainNumber, activeClassType, activeQuota, sectionIdentifier);
        }
      }
    }

    return allNonBookable;
  }

  getAvailabilityCard() {
    const {
      onEmptyAvailabilityCardClicked,
      onAvailabilityCardClicked,
      actionSaveRailofyZcShown,
      railsShowConfirmationChance,
      confirmationChance,
      trainData,
      labels,
      id,
      trainPosition,
      toggleNearbyDatesList,
      nearbyDatesCardClick,
      nearbyDatesInfo,
      toggleScheduleTatakalBottomSheet,
      onScheduleTatkalCardClick,
      logTrainCardPdtClickEvents,
      availabilityPercentage,
      showTatkalAlertTooltipPerSession,
      index,
      updateTatkalAlertTooltip,
      isShownTatkalAlertTooltip,
    } = this.props;

    let sectionIdentifier = '';
    if (trainData && trainData.uniqueCardId) {
      sectionIdentifier = trainData.uniqueCardId.split('_')[0] || '';
    }

    return (
      <View
        style={styles.marginHor16}
        ref={this.availabilityCardListRef}
        onLayout={() => {
          if (this.availabilityCardListRef.current) {
            this.availabilityCardListRef.current.measure((x, y, width, height, pageX, pageY) => {
              this.setState({ cardYPosition: pageY });
            });
          }
        }}
      >
        <AvailabilityCardList
          railsShowConfirmationChance={railsShowConfirmationChance}
          confirmationChance={confirmationChance}
          onUpdateCardClick={onEmptyAvailabilityCardClicked}
          onAvailabilityCardClick={onAvailabilityCardClicked}
          onScheduleTatkalCardClick={onScheduleTatkalCardClick}
          trainData={trainData}
          labels={labels}
          actionSaveRailofyZcShown={actionSaveRailofyZcShown}
          id={`${id}_trainAvailabilityCardList`}
          trainPosition={trainPosition}
          toggleNearbyDatesList={toggleNearbyDatesList}
          nearbyDatesCardClick={nearbyDatesCardClick}
          nearbyDatesInfo={nearbyDatesInfo}
          sectionIdentifier={sectionIdentifier}
          toggleScheduleTatakalBottomSheet={toggleScheduleTatakalBottomSheet}
          logTrainCardPdtClickEvents={logTrainCardPdtClickEvents}
          availabilityPercentage={availabilityPercentage}
          showTatkalAlertTooltipPerSession={showTatkalAlertTooltipPerSession}
          isShownTatkalAlertTooltip={isShownTatkalAlertTooltip}
          index={index}
          cardYPosition={this.state.cardYPosition}
          updateTatkalAlertTooltip={updateTatkalAlertTooltip}
          notAvailableCard={this.props.notAvailableCard}
          onUnavailableCardClick={this.handleUnavailableCardClick}
        />
      </View>
    );
  }

  getNoTrainBanner() {
    const bannerTitle = transformString(this.state.cancelledOrDepartedTrainStatus, 'capitalize');
    return (
      <View style={styles.noTrainContainer}>
        <Text style={[fontStyle('bold'), styles.trainStatusText]}>
          {_label(bannerTitle, { capitalize: true })}
        </Text>
      </View>
    );
  }

  handleUnavailableCardClick = (trainData, className, quota) => {
    let sectionIdentifier = '';
    if (trainData?.uniqueCardId) {
      sectionIdentifier = trainData.uniqueCardId.split('_')[0] || '';
    }

    if (sectionIdentifier === WIDGET_SECTION_SEARCHED || sectionIdentifier === WIDGET_SECTION_BOOKED) {
      return;
    }

    const { setNewTbsAvailability, notAvailableCard } = this.props;
    const clickedCardId = `${trainData.trainNumber}-${className}-${quota}`;
    if (notAvailableCard === clickedCardId) {
      this.props.clearNotAvailableCard();
      return;
    }

    const availabilityItem = trainData?.tbsAvailability?.find(avl =>
      avl?.className === className && avl?.quota === quota,
    );

    if (availabilityItem) {
      const response = {
        totalFare: availabilityItem.totalFare || 0,
        baseFare: availabilityItem.baseFare || 0,
        avlDayList: [availabilityItem],
      };
      setNewTbsAvailability(response, className, quota, trainData);
    }
  };

  renderNotAvailableEducationCard = () => {
    const { trainData, notAvailableCard } = this.props;
    const { isNewNearbyDates } = this.state;

    const ListingRegretEduPokus = getRailsListingRegretEduApps();
    if (ListingRegretEduPokus !== LISTING_REGRET_EDU_POKUS.SHOWN) {
      return null;
    }

    let sectionIdentifier = '';
    if (trainData?.uniqueCardId) {
      sectionIdentifier = trainData.uniqueCardId.split('_')[0] || '';
    }

    if (sectionIdentifier === WIDGET_SECTION_SEARCHED || sectionIdentifier === WIDGET_SECTION_BOOKED) {
      return null;
    }

    const hasSelectedCard = trainData?.tbsAvailability?.some(avl => {
      const cardIdentifier = `${trainData.trainNumber}-${avl.className}-${avl.quota}`;
      const hasUnavailableStatus =
        avl?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.NOT_AVAILABLE ||
        avl?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.REGRET ||
        avl?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.BOOKING_NOT_ALLOWED;
      const isSelectedCard = notAvailableCard === cardIdentifier;
      return hasUnavailableStatus && isSelectedCard;
    });

    const allClassesNonBookable = this.areAllClassesNonBookable();
    const showEducationCard = (
      hasSelectedCard &&
      !this.state.isTrainCancelledOrDeparted &&
      !allClassesNonBookable &&
      !isNewNearbyDates
    );

    if (showEducationCard) {
      if (this.state.trackedRegretEducationCard !== notAvailableCard) {
        this.props.logListingPdtClickEvents(REGRET_EDUCATION_PDT_EVENT);
        removeEventFromEvar99Variable(REGRET_EDUCATION_EVAR99);
        trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY, REGRET_EDUCATION_EVAR99);
        this.setState({ trackedRegretEducationCard: notAvailableCard });
      }
      return <NotAvailableEducationCard />;
    }
    return null;
  };

  getTrainListOrNoListComponent() {
    const {
      detailList,
      isTrainCancelledOrDeparted,
      seatsSoldoutConfig,
      showSoldOutBanner,
    } = this.state;
    if (detailList) {
      return null;
    }
    if (isTrainCancelledOrDeparted) {
      return this.getNoTrainBanner();
    }

    const allNonBookable = this.areAllClassesNonBookable();
    if (showSoldOutBanner && allNonBookable) {
      return <NonBookableBanner seatsSoldoutConfig={seatsSoldoutConfig} />;
    }
    return this.getAvailabilityCard();
  }

  _showNearbyDates = (e) => {
    if (e && e.preventDefault) {
      e.preventDefault();
    }
    this.props.clearNotAvailableCard();
    trackNewListingEvent(
      'mob_rail_listing_v2_nearby_dates_clicked',
      getViewDetailsTrackingData(this.props.trainData),
    );
    this.setState({detailList: !this.state.detailList, show: true}, () => {
      if (this.state.detailList) {
        this.props.onViewDetailsClicked(this.props.trainData);
        this.startAnimation();
      } else {
        this.setToDefault();
      }
    });
  };

  startAnimation = () => {
    const {minHeight, height} = this.state;
    const heightToAnimate = minHeight > height ? minHeight : height;
    Animated.timing(
      this.state.animation,
      {
        toValue: heightToAnimate,
        duration: 200,
        useNativeDriver: false,
      },
    ).start();
  };
  setToDefault = () => {
    Animated.timing(
      this.state.animation,
      {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      },
    ).start(() => { this.setState({show: false}); });
  };

  onLoad = () => {
    if (!isEmpty(this._ref)) {
      this?._ref?.measure((x, y, w, h) => {
        this.setState({height: h});
        Animated.timing(
          this.state.animation,
          {
            toValue: h,
            duration: 200,
            useNativeDriver: false,
          },
        ).start();
      });
    }
  };

  _renderConfirmedOptions = (trainNumber) => {
    let { alternateAvailabilityResponse } = this.props;
    if (Object.keys(alternateAvailabilityResponse).length === 0 ||
    !alternateAvailabilityResponse?.alternateAvailabilityList) {return <></>;}
    const data = alternateAvailabilityResponse;
    alternateAvailabilityResponse = {
      ...data,
      alternateAvailabilityList: data?.alternateAvailabilityList?.filter(
        (item) => item?.trainNumber === trainNumber,
      ),
    };
    return (
      <ConfirmedOptionTrainCard
        disableNearbydates={this.props.disableNearbydates}
        alternateAvailabilityResponse={alternateAvailabilityResponse}
        toggleTrainScheduleBottomSheet={this.props?.toggleTrainScheduleBottomSheet}
      />
    );
  };

  _openTrainScheduleBottomSheet = () => {
    const { trainData } = this.props;
    const { odt } = trainData;
    if (odt) {
      trackClickEventProp61(RIS_LANDING_TRAIN_SCHEDULE_SUCCESS, LISTING_TRAIN_SCHEDULE_WITH_ODT);
    }
    if (this.state?.newTrainSchedule){
      removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_TRAIN_SCHEDULE_V2_CLICK);
      trackGenericEvar99Event(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_TRAIN_SCHEDULE_V2_CLICK,
      );
    }
    trackClickEventProp61(RIS_LANDING_TRAIN_SCHEDULE_SUCCESS, LISTING_TRAIN_SCHEDULE);
    this.props.toggleTrainScheduleBottomSheet(true, this.props.trainData?.trainNumber);
  };

  _openTatkalBottomSheet = () => {
    this.props.toggleScheduleTatakalBottomSheet(true);
  };

  _renderListingAds = (trainPosition, listingAdsConfig) => {
    if (!listingAdsConfig || listingAdsConfig.totalAds < 1 || listingAdsConfig > 3) {
      return null;
    }
    if (trainPosition === listingAdsConfig.adPosition) {
      return null;
    }
  };

  render() {
    const {
      trainData = {},
      alternateAvailabilityResponse,
      nearbyDatesInfo,
      toggleNearbyDatesList,
      isOtherDayTrainsList,
      dynamicPriceConfig,
    } = this.props;
    const {
      isNewNearbyDates,
      showAvailDepletV1,
      showAvailDepletV2,
      showWlUrgencyPersuasion,
      showDynamicPriceIcon,
      showRailsListingMealInfo,
    } = this.state;
    const {
      availabilityDepletion = null,
      wlSeatUrgencyPersuasion = null,
      showAvailDepletIntro = false,
      foodAvail = false,
    } = trainData || {};
    const { selectedNearbyCardsHashList, selectedTrainsList } = nearbyDatesInfo || {
      selectedNearbyCardsHashList: [],
      selectedTrainsList: [],
    };
    let sectionIdentifier = '';
    if (trainData && trainData.uniqueCardId) {
      sectionIdentifier = trainData.uniqueCardId.split('_')[0] || '';
    }
    let cardIsSelectedForNearbyDates = false;
    let activeClassType = '';
    let activeQuota = '';

    if (selectedTrainsList.includes(trainData?.trainNumber)) {
      const activeHashForThisTrainNumber = selectedNearbyCardsHashList.find(hash => hash.startsWith(`${trainData?.trainNumber}*`));
      if (activeHashForThisTrainNumber) {
        const parts = activeHashForThisTrainNumber.split('*');
        activeClassType = parts[1];
        activeQuota = parts[2];
        const hashSection = parts.length > 3 ? parts[3] : '';
        if (hashSection === sectionIdentifier) {
          cardIsSelectedForNearbyDates = true;
        }
      }
    }

    const canShowToggle =
      this.state.cancelledOrDepartedTrainStatus?.toLowerCase() !== TBS_STATUSES_TO_HANDLE[2] &&
      !this.props.disableNearbydates &&
      !isNewNearbyDates &&
      !isOtherDayTrainsList;

    let nearbyDatesToggleComponent = null;
    if (canShowToggle) {
      if (cardIsSelectedForNearbyDates) {
        nearbyDatesToggleComponent = (
          <TouchableWithoutFeedback
            onPress={() => {
              toggleNearbyDatesList(false, trainData?.trainNumber, activeClassType, activeQuota, sectionIdentifier);
            }}
            testID={`${this.props?.id}_nearbyDates_close_${this.props?.idIndex}`}
          >
            <View
              style={[styles.nearByDatesContainer, this.state.detailList && styles.top6]} // Kept original style condition for now
              testID={`${this.props?.id}_nearbyDates_close_${this.props?.idIndex}`}>
              <Text style={styles.nearByDatesText}>
                {_label('nearby_dates')}
              </Text>
              <Image
                source={arrowUp}
                style={styles.arrowContainer}
                testID={`${this.props?.id}_nearbyDates_close_${this.props?.idIndex}_arrow`}
              />
            </View>
          </TouchableWithoutFeedback>
        );
      } else {
        nearbyDatesToggleComponent = (
          <TouchableWithoutFeedback onPress={this._showNearbyDates}>
            <View
              style={[styles.nearByDatesContainer, this.state.detailList && styles.top6]}
              testID={`${this.props?.id}_nearbyDates_open`}>
              {
                !this.state.detailList &&
                <Text style={styles.nearByDatesText}>
                  {_label('nearby_dates')}
                </Text>
              }
              <Image
                source={this.state.detailList ? arrowUp : arrowDown}
                style={styles.arrowContainer}
              />
            </View>
          </TouchableWithoutFeedback>
        );
      }
    }

    return (
      <View>
        <View testID={`${this.props?.id}`}>
          {
            <View style={styles.foodAndAvailDepletContainer}>
              <View>{showRailsListingMealInfo && foodAvail && <MealInfo />}</View>
              {showAvailDepletV1 && availabilityDepletion && (
                <TouchableOpacity
                  style={styles.marginLeft}
                  onPress={() => {
                    this.props.displayAvailDepletionListingBottomSheet(true);
                  }}
                >
                  <LinearGradient
                    style={styles.availDepletContainer}
                    colors={[
                      availabilityDepletion?.backgroundGradient?.start || colors.white,
                      availabilityDepletion?.backgroundGradient?.end || colors.white,
                    ]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    testID="avail_depletion_linear_gradient"
                  >
                    <Image
                      source={{ uri: availabilityDepletion?.mainIconUrl }}
                      style={styles.availDepletIcon}
                      testID="avail_depletion_icon"
                    />
                    <Text
                      style={[styles.availDepletText, { color: availabilityDepletion?.textColor }]}
                      testID="avail_depletion_text"
                    >
                      {availabilityDepletion?.text}
                    </Text>
                    <Image
                      source={{ uri: availabilityDepletion?.infoIconUrl }}
                      style={styles.infoIcon}
                      testID="avail_depletion_info_icon"
                    />
                  </LinearGradient>
                </TouchableOpacity>
              )}
              {showWlUrgencyPersuasion && wlSeatUrgencyPersuasion && (
                <View style={styles.marginLeft} testID="wl_urgency_persuasion_container">
                  <LinearGradient
                    style={styles.availDepletContainer}
                    colors={[
                      wlSeatUrgencyPersuasion?.backgroundGradient?.start || '#ffb366',
                      wlSeatUrgencyPersuasion?.backgroundGradient?.end || '#ff7f3f',
                    ]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    testID="wl_urgency_persuasion_linear_gradient"
                  >
                    <Text
                      style={[
                        styles.availDepletText,
                        { color: wlSeatUrgencyPersuasion?.textColor },
                      ]}
                      testID="wl_urgency_persuasion_text"
                    >
                      {wlSeatUrgencyPersuasion?.text}
                    </Text>
                  </LinearGradient>
                </View>
              )}
            </View>
          }
          <View style={{ paddingTop: 16, paddingBottom: 10, backgroundColor: colors.white }}>
            <View style={{ marginHorizontal: 16 }}>
              {isOtherDayTrainsList && (
                <NextRunningTrainDetails otherDayTrainsList={this.props.trainData} />
              )}
              <TrainNameAndNumber
                trainName={this.props.trainData.trainName}
                trainNumber={this.props.trainData.trainNumber}
                id={this.props?.id}
                showDaysOfRunChip={!isOtherDayTrainsList}
                trainData={trainData}
                showDynamicPriceIcon={showDynamicPriceIcon}
                dynamicPriceConfig={dynamicPriceConfig}
              />
              <View style={styles.marginTop12}>
                <NewTrainInfo trainData={this.props.trainData} id={`${this.props?.id}_trainInfo`} />
              </View>
            </View>
            {this.getTrainListOrNoListComponent()}
            {this.renderNotAvailableEducationCard()}
            {this.state.show && (
              <Animated.View style={{ height: this.state.animation }}>
                <View
                  ref={(ref) => {
                    this._ref = ref;
                  }}
                  onLayout={() => {
                    //
                  }}
                  testID={`${this.props?.id}_nearbyDatesOnClick`}
                >
                  <View style={styles.quotaBarContainer}>
                    <QuotaBar
                      trainData={this.props.trainData}
                      id={`${this.props?.id}_nearbyDatesOnClick_quotaBar`}
                    />
                  </View>
                  <View style={styles.marginTop12}>
                    <ClassBar
                      trainData={this.props.trainData}
                      id={`${this.props?.id}_nearbyDatesOnClick_classBar`}
                    />
                  </View>
                  <FutureAvailabilityList
                    id={`${this.props?.id}_nearbyDatesOnClick_futureAvailabilityList`}
                    trainData={this.props.trainData}
                    onLoad={this.onLoad}
                  />
                </View>
              </Animated.View>
            )}
            {this.state.cancelledOrDepartedTrainStatus?.toLowerCase() !==
              TBS_STATUSES_TO_HANDLE[2] &&
              cardIsSelectedForNearbyDates && (
                <View style={styles.outerContainer}>
                  <View style={styles.newNearByDatesContainer}>
                    <FutureAvailabilityListNew
                      id={`${this.props?.id}_nearbyDatesOnClick_futureAvailabilityListNew`}
                      trainData={{ ...this.props.trainData, sectionIdentifier }}
                      onLoad={this.onLoad}
                      nearbyDatesInfo={nearbyDatesInfo}
                    />
                  </View>
                </View>
              )}
            <View style={styles.trainExtrasContainer}>
              {nearbyDatesToggleComponent}
              {this.state.cancelledOrDepartedTrainStatus?.toLowerCase() !==
                TBS_STATUSES_TO_HANDLE[2] && (
                <TouchableWithoutFeedback
                  onPress={this._openTrainScheduleBottomSheet}
                  testID="train_schedule_bottom_sheet_touchable_without_feedback"
                >
                  <View
                    style={[
                      styles.trainScheduleTextContainer,
                      this.state.detailList && styles.top6,
                    ]}
                    testID="train_schedule_bottom_sheet_view"
                  >
                    <Text
                      style={[
                        { ...fontStyle('bold'), ...getLineHeight(12) },
                        styles.trainScheduleText,
                      ]}
                      testID="train_schedule_bottom_sheet_text"
                    >
                      {_label('see_train_schedule')}
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
              )}
            </View>
          </View>
        </View>
        {showAvailDepletV2 && showAvailDepletIntro && availabilityDepletion && (
          <LinearGradient
            style={styles.availDepletInfoContainer}
            colors={[
              availabilityDepletion?.backgroundGradient?.start || colors.white,
              availabilityDepletion?.backgroundGradient?.end || colors.white,
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            testID="avail_depletion_info_container"
          >
            <Image
              source={{ uri: availabilityDepletion?.mainIconUrl }}
              style={styles.availDepletBigIcon}
              testID="avail_depletion_info_container_icon"
            />
            <Text
              style={[styles.availDepletText, { color: availabilityDepletion?.textColor }]}
              testID="avail_depletion_info_container_text"
            >
              {_label('introducing')}
            </Text>
            <Text
              style={[styles.availDepletText2, { color: availabilityDepletion?.textColor }]}
              testID="avail_depletion_info_container_text_2"
            >
              {_label('seat_avail_forecast')}
            </Text>
            <TouchableOpacity
              onPress={() => {
                this.props.displayAvailDepletionListingBottomSheet(true);
              }}
              testID="avail_depletion_info_container_touchable_opacity"
            >
              <Text
                style={styles.learnMoreText}
                testID="avail_depletion_info_container_touchable_opacity_text"
              >
                {_label('learn_more')}
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        )}
        {alternateAvailabilityResponse?.alternateAvailabilityList?.length > 0 &&
          this._renderConfirmedOptions(trainData.trainNumber)}
        {this._renderListingAds(this.props?.trainPosition, this.props.listingAdsConfig)}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  emptyNode: {
    height: 30,
  },
  cnfOption: {
    fontSize: 12,
    color: colors.azure,
  },
  gradient: {
    paddingVertical: 15,
    height: 14,
    width: 150,
  },
  pad8: {
    padding: 8,
  },
  noTrainContainer: {
    borderRadius: 4,
    backgroundColor: colors.grayBg,
    paddingVertical: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
    marginHorizontal: 16,
    paddingVertical: 4,
  },
  marginHor16: {
    marginHorizontal: 16,
  },
  trainStatusText: {
    color: colors.textGrey,
  },
  trainScheduleTextContainer: {
    marginLeft: 'auto',
    alignSelf: 'center',
  },
  trainScheduleText: {
    color: colors.azure,
  },
  singleAdAlign: {
    alignSelf: 'center',
    paddingTop: 5,
  },
  listingAdContainer: {
    backgroundColor: colors.grayBg,
    paddingTop: 5,
  },
  nearByDatesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  top6: {
    top: 6,
  },
  trainExtrasContainer: {
    marginBottom: 2,
    marginHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  arrowContainer: {
    width: 24,
    height: 24,
  },
  nearByDatesText: {
    fontSize: 12,
    ...fontStyle('bold'),
    ...getLineHeight(12),
    color: colors.azure,
  },
  quotaBarContainer: {
    marginHorizontal: 16,
    marginTop: 12,
  },
  newNearByDatesContainer: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.lightSilver,
  },
  outerContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    marginBottom: 10,
  },
  marginTop12: {
    marginTop: 12,
  },
  availDepletBackground: {
    width: '100%',
    backgroundColor: colors.white,
  },
  availDepletContainer: {
    flexDirection: 'row',
    gap: 4,
    padding: 10,
    alignItems: 'center',
  },
  marginLeft: {
    marginLeft: 'auto',
  },
  availDepletIcon: {
    width: 10,
    height: 11,
    resizeMode: 'contain',
  },
  availDepletBigIcon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  availDepletText: {
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.5,
    color: colors.black,
    flexShrink: 0,
  },
  availDepletText2: {
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 14.5,
    color: colors.black,
  },
  infoIcon: {
    width: 14,
    height: 14,
    resizeMode: 'contain',
  },
  toolTipinfoIcon: {
    marginTop: 2,
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  availDepletInfoContainer: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 5,
    height: 28,
    gap: 2,
    alignItems: 'center',
  },
  learnMoreText: {
    color: colors.primary,
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '700',
    marginLeft: 6,
  },
  foodAndAvailDepletContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

const LoaderText = ({ loading, text = null, onPress, id }) => {
  return (
    <View style={{alignSelf: 'center'}}>
      {
        loading &&
        <LinearGradient
          style={styles.gradient}
          colors={['#fafafa', '#f2f2f2']}
          start={{x: 0.0, y: 0.0}}
          end={{x: 1.0, y: 0.0}}
        />
      }
      {
        !loading && !isEmpty(text) &&
        <TouchableRipple onPress={onPress}>
          <View style={styles.pad8} testID={id}>
            <Text style={[styles.cnfOption, fontStyle('bold'), getLineHeight(12)]}>
              {text}
            </Text>
          </View>
        </TouchableRipple>
      }
      {
        !loading && isEmpty(text) &&
          <View style={styles.emptyNode} />
      }
    </View>
  );
};

TrainItem.propTypes = {
  onViewDetailsClicked: PropTypes.func.isRequired,
  onEmptyAvailabilityCardClicked: PropTypes.func.isRequired,
  onAvailabilityCardClicked: PropTypes.func.isRequired,
  trainData: PropTypes.object.isRequired,
  tatkalExists: PropTypes.bool.isRequired,
  railsShowConfirmationChance: PropTypes.bool,
  confirmationChance: PropTypes.object,
  originStation: PropTypes.object.isRequired,
  destinationStation: PropTypes.object.isRequired,
  departureDate: PropTypes.object.isRequired,
  actionSaveRailofyZcShown: PropTypes.func.isRequired,
  alternateAvailabilityResponse: PropTypes.object,
  trainPosition: PropTypes.number,
  listingAdsConfig: PropTypes.object,
  id: PropTypes.string,
  idIndex: PropTypes.number,
  toggleNearbyDatesList: PropTypes.func,
  nearbyDatesCardClick: PropTypes.func,
  nearbyDatesInfo: PropTypes.shape({
    selectedTrainsList: PropTypes.arrayOf(PropTypes.string),
  }),
  isOtherDayTrainsList: PropTypes.bool,
  dynamicPriceConfig: PropTypes.object,
  displayAvailDepletionListingBottomSheet: PropTypes.func,
  toggleTrainScheduleBottomSheet: PropTypes.func,
  onScheduleTatkalCardClick: PropTypes.func,
  logTrainCardPdtClickEvents: PropTypes.func,
  logListingPdtClickEvents: PropTypes.func,
  labels: PropTypes.object,
  toggleScheduleTatakalBottomSheet: PropTypes.func,
  disableNearbydates: PropTypes.bool,
  availabilityPercentage: PropTypes.object,
  showTatkalAlertTooltipPerSession: PropTypes.bool,
  index: PropTypes.number,
  updateTatkalAlertTooltip: PropTypes.func,
  isShownTatkalAlertTooltip: PropTypes.bool,
  notAvailableCard: PropTypes.string,
  setNewTbsAvailability: PropTypes.func,
  clearNotAvailableCard: PropTypes.func,
};
TrainItem.defaultProps = {
  railsShowConfirmationChance: false,
  confirmationChance: null,
  alternateAvailabilityResponse: {},
};

LoaderText.propTypes = {
  loading: PropTypes.bool.isRequired,
  onPress: PropTypes.func.isRequired,
  text: PropTypes.string,
  id: PropTypes.string,
};

const mapDispatchToProps = {
  toggleTrainScheduleBottomSheet,
  displayAvailDepletionListingBottomSheet,
  toggleScheduleTatakalBottomSheet,
  onScheduleTatkalCardClick,
  logTrainCardPdtClickEvents,
  logListingPdtClickEvents,
  updateTatkalAlertTooltip,
  setNewTbsAvailability,
  clearNotAvailableCard,
};

const mapStateToProps = ( state, ownProps) => {
  const {
    railsListing: {
      alternateAvailabilityResponse,
      otherDayTrainsList,
      availabilityPercentage,
      showTatkalAlertTooltipPerSession,
      isShownTatkalAlertTooltip,
      notAvailableCard,
    },
  } = state;

  return {
    ...ownProps,
    alternateAvailabilityResponse,
    otherDayTrainsList,
    availabilityPercentage,
    showTatkalAlertTooltipPerSession,
    isShownTatkalAlertTooltip,
    notAvailableCard,
  };
};

export default connect( mapStateToProps, mapDispatchToProps)(TrainItem);
