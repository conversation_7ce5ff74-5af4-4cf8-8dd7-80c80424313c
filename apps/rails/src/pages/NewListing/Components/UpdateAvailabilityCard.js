import {Text, View, StyleSheet} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import React, {PureComponent} from 'react';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {getClassType} from '../../Types/ClassType';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';

export class UpdateAvailabilityCard extends PureComponent {
  render() {
    const {className, onClick, isTatkal,labels} = this.props;
    return (
      <TouchableRipple onPress={onClick}>
        <View testID={this.props?.id}>
          <Card style={{
            justifyContent: 'space-between',
            height: 104,
            width: 170,
            marginHorizontal: 0,
            marginVertical: 12,
            marginRight: 12,
            paddingHorizontal: 16,
            paddingVertical: 12,
            marginLeft: 2,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderColor: colors.lightGrey,
          }}
          >
            {
              isTatkal &&
              <LinearGradient
                style={styles.gradientContainer}
                colors={[colors.goldenYellow, colors.yello]}
                start={{
                  x: 0,
                  y: 2,
                }}
                end={{
                  x: 1,
                  y: 1,
                }}
              >
                <Text style={{ fontSize: 10, color: colors.white, fontFamily: fonts.bold }}>TATKAL</Text>
              </LinearGradient>
            }
            <Text style={styles.classTypeText}>
              {getClassType(className).value}
            </Text>
            <LinearGradient
              style={{height: 14}}
              colors={['#fafafa', '#f2f2f2']}
              start={{x: 0.0, y: 0.0}}
              end={{x: 1.0, y: 0.0}}
            />
            <LinearGradient
              style={{height: 14}}
              colors={['#fafafa', '#f2f2f2']}
              start={{x: 0.0, y: 0.0}}
              end={{x: 1.0, y: 0.0}}
            />
            <Text style={styles.tapText}>
            {labels.availibilty_card_tap_to_update}
            </Text>
          </Card>
        </View>
      </TouchableRipple>
    );
  }
}

UpdateAvailabilityCard.propTypes = {
  className: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  isTatkal: PropTypes.bool,
  labels: PropTypes.shape({
    availibilty_card_tap_to_update: PropTypes.string,
  }).isRequired,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  classTypeText: {
    fontSize: 14, fontFamily: fonts.regular, fontWeight: 'bold', color: colors.black,
  },
  tapText: {fontSize: 12, fontFamily: fonts.regular, color: colors.lightTextColor},
  gradientContainer: {
    height: 16,
    width: 64,
    borderRadius: 12,
    left: 12,
    top: -8,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
