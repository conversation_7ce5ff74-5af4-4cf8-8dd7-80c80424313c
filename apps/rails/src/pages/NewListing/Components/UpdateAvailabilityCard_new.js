/* eslint-disable */
import { Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from "../../../vernacular/VernacularUtils";
import getPlatformElevation from 'packages/legacy-commons/Styles/getPlatformElevation';

export class UpdateAvailabilityCardV2 extends PureComponent {
  render() {
    const { className, onClick, showQuotaChip, quotaLabel,  isLoading } = this.props;
    if (isLoading) {
      return (
        <View style={styles.cardTouchable}>
          <View style={styles.loaderContainer}>
            <Spinner size={20} color="#008b8b" />
          </View>
        </View>
      );
    }

    return (
      <TouchableOpacity style={styles.cardTouchable} onPress={onClick}>
        <View testID={this.props?.id} style={styles.cardStyle}>
            <View style={styles.row}>
              <View style={styles.classAndTatkaal}>
                <Text style={[styles.classTypeText, fontStyle("regular"), getLineHeight(12)]} testID={`${this.props?.id}_class`}>
                  {
                    className
                    // getClassType(className).value
                  }
                </Text>
                {showQuotaChip && (
                  <LinearGradient
                    style={styles.gradientContainer}
                    colors={[colors.goldenYellow, colors.yello]}
                    start={{
                      x: 0,
                      y: 2,
                    }}
                    end={{
                      x: 1,
                      y: 1,
                    }}
                  >
                    <Text style={[{ fontSize: 10, color: colors.white}, fontStyle("bold"), getLineHeight(10)]} testID={`${this.props?.id}_tatkal`}>
                      {quotaLabel}
                    </Text>
                  </LinearGradient>
                )}
              </View>
              <LinearGradient
                style={{ height: 12, flex: 1, top: 3 }}
                colors={['#fafafa', '#f2f2f2']}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 0.0 }}
              />
            </View>

            <LinearGradient
              style={{ height: 12, position: 'absolute', top: 32, width: '90%', left: 8 }}
              colors={['#fafafa', '#f2f2f2']}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 0.0 }}
            />
            <Text style={[styles.tapText, fontStyle("regular"), getLineHeight(10)]} testID={`${this.props?.id}_tapToUpdate`}>{_label("tap_to_update")}</Text>
        </View>
      </TouchableOpacity>
    );
  }
}

UpdateAvailabilityCardV2.propTypes = {
  className: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  classTypeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.black,
    height: 16,
    marginRight: 4,
  },
  tapText: {
    color: colors.lightTextColor,
    marginLeft: 8,
    marginBottom: 8,
  },
  gradientContainer: {
    height: 16,
    width: 64,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  row: {
    flex: 1,
    flexDirection: 'row',
    height: 12,
    justifyContent: 'space-between',
    marginHorizontal: 8,
  },
  classAndTatkaal: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  loaderContainer: {
    height: '100%',
    flex: 1,
    justifyContent: 'center',
    alignItems:'center'
  },
  cardTouchable: {
    flex: 1,
    height: 90,
    minWidth: 152,
    marginVertical: 12,
    marginRight: 8,
    marginLeft: 2,
    marginBottom: 15,
    marginTop: 2,
    backgroundColor: colors.white,
    ...getPlatformElevation(Platform.select({android:4, ios: 2})),
    shadowRadius: 4,
    borderRadius: 4,
  },
  cardStyle: {
    flex: 1,
    padding: 8,
  },
});
