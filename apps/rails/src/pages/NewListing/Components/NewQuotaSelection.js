import React, {Component} from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';
import Card from '@mmt/legacy-commons/Common/Components/Card/index';
import {QuotaItem} from './QuotaItem';

export default class QuotaBar extends Component {
  render() {
    const {
      quotaList, selectedQuota, onQuotaClicked, trainData,
    } = this.props;
    return (
      <View style={{ flexDirection: 'row' }} testID={this.props?.id}>
        <Card style={{
          marginHorizontal: 2, marginVertical: 2, borderRadius: 24, flexDirection: 'row',
        }}
        >
          {
            quotaList.map(item => (
              <View key={item.id}>
                <QuotaItem
                  itemID={item.id}
                  id={`${this.props?.id}_${item.id}`}
                  selected={selectedQuota.id === item.id}
                  quotaName={item.text}
                  onSelect={(quotaSelected) => {
                    onQuotaClicked(quotaSelected, trainData);
                  }}
                />
              </View>
            ))
          }
        </Card>
      </View>
    );
  }
}

QuotaBar.propTypes = {
  onQuotaClicked: PropTypes.func.isRequired,
  quotaList: PropTypes.array.isRequired,
  selectedQuota: PropTypes.object.isRequired,
  trainData: PropTypes.object.isRequired,
  id: PropTypes.string,
};
