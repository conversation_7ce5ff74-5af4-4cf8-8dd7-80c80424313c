import React from 'react';
import PropTypes from 'prop-types';
import { RailsCollapsibleList } from '@mmt/rails/src/pages/RailsListingV3/Components';
import fecha from 'fecha';
import { View, Image, Text, StyleSheet, ScrollView } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import ConnectedTravelWidget from '../Components/ConnectedTravel/ConnectedTravelWidget';
import noTrains from '@mmt/legacy-assets/src/ic-nobuses.webp';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Calendar from '../../Calendar/DatePicker';
import {today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import { MAX_DAYS, EXPLORE_OPTIONS } from '../../../Utils/RailsConstant';
import {trackClickNoDirectTrainsFound} from '../../../railsAnalytics';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import ConnectedTravelWidgetV2 from './ConnectedTravelV2/ConnectedTravelWidgetV2';
import arrowImage from '@mmt/legacy-assets/src/ic_arrow_long_thin_blue.webp';
import { connectedTravelVariants } from '@mmt/rails/src/RailsAbConstants';

const formatDate = (date) => {
  try {
    return fecha.format(date, 'DD MMMM, dddd');
  } catch (error) {
    return null;
  }
};

const NoDirectTrainsFound = (props) => {
  const {
    recommendedMessage,
    recommendedList,
    departureDate,
    changeDate,
    showLoaderForRailsConnectedTravel = false,
    connectedTravelApiResponseJson = null,
    showCtV2 = connectedTravelVariants.SHOWN_EXPANDED_CARD,
  } = props;
  // trackPageVisits('mob_rail_listing_v2_noTrainsFound_TrainsExistOnOtherDates', {});

  const datePicker = (date) => {
    trackClickNoDirectTrainsFound('mob_rail_listing_v2_noTrainsFound_dateClicked');
    changeDate(new Date(date));
  };
  return (
    <View style={{ flex: 1 }} testID="no_direct_trains_found_view">
      <View
        elevation={1}
        style={{
          width: '100%',
          borderBottomWidth: 2,
          borderColor: colors.datePickerColor,
        }}
      >
        <Calendar
          title="Calendar"
          minDate={today()}
          maxDays={MAX_DAYS + 1}
          selectedDate={departureDate}
          changeDate={changeDate}
        />
        <View style={{ borderBottomColor: colors.white, borderBottomWidth: 10 }} />
      </View>
      <ScrollView testID="no_direct_trains_found_scrollview">
        <View style={styles.noTrainsContainer} testID="no_direct_trains_found_container">
          <Image style={styles.noTrainsImage} source={noTrains} resizeMode="contain" />
          <Text style={[styles.noTrainTitle, fontStyle('bold'), getLineHeight(24)]}>
            No Trains Found
          </Text>
          <Text style={[styles.forDateTitle, fontStyle('regular'), getLineHeight(14)]}>
            For {formatDate(departureDate)}
          </Text>
          <Text style={[styles.recommendationMsg, fontStyle('regular'), getLineHeight(14)]}>
            {recommendedMessage}
          </Text>
          {recommendedList.map((item) => (
            <TouchableRipple  key={'recommended-date'}  onPress={() => datePicker(item.date)}>
              <View style={styles.cardStyle}>
                <View style={styles.customTextStyle}>
                  <CustomText item={item.displayMessage} />
                </View>
                <View style={styles.arrowStyle}>
                  <Image source={arrowImage} style={{ width: 26, height: 7 }} />
                </View>
                <View style={{ height: 1 }} />
              </View>
            </TouchableRipple>
          ))}
          {showLoaderForRailsConnectedTravel && (
            <View style={styles.center}>
              <Text style={[styles.noTrainsSubtitle, fontStyle('regular'), getLineHeight(14)]}>
                {EXPLORE_OPTIONS}
              </Text>
              <Spinner size={30} color={colors.black} />
            </View>
          )}
        </View>
        {!showLoaderForRailsConnectedTravel &&
          (showCtV2 === connectedTravelVariants.SHOWN_EXPANDED_CARD ||
            showCtV2 === connectedTravelVariants.MOVED_DIRECT_LISTING) &&
          connectedTravelApiResponseJson && (
            <ConnectedTravelWidget
              errorView={true}
              connectedTravelData={connectedTravelApiResponseJson}
            />
          )}
        {!showLoaderForRailsConnectedTravel &&
          showCtV2 === connectedTravelVariants.INTRODUCING_CONNECTED_TRAVEL &&
          connectedTravelApiResponseJson && (
            <ConnectedTravelWidgetV2 connectedTravelData={connectedTravelApiResponseJson} />
          )}
        <View style={styles.border} />
        <RailsCollapsibleList
          otherDayTrainsList={props.otherDayTrainsList}
          tatkalExists={props.tatkalExists}
          originStation={props.originStation}
          destinationStation={props.destinationStation}
          departureDate={props.departureDate}
          _changeDepartureDate={props._changeDepartureDate}
          isOtherDayTrainsList={true}
        />
      </ScrollView>
    </View>
  );
};

const CustomText = (props) => {
  const res = props.item.split('<b>');
  return (
    <View style={{flexDirection: 'row'}}>
      <Text style={[{ color: colors.black, fontSize: 14}, fontStyle('regular'), getLineHeight(14)]}>{res[0]}</Text>
      <Text style={[{ color: colors.black, fontSize: 14}, fontStyle('bold'), getLineHeight(14)]}>{res[1]}</Text>
    </View>
  );
};

NoDirectTrainsFound.propTypes = {
  recommendedMessage: PropTypes.string,
  recommendedList: PropTypes.arrayOf(
    PropTypes.shape({
      date: PropTypes.string,
      displayMessage: PropTypes.string,
    }),
  ),
  departureDate: PropTypes.instanceOf(Date),
  changeDate: PropTypes.func,
  noCrossSell: PropTypes.bool,
  showLoaderForRailsConnectedTravel: PropTypes.bool,
  connectedTravelApiResponseJson: PropTypes.object,
  showCtV2: PropTypes.bool,
  otherDayTrainsList: PropTypes.array,
  tatkalExists: PropTypes.bool,
  originStation: PropTypes.string,
  destinationStation: PropTypes.string,
  _changeDepartureDate: PropTypes.func,
};

CustomText.propTypes = {
  item: PropTypes.string.isRequired,
};

const styles = StyleSheet.create({
  noTrainsContainer: {
    paddingBottom: 30,
    backgroundColor: colors.white,
    paddingHorizontal: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
  weeklyTrains: {
    backgroundColor: colors.lightGreen1,
    padding:10,
    paddingLeft:15,
    marginBottom:10,
  },
  border: {
    height: 10,
    backgroundColor: colors.grayBg,
  },
  center: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  noTrainsImage: {
    justifyContent: 'center',
    height: 180,
    width: 240,
    paddingVertical: 16,
  },
  noTrainTitle: {
    justifyContent: 'center',
    marginTop: 16,
    fontSize: 24,
    color: colors.black,
  },
  forDateTitle: {
    textAlign: 'center',
    marginTop: 5,
    fontSize: 14,
    color: colors.defaultTextColor,
  },
  recommendationMsg: {
    marginLeft: 16,
    marginRight: 16,
    paddingHorizontal: 12,
    justifyContent: 'center',
    marginTop: 30,
    marginBottom: 15,
    fontSize: 14,
    lineHeight: 20,
    color: colors.defaultTextColor,
  },
  cardStyle: {
    backgroundColor: colors.grey13,
    marginRight: 16,
    marginLeft: 16,
    paddingHorizontal: 16,
    marginTop: 10,
    width: '100%',
    height: 43,
    flexDirection: 'row',
    borderColor: colors.grey12,
    borderWidth: 1,
  },
  customTextStyle: {
    justifyContent: 'flex-start',
    alignSelf: 'center',
    flex: 9,
  },
  arrowStyle: {
    alignSelf: 'center',
    flex: 1,
    marginRight: 16,
  },
});

export default NoDirectTrainsFound;
