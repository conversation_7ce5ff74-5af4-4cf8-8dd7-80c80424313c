import React from 'react';
import {StyleSheet, Text, View } from 'react-native';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import Triangle from 'react-native-triangle';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TrainRunningDaysChip from './TrainRunningDaysChip';

interface Props {
    otherDayTrainsList: unknown;
}

const NextRunningTrainDetails = ({ otherDayTrainsList }: Props) => {

    function formatDate(inputDate: unknown) {
        if (!inputDate)
            {return;}
        // Convert input date to a valid JavaScript date format (YYYYMMDD)
        const year = inputDate.substring(0, 4);
        const month = inputDate.substring(4, 6);
        const day = inputDate.substring(6, 8);

        const jsDate = new Date(`${year}-${month}-${day}`);

        // Define options for formatting
        const options = {
            day: 'numeric',
            month: 'short', // You can use 'long' for the full month name
            weekday: 'short', // You can use 'long' for the full weekday name
        };

        // Format the date
        const formattedDate = jsDate.toLocaleDateString('en-US', options);

        return formattedDate;
    }

    const { possibleDates } = otherDayTrainsList ?? {};
    const nextDate = formatDate(possibleDates?.[0]);

    return (
    <View style={styles.container} testID="other_day_trains_next_running_container">
      <View style={styles.lhs} testID="other_day_trains_next_running_info">
        <Text style={[styles.text, fontStyle('bold')]} testID="other_day_trains_next_running_text">
          Next runs on {nextDate}
        </Text>
        <Triangle
          style={styles.triangleLeft}
          width={25}
          height={25}
          color={'white'}
          direction={'left'}
        />
      </View>
      <TrainRunningDaysChip trainData={otherDayTrainsList} />
    </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
        marginBottom: 8,
    justifyContent: 'space-between',
    },
    lhs: {
        flexDirection: 'row',
        marginLeft: -15,
    },
    text: {
        backgroundColor: colors.lightGreen18,
        padding: 5,
        color: colors.lightGreen16,
        paddingLeft: 15,
    flexShrink: 0,
        fontSize: 12,
    paddingRight: 32,
    },
    triangleLeft: {
        position: 'relative',
        right: 25,
    },
});

export default NextRunningTrainDetails;
