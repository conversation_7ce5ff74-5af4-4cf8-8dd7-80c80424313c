import React from 'react';
import { Image, Text, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { ConnectedTravelResponse } from '../ConnectedTravel/types';
import { Actions } from 'apps/rails/src/navigation';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { trackClickEventProp61 } from '../../../RailsBusHomePage/Analytics';
import { LISTING_CONNECTED_TRAVEL_EVENTS, LISTING_PAGE_TRACKING_KEY } from 'apps/rails/src/Utils/RailsConstant';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';

import { styles } from './ConnectedTravelWidgetV2.styles';
interface ConnectedTravelWidgetV2Props {
  connectedTravelData: ConnectedTravelResponse;
}

const ConnectedTravelWidgetV2 = ({ connectedTravelData }: ConnectedTravelWidgetV2Props) => {
  const {
    srcCityName = '',
    dstnCityName = '',
    twoTrains = [],
    trainAndBus = [],
  } = connectedTravelData || {};

  const totalTravelOptions = (twoTrains?.length ?? 0) + (trainAndBus?.length ?? 0);
  if (!totalTravelOptions) {
    return null;
  }

  const onViewOptionsClicked = () => {
    Actions.connectedTravelPage();
    trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, LISTING_CONNECTED_TRAVEL_EVENTS.ctV2ViewClick);
  };

  return (
    <View style={styles.outerContainer} testID="connected_travel_widget_v2_outer_container">
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <Text style={[styles.headerText, fontStyle('medium')]}>{_label('introducing_connected_travel')}</Text>
          <FastImage source={ASSETS.railsNewTag} style={styles.newIcon} resizeMode={FastImage.resizeMode.contain}/>
        </View>
        <View style={styles.infoContainer}>
          <Image source={ASSETS.trainAndBusIcon} style={styles.icon} />
          <View style={styles.infoContainerHeader}>
            <Text style={styles.infoContainerHeaderText}>
              {_label('view_alternate_travel_options')}
            </Text>
            <Text style={styles.infoContainerSubHeaderText}>
              {_label('ct_from_src_to_dest', undefined, { srcCityName, dstnCityName })}
            </Text>
          </View>
        </View>
        <TouchableRipple onPress={onViewOptionsClicked} testID="ct_view_option_click_button">
          <LinearGradient
            colors={[colors.lightBlue, colors.darkBlue]}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
            style={styles.button}
          >
            <Text style={[styles.label, fontStyle('bold')]}>
              {_label('view')} {totalTravelOptions} {_label('travel_options')}
            </Text>
          </LinearGradient>
        </TouchableRipple>
      </View>
    </View>
  );
};

export default React.memo(ConnectedTravelWidgetV2);
