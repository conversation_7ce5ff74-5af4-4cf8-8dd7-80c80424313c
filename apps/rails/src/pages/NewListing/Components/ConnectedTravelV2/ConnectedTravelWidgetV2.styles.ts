import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  outerContainer: {
    backgroundColor: colors.grayBg,
  },
  container: {
    backgroundColor: colors.white,
    marginVertical: 10,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginTop: 10,
    height: 'auto',
    gap: 10,
  },
  headerContainer: {
    flexDirection: 'row',
    gap: 4,
    width: '100%',
    height: 'auto',
  },
  newContainer: {
    height: 24,
    width: 100,
    borderRadius: 4,
  },
  headerText: {
    fontSize: 15,
    color: colors.brown2,
  },
  infoContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  infoContainerHeader: {
    flexDirection: 'column',
    textAlign: 'left',
  },
  infoContainerHeaderText: {
    color: colors.black,
    fontSize: 16,
  },
  infoContainerSubHeaderText: {
    color: colors.black,
    fontSize: 16,
    fontWeight: '700',
  },
  icon: {
    height: 40,
    width: 52,
  },
  newIcon: {
    alignSelf: 'center',
    height: 15,
    width: 35,
    paddingTop: 1,
  },
  button: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  label: {
    color: colors.white,
    fontSize: 16,
  },
});
