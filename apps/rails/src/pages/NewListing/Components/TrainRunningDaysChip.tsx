import React, { memo, useMemo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';

interface Props {
  trainData: unknown;
}

const weekDays = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];

const TrainRunningDaysChip = (props: Props) => {
  const { trainData } = props;

  const weekDaysValues = useMemo(() => {
    try {
      const { runningMon, runningTue, runningWed, runningThu, runningFri, runningSat, runningSun } =
        trainData ?? {};
      const runningDaysList = [
        runningMon,
        runningTue,
        runningWed,
        runningThu,
        runningFri,
        runningSat,
        runningSun,
      ];
      const detailsUnavailable = runningDaysList.some((element) => element === undefined);
      return detailsUnavailable ? null : runningDaysList;
    } catch (err) {
      return null;
    }
  }, [trainData]);

  return weekDaysValues ? (
    <View style={styles.wrapper} testID="train_running_days_chip_wrapper">
      {weekDays?.map((weekDay, i) => (
        <View
          key={i}
          style={styles.textContainer}
          testID={`train_running_days_chip_text_container_${i}`}
        >
          <Text
            style={[
              styles.weekDayText,
              weekDaysValues[i] === 'Y' && styles.trainRunsDay,
              fontStyle('regular'),
            ]}
            testID={`train_running_days_chip_text_${i}`}
          >
            {weekDay}
          </Text>
          {weekDaysValues[i] === 'N' && (
            <View
              style={[
                styles.strikethrough,
                (weekDay === 'M' || weekDay === 'W') && styles.strikethroughWide,
              ]}
              testID={`train_running_days_chip_strikethrough_${i}`}
            />
          )}
        </View>
      ))}
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  wrapper: {
    width: 92,
    height: 16,
    borderColor: colors.greyText1,
    paddingVertical: 2,
    paddingHorizontal: 4,
    borderWidth: 0.2,
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    flexGrow: 0,
    gap: 2,
  },
  textContainer: {
    position: 'relative',
  },
  weekDayText: {
    fontSize: 12,
    lineHeight: 12.5,
    color: colors.greyText1,
  },
  trainRunsDay: {
    fontWeight: 'bold',
    color: colors.black,
  },
  strikethrough: {
    position: 'absolute',
    left: '-30%',
    top: '50%',
    width: 12,
    borderBottomColor: colors.greyText1,
    borderBottomWidth: 0.2,
    transform: [{ rotate: '-45deg' }],
  },
  strikethroughWide: {
    left: 0,
  },
});

export default memo(TrainRunningDaysChip);
