import {StyleSheet, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import React, {PureComponent} from 'react';
import fecha from 'fecha';
import {convertMinsToHrsMins, getFormattedDates} from '../Utils/RailListingUtils';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';

import {
  getArrivalTime,
  getDepartureTime,
  getDuration,
  getFromStationCode,
  getFromStationName,
  getToStationCode,
  getToStationName,
} from '../readers/newTrainInfo.reader';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

const MAX_STN_LENGTH = 15;
export const getStnNameConcatenated = (name = '') => (
  name.length > MAX_STN_LENGTH ? name.substring(0, MAX_STN_LENGTH).concat('...') : name
);

export class NewTrainInfo extends PureComponent {
  render() {
    const {trainData} = this.props;
    const formattedDepartureTime = fecha.format(fecha.parse(getDepartureTime(trainData), 'HH:mm'), 'h:mm A');
    const formattedArrivalTime = fecha.format(fecha.parse(getArrivalTime(trainData), 'HH:mm'), 'h:mm A');
    const {formattedDepartureDate, formattedArrivalDate} = getFormattedDates(trainData); // in format of DD MMM e.g, 7 Aug

    return (
      <View testID={this.props?.id}>
        <View style={styles.timeContainer}>
          <View style={{flexDirection: 'row'}} testID={`${this.props?.id}_sourceTimings`}>
            <Text style={[styles.timeText, textStyle.getTimeTextFontStyle(), getLineHeight(12)]}>
              {`${formattedDepartureTime}, `}</Text>
            <Text style={[styles.dayText, textStyle.getDayTextFontStyle(), getLineHeight(12)]}>
              {formattedDepartureDate}</Text>
          </View>
          <View style={styles.separator} />
          <Text style={[styles.duration, textStyle.getDurationFontStyle(), getLineHeight(12)]} testID={`${this.props?.id}_travelDuration`}>
            {convertMinsToHrsMins(getDuration(trainData))}
          </Text>
          <View style={styles.separator} />
          <View style={{flexDirection: 'row'}} testID={`${this.props?.id}_destinationTimings`}>
            <Text style={[styles.timeText, textStyle.getTimeTextFontStyle(), getLineHeight(12)]}>{`${formattedArrivalTime}, `}</Text>
            <Text style={[styles.dayText, textStyle.getDayTextFontStyle(), getLineHeight(12)]}>
              {formattedArrivalDate}</Text>
          </View>
        </View>

        <View style={styles.stationDetailContainer}>
          <Text style={[styles.stationDetailsText, textStyle.getStationDetailsTextFontStyle(), getLineHeight(12)]} testID={`${this.props?.id}_sourceStationName`}>
            {`${getStnNameConcatenated(getFromStationName(trainData))} (${getFromStationCode(trainData)})`}
          </Text>
          <Text style={[styles.stationDetailsText, textStyle.getStationDetailsTextFontStyle(), getLineHeight(12)]} testID={`${this.props?.id}_destinationStationTimings`}>
            {`${getStnNameConcatenated(getToStationName(trainData))} (${getToStationCode(trainData)})`}
          </Text>
        </View>
      </View>
    );
  }
}

const regularLightText = {
  fontSize: 12,
  color: colors.lightTextColor,
  lineHeight: 18,
};

const textStyle = {
  getRegularLightTextFontStyle : () => {
    return fontStyle('regular');
  },
  getDayTextFontStyle : () => {
    return {...textStyle.getRegularLightTextFontStyle()};
  },
  getDurationFontStyle : () => {
    return {...textStyle.getRegularLightTextFontStyle()};
  },
  getStationDetailsTextFontStyle : () => {
    return {...textStyle.getRegularLightTextFontStyle()};
  },
  getTimeTextFontStyle : () => {
    return fontStyle('black');
  },
};
const styles = StyleSheet.create({
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: colors.black,
    lineHeight: 16,
  },
  dayText: {
    ...regularLightText,
  },
  duration: {
    ...regularLightText,
  },
  stationDetailContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginTop: 10,
  },
  stationDetailsText: {
    ...regularLightText,
  },
  separator: {
    width: 30,
    height: 1,
    borderColor: colors.lightGrey,
    borderWidth: 1,
  },
});

NewTrainInfo.propTypes = {
  trainData: PropTypes.object.isRequired,
  id: PropTypes.string,
};
