import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    padding: 12,
    backgroundColor: colors.white,
    marginBottom: 16,
    border: 1,
    borderRadius: 16,
    borderColor: colors.lightSilver,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  headerText: {
    color: colors.lightGreen16,
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '700',
  },
  headerRightContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginLeft: 'auto',
  },
  headerDurationText: {
    color: colors.brown2,
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 14.5,
    marginRight: 8,
  },
  headerFromText: {
    color: colors.midnightBlue,
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.5,
  },
  headerAmountText: {
    color: colors.black,
    fontSize: 16,
    fontWeight: '900',
    lineHeight: 19.5,
  },
  firstLegContainer: {
    borderWidth: 0.5,
    borderColor: colors.lightSilver,
    flexDirection: 'row',
    padding: 10,
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
    backgroundColor: colors.white,
    gap: 3,
  },
  secondLegContainer: {
    borderWidth: 0.5,
    borderColor: colors.lightSilver,
    borderTopWidth: 0,
    flexDirection: 'row',
    padding: 10,
    borderBottomRightRadius: 12,
    borderBottomLeftRadius: 12,
    backgroundColor: colors.white,
    gap: 3,
  },
  layoverContainer: {
    height: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',

  },
  cardIcon: {
    height: 16,
    width: 16,
  },
  legCard: {
    flexDirection: 'column',
    width:'100%',
    flexShrink:1,
  },
  legCardHeader: {
    flexDirection: 'row',
  },
  legCardHeaderText: {
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 17,
  },
  legCardHeaderSecondaryText: {
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 17,
    color: colors.greyText1,
  },
  legCardSubtitleText: {
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.5,
    color: colors.greyText1,
    marginTop: 2,
  },
  rotateCurve: {
    transform: [{ rotate: '180deg' }],
  },
  layoverText: {
    alignItems: 'center',
    flexWrap: 'wrap',
    flexShrink: 1,
    marginHorizontal: 8,
    marginVertical: 'auto',
  },
  separator: {
    height: '100%',
    width: 1,
    borderColor: colors.lightSilver,
    borderWidth: 1,
  },
  srcContainer: {
    width: '100%',
    flexDirection: 'row',
    marginTop: 8,
    justifyContent: 'space-between',
  },
  destContainer: {
    width: '100%',
    flexDirection: 'row',
    marginBottom: 8,
    justifyContent: 'space-between',
  },
  flexRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    flexShrink: 0,
    flex: 1,
  },
  p:{
    textAlignVertical: 'center',
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '400',
    color: colors.black,
    flexWrap: 'wrap',
    flexShrink: 1,
    marginHorizontal: 10,
  },
  b: {
    color: colors.brown2,
    fontWeight: '700',
  },
  flexRow2: {
    flexDirection: 'row',
  },
  smallBoldText: {
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '700',
  },
  smallNormalText: {
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '400',
  },
  legDurationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  legDurationText: {
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '400',
    color: colors.midnightBlue,
  },
});
