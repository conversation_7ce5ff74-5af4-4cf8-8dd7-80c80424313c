import { StyleSheet } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    padding: 8,
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 12,
    marginBottom: 7,
    backgroundColor: colors.white,
    width: '100%',
  },
  marginBottom4: {
    marginBottom: 4,
  },
  legContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trainbusIcon: {
    width: 20,
    height: 20,
  },
  blackText: {
    color: colors.black,
  },
  greyText: {
    color: colors.textGrey,
  },
  greyText1: {
    color: colors.greyText1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  justifyText: {
    textAlign: 'justify',
  },
  marginTop4: {
    marginTop: 4,
  },
  busFooterContainer: {
    paddingVertical: 4,
    flexDirection: 'row',
  },
  flexRow: {
    flexDirection: 'row',
  },
  footerImg: {
    height: 13,
    width: 11,
    marginRight: 4,
  },
  footerImgContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  chip: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderTopLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  chipRecommended: { backgroundColor: colors.lightGreen1 },
  chipCheapest: { backgroundColor: colors.lightBlue30, color: colors.primary },
  chipFastest: { backgroundColor: colors.peach },
  chipText: {
    fontFamily: fonts.regular,
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400',
  },
  chipTextRecommended: { color: colors.lightGreen16 },
  chipTextCheapest: { color: colors.lightGreen16 },
  chipTextFastest: { opacity: 0 },
  layoverContainer: {
    flexDirection: 'row',
    marginVertical: 8,
  },
  layoverIcons: {
    height: 13,
    width: 13,
    alignSelf: 'flex-start',
  },
  layoverBrownText: {
    color: colors.brown2,
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '700',
    marginLeft: 2,
  },
  layoverText: {
    color: colors.greyText1,
    fontSize: 12,
    lineHeight: 14.5,
    fontWeight: '400',
    marginLeft: 4,
  },
});
