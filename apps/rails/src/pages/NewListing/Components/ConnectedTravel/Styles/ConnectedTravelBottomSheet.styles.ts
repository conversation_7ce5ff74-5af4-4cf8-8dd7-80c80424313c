import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: colors.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  layoverContainer: {
    flexDirection: 'row',
    marginVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blackText: {
    color: colors.black,
  },
  layoverIcon: {
    width: 5,
    height: 24,
    marginRight: 8,
  },
  trainCardContainer: {
    borderColor: colors.lightSilver,
    borderWidth: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  trainCardHeader: {
    flexDirection: 'row',
    backgroundColor: colors.lighterBlue,
    paddingVertical: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  trainIcon: {
    width: 20,
    height: 20,
    marginRight: 4,
  },
  padding8: {
    padding: 8,
  },
  marginTop12: {
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  crossButton: {
    width: 40,
    height: 40,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
  },
  crossIcon: {
    width: 24,
    height: 24,
  },
});

export const layoverTextStyle = StyleSheet.create({
  p:{
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '400',
    color: colors.black,
  },
  b:{
    fontWeight: '600',
  },
});


