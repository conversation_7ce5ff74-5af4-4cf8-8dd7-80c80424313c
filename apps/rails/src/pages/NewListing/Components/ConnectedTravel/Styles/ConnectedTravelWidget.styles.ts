import { Platform, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    marginTop: 10,
  },
  blackText: { color: colors.black },
  headerContainer: { marginBottom: 12 },
  cardsContainer: {
    flex: 1,
    alignItems: 'center',
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreOptionsText: {
    marginRight: 3,
    color: colors.textGrey,
  },
  marginRight3: {
    marginRight: 3,
  },
  azureColor: {
    color: colors.azure,
  },
  arwImg: {
    height: 20,
    width: 20,
  },
  arwImgRight: {
    height: Platform.OS === 'ios' ? 8 : 10,
    width: 8,
    marginLeft: 4,
    marginBottom: Platform.OS === 'ios' ? 3 : 0,
  },
  optionsView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  linearGrad: {
    width: '100%',
    height: 32,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  blueButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 14.5,
  },
});
