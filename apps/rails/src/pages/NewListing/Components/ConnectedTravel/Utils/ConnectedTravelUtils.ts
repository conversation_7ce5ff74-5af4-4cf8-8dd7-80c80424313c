import {
  getArrivalDate,
  getArrivalDateAndTime,
  getArrivalTime,
  getDepartureDate,
  getDepartureTime,
  getDepartureDateAndTime,
} from '../../../readers/newTrainInfo.reader';
import { JourneyLeg } from '../types';
import fecha from 'fecha';

export const getConnectedTravelCardWidth = (index: number) => {
  switch (index) {
    case 0:
      return '100%';
    case 1:
      return '98%';
    case 2:
      return '96%';
    default:
      return '100%';
  }
};

export const toSentenceCase = (str: string) => {
  return str[0].toUpperCase() + str.slice(1).toLowerCase();
};

export const TRAVEL_MODE_CONSTANTS = {
  train: 'TRAIN',
  bus: 'BUS',
};

export const updateArrivalAndDepartureDates = (trainMetaData) => {
  const trainInfo = trainMetaData;

  const departureTimeEpochInSec = trainInfo?.departureTimeEpochInSec;
  const arrivalTimeEpochInSec = trainInfo?.arrivalTimeEpochInSec;

  trainInfo.departureDateAndTime = new Date(departureTimeEpochInSec * 1000);
  trainInfo.arrivalDateAndTime = new Date(arrivalTimeEpochInSec * 1000);

  return trainInfo;
};

export const OPTION_CATEGORY_TYPE = {
  Recommended: 'Recommended',
  Cheapest: 'Cheapest',
  Fastest: 'Fastest',
};

export const getFormattedLegData = (journeyLeg: JourneyLeg[], index: number) => {
  if (journeyLeg?.[index]?.travelMode === TRAVEL_MODE_CONSTANTS.train) {
    const trainData = updateArrivalAndDepartureDates(journeyLeg?.[index]?.trainMetaData);
    const formattedDepartureTime = fecha.format(
      fecha.parse(getDepartureTime(trainData), 'HH:mm'),
      'h:mm A',
    );
    const formattedArrivalTime = fecha.format(
      fecha.parse(getArrivalTime(trainData), 'HH:mm'),
      'h:mm A',
    );
    const { formattedDepartureDate, formattedArrivalDate } = getFormattedDates(trainData);

    return {
      legIndex: index,
      travelMode: TRAVEL_MODE_CONSTANTS.train,
      title: trainData?.trainName?.slice(0, 18),
      subTitle: trainData?.trainNumber,
      description: '',
      from: {
        city: journeyLeg?.[index]?.fromCity?.slice(0, 18),
        date: formattedDepartureDate,
        time: formattedDepartureTime || '',
        stnName: trainData?.frmStnName || '',
        stnCode: trainData?.frmStnCode || '',
      },
      to: {
        city: journeyLeg?.[index]?.toCity?.slice(0, 18),
        date: formattedArrivalDate,
        time: formattedArrivalTime || '',
        stnName: trainData?.toStnName || '',
        stnCode: trainData?.toStnCode || '',
      },
      duration: convertMinsToHrsMins(trainData?.duration),
    };
  } else {
    const busData = journeyLeg?.[index]?.busMetaData;
    return {
      legIndex: index,
      travelMode: TRAVEL_MODE_CONSTANTS.bus,
      title: busData?.vendor_operator_name,
      subTitle: '',
      description: busData?.vendor_bus_type_name || '',
      from: {
        city: busData?.mmt_source_city_name?.slice(0, 18),
        date: '',
        time: busData?.departure_time || '',
        stnName: '',
        stnCode: '',
      },
      to: {
        city: busData?.mmt_destination_city_name?.slice(0, 18),
        date: busData?.nextDayBus ? '+1 day' : '',
        time: busData?.arrival_time || '',
        stnName: '',
        stnCode: '',
      },
      duration: convertMinsToHrsMins(busData?.journey_time_in_minutes),
    };
  }
};

// Added this two functions duplications here because they are causing import error crashes
const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

function getFormattedDates(trainData) {
  // returns in format of DD MMM e.g, 7 Aug
  let formattedDepartureDate;
  let formattedArrivalDate;
  if (getDepartureDateAndTime(trainData)) {
    formattedDepartureDate = fecha.format(getDepartureDateAndTime(trainData), 'D MMM');
    formattedArrivalDate = fecha.format(getArrivalDateAndTime(trainData), 'D MMM');
  } else {
    const [departureDate, departureMonth] = getDepartureDate(trainData).split('-');
    const [arrivalDate, arrivalMonth] = getArrivalDate(trainData).split('-');
    formattedDepartureDate = `${Number(departureDate)} ${months[Number(departureMonth) - 1]}`;
    formattedArrivalDate = `${Number(arrivalDate)} ${months[Number(arrivalMonth) - 1]}`;
  }

  return {
    formattedDepartureDate,
    formattedArrivalDate,
  };
}

const convertMinsToHrsMins = (minutes) => {
  let roundedHrs = Math.floor(minutes / 60);
  let roundedMinutes = minutes % 60;
  roundedHrs = roundedHrs < 10 ? `0${roundedHrs}` : roundedHrs;
  roundedMinutes = roundedMinutes < 10 ? `0${roundedMinutes}` : roundedMinutes;
  return `${roundedHrs}h ${roundedMinutes}m`;
};
