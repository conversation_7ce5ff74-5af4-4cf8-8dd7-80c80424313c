import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { connect } from 'react-redux';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { NewTrainInfo } from 'apps/rails/src/pages/NewListing/Components/NewTrainInfo';
import TrainNameAndNumber from 'apps/rails/src/pages/NewListing/Components/TrainNameAndNumber';
import { toggleConnectedTravelBottomSheet, availabilityCardClicked, saveRailofyZcShown } from '../../../RailsListingActions';
import TrainCards from './TrainCards';
import BusCard from './BusCard';
import { layoverTextStyle, styles } from '../Styles/ConnectedTravelBottomSheet.styles';
import HTMLView from 'react-native-htmlview';
import { ConnectedTravelData, ConnectedTravelResponse } from '../types';
import {
    TRAVEL_MODE_CONSTANTS,
    updateArrivalAndDepartureDates,
} from '../Utils/ConnectedTravelUtils';
import isEmpty from 'lodash/isEmpty';
import { TbsAvailability } from '../../../../RailsListingV3/types';
// eslint-ignore-next-line
import { pdtCtEventValues } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
// eslint-ignore-next-line
import { logConnectedTravelPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';

import trainIcon from '@mmt/legacy-assets/src/connected_travel_train_icon.webp';
import layoverIcon from '@mmt/legacy-assets/src/connected_travel_layover_icon.webp';
import crossIcon from '@mmt/legacy-assets/src/grey_cross.webp';
import PropTypes from 'prop-types';

interface HeaderProps {
    srcCityName: string;
    dstnCityName: string;
    closeBottomSheet: (proceedToTraveler: boolean) => void;
}

interface ConnectedTravelBottomSheetProps {
    connectedBottomSheetData: ConnectedTravelData;
    connectedTravelApiResponseJson: ConnectedTravelResponse;
    closeBottomSheet: (proceedToTraveler: boolean) => void;
    closeCtV2BottomSheet?: () => void;
    searchContext: unknown;
    availabiltyBucket: string;
}

const Header = (props: HeaderProps) => {
    return (
        <View style={styles.header}>
            <Text style={[styles.blackText, fontStyle('black'), getLineHeight(18)]}>
                {`${props?.srcCityName} ${_label('to', { lowercase: true })} ${props?.dstnCityName}`}
            </Text>
            <TouchableOpacity style={styles.crossButton} onPress={() => { props.closeBottomSheet(false); }}>
                <Image source={crossIcon} style={styles.crossIcon} />
            </TouchableOpacity>
        </View>
    );
};

Header.propTypes = {
    srcCityName: PropTypes.string,
    dstnCityName: PropTypes.string,
    closeBottomSheet: PropTypes.func,
  };


const TrainCard = ({ journeyLeg, availabilityCardClicked, logPdtClickEvent, journeyLegIndex }) => {
    const { trainMetaData, fromCity, toCity } = journeyLeg ?? {};
    const { trainName, trainNumber } = trainMetaData;
    const trainData = updateArrivalAndDepartureDates(trainMetaData);

    return (
    <View style={styles.trainCardContainer} testID="connected_travel_train_card_container">
      <View style={styles.trainCardHeader}>
        <Image
          source={trainIcon}
          style={styles.trainIcon}
          testID="connected_travel_train_card_header_icon"
        />
        <Text
          style={[styles.blackText, fontStyle('black'), getLineHeight(12)]}
          testID="connected_travel_train_card_header_text"
        >
          {_label('train_from_src_to_dest', undefined, { fromCity, toCity })}
        </Text>
        </View>
      <View style={styles.padding8}>
        <TrainNameAndNumber trainName={trainName} trainNumber={trainNumber} />
        <View style={styles.marginTop12}>
          <NewTrainInfo trainData={trainData} />
        </View>
        <TrainCards
          journeyLeg={journeyLeg}
          availabilityCardClicked={availabilityCardClicked}
          logPdtClickEvent={logPdtClickEvent}
          journeyLegIndex={journeyLegIndex}
        />
      </View>
    </View>
    );
};

TrainCard.propTypes = {
    journeyLeg: PropTypes.shape({
      trainMetaData: PropTypes.shape({
        trainName: PropTypes.string,
        trainNumber: PropTypes.string,
      }),
      fromCity: PropTypes.string,
      toCity: PropTypes.string,
    }),
    availabilityCardClicked: PropTypes.func,
    logPdtClickEvent: PropTypes.func,
    journeyLegIndex: PropTypes.number,
  };

const LayoverText = ({ layoverText }) => {
    return (
        <View style={styles.layoverContainer}>
            <Image source={layoverIcon} style={styles.layoverIcon} />
            <HTMLView
                stylesheet={layoverTextStyle}
                value={layoverText} />
        </View>
    );
};

LayoverText.propTypes = {
    layoverText: PropTypes.string,
  };

function ConnectedTravelBottomSheet(props: ConnectedTravelBottomSheetProps) {
    const { srcCityName, dstnCityName } = props?.connectedTravelApiResponseJson ?? {};
    const { journeyLegs, layoverDetails } = props?.connectedBottomSheetData;
    const isDirectBus = isEmpty(layoverDetails);

    const logPdtClickEvent = (journeyLegIndex: number, tbsAvailability?: TbsAvailability) => {
        const travelMode = journeyLegs[journeyLegIndex]?.travelMode;
        const journeyLeg = [{
            ...journeyLegs[journeyLegIndex],
            trainMetaData: {
                ...journeyLegs[journeyLegIndex]?.trainMetaData,
                tbsAvailability: [tbsAvailability],
            },
        }];
        const connectedTravel = {
            optionCategoryType: props?.connectedBottomSheetData?.persuasion,
            journeyLegs: journeyLeg,
        };
        const pdtCtEventValue = travelMode === TRAVEL_MODE_CONSTANTS.train
            ? pdtCtEventValues.TRAIN : pdtCtEventValues.BUS;
        logConnectedTravelPdtEvent(pdtCtEventValue, [connectedTravel], props?.searchContext, props.availabiltyBucket);
    };

    const RenderTrainOrBusCard = ({ journeyLeg, availabilityCardClicked, closeBottomSheet, journeyLegIndex }) => {

        return (
            journeyLeg?.travelMode === TRAVEL_MODE_CONSTANTS.train ?
                <TrainCard
                    journeyLegIndex={journeyLegIndex}
                    logPdtClickEvent={logPdtClickEvent}
                    availabilityCardClicked={availabilityCardClicked}
                    journeyLeg={journeyLeg} /> :
                <BusCard
                    journeyLegIndex={journeyLegIndex}
                    logPdtClickEvent={logPdtClickEvent}
                    closeBottomSheet={closeBottomSheet}
                    minimumFare={journeyLeg?.minimumFare}
                    busMetaData={journeyLeg?.busMetaData}
                    deepLink={journeyLeg?.deepLink}
                    isDirectBus={isDirectBus}/>
        );
    };

    RenderTrainOrBusCard.propTypes = {
        journeyLeg: PropTypes.shape({
          travelMode: PropTypes.string,
          minimumFare: PropTypes.number,
          busMetaData: PropTypes.object,
          deepLink: PropTypes.string,
        }),
        availabilityCardClicked: PropTypes.func,
        closeBottomSheet: PropTypes.func,
        journeyLegIndex: PropTypes.number,
      };

    return (
    <View style={styles.container} testID="connected_travel_bottom_sheet_container">
            <Header closeBottomSheet={props.closeBottomSheet}
                srcCityName={srcCityName}
                dstnCityName={dstnCityName}
                closeCtBottomSheet={props?.closeCtBottomSheet}
            />
            {isDirectBus ?
                <BusCard
                    journeyLegIndex={0}
                    logPdtClickEvent={logPdtClickEvent}
                    closeBottomSheet={props.closeBottomSheet}
                    minimumFare={journeyLegs[0]?.minimumFare}
                    busMetaData={journeyLegs[0]?.busMetaData}
                    deepLink={journeyLegs[0]?.deepLink}
                    isDirectBus={isDirectBus}/>
                :
                <>
                    <RenderTrainOrBusCard
                        journeyLegIndex={0}
                        journeyLeg={journeyLegs[0]}
                        availabilityCardClicked={props.availabilityCardClicked}
                        closeBottomSheet={props.closeBottomSheet} />
                    <LayoverText layoverText={layoverDetails?.['1']?.layoverText} />
                    <RenderTrainOrBusCard
                        journeyLegIndex={1}
                        journeyLeg={journeyLegs[1]}
                        availabilityCardClicked={props.availabilityCardClicked}
                        closeBottomSheet={props.closeBottomSheet} />
                </>
            }
        </View>
    );
}

ConnectedTravelBottomSheet.propTypes = {
    connectedBottomSheetData: PropTypes.shape({
      journeyLegs: PropTypes.arrayOf(
        PropTypes.shape({
          travelMode: PropTypes.string,
          minimumFare: PropTypes.number,
          busMetaData: PropTypes.object,
          deepLink: PropTypes.string,
        }),
      ),
      layoverDetails: PropTypes.object,
      persuasion: PropTypes.string,
    }),
    connectedTravelApiResponseJson: PropTypes.shape({
      srcCityName: PropTypes.string,
      dstnCityName: PropTypes.string,
    }),
    closeBottomSheet: PropTypes.func,
    closeCtV2BottomSheet: PropTypes.func,
    availabilityCardClicked: PropTypes.func,
    searchContext: PropTypes.object,
    availabiltyBucket: PropTypes.string,
    closeCtBottomSheet: PropTypes.func,
  };

const mapStateToProps = (state) => {
    const {
        railsListing: { connectedTravelApiResponseJson, originStation = '', destinationStation = '', departureDate = '' },
    } = state;
    const searchContext = {
        originStation: originStation,
        destinationStation: destinationStation,
        departureDate: departureDate,
    };
    return { connectedTravelApiResponseJson, searchContext };
};

const mapDispatchToProps = (dispatch, ownProps) => ({
    closeBottomSheet: (proceedToTraveler) => {
        dispatch(toggleConnectedTravelBottomSheet(false, proceedToTraveler));
        ownProps?.closeCtV2BottomSheet?.();
    },
    availabilityCardClicked: (className, trainInfo, quotaCode, railofyZcShown) => {
        dispatch(availabilityCardClicked(className, trainInfo, quotaCode));
        dispatch(saveRailofyZcShown(railofyZcShown));
        dispatch(toggleConnectedTravelBottomSheet(false, true));
        ownProps?.closeCtV2BottomSheet?.();
    },
});

export default connect(mapStateToProps, mapDispatchToProps)(ConnectedTravelBottomSheet);
