import React, { useCallback } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { colors, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';
import { updateEvar47or97Variable } from 'apps/rails/src/railsAnalytics';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { trackClickEventProp61 } from 'apps/rails/src/pages/RailsBusHomePage/Analytics';
import { LISTING_CONNECTED_TRAVEL_EVENTS, RAILS_LISTING } from 'apps/rails/src/Utils/RailsConstant';
import { JourneyLeg } from '../types';
import { TbsAvailability } from '../../../../RailsListingV3/types';
import TG_fareBreakup_icon from 'packages/legacy-assets/src/TG_fareBreakup_icon.webp';

interface TrainCardProps {
    journeyLeg: JourneyLeg;
    availabilityCardClicked: unknown;
    logPdtClickEvent: unknown;
    journeyLegIndex: number;
}

const TrainCards = (props: TrainCardProps) => {
    const { journeyLeg, availabilityCardClicked, logPdtClickEvent, journeyLegIndex } = props;
    const { tbsAvailability } = journeyLeg?.trainMetaData;

    const handleAvailabilityCardClicked = (availability: TbsAvailability) => {
        trackClickEventProp61(RAILS_LISTING, LISTING_CONNECTED_TRAVEL_EVENTS.trainClick);
        const { className, quota } = availability;
        const { departureTimeEpochInSec } = journeyLeg?.trainMetaData;
        const utcSeconds = departureTimeEpochInSec;
        const date = new Date(0);
        date.setUTCSeconds(utcSeconds);
        const departureDateAndTime = date;
        const trainMetaData = { ...journeyLeg?.trainMetaData, departureDateAndTime }
    updateEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedTrue);
        const railofyZcShown = {
            railofyShown: Boolean(availability?.confirmationGuaranteeText),
            zcShown: Boolean(availability?.freeCancellationText),
        };
        logPdtClickEvent(journeyLegIndex, availability);
        availabilityCardClicked(className, trainMetaData, quota, railofyZcShown);
    };

    const trainItem = useCallback(({ item }) => {
        const isWL = item.prettyPrintingAvailablityStatus.includes('WL');
        return (
            <TouchableOpacity style={styles.availabilityCardContainer}
            onPress={() => handleAvailabilityCardClicked(item)}>
                <View style={styles.classContainer}>
                    <Text style={[fontStyle('black'), getLineHeight(14), styles.blackText]}>{item.className}</Text>
                    <Text style={[fontStyle('black'), getLineHeight(14), styles.blackText]}>{`₹${item.totalFare}`}</Text>
                </View>
                <View style={styles.availabilityTextContainer}>
                    <Text style={[styles.availabilityTextLabel, isWL && styles.availabilityTextWL]}>
                        {item.prettyPrintingAvailablityStatus || item.availablityStatus}
                    </Text>
                </View>
                {item.confirmationGuaranteeText && (
                    <View style={styles.confirmationGuaranteeContainer}>
                        <Image
                            source={TG_fareBreakup_icon}
                            style={styles.confirmationGuaranteeIcon}
                        />
                        <Text style={[fontStyle('normal'), getLineHeight(12), styles.confirmationGuaranteeText]}>
                            {item.confirmationGuaranteeText}
                        </Text>
                    </View>
                )}
            </TouchableOpacity>
        );
    }, []);

    return (
        <View style={styles.availabilityCardList}>
            <FlatList
                horizontal
                data={tbsAvailability}
                showsHorizontalScrollIndicator={false}
                renderItem={trainItem}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    flexDisplay: {
        flex: 1,
    },
    blackText: {
        color: colors.black,
    },
    availabilityCardList: {
        marginTop: 12,
    },
    confirmationGuaranteeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 4,
    },
    confirmationGuaranteeIcon: {
        height: 12,
        width: 12,
    },
    confirmationGuaranteeText: {
    color: colors.purple,
    fontSize: fontSizes.reg,
        lineHeight: 14,
        fontFamily: 'Lato',
        marginLeft: 2,
    },
    availabilityCardContainer: {
        paddingHorizontal: 8,
        paddingVertical: 8,
        width: 112,
        borderWidth: 1,
        borderColor: colors.lightSilver,
        borderRadius: 12,
        marginRight: 8,
    },
    classContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    availabilityTextContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        width: '100%',
        marginBottom: 4,
    },
    availabilityTextLabel: {
    color: colors.lightGreen16,
        fontFamily: 'Lato',
        fontWeight: '700',
    fontSize: fontSizes.reg,
        lineHeight: 14.4,
        marginRight: 2,
    },
    availabilityTextWL: {
    color: colors.lightYello,
    },
});

export default TrainCards;
