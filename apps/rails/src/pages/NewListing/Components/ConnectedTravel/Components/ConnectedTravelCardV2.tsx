import React from 'react';
import { Image, Text, TouchableWithoutFeedback, View } from 'react-native';
import HTM<PERSON>View from 'react-native-htmlview';
import { styles } from '../Styles/ConnectedTravelCardV2.styles';
import { ConnectedTravelData } from '../types';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { convertMinsToHrsMins } from '../../../Utils/RailListingUtils';
import { TRAVEL_MODE_CONSTANTS, getFormattedLegData } from '../Utils/ConnectedTravelUtils';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { Curve } from 'apps/rails/src/pages/Common/SvgComponents';
import { getTrimmedStationName } from '../../ConfirmedOptions/ConfirmedOptionsUtils';

interface ConnectedTravelCardV2Props {
  connectedTravel: ConnectedTravelData;
  handleCardClick: unknown;
}

interface SrcAndDestDetails {
  city: string;
  date: string;
  time: string;
  stnName: string;
  stnCode: string;
}

interface CtLegCardProps {
  legDetails: {
    legIndex: number;
    travelMode: string;
    title: string;
    subTitle: string;
    description: string;
    from: SrcAndDestDetails;
    to: SrcAndDestDetails;
    duration: string;
  };
}

const ConnectedTravelCardV2 = ({
  connectedTravel,
  handleCardClick,
}: ConnectedTravelCardV2Props) => {
  const {
    journeyLegs,
    startingPrice = 0,
    durationMins = 0,
    layoverDetails,
    optionCategoryType = '',
  } = connectedTravel;

  const firstLegDetails = getFormattedLegData(journeyLegs, 0);
  const secondLegDetails = getFormattedLegData(journeyLegs, 1);

  return (
    <TouchableWithoutFeedback onPress={handleCardClick} testID="connected_travel_card_v2_touchable">
      <LinearGradient
        colors={[optionCategoryType ? colors.lightGreen1 : colors.white, colors.white]}
        start={{ x: 0, y: 0 }}
        locations={[0, 0.25]}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        <View style={styles.header}>
          {optionCategoryType && (
            <Text style={styles.headerText}>{optionCategoryType?.toUpperCase()}</Text>
          )}
          <View style={styles.headerRightContainer}>
            <Text style={styles.headerDurationText}>{convertMinsToHrsMins(durationMins)}</Text>
            <Text style={styles.headerFromText}>{_label('from')}</Text>
            <Text style={styles.headerAmountText}>₹{startingPrice}</Text>
          </View>
        </View>
        <ConnectedTravelLegCard legDetails={firstLegDetails} />
        <LinearGradient
          colors={[colors.grey11, colors.grey36, colors.grey36, colors.grey11]}
          style={styles.layoverContainer}
        >
          <Curve />
          <View style={styles.layoverText}>
            <HTMLView
              value={
                layoverDetails?.['1']?.layoverListingWidgetText ||
                layoverDetails?.['1']?.layoverText
              }
              stylesheet={styles}
            />
          </View>
          <Curve style={styles.rotateCurve} />
        </LinearGradient>
        <ConnectedTravelLegCard legDetails={secondLegDetails} />
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

export default ConnectedTravelCardV2;

const ConnectedTravelLegCard = ({ legDetails }: CtLegCardProps) => {
  const {
    travelMode = '',
    legIndex = 0,
    title = '',
    subTitle = '',
    description = '',
    duration = '',
    from = {},
    to = {},
  } = legDetails;
  return (
    <View
      style={legIndex === 0 ? styles.firstLegContainer : styles.secondLegContainer}
      testID="connected_travel_card_v2_leg_container"
    >
      <Image source={(travelMode === TRAVEL_MODE_CONSTANTS.train) ? ASSETS.trainIcon : ASSETS.busIcon}
      style={styles.cardIcon} />
      <View style={styles.legCard}>
        <View style={styles.legCardHeader}>
          <Text style={styles.legCardHeaderText}>{title}</Text>
          <Text style={styles.legCardHeaderSecondaryText}>
            {' - '}
            {subTitle}
          </Text>
        </View>
        <Text style={styles.legCardSubtitleText}>{description}</Text>
        <View style={styles.srcContainer}>
          <LegDetailsCard details={from} />
        </View>
        <View style={styles.legDurationContainer}>
          <View style={styles.separator} />
          <Text style={styles.legDurationText}>{duration}</Text>
        </View>
        <View style={styles.destContainer}>
          <LegDetailsCard details={to} />
        </View>
      </View>
    </View>
  );
};

const LegDetailsCard = ({ details }: { details: SrcAndDestDetails }) => {
  return (
    <>
      <View style={styles.flexRow}>
        <Text style={styles.smallBoldText}>{details?.city}</Text>
        {details?.stnName && <Text style={styles.smallNormalText}>{`, ${getTrimmedStationName(details?.stnName)}`}</Text>}
        {details?.stnCode && <Text style={styles.smallNormalText}>{`(${details.stnCode})`}</Text>}
      </View>
      <View style={styles.flexRow2}>
        <Text style={styles.smallBoldText}>{details?.date}</Text>
        <Text style={styles.smallNormalText}> {details?.time}</Text>
      </View>
    </>
  );
};
