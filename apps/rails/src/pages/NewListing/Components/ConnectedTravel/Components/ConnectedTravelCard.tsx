import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableWithoutFeedback, Image, Animated } from 'react-native';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import TrainInfo from './TrainInfo';
import { TRAVEL_MODE_CONSTANTS, getConnectedTravelCardWidth, toSentenceCase } from '../Utils/ConnectedTravelUtils';
import { styles } from '../Styles/ConnectedTravelCard.styles';
import { ConnectedTravelData } from '../types';
import { getTrimmedStationName } from '../../ConfirmedOptions/ConfirmedOptionsUtils';
import OptionCategoryTypeChip from './OptionCategoryTypeChip';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { convertMinsToHrsMins } from '../../../Utils/RailListingUtils';

import busIcon from '@mmt/legacy-assets/src/connected_travel_bus_icon.webp';
import trainIcon from '@mmt/legacy-assets/src/connected_travel_train_icon.webp';
import PropTypes from 'prop-types';

interface ConnectedTravelCardProps {
    connectedTravel: ConnectedTravelData;
    isExpanded: boolean;
    index: number;
    handleCardClick: unknown;
    showLayoverDetails?: boolean;
}

const CardHeader = ({ header, startingPrice }) => {
    return (
    <View style={styles.headerContainer} testID="connected_travel_card_header_container">
            <Text style={[styles.blackText, fontStyle('black'), getLineHeight(14)]}>{header}</Text>
            <Text style={styles.justifyText}>
                <Text style={[styles.greyText1, fontStyle('regular'), getLineHeight(12)]}>{`${_label('from')}   `}</Text>
                <Text style={[styles.blackText, fontStyle('black'), getLineHeight(14)]}>₹{startingPrice}</Text>
            </Text>
        </View>
    );
};

CardHeader.propTypes = {
    header: PropTypes.string,
    startingPrice: PropTypes.number,
  };

const GetLegInfo = ({ legDetails }) => {

    const {
        isLegTrain,
        source,
        destination,
        sourceCode = '',
        destinationCode = '',
    } = legDetails;

    const travelMode = isLegTrain ? _label('train') : _label('bus');

    return (
        <View style={styles.legContainer}>
            <Image source={isLegTrain ? trainIcon : busIcon} style={styles.trainbusIcon} />
            <Text style={[styles.greyText, fontStyle('regular'), getLineHeight(12)]}>{`${travelMode}: `}</Text>
            <Text style={[styles.blackText, fontStyle('black'), getLineHeight(12)]}>{`${getTrimmedStationName(source)} `}</Text>
            {sourceCode && <Text style={[styles.blackText, fontStyle('regular'), getLineHeight(12)]}>{`(${sourceCode}) `}</Text>}
            <Text style={[styles.greyText, fontStyle('regular'), getLineHeight(12)]}>{`${_label('to', { lowercase: true })} `}</Text>
            <Text style={[styles.blackText, fontStyle('black'), getLineHeight(12)]}>{`${getTrimmedStationName(destination)}`}</Text>
            {destinationCode && <Text style={[styles.blackText, fontStyle('regular'), getLineHeight(12)]}>{` (${destinationCode})`}</Text>}
        </View>
    );
};

GetLegInfo.propTypes = {
    legDetails: PropTypes.shape({
      isLegTrain: PropTypes.bool,
      source: PropTypes.string,
      destination: PropTypes.string,
      sourceCode: PropTypes.string,
      destinationCode: PropTypes.string,
    }),
  };

function ConnectedTravelCard(props: ConnectedTravelCardProps) {
    const [containerMargin, setContainerMargin] = useState(0);
    const animation = useRef(new Animated.Value(0)).current;
    const { startingPrice, journeyLegs, startDateTimeStr,
        endDateTimeStr,
        startDateTime,
        endDateTime,
        durationMins,
        layoverDetails,
        optionCategoryType = null } = props?.connectedTravel;

    const containerWidth = {
        width: getConnectedTravelCardWidth(props.index),
    };

    const cardAspects = {
        marginTop: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, containerMargin],
        }),
    };

    const onContainerLayout = (event) => {
        const { height } = event.nativeEvent.layout;
        if (props.index !== 0) {
            setContainerMargin(-(height + 2));
        }
    };

    useEffect(() => {
        Animated.timing(animation, {
            toValue: props.isExpanded ? 0 : 1,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [props.isExpanded, animation]);


    const firstLegDetails = {
        isLegTrain: journeyLegs[0]?.travelMode === TRAVEL_MODE_CONSTANTS.train,
        source: journeyLegs[0]?.travelMode === TRAVEL_MODE_CONSTANTS.train
            ? journeyLegs[0]?.fromCity?.slice(0, 18) : journeyLegs[0]?.busMetaData?.mmt_source_city_name?.slice(0, 18),
        sourceCode: (journeyLegs[0]?.travelMode === TRAVEL_MODE_CONSTANTS.train
            ? journeyLegs[0]?.trainMetaData?.frmStnCode
            : '') || '',
        destination: journeyLegs[0]?.travelMode === TRAVEL_MODE_CONSTANTS.train ? journeyLegs[0]?.toCity?.slice(0, 18)
            : journeyLegs[0]?.busMetaData?.mmt_destination_city_name?.slice(0, 18),
        destinationCode: (journeyLegs[0]?.travelMode === TRAVEL_MODE_CONSTANTS.train
            ? journeyLegs[0]?.trainMetaData?.toStnCode
            : '') || '',
    };
    const secondLegDetails = {
        isLegTrain: journeyLegs[1]?.travelMode === TRAVEL_MODE_CONSTANTS.train,
        source:
            journeyLegs[1]?.travelMode === TRAVEL_MODE_CONSTANTS.train
                ? journeyLegs[1]?.fromCity?.slice(0, 18)
                : journeyLegs[1]?.busMetaData?.mmt_source_city_name?.slice(0, 18),
        sourceCode: journeyLegs[1]?.travelMode === TRAVEL_MODE_CONSTANTS.train
            ? journeyLegs[1]?.trainMetaData?.frmStnCode
            : '',
        destination:
            journeyLegs[1]?.travelMode === TRAVEL_MODE_CONSTANTS.train
                ? journeyLegs[1]?.toCity?.slice(0, 18)
                : journeyLegs[1]?.busMetaData?.mmt_destination_city_name?.slice(0, 18),
        destinationCode: journeyLegs[1]?.travelMode === TRAVEL_MODE_CONSTANTS.train
            ? journeyLegs[1]?.trainMetaData?.toStnCode
            : '',
    };

    const header = toSentenceCase(journeyLegs[0]?.travelMode) + ' + ' + toSentenceCase(journeyLegs[1]?.travelMode);

    const formattedStartTime = new Date(startDateTime);
    const formattedEndTime = new Date(endDateTime);

    const trainData = {
        departureTime: startDateTimeStr.split(' ')[1],
        arrivalTime: endDateTimeStr.split(' ')[1],
        duration: durationMins,
        departureDateAndTime: formattedStartTime,
        arrivalDateAndTime: formattedEndTime,
    };

    const { layoverDuration = 0, layoverDistance = 0 } = layoverDetails?.[1] || {};

    const showLayoverDetails = props?.showLayoverDetails && (layoverDistance || layoverDuration);

    return (
    <TouchableWithoutFeedback
      onLayout={onContainerLayout}
      onPress={props.handleCardClick}
      testID="connected_travel_card_touchable"
    >
      <Animated.View
        style={[
          styles.container,
          { zIndex: 3 - props.index },
          props.isExpanded ? null : containerWidth,
          cardAspects,
        ]}
      >
        <CardHeader header={header} startingPrice={startingPrice} />
        <TrainInfo
          trainData={trainData}
          layoverDetails={layoverDetails}
          showLayoverDetails={!showLayoverDetails}
        />
        <GetLegInfo legDetails={firstLegDetails} />
        {!showLayoverDetails && <View style={styles.marginBottom4} />}
        {showLayoverDetails && (
          <View style={styles.layoverContainer} testID="connected_travel_card_layover_container">
            <View style={{ flexDirection: 'row', flex: 1, flexShrink: 1 }}>
              <Image source={ASSETS.ctClockIcon} style={styles.layoverIcons} />
              <Text style={styles.layoverBrownText}>{convertMinsToHrsMins(layoverDuration)}</Text>
              <Text numberOfLines={1} ellipsizeMode="tail" style={styles.layoverText}>
                {'layover at '}
                {firstLegDetails?.destination}
              </Text>
            </View>
            {Boolean(layoverDistance) && (
              <View
                style={{ flexDirection: 'row', flex: 1, marginLeft: 50 }}
                testID="connected_travel_card_layover_distance_container"
              >
                <Image source={ASSETS.ctLocationIcon} style={styles.layoverIcons} />
                <Text style={styles.layoverBrownText}>
                  {layoverDistance.toFixed(1)}
                  {' km'}
                </Text>
                <Text style={styles.layoverText}>
                  {secondLegDetails?.isLegTrain ? _label('to_next_station') : _label('to_bus_stop')}
                </Text>
              </View>
            )}
          </View>
        )}
        <GetLegInfo legDetails={secondLegDetails} />
        {optionCategoryType && (
          <OptionCategoryTypeChip
            allCardsExpanded={props.isExpanded}
            cardIndex={props.index}
            optionCategoryType={optionCategoryType}
          />
        )}
      </Animated.View>
    </TouchableWithoutFeedback>
  );
}

export default ConnectedTravelCard;
