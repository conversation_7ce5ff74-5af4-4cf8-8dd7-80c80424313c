import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { trackClickEventProp61 } from 'apps/rails/src/pages/RailsBusHomePage/Analytics';
import { convertMinsToHrsMins } from 'apps/rails/src/pages/NewListing/Utils/RailListingUtils';
import { LISTING_CONNECTED_TRAVEL_EVENTS, RAILS_LISTING } from 'apps/rails/src/Utils/RailsConstant';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { BusMetaData } from '../types';
import isEmpty from 'lodash/isEmpty';
import RedDealsInfoBar from 'apps/bus/legacy/Pages/Listing/Components/RedDealsInfoBar';

import starIcon from '@mmt/legacy-assets/src/star_icon.webp';
import busIcon from '@mmt/legacy-assets/src/connected_travel_bus_icon.webp';

interface BusCardProps {
    busMetaData: BusMetaData;
    minimumFare: number;
    deepLink: string;
    closeBottomSheet: (proceedToTraveler: boolean) => void;
    isDirectBus: boolean;
    journeyLegIndex: number;
    logPdtClickEvent: unknown;
}

function BusCard(props: BusCardProps) {

    const {
        mmt_source_city_name,
        mmt_destination_city_name,
        vendor_operator_name,
        rating,
        vendor_bus_type_name,
        totalReviews,
        departure_time,
        journey_time_in_minutes,
        availableSeats,
        avlSingleSeats,
        arrival_time,
        nextDayBus,
        footerTags,
    } = props?.busMetaData;

    function handleCardClick() {
        trackClickEventProp61(
            RAILS_LISTING,
            props?.isDirectBus
              ? LISTING_CONNECTED_TRAVEL_EVENTS.onlyBusClick
              : LISTING_CONNECTED_TRAVEL_EVENTS.busClick,
          );
        props?.closeBottomSheet(true);
        props?.logPdtClickEvent(props?.journeyLegIndex);
        GenericModule.openDeepLink(props?.deepLink);
    }

    return (
        <TouchableOpacity style={styles.container} onPress={handleCardClick}>
            <View style={styles.header}>
                <Image source={busIcon} style={styles.busImg} />
                <Text style={[styles.blackText, fontStyle('black'), getLineHeight(12)]}>{_label('bus_from_src_to_dest', undefined, { mmt_source_city_name, mmt_destination_city_name })}</Text>
            </View>
            <View style={styles.detailsContainer}>
                <View style={styles.nameAndRating}>
                    <Text style={[styles.blackText, fontStyle('black'), getLineHeight(14)]}>{vendor_operator_name}</Text>
                    <View style={styles.starImgContainer}>
                        <Image source={starIcon} style={styles.starImg} />
                        <Text style={[styles.whiteText, fontStyle('black'), getLineHeight(12)]}>{rating}</Text>
                    </View>
                </View>
                <View style={styles.nameContainer}>
                    <Text style={[styles.greyText1, fontStyle('regular'), getLineHeight(12)]}>{vendor_bus_type_name}</Text>
                    <Text style={[styles.greyText1, fontStyle('regular'), getLineHeight(12)]}>{totalReviews} ratings</Text>
                </View>
                <View style={styles.timeAndPriceContainer}>
                    <View style={styles.timeContainer}>
                        <Text style={[{ color: colors.black }, fontStyle('black'), getLineHeight(16)]}>{departure_time}</Text>
                        <View style={styles.lineSeperator} />
                        <Text style={[styles.greyText, fontStyle('regular'), getLineHeight(12)]}>{convertMinsToHrsMins(journey_time_in_minutes)}</Text>
                        <View style={styles.lineSeperator} />
                        <Text style={[styles.greyText, fontStyle('black'), getLineHeight(14)]}>{arrival_time}</Text>
                        {nextDayBus && <Text style={[styles.greyText1, fontStyle('regular'), getLineHeight(10)]}> +1 day</Text>}
                    </View>
                    <Text style={[styles.blackText, fontStyle('black'), getLineHeight(18)]}>₹{props?.minimumFare}</Text>
                </View>
                <Text style={[styles.greyText, fontStyle('regular'), getLineHeight(12)]}>
                    <Text>{_label('seats_left', undefined, {num: availableSeats})}</Text>
                    <Text>{`  ${_label('single_seats_left', undefined, {avlSingleSeats})}`}</Text>
                </Text>
            </View>
            {!isEmpty(footerTags) && (
                <View style={styles.redDealsInfoContainer}>
                    <RedDealsInfoBar footerTags={footerTags} />
                </View>
            )}
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    container: {
        borderColor: colors.lightSilver,
        borderWidth: 1,
        borderRadius: 16,
        overflow: 'hidden',
    },
    header: {
        flexDirection: 'row',
        backgroundColor: colors.lighterBlue,
        paddingVertical: 5,
        alignItems: 'center',
        justifyContent: 'center',
    },
    busImg: {
        width: 20,
        height: 20,
        marginRight: 4,
    },
    blackText: { color: colors.black },
    whiteText: { color: colors.white },
    greyText: { color: colors.textGrey },
    greyText1: { color: colors.greyText1 },
    detailsContainer: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        marginBottom: 8,
    },
    nameAndRating: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    lineSeperator: {
        borderWidth: 0.5,
        height: 0.5,
        width: 6,
        borderColor: colors.greyText1,
        marginHorizontal: 4,
    },
    timeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    timeAndPriceContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    nameContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    starImgContainer: {
        width: 38,
        backgroundColor: colors.darkBlue2,
        borderRadius: 4,
        flexDirection: 'row',
        padding: 4,
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    starImg: {
        width: 10,
        height: 10,
        marginRight: 3,
    },
    redDealsInfoContainer: {
        borderTopWidth: 1,
        borderTopColor: colors.lightGrey,
        backgroundColor: colors.white,
        paddingHorizontal: 16,
        flexDirection: 'row',
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
});

export default BusCard;
