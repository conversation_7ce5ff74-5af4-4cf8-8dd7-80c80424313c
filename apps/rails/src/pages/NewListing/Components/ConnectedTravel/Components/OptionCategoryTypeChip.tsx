import React, { Fragment } from 'react';
import { View, Text } from 'react-native';
import MaskedView from '@react-native-community/masked-view';
import LinearGradient from 'react-native-linear-gradient';
import { OptionCategoryTypeChipProps } from '../types';
import { styles } from '../Styles/ConnectedTravelCard.styles';
import { OPTION_CATEGORY_TYPE } from '../Utils/ConnectedTravelUtils';

const FastestTextGradient = (props: unknown) => {
    return (
        <MaskedView maskElement={<Text {...props} />}>
            <LinearGradient
                colors={['#FF7F3F', '#FF3E5E']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
            >
                <Text {...props} style={[props.style, styles.chipTextFastest]} />
            </LinearGradient>
        </MaskedView>
    );
};

const OptionCategoryTypeChip = (props: OptionCategoryTypeChipProps) => {
    const { allCardsExpanded, cardIndex, optionCategoryType } = props;

    return (
        <Fragment>
            {optionCategoryType && optionCategoryType?.length > 0 &&
            OPTION_CATEGORY_TYPE.hasOwnProperty(optionCategoryType) &&
                (allCardsExpanded || cardIndex === 0) &&
                (<Fragment>
                    {optionCategoryType === OPTION_CATEGORY_TYPE.Fastest ?
                        <View style={[styles.chip, styles.chipFastest]}>
                            <FastestTextGradient style={styles.chipText}>{OPTION_CATEGORY_TYPE.Fastest}
                            </FastestTextGradient>
                        </View>
                        : <View style={[styles.chip, optionCategoryType === OPTION_CATEGORY_TYPE.Recommended
                        ? styles.chipRecommended : styles.chipCheapest]}>
                            <Text style={[styles.chipText, optionCategoryType === OPTION_CATEGORY_TYPE.Recommended
                                ? styles.chipTextRecommended : styles.chipTextCheapest]}>{optionCategoryType}</Text>
                        </View>
                    }
                </Fragment>)}
        </Fragment>
    );
};

export default OptionCategoryTypeChip;
