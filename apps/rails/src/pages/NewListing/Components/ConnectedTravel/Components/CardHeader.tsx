import React from 'react';
import { View, Text } from 'react-native';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { styles } from '../Styles/ConnectedTravelCard.styles';

interface CardHeaderProps {
    header: string;
    startingPrice: number;
}

const CardHeader: React.FC<CardHeaderProps> = ({header,startingPrice}) => {
    return (
        <View style={styles.headerContainer}>
            <Text style={[styles.blackText, fontStyle('black'), getLineHeight(14)]}>{header}</Text>
            <Text style={styles.justifyText}>
                <Text style={[styles.greyText1, fontStyle('regular'), getLineHeight(12)]}>{`${_label('from')}   `}</Text>
                <Text style={[styles.blackText, fontStyle('black'), getLineHeight(14)]}>₹{startingPrice}</Text>
            </Text>
        </View>
    );
};

export default CardHeader;
