import React, { useState, useRef, useEffect } from 'react';
import { View, TouchableWithoutFeedback, Animated, Text, Image } from 'react-native';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { styles } from '../Styles/ConnectedTravelCard.styles';
import { getConnectedTravelCardWidth } from '../Utils/ConnectedTravelUtils';
import CardHeader from './CardHeader';
import TrainInfo from './TrainInfo';
import { ConnectedTravelData } from '../types';
import OptionCategoryTypeChip from './OptionCategoryTypeChip';

import dateChange from '@mmt/legacy-assets/src/date_change.webp';
import liveTracking from '@mmt/legacy-assets/src/live_tracking.webp';

interface DirectBusCardProps {
    connectedTravel: ConnectedTravelData;
    isExpanded: boolean;
    index: number;
    handleCardClick: unknown;
}

function DirectBusCard(props: DirectBusCardProps) {

    const {
        startingPrice,
        journeyLegs,
        startDateTimeStr,
        endDateTimeStr,
        startDateTime,
        endDateTime,
        durationMins,
        optionCategoryType = null,
    } = props?.connectedTravel;

    const { busMetaData: { vendor_bus_type_name = '' } = {} } = journeyLegs[0];

    const busData = {
        departureTime: startDateTimeStr.split(' ')[1],
        arrivalTime: endDateTimeStr.split(' ')[1],
        duration: durationMins,
        departureDateAndTime: new Date(startDateTime),
        arrivalDateAndTime: new Date(endDateTime),
    };

    const [containerMargin, setContainerMargin] = useState(0);
    const animation = useRef(new Animated.Value(0)).current;

    const containerWidth = {
        width: getConnectedTravelCardWidth(props.index),
    };

    const onContainerLayout = (event) => {
        const { height } = event.nativeEvent.layout;
        if (props.index !== 0) {
            setContainerMargin(-(height + 2));
        }
    };

    const cardAspects = {
        marginTop: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, containerMargin],
        }),
    };

    useEffect(() => {
        Animated.timing(animation, {
            toValue: props.isExpanded ? 0 : 1,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [props.isExpanded, animation]);


    return (
        <TouchableWithoutFeedback onLayout={onContainerLayout} onPress={props.handleCardClick}>
            <Animated.View style={[styles.container, { zIndex: 3 - props.index },
                props.isExpanded ? null : containerWidth, cardAspects]}>
                <CardHeader header={_label('direct_bus')} startingPrice={startingPrice} />
                <View style={styles.marginTop4}>
                    <Text style={[styles.greyText1, fontStyle('regular'), getLineHeight(12)]}>
                        {vendor_bus_type_name}
                    </Text>
                </View>
                <TrainInfo trainData={busData} />
                <View style={styles.busFooterContainer}>
                    <View style={styles.footerImgContainer}>
                        <Image source={dateChange} style={styles.footerImg} />
                        <Text style={[styles.greyText, fontStyle('regular'), getLineHeight(12)]}>{_label('free_date_change')}</Text>
                    </View>
                    <View style={styles.flexRow}>
                        <Image source={liveTracking} style={styles.footerImg} />
                        <Text style={[styles.greyText, fontStyle('regular'), getLineHeight(12)]}>{_label('live_tracking')}</Text>
                    </View>
                </View>
                {optionCategoryType && <OptionCategoryTypeChip allCardsExpanded={props.isExpanded}
                cardIndex={props.index} optionCategoryType={optionCategoryType} />}
            </Animated.View>
        </TouchableWithoutFeedback>
    );
}

export default DirectBusCard;
