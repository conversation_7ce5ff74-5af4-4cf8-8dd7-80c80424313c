import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import fecha from 'fecha';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import {
  getArrivalTime,
  getDepartureTime,
  getDuration,
} from 'apps/rails/src/pages/NewListing/readers/newTrainInfo.reader';
import { convertMinsToHrsMins, getFormattedDates } from 'apps/rails/src/pages/NewListing/Utils/RailListingUtils';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
export default function TrainInfo({trainData, layoverDetails, showLayoverDetails = true}) {

  const formattedDepartureTime = fecha.format(fecha.parse(getDepartureTime(trainData), 'HH:mm'), 'h:mm A');
  const formattedArrivalTime = fecha.format(fecha.parse(getArrivalTime(trainData), 'HH:mm'), 'h:mm A');
  const { formattedDepartureDate, formattedArrivalDate } = getFormattedDates(trainData);
  const { layoverDuration } = layoverDetails?.[1] || {};

  return (
    <View style={styles.timeContainer} testID="train_info_container">
    <View style={{ flexDirection: 'row' }}>
      <Text style={[styles.timeText, textStyle.getTimeTextFontStyle(), getLineHeight(12)]}>{`${formattedDepartureDate}  `}</Text>
      <Text style={[styles.dayText, textStyle.getDayTextFontStyle(), getLineHeight(12)]}>{formattedDepartureTime}</Text>
    </View>
    {!showLayoverDetails &&
      <>
        <View style={styles.separator} />
        <Text style={[styles.duration, textStyle.getDurationFontStyle(), getLineHeight(12)]}>
          {convertMinsToHrsMins(getDuration(trainData))}
        </Text>
        <View style={styles.separator} />
      </>
    }
    {showLayoverDetails &&
      <View style={styles.layoverContainer}>
        <Text style={[styles.duration, textStyle.getDurationFontStyle(), getLineHeight(12)]}>
          {convertMinsToHrsMins(getDuration(trainData))}
        </Text>
        <View style={styles.separator1} />
        <Text style={[styles.layoverDuration, textStyle.getDurationFontStyle(), getLineHeight(12)]}>
          {convertMinsToHrsMins(layoverDuration)} {_label('layover')}
        </Text>
      </View>
    }
    <View style={{ flexDirection: 'row' }}>
      <Text style={[styles.timeText, textStyle.getTimeTextFontStyle(), getLineHeight(12)]}>{`${formattedArrivalDate}  `}</Text>
      <Text style={[styles.dayText, textStyle.getDayTextFontStyle(), getLineHeight(12)]}>{formattedArrivalTime}</Text>
    </View>
  </View>
);
}

TrainInfo.propTypes = {
  trainData: PropTypes.object.isRequired,
  layoverDetails: PropTypes.arrayOf(
    PropTypes.shape({
      layoverDuration: PropTypes.number,
    }),
  ),
  showLayoverDetails: PropTypes.bool,
};

const regularLightText = {
  fontSize: 12,
  color: colors.lightTextColor,
  lineHeight: 18,
};

const textStyle = {
  getRegularLightTextFontStyle: () => {
    return fontStyle('regular');
  },
  getDayTextFontStyle: () => {
    return { ...textStyle.getRegularLightTextFontStyle() };
  },
  getDurationFontStyle: () => {
    return { ...textStyle.getRegularLightTextFontStyle() };
  },
  getStationDetailsTextFontStyle: () => {
    return { ...textStyle.getRegularLightTextFontStyle() };
  },
  getTimeTextFontStyle: () => {
    return fontStyle('black');
  },
};
const styles = StyleSheet.create({
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 12,
  },
  timeText: {
    fontSize: 12,
    color: colors.black,
    lineHeight: 16,
  },
  dayText: {
    ...regularLightText,
  },
  duration: {
    ...regularLightText,
  },
  layoverDuration: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.brown2,
  },
  layoverContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  separator1: {
    width: '100%',
    height: 1,
    borderColor: colors.lightGrey,
    borderWidth: 1,
  },
  separator: {
    width: 30,
    height: 1,
    borderColor: colors.lightGrey,
    borderWidth: 1,
  },
});
