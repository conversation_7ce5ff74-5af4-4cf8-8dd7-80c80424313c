import { TrainBtwnStnsList } from '../../../RailsListingV3/types';

export interface LayoverDetails {
  startStationName: string;
  endStationName: string;
  layoverDuration: number;
  layoverDistance: number;
  layoverText: string;
  layoverListingWidgetText?: string;
}

export interface BusFooterTag {
  tag: string;
  iconUrl: string;
  placeHolder?: string;
  tagColor?: string;
}

export interface BusMetaData {
  mmt_source_city_name: string;
  mmt_destination_city_name: string;
  vendor_operator_name: string;
  rating: number;
  vendor_bus_type_name: string;
  totalReviews: number;
  departure_time: string;
  journey_time_in_minutes: number;
  availableSeats: number;
  avlSingleSeats: number;
  arrival_time: string;
  nextDayBus: boolean;
  footerTags: BusFooterTag[];
  departure_date: string;
  arrival_date: string;
}

export interface JourneyLeg {
  legNo: number;
  travelMode: string;
  minimumFare: number;
  fromCity: string;
  toCity: string;
  trainMetaData?: TrainBtwnStnsList;
  busMetaData?: BusMetaData;
  deepLink?: string;
}

export interface ConnectedTravelData {
  startDateTime: number;
  endDateTime: number;
  startDateTimeStr: string;
  endDateTimeStr: string;
  durationMins: number;
  startingPrice: number;
  layoverDetails: { 1: LayoverDetails };
  journeyLegs: JourneyLeg[];
  layOverDistanceInFloat: number;
  journeyDurationInFloat: number;
  startingPriceInFloat: number;
  optionCategoryType?: null | string;
  optionType?: null | string;
}

export interface optionTypes {
  price: number;
  durationMins: number;
}
export interface ConnectedTravelResponse {
  srcCityName: string;
  dstnCityName: string;
  connectedTravelOptions: ConnectedTravelData[];
  date?: number;
  twoTrains?: ConnectedTravelData[]
  trainAndBus?: ConnectedTravelData[]
  connectedTravelListingOptions: ConnectedTravelData[];
  optionTypes?: {
    TRAIN_AND_BUS: optionTypes;
    TWO_TRAINS: optionTypes;
  }
}

export interface OptionCategoryTypeChipProps {
  allCardsExpanded: boolean;
  cardIndex: number;
  optionCategoryType?: null | string;
}
