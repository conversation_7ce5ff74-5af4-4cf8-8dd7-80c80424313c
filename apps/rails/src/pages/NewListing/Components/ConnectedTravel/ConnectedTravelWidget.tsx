import React, { useEffect, useState } from 'react';
import { View, Text, TouchableWithoutFeedback, Image } from 'react-native';
import { connect } from 'react-redux';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { colors, gradient } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import ConnectedTravelCard from './Components/ConnectedTravelCard';
import LinearGradient from 'react-native-linear-gradient';
import { styles } from './Styles/ConnectedTravelWidget.styles';
import {
  toggleConnectedTravelBottomSheet,
  dispatchConnectedBottomSheetData,
} from '../../RailsListingActions';
import { trackClickEventProp61 } from 'apps/rails/src/pages/RailsBusHomePage/Analytics';
import { LISTING_CONNECTED_TRAVEL_EVENTS, RAILS_LISTING } from 'apps/rails/src/Utils/RailsConstant';
import { ConnectedTravelData, ConnectedTravelResponse } from './types';
import DirectBusCard from './Components/DirectBusCard';
import isEmpty from 'lodash/isEmpty';
import { getConnectedTravelCardProp61 } from '../../../../Utils/RailsConstant';
import { pdtCtEventValues } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { logConnectedTravelPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import { Actions } from 'apps/rails/src/navigation';
import MultiStyleVernacText from '@mmt/rails/src/vernacular/MultiStyleVernacText';
import { connectedTravelVariants } from '@mmt/rails/src/RailsAbConstants';

import downArrow from '@mmt/legacy-assets/src/ic_arrow_blue_down.webp';
import rightArrow from '@mmt/legacy-assets/src/right_arrow_blue.webp';
interface ConnectedTravelWidgetProps {
  connectedTravelData: ConnectedTravelResponse;
  dispatchConnectedBottomSheetData: (data: ConnectedTravelData) => void;
  openBottomSheet: () => void;
  errorView?: boolean;
  originStation: string;
  destinationStation: string;
  departureDate: string;
  availabiltyBucket: string;
  showCtV2: number;
}

function ConnectedTravelWidget(props: ConnectedTravelWidgetProps) {
  const connectedTravelData = props?.connectedTravelData;
  const [isExpanded, setIsExpanded] = useState(false);
  useEffect(() => {
    setIsExpanded(!!props.errorView);
  }, [props.errorView]);
  const { srcCityName, dstnCityName, connectedTravelOptions = [],
    connectedTravelListingOptions = [] } = connectedTravelData ?? {};
  const totalNumberOfOptions = connectedTravelOptions?.length;

  const toggleViewOptions = () => {
    if (props?.showCtV2 === connectedTravelVariants.MOVED_DIRECT_LISTING) {
      Actions.connectedTravelPage();
      trackClickEventProp61(RAILS_LISTING, LISTING_CONNECTED_TRAVEL_EVENTS.viewAllClickedV2);
      return;
    }
    if (isExpanded) {
      Actions.connectedTravelPage();
      trackClickEventProp61(RAILS_LISTING, LISTING_CONNECTED_TRAVEL_EVENTS.viewAllClicked);
    } else {
      setIsExpanded((isExpanded) => !isExpanded);
      trackClickEventProp61(RAILS_LISTING, LISTING_CONNECTED_TRAVEL_EVENTS.expandClick);
    }
  };

  const handleCardClick = (connectedTravel: ConnectedTravelData) => {
    if (props?.showCtV2 === connectedTravelVariants.MOVED_DIRECT_LISTING) {
      Actions.connectedTravelPage();
      trackClickEventProp61(RAILS_LISTING, LISTING_CONNECTED_TRAVEL_EVENTS.viewAllClickedV2);
      return;
    }
    const searchContext = {
      originStation: props?.originStation,
      destinationStation: props?.destinationStation,
      departureDate: props?.departureDate,
    };
    if (isExpanded) {
      props.dispatchConnectedBottomSheetData(connectedTravel);
      props.openBottomSheet();
      trackClickEventProp61(
        RAILS_LISTING,
        connectedTravel?.optionCategoryType
          ? getConnectedTravelCardProp61(connectedTravel.optionCategoryType)
          : LISTING_CONNECTED_TRAVEL_EVENTS.cardClick,
      );
      logConnectedTravelPdtEvent(pdtCtEventValues.INTERACTED, [connectedTravel], searchContext,props.availabiltyBucket);
    } else {
      setIsExpanded((isExpanded) => !isExpanded);
      props.dispatchConnectedBottomSheetData(connectedTravel);
      trackClickEventProp61(RAILS_LISTING, LISTING_CONNECTED_TRAVEL_EVENTS.expandClick);
      logConnectedTravelPdtEvent(
        pdtCtEventValues.EXPANDED,
        connectedTravelData?.connectedTravelOptions,
        searchContext,
        props.availabiltyBucket,
      );
    }
  };

  const ConnectedTravelCards = () => {
    const topConnectedTravelOptions = (connectedTravelListingOptions?.length > 0)
    ? connectedTravelListingOptions : connectedTravelOptions.slice(0,2);
    return topConnectedTravelOptions.map((connectedTravel: ConnectedTravelData, i: number) => {
      const isDirectBusOption = isEmpty(connectedTravel.layoverDetails);

      return isDirectBusOption ? (
        <DirectBusCard
          connectedTravel={connectedTravel}
          index={i}
          isExpanded={isExpanded}
          handleCardClick={() => handleCardClick(connectedTravel)}
        />
      ) : (
        <ConnectedTravelCard
          connectedTravel={connectedTravel}
          index={i}
          isExpanded={isExpanded}
          handleCardClick={() => handleCardClick(connectedTravel)}
        />
      );
    });
  };

  return (
    <>
      {connectedTravelData && (
        <LinearGradient
          style={styles.container}
          colors={[colors.orange6, colors.white]}
          start={{ x: 0.9, y: 0 }}
          end={{ x: 0.5, y: 0.5 }}
        >
          <View style={styles.headerContainer} testID="connected_travel_widget_header_container">
            <Text style={[styles.blackText, fontStyle('regular'), getLineHeight(16)]}>
              {_label('explore_other_options')}
            </Text>
            <Text style={[styles.blackText, fontStyle('black'), getLineHeight(16)]}>
              {`${srcCityName} ${_label('to', { lowercase: true })} ${dstnCityName}`}
            </Text>
          </View>
          <View style={styles.cardsContainer}>
            <ConnectedTravelCards />
          </View>
          {totalNumberOfOptions > 1 && (
            <View style={styles.textContainer}>
              <>
                {!isExpanded && (
                  <TouchableWithoutFeedback
                    onPress={toggleViewOptions}
                    testID="connected_travel_widget_view_options_button"
                  >
                    <View style={styles.optionsView}>
                      <Text style={[styles.azureColor, fontStyle('regular'), getLineHeight(12)]}>
                        {props?.showCtV2 === connectedTravelVariants.MOVED_DIRECT_LISTING ? (
                          <MultiStyleVernacText
                            content={_label('view_all_options')}
                            contentHorizontal
                            defaultContentStyle={[
                              { color: colors.azure },
                              fontStyle('regular'),
                              getLineHeight(12),
                            ]}
                            params={{ X: totalNumberOfOptions }}
                          />
                        ) : (
                          _label('view_options')
                        )}
                      </Text>
                      {props?.showCtV2 === connectedTravelVariants.MOVED_DIRECT_LISTING ? (
                        <Image style={styles.arwImgRight} source={rightArrow} />
                      ) : (
                        <Image style={styles.arwImg} source={downArrow} />
                      )}
                    </View>
                  </TouchableWithoutFeedback>
                )}
                {isExpanded && totalNumberOfOptions > 3 && (
                  <TouchableWithoutFeedback
                    onPress={toggleViewOptions}
                    testID="connected_travel_widget_view_all_options_button"
                  >
                    <LinearGradient
                      style={styles.linearGrad}
                      colors={gradient.lightBlue}
                      start={{
                        x: 0,
                        y: 2,
                      }}
                      end={{
                        x: 1,
                        y: 1,
                      }}
                    >
                      <Text style={styles.blueButtonText}>
                        {_label('view_all')} {totalNumberOfOptions} {_label('options')}
                      </Text>
                    </LinearGradient>
                  </TouchableWithoutFeedback>
                )}
              </>
            </View>
          )}
        </LinearGradient>
      )}
    </>
  );
}
//

const mapStateToProps = ({
  railsListing: { originStation = '', destinationStation = '', departureDate = '' },
}) => {
  return { originStation, destinationStation, departureDate };
};

const mapDispatchToProps = (dispatch) => ({
  dispatchConnectedBottomSheetData: (data) => {
    dispatch(dispatchConnectedBottomSheetData(data));
  },
  openBottomSheet: () => {
    dispatch(toggleConnectedTravelBottomSheet(true, false));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(ConnectedTravelWidget);
