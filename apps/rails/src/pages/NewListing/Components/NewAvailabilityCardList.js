import { ScrollView, View } from 'react-native';
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  getAvailabilityCardTrackingData,
} from '../RailsListingActions';
import { UpdateAvailabilityCardV2 } from './UpdateAvailabilityCard_new';
import {
  removeEventFromEvar99Variable,
  trackGenericEvar99Event,
  trackNewListingClickEvent,
  trackNewListingEvent,
} from '../../../railsAnalytics';
import QuotaType from '../../Types/QuotaType';
import AvailabilityCardV3 from './AvailabilityCardV3';
import {
  getNearbyDatesNew,
  getRailsFcCalloutFlag,
  getRailsListingMealInfo,
  getGreenWaitlistedComponent,
  getRailsWlUrgencyPersuasion,
  getRailsListingRegretEduApps,
} from 'apps/rails/src/RailsAbConfig';
import { getSelectedCardHash } from './NearByDates/Utils/NearByDatesUtils';
import { _label } from '../../../vernacular/AppLanguage';
import {
  LISTING_PAGE_TRACKING_KEY,
  LISTING_PDT_TRAIN_CARD_DETAILS,
  SCHEDULE_TATKAL_EVENTS,
  AVAILABILITY_STATUS,
  WIDGET_SECTION_SEARCHED,
  WIDGET_SECTION_BOOKED,
} from '../../../Utils/RailsConstant';
import { setDataInStorage, removeDataFromStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { showScheduleTatkal } from '../../../RailsAbConfig';
import { WlUrgencyPersuasion, LISTING_REGRET_EDU_POKUS } from '../../../RailsAbConstants';
import ScheduleTatkalListingCard from './ScheduleTatkalListingCard/ScheduleTatkalListingCard';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { LISTING_PDT_CLICK_EVENTS } from '../../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import TatkalAlertIcon from '../../TravelerDetails/Components/TatkalAlertIcon';
import { getTomorrow, truncateHours } from '@mmt/legacy-commons/Helpers/dateHelpers';

let lastWlOmnitureEvent = null;

export default class AvailabilityCardList extends PureComponent {
  constructor(props) {
    super(props);
    this.prevAvailability = [];
    this.shouldScroll = false;
    this.scrollRef = React.createRef();
    this.state = {
      showFcStamps: false,
      isNewNearbyDates: false,
      showGreenTGWL: false,
      showScheduleTatkalCard: false,
      showRailsMealInfo: false,
    };
  }

  async componentDidMount() {
    const showFcStampsPokus = await getRailsFcCalloutFlag();
    const isNewNearbyDates = await getNearbyDatesNew();
    const getWaitlistedComponent = getGreenWaitlistedComponent();
    const isLoggedIn = await isUserLoggedIn();
    const showListingMealInfo = await getRailsListingMealInfo();
    this.setState({
      showFcStamps: showFcStampsPokus,
      showGreenTGWL: getWaitlistedComponent,
      isNewNearbyDates,
      showScheduleTatkalCard: isLoggedIn && showScheduleTatkal(),
      showRailsMealInfo: showListingMealInfo,
    });
  }
  handleTatkalAlertClick = () => {
    if (this.scrollRef.current) {
      this.scrollRef.current.scrollToEnd({ animated: true });
    }
  };
  storeTrainDataInLocalStorage = async (trainData, position, cardClicked) => {
    // please get the filters information also.. check the state
    const trainCard = {
      trainPosition: position,
      trainCardClickedData: cardClicked,
      trainCardData: trainData,
    };

    await removeDataFromStorage(LISTING_PDT_TRAIN_CARD_DETAILS);
    await setDataInStorage(LISTING_PDT_TRAIN_CARD_DETAILS, trainCard);
  };

  handleUpdateAvailabilityCardClick = (item) => {
    const { trainData, onUpdateCardClick, trainPosition } = this.props;
    const { className, quota } = item;
    onUpdateCardClick(className, trainData, quota);
    this.storeTrainDataInLocalStorage(trainData, trainPosition, item);
  };

  handleAvailabilityCardClick = (item, index) => {
    const {
      trainData,
      onAvailabilityCardClick,
      actionSaveRailofyZcShown,
      trainPosition,
      toggleNearbyDatesList,
      nearbyDatesCardClick,
      nearbyDatesInfo,
      logTrainCardPdtClickEvents,
      sectionIdentifier,
      onUnavailableCardClick,
    } = this.props;
    const { showRailsMealInfo } = this.state;
    const { foodAvail } = trainData;
    const { className, quota } = item;
    const { selectedNearbyCardsHashList } = nearbyDatesInfo;
    const selectedCardHash = getSelectedCardHash(trainData.trainNumber, className, quota, sectionIdentifier);
    const isNotAvailableCard =
      item?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.NOT_AVAILABLE ||
      item?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.REGRET ||
      item?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.BOOKING_NOT_ALLOWED;

      if (isNotAvailableCard && onUnavailableCardClick) {
      const listingRegretEduPokus = getRailsListingRegretEduApps();
        onUnavailableCardClick(trainData, className, quota);
      if (listingRegretEduPokus === LISTING_REGRET_EDU_POKUS.SHOWN) {
        return;
      }
      }
    lastWlOmnitureEvent && removeEventFromEvar99Variable(lastWlOmnitureEvent);
    const wlUrgencyPersuasion = getRailsWlUrgencyPersuasion();
    const showWlUrgencyPersuasion = wlUrgencyPersuasion === WlUrgencyPersuasion.SHOWN;
    const omnitureEvent = item?.wlSeatUrgencyPersuasion?.omnitureEvent;
    lastWlOmnitureEvent = null;
    if (omnitureEvent && showWlUrgencyPersuasion) {
      trackGenericEvar99Event(LISTING_PAGE_TRACKING_KEY, omnitureEvent);
      lastWlOmnitureEvent = omnitureEvent;
    }

    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_MEAL_INFO);
    if (showRailsMealInfo && foodAvail) {
      trackGenericEvar99Event(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_MEAL_INFO,
      );
    }
    const availabilityDepletionTracking = item?.availabilityDepletionTracking;
    logTrainCardPdtClickEvents(
      LISTING_PDT_CLICK_EVENTS.TRAIN_CARD_CLICK,
      this.props.trainPosition,
      index + 1,
      this.props.trainData.trainNumber,
      availabilityDepletionTracking,
      item?.wlSeatUrgencyPersuasion || null,
    );

    trackNewListingEvent(
      `mob_rail_listing_train_clicked_pos${trainPosition}`,
      getAvailabilityCardTrackingData(className, trainData, quota, item),
    );

    if (this.state.isNewNearbyDates) {
      if (selectedNearbyCardsHashList.includes(selectedCardHash)) {
        toggleNearbyDatesList(false, trainData.trainNumber, className, quota, sectionIdentifier);
      } else {
        toggleNearbyDatesList(true, trainData.trainNumber, className, quota, sectionIdentifier);
        nearbyDatesCardClick(trainData, className, quota, sectionIdentifier);
      }
      return;
    }

    onAvailabilityCardClick(className, trainData, quota);
    actionSaveRailofyZcShown({
      railofyShown: Boolean(item.confirmationGuaranteeText),
      zcShown: Boolean(item.freeCancellationText),
    });
    this.storeTrainDataInLocalStorage(trainData, trainPosition, item);
  };

  handleScheduleTatkalClick = async (item) => {
    const {
      trainData,
      onScheduleTatkalCardClick,
    } = this.props;
    await onScheduleTatkalCardClick?.(item, trainData);
    trackNewListingClickEvent(
      `${SCHEDULE_TATKAL_EVENTS.listingScheduleTatkalCardClick}${this.props?.trainPosition}`,
    );
  };

  showQuotaChip = (quota) => {
    return quota === QuotaType.TQ.code || quota === QuotaType.LD.code || quota === QuotaType.SS.code;
  };

  quotaLabel = (quota) => {
    switch (quota) {
      case QuotaType.TQ.code:
        return _label('tatkal', { uppercase: true });
      case QuotaType.LD.code:
        return _label('ladies_label');
      case QuotaType.SS.code:
        return _label('senior_citizen_label');
    }
  };

  handleNotAvailableCard = (item) => {
    const { trainData, notAvailableCard } = this.props;

    const regretEduPokus = getRailsListingRegretEduApps();
    if (regretEduPokus !== LISTING_REGRET_EDU_POKUS.SHOWN) {
      return false;
    }

    let sectionIdentifier = '';
    if (trainData?.uniqueCardId) {
      sectionIdentifier = trainData.uniqueCardId.split('_')[0] || '';
    }

    if (sectionIdentifier === WIDGET_SECTION_SEARCHED || sectionIdentifier === WIDGET_SECTION_BOOKED) {
      return false;
    }

    const hasUnavailableStatus =
      item?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.NOT_AVAILABLE ||
      item?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.REGRET ||
      item?.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.BOOKING_NOT_ALLOWED;
    const cardIdentifier = `${trainData.trainNumber}-${item.className}-${item.quota}`;
    const isSelectedCard = notAvailableCard === cardIdentifier;
    return hasUnavailableStatus && isSelectedCard;
  };

  render() {
    const {
      trainData,
      actionSaveRailofyZcShown,
      nearbyDatesInfo,
      showTatkalAlertTooltipPerSession,
      index,
      cardYPosition,
      updateTatkalAlertTooltip,
      isShownTatkalAlertTooltip,
    } = this.props;
    const {
      tbsAvailability: availability = [],
      scheduledTatkalCards = [],
    } = trainData;
    this.shouldScroll = availability?.length !== this.prevAvailability?.length;
    this.prevAvailability = availability;
    const { selectedNearbyCardsHashList } = nearbyDatesInfo;
    const now = new Date();
    const isBefore1110 = now.getHours() < 11 || (now.getHours() === 11 && now.getMinutes() < 10);
    const renderTrue = Boolean(
      isShownTatkalAlertTooltip &&
        scheduledTatkalCards?.length > 0 &&
        showTatkalAlertTooltipPerSession &&
        index === 0 &&
        !!cardYPosition,
    );
    return (
      <View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={{ paddingTop: 10 }}
          ref={this.scrollRef}
        >
          {availability?.map((item, index) => {
            const {
              totalFare, className, quota, isLoading,
            } = item;
            if (totalFare === 0 && !item.availablityStatus) {
              return (
                <View style={{ flexDirection: 'column' }} key={`update_${className}_${quota}`}>
                  <View style={{ height: 5 }} />
                  <UpdateAvailabilityCardV2
                    showQuotaChip={this.showQuotaChip(quota)}
                    quotaLabel={this.quotaLabel(quota)}
                    className={className}
                    key={className + quota}
                    id={`${this.props?.id}_updateAvailability_${quota}_${className}`}
                    onClick={() => {
                      this.handleUpdateAvailabilityCardClick(item);
                    }}
                    isLoading={isLoading}
                  />
                </View>
              );
            }
            return (
              <View style={{ flexDirection: 'column' }} key={`avail_${className}_${quota}`}>
                <View style={{ height: 5 }} />
                <AvailabilityCardV3
                  showQuotaChip={this.showQuotaChip(quota)}
                  quotaLabel={this.quotaLabel(quota)}
                  key={className + quota}
                  id={`${this.props?.id}_available_${quota}_${className}`}
                  availabilityItem={item}
                  onClick={() => {
                    this.handleAvailabilityCardClick(item, index);
                  }}
                  showFcStamps={this.state.showFcStamps}
                  actionSaveRailofyZcShown={actionSaveRailofyZcShown}
                  nearbyDatesInfo={nearbyDatesInfo}
                  isCardSelected={selectedNearbyCardsHashList.includes(
                    getSelectedCardHash(trainData.trainNumber, className, quota, this.props.sectionIdentifier),
                  )}
                  showGreenTGWL={this.state.showGreenTGWL}
                  isNewNearbyDates={this.state.isNewNearbyDates}
                  isNotAvailableCard={this.handleNotAvailableCard(item)}
                />
              </View>
            );
          })}
          {(isBefore1110 ||
            truncateHours(getTomorrow()).getTime() !==
              truncateHours(new Date(trainData?.departureDateAndTime)).getTime()) &&
            this.state?.showScheduleTatkalCard &&
            scheduledTatkalCards?.map((item) => {
              return (
                <View style={{ flexDirection: 'column' }} key={'tatkal-card'}>
                  <View style={{ height: 5 }} />
                  <ScheduleTatkalListingCard
                    item={item}
                    onScheduleTatkalCardClick={(item) => {
                      this.handleScheduleTatkalClick(item);
                    }}
                  />
                </View>
              );
            })}
        </ScrollView>
        {renderTrue && (
          <TatkalAlertIcon
            onClick={this.handleTatkalAlertClick}
            positionY={cardYPosition}
            updateTatkalAlertTooltip={updateTatkalAlertTooltip}
          />
        )}
      </View>
    );
  }
}

AvailabilityCardList.propTypes = {
  onAvailabilityCardClick: PropTypes.func.isRequired,
  onUpdateCardClick: PropTypes.func.isRequired,
  trainData: PropTypes.shape({
    tbsAvailability: PropTypes.array,
    scheduledTatkalCards: PropTypes.array,
    trainNumber: PropTypes.string,
    foodAvail: PropTypes.bool,
    departureDateAndTime: PropTypes.string,
    uniqueCardId: PropTypes.string,
  }),
  railsShowConfirmationChance: PropTypes.bool,
  confirmationChance: PropTypes.object,
  logTrainCardPdtClickEvents: PropTypes.func,
  sectionIdentifier: PropTypes.string,
  trainPosition: PropTypes.number,
  actionSaveRailofyZcShown: PropTypes.func,
  toggleNearbyDatesList: PropTypes.func,
  nearbyDatesCardClick: PropTypes.func,
  nearbyDatesInfo: PropTypes.shape({
    selectedNearbyCardsHashList: PropTypes.arrayOf(PropTypes.string),
  }),
  onScheduleTatkalCardClick: PropTypes.func,
  showTatkalAlertTooltipPerSession: PropTypes.bool,
  index: PropTypes.number,
  cardYPosition: PropTypes.number,
  updateTatkalAlertTooltip: PropTypes.func,
  isShownTatkalAlertTooltip: PropTypes.bool,
  id: PropTypes.string,
  notAvailableCard: PropTypes.string,
  onUnavailableCardClick: PropTypes.func,
};


AvailabilityCardList.defaultProps = {
  railsShowConfirmationChance: false,
  confirmationChance: null,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  logTrainCardPdtClickEvents: () => {},
  sectionIdentifier: '',
};
