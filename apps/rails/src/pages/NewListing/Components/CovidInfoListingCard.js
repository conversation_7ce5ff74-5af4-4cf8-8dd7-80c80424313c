import React from 'react';
import PropTypes from 'prop-types';
import {View, TouchableOpacity, Text, StyleSheet} from 'react-native';
import { Actions } from '../../../navigation';
import LinearGradient from 'react-native-linear-gradient';
import { colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';


export default function CovidInfoListingCard(props) {
  const {
    data: {
      heading,
      subHeading,
      guideLinesLinkText,
      guideLinesLinkUrl,
    } = {},
  } = props;

  const onMoreDetailsPressed = () => {
    Actions.openWebView({
      url: guideLinesLinkUrl,
      headerText: 'MakeMyTrip',
      headerIcon: backIcon,
    });
  };

  if (!heading) {
    return null;
  }

  return (
    <View style={styles.container} testID={props?.id}>
      <LinearGradient
        colors={['#ffffff', '#ffeeed']}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
        style={styles.gradient}
      >
        <Text style={[styles.heading, fontStyle('black'), getLineHeight(16)]}>{heading}</Text>
        <Text style={[styles.subHeading, fontStyle('regular'), getLineHeight(12)]}>{subHeading}</Text>
        <TouchableOpacity activeOpacity={0.6} onPress={onMoreDetailsPressed} style={styles.button}>
          <Text style={[styles.linkText, fontStyle('black'), getLineHeight(12)]}>{guideLinesLinkText}</Text>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.grayBg,
    paddingVertical: 10,
  },
  gradient: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderColor: colors.lightGray,
    borderWidth: 1,
  },
  heading: {
    fontSize: 16,
    color: colors.black,
  },
  subHeading: {
    fontSize: 12,
    color: colors.black,
    marginTop: 5,
  },
  button: {
    paddingVertical: 5,
  },
  linkText: {
    color: colors.azure,
    fontSize: 12,
  },
});

CovidInfoListingCard.propTypes = {
  data: PropTypes.object,
  id: PropTypes.string,
};

CovidInfoListingCard.defaultProps = {
  data: {},
};
