import React from 'react';
import { View, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default function Placeholder(props: {id?: string}) {
  const placeholderColors = ['#f1f1f1', '#f3f3f3', '#d3d3d3', '#f5f5f5'];
  return (
    <View style={styles.container} testID={props?.id}>
      <View style={styles.shimmerContainer}>
        <LinearGradient
          colors={placeholderColors}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 0.0 }}
          style={styles.sideShimmer}
        />
        <View style={{ flex: 1 }}>
          <LinearGradient
            colors={placeholderColors}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
            style={styles.shimmer1}
          />
          <LinearGradient
            colors={placeholderColors}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
            style={styles.shimmer2}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.grayBg,
    paddingVertical: 12,
  },
  shimmerContainer: {
    height: 100,
    width: '100%',
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: 'row',
  },
  sideShimmer: {
    height: 70,
    width: 70,
    borderRadius: 36,
    marginRight: 24,
  },
  shimmer1: {
    marginBottom: 8,
    height: 16,
  },
  shimmer2: {
    marginBottom: 8, height: 38,
  },
  shimmer3: {
    height: 16,
    width: 150,
  },
});
