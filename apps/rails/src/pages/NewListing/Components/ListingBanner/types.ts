export interface RailsListingBanners {
  railsListingBanners: RailsListingBanner[];
}

export interface RailsListingBanner {
  type: string;
  value: TGSBannerConfig;
}

export interface TGSBannerConfig {
  headerText: string;
  backgroundColor: string[];
  bannerImageUrl: string;
  additionalText: string;
  additionalTextURL: string;
  bannerTag: BannerTag;
  description: Description[];
}

export interface BannerTag {
  text: string;
  color: string;
  fontFamily: string;
  fontSize: string;
  backgroundColor: string[];
  nonBreakableText: boolean;
}

export interface Description {
  text: string;
  color: string;
  fontFamily: string;
  fontSize: string;
  backgroundColor: unknown[];
  nonBreakableText: boolean;
}
