import React, { useState, useEffect } from 'react';
import MMTHighlightsCarousel from 'apps/rails/src/configStore/Landing/MMTListingCarousel';
import NewUserPersuation from '../../../Common/NewUserPersuationBanner';
import { SEATLOCK_EVAR_97, seatLockConfig } from '@mmt/rails/src/Utils/RailsConstant';
import { getRailsSeatLockListingVariants } from '@mmt/rails/src/RailsAbConfig';
import { LISTING_PAGE_CONST } from '../../../../Utils/RailsConstant';
import { TextStyleBE } from '../../../../types/railofy.types';
import ListingBannerPlaceholder from './ListingBannerPlaceholder';
import { MmtlistingHighlight } from 'apps/rails/src/configStore/Landing/types';
import {
  removeEventFromEvar47or97Variable,
  updateEvar47or97Variable,
} from 'apps/rails/src/railsAnalytics';

interface ListingBannerProps {
  confirmationGuaranteeConfig: {
    confirmationGuaranteeWidget: {
      additionalText: string;
      additionalTextURL: string;
      backgroundColor: string[];
      bannerImageUrl: string;
      bannerTag: TextStyleBE;
      description: TextStyleBE[];
      headerText: string;
    };
    railsConfirmationGuaranteeOption: number;
    isInit: boolean;
  };
  mmtlistingHighlights: MmtlistingHighlight;
  covidSafetyCardData: unknown;
  id?: string;
  appVersion?: string;
  showBnpp?: boolean;
}

const ListingBanner = (props: ListingBannerProps) => {
  const [highLightsData, setHighLightsData] = useState([]);

  useEffect(() => {
    (async () => {
      removeEventFromEvar47or97Variable(`${SEATLOCK_EVAR_97}1`);
      removeEventFromEvar47or97Variable(`${SEATLOCK_EVAR_97}2`);
      const val = await getRailsSeatLockListingVariants();
      let data = props?.mmtlistingHighlights;
      if (val && props?.showBnpp) {
        updateEvar47or97Variable(`${SEATLOCK_EVAR_97}${val}`);
        if (val === 2) {
          const filteredData = data?.filter((x) => x.code !== 'BNPP');
          data = [seatLockConfig, ...filteredData];
        }
      }
      setHighLightsData(data);
    })();
  }, [props?.mmtlistingHighlights, props?.showBnpp]);


  if (props.mmtlistingHighlights === undefined) {
    return <ListingBannerPlaceholder id={`${props?.id}_listingBannerPlaceholder`} />;
  }

  return (
    <>
      <NewUserPersuation page={LISTING_PAGE_CONST} />
      <MMTHighlightsCarousel
        mmtlistingHighlights={highLightsData}
        id={`${props?.id}_railofy`}
        appVersion={props.appVersion}
        showBnpp={props.showBnpp}
      />
    </>
  );
};

export default React.memo(ListingBanner);
