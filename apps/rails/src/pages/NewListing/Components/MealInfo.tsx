import React, { useEffect, useState } from 'react';
import { StyleSheet, Text } from 'react-native';
import LottieView from 'lottie-react-native';
import LinearGradient from 'react-native-linear-gradient';
import { MEALS_AVAILABLE } from '../../../Utils/RailsConstant';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import LottieDataProvider from 'apps/rails/src/pages/NewListing/Utils/LottieProvider';


const MealInfo = () => {
  const [animationData, setAnimationData] = useState(null);
  useEffect(() => {
    const fetchAnimation = async () => {
      try {
        const data = await LottieDataProvider.getLottieData();
        setAnimationData(data);
      } catch (error) {
        console.error('Error fetching animation data:', error);
      }
    };
    fetchAnimation();
  }, []);
  return (
    <LinearGradient
      colors={[colors.green18, colors.white, colors.white]}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      testID="meal_info_linear_gradient"
    >
      {animationData && (
        <LottieView
          source={animationData}
          autoPlay
          loop
          style={styles.lottie}
          testID="meal_info_lottie_view"
        />
      )}
      <Text style={styles.textStyle} testID="meal_info_text">{MEALS_AVAILABLE}</Text>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    padding: 10,
    gap: 4,
  },
  lottie: {
    width: 14,
    height: 14,
    backgroundColor: colors.transparent,
  },
  textStyle: {
    fontFamily: 'Inter, Lato',
    fontSize: 12,
    fontWeight: '400',
    color: colors.lightGreen16,
  },
});

export default MealInfo;
