import React from 'react';
import { Platform, View, Text, Image, StyleSheet } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import DateInput from '../../RailsLandingPage/Components/DateInput/DateInput_v2';
import { connect } from 'react-redux';
import {
  onOriginClicked,
  onDestinationClicked,
  setDepartureDate,
  onDateClick,
  onSwapClicked,
  showHideEditWidget, ACTION_RAILS_LISTING_EDIT_ORIGIN,ACTION_RAILS_LISTING_EDIT_DESTINATION,
} from '../RailsListingActions';
import { RailCityInput } from '../../RailsLandingPage/Components/RailsCityInput/RailsCityInput_v2';
import _isEmpty from 'lodash/isEmpty';
import SwapBtnWithDivider from '../../RailsLandingPage/Components/SwapButton/swapBtnWithDivider_v2';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
// import { backIcon } from '../../../../../Assets/ic_close_blue.png';
import LinearGradient from 'react-native-linear-gradient';
import { LISTING_PAGE_CONST } from '../../../Utils/RailsConstant';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { trackClickEvent } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import CustomSafeAreaView from '@mmt/legacy-commons/Common/Components/CustomSafeAreaView';
import { ACTION_ON_DESTINATION_SELECTED,ACTION_ON_ORIGIN_SELECTED} from '../../RailsLandingPage/Store/RailsLandingActionTypes';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { _label } from '../../../vernacular/AppLanguage';
import {setDepartureDate as setDepartureDateOnLandingPage, onSwapClicked as onSwapClickedOnLandingPage} from '../../RailsLandingPage/Store/RailsLandingPageActions';
import {format} from 'fecha';
import PropTypes from 'prop-types';
const mapStateOrigin = (state) => {
  const {
    railsListing: { originStation },
  } = state;
  const origin = _isEmpty(originStation)
    ? {}
    : originStation.code
    ? originStation
    : {};
  return {
    label: _label('from', { uppercase: true }),
    value: origin,
    placeholder: _label('search_city_placeholder'),
    icon: 'from',
  };
};

const mapDispatchOrigin = (dispatch) => ({
  onClick: () => {
    trackClickEvent('mob_rail_listing_v2', 'from_city_clicked');
    return dispatch(onOriginClicked);
  },
});

const OriginStationInput = connect(
  mapStateOrigin,
  mapDispatchOrigin,
)(RailCityInput);

const mapStateDestination = (state) => {
  const {
    railsListing: { destinationStation },
  } = state;
  const dest = _isEmpty(destinationStation)
    ? {}
    : destinationStation.code
    ? destinationStation
    : {};
  return {
    label: _label('to'),
    value: dest,
    placeholder: _label('search_city_placeholder'),
    icon: 'to',
  };
};

const mapDispatchDestination = (dispatch) => ({
  onClick: () => {
    trackClickEvent('mob_rail_listing_v2', 'to_city_clicked');
    return dispatch(onDestinationClicked);
  },
});

const DestinationStationInput = connect(
  mapStateDestination,
  mapDispatchDestination,
)(RailCityInput);

const mapStateToPropsDate = (state) => {
  const {
    railsListing: { departureDate = new Date() },
  } = state;
  return {
    departureDate,
    fromPage: LISTING_PAGE_CONST,
    labelText: _label('date'),
  };
};

const mapDispatchToPropsDate = (dispatch) => ({
  onDateClick: () => {
    trackClickEvent('mob_rail_listing_v2', 'dep_date_clicked');
    return dispatch(onDateClick);
  },
  setDepartureDate: (date) => dispatch(setDepartureDate(date)),
});

const JourneyDate = connect(
  mapStateToPropsDate,
  mapDispatchToPropsDate,
)(DateInput);

const mapDispatchToPropsSwapButton = (dispatch) => ({
  onSwapClicked: () => {
    dispatch(onSwapClicked());
    dispatch(onSwapClickedOnLandingPage());
  },
});

const SwapButton = connect(
  null,
  mapDispatchToPropsSwapButton,
)(SwapBtnWithDivider);

class RailsListingEditWidget extends React.Component {
  constructor(props) {
    super(props);
    let originDestinationPresent =
      !_isEmpty(props.originStation) &&
      props.originStation.code &&
      !_isEmpty(props.destinationStation) &&
      props.destinationStation.code;
    this.state = {
      hasFields: originDestinationPresent,
      btnTitle: this.props.isEdit ? _label('modify_search', { uppercase: true }) : _label('search', { uppercase: true }),
      screenTitle: this.props.isEdit ? _label('modify_search', { uppercase: true }) : _label('train_search', { uppercase: true }),
    };
    this.originStationBeforeUpdate = null;
    this.destinationStationBeforeUpdate = null;
    this.dateBeforeUpdate = null;
    this.isSearchClicked = false;
  }

  backHandler = () => {
    this.props.onBack();
    trackClickEvent('mob_rail_listing_v2', 'back_button_header_clicked');
  };

  componentDidMount() {
    // Memoize The Values present at the time we enter the form , call it initial state
    this.originStationBeforeUpdate = this.props.originStation;
    this.destinationStationBeforeUpdate = this.props.destinationStation;
    this.dateBeforeUpdate = this.props.departureDate;
    // To make sure both landing and listing pages are in sync when edit widget opens
    this.revertToInitialState(
      this.originStationBeforeUpdate !== this.props.originStationOnLanding,
      this.destinationStationBeforeUpdate !== this.props.destinationStationOnLanding ,
      format(this.dateBeforeUpdate, 'DD/MM/YYYY') !== format(this.props.departureDateOnLanding,'DD/MM/YYYY'));
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (_isEmpty(this.originStationBeforeUpdate?.stationName) && !_isEmpty(nextProps.originStation?.stationName)) {
      this.originStationBeforeUpdate = nextProps.originStation;
      // Update on Landing and Listing
      this.revertToInitialState(true,false,false);
    }
    if (_isEmpty(this.destinationStationBeforeUpdate?.stationName)
      && !_isEmpty(nextProps.destinationStation?.stationName)) {
      this.destinationStationBeforeUpdate = nextProps.destinationStation;
      // Update on Landing and Listing
      this.revertToInitialState(false,true,false);
    }
  }

  componentWillUnmount() {
    // if uncommited changes AND if modify-search is NOT pressed after changing current state from initial state several times , then discard all these changes
    const hasOriginChanged = this.originStationBeforeUpdate?.code !== this.props.originStation?.code;
    const hasDestinationChanged = this.destinationStationBeforeUpdate?.code !== this.props.destinationStation?.code;
    const hasDateChanged = this.dateBeforeUpdate !== this.props.departureDate;
    if (!this.isSearchClicked && !this.props.showEditWidget) {
        this.revertToInitialState(hasOriginChanged, hasDestinationChanged, hasDateChanged);
      }
  }

  revertToInitialState = (hasOriginChanged, hasDestinationChanged, hasDateChanged) => {
      if (hasOriginChanged) {
        this.props.onDone(this.originStationBeforeUpdate,ACTION_RAILS_LISTING_EDIT_ORIGIN);
        this.props.onDone(this.originStationBeforeUpdate,ACTION_ON_ORIGIN_SELECTED);
      }
      if (hasDestinationChanged) {
        this.props.onDone(this.destinationStationBeforeUpdate,ACTION_ON_DESTINATION_SELECTED);
        this.props.onDone(this.destinationStationBeforeUpdate,ACTION_RAILS_LISTING_EDIT_DESTINATION);
      }
      if (hasDateChanged) {
        this.props.setDepartureDate(this.dateBeforeUpdate);
        this.props.setDepartureDateOnLandingPage(this.dateBeforeUpdate);
      }
  };
  onModifySearchClicked = () => {
    this.isSearchClicked = true;    // Remember the modify-search click
    this.props.setDepartureDateOnLandingPage(this.props.departureDate); // update date on search on Landing Page
    this.props.SearchClicked();
  };
  render() {
    let { hasFields, btnTitle, screenTitle } = this.state;
    let containerHeight = Platform.OS === 'web' ? {} : { height: 340 };
    return (
      <>
      { Platform.OS === 'ios' ? <CustomSafeAreaView /> : null }
      <View style={Platform.OS === 'ios' ? styles.formStyle : styles.form}>
        <View style={{ ...styles.containerStyle, ...containerHeight }}>
          <View style={styles.headerStyle} testID={this?.props?.id}>
            <TouchableRipple
              onPress={() => {
                this.backHandler();
              }}
            >
              <Image style={{ height: 16, width: 16 }} source={backIcon} />
            </TouchableRipple>
            <Text style={[styles.screenTitle, fontStyle('bold'), getLineHeight(18)]} >
              {screenTitle}
            </Text>
          </View>

          {/* <View style={divider} /> */}

          <OriginStationInput id={`${this?.props?.id}_origin_station`}/>

          {/* {!hasFields && <View style={divider} />} */}

          <DestinationStationInput id={`${this?.props?.id}_destination_station`}/>

          {hasFields && <SwapButton id={`${this?.props?.id}_swapButton`}/>}

          {/* <View style={divider} /> */}

          <JourneyDate id={`${this?.props?.id}_journey_date`}/>

          {/* <TouchableRipple style={btnStyle} onPress={() => this.props.SearchClicked()} title={btnTitle} >
                        <Text style={{color:'#fff',fontSize:16}}>{btnTitle}</Text>
                    </TouchableRipple> */}

          <TouchableRipple
            onPress={this.onModifySearchClicked}
            title={btnTitle}
          >
            <LinearGradient
              colors={['#53B2FE', '#065AF3']}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 0.0 }}
              style={styles.btnStyle}
            >
              <Text
                style={{ color: colors.white, fontSize: 18, fontFamily: fonts.black, ...getLineHeight(18) }}
                testID={`${this?.props?.id}_modify_search_button_text`}
              >
                {btnTitle}
              </Text>
            </LinearGradient>
          </TouchableRipple>
          {this.props.error !== '' && (
            <Text
              style={{
                display: 'flex',
                color: colors.red,
                alignSelf: 'center',
                marginBottom: 10,
              }}
            >
              {this.props.error}
            </Text>
          )}
          {/* <Button title={btnTitle}  onPress={() => this.props.SearchClicked()} /> */}
        </View>
      </View></>
    );
  }
}


RailsListingEditWidget.propTypes = {
  originStation: PropTypes.shape({
    code: PropTypes.string,
    stationName: PropTypes.string,
  }),
  destinationStation: PropTypes.shape({
    code: PropTypes.string,
    stationName: PropTypes.string,
  }),
  departureDate: PropTypes.instanceOf(Date),
  originStationOnLanding: PropTypes.shape({
    code: PropTypes.string,
    stationName: PropTypes.string,
  }),
  destinationStationOnLanding: PropTypes.shape({
    code: PropTypes.string,
    stationName: PropTypes.string,
  }),
  departureDateOnLanding: PropTypes.instanceOf(Date),
  isEdit: PropTypes.bool,
  onBack: PropTypes.func,
  showEditWidget: PropTypes.bool,
  onDone: PropTypes.func,
  setDepartureDate: PropTypes.func,
  setDepartureDateOnLandingPage: PropTypes.func,
  SearchClicked: PropTypes.func,
  id: PropTypes.string,
  error: PropTypes.string,
};

const styles = StyleSheet.create({
  formStyle: {
    backgroundColor: colors.white,
  },
  form: {
    backgroundColor: colors.white,
    paddingTop: 16,
  },
  divider: {
    width: '100%',
    borderBottomColor: colors.gray1,
    borderBottomWidth: 1.5,
  },
  containerStyle: {
    display: 'flex',
  },
  btnStyle: {
    display: 'flex',
    height: Platform.OS === 'web' ? 44 : 60,
    // backgroundImage: 'linear-gradient(to right, #53b2fe, #065af3)',
    margin: 10,
    marginHorizontal: 15,
    borderRadius: 4,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerStyle: {
    height: 60,
    display: 'flex',
    flexDirection: 'row',
    marginLeft: 10,
    alignItems: 'center',
  },
  screenTitle: {
    marginLeft: 16,
    fontSize: 18,
    lineHeight: 22,
    fontFamily: fonts.bold,
    color: colors.defaultTextColor,
  },
});

const mapStateToProps = (state) => {
  const {
    railsListing: { originStation, destinationStation, departureDate, showEditWidget },
    railsLanding: {originStation : originStationOnLanding ,
      destinationStation : destinationStationOnLanding , departureDate : departureDateOnLanding},
  } = state;
  return {
    originStation,
    destinationStation,
    departureDate,
    originStationOnLanding,
    destinationStationOnLanding,
    departureDateOnLanding,
    showEditWidget,
  };
};

const mapDispatchToProps = (dispatch) => ({
  showHideEditWidget: (data) => dispatch(showHideEditWidget(data)),
  onDone: (station, actionType) => { dispatch({ type: actionType, data: station }); },
  setDepartureDate: (date) => dispatch(setDepartureDate(date)),
  setDepartureDateOnLandingPage : (date) => dispatch(setDepartureDateOnLandingPage(date)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(RailsListingEditWidget);
