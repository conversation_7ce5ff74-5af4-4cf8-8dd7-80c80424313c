import { Image, StyleSheet, Text, View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import React, {Component} from 'react';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {AvailabilityItem} from './AvailabilityItem';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

import dynamicImage from '@mmt/legacy-assets/src/ic_fareup.webp';
export default class FutureAvailabilityList extends Component {
  componentDidUpdate() {
    if (!this.props.isLoading) {
      setTimeout(() => { this.props.onLoad(); }, 100);
    }
  }

  render() {
    const {
      selectedDate, availablityList, isLoading, trainData, onDateClick, error, errorMessage,
    } = this.props;
    if (isLoading) {
      return (
        <View style={styles.loadContainer}>
          <Spinner size={30} color={colors.azure} />
        </View>
      );
    }
    if (error) {
      return (
        <View style={styles.loadContainer} testID={`${this.props?.id}_error`}>
          <Text style={[styles.errorMessage, fontStyle('regular'), getLineHeight(14)]}>
            {errorMessage}
          </Text>
        </View>
      );
    }
    return (
      <View style={{ minHeight: 70 }} testID={this.props?.id}>
        {
          !isEmpty(availablityList) && availablityList.map((item, index) => {
            let selected = false;
            if (isEmpty(selectedDate)) {
              selected = index === 0;
            } else {
              selected = item.availablityDate === selectedDate.availablityDate;
            }
              return (
                <AvailabilityItem
                  id={`${this.props?.id}_${index}`}
                  key={item.availablityDate}
                  selected={selected}
                  item={item}
                  onClick={(dateSelected) => { onDateClick(dateSelected, trainData); }}
                />
            );
          })
        }
        {
          this.props.isDynamicPriced &&
          <View style={styles.dynamicContainer}>
            <View style={{flex: 1}}>
              <Text style={[styles.dynamicText, fontStyle('regular'), getLineHeight(12)]}>
              { _label('dynamic_pricing_info') }
              </Text>
            </View>
            <View style={styles.dynamicImageContainer}>
              <Image source={dynamicImage} style={styles.dynamicImage}/>
            </View>
          </View>
        }
      </View>
    );
  }
}

const styles = StyleSheet.create({
  loadContainer: {height: 70, alignItems: 'center', justifyContent: 'center'},
  errorMessage: {
    fontSize: 14,  color: colors.defaultTextColor, marginHorizontal: 16,
  },
  dynamicContainer: {
    flexDirection: 'row', marginHorizontal: 16, marginTop: 12, justifyContent: 'space-between',
  },
  dynamicImageContainer: {height: 24, width: 24},
  dynamicImage: {width: 24, height: 24},
  dynamicText: {
     fontSize: 12, color: colors.defaultTextColor, marginRight: 4,
  },
  dynamicPricingText: { fontSize: 12, color: colors.defaultTextColor},
});

FutureAvailabilityList.propTypes = {
  availablityList: PropTypes.array.isRequired,
  error: PropTypes.bool.isRequired,
  errorMessage: PropTypes.string.isRequired,
  isDynamicPriced: PropTypes.bool.isRequired,
  isLoading: PropTypes.bool.isRequired,
  onDateClick: PropTypes.func.isRequired,
  onLoad: PropTypes.func.isRequired,
  selectedDate: PropTypes.object,
  trainData: PropTypes.object.isRequired,
  id: PropTypes.string,
};

FutureAvailabilityList.defaultProps = {
  selectedDate: {},
};
