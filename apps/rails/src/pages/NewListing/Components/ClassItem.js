import {StyleSheet, Text, View} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import React from 'react';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {numAppendedWithRuppeeSymbol} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { _label } from '../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const ClassItem = ({
  className, classRate, selected, onSelect, itemID, id,
}) => {
  const bottomViewStyle = selected ? {height: 4, backgroundColor: '#008cff', width: '100%'} : {
    height: 4,
    backgroundColor: 'white',
    width: '100%',
  };
  return (
    <TouchableRipple
      testID={id}
      onPress={() => {
        onSelect(itemID);
      }}
    >
      <View style={styles.container}>
        <View style={{marginHorizontal: 12}}>
          <Text style={[styles.classNameText, fontStyle('bold'), getLineHeight(14)]}>
            {_label(className)}
          </Text>
          {
          isEmpty(classRate) ?
            <View style={{flexDirection: 'row'}}>
              <LinearGradient
                style={{height: 14, flex: 1}}
                colors={['#fafafa', '#f2f2f2']}
                start={{x: 0.0, y: 0.0}}
                end={{x: 1.0, y: 0.0}}
              />
            </View>
            :
            <RupeeText style={[styles.classRateText, fontStyle('bold'), getLineHeight(14)]}>{numAppendedWithRuppeeSymbol(classRate)}</RupeeText>
        }
        </View>
        <View style={bottomViewStyle} />
      </View>
    </TouchableRipple>
  );
};

ClassItem.propTypes = {
  className: PropTypes.string.isRequired,
  classRate: PropTypes.string.isRequired,
  itemID: PropTypes.string.isRequired,
  onSelect: PropTypes.func.isRequired,
  selected: PropTypes.bool.isRequired,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    height: 66,
    borderWidth: StyleSheet.hairlineWidth * 1,
    borderColor: colors.lightGrey,
    paddingTop: 8,
    minWidth: 90,
  },
  classNameText: {
    fontSize: 14, fontWeight: 'bold', color: colors.black, marginBottom: 8,
  },
  classRateText: {fontSize: 14,  color: colors.black},
});
