import React from 'react';
import { View, StyleSheet } from 'react-native';
import isEmpty from 'lodash/isEmpty';

import BottomsheetHeaderContainer from '../../Containers/Filters/BottomsheetHeaderContainer';
import { SmallBoxFilters } from '../SortAndFilterComponents';
import { _label } from '../../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

const filterStyles = StyleSheet.create({
  wrapper: {
    width: '100%',
    backgroundColor: colors.white,
    alignSelf: 'center',
    borderRadius: 4,
  },
});

function AvailabilityFilters({
  availabilityFilter,
  onFilterClick,
  onClearAllFiltersClicked,
  onClearSelectedFilter,
  resetState,
  version = 'V1',
  id,
}) {
  return (
    <View style={filterStyles.wrapper}>
      <BottomsheetHeaderContainer
        heading={_label('availability_filter', { uppercase: true })}
        onClearAllFiltersClicked={onClearAllFiltersClicked}
        onClearSelectedFilter={onClearSelectedFilter}
        filter1={availabilityFilter}
        resetState={resetState}
      />
      {!isEmpty(availabilityFilter?.filterTypesArray) && (
        <SmallBoxFilters
          id={id}
          headerLabel={_label('availability_filter')}
          filterType={availabilityFilter}
          onFilterClick={onFilterClick}
          version={version}
        />
      )}
    </View>
  );
}

AvailabilityFilters.propTypes = {
  availabilityFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  resetState: PropTypes.func,
  version: PropTypes.string,
  id: PropTypes.string,
};

export default AvailabilityFilters;
