import React from 'react';
import { View, StyleSheet } from 'react-native';

import isEmpty from 'lodash/isEmpty';

import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../../vernacular/AppLanguage';

import { SmallBoxFilters } from '../SortAndFilterComponents';
import BottomsheetHeaderContainer from '../../Containers/Filters/BottomsheetHeaderContainer';
import PropTypes from 'prop-types';

const filterStyles = StyleSheet.create({
  wrapper: {
    width: '100%',
    backgroundColor: colors.white,
    alignSelf: 'center',
    borderRadius: 4,
  },
});

function JourneyClassFilters({
  journeyClassFilter,
  onFilterClick,
  onClearAllFiltersClicked,
  onClearSelectedFilter,
  resetState,
  version = 'V1',
  id,
}) {
  return (
    <View style={filterStyles.wrapper}>
      <BottomsheetHeaderContainer
        heading={_label('journey_class', { uppercase: true })}
        onClearAllFiltersClicked={onClearAllFiltersClicked}
        onClearSelectedFilter={onClearSelectedFilter}
        filter1={journeyClassFilter}
        resetState={resetState}
      />
      {!isEmpty(journeyClassFilter?.filterTypesArray) && (
        <SmallBoxFilters
          id={id}
          headerLabel={_label('journey_class')}
          filterType={journeyClassFilter}
          onFilterClick={onFilterClick}
          version={version}
        />
      )}
    </View>
  );
}

JourneyClassFilters.propTypes = {
  journeyClassFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  resetState: PropTypes.func,
  version: PropTypes.string,
  id: PropTypes.string,
};

export default JourneyClassFilters;
