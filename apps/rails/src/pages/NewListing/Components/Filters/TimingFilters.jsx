import React from 'react';
import { StyleSheet, View } from 'react-native';
import isEmpty from 'lodash/isEmpty';

import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import BottomsheetHeaderContainer from '../../Containers/Filters/BottomsheetHeaderContainer';
import { LargeBoxFilters } from '../SortAndFilterComponents';
import { _label } from '../../../../vernacular/AppLanguage';
import PropTypes from 'prop-types';

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    backgroundColor: colors.white,
    alignSelf: 'center',
    borderRadius: 4,
  },
});

const TimingFilters = ({
  labels,
  departureTimeFilter,
  arrivalTimeFilter,
  onFilterClick,
  onClearAllFiltersClicked,
  onClearSelectedFilter,
  resetState,
  version = 'V1',
  id,
}) => {
  return (
    <View style={styles.wrapper}>
      <BottomsheetHeaderContainer
        heading={_label('timing_filter', { uppercase: true })}
        onClearAllFiltersClicked={onClearAllFiltersClicked}
        onClearSelectedFilter={onClearSelectedFilter}
        filter1={departureTimeFilter}
        filter2={arrivalTimeFilter}
        resetState={resetState}
      />
      {!isEmpty(departureTimeFilter?.filterTypesArray) && (
        <LargeBoxFilters
          id={`${id}_departureTime`}
          labels={labels}
          headerLabel={labels.departure_from_city}
          filterType={departureTimeFilter}
          onFilterClick={onFilterClick}
          version={version}
        />
      )}
      {!isEmpty(arrivalTimeFilter?.filterTypesArray) && (
        <React.Fragment>
          <LargeBoxFilters
            id={`${id}_arrivalTime`}
            labels={labels}
            headerLabel={labels.arrival_in_city}
            filterType={arrivalTimeFilter}
            onFilterClick={onFilterClick}
            version={version}
          />
        </React.Fragment>
      )}
    </View>
  );
};

TimingFilters.propTypes = {
  labels: PropTypes.shape({
    departure_from_city: PropTypes.string,
    arrival_in_city: PropTypes.string,
  }),
  departureTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  arrivalTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  resetState: PropTypes.func,
  version: PropTypes.string,
  id: PropTypes.string,
};

export default TimingFilters;
