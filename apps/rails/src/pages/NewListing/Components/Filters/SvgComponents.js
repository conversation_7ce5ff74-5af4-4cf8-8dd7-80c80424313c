import PropTypes from 'prop-types';
import * as React from 'react';
import Svg, { Path,Rect } from 'react-native-svg';

const AvailabilityIcon = ({ fillColor = '#ffffff' }) => (
  <Svg
    width={15}
    height={16}
    viewBox="0 0 15 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M3.27272 1.65H3.32272V1.6V0.05H4.82272V1.6V1.65H4.87272H9.67272H9.72272V1.6V0.05H11.2227V1.6V1.65H11.2727H12.8727C13.2838 1.65 13.6781 1.8133 13.9687 2.10398C14.2594 2.39467 14.4227 2.78891 14.4227 3.2V14.4C14.4227 14.8111 14.2594 15.2053 13.9687 15.496C13.6781 15.7867 13.2838 15.95 12.8727 15.95H1.67272C1.26164 15.95 0.867389 15.7867 0.576708 15.496C0.286026 15.2053 0.122723 14.8111 0.122723 14.4V3.2C0.122723 2.78891 0.286026 2.39467 0.576708 2.10398C0.867389 1.8133 1.26164 1.65 1.67272 1.65H3.27272ZM1.67272 6.35H1.62272V6.4V14.4V14.45H1.67272H12.8727H12.9227V14.4V6.4V6.35H12.8727H1.67272ZM1.67272 3.15H1.62272V3.2V4.8V4.85H1.67272H12.8727H12.9227V4.8V3.2V3.15H12.8727H1.67272ZM6.50808 10.9042L9.90712 7.50511L10.9676 8.5656L6.47272 13.0605L3.57783 10.1656L4.63832 9.10511L6.43737 10.9042L6.47272 10.9395L6.50808 10.9042Z"
      fill={fillColor}
      stroke="#191919"
      strokeWidth={0.1}
    />
  </Svg>
);

const ClassIcon = ({ fillColor = '#ffffff' }) => (
  <Svg
    width={12}
    height={16}
    viewBox="0 0 12 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M9.4036 11.9177C9.4036 11.9177 11.4507 6.45882 11.4507 3.72938C11.4507 0.999932 10.5 0.999932 10.5 0.999932C10.5 0.999932 9.5 0.817639 9.5 1.50003C9.5 2.18239 10 2.37475 10 3C10 4.36472 7.35652 6.45882 7.35652 8.5059C7.35652 10.553 9.4036 11.9177 9.4036 11.9177ZM5.9918 11.3036C6.67416 11.3036 9.06275 12.9411 9.06275 12.9411C9.26746 13.0775 9.06275 14.4764 9.06275 14.4764C9.06275 14.4764 8.63306 14.7154 7.26834 14.7154H2.16089C1.4103 14.7154 1.21527 14.1013 1.21527 13.3507V12.6683C1.21527 11.9177 1.8294 11.3036 2.57999 11.3036H5.9918Z"
      fill={fillColor}
    />
    <Path
      d="M2.16089 14.7154H7.26834C8.63306 14.7154 9.06275 14.4764 9.06275 14.4764C9.06275 14.4764 9.26746 13.0775 9.06275 12.9411C9.06275 12.9411 6.67416 11.3036 5.9918 11.3036H2.57999C1.8294 11.3036 1.21527 11.9177 1.21527 12.6683V13.3507C1.21527 14.1013 1.4103 14.7154 2.16089 14.7154ZM2.16089 14.7154L2.57999 15.4999H7.72241L8.55098 14.4764M9.4036 11.9177C9.4036 11.9177 11.4507 6.45882 11.4507 3.72938C11.4507 0.999932 10.5 0.999932 10.5 0.999932C10.5 0.999932 9.5 0.817639 9.5 1.50003C9.5 2.18239 10 2.37475 10 3C10 4.36472 7.35652 6.45882 7.35652 8.5059C7.35652 10.553 9.4036 11.9177 9.4036 11.9177Z"
      stroke={fillColor}
      strokeWidth={0.511771}
    />
  </Svg>
);


const FilterIcon = ({ fillColor = '#ffffff'}) => (
  <Svg
    width={14}
    height={14}
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M12.8949 0H1.10529C0.909862 0 0.722442 0.0776319 0.584256 0.215818C0.446071 0.354003 0.368439 0.541424 0.368439 0.736848V2.64528C0.368439 3.03066 0.525387 3.4094 0.798021 3.68203L4.78953 7.67353V13.2633C4.78967 13.3888 4.82185 13.5122 4.88303 13.6218C4.94421 13.7315 5.03235 13.8237 5.13912 13.8897C5.24588 13.9557 5.36774 13.9934 5.49314 13.9992C5.61855 14.005 5.74336 13.9787 5.85574 13.9227L8.80314 12.449C9.05293 12.3238 9.21061 12.0688 9.21061 11.7896V7.67353L13.2021 3.68203C13.4748 3.4094 13.6317 3.03066 13.6317 2.64528V0.736848C13.6317 0.541424 13.5541 0.354003 13.4159 0.215818C13.2777 0.0776319 13.0903 0 12.8949 0ZM7.95281 6.84753C7.88426 6.91584 7.82989 6.99703 7.79283 7.08644C7.75578 7.17585 7.73678 7.2717 7.73692 7.36848V11.3342L6.26322 12.071V7.36848C6.26336 7.2717 6.24436 7.17585 6.20731 7.08644C6.17025 6.99703 6.11588 6.91584 6.04733 6.84753L1.84213 2.64528V1.4737H12.1587L12.1602 2.64013L7.95281 6.84753Z"
      fill={fillColor}
    />
  </Svg>
);

const SortIcon = ({ fillColor = '#ffffff'}) => (
  <Svg
    width={16}
    height={13}
    viewBox="0 0 16 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M9.39285 11.1429H7.5357C7.41257 11.1429 7.29448 11.1918 7.20741 11.2788C7.12033 11.3659 7.07142 11.484 7.07142 11.6071V12.5357C7.07142 12.6588 7.12033 12.7769 7.20741 12.864C7.29448 12.9511 7.41257 13 7.5357 13H9.39285C9.51598 13 9.63408 12.9511 9.72115 12.864C9.80822 12.7769 9.85713 12.6588 9.85713 12.5357V11.6071C9.85713 11.484 9.80822 11.3659 9.72115 11.2788C9.63408 11.1918 9.51598 11.1429 9.39285 11.1429ZM1.0357 3.71429H2.42856V12.5357C2.42856 12.6588 2.47748 12.7769 2.56455 12.864C2.65162 12.9511 2.76971 13 2.89285 13H3.82142C3.94455 13 4.06265 12.9511 4.14972 12.864C4.23679 12.7769 4.2857 12.6588 4.2857 12.5357V3.71429H5.67856C6.09091 3.71429 6.29896 3.21402 6.00675 2.92181L3.68532 0.136094C3.59826 0.0490891 3.48022 0.00021519 3.35713 0.00021519C3.23405 0.00021519 3.116 0.0490891 3.02894 0.136094L0.707512 2.92181C0.416173 3.21344 0.62278 3.71429 1.0357 3.71429ZM13.1071 3.71429H7.5357C7.41257 3.71429 7.29448 3.7632 7.20741 3.85027C7.12033 3.93734 7.07142 4.05544 7.07142 4.17857V5.10714C7.07142 5.23028 7.12033 5.34837 7.20741 5.43544C7.29448 5.52251 7.41257 5.57143 7.5357 5.57143H13.1071C13.2303 5.57143 13.3484 5.52251 13.4354 5.43544C13.5225 5.34837 13.5714 5.23028 13.5714 5.10714V4.17857C13.5714 4.05544 13.5225 3.93734 13.4354 3.85027C13.3484 3.7632 13.2303 3.71429 13.1071 3.71429ZM11.25 7.42857H7.5357C7.41257 7.42857 7.29448 7.47749 7.20741 7.56456C7.12033 7.65163 7.07142 7.76972 7.07142 7.89286V8.82143C7.07142 8.94456 7.12033 9.06266 7.20741 9.14973C7.29448 9.2368 7.41257 9.28571 7.5357 9.28571H11.25C11.3731 9.28571 11.4912 9.2368 11.5783 9.14973C11.6654 9.06266 11.7143 8.94456 11.7143 8.82143V7.89286C11.7143 7.76972 11.6654 7.65163 11.5783 7.56456C11.4912 7.47749 11.3731 7.42857 11.25 7.42857ZM14.9643 0H7.5357C7.41257 0 7.29448 0.0489157 7.20741 0.135986C7.12033 0.223057 7.07142 0.341149 7.07142 0.464286V1.39286C7.07142 1.51599 7.12033 1.63409 7.20741 1.72116C7.29448 1.80823 7.41257 1.85714 7.5357 1.85714H14.9643C15.0874 1.85714 15.2055 1.80823 15.2926 1.72116C15.3796 1.63409 15.4286 1.51599 15.4286 1.39286V0.464286C15.4286 0.341149 15.3796 0.223057 15.2926 0.135986C15.2055 0.0489157 15.0874 0 14.9643 0Z"
      fill={fillColor}
    />
  </Svg>
);

const TimingIcon = ({ fillColor = '#ffffff' }) => (
  <Svg
    width={16}
    height={15}
    viewBox="0 0 16 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M8 0C3.85685 0 0.5 3.35685 0.5 7.5C0.5 11.6431 3.85685 15 8 15C12.1431 15 15.5 11.6431 15.5 7.5C15.5 3.35685 12.1431 0 8 0ZM8 13.5484C4.65827 13.5484 1.95161 10.8417 1.95161 7.5C1.95161 4.15827 4.65827 1.45161 8 1.45161C11.3417 1.45161 14.0484 4.15827 14.0484 7.5C14.0484 10.8417 11.3417 13.5484 8 13.5484ZM9.86895 10.3911L7.30141 8.5252C7.20766 8.45564 7.15323 8.34677 7.15323 8.23186V3.26613C7.15323 3.06653 7.31653 2.90323 7.51613 2.90323H8.48387C8.68347 2.90323 8.84677 3.06653 8.84677 3.26613V7.55141L10.8669 9.02117C11.0302 9.13911 11.0635 9.36593 10.9456 9.52923L10.377 10.3125C10.2591 10.4728 10.0323 10.5091 9.86895 10.3911Z"
      fill={fillColor}
    />
  </Svg>
);

const MorningIcon = ({ fillColor }) => (
  <Svg
    width={20}
    height={21}
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M9.99306 13.9669C8.30174 13.9669 6.87049 15.1919 6.34028 16.8911C6.28705 17.0616 6.28604 17.711 6.86285 17.711C7.09795 17.711 7.29688 17.5409 7.37431 17.295C7.75521 16.0837 8.78646 15.2149 9.9934 15.2149C11.2045 15.2149 12.2326 16.0837 12.616 17.2954C12.6909 17.5321 12.8802 17.711 13.1392 17.711C13.5188 17.711 13.776 17.2907 13.651 16.8907C13.1181 15.1895 11.6875 13.9669 9.99306 13.9669ZM3.15972 17.3966C3.26274 17.5707 3.43656 17.6755 3.62292 17.6755C4.06701 17.6755 4.3316 17.1196 4.08542 16.7048L3.13368 15.1017L6.11979 14.429C6.34115 14.3803 6.51493 14.1901 6.55799 13.9415L7.15694 10.5872L9.69167 12.4837C9.8783 12.6251 10.1215 12.6251 10.308 12.4837L12.8427 10.5872L13.4417 13.9415C13.4807 14.1853 13.6543 14.3802 13.8799 14.429L16.866 15.1017L15.9149 16.7044C15.6685 17.1196 15.9334 17.6759 16.3778 17.6759C16.5636 17.6759 16.7372 17.5717 16.8403 17.3981L18.2549 15.0188C18.359 14.8482 18.3764 14.6239 18.3069 14.4337C18.2375 14.2434 18.0813 14.1022 17.899 14.0583L14.4615 13.288L13.7757 9.42667C13.741 9.2219 13.6151 9.05124 13.4415 8.96847C13.2679 8.8905 13.0727 8.90995 12.9207 9.02698L10.004 11.211L7.08733 9.02698C6.9375 8.94734 6.73611 8.9239 6.56597 9.00202C6.3967 9.08007 6.27083 9.25554 6.23177 9.46023L5.54861 13.3223L2.11111 14.0926C1.92882 14.1316 1.77691 14.273 1.70312 14.468C1.62934 14.663 1.65104 14.8825 1.75521 15.0532L3.15972 17.3966ZM19.4444 18.9551H0.555556C0.249965 18.9551 0 19.236 0 19.5794C0 19.9239 0.249965 20.2051 0.555556 20.2051H19.4444C19.75 20.2051 20 19.9243 20 19.5813C20 19.2364 19.75 18.9551 19.4444 18.9551ZM7.05903 5.02155L9.44444 2.33913V8.33015C9.44444 8.6739 9.69444 8.95515 10 8.95515C10.3056 8.95515 10.5556 8.67562 10.5556 8.33015V2.33913L12.9406 5.02233C13.0486 5.14265 13.191 5.20515 13.3333 5.20515C13.4757 5.20515 13.6176 5.14413 13.726 5.02202C13.9431 4.77788 13.9431 4.38218 13.726 4.13843L10.3927 0.388428C10.1757 0.144287 9.82396 0.144287 9.60729 0.388428L6.27396 4.13843C6.05694 4.38257 6.05694 4.77827 6.27396 5.02202C6.49097 5.26577 6.84375 5.26765 7.05903 5.02155Z"
      fill={fillColor}
    />
  </Svg>
);

const AfternoonIcon = ({ fillColor }) => (
  <Svg
    width={20}
    height={21}
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M10 4.58008C9.65625 4.58008 9.375 4.29883 9.375 3.95508V0.830078C9.375 0.484609 9.65625 0.205078 10 0.205078C10.3438 0.205078 10.625 0.484609 10.625 0.830078V3.95508C10.625 4.29883 10.3438 4.58008 10 4.58008ZM10.625 19.5801V16.4551C10.625 16.1096 10.3455 15.8301 10 15.8301C9.65453 15.8301 9.375 16.1113 9.375 16.4551V19.5801C9.375 19.9255 9.65453 20.2051 10 20.2051C10.3455 20.2051 10.625 19.9238 10.625 19.5801ZM20 10.2051C20 9.85961 19.7205 9.58008 19.375 9.58008H16.25C15.9045 9.58008 15.625 9.85961 15.625 10.2051C15.625 10.5505 15.9045 10.8301 16.25 10.8301H19.375C19.7188 10.8301 20 10.5488 20 10.2051ZM4.375 10.2051C4.375 9.85961 4.09547 9.58008 3.75 9.58008H0.625C0.279531 9.58008 0 9.86133 0 10.2051C0 10.5488 0.279531 10.8301 0.625 10.8301H3.75C4.09375 10.8301 4.375 10.5488 4.375 10.2051ZM14.8633 6.22852L17.0727 4.01797C17.3168 3.77383 17.3168 3.37812 17.0727 3.13437C16.8285 2.89062 16.4328 2.89023 16.1891 3.13437L13.9797 5.34492C13.7355 5.58906 13.7355 5.98438 13.9797 6.22852C14.1018 6.35059 14.2617 6.41176 14.4215 6.41176C14.5813 6.41176 14.7383 6.34961 14.8633 6.22852ZM3.81289 17.2754L6.02227 15.0648C6.26641 14.8207 6.26641 14.4254 6.02227 14.1813C5.77812 13.9371 5.38242 13.937 5.13867 14.1811L2.9293 16.3917C2.68516 16.6358 2.68516 17.0315 2.9293 17.2753C3.05137 17.3973 3.21129 17.4584 3.37109 17.4584C3.5309 17.4584 3.69062 17.4004 3.81289 17.2754ZM17.0703 17.2754C17.3145 17.0312 17.3145 16.6355 17.0703 16.3918L14.8609 14.1813C14.6168 13.9371 14.2211 13.9372 13.9773 14.1814C13.7336 14.4255 13.7332 14.8208 13.9773 15.065L16.1867 17.2755C16.3088 17.3976 16.4687 17.4586 16.6285 17.4586C16.7883 17.4586 16.9492 17.4004 17.0703 17.2754ZM6.02344 6.22852C6.26758 5.98438 6.26758 5.58906 6.02344 5.34492L3.81289 3.13359C3.56875 2.88945 3.17305 2.88945 2.9293 3.13359C2.68555 3.37773 2.68516 3.77344 2.9293 4.01719L5.13867 6.22773C5.26074 6.3498 5.42066 6.41086 5.58047 6.41086C5.74027 6.41086 5.89844 6.34961 6.02344 6.22852ZM14.375 10.2051C14.375 7.79297 12.4121 5.83008 10 5.83008C7.58789 5.83008 5.625 7.79492 5.625 10.2051C5.625 12.6152 7.58789 14.5801 10 14.5801C12.4121 14.5801 14.375 12.6191 14.375 10.2051ZM13.125 10.2051C13.125 11.9289 11.723 13.3301 10 13.3301C8.27695 13.3301 6.875 11.9277 6.875 10.2051C6.875 8.48242 8.27734 7.08008 10 7.08008C11.7227 7.08008 13.125 8.48242 13.125 10.2051Z"
      fill={fillColor}
    />
  </Svg>
);

const EveningIcon = ({ fillColor }) => (
  <Svg
    width={20}
    height={21}
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M3.15972 17.3965C3.26274 17.5707 3.43656 17.6754 3.62292 17.6754C4.06701 17.6754 4.3316 17.1196 4.08542 16.7047L3.13368 15.1016L6.11979 14.4289C6.34115 14.3802 6.51493 14.1901 6.55799 13.9414L7.15694 10.5871L9.69167 12.4836C9.8783 12.625 10.1215 12.625 10.308 12.4836L12.8427 10.5871L13.4417 13.9414C13.4807 14.1852 13.6543 14.3801 13.8799 14.4289L16.866 15.1016L15.9149 16.7043C15.6685 17.1196 15.9334 17.6758 16.3778 17.6758C16.5636 17.6758 16.7372 17.5716 16.8403 17.398L18.2549 15.0188C18.359 14.8482 18.3764 14.6238 18.3069 14.4336C18.2375 14.2434 18.0813 14.1021 17.899 14.0582L14.4615 13.2879L13.7757 9.4266C13.741 9.22184 13.6151 9.05117 13.4415 8.9684C13.2679 8.89043 13.0727 8.90988 12.9207 9.02691L10.004 11.2109L7.08733 9.02691C6.93542 8.91477 6.73559 8.89043 6.56649 8.9684C6.39722 9.04645 6.27135 9.22191 6.23229 9.4266L5.54861 13.3223L2.11111 14.0926C1.92882 14.1316 1.77691 14.2729 1.70312 14.4679C1.62934 14.6629 1.65104 14.8824 1.75521 15.0531L3.15972 17.3965ZM9.99306 13.9668C8.30174 13.9668 6.87049 15.1918 6.34028 16.891C6.28705 17.0615 6.28604 17.7109 6.86285 17.7109C7.09795 17.7109 7.29688 17.5408 7.37431 17.2949C7.75521 16.0836 8.78646 15.2148 9.9934 15.2148C11.2045 15.2148 12.2326 16.0836 12.616 17.2953C12.6909 17.5321 12.8802 17.7109 13.1392 17.7109C13.5188 17.7109 13.776 17.2906 13.651 16.8906C13.1181 15.1895 11.6875 13.9668 9.99306 13.9668ZM19.4444 18.9551H0.555556C0.249965 18.9551 0 19.2359 0 19.5793C0 19.9238 0.249965 20.2051 0.555556 20.2051H19.4444C19.75 20.2051 20 19.9243 20 19.5812C20 19.2363 19.75 18.9551 19.4444 18.9551ZM9.60764 8.77148C9.71528 8.89258 9.85764 8.95508 10 8.95508C10.1424 8.95508 10.2843 8.89406 10.3927 8.77195L13.726 5.02195C13.9431 4.77781 13.9431 4.38211 13.726 4.13836C13.509 3.89461 13.1573 3.89422 12.9406 4.13836L10.5556 6.82227V0.830078C10.5556 0.484609 10.3056 0.205078 10 0.205078C9.69444 0.205078 9.44444 0.484609 9.44444 0.830078V6.82227L7.05903 4.13867C6.84201 3.89453 6.49028 3.89453 6.27361 4.13867C6.05694 4.38281 6.0566 4.77852 6.27361 5.02227L9.60764 8.77148Z"
      fill={fillColor}
    />
  </Svg>
);

const NightIcon = ({ fillColor }) => (
  <Svg
    width={20}
    height={21}
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
     <Path
      d="M15.7305 13.5605C15.5114 13.1968 15.1023 13.0172 14.6855 13.093C13.0297 13.4128 11.3328 12.9782 10.0293 11.9016C8.70312 10.8066 7.94141 9.18555 7.94141 7.45898C7.94141 5.39844 9.04297 3.48242 10.816 2.4668C11.1853 2.25563 11.3777 1.84648 11.3063 1.42539C11.2355 1.00547 10.9199 0.683203 10.5012 0.605078C10.0586 0.519922 9.36328 0.455078 8.91797 0.455078C4.13672 0.455078 0.25 4.38086 0.25 9.20508C0.25 14.0293 4.13867 17.9551 8.91797 17.9551C11.5426 17.9551 13.9961 16.7734 15.6523 14.7129C15.918 14.377 15.9492 13.9277 15.7305 13.5605ZM8.91797 16.7051C4.82812 16.7051 1.5 13.3418 1.5 9.20508C1.5 5.06836 4.82773 1.70508 8.91797 1.70508C9.10902 1.70508 9.36836 1.72095 9.62461 1.74535C7.80078 3.04648 6.69141 5.17773 6.69141 7.45898C6.69141 9.56211 7.61719 11.5332 9.23242 12.8691C10.6484 14.0387 12.4422 14.5832 14.252 14.4195C12.8594 15.877 10.9453 16.7051 8.91797 16.7051Z"
      fill={fillColor}
    />
  </Svg>
);

export const IndicatorIcon = ()=> (
    <Svg
      width={8}
      height={8}
      viewBox="0 0 8 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Rect width={8} height={8} rx={4} fill="#1A7971" />
      <Path d="M2 4.442L3.167 5.5 6 2.5" stroke="#fff" strokeLinecap="square" />
    </Svg>
  );


const getTimingFillColor = (isActive) => {
  return isActive ? '#ffffff' : '#4A4A4A';
};

const getBarElementFillColor = (isActive) => {
  return isActive ? '#F4A200' : '#ffffff';
};
export const getFilterIcon = ({
  element,
  elementType,
  isActive,
}) => {
  const fillColor = elementType === 'barElements' ? getBarElementFillColor(isActive) : getTimingFillColor(isActive);
  switch (element) {
    case 'journey_class':
    case 'ac':
      return <ClassIcon fillColor={fillColor} />;
    case 'availability':
    case 'available':
      return <AvailabilityIcon fillColor={fillColor} />;
    case 'timing':
      return <TimingIcon fillColor={fillColor} />;
    case 'sort':
      return <SortIcon fillColor={fillColor} />;
    case 'filters':
      return <FilterIcon fillColor={fillColor} />;
    case 'night':
      return <NightIcon fillColor={fillColor} />;
    case 'morning':
      return <MorningIcon fillColor={fillColor} />;
    case 'afternoon':
      return <AfternoonIcon fillColor={fillColor} />;
    case 'evening':
      return <EveningIcon fillColor={fillColor} />;
    default:
      return null;
  }
};

AvailabilityIcon.propTypes = {
  fillColor: PropTypes.string,
};

ClassIcon.propTypes = {
  fillColor: PropTypes.string,
};

NightIcon.propTypes = {
  fillColor: PropTypes.string,
};

EveningIcon.propTypes = {
  fillColor: PropTypes.string,
};

AfternoonIcon.propTypes = {
  fillColor: PropTypes.string,
};

MorningIcon.propTypes = {
  fillColor: PropTypes.string,
};

TimingIcon.propTypes = {
  fillColor: PropTypes.string,
};

SortIcon.propTypes = {
  fillColor: PropTypes.string,
};

FilterIcon.propTypes = {
  fillColor: PropTypes.string,
};
