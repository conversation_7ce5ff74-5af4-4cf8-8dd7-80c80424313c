import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { _label } from '../../../../vernacular/AppLanguage';
import { fontStyle, getLineHeight } from '../../../../vernacular/VernacularUtils';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

const styles = StyleSheet.create({
  separator: {
    height: 1,
    backgroundColor: colors.white,
    marginBottom: 12,
    ...getPlatformElevation(1),
  },
  trainsFound: {
    backgroundColor: colors.mintGreen,
    paddingVertical: 4,
    alignItems: 'center',
  },
  trainsFoundText: {
    color: colors.green7,
    fontSize: 14,
    paddingVertical: 2,
  },
  noTrainsFound: {
    backgroundColor: colors.creamWhite,
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  noTrainsText: {
    fontSize: 14,
    paddingVertical: 2,
    color: colors.lightYello,
    textAlign: 'center',
  },
});

function BottomsheetMessage({ hasFilters, noTrainsFlag, foundTrainsCount }) {
  if (noTrainsFlag) {
    return (
      <View style={styles.noTrainsFound}>
        <Text style={[styles.noTrainsText, fontStyle('bold'), getLineHeight(14)]}>
          {_label('no_trains_found_for_the_selected_filters')}
        </Text>
      </View>
    );
  }

  if (hasFilters) {
    return (
      <View style={styles.trainsFound}>
        <Text style={styles.trainsFoundText}>
          {_label('count_trains_found', undefined, { count: foundTrainsCount })}
        </Text>
      </View>
    );
  }

  return <View style={styles.separator} />;
}

BottomsheetMessage.propTypes = {
  hasFilters: PropTypes.bool,
  noTrainsFlag: PropTypes.bool,
  foundTrainsCount: PropTypes.number,
};

export default BottomsheetMessage;
