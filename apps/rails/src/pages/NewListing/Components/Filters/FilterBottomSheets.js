import PropTypes from 'prop-types';
import React from 'react';
import { Dimensions, Image, ScrollView, StyleSheet, Text, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import isEmpty from 'lodash/isEmpty';
import { _label } from '../../../../vernacular/AppLanguage';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import { LargeBoxFilters, SmallBoxFiltersWithHeader } from '../SortAndFilterComponents';
import LinearGradient from 'react-native-linear-gradient';
import { fireOmnitureEventsNew } from '../../../RailsFilters/RailsFilterActions';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import BottomsheetMessage from './BottomsheetMessage';

import closeIcon from '@mmt/legacy-assets/src/lts_close_icon.webp';

const fpStyles = StyleSheet.create({
  separator: {
    height: 2,
    backgroundColor: colors.white,
    marginBottom: 12,
    ...getPlatformElevation(2),
  },
  wrapper: {
    height: Math.floor(Dimensions.get('screen').height * 0.7),
    width: '100%',
    backgroundColor: colors.white,
    alignSelf: 'flex-end',
    borderRadius: 4,
  },
  closeIconWrapper: {
    width: 40,
    height: 40,
    position: 'absolute',
    right: 5,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerIcon: {
    width: 10,
    height: 10,
  },
  headerTextWrapper: {
    paddingVertical: 12,
    flex: 1,
    alignItems: 'center',
  },
  flexRow: {
    flexDirection: 'row',
  },
  headerText: {
    color: colors.black,
    fontSize: 16,
    lineHeight: 20,
  },
  btnToolbar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 14,
    marginBottom: 4,
  },
  btnWrapper: {
    minWidth: '40%',
  },
  buttonBg: {
    borderRadius: 4,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 13,
    color: colors.white,
  },
  clearAll: {
    fontSize: 13,
    color: colors.azure,
    padding: 5,
    textAlign: 'center',
  },
  disabled: {
    fontSize: 12,
    color: colors.grey29,
    padding: 5,
    textAlign: 'center',
  },
});

const FiltersPage = ({
  labels,
  noTrainsFlag,
  journeyClassFilter,
  trainTypeFilter,
  departureTimeFilter,
  arrivalTimeFilter,
  fromStnFilter,
  toStnFilter,
  onFilterClick,
  ticketTypeFilter,
  availabilityFilter,
  onCloseClick,
  onDoneClick,
  onClearAllFiltersClicked,
  resetState,
  hasFilters,
  foundTrainsCount,
  version,
  id,
}) => {
  const onApplyPress = () => {
    if (noTrainsFlag) {
      return;
    }
    resetState();
    onDoneClick('filterSheet_apply');
  };
  return (
    <View style={fpStyles.wrapper}>
      <View style={fpStyles.flexRow}>
        <View style={fpStyles.headerTextWrapper}>
          <Text style={[fpStyles.headerText, fontStyle('bold')]}>{_label('Filters')}</Text>
        </View>
        <TouchableRipple onPress={() => {
          resetState();
          fireOmnitureEventsNew('Filter_Close');
          onCloseClick();
        }}>
          <View style={fpStyles.closeIconWrapper}>
            <Image source={closeIcon} style={fpStyles.headerIcon} />
          </View>
        </TouchableRipple>
      </View>
      <ScrollView style={{ flex: 1 }}>

      {!isEmpty(availabilityFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <SmallBoxFiltersWithHeader
              id={`${id}_availability_filter`}
              headerLabel={_label('Availability')}
              filterType={availabilityFilter}
              onFilterClick={onFilterClick}
              version={version}
            />
          </React.Fragment>
        )}

        {!isEmpty(ticketTypeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <SmallBoxFiltersWithHeader
              id={`${id}_ticket_type_filter`}
              headerLabel={_label('ticket_type_filter', { capitalize: true })}
              filterType={ticketTypeFilter}
              onFilterClick={onFilterClick}
              version={version}
            />
          </React.Fragment>
        )}

        {!isEmpty(fromStnFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <SmallBoxFiltersWithHeader
              id={`${id}_fromStnFilter`}
              filterType={fromStnFilter}
              onFilterClick={onFilterClick}
              headerLabel={labels.stations_in_origin_city}
              version={version}
            />
          </React.Fragment>
        )}

        {!isEmpty(toStnFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <SmallBoxFiltersWithHeader
              id={`${id}_toStnFilter`}
              headerLabel={labels.stations_in_destination_city}
              filterType={toStnFilter}
              onFilterClick={onFilterClick}
              version={version}
            />
          </React.Fragment>
        )}

        {!isEmpty(journeyClassFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <SmallBoxFiltersWithHeader
              id={`${id}_journeyClassFilter`}
              headerLabel={_label('journey_class_filter')}
              filterType={journeyClassFilter}
              onFilterClick={onFilterClick}
              version={version}
            />
          </React.Fragment>
        )}

        {!isEmpty(departureTimeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <LargeBoxFilters
              id={`${id}_departureTimeFilter`}
              labels={labels}
              headerLabel={labels.departure_from_city}
              filterType={departureTimeFilter}
              onFilterClick={onFilterClick}
              version={version}
            />
          </React.Fragment>
        )}

        {!isEmpty(arrivalTimeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <LargeBoxFilters
              id={`${id}_arrivalTimeFilter`}
              labels={labels}
              headerLabel={labels.arrival_in_city}
              filterType={arrivalTimeFilter}
              onFilterClick={onFilterClick}
              version={version}
            />
          </React.Fragment>
        )}

        {!isEmpty(trainTypeFilter?.filterTypesArray) && (
          <React.Fragment>
            <View style={fpStyles.separator} />
            <SmallBoxFiltersWithHeader
              id={`${id}_trainTypeFilter`}
              headerLabel={_label('train_types')}
              filterType={trainTypeFilter}
              onFilterClick={onFilterClick}
              version={version}
            />
          </React.Fragment>
        )}

      </ScrollView>

      <BottomsheetMessage
        noTrainsFlag={noTrainsFlag}
        hasFilters={hasFilters}
        foundTrainsCount={foundTrainsCount}
      />
      <View style={fpStyles.btnToolbar}>
        <TouchableRipple onPress={() => { onClearAllFiltersClicked(); onDoneClick('clear_filterPage'); }} disabled={!hasFilters}>
          <View style={fpStyles.btnWrapper}>
            <Text
              testID={`${id}_clear_filters`}
              style={[fpStyles.clearAll, fontStyle('bold'), !hasFilters && fpStyles.disabled]}
            >
              {_label('CLEAR')}
            </Text>
          </View>
        </TouchableRipple>

        <TouchableRipple onPress={onApplyPress} >
          <LinearGradient
            style={[fpStyles.buttonBg, fpStyles.btnWrapper]}
            colors={!noTrainsFlag ? ['#53B2FE', '#065AF3'] : ['#CAC9C9', '#CAC9C9']}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
          >
            <Text
              testID={`${id}_apply_filters`}
              style={[fpStyles.buttonText, fontStyle('bold'), noTrainsFlag && fpStyles.disabled]}
            >
              {_label('apply', { uppercase: true })}
            </Text>
          </LinearGradient>
        </TouchableRipple>
      </View>
    </View>
  );
};

FiltersPage.propTypes = {
  labels: PropTypes.shape({
    stations_in_origin_city: PropTypes.string,
    stations_in_destination_city: PropTypes.string,
    departure_from_city: PropTypes.string,
    arrival_in_city: PropTypes.string,
  }),
  noTrainsFlag: PropTypes.bool,
  journeyClassFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  trainTypeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  departureTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  arrivalTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  fromStnFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  toStnFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onFilterClick: PropTypes.func,
  ticketTypeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  availabilityFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  quotaFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onCloseClick: PropTypes.func,
  onDoneClick: PropTypes.func,
  onClearAllFiltersClicked: PropTypes.func,
  resetState: PropTypes.func,
  hasFilters: PropTypes.bool,
  foundTrainsCount: PropTypes.number,
  version: PropTypes.string,
  id: PropTypes.string,
};

export default FiltersPage;
