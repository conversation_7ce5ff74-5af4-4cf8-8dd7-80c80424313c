import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { fontStyle, getLineHeight } from '../../../../vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../../vernacular/AppLanguage';
/* eslint-disable */
import BottomsheetMessage from './BottomsheetMessage';
import { removeSpaceAndLowercase } from '../../../RailsFilters/RailsFilterActions';
import PropTypes from 'prop-types';

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    margin: 5,
    alignItems: 'center',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  btn: {
    fontSize: 12,
    color: colors.azure,
    paddingVertical: 10,
    paddingRight: 12,
    paddingLeft: 4,
    textAlign: 'center',
  },
  disabled: {
    fontSize: 12,
    color: colors.lightTextColor,
    paddingVertical: 10,
    paddingHorizontal: 20,
    textAlign: 'center',
  },
  heading: {
    fontSize: 14,
    color: colors.black,
    paddingLeft: 12,
  },
});

function BottomsheetHeader({
  heading,
  onDoneClick,
  onClearSelectedFilter,
  filter1,
  filter2,
  resetState,
  noTrainsFlag,
  foundTrainsCount,
  hasFilters,
}) {
  const onClearTap = () => {
    onClearSelectedFilter(filter1, `bottomSheet_${heading}_Clear`);
    if (filter2) {
      onClearSelectedFilter(filter2, `bottomSheet_${heading}_Clear`);
    }
  };

  const onDonePress = () => {
    resetState();
    onDoneClick(`bottomSheet_${removeSpaceAndLowercase(heading)}_Done`);
  };
  return (
    <>
      <View style={styles.wrapper}>
        <View style={{ flex: 2 }}>
          <Text style={[fontStyle('bold'), getLineHeight(14), styles.heading]}>{heading}</Text>
        </View>
        <View style={{ flex: 2, flexDirection: 'row', justifyContent: 'flex-end' }}>
          <TouchableOpacity disabled={!hasFilters} onPress={onClearTap}>
            <Text style={[styles.btn, fontStyle('bold'), !hasFilters && styles.disabled]}>
              {_label('clear', { uppercase: true })}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={onDonePress} disabled={noTrainsFlag}>
            <Text
              style={
                !noTrainsFlag
                  ? [styles.btn, fontStyle('bold')]
                  : [styles.disabled, fontStyle('regular')]
              }
            >
              {_label('done', { uppercase: true })}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      <BottomsheetMessage
        hasFilters={hasFilters}
        noTrainsFlag={noTrainsFlag}
        foundTrainsCount={foundTrainsCount}
      />
    </>
  );
}

BottomsheetHeader.propTypes = {
  heading: PropTypes.string,
  onDoneClick: PropTypes.func,
  onClearSelectedFilter: PropTypes.func,
  filter1: PropTypes.object,
  filter2: PropTypes.object,
  resetState: PropTypes.func,
  noTrainsFlag: PropTypes.bool,
  foundTrainsCount: PropTypes.number,
  hasFilters: PropTypes.bool,
};

export default BottomsheetHeader;
