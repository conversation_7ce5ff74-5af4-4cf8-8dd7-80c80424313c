import PropTypes from 'prop-types';
import React, { useMemo } from 'react';
import {Image, ScrollView, StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import isEmpty from 'lodash/isEmpty';
import isUndefined from 'lodash/isUndefined';
import { _label } from '../../../../vernacular/AppLanguage';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';
import { getRailsLDAndSSQuotaEnabled } from 'apps/rails/src/RailsAbConfig';
import { QUOTA_FILTER_KEY } from '../../../RailsFilters/railsFilter.utils';
import { handleNewQuotaClickEvent } from '../../../RailsFilters/RailsFilterActions';

import morningIcon from '@mmt/legacy-assets/src/ic_morning.webp';
import afterNoonIcon from '@mmt/legacy-assets/src/ic_afternoon.webp';
import eveningIcon from '@mmt/legacy-assets/src/ic_evening.webp';
import nightIcon from '@mmt/legacy-assets/src/ic_night.webp';

export const FilterCard = ({
  heading,
  selected,
  subHeading = '',
  noOfTrains = undefined,
  onClick,
  id,
}) => {
  const cardStyle = selected ? filterCardStyle.cardSelected : filterCardStyle.cardDeSelected;
  const textStyle = selected ? [filterCardStyle.textSelected, fontStyle('bold'), getLineHeight(12)] : [filterCardStyle.textDeSelected, fontStyle('regular'), getLineHeight(12)];
  const subTextStyle = [ fontStyle('bold'), getLineHeight(12),
    selected ?
    filterCardStyle.subTextSelected :
    filterCardStyle.subTextDeSelected];
  let headingText;
  if (isEmpty(subHeading)) {
    if (!isUndefined(noOfTrains)) {
      headingText = `${heading} (${noOfTrains})`;
    } else {
      headingText = heading;
    }
  } else if (!isUndefined(noOfTrains)) {
    headingText = ` - ${heading} (${noOfTrains})`;
  } else {
    headingText = ` - ${heading}`;
  }
  return (
    <TouchableRipple onPress={onClick}>
      <View>
        <Card style={cardStyle} showBorder>
          <Text style={textStyle} testID={id}>
            {
              !isEmpty(subHeading) &&
              <Text style={subTextStyle}>
                {subHeading}
              </Text>
            }
            {headingText}
          </Text>
        </Card>
      </View>
    </TouchableRipple>
  );
};
export const FilterCardOnListing = ({
  heading, selected, onClick,
}) => {
  const textStyle = selected ? {
    ...filterCardStyle.textSelected,
    ...fontStyle('bold'),
    ...getLineHeight(12),
  } : {
    ...filterCardStyle.textSelected,
    ...fontStyle('regular'),
    ...getLineHeight(12),
  };
  return (
    <TouchableRipple onPress={onClick}>
      <View style={filterCardStyle.cardStyle}>
        {selected && <View style={filterCardStyle.circle} />}
        <Text style={textStyle}>
          {heading}
        </Text>
      </View>
    </TouchableRipple>
  );
};

FilterCard.propTypes = {
  heading: PropTypes.string.isRequired,
  noOfTrains: PropTypes.number,
  onClick: PropTypes.func.isRequired,
  selected: PropTypes.bool.isRequired,
  subHeading: PropTypes.string,
  id: PropTypes.string,
};

FilterCardOnListing.propTypes = {
  heading: PropTypes.string,
  selected: PropTypes.bool,
  subHeading: PropTypes.string,
  noOfTrains: PropTypes.number,
  onClick: PropTypes.func,
};

const filterCardStyle = {
  cardSelected: {
    marginHorizontal: 4,
    backgroundColor: colors.azure,
    marginVertical: 8,
    paddingHorizontal: 10,
    paddingVertical: 12,
    minWidth: 60,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardDeSelected: {
    marginHorizontal: 4,
    marginVertical: 8,
    paddingHorizontal: 10,
    paddingVertical: 12,
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
  },
  circle: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.easternBlue,
    position: 'absolute',
    top: 5,
  },
  cardStyle: {
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textSelected: {
    fontSize: 12,
    color: colors.white,
    textAlign: 'center',
    paddingHorizontal: 10,
    marginVertical: 5,
    lineHeight: 20,
    maxWidth: 140, // @tamil check english/hindi version
  },
  textDeSelected: {fontSize: 12, color: colors.defaultTextColor},
  subTextSelected: {fontSize: 12,  color: colors.white},
  subTextDeSelected: {fontSize: 12,  color: colors.defaultTextColor},
};

const TimeFilterCard = ({
  label, imageIcon, onClick, filterObject, noOfTrains, id,
}) => {
  const {selected} = filterObject;
  const titleStyle = [largeBoxStyles.title, fontStyle('bold'), getLineHeight(12)];
  const subTitleStyle = [largeBoxStyles.subTitle, fontStyle('medium'), getLineHeight(10)];
  const imageStyle = [largeBoxStyles.image];
  if (selected) {
    titleStyle.push(largeBoxStyles.activeTitle);
    subTitleStyle.push(largeBoxStyles.activeTitle);
    imageStyle.push(largeBoxStyles.activeImage);
  }
  return (
    <TouchableWithoutFeedback onPress={onClick}>
      <View style={{ flex: 1 }} testID={`${id}_${label}`}>
        <Card
          showBorder
          style={{
            paddingVertical: 10, alignItems: 'center', marginHorizontal: 0, marginRight: 8, borderRadius: 6,
          }}
        >
          <Image source={imageIcon} style={imageStyle} />
          <Text style={titleStyle}>{`${label || filterObject.heading} (${noOfTrains})`}</Text>
          <Text style={subTitleStyle}>{_label(filterObject.subHeading)}</Text>
        </Card>
      </View>
    </TouchableWithoutFeedback>
  );
};

TimeFilterCard.propTypes = {
  label: PropTypes.string,
  imageIcon: PropTypes.any.isRequired,
  onClick: PropTypes.func.isRequired,
  filterObject: PropTypes.shape({
    selected: PropTypes.bool,
    heading: PropTypes.string,
    subHeading: PropTypes.string,
  }),
  noOfTrains: PropTypes.number.isRequired,
  id: PropTypes.string,
};

const largeBoxStyles = StyleSheet.create({
  title: {
    fontSize: 12,
    marginHorizontal: 4,
    textAlign: 'center',
    color: colors.defaultTextColor,
  },
  activeTitle: {
    color: colors.azure,
  },
  subTitle: {
    fontSize: 10,
    lineHeight: 14,
    paddingVertical: 4,
    marginHorizontal: 8,
    textAlign: 'center',
    color: colors.darkGrey2,
  },
  image: {
    width: 32,
    height: 32,
  },
  activeImage: {
    tintColor: colors.azure,
  },
});

const LargeBoxFilters = ({ filterType: filterObject, onFilterClick, headerLabel, id }) => {
  const {
    nightFilter, morningFilter, afternoonFilter, eveningFilter,
  } = filterObject.filterTypes;
  return (
    <View style={{ paddingHorizontal: 16, paddingVertical: 20 }}>
      <Text style={{
        fontSize: 22,  color: colors.black, marginBottom: 12,
      }}
      >
        {headerLabel || filterObject.heading}
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ flex: 1 }}>
        <TimeFilterCard
          id={id}
          imageIcon={nightIcon}
          label={_label('night')}
          filterObject={nightFilter}
          onClick={() => { onFilterClick('nightFilter', filterObject); }}
          noOfTrains={nightFilter.noOfTrains}
        />
        <TimeFilterCard
          id={id}
          imageIcon={morningIcon}
          label={_label('morning')}
          filterObject={morningFilter}
          onClick={() => { onFilterClick('morningFilter', filterObject); }}
          noOfTrains={morningFilter.noOfTrains}
        />
        <TimeFilterCard
          id={id}
          imageIcon={afterNoonIcon}
          label={_label('afternoon')}
          filterObject={afternoonFilter}
          onClick={() => { onFilterClick('afternoonFilter', filterObject); }}
          noOfTrains={afternoonFilter.noOfTrains}
        />
        <TimeFilterCard
          id={id}
          label={_label('evening')}
          imageIcon={eveningIcon}
          filterObject={eveningFilter}
          onClick={() => { onFilterClick('eveningFilter', filterObject); }}
          noOfTrains={eveningFilter.noOfTrains}
        />
      </ScrollView>
    </View>
  );
};

LargeBoxFilters.propTypes = {
  filterType: PropTypes.shape({
    heading: PropTypes.string,
    filterTypes: PropTypes.shape({
      nightFilter: PropTypes.object,
      morningFilter: PropTypes.object,
      afternoonFilter: PropTypes.object,
      eveningFilter: PropTypes.object,
    }),
  }),
  onFilterClick: PropTypes.func.isRequired,
  headerLabel: PropTypes.string,
  id: PropTypes.string,
};

const SmallBoxFilters = ({ filterType: filterObject = {}, onFilterClick, headerLabel, id }) => {
  const filterObjectArray = filterObject.filterTypesArray || [];
  return (
    <View style={{ paddingHorizontal: 16, paddingVertical: 20 }}>
      <Text style={{
        fontSize: 22,  color: colors.black, marginBottom: 12,
      }}
      >
        {headerLabel || filterObject.heading}
      </Text>
      <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
        {
          filterObjectArray.map((item, index) => {
            return (
              <View style={{ marginRight: 10 }} key={filterObject.filterTypes[item].key}>
                <FilterCard
                  id={`${id}_${index}`}
                  heading={filterObject.filterTypes[item].heading}
                  subHeading={filterObject.filterTypes[item].subHeading}
                  noOfTrains={filterObject.filterTypes[item].noOfTrains}
                  selected={filterObject.filterTypes[item].selected}
                  onClick={() => { onFilterClick(item, filterObject); }}
                />
              </View>
            );
          })
        }
      </View>
    </View>
  );
};

SmallBoxFilters.propTypes = {
  filterType: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.string),
    heading: PropTypes.string,
    filterTypes: PropTypes.object,
  }),
  onFilterClick: PropTypes.func.isRequired,
  headerLabel: PropTypes.string,
  id: PropTypes.string,
};

const NoTrainsAlert = () => (
  <View style={{backgroundColor: colors.creamWhite, paddingHorizontal: 8, paddingVertical: 8}}>
    <Text style={{fontSize: 12,  color: colors.lightYello}}>
      { _label('too_many_filters') }
    </Text>
  </View>
);

const FiltersPage = ({
  labels,
  noTrainsFlag,
  quickFilter,
  journeyClassFilter,
  trainTypeFilter,
  departureTimeFilter,
  arrivalTimeFilter,
  fromStnFilter,
  toStnFilter,
  onQuickFilterClick,
  onFilterClick,
  ticketTypeFilter,
  availabilityFilter,
  quotaFilter,
  id,
}) => {
  const ldAndSsQuotaEnabled = getRailsLDAndSSQuotaEnabled();
  const newQuickFilter = useMemo(() => {
    try {
      const modifiedQuickFilter = { ...quickFilter };
      if (ldAndSsQuotaEnabled) {
        modifiedQuickFilter.filterTypesArray = modifiedQuickFilter?.filterTypesArray?.filter(
          (item) => item !== QUOTA_FILTER_KEY,
        );
      }
      return modifiedQuickFilter;
    } catch (err) {
      return quickFilter;
    }
  }, [quickFilter]);

  const handleNewQuotaClick = (filterItem, filterObject) => {
    // If Quota filter selected is false, tapping it will select it and then trigger the click event for the filter
    if (filterObject?.filterTypes?.[filterItem]?.selected === false) {
      handleNewQuotaClickEvent(filterItem);
    }
    onFilterClick(filterItem, filterObject);
  };

  return (<View style={{ flex: 1 }}>
    {noTrainsFlag && <NoTrainsAlert />}
    <ScrollView style={{ flex: 1 }}>
      {!isEmpty(newQuickFilter?.filterTypesArray) && (
        <SmallBoxFilters
          id={`${id}_quick_filters`}
          headerLabel={_label('quick_filters')}
          filterType={newQuickFilter}
          onFilterClick={onQuickFilterClick}
        />
      )}

      {ldAndSsQuotaEnabled && !isEmpty(quotaFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_quota_filter`} />
          <SmallBoxFilters
            id={`${id}_quota_filter`}
            headerLabel={_label('Quota')}
            filterType={quotaFilter}
            onFilterClick={handleNewQuotaClick}
          />
        </React.Fragment>
      )}

      {!isEmpty(ticketTypeFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_ticket_type_filter`}/>
          <SmallBoxFilters
            id={`${id}_ticket_type_filter`}
            headerLabel={_label('ticket_type_filter')}
            filterType={ticketTypeFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}
{/** Remove the following two filters, this was done for dev purpose only */}
      {!isEmpty(availabilityFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_availability_filter`}/>
          <SmallBoxFilters
            id={`${id}_availability_filter`}
            headerLabel={_label('Availability')}
            filterType={availabilityFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}

      {!ldAndSsQuotaEnabled && !isEmpty(quotaFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_quota_filter`}/>
          <SmallBoxFilters
            id={`${id}_quota_filter`}
            headerLabel={_label('Quota')}
            filterType={quotaFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}

      {!isEmpty(journeyClassFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_journey_class_filter`}/>
          <SmallBoxFilters
            id={`${id}_journey_class_filter`}
            headerLabel={_label('journey_class_filter')}
            filterType={journeyClassFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}

      {!isEmpty(trainTypeFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_train_types`}/>
          <SmallBoxFilters
            id={`${id}_train_types`}
            headerLabel={_label('train_types')}
            filterType={trainTypeFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}

      {!isEmpty(departureTimeFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_departureTimeFilter`}/>
          <LargeBoxFilters
            id={`${id}_departureTimeFilter`}
            labels={labels}
            headerLabel={labels.departure_from_city}
            filterType={departureTimeFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}

      {!isEmpty(arrivalTimeFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_arrivalTimeFilter`}/>
          <LargeBoxFilters
            id={`${id}_arrivalTimeFilter`}
            labels={labels}
            headerLabel={labels.arrival_in_city}
            filterType={arrivalTimeFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}

      {!isEmpty(fromStnFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_fromStnFilter`}/>
          <SmallBoxFilters
            id={`${id}_fromStnFilter`}
            filterType={fromStnFilter}
            onFilterClick={onFilterClick}
            headerLabel={labels.stations_in_origin_city}
          />
        </React.Fragment>
      )}

      {!isEmpty(toStnFilter?.filterTypesArray) && (
        <React.Fragment>
          <View style={styles.separator} testID={`${id}_toStnFilter`}/>
          <SmallBoxFilters
            id={`${id}_toStnFilter`}
            headerLabel={labels.stations_in_destination_city}
            filterType={toStnFilter}
            onFilterClick={onFilterClick}
          />
        </React.Fragment>
      )}
    </ScrollView>
  </View>
  );
};

FiltersPage.propTypes = {
  labels: PropTypes.shape({
    departure_from_city: PropTypes.string,
    arrival_in_city: PropTypes.string,
    stations_in_origin_city: PropTypes.string,
    stations_in_destination_city: PropTypes.string,
  }).isRequired,
  noTrainsFlag: PropTypes.bool,
  quickFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.string),
  }),
  journeyClassFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  trainTypeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  departureTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  arrivalTimeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  fromStnFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  toStnFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  onQuickFilterClick: PropTypes.func,
  onFilterClick: PropTypes.func,
  ticketTypeFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  availabilityFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  quotaFilter: PropTypes.shape({
    filterTypesArray: PropTypes.arrayOf(PropTypes.object),
  }),
  id: PropTypes.string,
};

const styles = {
  separator: {
    height: 2,
    backgroundColor: colors.white,
    marginBottom: 12,
    ...getPlatformElevation(2),
  },
};

export default FiltersPage;
