import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Image,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import noCabs from '@mmt/legacy-assets/src/no_cabs.webp';
import { colors, gradient } from '@mmt/legacy-commons/Styles/globalStyles';
import { MAX_DAYS, EXPLORE_OPTIONS, RETRY_ACTION, GO_BACK } from '../../../Utils/RailsConstant';
import { getAdsCard } from '@mmt/legacy-commons/adsConfig/adsCard';
import { RAIL_NO_TRAINS } from '@mmt/legacy-commons/adsConfig/adsConfigConstants';
import { isPhonePePlatform } from '../../../Utils/RisUtils';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import {getAllTypesAdsAb} from '../../Common/utils';
import { trackAdLoad } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import ConnectedTravelWidget from '../Components/ConnectedTravel/ConnectedTravelWidget';
import ConnectedTravelWidgetV2 from './ConnectedTravelV2/ConnectedTravelWidgetV2';
import { getRailsTbsErrorHandlingFlowV1 } from 'apps/rails/src/RailsAbConfig';
import LinearGradient from 'react-native-linear-gradient';
import ASSETS from '../../../Utils/Assets/RailsAssets';
import isEmpty from 'lodash/isEmpty';
import Calendar from '../../Calendar/DatePicker';
import { today } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { connectedTravelVariants } from '@mmt/rails/src/RailsAbConstants';

const NoTrainsView = ({
  errorMessage,
  errorSubtitle,
  departureDate,
  changeDate,
  connectedTravelApiResponseJson,
  showLoaderForRailsConnectedTravel,
  showCtV2 = connectedTravelVariants.SHOWN_EXPANDED_CARD,
  errorFlow,
  retrySearchData,
  handleRetryButtonTap,
}) => {
  // trackPageVisits('mob_rail_listing_v2_noTrainsFound_noTrainsOnOtherDates', {});
  const [showDiffTypeOfAds, setShowDiffTypeOfAds] = useState({multi_banner: 0, snackbar: 0, adfeed:0,interstitial: 0});
  useEffect(()=>{
    getAdABConfig();
  }, []);

  const getAdABConfig = async () => {
    const AdsAb = await getAllTypesAdsAb();
    setShowDiffTypeOfAds(AdsAb);
    trackAdLoad('mob_rail_listing_v2_noTrainsFound_noTrainsOnOtherDates',AdsAb.trackingPayload);
  };
  const showAdds = !showLoaderForRailsConnectedTravel && connectedTravelApiResponseJson === null;

  const isTbsErrorHandlingFlowV1 = useMemo(() => {
    return getRailsTbsErrorHandlingFlowV1() && !isEmpty(errorFlow);
  }, [errorFlow]);

  return (
    <View testID={'id'}>
      <View
        elevation={1}
        style={styles.datePicker}
      >
        <Calendar
          title="Calendar"
          minDate={today()}
          maxDays={MAX_DAYS + 1}
          selectedDate={departureDate}
          changeDate={changeDate}
        />
        <View style={{ borderBottomColor: colors.white, borderBottomWidth: 10 }} />
      </View>
      <ScrollView>
        {isTbsErrorHandlingFlowV1 ? (
          <View style={styles.newErrorFlowContainer} testID="no_trains_view_error_flow_container">
            <Image
              source={ASSETS.warningExclamationImage}
              width={40}
              height={40}
              resizeMode="contain"
            />
            <Text style={[styles.newErrorFlowTitle, fontStyle('bold')]}>{errorFlow?.title}</Text>
            <Text style={[styles.newErrorFlowSubtitle, fontStyle('regular')]}>
              {errorFlow?.subTitle}
            </Text>
            {errorFlow?.ctas?.map((cta) => (
              <TouchableOpacity
                key={'cta-button'}
                onPress={() => handleRetryButtonTap(cta?.action)}
                style={styles.newErrorFlowButton}
              >
                <LinearGradient
                  colors={gradient.lightBlue}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.newErrorFlowButtonGradient}
                >
                  <Text style={[styles.newErrorFlowButtonText, fontStyle('bold')]}>
                    {cta?.action === RETRY_ACTION
                      ? retrySearchData?.triesLeft > 0
                        ? cta?.text
                        : GO_BACK
                      : cta?.text}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.noTrainsContainer} testID="no_trains_view_container">
            <Image style={styles.noTrainsImage} source={noCabs} resizeMode="contain" />
            <Text style={[styles.noTrainTitle, fontStyle('bold'), getLineHeight(16)]}>
              {errorMessage}
            </Text>
            {showAdds && (
              <Text style={[styles.noTrainsSubtitle, fontStyle('regular'), getLineHeight(14)]}>
                {errorSubtitle}
              </Text>
            )}
            {showLoaderForRailsConnectedTravel && (
              <Text style={[styles.noTrainsSubtitle, fontStyle('regular'), getLineHeight(14)]}>
                {EXPLORE_OPTIONS}
              </Text>
            )}
            {showLoaderForRailsConnectedTravel && (
              <Spinner size={30} color={colors.black} />
            )}
          </View>
        )}
        {showAdds && (
          <View style={{ margin: 16, alignSelf: 'center' }}>
            {!isPhonePePlatform() &&
              !!showDiffTypeOfAds.multi_banner &&
              getAdsCard(Platform.OS, RAIL_NO_TRAINS)}
          </View>
        )}
        {!showLoaderForRailsConnectedTravel &&
          (showCtV2 === connectedTravelVariants.SHOWN_EXPANDED_CARD ||
            showCtV2 === connectedTravelVariants.MOVED_DIRECT_LISTING) &&
          connectedTravelApiResponseJson && (
          <ConnectedTravelWidget
              showCtV2={showCtV2}
            errorView={true}
            connectedTravelData={connectedTravelApiResponseJson}
            />
        )}
        <View style={styles.connectedTravelContainer}>
          {!showLoaderForRailsConnectedTravel &&
            showCtV2 === connectedTravelVariants.INTRODUCING_CONNECTED_TRAVEL &&
            connectedTravelApiResponseJson && (
          <ConnectedTravelWidgetV2 connectedTravelData={connectedTravelApiResponseJson} />
        )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  noTrainsContainer: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noTrainsImage: {
    height: 180,
    marginVertical: 16,
  },
  noTrainTitle: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 32,
    fontSize: 16,
    lineHeight: 32,
    color: colors.black,
  },
  noTrainsSubtitle: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 32,
    fontSize: 14,
    lineHeight: 21,
    color: colors.defaultTextColor,
  },
  newErrorFlowContainer: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 200,
  },
  newErrorFlowTitle: {
    textAlign: 'center',
    marginTop: 12,
    fontSize: 16,
    lineHeight: 19.2,
    color: colors.black,
  },
  newErrorFlowSubtitle: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    lineHeight: 16.8,
    color: colors.textGrey,
  },
  newErrorFlowButton: {
    width: 120,
    height: 32,
    marginTop: 18,
  },
  newErrorFlowButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  newErrorFlowButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: 900,
    textTransform: 'uppercase',
  },
  datePicker: {
    width: '100%',
    borderBottomWidth: 2,
    borderColor: colors.datePickerColor,
    paddingLeft: 10,
    paddingRight: 15,
  },
  connectedTravelContainer: {
    flex: 1,
    padding: 15,
    backgroundColor: colors.white,
  },
});

NoTrainsView.propTypes = {
  retry: PropTypes.func.isRequired,
  errorMessage: PropTypes.string.isRequired,
  errorSubtitle: PropTypes.string,
  noCrossSell: PropTypes.bool,
  departureDate: PropTypes.instanceOf(Date),
  changeDate: PropTypes.func,
  connectedTravelApiResponseJson: PropTypes.object,
  showLoaderForRailsConnectedTravel: PropTypes.bool,
  showCtV2: PropTypes.bool,
  errorFlow: PropTypes.shape({
    title: PropTypes.string,
    subTitle: PropTypes.string,
    ctas: PropTypes.arrayOf(
      PropTypes.shape({
        action: PropTypes.string,
        text: PropTypes.string,
      }),
    ),
  }),
  retrySearchData: PropTypes.shape({
    triesLeft: PropTypes.number,
  }),
  handleRetryButtonTap: PropTypes.func,
};

export default NoTrainsView;
