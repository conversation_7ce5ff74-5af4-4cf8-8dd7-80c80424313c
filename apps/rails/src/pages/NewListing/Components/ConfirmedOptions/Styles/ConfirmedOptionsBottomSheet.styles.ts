import { StyleSheet } from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { railsConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConfig';
import { ConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConstants';

const showConfirmedOptionsV2 =
  railsConfirmOptionsV2() === ConfirmOptionsV2.SHOWN ||
  railsConfirmOptionsV2() === ConfirmOptionsV2.UX_V2;
const hideConfirmedOptionsV2 = ( railsConfirmOptionsV2() === ConfirmOptionsV2.NOT_SHOWN);

export const styles = StyleSheet.create({
    container: {
        height: 433,
        backgroundColor: colors.white,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        overflow: 'hidden',
    },
    headingContainer: {
        height: 78,
        backgroundColor: colors.lightGreen18,
        display: 'flex',
        flexDirection: 'column',
    },
    cnfContainer: {
        height: 392,
        backgroundColor: colors.white,
    },
    cnfText: {
        position: 'relative',
        left: 20,
        top:20,
        color: colors.greyText1,
        fontSize: 20,
    },
    crossButton: {
        position:'absolute',
        width: 45,
        height: 45,
        right: 0,
        alignItems:'center',
        justifyContent: 'center',
    },
    trainDetails: {
        left: 20,
        top: 27,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
    },
    cnfDesc: {
        position: 'relative',
        top: 16,
        left:'4.5%',
        width: '91%',
        color: hideConfirmedOptionsV2 ? colors.textGrey : colors.black,
    },
    trainName: {
        position: 'relative',
        color: colors.textGrey,
        width: '80%',
    },
    trainNumber: {
        position: 'relative',
        left: 8,
        color: colors.textGrey,
        marginLeft: 8,
        marginRight: 8,
    },
    dot: {
        position: 'relative',
        width: 6,
        height: 6,
        left: 8,
        borderRadius: 3,
        overflow: 'hidden',
        backgroundColor: colors.defaultTextColor,
    },
    boardingContainer: {
        position: 'relative',
        height: 118,
        width: '82%',
        left: '9%',
        top: 37,
        borderWidth: 1,
        borderRadius: 20,
        borderColor: colors.lightGrey,
    },
    boardTxt: {
        position: 'absolute',
        left: '14%',
        top: 16,
        color: colors.black,
    },
    blackText: {
        color: colors.black,
    },
    getDownTxt: {
        position: 'absolute',
        left: '6.8%',
        top: 72,
        color: colors.black,
    },
    boardStnBox: {
        position: 'absolute',
        left: '48%',
        top: 16,
        width: '50%',
        height: 40,
    },
    boardStn: {
        color: colors.black,
        width: '100%',
    },
    boardStnDate: {
        color: colors.lightTextColor,
        width: '100%',
    },
    dropStnBox: {
        position: 'absolute',
        left: '48%',
        top: 72,
        width: '50%',
        height: 40,
    },
    dropStn: {
        color: colors.black,
        width: '100%',
    },
    dropStnDate: {
        color: colors.lightTextColor,
        width: '100%',
    },
    fareTxtContainer: {
        position: 'absolute',
        width: '91%',
        bottom: 66,
        left: '4.45%',
        backgroundColor: showConfirmedOptionsV2 ? colors.smokeWhite : colors.white,
        borderRadius: showConfirmedOptionsV2 ? 10 : 0,
    },
    fareTxt: {
        color: colors.textGrey,
        paddingVertical:8,
        paddingHorizontal: 12,
    },
    imgContainer: {
        position: 'absolute',
        left: '40%',
        top: 72,
        width: 14,
        height: 178,
    },
    bookNowBtn: {
        position: 'absolute',
        left: '45%',
        bottom: 16,
        width: '50%',
        height: 40,
        justifyContent:'center',
        alignItems: 'center',
    },
    bookNow: {
        width: '100%',
        height: '100%',
        borderRadius: 4,
        justifyContent:'center',
        alignItems: 'center',
        overflow: 'hidden',
    },
    bookNowTxt: {
        color: colors.white,
    },
    tktDetails: {
        position: 'absolute',
        left: '4.45%',
        bottom: 42,
        height: 18,
        width: '32%',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
    },
    coachTxt: {
        color: colors.black,
    },
    priceTxt: {
        marginLeft: 16,
        color: colors.black,
    },
    availableTxt: {
        position: 'absolute',
        left: '4.45%',
        bottom: 16,
        color: hideConfirmedOptionsV2 ? colors.lightGreen4 : colors.green,
        width: '50%',
    },
    originStn: {
        position: 'relative',
        top: 36,
        left: '18%',
        color: colors.textGrey,
    },
    originStnName: {
        position: 'relative',
        top: 17,
        width: '40%',
        left: '48%',
        color: colors.textGrey,
    },
    destinationStn: {
        position: 'relative',
        top: 45,
        left: '18%',
        color: colors.textGrey,
    },
    destinationStnName: {
        position: 'relative',
        top: 29,
        left: '48%',
        color: colors.textGrey,
        width: '40%',
    },
    cnfOptionIcon: {
        left: 20,
        top: 20,
        width: 20,
        height: 20,
        marginRight: 6,
  },
  cnfOptionIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    },
});
