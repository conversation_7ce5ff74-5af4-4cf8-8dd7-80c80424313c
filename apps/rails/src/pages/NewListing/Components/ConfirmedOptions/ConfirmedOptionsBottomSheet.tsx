import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { styles } from './Styles/ConfirmedOptionsBottomSheet.styles';
import LinearGradient from 'react-native-linear-gradient';
import { CrossButton } from './Assets/ConfirmedOptionsSvgComponents';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import {
  closeConfirmOptionsBottomSheet,
  availabilityCardClicked,
  ALTERNATE_LISTING_IDENTIFIER,
  onAvailabilityDateClickedV2,
} from '../../RailsListingActions';
import {
    getBottomSheetHeight,
    getCnfContainerHeight,
    getConfirmedOptionsVersion,
    getImagePosition,
    GetImagebasedOnStations,
    getBoardingContainerPosition,
    V1,
    V2,
    V3,
    getLabels,
    getDestStnPos,
    getDestStnNamePos,
    getStationCodesAndNamesFromTrainData,
    getTrimmedStationName,
    getAvailabilityItem,
    getDataForConfirmedOptions,
    getFormattedDateandTime,
    getFormattedStnName,
} from './ConfirmedOptionsUtils';
import {
  removeEventFromEvar99Variable,
  trackGenericEvar99Event,
} from '@mmt/rails/src/railsAnalytics';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import { LISTING_PAGE_TRACKING_KEY } from '@mmt/rails/src/Utils/RailsConstant';
import { RAILS_ROUTE_KEYS } from '@mmt/rails/src/navigation/railsPageKeys';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';

import confirmedOptionsIcon from '@mmt/legacy-assets/src/confirmed_options_icon.webp';
import PropTypes from 'prop-types';
export interface selectedTrainInfoProps {
    frmStnName : string,
    frmStnCode : string,
    toStnName : string,
    toStnCode : string,
    trainNumber : string
}
export interface  ConfirmedOptionsBottomSheetProps {
    selectedTrainInfo: selectedTrainInfoProps,
    selectedClassType: string,
    selectedQuota: string,
    alternateAvailabilityResponse: {alternateAvailabilityList : []},
    trainsList: object,
    goToTravelersPage: (classType: string, info: object, quota : string)=> void,
    isNearbyConfirmedOption: boolean,
  goToTravelersFromNearbyDates: unknown;
}

const ConfirmedOptionsBottomSheet = ( props : ConfirmedOptionsBottomSheetProps) => {

    const { isNearbyConfirmedOption } = props;
    const [cnfVersion,setCnfVersion] = useState(V1);
    const { selectedTrainInfo,selectedClassType, selectedQuota, alternateAvailabilityResponse } = props;
    const { frmStnCode: originStnCode,
            toStnCode: destinationStnCode,
            frmStnName,
    toStnName,
  } = selectedTrainInfo;
    const originStnName = getFormattedStnName(frmStnName);
    const destinationStnName = getFormattedStnName(toStnName);
    const { boardStnCode, boardStnName, dropStnCode, dropStnName } = getStationCodesAndNamesFromTrainData(props);
    const labels = getLabels({originStnName,destinationStnName,boardStnName,dropStnName, cnfVersion});
  const alternateAvailabilityList = isNearbyConfirmedOption
    ? alternateAvailabilityResponse?.nearbyDatesAlternateAvlList
    : alternateAvailabilityResponse?.alternateAvailabilityList;
    const classType = selectedClassType.split('-')[0];
    const trainData = alternateAvailabilityList.filter( item => item.trainNumber === selectedTrainInfo.trainNumber)[0];
    const availabilityItem = getAvailabilityItem(selectedTrainInfo.trainNumber,
      classType,selectedQuota, alternateAvailabilityList);
    const ticketFare = availabilityItem?.tbsAvailability?.totalFare;
    const availabilityStatus = availabilityItem?.tbsAvailability?.prettyPrintingAvailablityStatus
      || availabilityItem?.tbsAvailability?.availablityStatus;
    const {
        formattedDepartureDate,
        formattedDepartureTime,
        formattedArrivalDate,
        formattedArrivalTime,
        formattedArrivalDayName,
        formattedDepartureDayName,
    } = getFormattedDateandTime({ trainData, isNearbyConfirmedOption });
    const arrivalDateTime = `${formattedArrivalDayName}, ${formattedArrivalDate}, ${formattedArrivalTime}`;
    const departureDateTime = `${formattedDepartureDayName}, ${formattedDepartureDate}, ${formattedDepartureTime}`;

    useEffect(()=>{
        const currVersion = getConfirmedOptionsVersion({originStnCode, boardStnCode, dropStnCode, destinationStnCode});
        setCnfVersion(currVersion);
        return () => props.onBackClick();
    },[]);

  const { startNextScreenTimer } = useScreenProfiler();

  const navigateToTravellersPage = () => {
    const { trainsList, goToTravelersPage, goToTravelersFromNearbyDates } = props;
    startNextScreenTimer(RAILS_ROUTE_KEYS.travelers, Date.now());
    const selectedClassType = availabilityItem?.tbsAvailability?.classType;
    const selectedQuota = availabilityItem?.tbsAvailability?.quota;
    const pageName = LISTING_PAGE_TRACKING_KEY;
    props.onBackClick();
    if (isNearbyConfirmedOption) {
      const data = availabilityItem?.tbsAvailability;
      goToTravelersFromNearbyDates(data, selectedTrainInfo, selectedClassType, selectedQuota, true);
    } else {
      const updatedInfo = getDataForConfirmedOptions({ availabilityItem, trainData, trainsList });
      goToTravelersPage(
        `${selectedClassType}-${ALTERNATE_LISTING_IDENTIFIER}`,
        updatedInfo,
        selectedQuota,
      );
    }
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_BOOK_NOW);
    trackGenericEvar99Event(pageName, RAIL_EVENTS.LISTING.RAILS_CONFIRMED_OPTIONS_BOOK_NOW);
    };


    return (
    <View
      style={[styles.container, getBottomSheetHeight(cnfVersion)]}
      testID="confirmed_options_bottomsheet_container"
    >
      <View style={styles.headingContainer} testID="confirmed_options_header">
        <View style={styles.cnfOptionIconContainer} testID="confirmed_options_icon_container">
          <Image
            source={confirmedOptionsIcon}
            style={styles.cnfOptionIcon}
            testID="confirmed_options_icon"
          />
          <Text numberOfLines={2} style={[styles.cnfText, fontStyle('regular'), getLineHeight(20)]}>
            {labels.confirmed_option}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.crossButton}
          onPress={props.onBackClick}
          testID="confirmed_options_close_button"
        >
          <CrossButton />
        </TouchableOpacity>
        <View style={styles.trainDetails}>
          <Text
            numberOfLines={1}
            style={[styles.trainName, getLineHeight(16), fontStyle('semiBold')]}
          >{`#${trainData.trainNumber}  ${trainData.trainName}`}</Text>
        </View>
      </View>
      <View style={[styles.cnfContainer, getCnfContainerHeight(cnfVersion)]}>
        <Text numberOfLines={2} style={[styles.cnfDesc, fontStyle('regular'), getLineHeight(15)]}>
          <Text>{labels.book_from}</Text>
          <Text style={fontStyle('black')}>{` ${getTrimmedStationName(originStnName)}`}</Text>
          <Text>{` ${labels.to}`}</Text>
          <Text style={fontStyle('black')}>{` ${getTrimmedStationName(destinationStnName)}`}</Text>
          <Text>{`. ${labels.board_at}`}</Text>
          <Text style={fontStyle('black')}>{` ${getTrimmedStationName(boardStnName)}`}</Text>
          <Text>{` ${labels.and} ${labels.lower_get_down_at}`}</Text>
          <Text style={fontStyle('black')}>{` ${dropStnName}`}</Text>
        </Text>
        {(cnfVersion === V1 || cnfVersion === V2) && (
          <View testID="confirmed_options_origin_section">
            <Text
              style={[styles.originStn, getLineHeight(16)]}
              testID="confirmed_options_origin_label"
            >
              {labels.book_from}
            </Text>
            <Text
              numberOfLines={1}
              style={[styles.originStnName, getLineHeight(16)]}
              testID="confirmed_options_origin_name"
            >
              {originStnName}
            </Text>
          </View>
        )}
        <View style={[styles.boardingContainer, getBoardingContainerPosition(cnfVersion)]}>
          <Text style={[styles.boardTxt, fontStyle('black'), getLineHeight(16)]}>
            {labels.board_at}
          </Text>
          <Text style={[styles.getDownTxt, fontStyle('black'), getLineHeight(16)]}>
            {labels.get_down_at}
          </Text>
          <View style={styles.boardStnBox}>
            <Text
              numberOfLines={1}
              style={[styles.boardStn, fontStyle('black'), getLineHeight(16)]}
            >
              {boardStnName}
            </Text>
            <Text
              numberOfLines={1}
              style={[styles.boardStnDate, fontStyle('regular'), getLineHeight(16)]}
            >
              {departureDateTime}
            </Text>
          </View>
          <View style={styles.dropStnBox}>
            <Text numberOfLines={1} style={[styles.dropStn, fontStyle('black'), getLineHeight(16)]}>
              {dropStnName}
            </Text>
            <Text
              numberOfLines={1}
              style={[styles.dropStnDate, fontStyle('regular'), getLineHeight(16)]}
            >
              {arrivalDateTime}
            </Text>
          </View>
        </View>
        {(cnfVersion === V1 || cnfVersion === V3) && (
          <View testID="confirmed_options_destination_section">
            <Text
              style={[styles.destinationStn, getLineHeight(16), getDestStnPos(cnfVersion)]}
              testID="confirmed_options_destination_label"
            >
              {labels.book_upto}
            </Text>
            <Text
              numberOfLines={1}
              style={[styles.destinationStnName, getLineHeight(16), getDestStnNamePos(cnfVersion)]}
            >
              {destinationStnName}
            </Text>
          </View>
        )}
        <View style={styles.fareTxtContainer}>
          <Text numberOfLines={2} style={[styles.fareTxt, fontStyle('regular'), getLineHeight(14)]}>
            {labels.cnf_fare_desc}
          </Text>
        </View>
        <View
          style={[styles.imgContainer, getImagePosition(cnfVersion)]}
          testID="confirmed_options_image_container"
        >
          {<GetImagebasedOnStations cnfVersion={cnfVersion} />}
        </View>
        <View style={styles.tktDetails}>
          <Text style={[styles.coachTxt, fontStyle('black'), getLineHeight(18)]}>{classType}</Text>
          <View style={styles.dot} />
          <Text
            style={[styles.priceTxt, fontStyle('black'), getLineHeight(18)]}
          >{`₹${ticketFare}`}</Text>
        </View>
        <TouchableOpacity
          style={styles.bookNowBtn}
          onPress={navigateToTravellersPage}
          testID="confirmed_options_book_now_button"
        >
          <LinearGradient
            colors={[colors.lightBlue, colors.darkBlue]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.bookNow}
          >
            <Text style={[styles.bookNowTxt, fontStyle('black'), getLineHeight(18)]}>
              {labels.book_now}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
        <Text style={[styles.availableTxt, fontStyle('black'), getLineHeight(18)]}>
          {availabilityStatus}
        </Text>
      </View>
    </View>
  );
};

ConfirmedOptionsBottomSheet.propTypes = {
  onBackClick: PropTypes.func,
};

const mapStateToProps = (state) => {
  const {
    railsListing: {
      selectedTrainInfo,
      selectedClassType,
      selectedQuota,
      alternateAvailabilityResponse,
      trainsList,
      isNearbyConfirmedOption,
      nearbyDatesAlternateAvlData,
      },
    } = state;
    const {
      frmStnCode,
      toStnCode,
      toStnName,
      frmStnName,
      boardingStation,
      droppingStation,
    } = selectedTrainInfo || {};
    return {
      selectedTrainInfo,
      selectedClassType,
      selectedQuota,
      frmStnCode,
      toStnCode,
      frmStnName,
      toStnName,
      boardingStation,
      droppingStation,
      alternateAvailabilityResponse,
      trainsList,
      isNearbyConfirmedOption,
      nearbyDatesAlternateAvlData,
    };
};

const mapDispatchToProps = dispatch => ({
  onBackClick: () => {
    dispatch(closeConfirmOptionsBottomSheet());
  },
  goToTravelersPage: (className, trainInfo, quotaCode) => {
    dispatch(availabilityCardClicked(className, trainInfo, quotaCode, true));
  },
  goToTravelersFromNearbyDates: (
    confirmedOptionData,
    trainData,
    selectedClassType,
    selectedQuota,
  ) => {
    dispatch(
      onAvailabilityDateClickedV2(
        confirmedOptionData,
        trainData,
        selectedClassType,
        selectedQuota,
        true,
      ),
    );
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(ConfirmedOptionsBottomSheet);
