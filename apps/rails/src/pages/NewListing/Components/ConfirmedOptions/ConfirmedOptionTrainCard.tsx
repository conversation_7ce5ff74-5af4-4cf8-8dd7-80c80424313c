import React from 'react';
import {connect} from 'react-redux';
import { FlatList, View, StyleSheet, Text, TouchableWithoutFeedback } from 'react-native';
import {
  trackAlternateStationsPageEvent,
} from '../../../../railsAnalytics';
import AlternateTrainItem from '../AlternateTrainCard';
import {availabilityCardClicked, ALTERNATE_LISTING_IDENTIFIER} from '../../RailsListingActions';
import { getDataForConfirmedOptions } from './ConfirmedOptionsUtils';
import { trackConfirmedOptionsClick } from 'apps/rails/src/railsAnalytics';
import { colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from '@mmt/rails/src/vernacular/VernacularUtils';
import { railsConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConfig';
import { ConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConstants';

interface ConfirmedOptionsTrainCardProps {
  trainsList: object,
  selectedTrainInfo: object,
  onAvailabilityCardClicked: (classType: string, info: object, quota : string)=> void,
  alternateAvailabilityResponse: { alternateAvailabilityList : []},
  toggleTrainScheduleBottomSheet: (
    visible: boolean,
    trainNumber: string,
    isCnfOptionTrain: boolean,
  ) => void;
  disableNearbydates?: boolean;
}

class ConfirmedOptionsTrainCard extends React.Component<ConfirmedOptionsTrainCardProps> {

  availabilityCardClicked = (availabilityItem, trainData) => {
    trackAlternateStationsPageEvent('mob_rail_alternate_stations_card_clicked');
    trackConfirmedOptionsClick();
    const {trainsList, onAvailabilityCardClicked} = this.props;
    const updatedInfo = getDataForConfirmedOptions({availabilityItem, trainData, trainsList});
    onAvailabilityCardClicked(
      `${availabilityItem.tbsAvailability.classType}-${ALTERNATE_LISTING_IDENTIFIER}`,
      updatedInfo,
      availabilityItem.tbsAvailability.quota,
    );
  };

  render() {
    const {
      alternateAvailabilityResponse: {alternateAvailabilityList},
    } = this.props;

    return (
      <View style={{}} testID="confirmed_options_train_card_container">
        <View style={{ flex: 1 }} testID="confirmed_options_train_card_view">
          <View style={{ flex: 1 }} testID="confirmed_options_train_card_flat_list_view">
              <FlatList
              data={alternateAvailabilityList}
              renderItem={this._renderTrainListItem}
              keyExtractor={this._keyExtractor}
              />
          </View>
        </View>
      </View>
    );
  }

  _keyExtractor = item => item.trainNumber;

  _renderTrainListItem = ({item}) => {
    const showConfirmedOptionsV2 =
      railsConfirmOptionsV2() === ConfirmOptionsV2.SHOWN ||
      railsConfirmOptionsV2() === ConfirmOptionsV2.UX_V2;
    const trainComponent = (
      <AlternateTrainItem
        id="confirmed_options_train_card"
        trainData={item}
        key={item.trainNumber}
        onAvailabilityCardClicked={this.availabilityCardClicked}
      />
    );
    if (this.props.disableNearbydates) {
      return null;
    }
    return (
      <View>
        <View style={styles.seperator} />
        {trainComponent}
        {showConfirmedOptionsV2 && (
        <TouchableWithoutFeedback onPress={() => {
              this.props?.toggleTrainScheduleBottomSheet(true, item?.trainNumber, true);
        }}>
          <View style={styles.trainScheduleTextContainer}>
            <Text style={[{...fontStyle('bold'), ...getLineHeight(14.4)}, styles.trainScheduleText]}>
              {_label('see_train_schedule')}
            </Text>
          </View>
        </TouchableWithoutFeedback>
        )}
      </View>
    );
  };
}

const mapStateToProps = (state) => {
  const {
    railsListing: {
      trainsList,
      selectedTrainInfo,
    },
  } = state;
  return {trainsList,selectedTrainInfo};
};


const mapDispatchToProps = dispatch => ({
  onAvailabilityCardClicked: (classType, trainInfo, quotaCode) =>
    dispatch(availabilityCardClicked(classType, trainInfo, quotaCode)),
});

const styles = StyleSheet.create({
   seperator: {
    backgroundColor: colors.grayBg,
    height: 10,
  },
  trainScheduleTextContainer: {
    marginLeft: 'auto',
    alignSelf: 'center',
    marginRight: 16,
    marginBottom: 16,
  },
  trainScheduleText: {
    color: colors.azure,
    fontSize: 12,
    fontweight: 900,
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(ConfirmedOptionsTrainCard);
