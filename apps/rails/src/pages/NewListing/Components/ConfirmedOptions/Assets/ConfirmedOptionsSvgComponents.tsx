import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';
import { railsConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConfig';
import { ConfirmOptionsV2 } from "@mmt/rails/src/RailsAbConstants";
import { Dimensions } from 'react-native';

const hideConfirmedOptionsV2 = ( railsConfirmOptionsV2() === ConfirmOptionsV2.NOT_SHOWN);
const fillColor = hideConfirmedOptionsV2 ? colors.lightGreen4 : colors.green;
const fillColorV3 = colors.lightGreen4;

export const LongerRouteAtBothEnds = (props: unknown) => {
  return (
    <Svg
      width={14}
      height={178}
      viewBox="0 0 14 178"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: [{ translateY: 4 }] }}
      {...props}
    >
      <Circle cx={7} cy={5} r={5} fill="#D9D9D9" />
      <Circle cx={7} cy={61} r={7} fill={fillColor} />
      <Circle cx={7} cy={118} r={7} fill={fillColor} />
      <Circle cx={7} cy={173} r={5} fill="#D9D9D9" />
      <Path stroke={fillColor} strokeWidth={2} d="M7 65L7 114" />
      <Path
        stroke="#D9D9D9"
        strokeWidth={2}
        strokeDasharray="4 4"
        d="M7 10L7 54"
      />
      <Path
        stroke="#D9D9D9"
        strokeWidth={2}
        strokeDasharray="4 4"
        d="M7 125L7 168"
      />
      <Path
        d="M4 61l2 2.5 4-4M4 117.5l2 2.5 4-4"
        stroke="#fff"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const LongerRouteAtBothEndsHorizontal = (props) => {
  const { width } = Dimensions.get('screen');
  return (
    <Svg
      width={width}
      height="100%"
      viewBox={`0 0 ${width} 20`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="none"
      {...props}
    >
      <Circle cx={0.08 * width} cy={7} r={5} fill="#D9D9D9" />
      <Circle cx={0.33 * width} cy={8} r={6} fill={fillColorV3} />
      <Circle cx={0.58 * width} cy={8} r={6} fill={fillColorV3} />
      <Circle cx={0.83 * width} cy={7} r={5} fill="#D9D9D9" />
      <Path stroke={fillColorV3} strokeWidth={2} d={`M${0.325 * width} 7L${0.575 * width} 7`} />
      <Path
        stroke="#D9D9D9"
        strokeWidth={1.5}
        strokeDasharray="4 4"
        d={`M${0.071 * width} 7L${0.315 * width} 7`}
      />
      <Path
        stroke="#D9D9D9"
        strokeWidth={1.5}
        strokeDasharray="4 4"
        d={`M${0.595 * width} 7L${0.824 * width} 7`}
      />
      <Path
        d={`M${0.323 * width} 7.5l2 1.5 3-3 M${0.573 * width} 7.5l2 1.5 3-3`}
        stroke="#fff"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};
export const LongerRouteAtSource = (props) => {
  return (
    <Svg
      width={14}
      height={125}
      viewBox="0 0 14 125"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: [{ translateY: 4 }] }}
      {...props}
    >
      <Circle cx={7} cy={5} r={5} fill="#D9D9D9" />
      <Circle cx={7} cy={61} r={7} fill={fillColor} />
      <Circle cx={7} cy={118} r={7} fill={fillColor} />
      <Path stroke={fillColor} strokeWidth={2} d="M7 65L7 114" />
      <Path
        stroke="#D9D9D9"
        strokeWidth={2}
        strokeDasharray="4 4"
        d="M7 10L7 54"
      />
      <Path
        d="M4 61l2 2.5 4-4M4 117.5l2 2.5 4-4"
        stroke="#fff"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};
export const LongerRouteAtSourceHorizontal = (props) => {
  const { width } = Dimensions.get('screen');
  return (
    <Svg
      width={width}
      height="100%"
      viewBox={`0 0 ${width} 20`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="none"
      {...props}
    >
      <Circle cx={0.08 * width} cy={7} r={5} fill="#D9D9D9" />
      <Circle cx={0.45 * width} cy={8} r={6} fill={fillColorV3} />
      <Circle cx={0.82 * width} cy={8} r={6} fill={fillColorV3} />
      <Path
        stroke="#D9D9D9"
        strokeWidth="1"
        strokeDasharray="4 4"
        d={`M${0.088 * width} 8 L${0.438 * width} 8`}
      />
      <Path stroke={fillColorV3} strokeWidth="2" d={`M${0.445 * width} 8 L${0.815 * width} 8`} />
      <Path
        d={`M${0.443 * width} 7.5l2 1.5 3-3 M${0.814 * width} 7.5l2 1.5 3-3`}
        stroke="#fff"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const LongerRouteAtDestination = (props) => {
  return (
    <Svg
      width={14}
      height={124}
      viewBox="0 0 14 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: [{ translateY: 4 }] }}
      {...props}
    >
      <Circle cx={7} cy={7} r={7} fill={fillColor} />
      <Circle cx={7} cy={64} r={7} fill={fillColor} />
      <Circle cx={7} cy={119} r={5} fill="#D9D9D9" />
      <Path stroke={fillColor} strokeWidth={2} d="M7 11L7 60" />
      <Path
        stroke="#D9D9D9"
        strokeWidth={2}
        strokeDasharray="4 4"
        d="M7 71L7 114"
      />
      <Path
        d="M4 7l2 2.5 4-4M4 63.5L6 66l4-4"
        stroke="#fff"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  )
};

export const LongerRouteAtDestinationHorizontal = (props) => {
  const { width } = Dimensions.get('screen');
  return (
    <Svg
      width={width}
      height="100%"
      viewBox={`0 0 ${width} 20`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="none"
      {...props}
    >
      <Circle cx={0.08 * width} cy={8} r={6} fill={fillColorV3} />
      <Circle cx={0.45 * width} cy={8} r={6} fill={fillColorV3} />
      <Circle cx={0.82 * width} cy={7} r={5} fill="#D9D9D9" />
      <Path stroke={fillColorV3} strokeWidth={2} d={`M${0.075 * width} 7L${0.445 * width} 7`} />
      <Path
        stroke="#D9D9D9"
        strokeWidth={1.5}
        strokeDasharray="4 4"
        d={`M${0.465 * width} 7L${0.805 * width} 7`}
      />
      <Path
        d={`M${0.075 * width} 7.5l2 1.5 3-3 M${0.4438 * width} 7.5l2 1.5 3-3`}
        stroke="#fff"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const CrossButton = (props) => {
  return (
    <Svg
      width={13}
      height={13}
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M1 1l11 11m0-11L1 12"
        stroke="#9B9B9B"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};
