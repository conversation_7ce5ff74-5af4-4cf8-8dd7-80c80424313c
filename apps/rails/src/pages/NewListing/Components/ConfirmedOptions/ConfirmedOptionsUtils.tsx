import React from 'react';
import {
  LongerRouteAtBothEnds,
  LongerRouteAtSource,
  LongerRouteAtDestination,
  LongerRouteAtBothEndsHorizontal,
  LongerRouteAtSourceHorizontal,
  LongerRouteAtDestinationHorizontal,
} from './Assets/ConfirmedOptionsSvgComponents';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import fecha from 'fecha';
import {diffMinutes, getDateObjectFromDateAndTime} from '@mmt/legacy-commons/Helpers/dateHelpers';
import { getArrivalDate, getArrivalTime, getDepartureDate, getDepartureTime} from '../../readers/newTrainInfo.reader';
import { getFormattedDates} from '../../Utils/RailListingUtils';
import { getDaysNamesShort } from '../../../../vernacular/calendarUtil';
import railsConfig from '../../../../RailsConfig';
import fetch2 from '../../../../fetch2';
import { API_POST_METHOD } from '../../../CommonCalendar/Utils/CalenderUtils';
import { YEAR_MONTH_DATE_FORMAT } from 'apps/rails/src/Utils/RailsConstant';
import PropTypes from 'prop-types';


export const V1 = 'v1';
export const V2 = 'v2';
export const V3 = 'v3';

export const GetImagebasedOnStations = ({cnfVersion}) => {
  switch (cnfVersion) {
    case V1:
      return <LongerRouteAtBothEnds />;
    case V2:
      return <LongerRouteAtSource />;
    case V3:
      return <LongerRouteAtDestination />;
    default:
      return null;
  }
};

GetImagebasedOnStations.propTypes = {
  cnfVersion: PropTypes.string,
};

export const GetImagebasedOnStationsHorizontal = ({ cnfVersion }) => {
  switch (cnfVersion) {
    case V1:
      return <LongerRouteAtBothEndsHorizontal />;
    case V2:
      return <LongerRouteAtSourceHorizontal />;
    case V3:
      return <LongerRouteAtDestinationHorizontal />;
    default:
      return null;
  }
};

GetImagebasedOnStationsHorizontal.propTypes = {
  cnfVersion: PropTypes.string,
};

export const getBottomSheetHeight = (cnfVersion: string) => {
    switch (cnfVersion) {
        case V1 : return { height : 470};
        case V2 : return { height : 444};
        case V3 : return { height : 433};
    }
};
export const getCnfContainerHeight = (cnfVersion: string) => {
    switch (cnfVersion) {
        case V1 : return { height : 392};
        case V2 : return { height : 366};
        case V3 : return { height : 355};
    }
};
export const getConfirmedOptionsVersion = ({
    originStnCode, boardStnCode, dropStnCode, destinationStnCode,
}) => {
    if (originStnCode !== boardStnCode) {
        return (dropStnCode !== destinationStnCode) ? V1 : V2;
    } else {
        return V3;
    }
};
export const getImagePosition = (cnfVersion: string) => {
    if (cnfVersion === V3) {
        return { top: 98 };
    }
};
export const getBoardingContainerPosition = (cnfVersion: string) => {
    if (cnfVersion === V3) {
        return { top: 48 };
    }
};
export const getDestStnPos = (cnfVersion: string) => {
    if (cnfVersion === V3) {
        return { top: 57 };
    }
};
export const getDestStnNamePos = (cnfVersion :string) => {
    if (cnfVersion === V3) {
        return { top: 40 };
    }
};

export const getTrimmedStationName = (stationName:string, cap = 12) => {
    return stationName.length > cap ? `${stationName.substring(0,cap)}...` : stationName;
};
export const getAlteredStationName = (str: string, cap = 15) => {
    const capitalizedStr = str.toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    return capitalizedStr.length > cap ? `${capitalizedStr.substring(0, cap)}...` : capitalizedStr;
};


export const getStationCodesAndNamesFromTrainData = ( train = {} ) => {
    const frmStnCode = train.boardingStation ? train.boardingStation.code : train.frmStnCode;
    const frmStnName = train.boardingStation ? train.boardingStation.name : train.frmStnName;
    const toStnCode = train.droppingStation ? train.droppingStation.code : train.toStnCode;
    const toStnName = train.droppingStation ? train.droppingStation.name : train.toStnName;

    return {
        boardStnCode : frmStnCode,
        boardStnName : frmStnName,
        dropStnCode : toStnCode,
        dropStnName : toStnName,
    };
};

export const getAvailabilityItem = ( trainNumber: string,classType: string,quota: string,availabilityList: object) => {
    const availabilityItem = availabilityList.find((avl) => avl?.trainNumber === trainNumber);
    const alternateClassAvlList = availabilityItem.classAvailabilityList;
    const alternateClassAvlItem = alternateClassAvlList.find((acvi) => {
        const acviClassName = acvi?.tbsAvailability?.className || acvi?.tbsAvailability?.classType;
        return (acviClassName === classType && acvi?.tbsAvailability?.quota === quota?.code);
    });
    return alternateClassAvlItem;
};


export const getLabels = ({ originStnName, destinationStnName, boardStnName, dropStnName, cnfVersion}) => {
    let origin = originStnName,destination = destinationStnName;
    if (cnfVersion === V2) {
        destination = dropStnName;
    }
    if (cnfVersion === V3) {
        origin = boardStnName;
    }
    return ({
    confirmed_option : _label('confirmed_option'),
    cnf_options_desc : _label('cnf_options_desc', undefined ,{
        originStnName: origin,
        destinationStnName: destination,
        boardStnName,
        dropStnName,
    }),
    book_from : _label('book_from'),
    board_at :  _label('board_at'),
    get_down_at : _label('get_down_at'),
    lower_get_down_at : _label('get_down_at',{lowercase: true}),
    book_upto : _label('book_upto'),
    cnf_fare_desc : _label('cnf_fare_desc', undefined ,{
        originStnName,
        destinationStnName,
    }),
    available : _label('available'),
    book_now : _label('book_now', { uppercase: true}),
    and : _label('and'),
    to : _label('to',{lowercase: true}),
});
};

export const getDayNameFromDate = (dateStr: string) => {
    const date = dateStr.split('-').reverse().join('-');
    const dateIndex = new Date(date).getDay();
    return getDaysNamesShort()[dateIndex];
};

export const getFormattedDateandTime = ({ trainData, isNearbyConfirmedOption }) => {
    const formattedDepartureTime = fecha.format(fecha.parse(getDepartureTime(trainData), 'HH:mm'), 'h:mm A');
    const formattedArrivalTime = fecha.format(fecha.parse(getArrivalTime(trainData), 'HH:mm'), 'h:mm A');
    const {formattedDepartureDate, formattedArrivalDate} = getFormattedDates(trainData);
    const formattedDepartureDayName = isNearbyConfirmedOption
        ? getDayNameFromDate(trainData.boardingDate)
        : getDayNameFromDate(getDepartureDate(trainData));
    const formattedArrivalDayName = isNearbyConfirmedOption
        ? getDayNameFromDate(trainData.droppingDate)
        : getDayNameFromDate(getArrivalDate(trainData));
    return {
        formattedDepartureTime,
        formattedDepartureDate: isNearbyConfirmedOption ? trainData.formattedBoardingDate : formattedDepartureDate,
        formattedArrivalDate: isNearbyConfirmedOption ? trainData.formattedDroppingDate : formattedArrivalDate,
        formattedArrivalTime,
        formattedDepartureDayName,
        formattedArrivalDayName,
    };
};

export const getFormattedStnName = (stnName:string) => {
    let stnNameFormatted = stnName.replace(/\s+/g,' ').trim();
    try {
    stnNameFormatted = stnNameFormatted.split(' ')
                  .map((item) => {
                    return item[0].toUpperCase() + item.substring(1).toLowerCase();
                  }).join(' ');
    }
    catch (e) {
        return stnName;
    }
    return stnNameFormatted;
};

export const getDataForConfirmedOptions = ( {availabilityItem, trainData, trainsList} ) => {

    const trainInfo = trainsList.find(train => train.trainNumber === trainData.trainNumber);
    const {
      frmStnCode,
      frmStnName,
      toStnCode,
      toStnName,
      departureTime,
      arrivalTime,
      departureDate,
      arrivalDate,
      distance,
    } = availabilityItem;
    const departureDateAndTime = getDateObjectFromDateAndTime(
      departureDate,
      'DD-MM-YYYY',
      departureTime,
      'HH:mm',
    );
    const arrivalDateAndTime = getDateObjectFromDateAndTime(
      arrivalDate,
      'DD-MM-YYYY',
      arrivalTime,
      'HH:mm',
    );
    const duration = diffMinutes(arrivalDateAndTime, departureDateAndTime);
    const boardingStation = {code: trainData.frmStnCode, name: trainData.frmStnName};
    const droppingStation = {code: trainData.toStnCode, name: trainData.toStnName};
    return {
      ...trainInfo,
      frmStnCode,
      frmStnName,
      toStnCode,
      toStnName,
      departureDateAndTime,
      arrivalDateAndTime,
      arrivalTime,
      departureTime,
      distance,
      duration,
      boardingStation,
      droppingStation,
    };
};

export const getAlternateAvailabilityResponse = async (
  originStationCode: string,
  destinationStationCode: string,
  departureDate: Date,
) => {
  const response = await fetch2(railsConfig.alternateAvailabilityUrl, {
    method: API_POST_METHOD,
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      source: originStationCode,
      destination: destinationStationCode,
      doj: fecha.format(departureDate, YEAR_MONTH_DATE_FORMAT),
    }),
  });
  const responseJson = await response.json();
  return {
    data: responseJson,
    status: response.status,
  };
};
