import React, { useEffect, useState } from 'react';
import { Image, TouchableOpacity, View, Text } from 'react-native';
import { styles } from './AvailDepletionBottomSheet.styles';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

interface AvailDepletionBottomSheetProps {
  changeDisplayAvailDepletionBottomSheet: (val: boolean) => void;
  logTravellerPageBottomSheetEvents?: (eventValue: unknown) => void;
}

export const AvailDepletionBottomSheet = ({
  changeDisplayAvailDepletionBottomSheet = () => {
    //
  },
  logTravellerPageBottomSheetEvents = () => {
    //
  },
}: AvailDepletionBottomSheetProps) => {
  const [availDepletionConfig, setAvailDepletionConfig] = useState({});
  useEffect(() => {
    async function initializeAvailDepletionConfig() {
      const config = await getConfigStore(configKeys.RAILS_AVAIL_DEPLETION);
      setAvailDepletionConfig(config || {});
    }
    initializeAvailDepletionConfig();
    logTravellerPageBottomSheetEvents?.(TRAVELLER_CLICK_EVENTS.AVAIL_DEPLETION_BOTTOM_SHEET);
  }, []);

  return (
    <View style={styles.container} testID="avail_depletion_bottomsheet_container">
      <View style={styles.headingContainer} testID="avail_depletion_bottomsheet_heading_container">
        <Image
          source={{ uri: availDepletionConfig?.mainIconUrl }}
          style={styles.headingIcon}
          testID="avail_depletion_bottomsheet_heading_icon"
        />
        <Text style={styles.headingText} testID="avail_depletion_bottomsheet_heading_text">
          {availDepletionConfig?.title}
        </Text>
        <TouchableOpacity
          style={styles.stickyCloseButton}
          onPress={() => {
            changeDisplayAvailDepletionBottomSheet(false);
          }}
          testID="avail_depletion_bottomsheet_close_button"
        >
          <Image
            source={ASSETS.crossIcon}
            style={styles.closeIcon}
            testID="avail_depletion_bottomsheet_close_icon"
          />
        </TouchableOpacity>
      </View>
      <View style={styles.contentContainer} testID="avail_depletion_bottomsheet_content_container">
        <Text style={styles.descriptionText} testID="avail_depletion_bottomsheet_description_text">
          {availDepletionConfig?.description}
        </Text>
        <Image
          source={{ uri: availDepletionConfig?.chartIcon }}
          style={styles.chartIcon}
          testID="avail_depletion_bottomsheet_chart_icon"
        />
        <Text style={styles.chartTitleText} testID="avail_depletion_bottomsheet_chart_title_text">
          {availDepletionConfig?.chartTitle}
        </Text>
        <Text style={styles.disclaimerText} testID="avail_depletion_bottomsheet_disclaimer_text">
          {availDepletionConfig?.disclaimer}
        </Text>
        <TouchableOpacity
          onPress={() => {
            changeDisplayAvailDepletionBottomSheet(false);
          }}
          feedbackColor={colors.transparent}
          style={styles.primaryButton}
          testID="avail_depletion_bottomsheet_close_button"
        >
          <Text
            style={styles.primaryButtonText}
            testID="avail_depletion_bottomsheet_close_button_text"
          >
            {_label('close', { uppercase: true })}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
