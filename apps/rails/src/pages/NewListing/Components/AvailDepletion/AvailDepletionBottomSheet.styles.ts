import { Platform, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: Platform.OS === 'android' ? 20 : 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  headingContainer: {
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headingIcon: {
    width: 24,
    height: 24,
  },
  headingText: {
    fontSize: 18,
    fontWeight: '700',
    lineHeight: 21,
    color: colors.black,
  },
  closeIcon: {
    width: 24,
    height: 24,
  },
  stickyCloseButton: {
    marginLeft: 'auto',
  },
  contentContainer: {
    marginTop: 15,
  },
  descriptionText: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 17,
    color: colors.textGrey,
  },
  chartIcon: {
    width: '100%',
    height: 150,
    marginTop: 15,
    resizeMode: 'contain',
  },
  chartTitleText: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 17,
    marginTop: 7,
    color: colors.textGrey,
    textAlign: 'center',
  },
  disclaimerText: {
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 15,
    color: colors.greyText1,
    marginTop: 20,
  },
  primaryButton: {
    width: '100%',
    height: 44,
    marginTop: 30,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 19,
  },
});
