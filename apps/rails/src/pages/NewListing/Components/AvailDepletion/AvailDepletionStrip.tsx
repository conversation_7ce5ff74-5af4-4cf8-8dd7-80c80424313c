import { connect } from 'react-redux';
import React from 'react';
import {
  changeDisplayAvailDepletionStrip,
  displayAvailDepletionTravelerBottomSheet,
} from '../../../TravelerDetails/TravelerDetailsActions';
import LinearGradient from 'react-native-linear-gradient';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { AvailabilityDepletionType } from '../../../TravelerDetails/Components/Railofy/types';

interface AvailDepletionStripProps {
  displayAvailDepletionStrip: boolean;
  availabilityDepletion: AvailabilityDepletionType | null;
  displayAvailDepletionTravelerBottomSheet: (value: boolean) => void;
  changeDisplayAvailDepletionStrip: (value: boolean) => void;
}

const AvailDepletionStrip = (props: AvailDepletionStripProps) => {
  const { availabilityDepletion = null, displayAvailDepletionStrip = false } = props;
  return (
    <>
      {displayAvailDepletionStrip && availabilityDepletion && (
        <LinearGradient
          colors={[
            availabilityDepletion?.backgroundGradient?.start || colors.white,
            availabilityDepletion?.backgroundGradient?.end || colors.white,
          ]}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={styles.container}
          testID="avail_deletion_strip_linear_gradient"
        >
          <View style={styles.leftSubContainer}>
            <Image
              source={{ uri: availabilityDepletion?.mainIconUrl }}
              style={styles.mainIcon}
              testID="avail_deletion_strip_main_icon"
            />
            <Text
              style={[styles.text, { color: availabilityDepletion?.textColor }]}
              testID="avail_deletion_strip_text"
            >
              {availabilityDepletion?.text}
            </Text>
          </View>
          <View style={styles.rightSubContainer}>
            <TouchableOpacity
              onPress={() => {
                props.displayAvailDepletionTravelerBottomSheet(true);
              }}
              testID="avail_deletion_strip_learn_more_touchable_opacity"
            >
              <Text style={styles.learnMoreText} testID="avail_deletion_strip_learn_more_text">{_label('learn_more')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                props.changeDisplayAvailDepletionStrip(false);
              }}
              testID="avail_deletion_strip_cross_touchable_opacity"
            >
              <Image
                source={ASSETS.crossIcon}
                style={styles.crossIcon}
                testID="avail_deletion_strip_cross_icon"
              />
            </TouchableOpacity>
          </View>
        </LinearGradient>
      )}
    </>
  );
};

const mapStateToProps = (state: unknown) => {
  const {
    railsTraveler: {
      displayAvailDepletionStrip = false,
      railofy: { availabilityDepletion = null } = {},
    } = {},
  } = state;
  return {
    displayAvailDepletionStrip,
    availabilityDepletion,
  };
};

const mapDispatchToProps = {
  displayAvailDepletionTravelerBottomSheet,
  changeDisplayAvailDepletionStrip,
};

export default connect(mapStateToProps, mapDispatchToProps)(AvailDepletionStrip);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 10,
    gap: 8,
  },
  leftSubContainer: {
    flex: 1,
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  rightSubContainer: {
    marginLeft: 'auto',
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  mainIcon: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
  },
  crossIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 17,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  learnMoreText: {
    color: colors.primary,
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '700',
  },
});
