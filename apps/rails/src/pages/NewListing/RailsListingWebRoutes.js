/* eslint-disable */
import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import url from 'url';
import {connect} from 'react-redux';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import railsListingReducer from './RailsListingReducer';
import {withRouterState} from '../../../../../../web/WebRouter';
import RailsListingWrapper from "./RailsListingWrapper";
import RailsFilters from "../RailsFilters/RailsFilterContainer";
import {getQueryParamsFromUrl, getDateFromURLFormat} from '@mmt/legacy-commons/Helpers/misc';
import { getStationDetailsFromLocus, setLocusData } from "../RailsLandingPage/Store/RailsLandingPageActions";
import isEmpty from 'lodash/isEmpty';
class RailsListingWeb extends React.Component {
  constructor(props){
    super(props);
    const urlObj = url.parse(window.location.href, window.location.search);
    const {query = {}} = urlObj;
    const queryParams = getQueryParamsFromUrl(window.location.href);
    let tempDate = queryParams.departure;
    const loadListing = queryParams.from_locus_id ? false : true
    let dateVal= getDateFromURLFormat(tempDate)
    this.state = {
      childProps: {
        originStation: getDataFromProps(props.originStation, {
          cityName: query.from_city,
          code: query.from,
          stationName: query.from_station
      }),
        destinationStation: getDataFromProps(props.destinationStation, {
          cityName: query.to_city,
          code: query.to,
          stationName: query.to_station
        }),
        departureDate: dateVal,//fecha.parse(query.departure, 'YYYYMMDD')
        trainNumber: query.trainNumber,
        openUniversalWidget:query.openWidget
      },
      loadListing
    }
  }
  async componentDidMount(){
    const urlObj = url.parse(window.location.href, window.location.search);
    const {query = {}} = urlObj;
    const {from_locus_id,to_locus_id} = query
    if(isEmpty(from_locus_id) && isEmpty(to_locus_id)){
      return
    }
    try{
      const response = await getStationDetailsFromLocus({from_locus_id, to_locus_id});
      this.props.setLocusData(response);
      const {originStation,destinationStation}= this.state.childProps;
      const newOrigin = {...originStation, code: response.fromLobCode }
      const newDest= {...destinationStation, code: response.toLobCode }
      const childProps ={ ...this.state.childProps,originStation:newOrigin,destinationStation:newDest  }
      this.setState({childProps,loadListing:true})
    }
    catch(err){
      this.setState({loadListing:true})
      console.log('err web',err)
    }
  }

  render() {
    const {loadListing}= this.state
    return loadListing ? <RailsListingWrapper {...this.state.childProps}/> :<></>
  }
}

const mapStateToProps = ({railsListing,railsLanding}, ...ownProps) => {
  return {
    ...ownProps,
    destinationStation: railsLanding?.destinationStation,
    originStation: railsLanding?.originStation,
  };
};

const mapDispatchToProps = (dispatch) => ({
  setLocusData : locusData => dispatch(setLocusData(locusData))
});

injectAsyncReducer('railsListing', railsListingReducer);

export const RailsListingContainer = connect(mapStateToProps, mapDispatchToProps)(RailsListingWeb);

const RailsListingRoutes = () => (
  <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
    <Switch>
      <Route exact path="(/pwa/v2|/booking|/railways/search)/railsListingPage" component={withRouterState(RailsListingContainer)} />
      <Route exact path="(/pwa/v2|/booking|/railways/search)/railsListingPage/filters" component={withRouterState(RailsFilters)} />
    </Switch>
  </View>
);

export default withRouter(RailsListingRoutes);
