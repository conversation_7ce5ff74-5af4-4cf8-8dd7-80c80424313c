import _property from 'lodash/property';

export const getDepartureDateAndTime = _property('departureDateAndTime');
export const getArrivalDateAndTime = _property('arrivalDateAndTime');
export const getDepartureTime = _property('departureTime');
export const getArrivalTime = _property('arrivalTime');
export const getDuration = _property('duration');
export const getFromStationCode = _property('frmStnCode');
export const getFromStationName = _property('frmStnName');
export const getToStationCode = _property('toStnCode');
export const getToStationName = _property('toStnName');
export const getArrivalDate = data => data.arrivalDate || ''; // didn't use get because get would take more time to execute
export const getDepartureDate = data => data.departureDate || '';
