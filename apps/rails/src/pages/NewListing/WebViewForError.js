import React, {Component} from 'react';
import { View, BackHandler, Linking} from 'react-native';
import {WebView} from 'react-native-webview';
import PropTypes from 'prop-types';
import { Actions } from '../../navigation';
import CommonHeader from '@mmt/legacy-commons/Common/Components/Header/CommonHeader';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import { isIos } from '../../Utils/device';

/**
 * WebView For handling the typeoform
 * Parameters needed : url, header text
 */

const LANDING_PAGE_URL = 'https://rails.makemytrip.com/pwa/v2';
const NEW_LANDING_PAGE_URL = 'https://rails.makemytrip.com';

const HOME_PAGE_DEEPLINK = 'https://www.makemytrip.com';

class WebViewForError extends Component {
  constructor(props) {
    super(props);
    this.handleBackButtonClick = this.onBackPress.bind(this);
    this.timer = null;
  }

  UNSAFE_componentWillMount () {
    BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
    this.timer = setTimeout(() => {
      this.setState({invalidate: true});
    }, 300);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
  }

  render() {
    const uri = this.props.url;
    return (
      <View style={{flex: 1, flexDirection: 'column'}}>
        <CommonHeader
          headerText={this.props.headerText}
          imgSrc={backIcon}
          whiteHeader
          backPressHandler={this.onBackPress}
        />
        <WebView
          style={{flex: 1, flexGrow: 1}}
          ref={e => this._webView = e}
          source={{uri}}
          onNavigationStateChange={this.onNavigationStateChange}
          onShouldStartLoadWithRequest={this.onShouldStartLoadWithRequest}
          startInLoadingState
        />
      </View>
    );
  }

  onShouldStartLoadWithRequest = (event) => {
    const {url} = event;
    if (url === this.props.url) {
      return true;
    }
    else if (url === this.props.goBackUrl) {
      this.onBackPress();
    }
    else if (url.startsWith(LANDING_PAGE_URL)) {
      Actions.rails();
    }
    else if (url.startsWith(NEW_LANDING_PAGE_URL)) {
      Actions.railNewLanding();
    }
    else if (url.startsWith('http') && url.includes('makemytrip.com')) {
      Linking.openURL(HOME_PAGE_DEEPLINK);
    }
    else {
      Linking.openURL(event.url);
    }
    return false;
  };

  onBackPress = () => {
    const somethingPoped = Actions.pop();
    if (!somethingPoped) {
      if (isIos()) {
        ViewControllerModule.popViewController(this.props.rootTag);
      } else {
        BackHandler.exitApp();
      }
    }
    return true;
  };
}

WebViewForError.propTypes = {
  url: PropTypes.string.isRequired,
  headerText: PropTypes.string.isRequired,
  goBackUrl: PropTypes.string,
  rootTag: PropTypes.number,
};

WebViewForError.defaultProps = {
  goBackUrl: '',
};


export default WebViewForError;
