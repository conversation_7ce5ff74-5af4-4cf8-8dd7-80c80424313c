import fecha from 'fecha';
import { Platform } from 'react-native';
import { Actions } from '../../navigation';
import {
  RAIL_AVAILABILITY_CARD_DATA_TYPE,
  getCommonPdtData,
  getCommonSearchContextData,
} from '../../PdtAnalytics/PdtHelper/RailsPdtUtils';
import { railsConfig,SEARCH_CONTEXT } from '../../RailsConfig';
import {
  addMinutes,
  getImageSign,
  updateTimeInfoInSelectedTrain,
  getNecessaryStationDetails,
} from './Utils/RailListingUtils';
import {
  trackNewListingClickEvent,
  trackNewListingDateChangeEvent,
  trackNewListingEvent,
  trackNewListingLoad,
  trackNewListingSortEvent,
  trackTravellerPageLoad,
  updateEvar47or97Variable,
  removeEventFromEvar47or97Variable,
  trackEventAfterRemovingOlderEvent,
  trackEvar22Event,
  removeEventFromEvar99Variable,
  trackGenericEvar99Event,
  updateEvar99Variable,
  trackGenericEvar47or97Event,
  } from '../../railsAnalytics';
import {setDataInStorage} from '@mmt/legacy-commons/AppState/LocalStorage';
import {
  NO_RESERVATION_CHOICE_CODE,
  INPUT_CITY_SELECTED,
  voucherState,
  LISTING_PAGE_CONST,
  RAILS_LISTING,
  dummyArpResponse,
  LISTING_CONNECTED_TRAVEL_EVENTS,
  LISTING_PAGE_TRACKING_KEY,
  DATE_MONTH_YEAR_FORMAT,
  TRAIN_OUTSIDE_RESERVATION_WINDOW,
  NEARBY_DATES_EVENTS,
  ODT_EVENTS,
  RAILS_CONNECTED_TRAVEL_POKUS_CONSTANTS,
  DOUBLE_DIGIT_DATE_WITH_MONTH_FORMAT,
  ENTITY,
} from '../../Utils/RailsConstant';
import QuotaType, { getQuota } from '../Types/QuotaType';
import type { QuotaTypeInterface } from './interface';
import AvailabilityStatusTypes from '../Types/AvailabilityStatusTypes';
import StationChangeSignType from '../Types/StationChangeSignType';
import { getFormattedDateFromDate, twentyFourHourDateTimeFormat } from '@mmt/legacy-commons/Helpers/dateHelpers';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import fetch2 from '../../fetch2';
import {
  AbConfigKeyMappings,
  getPokusConfig,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {PokusLobs} from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import {ACTION_SET_PPBOOKING_LANDING_DATA} from '../Review/RailsReviewActions';
import {tuneRailsListingLoadTracker} from '../../Utils/railsTuneTracker';
import {
  fireOmnitureEventsNew,
  onClearAllFiltersClickedInListing,
  onFilterIconClick,
} from '../RailsFilters/RailsFilterActions';
import RailsPdtListingHelper from '../../PdtAnalytics/PdtHelper/RailsPdtListingHelper';
import SortType from '../Types/SortType';
import {botmanSensorHeader} from '@mmt/legacy-commons/Native/NetworkModule';
import AvailabilityStatusValues from '../Types/AvailabilityStatusValues';
import {ACTION_DATE_SELECTED} from '../RailsLandingPage/Store/RailsLandingActionTypes';
import {getStationDetails} from '../../Utils/CityRepository';
import { trackTotalCardsOnListing } from '../../Utils/railofyUtils';
import {
  FutureSelectedDateData, FutureSelectedTrain,
} from '../../types/futureAvailaibility.types';
import {skywalkerSearchConnector} from '../../Skywalker/connector';
import {SearchConnector} from '../../Skywalker/searchConnector';
import { getQueryParamsFromUrl } from '@mmt/legacy-commons/Helpers/misc';
import {
  getLocusIdFromStationDetails,
  initAppVersion,
} from '../RailsLandingPage/Store/RailsLandingPageActions';
import * as RailsLandingActionTypes from '../RailsLandingPage/Store/RailsLandingActionTypes';
import { pushListingIntoGtm } from '../../GtmConfig/gtmConfig';
import {
  trackPageVisits,
  trackClickEventProp61,
} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import sortBy from 'lodash/sortBy';
import reverse from 'lodash/reverse';
import isUndefined from 'lodash/isUndefined';
import { _label } from '../../vernacular/AppLanguage';
import {
  getRailsRouteExtenstionBottomsheet,
  getRailsListingSortingLogic,
  RailsAbConfig,
  getRailsPokusConfig,
  getIfCanShowBNPP,
  getNearbyDatesNew,
  getRailsCtSource,
  getRailsAvailDepletion,
  getIfToShowConnectedTravelV3,
  getRailsListingMealInfo,
  getNewRailsSortingLogic,
} from '../../RailsAbConfig';
import { getMigratedTrainsList } from 'apps/rails/src/pages/NewListing/RailsListingActionsV3';
import { getDataForConfirmedOptions } from './Components/ConfirmedOptions/ConfirmedOptionsUtils';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { getCurrentInternetConnectionAndApproxSpeed } from '@mmt/legacy-commons/Common/utils/AppUtils';
import {
  getDataForConfirmedOptionsBottomSheet,
  getNearbyDatesurl,
  getSelectedCardHash,
  getTrainDataForNearbyDatesConfirmedOption,
} from './Components/NearByDates/Utils/NearByDatesUtils';
import { configKeys } from '../../configStore/Common/constants';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { logListingPdtClickEvents } from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import { LISTING_PDT_CLICK_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { ENTITY_TYPE, GRAFANA_ERROR_CODES, PAGE } from 'apps/rails/src/types/grafanaTracking.types';
import { logGrafanaErrorMetrics } from '../../Utils/RailsGrafanaTracker';
import { connectedTravelVariants } from '../../RailsAbConstants';
import LottieDataProvider from './Utils/LottieProvider';

export const ACTION_RAILS_LISTING_INIT_STATE = '@rails/ACTION_RAILS_LISTING_INIT_STATE';
export const ACTION_RAIL_LISTING_SHOW_WEBVIEW = '@rails/ACTION_RAIL_LISTING_SHOW_WEBVIEW';
export const ACTION_RAIL_LISTING_REMOVE_WEBVIEW = '@rails/ACTION_RAIL_LISTING_REMOVE_WEBVIEW';
export const ACTION_RAILS_LISTING_COMPLETE = '@rails/ACTION_RAILS_LISTING_COMPLETE';
export const ACTION_RAILS_LISTING_ERROR = '@rails/ACTION_RAILS_LISTING_ERROR';
export const ACTION_SET_LIST_SORTED_ORDER = '@rails/ACTION_SET_LIST_SORTED_ORDER';
export const ACTION_TRAIN_SELECTED = '@rails/ACTION_TRAIN_SELECTED';
export const ACTION_CLOSE_CLASS_AVAILABILITY = '@rails/ACTION_CLOSE_CLASS_AVAILABILITY';
export const ACTION_QUOTA_TYPE_SELECTED = '@rails/ACTION_QUOTA_TYPE_SELECTED';
export const ACTION_SET_ALL_AVAILABLE_CLASSES = '@rails/ACTION_SET_ALL_AVAILABLE_CLASSES';
export const ACTION_SET_CLASS_TYPE_SELECTED = '@rails/ACTION_SET_CLASS_TYPE_SELECTED';
export const ACTION_IRCTC_BOOKING_ERROR = '@rails/ACTION_IRCTC_BOOKING_ERROR';
export const ACTION_TATKAL_BOOKING_ERROR = '@rails/ACTION_TATKAL_BOOKING_ERROR';
export const ACTION_SET_AVAILABILITY_LIST = '@rails/ACTION_SET_AVAILABILITY_LIST';
export const ACTION_INIT_AVAILABILITY_LIST = '@rails/ACTION_INIT_AVAILABILITY_LIST';
export const ACTION_SELECTED_BOOKING_DATE = '@rails/ACTION_SELECTED_BOOKING_DATE';
export const ACTION_STATION_CHANGE_ALERT = '@rails/ACTION_STATION_CHANGE_ALERT';
export const ACTION_PREFILL_DATA_FOR_POPUP = '@rails/ACTION_PREFILL_DATA_FOR_POPUP';
export const ACTION_SCHEDULE_TATAKAL_BOTTOM_SHEET = '@rails/ACTION_SCHEDULE_TATAKAL_BOTTOM_SHEET';
export const ACTION_UPDATE_SELECTED_TRAIN_INFO_JOURNEY_DATE = '@rails/ACTION_UPDATE_SELECTED_TRAIN_INFO_JOURNEY_DATE';
export const ACTION_UPDATE_TBS_AVAILABILITY = '@rails/ACTION_UPDATE_TBS_AVAILABILITY';
export const ACTION_UPDATE_TBS_CONFIRM_PROBABLITY = '@rails/ACTION_UPDATE_TBS_CONFIRM_PROBABLITY';
export const ACTION_SET_DATA_FOR_TRAVELERS_PAGE = '@rails/ACTION_SET_DATA_FOR_TRAVELERS_PAGE';
export const ACTION_UPDATE_QUOTA_SELECTED_OF_TRAIN = '@rails/ACTION_UPDATE_QUOTA_SELECTED_OF_TRAIN';
export const ACTION_UPDATE_CLASS_TYPE_SELECTED_OF_TRAIN = '@rails/ACTION_UPDATE_CLASS_TYPE_SELECTED_OF_TRAIN';
export const ACTION_UPDATE_AVAILABILITY_OF_TRAIN = '@rails/ACTION_UPDATE_AVAILABILITY_OF_TRAIN';
export const ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN = '@rails/ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN';
export const ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN_FOR_ODT = '@rails/ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN_FOR_ODT';
export const ACTION_SET_HOTELS_CROSS_SELL_DATA = '@rails/ACTION_SET_HOTELS_CROSS_SELL_DATA';
export const ACTION_SET_VOUCHER_OBJECT = '@rails/ACTION_SET_VOUCHER_OBJECT';
export const ACTION_INIT_RAILS_LISTING_FILTERS = '@rails/ACTION_INIT_RAILS_LISTING_FILTERS';
export const ACTION_SET_FILTER = '@rails/ACTION_SET_FILTER';
export const ACTION_COMMIT_TRAIN_AND_FILTER = '@rails/ACTION_COMMIT_TRAIN_AND_FILTER';
export const ACTION_SET_QUICK_FILTER = '@rails/ACTION_SET_QUICK_FILTER';
export const ACTION_SET_LISTING_QUICK_FILTER = '@rails/ACTION_SET_LISTING_QUICK_FILTER';
export const ACTION_INIT_FILTER_STATE = '@rails/ACTION_INIT_FILTER_STATE';
export const ACTION_SET_TO_PREV_FILTER_STATE = '@rails/ACTION_SET_TO_PREV_FILTER_STATE';
export const ACTION_CLEAR_ALL_FILTERS_ON_FILTER_PAGE = '@rails/ACTION_CLEAR_ALL_FILTERS_ON_FILTER_PAGE';
export const ACTION_CLEAR_SELECTED_FILTERS_ON_FILTER_PAGE = '@rails/ACTION_CLEAR_SELECTED_FILTERS_ON_FILTER_PAGE';
export const ACTION_CLEAR_ALL_FILTERS_ON_LISTING_PAGE = '@rails/ACTION_CLEAR_ALL_FILTERS_ON_LISTING_PAGE';
export const ACTION_INIT_SHOW_CONFIRMATION_CHANCE_AB = '@rails/ACTION_INIT_SHOW_CONFIRMATION_CHANCE_AB';
export const ACTION_INIT_SHOW_CONFIRMATION_CHANCE_V2_AB = '@rails/ACTION_INIT_SHOW_CONFIRMATION_CHANCE_V2_AB';
export const ACTION_INIT_RAIL_LISTING_V3_AB = '@rails/ACTION_INIT_RAIL_LISTING_V3_AB';
export const ACTION_SET_CONFIRMATION_CHANCES = '@rails/ACTION_SET_CONFIRMATION_CHANCES';
export const ACTION_DATE_CHANGED = '@rails/ACTION_DATE_CHANGED';
export const ACTION_RAILS_LISTING_UPDATE_ORIGIN = '@rails/ACTION_RAILS_LISTING_UPDATE_ORIGIN';
export const ACTION_RAILS_LISTING_UPDATE_DESTINATION = '@rails/ACTION_RAILS_LISTING_UPDATE_DESTINATION';
export const ACTION_RAILS_LISTING_EDIT_ORIGIN = '@rails/ACTION_RAILS_LISTING_EDIT_ORIGIN';
export const ACTION_RAILS_LISTING_EDIT_DESTINATION = '@rails/ACTION_RAILS_LISTING_EDIT_DESTINATION';
export const ACTION_RAIL_LISTING_SHOW_HIDE_EDIT_WIDGET = '@rails/ACTION_RAIL_LISTING_SHOW_HIDE_EDIT_WIDGET';
export const ACTION_RAIL_LISTING_INTIAL_LOADING = '@rails/ACTION_RAIL_LISTING_INTIAL_LOADING';
export const ACTION_RAIL_LISTING_SHOW_LOADING = '@rails/ACTION_RAIL_LISTING_SHOW_LOADING';
export const ACTION_RAIL_LISTING_SEARCH_PARAMS_CHANGED =
  '@rails/ACTION_RAIL_LISTING_SEARCH_PARAMS_CHANGED';
export const ACTION_DATE_SELECTED_LISTING = '@rails/ACTION_DATE_SELECTED_LISTING';
export const ACTION_SWAP_SEARCH_LISTING = '@rails/ACTION_SWAP_SEARCH_LISTING';
export const ACTION_SKIP_UNIVERAL_WIDGET_URL = '@rails/ACTION_SKIP_UNIVERAL_WIDGET_URL';
export const COVID_SAFETY_CARD_LISTING = '@rails/COVID_SAFETY_CARD_LISTING';
export const ACTION_BOOKING_SUCCESS_RAILS = '@rails/ACTION_BOOKING_SUCCESS_RAILS';
export const ACTIONS_CLEAR_SELECTED_TRAIN_INFO = '@rails/ACTIONS_CLEAR_SELECTED_TRAIN_INFO';
export const ACTION_CLEAR_NOT_AVAILABLE_CARD = '@rails/ACTION_CLEAR_NOT_AVAILABLE_CARD';
export const ACTION_SET_ALTERNATE_AVAILABILTY_LIST = '@rails/ACTION_SET_ALTERNATE_AVAILABILTY_LIST';
export const CONFIRMATION_GUARANTEE_FILTER_FILL = '@rails/CONFIRMATION_GUARANTEE_FILTER_FILL';
export const SAVE_CONFIRMATION_GUARANTEE_POKUS_OPTION = '@rails/SAVE_CONFIRMATION_GUARANTEE_POKUS_OPTION';
export const CONFIRMATION_GUARANTEE_ERROR = '@rails/CONFIRMATION_GUARANTEE_ERROR';
export const SAVE_FREE_CANCELLATION_CONFIG = '@rails/SAVE_FREE_CANCELLATION_CONFIG';
export const SAVE_RAILOFY_ZC_SHOWN = '@rails/SAVE_CGFM_GRNTEE_OR_ZC_SHOWN';
export const ACTION_GET_PREVIOUS_SEARCH_POSITION = 'ACTION_GET_PREVIOUS_SEARCH_POSITION';
export const ACTION_GET_PREVIOUS_BOOKED_POSITION = 'ACTION_GET_PREVIOUS_BOOKED_POSITION';
export const ACTION_GET_PREVIOUS_SEARCHED_HEADING = 'ACTION_GET_PREVIOUS_SEARCHED_HEADING';
export const ACTION_GET_PREVIOUS_BOOKED_HEADING = 'ACTION_GET_PREVIOUS_BOOKED_HEADING';
export const ACTION_RAIL_LISTING_SET_CROSS_SELL_MODAL = '@rails/ACTION_RAIL_LISTING_SET_CROSS_SELL_MODAL';
export const ACTION_RAILS_LISTING_MODIFY_SEARCH_CLICKED = '@rails/ACTION_RAILS_LISTING_MODIFY_SEARCH_CLICKED';
export const ACTION_RAILS_LISTING_FETCHING_LOCUS_ID = '@rails/ACTION_RAILS_LISTING_FETCHING_LOCUS_ID';
export const ACTION_RAILS_LISTING_FETCHING_LOCUS_ID_ERROR = '@rails/ACTION_RAILS_LISTING_FETCHING_LOCUS_ID_ERROR';
export const ACTION_RAILS_LISTING_OMNITURE = '@rails/ACTION_RAILS_LISTING_OMNITURE';
export const ACTION_INIT_RAIL_LISTING = '@rails/ACTION_INIT_RAIL_LISTING';
export const ACTION_CONFIRM_OPTIONS_ALERT = '@rails/ACTION_CONFIRM_OPTIONS_ALERT';
export const ACTION_SEAT_LOCK_BOTTOMSHEET = '@rails/ACTION_SEAT_LOCK_BOTTOMSHEET';
export const ACTION_TOGGLE_TRAIN_SCHEDULE_BOTTOM_SHEET = '@rails/ACTION_TOGGLE_TRAIN_SCHEDULE_BOTTOM_SHEET';
export const ACTION_TOGGLE_CONNECTED_TRAVEL_BOTTOM_SHEET = '@rails/ACTION_TOGGLE_CONNECTED_TRAVEL_BOTTOM_SHEET';
export const ACTION_CONNECTED_TRAVEL_BOTTOM_SHEET_DATA = '@rails/ACTION_CONNECTED_TRAVEL_BOTTOM_SHEET_DATA';
export const ACTION_SET_CONNECTED_TRAVEL_LIST = '@rails/ACTION_SET_CONNECTED_TRAVEL_LIST';
export const ACTION_TOGGLE_NEARBY_DATES_LIST = '@rails/ACTION_TOGGLE_NEARBY_DATES_LIST';
export const ACTION_UPDATE_NEARBY_DATES_SELECTED_CARD = '@rails/ACTION_UPDATE_NEARBY_DATES_SELECTED_CARD';
export const ACTION_INT_NEARBY_DATES_LIST = '@rails/ACTION_INT_NEARBY_DATES_LIST';
export const ACTION_UPDATE_NEARBY_DATES_LIST = '@rails/ACTION_UPDATE_NEARBY_DATES_LIST';
export const ACTION_NEARBY_DATES_ERROR = '@rails/ACTION_NEARBY_DATES_ERROR';
export const ACTION_UPDATE_NEARBY_DATES_TTU = '@rails/ACTION_UPDATE_NEARBY_DATES_TTU';
export const ACTION_UPDATE_QUOTA_SELECTED_OF_OTHER_DAY_TRAIN =
  '@rails/ACTION_UPDATE_QUOTA_SELECTED_OF_OTHER_DAY_TRAIN';
export const ACTION_UPDATE_AVAILABILITY_OF_TRAIN_FOR_ODT = '@rails/ACTION_UPDATE_AVAILABILITY_OF_TRAIN_FOR_ODT';
export const ACTION_DISPLAY_AVAIL_DEPLETION_BOTTOM_SHEET = '@rails/ACTION_DISPLAY_AVAIL_DEPLETION_BOTTOM_SHEET';
export const ACTION_SET_SCHEDULE_TATKAL_DATA = '@rails/ACTION_SET_SCHEDULE_TATKAL_DATA';
export const ACTION_RAILS_LISTING_UPDATE_TATKAL_ALERT_TOOLTIP =
  '@rails/ACTION_RAILS_LISTING_UPDATE_TATKAL_ALERT_TOOLTIP';
export const ACTION_RAILS_LISTING_SHOWN_TOOLTIP = '@rails/ACTION_RAILS_LISTING_SHOWN_TOOLTIP';
export const ACTION_RAILS_LISTING_SHOWN_RTB_BOTTOMSHEET =
  '@rails/ACTION_RAILS_LISTING_SHOWN_RTB_BOTTOMSHEET';
export const ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG = '@rails/ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG';
export const ACTION_SET_PREV_BOOKED_COLLAPSED_STATE =
  '@rails/ACTION_SET_PREV_BOOKED_COLLAPSED_STATE';
export const ACTION_RESET_PREV_BOOKED_COLLAPSED_STATE =
  '@rails/ACTION_RESET_PREV_BOOKED_COLLAPSED_STATE';
export const ACTION_RESET_NEARBY_DATES_STATE = '@rails/ACTION_RESET_NEARBY_DATES_STATE';
export const ACTION_UPDATE_GRANULAR_AVAILABILITY_TRACKING = 'ACTION_UPDATE_GRANULAR_AVAILABILITY_TRACKING';

export const ALTERNATE_LISTING_IDENTIFIER = 'ALTERNATE_LISTING';
const TrainsEnquiryAvailabilityAPI = railsConfig.trainAvailabilityUrl;
// ===========================================================================

export const setCrossSellModal = (data: boolean) => {
  return { type: ACTION_RAIL_LISTING_SET_CROSS_SELL_MODAL, data: { showCrossSellModal: data } };
};

export const updateTatkalAlertTooltip = () => (dispatch) => {
  dispatch({
    type: ACTION_RAILS_LISTING_UPDATE_TATKAL_ALERT_TOOLTIP,
    data: { showTatkalAlertTooltipPerSession: false },
  });
};

export const showRtbListingBottomSheet = (data: boolean) => (dispatch) => {
  dispatch({
    type: ACTION_RAILS_LISTING_SHOWN_RTB_BOTTOMSHEET,
    data: { showRtbListingBottomSheet: data },
  });
};

export const railsListingBackPress = () => (dispatch, getState) => {
  const {
    railsListing: {
      showEditWidget,
      showCrossSellModal,
      displayStationChangeAlert,
      displayConfirmOptionsBottomSheet,
      displayTrainScheduleBottomSheet,
      displayConnectedTravelBottomSheet,
      displaySeatLockBottomSheet,
    },
  } = getState();

  dispatch(logListingPdtClickEvents(LISTING_PDT_CLICK_EVENTS.BACK_CLICK));
  const actions = [
    {
      condition: showEditWidget,
      action: () => dispatch(showHideEditWidget({ showEditWidget: false, isEditClicked: false })),
    },
    { condition: showCrossSellModal, action: () => dispatch(setCrossSellModal(false)) },
    { condition: displayStationChangeAlert, action: () => dispatch(closeStationChangeAlert()) },
    {
      condition: displayConfirmOptionsBottomSheet,
      action: () => dispatch(closeConfirmOptionsBottomSheet()),
    },
    {
      condition: displayTrainScheduleBottomSheet,
      action: () => dispatch(toggleTrainScheduleBottomSheet(false)),
    },
    { condition: displaySeatLockBottomSheet, action: () => dispatch(closeSeatLockBottomSheet()) },
    {
      condition: displayConnectedTravelBottomSheet,
      action: () => dispatch(toggleConnectedTravelBottomSheet(false, false)),
    },
  ];

  for (const { condition, action } of actions) {
    if (condition) {
      action();
      return true;
    }
  }

  Actions.pop();
  return true;
};

export const railsListingOmniture = (trackingIdentifier: string) => (dispatch, getState) => {
  const {
    newListingLoadOmitureDone = false,
    confirmationProbabilityOmitureDone = false,
    noTrainsViewOmnitureDone = false,
    noDirectTrainsFoundOmnitureDone = false,
    tuneTrackerOmnitureDone = false,
  } = getState().railsListing;

  switch (trackingIdentifier) {
    case 'new-listing-load':
      if (!newListingLoadOmitureDone) {
        const { originStation: from = {}, destinationStation: to = {} } = getState().railsListing;
        trackNewListingLoad(from, to);
        dispatch({
          type: ACTION_RAILS_LISTING_OMNITURE,
          data: { newListingLoadOmitureDone: true },
        });
      }
      break;

    case 'confirmation-probability':
      const { trainBtwnStnsList, trainsList } = getState().railsListing;
      if (!confirmationProbabilityOmitureDone && !isEmpty(trainBtwnStnsList) && !isEmpty(trainsList)) {
        trackTotalCardsOnListing(trainBtwnStnsList, trainsList);
        dispatch({
          type: ACTION_RAILS_LISTING_OMNITURE,
          data: { confirmationProbabilityOmitureDone: true },
        });
      }
      break;

    case 'tune-tracker':
      const {
        originStation: from ,
        destinationStation: to ,
        departureDate: date,
      } = getState().railsListing;
      if (!tuneTrackerOmnitureDone && !isEmpty(from?.cityName) && !isEmpty(to?.cityName) && date) {
        dispatch(tuneRailsListingLoad(from, to, date));
        dispatch({
          type: ACTION_RAILS_LISTING_OMNITURE,
          data: { tuneTrackerOmnitureDone: true },
        });
      }
      break;

    case 'no-trains-view':
      if (!noTrainsViewOmnitureDone) {
        trackPageVisits('mob_rail_listing_v2_noTrainsFound_noTrainsOnOtherDates', {});
        dispatch({
          type: ACTION_RAILS_LISTING_OMNITURE,
          data: { noTrainsViewOmnitureDone: true },
        });
      }
      break;

    case 'no-direct-trains-found':
      if (!noDirectTrainsFoundOmnitureDone) {
        trackPageVisits('mob_rail_listing_v2_noTrainsFound_TrainsExistOnOtherDates', {});
        dispatch({
          type: ACTION_RAILS_LISTING_OMNITURE,
          data: { noDirectTrainsFoundOmnitureDone: true },
        });
      }
      break;
  }
};

export const setIntialLoading = (data: boolean) => (dispatch) => {
  dispatch({
    type: ACTION_RAIL_LISTING_INTIAL_LOADING,
    data: {intialLoadingDone: data},
  });
};

const necessaryStationDetailsPresent = (station) => {
  const failed = (isEmpty(station) || isEmpty(station?.code));
  return !failed;
};

//Reason to disbale, just wrapped with try-catch
/* eslint-disable */
export const fetchCall = (props) => async (dispatch, getState) => {
  try {
    let origin = props?.originStation;
    let destination = props?.destinationStation;
    let date = props?.departureDate;
    let locusDetail = {};
    if (
      !necessaryStationDetailsPresent(origin) ||
      !necessaryStationDetailsPresent(destination) ||
      !date
    ) {
      const {
        originStation,
        destinationStation,
        departureDate,
        locusData,
      } = await getNecessaryStationDetails(props);
      origin = originStation;
      destination = destinationStation;
      date = departureDate;
      locusDetail = locusData;
    }

    dispatch(initState(origin, destination, date, true));
    dispatch(setIntialLoading(true));
    dispatch(initNewListing(props));
    dispatch(getMigratedTrainsList(origin, destination, date));
    dispatch(initAppVersion());

    const queryParams = getQueryParamsFromUrl(props?.deep_link_intent_url);

    if (
      (!isEmpty(queryParams['skywalker']) && queryParams['skywalker'] === 'true') ||
      (props?.getSkywalker && props?.getSkywalker === 'true')
    ) {
      trackNewListingFromSkyWalker();
    }

    const {
      railsLanding: {
        originStation: landingOrigin,
        destinationStation: landingDestination,
        departureDate: landingDate,
      },
    } = getState();
    getStationDetails(origin)
      .then((originDetails) => {
        const originStation = {
          ...origin,
          ...originDetails,
          ...getState()?.railsListing?.originStation,
        };
        dispatch(updateOriginStation(originStation));
        if (
          (!landingOrigin || landingOrigin?.code !== originStation?.code) &&
          necessaryStationDetailsPresent(originStation)
        ) {
          dispatch({
            type: RailsLandingActionTypes.ACTION_ON_ORIGIN_SELECTED,
            data: { ...originStation, locusCode: undefined },
          });
        }
      })
      .catch((err) => {
        return;
      });
    getStationDetails(destination)
      .then((destinationDetails) => {
        const destinationStation = {
          ...destination,
          ...destinationDetails,
          ...getState()?.railsListing?.destinationStation,
        };
        dispatch(updateDestinationStation(destinationStation));
        if (
          (!landingDestination || landingDestination?.code !== destinationStation?.code) &&
          necessaryStationDetailsPresent(destinationStation)
        ) {
          dispatch({
            type: RailsLandingActionTypes.ACTION_ON_DESTINATION_SELECTED,
            data: { ...destinationStation, locusCode: undefined },
          });
        }
      })
      .catch((err) => {
        return;
      });
    if (!landingDate && landingDate?.date.toLocaleDateString() !== date.toLocaleDateString()) {
      dispatch({
        type: RailsLandingActionTypes.ACTION_DATE_SELECTED,
        data: date,
      });
    }
    //skywalker start
    try {
      const skyWalkerData = {
        fromCity: origin?.code,
        toCity: destination?.code,
        fromDateTime: getFormattedDateFromDate(
          date,
          twentyFourHourDateTimeFormat,
        ),
      };

      skywalkerSearchConnector(skyWalkerData, 'listing');

      //New connector
      if (
        isEmpty(origin?.locusV2Id) ||
        isEmpty(destination?.locusV2Id)
      ) {
        const locusData = await getLocusIdFromStationDetails({
          from_station_code: origin?.code,
          to_station_code: destination?.code,
        });
  
        origin.locusV2Type = locusData?.sourceLocusV2Type;
        origin.locusV2Id = locusData?.sourceLocusV2Id;
        destination.locusV2Type = locusData?.destinationLocusV2Type;
        destination.locusV2Id = locusData?.destinationLocusV2Id;
      }

      const searchContext={
        fromCity: origin?.code,
        fromCityName :origin?.cityName,
        fromStationName: origin?.stationName,
        toCity: destination?.code,
        toCityName: destination?.cityName,
        toStationName: destination?.stationName,
        fromDateTime: date,
        sourceLocusV2Type: origin?.locusV2Type,
        sourceLocusV2Id: origin?.locusV2Id,
        destinationLocusV2Type: destination?.locusV2Type,
        destinationLocusV2Id: destination?.locusV2Id,
      };
      SearchConnector(searchContext,'listing');

    } catch (e) {
      console.log('Skywalker error: ', e);
    }
    //skywalker end
    if (Platform.OS === 'web') {
      const payload = {
        source: origin?.code,
        sourceStation: origin?.cityName,
        destination: destination?.code,
        destinationStation: destination?.cityName,
        doj: date,
      };
      pushListingIntoGtm(payload);
    }
  } catch (error) {
    const errorData = {
      catchedErrorMesage: error,
      source: 'RAILS_LISTING_ACTION@fetchCall'
    };
    logErrorToFirebase(errorData);
  }
};
/* eslint-enable */

let prevUrlListingInit;
export const initState =
  (originStation, destinationStation, departureDate, loading) => (dispatch, getState) => {
    if (Platform.OS === 'web') {
      if (prevUrlListingInit === window.location.href) {
        return;
    }
      prevUrlListingInit = window.location.href;
    }
    if (getRailsListingMealInfo() === 1) {
    LottieDataProvider.setLottieData();
    }
  let { railsListing: { isLoading } } = getState();
  if (loading !== undefined) {
    isLoading = loading;
  }
  if (originStation?.code && destinationStation?.code && departureDate) {
      try {
        const formattedDate = fecha.format(departureDate, 'YYYY-MM-DD').replace(/-/g, '');
        setDataInStorage(SEARCH_CONTEXT, {
          source: originStation.code,
          destination: destinationStation.code,
          doj: formattedDate,
        });
      } catch (error) {
        console.error('Error in getting formattedDate', error);
        logGrafanaErrorMetrics(
          PAGE.LISTING_V2,
          ENTITY_TYPE.COMPONENT,
          ENTITY.LISTING_PAGE_FECHA_ERROR,
          error,
          GRAFANA_ERROR_CODES.LISTING_PAGE_FECHA_ERROR,
        );
      }
  }
  dispatch(resetNearbyDatesState());

  dispatch({
    type: ACTION_RAILS_LISTING_INIT_STATE,
    data: {
      originStation,
      destinationStation,
      departureDate,
        isLoading,
      },
    });
  };
export const onOriginClicked = (dispatch, getState) => {
  const { railsListing: { originStation, destinationStation } } = getState();
  Actions.railsCityPicker({
    selectedCity: originStation,
    pairedCity: destinationStation,
    inputCitySelected: INPUT_CITY_SELECTED.source,
    actionType: ACTION_RAILS_LISTING_EDIT_ORIGIN,
  });
};

export const onDestinationClicked = (dispatch, getState) => {
  const { railsListing: { originStation, destinationStation } } = getState();
  Actions.railsCityPicker({
    selectedCity: destinationStation,
    pairedCity: originStation,
    inputCitySelected: INPUT_CITY_SELECTED.destination,
    actionType: ACTION_RAILS_LISTING_EDIT_DESTINATION,
  });
};

export const showHideEditWidget = (data) => (dispatch) => {
  dispatch({
    type: ACTION_RAIL_LISTING_SHOW_HIDE_EDIT_WIDGET,
    data,
  });
  dispatch(
    logListingPdtClickEvents(
      `${LISTING_PDT_CLICK_EVENTS.EDIT_WIDGET_CLICK}_${data?.isEditClicked}`,
    ),
  );
};

export const showIsLoading = (data) => (dispatch) => {
  dispatch({
    type: ACTION_RAIL_LISTING_SHOW_LOADING,
    data,
  });
};

export const setLocusIdFromStationDetailsError = (locusIdFromStationDetailsError: boolean) => ({
  type: ACTION_RAILS_LISTING_FETCHING_LOCUS_ID_ERROR,
  data: locusIdFromStationDetailsError,
});

export const setFetchingLocusId = (fetchingLocusId: boolean) => ({
  type: ACTION_RAILS_LISTING_FETCHING_LOCUS_ID,
  data: fetchingLocusId,
});

export const railsListingSearchParamsChanged = (noResolving) => async (dispatch, getState) => {
  if (!noResolving) {
    const {
      railsListing: { originStation, destinationStation, departureDate },
    } = getState();
    const [fromLocusCode, toLocusCode] = [originStation?.locusCode, destinationStation?.locusCode];
    const origin: unknown = {},
      destination: unknown = {};
    if (isEmpty(fromLocusCode) || isEmpty(toLocusCode)) {
      const [from_station_code, to_station_code] = [originStation?.code, destinationStation?.code];
      if (!isEmpty(from_station_code) && !isEmpty(to_station_code)) {
        dispatch(setFetchingLocusId(true));
        const retryLocus = async (retry: number) => {
          getLocusIdFromStationDetails({
            from_station_code,
            to_station_code,
          })
            .then((locusData) => {
              const { railsListing: {
                originStation,
                destinationStation,
              }} = getState();
              origin.locusCode = locusData?.fromLocusCode;
              origin.locusType = locusData?.sourceLocusType;
              destination.locusCode = locusData?.toLocusCode;
              destination.locusType = locusData?.destinationLocusType;
              destination.locusV2Type = locusData?.destinationLocusV2Type;
              destination.locusV2Id = locusData?.destinationLocusV2Id;
              origin.locusV2Id = locusData?.sourceLocusV2Id;
              origin.locusV2Type = locusData?.sourceLocusV2Type;
              dispatch(
                initState(
                  { ...originStation, ...origin },
                  { ...destinationStation, ...destination },
                  departureDate,
                  getState()?.railsListing?.isLoading,
                ),
              );
              dispatch(setLocusIdFromStationDetailsError(false));
              dispatch(setFetchingLocusId(false));
            })
            .catch((_err) => {
              if (retry === 0) {
                dispatch(setLocusIdFromStationDetailsError(true));
                dispatch(setFetchingLocusId(false));
              } else {
                retryLocus(retry - 1);
              }
            });
        };
        retryLocus(2);
      }
    }
  }
  dispatch({
    type: ACTION_RAIL_LISTING_SEARCH_PARAMS_CHANGED,
    data: { searchParamsChanged: true },
  });
};

export const setDepartureDate = date => (dispatch) => {
  dispatch({
    type: ACTION_DATE_SELECTED_LISTING,
    data: date,
  });
};
export const onDateClick = (dispatch, getState) => {
  const {
    railsListing: { departureDate },
  } = getState();
  Actions.railsCalendar({ selectedDate: departureDate, fromPage: LISTING_PAGE_CONST });
};
export const onSwapClicked = () => ({
  type: ACTION_SWAP_SEARCH_LISTING,
  data: null,
});

export const updateOriginStation = (originStation) => (dispatch) => {
  dispatch({
    type: ACTION_RAILS_LISTING_UPDATE_ORIGIN,
    data: {
      originStation,
    },
  });
};

export const updateDestinationStation = (destinationStation) => (dispatch) => {
  dispatch({
    type: ACTION_RAILS_LISTING_UPDATE_DESTINATION,
    data: {
      destinationStation,
    },
  });
};

export const skipUniversalWidgetParam = () => (dispatch) => {
  dispatch({
    type: ACTION_SKIP_UNIVERAL_WIDGET_URL,
  });
};

export const onSortTrainClicked = selectedSortParam => (dispatch, getState) => {
  const { railsListing: { trainsList } } = getState();
  trackNewListingSortEvent(`mob_rail_listing_v2_sort_${selectedSortParam.value}_selected`);
  // tracking
  fireOmnitureEventsNew('Sort_Apply');
  dispatch({
    type: ACTION_SET_LIST_SORTED_ORDER,
    data: {
      trainsList: sortTrainsList(trainsList, selectedSortParam),
      selectedSortParam,
    },
  });
};

export const sortTrainsList = (trainsList, selectedSortParam) => {
  interface Train {
    duration: number;
    departureDateAndTime: Date;
    clusterTrain: boolean;
  }
  switch (selectedSortParam.id) {
    case SortType.DurationFastest.id: {
      return sortBy(trainsList, [train => train.duration]);
    }
    case SortType.DepartureEarliest.id: {
      const pokusVal = getRailsListingSortingLogic();
      if (pokusVal === '5') {
        const { irctcTrains, clusterTrains } = (trainsList || []).reduce(
          (acc: { irctcTrains: Train[]; clusterTrains: Train[] }, train: Train) => {
            const sortedTrain = sortBy([train], [(train) => train.departureDateAndTime]);
            if (train.clusterTrain) {
              acc.clusterTrains.push(...sortedTrain);
            } else {
              acc.irctcTrains.push(...sortedTrain);
            }
            return acc;
          },
          { irctcTrains: [], clusterTrains: [] },
        );
        return [...irctcTrains, ...clusterTrains];
      }
      return sortBy(trainsList, [train => train.departureDateAndTime]);
    }
    case SortType.DepartureLatest.id: {
      return reverse(sortBy(trainsList, [train => train.departureDateAndTime]));
    }
    case SortType.ArrivalEarliest.id: {
      return sortBy(trainsList, [train => train.arrivalDateAndTime]);
    }
    case SortType.ArrivalLatest.id: {
      return reverse(sortBy(trainsList, [train => train.arrivalDateAndTime]));
    }
    default: {
      return sortBy(trainsList, [train => train.defaultSortedPriority]);
    }
  }
};

export const buildTrainList = trainsList => (dispatch, getState) => {
  const { railsListing: { originStation, destinationStation, departureDate, selectedSortParam } } = getState();
  if (isEmpty(trainsList)) {
    dispatch({
      type: ACTION_RAILS_LISTING_ERROR,
      data: {
        errorMessage: 'No trains found on this route',
        errorSubtitle: 'Please try searching for other nearby cities',
        errorFlag: true,
        recommendedMessage: '',
        recommendedList: [],
        noTrainFound: true,
        trainsList: [],
        minPriceDetails: {},
        minDuration: null,
      },
    });
    return [];
  }
  const availDepletionInitialIndex = trainsList?.findIndex(
    (train) => !isEmpty(train?.availabilityDepletion),
  );
  return sortTrainsList(trainsList.map((train,defaultSortedPriority) => {
    const departureDateAndTime = getDepartureDateAndTime(train, departureDate);
    const arrivalDateAndTime = addMinutes(departureDateAndTime, train.duration);
    const stationChangeSign = getImageSign(originStation, destinationStation, train);
    const clusterTrain = train.clusterTrain || false;
    return ({
      ...train,
      avlClasses: train.avlClasses,
      departureDateAndTime,
      arrivalDateAndTime,
      stationChangeSign,
      clusterTrain,
      defaultSortedPriority,
      ...((defaultSortedPriority === availDepletionInitialIndex) && {showAvailDepletIntro: true}),
    });
  }), selectedSortParam);
};


export const buildOtherDayTrainsList = trainsList => (dispatch, getState) => {
  const { railsListing: { originStation, destinationStation, selectedSortParam } } = getState();

  return sortTrainsList(
    trainsList?.map((train, defaultSortedPriority) => {
    const dateString = train?.possibleDates?.[0];
      const deptDate = new Date(
        `${dateString?.substring(0, 4)}-${dateString?.substring(4, 6)}-${dateString?.substring(
          6,
          8,
        )}`,
      );
    const departureDateAndTime = getDepartureDateAndTime(train, deptDate);
    const arrivalDateAndTime = addMinutes(departureDateAndTime, train.duration);
    const stationChangeSign = getImageSign(originStation, destinationStation, train);
    const clusterTrain = train.clusterTrain || false;
    return ({
      ...train,
      avlClasses: train.avlClasses,
      departureDateAndTime,
      arrivalDateAndTime,
      stationChangeSign,
      clusterTrain,
      defaultSortedPriority,
    });
  }), selectedSortParam);
};

interface AvailabilityResponseRequest {
  reservationChoice: typeof NO_RESERVATION_CHOICE_CODE;
  source: string;
  destination: string;
  class: string; // enum
  quota: string; // enum
  doj: string; //  "20210522"
  trainNumber: string; //  "02138"
  moreThanOneDay: boolean;
  zcEnable?: boolean,
  railsConfirmationGuaranteeOption?: number;
}
export const getAvailabilityResponse = async (
  className,
  trainInfo,
  quotaCode,
  departureDate,
  freeCancellationConfig,
  confirmationGuaranteeConfig,
  moreThanOneDay = false,
) => {
  try {
    // removing Tatkal timing check from frontend
    // const {error, errorMessage} = tatkaalTimings(quotaCode, departureDate);
    // if (error) {
    //   return {errorMessage};
    // }
    const { frmStnCode, toStnCode, trainNumber, possibleDates, odt } = trainInfo;

    const searchDate =  fecha.format(departureDate, 'YYYYMMDD');
    const departDate = odt ? possibleDates?.[0] : searchDate;
    const requestBody: AvailabilityResponseRequest = {
      reservationChoice: NO_RESERVATION_CHOICE_CODE,
      source: frmStnCode,
      destination: toStnCode,
      class: className,
      quota: quotaCode,
      doj: departDate,
      trainNumber,
      moreThanOneDay: moreThanOneDay,
    };
    if (freeCancellationConfig) {
      requestBody.zcEnable = freeCancellationConfig.freeCancellationEnabled || false;
    }
    if (confirmationGuaranteeConfig) {
      requestBody.railsConfirmationGuaranteeOption = confirmationGuaranteeConfig.railsConfirmationGuaranteeOption;
    }

    const res = await fetch2(TrainsEnquiryAvailabilityAPI, {
      method: 'POST',
      headers: getDefaultPostHeaders(),
      body: JSON.stringify(requestBody),
    });
    const response = await res.json();
    return response.errorMessage ? { errorMessage: response.errorMessage } : response;
  } catch (e) {
    console.log('error in fillCreateAccountData', e);
    return {
      errorMessage: _label('something_went_wrong_try_again_later') + ' Error Code: ' + e?.errorCode,
    };
  }
};

export const setNewTbsAvailability =
  (response, className, quota, trainInfo, lastUpdatedOn = _label('updated_few_minutes_ago')) =>
  (dispatch) => {
  const { totalFare, avlDayList, baseFare } = response;
  const [availability] = avlDayList;
  const {
      availablityDate,
      availablityStatus,
      availablityType,
      reason,
      reasonType,
      prettyPrintingAvailablityStatus,
      confirmationGuaranteeText,
      freeCancellationText,
      availabilityDepletion = null,
      wlSeatUrgencyPersuasion = null,
  } = availability;
  const newTbsAvailability = {
    availablityDate,
    availablityStatus,
    prettyPrintingAvailablityStatus,
    availablityType,
    reason,
    reasonType,
    wlType: '',
    currentBkgFlag: 'D',
    className,
    quota,
    totalFare,
    baseFare,
      confirmationGuaranteeText,
    freeCancellationText,
      lastUpdatedOn: availability?.lastUpdatedOn ?? lastUpdatedOn,
      availabilityDepletion,
      wlSeatUrgencyPersuasion,
  };

  dispatch({
    type: ACTION_UPDATE_TBS_AVAILABILITY,
    data: {
      newTbsAvailability,
      trainInfo,
    },
  });
};

export const getAvailabilityCardTrackingData =
  (className, trainData, quotaCode, availabilityItem) => {
    const { trainName, trainNumber } = trainData;
    const { availablityStatus } = availabilityItem;
    return `${trainName}|${trainNumber}|${quotaCode}|${className}|${availablityStatus}|NA`;
  };

export const getUpdateTrackingData = (className, trainData, quotaCode) => {
  const { trainName, trainNumber } = trainData;
  return `${trainName}|${trainNumber}|${quotaCode}|${className}|NA|NA`;
};

export const getViewDetailsTrackingData = (trainData) => {
  const { trainName, trainNumber } = trainData;
  return `${trainName}|${trainNumber}|NA|NA|NA|NA`;
};

export const getSeatExpandedClickData =
  (className, quotaCode, selectedDateObject, trainData) => {
  const { availabilityResponse } = trainData;
  const { trainName, trainNumber } = availabilityResponse;
    const { availablityStatus } = selectedDateObject;
    return `${trainName}|${trainNumber}|${quotaCode}|${className}|${availablityStatus}|NA`;
  };

export const updateCardClicked = (className, trainInfo, quotaCode) =>
  async (dispatch, getState) => {
    trackNewListingEvent(
      'mob_rail_listing_v2_tap_to_update_clicked',
      getUpdateTrackingData(className, trainInfo, quotaCode),
    );

    dispatch({
      type: '@rails/TBS_AVAILABILITY_CARD_LOADING',
      data: {
        trainInfo,
        quotaCode,
        className,
        isLoading: true,
      },
    });
    try {
      const { railsListing: { departureDate, freeCancellationConfig, confirmationGuaranteeConfig } } = getState();
      const response =
      await getAvailabilityResponse(
        className,
        trainInfo,
        quotaCode,
        departureDate,
        freeCancellationConfig,
        confirmationGuaranteeConfig,
        true,
      );      if (!isEmpty(response.errorMessage)) {
        trackClickEventProp61(RAILS_LISTING,`rails_ttu_${response.errorMessage}`);
      dispatch({
        type: ACTION_STATION_CHANGE_ALERT,
        data: { settingListingDataForTravellers: false },
      });
      showShortToast(_label(response.errorMessage));

      if (response?.errorMessage?.includes(TRAIN_OUTSIDE_RESERVATION_WINDOW)) {
        dispatch(setNewTbsAvailability(dummyArpResponse, className, quotaCode, trainInfo));
      }
        return;
      }
      const { avlDayList = null } = response;
      if (avlDayList && avlDayList?.length !== 0) {
        const { availablityStatus } = avlDayList[0];
        trackClickEventProp61(RAILS_LISTING, `rails_ttu_${availablityStatus}`);
      }
      const { travelplexChatConfig, travelplexTickers } = response?.travelplexConfig || {};
      dispatch({
        type: ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG,
        data: {
          travelplexChatConfig,
          travelplexTickers,
        },
      });

      dispatch(setNewTbsAvailability(response, className, quotaCode, trainInfo));
    } catch (e) {
      console.log('error in updateCardClick', e);
      trackClickEventProp61(RAILS_LISTING,'rails_ttu_something_went_wrong_try_again_later');
      showShortToast(_label('something_went_wrong_try_again_later') + ' Error Code: ' + e?.errorCode);
    } finally {
      dispatch({
        type: '@rails/TBS_AVAILABILITY_CARD_LOADING',
        data: {
          trainInfo,
          quotaCode,
          className,
          isLoading: false,
        },
      });
      const isNewNearbyDates = getNearbyDatesNew();
      if (isNewNearbyDates){
        const sectionIdentifier = trainInfo.sectionIdentifier || '';
        dispatch(nearbyDatesCardClick(trainInfo, className, quotaCode));
        dispatch(toggleNearbyDatesList(true, trainInfo.trainNumber, className, quotaCode, sectionIdentifier));
      }
    }
  };

export const changeFiltersClicked = () => (dispatch) => {
  dispatch(onFilterIconClick());
  dispatch(logListingPdtClickEvents(LISTING_PDT_CLICK_EVENTS.CHANGE_FILTERS_LISTING_CLICK));
};

export const clearAllFiltersClickedInListing = () => (dispatch) => {
  dispatch(onClearAllFiltersClickedInListing());
};

const getStationChangeAlertBottomsheetVal = (clusterTrain: boolean, stationChangeSign: string) => {
  return clusterTrain && !(stationChangeSign === StationChangeSignType.S0D0.code);
};

export const onAvailabilityDateClicked =
  (selectedDate: FutureSelectedDateData, trainData: FutureSelectedTrain) =>
  (dispatch: unknown, getState: unknown) => {
    const { trainNumber, avlClasses } = trainData;
  const updateTrainData = updateTimeInfoInSelectedTrain(trainData, selectedDate.availablityDate);
    const {
      railsListing: { trainsListObject },
    } = getState();
  const { availabilityResponse: response, selectedClassType, selectedQuota } =
    trainsListObject[trainNumber];
  const { avlDayList, originalAvlDayList } = getAvlDayList(response, trainData);
  const selectedAvlStatus = getDefaultAvlStatus(avlDayList, selectedDate.availablityDate);
  const selectedBookingDate = selectedDate;
  trackNewListingDateChangeEvent(
    'mob_rail_listing_v2_expanded_card_clicked',
    getSeatExpandedClickData(selectedClassType, selectedQuota?.code, selectedDate, trainsListObject[trainNumber]),
    selectedDate,
  );
  if (!isBookNowEnabled(selectedBookingDate)) {
    const { railsListing } = getState();
    const { notAvailableCard } = railsListing;
    if (!notAvailableCard) {
      showShortToast(_label('booking_not_available_for_this_train'));
    }
    return;
  }
  const {
    insuranceCharge,
    totalCollectibleAmount,
    totalFare, baseFare,
    applicableFoodTypes,
    travelplexConfig,
  } = response;
  const { travelplexChatConfig, travelplexTickers } = travelplexConfig || {};
  const { stationChangeSign, clusterTrain } = trainData;

  const displayStationChangeAlert = getStationChangeAlertBottomsheetVal(clusterTrain, stationChangeSign);
  const dispatchData = {
    applicableFoodTypes,
    selectedTrainInfo: updateTrainData,
    selectedClassType,
    allAvailableClasses: avlClasses,
    selectedQuota,
    selectedBookingDate,
    originalSelectedBookingDate: originalAvlDayList[0],
    selectedAvlStatus,
    avlDayList,
    ...response.bkgCfg,
    insuranceCharge,
    totalCollectibleAmount,
    displayStationChangeAlert,
    totalFare,
    baseFare,
    travelplexChatConfig,
    travelplexTickers,
  };

  const railofyZcShown = {
    railofyShown: Boolean(selectedDate?.confirmationGuaranteeText),
    zcShown: Boolean(selectedDate.freeCancellationText),
  };

  dispatch(saveRailofyZcShown(railofyZcShown));
  dispatch({
    type: ACTION_SET_DATA_FOR_TRAVELERS_PAGE,
    data: dispatchData,
  });
  if (!displayStationChangeAlert) {
      dispatch(goToTravelersPage);
    }
  };

export const availabilityCardClicked =
  (className: string, trainInfo, quotaCode: string, isNext = false) =>
  async (dispatch, getState) => {
    try {
      const isAlternateAvailabilyCard = className.includes(ALTERNATE_LISTING_IDENTIFIER);
      const { avlClasses, departureDateAndTime } = trainInfo;
      const { stationChangeSign, clusterTrain } = trainInfo;
      const displayStationChangeAlert =
        getStationChangeAlertBottomsheetVal(clusterTrain, stationChangeSign) &&
        !(trainInfo.boardingStation && trainInfo.droppingStation);
      const displayConfirmOptionsBottomSheet =
        getRailsRouteExtenstionBottomsheet() && isAlternateAvailabilyCard;

      const selectedTrainClassQuotaAvailibilty = trainInfo.tbsAvailability.filter(
        (x) => x.quota === quotaCode && x.className === className,
      );
      let isBookingAllowed =
        selectedTrainClassQuotaAvailibilty.length > 0 &&
        !isEmpty(selectedTrainClassQuotaAvailibilty[0].availablityType) &&
        selectedTrainClassQuotaAvailibilty[0].availablityType !==
          AvailabilityStatusValues.NO_ROOM.value &&
        !selectedTrainClassQuotaAvailibilty[0].availablityType.startsWith(
          AvailabilityStatusValues.DEPARTED.value,
        );
      if (isAlternateAvailabilyCard) {
        const alternateAvlClass = className.split('-')[0];
        const alternateAvailabilityList =
          getState()?.railsListing?.alternateAvailabilityResponse?.alternateAvailabilityList || [];
        const alternateAvlItem = alternateAvailabilityList.find(
          (avl: unknown) => avl?.trainNumber === trainInfo?.trainNumber,
        );
        const alternateClassAvlList = alternateAvlItem.classAvailabilityList;
        const alternateClassAvlItem = alternateClassAvlList.find((acvi: unknown) => {
          const acviClassName =
            acvi?.tbsAvailability?.className || acvi?.tbsAvailability?.classType;
          return acviClassName === alternateAvlClass && acvi?.tbsAvailability?.quota === quotaCode;
        });
        const alterAvlType = alternateClassAvlItem?.tbsAvailability?.availablityType;
        const isBookingNotAllowed =
          alterAvlType === AvailabilityStatusValues.NO_ROOM.value ||
          alterAvlType === AvailabilityStatusValues.DEPARTED.value ||
          alterAvlType.startsWith(AvailabilityStatusValues.DEPARTED.value);
        isBookingAllowed = !isBookingNotAllowed;
        dispatch(saveRailofyZcShown({ railofyShown: false, zcShown: false }));
      }

      if (!isAlternateAvailabilyCard) {
        removeEventFromEvar47or97Variable(
          `${RAIL_EVENTS.LISTING.RAIL_LISTING_CONFIRMED_OPTIONS_CLICKED}`,
        );
      }

      if (!isBookingAllowed) {
        trackClickEventProp61(RAILS_LISTING, 'rail_card_booking_not_available_for_this_train');
        const { railsListing } = getState();
        const { notAvailableCard } = railsListing;
        if (!notAvailableCard) {
          showShortToast(_label('booking_not_available_for_this_train'));
        }
        return;
      }

      if (displayConfirmOptionsBottomSheet && !isNext) {
        const selectedQuota: QuotaTypeInterface = getQuota(quotaCode);
        dispatch({
          type: ACTION_CONFIRM_OPTIONS_ALERT,
          data: {
            displayConfirmOptionsBottomSheet,
            selectedClassType: className,
            selectedQuota,
            selectedTrainInfo: trainInfo,
            isNearbyConfirmedOption: false,
          },
        });
        return;
      }

      if (displayStationChangeAlert && !isNext) {
        const selectedQuota: QuotaTypeInterface = getQuota(quotaCode);
        dispatch({
          type: ACTION_STATION_CHANGE_ALERT,
          data: {
            displayStationChangeAlert,
            selectedClassType: className,
            selectedQuota,
            selectedTrainInfo: trainInfo,
          },
        });
        return;
      }
        dispatch({
          type: ACTION_STATION_CHANGE_ALERT,
        data: { settingListingDataForTravellers: true },
        });

      const formattedClassName = isAlternateAvailabilyCard ? className.split('-')[0] : className;
      const {
        railsListing: { freeCancellationConfig, confirmationGuaranteeConfig },
      } = getState();

      const response = await getAvailabilityResponse(
        formattedClassName,
        trainInfo,
        quotaCode,
        departureDateAndTime,
        freeCancellationConfig,
        confirmationGuaranteeConfig,
        false,
      );
      if (!isEmpty(response.errorMessage)) {
        trackClickEventProp61(RAILS_LISTING, `rail_card_${response.errorMessage}`);
        showShortToast(response.errorMessage);
        dispatch({
          type: ACTION_STATION_CHANGE_ALERT,
          data: { settingListingDataForTravellers: false },
        });
        return;
      } else {
        dispatch(
          setNewTbsAvailability(
            response,
            className,
            quotaCode,
            trainInfo,
            selectedTrainClassQuotaAvailibilty[0]?.lastUpdatedOn,
          ),
        );
      }
      const { avlDayList, originalAvlDayList } = getAvlDayList(response, trainInfo);
      const formattedDate = fecha.format(departureDateAndTime, DATE_MONTH_YEAR_FORMAT);
      const selectedAvlStatus = getDefaultAvlStatus(avlDayList, formattedDate);
      const selectedBookingDate = avlDayList[0];
      const { travelplexChatConfig, travelplexTickers } = response?.travelplexConfig || {};
      const { insuranceCharge, totalCollectibleAmount, totalFare, baseFare, applicableFoodTypes } =
        response;
      if (!isBookNowEnabled(selectedBookingDate)) {
        dispatch({
          type: ACTION_STATION_CHANGE_ALERT,
          data: { settingListingDataForTravellers: false },
        });
        trackClickEventProp61(RAILS_LISTING, 'rail_card_booking_not_available_for_this_train');
        const { railsListing } = getState();
        const { notAvailableCard } = railsListing;
        if (!notAvailableCard) {
          showShortToast(_label('booking_not_available_for_this_train'));
        }
        return;
      }
      const { railsLanding, railsListing } = getState();
      removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_ODT_TRAIN_CLICKED);
      removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_CLUSTER_TRAIN_CLICKED);
      if (trainInfo?.odt) {
        updateEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_ODT_TRAIN_CLICKED);
      }
      if (trainInfo?.clusterTrain) {
        updateEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_CLUSTER_TRAIN_CLICKED);
      }
      const selectedQuota: QuotaTypeInterface = getQuota(quotaCode);
      const dispatchData = {
        applicableFoodTypes,
        selectedClassType: formattedClassName,
        allAvailableClasses: avlClasses,
        selectedQuota,
        selectedBookingDate,
        originalSelectedBookingDate: originalAvlDayList[0],
        selectedAvlStatus,
        avlDayList,
        ...response.bkgCfg,
        travelAdvisoryDetails: response.travelAdvisoryDetails,
        insuranceCharge,
        totalCollectibleAmount,
        totalFare,
        baseFare,
        selectedTrainInfo: trainInfo,
        settingListingDataForTravellers: false,
        travelplexChatConfig,
        travelplexTickers,
      };
      dispatch({
        type: ACTION_SET_DATA_FOR_TRAVELERS_PAGE,
        data: dispatchData,
      });
      trackClickEventProp61(RAILS_LISTING, `rail_card_${selectedAvlStatus}`);
      dispatch(goToTravelersPage);
      const selectedClass = { selectedClassType: className, selectedQuota: quotaCode };
      trackPdtListingLoad(
        getListingPagePdt(trainInfo, railsLanding, railsListing, selectedClass),
        RAIL_AVAILABILITY_CARD_DATA_TYPE,
        null,
      );
      if (!isEmpty(response?.avlDayList?.[0]?.availabilityDepletion)) {
        switch (getRailsAvailDepletion()) {
          case 1:
            updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_V1);
            break;
          case 2:
            updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_V2);
            break;
          default:
            break;
        }
      } else {
        removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_V1);
        removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_V2);
      }
    } catch (e) {
      trackClickEventProp61(RAILS_LISTING, 'rail_card_wrong_date_format');
      dispatch({
        type: ACTION_STATION_CHANGE_ALERT,
        data: { settingListingDataForTravellers: false },
      });
      console.log('wrong date format', e);
      const errorData = {
        catchedErrorMesage: e?.message,
        railsFromCode: trainInfo?.frmStnCode,
        railsFromStation: trainInfo?.frmStnName,
        railsToCode: trainInfo?.toStnCode,
        railsToStation: trainInfo?.toStnName,
        trainNumber: trainInfo?.trainNumber,
        railsRawDeptDate: trainInfo?.departureDateAndTime,
        railsRawArrivalDate: trainInfo?.arrivalDateAndTime,
      };
      logErrorToFirebase(errorData);
    }
  };

export const getDepartureDateAndTime = (currentTrain, departureDate) => {
  try {
    const hoursAndMinutes = fecha.parse(currentTrain.departureTime, 'HH:mm');
    const hours = hoursAndMinutes.getHours();
    const minutes = hoursAndMinutes.getMinutes();
    const departureDateAndTime = new Date(departureDate.getTime());
    departureDateAndTime.setHours(hours);
    departureDateAndTime.setMinutes(minutes);
    return departureDateAndTime;
  } catch (e) {
    console.log(e);
    throw e;
  }
};

export const getQuotaClassRate = (quotaList, classList) => quotaList.reduce((acc, item) => {
  const classItem = classList.reduce((classAcc, classType) => ({
    ...classAcc,
    [classType]: { rate: '' },
  }), {});
  return {
    ...acc,
    [item]: classItem,
  };
}, {});

export const getOrderedQuotaList = (quotaList) => {
  if (isEmpty(quotaList)) {
    return [];
  }
  const ladiesQuotaIndex = quotaList.indexOf(QuotaType.LD.code);
  if (ladiesQuotaIndex === -1) {
    return quotaList;
  }
  const ldArray = quotaList.splice(ladiesQuotaIndex, 1);
  return [...quotaList, ...ldArray];
};

export const promiseTimeout = (ms, promise) => {
  // Create a promise that rejects in <ms> milliseconds
  const timeout = new Promise((resolve, reject) => {
    const id = setTimeout(() => {
      clearTimeout(id);
      reject(new Error('timed out'));
    }, ms);
  });

  // Returns a race between our timeout and the passed in promise
  return Promise.race([
    promise,
    timeout,
  ]);
};

export const getNewVoucherClick = async (dispatch) => {
  try {
    dispatch({
      type: ACTION_SET_VOUCHER_OBJECT,
      data: { voucherLoading: true },
    });
    const res = await fetch2(railsConfig.generateVoucher, {
      method: 'POST',
    });
    const voucherObject = await res.json();
    if (!isEmpty(voucherObject.error)) {
      throw new Error('api errored out');
    }
    dispatch({
      type: ACTION_SET_VOUCHER_OBJECT,
      data: {
        voucherObject: {
          ...voucherObject,
          voucherStatus: voucherState.ACTIVE,
        },
        voucherLoading: false,
      },
    });
    trackNewListingClickEvent('mob_rail_listing_v2_tap_to_win_clicked');
  } catch (e) {
    console.log('error is', e);
    showShortToast(_label('something_went_wrong_try_again_later') + ' Error Code: ' + e?.errorCode );
    Actions.pop();
  }
};

export const refreshListingPage = () => async (dispatch, getState) => {
  dispatch(showIsLoading(true));
  const { railsListing: { originStation, destinationStation, departureDate } } = getState();
  //skywalker start
  try {
    const skyWalkerData = {
      fromCity: originStation.code,
      toCity: destinationStation.code,
      fromDateTime: fecha.format(departureDate,'DD-MM-YYYY HH:MM:ss'),
    };
    skywalkerSearchConnector(skyWalkerData, 'listing');
  } catch (e) {
    console.log('Skywalker error: ', e);
  }
  //skywalker end
  dispatch(getMigratedTrainsList(originStation, destinationStation, departureDate));
};

interface railsListingShowErrorScreenInterface {
  listingErrorMessage: string,
  listingErrorSubtitle: string,
  listingErrorCode?: string,
  recommendedMessage?: string,
  recommendedList?: unknown[],
  noTrainFound?: boolean,
}
export const railsListingShowErrorScreen = ({
  listingErrorMessage,
  listingErrorSubtitle,
  listingErrorCode,
  noTrainFound,
}: railsListingShowErrorScreenInterface) => {
  return {
    type: ACTION_RAILS_LISTING_ERROR,
    data: {
      errorCode: listingErrorCode,
      errorMessage: listingErrorMessage,
      errorSubtitle: listingErrorSubtitle,
      errorFlag: true,
      recommendedMessage: '',
      recommendedList: [],
      noTrainFound,
      trainsList: [],
      minPriceDetails: {},
      minDuration: null,
    },
  };
};

export const connectedTravelApiResponse = async (payload) => {
  const {
    originStation,
    destinationStation,
    departureDate,
    maxLimitSuggestionsPerOption,
    suggestionSource,
    connectedTravelVersion,
  } = payload;
  const body = {
    sourceStationCode: originStation,
    destinationStationCode: destinationStation,
    dateOfJourney: departureDate,
    ...maxLimitSuggestionsPerOption && { maxLimitSuggestionsPerOption },
    ...suggestionSource && { suggestionSource },
    ...connectedTravelVersion && { connectedTravelVersion },
  };
  const res = await fetch2(railsConfig.connectedTraveller, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  const connectedTravelApiResponse = await res.json();
  if (connectedTravelApiResponse?.error || res?.status !== 200) {
    return null;
  }
  return connectedTravelApiResponse;
};

export const getRailsPostHeaders = async () => {
  const header = await botmanSensorHeader();
  return {
    ...header,
    'mmt-sorting-logic': getNewRailsSortingLogic()?.toString(),
  };
};

export const connectedTravellerforDeadEnds =
  (from: unknown, to: unknown, date: Date, listingErrorCode: string) => (dispatch: unknown) => {
    const connectedTravelPokusVal = getIfToShowConnectedTravelV3();
    if (connectedTravelPokusVal) {
      const { code: fromCode } = from || {};
      const { code: toCode } = to || {};
      if (listingErrorCode === 'RAIL_TBS_03_CODE') {
        dispatch(setConnectedTravelData(fromCode, toCode, date, true));
      } else {
        removeEventFromEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedNotShown);
        removeEventFromEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedShown);
        dispatch({
          type: ACTION_SET_CONNECTED_TRAVEL_LIST,
        data: {
          connectedTravelApiResponseJson: null,
        },
      });
    }
    } else {
      dispatch({
        type: ACTION_SET_CONNECTED_TRAVEL_LIST,
        data: {
          connectedTravelApiResponseJson: null,
        },
      });
      updateEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedNotShown);
    }
  };

export const onViewDetailsClicked = (trainData) => async (dispatch, getState) => {
  const { trainNumber } = trainData;
  const {
    railsListing: { trainsListObject },
  } = getState();
  const requiredTrainObject = trainsListObject[trainNumber];
  const { availabilityResponse } = requiredTrainObject;
  if (isEmpty(availabilityResponse)) {
    const { selectedClassType, selectedQuota } = requiredTrainObject;
    dispatch(updateAvailabilityList(trainData, selectedClassType, selectedQuota?.code));
  } else if (!isEmpty(availabilityResponse) && (availabilityResponse.errorMessage || availabilityResponse.error)) {
    const { selectedClassType, selectedQuota } = requiredTrainObject;
    dispatch(updateAvailabilityList(trainData, selectedClassType, selectedQuota?.code));
  }
};

export const initAvailabilityStateOfTrain = (trainNumber,isOtherDayTrainsList = false) => (dispatch, getState) => {
  const {
    railsListing: { trainsListObject,otherDayTrainsListObject },
  } = getState();

  if (!isOtherDayTrainsList) {
    const updatedTrainsListObject = {
      ...trainsListObject,
      [trainNumber]: {
        ...trainsListObject[trainNumber],
        isLoading: true,
        error: false,
        errorMessage: '',
      },
    };
    dispatch({
      type: ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN,
      data: updatedTrainsListObject,
    });
  }
  else {
    const updatedTrains = {
      ...otherDayTrainsListObject,
      [trainNumber]: {
        ...otherDayTrainsListObject[trainNumber],
        isLoading: true,
        error: false,
        errorMessage: '',
      },
    };
    dispatch({
      type: ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN_FOR_ODT,
      data: updatedTrains,
    });
  }
};

const getAvailabilityList = (response) => {
  return get(response, 'originalData.avlDayList', response.avlDayList);
};

const confirmProbablityForNearbyDates = (
  response: { trainName: string; avlDayList: unknown; },
  trainNumber: string,
  quotaSelected: string,
  quotaClassRate: unknown,
  selectedClassType: string,
  trainData: { frmStnCode: string; toStnCode: string; },
  updatedTrainsListObject: unknown,
  selectedBookingDate: unknown,
  isOtherDayTrainsList = false,
) => async (dispatch: unknown, getState: unknown) => {
  try {
    const {
      confirmationGuaranteeConfig: { railsConfirmationGuaranteeOption = 0 } = {},
      freeCancellationConfig: { freeCancellationEnabled = false } = {},
    } = getState().railsListing;

    const request = {
      trainNumber,
      quota: quotaSelected,
      ticketClass: selectedClassType,
      boardingStationCode: trainData.frmStnCode,
      destinationStationCode: trainData.toStnCode,
      trainAvailabilityDayBasisList: getAvailabilityList(response),
      railsConfirmationGuaranteeOption,
      zcEnable: freeCancellationEnabled,
    };
    const url = railsConfig.neabyDatesProbablityUrl;
    const res = await fetch2(url, {
      method: 'POST',
      headers: getDefaultPostHeaders(),
      body: JSON.stringify(request),
    });
    const responseJson = await res?.json();
    // ===========================================================================================
    //      Error handling here is in confusing manner and also error scenarios are not clear
    //      as of now hence notmaking unknown drastic change. BUT
    //      once the error scenarios are clear, error handling should be done in
    //      proper manner with try-catch block.
    // ============================================================================================
    if (!isEmpty(responseJson)) {
      const newTrainsListObject = {
        ...updatedTrainsListObject,
        [trainNumber]: {
          ...updatedTrainsListObject[trainNumber],
          availabilityResponse: {
            ...response,
            ...responseJson,
            avlDayList: responseJson.trainAvailabilityDayBasisList,
          },
          error: responseJson.error ?? false,
          errorMessage: responseJson.errorMessage ?? '',
          quotaClassRate,
          selectedBookingDate,
          isLoading: false,
        },
      };
      if (isOtherDayTrainsList) {
        dispatch({
          type: ACTION_UPDATE_AVAILABILITY_OF_TRAIN_FOR_ODT,
          data: newTrainsListObject,
        });
      }
      else {
        dispatch({
          type: ACTION_UPDATE_AVAILABILITY_OF_TRAIN,
          data: newTrainsListObject,
        });
      }
    } else {
      if (isOtherDayTrainsList) {
        dispatch({
          type: ACTION_UPDATE_AVAILABILITY_OF_TRAIN_FOR_ODT,
          data: updatedTrainsListObject,
        });
      }
      else
      {
      dispatch({
        type: ACTION_UPDATE_AVAILABILITY_OF_TRAIN,
            data: updatedTrainsListObject,
      });
    }
    }
      dispatch(logListingPdtClickEvents(LISTING_PDT_CLICK_EVENTS.NEARBY_DATE_CLICK, { trainData }));
  } catch {
    console.log('error in fetching nearby dates confirmation propbablity');
    dispatch({
      type: ACTION_UPDATE_AVAILABILITY_OF_TRAIN,
      data: updatedTrainsListObject,
    });
  }
};

export const updateAvailabilityList = (trainData, selectedClassType, quotaSelected) =>
  async (dispatch, getState) => {
    try {
      const { trainNumber } = trainData;
      dispatch(initAvailabilityStateOfTrain(trainNumber));
      const { railsListing: { trainsListObject, departureDate } } = getState();


      const response = await
        getAvailabilityResponse(selectedClassType, trainData, quotaSelected, departureDate,null,null,true);
      if (!isEmpty(response.errorMessage)) {
          dispatch({
            type: ACTION_UPDATE_AVAILABILITY_OF_TRAIN,
            data: {
              ...trainsListObject,
              [trainNumber]: {
                ...trainsListObject[trainNumber],
                error: true,
                errorMessage: response.errorMessage,
                isLoading: false,
              },
            },
          });
        return;
      }

      const { avlDayList, originalAvlDayList }  = getAvlDayList(response, trainData);
      const { quotaClassRate } = trainsListObject[trainNumber];
      const { travelplexChatConfig, travelplexTickers } = response?.travelplexConfig || {};
      const newQuotaClassRate = {
        ...quotaClassRate,
        [quotaSelected]: {
          ...quotaClassRate[quotaSelected],
          [selectedClassType]: { rate: response.totalFare },
        },
      };
      const selectedBookingDate = avlDayList[0];
      const updatedTrainsListObject = {
        ...trainsListObject,
        [trainNumber]: {
          ...trainsListObject[trainNumber],
          availabilityResponse: response,
          quotaClassRate: newQuotaClassRate,
          selectedBookingDate: originalAvlDayList[0],
          isLoading: false,
        },
      };
      if (quotaSelected === 'GN') {
        dispatch(setNewTbsAvailability(response, selectedClassType, quotaSelected, trainData));
      }
      dispatch(
        confirmProbablityForNearbyDates(
          response,
          trainNumber,
          quotaSelected,
          newQuotaClassRate,
          selectedClassType,
          trainData,
          updatedTrainsListObject,
          selectedBookingDate,
        ),
      );
      dispatch({
        type: ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG,
        data: {
          travelplexChatConfig,
          travelplexTickers,
        },
      });
    } catch (error) {
      console.error('JS ERROR in updateAvailabilityList', error.message);
    }

  };

export const getNewQuotaClickData = (classType, quotaCode, trainData) => {
  const { trainName, trainNumber } = trainData;
  return `${trainName}|${trainNumber}|${quotaCode}|${classType}|NA|NA`;
};

export const onNewQuotaClicked = (quotaSelected, trainData) => (dispatch, getState) => {
  const { railsListing: { trainsListObject } } = getState();
  const selectedQuota: QuotaTypeInterface = getQuota(quotaSelected);
  const { trainNumber } = trainData;
  const { selectedClassType } = trainsListObject[trainNumber];
  trackNewListingEvent(
    `mob_rail_listing_v2_quota_${selectedQuota?.code}_applied`,
    getNewQuotaClickData(selectedClassType, selectedQuota?.code, trainData),
  );
  const updatedTrainsListObject = {
    ...trainsListObject,
    [trainNumber]: {
      ...trainsListObject[trainNumber],
      selectedQuota,
    },
  };
  dispatch({
    type: ACTION_UPDATE_QUOTA_SELECTED_OF_TRAIN,
    data: updatedTrainsListObject,
  });
    dispatch(updateAvailabilityList(trainData, selectedClassType, selectedQuota?.code));
};
export const getNewClassTypeClickData = (classType, quotaCode, trainData) => {
  const { trainName, trainNumber } = trainData;
  return `${trainName}|${trainNumber}|${quotaCode}|${classType}|NA|NA`;
};

export const onNewClassTypeClicked = (classTypeSelected, trainData) => (dispatch, getState) => {
  const {
    railsListing: { trainsListObject },
  } = getState();
  const { trainNumber } = trainData;
  const { selectedQuota } = trainsListObject[trainNumber];
  trackNewListingEvent(
    `mob_rail_listing_v2_class_${classTypeSelected}_selected`,
    getNewClassTypeClickData(classTypeSelected, selectedQuota?.code, trainData),
  );
  const updatedTrainsListObject = {
    ...trainsListObject,
    [trainNumber]: {
      ...trainsListObject[trainNumber],
      selectedClassType: classTypeSelected,
    },
  };
  dispatch({
    type: ACTION_UPDATE_CLASS_TYPE_SELECTED_OF_TRAIN,
    data: updatedTrainsListObject,
  });
  dispatch(updateAvailabilityList(trainData, classTypeSelected, selectedQuota?.code));
};

export const closeStationChangeAlert = () => ({
  type: ACTION_STATION_CHANGE_ALERT,
  data: {
    displayStationChangeAlert: false,
  },
});

export const closeConfirmOptionsBottomSheet = () => ({
  type: ACTION_CONFIRM_OPTIONS_ALERT,
  data: {
    displayConfirmOptionsBottomSheet: false,
  },
});

export const toggleTrainScheduleBottomSheet = (
  showBottomSheet: boolean,
  trainNumber = '',
  isCnfOptionTrain = false,
) => ({
  type: ACTION_TOGGLE_TRAIN_SCHEDULE_BOTTOM_SHEET,
  data: {
    displayTrainScheduleBottomSheet: showBottomSheet,
    trainNumberForSchedule: trainNumber,
    isCnfOptionsTrainSchedule: isCnfOptionTrain,
  },
});

export const toggleConnectedTravelBottomSheet = (
  showBottomSheet: boolean,
  proceedToTraveler: boolean,
) => (dispatch) => {
  if (!showBottomSheet && !proceedToTraveler) {
    trackClickEventProp61(RAILS_LISTING, LISTING_CONNECTED_TRAVEL_EVENTS.bottomSheetDismissClick);
  }

  dispatch({
    type: ACTION_TOGGLE_CONNECTED_TRAVEL_BOTTOM_SHEET,
    data: {
      displayConnectedTravelBottomSheet: showBottomSheet,
    },
  });
};

export const dispatchConnectedBottomSheetData = (data: unknown) => (dispatch) => {
  dispatch({
    type: ACTION_CONNECTED_TRAVEL_BOTTOM_SHEET_DATA,
    data: {
      connectedBottomSheetData: data,
    },
  });
  return;
};

export const verifyTatkalExists = availableQuotaList => (
  availableQuotaList.some(item => (item === 'TQ'))
);

export const tuneRailsListingLoad = (originStation, destinationStation, departureDate) => () => {
  tuneRailsListingLoadTracker({
    from: originStation.cityName,
    to: destinationStation.cityName,
    date: fecha.format(departureDate, DATE_MONTH_YEAR_FORMAT),
    travelByAc: 'N',
  });
};

const initAbConfigForConfirmationChance = () => (dispatch, _getState) => {
  const railsShowConfirmationChance = getPokusConfig(
    PokusLobs.RAIL,
    AbConfigKeyMappings.railsShowConfirmationChance,
    false,
  );  dispatch({
    type: ACTION_INIT_SHOW_CONFIRMATION_CHANCE_AB,
    data: {
      railsShowConfirmationChance: isUndefined(railsShowConfirmationChance) ? false : railsShowConfirmationChance,
    },
  });
};

export const initOmniture = () => (dispatch) => {
  dispatch({
    type: ACTION_RAILS_LISTING_OMNITURE,
    data: {
      newListingLoadOmitureDone: false,
      confirmationProbabilityOmitureDone: false,
      noTrainsViewOmnitureDone: false,
      noDirectTrainsFoundOmnitureDone: false,
      tuneTrackerOmnitureDone: false,
    },
  });
};

let prevUrlListing;

export const initNewListing = (_props) => (dispatch) => {
  if (Platform.OS === 'web') {
    if (prevUrlListing === window.location.href) {
      return;
    }
      prevUrlListing = window.location.href;

  }
  dispatch(initOmniture());
  dispatch(initAbConfigForConfirmationChance());
};
export const changeDepartureDate = (date, noApiCall) => (dispatch, getState) => {
  const {
    railsListing: { originStation, destinationStation },
  } = getState();
  dispatch(logListingPdtClickEvents(LISTING_PDT_CLICK_EVENTS.LISTING_DATE_CHANGE_CLICK));
  dispatch({
    type: ACTION_DATE_CHANGED,
    data: date,
  });
  dispatch({
    type: ACTION_DATE_SELECTED,
    data: date,
  });
  if (!noApiCall) {
    dispatch(getMigratedTrainsList(originStation, destinationStation, date));
  }
};


export const closeClassAvailability = () => ({
  type: ACTION_CLOSE_CLASS_AVAILABILITY,
  data: {
    showClassAvailability: false,
    displayStationChangeAlert: false,
  },
});

export const getDefaultPostHeaders = () => ({
  Accept: 'application/json',
  'Content-Type': 'application/json',
});

export const getAvlDayList = (response, _selectedTrainInfo) => {
  const avlDayList = response?.avlDayList || [];
  const originalAvlDayList = response?.originalData?.avlDayList || avlDayList;
  return {
    avlDayList,
    originalAvlDayList,
  };
};

export const getDefaultAvlStatus = (avlDayList, selectedDate) => {
  let avlStatus = null;
  avlDayList.forEach((item) => {
    if (item.availablityDate === selectedDate) {
      avlStatus = item.availablityStatus;
    }
  });
  return avlStatus;
};

export const isBookNowEnabled = selectedBookingDate => (
  !(isEmpty(selectedBookingDate) || isEmpty(selectedBookingDate.availablityStatus) ||
    selectedBookingDate.availablityStatus.startsWith(AvailabilityStatusTypes.NOTAVAILABLE.code) ||
    selectedBookingDate.availablityStatus === AvailabilityStatusTypes.DEPARTED.code ||
    selectedBookingDate.availablityStatus.startsWith(AvailabilityStatusTypes.REGRET.code) ||
    selectedBookingDate.availablityStatus.startsWith(AvailabilityStatusTypes.TRAIN_CANCELLED.code) ||
    selectedBookingDate.availablityStatus === AvailabilityStatusTypes.CLASS_NOT_EXIST.code)
);

export const setLandingData = landingData => ({
  type: ACTION_SET_PPBOOKING_LANDING_DATA,
  data: landingData,
});

export const goToTravelersPage = (dispatch, getState) => {
  const { railsListing: { selectedTrainInfo, departureDate, selectedClassType, selectedQuota } } = getState();
  dispatch(trackEvents);
  dispatch(closeStationChangeAlert());
  dispatch(closeConfirmOptionsBottomSheet());
  Actions.travelers({
    originStation: {
      cityName: selectedTrainInfo.frmStnName,
      code: selectedTrainInfo.frmStnCode,
      stationName: selectedTrainInfo.frmStnName,
    },
    destinationStation: {
      cityName: selectedTrainInfo.toStnName,
      code: selectedTrainInfo.toStnCode,
      stationName: selectedTrainInfo.toStnName,
    },
    departureDate,
    trainNumber: selectedTrainInfo.trainNumber,
    classCode: selectedClassType,
    quota: selectedQuota?.code,
    inNormalFlow: true,
  });
};

export const trackEvents = (dispatch, getState) => {
  const {
    railsListing: {
      selectedClassType: classValue,
      selectedQuota,
      selectedBookingDate,
      selectedTrainInfo,
      pageSource,
      showBnpp,
    },
  } = getState();
  const { trainName, trainNumber } = selectedTrainInfo;
  const { availablityStatus: selectedAvlStatus } = selectedBookingDate;

  const commonDataForTravelerTracking = `${trainName}|${trainNumber}|${selectedQuota?.code}|${classValue}|${selectedAvlStatus}|NA`;
  trackTravellerPageLoad(commonDataForTravelerTracking, pageSource, showBnpp);
};

export const saveConfirmationGuaranteeConfig = (
  railsConfirmationGuaranteeOption: number,
  hasWidgetError = false,
) => async (dispatch: unknown, getState: unknown) => {
  const { confirmationGuaranteeConfig = {} } = getState().railsListing;  const confirmationGuaranteeConfigObj = {
    ...confirmationGuaranteeConfig,
    railsConfirmationGuaranteeOption,
  };

  if (hasWidgetError) {
    confirmationGuaranteeConfigObj.confirmationGuaranteeWidget = null;
    confirmationGuaranteeConfigObj.isInit = false;
  }

  dispatch({
    type: SAVE_CONFIRMATION_GUARANTEE_POKUS_OPTION,
    data: {
      confirmationGuaranteeConfig: confirmationGuaranteeConfigObj,
    },
  });
};

export const saveFreeCancellationConfig = (
  freeCancellationEnabled: boolean,
) => (dispatch: unknown, getState: unknown) => {  const { freeCancellationConfig = {} } = getState().railsListing;
  dispatch({
    type: SAVE_FREE_CANCELLATION_CONFIG,
    data: {
      freeCancellationConfig: {
        ...freeCancellationConfig,
        freeCancellationEnabled,
      },
    },
  });
};
interface RailofyZcShown {
  railofyShown: boolean;
  zcShown: boolean
}

export const saveRailofyZcShown = (data: RailofyZcShown) => (dispatch: unknown, getState) => {
  const {
    railsListing: { showBnpp },
  } = getState();
  const canShowBNPP = getIfCanShowBNPP();
  const { railofyShown } = data;
  dispatch({
    type: SAVE_RAILOFY_ZC_SHOWN,
    data: {
      railofyZcShown: {
        railofyShown,
        zcShown: true,
        bnppShown: canShowBNPP && Boolean(showBnpp),
      },
    },
  });
};


/**
 *
 * Pdt related actions
 *
 */

export const getListingPagePdt = (
  searchData: unknown,
  railsLanding: unknown,
  railsListing: unknown,
  selectedClass: unknown,
) => {  const {originStation, destinationStation, departureDate} = railsListing;
  const searchContextData = getCommonSearchContextData(railsLanding);
  const data = {
    searchContext: {
      frmStnName: originStation?.cityName,
      frmStnCode: originStation?.code,
      toStnName: destinationStation?.cityName,
      toStnCode: destinationStation?.code,
      journeyDate: departureDate,
    },
    ...searchContextData,
  };
  if (isEmpty(searchData)) {searchData = data;}
  return {
    searchContextData : data,
    searchData,
    selectedClass,
    };
};
export const trackPdtListingLoad = async (
  data: unknown,
  dataType: string,
  listingErrorCodeString: string | null,
) => {
  const commonPdt = await getCommonPdtData();
  const pdtData = {
    ...commonPdt,
    ...data,
    listingErrorCodeString,
  };
  RailsPdtListingHelper.trackPageLoad(pdtData, dataType);
};

export const logErrorToFirebase = async (jsonData) => {
  try {
    if (
      Platform.OS === 'android' &&
      getRailsPokusConfig(RailsAbConfig.railsReportCustomFirebaseErrors, false)
    ) {
      const internetInfo = await getCurrentInternetConnectionAndApproxSpeed();
      jsonData.internetInfo = internetInfo ?? 'NA';
      RailsModule?.logRailsListingError &&
        RailsModule?.logRailsListingError(JSON.stringify(jsonData));
    }
  } catch (error) {
    //ignore
  }
};

export const setConnectedTravelData =
  (fromCode: string, toCode: string, date: Date, showNoTrain = false) =>
  async (dispatch: unknown) => {
    try {
      const deptDateConnectedTravellers = fecha.format(date, DATE_MONTH_YEAR_FORMAT);
      const ctV2ConfigResponse = (await getConfigStore(configKeys.RAILS_CT_V2)) || {};
      const showConnectedTravelV2 = getIfToShowConnectedTravelV3();
      const connectedTravelApiBody = {
        originStation: fromCode,
        destinationStation: toCode,
        departureDate: deptDateConnectedTravellers,
        suggestionSource:
          RAILS_CONNECTED_TRAVEL_POKUS_CONSTANTS?.[getRailsCtSource()]?.backendKey ||
          RAILS_CONNECTED_TRAVEL_POKUS_CONSTANTS[3].backendKey,
        maxLimitSuggestionsPerOption: ctV2ConfigResponse?.maxLimitSuggestionsPerOption || 3,
        connectedTravelVersion: 'V2',
      };
      if (showNoTrain) {
        dispatch({
          type: ACTION_SET_CONNECTED_TRAVEL_LIST,
          data: {
            showLoaderForRailsConnectedTravel: true,
          },
        });
      }

      let connectedTravelApiResponseJson = await connectedTravelApiResponse(connectedTravelApiBody);
      if (!isEmpty(connectedTravelApiResponseJson)) {
        updateEvar47or97Variable(
          RAILS_CONNECTED_TRAVEL_POKUS_CONSTANTS?.[getRailsCtSource()]?.omnitureKey,
        );
        connectedTravelApiResponseJson = {
          ...connectedTravelApiResponseJson,
          date: fecha.format(date, DOUBLE_DIGIT_DATE_WITH_MONTH_FORMAT),
          twoTrains: connectedTravelApiResponseJson?.connectedTravelOptions?.filter((item) => {
            return item?.optionType === 'TWO_TRAINS';
          }),
          trainAndBus: connectedTravelApiResponseJson?.connectedTravelOptions?.filter((item) => {
            return item?.optionType === 'TRAIN_AND_BUS';
          }),
        };
        if (showConnectedTravelV2 === connectedTravelVariants.INTRODUCING_CONNECTED_TRAVEL) {
          trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, LISTING_CONNECTED_TRAVEL_EVENTS.ctv2);
        } else if (showConnectedTravelV2 === connectedTravelVariants.SHOWN_EXPANDED_CARD) {
          trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, LISTING_CONNECTED_TRAVEL_EVENTS.ctv1);
        } else if (showConnectedTravelV2 === connectedTravelVariants.MOVED_DIRECT_LISTING) {
          trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, LISTING_CONNECTED_TRAVEL_EVENTS.ctv3);
        } else {
          trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, LISTING_CONNECTED_TRAVEL_EVENTS.ctv0);
        }
        updateEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedShown);
      } else {
        updateEvar47or97Variable(LISTING_CONNECTED_TRAVEL_EVENTS.connectedNotShown);
      }

      if (showNoTrain) {
        trackGenericEvar47or97Event(LISTING_PAGE_TRACKING_KEY, '');
        dispatch({
          type: ACTION_SET_CONNECTED_TRAVEL_LIST,
          data: {
            connectedTravelApiResponseJson,
            showLoaderForRailsConnectedTravel: false,
        },
        });
      }
      dispatch({
        type: ACTION_SET_CONNECTED_TRAVEL_LIST,
        data: {
          connectedTravelApiResponseJson,
        },
      });
    } catch (e) {
      if (showNoTrain) {
        dispatch({
          type: ACTION_SET_CONNECTED_TRAVEL_LIST,
          data: {
            showLoaderForRailsConnectedTravel: false,
          },
        });
      }
      trackGenericEvar47or97Event(LISTING_PAGE_TRACKING_KEY, '');
      console.error('Error in fetching connected Travel response: ', e);
    }
  };

export const onAvailabilityDateClickedV2 =
  (
    selectedDate: FutureSelectedDateData,
    trainData: FutureSelectedTrain,
    selectedClassType: string,
    selectedQuota: string,
    isConfirmedOption: boolean = false,
  ) =>
  (dispatch: unknown, getState: unknown) => {
  const { trainNumber, avlClasses } = trainData;
  const sectionIdentifier = trainData.sectionIdentifier || '';
  const selectedCardHash = getSelectedCardHash(trainNumber, selectedClassType, selectedQuota, sectionIdentifier);
  let updatedTrainDataForSelection = updateTimeInfoInSelectedTrain(trainData, selectedDate.availablityDate);
  const { railsListing: { nearbyDatesTrainsListObject } } = getState();
    const { availabilityResponse: response = {} } =
      nearbyDatesTrainsListObject?.[selectedCardHash] || {};
    const { avlDayList, originalAvlDayList } = getAvlDayList(response, trainData);
    const selectedAvlStatus = getDefaultAvlStatus(avlDayList, selectedDate.availablityDate);
  const selectedBookingDate = selectedDate;
    const quota: QuotaTypeInterface = getQuota(selectedQuota);

    if (!isBookNowEnabled(selectedBookingDate)) {
      const { railsListing } = getState();
      const { notAvailableCard } = railsListing;
      if (!notAvailableCard) {
        showShortToast(_label('booking_not_available_for_this_train'));
      }
      return;
    }
    const { insuranceCharge, totalCollectibleAmount, totalFare, baseFare, applicableFoodTypes } =
      response;
    const { stationChangeSign, clusterTrain, odt } = trainData;
    if (odt) {
      updateEvar47or97Variable(ODT_EVENTS.odt_chosen);
      trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, ODT_EVENTS.odt_clicked);
    }
    if (isConfirmedOption) {
      const currentDayList = avlDayList.find(
        (avl) => avl.availabilityKey === selectedDate.availabilityKey,
      );
      const confirmedOption = currentDayList?.confirmedOption;
      updatedTrainDataForSelection = getTrainDataForNearbyDatesConfirmedOption(
        updatedTrainDataForSelection,
        confirmedOption,
      );
    }

  const displayStationChangeAlert = getStationChangeAlertBottomsheetVal(clusterTrain, stationChangeSign);
  const dispatchData = {
    applicableFoodTypes,
    selectedTrainInfo: updatedTrainDataForSelection,
    selectedClassType,
    allAvailableClasses: avlClasses,
    selectedQuota: quota,
    selectedBookingDate,
    originalSelectedBookingDate: originalAvlDayList[0],
    selectedAvlStatus,
    avlDayList,
    ...response?.bkgCfg,
    insuranceCharge,
    totalCollectibleAmount,
    displayStationChangeAlert,
    totalFare,
    baseFare,
  };

  const railofyZcShown = {
    railofyShown: Boolean(selectedDate?.confirmationGuaranteeText),
    zcShown: Boolean(selectedDate.freeCancellationText),
  };

  dispatch(saveRailofyZcShown(railofyZcShown));
  dispatch({
    type: ACTION_SET_DATA_FOR_TRAVELERS_PAGE,
    data: dispatchData,
  });
  if (!displayStationChangeAlert) {
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_CARD_CLICK);
    removeEventFromEvar99Variable(RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_CARD_CLICK);
    if (sectionIdentifier === 'booked') {
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_CARD_CLICKED,
      );
      trackGenericEvar99Event(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_BOOKED_CARD_CLICK,
      );
    } else if (sectionIdentifier === 'searched') {
      trackGenericEvar99Event(
        LISTING_PAGE_TRACKING_KEY,
        RAIL_EVENTS.LISTING.RAILS_LISTING_PREV_SEARCHED_CARD_CLICK,
      );
      let cardPosition = 1;
      if (trainData.uniqueCardId) {
        const idParts = trainData.uniqueCardId.split('_');
        if (idParts.length >= 3) {
          cardPosition = parseInt(idParts[2], 10) + 1;
        }
      }
      trackClickEventProp61(
        LISTING_PAGE_TRACKING_KEY,
        `mob_rail_listing_prevsearched_card_clicked_pos${cardPosition}`,
      );
    }
    dispatch(goToTravelersPage);
  }
};


export const initNearbyDatesTrainList = (
  trainNumber,
  className,
  quotaSelected,
  sectionIdentifier,
) => (dispatch, getState) => {
  const {
    railsListing: { nearbyDatesTrainsListObject },
  } = getState();

  const selectedCardHash = getSelectedCardHash(trainNumber, className, quotaSelected, sectionIdentifier);

  const updatedTrainsListObject = {
    ...nearbyDatesTrainsListObject,
    [selectedCardHash]: {
      ...nearbyDatesTrainsListObject[selectedCardHash],
      isLoading: true,
      error: false,
      errorMessage: '',
    },
  };
  dispatch({
    type: ACTION_INT_NEARBY_DATES_LIST,
    data: updatedTrainsListObject,
  });
};

export const toggleNearbyDatesList = (
  showNearbyDates: boolean,
  selectedTrainNumber: string,
  selectedClassType: string,
  selectedQuota: string,
  sectionIdentifier?: string,
) => (dispatch, getState) => {
  const {
    railsListing: {
      nearbyDatesInfo: {
        selectedNearbyCardsHashList,
        selectedTrainsList,
      },
    },
  } = getState();
  let updatedCardsHashList = [...selectedNearbyCardsHashList];
  let updatedTrainsList = [...selectedTrainsList];
  const currentCardHash = getSelectedCardHash(selectedTrainNumber, selectedClassType, selectedQuota, sectionIdentifier);

  if (showNearbyDates) {
    updatedCardsHashList = updatedCardsHashList.filter(hash => !hash.startsWith(`${selectedTrainNumber}*`));
    updatedCardsHashList.push(currentCardHash);
    if (!updatedTrainsList.includes(selectedTrainNumber)) {
      updatedTrainsList.push(selectedTrainNumber);
    }
  } else {
    updatedCardsHashList = updatedCardsHashList.filter(hash => hash !== currentCardHash);

    const anyOtherCardForThisTrainExists = updatedCardsHashList.some(hash => hash.startsWith(`${selectedTrainNumber}*`));
    if (!anyOtherCardForThisTrainExists) {
      updatedTrainsList = updatedTrainsList.filter(train => train !== selectedTrainNumber);
    }
  }

  dispatch({
    type: ACTION_TOGGLE_NEARBY_DATES_LIST,
    data: {
      selectedNearbyCardsHashList: updatedCardsHashList,
      selectedTrainsList: updatedTrainsList,
    },
  });
};

const getNearbyDatesAvlList = (availabilities, confirmedOptions) => {
  const avlList = [];

  for (const availabilityDate of Object.keys(availabilities ?? {})) {
    const currentAvailability = availabilities[availabilityDate]?.availabilityData;
    currentAvailability.availabilityKey = availabilityDate;
    if (!isEmpty(confirmedOptions) && confirmedOptions[availabilityDate]) {
      currentAvailability.confirmedOption = confirmedOptions[availabilityDate]?.availabilityData;
      currentAvailability.confirmedOption.availabilityKey = availabilityDate;
    }
    avlList.push(currentAvailability);
  }

  return avlList;
};

const getNearbyDatesAvlResponse = (avlDayList) => {
  const {
    travelAdvisoryDetails,
    insuranceCharge,
    totalCollectibleAmount,
    totalFare,
    baseFare,
    bkgCfg,
    applicableFoodTypes,
  } = avlDayList[0];

 const availabilityResponse = {
  travelAdvisoryDetails,
  insuranceCharge,
  totalCollectibleAmount,
  totalFare,
  baseFare,
  bkgCfg,
  applicableFoodTypes,
  avlDayList,
 };

 return availabilityResponse;
};

const getNearbyDatesConfirmedOptions =
  (trainData, selectedClassType: string, quotaSelected: string) => async (dispatch, getState) => {
    try {
      const {
        railsListing: {
          nearbyDatesTrainsListObject,
          departureDate,
        },
      } = getState();
      const { trainNumber, possibleDates = [], odt } = trainData;
      const sectionIdentifier = trainData.sectionIdentifier || '';

      const selectedCardHash = getSelectedCardHash(trainNumber, selectedClassType, quotaSelected, sectionIdentifier);
      const deptDate = odt ? possibleDates?.[0] : fecha.format(departureDate, 'YYYYMMDD');

      const urlWithParams = getNearbyDatesurl({
        fromCode: trainData.frmStnCode,
        toCode: trainData.toStnCode,
        deptDate,
        trainNumber,
        selectedClassType,
        quotaSelected,
        fromStation: trainData.frmStnName,
        toStation: trainData.toStnName,
        onlyConfirmedOptions: true,
      });

      const header = await getRailsPostHeaders();
      const res = await fetch2(urlWithParams, {
        headers: header,
      });
      const responseJson = await res.json();

      if (responseJson?.errorCode || isEmpty(responseJson?.confirmedOptions)) {
        console.error('Error in getting nearby dates confirmed options data:', responseJson?.errorMessage);
        return;
      }

      const currAvlDayList = nearbyDatesTrainsListObject[selectedCardHash]?.availabilityResponse?.avlDayList;

      const avlDayList = getNearbyDatesAvlList(currAvlDayList, responseJson?.confirmedOptions);
      const availabilityResponse =  getNearbyDatesAvlResponse(avlDayList);
      const {travelplexChatConfig, travelplexTickers} = responseJson?.travelplexConfig || {};

      const updatedTrainsListObject = {
        ...nearbyDatesTrainsListObject,
        availabilityResponse,
      };

      dispatch({
        type: ACTION_UPDATE_NEARBY_DATES_LIST,
        data: updatedTrainsListObject,
      });

      dispatch({
        type: ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG,
        data: {
          travelplexChatConfig,
          travelplexTickers,
        },
      });

    } catch (e) {
      console.error('Error in getting nearby dates confirmed options data: ',e);
    }
};

export const updateNearbyDatesInfo = (trainData, selectedClassType: string, quotaSelected: string) =>
  async (dispatch, getState) => {

    const { trainNumber, possibleDates = [], odt } = trainData;
    const sectionIdentifier = trainData.sectionIdentifier || '';
    const selectedCardHash = getSelectedCardHash(trainNumber, selectedClassType, quotaSelected, sectionIdentifier);
    dispatch(initNearbyDatesTrainList(trainNumber, selectedClassType, quotaSelected, sectionIdentifier));
    const {
      railsListing: { nearbyDatesTrainsListObject, departureDate },
    } = getState();
    const currentTrainListObject = nearbyDatesTrainsListObject[selectedCardHash];
    try {

      const deptDate = odt ? possibleDates?.[0] : fecha.format(departureDate, 'YYYYMMDD');

      const urlWithParams = getNearbyDatesurl({
        fromCode: trainData.frmStnCode,
        toCode: trainData.toStnCode,
        deptDate,
        trainNumber,
        selectedClassType,
        quotaSelected,
        fromStation: trainData.frmStnName,
        toStation: trainData.toStnName,
        onlyConfirmedOptions: false,
      });

      const header = await getRailsPostHeaders();
      const res = await fetch2(urlWithParams, {
        headers: header,
      });
      const responseJson = await res.json();

      const { travelplexChatConfig, travelplexTickers } = responseJson?.travelplexConfig || {};


      let updatedTrainsListObject = {};

      if (res?.status !== 200 || responseJson?.errorCode) {
        updatedTrainsListObject = {
          ...nearbyDatesTrainsListObject,
          [selectedCardHash]: {
            ...currentTrainListObject,
            error: true,
            errorMessage: responseJson?.errorMessage || _label('something_went_wrong'),
            isLoading: false,
            showRetryIcon: responseJson?.canRetryError,
          },
        };
      } else {
        const avlDayList = getNearbyDatesAvlList(responseJson?.availabilities, responseJson?.confirmedOptions);
        const availabilityResponse =  getNearbyDatesAvlResponse(avlDayList);
        const responseFrom = responseJson?.availabilityResponseFrom;
        let trackingKey = '';
        if (responseFrom) {
          trackingKey = `nb_${responseFrom}`;
        }
        trackEventAfterRemovingOlderEvent(
          LISTING_PAGE_TRACKING_KEY,
          trackingKey,
          trackingKey,
        );
        if (currentTrainListObject?.showRetryIcon) {
          trackEvar22Event(LISTING_PAGE_TRACKING_KEY, NEARBY_DATES_EVENTS.retrySuccess);
        }

        updatedTrainsListObject = {
          ...nearbyDatesTrainsListObject,
          [selectedCardHash]: {
            ...currentTrainListObject,
            availabilityResponse,
            selectedBookingDate: avlDayList[0],
            isLoading: false,
          },
        };
      }

      dispatch({
        type: ACTION_UPDATE_NEARBY_DATES_LIST,
        data: updatedTrainsListObject,
      });

      dispatch({
        type: ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG,
        data: {
          travelplexChatConfig,
          travelplexTickers,
        },
      });

      if (responseJson?.shouldCallConfirmedOptionApi) {
        dispatch(getNearbyDatesConfirmedOptions(trainData, selectedClassType, quotaSelected));
      }
    } catch (error) {

      const updatedTrainsListObject = {
        ...nearbyDatesTrainsListObject,
        [selectedCardHash]: {
          ...currentTrainListObject,
          error: true,
          errorMessage: _label('inventory_availability_retry'),
          isLoading: false,
          showRetryIcon: true,
        },
      };

      dispatch({
        type: ACTION_NEARBY_DATES_ERROR,
        data: updatedTrainsListObject,
      });

      console.error('JS ERROR in updateNearbyDatesInfo: ', error);
    }
  };

export const nearbyDatesCardClick =
  (trainData, selectedClassType: string, selectedQuota: string) => async (dispatch, getState) => {
    try {
      const { trainNumber } = trainData;
      const sectionIdentifier = trainData.sectionIdentifier || '';
      const selectedCardHash = getSelectedCardHash(trainNumber, selectedClassType, selectedQuota, sectionIdentifier);
    const {
      railsListing: { nearbyDatesTrainsListObject },
    } = getState();
    const requiredTrainObject = nearbyDatesTrainsListObject[selectedCardHash];
    const availabilityResponse = requiredTrainObject?.availabilityResponse;
    const isResponeLoading = requiredTrainObject?.isLoading;
    if (requiredTrainObject?.showRetryIcon) {
      trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, NEARBY_DATES_EVENTS.retryClick);
    }
    if ((isEmpty(availabilityResponse) || availabilityResponse?.error) && !isResponeLoading) {
      dispatch(updateNearbyDatesInfo(trainData, selectedClassType, selectedQuota));
    }
    } catch (error) {
      console.error('Error in nearbyDatesCardClick:', error);
    }
  };

export const handleNearByDatesConfirmedOptionsClick =
  (
    selectedDate: FutureSelectedDateData,
    trainData: FutureSelectedTrain,
    selectedClassType: string,
    selectedQuota: string,
  ) =>
  (dispatch, getState) => {
    const { trainNumber, odt } = trainData;
    const sectionIdentifier = trainData.sectionIdentifier || '';
    const selectedCardHash = getSelectedCardHash(trainNumber, selectedClassType, selectedQuota, sectionIdentifier);
    const {
      railsListing: {
        nearbyDatesTrainsListObject,
        trainsList,
        otherDayTrainsList,
        alternateAvailabilityResponse,
      },
    } = getState();
    const {
      availabilityResponse: { avlDayList },
    } = nearbyDatesTrainsListObject[selectedCardHash];

    const currentDayList = avlDayList.find(
      (avl) => avl.availabilityKey === selectedDate.availabilityKey,
    );
    const confirmedOption = currentDayList?.confirmedOption;

    const { confirmedOptionData, nearbyDatesAlternateAvlData } =
      getDataForConfirmedOptionsBottomSheet(
        confirmedOption,
        trainData,
        selectedClassType,
        selectedQuota,
      );
    let updatedTrainInfo = {};
    if (odt) {
      updatedTrainInfo = getDataForConfirmedOptions({
        availabilityItem: confirmedOptionData,
        trainData,
        trainsList: otherDayTrainsList,
      });
    } else {
      updatedTrainInfo = getDataForConfirmedOptions({
        availabilityItem: confirmedOptionData,
        trainData,
        trainsList,
      });
    }
    updatedTrainInfo = {
      ...updatedTrainInfo,
      sectionIdentifier: sectionIdentifier,
      selectedCardTrainNumber: trainData.trainNumber,
    };

    dispatch({
      type: ACTION_CONFIRM_OPTIONS_ALERT,
      data: {
        alternateAvailabilityResponse: {
          ...alternateAvailabilityResponse,
          nearbyDatesAlternateAvlList:[
            nearbyDatesAlternateAvlData,
          ],
        },
        selectedClassType: `${selectedClassType}-${ALTERNATE_LISTING_IDENTIFIER}`,
        selectedQuota: getQuota(selectedQuota),
        selectedTrainInfo: updatedTrainInfo,
        isNearbyConfirmedOption: true,
      },
    });

    dispatch({
      type: ACTION_CONFIRM_OPTIONS_ALERT,
      data: {
        displayConfirmOptionsBottomSheet: true,
      },
    });
};

export const showSeatLockBottomSheet = () => ({
  type: ACTION_SEAT_LOCK_BOTTOMSHEET,
  data: { displaySeatLockBottomSheet: true },
});

export const closeSeatLockBottomSheet = () => ({
  type: ACTION_SEAT_LOCK_BOTTOMSHEET,
  data: { displaySeatLockBottomSheet: false },
});

export const handleNearbyDatesTtuCardClick =
  (
    selectedDate: FutureSelectedDateData,
    trainData: FutureSelectedTrain,
    selectedClassType: string,
    selectedQuota: string,
  ) =>
  async (dispatch, getState) => {
    const { trainNumber } = trainData;
    const sectionIdentifier = trainData.sectionIdentifier || '';
    const selectedCardHash = getSelectedCardHash(trainNumber, selectedClassType, selectedQuota, sectionIdentifier);
    const {
      railsListing: { nearbyDatesTrainsListObject },
    } = getState();
    let currentTrainListObject = nearbyDatesTrainsListObject[selectedCardHash];
    const {
      availabilityResponse: { avlDayList },
    } = currentTrainListObject;

    try {
      const departureDate = fecha.parse(selectedDate.availablityDate, DATE_MONTH_YEAR_FORMAT);
      const responseJson = await getAvailabilityResponse(
        selectedClassType,
        trainData,
        selectedQuota,
        departureDate,
        null,
        null,
        false,
      );

      if (isEmpty(responseJson?.errorMessage)) {
        const responseAvlList = responseJson?.avlDayList[0];
        const updatedAvlDayList = avlDayList.map((avl) =>
          avl.availablityDate === selectedDate.availablityDate ? { ...responseAvlList } : avl,
        );
        currentTrainListObject = {
          ...currentTrainListObject,
          availabilityResponse: {
            ...currentTrainListObject.availabilityResponse,
            avlDayList: updatedAvlDayList,
          },
        };
        dispatch({
          type: ACTION_UPDATE_NEARBY_DATES_TTU,
          data: { currentTrainListObject, selectedCardHash },
        });
        trackClickEventProp61(LISTING_PAGE_TRACKING_KEY, NEARBY_DATES_EVENTS.tapToUpdateClick);
      } else {
        showShortToast(_label(responseJson?.errorMessage));
        trackClickEventProp61(
          LISTING_PAGE_TRACKING_KEY,
          NEARBY_DATES_EVENTS.tapToUpdateClickOutsideARP,
        );
      }
    } catch (e) {
      showShortToast(_label('something_went_wrong'));
      console.error('Error in fetching nearby dates ttu response: ', e);
    }
  };

  export const displayAvailDepletionListingBottomSheet = (value: boolean) => (dispatch) => {
    dispatch({
      type: ACTION_DISPLAY_AVAIL_DEPLETION_BOTTOM_SHEET,
      data: {
        displayAvailDepletionBottomSheet: value,
      },
    });
    if (value) {
      trackNewListingClickEvent(RAIL_EVENTS.LISTING.RAILS_AVAIL_DEPLETION_BS_SHOWN);
    }
};

export const getPreviousSearchPosition =
  (position: number | null) =>
  (dispatch: unknown): number => {
    const searchPosition = position || 0;

    dispatch({
      type: ACTION_GET_PREVIOUS_SEARCH_POSITION,
      data: {
        previousSearchPosition: searchPosition,
      },
    });

    return searchPosition;
  };

export const getPreviousBookedPosition =
  (position: number | null) =>
  (dispatch: unknown): number => {
    const bookedPosition = position || 0;

    dispatch({
      type: ACTION_GET_PREVIOUS_BOOKED_POSITION,
      data: {
        previousBookedPosition: bookedPosition,
      },
    });

    return bookedPosition;
  };

export const getPreviousSearchedHeading =
  (heading: string | null) =>
  (dispatch: unknown): string => {
    const headingText = heading || _label('prev_search_text');

    dispatch({
      type: ACTION_GET_PREVIOUS_SEARCHED_HEADING,
      data: {
        previousSearchedHeading: headingText,
      },
    });

    return headingText;
  };

export const getPreviousBookedHeading =
  (heading: string | null) =>
  (dispatch: unknown): string => {
    const headingText = heading || _label('previously_booked');
    dispatch({
      type: ACTION_GET_PREVIOUS_BOOKED_HEADING,
      data: {
        previousBookedHeading: headingText,
      },
    });

    return headingText;
  };

export const renderTatkalAlertTooltip = () => (dispatch) => {
  dispatch({
    type: ACTION_RAILS_LISTING_SHOWN_TOOLTIP,
    data: {
      isShownTatkalAlertTooltip: true,
    },
  });
};

export const setPrevBookedCollapsedState = (originStation, destinationStation, isCollapsed) => {
  const routeKey = `${originStation?.code}-${destinationStation?.code}`;

  return {
    type: ACTION_SET_PREV_BOOKED_COLLAPSED_STATE,
    data: {
      routeKey,
      isCollapsed,
    },
  };
};

export const resetPrevBookedCollapsedState = () => ({
  type: ACTION_RESET_PREV_BOOKED_COLLAPSED_STATE,
});

export const resetNearbyDatesState = () => ({
  type: ACTION_RESET_NEARBY_DATES_STATE,
});

export const updateGranularAvailabilityTracking = (trackingInfo: string[]) => ({
  type: ACTION_UPDATE_GRANULAR_AVAILABILITY_TRACKING,
  data: trackingInfo,
});

export const clearNotAvailableCard = () => ({
  type: ACTION_CLEAR_NOT_AVAILABLE_CARD,
});
