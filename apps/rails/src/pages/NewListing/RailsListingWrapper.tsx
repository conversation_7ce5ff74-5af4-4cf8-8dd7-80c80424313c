import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import {
  setFreeCancellationUsingStorage,
  setSemLandingOverlayVisibility,
  getRailOffers,
  initRailsLanding,
} from 'apps/rails/src/pages/RailsLandingPage/Store/RailsLandingPageActions';
import { connect } from 'react-redux';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { fetchCall, showRtbListingBottomSheet } from './RailsListingActions';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {RailsListingV3} from 'apps/rails/src/pages/RailsListingV3';
import { getPokusConfigWaitingPromise } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../configStore/Common/constants';
import {
  SCREENER_INTERACTIVE,
  SCREENER_LOADING,
  SEM_LANDING_VERSION_V2,
} from '../../Utils/RailsConstant';
import ErrorBoundary from 'apps/rails/src/pages/GenericErrorBoundary';
import { PAGE } from '../../types/grafanaTracking.types';
import PropTypes from 'prop-types';
import RailsLandingV2 from '../RailsLandingV2/RailsLandingV2';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import { getShowSemLanding } from '../../RailsAbConfig';
import { SEM_LANDING_PAGE } from '../../RailsAbConstants';
import { getQueryParamsFromUrl } from '@mmt/legacy-commons/Helpers/misc';

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
    backgroundColor: colors.white,
  },
});
const RailsListingWrapper = (props) => {
  const shouldShowSemOverlay =
    props.cmp?.includes('SEM') &&
    props.showSemLandingOverlay &&
    getShowSemLanding() === SEM_LANDING_PAGE.SHOWN;
  const [semLandingConfig, setSemLandingConfig] = useState(null);
  const [configLoading, setConfigLoading] = useState(false);
  const [semFlowFailed, setSemFlowFailed] = useState(false);

  const closeOverlay = () => {
    props.setSemLandingOverlayVisibility(false);
  };

  useEffect(() => {
    async function fetchConfig() {
      setConfigLoading(true);
      try {
        const config = await getConfigStore(configKeys.RAILS_SEM_LANDING_PAGE_CONFIG);
        if (config?.components && Array.isArray(config.components)) {
          setSemLandingConfig(config);
          const isFromSEM =
            props.cmp?.includes('SEM') && getShowSemLanding() === SEM_LANDING_PAGE.SHOWN;
          if (isFromSEM) {
            const queryParams = getQueryParamsFromUrl(props.deep_link_intent_url);
            const deeplinkParams = {
              deeplinkKey: queryParams?.deeplinkKey,
              priorityRTB: queryParams?.priorityFeature,
            };
            const landingURL =
              (typeof window !== 'undefined' && window.location?.href) ||
              props.deep_link_intent_url;
            await props.initRailsLanding({
              landingURL,
              cmp: props.cmp,
              deeplinkParams,
              from: queryParams.from,
              to: queryParams.to,
              landingVersion: SEM_LANDING_VERSION_V2,
              ...props,
            });
            const { components } = config;
            const hasOffers = components.find((c) => c.type === 'OFFERS');
            if (hasOffers) {
              props.getRailOffers(false);
            }
          }
        }
      } catch (error) {
        setSemFlowFailed(true);
      } finally {
        setConfigLoading(false);
      }
    }

    async function fetchRailsListing() {
      await getPokusConfigWaitingPromise(1000);
      props?.fetchCall(props);
    }
    if (shouldShowSemOverlay) {
      fetchConfig();
    }

    if (!props?.crossSellEnabled) {
      fetchRailsListing();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldShowSemOverlay, props?.crossSellEnabled]);

  const mmtlistingHighlightsData = useConfigStore(configKeys.RAILS_LISTING_PAGE_MMT_HIGHLIGHTS);
  const railsV3RetryData = useConfigStore(configKeys.RAILS_LISTING_ERROR_HANDLING);
  const { updateState } = useScreenProfiler();

  const listingView = () => {
    if (!props?.intialLoadingDone) {
      updateState(SCREENER_LOADING);
      return (
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <Spinner size={30} color={colors.azure} />
        </View>
      );
    }
    updateState(SCREENER_INTERACTIVE);

    if (shouldShowSemOverlay && !semFlowFailed && semLandingConfig) {
      try {
        return (
          <View style={styles.overlayContainer}>
            <RailsLandingV2
              {...props}
              semLandingPageConfig={semLandingConfig}
              closeOverlayFunction={closeOverlay}
            />
          </View>
        );
      } catch (error) {
        return (
          <RailsListingV3
            {...{
              ...props,
              ...{ openUniversalWidget: props?.openWidget },
              ...mmtlistingHighlightsData,
              ...railsV3RetryData,
            }}
          />
        );
      }
    }

    if (configLoading && !semFlowFailed) {
      return (
        <View style={styles.overlayContainer}>
          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <Spinner size={30} color={colors.azure} />
          </View>
        </View>
      );
    }

    return (
        <RailsListingV3
          {...{
            ...props,
            ...{ openUniversalWidget: props?.openWidget },
            ...mmtlistingHighlightsData,
            ...railsV3RetryData,
          }}
        />
      );

  };

  return (
    <ErrorBoundary page={PAGE.LISTING_V3}>
      {listingView()}
    </ErrorBoundary>
  );
};

const mapStateToProps = (state) => {
  const {
    railsListing: {
      intialLoadingDone,
      connectedTravelApiResponseJson,
      recommendedList,
      otherDayTrainsList,
      errorFlow,
      showRtbListingBottomSheet,
      tbsRtbBottomSheet,
      listingEducationCard,
    },
    railsLanding: {
      appVersion,
      socialProofingResponse,
      rtbLandingActionsData,
      showSemLandingOverlay,
    },
  } = state;
  return {
    intialLoadingDone,
    appVersion,
    connectedTravelApiResponseJson,
    recommendedList,
    otherDayTrainsList,
    errorFlow,
    socialProofingResponse,
    showRtbListingBottomSheet,
    rtbLandingActionsData,
    tbsRtbBottomSheet,
    listingEducationCard,
    showSemLandingOverlay,
  };
};

const mapDispatchToProps = (dispatch) => ({
  fetchCall: (props: unknown) => dispatch(fetchCall(props)),
  setFreeCancellation: () => dispatch(setFreeCancellationUsingStorage()),
  updateRtbListingBottomSheet: (data: boolean) => dispatch(showRtbListingBottomSheet(data)),
  setSemLandingOverlayVisibility: (showOverlay: boolean) =>
    dispatch(setSemLandingOverlayVisibility(showOverlay)),
  getRailOffers: (isPremiumUser: boolean) => dispatch(getRailOffers(isPremiumUser)),
  initRailsLanding: (dataObject: unknown) => dispatch(initRailsLanding(dataObject)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsListingWrapper);

RailsListingWrapper.propTypes = {
  fetchCall: PropTypes.func,
  intialLoadingDone: PropTypes.bool,
  crossSellEnabled: PropTypes.bool,
  openWidget: PropTypes.func,
  setSemLandingOverlayVisibility: PropTypes.func,
  getRailOffers: PropTypes.func,
  initRailsLanding: PropTypes.func,
  cmp: PropTypes.string,
  showSemLandingOverlay: PropTypes.bool,
  deep_link_intent_url: PropTypes.string,
};
