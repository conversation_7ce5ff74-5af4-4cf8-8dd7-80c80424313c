import { connect } from 'react-redux';
import { View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import React from 'react';
import isEmpty from 'lodash/isEmpty';
import {removeFCKey} from 'apps/rails/src/Utils/RailsConstant';
import {getPokusConfigWaitingPromise} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {getRailsLandingData} from '../Review/RailsReviewActions';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import RailsListingWrapper from './RailsListingWrapper';
import {setLandingData} from './RailsListingActions';
import {today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import { Actions } from '../../navigation';
import { initUserFlowDetails } from '../User/UserVerification/UserVerificationActions';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {getQueryParamsFromUrl, getDateFromURLFormat} from '@mmt/legacy-commons/Helpers/misc';
import { _label } from '../../vernacular/AppLanguage';
import PropTypes from 'prop-types';

class RailsListingDeeplink extends React.Component {
  async  UNSAFE_componentWillMount() {
    try {
      await getPokusConfigWaitingPromise();
    } catch (e) {
    }
    await removeFCKey();
    let departureDate;
    try {
      const queryParams = getQueryParamsFromUrl(this.props.deep_link_intent_url);
      let tempDate = queryParams.date || this.props.date;
      let dateVal = getDateFromURLFormat(tempDate);
      departureDate = dateVal;// fecha.parse(this.props.date, 'YYYYMMDD');
      if (departureDate < today()) {
        departureDate = today();
      }
    } catch (e) {
      departureDate = today();
    }
    try {
      const landingData = await getRailsLandingData(this.props.from, this.props.to, departureDate);
      this.props.setLandingData(landingData);
    } catch (e) {
      Actions.rails({type: 'replace', postPaymentBooking: true});
      showShortToast(_label('something_went_wrong_retry'));
    }
  }

  async componentDidMount() {
    await this.props.initUserFlowDetails();
  }
  render() {
    if (this.props.loading) {
      return (
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
          <Spinner size={30} color={colors.azure} />
        </View>
      );
    }
    return (<RailsListingWrapper
      postPaymentBooking
      alternateAvailability={this.props.alternate}
      pnrNumber={this.props.pnrNumber}
      originStation={this.props.originStation}
      destinationStation={this.props.destinationStation}
      departureDate={this.props.departureDate}
    />);
  }
}

const mapStateToProps = (state, ownProps) => {
  const {
    railsLanding: {
       originStation, destinationStation, departureDate,
    },
  } = state;
  const loading = isEmpty(originStation) || isEmpty(destinationStation);
  return {
    loading,
    originStation,
    destinationStation,
    departureDate,
    ...ownProps,
  };
};

const mapDispatchToProps = (dispatch) => ({
  setLandingData: (landingData) => {
    dispatch(setLandingData(landingData));
  },
  initUserFlowDetails: () => {
    dispatch(initUserFlowDetails());
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsListingDeeplink);

RailsListingDeeplink.propTypes = {
  deep_link_intent_url: PropTypes.string,
  date: PropTypes.string,
  from: PropTypes.string,
  to: PropTypes.string,
  setLandingData: PropTypes.func,
  initUserFlowDetails: PropTypes.func,
  loading: PropTypes.bool,
  alternate: PropTypes.bool,
  pnrNumber: PropTypes.string,
  originStation: PropTypes.string,
  destinationStation: PropTypes.string,
  departureDate: PropTypes.string,
};
