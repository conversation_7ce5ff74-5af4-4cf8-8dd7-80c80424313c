import isEmpty from 'lodash/isEmpty';
import * as actions from './RailsListingActions';
import SortType from '../Types/SortType';
import QuotaType from '../Types/QuotaType';
import {
  fireOmnitureEventsNew,
  getFilteredTrainsWithGNAndTQClass,
  getNewFilterObject,
  getStagedTrainsList,
} from '../RailsFilters/RailsFilterActions';
import { clearSelectedFilter } from '../RailsFilters/railsFilter.utils';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {ACTION_UPDATE_TBS_CONFIRM_PROBABLITY} from './RailsListingActions';
import { _label } from '../../vernacular/AppLanguage';
import { getRailsLDAndSSQuotaEnabled } from '../../RailsAbConfig';
import { AVAILABILITY_STATUS } from '../../Utils/RailsConstant';

export const initRailsFilterObject = (filterObject = {}) => ({
  quickFilter: {
    heading: 'quick_filter',
    filterTypes: {},
  },
  journeyClassFilter: {
    heading: 'journey_class',
    filterTypes: {},
  },
  trainTypeFilter: {
    heading: 'train_type',
    filterTypes: {},
  },
  availabilityFilter: {
    heading: 'availability_filter',
    filterTypes: {},
  },
  quotaFilter: {
    heading: 'quota_filter',
    filterTypes: {},
  },
  departureTimeFilter: {
    heading: '',
    filterTypes: {
      nightFilter: {
        noOfTrains: 0,
        key: 'nightFilter',
        heading: 'night',
        subHeading: 'night_time',
        selected: false,
        condition: (train) => {
          const hours = train.departureDateAndTime.getHours();
          return (hours >= 0 && hours < 6);
        },
      },
      morningFilter: {
        noOfTrains: 0,
        key: 'morningFilter',
        heading: 'morning',
        subHeading: 'morning_time',
        selected: false,
        condition: (train) => {
          const hours = train.departureDateAndTime.getHours();
          return (hours >= 6 && hours < 12);
        },
      },
      afternoonFilter: {
        noOfTrains: 0,
        heading: 'afternoon',
        key: 'afternoonFilter',
        subHeading: 'afternoon_time',
        selected: false,
        condition: (train) => {
          const hours = train.departureDateAndTime.getHours();
          return (hours >= 12 && hours < 18);
        },
      },
      eveningFilter: {
        noOfTrains: 0,
        heading: 'evening',
        key: 'eveningFilter',
        subHeading: 'evening_time',
        selected: false,
        condition: (train) => {
          const hours = train.departureDateAndTime.getHours();
          return (hours >= 18 && hours <= 23);
        },
      },
    },
  },
  arrivalTimeFilter: {
    heading: '',
    filterTypes: {
      nightFilter: {
        noOfTrains: 0,
        heading: 'night',
        key: 'nightFilter',
        subHeading: 'night_time',
        selected: false,
        condition: (train) => {
          const hours = train.arrivalDateAndTime.getHours();
          return (hours >= 0 && hours < 6);
        },
      },
      morningFilter: {
        noOfTrains: 0,
        heading: 'morning',
        key: 'morningFilter',
        subHeading: 'morning_time',
        selected: false,
        condition: (train) => {
          const hours = train.arrivalDateAndTime.getHours();
          return (hours >= 6 && hours < 12);
        },
      },
      afternoonFilter: {
        noOfTrains: 0,
        heading: 'afternoon',
        key: 'afternoonFilter',
        subHeading: 'afternoon_time',
        selected: false,
        condition: (train) => {
          const hours = train.arrivalDateAndTime.getHours();
          return (hours >= 12 && hours < 18);
        },
      },
      eveningFilter: {
        noOfTrains: 0,
        heading: 'evening',
        key: 'eveningFilter',
        subHeading: 'evening_time',
        selected: false,
        condition: (train) => {
          const hours = train.arrivalDateAndTime.getHours();
          return (hours >= 18 && hours <= 23);
        },
      },
    },
  },
  fromStnFilter: {
    heading: '',
    filterTypes: {},
  },
  toStnFilter: {
    heading: '',
    filterTypes: {},
  },
  ...filterObject,
});

const initialState = {
  isLoading: true,
  searchParamsChanged: false,
  intialLoadingDone: false,
  fcStripIndex:0,
  loadingMsg: '',
  showWebview: false,
  webviewUrl: '',
  webviewHeader: '',
  goBackUrl: '',
  originStation: null,
  destinationStation: null,
  departureDate: null,
  showNoTrainView: false,
  showCrossSellModal: false,
  trainsList: [],
  showClassAvailability: false,
  showAlternateAvailability: false,
  selectedSortParam: SortType.DEFAULT,
  trainsListObject: {},
  allAvailableClasses: [],
  irctcBookingError: false,
  tatkalBookingError: false,
  isClassLoading: true,
  displayStationChangeAlert: false,
  stationChangeImageSrc: null,
  irctcErrorMessage: null,
  hotelCrossSellBannerText: '',
  cdnTncUrl: '',
  hotelsDeepLinkMobileApp: '',
  listingErrorCode: '',
  listingErrorMessage: '',
  listingErrorSubtitle: '',
  hotelsData: {hotelsApiError: true},
  voucherObject: null,
  filterObject: initRailsFilterObject(),
  filterObjectInitState: initRailsFilterObject(),
  stagedFilterObject: initRailsFilterObject(),
  filteredTrainsList: [],
  stagedTrainsList: [],
  confirmationChances: {},
  railsShowConfirmationChance: false,
  voucherLoading: false,
  showEditWidget: false,
  isEditClicked: false,
  skipUniversalWidgetFromURL: false,
  captureAddress: false,
  availableQuotaList: [],
  selectedTrainInfo: null,
  avlDayList: [],
  totalCollectibleAmount: '0',
  applicableBerthTypes: [],
  foodChoiceEnabled: false,
  bedRollFlagEnabled: false,
  selectedAvlStatus: null,
  selectedBookingDate: null,
  prevBookedCollapsedRoutes: {},
  selectedQuota: QuotaType.GN,
  selectedClassType: '',
  insuranceCharge: 0.0,
  confirmationGuaranteeConfig: {
    isInit: true,
  },
  otherDayTrainsListObject:{},
  settingListingDataForTravellers: false,
  fetchingLocusIds: false,
  locusIdFromStationDetailsError: false,
  newListingLoadOmitureDone: false,
  confirmationProbabilityOmitureDone: false,
  noTrainsViewOmnitureDone: false,
  noDirectTrainsFoundOmnitureDone: false,
  notAvailableCard: null,
  tuneTrackerOmnitureDone: false,
  sortFilterVersion: 'V1',
  covidSafetyCardData: undefined,
  pageSource: undefined,
  displayConfirmOptionsBottomSheet: false,
  displayTrainScheduleBottomSheet: false,
  trainNumberForSchedule: '',
  showBnpp: undefined,
  doCacheAtClient: false,
  displayConnectedTravelBottomSheet: false,
  connectedTravelApiResponseJson:null,
  nearbyDatesInfo: {
    selectedNearbyCardsHashList: [],
    selectedTrainsList: [],
  },
  nearbyDatesTrainsListObject: {},
  alternateAvailabilityResponse: {},
  otherDayTrainsList: [],
  errorFlow: null,
  displaySeatLockBottomSheet: false,
  displayAvailDepletionBottomSheet: false,
  displayScheduleTatkalBottomSheet: false,
  isCnfOptionsTrainSchedule: false,
  showTatkalAlertTooltipPerSession: true,
  isShownTatkalAlertTooltip: false,
  travelplexChatConfig: {},
  travelplexTickers: [],
  showRtbListingBottomSheet: false,
  tbsRtbBottomSheet: {},
  granularAvailabilityTracking: [],
  listingEducationCard: undefined,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case actions.ACTION_RAIL_LISTING_INTIAL_LOADING: {
      const { intialLoadingDone } = action.data;
      return {
        ...state,
        intialLoadingDone,
      };
    }

    case actions.ACTION_RAILS_LISTING_FETCHING_LOCUS_ID: {
      return {
        ...state,
        fetchingLocusIds: action.data,
      };
    }

    case actions.ACTION_RAILS_LISTING_FETCHING_LOCUS_ID_ERROR: {
      return {
        ...state,
        locusIdFromStationDetailsError: action.data,
      };
    }

    case actions.ACTION_RAIL_LISTING_SEARCH_PARAMS_CHANGED: {
      const { searchParamsChanged } = action.data;
      return {
        ...state,
        searchParamsChanged,
      };
    }

    case actions.ACTION_RAIL_LISTING_SET_CROSS_SELL_MODAL: {
      const { showCrossSellModal } = action.data;
      return {
        ...state,
        showCrossSellModal,
      };
    }

    case actions.ACTION_RAILS_LISTING_INIT_STATE: {
      const { originStation, destinationStation, departureDate, isLoading, pageSource} = action.data;
      return {
        ...state,
        originStation,
        destinationStation,
        departureDate,
        isLoading,
        confirmationChances: {},
        railsShowConfirmationChance: false,
        hotelsData: {hotelsApiError: true},
        recommendedMessage: null,
        recommendedList: [],
        noTrainFound: false,
        pageSource,
        showBnpp: undefined,
      };
    }
    case actions.ACTION_RAIL_LISTING_SHOW_WEBVIEW: {
      const {
        showWebview, webviewUrl, goBackUrl, webviewHeader, from, to, deptDate,
      } = action.data;
      return {
        ...state,
        showWebview,
        webviewUrl,
        goBackUrl,
        webviewHeader,
        from,
        to,
        deptDate,
      };
    }

    case actions.ACTION_RAIL_LISTING_REMOVE_WEBVIEW: {
      const {showWebview} = action.data;
      return {
        ...state,
        showWebview,
      };
    }

    case actions.ACTION_INIT_RAIL_LISTING:
      return {
        ...state,
        isLoading: true,
        confirmationGuaranteeConfig: {
          isInit: true,
        },
      };

    case actions.ACTION_RAILS_LISTING_COMPLETE: {
      const {
        searchData: trainsList,
        availableQuotaList,
        selectedTrainInfo,
        showClassAvailability,
        clientConfig,
        trainsListObject,
        showAlternateAvailability,
        minPriceDetails,
        minDuration,
        alternateAvailabilityResponse,
        showBnpp,
        doCacheAtClient,
        connectedTravelApiResponseJson,
        otherDayTrainsList,
        interactedTrains,
        prevSearchPosition,
        prevBookedPosition,
        prevSearchedHeading,
        prevBookedHeading,
        bookedTrains,
        omnitureDetails,
        availabiltyBucket = '',
        availabilityPercentage = '',
        tbsRtbBottomSheet,
        listingEducationCard,
      } = action.data;
      const ldAndSsQuotaEnabled = getRailsLDAndSSQuotaEnabled();
      return {
        ...state,
        showNoTrainView: false,
        isLoading: false,
        searchParamsChanged: false,
        trainsList,
        availableQuotaList,
        selectedTrainInfo,
        showClassAvailability,
        showAlternateAvailability,
        trainsListObject,
        filteredTrainsList: ldAndSsQuotaEnabled ? getFilteredTrainsWithGNAndTQClass(trainsList) : trainsList,
        stagedTrainsList: ldAndSsQuotaEnabled ? getFilteredTrainsWithGNAndTQClass(trainsList) : trainsList,
        minPriceDetails,
        minDuration,
        alternateAvailabilityResponse,
        showBnpp,
        doCacheAtClient,
        connectedTravelApiResponseJson,
        otherDayTrainsList,
        interactedTrains: ldAndSsQuotaEnabled
          ? getFilteredTrainsWithGNAndTQClass(interactedTrains)
          : interactedTrains,
        prevSearchPosition,
        prevBookedPosition,
        prevSearchedHeading,
        prevBookedHeading,
        bookedTrains: ldAndSsQuotaEnabled
          ? getFilteredTrainsWithGNAndTQClass(bookedTrains)
          : bookedTrains,
        ...clientConfig,
        omnitureDetails,
        availabiltyBucket,
        availabilityPercentage,
        tbsRtbBottomSheet,
        listingEducationCard,
      };
    }
    case actions.ACTION_SEAT_LOCK_BOTTOMSHEET:
    case actions.ACTION_PREFILL_DATA_FOR_POPUP: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_RAILS_LISTING_ERROR: {
      const {
        errorMessage,
        errorSubtitle,
        errorFlag,
        errorCode,
        recommendedMessage,
        recommendedList,
        noTrainFound,
        trainsList,
        minPriceDetails,
        minDuration,
        otherDayTrainsList,
        errorFlow,
        interactedTrains,
        bookedTrains,
        prevSearchPosition,
        prevBookedPosition,
        prevSearchedHeading,
        prevBookedHeading,
      } = action.data;
      return {
        ...state,
        isLoading: false,
        searchParamsChanged: false,
        showNoTrainView: errorFlag,
        listingErrorCode: errorCode,
        listingErrorMessage: errorMessage,
        listingErrorSubtitle: errorSubtitle,
        isLoading: false,
        recommendedMessage,
        recommendedList,
        noTrainFound,
        trainsList,
        filteredTrainsList: trainsList,
        stagedTrainsList: trainsList,
        minPriceDetails,
        minDuration,
        otherDayTrainsList,
        errorFlow,
        interactedTrains,
        bookedTrains,
        prevSearchPosition,
        prevBookedPosition,
        prevSearchedHeading,
        prevBookedHeading,
      };
    }
    case actions.ACTION_SET_LIST_SORTED_ORDER: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_TRAIN_SELECTED: {
      const {showClassAvailability, selectedTrainInfo} = action.data;
      return {
        ...state,
        showClassAvailability,
        selectedTrainInfo,
      };
    }
    case actions.ACTION_CLOSE_CLASS_AVAILABILITY: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_QUOTA_TYPE_SELECTED: {
      return {
        ...state,
        selectedQuota: action.data,
      };
    }
    case actions.ACTION_SET_ALL_AVAILABLE_CLASSES: {
      return {
        ...state,
        allAvailableClasses: action.data,
      };
    }
    case actions.ACTION_SET_CLASS_TYPE_SELECTED: {
      return {
        ...state,
        selectedClassType: action.data,
      };
    }
    case actions.ACTION_INIT_AVAILABILITY_LIST: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_IRCTC_BOOKING_ERROR: {
      const {irctcErrorStatus, irctcErrorMessage} = action.data;
      return {
        ...state,
        isClassLoading: false,
        irctcBookingError: irctcErrorStatus,
        irctcErrorMessage,
      };
    }
    case actions.ACTION_TATKAL_BOOKING_ERROR: {
      return {
        ...state,
        isClassLoading: false,
        tatkalBookingError: action.data,
      };
    }
    case actions.ACTION_UPDATE_TBS_AVAILABILITY: {
      const {newTbsAvailability, trainInfo} = action.data;
      if (!newTbsAvailability || !trainInfo) {
        return state;
      }
      let newNotAvailableCard = state.notAvailableCard;
      if (
        newTbsAvailability.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.NOT_AVAILABLE ||
        newTbsAvailability.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.REGRET ||
        newTbsAvailability.availablityStatus?.toLowerCase() === AVAILABILITY_STATUS.BOOKING_NOT_ALLOWED
      ) {
        newNotAvailableCard = `${trainInfo.trainNumber}-${newTbsAvailability.className}-${newTbsAvailability.quota}`;
      } else {
        newNotAvailableCard = null;
      }
      const isOdt = trainInfo?.odt;
      const {
        otherDayTrainsList, filteredTrainsList, railsShowConfirmationChance, bookedTrains, interactedTrains,
      } = state;
      const trainsList = isOdt ? otherDayTrainsList : state?.trainsList;
      let {confirmationChances} = state;
      const updateTrainWithAvailability = (train) => {
        if (!train || !train.trainNumber || !Array.isArray(train.tbsAvailability)) {
          return train;
        }
        if (train.trainNumber === trainInfo.trainNumber) {
          const updatedTbsAvailability = train.tbsAvailability.map(item =>
            ((item && item.quota === newTbsAvailability?.quota && item.className === newTbsAvailability?.className)
              ? newTbsAvailability : item));
          return (
            {...train, tbsAvailability: updatedTbsAvailability}
          );
        }
        return train;
      };
      const newTrainsList = trainsList.map(updateTrainWithAvailability);
      const newfilteredTrainsList = filteredTrainsList.map(updateTrainWithAvailability);
      const hasTrainInBooked = Array.isArray(bookedTrains) &&
        bookedTrains.some(train => train?.trainNumber === trainInfo.trainNumber);
      const hasTrainInInteracted = Array.isArray(interactedTrains) &&
        interactedTrains.some(train => train?.trainNumber === trainInfo.trainNumber);
      const newBookedTrains = hasTrainInBooked ?
        bookedTrains.map(updateTrainWithAvailability) : bookedTrains;
      const newInteractedTrains = hasTrainInInteracted ?
        interactedTrains.map(updateTrainWithAvailability) : interactedTrains;

      try {
        if (railsShowConfirmationChance && newTbsAvailability.availablityType === '3') {
          const classAvailability = isEmpty(confirmationChances[trainInfo.trainNumber])
          ? []
          : confirmationChances[trainInfo.trainNumber].classAvailability;
          confirmationChances = {
            ...confirmationChances,
            [trainInfo.trainNumber]: {
              classAvailability: [...classAvailability, {
                tClass: newTbsAvailability.className,
                availability:
                newTbsAvailability.prettyPrintingAvailablityStatus ||
                newTbsAvailability.availablityStatus,
                Prediction: null,
                color: '#4a4a4a',
              }],
            },
          };
        }
      } catch (e) {
        console.log('error', e);
      }
      if (isOdt) {
        return {
          ...state,
          otherDayTrainsList: newTrainsList,
          filteredTrainsList: newfilteredTrainsList,
          confirmationChances,
          bookedTrains: newBookedTrains,
          interactedTrains: newInteractedTrains,
          notAvailableCard: newNotAvailableCard,
        };
      }
      return {
        ...state,
        trainsList: newTrainsList,
        filteredTrainsList: newfilteredTrainsList,
        confirmationChances,
        bookedTrains: newBookedTrains,
        interactedTrains: newInteractedTrains,
        notAvailableCard: newNotAvailableCard,
      };
    }
    case ACTION_UPDATE_TBS_CONFIRM_PROBABLITY: {
      const {
        trainBtwnStnsList,
        trainsList,
        confirmationGuaranteeConfig,
        filterObject,
      } = action.data;
      trainsList.forEach((train, index1) => {
        if (trainBtwnStnsList[index1].trainNumber === train.trainNumber) {
          trainBtwnStnsList[index1].tbsAvailability.forEach((item, index2) => {
            if (item.quota === train.tbsAvailability[index2].quota) {
              Object.assign(train.tbsAvailability[index2], {
                displayPredictionFlag: item.displayPredictionFlag,
                predictionPercentage: item.predictionPercentage,
                confirmationGuaranteeText: item.confirmationGuaranteeText,
                freeCancellationText: item.freeCancellationText,
              });
            }
          });
        }
      });

      return {
        ...state,
        trainsList,
        confirmationGuaranteeConfig,
        filterObject,
        filterObjectInitState: filterObject,
        stagedFilterObject: filterObject,
        trainBtwnStnsList,
      };
    }

    case actions.ACTION_SET_DATA_FOR_TRAVELERS_PAGE: {
      const {
        childBerthMandatory: childBerthMandatoryString,
        seniorCitizenApplicable: seniorCitizenApplicableString,
      } = action.data;
      return {
        ...state,
        ...action.data,
        childBerthMandatory: childBerthMandatoryString === 'true',
        seniorCitizenApplicable: seniorCitizenApplicableString === 'true',
      };
    }
    case actions.ACTION_UPDATE_QUOTA_SELECTED_OF_TRAIN: {
      return {
        ...state,
        trainsListObject: action.data,
      };
    }
    case actions.ACTION_UPDATE_QUOTA_SELECTED_OF_OTHER_DAY_TRAIN: {
      return {
        ...state,
        otherDayTrainsListObject: action.data,
      };
    }
    case actions.ACTION_UPDATE_CLASS_TYPE_SELECTED_OF_TRAIN: {
      return {
        ...state,
        trainsListObject: action.data,
      };
    }
    case actions.ACTION_UPDATE_AVAILABILITY_OF_TRAIN: {
      return {
        ...state,
        trainsListObject: action.data,
      };
    }
    case actions.ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN: {
      return {
        ...state,
        trainsListObject: action.data,
      };
    }
    case actions.ACTION_INIT_AVAILABILITY_STATE_OF_TRAIN_FOR_ODT: {
      return {
        ...state,
        otherDayTrainsListObject: action.data,
      };
    }
    case actions.ACTION_SET_AVAILABILITY_LIST: {
      const {
        bookingConfig,
        isClassLoading,
        avlDayList,
        selectedAvlStatus,
        irctcBookingError,
        insuranceCharge,
        totalCollectibleAmount,
      } = action.data;
      return {
        ...state,
        isClassLoading,
        avlDayList,
        selectedAvlStatus,
        irctcBookingError,
        insuranceCharge,
        totalCollectibleAmount,
        ...bookingConfig,
        childBerthMandatory: bookingConfig.childBerthMandatory === 'true',
        seniorCitizenApplicable: bookingConfig.seniorCitizenApplicable === 'true',
      };
    }
    case actions.ACTION_SELECTED_BOOKING_DATE: {
      return {
        ...state,
        selectedBookingDate: action.data,
      };
    }
    case actions.ACTION_DATE_CHANGED: {
      return {
        ...state,
        departureDate: action.data,
        isLoading: true,
        noTrainFound: false,
        recommendedMessage: null,
        recommendedList: null,
      };
    }
    case actions.ACTION_STATION_CHANGE_ALERT: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_UPDATE_SELECTED_TRAIN_INFO_JOURNEY_DATE: {
      const {selectedTrainInfo} = action.data;
      return {
        ...state,
        selectedTrainInfo,
      };
    }
    case actions.ACTION_SET_PPBOOKING_LISTING_DATA: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_SET_PREV_BOOKED_COLLAPSED_STATE: {
      const { routeKey, isCollapsed } = action.data;
      return {
        ...state,
        prevBookedCollapsedRoutes: {
          ...state.prevBookedCollapsedRoutes,
          [routeKey]: isCollapsed,
        },
      };
    }
    case actions.ACTION_RESET_PREV_BOOKED_COLLAPSED_STATE: {
      return {
        ...state,
        prevBookedCollapsedRoutes: {},
      };
    }
    case actions.ACTION_SET_HOTELS_CROSS_SELL_DATA: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_RAILS_LISTING_OMNITURE: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_SET_VOUCHER_OBJECT: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_INIT_RAILS_LISTING_FILTERS: {
      const {filterObject} = action.data;
      return {
        ...state,
        filterObject,
        filterObjectInitState: filterObject,
        stagedFilterObject: filterObject,
      };
    }
    case actions.ACTION_SET_FILTER: {
      const {stagedFilterObject, trainsList} = state;
      const {filterItem, filterTypeObject} = action.data;
      const selectedFilterObject = stagedFilterObject[filterTypeObject.id];
      const newFilteredObject = {
        ...stagedFilterObject,
        [filterTypeObject.id]: {
          ...selectedFilterObject,
          filterTypes: {
            ...selectedFilterObject.filterTypes,
            [filterItem]: {
              ...selectedFilterObject.filterTypes[filterItem],
              selected: !selectedFilterObject.filterTypes[filterItem].selected,
            },
          },
        },
      };
      try {
        if (getRailsLDAndSSQuotaEnabled() && filterTypeObject.id === 'quotaFilter') {
          let quotaSelectedInQuickFilters = Object.keys(
            newFilteredObject?.quotaFilter?.filterTypes,
          )?.some((key) => {
            return newFilteredObject?.quotaFilter?.filterTypes[key]?.selected;
          });

          // depending on if quota(s) are selected in filters screen, update the quota selected in the quick filters as well
          if (newFilteredObject?.quickFilter?.filterTypes?.quota) {
            newFilteredObject.quickFilter.filterTypes.quota.selected = quotaSelectedInQuickFilters;
          }
        }
      } catch (err) {
        console.error('error in quotaSelectedInQuickFilters', err);
      }
      const stagedTrainsList = getStagedTrainsList(trainsList, newFilteredObject);
      return {
        ...state,
        stagedFilterObject: newFilteredObject,
        stagedTrainsList,
      };
    }
    case actions.ACTION_COMMIT_TRAIN_AND_FILTER: {
      const {stagedFilterObject, stagedTrainsList} = state;
      const {location} = action.data;
      if (location !== null && !location.includes('bottomSheet')) {fireOmnitureEventsNew(location);}
      // fireFilterOmnitureEvents(stagedFilterObject,location);
      return {
        ...state,
        filterObject: stagedFilterObject,
        filteredTrainsList: stagedTrainsList,
      };
    }
    case actions.ACTION_SET_QUICK_FILTER: {
      const {stagedFilterObject, trainsList} = state;
      const {filterItem: selectedFilterItem, filterTypeObject} = action.data;
      const newFilteredObject =
        getNewFilterObject(stagedFilterObject, selectedFilterItem, filterTypeObject);
      const stagedTrainsList = getStagedTrainsList(trainsList, newFilteredObject);
      return {
        ...state,
        stagedFilterObject: newFilteredObject,
        stagedTrainsList,
      };
    }
    case actions.ACTION_SET_LISTING_QUICK_FILTER: {
      const {filterObject, trainsList} = state;
      const {filterItem: selectedFilterItem, filterTypeObject} = action.data;
      const newFilteredObject =
        getNewFilterObject(filterObject, selectedFilterItem, filterTypeObject);
      const stagedTrainsList = getStagedTrainsList(trainsList, newFilteredObject);
      showShortToast(_label('count_of_total_trains_found', undefined, {
        count: stagedTrainsList.length,
        total: trainsList.length,
      }));
      return {
        ...state,
        filterObject: newFilteredObject,
        filteredTrainsList: stagedTrainsList,
      };
    }
    case actions.ACTION_INIT_FILTER_STATE: {
      const {filterObject, filteredTrainsList} = state;
      return {
        ...state,
        stagedFilterObject: filterObject,
        stagedTrainsList: filteredTrainsList,
      };
    }
    case actions.ACTION_SET_TO_PREV_FILTER_STATE: {
      const {filterObject, filteredTrainsList } = state;
      return {
        ...state,
        stagedFilterObject: filterObject,
        stagedTrainsList: filteredTrainsList,
      };
    }
    case actions.ACTION_CLEAR_ALL_FILTERS_ON_FILTER_PAGE: {
      const {filterObjectInitState, trainsList} = state;
      return {
        ...state,
        stagedFilterObject: filterObjectInitState,
        stagedTrainsList: trainsList,
      };
    }
    case actions.ACTION_CLEAR_SELECTED_FILTERS_ON_FILTER_PAGE:{
      const {filterObject, trainsList} = state;
      const {filterItem} = action.data;
      const finalFilterObject  = clearSelectedFilter(filterObject,filterItem.id);
      const stagedTrainsList = getStagedTrainsList(trainsList, finalFilterObject);
      return {
        ...state,
        stagedFilterObject: finalFilterObject,
        filterObject: finalFilterObject,
        stagedTrainsList: stagedTrainsList,
        filteredTrainsList : stagedTrainsList,
      };

    }

    case actions.ACTION_CLEAR_ALL_FILTERS_ON_LISTING_PAGE: {
      const {filterObjectInitState, trainsList} = state;
      return {
        ...state,
        filterObject: filterObjectInitState,
        filteredTrainsList: trainsList,
      };
    }
    case actions.ACTION_INIT_SHOW_CONFIRMATION_CHANCE_AB: {
      const {railsShowConfirmationChance} = action.data;
      return {
        ...state,
        railsShowConfirmationChance,
      };
    }

    case actions.ACTION_SET_CONFIRMATION_CHANCES: {
      const {confirmationChances} = action.data;
      return {
        ...state,
        confirmationChances,
      };
    }
    case actions.ACTION_RAILS_LISTING_UPDATE_ORIGIN: {
      const {originStation} = action.data;
      return {
        ...state,
        originStation,
      };
    }
    case actions.ACTION_RAILS_LISTING_UPDATE_DESTINATION: {
      const {destinationStation} = action.data;
      return {
        ...state,
        destinationStation,
      };
    }
    case actions.ACTION_RAILS_LISTING_EDIT_ORIGIN: {
      const originStation = action.data;
      return {
        ...state,
        originStation,
      };
    }
    case actions.ACTION_RAILS_LISTING_EDIT_DESTINATION: {
      const destinationStation = action.data;
      return {
        ...state,
        destinationStation,
      };
    }
    case actions.ACTION_RAIL_LISTING_SHOW_HIDE_EDIT_WIDGET: {
      const {showEditWidget, isEditClicked} = action.data;
      return {
        ...state,
        showEditWidget,
        isEditClicked,
      };
    }
    case actions.ACTION_RAIL_LISTING_SHOW_LOADING: {
      const isLoading = action.data;
      return {
        ...state,
        isLoading,
        recommendedMessage: null,
        recommendedList: [],
        noTrainFound: false,
      };
    }
    case actions.ACTION_DATE_SELECTED_LISTING: {
      const {data: date} = action;
      return {
        ...state,
        departureDate: date,
      };
    }
    case actions.ACTION_SWAP_SEARCH_LISTING: {
      const {originStation, destinationStation} = state;
      return {
        ...state,
        originStation: destinationStation,
        destinationStation: originStation,
      };
    }
    case actions.ACTION_SKIP_UNIVERAL_WIDGET_URL: {
      return {
        ...state,
        skipUniversalWidgetFromURL: true,
      };
    }
    case actions.COVID_SAFETY_CARD_LISTING: {
      const {covidSafetyCardData} = action.data;
      return {
        ...state,
        covidSafetyCardData,
      };
    }

    case actions.ACTIONS_CLEAR_SELECTED_TRAIN_INFO: {
      return {
        ...state,
        selectedTrainInfo: null,
      };
    }

    case actions.ACTION_SET_ALTERNATE_AVAILABILTY_LIST: {
      return {
        ...state,
        ...action.data,
      };
    }

    case '@rails/TBS_AVAILABILITY_CARD_LOADING': {
      const {
        trainInfo, quotaCode, className, isLoading,
      } = action.data;

      if (!trainInfo || !quotaCode || !className || typeof isLoading !== 'boolean') {
        return state;
      }

      const {otherDayTrainsList, bookedTrains, interactedTrains} = state;
      const isOdt = trainInfo?.odt;
      const filteredTrainsList = isOdt ? otherDayTrainsList : state?.filteredTrainsList;
      const updateTrainWithLoading = (train) => {
        if (!train || !train.trainNumber || !Array.isArray(train.tbsAvailability)) {
          return train;
        }
        if (train.trainNumber === trainInfo.trainNumber) {
          const tbsAvalWithLoader = train.tbsAvailability.map((avl = {}) => {
            if (avl && avl.quota === quotaCode && avl.className === className) {
              return {
                ...avl,
                isLoading,
              };
            }
            return avl;
          });

          return {
            ...train,
            tbsAvailability: tbsAvalWithLoader,
          };
        }
        return train;
      };
      const newFilteredTrainsList = filteredTrainsList?.map(updateTrainWithLoading);
      const hasTrainInBooked = Array.isArray(bookedTrains) &&
        bookedTrains.some(train => train?.trainNumber === trainInfo.trainNumber);
      const hasTrainInInteracted = Array.isArray(interactedTrains) &&
        interactedTrains.some(train => train?.trainNumber === trainInfo.trainNumber);
      const newBookedTrains = hasTrainInBooked ?
        bookedTrains.map(updateTrainWithLoading) : bookedTrains;
      const newInteractedTrains = hasTrainInInteracted ?
        interactedTrains.map(updateTrainWithLoading) : interactedTrains;
      if (isOdt)
      {
        return {
          ...state,
          otherDayTrainsList: newFilteredTrainsList,
          bookedTrains: newBookedTrains,
          interactedTrains: newInteractedTrains,
        };
      }
      return {
        ...state,
        filteredTrainsList: newFilteredTrainsList,
        bookedTrains: newBookedTrains,
        interactedTrains: newInteractedTrains,
      };
    }
    case actions.SAVE_CONFIRMATION_GUARANTEE_POKUS_OPTION: {
      const { confirmationGuaranteeConfig } = action.data;
      return {
        ...state,
        confirmationGuaranteeConfig,
      };
    }
    case actions.CONFIRMATION_GUARANTEE_ERROR: {
      const { confirmationGuaranteeConfig } = action.data;
      return {
        ...state,
        confirmationGuaranteeConfig,
      };
    }
    case actions.SAVE_FREE_CANCELLATION_CONFIG: {
      const { freeCancellationConfig } = action.data;
      return {
        ...state,
        freeCancellationConfig,
      };
    }
    case actions.SAVE_RAILOFY_ZC_SHOWN: {
      const { railofyZcShown } = action.data;
      return {
        ...state,
        railofyZcShown,
      };
    }
    case actions.ACTION_CONFIRM_OPTIONS_ALERT: {
      return {
        ...state,
        ...action.data,
      };
    }

    case actions.ACTION_GET_PREVIOUS_SEARCH_POSITION: {
      return {
        ...state,
        previousSearchPosition: action.data.previousSearchPosition,
      };
    }

    case actions.ACTION_GET_PREVIOUS_BOOKED_POSITION: {
      return {
        ...state,
        previousBookedPosition: action.data.previousBookedPosition,
      };
    }

    case actions.ACTION_GET_PREVIOUS_SEARCHED_HEADING: {
      return {
        ...state,
        previousSearchedHeading: action.data?.previousSearchedHeading || null,
      };
    }

    case actions.ACTION_GET_PREVIOUS_BOOKED_HEADING: {
      return {
        ...state,
        previousBookedHeading: action.data?.previousBookedHeading || null,
      };
    }

    case actions.ACTION_TOGGLE_TRAIN_SCHEDULE_BOTTOM_SHEET: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_TOGGLE_CONNECTED_TRAVEL_BOTTOM_SHEET: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_RAILS_TRAVELPLEX_CHAT_CONFIG: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_SET_SCHEDULE_TATKAL_DATA:
    case actions.ACTION_DISPLAY_AVAIL_DEPLETION_BOTTOM_SHEET:
    case actions.ACTION_CONNECTED_TRAVEL_BOTTOM_SHEET_DATA: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_SET_CONNECTED_TRAVEL_LIST: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_TOGGLE_NEARBY_DATES_LIST: {
      return {
        ...state,
        nearbyDatesInfo: action.data,
      };
    }
    case actions.ACTION_INT_NEARBY_DATES_LIST: {
      return {
        ...state,
        nearbyDatesTrainsListObject: action.data,
      };
    }
    case actions.ACTION_UPDATE_NEARBY_DATES_LIST: {
      return {
        ...state,
        nearbyDatesTrainsListObject: action.data,
      };
    }
    case actions.ACTION_NEARBY_DATES_ERROR: {
      return {
        ...state,
        nearbyDatesTrainsListObject: action.data,
      };
    }
    case actions.ACTION_UPDATE_NEARBY_DATES_TTU: {
      const { currentTrainListObject, selectedCardHash } = action.data;
      const { nearbyDatesTrainsListObject } = state;
      return {
        ...state,
        nearbyDatesTrainsListObject: {
          ...nearbyDatesTrainsListObject,
          [selectedCardHash]: {
            ...currentTrainListObject,
          },
        },
      };
    }
    case actions.ACTION_RESET_NEARBY_DATES_STATE: {
      return {
        ...state,
        nearbyDatesInfo: {
          selectedNearbyCardsHashList: [],
          selectedTrainsList: [],
        },
        nearbyDatesTrainsListObject: {},
      };
    }
    case actions.ACTION_RAILS_LISTING_UPDATE_TATKAL_ALERT_TOOLTIP: {
      const { showTatkalAlertTooltipPerSession } = action.data;
      return {
        ...state,
        showTatkalAlertTooltipPerSession,
      };
    }

    case actions.ACTION_RAILS_LISTING_SHOWN_TOOLTIP: {
      const { isShownTatkalAlertTooltip } = action.data;
      return {
        ...state,
        isShownTatkalAlertTooltip,
      };
    }

    case actions.ACTION_RAILS_LISTING_SHOWN_RTB_BOTTOMSHEET: {
      const { showRtbListingBottomSheet } = action.data;
      return {
        ...state,
        showRtbListingBottomSheet,
      };
    }
    case actions.ACTION_UPDATE_GRANULAR_AVAILABILITY_TRACKING:
      return {
        ...state,
        granularAvailabilityTracking: action.data
      };
    case actions.ACTION_CLEAR_NOT_AVAILABLE_CARD:
      return {
        ...state,
        notAvailableCard: null
      };
    default:
      return state;
  }
};
