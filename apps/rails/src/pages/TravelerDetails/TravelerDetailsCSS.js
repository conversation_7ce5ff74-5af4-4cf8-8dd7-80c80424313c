import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../vernacular/VernacularUtils';

export const travelerTextStyle = {
    getTravelHeaderFontStyle : () => {
        return fontStyle('black');
    },
    getTextFieldLabelStyleAzureFontStyle : () => {
        return fontStyle('light');
    },
    getTextFieldLabelStyleFontStyle : () => {
        return fontStyle('regular');
    },
    getTextFieldValueStyleFontStyle : () => {
        return fontStyle('bold');
    },
    getMpTextFontStyle : () => {
        return fontStyle('regular');
    },
};
const travelerStyle = {
  contactCard: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  latoBlackText14: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.black,
  },
  latoBlackTextAzure14: {
    fontSize: 14,
    lineHeight: 24,
    letterSpacing: 0,
    color: colors.azure,
  },
  latoRegularText12: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.lightTextColor,
    paddingRight: 12,
  },
  latoRegularText14: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.lightTextColor,
  },
  latoRegularBlack04Text14: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.black04,
  },
  latoRegularYellowText14: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.yello,
  },
  latoRegularTextRed14: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.red,
  },
  latoRegularTextGreen12: {
    fontSize: 12,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.green,
  },
  latoRegularTextRed12: {
    fontSize: 12,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.red,
  },
  latoRegularTextYellow12: {
    fontSize: 12,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.lightYello,
  },
  latoBoldText14: {
    fontSize: 14,
    letterSpacing: 0,
    color: colors.lighterTextColor,
  },
  latoBoldText16: {
    letterSpacing: 0,
    color: colors.black,
    marginBottom: 5,
  },
  latoBoldTextAzure14: {
    fontSize: 12,
    letterSpacing: 0,
    color: colors.azure,
  },
  container: {
    backgroundColor: colors.grayBg,
    flex: 1,
  },
  marginTop16: {
    marginTop: 16,
  },
  paddingTop16: {
    paddingTop: 16,
  },
  paddingHorizontal16: {
    paddingHorizontal: 16,
  },
  paddingVertical16: {
    paddingVertical: 16,
  },
  redWarningBox: {
    padding: 10,
  },
  travelHeader: {
    color: colors.black,
    fontSize: 22,
    letterSpacing: 0,
    lineHeight: 32,
  },
  latoBoldText12: {
    fontSize: 12,
    letterSpacing: 0,
    lineHeight: 16,
    color: colors.black,
  },
  greenBoxWarning: {
    backgroundColor: '#c1f1dd',
    marginTop: 4,
    marginRight: 4,
    elevation: 0,
  },
  creamWhiteWarning: {
    backgroundColor: colors.creamWhite,
    marginTop: 4,
    marginRight: 4,
    elevation: 0,
  },
  lemonYellowWarning: {
    backgroundColor: '#ffedd1',
    marginTop: 4,
    marginRight: 4,
    elevation: 0,
  },
  latoRegularTextLemonYellow12: {
    fontSize: 12,
    lineHeight: 18,
    letterSpacing: 0,
    color: '#cf8100',
  },
  redWarning: {
    backgroundColor: colors.lightPink,
    marginTop: 4,
    marginRight: 4,
    elevation: 0,
  },
  redBoxWarning: {
    backgroundColor: colors.lightPink,
    marginTop: 4,
    marginRight: 4,
    elevation: 0,
  },
  warningBox: {
    paddingVertical: 4,
    paddingHorizontal: 10,
  },
  flexRowSpaceBetween: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  flexRow: {
    flex: 1,
    flexDirection: 'row',
  },
  viewStyle: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
  },
  downArrow: {
    height: 10,
    width: 10,
    marginLeft: 5,
  },
  travelerIcon: {
    marginTop: 3,
    height: 15,
    width: 15,
  },
  latoLightTextBlack0422: {
    fontSize: 22,
    lineHeight: 26,
    letterSpacing: 0,
    color: colors.black04,
  },
  textFieldLabelStyleAzure: {
    fontSize: 10,
    letterSpacing: 0,
    lineHeight: 14,
    color: colors.azure,
  },
  textFieldLabelStyle: {
    fontSize: 10,
    letterSpacing: 0,
    lineHeight: 14,
    color: colors.lightTextColor,
  },
  textFieldValueStyle: {
    fontSize: 14,
    letterSpacing: 0,
    lineHeight: 18,
    color: colors.black04,
  },
  latoBoldText16LineHeight20: {
    fontSize: 16,
    letterSpacing: 0,
    lineHeight: 20,
    color: colors.black,
  },
  dynamicPricing: {
    paddingLeft: 16,
    color: colors.lightYello,
    alignItems: 'center',
  },
  containerView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.creamWhite,
    height: 35,
  },
  fareIncrease: {
    width: 20,
    height: 20,
    paddingLeft: 16,
    alignItems: 'center',
  },
  latoRegularText14LineHeight20: {
    fontSize: 14,
    letterSpacing: 0,
    lineHeight: 20,
    color: colors.black04,
  },
  mpContainer: {
    flexDirection: 'row',
    // paddingTop: 26,
    alignItems: 'center',
  },
  marginLeft20: {
    paddingLeft: 20,
  },
  mpText: {
    fontSize: 14,
    color: colors.black04,
  },
  marginTop32: {
    marginTop: 32,
  },
  marginAlignment: {
    marginTop: 32,
    marginBottom: 16,
  },
};
export default travelerStyle;
