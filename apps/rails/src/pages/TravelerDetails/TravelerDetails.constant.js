export const TATKAL = {
  code: 'TQ',
  id: 'TQ',
  text: '<PERSON>t<PERSON>',
  value: 'Tat<PERSON>',
};

export const TRAVEL_INSURANCE = {
  NONE: 0,
  OPTED: 1,
  NOT_OPTED: 2,
  NOT_SELECTED: 3,
};

export const ERROR_FLOW = {
  templateType: 'SIMPLE',
  title: 'Oops, Something went wrong',
  subTitle: 'Apologies for the incovenience caused',
  ctas: [
    {
      action: 'RETRY',
      text: 'RETRY',
      code: 'null',
    },
    {
      action: 'CLOSE_BOTTOMSHEET',
      text: 'GO BACK',
      code: 'null',
    },
  ],
  iconUrl: 'https://go-assets.ibcdn.com/u/MMT/images/1711436200537-error_red.webp',
};

export const PASSWORD_RESET_CONSTANTS = {
  MOBILE_LABEL: 'Password will be sent on registered no. with IRCTC',
  EMAIL_LABEL: 'Password will be sent on registered email with IRCTC',
  MOBILE_OPTION: 'On Mobile Number',
  EMAIL_OPTION: 'On Email Id',
};
