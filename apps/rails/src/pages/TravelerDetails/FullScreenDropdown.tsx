import React from 'react';
import { View } from 'react-native';
import DisplayDropdown, { Country } from './DisplayDropdown';

interface FullScreenDropdownProps {
  label?: string;
  nationality_indian?: string;
  selectedValue?: string | null;
  onCountrySelect?: (country: Country) => void;
  onExpand?: () => void;
  onCollapse?: () => void;
}

export default function FullScreenDropdown(props: FullScreenDropdownProps) {
  const { label, nationality_indian, selectedValue, onCountrySelect, onExpand, onCollapse } = props;

  return (
    <View>
      <DisplayDropdown
        label={label}
        nationality_indian={nationality_indian}
        selectedValue={selectedValue}
        onCountrySelect={onCountrySelect}
        onExpand={onExpand}
        onCollapse={onCollapse}
      />
    </View>
  );
}
