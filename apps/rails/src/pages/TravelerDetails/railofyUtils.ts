import { Actions } from '../../navigation';
import { TextStyleBE } from '../../types/railofy.types';
import get from 'lodash/fp/get';
import find from 'lodash/fp/find';
import defaultTo from 'lodash/fp/defaultTo';
import { MULTI_SELECT } from '../../Utils/RailsConstant';


import webViewBackIcon from '@mmt/legacy-assets/src/ic_back_arrow.webp';

const RAILOFY_TYPE = {
  FC: 'FC',
  TG: 'TG',
  TGP: 'TGP',
  TGPV3: 'TGPV3',
  NONE: 'NA',
};

export const COUPON_TYPE = {
  FC_TG_TOGETHER: 'FC_TG_TOGETHER',
  TG_ONLY: 'TG_ONLY',
  FC_ONLY: 'FC_ONLY',
};


function isEmpty (obj: object) {
  if (obj === undefined || obj === null) {return true;}
  return Object.keys(obj).length === 0;
}

function getFcTgType ({ fc, tg }) {
  //TG Check should be at the top.
  if (!isEmpty(tg)) {
    return RAILOFY_TYPE.TG;
  }
  if (!isEmpty(fc)) {
    return RAILOFY_TYPE.FC;
  }
  return RAILOFY_TYPE.NONE;
}

export const getTicketFare = (fareInfoRevamped :{
  key: TextStyleBE[];
  value: TextStyleBE[];
}[]) => {
  const ticketFareText = fareInfoRevamped[0]?.value[0]?.text;
  return (ticketFareText?.length > 2) ? Number(ticketFareText?.substring(2)) : 0;
};

function getDefaultPremium (railofyType, freeCancellationResponse, confirmationGuaranteeResponse) {
  if (railofyType === RAILOFY_TYPE.TG) {
    const { preferenceInfoRevamped = []} = confirmationGuaranteeResponse;
    if (preferenceInfoRevamped.length) {
      return preferenceInfoRevamped[0].premiumPerPerson;
    }
    return 0;
  }
  return defaultTo(0, get(['insuranceAmount'], find(option => option.value, freeCancellationResponse?.cancellationOptions)));
}

// @TODOSFCTG response type
function updateRailofyData (response) {
  const {
    railofyErrorMessage,
    freeCancellationResponse,
    confirmationGuaranteeResponse,
    railsConfirmationGuaranteeOption,
    zcShown,
    railofyShown,
    fcUpgradeOption,
    isFCPreSelected,
    bnppResponse,
    bnppShown,
    tgPlusResponse,
    fcTgSelectionType,
    availabilityDepletion = null,
    updatedAvailabilityStatus,
  } = response;

  const railofyType = getFcTgType({ fc: freeCancellationResponse, tg: confirmationGuaranteeResponse});
  let preSelectedFC = {};
  if (isFCPreSelected && freeCancellationResponse?.header){
    preSelectedFC = {
      selectedIndex : 0,
      isFCSelected: true,
      isTGSelected: false,
      freeCancellationInsuranceOpted: true,
      railofyType: RAILOFY_TYPE.FC,
      premiumAmount: defaultTo(0, get(['insuranceAmount'], find(option => option.value, freeCancellationResponse?.cancellationOptions))),
    };
    const fcTgTogetheMultiSelect = fcTgSelectionType === MULTI_SELECT;
    if (fcTgTogetheMultiSelect) {
      const tgDiscountPremium = confirmationGuaranteeResponse?.discountedPremium;
      preSelectedFC.isTgDiscounted = tgDiscountPremium > 0;
    } else {
      preSelectedFC.isTgDiscounted = false;
    }
  }
  const res = {
    railofyErrorMessage,
    railofyType,
    freeCancellationData: freeCancellationResponse,
    tripGuaranteeData: confirmationGuaranteeResponse,
    railsConfirmationGuaranteeOption,
    bnppResponse,
    bnppShown,
    zcShown,
    railofyShown,
    fcUpgradeOption,
    defaultPremiumAmount: getDefaultPremium(railofyType, freeCancellationResponse, confirmationGuaranteeResponse),
    ...preSelectedFC,
    tgPlusData: tgPlusResponse,
    fcTgSelectionType,
    availabilityDepletion,
    updatedAvailabilityStatus,
  };

  return res;
}

function updateSelectionValue(railofy, index) { // @todosfctg define type
  const { railofyType } = railofy;
  if (railofyType === RAILOFY_TYPE.TG) {
    const { tripGuaranteeData : {
      preferenceInfoRevamped,
    } } = railofy;
    return {
      cnfmGuaranteeOpted: preferenceInfoRevamped[index].value,
    };
  }
  if (railofyType === RAILOFY_TYPE.FC) {
    const {
      freeCancellationData: {
        cancellationOptions,
      },
    } = railofy;
    return {
      freeCancellationInsuranceOpted: cancellationOptions[index].value,
    };
  }
  return {};
}

function updatePreserveFcSelection(railofy, index) {
  const { railofyType } = railofy;
  if (railofyType === RAILOFY_TYPE.FC) {
    return {
      preserveFcSelection: index === 0,
    };
  }
  return {};
}

function openWebView (url: string) {
  Actions.openWebView({
    url,
    headerText: 'MakeMyTrip',
    headerIcon: webViewBackIcon,
  });
}

function getTgType(railofyType: string, tgVersion: number) {
  switch (railofyType) {
    case RAILOFY_TYPE.TG:
      return tgVersion === 1 ? 0 : 1;
    case RAILOFY_TYPE.TGP:
      return 2;
    case RAILOFY_TYPE.TGPV3:
      return 4;
    case RAILOFY_TYPE.FC:
      return tgVersion === 1 ? 0 : 1;
    default:
      return null;
  }
}

export const getAvailabilityStatus = (railofy: unknown, selectedBookingDate: unknown) => {
  if (railofy?.updatedAvailabilityStatus) {
    return {
      status: railofy.updatedAvailabilityStatus,
      isUsingUpdatedAvailability: true,
    };
  }

  if (selectedBookingDate?.prettyPrintingAvailablityStatus) {
    return {
      status: selectedBookingDate.prettyPrintingAvailablityStatus,
      isUsingUpdatedAvailability: false,
    };
  }

  const fallbackStatus = selectedBookingDate?.availablityStatus?.replace('/', ' | ') || null;
  return {
    status: fallbackStatus,
    isUsingUpdatedAvailability: false,
  };
};

export {
  RAILOFY_TYPE,
  updateRailofyData,
  openWebView,
  updateSelectionValue,
  updatePreserveFcSelection,
  getTgType,
};
