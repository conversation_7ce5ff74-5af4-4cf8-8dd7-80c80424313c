import { trackClickEventProp61 } from '../RailsBusHomePage/Analytics';
import { TRAVELLER_DETAILS, TRAVELER_FREE_CANCELLATION, TRAVELER_FC_PROMT } from '../../Utils/RailsConstant';

function getYesNoFromIndex(index: number) {
  return index === 0 ? 'yes' : 'no';
}

function trackFreeCancellationSelection(index: number) {
  trackClickEventProp61(TRAVELLER_DETAILS, `${TRAVELER_FREE_CANCELLATION}_${getYesNoFromIndex(index)}`);
}

function trackFcBottomSheetSelection(index: number) {
  trackClickEventProp61(TRAVELLER_DETAILS, `${TRAVELER_FC_PROMT}_${getYesNoFromIndex(index)}`);
}

export { trackFreeCancellationSelection, trackFcBottomSheetSelection };
