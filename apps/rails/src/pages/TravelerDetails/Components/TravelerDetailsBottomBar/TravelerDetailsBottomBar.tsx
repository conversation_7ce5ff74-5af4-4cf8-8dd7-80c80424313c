import React, { useEffect, useMemo, useState } from 'react';
import {
  Animated,
  Image,
  LayoutAnimation,
  Platform,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  UIManager,
  View,
} from 'react-native';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import BookNowTravellers from 'apps/rails/src/pages/TravelerDetails/BookNowTravellers';
import {
  BookNowPayPartial,
  BookNowPayPartialStrip,
} from 'apps/rails/src/pages/TravelerDetails/Components/BookNowPayPartial';
import {
  BNPP,
  BnppResponse,
} from 'apps/rails/src/pages/TravelerDetails/Components/BookNowPayPartial/types';
import { trackTravellerPageEvent } from 'apps/rails/src/railsAnalytics';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import _isEmpty from 'lodash/isEmpty';
import { styles } from './styles';
import { useAnimatedBottom } from 'apps/rails/src/pages/Common/useAnimatedBottom';
import { connect } from 'react-redux';
import { calculateBNPPFare } from 'apps/rails/src/pages/TravelerDetails/Components/BookNowPayPartial/utils';
import { getTravelersListFromId } from '../../Containers/RailsTravelerRepository';
import filter from 'lodash/filter';
import AvailDepletionStrip from '../../../NewListing/Components/AvailDepletion/AvailDepletionStrip';

import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down.webp';
import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up.webp';

interface TravelerDetailsBottomBarProps {
  bookNowButtonEnabled: boolean;
  onBookNowClicked: () => void;
  toggleBNPPBottomSheet: (showBNPPBottomSheet: boolean) => void;
  showLoader: boolean;
  isLoading: boolean;
  id: string;
  isBnppOpted: boolean;
  bnppResponse?: BnppResponse;
  numberOfPassengers: number;
  listingTotalFare: number;
  isBNPPPriceShown: boolean;
}
const TravelerDetailsBottomBar = (props: TravelerDetailsBottomBarProps) => {
  const {
    isBnppOpted,
    bnppResponse,
    isLoading,
    numberOfPassengers,
    listingTotalFare,
    isBNPPPriceShown,
  } = props;
  const [showBNPPBottomSheet, setBNPPBottomSheet] = useState(false);
  const bnppConfigData = useConfigStore(configKeys.RAILS_BNPP_CONFIG);
  const bnppConfigDataJson: BNPP = !_isEmpty(bnppConfigData) ? JSON.parse(bnppConfigData) : {};
  const { traveler } = bnppConfigDataJson;
  const greySeatLockIcon = traveler?.secondaryIconUrl || '';
  const { fullAmount, partialAmount } = bnppResponse || {};
  const { bottomBar: { fullText, partialText, ctaText } = {} } = traveler || {};
  const { totalPartialAmount, totalFullAmount } = useMemo(
    () => calculateBNPPFare(partialAmount, fullAmount, numberOfPassengers, listingTotalFare),
    [partialAmount, fullAmount, numberOfPassengers, listingTotalFare],
  );

  if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }

  useEffect(() => {
    if (!isLoading) {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    }
  }, [isLoading]);

  const toggleBottomSheet = () => {
    trackTravellerPageEvent(
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_BNPP_ARROW}_${
        !showBNPPBottomSheet ? 'open' : 'close'
      }`,
    );
    props.toggleBNPPBottomSheet(!showBNPPBottomSheet);
    setBNPPBottomSheet(!showBNPPBottomSheet);
  };
  const bottom = useAnimatedBottom(showBNPPBottomSheet, 200);
  const payableAmount = isBnppOpted ? totalPartialAmount : totalFullAmount;
  if (!(payableAmount > 0)) {
    return null;
  }

  return (
    <View
      style={[styles.container, showBNPPBottomSheet && styles.bottomSheetContainer]}
      testID="traveler_details_bottom_bar_container"
    >
      {showBNPPBottomSheet ? (
        <View style={styles.fullFlex} testID="traveler_details_bottom_bar_full_flex">
          <TouchableOpacity
            style={styles.bottomTouchable}
            onPress={toggleBottomSheet}
            testID="traveler_details_bottom_bar_bottom_touchable_opacity"
          >
            <View />
          </TouchableOpacity>
          <View
            style={styles.bottomSheetContentContainer}
            pointerEvents="box-none"
            testID="traveler_details_bottom_bar_bottom_sheet_content_container"
          >
            <Animated.View style={[styles.bottomSheetBackground, { bottom }]}>
              <BookNowPayPartial
                isInBottomSheet={true}
                closeBottomSheet={toggleBottomSheet}
              />
            </Animated.View>
          </View>
        </View>
      ) : isBNPPPriceShown ? (
        <>
          <AvailDepletionStrip />
          <BookNowPayPartialStrip
            text={traveler?.bottomStripText}
            iconUrl={traveler?.iconUrl}
            type="TRAVELLER"
            price={totalPartialAmount}
          />
        </>
      ) : (
        <AvailDepletionStrip />
      )}
      <View style={styles.barContainer} testID="traveler_details_bottom_bar_bar_container">
        <View>
          <TouchableWithoutFeedback
            disabled={!isBNPPPriceShown}
            onPress={toggleBottomSheet}
            testID="traveler_details_bottom_bar_fare_container_touchable_without_feedback"
          >
            <View style={styles.fareContainer} testID="traveler_details_bottom_bar_fare_container">
              <Text
                style={[styles.payText, fontStyle('black')]}
                testID="traveler_details_bottom_bar_pay_text"
              >
                {fullText
                  ? isBnppOpted
                    ? partialText
                    : fullText
                  : _label(isBnppOpted ? 'paying_partial_amount' : 'paying_full_amount')}
              </Text>
              {isBNPPPriceShown && (
                <Image
                  source={showBNPPBottomSheet ? arrowDown : arrowUp}
                  style={styles.arrowIcon}
                  testID="traveler_details_bottom_bar_arrow_icon"
                />
              )}
              {isBNPPPriceShown && (
                <Image
                  source={{ uri: greySeatLockIcon }}
                  style={styles.bnppIcon}
                  testID="traveler_details_bottom_bar_bnpp_icon"
                />
              )}
            </View>
          </TouchableWithoutFeedback>
        </View>
        <View testID="traveler_details_bottom_bar_book_now_travellers_container">
          <BookNowTravellers
            BookNowButtonEnabled={props.bookNowButtonEnabled}
            onBookNowClicked={props.onBookNowClicked}
            buttonText={ctaText ?? _label('$continue', { uppercase: true })}
            showLoader={props.showLoader}
            id={props.id}
            payNowContainerStyle={styles.payNowContainerStyle}
            buttonContainer={styles.buttonContainer}
            hideShadow
          />
        </View>
      </View>
    </View>
  );
};

const mapStateToProps = (state: unknown) => {
  const {
    railsTraveler: {
      isBnppOpted = false,
      railofy: { isLoading, bnppResponse = {} } = {},
      selectedTravelers = [],
      travelers,
    } = {},
    railsListing: { totalFare = 0 },
  } = state;
  const isBNPPPriceShown = bnppResponse?.fullAmount > 0;
  const selectedTravelersList = getTravelersListFromId(selectedTravelers, travelers);
  const numberOfPassengers = filter(
    selectedTravelersList,
    (selectedTraveler) => selectedTraveler?.basicInfo?.age > 4,
  );
  return {
    isBnppOpted,
    bnppResponse,
    isLoading,
    numberOfPassengers: Math.max(numberOfPassengers?.length, 1),
    listingTotalFare: totalFare,
    isBNPPPriceShown,
  };
};
export default connect(mapStateToProps, null)(TravelerDetailsBottomBar);
