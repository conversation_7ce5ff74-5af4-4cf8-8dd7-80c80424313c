import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
export const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.transparent,
  },
  fullFlex: {
    flex: 1,
  },
  bottomSheetContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  bottomSheetContentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomSheetBackground: {
    width: '100%',
    justifyContent: 'flex-end',
  },
  bottomTouchable: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.modalgrey,
  },
  barContainer: {
    backgroundColor: colors.black28,
    paddingHorizontal: 16,
    paddingVertical: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 9999,
  },
  fareContainer: {
    flexDirection: 'row',
  },
  priceText: {
    fontSize: 18,
    fontWeight: '900',
    color: colors.white,
  },
  payText: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.white,
  },
  payNowContainerStyle: {
    backgroundColor: colors.transparent,
    paddingHorizontal: 0,
    paddingBottom: 0,
    paddingTop: 0,
  },
  buttonContainer: {
    paddingHorizontal: 20,
  },
  arrowIcon: {
    width: 24,
    height: 24,
    marginHorizontal: 8,
    resizeMode: 'contain',
    tintColor: colors.white,
  },
  bnppIcon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
});
