
import React from 'react';
import PropTypes from 'prop-types';
import {Text, View, StyleSheet} from 'react-native';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';

import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import travelerStyle from '../TravelerDetailsCSS';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import TravelerCard from './TravelerCard';
import WarningBox from './WarningBox';
import { isChild, RailsListingUtilsClass, TravelDetailsUtilsClass } from '../TravelerDetailsUtils';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { isApp } from '../../../Utils/device';

const RailsTraveler = (props) => {
  const {
    traveler,
    isSelected,
    onTravelerSelected,
    onEditTraveler,
    childBerthMandatory,
    railsListing,
    // seniorCitizenApplicable,
  } = props;

  const travelerDetailsUtilObj = new TravelDetailsUtilsClass(traveler);
  const railsListingUtilObj = new RailsListingUtilsClass(railsListing);
  return (
    <View
      key={`${traveler.basicInfo.age}|${traveler.basicInfo.name}|${traveler.basicInfo.travelerId}`}
    >
      <TouchableRipple
        onPress={() => {
          onTravelerSelected(traveler);
        }}
        key={`${traveler.basicInfo.age}|${traveler.basicInfo.name}|${traveler.basicInfo.travelerId}`}
      >
        <View style={{
          flexDirection: 'row',
          paddingVertical: 10,
        }}
          testID={props?.id}
        >
          <CheckBox
            textLine1=""
            isChecked={isSelected}
            onPress={() => {
              if (isApp()) {
                if (!traveler.isAgeSpecified) {
                  onEditTraveler(traveler);
                } else {
                  onTravelerSelected(traveler);
                }
              }
            }}
          />

          <View style={styles.travelerContainer}>
            <View style={travelerStyle.flexRowSpaceBetween}>
              <TravelerCard
                id={props?.id}
                traveler={traveler}
                childBerthMandatory={childBerthMandatory}
                railsListingUtilObj={railsListingUtilObj}
              />
              <TouchableRipple onPress={() => onEditTraveler(traveler)}>
                <Text style={[styles.editButton, fontStyle('bold'), getLineHeight(12)]} testID={`${props?.id}_editButton`}>
                  {_label('edit', { uppercase: true })}
                </Text>
              </TouchableRipple>
            </View>

            {traveler.isNameLengthExceed &&
              <WarningBox
                cardStyle={travelerStyle.greenBoxWarning}
                textStyle={[travelerStyle.warningBox, travelerStyle.latoRegularTextGreen12, fontStyle('regular'), getLineHeight(12)]}
                value={_label('name_length_exceed_warning')}
              />
            }

            {isSelected && traveler.isberthMismatch && traveler.showBerthMismatchWarning &&
              <WarningBox
                cardStyle={travelerStyle.creamWhiteWarning}
                textStyle={[travelerStyle.warningBox, travelerStyle.latoRegularTextYellow12, fontStyle('regular'), getLineHeight(12)]}
                value={traveler.berthMismatchWarningMessage}
              />
            }

            {
              isSelected && railsListingUtilObj.isSrCitizen(traveler.basicInfo.age, traveler.basicInfo.gender) &&
              travelerDetailsUtilObj.hasOptedForSrCitizenConcession()
              && !railsListingUtilObj.isSeniorCitizenApplicableOnTrain() &&
              <WarningBox
                cardStyle={travelerStyle.creamWhiteWarning}
                textStyle={[travelerStyle.warningBox, travelerStyle.latoRegularTextYellow12, fontStyle('regular'), getLineHeight(12)]}
                value={railsListingUtilObj.getSeniorCitizenWarningMessage()}
              />
            }

            {isSelected && railsListingUtilObj.isAgeProofRequired(traveler) &&
              <WarningBox
                cardStyle={travelerStyle.greenBoxWarning}
                textStyle={[travelerStyle.warningBox, travelerStyle.latoRegularTextGreen12, fontStyle('regular'), getLineHeight(12)]}
                value={`${(railsListingUtilObj.isInfant(traveler.basicInfo.age) &&
                  !traveler?.basicInfo?.childBerthFlag) ?
                   _label('infant_no_ticket_warning') : ''}${_label('age_proof_warning')
                    || _label('age_proof_required_warning')}`}
              />
            }

            {isSelected && traveler.isMaleTravelingInLadiesQuota && traveler.highlightTravelerForError &&
              <WarningBox
                cardStyle={travelerStyle.redWarning}
                textStyle={[travelerStyle.redWarningBox, travelerStyle.latoRegularTextRed12, fontStyle('regular'), getLineHeight(12)]}
                value={_label('male_travelling_in_ladies_quota_warning')}
              />
            }

            {isSelected &&
              traveler.isNotSrCitizenTravellingInSrCitizenQuota &&
              traveler.highlightTravelerForError && (
                <WarningBox
                  cardStyle={travelerStyle.redWarning}
                  textStyle={[
                    travelerStyle.redWarningBox,
                    travelerStyle.latoRegularTextRed12,
                    fontStyle('regular'),
                    getLineHeight(12),
                  ]}
                  value={_label('not_sr_citizen_travelling_in_sr_citizen_quota_warning')}
                />
              )}

            {
              isSelected && childBerthMandatory &&
              !traveler.basicInfo.childBerthFlag && isChild(traveler.basicInfo.age) &&
              <WarningBox
                cardStyle={travelerStyle.lemonYellowWarning}
                textStyle={[travelerStyle.redWarningBox, travelerStyle.latoRegularTextLemonYellow12, fontStyle('regular'), getLineHeight(12)]}
                value={_label('child_berth_mandatory_warning')}
              />
            }

            {!traveler.isAgeSpecified &&
              <WarningBox
                cardStyle={travelerStyle.redWarning}
                textStyle={[travelerStyle.redWarningBox, travelerStyle.latoRegularTextRed12, fontStyle('regular'), getLineHeight(12)]}
                value={_label('age_not_specified')}
              />
            }
          </View>
        </View>
      </TouchableRipple>
    </View>
  );
};

const styles = StyleSheet.create({
  travelerContainer: {
    flex: 1,
    flexDirection: 'column',
    paddingLeft: 10,
  },
  editButton: {
    color: colors.azure,
    fontSize: 12,
    letterSpacing: 0,
  },
});

RailsTraveler.propTypes = {
  traveler: PropTypes.object.isRequired,
  isSelected: PropTypes.bool.isRequired,
  onTravelerSelected: PropTypes.func.isRequired,
  onEditTraveler: PropTypes.func.isRequired,
  childBerthMandatory: PropTypes.bool.isRequired,
  railsListing: PropTypes.object.isRequired,
  id: PropTypes.string,
};
export default RailsTraveler;
