import React from 'react';
import {Text, View} from 'react-native';
import includes from 'lodash/includes';
import {PropTypes} from 'prop-types';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import travelerStyle,{travelerTextStyle} from '../TravelerDetailsCSS';
import AddViewTravelerCard from './AddViewTravelerCard';
import WarningBox from './WarningBox';
import RailsTraveler from './RailsTraveler';
import {COMPONENT_TRAVELER} from '../TravelerDetailsActions';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { SCHEDULE_TATKAL_REF_COMPONENTS } from '../../ScheduleTatkalTravelerPage/constants/ScheduleTatkalTravelerPageConstants';

export default class RailsTravelersList extends React.Component {
  renderTravelerList = () => {
    const {
      travelers,
      selectedTravelers,
      onTravelerSelected,
      onEditTraveler,
      seniorCitizenApplicable,
      childBerthMandatory,
      railsListing,
    } = this.props;
    const {
      showChildWithoutBerthWarning,
      showMaxTravelerWarningForTatkal,
      showMaxSrCitizenTravelerWarning,
    } = this.props.warning;
    return (
      <View style={{ backgroundColor: colors.white }}>
        {showChildWithoutBerthWarning &&
          <WarningBox
            cardStyle={travelerStyle.redBoxWarning}
            textStyle={[travelerStyle.warningBox, travelerStyle.latoRegularTextRed12, fontStyle('regular'), getLineHeight(12)]}
            value={_label('child_without_berth_warning')}
          />
        }
        {showMaxTravelerWarningForTatkal && (
          <WarningBox
            cardStyle={travelerStyle.greenBoxWarning}
            textStyle={[travelerStyle.warningBox, travelerStyle.latoRegularTextGreen12, fontStyle('regular'), getLineHeight(12)]}
            value={_label('tatkal_max_traveller_warning')}
          />
        )}
        {showMaxSrCitizenTravelerWarning && (
          <WarningBox
            cardStyle={travelerStyle.greenBoxWarning}
            textStyle={[
              travelerStyle.warningBox,
              travelerStyle.latoRegularTextGreen12,
              fontStyle('regular'),
              getLineHeight(12),
            ]}
            value={_label('sr_citizen_max_traveller')}
          />
        )}
        {travelers.length === 0 && (
          <View style={styles.noTravellerTextContainer}>
            <Text style={[styles.noTravellerText, fontStyle('regular'), getLineHeight(12)]}>
              {_label('no_saved_travellers')}
            </Text>
          </View>
        )}
        <View>
          {
            travelers.map((traveler, index) => (
              <RailsTraveler
                labels={this.props.labels}
                seniorCitizenApplicable={seniorCitizenApplicable}
                traveler={traveler}
                isSelected={includes(selectedTravelers, traveler.travelerId)}
                onTravelerSelected={onTravelerSelected}
                onEditTraveler={onEditTraveler}
                key={traveler.basicInfo.travelerId}
                childBerthMandatory={childBerthMandatory}
                railsListing={railsListing}
                id={`${this.props?.id}_${index + 1}`}
              />
            ))
          }
        </View>
      </View>
    );
  };

  render() {
    const {
      viewAllTravelers, onViewAllClicked, onAddTraveler, showViewAll,
    } = this.props;

    return (
      <View
        ref={(ref) => {
          if (this.props?.isScheduleTatkalPage) {
            this.props.captureRef(SCHEDULE_TATKAL_REF_COMPONENTS.COMPONENT_TRAVELER, ref);
          } else {
            this.props.captureRef(COMPONENT_TRAVELER, ref);
          }
        }}
        testID={this.props?.id}
      >
        <View style={styles.container}>
          <Text
            style={[
              travelerStyle.travelHeader,
              travelerTextStyle.getTravelHeaderFontStyle(),
              getLineHeight(22),
            ]}
          >
            {_label('traveller_details')}
          </Text>
          {this.renderTravelerList()}
          <AddViewTravelerCard
            viewAllTravelers={viewAllTravelers}
            onViewAllClicked={onViewAllClicked}
            onAddTraveler={onAddTraveler}
            showViewAll={showViewAll}
            id={`${this.props?.id}_addTravelerDetailsButton`}
          />
        </View>
      </View>
    );
  }
}

const styles = {
  container: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  noTravellerTextContainer: {
    backgroundColor: colors.creamWhite,
    marginTop: 10,
    marginBottom: 20,
    padding: 4,
    paddingLeft: 8,
  },
  noTravellerText: {
    fontSize: 12,
    color: colors.lightYello,
  },
};

RailsTravelersList.propTypes = {
  travelers: PropTypes.array.isRequired,
  selectedTravelers: PropTypes.array.isRequired,
  viewAllTravelers: PropTypes.bool.isRequired,
  onViewAllClicked: PropTypes.func.isRequired,
  onTravelerSelected: PropTypes.func.isRequired,
  warning: PropTypes.object.isRequired,
  onAddTraveler: PropTypes.func.isRequired,
  onEditTraveler: PropTypes.func.isRequired,
  childBerthMandatory: PropTypes.bool.isRequired,
  showViewAll: PropTypes.bool.isRequired,
  onRef: PropTypes.func,
  captureRef: PropTypes.func,
  railsListing: PropTypes.object.isRequired,
  seniorCitizenApplicable: PropTypes.bool,
  isScheduleTatkalPage: PropTypes.bool,
  id: PropTypes.string,
  labels: PropTypes.object,
};

RailsTravelersList.defaultProps = {
  captureRef: null,
};
