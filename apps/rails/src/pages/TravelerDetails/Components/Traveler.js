import {
  getMealPreferenceBasedOnApplicableFoodTypes,
  isAgeAndGenderSpecified,
  isAgeProofRequired,
  isAgeSpecifiedInTraveler,
  isInfant,
  isSrCitizen,
  stringCompare,
  validateForLadiesQuota,
  validateForSeniorCitizenQuota,
} from '../TravelerDetailsUtils';
import Berth, {isEqualsBerth} from '../../Constants/Berth';
import Meal from '../../Constants/MealNew';
import SrCitizenConcession from '../../Constants/SrCitizenConcession';
import {CHILD_MAX_AGE} from '../../../Utils/RailsConstant';
import {isTatkalQuota} from '../../Types/QuotaType';
import Gender from '../../Constants/Gender';
import {countries} from '../../Constants/Country';
import trim from 'lodash/trim';
import isUndefined from 'lodash/isUndefined';
import isNull from 'lodash/isNull';
import includes from 'lodash/includes';
import { _label } from '../../../vernacular/AppLanguage';

const BERTH_PREFERENCE_DEFAULT = Berth.NO_BERTH_PREF;
const MEAL_REPFERENCE_DEFAULT = Meal.NO;
const SENIOR_CITIZEN_CONSCESSION_DEFAULT = SrCitizenConcession.FULL_CONCESSION;



export default class Traveler {
  constructor(
    travelerId, name, age, gender, childBerthFlag,
    applicableBerthTypes, foodChoiceEnabled, selectedQuota, classValue, isSelected, seniorCitizenApplicable,
    berth = BERTH_PREFERENCE_DEFAULT, meal = MEAL_REPFERENCE_DEFAULT,
    seniorCitizenConcession = SENIOR_CITIZEN_CONSCESSION_DEFAULT, nationality = countries.IN,
    mealPreferenceArray = [], applicableFoodTypes, updateMealOption,
  ) {
    this.travelerId = travelerId;
    this.basicInfo = {
      travelerId,
      name,
      age,
      gender,
      berth,
      meal: getMealPreferenceBasedOnApplicableFoodTypes(meal, applicableFoodTypes,
                                                        mealPreferenceArray, updateMealOption),
      seniorCitizenConcession,
      nationality,
      childBerthFlag,
      mealPreferenceArray,
    };
    this.fullName = _getFullName(this.basicInfo.name);
    this.isGenderSpecified = !stringCompare(this.basicInfo.gender.key, Gender.NOT_SPECIFIED.key);
    this.isAgeSpecified = isAgeSpecifiedInTraveler(this.basicInfo.age);
    this.isberthMismatch = false;
    this.berthMismatchWarningMessage = '';
    this.showBerthMismatchWarning = false;
    this.isMaleTravelingInLadiesQuota = false;
    this.showMaleInLadiesQuotaWarning = false;
    this.isNotSrCitizenTravellingInSrCitizenQuota = false;
    this.showSrCitizenWarning = false;
    this.isFoodChoiceEnabled = false;
    this.isSrConcessionApplicable = isAgeAndGenderSpecified(this) &&
                                      isSrCitizen(this.basicInfo.age, this.basicInfo.gender);
    this.showSrConcessionApplicable = isAgeAndGenderSpecified(this) &&
                                      isSrCitizen(this.basicInfo.age, this.basicInfo.gender);
    this.isNameLengthExceed = false;
    this.ageProofRequired = false;
    this.isSelected = false;
    this.highlightTravelerForError = false;
    _validateTravelerInfoForMismatch(this, applicableBerthTypes, foodChoiceEnabled,
                                    selectedQuota, classValue, isSelected, seniorCitizenApplicable);
  }

}

export const isAdult = (traveler) => parseInt(traveler.basicInfo.age, 10) > CHILD_MAX_AGE;
const _getFullName = (name) => {
  let fullName = name.firstName;
  if (!isUndefined(name.middleName) && !isNull(name.middleName)) {
    fullName = `${fullName} ${name.middleName}`;
  }
  if (!isUndefined(name.lastName) && !isNull(name.lastName)) {
    fullName = `${fullName} ${name.lastName}`;
  }

  return trim(fullName);
};



const _validateTravelerInfoForMismatch = (traveler, applicableBerthTypes, foodChoiceEnabled,
                              selectedQuota, classValue, isSelected, seniorCitizenApplicable) => {
  traveler.isberthMismatch =
    !includes(applicableBerthTypes, traveler?.basicInfo?.berth?.key) &&
    !isEqualsBerth(traveler?.basicInfo?.berth, BERTH_PREFERENCE_DEFAULT);
                              // traveler.basicInfo.berth.key === Berth.NO_BERTH_PREF.key

  traveler.basicInfo.berth =
    traveler?.isberthMismatch && isSelected ? BERTH_PREFERENCE_DEFAULT : traveler?.basicInfo?.berth;
  traveler.berthMismatchWarningMessage = traveler?.isberthMismatch
    ? _label('berth_mismatch_warning', undefined, { berthClass: _label(classValue.value) })
    : '';
  traveler.showBerthMismatchWarning = isSelected && !isInfant(traveler?.basicInfo?.age);

  traveler.isFoodChoiceEnabled = foodChoiceEnabled;

  traveler.isSrConcessionApplicable = seniorCitizenApplicable &&
          traveler.isSrConcessionApplicable && !isTatkalQuota(selectedQuota);

  traveler.isNameLengthExceed = traveler?.fullName?.length > 16;
  traveler.fullName = traveler?.isNameLengthExceed
    ? traveler?.fullName?.substring?.(0, 16)
    : traveler?.fullName;

  traveler.ageProofRequired = isAgeProofRequired(traveler);

  if (isAgeAndGenderSpecified(traveler)) {
    traveler.isMaleTravelingInLadiesQuota = validateForLadiesQuota(
      selectedQuota,
      traveler?.basicInfo?.age,
      traveler?.basicInfo?.gender,
    );
    traveler.showMaleInLadiesQuotaWarning = isSelected;

    if (selectedQuota.id === 'SS') {
      traveler.isNotSrCitizenTravellingInSrCitizenQuota = !validateForSeniorCitizenQuota(
        selectedQuota,
        traveler?.basicInfo?.age,
        traveler?.basicInfo?.gender,
      );
      traveler.showSrCitizenWarning = isSelected;
    }

    traveler.highlightTravelerForError =
      (traveler?.isMaleTravelingInLadiesQuota && traveler?.showMaleInLadiesQuotaWarning) ||
      (traveler?.isNotSrCitizenTravellingInSrCitizenQuota && traveler?.showSrCitizenWarning);
  }
};
