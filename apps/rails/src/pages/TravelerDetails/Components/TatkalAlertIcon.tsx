import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { configKeys } from '@mmt/rails/src/configStore/Common/constants';
import useConfigStore from '@mmt/rails/src/configStore/Common/RailsConfigStoreUtils';
import React, { useState } from 'react';
import { Dimensions, StyleSheet, Text, View, TouchableOpacity, Modal, Image } from 'react-native';
import { trackClickEventProp61 } from '../../RailsBusHomePage/Analytics';
import {
  RAILS_LISTING,
  SCHEDULE_TATKAL_EVENTS,
} from '@mmt/rails/src/Utils/RailsConstant';

const { width } = Dimensions.get('screen');
import swipe_icon from '@mmt/legacy-assets/src/rails/Frame.png';
interface TatkalAlertIconProps {
  onClick: () => void;
  positionY: number;
  updateTatkalAlertTooltip: () => void;
}
const TatkalAlertIcon = ({
  onClick,
  positionY,
  updateTatkalAlertTooltip,
}: TatkalAlertIconProps) => {
  const [isModalVisible, setModalVisible] = useState(true);
  const configVal = useConfigStore(configKeys.RAILS_TATKAL_ALERT_TOOLTIP);

  const handleClose = () => {
    setModalVisible(false);
    updateTatkalAlertTooltip();
  };

  const handleSwipe = () => {
    trackClickEventProp61(RAILS_LISTING, SCHEDULE_TATKAL_EVENTS.tatkalAlertTooltipClick);
    onClick();
    setModalVisible(false);
    updateTatkalAlertTooltip();
  };
  return (
    isModalVisible && (
      <Modal transparent visible={isModalVisible}>
        <TouchableOpacity
          accessible={false}
          style={styles.touchableOpaceStyle}
          onPressIn={() => handleClose()}
        >
          <View style={styles.flex_1}>
            <TouchableOpacity
              onPress={handleSwipe}
              style={[styles.tatkalAlertContainer, { top: positionY + 5 }]}
            >
              <Text style={styles.tatkalAlertText}>{configVal?.title}</Text>
              <Text style={styles.tatkalAlertSubText}>{configVal?.subTitle}</Text>
              <View style={styles.swipeContainer}>
                <Image source={swipe_icon} style={styles.swipeIcon} />
                <Text style={styles.swipeText}>SWIPE</Text>
              </View>
              <View style={styles.tooltipArrow} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    )
  );
};

const styles = StyleSheet.create({
  tatkalAlertContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: colors.black,
    borderRadius: 12,
    position: 'absolute',
    right: 20,
    width: 0.3 * width,
    zIndex: 999,
    flex: 1,
  },
  touchableOpaceStyle: {
    width: '100%',
    height: '100%',
  },
  flex_1: {
    flex: 1,
  },
  tatkalAlertText: {
    fontSize: 16,
    fontWeight: '700',
    fontFamily: 'Lato',
    fontStyle: 'italic',
    color: colors.white,
  },
  swipeIcon: {
    height: 12,
    width: 12,
    marginRight: 5,
  },
  tatkalAlertSubText: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'Lato',
    color: colors.white,
  },
  swipeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 5,
    marginTop: 10,
    width: '75%',
  },
  tatkalAlertIcon: {
    width: 20,
    height: 20,
  },
  swipeText: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '400',
    color: colors.black,
  },
  tooltipArrow: {
    position: 'absolute',
    right: -10,
    top: '60%',
    transform: [{ translateY: -5 }],
    width: 0,
    height: 0,
    borderTopWidth: 8,
    borderBottomWidth: 8,
    borderLeftWidth: 10,
    borderTopColor: colors.transparent,
    borderBottomColor: colors.transparent,
    borderLeftColor: colors.black,
  },
});

export default TatkalAlertIcon;
