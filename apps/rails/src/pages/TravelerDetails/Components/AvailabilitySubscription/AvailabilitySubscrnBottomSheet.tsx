import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Image, View, TouchableOpacity, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { styles } from './AvailabilitySubscrnBottomSheet.styles';
import ASSETS from '../../../../Utils/Assets/RailsAssets';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  displayAvailSubscrnBottomSheet,
  updateAvailabilitySubscription,
} from '../../TravelerDetailsActions';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { trackTravellerPageEvent } from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import {
  logTravellerPageBottomSheetEvents,
} from '../../../Review/RailsReviewActions';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

const enabledColors = ['#53b2ff', '#065af3'];

interface AvailabilitySubscrnBottomSheetProps {
  closeAvailSubscrnBottomSheet: () => void;
  updateAvailabilitySubscription: () => void;
  isAvailSubscrnEnabled: boolean;
  logTravellerPageBottomSheetEvents: (eventValue: unknown) => void;
}

const AvailabilitySubscrnBottomSheet = ({
  closeAvailSubscrnBottomSheet,
  updateAvailabilitySubscription,
  isAvailSubscrnEnabled,
  logTravellerPageBottomSheetEvents,
}: AvailabilitySubscrnBottomSheetProps) => {
  const [availSubscrnConfig, setAvailSubscrnConfig] = useState({});

  useEffect(() => {
    async function initializeAvailAlertConfig() {
      const config = await getConfigStore(configKeys.RAILS_AVAILABILITY_SUBSCRIPTION);
      setAvailSubscrnConfig(config?.bottomSheet || {});
    }
    initializeAvailAlertConfig();
    logTravellerPageBottomSheetEvents(
      TRAVELLER_CLICK_EVENTS.AVAILABILITY_SUBSCRIPTION_BOTTOM_SHEET,
    );
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_BS_SHOWN);
  }, []);

  const onPrimaryButtonClick = () => {
    updateAvailabilitySubscription();
    closeAvailSubscrnBottomSheet();
  };
  return (
    <>
      <View style={styles.container} testID="avail_subscrn_bottomsheet_container">
        <TouchableOpacity
          style={styles.stickyCloseButton}
          onPress={() => {
            closeAvailSubscrnBottomSheet();
          }}
          testID="avail_subscrn_bottomsheet_close_button"
        >
          <Image
            source={ASSETS.crossIcon}
            style={styles.closeIcon}
            testID="avail_subscrn_bottomsheet_close_icon"
          />
        </TouchableOpacity>
        <Image
          style={styles.icon}
          source={ASSETS.availabilitySubscriptionIcon}
          testID="avail_subscrn_bottomsheet_icon"
        />
        <Text style={styles.title} testID="avail_subscrn_bottomsheet_title">
          {isAvailSubscrnEnabled
            ? availSubscrnConfig?.enabled?.title
            : availSubscrnConfig?.disabled?.title}
        </Text>
        <Text style={styles.description} testID="avail_subscrn_bottomsheet_description">
          {isAvailSubscrnEnabled
            ? availSubscrnConfig?.enabled?.description
            : availSubscrnConfig?.disabled?.description}
        </Text>
        <View style={styles.buttonContainer} testID="avail_subscrn_bottomsheet_button_container">
          <TouchableOpacity
            onPress={onPrimaryButtonClick}
            feedbackColor={colors.transparent}
            style={styles.blueButton}
            testID="avail_subscrn_bottomsheet_primary_button"
          >
            <LinearGradient
              style={styles.linearGrad}
              colors={enabledColors}
              start={{
                x: 0,
                y: 2,
              }}
              end={{
                x: 1,
                y: 1,
              }}
              testID="avail_subscrn_bottomsheet_primary_button_linear_gradient"
            >
              <Text
                style={styles.blueButtonText}
                testID="avail_subscrn_bottomsheet_primary_button_text"
              >
                {_label(isAvailSubscrnEnabled ? 'remove_alert' : 'yes_notify_me', {
                  uppercase: true,
                })}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={closeAvailSubscrnBottomSheet}
            feedbackColor={colors.transparent}
            style={styles.secondaryButton}
            testID="avail_subscrn_bottomsheet_secondary_button"
          >
            <Text
              style={styles.secondaryButtonText}
              testID="avail_subscrn_bottomsheet_secondary_button_text"
            >
              {_label(isAvailSubscrnEnabled ? 'got_it' : 'skip', { uppercase: true })}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

const mapStateToProps = (state: unknown) => {
  const {
    railsTraveler: { availabilitySubscription: { isAvailSubscrnEnabled = false } = {} } = {},
  } = state;

  return {
    isAvailSubscrnEnabled,
  };
};

const mapDispatchToProps = (dispatch: unknown) => ({
  closeAvailSubscrnBottomSheet: () => dispatch(displayAvailSubscrnBottomSheet(false)),
  updateAvailabilitySubscription: () => dispatch(updateAvailabilitySubscription()),
  logTravellerPageBottomSheetEvents: (eventValue: unknown) =>
    dispatch(logTravellerPageBottomSheetEvents(eventValue)),
});

export default connect(mapStateToProps, mapDispatchToProps)(AvailabilitySubscrnBottomSheet);
