import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { Platform, StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: colors.white,
    flexDirection: 'column',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: Platform.OS === 'android' ? 20 : 0,
  },
  icon: {
    width: 40,
    height: 40,
  },
  closeIcon: {
    width: 24,
    height: 24,
  },
  stickyCloseButton: {
    marginLeft: 'auto',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    lineHeight: 21,
    color: colors.black,
    marginTop: 12,
  },
  description: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 19,
    color: colors.textGrey,
    marginTop: 5,
    marginBottom: 40,
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 8,
  },
  linearGrad: {
    width: '100%',
    height: 44,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  blueButton: {
    width: '100%',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blueButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 19,
  },
  secondaryButton: {
    width: '100%',
    height: 44,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: colors.blue,
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 19,
  },
});
