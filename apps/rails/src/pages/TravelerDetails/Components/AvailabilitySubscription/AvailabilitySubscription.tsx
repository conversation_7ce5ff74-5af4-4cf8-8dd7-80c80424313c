import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Image, View, Text } from 'react-native';
import ASSETS from '../../../../Utils/Assets/RailsAssets';
import { styles } from './AvailabilitySubscription.styles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import {
  displayAvailSubscrnBottomSheet,
  updateAvailabilitySubscription,
} from '../../TravelerDetailsActions';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import ToggleSlider from './ToggleSlider';

interface AvailabilitySubscriptionProps {
  openAvailSubscrnBottomSheet: () => void;
  updateAvailabilitySubscription: () => void;
  showAvailSubscrnBottomSheet: boolean;
  isAvailSubscrnEnabled: boolean;
}

const AvailabilitySubscription = ({
  updateAvailabilitySubscription,
  openAvailSubscrnBottomSheet,
  isAvailSubscrnEnabled,
}: AvailabilitySubscriptionProps) => {
  const [availSubscrnConfig, setAvailSubscrnConfig] = useState({});

  useEffect(() => {
    async function initializeAvailAlertConfig() {
      const config = await getConfigStore(configKeys.RAILS_AVAILABILITY_SUBSCRIPTION);
      setAvailSubscrnConfig(config?.widget || {});
    }
    initializeAvailAlertConfig();
  }, []);

  const onSwitchChange = () => {
    updateAvailabilitySubscription();
  };

  return (
    <View style={isAvailSubscrnEnabled ? styles.containerActive : styles.containerDisabled}>
      <Image style={styles.icon} source={ASSETS.availabilitySubscriptionIcon} />
      <View style={styles.textContainer}>
        <Text>
          <Text style={styles.text}>
            {isAvailSubscrnEnabled
              ? availSubscrnConfig?.enabledText
              : availSubscrnConfig?.disabledText}
          </Text>
          <Text style={styles.knowMoreText} onPress={openAvailSubscrnBottomSheet}>
            {'  '}
            {_label('know_more', { sentenceCase: true })}
          </Text>
        </Text>
      </View>
      <ToggleSlider isEnabled={isAvailSubscrnEnabled} onSwitchChange={onSwitchChange} />
    </View>
  );
};

const mapStateToProps = (state: unknown) => {
  const {
    railsTraveler: { availabilitySubscription: { isAvailSubscrnEnabled = false } = {} } = {},
  } = state;

  return {
    isAvailSubscrnEnabled,
  };
};

const mapDispatchToProps = (dispatch: unknown) => ({
  openAvailSubscrnBottomSheet: () => dispatch(displayAvailSubscrnBottomSheet(true)),
  updateAvailabilitySubscription: () => dispatch(updateAvailabilitySubscription()),
});

export default connect(mapStateToProps, mapDispatchToProps)(AvailabilitySubscription);
