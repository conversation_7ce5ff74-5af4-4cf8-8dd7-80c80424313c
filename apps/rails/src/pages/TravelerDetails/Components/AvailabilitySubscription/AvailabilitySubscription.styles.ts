import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  containerDisabled: {
    width: '100%',
    minHeight: 50,
    borderRadius: 8,
    backgroundColor: colors.grayBg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 12,
  },
  containerActive: {
    width: '100%',
    minHeight: 50,
    borderRadius: 8,
    backgroundColor: colors.lighterBlue,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 12,
  },
  icon: {
    width: 24,
    height: 24,
  },
  textContainer: {
    maxWidth: 240,
    marginLeft: 12,
  },
  text: {
    color: colors.textGrey,
    fontSize: 14,
    lineHeight: 16,
    marginLeft: 12,
    paddingLeft: 50,
  },
  knowMoreText: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '700',
    color: colors.azure,
  },
  customSwitchTrackDisabled: {
    width: 46,
    height: 28,
    borderRadius: 25,
    borderColor: colors.lightGrey17,
    borderWidth: 1.5,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'flex-start',
    backgroundColor: colors.lighterBlue,
    marginLeft: 'auto',
  },
  customSwitchTrackActive: {
    width: 46,
    height: 28,
    borderRadius: 25,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'flex-end',
    backgroundColor: colors.primary,
    marginLeft: 'auto',
  },
  customSwitchButtonDisabled: {
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: colors.midnightBlue,
  },
  customSwitchButtonActive: {
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: colors.white,
  },
});
