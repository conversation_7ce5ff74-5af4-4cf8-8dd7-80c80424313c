import React, { useRef } from 'react';
import { TouchableOpacity, Animated } from 'react-native';
import { styles } from './AvailabilitySubscription.styles';

interface CustomSwitchProps {
  isEnabled: boolean;
  onSwitchChange: () => void;
}

const ToggleSlider = ({ isEnabled, onSwitchChange }: CustomSwitchProps) => {
  const animation = useRef(new Animated.Value(isEnabled ? 1 : 0)).current;

  Animated.timing(animation, {
    toValue: isEnabled ? 1 : 0,
    duration: 100,
    useNativeDriver: true,
  }).start();

  const thumbTranslate = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const backgroundColor = animation.interpolate({
    inputRange: [0, 1],
    outputRange: ['#EAF5FF', '#008CFF'],
  });

  const thumbColor = animation.interpolate({
    inputRange: [0, 1],
    outputRange: ['#0000004D', '#FFFFFF'],
  });

  return (
    <TouchableOpacity
      style={[
        isEnabled ? styles.customSwitchTrackActive : styles.customSwitchTrackDisabled,
        { backgroundColor },
      ]}
      onPress={onSwitchChange}
    >
      <Animated.View
        style={[
          isEnabled ? styles.customSwitchButtonActive : styles.customSwitchButtonDisabled,
          { transform: [{ translateX: thumbTranslate }], backgroundColor: thumbColor },
        ]}
      />
    </TouchableOpacity>
  );
};

export default ToggleSlider;
