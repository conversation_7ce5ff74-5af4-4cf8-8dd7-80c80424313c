import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import MyraBotCTA from '@Frontend_Ui_Lib_App/MyraBotCTA';
import { useChatBot } from './useChatBot';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

const MyraBot = () => {
  const [show, setShow] = useState(false);
  const { unmount, expandIfShown } = useChatBot({
    shouldShow: show,
    onDismiss: () => {
      setShow(false);
    },
  });
  useEffect(() => {
    return () => {
      unmount();
    };
  }, [unmount]);
  const openMyraBot = () => {
    setShow(true);
    expandIfShown();
  };
  return (
    <View style={styles.chatbotContainer}>
      <MyraBotCTA onPress={openMyraBot} />
    </View>
  );
};

const styles = StyleSheet.create({
  chatbotContainer: {
    position: 'absolute',
    zIndex: 9,
    elevation: 4,
    bottom: 85,
    right: 16,
    backgroundColor: colors.transparent,
  },
});

export default MyraBot;
