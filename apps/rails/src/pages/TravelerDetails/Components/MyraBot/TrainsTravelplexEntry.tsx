import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getShowTravelplexPokus } from 'apps/rails/src/RailsAbConfig';
import {
  TravelPlexBot,
  TravelPlexBotProps,
  TravelPlexBotRef,
  TravelplexFloatingCta,
} from '@travelplex/react-native';
import { OnActionProps } from '@travelplex/react-native/src/containers/types';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { TRAVELERS_PAGE_TRACKING_KEY_NEW, LISTING_PAGE_TRACKING_KEY } from '@mmt/rails/src/Utils/RailsConstant';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';

const { TRAVELLER, LISTING } = RAIL_EVENTS;

const chatConfigFallback: TravelPlexBotProps['chatConfig'] = {
  context: {
    lob: 'RAILS',
    lobCategory: null,
    view: 'TRAVELLERS',
    project: 'ris_chatbot',
    prevPage: null,
    platform: Platform.OS,
  },
  contextMetadata: {
    pageContext: {
      lob: 'rails',
      lobCategory: 'rails',
      pageName: 'travellers',
      prevPageName: 'listing',
    },
    searchContext: {},
  },
  expertMetaData: {},
};

const tickersFallback: string[] = ["Hi, I'm Myra", 'Need to plan a trip?', 'Ask me anything!'];

interface TravelPlexTrainsEntry  {
  chatConfig: TravelPlexBotProps['chatConfig'];
  tickers: string[];
  pageType?: 'traveler' | 'listing';
}

export const TravelplexTrainsEntry = ({
  chatConfig,
  tickers,
  pageType = 'traveler',
}: TravelPlexTrainsEntry) => {
  const trackingKey = pageType === 'listing' ? LISTING_PAGE_TRACKING_KEY : TRAVELERS_PAGE_TRACKING_KEY_NEW;
  const events = pageType === 'listing' ? LISTING : TRAVELLER;

  const [showChatBot, setShowChatBot] = useState<boolean>(false);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);
  const chatBotRef = useRef<TravelPlexBotRef>(null);

  const finalChatConfig = (_isEmpty(chatConfig) || !chatConfig) ?
  chatConfigFallback : chatConfig;

  const finalTickers = (_isEmpty(tickers) || !tickers) ?
  tickersFallback : tickers;

  const openChatBotOverlay = () => {
    trackClickEventProp61(
      trackingKey,
      events?.RAILS_LISTING_TLX_CTA_CLICK || events?.RAILS_TRAVELLER_TLX_CTA_CLICK,
    );
    setShowChatBot(true);
    chatBotRef.current?.expand({
      chatConfig: finalChatConfig,
    });
    // trackEvent(FEED_LANDING_EVENTS.trvlplex_click_event);
    unreadMessageCount > 0 && setUnreadMessageCount(0);
  };

  useEffect(() => {
    const showTravelPlexFromPokus = getShowTravelplexPokus() === 1;
    if (showTravelPlexFromPokus) {
      trackClickEventProp61(
        trackingKey,
        events?.RAILS_LISTING_TLX_CTA_IMPRESSION || events?.RAILS_TRAVELLER_TLX_CTA_IMPRESSION,
      );
    }
  }, []);

  const isChatShownRef = useRef(showChatBot);
  useEffect(() => {
    if (!showChatBot && showChatBot !== isChatShownRef.current) {
      // trackEvent(FEED_LANDING_EVENTS.trvlplex_close_event);
    }
    isChatShownRef.current = showChatBot;
  }, [showChatBot]);
  const onActionCallback = useCallback((onActionProps: OnActionProps) => {
    if (onActionProps.actionType !== 'Analytics') {
      return;
    }
    const { actionPayload } = onActionProps;
    if (!Array.isArray(actionPayload?.tracking)) {
      return;
    }
    const hasSendMsgEvent = actionPayload.tracking.some((ev: unknown) => {
      if (typeof ev !== 'object' || ev === null) {
        return false;
      }
      return (ev as Record<string, unknown>)?.pdtTrackingId === 'travelplex_send_msg';
    });
    if (hasSendMsgEvent) {
      trackClickEventProp61(
        trackingKey,
        events?.RAILS_LISTING_TLX_CHAT_SEND_ACTION || events?.RAILS_TRAVELLER_TLX_CHAT_SEND_ACTION,
      );
    }
  }, []);
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const onViewStateChange = useCallback(() => {}, []);
  return (
    <>
      <View style={styles.chatbotContainer} testID="travelplex_chatbot_container">
        <TravelplexFloatingCta
          textScrollProps={{
            texts: finalTickers,
          }}
          onPress={openChatBotOverlay}
          shouldAnimate={true}
          showNewMessageIcon={false}
        />
      </View>
      <View style={StyleSheet.absoluteFillObject} testID="travelplex_chatbot_view">
        <TravelPlexBot
          ref={chatBotRef}
          onViewStateChange={onViewStateChange}
          chatConfig={finalChatConfig}
          onAction={onActionCallback}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  chatbotContainer: {
    position: 'absolute',
    zIndex: 9,
    elevation: 4,
    bottom: 130,
    right: 16,
    backgroundColor: colors.transparent,
  },
});
