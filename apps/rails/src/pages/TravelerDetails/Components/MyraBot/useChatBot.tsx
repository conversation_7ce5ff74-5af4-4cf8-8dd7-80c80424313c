import { useCallback, useEffect } from 'react';
import { DeviceEventEmitter, NativeModules, Platform } from 'react-native';
import { chat_bot_url } from './Constant';

interface ChatBotProps {
  shouldShow: boolean;
  onDismiss: () => void;
  railIdentifiers?: Record<string, unknown>;
}
export const isMyraNativeModuleAvailable = (): boolean => {
  return !!NativeModules.MyraNativeModule;
};

export const useChatBot = (props: ChatBotProps) => {
  if (!isMyraNativeModuleAvailable()) {
    if (__DEV__ && Platform.OS === 'android') {
      throw new Error(
        'MyraNativeModule is not available. Please make sure you have the latest native build installed',
      );
    }
    return {
      unmount: () => {
        // No-op: MyraNativeModule not available
      },
      expandIfShown: () => {
         // No-op: MyraNativeModule not available
      },
    };
  }
  const { onDismiss, shouldShow } = props;

  const uri = chat_bot_url;
  const myraViewId = 'rails-chatbot';
  const { MyraNativeModule } = NativeModules;

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (!shouldShow) {
      MyraNativeModule.isMyraViewShown(myraViewId)
        .then((isShown: boolean) => {
          if (isShown) {
            return MyraNativeModule.minimiseMyraView(myraViewId);
          }
          return undefined;
        })
        .catch(() => {
            /* Intentionally empty */
        });
      return;
    }
    MyraNativeModule.showMyraView(myraViewId, uri)
      .then(() => {
        return undefined;
      })
      .catch(() => {
          /* Intentionally empty */
      });

    const chatBotSubscription = DeviceEventEmitter.addListener(myraViewId, (msg: unknown) => {
      switch (msg.type) {
        case 'CloseWebView':
          MyraNativeModule.removeMyraView(myraViewId);
          onDismiss();
          break;

        case 'MinimizeWebView':
          MyraNativeModule.minimiseMyraView(myraViewId);
          onDismiss();
          break;

        case 'Action':
        case 'ActionWithCallback':
        case 'PageFinishedLoading':
        case 'PageLoadStarted':
        case 'PageReceivedError':
          // No additional actions for these cases
          break;
      }
    });

    return () => {
      chatBotSubscription.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldShow, uri]);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const unmount = useCallback(() => {
    MyraNativeModule?.removeMyraView(myraViewId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const expandIfShown = useCallback(() => {
    if (!shouldShow) {
      return;
    }
    MyraNativeModule?.showMyraView(myraViewId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldShow]);
  return {
    unmount,
    expandIfShown,
  };
};
