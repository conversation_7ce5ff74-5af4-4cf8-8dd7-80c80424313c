import PropTypes from 'prop-types';
import React, {Component} from 'react';
import {Keyboard, StyleSheet, View} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import { showLongToast, showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {trackTravellerPageEvent} from '../../../railsAnalytics';
import {getTrvListForCouponApply, validateCoupon} from '../TravelerDetailsUtils';
import { _label } from '../../../vernacular/AppLanguage';
import {checkEnglishKeyboard, fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import InputField from '@Frontend_Ui_Lib_App/InputField';

export default class CouponTextInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      couponText: '',
      error: null,
    };
  }

  onTextChange = (text) => {
    this.setState({couponText: text});
    checkEnglishKeyboard('error',text, this);
  };

  onApplyClick2 = () => {
    showShortToast(_label('no_longer_applicable_coupon_removed'));
  };

  onApplyClick = async () => {
    Keyboard.dismiss();
    trackTravellerPageEvent('mob_rail_travellers_coupon_apply_clicked');
    this.setState({error: null});
    const {couponText} = this.state;
    const {
      selectedQuota,
      classValue,
      selectedTrainInfo,
      totalFare,
      baseFare,
      childBerthMandatory,
      seniorCitizenApplicable,
      selectedTravelers,
      travelers,
      cmp,
      fcTgAvailableType,
      fcTgPrice,
      couponDataForAncillary,
    } = this.props;
    if (isEmpty(couponText)) {
      this.setState({error: _label('please_enter_coupon')});
      trackTravellerPageEvent('mob_rail_travellers_coupon_blank');
      return;
    }
    const passengerList = getTrvListForCouponApply(selectedTravelers,travelers,selectedQuota,
                          childBerthMandatory,seniorCitizenApplicable);

    const response = await validateCoupon({
      couponCode: couponText,
      selectedQuota,
      classValue,
      selectedTrainInfo,
      baseFare,
      totalFare,
      passengerList,
      cmp,
      fcTgAvailableType,
      fcTgPrice,
      couponDataForAncillary,
    });
    if (!response) {
      showShortToast(_label('something_went_wrong_retry'));
      trackTravellerPageEvent('mob_rail_travellers_coupon_blank');
      return;
    }
    if (response.errorDetails) {
      this.setState({error: response.errorDetails.errorMessage});
      trackTravellerPageEvent('mob_rail_travellers_coupon_blank');
      return;
    }
    if (response.status !== 'success') {
      showShortToast(_label('something_went_wrong_retry'));
      trackTravellerPageEvent('mob_rail_travellers_coupon_blank');
      return;
    }
    showLongToast(`Coupon ${response?.couponCode} Applied!`);
    this.props.setCouponData(response);
    this.props.addCouponToRecommendedList(response);
    this.setState({couponText: ''});
    trackTravellerPageEvent('mob_rail_travellers_coupon_success');
  };
  render() {
    return (
      <View testID="coupon_text_input_container">
        <InputField
          actionText={_label('apply')}
          onActionTextPress={this.onApplyClick}
          testID={`${this.props?.id}_inputCode`}
          label={''}
          placeholder={_label('enter_coupon_code')}
          placeholderTextColor={colors.disabledButton}
          inputProps={{
            autoCapitalize: 'words',
            onSubmitEditing: () => {
              Keyboard.dismiss();
            },
          }}
          value={this.state.couponText}
          onChangeText={this.onTextChange}
          isError={!!this.state.error}
          errorMessage={this.state.error}
          customStyle={{
            inputFieldStyle: [styles.inputFieldStyle, fontStyle('bold'), getLineHeight(16)],
            actionTextStyle: [styles.applyText, fontStyle('bold'), getLineHeight(14)],
          }}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row', borderRadius: 4, borderWidth: 1, alignItems: 'center', borderColor: colors.lightTextColor, height: 40,
  },
  textInput: {
    flex: 1,
    color: colors.defaultTextColor,
    fontSize: 11,
    paddingLeft: 12,
  },
  applyText: {
    color: colors.azure,
    fontSize: 14,
    fontFamily: fonts.bold,
  },
  errorText: {
     fontSize: 12,
     color: colors.red6,
    paddingTop: 8,
  },
  inputFieldStyle: {
    fontFamily: fonts.bold,
  },
});

CouponTextInput.propTypes = {
  selectedTrainInfo: PropTypes.object.isRequired,
  setCouponData: PropTypes.func.isRequired,
  addCouponToRecommendedList: PropTypes.func.isRequired,
  totalCollectibleAmount: PropTypes.number.isRequired,
  selectedQuota: PropTypes.object,
  classValue: PropTypes.object,
  totalFare: PropTypes.number,
  baseFare: PropTypes.number,
  childBerthMandatory: PropTypes.bool,
  seniorCitizenApplicable: PropTypes.bool,
  selectedTravelers: PropTypes.array,
  travelers: PropTypes.array,
  cmp: PropTypes.any,
  fcTgAvailableType: PropTypes.any,
  fcTgPrice: PropTypes.number,
  couponDataForAncillary: PropTypes.object,
  id: PropTypes.string,
};

CouponTextInput.defaultProps = {
  selectedQuota: {},
  classValue: {},
};
