import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  outerBorder: {
    borderWidth: 0.5,
    borderColor: colors.lightGray,
    marginBottom: 10,
  },
  assuredCnfmLoaderContainer: {
    backgroundColor: colors.white,
    paddingVertical: 32,
    alignItems: 'center',
  },
  cnfmLoaderText: {
    color: colors.governorBay,
    fontSize: 12,
    marginTop: 5,
  },

  freeCancellationContainer: {
    borderWidth: 0.5,
    borderColor: colors.lightGray,
    marginBottom: 10,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  fcTgContainer: {
    paddingHorizontal: 16,
    overflow: 'hidden',
    backgroundColor: colors.grayBg,
    paddingBottom: 12,
  },
  orText: {
    color: colors.azureBlue6,
    fontSize: 14,
    flex: 1,
    marginBottom: 8,
    textAlign: 'center',
  },
  discountImage: {
    height: 18,
    width: 18,
    marginRight: 3,
  },
  discountContainer: {
    flexDirection: 'row',
    padding: 10,
    paddingTop: 22,
    paddingHorizontal: 14,
    paddingBottom: 8,
    borderRadius: 16,
    borderColor: colors.lightGreen9,
    borderWidth: 1,
    zIndex: -1,
    position: 'relative',
    backgroundColor: colors.white,
  },
  graidentStyle: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    borderRadius: 16,
    width: '60%',
  },
  textStyle: { color: colors.lightGreen16, fontSize: 14 },
  isFcDiscountedContainer: {
    top: 20,
    paddingTop: 6,
    paddingBottom: 20,
    marginTop: -20,
    paddingHorizontal: 32,
  },
  isTgDiscountedContainer: {
    top: -25,
    paddingTop: 20,
    paddingBottom: 6,
    marginBottom: -20,
    paddingHorizontal: 32,
  },
  isFcTgDiscountedContainer: {
    paddingTop: 23,
    paddingBottom: 26,
    marginTop: -20,
    marginBottom: -20,
    borderWidth: 0,
  },
});
