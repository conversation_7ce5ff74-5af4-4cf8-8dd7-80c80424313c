import { BnppResponse } from '../BookNowPayPartial/types';
import { TgPlusResponse, TgPlusConfig  } from '../TripGuaranteePlus/types';

export interface RailofyProps {
  isFCTGBothSelected: unknown;
  isLoading: boolean;
  fetchRailofyData: () => void;
  railofyType: string; // @TODOSFCTG use enum
  captureRef: () => void;
  pageId: string;
  railofyErrorMessage: string;
  tripGuaranteeData: unknown;
  freeCancellationData: FreeCancellationData;
  tgPlusData: TgPlusResponse;
  isFcDiscounted: boolean;
  isTgDiscounted: boolean;
  fcDiscountValue: number;
  tgDiscountValue: number;
  ancillaryType: string;
  ancillaryDiscountDetails: unknown;
  isTGSelected: boolean;
}

export interface FreeCancellationData {
  header: string;
  subHeader: string;
  freeCancellationInfo: string;
  noSelectedOptionInfo: string;
  socialProofing: string[];
  cancellationOptions: {
    value: boolean;
    optionInfo: string;
    refundInfo: string | null;
    refundAmount: string | null;
    insuranceAmount: number | null;
    paxInfo: string | null;
    additionalDetails: string | null;
    additionalDetailsURL: string | null;
    onSelectInfo: string | null;
    totalFareWithInsuranceAmount: number | null;
  }[]
}

export interface FreeCancellationProps {
  selectedIndex: number | null;
  hasError: boolean;
  updateUserResponse: (index: number, premiumPerPerson: number) => void;
  updateFCTGUserResponse: (index: number, premiumPerPerson: number) => void;
  freeCancellationData: FreeCancellationData;
  openWebView: (data: { url: string }) => void;
  captureRef: (comp: string, ref: unknown) => void;
  fcUpgradeOption: number | null;
  id?: string;
  ancillaryDiscountDetails?: unknown; // @todosfctg update type
  preserveFcSelection?: boolean;
  isFCSelected?: boolean;
  canShowFCTGTogether?: boolean;
  isTgPlusShown: boolean;
}

export interface FreeCancellationBottomSheetProps {
  data: FreeCancellationData,
  updateFcSelection: (index: number, premiumAmount: number) => void;
  onBookNowClicked: unknown,
  isBnppOpted: boolean;
  bnppResponse: BnppResponse;
  id?: string;
  fcDiscountValue: number;
  fcDiscountPremium: number;
  isFcDiscounted: boolean;
  ancillaryDiscountDetails: unknown;
}

interface TextStyleBE {
  text: string;
  color: string;
  fontSize: string;
  fontFamily: string; // when moving to new architecture, change it to FontFamily types
  backgroundColor: string[];
  letterSpacing?: string;
  nonBreakableText?: boolean;
}

interface fareInfoRevampedItem {
  key: TextStyleBE[];
  value: TextStyleBE[];
}

interface SocialProofingBottomSheet {
  title: string;
  subtitle: string;
  discountPercentage: string;
  subtitleIcon: string;
  mainText: string;
  subText: string;
  ctaText: string;
}

interface preferenceInfoRevampedItem {
  text: TextStyleBE[];
  value: boolean;
  onSelectInfo: string;
  premiumPerPerson: number;
}

interface refundExplanationItem {
  title : TextStyleBE;
  explanation : TextStyleBE[];
}

interface ConfirmationGuaranteeWidget {
  premiumUpperCap : number;
  refundCouponUpperCap : number;
  additionalTextRevamped: TextStyleBE;
  additionalTextURL: string;
  availabilityText: string;
  socialProofing: string[];
  socialProofingDiscountValue: number;
  socialProofingDiscountedPremium: number;
  socialProofingBottomSheet: SocialProofingBottomSheet;
  discountText: string;
  backgroundColor: string[];
  bannerTag: TextStyleBE;
  description: TextStyleBE[];
  fareInfoRevamped: fareInfoRevampedItem[];
  premiumTextRevamped : TextStyleBE[];
  preferenceInfoRevamped: preferenceInfoRevampedItem[];
  offerTextRevamped : TextStyleBE;
  noSelectedOptionInfo: string;
  tripGuaranteeIntro : TextStyleBE[];
  refundExplanation : refundExplanationItem[];
  refundWaysTextStyle : TextStyleBE,
  [key : string] : unknown
  isTGPlusSelected?: unknown;
}

export interface AssuredConfirmationProps {
  assuredConfirmationData: ConfirmationGuaranteeWidget;
  selectedIndex: number | null;
  updateUserResponse: (index: number, premiumPerPerson: number) => void;
  captureRef: (comp: string, ref: unknown) => void;
  hasError: boolean;
  id?: string;
  numberOfPassengers : number;
  ancillaryDiscountDetails: unknown; // @todosfctg update type
  isTGSelected?: boolean;
  tgpTextConfig?: TgPlusConfig;
  tgPlusData?: TgPlusResponse;
  isTGPlusSelected?: boolean;
  isFCSelected?: boolean;
  tgVersion: number;
  discountText: string;
}

export interface TgWidgetData {
  tgWidgetRacText?: string
  tgWidgetCheckBoxText?: string
}

export interface AvailabilityDepletionType {
  text: string;
  textColor: string;
  backgroundGradient: {
    start: string;
    end: string;
  };
  mainIconUrl: string;
  infoIconUrl: string | null;
  inventorySellOutInDays: number;
}

export interface WlSeatUrgencyPersuasionType {
  text: string;
  textColor: string;
  backgroundGradient: {
    start: string;
    end: string;
  };
  wlSeatsRemaining: number;
  omnitureEvent: string;
}
