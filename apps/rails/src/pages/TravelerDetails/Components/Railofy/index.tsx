import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import HTMLView from 'react-native-htmlview';
import { View, Text, Image } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../../vernacular/AppLanguage';

import { styles } from './styles';
import { COUPON_TYPE, RAILOFY_TYPE } from '../../railofyUtils';

import FreeCancellation from '../../Containers/Railofy/FreeCancellation';
import AssuredConfirmation from '../../Containers/Railofy/AssuredConfirmation';
import TripGuarantee from '../../Containers/Railofy/TripGuarantee';
import TripGuaranteePlus from '../TripGuaranteePlus/Components/TripGuaranteePlus';
import TripGuaranteeUpgraded from '../../Containers/Railofy/TripGuaranteeUpgraded';
import { RailofyProps } from './types';
import { RailofyError } from '../AssuredConfirmation';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import { getRailsTGVersion } from 'apps/rails/src/RailsAbConfig';
import {
  DISCOUNT_PLACEHOLDER,
} from 'apps/rails/src/Utils/RailsConstant';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { FCTGTextConfig } from 'apps/rails/src/pages/TravelerDetails/Components/FreeCancellation/types';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { TgPlusConfig } from '../TripGuaranteePlus/types';
import { FcTgCouponComponent } from '../RailsFcTgCoupons';
import PropTypes from 'prop-types';

function Railofy(props: RailofyProps) {
  const {
    isLoading,
    railofyType,
    captureRef,
    pageId,
    railofyErrorMessage,
    tripGuaranteeData,
    freeCancellationData,
    tgPlusData,
    ancillaryType,
    ancillaryDiscountDetails,
    isTGSelected,
  } = props;

  const [tgVersion, setTgVersion] = useState(1);

  const fcTextConfig: FCTGTextConfig = useConfigStore(configKeys.RAILS_TG_FC_TEXT_CONFIG);
  const tgpTextConfig: TgPlusConfig = useConfigStore(configKeys.RAILS_TG_PLUS_CONFIG);
  useEffect(() => {
    async function initializeFcConfig() {
      const currentTgVersion = await getRailsTGVersion();
      setTgVersion(currentTgVersion);
      //On page load when Pokus Key is False: rails_TG_FC_together_false (triggered only when TG is eligible)
    }
    initializeFcConfig();
  }, []);

  if (isLoading) {
    return (
      <View style={[styles.outerBorder, styles.assuredCnfmLoaderContainer]}>
        <Spinner size={30} color={colors.governorBay} />
        <Text style={styles.cnfmLoaderText}>{_label('fetching_deals')}</Text>
      </View>
    );
  }

  const calculateAncillaryDiscount = () => {
    if (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY) {
      return (
        ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountAmount
      );
    } else if (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY) {
      return (
        ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountAmount
      );
    }
    return null;
  };
  const BannerText = () => {
    const { fcDiscountText, tgDiscountText, fcTgDiscountText } =
      fcTextConfig?.FC_TG_DISCOUNT_V2 ?? {};

    const ancillaryDiscount = calculateAncillaryDiscount();
    const discountValue =
      ancillaryDiscount ||
      (props?.isFcDiscounted ? props?.fcDiscountValue : props?.tgDiscountValue);

    const updatedFcDiscountText = fcDiscountText?.replace(
      DISCOUNT_PLACEHOLDER,
      ancillaryDiscountDetails?.ancillaryType === 'TG_ONLY'
        ? ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountAmount ||
            props?.fcDiscountValue
        : props?.fcDiscountValue,
    );
    const updatedTgDiscountText = tgDiscountText?.replace(
      DISCOUNT_PLACEHOLDER,
      ancillaryDiscountDetails?.ancillaryType === 'FC_ONLY'
        ? ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountAmount ||
            props?.tgDiscountValue
        : props?.tgDiscountValue,
      props?.tgDiscountValue,
    );
    const updatedFcTgDiscountText = fcTgDiscountText?.replace(DISCOUNT_PLACEHOLDER, discountValue);

    const htmlValue =
      props?.isFCTGBothSelected ||
      ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_TG_TOGETHER
      ? updatedFcTgDiscountText
        : props?.isFcDiscounted || ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY
      ? updatedFcDiscountText
      : updatedTgDiscountText;

    return (
      <View>
        <HTMLView
          value={htmlValue}
          stylesheet={{
            p: styles.textStyle,
            b: fontStyle('bold'),
          }}
        />
      </View>
    );
  };
  if (tripGuaranteeData?.availabilityText && freeCancellationData) {
    return (
      <View style={styles.fcTgContainer}>
        <RailofyError errorMessage={railofyErrorMessage} />
        {(tgVersion === 4 || tgVersion === 7) && tgPlusData ? (
          <View>
            <TripGuaranteeUpgraded
              tgVersion={tgVersion}
              tgTextConfig={fcTextConfig?.TG}
              tgpTextConfig={tgpTextConfig}
              fcTextConfig={fcTextConfig?.FC}
            />
          </View>
        ) : (tgVersion === 2 || tgVersion === 3 || tgVersion === 6 || tgVersion === 5) &&
          tgPlusData ? (
          <TripGuaranteePlus tgpTextConfig={tgpTextConfig} />
        ) : (
          <TripGuarantee tgTextConfig={fcTextConfig?.TG} />
        )}
        {(!ancillaryType ||
          (ancillaryType === COUPON_TYPE.TG_ONLY &&
            ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium) ||
          (ancillaryType === COUPON_TYPE.FC_ONLY &&
            ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountedPremium)) &&
          (props?.isFcDiscounted ||
            props?.isTgDiscounted ||
            (ancillaryType === COUPON_TYPE.FC_ONLY &&
              ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountedPremium) ||
            (ancillaryType === COUPON_TYPE.TG_ONLY &&
              ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium)) &&
          (props?.fcDiscountValue > 0 || props?.tgDiscountValue > 0) && (
            <View
              style={[
                styles.discountContainer,
                props?.isFCTGBothSelected ||
                ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_TG_TOGETHER
                  ? styles.isFcTgDiscountedContainer
                  : props?.isFcDiscounted ||
                    ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY ||
                    isTGSelected
                  ? styles.isFcDiscountedContainer
                  : styles.isTgDiscountedContainer,
              ]}
            >
              <LinearGradient
                colors={[colors.white, colors.lightGreen19]}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 1.0 }}
                style={styles.graidentStyle}
              />
              <Image
                source={require('packages/legacy-assets/src/rails/offers.webp')}
                style={styles.discountImage}
              />
              <BannerText />
            </View>
          )}
        <FreeCancellation
          hasError={false}
          captureRef={captureRef}
          id={`${pageId}_FC`}
          canShowFCTGTogether={true}
          fcTextConfig={fcTextConfig?.FC}
        />
        <FcTgCouponComponent />
      </View>
    );
  }

  if (railofyType === RAILOFY_TYPE.FC) {
    return (
      <>
        <RailofyError errorMessage={railofyErrorMessage} />
        <View style={styles.freeCancellationContainer}>
          <FreeCancellation
            hasError={false}
            captureRef={captureRef}
            id={`${pageId}_FC`}
          />
        </View>
      </>
    );
  }
  if (railofyType === RAILOFY_TYPE.TG) {
    return (
      <>
        <RailofyError errorMessage={railofyErrorMessage} />
        <AssuredConfirmation
          captureRef={captureRef}
          hasError={false} // @TODOSFCTG compute has error
          id={`${pageId}_TG`}
        />
      </>
    );
  }
  return null;
}

const mapStateToProps = ({ railsTraveler }) => {
  const { railofy, couponData = {} } = railsTraveler;
  const {
    isLoading,
    railofyType,
    railofyErrorMessage,
    tripGuaranteeData,
    freeCancellationData,
    tgPlusData,
    isTgDiscounted,
    isFcDiscounted,
    isTGSelected,
    isFCSelected,
    isFCPreSelected = false,
  } = railofy;
  const fcDiscountValue = freeCancellationData?.freeCancellationPremiumDetails?.discountValue ?? 0;
  const tgDiscountValue = tripGuaranteeData?.discountValue ?? 0;
  const fcDiscountPremium =
    freeCancellationData?.freeCancellationPremiumDetails?.discountedPremium ?? 0;
  const tgDiscountPremium = tripGuaranteeData?.discountedPremium ?? 0;
  const { ancillaryDiscountDetails } = couponData;
  const ancillaryType = ancillaryDiscountDetails?.ancillaryType;

  const isFCTGBothSelected = isTGSelected && (isFCSelected || isFCPreSelected);
  return {
    isLoading,
    railofyType,
    railofyErrorMessage,
    tripGuaranteeData,
    freeCancellationData,
    tgPlusData,
    tgDiscountPremium,
    fcDiscountPremium,
    tgDiscountValue,
    fcDiscountValue,
    isTgDiscounted,
    isFcDiscounted,
    isFCTGBothSelected,
    ancillaryType,
    ancillaryDiscountDetails,
    isTGSelected,
    isFCSelected,
  };
};
Railofy.propTypes = {
  isFcDiscounted: PropTypes.bool,
  fcDiscountValue: PropTypes.number,
  tgDiscountValue: PropTypes.number,
  isFCTGBothSelected: PropTypes.bool,
  ancillaryDiscountDetails: PropTypes.shape({
    ancillaryType: PropTypes.string,
    ancillaryDetails: PropTypes.shape({
      fc: PropTypes.shape({
        bundleDiscountAmount: PropTypes.number,
        bundleDiscountedPremium: PropTypes.number,
      }),
      tg: PropTypes.shape({
        bundleDiscountAmount: PropTypes.number,
        bundleDiscountedPremium: PropTypes.number,
      }),
    }),
  }),
};

export default connect(mapStateToProps, null)(Railofy);
