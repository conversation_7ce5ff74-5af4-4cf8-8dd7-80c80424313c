import React from 'react';
import {Text, View} from 'react-native';
import PropTypes from 'prop-types';
import travelerStyle, {travelerTextStyle} from '../TravelerDetailsCSS';
import ContactCardContainer from '../Containers/ContactCardContainer';
import {COMPONENT_CONTACT_CARD} from '../TravelerDetailsActions';
import { colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import {getLineHeight} from '../../../vernacular/VernacularUtils';
import ScheduleTatkalContactCardContainer from '../../ScheduleTatkalTravelerPage/containers/ScheduleTatkalContactCardContainer';
import { SCHEDULE_TATKAL_REF_COMPONENTS } from '../../ScheduleTatkalTravelerPage/constants/ScheduleTatkalTravelerPageConstants';

export default class RailsContactCard extends React.Component {
  getContactCardRenderer() {
    const {contactDetails, isScheduleTatkalPage = false} = this.props;
    if (contactDetails && contactDetails?.isFetching) {
      return null;
    }
    if (isScheduleTatkalPage) {
      return <ScheduleTatkalContactCardContainer id={this.props?.id} />;
    } else {
      return <ContactCardContainer id={this.props?.id}/>;
    }
  }

  render() {
    return (
      <View
        style={styles.container}
        ref={(ref) => {
          if (this.props?.isScheduleTatkalPage) {
            this.props.captureRef(SCHEDULE_TATKAL_REF_COMPONENTS.COMPONENT_CONTACT_CARD, ref);
          } else {
            this.props.captureRef(COMPONENT_CONTACT_CARD, ref);
          }
        }}
        testID={this.props?.id}
      >
        <View style={travelerStyle.contactCard} testID="rails_contact_card_header_container">
          <Text style={[travelerStyle.travelHeader, travelerTextStyle.getTravelHeaderFontStyle(), getLineHeight(22)]}>
            {_label('contact_details')}
          </Text>
        </View>
        {this.getContactCardRenderer()}
      </View>
    );
  }
}

const styles = {
  container: {
    backgroundColor: colors.white,
  },
};

RailsContactCard.propTypes = {
  captureRef: PropTypes.func,
  contactDetails: PropTypes.object,
  id: PropTypes.string,
  isScheduleTatkalPage: PropTypes.bool,
};

RailsContactCard.navigationOptions = {
  captureRef: null,
  contactDetails: {},
};
