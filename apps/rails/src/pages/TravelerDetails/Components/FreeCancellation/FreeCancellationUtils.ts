import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../../configStore/Common/constants';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
  /* eslint-disable */
export const FC_WIDGET_STATIC_CONFIG = {
  freeCancellationConfig: {
    header: _label('fc_header'),
    subHeader: 'Just at ₹{price} per person',
    yesOption: _label('fc_yes_option'),
    refundText: 'Refund: ₹ {refund} per person',
    noOption: _label('fc_no_option'),
    fcUnSelected: `<p>${_label('fc_unselected_msg')}</p>`,
    fcOpted: `<p>${_label('fc_opted_msg')}</p>`,
    fcNotOpted: '<p>IRCTC <b>Cancellation Fees</b> can be up to <b>50% of the ticket price</b></p>',
  },
};

export const getFcWidgetConfigObj = () => {
  const timeoutPromise = new Promise((resolve) => {
    setTimeout(() => resolve(FC_WIDGET_STATIC_CONFIG), 3000);
  });
  const fcConfigPromise = new Promise(async (resolve) => {
    try {
      const configDataResponse = await getConfigStore(configKeys.RAILS_FC_WIDGET_TEXT_CONFIG);
      resolve(configDataResponse);
    } catch (error) {
      console.log('Error while fetching FC Widget Config from Config Store: ', error);
      resolve(FC_WIDGET_STATIC_CONFIG);
    }
  });
  return Promise.race([timeoutPromise, fcConfigPromise]);
};
