import { StyleSheet, Dimensions, Platform } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  freeCancellationIcon: {
    height: 24,
    width: 24,
    marginRight: 4,
    justifyContent: 'center',
  },
  greenTickImage: {
    height: 16,
    width: 16,
    marginLeft: 5,
    marginTop: 2,
    marginRight: 8,
  },
  onlyTickImg: {
    width: 12,
    marginLeft: 5,
    marginTop: 2,
    marginRight: 8,
    height: 11,
  },
  refundAmountMessage: {
    fontSize: 16,
    color: colors.lightGreen4,
    marginTop: 2,
    lineHeight: 19,
    fontWeight: '700',
  },
  bottomSheetContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  FCBottomSheetBody: {
    paddingTop: 10,
    paddingLeft: 24,
    paddingRight: 24,
  },
  label: {
    color: colors.blue7,
  },
  ctaBottomSheetButtons: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ctaBottomSheetYesButton: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 43,
    borderRadius: 8,
  },
  ctaBottomSheetNoButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 43,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: colors.azure,
    marginRight: 16,
  },
  greenStringContainer: {
    backgroundColor: colors.lightGreen4,
    padding: 6,
    borderRadius: 4,
    width: Dimensions.get('window').width - 48,
    marginBottom: 8,
  },
  fcTgGreenStringContainer: {
    backgroundColor: colors.lightGreen18,
    alignItems: 'center',
  },
  greenString: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.white,
  },
  fcTgGreenString: {
    color: colors.lightGreen16,
  },
  cutText: {
    textDecorationLine: 'line-through',
    fontSize: 14,
    color: colors.gray8,
  },
  fcBtMiddleBox: {
    paddingTop: 12,
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  additionalText: {
    fontSize: 14,
    color: colors.defaultTextColor,
    marginBottom: 20,
  },
  listItem: {
    marginLeft: 4,
    color: colors.defaultTextColor,
  },
  fcMiddleBoxUpperPart: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  listItems: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  buttonText: {
    fontSize: 15.75,
    fontWeight: '700',
  },
  noButtonText: {
    color: colors.azure,
  },
  yesButtonText: {
    color: colors.white,
  },
  FCBottomSheetHeading: {
    fontWeight: '700',
    fontSize: 18,
    color: colors.black,
  },
  FCBTsubHeader: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  FCBottomSheetHeader: {
    flexDirection: 'row',
    backgroundColor: colors.lightBlue20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 15,
    paddingTop: 12,
  },
  FcBtHeader: {
    display: 'flex',
    justifyContent: 'center',
    width: Dimensions.get('window').width - 100,
  },
  fcBottomSheetIcon: {
    width: 48,
    height: 48,
    marginLeft: 6,
    marginRight: 6,
  },
  fullFlex: {
    flex: 1,
  },
  payCancelContainer: {
    alignItems: 'center',
    marginTop: 22,
    ...Platform.select({
      android: { marginBottom: 22 },
    }),
  },
  bottomButtonContainer: {
    justifyContent: 'center',
  },
  marginBottom: {
    ...Platform.select({
      android: { marginBottom: 22 },
    }),
  },
  discountContainer: {
    flexDirection: 'row',
    padding: 10,
    borderRadius: 16,
    borderColor: colors.lightGreen9,
    borderWidth: 1,
    zIndex: -1,
    position: 'relative',
    backgroundColor: colors.lightGreen18,
    alignItems: 'center',
    top: 18,
    paddingTop: 6,
    paddingBottom: 20,
  },
  borderWidthZero: {
    borderWidth: 0,
  },
  discountImage: {
    height: 23,
    width: 23,
    marginRight: 6,
  },
  textStyle: { color: colors.lightGreen16, fontSize: 14, width: '95%' },
});
