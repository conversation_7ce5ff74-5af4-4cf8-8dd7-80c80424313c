import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderColor: colors.transparent,
  },
  containerForFCOption1: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    borderColor: colors.transparent,
  },
  headerContainer: {
    backgroundColor: colors.lighterBlue,
    paddingHorizontal: 16,
    paddingVertical: 15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  headerContainerForFCOption1: {
    paddingHorizontal: 16,
    paddingTop: 15,
  },
  subHeaderText: {
    marginLeft: 30,
    color: colors.defaultTextColor,
  },
  bodyContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingBottom: 15,
  },
  headerHighlight: {
    height: 24,
    width: 24,
    borderRadius: 12,
    backgroundColor: colors.lightBlue28,
    marginRight: 11,
  },
  freeCancellationIcon: {
    height: 24,
    width: 24,
    marginRight: 4,
    marginLeft: 2,
  },
  newTagContainer: {
    height: 16,
    width: 50,
    backgroundColor: colors.transparent,
    paddingHorizontal: 10,
    paddingVertical: 1,
    borderRadius: 15,
    marginTop: -23,
  },
  newTagText: {
    color: colors.white,
    lineHeight: 11,
    textAlign: 'center',
  },
  heading: {
    color: colors.black,
    marginTop: 2,
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  radioButton: {
    marginTop: 1,
    alignContent: 'flex-start',
  },
  radioLabelContainer: {
    marginLeft: 14,
    flex: 1,
  },
  radioLabel: {
    color: colors.black,
  },
  radioLabelBold: {
  },
  refundAmountMessage: {
    color: colors.lightGreen4,
    marginTop: 2,
  },
  greenTickImage: {
    height: 12,
    width: 12,
    marginLeft: 5,
    marginTop: 2,
  },
  additionalText: {
    color: colors.defaultTextColor,
    marginTop: 2,
  },
  linkText: {
    color: colors.azure,
    textTransform: 'uppercase',
    marginTop: 5,
  },
  infoTextContainer: {
    marginTop: 15,
    paddingLeft: 8,
    paddingVertical: 4,
    borderRadius: 2,
  },
  infoText: {
    color: colors.defaultTextColor,
  },
  successContainer: {
    marginTop: 15,
    paddingLeft: 8,
    paddingVertical: 4,
    borderRadius: 2,
    backgroundColor: colors.lightGreen7,
  },
  successText: {
    color: colors.lightGreen11,
  },
  notSuccessContainer: {
    marginTop: 15,
    paddingLeft: 8,
    paddingVertical: 4,
    borderRadius: 2,
    backgroundColor: colors.creamWhite,
  },
  notSuccessText: {
    color: colors.lightYello,
  },
  errorContainer: {
    backgroundColor: colors.red3,
    marginTop: 10,
    paddingLeft: 8,
    paddingVertical: 4,
    borderRadius: 2,
  },
  errorText: {
    color: colors.red,
  },
});

const persuassionMsgContainer = {
  width:'100%',
  height: 29,
  zIndex:0,
  top: -10,
  borderBottomLeftRadius: 12,
  borderBottomRightRadius:12,
};

export const newFcWidgetStyles = StyleSheet.create({
  container: {
  },
  persuassionMsgContainerUnselected: {
    ...persuassionMsgContainer,
    backgroundColor: colors.grayBg,
  },
  persuassionMsgContainerOpted: {
    ...persuassionMsgContainer,
    backgroundColor: colors.lightGreen22,
  },
  persuassionMsgContainerNotOpted: {
    ...persuassionMsgContainer,
    backgroundColor: colors.creamWhite,
  },
  persuassionMsg: {
    flexDirection: 'row',
    top: 12,
    width: '100%',
    height: 13,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loader: {
    backgroundColor: colors.white,
    paddingVertical: 32,
    alignItems: 'center',
  },
  loaderText: {
    color: colors.governorBay,
    fontSize: 12,
    marginTop: 5,
  },
  headerContainer: {
    width: '100%',
    backgroundColor: colors.lighterBlue,
    paddingHorizontal: 18,
    flexDirection: 'row',
    paddingTop: 12,
  },
  fcImg: {
    height: 40,
    width: 40,
  },
  headerText: {
    color: colors.black,
  },
  subHeaderText: {
    color: colors.textGrey,
    marginTop: 2,
  },
  optionsContainer: {
    marginHorizontal: 16,
    paddingBottom: 8,
    backgroundColor: colors.white,
    borderRadius: 12,
    zIndex: 1,
    marginTop: 12,
    top: 'auto',
  },
  innerContainer: {
    width: '100%',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.lightSilver,
    zIndex: 2,
    backgroundColor: colors.white,
  },
  option: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16,
    flexWrap: 'wrap',
  },
  radioButton: {
    width: 16,
    height: 16,
  },
  yesOption: {
    marginLeft: 12,
    flex: 1,
    justifyContent: 'flex-start',
    paddingVertical: 12,
  },
  optionText: {
    color: colors.black,
  },
  noOption: {
    marginLeft: 12,
    color: colors.black,
    paddingVertical: 13.5,
  },
  refundContainer: {
    flexDirection: 'row',
    marginTop: 4,
    flexWrap: 'wrap',
  },
  refundText: {
    color: colors.lightGreen4,
    marginRight: 4,
  },
  refundText1: {
    color: colors.lightGreen16,
    marginRight: 4,
    fontWeight: '700',
  },
  tickImg: {
    height: 13.5,
    width: 13.5,
  },
  seperator: {
    width: '100%',
    height: 1,backgroundColor: colors.lightGray,
  },
  infoImg: {
    height: 12,
    width: 12,
    marginRight: 4,
  },
  tncText: {
    color: colors.primary,
  },
  headerMargin: {
    marginLeft:4,
  },
});

export const fcUnSelectedStyles = StyleSheet.create({
  p:{
    fontSize: 11,
    lineHeight: 13,
    fontWeight: '400',
    color: colors.textGrey,
  },
  b:{
    fontWeight: '600',
  },
});

export const fcNotOptedStyles = StyleSheet.create({
  p:{
    fontSize: 11,
    lineHeight: 13,
    fontWeight: '400',
    color: colors.brown2,
  },
  b:{
    fontWeight: '600',
  },
});

export const fcOptedStyles = StyleSheet.create({
  p:{
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '500',
    color: colors.lightGreen16,
  },
  b:{
    fontWeight: '600',
  },
});

