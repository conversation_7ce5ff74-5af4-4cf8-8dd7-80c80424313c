import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';

export const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: colors.grayBg,
  },
  fcTgContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    ...getPlatformElevation(1),
    overflow: 'hidden',
    marginTop: 2,
  },
  gradientContainer: {
    flex: 1,
    paddingHorizontal: 16,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingVertical: 12,
  },
  bottomContainer: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    paddingTop: 8,
    paddingBottom: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    borderColor: colors.lightGray,
    borderWidth: 0.5,
  },
  headerFcText: {
    color: colors.white,
    fontSize: 16,
    flex: 1,
    marginRight: 16,
    lineHeight: 18,
  },
  subText: {
    color: colors.white,
    fontSize: 14,
    flex: 1,
    marginRight: 16,
    lineHeight: 18,
  },
  imageView: {
    position: 'absolute',
    right: 8,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
  },
  fcImg: {
    width: 48,
    height: 40,
    tintColor: colors.white,
    resizeMode: 'contain',
  },
  refundText: {
    color: colors.black,
    fontSize: 12,
  },
  refundPriceText: {
    fontSize: 12,
    color: colors.lightGreen16,
    marginRight: 4,
    fontWeight: '900',
  },
  checkboxContainer: {
    marginTop: 8,
    alignItems: 'center',
    paddingVertical: 8,
  },
  checkBoxText: {
    color: colors.black,
    fontSize: 14,
  },
  noteText: {
    color: colors.greyText1,
    fontSize: 12,
    marginTop: 8,
  },
  tncText: {
    color: colors.primary,
    fontSize: 12,
    marginTop: 8,
  },
  refundRow: {
    flexDirection: 'row',
  },
  row: {
    flexDirection: 'row',
  },
  greenTickIcon: {
    height: 12,
    width: 12,
    alignSelf: 'center',
    marginLeft: 4,
    tintColor: colors.lightGreen16,
  },
  blackText: {
    color: colors.black,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 12,
    borderRadius: 12,
    padding: 6,
  },
  checkBoxWrapper: {
    alignSelf: 'flex-start',
    alignItems: 'center',
    marginLeft: 4,
    marginTop: 20,
  },
  checkboxIcon: {
    marginRight: 10,
  },
});
