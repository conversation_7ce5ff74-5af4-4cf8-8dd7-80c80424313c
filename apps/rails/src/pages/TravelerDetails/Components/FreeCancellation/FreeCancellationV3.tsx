import React, { useEffect, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import HTMLView from 'react-native-htmlview';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import { styles as htmlViewStyles } from '@mmt/legacy-commons/Common/Components/Checkbox';

import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import {
  DISCOUNT_PLACEHOLDER,
  PRICE_REPLACER,
} from 'apps/rails/src/Utils/RailsConstant';
import { RAILOFY_TYPE } from 'apps/rails/src/pages/TravelerDetails/railofyUtils';
import { trackFCTGEvar47or97Event, trackTravellerPageEvent } from 'apps/rails/src/railsAnalytics';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import LinearGradient from 'react-native-linear-gradient';
import { styles } from './styles/FreeCancellationV3.styles';
import { FCTGTextConfig, Fc } from './types';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { getRailsTGVersion } from 'apps/rails/src/RailsAbConfig';

import { railofyValuesTrackingParams } from '../../../Review/RailsReviewActions';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { COUPON_TYPE } from '../../railofyUtils';
import freeCancellationIcon from '@mmt/legacy-assets/src/trains_free_cancellation_blue.webp';
import greenTick from '@mmt/legacy-assets/src/free_cancellation_tick_green.webp';
interface FreeCancellationV3Props {
  refundAmount: number;
  insuranceAmount: number;
  additionalDetailsURL?: string;
  onAdditionalDetailsPress?: unknown;
  updateFCTGUserResponse?: unknown;
  isFCSelected?: unknown;
  fcTextConfig: Fc;
  isTgPlusShown: boolean;
  fcDiscountPremium: number;
  isFcDiscounted: boolean;
  ancillaryDiscountDetails?: unknown;
  logTravellerPageAddOnEvents: (railofyValuesTrackingParams: unknown, eventValue: string) => void;
}

const FreeCancellationV3 = ({
  refundAmount,
  insuranceAmount,
  additionalDetailsURL,
  onAdditionalDetailsPress,
  updateFCTGUserResponse,
  isFCSelected,
  fcTextConfig,
  fcDiscountPremium,
  isFcDiscounted,
  logTravellerPageAddOnEvents,
  ancillaryDiscountDetails,
}: FreeCancellationV3Props) => {

  const [tgpVersion, setTgVersion] = useState(1);

  let fcConfig = fcTextConfig;
  const fcTgTextConfig: FCTGTextConfig = useConfigStore(configKeys.RAILS_TG_FC_TEXT_CONFIG);
  if (!fcConfig?.header) {
    fcConfig = fcTgTextConfig.FC;
  }

  useEffect(() => {
    async function initialiseTgVersionValue (){
      const currentTgVersion = await getRailsTGVersion();
      setTgVersion(currentTgVersion);
    }
    initialiseTgVersionValue();
  },[]);

  const onFCSelected = () => {
    const index = !isFCSelected ? 0 : 1;
    const toast = tgpVersion === 1 ? fcConfig?.toast : fcConfig?.toastNew;
    updateFCTGUserResponse(
      index,
      !isFCSelected ? insuranceAmount : 0,
      RAILOFY_TYPE.FC,
      toast,
    );
    if (!isFCSelected) {
      logTravellerPageAddOnEvents(
        railofyValuesTrackingParams,
        TRAVELLER_CLICK_EVENTS.FREE_CANCELLATION_OPTED,
      );
      logTravellerPageAddOnEvents(railofyValuesTrackingParams, TRAVELLER_CLICK_EVENTS.FC_TG_WIDGET);
    } else {
      logTravellerPageAddOnEvents(
        railofyValuesTrackingParams,
        TRAVELLER_CLICK_EVENTS.FREE_CANCELLATION_DESELECTED,
      );
    }
    trackFCTGEvar47or97Event(
      null,
      `${RAIL_EVENTS.FCTG.FC}${RAIL_EVENTS.FCTG.TRAVELER}${
        !isFCSelected ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
      }`,
      RAIL_EVENTS.FCTG.FC,
    );
    trackTravellerPageEvent(
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_FC}_${!isFCSelected ? 'check' : 'uncheck'}`,
    );
  };
  /* eslint-disable */
  const onKnowMorePressed = () => onAdditionalDetailsPress(additionalDetailsURL);
  const replaceText = (text, discountPremium, premium) => {

    return text?.replace(PRICE_REPLACER, premium)?.replace(DISCOUNT_PLACEHOLDER, discountPremium);
  };
  const checkBoxCTA =
    (fcDiscountPremium > 0 && isFcDiscounted && !ancillaryDiscountDetails?.ancillaryType) ||
    (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY &&
      ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium)
      ? replaceText(
          fcTgTextConfig?.FC_TG_DISCOUNT_V2?.fcCheckBoxCTA,
          insuranceAmount,
          ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY
            ? ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium ||
                fcDiscountPremium
            : fcDiscountPremium,
        )
      : replaceText(fcTgTextConfig?.FC?.checkBoxCTA, insuranceAmount, insuranceAmount);

  const checkBoxCTAForSelected = replaceText(
    fcDiscountPremium > 0
      ? isFcDiscounted
        ? fcTgTextConfig?.FC_TG_DISCOUNT_V2?.fcCheckBoxCTASelected
        : fcTgTextConfig?.FC_TG_DISCOUNT_V2?.fcCheckBoxCTASelected?.replace(
            `<a>₹${DISCOUNT_PLACEHOLDER}</a> `,
            '',
          )
      : fcTgTextConfig?.FC?.checkBoxCTASelected,
    insuranceAmount,
    fcDiscountPremium > 0 && isFcDiscounted
      ? ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY
        ? ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium ||
          fcDiscountPremium
        : fcDiscountPremium
      : insuranceAmount,
  );
  const fcCheckBoxCTASelectedCouponText = replaceText(
    fcTgTextConfig?.FC_TG_DISCOUNT_V2?.fcCheckBoxCTASelectedCouponText?.replace(
      `${DISCOUNT_PLACEHOLDER} `,
      insuranceAmount,
    ),
    insuranceAmount,
    ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY
      ? ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium
      : ancillaryDiscountDetails?.ancillaryDetails?.fc?.discountedPremium,
  );
  return (
    <View style={styles.fcTgContainer}>
      <LinearGradient
        colors={[colors.skyBlue2, colors.skyBlue1]}
        start={{ x: 0.01, y: 1.8 }}
        end={{ x: 1, y: 0.1 }}
        style={styles.gradientContainer}
      >
        <Text style={[styles.headerFcText, fontStyle('bold')]}>
          {fcConfig?.header}
          {tgpVersion !== 1 && isFCSelected && <Text>{` ${_label('added')}`}</Text>}
        </Text>
        <Text style={[styles.subText, fontStyle('bold')]}>{fcConfig?.subHeader}</Text>
        <View style={styles.imageView}>
          <Image style={styles.fcImg} source={freeCancellationIcon} />
        </View>
      </LinearGradient>
      <View style={styles.bottomContainer}>
        <View style={styles.refundRow}>
          <Text style={[styles.refundText, fontStyle('regular')]}>
            {fcConfig?.approxRefund}
            <Text style={[styles.refundPriceText, fontStyle('bold')]}> ₹ {refundAmount}</Text>
          </Text>
          <Image source={greenTick} style={styles.greenTickIcon} />
        </View>
        <View style={styles.row}>
          <Text style={[styles.noteText, fontStyle('regular')]}>{fcConfig?.description}</Text>
          {additionalDetailsURL && (
            <TouchableOpacity onPress={onKnowMorePressed}>
              <Text style={[styles.tncText, fontStyle('regular')]}>{fcConfig?.tncCTA}</Text>
            </TouchableOpacity>
          )}
        </View>
        <CheckBox
          children={
            <HTMLView
                value={
                  isFCSelected
                  ? ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY &&
                    ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium
                      ? fcCheckBoxCTASelectedCouponText
                    : ancillaryDiscountDetails?.ancillaryDetails?.fc?.discountedPremium
                    ? fcCheckBoxCTASelectedCouponText
                    : checkBoxCTAForSelected
                    : checkBoxCTA
                }
                stylesheet={htmlViewStyles}
              />
            }
            isChecked={isFCSelected}
            onPress={onFCSelected}
            customStyles={styles.checkBoxWrapper}
            customContainerStyle={styles.checkboxIcon}
          />
      </View>
    </View>
  );
};
export default React.memo(FreeCancellationV3);
