import React from 'react';
import { connect } from 'react-redux';
import { updateRailofyUserResponse } from '../../TravelerDetailsActions';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { View, Text, StyleSheet, TouchableOpacity, Image, Dimensions } from 'react-native';
import { ADD_NOW } from '@mmt/rails/src/Utils/RailsConstant';
import useConfigStore from '@mmt/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '@mmt/rails/src/configStore/Common/constants';
import { FCTGTextConfig, FCSnackBarProps } from './types';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

const FreeCancellationSnackBar: React.FC<FCSnackBarProps> = ({
  freeCancellationData,
  isFCSelected,
  tripGuaranteeData,
  closeSnackBar,
  updateUserResponse,
  selectedIndex,
}) => {
  const cancellationOptions = freeCancellationData?.cancellationOptions;
  const insuranceAmount = cancellationOptions?.[0]?.insuranceAmount;

  const fcTextConfig: FCTGTextConfig = useConfigStore(configKeys.RAILS_TG_FC_TEXT_CONFIG);
  let { fcSnackBarText = '' } = fcTextConfig?.FC_SNACKBAR_DISCOUNT_TEXT ?? {};
  fcSnackBarText = fcSnackBarText?.replace('{discount}', `${insuranceAmount}`);

  if (
    isFCSelected ||
    tripGuaranteeData?.availabilityText ||
    !insuranceAmount ||
    !fcSnackBarText ||
    selectedIndex !== -1
  ) {
    return null;
  }

  const handleAddNow = () => {
    updateUserResponse(0, insuranceAmount);
    closeSnackBar();
  };

  return (
    <View style={styles.snackBar} testID="free_cancellation_snack_bar">
      <View style={styles.content} testID="free_cancellation_snack_bar_content">
        <View
          style={styles.iconTextContainer}
          testID="free_cancellation_snack_bar_icon_text_container"
        >
          <View style={styles.iconBox} testID="free_cancellation_snack_bar_icon_box">
            <Image source={ASSETS.fcIcon} style={styles.icon} resizeMode="contain" />
          </View>
          <Text style={styles.message} testID="free_cancellation_snack_bar_message">
            {fcSnackBarText}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.button}
          onPress={handleAddNow}
          testID="free_cancellation_snack_bar_button"
        >
          <Text style={styles.buttonText} testID="free_cancellation_snack_bar_button_text">
            {ADD_NOW}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.closeIcon}
          onPress={closeSnackBar}
          testID="free_cancellation_snack_bar_close_icon"
        >
          <Text style={styles.closeText} testID="free_cancellation_snack_bar_close_text">
            ✕
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const screenWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  snackBar: {
    position: 'absolute',
    bottom: 90,
    alignSelf: 'center',
    width: screenWidth - 32,
    height: 48,
    backgroundColor: colors.textGrey,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  iconBox: {
    width: 24,
    height: 24,
    backgroundColor: colors.primary,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    width: 24,
    height: 24,
  },
  message: {
    color: colors.white,
    fontSize: 12,
    fontFamily: 'Lato',
    flex: 1,
  },
  button: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginHorizontal: 8,
  },
  buttonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '700',
    textAlign: 'center',
    fontFamily: 'Lato',
  },
  closeIcon: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    color: colors.greyText1,
    fontSize: 16,
    fontFamily: 'Lato',
  },
});

const mapStateToProps = (state: unknown) => {
  const { railofy } = state.railsTraveler || {};
  const { isFCSelected, freeCancellationData, tripGuaranteeData, selectedIndex } = railofy || {};
  return {
    freeCancellationData,
    isFCSelected,
    tripGuaranteeData,
    selectedIndex,
  };
};

const mapDispatchToProps = (dispatch: unknown) => ({
  updateUserResponse: (index: number, premium: number = 0) => {
    dispatch(updateRailofyUserResponse(index, premium));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(FreeCancellationSnackBar);
