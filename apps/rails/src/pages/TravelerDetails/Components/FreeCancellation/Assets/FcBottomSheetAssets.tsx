import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, De<PERSON>, ClipPath } from 'react-native-svg';
interface SvgProps {
  fill?: string;
  [key: string]: unknown; 
}

export const GreenTick: React.FC<SvgProps> = (props) => {
  return (
    <Svg
      width={16}
      height={17}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.294 7.078a1.573 1.573 0 00.261 1.832 1.571 1.571 0 01-.35 2.46 1.567 1.567 0 00-.763 1.684c.099.478-.03.974-.346 1.343a1.543 1.543 0 01-1.268.536 1.55 1.55 0 00-1.544 1 1.555 1.555 0 01-1.011.941 1.54 1.54 0 01-1.355-.24 1.54 1.54 0 00-1.836 0 1.54 1.54 0 01-1.354.24 1.555 1.555 0 01-1.011-.94 1.549 1.549 0 00-1.545-1.001 1.544 1.544 0 01-1.268-.536 1.573 1.573 0 01-.346-1.343 1.567 1.567 0 00-.763-1.683 1.57 1.57 0 01-.35-2.46c.474-.488.58-1.23.26-1.833a1.573 1.573 0 01-.042-1.386c.2-.444.595-.767 1.067-.875a1.559 1.559 0 001.202-1.399c.039-.486.3-.926.707-1.19.407-.263.912-.32 1.367-.154.637.234 1.35.022 1.761-.521.294-.388.75-.615 1.233-.615.483 0 .94.227 1.233.615.41.543 1.124.755 1.761.521.455-.166.96-.109 1.367.155.406.263.668.703.707 1.19a1.558 1.558 0 001.202 1.398c.472.108.866.431 1.067.875.2.443.185.956-.043 1.386zM3.272 9.436l3.402 3.431 5.959-6.008-1.45-1.46-4.51 4.547-1.952-1.97-1.449 1.46z"
        fill={props?.fill ? props?.fill : '#33D18F'}
      />
    </Svg>
  );
};

export const FcShield: React.FC<SvgProps> = (props) => {
  return (
    <Svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G clipPath="url(#clip0_1518_6518)">
        <Path
          d="M20.834 4.933a29.195 29.195 0 01-4.414-1.566 30.004 30.004 0 01-4.053-2.14L12 1l-.36.233a29.995 29.995 0 01-4.053 2.14 29.193 29.193 0 01-4.42 1.56l-.5.127v5.56c0 8.927 9.02 12.267 9.107 12.3L12 23l.227-.08c.093 0 9.107-3.367 9.107-12.3V5.06l-.5-.127z"
          fill="#33D18F"
        />
        <Path
          d="M15.366 9.089h-2.478a1.89 1.89 0 01.931 1.302h1.547v1.176h-1.554a2.561 2.561 0 01-.679 1.232c-.34.35-.793.614-1.358.791a1.683 1.683 0 01.616.567L15.114 18h-1.47c-.284 0-.492-.112-.623-.336l-2.359-3.395a.774.774 0 00-.259-.252c-.093-.051-.233-.077-.42-.077H8.73v-1.12h1.358c.588 0 1.06-.114 1.414-.343.36-.233.6-.537.721-.91h-3.5v-1.176h3.542a1.518 1.518 0 00-.693-.924c-.35-.22-.83-.329-1.442-.329h-1.4V7.92h6.636v1.169z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_1518_6518">
          <Path fill="#fff" d="M0 0H24V24H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export const TickBullet = (props) => {
    return (
      <Svg
        width={11}
        height={9}
        viewBox="0 0 11 9"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <Path
          d="M1 4.5l3 3 6-6"
          stroke="#33D18F"
          strokeWidth={2}
          strokeLinecap="round"
        />
      </Svg>
    );
};
