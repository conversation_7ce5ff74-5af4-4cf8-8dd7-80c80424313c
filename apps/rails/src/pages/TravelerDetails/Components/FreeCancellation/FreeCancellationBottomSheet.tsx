import React, { ReactNode, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';
import {
  DISCOUNT_PLACEHOLDER,
  DUE_DATE_REPLACER,
  FC_DUE_DATE,
  FC_DUE_HOUR,
  PRICE_REPLACER,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  TRAVELER_DETAILS_FC_BOTTOMSHEET_CONTENT,
  TRAVELER_DETAILS_FC_BOTTOMSHEET_CONTENT_WHEN_BNPP,
} from '../../../../Utils/RailsConstant';
import { FreeCancellationBottomSheetProps } from '../Railofy/types';
import { styles } from './styles/FcBottomSheet.styles';
import { GreenTick, TickBullet } from './Assets/FcBottomSheetAssets';
import {
  getIfCanShowNewBNPPTagWhenFC,
} from 'apps/rails/src/RailsAbConfig';
import {
  removeEventFromEvar47or97Variable,
  trackFCTGEvar47or97Event,
  trackGenericEvar47or97Event,
  trackTravellerPageEvent,
} from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { FCTGTextConfig } from './types';
import HTMLView from 'react-native-htmlview';
import { htmlStyles } from './styles/htmlStyle';
import isEmpty from 'lodash/isEmpty';
import { railofyValuesTrackingParams } from '../../../Review/RailsReviewActions';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { COUPON_TYPE } from '../../railofyUtils';

import FCLogoLottieImage from '@mmt/legacy-assets/src/rails/FC_icon.webp';
const B = ({ children }: { children: ReactNode }) => (
  <Text style={[styles.bold, fontStyle('bold')]} testID="fc_bottomsheet_bold_text">
    {children}
  </Text>
);

const FreeCancellationBottomSheet = ({
  data,
  updateFcSelection,
  onBookNowClicked,
  isBnppOpted,
  bnppResponse,
  id,
  fcDiscountValue,
  fcDiscountPremium,
  isFcDiscounted,
  logTravellerPageBottomSheetEvents,
  logTravellerPageAddOnEvents,
  ancillaryDiscountDetails,
}: FreeCancellationBottomSheetProps) => {
  const cancellationOptions = data?.cancellationOptions;
  const yesOption = cancellationOptions[0];
  const [refundInfo, setRefundInfo] = useState();
  const fcTextConfig: FCTGTextConfig = useConfigStore(configKeys.RAILS_TG_FC_TEXT_CONFIG);

  const processData = () => {
    const refundInfo = yesOption.refundInfo;
    setRefundInfo(refundInfo);
   };


  const canShowBNPPItem =
    getIfCanShowNewBNPPTagWhenFC() && isBnppOpted && !isEmpty(bnppResponse?.fcDueDate);
  useEffect(() => {
    removeEventFromEvar47or97Variable(
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_FC_WITH_BNPP}${canShowBNPPItem ? 'n' : 'y'}`,
    );
    trackGenericEvar47or97Event(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_FC_WITH_BNPP}${canShowBNPPItem ? 'y' : 'n'}`,
    );
  }, []);
  useEffect(() => {
    processData();
    logTravellerPageBottomSheetEvents(TRAVELLER_CLICK_EVENTS.FREE_CANCELLATION_BOTTOM_SHEET);
  }, [cancellationOptions]);


  const onFcSelection = (index: number, insuranceAmount: number) => {
    updateFcSelection(index, insuranceAmount);
    if (index === 0) {
      logTravellerPageAddOnEvents(
        railofyValuesTrackingParams,
        TRAVELLER_CLICK_EVENTS.FREE_CANCELLATION_OPTED_BOTTOM_SHEET,
      );
    }
    //FC_prompt_select or FC_prompt_unselect
    trackFCTGEvar47or97Event(
      null,
      `${RAIL_EVENTS.FCTG.FC}${RAIL_EVENTS.FCTG.PROMPT}${
        !index ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
      }`,
      RAIL_EVENTS.FCTG.FC,
    );
    trackTravellerPageEvent(
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_FC_PROMPT}_${!index ? 'yes' : 'no'}`,
    );

    onBookNowClicked();
  };

  const fcHeadings = canShowBNPPItem
    ? TRAVELER_DETAILS_FC_BOTTOMSHEET_CONTENT_WHEN_BNPP
    : TRAVELER_DETAILS_FC_BOTTOMSHEET_CONTENT;

  if (!data || data.isLoading) {
    return <></>;
  }

  const YesButtonText = () => {
    let buttonText = fcTextConfig?.FC_TG_DISCOUNT_V2?.fcBottomSheetButtonText || '';
    buttonText = buttonText?.replace(DISCOUNT_PLACEHOLDER, yesOption.insuranceAmount);
    const priceToShow =
      ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY
        ? ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium ||
          fcDiscountPremium
        : fcDiscountPremium;
    buttonText = buttonText?.replace(PRICE_REPLACER, priceToShow);

    if (
      (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY &&
        ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium) ||
      (fcDiscountPremium > 0 && isFcDiscounted)
    ) {
      return (
        <HTMLView
          value={buttonText}
          stylesheet={{
            p: [styles.yesButtonText, styles.buttonText, fontStyle('bold')],
            a: [styles.cutText, fontStyle('regular')],
          }}
        />
      );
    }
    return fcTextConfig.FC?.ctaYes?.replace(PRICE_REPLACER, yesOption.insuranceAmount);
  };

  return (
    <View style={styles.bottomSheetContent} testID={id}>
      <View style={styles.FCBottomSheetHeader} testID="fc_bottomsheet_header">
        <Image
          style={styles.fcBottomSheetIcon}
          source={FCLogoLottieImage}
          resizeMode="contain"
          testID="fc_bottomsheet_icon"
        />
        <View style={styles.FcBtHeader} testID="fc_bottomsheet_title">
          <Text
            style={[styles.FCBottomSheetHeading, fontStyle('bold')]}
            testID="fc_bottomsheet_title_text"
          >
            {data?.header}
          </Text>
          <Text
            style={[styles.FCBTsubHeader, fontStyle('regular')]}
            testID="fc_bottomsheet_sub_title_text"
          >
            {data?.subHeader}
          </Text>
        </View>
      </View>
      <View style={styles.FCBottomSheetBody} testID="fc_bottomsheet_body">
        <View
          style={[styles.greenStringContainer, styles.fcTgGreenStringContainer]}
          testID="fc_bottomsheet_green_string_container"
        >
          <Text
            style={[styles.greenString, styles.fcTgGreenString]}
            testID="fc_bottomsheet_green_string"
          >
            {fcHeadings.fcString}
          </Text>
        </View>
        <View style={styles.fcBtMiddleBox} testID="fc_bottomsheet_refund_info">
          <View style={styles.fcMiddleBoxUpperPart} testID="fc_bottomsheet_refund_heading">
            <View style={styles.freeCancellationIcon} testID="fc_bottomsheet_green_tick_container">
              <GreenTick fill={colors.lightGreen16} />
            </View>
            <Text
              style={[styles.refundAmountMessage, fontStyle('regular'), styles.fcTgGreenString]}
              testID="fc_bottomsheet_refund_amount_message"
            >
              {canShowBNPPItem ? (
                <B>{fcHeadings?.headerString}</B>
              ) : (
                <B>
                  <Text>
                    {refundInfo} {yesOption?.refundAmount}
                  </Text>
                </B>
              )}
            </Text>
          </View>

          {fcHeadings.fcList.map((item, index) => {
            if (isBnppOpted) {
              item = item
                .replace(DUE_DATE_REPLACER, bnppResponse?.dueDate ?? '')
                .replace(FC_DUE_DATE, bnppResponse?.fcDueDate ?? '')
                .replace(FC_DUE_HOUR, String(bnppResponse?.fcDueHour ?? ''));
            }
            return (
              <View key={index} style={styles.listItems} testID={`fc_bottomsheet_feature_${index}`}>
                <View style={styles.onlyTickImg} testID={`fc_bottomsheet_feature_tick_${index}`}>
                  <TickBullet />
                </View>
                {canShowBNPPItem ? (
                  <HTMLView stylesheet={htmlStyles} value={item} />
                ) : (
                  <Text
                    style={[styles.listItem, fontStyle('regular')]}
                    testID={`fc_bottomsheet_feature_text_${index}`}
                  >
                    {item}
                  </Text>
                )}
              </View>
            );
          })}
        </View>
        <View style={styles.bottomButtonContainer} testID="fc_bottomsheet_footer">
          {((fcDiscountValue > 0 && isFcDiscounted) ||
            (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY &&
              ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountedPremium)) && (
            <View
              style={[styles.discountContainer, styles.borderWidthZero]}
              testID="fc_bottomsheet_discount_section"
            >
              <Image
                source={require('packages/legacy-assets/src/rails/offers.webp')}
                style={styles.discountImage}
                testID="fc_bottomsheet_offers_icon"
              />
              <View testID="fc_bottomsheet_discount_container">
                <HTMLView
                  value={fcTextConfig?.FC_TG_DISCOUNT_V2?.fcBottomSheetDiscountText?.replace(
                    DISCOUNT_PLACEHOLDER,
                    ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY
                      ? ancillaryDiscountDetails?.ancillaryDetails?.fc?.bundleDiscountAmount ||
                          fcDiscountValue
                      : fcDiscountValue.toString(),
                  )}
                  stylesheet={{
                    p: styles.textStyle,
                    b: fontStyle('bold'),
                  }}
                />
              </View>
            </View>
          )}
          <View style={styles.ctaBottomSheetButtons}>
            <TouchableOpacity
              style={styles.fullFlex}
              onPress={() => {
                onFcSelection(0, yesOption.insuranceAmount);
              }}
              testID="fc_bottomsheet_yes_button"
            >
              <View testID={`${id}_yesOption`}>
                <LinearGradient
                  colors={[colors.lightBlue, colors.darkBlue]}
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 0.0 }}
                  style={styles.ctaBottomSheetYesButton}
                  testID="fc_bottomsheet_yes_gradient"
                >
                  <Text style={[styles.yesButtonText, styles.buttonText, fontStyle('bold')]}>
                    <YesButtonText />
                  </Text>
                </LinearGradient>
              </View>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={styles.payCancelContainer}
            onPress={() => {
              onFcSelection(1, 0);
            }}
            testID="fc_bottomsheet_no_button"
          >
            <Text style={[styles.noButtonText, styles.buttonText, fontStyle('bold')]}>
              {fcTextConfig.FC?.ctaNo}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

FreeCancellationBottomSheet.propTypes = {
  ancillaryDiscountDetails: PropTypes.shape({
    ancillaryType: PropTypes.string,
    ancillaryDetails: PropTypes.shape({
      fc: PropTypes.shape({
        bundleDiscountedPremium: PropTypes.number,
        bundleDiscountAmount: PropTypes.number,
      }),
    }),
  }),
};

export default FreeCancellationBottomSheet;
