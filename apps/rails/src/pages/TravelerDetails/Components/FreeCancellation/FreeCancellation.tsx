import React, { useEffect, useState, useRef } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { styles as htmlViewStyles } from '@mmt/legacy-commons/Common/Components/Checkbox';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import LinearGradient from 'react-native-linear-gradient';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import { FREE_CANCELLATION, scrollToErrorComponent } from '../../TravelerDetailsActions';
import { trackOmniture } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { FreeCancellationProps } from '../Railofy/types';
import { openWebView } from '../../railofyUtils';
import {
  fcNotOptedStyles,
  fcOptedStyles,
  fcUnSelectedStyles,
  newFcWidgetStyles,
  styles,
} from './styles/FreeCancellation.styles';
import HTMLView from 'react-native-htmlview';
import _isEmpty from 'lodash/isEmpty';
import { showSocialProofingFc } from 'apps/rails/src/RailsAbConfig';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { TERMS_AND_CONDITIONS } from '../../../RIS/PnrStatus/TGSUtils/Constants';
import {
  PRICE_REPLACER,
  REFUND_REPLACER,
  DISCOUNT_PLACEHOLDER,
} from 'apps/rails/src/Utils/RailsConstant';
import { getFcWidgetConfigObj } from './FreeCancellationUtils';
import { trackFCTGEvar47or97Event } from '../../../../railsAnalytics';
import FreeCancellationV3 from './FreeCancellationV3';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import FcCarousel from 'apps/rails/src/pages/TravelerDetails/Components/FreeCancellation/Carousel';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import { railofyValuesTrackingParams } from '../../../Review/RailsReviewActions';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import useConfigStore from '../../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../../configStore/Common/constants';
import greeTick from '@mmt/legacy-assets/src/free_cancellation_tick_green.webp';
import freeCancellationIcon from '@mmt/legacy-assets/src/trains_free_cancellation_blue.webp';
import greyInfoIcon from '@mmt/legacy-assets/src/fc_info_icon.webp';
import PropTypes from 'prop-types';


interface NewFreeCancellationWidgetProps {
  selectedIndex?: number;
  updateUserResponse: (index: number, premiumAmount?: number) => void;
  cancellationOptions?: Array<{
    value: boolean;
    insuranceAmount: number | string;
    refundAmount: string;
    additionalDetailsURL?: string;
  }>;
  onAdditionalDetailsPress: (url: string) => void;
  isFCSelected?: boolean;
  updateFCTGUserResponse?: (fcSelected: boolean, tgSelected: boolean) => void;
  canShowFCTGTogether?: boolean;
  fcTextConfig?: unknown;
  isTgPlusShown?: boolean;
  freeCancellationData?: {
    cancellationOptions?: unknown[];
    socialProofing?: string[];
    discountText?: string;
    freeCancellationPremiumDetails?: {
      discountedPremium?: number;
      socialProofingDiscountedPremium?: number;
    };
  };
  isFcDiscounted?: boolean;
  logTravellerPageAddOnEvents: (params: unknown, event: string) => void;
  ancillaryDiscountDetails?: {
    couponSuccessMessage?: string;
    ancillaryDetails?: {
      fc?: {
        discountedPremium?: number;
      };
    };
  };
}

const NewFreeCancellationWidget = ({
  selectedIndex,
  updateUserResponse,
  cancellationOptions,
  onAdditionalDetailsPress,
  isFCSelected,
  updateFCTGUserResponse,
  canShowFCTGTogether,
  fcTextConfig,
  isTgPlusShown,
  freeCancellationData,
  isFcDiscounted,
  logTravellerPageAddOnEvents,
  ancillaryDiscountDetails,
}: NewFreeCancellationWidgetProps) => {
  const [fcWidgetConfig, setFcWidgetConfig] = useState({});
  const carouselData =
    freeCancellationData?.socialProofing?.map((text) => ({ sentence: text })) || [];
  const selectedOption =
    typeof selectedIndex === 'number' ? cancellationOptions[selectedIndex] : null;
  const insuranceAmount = cancellationOptions[0]?.insuranceAmount;
  const refundAmount = cancellationOptions[0]?.refundAmount.split(' ')[1].split('/')[0];
  const subHeader = fcWidgetConfig?.subHeader?.replace(PRICE_REPLACER, insuranceAmount);
  const refundText = fcWidgetConfig?.refundText?.replace(REFUND_REPLACER, refundAmount);
  const additionalDetailsURL =
    cancellationOptions[0]?.additionalDetailsURL || cancellationOptions[1]?.additionalDetailsURL;
  const fcDiscountPremium =
    freeCancellationData?.freeCancellationPremiumDetails?.discountedPremium ?? 0;

  const socialProofingDiscountedPremium =
    freeCancellationData?.freeCancellationPremiumDetails?.socialProofingDiscountedPremium ?? 0;
  let persuassionContainerStyle = newFcWidgetStyles.persuassionMsgContainerUnselected;
  if (selectedOption?.value === true) {
    persuassionContainerStyle = newFcWidgetStyles.persuassionMsgContainerOpted;
  } else if (selectedOption?.value === false) {
    persuassionContainerStyle = newFcWidgetStyles.persuassionMsgContainerNotOpted;
  }
  const discountText = freeCancellationData?.discountText;
  const PRICE_PER_PERSON = `  ₹${socialProofingDiscountedPremium}/person`;
  const PREM_PER_PERSON = `₹${insuranceAmount}/person`;
  useEffect(() => {
    async function initializeFcConfig() {
      const fcConfig = await getFcWidgetConfigObj();
      setFcWidgetConfig(fcConfig?.freeCancellationConfig);
    }
    initializeFcConfig();
  }, []);

  const onFCSelected = (index: number, premiumAmount: number = 0) => {
    updateUserResponse(index, premiumAmount);
    if (index === 0) {
      logTravellerPageAddOnEvents(
        railofyValuesTrackingParams,
        TRAVELLER_CLICK_EVENTS.FREE_CANCELLATION_OPTED,
      );
    } else if (index === 1) {
      logTravellerPageAddOnEvents(
        railofyValuesTrackingParams,
        TRAVELLER_CLICK_EVENTS.FREE_CANCELLATION_DESELECTED,
      );
    }
    // FC_traveler_select or FC_traveler_unselect
    trackFCTGEvar47or97Event(
      null,
      `${RAIL_EVENTS.FCTG.FC}${RAIL_EVENTS.FCTG.TRAVELER}${
        !index ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
      }`,
      RAIL_EVENTS.FCTG.FC,
    );
  };

  const replaceText = (text, discountPremium, premium) => {
    return text?.replace(PRICE_REPLACER, premium)?.replace(DISCOUNT_PLACEHOLDER, discountPremium);
  };

  const fcTgTextConfig = useConfigStore(configKeys.RAILS_TG_FC_TEXT_CONFIG);
  const fcCheckBoxCTASelectedCouponText = replaceText(
    fcTgTextConfig?.FC_TG_DISCOUNT_V2?.fcCheckBoxCTASelectedCouponText?.replace(
      `${DISCOUNT_PLACEHOLDER} `,
      insuranceAmount,
    ),
    insuranceAmount,
    ancillaryDiscountDetails?.ancillaryDetails?.fc?.discountedPremium,
  );

  return _isEmpty(fcWidgetConfig) && !canShowFCTGTogether ? (
    <View style={newFcWidgetStyles.loader}>
      <Spinner size={30} color={colors.governorBay} />
      <Text style={newFcWidgetStyles.loaderText}>{_label('fetching_deals')}</Text>
    </View>
  ) : canShowFCTGTogether ? (
    <FreeCancellationV3
      refundAmount={refundAmount}
      insuranceAmount={insuranceAmount}
      additionalDetailsURL={additionalDetailsURL}
      onAdditionalDetailsPress={onAdditionalDetailsPress}
      updateFCTGUserResponse={updateFCTGUserResponse}
      isFCSelected={isFCSelected}
      logTravellerPageAddOnEvents={logTravellerPageAddOnEvents}
      fcTextConfig={fcTextConfig}
      isTgPlusShown={isTgPlusShown}
      fcDiscountPremium={fcDiscountPremium}
      isFcDiscounted={isFcDiscounted}
      ancillaryDiscountDetails={ancillaryDiscountDetails}
    />
  ) : showSocialProofingFc() === 0 ? (
    <View style={newFcWidgetStyles.container}>
      <LinearGradient
        colors={[colors.lightBlue21, colors.white]}
        start={{ x: 0.5, y: 0 }}
        end={{ x: 0.5, y: 1 }}
        style={newFcWidgetStyles.headerContainer}
      >
        <Image source={freeCancellationIcon} resizeMode="contain" style={newFcWidgetStyles.fcImg} />
        <View style={newFcWidgetStyles.headerMargin}>
          <Text style={[newFcWidgetStyles.headerText, getLineHeight(20), fontStyle('bold')]}>
            {fcWidgetConfig?.header}
          </Text>
          <Text style={[newFcWidgetStyles.subHeaderText, getLineHeight(12), fontStyle('regular')]}>
            {subHeader}
          </Text>
        </View>
      </LinearGradient>
      <View style={newFcWidgetStyles.optionsContainer}>
        <View style={newFcWidgetStyles.innerContainer}>
          <TouchableOpacity
            style={newFcWidgetStyles.option}
            onPress={() => onFCSelected(0, insuranceAmount)}
          >
            <RadioButton
              isSelected={selectedIndex === 0}
              customStyle={newFcWidgetStyles.radioButton}
              onPress={() => onFCSelected(0, insuranceAmount)}
            />
            <View style={newFcWidgetStyles.yesOption}>
              <Text style={[newFcWidgetStyles.optionText, getLineHeight(16), fontStyle('normal')]}>
                {fcWidgetConfig?.yesOption}
              </Text>
              <View style={newFcWidgetStyles.refundContainer}>
                <Text style={[newFcWidgetStyles.refundText, getLineHeight(12), fontStyle('bold')]}>
                  {refundText}
                </Text>
                <Image resizeMode="contain" source={greeTick} style={newFcWidgetStyles.tickImg} />
              </View>
            </View>
          </TouchableOpacity>
          <View style={newFcWidgetStyles.seperator} />
          <TouchableOpacity style={newFcWidgetStyles.option} onPress={() => onFCSelected(1, 0)}>
            <RadioButton
              isSelected={selectedIndex === 1}
              style={newFcWidgetStyles.radioButton}
              onPress={() => onFCSelected(1, 0)}
            />
            <Text style={[newFcWidgetStyles.noOption, getLineHeight(16), fontStyle('normal')]}>
              {fcWidgetConfig?.noOption}
            </Text>
          </TouchableOpacity>
        </View>
        <View style={persuassionContainerStyle}>
          <View style={newFcWidgetStyles.persuassionMsg}>
            {selectedOption === undefined && (
              <>
                <Image
                  resizeMode="contain"
                  source={greyInfoIcon}
                  style={newFcWidgetStyles.infoImg}
                />
                <HTMLView stylesheet={fcUnSelectedStyles} value={fcWidgetConfig?.fcUnSelected} />
              </>
            )}
            {selectedOption?.value === true && (
              <HTMLView stylesheet={fcOptedStyles} value={fcWidgetConfig?.fcOpted} />
            )}
            {selectedOption?.value === false && (
              <HTMLView stylesheet={fcNotOptedStyles} value={fcWidgetConfig?.fcNotOpted} />
            )}
          </View>
        </View>
        {additionalDetailsURL && (
          <TouchableOpacity onPress={() => onAdditionalDetailsPress(additionalDetailsURL)}>
            <Text style={[newFcWidgetStyles.tncText, getLineHeight(12), fontStyle('regular')]}>
              {TERMS_AND_CONDITIONS}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  ) : showSocialProofingFc() === 1 ? (
    <View style={newFcWidgetStyles.container}>
      <LinearGradient
        colors={[colors.lightBlue21, colors.white]}
        start={{ x: 0.5, y: 0 }}
        end={{ x: 0.5, y: 1 }}
        style={newFcWidgetStyles.headerContainer}
      >
        <Image source={freeCancellationIcon} resizeMode="contain" style={newFcWidgetStyles.fcImg} />
        <View style={newFcWidgetStyles.headerMargin}>
          <Text style={[newFcWidgetStyles.headerText, getLineHeight(20), fontStyle('bold')]}>
            {fcWidgetConfig?.header}
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 4 }}>
            <Text
              style={[newFcWidgetStyles.refundText1, getLineHeight(14.4), fontStyle('regular')]}
            >
              {refundText}
            </Text>
            <Image
              resizeMode="contain"
              source={ASSETS.greenTick2}
              style={newFcWidgetStyles.tickImg}
            />
          </View>
        </View>
      </LinearGradient>
      <View style={newFcWidgetStyles.optionsContainer}>
        <View>
          {carouselData &&
            carouselData.length > 0 &&
            carouselData.every((item) => item.sentence) && <FcCarousel data={carouselData} />}
        </View>
        <View style={newFcWidgetStyles.innerContainer}>
          <TouchableOpacity
            style={newFcWidgetStyles.option}
            onPress={() => onFCSelected(0, insuranceAmount)}
          >
            <RadioButton
              isSelected={selectedIndex === 0}
              customStyle={newFcWidgetStyles.radioButton}
              onPress={() => onFCSelected(0, insuranceAmount)}
            />
            {isFCSelected && ancillaryDiscountDetails?.ancillaryDetails?.fc?.discountedPremium ? (
              <View style={newFcWidgetStyles.yesOption}>
                <HTMLView value={fcCheckBoxCTASelectedCouponText} stylesheet={htmlViewStyles} />
              </View>
            ) : (
              <View style={newFcWidgetStyles.yesOption}>
                <View style={newFcWidgetStyles.refundContainer}>
                  <Text
                    style={[
                      newFcWidgetStyles.subHeaderText,
                      getLineHeight(14),
                      { fontSize: 14, fontWeight: '400', color: colors.black },
                    ]}
                  >
                    <Text>{_label('add_for')} </Text>
                    {socialProofingDiscountedPremium > 0 ? (
                      <>
                        <Text
                          style={{ color: colors.greyText1, textDecorationLine: 'line-through' }}
                        >
                          ₹{insuranceAmount}
                        </Text>
                        <Text style={{ fontWeight: 'bold', color: colors.black }}>
                          {PRICE_PER_PERSON}
                        </Text>
                      </>
                    ) : (
                      <Text style={{ fontWeight: 'bold', color: colors.black }}>
                        {PREM_PER_PERSON}
                      </Text>
                    )}
                  </Text>
                </View>
                {discountText && (
                  <Text style={{ color: colors.green, marginTop: 4, flexWrap: 'wrap' }}>
                    {discountText}
                  </Text>
                )}
              </View>
            )}
          </TouchableOpacity>
          <View style={newFcWidgetStyles.seperator} />
          <TouchableOpacity style={newFcWidgetStyles.option} onPress={() => onFCSelected(1, 0)}>
            <RadioButton
              isSelected={selectedIndex === 1}
              customStyle={newFcWidgetStyles.radioButton}
              onPress={() => onFCSelected(1, 0)}
            />
            <Text style={[newFcWidgetStyles.noOption, getLineHeight(16), fontStyle('normal')]}>
              {fcWidgetConfig?.noOption}
            </Text>
          </TouchableOpacity>
        </View>
        {carouselData.length === 0 ? (
          <View style={persuassionContainerStyle}>
            <View style={newFcWidgetStyles.persuassionMsg}>
              {selectedOption === undefined && (
                <>
                  <Image
                    resizeMode="contain"
                    source={greyInfoIcon}
                    style={newFcWidgetStyles.infoImg}
                  />
                  <HTMLView stylesheet={fcUnSelectedStyles} value={fcWidgetConfig?.fcUnSelected} />
                </>
              )}
              {selectedOption?.value === true && (
                <HTMLView stylesheet={fcOptedStyles} value={fcWidgetConfig?.fcOpted} />
              )}
              {selectedOption?.value === false && (
                <HTMLView stylesheet={fcNotOptedStyles} value={fcWidgetConfig?.fcNotOpted} />
              )}
            </View>
          </View>
        ) : (
          <View style={{ marginBottom: 20 }} />
        )}
        {additionalDetailsURL && (
          <TouchableOpacity onPress={() => onAdditionalDetailsPress(additionalDetailsURL)}>
            <Text style={[newFcWidgetStyles.tncText, getLineHeight(12), fontStyle('regular')]}>
              {TERMS_AND_CONDITIONS}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  ) : null;
};

export default function FreeCancellation(props: FreeCancellationProps) {
  const compRef = useRef(null);
  const {
    selectedIndex,
    updateUserResponse,
    freeCancellationData,
    captureRef,
    fcUpgradeOption,
    ancillaryDiscountDetails,
    preserveFcSelection,
    isFCSelected,
    updateFCTGUserResponse,
    canShowFCTGTogether,
    fcTextConfig,
    isTgPlusShown,
    isFcDiscounted,
    logTravellerPageAddOnEvents,
  } = props;

  const successMessage = ancillaryDiscountDetails?.couponSuccessMessage ?? null;

  const discountedPremium = ancillaryDiscountDetails?.ancillaryDetails?.fc?.discountedPremium || 0;

  const cancellationOptions = freeCancellationData?.cancellationOptions;
  const insuranceAmount = cancellationOptions[0]?.insuranceAmount;
  useEffect(() => {
    if (preserveFcSelection) {
      updateUserResponse(0, insuranceAmount);
    }
  }, [preserveFcSelection, updateUserResponse, insuranceAmount]);

  useEffect(() => {
    if (successMessage) {
      updateUserResponse(0, discountedPremium);
      setTimeout(() => {
        scrollToErrorComponent(FREE_CANCELLATION);
      }, 1000);
    }
  }, [successMessage]);

  const onAdditionalDetailsPress = (url: string) => {
    const pageName = 'mob_rail_travellers';
    const params = {
      pageName,
      m_v15: pageName,
      m_c61: 'mob_rail_travellers_free_cancellation_click_know_more',
      m_v24: 'rails',
    };
    trackOmniture(pageName, params);
    openWebView(url);
  };

  useEffect(() => {
    captureRef(FREE_CANCELLATION, compRef.current);
  }, [compRef.current]);

  return (
    <View
      style={[
        styles.container,
        fcUpgradeOption === 1 && !canShowFCTGTogether && styles.containerForFCOption1,
      ]}
      ref={compRef}
      testID={props?.id}
    >
      <NewFreeCancellationWidget
        isTgPlusShown={isTgPlusShown}
        selectedIndex={selectedIndex}
        updateUserResponse={updateUserResponse}
        updateFCTGUserResponse={updateFCTGUserResponse}
        cancellationOptions={cancellationOptions}
        onAdditionalDetailsPress={onAdditionalDetailsPress}
        isFCSelected={isFCSelected}
        canShowFCTGTogether={canShowFCTGTogether}
        fcTextConfig={fcTextConfig}
        freeCancellationData={freeCancellationData}
        isFcDiscounted={isFcDiscounted}
        logTravellerPageAddOnEvents={logTravellerPageAddOnEvents}
        ancillaryDiscountDetails={ancillaryDiscountDetails}
      />
    </View>
  );
}

FreeCancellation.propTypes = {
  selectedIndex: PropTypes.number,
  hasError: PropTypes.bool,
  updateUserResponse: PropTypes.func,
  freeCancellationData: PropTypes.object,
  captureRef: PropTypes.func,
  fcUpgradeOption: PropTypes.number,
  ancillaryDiscountDetails: PropTypes.object,
  preserveFcSelection: PropTypes.bool,
  isFCSelected: PropTypes.bool,
  updateFCTGUserResponse: PropTypes.func,
  canShowFCTGTogether: PropTypes.bool,
  fcTextConfig: PropTypes.object,
  isTgPlusShown: PropTypes.bool,
  isFcDiscounted: PropTypes.bool,
  logTravellerPageAddOnEvents: PropTypes.func,
  id: PropTypes.string,
};
