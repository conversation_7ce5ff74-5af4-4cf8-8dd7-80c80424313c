export interface FcWidgetConfig {
  header: string;
  subHeader: string;
  yesOption: string;
  refundText: string;
  noOption?: string;
  fcUnSelected?: string;
  fcOpted?: string;
  fcNotOpted?: string;
  note?: string;
}
export interface FCTGTextConfig {
  TG?: Tg
  FC?: Fc;
  tgUpgradeVersion7?: {
    tgRacCoverageTitle?: string;
    tgRacCoverageSubtitle?: string;
  };
  FC_TG_DISCOUNT_V2?: FcTgDiscount;
  FC_SNACKBAR_DISCOUNT_TEXT: {
    fcSnackBarText: string;
  };
}

export interface FCSnackBarProps {
  freeCancellationData?: {
    cancellationOptions?: { insuranceAmount: number }[];
  };
  isFCSelected?: boolean;
  tripGuaranteeData?: {
    availabilityText?: string;
  };
  closeSnackBar: () => void;
  updateUserResponse: (index: number, premium: number) => void;
  selectedIndex: number;
}
export interface Tg {
  header: string
  subHeader: string
  refunds: Refund[]
  approxRefund: string
  tncCTA: string
  usages: string[]
  checkBoxCTA: string
  ctaYes: string
  ctaNo: string
  toast: string
}

interface FcTgDiscount {
  fcDiscountText: string;
  tgDiscountText: string;
  fcTgDiscountText: string;
  fcBottomSheetDiscountText: string;
  tgBottomSheetDiscountText: string;
  fcBottomSheetButtonText: string;
  tgBottomSheetButtonText: string;
  tgCheckBoxCTA: string;
  fcCheckBoxCTA: string;
  fcCheckBoxCTASelected: string;
  tgCheckBoxCTASelected: string;
}

export interface Refund {
  label: string
  title: string
  refund: number
  description: string
  refundMode: string
}

export interface Fc {
  checkBoxCTASelected: unknown;
  header: string
  subHeader: string
  approxRefund: string
  description: string
  tncCTA: string
  checkBoxCTA: string
  ctaYes: string
  ctaNo: string
  toast: string
  toastNew: string
}
