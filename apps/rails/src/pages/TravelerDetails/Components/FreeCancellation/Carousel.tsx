import React, { useRef, useState, useEffect } from 'react';
import {
  Dimensions,
  Text,
  View,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';

const screenWidth = Dimensions.get('window').width;

interface MyCarouselProps {
  data: unknown[];
}

const MyCarousel = ({ data }: MyCarouselProps) => {
  const flatListRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const totalItems = data.length;

  useEffect(() => {
    if (totalItems > 1) {
      const interval = setInterval(() => {
        setActiveIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % totalItems;
          if (flatListRef.current && nextIndex >= 0 && nextIndex < totalItems) {
            flatListRef.current.scrollToIndex({
              index: nextIndex,
              animated: true,
            });
          }
          return nextIndex;
        });
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [totalItems]);

  const renderItem = ({ item }) => (
    <View style={styles.slide}>
      <Image source={ASSETS.tickIcon} style={styles.tickIcon} />
      <View style={styles.textContainer}>
        <Text style={styles.slideText}>{item.sentence}</Text>
      </View>
    </View>
  );

  const goToIndex = (index) => {
    setActiveIndex(index);
    flatListRef.current.scrollToIndex({ index, animated: true });
  };

  return (
    <View style={styles.carouselContainer}>
      <LinearGradient
        colors={[colors.carouselColor1, colors.carouselColor2]}
        start={{ x: 1, y: 1 }}
        end={{ x: 0, y: 0 }}
        style={styles.gradientBackground}
      />
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        scrollEnabled={false}
      />
      {totalItems > 1 && (
        <View style={styles.dotContainer}>
          {data.map((_, index) => (
            <TouchableOpacity key={index} onPress={() => goToIndex(index)}>
              <View
                style={[styles.dot, activeIndex === index ? styles.activeDot : styles.inactiveDot]}
              />
            </TouchableOpacity>
          ))}
      </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  carouselContainer: {
    marginHorizontal: 10,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderColor: colors.governorBay,
    borderWidth: 1,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: -6,
    flex: 1,
  },
  gradientBackground: {
    ...StyleSheet.absoluteFillObject,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  slide: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 15,
    paddingRight: 20,
    paddingTop: 15,
    paddingBottom: 15,
    width: screenWidth - 55,
  },
  tickIcon: {
    width: 20,
    height: 20,
    marginRight: 7,
  },
  textContainer: {
    flex: 1,
    flexWrap: 'wrap',
    maxWidth: '100%',
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
  },
  slideText: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.purple7,
    textAlign: 'left',
    flexWrap: 'wrap',
    overflow: 'hidden',
    alignItems: 'center',
    maxWidth: screenWidth - 130,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  dotContainer: {
    position: 'absolute',
    top: '50%',
    right: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 1,
  },
  activeDot: {
    backgroundColor: colors.governorBay,
  },
  inactiveDot: {
    backgroundColor: colors.lightViolet1,
  },
});


export default MyCarousel;
