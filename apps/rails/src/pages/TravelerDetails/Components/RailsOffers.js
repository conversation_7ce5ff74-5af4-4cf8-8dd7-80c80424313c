import PropTypes from 'prop-types';
import React from 'react';
import isEmpty from 'lodash/isEmpty';
import { View, Text, Image, StyleSheet, ImageBackground, TouchableOpacity } from 'react-native';
import { showLongToast, showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import CouponTextInputContainer from '../Containers/CouponTextInputContainer';
import { trackTravellerPageEvent } from '../../../railsAnalytics';
import { getTrvListForCouponApply, validateCoupon } from '../TravelerDetailsUtils';
import { _label } from '../../../vernacular/AppLanguage';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import SuccesMessage from './SuccessMessage';
import get from 'lodash/get';


import border from '@mmt/legacy-assets/src/dashed_border.webp';

import checkBoxEnabledImage from '@mmt/legacy-assets/src/ic-radiobutton-active.webp';
import checkBoxDisabledImage from '@mmt/legacy-assets/src/ic-radiobutton-inactive.webp';

export default class RailsOffers extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showCdf: false,
      error: null,
      clickedCoupon: null,
      showLoader: false,
    };
  }
  UNSAFE_componentWillMount() {
    const railsCouponDiscount = true;
    if (railsCouponDiscount) {
      this.setState({ showCdf: true, showLoader: false });
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.couponData !== this.props.couponData && !this.props.couponData) {
      this.setState({ error: null, clickedCoupon: null });
    }
  }
  _onRemoveClick = () => {
    trackTravellerPageEvent('mob_rail_travellers_coupon_remove_clicked');
    this.setState({ error: null, clickedCoupon: null });
    showLongToast(`Coupon ${this.props.couponData.couponCode} Removed!`);
    this.props.couponsRemoved(this.props.couponData?.ancillaryDiscountDetails?.ancillaryType);
    this.props.setCouponData(undefined);
  };

  renderCheckboxImage(item) {
    if (this.props.couponData) {
      if (this.props.couponData.couponCode === item.couponCode) {
        return <Image source={checkBoxEnabledImage} style={styles.checkBoxImage} />;
      } else {
        return <Image source={checkBoxDisabledImage} style={styles.checkBoxImage} />;
      }
    } else {
      return <Image source={checkBoxDisabledImage} style={styles.checkBoxImage} />;
    }
  }

  onApplyClick = async (couponCode) => {
    const {
      selectedQuota,
      classValue,
      selectedTrainInfo,
      totalCollectibleAmount,
      childBerthMandatory,
      seniorCitizenApplicable,
      selectedTravelers,
      travelers,
      cmp,
      totalFare,
      baseFare,
      fcTgAvailableType,
      fcTgPrice,
      couponDataForAncillary,
    } = this.props;

    const passengerList = getTrvListForCouponApply(
      selectedTravelers,
      travelers,
      selectedQuota,
      childBerthMandatory,
      seniorCitizenApplicable,
    );
    this.setState({ showLoader: true });
    const promiseTimer = new Promise(function (resolve) {
      setTimeout(() => {
        resolve(-1);
      }, 3000);
    });

    const response = await Promise.race([
      promiseTimer,
      await validateCoupon({
        couponCode,
        selectedQuota,
        classValue,
        selectedTrainInfo,
        baseFare,
        totalFare,
        totalCollectibleAmount,
        passengerList,
        cmp,
        fcTgAvailableType,
        fcTgPrice,
        couponDataForAncillary,
      }),
    ]);
    this.setState({ showLoader: false });
    if (!response) {
      showShortToast(_label('something_went_wrong_retry'));
      return;
    }
    if (response.errorDetails) {
      this.setState({
        error: response.errorDetails.errorMessage,
        clickedCoupon: response.errorDetails.errorMessage,
      });
      return;
    }
    if (response.status !== 'success') {
      showShortToast(_label('something_went_wrong_retry'));
      return;
    }
    showLongToast(`Coupon ${response?.couponCode} Applied!`);
    this.props.setCouponData(response);
    this.props.addCouponToRecommendedList(response);
  };

  render() {
    if (!this.state.showCdf) {
      return null;
    }
    const couponNotSuccess = get(this, 'props.couponData.status', '') !== 'success';
    return (
      <View style={styles.container} testID={this.props?.id}>
        <Text style={[styles.offerText, fontStyle('black'), getLineHeight(22)]}>
          {_label('offers_n_discounts')}
        </Text>
        {this.state.showLoader && (
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: colors.white,
              position: 'absolute',
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              zIndex: 50,
            }}
          >
            <Spinner size={30} color="#008b8b" />
          </View>
        )}
        {this.props?.recommendedCoupons?.map((item, index) => {
          return (
            <View style={{ flexDirection: 'column', marginBottom: 30 }} key={item.couponCode}>
              <View style={styles.couponContainer}>
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={{
                    flexDirection: 'row',
                  }}
                  onPress={() => {
                    this.onApplyClick(item.couponCode);
                  }}
                  testID="rails_offers_coupon_apply_button"
                >
                  <>
                    <View style={{ marginRight: 16 }}>{this.renderCheckboxImage(item)}</View>
                    <ImageBackground
                      style={{
                        width: null,
                        height: 24,
                      }}
                      imageStyle={{ resizeMode: 'stretch' }}
                      source={border}
                    >
                      <View style={styles.appliedCouponTextContainer}>
                        <Text
                          style={[styles.appliedCouponText, fontStyle('black'), getLineHeight(12)]}
                        >
                          {item.couponCode}
                        </Text>
                      </View>
                    </ImageBackground>
                  </>
                </TouchableOpacity>
                {this.props.couponData && this.props.couponData.couponCode === item.couponCode && (
                  <View style={{ zIndex: 5 }}>
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={this._onRemoveClick}
                      testID="rails_offers_coupon_remove_button"
                    >
                      <View testID={`${this.props?.id}_recommendedCoupons_${index + 1}_remove`}>
                        <Text style={[styles.removeText, fontStyle('black'), getLineHeight(12)]}>
                          {_label('remove', { uppercase: true })}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
              {(!this.props.couponData || this.props.couponData.couponCode !== item.couponCode) &&
                this.state.clickedCoupon !== item.couponCode && (
                  <Text
                    style={[styles.successMessageText, fontStyle('regular'), getLineHeight(14)]}
                  >
                    {item.recommendationMessage}
                  </Text>
                )}
              {!isEmpty(this.state.error) &&
                couponNotSuccess &&
                this.state.clickedCoupon === item.couponCode && (
                  <Text style={[styles.errorText, fontStyle('regular'), getLineHeight(12)]}>
                    {this.state.error}
                  </Text>
                )}
              <SuccesMessage
                appliedCouponCode={item.couponCode}
                couponDataCouponCode={get(this, 'props.couponData.couponCode', '')}
                normalCouponSuccessMessage={get(
                  this,
                  this.props?.couponData?.ancillaryDiscountDetails?.couponSuccessMessage
                    ? 'props.couponData.ancillaryDiscountDetails.couponSuccessMessage'
                    : 'props.couponData.message',
                  '',
                )}
                fcTgCouponSuccessMessage={get(
                  this,
                  'props.couponData.ancillaryDiscountDetails.couponSuccessMessage',
                  '',
                )}
                ancillaryCoupon={
                  !!this.props?.couponData?.ancillaryDiscountDetails?.couponSuccessMessage
                }
              />
            </View>
          );
        })}
        <View testID="rails_offers_coupon_text_input_container">
          <CouponTextInputContainer
            setCouponData={this.props.setCouponData}
            addCouponToRecommendedList={this.props.addCouponToRecommendedList}
            id={`${this.props?.id}_coupon`}
          />

        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: { padding: 16, backgroundColor: colors.white },
  offerText: {
    fontSize: 22,
    color: colors.black,
    marginBottom: 20,
  },
  checkBoxImage: { height: 24, width: 24 },
  couponContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  appliedCouponTextContainer: {
    height: 24,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  appliedCouponText: {
    color: colors.lightSeaGreenTextColor,
    marginHorizontal: 8,
  },
  removeText: {
    color: colors.azure,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  successMessageText: {
    color: colors.defaultTextColor,
    width: '100%',
  },
  errorText: {
    color: colors.red6,
    paddingTop: 8,
  },
});

RailsOffers.propTypes = {
  couponData: PropTypes.object,
  setCouponData: PropTypes.func.isRequired,
  addCouponToRecommendedList: PropTypes.func.isRequired,
  couponsRemoved: PropTypes.func,
  selectedQuota: PropTypes.string,
  classValue: PropTypes.object,
  selectedTrainInfo: PropTypes.object,
  totalCollectibleAmount: PropTypes.number,
  childBerthMandatory: PropTypes.bool,
  seniorCitizenApplicable: PropTypes.bool,
  selectedTravelers: PropTypes.array,
  travelers: PropTypes.array,
  totalFare: PropTypes.number,
  baseFare: PropTypes.number,
  fcTgPrice : PropTypes.number,
  couponDataForAncillary: PropTypes.object,
  id: PropTypes.string,
  recommendedCoupons: PropTypes.array,
  cmp: PropTypes.any,
  fcTgAvailableType: PropTypes.string,
};

RailsOffers.defaultProps = {
  couponData: undefined,
};
