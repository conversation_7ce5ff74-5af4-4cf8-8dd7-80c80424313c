
import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import ReservationChoiceType from '../../Constants/ReservationChoiceType';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import RadioGroup from '@mmt/legacy-commons/Common/Components/Radio/RadioGroup';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
import { _label } from '../../../vernacular/AppLanguage';
import get from 'lodash/get';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { showNewAdditionalPreferenceVariants } from '@mmt/rails/src/RailsAbConfig';

export default class ReservationChoice extends React.Component {
  handleReservationChoiceClicked = (selectedOption) => {
    const { onReservationChoiceClicked, logTravellerGenericClickEvents } = this.props;
    const optionId = Number(selectedOption.id);
    const eventMapping = {
      99: TRAVELLER_CLICK_EVENTS.NO_PREFERENCE,
      1: TRAVELLER_CLICK_EVENTS.SAME_COACH_PREFERENCE,
      2: TRAVELLER_CLICK_EVENTS.ADDPREF_ONE_LOWER_BERTH,
      3: TRAVELLER_CLICK_EVENTS.ADDPREF_TWO_LOWER_BERTH,
    };
    if (eventMapping[optionId]) {
      logTravellerGenericClickEvents(eventMapping[optionId]);
    }
    onReservationChoiceClicked(selectedOption);
  };

  handlePress = (option) => {
    this.props.onReservationChoiceClicked(option);
  };

  renderOldVariant = () => {
    let allOptions = this.props.reservationChoice.map(choice => ({
      id: choice.value,
      text: choice.key,
      value: choice.value,
      obj: choice,
    }));
    let options = [];
      if (this.props.travelerCount && this.props.travelerCount > 1){
        options = [...allOptions];
      } else {
        options = allOptions.filter(option => Number(option.id) === 99 || Number(option.id) === 2);
      }

    const reservationChoiceNoneId = get(ReservationChoiceType, 'None.id', '');
    const selectedReservationId = get(this, 'props.selectedReservationChoice.id', '');
    const showWarning = reservationChoiceNoneId !== selectedReservationId;
    return (
      <View style={{ marginHorizontal: 0 }}>
        <RadioGroup
          options={options}
          displayTextResolver={(item = {}) => _label(item.text)}
          keyResolver={(item) => item.value}
          onSelect={this.handleReservationChoiceClicked}
          containerStyle={{ flexDirection: 'column' }}
          radioInputCommonStyle={{ width: '100%' }}
          selected={this.props.selectedReservationChoice}
          selectedValueResolver={item => item.value}
          id={this.props?.id}
        />

        {showWarning && (
          <View style={styles.warningContainer1}>
            <Text style={[styles.warningText, fontStyle('regular'), getLineHeight(12)]}>
              {_label(this.props.selectedReservationChoice.warning)}
            </Text>
          </View>
        )}
      </View>
    );
  };

  renderNewVariant = () => {
    const allOptions = this.props.reservationChoice.map((choice) => ({
      id: choice.value,
      text: choice.key,
      value: choice.value,
      obj: choice,
    }));
    let options = [];
      if (this.props.travelerCount && this.props.travelerCount > 1) {
        options = [...allOptions];
      } else {
        options = allOptions.filter(
          (option) => Number(option.id) === 99 || Number(option.id) === 2,
        );
      }
    const reservationChoiceNoneId = get(ReservationChoiceType, 'None.id', '');
    const selectedReservationId = get(this, 'props.selectedReservationChoice.id', '');
    const showWarning = reservationChoiceNoneId !== selectedReservationId;
    return (
      <View style={{ marginHorizontal: 0 }}>
        {options.map((option) => (
          <View key={option.id}>
            <TouchableRipple onPress={() => this.handlePress(option)}>
              <View style={styles.optionContainer}>
                <RadioButton
                  isSelected={this.props.selectedReservationChoice.value === option.value}
                  onPress={() => this.handlePress(option)}
                  radioSize={18}
                />
                <Text style={[styles.radioText, fontStyle('regular'), getLineHeight(14)]}>
                  {_label(option.text)}
                </Text>
              </View>
            </TouchableRipple>
            {this.props.selectedReservationChoice.value === option.value && showWarning && (
              <View style={styles.warningContainer2}>
                <Text style={[styles.warningText, fontStyle('regular'), getLineHeight(12)]}>
                  {_label(this.props.selectedReservationChoice.warning)}
                </Text>
              </View>
            )}
          </View>
        ))}
      </View>
    );
  };

  render() {
    const showNewAdditionalPreference = showNewAdditionalPreferenceVariants();
    return (
      <View>
        {showNewAdditionalPreference === 0 ? this.renderOldVariant() : this.renderNewVariant()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  optionContainer: { flexDirection: 'row', alignItems: 'center', paddingVertical: 10 },
  radioText: { color: colors.black, fontSize: 14, marginLeft: 10 },
  warningContainer1: {
    backgroundColor: colors.creamWhite,
    marginBottom: 16,
    borderRadius: 4,
    padding: 8,
  },
  warningContainer2: {
    backgroundColor: colors.creamWhite,
    marginTop: 4,
    marginBottom: 8,
    borderRadius: 8,
    padding: 8,
  },

  warningText: {
    fontSize: 12,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.lightYello,
  },
});

ReservationChoice.propTypes = {
  reservationChoice: PropTypes.array.isRequired,
  selectedReservationChoice: PropTypes.object.isRequired,
  onReservationChoiceClicked: PropTypes.func.isRequired,
  logTravellerGenericClickEvents: PropTypes.func.isRequired,
  travelerCount: PropTypes.number,
  id: PropTypes.string,
};

ReservationChoice.navigationOptions = {
  header: null,
};
