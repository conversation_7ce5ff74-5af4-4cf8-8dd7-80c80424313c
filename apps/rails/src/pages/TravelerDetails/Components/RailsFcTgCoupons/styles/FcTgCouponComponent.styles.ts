import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  text: {
    color: colors.white,
  },
  boldText: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontWeight: '700',
    lineHeight: 16,
    fontFamily: 'Lato',
  },
  boldTextSelected: {
    fontSize: 12,
    color: colors.lightGreen20,
    fontWeight: '700',
    lineHeight: 16,
    fontFamily: 'Lato',
  },
  lightText: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontWeight: '400',
    fontFamily: 'Lato',
    lineHeight: 16,
  },
  container: {
    backgroundColor: colors.lightGreen1,
    padding: 8,
    flexDirection: 'row',
    borderRadius: 8,
    marginTop: 10,
  },
  containerGradient: {
    padding: 8,
    flexDirection: 'row',
    borderRadius: 8,
    marginTop: 10,
  },
  offers: {
    marginRight: 5,
  },
  bannerContainer: {
    padding: 8,
    marginHorizontal: 10,
    marginVertical: 10,
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    flexDirection: 'row',
    borderColor: colors.lightGreen1,
    borderWidth: 1,
    borderRadius: 12,
    borderStyle: 'solid',
  },
  discountText: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.lightGreen19,
    marginBottom: 5,
    fontFamily: 'Lato',
  },
  subText: {
    fontSize: 12,
    color: colors.gray9,
    fontWeight: '400',
    fontFamily: 'Lato',
  },
});
