import React from 'react';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import { View, Image, Text } from 'react-native';
import { styles } from './styles/FcTgCouponComponent.styles';
import { connect } from 'react-redux';
import { COUPON_TYPE } from '../../railofyUtils';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface AncillaryDiscountDetails {
  ancillaryType: string;
  widgetMessage: {
    text: string;
    subText: string;
  };
}

interface Coupon {
  couponCode: string;
  ancillaryDiscountDetails?: AncillaryDiscountDetails;
}

interface Props {
  recommendedCoupons: Coupon[];
  couponData?: Coupon;
}

const FcTgCouponComponent: React.FC<Props> = ({ recommendedCoupons = [], couponData }) => {
  if (recommendedCoupons.length === 0) {
    return null;
  }

  const renderCouponMessage = (coupon: Coupon, isSelected: boolean) => (
    <View>
      <Text style={isSelected ? styles.boldTextSelected : styles.boldText}>
        {coupon.ancillaryDiscountDetails?.widgetMessage?.text}
      </Text>
      <Text style={styles.lightText}>
        {coupon.ancillaryDiscountDetails?.widgetMessage?.subText}
      </Text>
    </View>
  );

  return (
    <>
      {recommendedCoupons.map((coupon, index) => {
        const { ancillaryDiscountDetails, couponCode } = coupon;

        if (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_TG_TOGETHER) {
          const isSelected = couponData?.couponCode === couponCode;

          const CouponContainer = isSelected ? LinearGradient : View;
          const containerProps = isSelected
            ? {
                colors: [colors.green18, colors.white],
                start: { x: 1, y: 0 },
                end: { x: 0, y: 0 },
                style: styles.containerGradient,
              }
            : { style: styles.container };

          return (
            <CouponContainer key={index} {...containerProps}>
              <View>
                <Image
                  source={ASSETS.offers}
                  style={styles.offers}
                  resizeMode="contain"
                  height={16}
                  width={16}
                />
              </View>
              {renderCouponMessage(isSelected ? couponData! : coupon, isSelected)}
            </CouponContainer>
          );
        }

        return null;
      })}
    </>
  );
};

const mapDispatchToProps = () => ({});

const mapStateToProps = ({ railsTraveler }: unknown) => {
  const { recommendedCoupons, couponData } = railsTraveler;

  return {
    recommendedCoupons,
    couponData,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(FcTgCouponComponent);
