import React, { useState, useEffect, useRef } from 'react';
import { Dimensions, View, Text, Image, StyleSheet, FlatList } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles.js';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import PropTypes from 'prop-types';

const screenWidth = Dimensions.get('window').width;

const CustomCarousel = ({ titles }) => {
  const [activeSlide, setActiveSlide] = useState(0);
  const flatListRef = useRef(null);
  const carouselItems = titles.map((title) => ({ title, image: ASSETS.tickIcon }));

  const renderItem = ({ item }) => (
    <View style={styles.carouselItem} testID="trip_guarantee_bottomsheet_carousel_item">
      <Image
        source={item.image}
        style={styles.carouselImage}
        testID="trip_guarantee_bottomsheet_carousel_image"
      />
      <Text
        style={styles.carouselTitle}
        testID={`trip_guarantee_bottomsheet_carousel_title_${item.title}`}
      >
        {item.title}
      </Text>
    </View>
  );

  useEffect(() => {
    if (carouselItems.length > 1) {
      const interval = setInterval(() => {
        setActiveSlide((prevSlide) => {
          const nextIndex = (prevSlide + 1) % carouselItems.length;
          if (flatListRef.current && nextIndex >= 0 && nextIndex < carouselItems.length) {
            flatListRef.current.scrollToIndex({
              index: nextIndex,
              animated: true,
            });
          }
          return nextIndex;
        });
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [carouselItems.length]);

  return (
    <View style={styles.carouselWrapper} testID="trip_guarantee_bottomsheet_carousel_wrapper">
      <FlatList
        ref={flatListRef}
        data={carouselItems}
        renderItem={renderItem}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        pagingEnabled
        snapToAlignment="start"
        scrollEnabled={false}
        style={{ width: screenWidth }}
        testID="trip_guarantee_bottomsheet_carousel_flatlist"
      />
      {carouselItems.length > 1 && (
        <View
          style={styles.paginationDotsContainer}
          testID="trip_guarantee_bottomsheet_carousel_pagination_dots_container"
        >
        {carouselItems.map((_, index) => (
            <View
              key={index}
              style={[styles.dot, { opacity: activeSlide === index ? 1 : 0.3 }]}
              testID={`trip_guarantee_bottomsheet_carousel_dot_${index}`}
            />
        ))}
      </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  carouselWrapper: {
    flexDirection: 'row',
    flex: 1,
    overflow: 'hidden',
    paddingRight: 40,
  },
  carouselItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 20,
    borderRadius: 10,
    width: screenWidth * 0.9,
    flex: 1,
  },
  carouselImage: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  carouselTitle: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.purple7,
    fontFamily: 'Lato',
    lineHeight: 14.4,
    width: screenWidth * 0.7,
  },
  paginationDotsContainer: {
    flexDirection: 'row',
    position: 'absolute',
    right: 15,
    top: '90%',
    transform: [{ translateY: -10 }],
    alignItems: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 4,
    backgroundColor: colors.purple7,
    marginHorizontal: 1,
  },
});

CustomCarousel.propTypes = {
  titles: PropTypes.arrayOf(PropTypes.string),
};

export default CustomCarousel;
