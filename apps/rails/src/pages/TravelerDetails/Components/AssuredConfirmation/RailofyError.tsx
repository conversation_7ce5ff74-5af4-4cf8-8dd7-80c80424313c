import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';

import icon from '@mmt/legacy-assets/src/yellow_info_icon.webp';

interface RailofyErrorProps {
  showIcon?: boolean;
  errorMessage: string;
}
export default function RailofyError(props: RailofyErrorProps) {
  const { showIcon, errorMessage } = props;
  if (errorMessage) {
    return (
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {showIcon && <Image source={icon} style={styles.image} />}
          <Text style={[styles.errorText, fontStyle('regular'), getLineHeight(14)]}>
            {errorMessage}
        </Text>
        </View>
      </View>
    );
  }
  return null;
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    borderWidth: 0.5,
    borderColor: colors.lightGray,
    marginBottom: 10,
  },
  contentContainer: {
    flexDirection: 'row',
    backgroundColor: colors.creamWhite,
    padding: 8,
    alignItems: 'center',
    borderRadius: 2,
  },
  image: {
    marginRight: 8,
    height: 24,
    width: 24,
  },
  errorText: {
    fontSize: 14,
    color: colors.lightYello,
  },
});
