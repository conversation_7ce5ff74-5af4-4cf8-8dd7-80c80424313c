import { useState,useEffect } from 'react';
import { preferenceInfoRevampedItem,fareInfoRevampedItem } from '../../../../types/railofy.types';
import { getTicketFare } from '../../railofyUtils';

interface useTgThresholdHandlerConfig {
  premiumUpperCap : number;
  refundCouponUpperCap : number;
  fareInfoRevamped : fareInfoRevampedItem[] ;
  preferenceInfoRevamped : preferenceInfoRevampedItem[];
  numberOfPassengers: number;
}

export default function useTgThresholdHandler(config : useTgThresholdHandlerConfig) {
  const {
    premiumUpperCap,
    refundCouponUpperCap,
    fareInfoRevamped,
    preferenceInfoRevamped,
    numberOfPassengers,
  } = config;

  const isTripleAmount = () => {
    return Math.round(state.totalRefund + 0.5) === Math.round((numberOfPassengers * 3 * ticketFare) + 0.5);
  };
  const recalculateCosts = () => {
    const premiumPerPersonFinal = Math.min(premiumPerPersonInitial , Math.round(premiumThreshold / numberOfPassengers));
    const totalTicketFare = numberOfPassengers * ticketFare;
    const totalRefund = totalTicketFare + Math.min(2 * totalTicketFare, extraRefundThreshold);
    setState({
      ...state,
      premiumPerPersonFinal,
      totalTicketFare,
      totalRefund,
    });
  };

  // These 3 values are recalculated each time number of passengers change
  // premiumPerPersonFinal , totalTicketFare , totalRefund will be calculated as per formula and displayed
  const [state, setState] = useState({
    premiumPerPersonFinal : 0,
    totalTicketFare : 0,
    totalRefund : 0,
    premiumPerPerson: preferenceInfoRevamped[0]?.premiumPerPerson,
  });

  //These 4 values remain fixed throughout
  // threshold values , premium per person , ticket fare per person initial values required for calculations
  const [
    premiumThreshold,
    extraRefundThreshold,
    premiumPerPersonInitial,
    ticketFare ] = [
      premiumUpperCap,
      refundCouponUpperCap,
      preferenceInfoRevamped[0]?.premiumPerPerson,
      getTicketFare(fareInfoRevamped),
    ];

  useEffect(() => {
    recalculateCosts();
  },[numberOfPassengers]);

  return { ...state, isTripleAmount } as const;
}
