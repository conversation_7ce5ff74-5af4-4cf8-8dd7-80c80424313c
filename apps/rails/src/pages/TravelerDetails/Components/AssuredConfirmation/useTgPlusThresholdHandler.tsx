import { useState, useEffect } from 'react';

interface useTgPlusThresholdHandlerConfig {
  premiumUpperCap: number;
  refundCouponUpperCap: number;
  ticketFare: number;
  premiumPerPerson: number;
  numberOfPassengers: number;
}

export default function useTgPlusThresholdHandler(config: useTgPlusThresholdHandlerConfig) {
  const {
    premiumUpperCap,
    refundCouponUpperCap,
    ticketFare,
    premiumPerPerson,
    numberOfPassengers,
  } = config;

  const isTripleAmount = () => {
    return Math.round(tgPlusState.totalRefund + 0.5) === Math.round((numberOfPassengers * 3 * ticketFareInitial) + 0.5);
  };
  const recalculateCosts = () => {
    const premiumPerPersonFinal = Math.min(premiumPerPersonInitial, Math.round(premiumThreshold / numberOfPassengers));
    const totalTicketFare = numberOfPassengers * ticketFareInitial;
    const totalRefund = totalTicketFare + Math.min(2 * totalTicketFare, extraRefundThreshold);
    setTgPlusState({
      ...tgPlusState,
      premiumPerPersonFinal,
      totalTicketFare,
      totalRefund,
    });
  };

  const [tgPlusState, setTgPlusState] = useState({
    premiumPerPersonFinal: 0,
    totalTicketFare: 0,
    totalRefund: 0,
    premiumPerPerson: premiumPerPerson,
  });

  const [
    premiumThreshold,
    extraRefundThreshold,
    premiumPerPersonInitial,
    ticketFareInitial,
  ] = [
      premiumUpperCap,
      refundCouponUpperCap,
      premiumPerPerson,
      ticketFare,
    ];

  useEffect(() => {
    recalculateCosts();
  }, [numberOfPassengers]);

  return { ...tgPlusState, isTripleAmount } as const;
}
