import React, { useEffect, useState } from 'react';
import { GestureResponderEvent, Image, Modal, Text, TouchableOpacity, View } from 'react-native';
import Checkbox from '@mmt/legacy-commons/Common/Components/Checkbox';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import HTMLView from 'react-native-htmlview';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import {
  DISCOUNT_PLACEHOLDER,
  PRICE_REPLACER,
  TEST_ID_CONSTANTS,
} from 'apps/rails/src/Utils/RailsConstant';
import {
  trackFCTGEvar47or97Event,
  trackTravellerPageEvent,
} from 'apps/rails/src/railsAnalytics';
import LinearGradient from 'react-native-linear-gradient';
import { trackRailofyKnowMoreClick } from '../../../../Utils/railofyUtils';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import { COUPON_TYPE, RAILOFY_TYPE, openWebView } from '../../railofyUtils';
import { AssuredConfirmationProps } from '../Railofy/types';
import { styles } from './AssuredConfirmationUtils';
import useTgThresholdHandler from './useTgThresholdHandler';
import { FCTGTextConfig } from 'apps/rails/src/pages/TravelerDetails/Components/FreeCancellation/types';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { TRAVELERS_PAGE_TRACKING_KEY_NEW } from '../../../../Utils/RailsConstant';
import SocialProofingCarousel from 'apps/rails/src/pages/TravelerDetails/Components/AssuredConfirmation/SocialProofingCarousel.tsx';
import TripGuaranteeBottomSheet from 'apps/rails/src/pages/TravelerDetails/Components/AssuredConfirmation/TripGuaranteeBottomSheet';
import { showSocialProofingTg, getRailsFcTgTogetherValue,getTgRacPremiumVariant } from 'apps/rails/src/RailsAbConfig';

import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import { railofyValuesTrackingParams } from '../../../Review/RailsReviewActions';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

import greenTick from '@mmt/legacy-assets/src/free_cancellation_tick_green.webp';
import dot from '@mmt/legacy-assets/src/ic_dot_hollow_gray_copy.webp';
const isFcTgTogetherPokus = getRailsFcTgTogetherValue();

const containerDefaultBg = ['#c86dd7', '#3023ae'];

const TripGuaranteeV2 = (props: AssuredConfirmationProps) => {
  const {
    updateTgSelection,
    assuredConfirmationData: {
      backgroundColor,
      fareInfoRevamped,
      preferenceInfoRevamped,
      refundCouponUpperCap,
      premiumUpperCap,
      socialProofing,
      discountText,
      socialProofingDiscountedPremium,
      socialProofingBottomSheet,
    },
    numberOfPassengers,
    isTGSelected,
    updateFCTGUserResponse,
    displayTgBottomSheet,
    onBookNowClicked,
    tgTextConfig,
    tgDiscountValue,
    tgDiscountPremium,
    isTgDiscounted,
    logTravellerPageAddOnEvents,
    logTravellerPageBottomSheetEvents,
    tgDiscount,
    ancillaryDiscountDetails,
  } = props;

  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [racPremiumPokusVal, setRacPremiumPokusVal] = useState(false);

  const toggleBottomSheet = () => {
    setBottomSheetVisible(!isBottomSheetVisible);
  };

  let tgConfig = tgTextConfig;

  useEffect(() => {
    async function getRacPremiumPokusVal() {
      const racPremiumPokusVal = await getTgRacPremiumVariant();
      setRacPremiumPokusVal(racPremiumPokusVal);
    }
    getRacPremiumPokusVal();
  }, []);

  useEffect(() => {
    trackFCTGEvar47or97Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, `${RAIL_EVENTS.FCTG.TGP}1`);
  },[]);

  const fcTgTextConfig: FCTGTextConfig = useConfigStore(configKeys.RAILS_TG_FC_TEXT_CONFIG);
  if (!tgConfig?.header) {
    tgConfig = fcTgTextConfig.TG;
  }

  const onCheckboxSelected = () => {
    const index = !isTGSelected ? 0 : 1;
    updateFCTGUserResponse(
      index,
      preferenceInfoRevamped?.[index].premiumPerPerson || 0,
      RAILOFY_TYPE.TG,
      tgConfig?.toast,
    );
    if (!isTGSelected) {
      logTravellerPageAddOnEvents(
        railofyValuesTrackingParams,
        TRAVELLER_CLICK_EVENTS.TRIP_GUARANTEE_OPTED,
      );
      logTravellerPageAddOnEvents(railofyValuesTrackingParams, TRAVELLER_CLICK_EVENTS.FC_TG_WIDGET);
    } else {
      logTravellerPageAddOnEvents(
        railofyValuesTrackingParams,
        TRAVELLER_CLICK_EVENTS.TRIP_GUARANTEE_DESELECTED,
      );
    }
    // TG_traveler_select or TG_traveler_unselect
    trackFCTGEvar47or97Event(
      null,
      `${RAIL_EVENTS.FCTG.TG}${RAIL_EVENTS.FCTG.TRAVELER}${
        !isTGSelected ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
      }`,
      RAIL_EVENTS.FCTG.TG,
    );
    trackTravellerPageEvent(
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_TG}_${!isTGSelected ? 'check' : 'uncheck'}`,
    );
  };

  const onTgSelection = (e: GestureResponderEvent) => (index: number) => {
    if (e.preventDefault) {
      e.preventDefault();
    }
    updateTgSelection(index, preferenceInfoRevamped?.[index].premiumPerPerson || 0);
    trackTravellerPageEvent(
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_TG_PROMPT}_${!index ? 'yes' : 'no'}`,
    );
    //TG_prompt_select or TG_prompt_unselect
    trackFCTGEvar47or97Event(
      null,
      `${RAIL_EVENTS.FCTG.TG}${RAIL_EVENTS.FCTG.PROMPT}${
        !isTGSelected ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
      }`,
      RAIL_EVENTS.FCTG.TG,
    );
    onBookNowClicked();
  };

  const ticketFareText = fareInfoRevamped && fareInfoRevamped[0].value[0].text;

  const onMoreDetailsPressed = () => {
    trackRailofyKnowMoreClick('mob_rail_travellers');
    const url = racPremiumPokusVal ? tgConfig?.tncCTARacPremiumLink : tgConfig?.tncCTALink;
    openWebView(url);
  };

  const { totalRefund, premiumPerPerson } = useTgThresholdHandler({
    premiumUpperCap,
    refundCouponUpperCap,
    fareInfoRevamped,
    preferenceInfoRevamped,
    numberOfPassengers,
  });

  const replaceText = (text: string, premium: number, discountPremium: number) => {
    return text
      ?.replace(DISCOUNT_PLACEHOLDER, premium.toString())
      ?.replace(PRICE_REPLACER, discountPremium?.toString());
  };

  const buttonTextForDiscount = replaceText(
    fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgBottomSheetButtonText,
    premiumPerPerson,
    ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY &&
      tgDiscount?.bundleDiscountedPremium
      ? tgDiscount?.bundleDiscountedPremium
      : tgDiscountPremium,
  );

  const checkBoxCTA =
    (tgDiscountPremium > 0 && isTgDiscounted && !ancillaryDiscountDetails?.ancillaryType) ||
    (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY &&
      tgDiscount?.bundleDiscountedPremium)
      ? replaceText(
          fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTA,
          premiumPerPerson,
          ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY
            ? tgDiscount?.bundleDiscountedPremium
            : tgDiscountPremium,
        )
      : replaceText(tgConfig?.checkBoxCTA, premiumPerPerson, premiumPerPerson);

  const checkBoxCTAForSelected = replaceText(
    tgDiscountPremium > 0
      ? isTgDiscounted
        ? fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTASelected
        : fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTASelected.replace(
            `<a>₹${DISCOUNT_PLACEHOLDER}</a> `,
            '',
          )
      : tgConfig?.checkBoxCTASelected,
    premiumPerPerson,
    tgDiscountPremium > 0 && isTgDiscounted ? tgDiscountPremium : premiumPerPerson,
  );

  const getSocialProofingCheckBoxCTA = () => {
    //Checkbox Text for Social Proofing Discounted Premium
    const textConfig =
      socialProofingDiscountedPremium > 0
        ? fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTA
        : tgConfig?.checkBoxCTA;

    return replaceText(textConfig, premiumPerPerson, socialProofingDiscountedPremium);
  };

  const getCheckBoxCTAForSelected = () => {
    const isDiscounted = socialProofingDiscountedPremium > 0;
    const isSelected = isTgDiscounted;

    if (tgDiscount?.ancillaryDiscountedPremium) {
      return tgCheckBoxCTASelectedCouponText;
    }

    let textConfig = isDiscounted
      ? isSelected
        ? fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTASelected
        : fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTASelected.replace(
            `<a>₹${DISCOUNT_PLACEHOLDER}</a> `,
            '',
          )
      : tgConfig?.checkBoxCTASelected;

    if (isFcTgTogetherPokus === 3) {
        textConfig = fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTASelected.replace(
          `<a>₹${DISCOUNT_PLACEHOLDER}</a> `,
          '',
        );
      }
    const premiumValue =
      tgDiscountPremium > 0 && isSelected ? tgDiscountPremium : socialProofingDiscountedPremium;
    return replaceText(textConfig, socialProofingDiscountedPremium, premiumValue);
  };

  useEffect(() => {
    let hasLogged = false;
    if (displayTgBottomSheet && !hasLogged) {
      logTravellerPageBottomSheetEvents(TRAVELLER_CLICK_EVENTS.TRIP_GUARANTEE_BOTTOM_SHEET);
      hasLogged = true;
    }
  }, []);


  const tgCheckBoxCTASelectedCouponText = replaceText(
    fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgCheckBoxCTASelectedCouponText?.replace(
      `${DISCOUNT_PLACEHOLDER} `,
      premiumPerPerson,
    ),
    premiumPerPerson,
    ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY
      ? tgDiscount?.bundleDiscountedPremium
      : tgDiscount?.ancillaryDiscountedPremium,
  );

  return (
    <View
      style={[styles.fcTgContainer, displayTgBottomSheet && styles.bottomSheetContent]}
      testID="trip_guarantee_bottomsheet_container"
    >
      <LinearGradient
        colors={backgroundColor || containerDefaultBg}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.gradientContainer}
        testID={`${props?.id}_banner`}
      >
        <Text style={[styles.header, fontStyle('bold')]} testID="trip_guarantee_bottomsheet_header">
          {tgConfig?.header}
        </Text>
        <Text
          style={[styles.description, fontStyle('bold')]}
          testID="trip_guarantee_bottomsheet_description"
        >
          {tgConfig?.subHeader}
        </Text>
      </LinearGradient>
      <View
        style={[styles.contentDataContainer, styles.flex0]}
        testID="trip_guarantee_bottomsheet_content"
      >
        <View style={styles.refundsContainer} testID="trip_guarantee_bottomsheet_refunds_container">
          {tgConfig?.refunds.map((refund, index) => {
            return (
              <View
                key={index}
                style={styles.refundContainer}
                testID={`trip_guarantee_bottomsheet_refund_${index}`}
              >
                <LinearGradient
                  colors={[colors.grey5, colors.grey7, colors.lightGrey]}
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 0.0 }}
                  style={styles.verticalGradient}
                  testID={`trip_guarantee_bottomsheet_refund_gradient_${index}`}
                >
                  <View
                    style={styles.refundLabelContainer}
                    testID={`trip_guarantee_bottomsheet_refund_label_container_${index}`}
                  >
                    <Text
                      style={[styles.refundLabel, fontStyle('regular')]}
                      testID={`trip_guarantee_bottomsheet_refund_label_${index}`}
                    >
                      {refund.label}
                    </Text>
                  </View>
                </LinearGradient>
                <View
                  style={styles.fareContainer}
                  testID={`trip_guarantee_bottomsheet_fare_container_${index}`}
                >
                  <Text style={[styles.fareTitle, fontStyle('bold')]}>
                    {refund.title}
                    {index === 0 && (
                      <Text
                        style={[styles.fare, fontStyle('medium')]}
                        testID={`trip_guarantee_bottomsheet_fare_text_${index}`}
                      >
                        {ticketFareText}
                      </Text>
                    )}
                  </Text>
                  <Text
                    style={[styles.fareDescription, fontStyle('regular')]}
                    testID={`trip_guarantee_bottomsheet_fare_description_${index}`}
                  >
                    {refund.description}
                  </Text>
                  <Text
                    style={[styles.refundMode, fontStyle('regular')]}
                    testID={`trip_guarantee_bottomsheet_refund_mode_${index}`}
                  >
                    {refund.refundMode}
                  </Text>
                </View>
              </View>
            );
          })}
        </View>
        <View style={styles.refundRow} testID="trip_guarantee_bottomsheet_refund_row">
          <Text
            style={[styles.approxRefund, fontStyle('black')]}
            testID="trip_guarantee_bottomsheet_approx_refund"
          >
            {tgConfig?.approxRefund}
            <Text
              style={[styles.totalRefund, fontStyle('black')]}
              testID="trip_guarantee_bottomsheet_total_refund"
            >{`₹${totalRefund}`}</Text>
          </Text>
          <Image source={greenTick} style={styles.darkGreenTickIcon} />
        </View>
        <View style={styles.marginTop}>
          {(racPremiumPokusVal ? tgConfig?.usagesRacPremium : tgConfig?.usages)?.map((usage, index) => {
            return (
                <View
                  key={index}
                  style={styles.usageContainer}
                  testID="trip_guarantee_bottomsheet_usage_container"
                >
                  <Image
                    source={dot}
                    style={styles.dotIcon}
                    testID="trip_guarantee_bottomsheet_dot_icon"
                  />
                  <Text
                    style={[styles.usageText, fontStyle('regular')]}
                    testID="trip_guarantee_bottomsheet_usage_text"
                  >
                  {usage}
                  {index === 1 && (
                    <Text
                      onPress={onMoreDetailsPressed}
                      style={[styles.tncText, fontStyle('regular')]}
                    >
                      {tgConfig?.tncCTA}
                    </Text>
                  )}
                </Text>
              </View>
            );
          })}
        </View>
        {showSocialProofingTg() === 1 && discountText && (
          <View testID="trip_guarantee_bottomsheet_discount_section">
            <View
              style={styles.newDiscountTextContainer}
              testID="trip_guarantee_bottomsheet_discount_container"
            >
              <Image
                source={ASSETS.offersIcon}
                style={styles.offersIcon}
                testID="trip_guarantee_bottomsheet_offers_icon"
              />
              <Text style={styles.newDiscountText}>
                <Text style={{ fontWeight: 'bold', fontSize: 14 }}>{discountText} </Text>
              </Text>
              {/* <TouchableOpacity onPress={toggleBottomSheet}>
              <Image source={infoIcon} style={styles.infoIcon} />
            </TouchableOpacity> */}
              <Modal
                visible={isBottomSheetVisible}
                animationType="slide"
                transparent={true}
                onRequestClose={toggleBottomSheet}
                testID="trip_guarantee_bottomsheet_modal"
              >
                <View
                  style={styles.bottomSheetContainer}
                  testID="trip_guarantee_bottomsheet_modal_container"
                >
                  <TripGuaranteeBottomSheet
                    onClose={toggleBottomSheet}
                    socialProofingBottomSheet={socialProofingBottomSheet}
                  />
                </View>
              </Modal>
            </View>
          </View>
        )}
        {!displayTgBottomSheet && (
          <Checkbox
            id={`${TEST_ID_CONSTANTS.TRAVELLER_DETAILS}TG_checkbox`}
            label={
              showSocialProofingTg() === 1 && socialProofingDiscountedPremium > 0
                ? isTGSelected
                  ? getCheckBoxCTAForSelected()
                  : getSocialProofingCheckBoxCTA()
                : isTGSelected
                ? (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY &&
                    tgDiscount?.bundleDiscountedPremium) ||
                  tgDiscount?.ancillaryDiscountedPremium
                  ? tgCheckBoxCTASelectedCouponText
                  : checkBoxCTAForSelected
                : checkBoxCTA
            }
            checked={isTGSelected}
            onChange={onCheckboxSelected}
            containerStyle={styles.checkboxContainer}
            labelStyle={[styles.checkBoxText, fontStyle('medium')]}
            htmlView={true}
          />
        )}
      </View>
      {showSocialProofingTg() === 1 && socialProofing && !displayTgBottomSheet && (
        <LinearGradient
          colors={['#F4E9FF', '#FBF8FF']}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 0 }}
          style={styles.newCarouselContainer}
          testID="trip_guarantee_bottomsheet_carousel_gradient"
        >
          <SocialProofingCarousel titles={socialProofing} />
        </LinearGradient>
      )}
      {displayTgBottomSheet && (
        <View style={styles.tgBottomSheetContainer} testID="trip_guarantee_bottomsheet_actions">
          {(tgDiscountValue > 0 && isTgDiscounted) ||
            (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY &&
              ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountedPremium && (
              <View style={[styles.discountContainer, styles.borderWidthZero]}>
                <Image
                  source={require('packages/legacy-assets/src/rails/offers.webp')}
                  style={styles.discountImage}
                    testID="trip_guarantee_bottomsheet_discount_image"
                />
                  <View>
                  <HTMLView
                    value={fcTgTextConfig?.FC_TG_DISCOUNT_V2?.tgBottomSheetDiscountText?.replace(
                      DISCOUNT_PLACEHOLDER,
                      ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY
                        ? ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountAmount ||
                              tgDiscountValue
                        : tgDiscountValue,
                    )}
                    stylesheet={{
                      p: styles.textStyle,
                      b: fontStyle('bold'),
                    }}
                      testID="trip_guarantee_bottomsheet_discount_text"
                  />
                </View>
              </View>
            ))}
          <TouchableOpacity
            onPress={(e) => onTgSelection(e)(0)}
            testID="trip_guarantee_bottomsheet_yes_button"
          >
            <LinearGradient
              colors={[colors.lightBlue, colors.darkBlue]}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 0.0 }}
              style={styles.ctaBottomSheetYesButton}
              testID="trip_guarantee_bottomsheet_yes_gradient"
            >
              {(tgDiscountValue > 0 && isTgDiscounted) ||
              (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY &&
                ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountedPremium) ? (
                <Text
                  style={[styles.yesButtonText, styles.buttonText]}
                  testID="trip_guarantee_bottomsheet_yes_text_discount"
                >
                  <HTMLView
                    value={buttonTextForDiscount}
                    stylesheet={{
                      p: [styles.yesButtonText, styles.buttonText, fontStyle('bold')],
                      a: [styles.cutText, fontStyle('regular')],
                    }}
                    testID="trip_guarantee_bottomsheet_yes_text_discount_htmlview"
                  />
                </Text>
              ) : (
                <Text
                  style={[styles.yesButtonText, styles.buttonText]}
                  testID="trip_guarantee_bottomsheet_yes_text"
                >
                  {tgConfig?.ctaYes?.replace(PRICE_REPLACER, premiumPerPerson?.toString())}
                </Text>
              )}
            </LinearGradient>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={(e) => onTgSelection(e)(1)}
            style={styles.noButtonHeight}
            testID="trip_guarantee_bottomsheet_no_button"
          >
            <Text
              style={[styles.noButtonText, styles.buttonText]}
              testID="trip_guarantee_bottomsheet_no_text"
            >
              {tgConfig?.ctaNo}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
export default React.memo(TripGuaranteeV2);
