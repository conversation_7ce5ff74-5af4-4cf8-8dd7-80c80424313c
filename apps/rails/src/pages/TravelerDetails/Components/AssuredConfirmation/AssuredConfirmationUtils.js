import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet, Platform } from 'react-native';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import flightsIcon from '@mmt/legacy-assets/src/flights_icon.webp';
import busIcon from '@mmt/legacy-assets/src/bus_icon.webp';
import cabIcon from '@mmt/legacy-assets/src/cabs_icon.webp';
import trainIcon from '@mmt/legacy-assets/src/train_icon.webp';
import dotIcon from '@mmt/legacy-assets/src/dot_icon.webp';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';

export const refundWays = [
  {
    icon: flightsIcon,
    text: 'flights',
    dot: dotIcon,
    width: 18,
    height: 9,
  },
  {
    icon: busIcon,
    text: 'buses',
    dot: dotIcon,
    width: 9,
    height: 9,
  },
  {
    icon: cabIcon,
    text: 'cabs',
    dot: dotIcon,
    width: 10,
    height: 9,
  },
  {
    icon: trainIcon,
    text: 'trains',
    dot: null,
    width: 8,
    height: 11,
  },
];

const flexDirectionRow = {
  flexDirection: 'row',
};

const commonAlignAndFlexDirection = {
  ...flexDirectionRow,
  alignItems: 'center',
};

export function getTextStyle(dataObject) {
  const style = StyleSheet.create({
    textStyle: {
      fontSize: Number(dataObject.fontSize) || 10,
      color: dataObject.color,
      letterSpacing: Number(dataObject.letterSpacing) || 0,
      marginRight: 3,
    },
  });
  return [style.textStyle, fontStyle(dataObject.fontFamily || 'regular')];
}

export const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
  refundWaysDotStyle: {
    marginVertical: 12,
    marginHorizontal: 4,
    height: 4,
    width: 4,
  },
  headerSubtitleText: {
    fontSize: 12,
    fontFamily: 'Lato',
    fontWeight: '400',
    color: colors.purple9,
  },
  additionalTextV2: {
    color: colors.primary,
    fontFamily: 'Lato',
    fontWeight: '400',
    fontSize: 12,
    flex: 1,
    textAlign: 'right',
    marginRight: 4,
  },
  flexDirectionRow: {
    ...flexDirectionRow,
  },
  fareInfoKeyStyle: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  approxRefundMargin: {
    marginLeft: 'auto',
    marginRight: 36,
  },
  marginLeftAuto: {
    marginLeft: 'auto',
  },
  marginRightAuto: {
    marginRight: 'auto',
  },
  radioTextStyle: {
    marginLeft: 8,
    marginVertical: 1,
  },
  descriptionTextStyle: {
    marginTop: 3,
    marginBottom: 7,
  },
  tgQuestionContainer: {
    marginTop: 16,
    marginBottom: 9,
    ...flexDirectionRow,
  },
  tgExplanationContainer: {
    ...flexDirectionRow,
    marginBottom: 8,
    justifyContent: 'space-between',
  },
  tgExplanation: {
    paddingLeft: 'auto',
    paddingRight: 'auto',
    width: '48%',
  },
  tgExplanationHeader: {
    backgroundColor: colors.purple10,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    paddingVertical: 5,
  },
  tgExplanationContentContainer: {
    paddingTop: 8,
    paddingBottom: 15,
    paddingLeft: 4,
    paddingRight: 4,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    borderColor: colors.lightGray,
    alignItems: 'center',
  },
  refundWaysImageStyle: {
    marginRight: 5,
  },
  container: {
    backgroundColor: colors.grayBg,
    position: 'relative',
    paddingTop: 11,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: colors.lightGray,
  },
  bannerTag: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    height: 24,
    position: 'absolute',
    top: 0,
    right: 28,
    zIndex: 99999,
    paddingHorizontal: 9,
  },
  gradientContainer: {
    paddingVertical: 13,
    paddingLeft: 16,
    paddingRight: 52,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  availabilityText: {
    fontFamily: fonts.black,
    fontSize: 18,
    color: colors.white,
  },
  contentContainer: {
    backgroundColor: colors.white,
    padding: 16,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  fareInfoContainer: {
    // justifyContent: 'space-between',
    marginBottom: 6,
  },
  fareInfoRow: {
    ...commonAlignAndFlexDirection,
  },
  refundWaysStyle: {
    ...commonAlignAndFlexDirection,
  },
  greenTickIcon: {
    height: 12,
    width: 12,
    alignSelf: 'center',
  },
  darkGreenTickIcon: {
    height: 12,
    width: 12,
    alignSelf: 'center',
    tintColor: colors.lightGreen16,
    marginLeft: 4,
  },
  dotIcon: {
    height: 6,
    width: 6,
    marginRight: 6,
    tintColor: colors.greyText1,
    marginTop: 4,
  },
  premiumTextContainer: {
    marginTop: 15,
    flexDirection: 'row',
  },
  alignTextCenter: {
    textAlign: 'center',
  },
  premiumText: {
    fontFamily: fonts.regular,
    fontSize: 13,
    color: colors.defaultTextColor,
  },
  radioGroupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  radioContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: colors.grey39,
    paddingVertical: 10,
    paddingHorizontal: 8,
    flex: 1,
  },
  radioLabel: {
    marginLeft: 8,
    fontFamily: fonts.regular,
    fontSize: 14,
    color: colors.black,
  },
  linkText: {
    color: colors.azure,
    fontFamily: fonts.bold,
    fontSize: 12,
    marginTop: 4,
    paddingRight: 8,
  },
  offerContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  offerTextContainer: {
    flex: 1,
    marginLeft: 20,
  },
  offerDummyComponent: {
    height: 15,
  },
  errorContainer: {
    backgroundColor: colors.red3,
    marginTop: 4,
    paddingLeft: 8,
    paddingVertical: 4,
    borderRadius: 2,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    fontFamily: fonts.regular,
  },
  successContainer: {
    marginTop: 4,
    paddingLeft: 8,
    paddingVertical: 4,
    borderRadius: 2,
    backgroundColor: colors.lightGreen7,
  },
  successText: {
    fontSize: 12,
    color: colors.lightGreen11,
    fontFamily: fonts.bold,
  },
  notOptedContainer: {
    marginTop: 4,
    paddingLeft: 8,
    paddingVertical: 4,
    borderRadius: 2,
    backgroundColor: colors.creamWhite,
  },
  notOptedText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.lightYello,
  },
  checkboxContainer: {
    marginTop: 8,
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginLeft: -4,
  },
  checkBoxText: {
    color: colors.black,
    fontSize: 14,
  },
  bottomSheetContent: {
    flex: 0,
    backgroundColor: colors.white,
    ...Platform.select({
      ios: {
        borderRadius: 0,
        marginBottom: -24, //Temp fix
      },
    }),
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  buttonText: {
    fontSize: 15.75,
    lineHeight: 21,
    fontWeight: '700',
  },
  noButtonText: {
    color: colors.azure,
    textAlign: 'center',
  },
  noButtonHeight: {
    height: 21,
    marginBottom: 5,
  },
  yesButtonText: {
    color: colors.white,
  },
  cutText: { textDecorationLine: 'line-through', fontSize: 14, color: colors.gray8 },
  ctaBottomSheetYesButton: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 43,
    borderRadius: 8,
    marginBottom: 16,
  },
  tgBottomSheetContainer: {
    marginVertical: 20,
    marginHorizontal: 16,
  },
  //v2
  verticalGradient: {
    width: 20,
  },
  seperator: {
    marginBottom: 12,
    marginTop: 6,
    borderColor: colors.lightGray,
    borderWidth: 0.5,
  },
  refundContainer: {
    flex: 1,
    height: 70,
    borderRadius: 8,
    borderColor: colors.lightGray,
    borderWidth: 1,
    flexDirection: 'row',
    overflow: 'hidden',
    marginHorizontal: 7,
  },
  refundLabelContainer: {
    transform: [{ rotate: '-90deg' }],
    position: 'absolute',
    left: -1,
    bottom: 0,
    top: 0,
  },
  refundLabel: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontWeight: '400',
    paddingTop: 2,
    paddingBottom: 4,
    width: 70,
    textAlign: 'center',
  },
  fareContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flexGrow: 1,
  },
  fareTitle: {
    fontSize: 12,
    color: colors.purple3,
    fontWeight: '900',
    marginBottom: 2,
  },
  fare: {
    fontSize: 12,
    color: colors.purple3,
    fontWeight: '900',
  },
  totalRefund: {
    fontSize: 14,
    color: colors.lightGreen16,
    fontWeight: '900',
  },
  fareDescription: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 2,
  },
  refundMode: {
    lineHeight: 14,
    fontSize: 12,
    color: colors.purple3,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 2,
  },
  approxRefund: {
    fontSize: 14,
    color: colors.black,
    fontWeight: '900',
    textAlign: 'center',
  },
  contentDataContainer: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    borderBottomRightRadius: 12,
    borderBottomLeftRadius: 12,
  },
  refundsContainer: {
    flexDirection: 'row',
    marginTop: 12,
    justifyContent: 'space-between',
  },
  description: {
    fontSize: 12,
    marginTop: 3,
    marginBottom: 3,
    color: colors.white,
  },
  header: {
    fontSize: 16,
    marginTop: 3,
    marginBottom: 3,
    color: colors.white,
  },
  marginTop: {
    marginTop: 14,
  },
  bottomText: {
    marginTop: 14,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  usageContainer: {
    marginHorizontal: 16,
    flexDirection: 'row',
    marginTop: 2,
  },
  usageText: {
    fontSize: 12,
    color: colors.greyText1,
    fontWeight: '400',
  },
  fcTgContainer: {
    paddingBottom: 0,
    backgroundColor: colors.white,
    marginBottom: 10,
    borderRadius: 12,
    ...getPlatformElevation(2),
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
  },
  fcTgStaticContainer: {
    paddingBottom: 6,
    backgroundColor: colors.white,
    marginBottom: 10,
    borderRadius: 12,
    marginHorizontal: 15,
    ...getPlatformElevation(2),
    overflow: 'hidden',
  },
  tncText: {
    color: colors.primary,
    fontSize: 12,
  },
  refundRow: {
    flexDirection: 'row',
    marginTop: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  flex0: {
    flex: 0,
  },
  tgMax: {
    height: 48,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexGrow: 0,
    paddingLeft: 8,
    paddingRight: 4,
    backgroundColor: colors.purple11,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  tgMaxVersion7: {
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 8,
    paddingRight: 4,
    backgroundColor: colors.lightGreen18,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    flex: 1,
  },
  tgMaxVersion7Upragde: {
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 8,
    paddingRight: 4,
    backgroundColor: colors.lighterBlue,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    flex: 1,
  },
  upgradeText: {
    lineHeight: 14,
    flex: 1,
    fontFamily: 'Lato',
    fontWeight: 'normal',
    fontStretch: 'normal',
    fontSize: 12,
    textAlign: 'left',
    color: colors.black,
    fontStyle: 'normal',
  },
  textButton: {
    flex: 0.2,
    height: 32,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 8,
    paddingRight: 4,
    borderRadius: 8,
  },

  buttonLabel: {
    lineHeight: 14,
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.azure,
  },
  racCoverageBold: {
    color: colors.lightGreen16,
    fontWeight: '700',
    fontSize: 12,
    fontFamily: 'Lato',
    lineHeight: 15,
  },
  racCoverage: {
    fontSize: 12,
    fontFamily: 'Lato',
    lineHeight: 15,
    color: colors.defaultTextColor,
  },
  racCoverageV7: {
    fontSize: 12,
    fontFamily: 'Lato',
    lineHeight: 15,
    color: colors.black,
  },
  crossIconSmall: {
    width: 17,
    height: 17,
  },
  crossIconLarge: {
    width: 24,
    height: 24,
  },
  marginHorizontal10: {
    marginHorizontal: 10,
  },
  discountContainer: {
    flexDirection: 'row',
    padding: 10,
    borderRadius: 16,
    borderColor: colors.lightGreen9,
    borderWidth: 1,
    zIndex: -1,
    position: 'relative',
    backgroundColor: colors.lightGreen18,
    alignItems: 'center',
    top: 18,
    paddingTop: 6,
    paddingBottom: 20,
  },
  borderWidthZero: {
    borderWidth: 0,
  },
  discountImage: {
    height: 23,
    width: 23,
    marginRight: 6,
  },
  newDiscountTextContainer: {
    backgroundColor: colors.lightGreen1,
    borderRadius: 8,
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingVertical: 9.5,
    marginTop: 10,
    flexDirection: 'row',
  },
  newDiscountText: {
    color: colors.lightGreen16,
    fontFamily: 'Lato',
    fontSize: 12,
    marginRight: 4,
    paddingLeft: 4,
    lineHeight: 14.4,
  },
  infoIcon: {
    width: 16,
    height: 16,
  },
  offersIcon: {
    width: 20,
    height: 20,
    marginLeft: 8,
    marginBottom: 4,
  },
  newCarouselContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 15,
    paddingVertical: 12,
    backgroundColor: colors.lightGreen1,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  newCarouselText: {
    color: colors.governorBay,
    fontSize: 12,
    fontFamily: 'Lato',
    lineHeight: 15,
  },
  newCarouselIcon: {
    width: 20,
    height: 20,
    marginRight: 12,
  },

  textStyle: { color: colors.lightGreen16, fontSize: 14, width: '95%' },
});
