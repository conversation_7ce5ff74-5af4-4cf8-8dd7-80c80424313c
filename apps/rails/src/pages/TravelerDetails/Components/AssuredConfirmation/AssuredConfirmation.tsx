import React, { useEffect, useRef } from 'react';
import { View, Text, Image, TouchableOpacity, ViewStyle, GestureResponderEvent } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import RadioButton from '@mmt/legacy-commons/Common/Components/Radio/RadioButton';
import { refundExplanationItem, TextStyleBE } from '../../../../types/railofy.types';
import { ASSURED_CONFIRMATION, scrollToErrorComponent } from '../../TravelerDetailsActions';
import { trackRailofyKnowMoreClick } from '../../../../Utils/railofyUtils';
import {fontStyle} from '../../../../vernacular/VernacularUtils';
import { styles, refundWays, getTextStyle } from './AssuredConfirmationUtils';
import SpendRefundToBook from './SpendRefundToBook';
import isEmpty from 'lodash/isEmpty';
import useTgThresholdHandler from './useTgThresholdHandler';
import { _label } from '../../../../vernacular/AppLanguage';
import { AssuredConfirmationProps } from '../Railofy/types';
import { openWebView } from '../../railofyUtils';
import MultiStyleVernacText from '../../../../vernacular/MultiStyleVernacText';
import { trackFCTGEvar47or97Event, trackTravellerPageEvent } from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
// =======================================================================================
import greenTick from '@mmt/legacy-assets/src/free_cancellation_tick_green.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
// =======================================================================================

export default function AssuredConfirmation(props: AssuredConfirmationProps) {
  const {
    selectedIndex,
    updateUserResponse,
    assuredConfirmationData: {
      additionalTextRevamped,
      additionalTextURL,
      availabilityText,
      backgroundColor,
      bannerTag,
      description,
      fareInfoRevamped,
      preferenceInfoRevamped,
      offerTextRevamped,
      noSelectedOptionInfo,
      refundWaysTextStyle,
      tripGuaranteeIntro,
      refundExplanation,
      refundCouponUpperCap,
      premiumUpperCap,
    },
    captureRef,
    hasError,
    numberOfPassengers,
    ancillaryDiscountDetails = null,
    tgDiscount = null,
  } = props;
  // if new fields are not present
  /* eslint-disable */
  if (isEmpty(tripGuaranteeIntro)) {
    selectedIndex === -1 && updateUserResponse(1, 0); // No Selection for old api response
    return null;
  }

  const successMessage = ancillaryDiscountDetails?.couponSuccessMessage ?? null;
  const { discountedPremium } = tgDiscount;

  const {
    totalTicketFare,
    totalRefund,
    isTripleAmount,
    premiumPerPerson,
  } = useTgThresholdHandler({
    premiumUpperCap,
    refundCouponUpperCap,
    fareInfoRevamped,
    preferenceInfoRevamped,
    numberOfPassengers,
  });

  useEffect(() => {
    if (successMessage) {
      updateUserResponse(0, discountedPremium);
      scrollToErrorComponent(ASSURED_CONFIRMATION);
      setTimeout(() => {
        // empty
      }, 2000);
    }
  }, [successMessage]);

  const compRef = useRef(null);
  const prevValueRef: React.RefObject<boolean | null> = useRef(null);

  useEffect(() => {
    captureRef(ASSURED_CONFIRMATION, compRef.current);
  }, [compRef.current]);

  const onRadioPress = (e: GestureResponderEvent) => (index: number) => {
    if (e.preventDefault) {
      e.preventDefault();
    }
    trackFCTGEvar47or97Event(
      null,
      `${RAIL_EVENTS.FCTG.TG}${RAIL_EVENTS.FCTG.TRAVELER}${
        !index ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
      }`,
      RAIL_EVENTS.FCTG.TG,
    );
    trackTravellerPageEvent(
      `${RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_OLD_TG}_${index === 0 ? 'yes' : 'no'}`,
    );
    updateUserResponse(index, preferenceInfoRevamped[index].premiumPerPerson || 0);
  };

  const onMoreDetailsPressed = () => {
    trackRailofyKnowMoreClick('mob_rail_travellers');
    openWebView(additionalTextURL);
  };

  // =========================================
  const containerDefaultBg = ['#c86dd7', '#3023ae'];
  const tagDefaultBg = ['#ff7f3f', '#ff3e5e'];

  const bannerTagTextStyle = getTextStyle(bannerTag);

  prevValueRef.current = selectedIndex !== -1 ? preferenceInfoRevamped[selectedIndex].value : null;
  // ===========================================

  const get3xTableContent = (explanationTableItem : refundExplanationItem) => {
    const explanationTitleStyle = getTextStyle(explanationTableItem.title);
    const explanationText = [
      explanationTableItem.explanation[0].text || '', explanationTableItem.explanation[1].text || '', explanationTableItem.explanation[2].text || '',
    ];
    const explanationTextStyle = [
      getTextStyle(explanationTableItem.explanation[0]),
      getTextStyle(explanationTableItem.explanation[1]),
      getTextStyle(explanationTableItem.explanation[2]),
    ];
    return [explanationTitleStyle, explanationText, explanationTextStyle];
  };

  const getTotalTicketFare = (fareCost : TextStyleBE , index : number) => {
      return (index !== 0) ? fareCost.text : `₹${totalTicketFare}`;
  };
  const getTotalRefundFare = (fareCost : TextStyleBE , index : number) => {
      return (index !== 0) ? fareCost.text : `₹${totalRefund}${!isTripleAmount() ? '*' : ''} `;
  };
  const getFareCostSubMessage = (fareCost : TextStyleBE, fareInfoIndex : number, index : number) => {
    return (fareInfoIndex === 0) ? getTotalTicketFare(fareCost,index) : getTotalRefundFare(fareCost,index);
  };
  const offerTextStyle = getTextStyle(offerTextRevamped);
  const additionTextStyle = getTextStyle(additionalTextRevamped);
  return (
    <>
    <View style={styles.container} ref={compRef}>
      {/* positioned absolute */}
      <LinearGradient
        colors={bannerTag.backgroundColor || tagDefaultBg}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.bannerTag}
      >
        <Text style={[...bannerTagTextStyle]} testID={`${props?.id}_bannerTag`}>{bannerTag.text}</Text>
      </LinearGradient>
      {/* blue/purple header */}
      <View>
        <LinearGradient
          colors={backgroundColor || containerDefaultBg}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 0.0 }}
          style={styles.gradientContainer}
          testID={`${props?.id}_banner`}
        >
        <Text style={[styles.availabilityText, fontStyle('medium')]}>{availabilityText}</Text>
        <Text style={styles.descriptionTextStyle}>
          {description && description.map((descTextObj, index) => {
            const descTextStyle = getTextStyle(descTextObj);
            return <Text
              key={index}
              style={[...descTextStyle]}
            >
              {descTextObj.text}
            </Text>;
            // don't remove the space after }
          })}
        </Text>
        <View style={styles.flexDirectionRow}>
          {refundWays.map((item,index) => {
            return <SpendRefundToBook
              icon = {item.icon}
              text = {_label(item.text)}
              dot = {item.dot}
              key = {index}
              width = {item.width}
              height = {item.height}
              refundWaysTextStyle = {[...getTextStyle(refundWaysTextStyle)]}
            />;
          })}
        </View>
      </LinearGradient>
      </View>
      <View style={styles.contentContainer}>
        {/* Fare Info rows */}
        <View style = {styles.flexDirectionRow}>
          {
            fareInfoRevamped && fareInfoRevamped.map(({ key, value }, fareInfoIndex) => {
            const showImage = fareInfoIndex === 1;
            return (
              <View style={[styles.fareInfoContainer, showImage ? 
              styles.approxRefundMargin : styles.marginRightAuto]} key={fareInfoIndex}>
                <View style={[styles.fareInfoKeyStyle, { width: '100%'}]}>
                {key.map((fareTitle, index) => {
                  const keyTextStyle = getTextStyle(fareTitle);
                    return (
                        <Text style = {keyTextStyle} key={index}>{fareTitle.text}</Text>
                    );
                })}
                </View>
                <View style = {styles.flexDirectionRow}>
                  {value.map((fareCost,index) => {
                    const valueTextStyle = getTextStyle(fareCost);
                    const fareCostSubMessage = getFareCostSubMessage(fareCost,fareInfoIndex,index);
                    return (
                      <Text style = {valueTextStyle} key={index} testID={`${props?.id}_${ !showImage ? 'ticketFare' : 'approxRefund' }`}>{fareCostSubMessage}</Text>
                    );
                  })}
                  {showImage && (
                    <Image source={greenTick} style={styles.greenTickIcon} />
                  )}
                </View>
              </View>
            );})
          }
        </View>

        {/* Premium Text */}
        {
           <View style={styles.premiumTextContainer} testID={`${props?.id}_premium`}>
             {
              discountedPremium && <MultiStyleVernacText
                content = {_label('tgPaxInfoWithCoupon')}
                contentHorizontal
                params = {{
                  amount : `₹${premiumPerPerson}`,
                  discountedPremium: `₹${discountedPremium}`,
                }}
                defaultContentStyle = {[{color: colors.lightGrey4}, fontStyle('regular')]}
                textStyles = {{
                  amount : [{color: '#303030', textDecorationLine: 'line-through', textDecorationStyle: 'solid'}, fontStyle('bold')],
                  discountedPremium : [{color: '#303030'}, fontStyle('bold')],

                }}
              />
            }
            {
              !discountedPremium && <MultiStyleVernacText
                  content = {_label('tgPaxInfo')}
                  contentHorizontal
                  params = {{amount : `₹${premiumPerPerson}`}}
                  defaultContentStyle = {[{color: colors.lightGrey4}, fontStyle('regular')]}
                  textStyles = {{
                    amount : [{color: '#303030'}, fontStyle('bold')],
                  }}
                />

            }
           </View>
        }

        {/* Radio Buttons Text */}
        <View style={styles.radioGroupContainer}>
          <View style={{ display: 'flex', flexDirection: 'row'}}>
            {preferenceInfoRevamped && preferenceInfoRevamped.map((info, index) => {
              const firstRadioStyle: ViewStyle = {
                borderRightWidth: 0,
                borderTopLeftRadius: 3,
                borderBottomLeftRadius: 3,
              };
              const secondRadioStyle: ViewStyle = {
                borderTopRightRadius: 3,
                borderBottomRightRadius: 3,
              };
              const radioStyle = [styles.radioContainer, index === 0 ? firstRadioStyle : secondRadioStyle];
              return (
                <TouchableOpacity style={radioStyle} activeOpacity={0.6} 
                onPress={(e) => onRadioPress(e)(index)} key={index}>
                  <RadioButton checked={index === selectedIndex} dimension={24} />
                  <View style={{flex: 1}} testID={`${props?.id}_${ index === 0 ? 'yes' : 'no' }Option`} key={index}>
                    {
                      info.text.map((infoText, index) => {
                        const infoTextStyle = getTextStyle(infoText);
                        return (
                          <Text 
                            key={index}
                            style={[infoTextStyle, styles.radioTextStyle, fontStyle('regular')]}
                          >
                            {infoText.text}
                          </Text>
                        );
                      })
                    }
                  </View>
                </TouchableOpacity>
              );})}
            </View>
        </View>
        {hasError && (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, fontStyle('regular')]}>
              {noSelectedOptionInfo}
            </Text>
          </View>
        )}
        {selectedIndex === 0 && preferenceInfoRevamped[0].onSelectInfo && (
          <View style={styles.successContainer}>
            <Text style={[styles.successText, fontStyle('bold')]}>
              {successMessage || preferenceInfoRevamped[selectedIndex].onSelectInfo}
            </Text>
          </View>
        )}
        {selectedIndex === 1 && preferenceInfoRevamped[1].onSelectInfo && (
          <View style={styles.notOptedContainer}>
            <Text style={[styles.notOptedText, fontStyle('bold')]}>
              {preferenceInfoRevamped[selectedIndex].onSelectInfo}
            </Text>
          </View>
        )}
        {/* What is 3x Refund Section */}
        <View testID={`${props?.id}_details`}>
            <View style = {styles.tgQuestionContainer}>
              {
                tripGuaranteeIntro && tripGuaranteeIntro.map((question,index) => {
                  const questionStyle = getTextStyle(question);
                  return (
                    <Text style={questionStyle} key={index}>{question.text}</Text>
                  );})
              }
            </View>
          {/*Table Explaining the feature*/}
          <View style={styles.tgExplanationContainer}>
            {
              refundExplanation && refundExplanation.map((explanationTableItem,index) => {
                const [explanationTitleStyle, explanationText, 
                  explanationTextStyle] = get3xTableContent(explanationTableItem);
                return (
                      <View style = {styles.tgExplanation} key={index}>
                        <View style={styles.tgExplanationHeader}>
                              <Text style={[...explanationTitleStyle, styles.marginLeftAuto,  
                                styles.marginRightAuto]}>{explanationTableItem.title?.text}</Text>
                        </View>
                        <View style={styles.tgExplanationContentContainer}>
                          {
                            <Text style = {explanationTextStyle[0]}>
                              {explanationText[0]}
                              <Text style = {explanationTextStyle[1]}>
                                {explanationText[1]}
                                <Text style = {explanationTextStyle[2]}>
                                  {explanationText[2]}
                                </Text>
                              </Text>
                            </Text>
                          }
                        </View>
                      </View>
                    );
              })
            }
          </View>
          {/*Click on Terms and Conditions*/}
            <View>
                <Text style={[...offerTextStyle,styles.alignTextCenter]}>
                  {offerTextRevamped.text}
                  <Text style={[additionTextStyle,styles.alignTextCenter]} onPress={onMoreDetailsPressed} testID={`${props?.id}_tnC`}>
                    {additionalTextRevamped.text}
                  </Text>
                </Text>
            </View>
        </View>
      </View>
    </View>
    </>
  );
}

