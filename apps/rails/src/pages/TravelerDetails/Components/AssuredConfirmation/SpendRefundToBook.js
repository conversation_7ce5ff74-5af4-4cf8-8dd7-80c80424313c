import React from 'react';
import { View, Text, Image } from 'react-native';
import {styles} from './AssuredConfirmationUtils';
import PropTypes from 'prop-types';
const getIconDimensions = (width, height) => {
    return {
        width,
        height,
    };
};
const SpendRefundToBook = (props) => {
    const {icon , text , dot, refundWaysTextStyle, index, width, height} = props;
    return (
        <View style={styles.refundWaysStyle} key={index}>
            <Image
                source = {icon}
                style ={[styles.refundWaysImageStyle,getIconDimensions(width,height) ]}
            />
            <Text style={refundWaysTextStyle}>
                {text}
            </Text>
            { dot  &&
            <Image
                source = {dot}
                style = {styles.refundWaysDotStyle}
            />
            }
        </View>
    );
};

SpendRefundToBook.propTypes = {
    icon: PropTypes.any,
    text: PropTypes.string,
    dot: PropTypes.any,
    refundWaysTextStyle: PropTypes.object,
    index: PropTypes.number,
    width: PropTypes.number,
    height: PropTypes.number,
  };


export default SpendRefundToBook;
