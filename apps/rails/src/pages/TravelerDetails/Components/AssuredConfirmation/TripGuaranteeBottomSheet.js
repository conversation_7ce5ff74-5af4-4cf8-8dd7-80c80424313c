import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, Modal } from 'react-native';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';


const TripGuaranteeBottomSheet = ({ isVisible, onClose, socialProofingBottomSheet}) => {
  return (
    <Modal
      transparent
      visible={isVisible}
      onRequestClose={onClose}
      animationType="slide"
      testID="trip_guarantee_bottomsheet_modal"
    >
      <View style={styles.background} testID="trip_guarantee_bottomsheet_background">
        <View style={styles.container} testID="trip_guarantee_bottomsheet_container">
          <LinearGradient
            colors={[colors.white, colors.white, colors.green18]}
            start={{ x: 0, y: 1 }}
            end={{ x: 0.9, y: 0.2 }}
            locations={[0, 0.5, 1]}
            style={styles.linearGradient}
            testID="trip_guarantee_bottomsheet_linear_gradient"
          >
            <View
              style={styles.headerContainer}
              testID="trip_guarantee_bottomsheet_header_container"
            >
              <Text style={styles.title} testID="trip_guarantee_bottomsheet_title">
                {socialProofingBottomSheet.title}
              </Text>
              <TouchableOpacity onPress={onClose} testID="trip_guarantee_bottomsheet_close_button">
                <Image
                  source={ASSETS.crossIcon}
                  style={styles.crossIcon}
                  testID="trip_guarantee_bottomsheet_cross_icon"
                />
            </TouchableOpacity>
          </View>
            <Text style={styles.subHeaderMain} testID="trip_guarantee_bottomsheet_sub_header_main">
              {socialProofingBottomSheet.discountPercentage}
              <Text style={styles.subHeader} testID="trip_guarantee_bottomsheet_sub_header">
                {socialProofingBottomSheet.subtitleIcon}
                {socialProofingBottomSheet.subtitle}
              </Text>
            </Text>
            <View style={styles.textContainer} testID="trip_guarantee_bottomsheet_text_container">
              <Text style={styles.content} testID="trip_guarantee_bottomsheet_content">
                <Text>{socialProofingBottomSheet.mainText} </Text>
              </Text>
            <Text
              style={{ lineHeight: 16.8 }}
            >{socialProofingBottomSheet.subText}</Text>
          </View>
            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
              testID="trip_guarantee_bottomsheet_close_button"
            >
              <Text
                style={styles.closeButtonText}
                testID="trip_guarantee_bottomsheet_close_button_text"
              >
                {socialProofingBottomSheet.ctaText}
              </Text>
          </TouchableOpacity>
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );
};

TripGuaranteeBottomSheet.propTypes = {
  isVisible: PropTypes.bool,
  onClose: PropTypes.func,
  socialProofingBottomSheet: PropTypes.shape({
    title: PropTypes.string,
    discountPercentage: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    subtitleIcon: PropTypes.string,
    subtitle: PropTypes.string,
    mainText: PropTypes.string,
    subText: PropTypes.string,
    ctaText: PropTypes.string,
  }),
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    backgroundColor: colors.backgroundColor,
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 32,
  },
  headerContainer: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'justify-between',
    marginHorizontal: 20,
    marginTop: 16,
  },
  linearGradient: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  subHeaderMain: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 28.8,
    marginBottom: 10,
    marginLeft: 20,
  },
  textContainer: {
    marginTop: 16,
    marginHorizontal: 20,
    alignItems: 'justify-between',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 10,
    color: colors.black,
    lineHeight: 28.8,
  },
  subHeader: {
    fontSize: 14,
    color: colors.lightGreen16,
    marginTop: 4,
    justifyContent: 'center',
    marginLeft: 5,
  },
  content: {
    fontSize: 18,
    marginBottom: 20,
    fontWeight: '400',
    lineHeight: 21.6,
    color: colors.textGrey,
  },
  closeButton: {
    backgroundColor: colors.lightBlue26,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 14,
    marginTop: 20,
  },
  closeButtonText: {
    paddingHorizontal: 24,
    paddingVertical: 12.5,
    color: colors.white,
    fontWeight: 'bold',
  },
  crossIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
    top: -4,
  },
});

export default TripGuaranteeBottomSheet;
