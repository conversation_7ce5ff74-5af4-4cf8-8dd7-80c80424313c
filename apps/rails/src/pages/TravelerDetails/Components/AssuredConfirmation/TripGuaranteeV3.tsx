import React, { useEffect, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { PRICE_REPLACER, TEST_ID_CONSTANTS } from 'apps/rails/src/Utils/RailsConstant';
import AddButtonUpgraded from '../TripGuaranteePlus/Components/AddButtonUpgraded';
import { trackFCTGEvar47or97Event } from 'apps/rails/src/railsAnalytics';
import LinearGradient from 'react-native-linear-gradient';
import { trackRailofyKnowMoreClick } from '../../../../Utils/railofyUtils';
import { fontStyle, getLineHeight } from '../../../../vernacular/VernacularUtils';
import { RAILOFY_TYPE, openWebView } from '../../railofyUtils';
import { AssuredConfirmationProps } from '../Railofy/types';
import { styles } from './AssuredConfirmationUtils';
import useTgThresholdHandler from './useTgThresholdHandler';
import { FCTGTextConfig } from 'apps/rails/src/pages/TravelerDetails/Components/FreeCancellation/types';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { TRAVELERS_PAGE_TRACKING_KEY_NEW } from '../../../../Utils/RailsConstant';
import useTgPlusThresholdHandler from './useTgPlusThresholdHandler';

import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import greenTick from '@mmt/legacy-assets/src/free_cancellation_tick_green.webp';
import dot from '@mmt/legacy-assets/src/ic_dot_hollow_gray_copy.webp';
import crossIcon from '@mmt/legacy-assets/src/cross_tg.webp';
const containerDefaultBg = ['#c86dd7', '#3023ae'];
const testID = `${TEST_ID_CONSTANTS.TRAVELLER_DETAILS}${'tg'}_addButton`;

const TripGuaranteeV3 = (props: AssuredConfirmationProps) => {
  const {
    assuredConfirmationData: {
      additionalTextURL,
      backgroundColor,
      fareInfoRevamped,
      preferenceInfoRevamped,
      refundCouponUpperCap,
      premiumUpperCap,
    },
    numberOfPassengers,
    isTGSelected,
    updateFCTGUserResponse,
    displayTgBottomSheet,
    tgTextConfig,
    tgpTextConfig,
    tgPlusData,
    isTGPlusSelected,
    isFCSelected,
    tgVersion,
  } = props;

  const fcTgTextConfig: FCTGTextConfig = useConfigStore(configKeys.RAILS_TG_FC_TEXT_CONFIG);

  let tgConfig = tgTextConfig;
  const tgpConfig = tgpTextConfig;

  const [isUpgradeClicked, setIsUpgradeClicked] = useState(false);

  useEffect(() => {
    trackFCTGEvar47or97Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, `${RAIL_EVENTS.FCTG.TGP}4`);
  }, []);
  useEffect(() => {
    if (isFCSelected) {
      setIsUpgradeClicked(false);
    }
  }, [isFCSelected]);

  if (!tgConfig?.header) {
    tgConfig = fcTgTextConfig.TG;
  }

  const tgPlusConfig: FCTGTextConfig = useConfigStore(configKeys.RAILS_TG_PLUS_CONFIG);
  const { tgUpgradeVersion7 = {} } = tgPlusConfig;
  const { tgRacCoverageTitle = '', tgRacCoverageSubtitle = '' } = tgUpgradeVersion7;

  const onCheckboxSelected = () => {
    let index = !isTGSelected ? 0 : 1;
    if (isTGPlusSelected && !isTGSelected) {
      index = 1;
    }
    if (isTGSelected || index === 1) {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.RAILS_TGP4_TG_REMOVED,
      );
    } else {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.RAILS_TGP4_TG_ADDED,
      );
    }
    updateFCTGUserResponse(
      index,
      preferenceInfoRevamped[index].premiumPerPerson || 0,
      RAILOFY_TYPE.TG,
      tgpConfig?.toasts[1],
    );
  };

  const handleUserSelection = () => {
    const railofyType = RAILOFY_TYPE.TGPV3;
    const toast = '';
    const index = isTGPlusSelected ? 1 : 0;
    const premiumAmount = index === 0 ? premiumPerPerson : premiumPerPersonForTg;
    if (isTGPlusSelected) {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.RAILS_TGP4_TGP_REMOVED,
      );
    } else {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.RAILS_TGP4_TGP_ADDED,
      );
    }
    updateFCTGUserResponse(index, premiumAmount, railofyType, toast);
  };


  const ticketFareText = fareInfoRevamped && fareInfoRevamped[0].value[0].text;

  const onMoreDetailsPressed = () => {
    trackRailofyKnowMoreClick('mob_rail_travellers');
    openWebView(additionalTextURL);
  };

  const { totalRefund: totalRefundForTg, premiumPerPerson: premiumPerPersonForTg } = useTgThresholdHandler({
    premiumUpperCap,
    refundCouponUpperCap,
    fareInfoRevamped,
    preferenceInfoRevamped,
    numberOfPassengers,
  });

  const { totalRefund, premiumPerPerson, totalTicketFare } = useTgPlusThresholdHandler({
    ...tgPlusData,
    numberOfPassengers,
  });

  Object.assign(tgPlusData, {
    totalRefund,
    premiumPerPerson,
    totalTicketFare,
  });

  return (
    <View style={[styles.fcTgContainer, displayTgBottomSheet && styles.bottomSheetContent]}>
      <LinearGradient
        colors={backgroundColor || containerDefaultBg}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.gradientContainer}
        testID={`${props?.id}_banner`}
      >
        <Text style={[styles.header, fontStyle('bold')]}>{tgConfig?.header}</Text>
        <Text style={[styles.description, fontStyle('bold')]}>{tgConfig?.subHeader}</Text>
      </LinearGradient>
      <View style={[styles.contentDataContainer, styles.flex0]}>
        <View style={styles.refundsContainer}>
          {tgConfig?.refunds.map((refund, index) => {
            return (
              <View key={index} style={styles.refundContainer}>
                <LinearGradient
                  colors={[colors.grey5, colors.grey7, colors.lightGrey]}
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 0.0 }}
                  style={styles.verticalGradient}
                >
                  <View style={styles.refundLabelContainer}>
                    <Text style={[styles.refundLabel, fontStyle('regular')]}>{refund.label}</Text>
                  </View>
                </LinearGradient>
                <View style={styles.fareContainer}>
                  <Text style={[styles.fareTitle, fontStyle('bold')]}>
                    {refund.title}
                    {index === 0 && (
                      <Text style={[styles.fare, fontStyle('medium')]}>{ticketFareText}</Text>
                    )}
                  </Text>
                  <Text style={[styles.fareDescription, fontStyle('regular')]}>
                    {refund.description}
                  </Text>
                  <Text style={[styles.refundMode, fontStyle('regular')]}>{refund.refundMode}</Text>
                </View>
              </View>
            );
          })}
        </View>
        <View style={styles.refundRow}>
          <Text style={[styles.approxRefund, fontStyle('black')]}>
            {tgConfig?.approxRefund.replace('Approx refund =', 'Refund =')}
            <Text style={[styles.totalRefund, fontStyle('black')]}>{`₹${totalRefundForTg}`}</Text>
          </Text>
          <Image source={greenTick} style={styles.darkGreenTickIcon} />
        </View>
        <View style={styles.marginTop}>
          {tgConfig?.usages.map((usage, index) => {
            return (
              <View key = {index} style={styles.usageContainer}>
                {index === 1 ? (
                  !isUpgradeClicked && <Image source={dot} style={styles.dotIcon} />
                ) : (
                  <Image source={dot} style={styles.dotIcon} />
                )}
                <Text style={[styles.usageText, fontStyle('regular')]}>
                  {index === 1 ? !isUpgradeClicked && usage : usage}
                  {index === 1 && !isUpgradeClicked && (
                    <Text
                      onPress={onMoreDetailsPressed}
                      style={[styles.tncText, fontStyle('regular'), getLineHeight(14)]}
                    >
                      {tgConfig?.tncCTA}
                    </Text>
                  )}
                </Text>
              </View>
            );
          })}
        </View>

        {!displayTgBottomSheet && (
          <AddButtonUpgraded
            label={tgConfig?.checkBoxCTA.replace(PRICE_REPLACER, premiumPerPersonForTg.toString())}
            labelStyle={[styles.checkBoxText, fontStyle('medium')]}
            testID={testID}
            isOptionSelected={isTGSelected || isTGPlusSelected}
            onSelect={() => {
              onCheckboxSelected();
              if (isUpgradeClicked) {
                setIsUpgradeClicked((prevState) => !prevState);
              }
            }}
          />
        )}
        <View>
          {!displayTgBottomSheet && (isTGSelected || isTGPlusSelected) && (
            <View
              style={
                tgVersion === 7 && isUpgradeClicked
                  ? styles.tgMaxVersion7
                  : tgVersion === 7 && !isUpgradeClicked
                  ? styles.tgMaxVersion7Upragde
                  : styles.tgMax
              }
            >
              <View style={styles.upgradeText}>
                {!isUpgradeClicked ? (
                  tgVersion === 7 ? (
                    <Text style={[styles.racCoverageV7, getLineHeight(13), fontStyle('regular')]}>
                      {_label('upgrade_rac')}
                      {` ₹${premiumPerPerson - premiumPerPersonForTg}/person`}
                    </Text>
                  ) : (
                    <Text style={[styles.racCoverage, getLineHeight(13), fontStyle('regular')]}>
                      {_label('rails_tg_added_tag_value')}
                    </Text>
                  )
                ) : tgVersion === 7 ? (
                  <View>
                    <View>
                      <Text style={[styles.racCoverageBold, getLineHeight(14)]}>
                        {`${tgRacCoverageTitle} ₹${
                          premiumPerPerson - premiumPerPersonForTg
                        }/person`}
                      </Text>
                    </View>
                    <View>
                      <Text style={[styles.racCoverage, getLineHeight(14)]}>
                        {tgRacCoverageSubtitle}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <Text>
                    {`${_label('rails_tg_removed_tag_value')} ₹${
                      premiumPerPerson - premiumPerPersonForTg
                    } extra per person`}
                  </Text>
                )}
              </View>
              <TouchableOpacity
                style={
                  tgVersion === 7 && !isUpgradeClicked
                    ? styles.textButton
                    : styles.marginHorizontal10
                }
                onPress={() => {
                  setIsUpgradeClicked((prevState) => !prevState);
                  handleUserSelection(); // Call handleUserSelection in both cases
                }}
              >
                {isUpgradeClicked ? (
                  <View>
                    <Image
                      source={crossIcon}
                      style={
                        tgVersion === 7 && !isUpgradeClicked
                          ? styles.crossIconSmall
                          : styles.crossIconLarge
                      }
                      resizeMode="contain"
                    />
                  </View>
                ) : (
                  <Text style={styles.buttonLabel}>Upgrade</Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};
export default React.memo(TripGuaranteeV3);
