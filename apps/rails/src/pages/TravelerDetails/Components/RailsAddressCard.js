

import React from 'react';
import {Text, View} from 'react-native';
import PropTypes from 'prop-types';

import { getPokusConfigWaitingPromise } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';

import travelerStyle,{travelerTextStyle} from '../TravelerDetailsCSS';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import SimpleTextInput from './FormInputComponents/SimpleTextInput';
import RailsDropDown from './FormInputComponents/DropDown';
import _trim from 'lodash/trim';
import _isEmpty from 'lodash/isEmpty';
import railsConfig from '../../../RailsConfig';
import fetch2 from '../../../fetch2';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {COMPONENT_DEST_ADDRESSLINE1,COMPONENT_DEST_PINCODE,COMPONENT_DEST_POSTOFFICE} from '../TravelerDetailsActions';
import {connect} from 'react-redux';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight, isOnlyEnglishCharacters} from '../../../vernacular/VernacularUtils';

class RailsAddressCard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      addressLine1: props.address1,
      addressLine2: props.address2,
      addressLine3: props.address3,
      pincode: props.pincode,
      state: props.state,
      city: props.city,
      temp_pincode: props.pincode,
      postoffice: props.postOffice,
      stateList: [],
      cityList: [],
      postOfficeList: props.postOfficeList,
    };
  }

  async componentDidMount() {
    await getPokusConfigWaitingPromise(1000);
  }
  _onAddress1Change = (val) => {
    this.setState({addressLine1:val});
    this.props.address1Change(val);
  };
  _onAddress1Blur = () => {
    this.addressLine2ref && this.addressLine2ref.focus();
  };

  _onAddress2Change = (val) => {
    // this.setState({addressLine2:val})
    this.props.address2Change(val);
  };
  _onAddress2Blur = () => {
    this.addressLine3ref && this.addressLine3ref.focus();
  };

  _onAddress3Change = (val) => {
    // this.setState({addressLine3:val})
    this.props.address3Change(val);
  };
  _onAddress3Blur = () => {
    this.pincodeRef && this.pincodeRef.focus();
  };

  _onPincodeChange = (val) => {
    this.setState({temp_pincode: val}, this.handlePincodeValidation);
    this.props.pincodeChange(val);
  };

  _onPincodeBlur = () => {
    // if (!this.pincodeRef.isFocused()) {
    //   // Keyboard.dismiss();
    // }
    this.handlePincodeValidation();
  };

  handlePincodeValidation = () => {
    this.props.stateChange(null);
    this.props.cityChange(null);
    this.props.postOfficeChange(null);
    this.props.postOfficeListChange([]);
    if (this.validatePinCode(this.props.pincode).valid) {
      this.onPinCodeBlur(this.props.pincode);
    }
  };

  isValidPincode = (pincode) => {
    const pincodeRegex = /^\d{6}$/;
    return pincode.match(pincodeRegex);
  };

  getAddressError = (text, isMandatoryField) => {
    if (!isOnlyEnglishCharacters(text)) {
        return _label('type_in_english');
    }
    if (!isMandatoryField) {
      return '';
    } else if (_isEmpty(text) || text.length < 3) {
      return _label('minimum_three_characters');
    } else {
      return '';
    }
  };

  validatePinCode(pincode) {
    if (!isOnlyEnglishCharacters(pincode)) {
        return {
          error: _label('type_in_english'),
          valid : false,
        };
    }
    if (!_trim(pincode)) {
      return {
        error: _label('empty_pincode'),
        valid: false,
      };
    }
    if (_trim(pincode).length < 6 || !this.isValidPincode(_trim(pincode))) {
      return {
        error: _label('invalid_pincode'),
        valid: false,
      };
    }
    return {
      error: '',
      valid: true,
    };
  }

  onPinCodeBlur = async (text) => {
    let response;
    try {
      const requestBody = {pinCode: text};
      const res = await fetch2(railsConfig.getAddressFromPinCode, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      response = await res.json();
    } catch (e) {
      console.log('error in onPincodeBlur', e);
      showShortToast(_label('something_went_wrong_try_again') + ' Error Code: ' + e?.errorCode);
      return;
    }

    if (!_isEmpty(response.errorMessage)) {
      showShortToast(_label('enter_valid_pincode'));
    } else {
      const {city, state, postOfficeList: newPostOfficeList} = response;
      const newCityList = [city];
      const newStateList = [state];
      const cityList = newCityList.map((item, index) => ({id: index.toString(), text: item}));
      const stateList = newStateList.map((item, index) => ({id: index.toString(), text: item}));
      const postOfficeList = newPostOfficeList.map((item, index) => ({
        id: index.toString(),
        text: item,
      }));
      this.props.stateChange(stateList[0].text);
      this.props.cityChange(cityList[0].text);
      this._onAddress1Change(cityList[0].text);
      // this.props.postOfficeChange(option.text)
      this.setState({
        cityList,
        stateList,
      });
      this.props.postOfficeListChange(postOfficeList);
      if (postOfficeList.length > 0){
        this.props.postOfficeChange(postOfficeList[0].text);
      }
      // return ({error:false, result:{cityList, stateList, postOfficeList}});
      // pinCodeData = {
      //   pinCode: {text},
      //   city: cityList[0],
      //   postOffice: postOfficeList[0],
      //   state: stateList[0],
      //   isOffice: true
      // }
    }
  };

  _renderAddressForm = () =>{
    return (
      <View style={styles.formField}>
        <SimpleTextInput
          label={_label('pincode')}
          subLabel={`(${_label('mandatory')})`}
          placeholder={_label('enter_pincode')}
          labelStyle={{...fontStyle('bold')}}
          placeholderTextColor={colors.disabledButton}
          autoCapitalize="none"
          keyboardType="phone-pad"
          returnKeyType="done"
          onChangeText={this._onPincodeChange}
          value={this.props.pincode}
          maxLength={10}
          inputStyle={{
            borderColor: colors.lightGrey,
          }}
          onSubmitEditing={this._onPincodeBlur}
          errorIcon
          error={null}
          // errorContainerStyle={{marginBottom: 0, marginTop: 10}}
          refProp={(ref) => {
            this.pincodeRef = ref;
            this.props.captureRef(COMPONENT_DEST_PINCODE,ref);
          }}
          id={`${this.props?.id}_pincode`}
        />

        { !(_isEmpty(this.props.state)  || _isEmpty(this.props.city) || _isEmpty(this.props.postOffice))
        &&
        <View>
          <SimpleTextInput
            inputStyle={{
              borderColor: colors.lightGrey,
            }}
            label={_label('address_line_num', undefined, { num: 1 })}
            subLabel={`(${_label('mandatory')})`}
            placeholder=""
            placeholderTextColor={colors.disabledButton}
            autoCapitalize="none"
            autoCompleteType="street-address"
            returnKeyType="next"
            value={this.props.address1}
            onChangeText={this._onAddress1Change}
            onSubmitEditing={this._onAddress1Blur}
            errorIcon
            error={this.getAddressError(this.props.address1, true)}
            refProp={(ref) => {
              this.addressLine1ref = ref;
              this.props.captureRef(COMPONENT_DEST_ADDRESSLINE1, ref);
            }}
            id={`${this.props?.id}_addressLine1`}
          />
         <View style={{marginBottom: 20}} >
          <Text style={{
            opacity: this.props.state ? 1 : 0.3,
            color: colors.black,
            fontSize: 14,
            ...fontStyle('bold'),
            ...getLineHeight(14),
            marginBottom: 10,
            marginTop: 0,
          }}
          >
            {_label('city')}
          </Text>
          <RailsDropDown
            disabled={this.props.postOfficeList.length === 0}
            fromAddressCard
            label={this.props.postOfficeList.length === 0 ? _label('enter_pincode') : this.props.city}
            selected={this.props.postOfficeList.length === 0 ? null : this.props.city}
            options={[{id: '0', text: this.props.city}]}
            onSelect={(option) => {
              this.props.cityChange(option.text);
            }}
            headerInfo="currentObject.headerInfo"
            header={_label('city')}
            error={false}
            errorMessage={{
              emptyErrorMessage: '',
            }}
            changeLabel=""
            hideDropDownIcon
              id={`${this.props?.id}_city`}
          />
        </View>
        <View
          style={{marginBottom: 20}}
        >
          <Text style={{
            opacity: this.props.state ? 1 : 0.3,
            color: colors.black,
            fontSize: 14,
            ...fontStyle('bold'),
            ...getLineHeight(14),
            marginBottom: 10,
            marginTop: 0,
          }}
          >
            {_label('state')}
          </Text>
          <RailsDropDown
            disabled={this.props.postOfficeList.length === 0}
            fromAddressCard
            label={this.props.postOfficeList.length === 0 ? _label('enter_pincode') : this.props.state}
            selected={this.props.postOfficeList.length === 0 ? null : this.props.state}
            options={[{id: '0', text: this.props.state}]}
            onSelect={(option) => {
              this.props.stateChange(option.text);
            }}
            headerInfo="currentObject.headerInfo"
            header={_label('state')}
            error={false}
            errorMessage={{
              emptyErrorMessage: '',
            }}
            changeLabel=""
            hideDropDownIcon
              id={`${this.props?.id}_state`}
          />
        </View>
        <View
          style={{marginBottom: 20, borderWidth: 0.1, borderColor: colors.white}}
          ref={(ref) => {
            this.props.captureRef(COMPONENT_DEST_POSTOFFICE, ref);
          }}
        >
          <Text style={
            {
              opacity: this.props.postOfficeList.length > 0 ? 1 : 0.3,
              color: colors.black,
              fontSize: 14,
              ...fontStyle('bold'),
              ...getLineHeight(14),
              marginBottom: 10,
              marginTop: 0,
            }
          }>
            {_label('post_office')}
          </Text>
          <RailsDropDown
            disabled={this.props.postOfficeList.length === 0}
            fromAddressCard
            label={this.props.postOfficeList.length === 0 ? _label('enter_pincode') : this.props.postOfficeList[0].text}
            selected={this.props.postOfficeList.length === 0 ? null : this.props.postOffice}
            options={this.props.postOfficeList}
            onSelect={(option) => {
              // this.setState({postoffice:option.text});
              this.props.postOfficeChange(option.text);
            }}
            headerInfo="currentObject.headerInfo"
            header={_label('post_office')}
            error={false}
            errorMessage={{
              emptyErrorMessage: '',
            }}
            changeLabel=""
              id={`${this.props?.id}_postOffice`}
          />
        </View>
        </View>}

      </View>
    );
    };


  render() {
    return (
      <View style={styles.container} testID={this.props?.id}>
        <View style={travelerStyle.contactCard}>
          <Text style={[travelerStyle.travelHeader, travelerTextStyle.getTravelHeaderFontStyle(), getLineHeight(22)]}>
          {_label('address_details')}
          </Text>
          <Text style={[styles.contactDetailsSubheader, fontStyle('bold'), getLineHeight(14)]}>
          {_label('address_details_destination')}
          </Text>
        </View>
        <View style={styles.formContainer}>
          {this._renderAddressForm()}
        </View>
      </View>
    );
  }
}

const styles = {
  container: {
    backgroundColor: colors.white,
  },
  contactDetailsSubheader: {
    fontSize: 14,
    color: colors.black,
    lineHeight: 20,
    letterSpacing: 0,
  },
  formField: {
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  formContainer: {
    paddingHorizontal: 12,
    paddingTop: 20,
    paddingBottom: 16,
  },
};

RailsAddressCard.propTypes = {
  captureRef: PropTypes.func,
  labels: PropTypes.object,
  contactDetails: PropTypes.object,
  address1: PropTypes.string,
  address2: PropTypes.string,
  address3: PropTypes.string,
  pincode: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  state: PropTypes.string,
  city: PropTypes.string,
  postOffice: PropTypes.string,
  postOfficeList: PropTypes.array,
  address1Change: PropTypes.func,
  address2Change: PropTypes.func,
  address3Change: PropTypes.func,
  pincodeChange: PropTypes.func,
  stateChange: PropTypes.func,
  cityChange: PropTypes.func,
  postOfficeChange: PropTypes.func,
  postOfficeListChange: PropTypes.func,
  id: PropTypes.string,
  texts: PropTypes.object,
};

RailsAddressCard.navigationOptions = {
  captureRef: null,
  contactDetails: {},
};

const mapStateToProps = (state)=>{
  const {railsVernacular:{texts}} = state;
  return {
    texts,
  };
};
export default connect(mapStateToProps, null)(RailsAddressCard);
