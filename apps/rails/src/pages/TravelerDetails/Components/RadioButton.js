import React from 'react';
import {Image} from 'react-native';
import PropTypes from 'prop-types';
import radioChecked from '@mmt/legacy-assets/src/ic-radiobutton-active.webp';
import radioUnchecked from '@mmt/legacy-assets/src/ic-radiobutton-inactive.webp';

const RadioButton = ({checked}) => (
  <Image
    style={{
      width: 24,
      height: 24,
    }}
    source={checked ? radioChecked : radioUnchecked}
  />
);

RadioButton.propTypes = {
  checked: PropTypes.bool.isRequired,
};
export default RadioButton;
