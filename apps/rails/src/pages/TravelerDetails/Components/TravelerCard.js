
import React from 'react';
import {PropTypes} from 'prop-types';
import {Text, View} from 'react-native';
import travelerStyle from '../TravelerDetailsCSS';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import Berth, {BerthText} from '../../Constants/Berth';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

function getAgeGenderString(traveler) {
  const {fullName, isAgeSpecified, isGenderSpecified, basicInfo} = traveler;
  let genderText = isGenderSpecified ? basicInfo.gender.displayText.slice(0, 1) : '';
  if (genderText?.toLowerCase() === 't') {
    genderText = 'o';
  }
  const ageText = isAgeSpecified ? basicInfo.age : '';
  return `${fullName}, ${ageText} (${genderText})`;
}

function getBerthMealConcatednatedText(traveler, railsListingUtilObj, noBerthString) {
  const {basicInfo, isFoodChoiceEnabled} = traveler;
  let berthString = BerthText[basicInfo.berth.key];
  if (!basicInfo.berth.key) {
    berthString = noBerthString || Berth.NO_BERTH.value;
  }
  if (railsListingUtilObj.isInfant(basicInfo.age) && !traveler.basicInfo.childBerthFlag) {
    berthString = 'child_seat';
  }
  if (railsListingUtilObj.isChild(basicInfo.age) && !traveler.basicInfo.childBerthFlag) {
    berthString = '';
  }
  const mealString = isFoodChoiceEnabled && !railsListingUtilObj.isInfant(basicInfo.age) ? basicInfo.meal.value : '';
  const separator = berthString && mealString ? ', ' : '';
  return `${_label(berthString)}${separator}${_label(mealString)}`;
}

function getSrCitizenConcessionText(traveler,displayText) {
  const {basicInfo, isSrConcessionApplicable} = traveler;
  if (!isSrConcessionApplicable) {
    return _label('senior_concession_unavailable');
  }
  else {
    if (basicInfo.seniorCitizenConcession.key){
      return displayText;
    }
    else {
      return basicInfo.seniorCitizenConcession.displayText;
    }
  }
}

function getChildBerthSelectionText(traveler) {
  const childBerthSelectionText = traveler.basicInfo.childBerthFlag ? _label('berth_reserved') : _label('no_berth_reserved');
  return childBerthSelectionText;
}

const TravelerCard = (props) => {
  props = {
    ...props,
    compactSize: typeof props.compactSize === 'undefined' ? true : props.compactSize,
    containerStyle: typeof props.containerStyle === 'undefined' ? null : props.containerStyle,
  };

  const { traveler, railsListingUtilObj, containerStyle } = props;
  const berthPref = getBerthMealConcatednatedText(traveler, railsListingUtilObj,'no_berth_preference');
  return (
    <View
      style={[travelerCardStyles.cardContainers, { paddingVertical: 0 }, containerStyle]}
      testID={props?.id}
    >
      <Text style={[travelerStyle.latoBoldText16, fontStyle('bold'), getLineHeight(16)]}>
        {getAgeGenderString(traveler)}
      </Text>
      {(railsListingUtilObj.isChild(traveler.basicInfo.age) || railsListingUtilObj.isInfant(traveler.basicInfo.age)) &&
          <View>
          <Text style={[travelerCardStyles.travelerDetailsText, fontStyle('regular'), getLineHeight(14)]}>{getChildBerthSelectionText(traveler)}</Text>
        </View>
      }
      {Boolean(berthPref) &&
        <Text style={[travelerCardStyles.travelerDetailsText, fontStyle('regular'), getLineHeight(14)]}>
          {berthPref}
        </Text>
      }
      {traveler.showSrConcessionApplicable &&
        <View>
          <Text style={[travelerCardStyles.travelerDetailsText, fontStyle('regular'), getLineHeight(14)]}>
            {getSrCitizenConcessionText(traveler, _label('availing_sr_citizen_concession'))}
          </Text>
        </View>
      }
    </View>
  );
};

const travelerCardStyles = {
  cardContainers: {
    flex: 1,
    flexDirection: 'column',
  },
  travelerDetailsText: {
    color: colors.lightTextColor,
    fontSize: 14,
    letterSpacing: -0.01,
  },
};

TravelerCard.propTypes = {
  traveler: PropTypes.object.isRequired,
  compactSize: PropTypes.bool,
  railsListingUtilObj: PropTypes.object.isRequired,
  id: PropTypes.string,
  containerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  // page: PropTypes.string,
  // childBerthMandatory: PropTypes.bool.isRequired
};

export default TravelerCard;
