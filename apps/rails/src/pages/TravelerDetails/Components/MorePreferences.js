
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import { Text, View, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import travelerStyle,{travelerTextStyle} from '../TravelerDetailsCSS';
import ReservationChoiceContainer from '../Containers/ReservationChoiceContainer';
import Accordion from '@mmt/legacy-commons/Common/Components/Accordion';
import TravelInsuranceContainer from '../Containers/TravelInsuranceContainer';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import {
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  OTHER_OPTIONS,
} from '../../../../src/Utils/RailsConstant';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import RAIL_EVENTS from '../../../../src/RailsOmnitureTracker';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { showNewAdditionalPreferenceVariants } from '@mmt/rails/src/RailsAbConfig';
import {
  removeEventFromEvar47or97Variable,
  updateEvar47or97Variable,
} from '@mmt/rails/src/railsAnalytics';


const showNewAdditionalPreference = showNewAdditionalPreferenceVariants();

export default class MorePreferences extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visibleComponents: 1,
    };
  }

  setVisibleComponents = (count) => {
    this.setState({ visibleComponents: count });
  };

  onViewMorePress = () => {
    this.setVisibleComponents(2);
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDITONAL_PREF_VIEW_MORE,
    );
  };
  onViewAllPress = () => {
    this.setVisibleComponents(3);
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDITONAL_PREF_VIEW_ALL,
    );
  };

  onPressCheckBox = () => {
    const onConfirmBookCheckboxPress = !this.props.confirmBookCheckbox;
    this.props.onConfirmBookCheckboxClicked();
    if (onConfirmBookCheckboxPress) {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDPREF_CONFIRMED_BERTHS,
      );
    }
  };

  componentDidMount() {
    const eventType = showNewAdditionalPreference === 1
    ? RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADDITIONAL_PREFERENCE_NEW
    : RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADDITIONAL_PREFERENCE_EXISTING;

    removeEventFromEvar47or97Variable(
      RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADDITIONAL_PREFERENCE_EXISTING,
    );
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADDITIONAL_PREFERENCE_NEW);

    updateEvar47or97Variable(eventType);
  }

  renderAccordionHeader = () => {
    const travelHeaderStyle = [
      travelerStyle.travelHeader,
      travelerTextStyle.getTravelHeaderFontStyle(),
      getLineHeight(22),
    ];
    const additionalSubheadingStyle1 = [
      styles.additionalSubheading1,
      fontStyle('regular'),
      getLineHeight(14),
    ];
    const additionalSubheadingStyle2 = [
      styles.additionalSubheading2,
      fontStyle('regular'),
      getLineHeight(16),
    ];


    return (
      <View>
        {showNewAdditionalPreference === 0 && (
          <View style={styles.headerContainer1}>
            <Text style={travelHeaderStyle}>{_label('additional_preferences')}</Text>
            <Text style={additionalSubheadingStyle1}>
              {_label('assured_lower_berth_same_coach')}
            </Text>
          </View>
        )}
        {showNewAdditionalPreference === 1 && (
          <View style={styles.headerContainer2}>
            <Text style={travelHeaderStyle}>{_label('additional_preferences')}</Text>
            <Text style={additionalSubheadingStyle2}>({_label('optional')})</Text>
          </View>
        )}
      </View>
    );
  };
  renderOldAccordionContent = () => {
    const { props } = this;
    return (
      <View>
        <ReservationChoiceContainer
          travelerCount={this.props.travelerCount}
          id={this.props?.id}
        />
        <Text
          style={[
            { fontSize: 14, lineHeight: 20, color: colors.black, marginBottom: 25 },
            fontStyle('bold'),
            getLineHeight(14),
          ]}
        >
          {OTHER_OPTIONS}
        </Text>
        <View testID={`${this.props?.id}_otherOptions_1`} >
          <CheckBox
            textLine1={_label('book_confirmed_berths')}
            isChecked={props.confirmBookCheckbox}
            onPress={() => {
              const newState = !props.confirmBookCheckbox;
              props.onConfirmBookCheckboxClicked();
              if (newState) {
                this.props?.logTravellerGenericClickEvents(
                  TRAVELLER_CLICK_EVENTS.BOOK_CONFIRMED_BERTHS,
                );
                trackClickEventProp61(
                  TRAVELERS_PAGE_TRACKING_KEY_NEW,
                  RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDPREF_CONFIRMED_BERTHS,
                );
              }
            }}
            customStyles={styles.checkBoxContainer}
            customLine1Style={[styles.checkboxLabel, fontStyle('regular'), getLineHeight(14)]}
          />
          {props.confirmBookCheckbox &&
            <View style={styles.warningContainer1}>
              <Text style={[styles.warningText, fontStyle('regular'), getLineHeight(12)]}>
                {_label('book_confirmed_berth_warning')}
              </Text>
            </View>
          }
        </View>

        <View style={styles.checkboxContentContainer} testID={`${this.props?.id}_otherOptions_2`}>
          <CheckBox
            textLine1={_label('consider_auto_upgradation')}
            isChecked={props.considerAutoUpgradation}
            onPress={() => {
              const newState = !props.considerAutoUpgradation;
              props.onConsiderAutoUpgradationClicked();
              if (newState) {
                this.props?.logTravellerGenericClickEvents(
                  TRAVELLER_CLICK_EVENTS.CONSIDER_AUTO_UPGRADATION,
                );
              }
            }}
            customStyles={styles.checkBoxContainer}
            customLine1Style={[styles.checkboxLabel, fontStyle('regular'), getLineHeight(14)]}
          />
        </View>
        <TravelInsuranceContainer id={`${this.props?.id}_travelInsurance`} />
      </View>
    );
  };

  renderNewAccordionContent = () => {
    const { visibleComponents } = this.state;
    const {
      id,
      travelerCount,
      confirmBookCheckbox,
      considerAutoUpgradation,
      onConsiderAutoUpgradationClicked,
    } = this.props;
    const travelInsuranceId = this.props ? `${this.props.id}_travelInsurance` : null;

    const renderSwitch = (state) => {
      switch (state) {
        case 1:
          return (
            <Text
              style={[styles.viewButton, fontStyle('bold'), getLineHeight(12)]}
              onPress={this.onViewMorePress}
            >
              {_label('view_more')}
            </Text>
          );
        case 2:
          return (
            <>
              <View style={styles.optionBox}>
                <ReservationChoiceContainer
                  travelerCount={travelerCount}
                  id={id}
                />
              </View>
              <Text
                style={[styles.viewButton, fontStyle('bold'), getLineHeight(12)]}
                onPress={this.onViewAllPress}
              >
                {_label('view_all')}
              </Text>
            </>
          );
        case 3:
          return (
            <>
              <View style={styles.optionBox}>
                <ReservationChoiceContainer
                  travelerCount={travelerCount}
                  id={id}
                />
              </View>
              <View style={styles.optionBox}>
                <View style={styles.rowContainer}>
                  <View style={styles.optionContainer} testID={`${id}_otherOptions_1`}>
                    <CheckBox
                      textLine1={_label('book_confirmed_berths')}
                      isChecked={confirmBookCheckbox}
                      onPress={() => {
                        const confirmBookOpted = !confirmBookCheckbox;
                        this.onPressCheckBox();
                        if (confirmBookOpted) {
                          this.props?.logTravellerGenericClickEvents(
                            TRAVELLER_CLICK_EVENTS.BOOK_CONFIRMED_BERTHS,
                          );
                        }
                      }}
                      customStyles={styles.checkBoxContainer}
                      customLine1Style={[
                        styles.checkboxLabel,
                        fontStyle('regular'),
                        getLineHeight(14),
                      ]}
                    />
                  </View>
                  {confirmBookCheckbox && (
                    <View style={styles.warningContainer2}>
                      <Text style={[styles.warningText, fontStyle('regular'), getLineHeight(12)]}>
                        {_label('book_confirmed_berth_warning')}
                      </Text>
                    </View>
                  )}
                  <View style={styles.optionContainer} testID={`${id}_otherOptions_2`}>
                    <CheckBox
                      textLine1={_label('consider_auto_upgradation')}
                      isChecked={considerAutoUpgradation}
                      onPress={() => {
                        const autoUpgradationOpted = !considerAutoUpgradation;
                        onConsiderAutoUpgradationClicked();
                        if (autoUpgradationOpted) {
                          this.props?.logTravellerGenericClickEvents(
                            TRAVELLER_CLICK_EVENTS.CONSIDER_AUTO_UPGRADATION,
                          );
                        }
                      }}
                      customStyles={styles.checkBoxContainer}
                      customLine1Style={[
                        styles.checkboxLabel,
                        fontStyle('regular'),
                        getLineHeight(14),
                      ]}
                    />
                  </View>
                </View>
              </View>
            </>
          );
        default:
          return null;
      }
    };

    return (
      <View>
        <View style={styles.componentGap}>
          <TravelInsuranceContainer id={travelInsuranceId} />
        </View>
        {renderSwitch(visibleComponents)}
      </View>
    );
  };

  render() {
    const showNewAdditionalPreference = showNewAdditionalPreferenceVariants();
    return (
      <View style={styles.container} testID={this.props?.id}>
        <Accordion
          renderHeader={this.renderAccordionHeader}
          renderContent={
            showNewAdditionalPreference === 0
              ? this.renderOldAccordionContent
              : this.renderNewAccordionContent
          }
          defaultCollapsed
          testID="rails_travellerPage_additionalPreferences_toggle"
          accordionOpenCallback={() => {
            this.setVisibleComponents(1);
            trackClickEventProp61(
              TRAVELERS_PAGE_TRACKING_KEY_NEW,
              RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDITONAL_PREF_EXPAND,
            );
          }}
          accordingCloseCallback={() =>
            trackClickEventProp61(
              TRAVELERS_PAGE_TRACKING_KEY_NEW,
              RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDITONAL_PREF_COLLAPSE,
            )
          }
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: colors.white,
  },
  headerContainer1: {
    flexDirection: 'column',
    backgroundColor: colors.white,
  },
  headerContainer2: {
    flexDirection: 'row',
    backgroundColor: colors.white,
  },
  additionalSubheading1: {
    color: colors.black,
    fontSize: 14,
    marginTop: 7,
    flexShrink: 1,
  },
  additionalSubheading2: {
    color: colors.textGrey,
    fontSize: 16,
    marginTop: 5,
    flexShrink: 1,
    marginLeft: 5,
  },
  checkboxContentContainer: {
    paddingTop:20,
  },
  checkBoxContainer: {
    alignSelf: 'flex-start',
    marginBottom: 5,
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: 14,
    color: colors.black04,
  },
  warningContainer1: {
    backgroundColor: colors.creamWhite,
    borderRadius: 4,
    padding: 8,
  },
  warningContainer2: {
    backgroundColor: colors.creamWhite,
    borderRadius: 8,
    padding: 8,
    marginBottom: 8,
  },
  warningText: {
    fontSize: 12,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.lightYello,
  },
  viewButton: {
    color: colors.azure,
    fontSize: 14,
    marginTop: 1,
    fontWeight: 900,
  },
  optionBox: {
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    marginBottom: 16,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  componentGap: {
    marginBottom: 16,
  },
});

MorePreferences.propTypes = {
  refundAndCanChecked: PropTypes.bool.isRequired,
  considerAutoUpgradation: PropTypes.bool.isRequired,
  onRefundAndCancellationClicked: PropTypes.func.isRequired,
  onConsiderAutoUpgradationClicked: PropTypes.func.isRequired,
  confirmBookCheckbox: PropTypes.bool.isRequired,
  onConfirmBookCheckboxClicked: PropTypes.func.isRequired,
  id: PropTypes.string,
  travelerCount: PropTypes.number,
  logTravellerGenericClickEvents: PropTypes.func,
};
