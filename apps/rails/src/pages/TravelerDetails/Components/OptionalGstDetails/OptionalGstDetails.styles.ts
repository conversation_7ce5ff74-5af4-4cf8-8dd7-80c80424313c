import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = {
  container: {
    borderWidth: 0.5,
    borderColor: colors.lightGray,
    backgroundColor: colors.white,
    marginBottom: 11,
    padding: 16,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkBoxContainer: {
    height: 20,
    width: 20,
  },
  title: {
    ...fontStyle('black'),
    color: colors.black,
    fontSize: 19,
    lineHeight: 26,
    marginLeft: 10,
  },
  subTitle: {
    ...fontStyle('medium'),
    fontSize: 12,
    lineHeight: 14.4,
    marginTop: 8,
    marginLeft: 2,
  },
  textInputContainer: {
    backgroundColor: colors.grey13,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: colors.grey12,
    paddingVertical: 8,
    paddingLeft: 16,
    marginVertical: 8,
  },
  textInputHeader: {
    fontSize: 12,
    color: colors.lightTextColor,
    lineHeight: 12,
    marginBottom: 5,
  },
  textInputStyle: {
    fontSize: 16,
    lineHeight: 18,
    padding: 0,
    marginVertical: 0,
  },
  errorTextStyle: {
    color: colors.red,
    fontSize: 12,
  },
  wrapperStyle: {
    marginVertical: 8,
  },
};
