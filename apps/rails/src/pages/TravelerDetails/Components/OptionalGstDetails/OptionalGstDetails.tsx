import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Text, View } from 'react-native';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import { isValidGstNumber } from '@mmt/legacy-commons/Helpers/validationHelpers';
import { handleGstDetailsChange } from '../../TravelerDetailsActions';
import { _label } from '../../../../vernacular/AppLanguage';
import { fontStyle, getLineHeight } from '../../../../vernacular/VernacularUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';

/* Style imports */
import { styles } from './OptionalGstDetails.styles';
import { TEST_ID_CONSTANTS } from 'apps/rails/src/Utils/RailsConstant';


interface OptionalGstDetailsProps {
  gstDetailsChange: (val: string) => void;
  onToggle: (val: boolean) => void;
  isOptionalGstDetailsEnabled: boolean;
  gstDetailsError: boolean;
  payload: unknown;
  testId?: string;
}

const OptionalGstDetails = (props: OptionalGstDetailsProps) => {
  const {
    gstDetailsChange,
    onToggle,
    isOptionalGstDetailsEnabled,
    payload,
    gstDetailsError,
    testId,
  } = props;

  const [showGstDetails, setShowGstDetails] = useState(isOptionalGstDetailsEnabled);
  const [gstDetails, setGstDetails] = useState(payload);

  const error = {
    gstIn: gstDetailsError && !isValidGstNumber(gstDetails?.gstIn),
    nameOnGst: gstDetailsError && gstDetails?.nameOnGst?.slice()?.trim()?.length === 0,
  };

  const handleInputChange = (val: string, dataPoint: string) => {
    const newGstDetails = {
      ...gstDetails,
      [dataPoint]: val,
    };
    setGstDetails(newGstDetails);
    gstDetailsChange(newGstDetails);
  };

  const onToggleChange = () => {
    setShowGstDetails(!showGstDetails);
    onToggle(!showGstDetails);
  };

  return (
    <View style={styles.container}>
      <TouchableRipple
        onPress={() => {
          onToggleChange();
        }}
      >
        <View style={styles.header}>
          <CheckBox
            textLine1=""
            isChecked={showGstDetails}
            onPress={() => {
              onToggleChange();
            }}
          />
          <Text style={styles.title} testID={`${testId}_checkbox`}>
            {_label('enter_gst_details')}
          </Text>
          <Text style={styles.subTitle}>({_label('optional')})</Text>
        </View>
      </TouchableRipple>
      {showGstDetails && (
        <View>
          <View style={styles.wrapperStyle}>
            <FloatingInput
              testID={`${testId}_gstNumber`}
              label={_label('gstIn_number')}
              customStyle={{
                labelStyle: [fontStyle('bold'), getLineHeight(12)],
                inputFieldStyle: [fontStyle('black'), getLineHeight(16)],
                errorMessageStyle: [fontStyle('regular'), getLineHeight(12)],
              }}
              inputProps={{
                autoCorrect: false,
                autoCapitalize: 'characters',
                maxLength: 15,
              }}
              value={gstDetails?.gstIn}
              onChangeText={(text: string) => {
                handleInputChange(text, 'gstIn');
              }}
              isError={!!error?.gstIn}
              errorMessage={_label('enter_valid_gst_no')}
            />
          </View>
          <View style={styles.wrapperStyle}>
            <FloatingInput
              testID={`${testId}_gstName`}
              label={_label('company_name')}
              customStyle={{
                labelStyle: [fontStyle('bold'), getLineHeight(12)],
                inputFieldStyle: [fontStyle('black'), getLineHeight(16)],
                errorMessageStyle: [fontStyle('regular'), getLineHeight(12)],
              }}
              inputProps={{
                autoCorrect: false,
                autoCapitalize: 'none',
              }}
              value={gstDetails?.nameOnGst}
              onChangeText={(text: string) => {
                handleInputChange(text, 'nameOnGst');
              }}
              isError={!!error?.nameOnGst}
              errorMessage={_label('enter_company_name')}
            />
          </View>
        </View>
      )}
    </View>
  );
};

const mapStateToProps = (state: unknown) => {
  const {
    railsTraveler: { optionalGstDetailsEnabled, gstDetails, gstDetailsError },
  } = state;
  const testId = `${TEST_ID_CONSTANTS.TRAVELLER_DETAILS}gstDetails`;
  return {
    isOptionalGstDetailsEnabled: optionalGstDetailsEnabled,
    payload: gstDetails,
    gstDetailsError,
    testId,
  };
};

const mapDisptachToProps = (dispatch: unknown) => ({
  gstDetailsChange: (val: string) => {
    dispatch(handleGstDetailsChange(val, 'gstDetails'));
  },
  onToggle: (val: boolean) => {
    dispatch(handleGstDetailsChange(val, 'optionalGstDetailsEnabled'));
  },
});

export default connect(mapStateToProps, mapDisptachToProps)(OptionalGstDetails);
