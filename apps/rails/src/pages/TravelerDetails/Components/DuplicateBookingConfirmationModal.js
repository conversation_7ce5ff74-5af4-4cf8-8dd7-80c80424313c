
import React, {useEffect} from 'react';
import PropTypes from 'prop-types';
import {View, Text, Modal, TouchableOpacity, SafeAreaView, StyleSheet, Linking} from 'react-native';
import {trackOmniture} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import { colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

export default function DuplicateBookingConfirmationModal(props) {
  props = {
    ...props,
    alertHeader: typeof props.alertHeader === 'undefined' ? '' : props.alertHeader,
    alertMessage: typeof props.alertMessage === 'undefined' ? '' : props.alertMessage,
  };

  const {visible, alertMessage, onContinue, onClose} = props;

  useEffect(() => {
    const pageName = 'mob_rail_traveller';
    const params = {
      pageName,
      m_v15: pageName,
      m_c61: 'mob_rail_traveller_duplicate_booking_alert_shown',
    };
    trackOmniture(pageName, params);
  }, []);

  const onBookingHistoryPress = () => {
    onClose();
    Linking.openURL('mmyt://support/booking/listing/');
  };

  return (
    <Modal visible={visible} transparent testID="duplicate_booking_confirmation_modal">
      <SafeAreaView style={{height: '100%'}}>
        <TouchableOpacity
          style={{ backgroundColor: colors.backgroundColor, flex: 1 }}
          onPress={onClose}
          testID="duplicate_booking_confirmation_modal_touchable"
        />
        <View style={styles.textContainer} testID={props?.id}>
          <Text style={[styles.detailsText, fontStyle('regular') , getLineHeight(16)]}>{alertMessage}</Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.button}
              onPress={onBookingHistoryPress}
              testID="duplicate_booking_confirmation_modal_booking_history_button"
            >
              <Text
                style={[styles.buttonText, fontStyle('bold'), getLineHeight(16)]}
                testID="duplicate_booking_confirmation_modal_booking_history_button_text"
              >
                {_label('view_booking_history')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.button}
              onPress={onContinue}
              testID="duplicate_booking_confirmation_modal_continue_button"
            >
              <Text
                style={[styles.buttonText, fontStyle('bold'), getLineHeight(16)]}
                testID="duplicate_booking_confirmation_modal_continue_button_text"
              >
                {_label('yes_continue')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  textContainer: {
    backgroundColor: colors.white,
    padding: 20,
  },
  headingText: {
    fontSize: 32,
    color: colors.black,
  },
  detailsText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.black,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    paddingVertical: 16,
  },
  buttonText: {
    color: colors.azure,
    fontSize: 16,
  },
});

DuplicateBookingConfirmationModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  alertHeader: PropTypes.string,
  alertMessage: PropTypes.string,
  onContinue: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  id: PropTypes.string,
};
