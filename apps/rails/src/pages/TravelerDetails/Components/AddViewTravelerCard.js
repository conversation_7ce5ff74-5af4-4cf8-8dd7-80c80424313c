import React from 'react';
import {Text, View, StyleSheet, Image} from 'react-native';
import {PropTypes} from 'prop-types';
import { colors} from '@mmt/legacy-commons/Styles/globalStyles';
import travelerStyle from '../TravelerDetailsCSS';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';
import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';

const AddViewTravelerCard = ({
  viewAllTravelers, onViewAllClicked, onAddTraveler, showViewAll, id,
}) => (
  <View style={{marginTop: 4, flexDirection: 'row', justifyContent: 'space-between'}}>
    <TouchableRipple onPress={onAddTraveler} testID={id}>
      <View style={styles.labelContainer}>
        <Text style={[styles.label, styles.labelPlus, fontStyle('bold'), getLineHeight(16)]}> + </Text>
        <Text style={[styles.label, styles.labelText, fontStyle('bold'), getLineHeight(12)]}>
          {_label('traveller_details', { uppercase: true })}
        </Text>
      </View>
    </TouchableRipple>

    {showViewAll &&
      <TouchableRipple onPress={onViewAllClicked}>
        <View style={travelerStyle.viewStyle}>
          <Text style={[styles.label, styles.labelText, fontStyle('bold'), getLineHeight(12)]}>
            { !viewAllTravelers ? _label('view_all', { uppercase: true }) : _label('view_less', { uppercase: true })}
          </Text>
          <Image
            source={viewAllTravelers ? arrowUp : arrowDown}
            style={{width: 24, height: 24}}
          />
        </View>
      </TouchableRipple>
    }
  </View>
);

const styles = StyleSheet.create({
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    color: colors.azure,
  },
  labelPlus: {
    fontSize: 16,
  },
  labelText: {
    fontSize: 12,
    marginLeft: 8,
  },
});

AddViewTravelerCard.propTypes = {
  viewAllTravelers: PropTypes.bool.isRequired,
  onViewAllClicked: PropTypes.func.isRequired,
  onAddTraveler: PropTypes.func.isRequired,
  showViewAll: PropTypes.bool.isRequired,
  id: PropTypes.string,
};


export default AddViewTravelerCard;
