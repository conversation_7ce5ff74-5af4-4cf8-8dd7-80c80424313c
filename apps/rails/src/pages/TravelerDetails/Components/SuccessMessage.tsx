import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { _label } from '../../../vernacular/AppLanguage';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import HTMLView from 'react-native-htmlview';

const styles = StyleSheet.create({
  successMessageText: {
    fontSize: 14,
    color: colors.defaultTextColor,
    width: '100%',
  },
  congratulationsMessageText: {
    fontSize: 14,
    color: colors.cyan,
  },
});


const htmlStyles = StyleSheet.create({
  // eslint-disable-next-line react-native/no-unused-styles
  p: {
    fontSize: 14,
    color: colors.defaultTextColor,
  },
  // eslint-disable-next-line react-native/no-unused-styles
  b: {
    color: colors.green,
    fontWeight: '700',
  },
});

interface SuccessMessageProps {
  appliedCouponCode: string;
  couponDataCouponCode: string;
  normalCouponSuccessMessage: string;
  fcTgCouponSuccessMessage?: string;
  ancillaryCoupon: boolean;
}

function SuccessMessage(props: SuccessMessageProps) {
  const {
    appliedCouponCode,
    couponDataCouponCode,
    normalCouponSuccessMessage,
    fcTgCouponSuccessMessage,
    ancillaryCoupon = false,
  } = props;

  const showSuccessMessage = appliedCouponCode === couponDataCouponCode;

  if (showSuccessMessage && normalCouponSuccessMessage && ancillaryCoupon) {
    return (
      <View>
        <HTMLView value={`<p>${normalCouponSuccessMessage}</p>`} stylesheet={htmlStyles} />
      </View>
    );
  }

  if (showSuccessMessage && fcTgCouponSuccessMessage) {
    return (
      <View>
        <Text style={[styles.successMessageText, fontStyle('regular')]}>
          {fcTgCouponSuccessMessage}
        </Text>
      </View>
    );
  }
  if (showSuccessMessage && normalCouponSuccessMessage) {
    return (
      <View>
        <Text style={[styles.successMessageText, fontStyle('regular')]}>
          <Text style={[styles.congratulationsMessageText, fontStyle('bold')]}>{`${_label('congratulations')}! `} </Text>
          {normalCouponSuccessMessage}
        </Text>
      </View>
    );
  }
  return null;
}

export default SuccessMessage;
