import React, { useEffect } from 'react';
import { Image, Text, View, TouchableOpacity } from 'react-native';
import { connect } from 'react-redux';
import { styles } from '../Styles/TripGuaranteePlus.styles';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import TripGuaranteeOptions from './TripGuaranteeOptions';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { updateFCTGUserResponse } from '../../../TravelerDetailsActions';
import useTgThresholdHandler from '../../AssuredConfirmation/useTgThresholdHandler';
import useTgPlusThresholdHandler from '../../AssuredConfirmation/useTgPlusThresholdHandler';
import { trackRailofyKnowMoreClick } from 'apps/rails/src/Utils/railofyUtils';
import { TRAVELERS_PAGE_TRACKING_KEY_NEW } from 'apps/rails/src/Utils/RailsConstant';
import { openWebView } from '../../../railofyUtils';
import { Refund, TgPlusConfig, TgPlusProps } from '../types';
import { trackFCTGEvar47or97Event } from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { getRailsTGVersion } from 'apps/rails/src/RailsAbConfig';

import addIcon from '@mmt/legacy-assets/src/add_icon.webp';
import infoIcon from '@mmt/legacy-assets/src/info_blue.webp';
import tgPlusIcon from '@mmt/legacy-assets/src/tg_plus_icon.webp';

const containerDefaultBg = ['#c86dd7', '#3023ae'];

interface RefundInfoContainerProps {
  refundInfo: Refund;
  ticketFare?: number;
  testID?: string;
}

const RefundInfoContainer = ({ refundInfo, ticketFare, testID }: RefundInfoContainerProps) => {
  return (
    <View style={styles.refundContainer} testID={testID}>
      <LinearGradient
        colors={[colors.grey5, colors.grey7, colors.lightGrey]}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.verticalGradient}
        testID={`${testID}_gradient`}
      >
        <View style={styles.refundLabelContainer} testID={`${testID}_label_container`}>
          <Text style={[styles.refundLabel, fontStyle('regular')]}>{refundInfo.label}</Text>
        </View>
      </LinearGradient>
      <View style={styles.fareContainer} testID={`${testID}_fare_container`}>
        <Text style={[styles.fareTitle, fontStyle('bold')]}>
          {refundInfo.refund === 1 && <Text style={styles.fareTitle2}>{refundInfo.title}</Text>}
          <Text>{refundInfo.refund === 1 ? `₹${ticketFare}` : refundInfo.title}</Text>
        </Text>
        <Text style={[styles.fareDescription, fontStyle('regular')]}>{refundInfo.description}</Text>
        <Text style={[styles.refundMode, fontStyle('regular')]}>{refundInfo.refundMode}</Text>
      </View>
    </View>
  );
};

const TripGuaranteePlus = (props: TgPlusProps) => {
  const {
    updateFCTGUserResponse,
    numberOfPassengers,
    isTGSelected,
    isTGPlusSelected,
    tripGuaranteeData: {
      premiumUpperCap,
      refundCouponUpperCap,
      fareInfoRevamped,
      preferenceInfoRevamped,
    },
    tgPlusData,
  } = props;

  let tgpConfig = props?.tgpTextConfig;
  const tncUrl = tgPlusData?.tncUrl;
  const ticketFare = tgPlusData?.ticketFare;
  const currentTgVersion = getRailsTGVersion(true);
  useEffect(() => {
    if (currentTgVersion !== null) {
      trackFCTGEvar47or97Event(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        `${RAIL_EVENTS.FCTG.TGP}${currentTgVersion}`,
      );
    }
  }, [currentTgVersion]);

  const tgpTextConfig: TgPlusConfig = useConfigStore(configKeys.RAILS_TG_PLUS_CONFIG);

  if (!tgpConfig?.header) {
    tgpConfig = tgpTextConfig;
  }

  const { totalRefund, premiumPerPerson, totalTicketFare } = useTgPlusThresholdHandler({
    ...tgPlusData,
    numberOfPassengers,
  });

  Object.assign(tgPlusData, {
    totalRefund,
    premiumPerPerson,
    totalTicketFare,
  });

  const {
    totalRefund: totalRefundForTg,
    premiumPerPerson: premiumPerPersonForTg,
    totalTicketFare: totalTicketFareForTg,
  } = useTgThresholdHandler({
    premiumUpperCap,
    refundCouponUpperCap,
    fareInfoRevamped,
    preferenceInfoRevamped,
    numberOfPassengers,
  });

  const tgData = {
    totalRefund: totalRefundForTg,
    premiumPerPerson: premiumPerPersonForTg,
    totalTicketFare: totalTicketFareForTg,
  };

  const subHeader = isTGSelected
    ? tgpConfig?.subHeader[1]
    : isTGPlusSelected
    ? tgpConfig?.subHeader[2]
    : tgpConfig?.subHeader[0];

  const onMoreDetailsPressed = () => {
    trackRailofyKnowMoreClick(TRAVELERS_PAGE_TRACKING_KEY_NEW);
    openWebView(tncUrl);
  };

  return (
    <View style={styles.mainContainer} testID="fc_tg_tg_plus_widget">
      <LinearGradient
        colors={tgPlusData?.backgroundColor || containerDefaultBg}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={styles.gradientContainer}
        testID="tg_plus_gradient_container"
      >
        <View style={styles.headerContainer} testID="tg_plus_header_container">
          <View style={styles.headerText} testID="tg_plus_header_text">
            <Text style={[styles.whiteText, getLineHeight(16), fontStyle('bold')]}>
              {tgpConfig?.header}
            </Text>
          </View>
          {isTGPlusSelected && (
            <Image source={tgPlusIcon} style={styles.tgPlusIcon} testID="tg_plus_icon" />
          )}
          {(isTGSelected || isTGPlusSelected) && (
            <Text style={[styles.whiteText, getLineHeight(12), fontStyle('regular')]}>
              {_label('added')}
            </Text>
          )}
        </View>
        <Text style={[styles.whiteText, getLineHeight(12), fontStyle('bold')]}>{subHeader}</Text>
      </LinearGradient>
      <View style={styles.detailsContainer} testID="tg_plus_details_container">
        <View style={styles.refundInfoContainer} testID="tg_plus_refund_info_container">
          <RefundInfoContainer
            refundInfo={tgpConfig?.refunds[0]}
            ticketFare={ticketFare}
            testID="tg_plus_refund_info_0"
          />
          <Image source={addIcon} style={styles.addIcon} testID="tg_plus_add_icon" />
          <RefundInfoContainer refundInfo={tgpConfig?.refunds[1]} testID="tg_plus_refund_info_1" />
        </View>
        <View style={styles.voucherContainer} testID="tg_plus_voucher_container">
          <Text style={[styles.greyText, getLineHeight(12), fontStyle('regular')]}>
            {tgpConfig?.voucherUsage}
          </Text>
          <TouchableOpacity
            style={styles.infoButton}
            onPress={onMoreDetailsPressed}
            testID="tg_plus_info_button"
          >
            <Image source={infoIcon} style={styles.infoIcon} testID="tg_plus_info_icon" />
          </TouchableOpacity>
        </View>
        <TripGuaranteeOptions
          tgpConfig={tgpConfig}
          tgData={tgData}
          tgPlusData={tgPlusData}
          updateFCTGUserResponse={updateFCTGUserResponse}
          isTGPlusSelected={isTGPlusSelected}
          isTGSelected={isTGSelected}
          tgDiscountPremium={props.tgDiscountPremium}
          isTgDiscounted={props.isTgDiscounted}
          ancillaryDiscountDetails={props.ancillaryDiscountDetails}
        />
      </View>
    </View>
  );
};

const mapStateToProps = ({ railsTraveler }) => {
  const { railofy, selectedTravelers, couponData = {} } = railsTraveler;
  const { ancillaryDiscountDetails = {} } = couponData;
  const {
    tgPlusData,
    isTGSelected,
    isTGPlusSelected,
    tripGuaranteeData,
    freeCancellationData,
    isTgDiscounted,
  } = railofy;
  const fcDiscountValue = freeCancellationData?.freeCancellationPremiumDetails?.discountValue ?? 0;
  const tgDiscountValue = tripGuaranteeData?.discountValue ?? 0;
  const fcDiscountPremium =
    freeCancellationData?.freeCancellationPremiumDetails?.discountedPremium ?? 0;
  const tgDiscountPremium = tripGuaranteeData?.discountedPremium ?? 0;
  return {
    numberOfPassengers: Math.max(selectedTravelers.length, 1),
    tgPlusData,
    isTGSelected,
    isTGPlusSelected,
    tripGuaranteeData,
    tgDiscountPremium,
    fcDiscountPremium,
    tgDiscountValue,
    fcDiscountValue,
    isTgDiscounted,
    ancillaryDiscountDetails,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    updateFCTGUserResponse: (
      index: number,
      premiumAmount: number = 0,
      type: string,
      toast: string,
    ) => {
      dispatch(updateFCTGUserResponse(index, premiumAmount, type, toast));
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(TripGuaranteePlus);
