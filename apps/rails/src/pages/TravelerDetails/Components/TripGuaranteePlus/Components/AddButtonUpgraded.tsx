import React from 'react';
import { Image, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { View } from 'react-native-animatable';
import addIcon from '@mmt/legacy-assets/src/blue_add_icon.webp';
interface AddButtonProps {
  isOptionSelected: boolean;
  onSelect: () => void;
  testID?: string;
  label?: string;
  labelStyle?: unknown;
}

function AddButtonUpgraded({
  isOptionSelected,
  onSelect,
  testID,
  label,
}: AddButtonProps) {
  return (
    <View
      style={styles.addIconContainer}
    >
      <Text style={[styles.blackText, getLineHeight(14), fontStyle('medium')]}>
        {isOptionSelected ? label?.replace( _label('rails_tg_added_tag'),_label('rails_tg_remove_tag')) : label}
      </Text>
      <TouchableOpacity
        testID={testID}
        style={[
          styles.container,
          { borderColor: isOptionSelected ? colors.greyText1 : colors.azure },
           styles.gradientContainer,
        ]}
        onPress={onSelect}
      >
        {!isOptionSelected && <Image source={addIcon} style={styles.addIcon} />}
        <Text
          style={[
            { color: isOptionSelected ? colors.greyText1 : colors.azure },
            getLineHeight(12),
            fontStyle('bold'),
          ]}
        >
          {isOptionSelected
            ? _label('Remove', { capitalize: true })
            : _label('add', { capitalize: true })}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: 66,
    height: 32,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: colors.white,
  },
  addIcon: {
    marginRight: 4,
    width: 16,
    height: 16,
  },
  addIconContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  blackText: {
    color: colors.black,
  },
  gradientContainer: {
    minWidth: 70,
    paddingHorizontal: 10,
  },
});

export default AddButtonUpgraded;
