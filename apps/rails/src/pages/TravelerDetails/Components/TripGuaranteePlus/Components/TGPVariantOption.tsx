
// had error while commiting, whole file needs to be formatted properly.
/* eslint-disable */
import React, { useEffect, useState } from 'react';
import { Image, Text, View, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import AddButton from './AddButton';
import { getRailsTGVersion } from 'apps/rails/src/RailsAbConfig';
import { RAILOFY_TYPE } from '../../../railofyUtils';
import { TgPlusConfig, TgPlusResponse } from '../types';
import { ConfirmationGuaranteeWidget } from 'apps/rails/src/types/railofy.types';
import { styles } from '../Styles/TGPVaraintOption.styles';
import { PRICE_REPLACER, TEST_ID_CONSTANTS, TRAVELERS_PAGE_TRACKING_KEY_NEW } from 'apps/rails/src/Utils/RailsConstant';
import { trackFCTGEvar47or97Event, updateEvar47or97Variable } from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import tgPlusIcon from '@mmt/legacy-assets/src/tg_plus_icon.webp';
import infoIcon  from '@mmt/legacy-assets/src/info_blue.webp';
import PropTypes from 'prop-types';

interface TGPVaraintOptionProps {
    tgpConfig: TgPlusConfig;
    data: ConfirmationGuaranteeWidget | TgPlusResponse;
    updateFCTGUserResponse: (index: number, premiumPerPerson: number) => void;
    isTgPlus: boolean;
    isOptionSelected: boolean;

}

function ToolTip({ text, leftMargin }) {

    return (
        <View style={styles.toolTipContainer}>
            <View style={styles.toolTipInnerContainer}>
                <Text style={[styles.whiteText, getLineHeight(14), fontStyle('regular')]}>
                    {text}
                </Text>
            </View>
            <View style={[styles.arrowBottom, { marginLeft: leftMargin }]} />
        </View>
    );
}

function TGPVaraintOption({
    tgpConfig,
    data,
    isTgPlus,
    updateFCTGUserResponse,
    isOptionSelected,
}: TGPVaraintOptionProps) {

    const [showToolTip, setShowToolTip] = useState(false);
    const [refundTextWidth, setRefundTextWidth] = useState(0);
    const [toolTipTimeoutId, setToolTipTimeoutId] = useState(null);


    const currentTgVersion = getRailsTGVersion(true);
    const istgVersion2 = currentTgVersion === 2;
    const isTgVersion5 = currentTgVersion === 5;
    const isTgVersion6 = currentTgVersion === 6;
    const {
      subTextVersion5: { tgText = '', tgPlusText = '' },
    } = tgpConfig;

    const currentOption = istgVersion2
      ? tgpConfig?.options[0]
      : isTgVersion6
      ? tgpConfig?.options[2]
      : tgpConfig?.options[1];
    const ticketFare = data?.totalTicketFare;
    const testID = `${TEST_ID_CONSTANTS.TRAVELLER_DETAILS}${isTgPlus ? 'tgPlus' : 'tg'}_addButton`;

    const {
        premiumPerPerson,
        totalRefund,
    } = data;

    const toolTipText = tgpConfig?.toolTip.replace(PRICE_REPLACER, ticketFare);

    useEffect(() => {
        if(currentTgVersion!= null){
          updateEvar47or97Variable(
            `${RAIL_EVENTS.FCTG.TGP}${currentTgVersion}`
          )}
    },[])

    const handleUserSelection = () => {
        const railofyType = isTgPlus ? RAILOFY_TYPE.TGP : RAILOFY_TYPE.TG;
        const toast = isTgPlus ? tgpConfig?.toasts[2] : tgpConfig?.toasts[1];
        const index = isOptionSelected ? 1 : 0;
        const premiumAmount = index === 0 ? premiumPerPerson : 0;
        updateFCTGUserResponse(index, premiumAmount, railofyType, toast);
        if (isTgPlus) {
            trackClickEventProp61(
                TRAVELERS_PAGE_TRACKING_KEY_NEW,
                `${
                    index
                    ? RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TGP_REMOVED
                    : RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TGP_ADDEED
                }`,
            );

            trackFCTGEvar47or97Event(
                null,
                `${RAIL_EVENTS.FCTG.TGP}${
                !index ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
                }`,
                RAIL_EVENTS.FCTG.TGP,
            );
        } else {

            trackClickEventProp61(
                TRAVELERS_PAGE_TRACKING_KEY_NEW,
                `${
                    index
                    ? RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TG_REMOVED
                    : RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TG_ADDEED
                }`,
            );

            trackFCTGEvar47or97Event(
                null,
                `${RAIL_EVENTS.FCTG.TG}${RAIL_EVENTS.FCTG.TRAVELER}${
                  !index ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
                }`,
                RAIL_EVENTS.FCTG.TG,
              );
        }
    };

    const handleInfoClicked = () => {

        trackClickEventProp61(
            TRAVELERS_PAGE_TRACKING_KEY_NEW,
            RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TGP_INFO_CLICK,
          );

        if (toolTipTimeoutId) {
            clearTimeout(toolTipTimeoutId);
            setToolTipTimeoutId(null);
        }

        setShowToolTip((showToolTip) => !showToolTip);

        if (!showToolTip) {
            const newTimeoutId = setTimeout(() => {
                setShowToolTip(false);
            }, 10000);

            setToolTipTimeoutId(newTimeoutId);
        }
    };

    const onRefundTextLayout = (event) => {
        const { width } = event.nativeEvent.layout;
        setRefundTextWidth(width + 6);
    };


    return (
      <>
        <LinearGradient
          colors={[isOptionSelected ? colors.skyBlue7 : colors.white, colors.white]}
          start={{ x: 1, y: 0 }}
          end={{ x: 0.6, y: 0.5 }}
          style={styles.gradientContainer}
        >
          {!isTgVersion6 && (
            <View style={styles.headerContainer}>
              <View style={styles.headerTextContainer}>
                <View style={styles.marginRight4}>
                  <Text style={[styles.blackText, getLineHeight(14), fontStyle('bold')]}>
                    {currentOption.tgHeader.add}
                  </Text>
                </View>
                <Text style={[styles.governorBayText, getLineHeight(14), fontStyle('bold')]}>
                  {currentOption.tgHeader.tg}
                </Text>
                {isTgPlus && (
                  <Image source={tgPlusIcon} resizeMode="contain" style={styles.tgPlusIcon} />
                )}
              </View>
              <Text
                style={[styles.blackText, getLineHeight(12), fontStyle('bold')]}
              >{`₹${premiumPerPerson}/person`}</Text>
            </View>
          )}
          <View style={styles.subHeaderContainer}>
            {istgVersion2 ? (
              <View style={styles.subHeaderView}>
                <Text
                  style={[styles.waitListedRefundText, getLineHeight(12), fontStyle('regular')]}
                >
                  {currentOption.subHeader.waitlistedRefund}
                  <Text style={fontStyle('bold')}>{` ₹${totalRefund}`}</Text>
                </Text>
                <View style={styles.flexRow}>
                  <Text
                    style={[styles.greyText, getLineHeight(12), fontStyle('regular')]}
                    onLayout={onRefundTextLayout}
                  >
                    {currentOption.subHeader.racRefund}
                    <Text style={[styles.purpleText, fontStyle('bold')]}>
                      {` ₹${isTgPlus ? totalRefund : ticketFare}`}
                    </Text>
                  </Text>
                  {!isTgPlus && (
                    <TouchableOpacity style={styles.infoButton} onPress={handleInfoClicked}>
                      <Image source={infoIcon} style={styles.infoIcon} />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            ) : isTgVersion6 ? (
              <View style={styles.subHeaderView3}>
                <View style={styles.flexRow} onLayout={onRefundTextLayout}>
                  <View style={styles.marginRight4}>
                    <Text style={[styles.defaultText, getLineHeight(14), fontStyle('semiBold')]}>
                      {currentOption.subHeader.common}
                    </Text>
                  </View>
                  <Text style={[styles.purpleText, getLineHeight(14), fontStyle('bold')]}>
                    {isTgPlus
                      ? currentOption.subHeader.waitlistedRAC
                      : currentOption.subHeader.waitlisted}
                  </Text>
                </View>
                <View>
                    <Text style={[styles.defaultText, fontStyle('semiBold')]}>{`for ₹${premiumPerPerson}/person`}</Text>
                  </View>
              </View>
            ) : (
              <View style={styles.subHeaderView2}>
                <View style={styles.flexRow} onLayout={onRefundTextLayout}>
                  <View style={styles.marginRight4}>
                    <Text style={[styles.greyText, getLineHeight(12), fontStyle('regular')]}>
                      <Text style={[styles.blackText, fontStyle('bold')]}>
                        {`₹${totalRefund} `}
                      </Text>
                      {currentOption.subHeader.common}
                    </Text>
                  </View>
                  <Text style={[styles.purpleText, getLineHeight(12), fontStyle('regular')]}>
                    {isTgPlus
                      ? currentOption.subHeader.waitlistedRAC
                      : currentOption.subHeader.waitlisted}
                  </Text>
                </View>
                {!isTgPlus && !isTgVersion5 && (
                  <TouchableOpacity
                    style={[styles.infoButton, styles.paddingTop4]}
                    onPress={handleInfoClicked}
                  >
                    <Image source={infoIcon} style={styles.infoIcon} />
                  </TouchableOpacity>
                )}
              </View>
            )}
            <AddButton
              testID={testID}
              isOptionSelected={isOptionSelected}
              onSelect={handleUserSelection}
            />
          </View>
          <View>
            {isOptionSelected && (isTgVersion5 || isTgVersion6) && (
              <Text
                style={[
                  styles.greyText,
                  getLineHeight(12),
                  fontStyle('regular'),
                  { flexShrink: 1 },
                ]}
              >
                {isTgPlus ? tgPlusText : tgText}
              </Text>
            )}
          </View>
        </LinearGradient>
        {!isTgPlus && showToolTip && <ToolTip text={toolTipText} leftMargin={refundTextWidth} />}
      </>
    );
}

ToolTip.propTypes = {
  text: PropTypes.string,
  leftMargin: PropTypes.number,
};

export default TGPVaraintOption;
