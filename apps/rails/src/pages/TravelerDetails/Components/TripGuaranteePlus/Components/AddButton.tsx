import React from 'react';
import { Image, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle, getLineHeight } from 'apps/rails/src/vernacular/VernacularUtils';

import addIcon from '@mmt/legacy-assets/src/blue_add_icon.webp';
interface AddButtonProps {
  isOptionSelected: boolean;
  onSelect: () => void;
  testID?: string;
}

function AddButton({ isOptionSelected, onSelect, testID }: AddButtonProps) {
  return (
    <TouchableOpacity
      testID={testID}
      style={[
        styles.container,
        { borderColor: isOptionSelected ? colors.greyText1 : colors.azure },
      ]}
      onPress={onSelect}
    >
      {!isOptionSelected && <Image source={addIcon} style={styles.addIcon} />}
      <Text
        style={[
          { color: isOptionSelected ? colors.greyText1 : colors.azure },
          getLineHeight(12),
          fontStyle('bold'),
        ]}
      >
        {isOptionSelected
          ? _label('added', { capitalize: true })
          : _label('add', { capitalize: true })}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: 66,
    height: 32,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: colors.white,
  },
  addIcon: {
    marginRight: 4,
    width: 16,
    height: 16,
  },
});

export default AddButton;
