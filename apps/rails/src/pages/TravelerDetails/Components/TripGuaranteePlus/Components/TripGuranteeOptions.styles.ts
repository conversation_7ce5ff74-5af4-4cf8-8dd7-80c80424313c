import { fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const msgStyle = StyleSheet.create({
  p: { fontSize: 12, color: colors.black },
  s: {
    ...fontStyle('bold'),
    color: colors.lightGreen16,
  },
  b: { ...fontStyle('bold') },
});

export const localStyles = StyleSheet.create({
  container: {
    marginHorizontal: 8,
    marginTop: -10,
  },
  optionContainer: {
    marginBottom: 12,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 70,
    paddingVertical: 12,
    paddingHorizontal: 10,
  },
  selectedBackground: {
    backgroundColor: colors.lighterBlue,
  },
  unselectedBackground: {
    backgroundColor: colors.grey22,
  },
  titleImage: {
    height: 25,
    resizeMode: 'contain',
    marginLeft: 10,
  },
  titleImageTgplus: {
    width: 124,
  },
  titleImageTg: {
    width: 95,
  },
  selectedBorder: {
    borderColor: colors.primary,
  },
  unselectedBorder: {
    borderColor: colors.lightGray,
  },
  contentContainer: {
    flex: 1,
    marginLeft: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  titleRow2: {
    flexDirection: 'row',
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
    flexWrap: 'wrap',
    paddingRight: 15,
    paddingLeft: 28,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  strikeThroughPrice: {
    textDecorationLine: 'line-through',
    color: colors.grey,
    marginRight: 4,
    fontSize: 14,
  },
  discountedPrice: {
    ...fontStyle('bold'),
    fontSize: 14,
    color: colors.black,
    marginLeft: 8,
  },
  detailBulletRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 2,
  },
  detailBullet: {
    fontSize: 16,
    lineHeight: 18,
    marginRight: 6,
  },
});
