import React, { useMemo } from 'react';
import { View, Text, Image, ImageSourcePropType, TouchableOpacity } from 'react-native';
import HTMLView from 'react-native-htmlview';
import { msgStyle, localStyles } from './TripGuranteeOptions.styles';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import { styles as baseStyles } from '../Styles/TGPVaraintOption.styles';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';
import { trackFCTGEvar47or97Event } from '@mmt/rails/src/railsAnalytics';
import { TRAVELERS_PAGE_TRACKING_KEY_NEW } from '@mmt/rails/src/Utils/RailsConstant';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { COUPON_TYPE, RAILOFY_TYPE } from '../../../railofyUtils';

const TG_PRICE_SUFFIX = '/person';
const CURRENCY_SYMBOL = '₹';
const TG = 'tg';
const TGP = 'tgplus';

export type OptionKey = 'tg' | 'tgplus';

interface Option {
  key: OptionKey;
  title: ImageSourcePropType;
  price: number;
  details: string[];
  plus: boolean;
}

interface TripGuaranteeOptionsProps {
  tgpConfig: unknown;
  tgPlusData: unknown;
  tgData: unknown;
  updateFCTGUserResponse: (
    index: number,
    premiumPerPerson: number,
    railofyType: string,
    toast: string,
  ) => void;
  isTGPlusSelected: boolean;
  isTGSelected: boolean;
  tgDiscountPremium: number;
  isTgDiscounted: boolean;
  ancillaryDiscountDetails: unknown;
}

const TripGuaranteeOptions: React.FC<TripGuaranteeOptionsProps> = ({
  tgpConfig,
  tgPlusData,
  tgData,
  updateFCTGUserResponse,
  isTGPlusSelected,
  isTGSelected,
  tgDiscountPremium,
  isTgDiscounted,
  ancillaryDiscountDetails,
}) => {
  const OPTIONS: Option[] = useMemo(
    () => [
      {
        key: TG,
        title: ASSETS.tripGuaranteeText,
        price: tgData?.premiumPerPerson,
        details: tgpConfig?.tgDetails,
        plus: false,
      },
      {
        key: TGP,
        title: ASSETS.tripGuaranteePlusText,
        price: tgPlusData?.premiumPerPerson,
        details: tgpConfig?.tgPlusDetails,
        plus: true,
      },
    ],
    [tgData, tgPlusData, tgpConfig?.tgDetails, tgpConfig?.tgPlusDetails],
  );

  const handleUserSelection = (key: OptionKey, isSelected: boolean) => {
    const railofyType = key === TGP ? RAILOFY_TYPE.TGP : RAILOFY_TYPE.TG;
    const toast = key === TGP ? tgpConfig?.toasts[2] : tgpConfig?.toasts[1];

    const index = isSelected ? 0 : 1;
    const premiumAmount =
      key === TGP
        ? !isSelected
          ? tgPlusData?.premiumPerPerson
          : 0
        : !isSelected
        ? tgData?.premiumPerPerson
        : 0;
    updateFCTGUserResponse(index, premiumAmount, railofyType, toast);

    if (key === TGP) {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        `${
          index
            ? RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TGP_REMOVED
            : RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TGP_ADDEED
        }`,
      );

      trackFCTGEvar47or97Event(
        null,
        `${RAIL_EVENTS.FCTG.TGP}${
          !index ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
        }`,
        RAIL_EVENTS.FCTG.TGP,
      );
    } else {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        `${
          index
            ? RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TG_REMOVED
            : RAIL_EVENTS.TRAVELLER.RAILS_TARVELLERS_TG_ADDEED
        }`,
      );

      trackFCTGEvar47or97Event(
        null,
        `${RAIL_EVENTS.FCTG.TG}${RAIL_EVENTS.FCTG.TRAVELER}${
          !index ? RAIL_EVENTS.FCTG.SELECTED : RAIL_EVENTS.FCTG.UN_SELECTED
        }`,
        RAIL_EVENTS.FCTG.TG,
      );
    }
  };

  const calculateAncillaryPrice = () => {
    if (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY) {
      return ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountedPremium;
    } else if (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY) {
      return ancillaryDiscountDetails?.ancillaryDetails?.tg?.discountedPremium;
    } else if (ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_TG_TOGETHER) {
      return ancillaryDiscountDetails?.ancillaryDetails?.tg?.discountedPremium;
    }
  };
  return (
    <View style={localStyles.container} testID="tg_options_container">
      {OPTIONS.map((option, index) => {
        const isSelected = option.key === TGP ? isTGPlusSelected : isTGSelected;
        let priceDisplay;
        if (
          (index === 0 &&
            ((ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY &&
              ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountedPremium) ||
              ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.TG_ONLY ||
              ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_TG_TOGETHER)) ||
          (option.key === TG &&
            tgDiscountPremium > 0 &&
          isTgDiscounted &&
            !ancillaryDiscountDetails?.ancillaryType)
        ) {
          priceDisplay = (
            <Text style={localStyles.priceRow} testID={`tg_option_${index}_price_discounted`}>
              <Text
                style={localStyles.strikeThroughPrice}
                testID={`tg_option_${index}_price_original`}
              >{`${CURRENCY_SYMBOL}${option.price}`}</Text>
              <Text
                style={localStyles.discountedPrice}
                testID={`tg_option_${index}_price_discounted_value`}
              >{`${CURRENCY_SYMBOL}${
                calculateAncillaryPrice() || tgDiscountPremium
              }${TG_PRICE_SUFFIX}`}</Text>
            </Text>
          );
        } else {
          priceDisplay = (
            <Text
              style={localStyles.discountedPrice}
              testID={`tg_option_${index}_price`}
            >{`${CURRENCY_SYMBOL}${option.price}${TG_PRICE_SUFFIX}`}</Text>
          );
        }
        return (
          <TouchableOpacity
            onPress={() => handleUserSelection(option.key, isSelected)}
            key={option.key}
            testID={`tg_option_${index}_touchable`}
          >
            <View
              style={[
                baseStyles.gradientContainer,
                localStyles.optionContainer,
                isSelected ? localStyles.selectedBackground : localStyles.unselectedBackground,
                isSelected ? localStyles.selectedBorder : localStyles.unselectedBorder,
              ]}
              testID={`tg_option_${index}_container`}
            >
              <View style={localStyles.contentContainer} testID={`tg_option_${index}_content`}>
                <View style={localStyles.titleRow} testID={`tg_option_${index}_title_row`}>
                  <View style={localStyles.titleRow2} testID={`tg_option_${index}_title_container`}>
                    <RadioButton
                      label=""
                      isSelected={isSelected}
                      radioSize={18}
                      onPress={() => handleUserSelection(option.key, isSelected)}
                      testIds={{ container: `tg_option_${index}_checkbox` }}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    />
                    <Image
                      source={option.title}
                      style={[
                        localStyles.titleImage,
                        option.key === TGP
                          ? localStyles.titleImageTgplus
                          : localStyles.titleImageTg,
                      ]}
                      testID={`tg_option_${index}_title_image`}
                    />
                  </View>
                  {priceDisplay}
                </View>
                <View style={localStyles.detailsRow} testID={`tg_option_${index}_details_row`}>
                  {option?.details?.map((detail, idx) => (
                    <View
                      key={idx}
                      style={localStyles.detailBulletRow}
                      testID={`tg_option_${index}_detail_${idx}`}
                    >
                      <Text
                        style={localStyles.detailBullet}
                        testID={`tg_option_${index}_bullet_${idx}`}
                      >
                        •
                      </Text>
                      <HTMLView
                        value={detail}
                        stylesheet={msgStyle}
                        testID={`tg_option_${index}_detail_text_${idx}`}
                      />
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default React.memo(TripGuaranteeOptions);
