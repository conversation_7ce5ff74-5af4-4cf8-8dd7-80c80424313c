import { ConfirmationGuaranteeWidget } from 'apps/rails/src/types/railofy.types';

export interface Refund {
  label: string;
  title: string;
  refund: number;
  description: string;
  refundMode: string;
}

export interface TgPlusOption {
  tgHeader: {
    add: string;
    tg: string;
  };
  tgpheader: {
    plus: string;
  };
  subHeader: {
    waitlistedRefund: string;
    racRefund: string;
    common: string;
    waitlistedRAC: string;
    waitlisted: string;
  };
}

export interface TgPlusConfig {
  header: string;
  subHeader: string[];
  refunds: Refund[];
  voucherUsage: string;
  toolTip: string;
  options: TgPlusOption[];
  toasts: string[];
  subTextVersion5: {
    tgText: string;
    tgPlusText: string;
  };
}

export interface TgPlusResponse {
  tncUrl: string;
  backgroundColor: string[];
  ticketFare: number;
  premiumPerPerson: number;
  refundCouponUpperCap: number;
  premiumUpperCap: number;
  totalTicketFare?: number;
}

export interface TgPlusProps {
  tripGuaranteeData: ConfirmationGuaranteeWidget;
  updateFCTGUserResponse: (index: number, premiumPerPerson: number) => void;
  numberOfPassengers: number;
  isTGSelected: boolean;
  isTGPlusSelected: boolean;
  tgPlusData: TgPlusResponse;
  tgpTextConfig: TgPlusConfig
}
