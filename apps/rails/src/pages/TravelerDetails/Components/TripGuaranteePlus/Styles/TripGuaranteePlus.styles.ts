import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
  gradientContainer: {
    padding: 12,
  },
  refundContainer: {
    flex: 1,
    height: 70,
    borderRadius: 8,
    borderColor: colors.lightGray,
    borderWidth: 1,
    flexDirection: 'row',
    overflow: 'hidden',
    marginHorizontal: 8,
  },
  refundLabelContainer: {
    transform: [{ rotate: '-90deg' }],
    position: 'absolute',
    left: -1,
    bottom: 0,
    top: 0,
  },
  refundLabel: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontWeight: '400',
    paddingTop: 2,
    paddingBottom: 4,
    width: 70,
    textAlign: 'center',
  },
  fareContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flexGrow: 1,
  },
  fareTitle: {
    fontSize: 12,
    color: colors.purple3,
    fontWeight: '900',
    marginBottom: 2,
  },
  fareTitle2: {
    fontSize: 12,
    color: colors.black,
    fontWeight: '900',
    marginBottom: 2,
  },
  fare: {
    fontSize: 12,
    color: colors.purple3,
    fontWeight: '900',
  },
  totalRefund: {
    fontSize: 14,
    color: colors.lightGreen16,
    fontWeight: '900',
  },
  fareDescription: {
    fontSize: 12,
    color: colors.defaultTextColor,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 2,
  },
  refundMode: {
    fontSize: 12,
    color: colors.purple3,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 2,
  },
  verticalGradient: {
    width: 20,
  },
  mainContainer: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 10,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    marginRight: 4,
    alignItems: 'center',
  },
  whiteText: {
    color: colors.white,
  },
  detailsContainer: {
    padding: 12,
    backgroundColor: colors.white,
  },
  refundInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  addIcon: {
    width: 20,
    height: 20,
  },
  voucherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  greyText: {
    color: colors.greyText1,
  },
  infoIcon: {
    marginLeft: 4,
    width: 12,
    height: 12,
  },
  seperator: {
    marginBottom: 12,
    marginTop: 6,
    borderColor: colors.lightGray,
    borderWidth: 0.5,
  },
  tgPlusIcon: {
    marginRight: 4,
    width: 30,
    height: 13,
  },
  infoButton: {
    width: 30,
    height: 30,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
});
