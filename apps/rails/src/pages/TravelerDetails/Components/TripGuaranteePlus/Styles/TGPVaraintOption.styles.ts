import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  infoIcon: {
    marginLeft: 4,
    width: 12,
    height: 12,
  },
  tgPlusIcon: {
    marginLeft: 4,
    width: 30,
    height: 13,
  },
  governorBayText: {
    color: colors.governorBay,
  },
  blackText: {
    color: colors.black,
  },
  defaultText: {
    color: colors.defaultTextColor,
  },
  greyText: {
    color: colors.greyText1,
  },
  purpleText: {
    color: colors.purple3,
  },
  whiteText: {
    color: colors.white,
  },
  marginRight4: {
    marginRight: 4,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  headerTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  waitListedRefundText: {
    color: colors.greyText1,
    marginBottom: 4,
  },
  flexRow: {
    flexDirection: 'row',
  },
  subHeaderView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  subHeaderView2: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subHeaderView3: {
    flexDirection: 'column',
  },
  marginLeft4: {
    marginLeft: 4,
  },
  arrowBottom: {
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopWidth: 8,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderTopColor: colors.lightGreen16,
  },
  toolTipContainer: {
    position: 'absolute',
    width: '100%',
    marginHorizontal: 12,
    bottom: 125,
    zIndex: 100,
  },
  toolTipInnerContainer: {
    backgroundColor: colors.lightGreen16,
    borderRadius: 8,
    padding: 8,
  },
  gradientContainer: {
    borderRadius: 12,
    padding: 6,
  },
  infoButton: {
    width: 30,
    height: 20,
  },
  paddingTop4: {
    paddingTop: 4,
  },
});
