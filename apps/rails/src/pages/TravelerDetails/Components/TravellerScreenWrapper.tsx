import React, { useEffect, ReactNode } from 'react';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { RAILS_ROUTE_KEYS } from '../../../navigation/railsPageKeys';
import { SCREENER_INTERACTIVE } from '@mmt/rails/src/Utils/RailsConstant';

interface Props {
  children: ReactNode;
  reviewStarted: boolean;
}

const TravellerScreenWrapper = ({ children, reviewStarted }: Props) => {
  const { updateState, startNextScreenTimer } = useScreenProfiler();

  useEffect(() => {
    if (reviewStarted) {
      startNextScreenTimer(RAILS_ROUTE_KEYS.railsReviewPage, Date.now());
    } else {
      updateState(SCREENER_INTERACTIVE);
    }
  }, [reviewStarted, startNextScreenTimer, updateState]);

  return <>{children}</>;
};

export default TravellerScreenWrapper;
