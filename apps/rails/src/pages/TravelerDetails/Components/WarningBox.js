import React from 'react';
import {Text} from 'react-native';
import PropTypes from 'prop-types';
import RailsCard from '../../Common/RailsCard';

const warningBoxDefaultPropCardStyle = {};
const warningBoxDefaultPropTextStyle = {};

const WarningBox = ({
  cardStyle = warningBoxDefaultPropCardStyle,
  textStyle = warningBoxDefaultPropTextStyle,
  value = '',
}) => (
  <RailsCard style={cardStyle}>
    <Text
      style={textStyle}
    >{value}
    </Text>
  </RailsCard>
);

WarningBox.propTypes = {
  cardStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  textStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  value: PropTypes.string,
};

export default WarningBox;
