import React from 'react';
import {
  Text,
  View,
  StyleSheet,
} from 'react-native';
import { RailsSimpleBottomSheet } from './RailsSimpleBottomSheet';
import { _label } from '../../../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface Props {
  buttonArray: [],
  outsideClick: () => void,
  content: [],
  logTravellerPageBottomSheetEvents: (eventValue: string) => void;
}

export const RailsSimpleBottomSheetContent = (props : Props) => {

  const {
    buttonArray,
    outsideClick,
    content,
    logTravellerPageBottomSheetEvents,
  } = props;

  return (
    <RailsSimpleBottomSheet
      buttons={buttonArray}
      customButtonStyles={styles.buttonStylesbottomsheet}
      onClosePress={outsideClick}
      logTravellerPageBottomSheetEvents={logTravellerPageBottomSheetEvents}
    >
      <View>
      {content?.map((text, index) => (
          <Text
            key={index}
            style={[
              styles.fontWeight700,
              styles.fontSize22,
              text.blackTextColorClass ? styles.blackText : styles.yellowText,
            ]}
          >
            {_label(text.line)}
          </Text>
        ))}
      </View>
    </RailsSimpleBottomSheet>
  );
};

const styles = StyleSheet.create({
  fontWeight700: {
    fontWeight: '700',
  },
  yellowText: {
    color: colors.yellowColor,
    marginBottom: 48,
  },
  fontSize22: {
    fontSize: 22,
  },
  marginBottom48: {
    marginBottom: 48,
  },
  marginBottom10: {
    marginBottom: 10,
  },
  blackText: {
    color: colors.black,
    marginBottom: 10,
  },
  buttonStylesbottomsheet: {
    flexDirection: 'row',
    fontWeight: '700',
    fontSize: 16,
    color: colors.azure,
  },
});
