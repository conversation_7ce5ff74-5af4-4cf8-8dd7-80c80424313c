import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import React, { ReactChild, ReactChildren, useEffect } from 'react';
import {
  GestureResponderEvent,
  Modal,
  Platform,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
  StyleProp,
  ViewStyle,
} from 'react-native';

interface Props {
  children: ReactChild | ReactChildren;
  buttons: [];
  customButtonStyles: StyleProp<ViewStyle>;
  customButtonContainerStyles: StyleProp<ViewStyle>,
  customBodyStyles: StyleProp<ViewStyle>;
  outsideClickEnable: boolean;
  onClosePress: () => void;
  logTravellerPageBottomSheetEvents: (eventValue: string) => void;
}

export const RailsSimpleBottomSheet = (props: Props) => {
  const {
    children,
    buttons,
    customButtonStyles,
    customButtonContainerStyles,
    customBodyStyles,
    onClosePress,
    logTravellerPageBottomSheetEvents,
  } = props;

  const onPressHandler = (e?: GestureResponderEvent) => {
    if (Platform.OS === 'web') {
      e?.preventDefault();
    }
    onClosePress && onClosePress();
  };

  useEffect(() => {
    if (logTravellerPageBottomSheetEvents) {
      logTravellerPageBottomSheetEvents(TRAVELLER_CLICK_EVENTS.RESISTANCE_SIMPLE_BOTTOM_SHEET);
    }
  }, [logTravellerPageBottomSheetEvents]);

  return (
    <Modal
      transparent
      animationType={'fade'}
      statusBarTranslucent
      onRequestClose={onPressHandler}
      ariaHideApp={false}
    >
      <TouchableWithoutFeedback onPress={onPressHandler}>
        <View style={styles.overlay} />
      </TouchableWithoutFeedback>
      <View style={styles.container}>
        <View style={[styles.textContainer, customBodyStyles]}>
          {children}
        </View>
        <View style={[styles.btnStyles, customButtonContainerStyles]}>
          {buttons?.map(eachButton => {
              return (
                <TouchableOpacity onPress={e => eachButton.func(e)} key={eachButton.title}>
                  <View>
                    <Text style={[styles.eachButton ,customButtonStyles]}>{eachButton?.title}</Text>
                  </View>
                </TouchableOpacity>
              );
            })
          }
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: colors.white,
    padding: 16,
    paddingBottom: 32,
    position: 'absolute',
    bottom: 0,
    width: '100%',
},
overlay: {
  position: 'absolute',
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
  backgroundColor: colors.lightBlack4,
},
btnStyles: {
  justifyContent: 'space-between',
  flexDirection: 'row',
},
});
