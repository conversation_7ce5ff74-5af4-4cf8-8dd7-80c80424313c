
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { View, TextInput, Text, Image, Keyboard, StyleSheet } from 'react-native';

import _isFunction from 'lodash/isFunction';
import _noop from 'lodash/noop';

import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../../../vernacular/VernacularUtils';
import { isAndroid } from '../../../../Utils/device';

import errorIconSrc from '@mmt/legacy-assets/src/error_red.webp';

export default class SimpleTextInput extends PureComponent {
  state = {
    hasFocus: false,
  };
  timerHandle = 0;

  componentDidMount(){
    this.timerHandle = setTimeout(() => {
      if (isAndroid() && this.inputRef && this.props.defaultOpenKeyboard) {
        this.inputRef.focus();
        this.timerHandle = 0;
      }
    }, 100);
  }
  componentWillUnmount = () => {
    // Is our timer running?
    if (this.timerHandle) {
        // Yes, clear it
        clearTimeout(this.timerHandle);
        this.timerHandle = 0;
    }
  };

  _onFocus = () => {
    const { onFocus } = this.props;
    this.setState({
      hasFocus: true,
    });
    if (_isFunction(onFocus)) {
      onFocus();
    }
  };

  _onBlur = () => {
    const { onBlur } = this.props;
    this.setState({
      hasFocus: false,
    });
    if (_isFunction(onBlur)) {
      onBlur();
    }
  };

  _onChangeText = (text) => {
    const { onChangeText } = this.props;
    if (_isFunction(onChangeText)) {
      onChangeText(text);
    }
  };

  render() {
    const {
      label,
      subLabel,
      placeholder,
      error,
      value,
      returnKeyType,
      autoCapitalize,
      spellCheck,
      autoCorrect,
      onSubmitEditing,
      onEndEditing,
      keyboardType,
      refProp,
      maxLength,
      errorIcon,
      placeholderTextColor,
      errorContainerStyle,
      inputStyle,
      disabled = false,
    } = this.props;
    const { hasFocus } = this.state;
    const additionalStyle = hasFocus ? styles.focussedInputStyle : styles.nonFocussedInputStyle;
    return (
      <View style={styles.container} ref={refProp}>
        <Text style={[styles.labelStyle, fontStyle('bold')]}>{label}<Text style={styles.subLabelStyle}>{subLabel}</Text></Text>
        <TextInput
          disabled={disabled}
          placeholder={placeholder}
          placeholderTextColor={placeholderTextColor}
          returnKeyType={returnKeyType}
          autoCapitalize={autoCapitalize}
          spellCheck={spellCheck}
          autoCorrect={autoCorrect}
          value={value}
          onSubmitEditing={onSubmitEditing}
          onChangeText={this._onChangeText}
          onFocus={this._onFocus}
          onBlur={this._onBlur}
          onEndEditing={onEndEditing}
          style={[styles.commonInputStyle, additionalStyle, ...inputStyle]}
          keyboardType={keyboardType}
          maxLength={maxLength}
          ref={(ref) => { this.inputRef = ref; }}
          testID={this.props?.id}
        />
        {Boolean(error) &&
        <View style={[styles.errorContainer, errorContainerStyle]}>
          {errorIcon && <Image source={errorIconSrc} style={styles.errorIcon} />}
          <Text style={styles.errorText}> {error} </Text>
        </View>
        }
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderWidth: 0.1,
    borderColor: colors.white,
  },
  commonInputStyle: {
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 16,
    height: 40,
    marginBottom: 20,
  },
  focussedInputStyle: {
    borderColor: colors.azure,
  },
  nonFocussedInputStyle: {
    borderColor: colors.lightTextColor,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: -10,
  },
  errorText: {
    color: colors.red,
  },
  labelStyle: {
    color: colors.black,
    marginBottom: 10,
  },
  subLabelStyle: {
    color: colors.lightTextColor,
    marginLeft: 5,
  },
});

SimpleTextInput.propTypes = {
  label: PropTypes.string,
  subLabel: PropTypes.string,
  placeholder: PropTypes.string,
  error: PropTypes.string,
  value: PropTypes.string,
  returnKeyType: PropTypes.oneOf(['done', 'go', 'next', 'search', 'send']),
  autoCapitalize: PropTypes.oneOf(['characters', 'words', 'sentences', 'none']),
  spellCheck: PropTypes.bool,
  autoCorrect: PropTypes.bool,
  onSubmitEditing: PropTypes.func,
  onEndEditing: PropTypes.func,
  keyboardType: PropTypes.string,
  onChangeText: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  refProp: PropTypes.any,

  maxLength: PropTypes.number,
  errorIcon: PropTypes.bool,
  placeholderTextColor: PropTypes.string,
  errorContainerStyle: PropTypes.object,
  inputStyle: PropTypes.object,
  id: PropTypes.string,
  disabled: PropTypes.bool,
  defaultOpenKeyboard: PropTypes.bool,
};

SimpleTextInput.defaultProps = {
  label: '',
  subLabel: '',
  placeholder: '',
  error: '',
  value: '',
  returnKeyType: 'next',
  autoCapitalize: 'words',
  spellCheck: false,
  autoCorrect: false,
  onSubmitEditing: () => Keyboard.dismiss(),
  keyboardType: 'default',
  onChangeText: _noop,
  onFocus: _noop,
  onBlur: _noop,
  refProp: null,
  errorIcon: false,
  onEndEditing: _noop,
  placeholderTextColor: colors.disabledButton,
  errorContainerStyle: {},
  inputStyle: {},
};
