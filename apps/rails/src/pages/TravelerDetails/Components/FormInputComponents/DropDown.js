import React, { Component, createContext } from 'react';
import {
  FlatList,
  Text,
  View,
  TouchableWithoutFeedback,
  StyleSheet, Keyboard, Image,
} from 'react-native';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';

import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';

const TravelerDropdownContext = createContext({
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  showDropDown: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onOptionSelected: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  hideDropDown: () => {},
});

class RailsDropDown extends Component {
  _captureRef = (ref) => {
    this._ref = ref;
  };

  render() {
    const { showDropDown, hideDropDown, onOptionSelected } = this.context || {};
    const {
      onSelect, options, overlayMode, error, errorMessage, maxHeight, disabled, hideDropDownIcon,
    } = this.props;
    return (
      <View nativeID="irctc_form_drop_down" ref={this._captureRef} style={{ borderRadius: 5 }} testID={this.props?.id}>
        <View>
          <TouchableRipple disabled={disabled} onPress={() => {
            Keyboard.dismiss();
            if (overlayMode) {
              return;
            }
            this._ref && this._ref.measureInWindow((x, y, w, h) => {
              const position = {
                x, y, w, h,
              };
              showDropDown(this.props, position, onSelect);
            });
          }}
          >
            <View style={{borderRadius: 5}}>
              { !overlayMode &&
              <View style={[style.container, disabled ? {borderColor: colors.greyBookedSeat,
                  backgroundColor: colors.grayBg} : this.props.error ? 
                  {borderColor: colors.red} : {borderColor: colors.primary}]}>
                <Text style={[style.innerText, fontStyle('regular'),
                   getLineHeight(14), {opacity: disabled ? 0.3 : 1}]}>
                  {isEmpty(this.props.selected) ? this.props.label : this.props.selected}
                </Text>
                <View style={{height: 40, width: 40, alignSelf: 'center', alignItems: 'center', justifyContent: 'center'}}>
                  { !hideDropDownIcon && <Image
                    source={arrowDown}
                    style={[style.arrow, disabled ? style.whitearrow : {}]}
                  />}
                </View>
              </View>
              }
            </View>
          </TouchableRipple>
          { !overlayMode && error &&
          <View style={{marginTop: 8}}>
            <Text style={[{color: colors.red, fontSize: 12}, fontStyle('regular'), getLineHeight(12)]}>
              {errorMessage}
            </Text>
          </View>
          }
        </View>
        {overlayMode &&
        <View style={{backgroundColor: colors.white, borderRadius: 5, paddingHorizontal: 16, paddingTop: 16}}>
          { !isEmpty(this.props.headerInfo) &&
          <Text style={{
            fontSize: 12,
           ...fontStyle('italic'),
           ...getLineHeight(12),
            fontStyle: 'italic',
            color: colors.lightTextColor,
            marginBottom: 16,
          }}>{this.props.headerInfo}</Text>
          }
          <Text
            style={
              {
                fontSize: 10,
                ...fontStyle('regular'),
                ...getLineHeight(10),
                color: colors.textGrey,
                marginBottom: 8,
                }
            }
          >
            {this.props.header.toUpperCase()}
          </Text>
          <View style={{maxHeight: maxHeight}}>
            <FlatList
              data={options}
              style={{backgroundColor: colors.white, borderRadius: 5}}
              keyboardShouldPersistTaps="handled"
              keyExtractor={item => item.id}
              renderItem={({item}) => {
                return (
                  <TouchableWithoutFeedback
                    onPress={(e) => {
                      e.preventDefault && e.preventDefault();
                      onOptionSelected(item);
                      hideDropDown();
                    }}
                    underlayColor="white"
                    activeOpacity={0.5}
                  >

                    <View key={item.id} style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      paddingVertical: 16,
                      paddingHorizontal: 2,
                      margin: 0,
                      backgroundColor: colors.white,
                      borderBottomWidth: StyleSheet.hairlineWidth,
                      borderBottomColor: colors.lightGrey14,
                    }}>
                      <Text style={{fontSize: 14, ...fontStyle('bold'), ...getLineHeight(14), color: colors.textGrey, fontWeight: 'bold'}}>{item.text}</Text>
                    </View>
                  </TouchableWithoutFeedback>
                );
              }}
            />
          </View>
        </View>
        }
      </View>
    );
  }
}


RailsDropDown.contextType = TravelerDropdownContext;
RailsDropDown.propTypes = {
  adjustPosition: PropTypes.bool,
  fixedPosition: PropTypes.object,
  overlayMode: PropTypes.bool,
  label: PropTypes.string.isRequired,
  selected: PropTypes.string,
  defaultText: PropTypes.string,
  onSelect: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  })).isRequired,
  hideDropDownIcon: PropTypes.bool,
  errorMessage: PropTypes.string,
  maxHeight: PropTypes.number,
  disabled: PropTypes.bool,
  id: PropTypes.string,
  error: PropTypes.bool,
  headerInfo: PropTypes.string,
  header: PropTypes.string,
  
};
RailsDropDown.defaultProps = {
  adjustPosition: true,
  fixedPosition: null,
  overlayMode: false,
  selected: '',
  hideDropDownIcon: false,
};
export default RailsDropDown;

const style = StyleSheet.create({
  container: {flex: 1, flexDirection: 'row', borderRadius: 5, borderWidth: StyleSheet.hairlineWidth * 1},
  innerText: {padding: 12, fontSize: 14,  color: colors.textGrey, flex: 1},
  arrow: {height: 24, width: 24},
  whitearrow: {tintColor: colors.white},
});
