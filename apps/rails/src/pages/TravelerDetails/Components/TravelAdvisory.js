
import React from 'react';
import PropTypes from 'prop-types';
import {Text, View} from 'react-native';
import { Actions } from '../../../navigation';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
// import railsConfig, { PRIVACY_POLICY_URL, USER_AGREEMENT_URL } from '../../../RailsConfig';

const TravelAdvisory = ({ travelAdvisoryDetails, id }) => {
  const knowMore = () => {
    Actions.openWebView({
      url: travelAdvisoryDetails.subHeadingLinkUrl,
      headerText: 'Travel Advisory',
      headerIcon: backIcon,
    });
  };

  return (
    <View style={{}} testID={id}>
      <View>
        <Text style={[styles.header, fontStyle('black'), getLineHeight(22)]}>{travelAdvisoryDetails.heading}</Text>
        <Text style={[styles.subText, fontStyle('bold')]}>{travelAdvisoryDetails.subHeading}
        </Text>
        {travelAdvisoryDetails.subHeadingLinkText &&
          <Text style={[styles.knowMore, fontStyle('black'), getLineHeight(12)]} onPress={knowMore} testID={`${id}_knowMore`}>
            {`${travelAdvisoryDetails.subHeadingLinkText}`}
          </Text>
        }
      </View>
    </View>

  );
};

const styles = {

  header: {
    fontSize: 22,
    color: '#000000',
    lineHeight: 32,
    marginBottom: 8,
  },
  subText: {
    marginBottom: 5,
    color: colors.black,
  },
  knowMore: {
    fontSize: 12,
    color: colors.azure,
  },
  textContainer: {
    paddingHorizontal: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  checkBoxText: {
    color: colors.black,
  },
  textContainerWeb: {
    paddingHorizontal: 12,
    // flexDirection: 'row',
    flexWrap: 'wrap',
    width: '80%',
  },
};

TravelAdvisory.propTypes = {
  travelAdvisoryDetails: PropTypes.object.isRequired,
  id: PropTypes.string.isRequired,
};

export default TravelAdvisory;
