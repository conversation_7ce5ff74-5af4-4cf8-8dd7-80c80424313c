import { StyleSheet, Platform } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: 16,
    marginVertical: 8,
    flex: 1,
    zIndex: 10,
  },
  loaderContainer: {
    backgroundColor: colors.white,
    justifyContent: 'center',
    height: 275,
  },
  bottomSheetContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    backgroundColor: colors.white,
    marginVertical: 0,
    flex: 0,
    paddingBottom: 38,
  },
  headerContainer: {
    flexDirection: 'row',
    marginBottom: 21,
  },
  optionWrapper: {
    borderRadius: 12,
  },
  gradientContainer: {
    flexDirection: 'row',
    overflow: 'hidden',
    padding: 12,
    borderRadius: 12,
    borderColor: colors.lightSilver,
    borderWidth: 1,
    marginBottom: 12,
  },
  rowContainer: {
    flexDirection: 'row',
    flex: 1,
    zIndex: 10,
    position: 'relative',
  },
  columnContainer: {
    flexDirection: 'column',
    flex: 1,
    zIndex: 10,
    position: 'relative',
  },
  optionContainer: {
    flex: 1,
    marginHorizontal: 8,
  },
  bnppIcon: {
    height: 24,
    width: 24,
    marginRight: 12,
  },
  closeIconContainer: {
    flex: 1,
  },
  closeIcon: {
    height: 24,
    width: 24,
    alignSelf: 'flex-end',
  },
  bnppStripImage: {
    height: 20,
    width: 20,
    marginRight: 12,
    resizeMode: 'contain',
  },
  bnppHeader: {
    color: colors.black,
    fontSize: 18,
    fontWeight: '900',
  },
  optionTitle: {
    color: colors.black,
    fontSize: 16,
    fontWeight: '700',
  },
  optionDescription: {
    color: colors.defaultTextColor,
    fontSize: 14,
    fontWeight: '400',
    marginTop: 4,
  },
  tncText: {
    color: colors.greyText1,
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '400',
    zIndex: -1,
  },
  tncBlueText: {
    color: colors.primary,
  },
  priceText: {
    color: colors.lightGreen16,
    fontSize: 16,
    fontWeight: '900',
  },
  stripContainer: {
    paddingLeft: 12,
    marginBottom: -2,
    justifyContent: 'flex-start',
    elevation: 0,
  },
  stripContainerBorder: {
    borderTopColor: colors.lightGrey16,
    borderBottomColor: colors.lightGrey16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    flexDirection: 'row',
    backgroundColor: colors.lightGreen1,
    paddingVertical: 4,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 0,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: -3,
    },
    shadowColor: colors.black,
    elevation: 4,
    marginBottom: 0,
  },
  stripText: {
    color: colors.lightGreen16,
    fontSize: 14,
    fontWeight: '700',
  },
  reviewBottomStripText: {
    color: colors.black,
    fontSize: 12,
    fontWeight: '400',
  },
  thankyouContainer: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  a: {
    color: colors.lightGreen16,
    fontSize: 14,
    fontWeight: '700',
    fontFamily: fonts.bold,
  },
  p: {
    color: colors.defaultTextColor,
    fontSize: 12,
    fontWeight: '400',
    fontFamily: fonts.regular,
  },
  b: {
    fontWeight: '900',
  },
  t: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '400',
  },
  h: {
    color: colors.defaultTextColor,
    fontSize: 14,
    fontWeight: '400',
    fontFamily: fonts.regular,
    lineHeight: 18,
    marginTop: 4,
  },
  hb: {
    fontWeight: '600',
  },
  hlb: {
    fontWeight: '500',
    color: colors.black,
  },
  infoIcon: {
    width: 16,
    height: 16,
  },
  toolTipContainer1: {
    width: '100%',
    marginHorizontal: 5,
    top: Platform.OS === 'android' ? -30 : -27,
    left: -5,
    bottom: 0,
    zIndex: 500,
    justifyContent: 'flex-start',
    color: colors.white,
    position: 'absolute',
  },
  toolTipContainer2: {
    width: '100%',
    marginHorizontal: 5,
    top: 36,
    left: -5,
    bottom: 0,
    justifyContent: 'flex-start',
    color: colors.white,
    position: 'absolute',
    overflow: 'visible',
    zIndex: 1000,
  },
  toolTipInnerContainer: {
    backgroundColor: colors.lightGreen16,
    borderRadius: 8,
    padding: 8,
    color: colors.white,
  },
  infoButton: {
    width: 30,
    height: 20,
    marginLeft: Platform.OS === 'android' ? 4 : 0,
    marginBottom: Platform.OS === 'android' ? -5 : -2,
  },
  paddingTop4: {
    paddingTop: 4,
  },
  whiteText: {
    color: colors.white,
  },
  arrowTop: {
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderBottomWidth: 8,
    borderTopWidth: 0,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.lightGreen16,
    // position: 'absolute',
  },
  bar: {
    margin: 0,
    marginVertical: 5,
    backgroundColor: colors.white,
    height: 1,
  },
  tooltipText: {
    display: 'flex',
    flexDirection: 'row',
    paddingVertical: 2,
    color: colors.white,
    justifyContent: 'space-between',
  },
  toolTipTextTitles: {
    wordWrap: 'wrap',
    color: colors.white,
    fontSize: 14,
  },
  toolTipPrice: {
    alignSelf: 'flex-end',
    color: colors.white,
    fontSize: 14,
  },
  tooltipTextTotalPayable: {
    display: 'flex',
    flexDirection: 'row',
    paddingVertical: 2,
    color: colors.white,
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  tooltipTextPartialTicketFare: {
    display: 'flex',
    flexDirection: 'row',
    paddingVertical: 2,
    color: colors.white,
    justifyContent: 'space-between',
    marginVertical: 5,
  },
  toolTipTotalPayable: {
    wordWrap: 'wrap',
    color: colors.white,
    fontSize: 14,
    fontWeight: '700',
  },
  greenText: {
    backgroundColor: colors.lightGreen1,
    marginTop: 13,
    fontSize: 12,
    fontWeight: '400',
    padding: 7,
    borderRadius: 8,
  },
  radioStyles: {
    alignSelf: 'flex-start',
  },
});
