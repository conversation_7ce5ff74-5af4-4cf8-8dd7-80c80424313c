export interface BNPP {
  apWindow?: number;
  cutOffPaymentTimeinHours?: number;
  moreInfoUrl?: string;
  bnppTitle?: string;
  bnppIcon?: string;
  traveler?: Traveler;
  review?: Review;
  thankyou?: Thankyou;
}

export interface Traveler {
  title: string;
  iconUrl: string;
  options: Option[];
  bottomBar?: BottomBar;
  terms: string;
  termsCTA: string;
  bottomStripText: string;
}

export interface Option {
  title: string;
  description?: string;
  descriptionWhenFC?: string;
  descriptionWhenFCSelected?: string;
}

export interface BottomBar {
  fullText?: string
  partialText?: string
  ctaText?: string
}

export interface Review {
  stripText: string;
  bottomStripText: string;
}

export interface Thankyou {
  description: string;
  iconUrl: string;
}

export interface BnppResponse {
  partialAmount: number;
  fullAmount: number;
  dueDate?: string;
  fcDueDate?: string;
  fcDueHour?: number;
  paymentFareBreakup?: PaymentFareBreakup[];
}

export interface PaymentFareBreakup {
  mainText?: string;
  amount?: number;
}
