import React from 'react';
import { Text, View } from 'react-native';
import { styles } from './styles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';

const rupee = '₹';

function ToolTip({
  paymentFareBreakup,
  basePrice,
  totalPrice,
  containerType,
  marginLeft,
}: {
  paymentFareBreakup: unknown;
  basePrice: number;
  totalPrice: number;
  containerType: number;
  marginLeft: number;
}) {
  const headerText = containerType === 1 ? _label('ticket_fare') : _label('partial_ticket_fare');

  return (
    <View
      style={containerType === 1 ? styles.toolTipContainer1 : styles.toolTipContainer2}
      testID="tool_tip_container"
    >
      <View style={[styles.arrowTop, { marginLeft }]} />
      <View style={styles.toolTipInnerContainer} testID="tool_tip_inner_container">
        <View style={styles.tooltipTextPartialTicketFare} testID="tool_tip_partial_ticket_fare">
          <Text
            style={[styles.toolTipTextTitles, { fontWeight: '700' }]}
            testID="tool_tip_text_titles"
          >
            {headerText}
          </Text>
          <Text
            style={[styles.toolTipPrice, { fontWeight: '700' }]}
            testID="tool_tip_price"
          >{`${rupee} ${basePrice}`}</Text>
        </View>
        <View style={styles.tooltipText} testID="tool_tip_text">
          <Text style={styles.toolTipTextTitles} testID="tool_tip_text_titles">
            {paymentFareBreakup[0].mainText}
          </Text>
          <Text style={styles.toolTipPrice} testID="tool_tip_price">
            {`${rupee} ${paymentFareBreakup[0].amount}`}
          </Text>
        </View>
        <View style={styles.tooltipText} testID="tool_tip_text">
          <Text style={styles.toolTipTextTitles} testID="tool_tip_text_titles">
            {paymentFareBreakup[1].mainText}
          </Text>
          <Text
            style={styles.toolTipPrice}
            testID="tool_tip_price"
          >{`${rupee} ${paymentFareBreakup[1].amount}`}</Text>
        </View>
        <View style={styles.bar} />
        <View style={styles.tooltipTextTotalPayable} testID="tool_tip_total_payable">
          <Text style={styles.toolTipTotalPayable} testID="tool_tip_total_payable_text">
            {_label('total_payable')}
          </Text>
          <Text
            style={[styles.toolTipPrice, { fontWeight: '700' }]}
            testID="tool_tip_total_payable_price"
          >{`${rupee} ${totalPrice}`}</Text>
        </View>
      </View>
    </View>
  );
}

export default ToolTip;
