export const calculateBNPPFare = (
  partialAmount: number,
  fullAmount: number,
  numberOfPassengers: number,
  listingTotalFare = 0,
) => {
  const totalPartialAmount = partialAmount * numberOfPassengers;
  let totalFullAmount = fullAmount * numberOfPassengers;
  if (!totalFullAmount && listingTotalFare ) {
    totalFullAmount = listingTotalFare * numberOfPassengers;
  }
  return { totalPartialAmount, totalFullAmount };
};
