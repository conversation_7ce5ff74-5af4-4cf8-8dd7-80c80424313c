import React from 'react';
import { Text, TouchableOpacity, View, Image, ScrollView } from 'react-native';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import { styles } from './styles';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import {
  DUE_DATE_REPLACER,
  FC_DUE_DATE,
  FC_DUE_HOUR,
  PAY_CUT_OFF_REPLACER,
  PRICE_REPLACER,
  TEST_ID_CONSTANTS,
  REMAINING_AMOUNT,
} from 'apps/rails/src/Utils/RailsConstant';
import HTMLView from 'react-native-htmlview';
import { PaymentFareBreakup } from './types';
import { railofyValuesTrackingParams } from '../../../Review/RailsReviewActions';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

import infoGreyIcon from '@mmt/legacy-assets/src/info_grey.webp';

interface BookNowOptionProps {
  title?: string;
  description?: string | null;
  descriptionWhenFCSelected?: string | null;
  paymentCutOffTimeInHours?: number;
  price?: number;
  checked: boolean;
  dueDate?: string;
  fcDueDate?: string;
  fcDueHour?: number;
  onBNPPOptionSelected: (bnppOpted: boolean) => void;
  payingPartial: boolean;
  showTooltipPokus: boolean;
  paymentFareBreakup?: PaymentFareBreakup[];
  toggleTooltip: () => void;
  descriptionToolTip?: string;
  remainingAmount: number;
  numberOfPassengers: number;
  isBottomSheet?: boolean;
  logTravellerPageAddOnEvents: (railofyValuesTrackingParams: unknown, eventValue: string) => void;
}

const BookNowOption = (props: BookNowOptionProps) => {
  const {
    checked,
    title,
    description,
    descriptionWhenFCSelected,
    price,
    paymentCutOffTimeInHours,
    dueDate,
    fcDueDate,
    fcDueHour,
    onBNPPOptionSelected,
    payingPartial,
    showTooltipPokus,
    paymentFareBreakup,
    toggleTooltip,
    descriptionToolTip,
    remainingAmount,
    numberOfPassengers,
    isBottomSheet,
  } = props;
  const titleArray = title?.split(/(₹{.*?})/);
  const toggleBnpp = () => {
    const event = isBottomSheet
      ? payingPartial
        ? TRAVELLER_CLICK_EVENTS.SEAT_LOCK_BOTTOM_SHEET_PAY_PARTIAL
        : TRAVELLER_CLICK_EVENTS.SEAT_LOCK_BOTTOM_SHEET_PAY_FULL
      : payingPartial
      ? TRAVELLER_CLICK_EVENTS.SEAT_LOCK_WIDGET_PAY_PARTIAL
      : TRAVELLER_CLICK_EVENTS.SEAT_LOCK_WIDGET_PAY_FULL;
    props?.logTravellerPageAddOnEvents(railofyValuesTrackingParams, event);

    onBNPPOptionSelected(payingPartial);
  };
  const testID = `${TEST_ID_CONSTANTS.TRAVELLER_DETAILS}bnpp_option_${
    payingPartial ? 'partial' : 'full'
  }`;

  const totalPriceWithServiceCharges =
    price + (paymentFareBreakup?.[0]?.amount ?? 0) + (paymentFareBreakup?.[1]?.amount ?? 0);
  const finalPrice = showTooltipPokus ? totalPriceWithServiceCharges : price;

  const leftMargin =
    Math.floor(finalPrice)?.toString().length <= 3
      ? 7
      : Math.floor(finalPrice)?.toString().length === 4
      ? 10
      : 12;

  return (
    <TouchableOpacity
      style={checked && payingPartial && !isBottomSheet ? styles.columnContainer : styles.rowContainer}
      onPress={toggleBnpp}
      testID={testID}
    >
      <View style={styles.rowContainer} testID={`${testID}_row_container`}>
        <RadioButton
          isSelected={checked}
          onPress={toggleBnpp}
          radioSize={20}
          customStyle={styles.radioStyles}
          testID={`${testID}_radio_button`}
        />
        <ScrollView style={styles.optionContainer} testID={`${testID}_scroll_view`}>
          {titleArray?.length && titleArray?.length > 1 && (
            <Text
              style={[styles.optionTitle, fontStyle('black')]}
              testID={`${testID}_option_title`}
            >
              {titleArray[0]}
              <Text
                style={[styles.priceText, fontStyle('bold')]}
                testID={`${testID}_price_text`}
              >{` ${titleArray[1]?.replace(
                PRICE_REPLACER,
                String(Math.floor(Number(finalPrice))),
              )}`}</Text>
              {showTooltipPokus && (
                <TouchableOpacity
                  style={[styles.infoButton, styles.paddingTop4]}
                  onPress={() => toggleTooltip()}
                  testID={`${testID}_info_button`}
                >
                  <Image
                    source={infoGreyIcon}
                    style={[styles.infoIcon, { marginLeft: leftMargin }]}
                    testID={`${testID}_info_icon`}
                  />
                </TouchableOpacity>
              )}
              {titleArray[2]}
            </Text>
          )}
          {description && (
            <HTMLView
              stylesheet={styles}
              value={
                showTooltipPokus
                  ? descriptionToolTip
                      ?.replace(DUE_DATE_REPLACER, dueDate ?? '')
                      .replace(REMAINING_AMOUNT, String(remainingAmount * numberOfPassengers ?? ''))
                  : description
                      .replace(DUE_DATE_REPLACER, dueDate ?? '')
                      .replace(PAY_CUT_OFF_REPLACER, String(paymentCutOffTimeInHours ?? ''))
              }
              testID={`${testID}_html_view`}
            />
          )}
          {descriptionWhenFCSelected && (
            <HTMLView
              stylesheet={styles}
              value={descriptionWhenFCSelected
                .replace(DUE_DATE_REPLACER, dueDate ?? '')
                .replace(FC_DUE_DATE, fcDueDate ?? '')
                .replace(FC_DUE_HOUR, String(fcDueHour ?? ''))}
              testID={`${testID}_html_view`}
            />
          )}
        </ScrollView>
      </View>
      {checked && payingPartial && !isBottomSheet && (
        <Text style={styles.greenText} testID={`${testID}_green_text`}>
          {_label('bnpp_great_choice')}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default BookNowOption;
