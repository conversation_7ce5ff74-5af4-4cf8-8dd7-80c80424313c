import React, { useEffect, useMemo, useState } from 'react';
import { Image, LayoutAnimation, Platform, Text, UIManager, View } from 'react-native';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  getIfCanShowBNPP,
  getIfCanShowNewBNPPTagWhenFC,
  getShowBNPPTooltip,
  getSeatLockReviewVariants,
} from 'apps/rails/src/RailsAbConfig';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { Actions } from 'apps/rails/src/navigation';
import {
  setBnppInteracted,
  setBnppSetSelection,
} from 'apps/rails/src/pages/TravelerDetails/TravelerDetailsActions';
import {
  trackTravellerPageEvent,
  trackGenericEvar47or97Event,
  removeEventFromEvar47or97Variable,
} from 'apps/rails/src/railsAnalytics';
import _isEmpty from 'lodash/isEmpty';
import LinearGradient from 'react-native-linear-gradient';
import { connect } from 'react-redux';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import NewTag from '../../../Common/NewTag';
import BookNowOption from './BookNowOption';
import { styles } from './styles';
import { BNPP, BnppResponse } from './types';
import { calculateBNPPFare } from './utils';
import { getTravelersListFromId } from '../../Containers/RailsTravelerRepository';
import filter from 'lodash/filter';
import ToolTip from './ToolTip';
import closeIcon from '@mmt/legacy-assets/src/reviewClose.webp';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  REVIEW_PAGE_TRACKING_KEY,
  SEATLOCK_REVIEW_TRACKING_EVENTS,
  PAY_CUT_OFF_REPLACER,
} from 'apps/rails/src/Utils/RailsConstant';
import { logTravellerPageAddOnEvents } from '../../../Review/RailsReviewActions';

interface BookNowPayPartialProps {
  isInBottomSheet?: boolean;
  isBnppOpted: boolean;
  setBnppSetSelection: (isBnppOpted: boolean) => void;
  closeBottomSheet?: () => void;
  bnppResponse?: BnppResponse;
  isLoading?: boolean;
  numberOfPassengers: number;
  bnppShownInListing: boolean;
  isFCSelected?: boolean;
  bnppVariant?: number;
  bnppInteracted: boolean;
  setBnppInteracted: (bnppInteracted: boolean) => void;
  logTravellerPageAddOnEvents: (railofyValuesTrackingParams: unknown, eventValue: string) => void;
}
  /* eslint-disable */
const BookNowPayPartial = (props: BookNowPayPartialProps) => {
  const {
    isBnppOpted,
    bnppResponse,
    isLoading,
    setBnppSetSelection,
    numberOfPassengers,
    isFCSelected,
    bnppVariant,
    setBnppInteracted,
  } = props;
  const [showToolTipPartial, setShowToolTipPartial] = useState(false);
  const [showToolTipFull, setShowToolTipFull] = useState(false);

  const canShowBNPP = useMemo(() => getIfCanShowBNPP(), []);
  if (!canShowBNPP) {
    return null;
  }

  const bnppConfigData = useConfigStore(configKeys.RAILS_BNPP_CONFIG);
  const bnppConfigDataJson: BNPP = !_isEmpty(bnppConfigData) ? JSON.parse(bnppConfigData) : {};
  const { traveler, cutOffPaymentTimeinHours } = bnppConfigDataJson;
  const { isInBottomSheet } = props;
  const {
    fullAmount,
    partialAmount,
    dueDate,
    fcDueDate,
    fcDueHour,
    paymentFareBreakup,
    remainingAmount,
  } = bnppResponse || {};
  let { totalPartialAmount, totalFullAmount} = useMemo(
    () => calculateBNPPFare(partialAmount, fullAmount, numberOfPassengers),
    [partialAmount, fullAmount, numberOfPassengers],
  );

  if (bnppVariant !== 1) {
    totalPartialAmount = bnppResponse?.partialAmount ?? 0;
    totalFullAmount = bnppResponse?.fullAmount ?? 0;
  }

  const showFCDueDate = getIfCanShowNewBNPPTagWhenFC() && isFCSelected && !_isEmpty(fcDueDate);
  const showBNPPtooltip = getShowBNPPTooltip();

  const totalPriceWithCharges =
    totalFullAmount +
    (paymentFareBreakup?.[0]?.amount ?? 0) +
    (paymentFareBreakup?.[1]?.amount ?? 0);
  const partialPriceWithCharges =
    totalPartialAmount +
    (paymentFareBreakup?.[0]?.amount ?? 0) +
    (paymentFareBreakup?.[1]?.amount ?? 0);

  if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }

  useEffect(() => {
    if (bnppResponse?.fullAmount) {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    }
  }, [bnppResponse?.fullAmount]);

  useEffect(() => {
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_BNPP_TOOLTIP_VISIBLE);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_BNPP_TOOLTIP_HIDDEN);

    const pageName = bnppVariant === 1 ? TRAVELERS_PAGE_TRACKING_KEY_NEW : REVIEW_PAGE_TRACKING_KEY;
    if (showBNPPtooltip) {
      trackGenericEvar47or97Event(pageName, RAIL_EVENTS.TRAVELLER.RAILS_BNPP_TOOLTIP_VISIBLE);
    } else {
      trackGenericEvar47or97Event(pageName, RAIL_EVENTS.TRAVELLER.RAILS_BNPP_TOOLTIP_HIDDEN);
    }
  }, []);

  const toggleBnpp = (bnppOpted: boolean) => {
    if (bnppOpted === isBnppOpted) {
      return;
    }
    setBnppSetSelection(bnppOpted);
    const eventName = isInBottomSheet
      ? RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_BNPP_BOTTOM_SHEET
      : RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_BNPP_ONPAGE;
    trackTravellerPageEvent(`${eventName}_${bnppOpted ? 'yes' : 'no'}`);

    if (bnppVariant !== 1) {
      if (bnppOpted) {
        trackClickEventProp61(
          REVIEW_PAGE_TRACKING_KEY,
          SEATLOCK_REVIEW_TRACKING_EVENTS.SEATLOCK_REVIEW_OPTED,
        );
      } else {
        trackClickEventProp61(
          REVIEW_PAGE_TRACKING_KEY,
          SEATLOCK_REVIEW_TRACKING_EVENTS.SEATLOCK_REVIEW_NOT_OPTED,
        );
      }
    }
    setBnppInteracted(true);
  };
  const openTnC = () => {
    Actions.openWebView({
      url: bnppConfigDataJson?.moreInfoUrl,
      headerText: bnppConfigDataJson?.bnppTitle,
      headerIcon: backIcon,
    });
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLERS_BNPP_TNC);
    if (bnppVariant !== 1) {
      trackClickEventProp61(
        REVIEW_PAGE_TRACKING_KEY,
        SEATLOCK_REVIEW_TRACKING_EVENTS.SEATLOCK_REVIEW_TNC_CLICK,
      );
    }
  };

  if (!props.bnppShownInListing || _isEmpty(bnppConfigData) || isLoading || !(fullAmount > 0)) {
    return null;
  }

  const handleFullPriceTooltipClick = () => {
    setShowToolTipPartial(false);
    setShowToolTipFull(!showToolTipFull);
    if (!showToolTipFull) {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.RAILS_BNPP_INFO_ICON_FULL_CLICK,
      );
    }
  };

  const handlePartialPriceTooltipClick = () => {
    setShowToolTipFull(false);
    setShowToolTipPartial(!showToolTipPartial);
    if (!showToolTipPartial) {
      trackClickEventProp61(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        RAIL_EVENTS.TRAVELLER.RAILS_BNPP_INFO_ICON_PARTIAL_CLICK,
      );
    }
  };

  return (
    <View style={[styles.container, isInBottomSheet && styles.bottomSheetContainer]} testID="bnpp_bottomsheet_container">
      <View style={styles.headerContainer} testID="bnpp_bottomsheet_header">
        <Text style={[styles.bnppHeader, fontStyle('bold')]}>{traveler?.title}</Text>
        {isInBottomSheet && (
          <View style={styles.closeIconContainer} testID="bnpp_bottomsheet_close_container">
            <TouchableRipple onPress={props.closeBottomSheet} testID="bnpp_bottomsheet_close_button">
              <Image style={styles.closeIcon} source={closeIcon} testID="bnpp_bottomsheet_close_icon" />
            </TouchableRipple>
          </View>
        )}
      </View>
      <View style={styles.gradientContainer} testID="bnpp_bottomsheet_full_option_container">
        <BookNowOption
          checked={!isBnppOpted}
          title={traveler?.options[0]?.title}
          price={totalFullAmount}
          onBNPPOptionSelected={toggleBnpp}
          payingPartial={false}
          showTooltipPokus={showBNPPtooltip}
          paymentFareBreakup={bnppResponse?.paymentFareBreakup}
          toggleTooltip={handleFullPriceTooltipClick}
          numberOfPassengers={numberOfPassengers}
          logTravellerPageAddOnEvents={props.logTravellerPageAddOnEvents}
        />
      </View>
      <View style={styles.optionWrapper} testID="bnpp_bottomsheet_partial_option_wrapper">
        <LinearGradient
          colors={[colors.white, '#CEF1EB']}
          start={{ x: 0.92, y: 1 }}
          end={{ x: Platform.OS === 'ios' ? 1.8 : 1, y: 0 }}
          style={styles.gradientContainer}
          testID="bnpp_bottomsheet_partial_gradient"
        >
          <BookNowOption
            checked={isBnppOpted}
            title={traveler?.options[1]?.title}
            price={totalPartialAmount}
            paymentCutOffTimeInHours={cutOffPaymentTimeinHours}
            description={showFCDueDate ? null : traveler?.options[1]?.descriptionWhenFC}
            descriptionWhenFCSelected={
              showFCDueDate ? traveler?.options[1]?.descriptionWhenFCSelected : null
            }
            onBNPPOptionSelected={toggleBnpp}
            dueDate={dueDate}
            fcDueDate={fcDueDate}
            fcDueHour={fcDueHour}
            payingPartial={true}
            showTooltipPokus={showBNPPtooltip}
            paymentFareBreakup={bnppResponse?.paymentFareBreakup}
            toggleTooltip={handlePartialPriceTooltipClick}
            descriptionToolTip={
              showBNPPtooltip ? traveler?.options[1]?.descriptionWhenTooltip : null
            }
            remainingAmount={remainingAmount}
            numberOfPassengers={numberOfPassengers}
            isBottomSheet={isInBottomSheet}
            logTravellerPageAddOnEvents={props.logTravellerPageAddOnEvents}
          />
        </LinearGradient>
        <NewTag />
        {showToolTipFull && (
          <ToolTip
            paymentFareBreakup={bnppResponse?.paymentFareBreakup}
            basePrice={totalFullAmount}
            totalPrice={totalPriceWithCharges}
            containerType={1}
            marginLeft={totalFullAmount?.toString().length > 3 ? 255 : 247}
          />
        )}
        {showToolTipPartial && (
          <ToolTip
            paymentFareBreakup={bnppResponse?.paymentFareBreakup}
            basePrice={totalPartialAmount}
            totalPrice={partialPriceWithCharges}
            containerType={2}
            marginLeft={totalPartialAmount?.toString().length > 3 ? 264 : 254}
          />
        )}
      </View>
      {(isBnppOpted || Boolean(isInBottomSheet)) && (
        <Text style={[styles.tncText, fontStyle('regular')]} testID="bnpp_bottomsheet_terms_text">
          {traveler?.terms?.replace(PAY_CUT_OFF_REPLACER, String(cutOffPaymentTimeinHours ?? ''))}
          <Text onPress={openTnC} style={styles.tncBlueText} testID="bnpp_bottomsheet_terms_link">
            {` ${traveler?.termsCTA}`}
          </Text>
        </Text>
      )}
    </View>
  );
};

const mapStateToProps = (state: unknown) => {
  const {
    railsTraveler: {
      isBnppOpted = false,
      railofy: {
        bnppResponse: bnppResponseTraveller = {},
        freeCancellationInsuranceOpted,
      } = {},
      selectedTravelers = [],
      travelers,
      bnppInteracted = false,
    } = {},
    railsListing: { showBnpp = false } = {},
    railsReview: { bnppResponse: bnppResponseReview = {} } = {},
  } = state;

  let {
    railsTraveler: {
    railofy: {
       isLoading
      } = {},
    } = {},
  } = state;
  
  const selectedTravelersList = getTravelersListFromId(selectedTravelers, travelers);
  const numberOfPassengers = filter(
    selectedTravelersList,
    (selectedTraveler) => selectedTraveler?.basicInfo?.age > 4,
  );

  const bnppVariant = Number(getSeatLockReviewVariants());

  let bnppResponse = {};
  if (bnppVariant === 1) {
    bnppResponse = bnppResponseTraveller;
  } else {
    bnppResponse = bnppResponseReview;
  }

  if (!_isEmpty(bnppResponseReview)) {
    isLoading = false;
  }
  return {
    isBnppOpted,
    bnppResponse,
    isFCSelected: freeCancellationInsuranceOpted,
    isLoading,
    numberOfPassengers: Math.max(numberOfPassengers?.length, 1),
    bnppShownInListing: showBnpp,
    bnppVariant,
    bnppInteracted,
  };
};

const mapDispatchToProps = (dispatch: unknown) => ({
  setBnppSetSelection: (isBnppOpted: boolean) => dispatch(setBnppSetSelection(isBnppOpted)),
  setBnppInteracted: (bnppInteracted: boolean) => dispatch(setBnppInteracted(bnppInteracted)),
  logTravellerPageAddOnEvents: (railofyValuesTrackingParams: unknown, eventValue: unknown) =>
    dispatch(logTravellerPageAddOnEvents(railofyValuesTrackingParams, eventValue)),
});

export default connect(mapStateToProps, mapDispatchToProps)(BookNowPayPartial);
