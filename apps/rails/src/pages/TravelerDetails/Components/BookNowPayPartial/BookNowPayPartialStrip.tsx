import React from 'react';
import { Image, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import { styles } from './styles';
import useConfigStore from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { BNPP } from 'apps/rails/src/pages/TravelerDetails/Components/BookNowPayPartial/types';
import HTMLView from 'react-native-htmlview';
import _isEmpty from 'lodash/isEmpty';
import {
  DUE_AMOUNT_REPLACER,
  DUE_DATE_REPLACER,
  PRICE_REPLACER,
} from 'apps/rails/src/Utils/RailsConstant';

interface BookNowPayPartialStripProps {
  text?: string;
  iconUrl?: string;
  type?: 'TRAVELLER' | 'REVIEW' | 'REVIEW_BOTTOM' | 'THANK_YOU';
  price?: number;
  dueDate?: string;
  dueAmount?: number;
}

const BookNowPayPartialStrip = (props: BookNowPayPartialStripProps) => {
  const { type, text, iconUrl, price, dueDate, dueAmount } = props;
  let stripText = text;
  let logo = iconUrl;

  const bnppConfigData = useConfigStore(configKeys.RAILS_BNPP_CONFIG);
  const bnppConfigDataJson: BNPP = !_isEmpty(bnppConfigData) ? JSON.parse(bnppConfigData) : {};
  const { traveler, review, bnppIcon, thankyou } = bnppConfigDataJson;
  if (type === 'REVIEW') {
    stripText = review?.stripText;
    logo = bnppIcon;
  } else if (type === 'REVIEW_BOTTOM') {
    stripText = review?.bottomStripText;
  } else if (type === 'THANK_YOU') {
    stripText = thankyou?.description;
    logo = thankyou?.iconUrl;
  } else {
    stripText = traveler?.bottomStripText;
  }

  if (!type || !stripText) {
    return null;
  }

  if (type === 'THANK_YOU') {
    return (
      <View style={styles.thankyouContainer} testID="book_now_pay_partial_strip_thankyou_container">
        <Image
          style={styles.bnppStripImage}
          source={{ uri: logo }}
          testID="book_now_pay_partial_strip_thankyou_image"
        />
        <HTMLView
          value={stripText
            ?.replace(DUE_AMOUNT_REPLACER, String(dueAmount))
            .replace(DUE_DATE_REPLACER, String(dueDate))}
          stylesheet={styles}
          testID="book_now_pay_partial_strip_thankyou_html_view"
        />
      </View>
    );
  }

  return (
    <LinearGradient
      colors={
        type === 'REVIEW'
          ? [colors.white, '#CEF1EB']
          : type === 'REVIEW_BOTTOM'
          ? [colors.creamWhite, colors.creamWhite]
          : ['#E6FFF9', '#E6FFF9']
      }
      start={{ x: 0.99, y: 1.5 }}
      end={{ x: 1, y: 0 }}
      style={[styles.stripContainerBorder, type === 'REVIEW' && styles.stripContainer]}
      testID="book_now_pay_partial_strip_linear_gradient"
    >
      {stripText &&
        (type !== 'REVIEW_BOTTOM' ? (
          <>
            <Image
              style={styles.bnppStripImage}
              source={{ uri: logo }}
              testID="book_now_pay_partial_strip_image"
            />
            <HTMLView
              value={stripText?.replace(PRICE_REPLACER, String(price))}
              stylesheet={styles}
              testID="book_now_pay_partial_strip_html_view"
            />
          </>
        ) : (
          <HTMLView
            value={stripText
              ?.replace(DUE_AMOUNT_REPLACER, String(dueAmount))
              .replace(DUE_DATE_REPLACER, String(dueDate))}
            stylesheet={styles}
            testID="book_now_pay_partial_strip_html_view"
          />
        ))}
    </LinearGradient>
  );
};

export default BookNowPayPartialStrip;
