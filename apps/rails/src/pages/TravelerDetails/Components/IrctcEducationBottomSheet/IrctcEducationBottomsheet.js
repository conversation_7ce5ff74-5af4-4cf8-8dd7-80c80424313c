import React from 'react';
import { Text, View, Modal, TouchableOpacity, Image } from 'react-native';
import PropTypes from 'prop-types';
import styles from './IrctcPassEducationBottomsheetStyles';
import { getRailsIrctcAccountToast } from 'apps/rails/src/RailsAbConfig';
import { Actions } from 'apps/rails/src/navigation/railsNavigation';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { fontStyle, getLineHeight } from '/apps/rails/src/vernacular/VernacularUtils';
import { _label } from '/apps/rails/src/vernacular/AppLanguage';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getConfigStore } from '@mmt/rails/src/configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '@mmt/rails/src/configStore/Common/constants';
import { trackClickEventProp61 } from '@mmt/rails/src/pages/RailsBusHomePage/Analytics';
import { TRAVELERS_PAGE_TRACKING_KEY_NEW } from '@mmt/rails/src/Utils/RailsConstant';
import RAIL_EVENTS from '@mmt/rails/src/RailsOmnitureTracker';

import tick from '@mmt/legacy-assets/src/black_Tick.webp';

class InfoBottomSheet extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      BottomSheetConfig: {},
    };
  }

  async componentDidMount() {
    const BottomSheetConfig = await getConfigStore(configKeys.RAILS_TRAVELLER_IRCTC_BS_CONFIG);
    this.setState({ BottomSheetConfig });
  }

  _onCreateNewClick = () => {
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLERS_IRCTC_CREATE_NOW,
    );
    const showIrctcAccountCreatingToast = getRailsIrctcAccountToast();
    this.props.onClose();
    if (showIrctcAccountCreatingToast) {
      return Actions.openCreateIRCTCAccountWebView({
        from: 'travelers',
      });
    }

    Actions.railsCreateAccountPage({
      from: this.props.from,
      probableTo: this.props.probableTo,
      nextPageProps: this.props.nextPageProps,
    });
  };

  render() {
    const { isVisible, onClose } = this.props;
    const { BottomSheetConfig } = this.state;
    return (
      <Modal
        transparent
        visible={isVisible}
        onRequestClose={onClose}
        animationType="slide"
        testID="irctc_education_bottomsheet_modal"
      >
        <View style={styles.background}>
          <View style={styles.bottomSheetContainer} testID="irctc_education_bottomsheet_container">
            <View style={styles.headerContainer}>
              <Text
                style={[
                  fontStyle('black'),
                  { fontWeight: 'bold', fontSize: 18, color: colors.black },
                ]}
              >
                {_label('irctc_education_info')}
              </Text>
              <TouchableOpacity onPress={onClose}>
                <Image source={ASSETS.crossIcon} style={styles.crossIcon} />
              </TouchableOpacity>
            </View>

            <View style={styles.infoContainer}>
              <Text
                style={[styles.infoText, getLineHeight(16.8), { fontWeight: '400', fontSize: 14 }]}
              >
                {BottomSheetConfig?.rails_traveller_irctc_bs_config?.BottomSheetHeader}
              </Text>
            </View>
            <View style={styles.imageContainer}>
              <Image source={ASSETS.progressBar} style={styles.singleImage} />
            </View>
            <View style={styles.yellowContainer}>
              <View style={styles.spike} />
              <Image source={ASSETS.irctcUserCard} style={styles.imageInYellow} />
              <View style={styles.textWithTick}>
                <Image source={tick} style={styles.tickIcon} />
                <Text style={styles.finalText}>
                  {BottomSheetConfig?.rails_traveller_irctc_bs_config?.BottomSheetText1}
                </Text>
              </View>

              <View style={styles.textWithTick1}>
                <Image source={tick} style={styles.tickIcon1} />
                <Text style={styles.finalText}>
                  {BottomSheetConfig?.rails_traveller_irctc_bs_config?.BottomSheetText2}
                </Text>
              </View>
            </View>

            <View style={styles.newContainer}>
              <Text style={styles.newContainerText}>{_label('dont_have_an_account')}</Text>
              <TouchableOpacity onPress={this._onCreateNewClick}>
                <Text style={styles.createNowText}>{_label('create_now')}</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
              testID={`${this.props?.id}_closeButton`}
            >
              <LinearGradient
                colors={[colors.lightBlue, colors.darkBlue]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.gradientText}
              />
              <Text style={styles.closeButtonText}>{_label('okay_button_text')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }
}

InfoBottomSheet.propTypes = {
  isVisible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  id: PropTypes.string,
  from: PropTypes.string,
  probableTo: PropTypes.string, 
  nextPageProps: PropTypes.object,
};

export default InfoBottomSheet;
