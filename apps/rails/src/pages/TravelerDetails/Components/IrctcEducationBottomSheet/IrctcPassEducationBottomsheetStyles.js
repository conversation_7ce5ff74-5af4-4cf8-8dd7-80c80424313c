import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export default StyleSheet.create({
  background: {
    flex: 1,
    backgroundColor: colors.modalgrey,
    justifyContent: 'flex-end',
  },
  bottomSheetContainer: {
    backgroundColor: colors.white,
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  crossIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  closeText: {
    color: colors.primary,
    marginTop: 20,
  },
  infoContainer: {
    marginTop: 20,
  },
  infoText: {
    color: colors.black,
    fontWeight: '400',
    fontSize: 14,
  },
  imageContainer: {
    alignItems: 'center',
    marginTop: 20,
    paddingHorizontal: 20,
  },
  singleImage: {
    alignItems: 'center',
    width: 380,
    height: 80,
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  yellowContainer: {
    height: 'auto',
    backgroundColor: colors.creamWhite,
    padding: 8,
    borderRadius: 20,
    marginTop: 9,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  spike: {
    width: 0,
    height: 0,
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderBottomWidth: 10,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.creamWhite,
    position: 'absolute',
    top: -10,
    right: 0,
    left: '67%',
    transform: [{ translateX: -10 }],
  },
  imageInYellow: {
    marginTop: 9,
    width: 240,
    height: 177.42,
    resizeMode: 'contain',
    borderRadius: 13.35,
    alignSelf: 'center',
  },

  textWithTick: {
    flexDirection: 'row',
    marginTop: 16,
    alignItems: 'flex-start',
    width: '100%',
  },
  textWithTick1: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%',
    marginTop: 8,
    marginBottom: 16,
  },
  tickIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
    resizeMode: 'contain',
  },
  tickIcon1: {
    width: 16,
    height: 16,
    marginRight: 8,
    resizeMode: 'contain',
  },
  finalText: {
    fontSize: 12,
    color: colors.black,
    lineHeight: 16,
    flex: 1,
    flexWrap: 'wrap',
    fontFamily: 'lato',
    fontWeight: '400',
  },
  closeButton: {
    backgroundColor: colors.blue,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  closeButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  newContainer: {
    marginTop: 22.5,
    paddingVertical: 7.5,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  newContainerText: {
    fontFamily: 'lato',
    fontWeight: '400',
    fontSize: 14,
    color: colors.black,
    textAlign: 'center',
  },
  createNowText: {
    paddingVertical: 9,
    paddingLeft: 8,
    fontSize: 12,
    color: colors.blue,
    textAlign: 'center',
    fontWeight: '700',
  },
});
