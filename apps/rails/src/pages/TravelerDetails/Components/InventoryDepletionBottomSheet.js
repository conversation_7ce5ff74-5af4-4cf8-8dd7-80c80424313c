/* eslint-disable */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import { View, Text, StyleSheet, TouchableOpacity, Image, Platform } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import ASSETS from '../../../Utils/Assets/RailsAssets';
import LinearGradient from 'react-native-linear-gradient';
import useConfigStore from '../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../configStore/Common/constants';
import { getRailsAvailabilitySubscription } from '../../../RailsAbConfig';
import { Actions } from '@mmt/rails/src/navigation/railsNavigation';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { SEAT_AVAILABILITY_URL, TERMS_AND_CONDITIONS_HEADER_TEXT } from '../../../Utils/RailsConstant';

const inventoryDepletionBottomSheetDefaultPropOnSetAlert = () => {};

const InventoryDepletionBottomSheet = ({ 
  onClose, 
  inventorySellOutInDays, 
  onSetAlert = inventoryDepletionBottomSheetDefaultPropOnSetAlert, 
  price,
  availabilityDepletion 
}) => {
  const [selectedDepletionMessage, setSelectedDepletionMessage] = useState(null);
  const [depletionCommonConfig, setDepletionCommonConfig] = useState(null);
  
  const depletionMessageConfig = useConfigStore(configKeys.RAIL_INVENTORY_DEPLETION_CONFIG);

  useEffect(() => {
    if (!_isEmpty(depletionMessageConfig)) {
      try {
        const selectedMessage = getSelectedDepletionMessage(depletionMessageConfig);
        setSelectedDepletionMessage(selectedMessage);
        setDepletionCommonConfig(depletionMessageConfig.common);
      } catch (error) {
        console.error('Error selecting depletion message:', error);
      }
    }
  }, [depletionMessageConfig, inventorySellOutInDays]);

  const getSelectedDepletionMessage = (messageConfig) => {
    const percentageMatch = availabilityDepletion?.text?.match(/rise by (\d+)%/);
    const percentage = percentageMatch ? percentageMatch[1] : null;
    const hasPriceRise = availabilityDepletion?.isDynamicTrain && percentage;

    // Case 1 & 2: 0-2 days
    if (inventorySellOutInDays >= 0 && inventorySellOutInDays <= 2) {
      if (hasPriceRise) {
        // Case 2: 0-2 days with price rise
        return {
          ...messageConfig.urgentPriceRise,
          title: messageConfig.urgentPriceRise.title
            .replace('{percentage}', percentage)
            .replace('{days}', inventorySellOutInDays),
          primaryButton: messageConfig.urgentPriceRise.primaryButton
            .replace('{price}', price)
        };
      } else {
        // Case 1: 0-2 days normal
        return messageConfig.urgent?.[inventorySellOutInDays] || messageConfig.urgent?.[0];
      }
    }

    // Case 3 & 4: 3-7 days with subscription
    if (inventorySellOutInDays >= 3 && 
        inventorySellOutInDays <= 7 && 
        getRailsAvailabilitySubscription() === 1) {
      
      if (hasPriceRise) {
        // Case 4: 3-7 days with price rise
        return {
          ...messageConfig.alertPriceRise,
          subtitle: messageConfig.alertPriceRise.subtitle
            .replace('{percentage}', percentage)
            .replace('{days}', inventorySellOutInDays)
        };
      } else {
        // Case 3: 3-7 days normal
        return {
          ...messageConfig.alert,
          subtitle: messageConfig.alert.subtitle
            .replace('{days}', inventorySellOutInDays)
        };
      }
    }

    // Default case: fallback to first urgent message
    return messageConfig.urgent?.[0];
  };

  if (!selectedDepletionMessage || !depletionCommonConfig) {
    return null;
  }

  const isAlertFlow = inventorySellOutInDays >= 3 && 
                     inventorySellOutInDays <= 7 && 
                     getRailsAvailabilitySubscription() === 1;

  const getTitleWithHighlight = (title) => {
    if (!title) {return '';}
    
    const parts = title.split(/\[(.*?)\]/);
    if (parts.length > 1) {
      return (
        <>
          {parts.map((part, index) => {
            if (index % 2 === 1) {
              return <Text key={index} style={styles.redText}>{part}</Text>;
            }
            return part;
          })}
        </>
      );
    }
    return title;
  };

  const getSubtitleWithHighlight = (subtitle) => {
    if (!subtitle) {return '';}

    const parts = subtitle.split(/\[(.*?)\]/);
    if (parts.length > 1) {
      return (
        <>
          {parts.map((part, index) => {
            if (index % 2 === 1) {
              return <Text key={index} style={styles.boldText}>{part}</Text>;
            }
            return part;
          })}
        </>
      );
    }

    if (isAlertFlow) {
      const daysText = `${inventorySellOutInDays} days*`;
      const parts = subtitle.split(daysText);
      if (parts.length > 1) {
        return (
          <>
            {parts[0]}
            <Text style={styles.boldText}>{daysText}</Text>
            {parts[1]}
          </>
        );
      }
    }
    return subtitle;
  };

  return (
    <View style={styles.container} testID="inventory_depletion_bottomsheet_container">
      <TouchableOpacity style={styles.closeButton} onPress={onClose} testID="inventory_depletion_bottomsheet_close_button">
        <View testID="inventory_depletion_bottomsheet_close_button_container">
          <Image source={ASSETS.closeIcon} style={styles.closeIcon} testID="inventory_depletion_bottomsheet_close_icon" />
        </View>
      </TouchableOpacity>
      <View style={styles.boltIconContainer} testID="inventory_depletion_bottomsheet_bolt_icon_container">
        <Image 
          source={ASSETS.boltIcon} 
          style={styles.boltIcon} 
          resizeMode="contain"
          testID="inventory_depletion_bottomsheet_bolt_icon"
        />
      </View>
      
      <Text style={styles.title} testID="inventory_depletion_bottomsheet_title">
        {getTitleWithHighlight(selectedDepletionMessage.title)}
      </Text>
      
      <Text style={[styles.subtitle, fontStyle('regular')]} testID="inventory_depletion_bottomsheet_subtitle">
        {getSubtitleWithHighlight(selectedDepletionMessage.subtitle)}
      </Text>
      
      <Text style={[styles.disclaimer, fontStyle('regular')]} testID="inventory_depletion_bottomsheet_disclaimer">
        {depletionCommonConfig.disclaimer}
      </Text>
      
      <TouchableOpacity style={styles.termsContainer} testID="inventory_depletion_bottomsheet_terms_button"
        onPress={() => {
        Actions.openWebView({
          url: SEAT_AVAILABILITY_URL,
          headerText: TERMS_AND_CONDITIONS_HEADER_TEXT,
          headerIcon: backIcon,
        });
        }}>
        <Text style={[styles.termsText, fontStyle('regular')]} testID="inventory_depletion_bottomsheet_terms_button_text">
          {depletionCommonConfig.termsText}
        </Text>
      </TouchableOpacity>
      
      <View style={styles.buttonsContainer}>
        {isAlertFlow && selectedDepletionMessage.secondaryButton && (
          <TouchableOpacity onPress={onSetAlert} testID="inventory_depletion_bottomsheet_set_alert_button">
            <LinearGradient
              colors={[colors.lightBlue, colors.darkBlue]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.setAlertButton}
              testID="inventory_depletion_bottomsheet_set_alert_button_linear_gradient"
            >
              <Text style={[styles.setAlertButtonText, fontStyle('bold')]} testID="inventory_depletion_bottomsheet_set_alert_button_text">
                {selectedDepletionMessage.secondaryButton}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
        
        {selectedDepletionMessage.primaryButton && (
          <TouchableOpacity 
            style={isAlertFlow && styles.continueButtonAlertFlow} 
            onPress={onClose}
            testID="inventory_depletion_bottomsheet_continue_button"
          >
            {isAlertFlow ? (
              <Text style={styles.continueButtonTextAlertFlow} testID="inventory_depletion_bottomsheet_continue_button_text">
                {selectedDepletionMessage.primaryButton}
              </Text>
            ) : (
              <LinearGradient
                colors={[colors.lightBlue, colors.darkBlue]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.continueButton}
                testID="inventory_depletion_bottomsheet_continue_button_linear_gradient"
              >
                <Text style={[styles.continueButtonText, fontStyle('bold')]} testID="inventory_depletion_bottomsheet_continue_button_text">
                  {selectedDepletionMessage.primaryButton}
                </Text>
              </LinearGradient>
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 60,
    paddingHorizontal: 24,
    ...Platform.select({
      android: {
        paddingBottom: 25,
      }
    }),
    alignItems: 'flex-start',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },
  closeIcon: {
    width: 24,
    height: 24,
  },
  boltIconContainer: {
    position: 'absolute',
    top: -50,
    left: 24, 
    width: 90,
    height: 90,
    borderRadius: 50,
    backgroundColor: colors.creamWhite,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  boltIcon: {
    width: 44,
    height: 44,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Lato',
    color: colors.black,
    textAlign: 'left',
    marginBottom: 16,
    lineHeight: 28.8,
    fontWeight: '800',
    marginTop: 10,
    width: '100%',
  },
  redText: {
    color: colors.red17,
    fontWeight: '800',
    fontFamily: 'Lato',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Lato',
    fontWeight: '400',
    color: colors.textGrey,
    textAlign: 'left',
    marginBottom: 32,
    lineHeight: 19.2,
    width: '100%',
  },
  disclaimer: {
    fontSize: 12,
    fontFamily: 'Lato',
    fontWeight: '400',
    color: colors.greyText1,
    textAlign: 'left',
    marginBottom: 8,
    lineHeight: 14.4,
    width: '100%',
  },
  termsContainer: {
    marginBottom: 24,
    alignSelf: 'flex-start',
  },
  termsText: {
    fontSize: 12,
    fontFamily: 'Lato',
    fontWeight: '400',
    color: colors.primary,
    textAlign: 'left',
  },
  buttonsContainer: {
    width: '100%',
    gap: 16,
  },
  setAlertButton: {
    height: 44,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  setAlertButtonText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: 'Lato',
    fontWeight: '900',
    textAlign: 'center',
  },
  continueButton: {
    height: 44,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonAlertFlow: {
    height: 44,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  continueButtonText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: 'Lato',
    fontWeight: '900',
    letterSpacing: 0.5,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  continueButtonTextAlertFlow: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '600',
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  boldText: {
    fontWeight: '700',
    color: colors.black,
    fontFamily: 'Lato',
  },
});

InventoryDepletionBottomSheet.propTypes = {
  onClose: PropTypes.func.isRequired,
  inventorySellOutInDays: PropTypes.number.isRequired,
  onSetAlert: PropTypes.func,
  price: PropTypes.number.isRequired,
  availabilityDepletion: PropTypes.shape({
    text: PropTypes.string,
    isDynamicTrain: PropTypes.bool,
    textColor: PropTypes.string,
    mainIconUrl: PropTypes.string,
    infoIconUrl: PropTypes.string,
    backgroundGradient: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string
    })
  }).isRequired
};

export default InventoryDepletionBottomSheet; 