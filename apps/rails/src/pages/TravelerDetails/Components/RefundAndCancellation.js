
import React from 'react';
import PropTypes from 'prop-types';
import { Text, View, Platform } from 'react-native';
import { Actions } from '../../../navigation';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { trackTravellerPageEvent } from '../../../railsAnalytics';
import RAIL_EVENTS from '../../../RailsOmnitureTracker';
import travelerStyle from '../TravelerDetailsCSS';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { _label } from '../../../vernacular/AppLanguage';
import { isMweb } from '../../../Utils/device';
import { getRulesDetail } from '../TravelerDetailsUtils';

const _trainBookingPolicy = () => {
  Actions.trainsBookingPolicy();
};

const onLinkPress = (key) => {
  const { url, headerText} = getRulesDetail(key);
  if (key === 'cancellation_and_refund_policy') {
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_CANCELLATION_POLICY_VIEW);
  }
  Actions.openWebView({
    url,
    headerText,
    headerIcon: backIcon,
  });
};

const RefundAndCancellation = ({ id }) => (
  <View style={styles.refundAndCancellationContainer}>
    <View style={styles.refundAndCancellationContainer}>
      <View style={isMweb() ? styles.textContainerWeb : styles.textContainer}>
        <Text>
          <Text style={travelerStyle.mpText}>
            {_label('refund_message_line1')}
          </Text>
          <Text style={[travelerStyle.mpText, {color: colors.azure}]} onPress={() => onLinkPress('cancellation_and_refund_policy')} testID={`${id}_cancellationPolicy`}>
            {` ${_label('refund_message_line2')},`}
          </Text>
          <Text style={[travelerStyle.mpText, { color: colors.azure }]} onPress={_trainBookingPolicy} testID={`${id}_trainBookingPolicy`}>
          {` ${_label('refund_message_line3')},`}
          </Text>
          <Text style={[travelerStyle.mpText, { color: colors.azure }]} onPress={() => onLinkPress('privacy_policy')} testid={`${id}_privacypolicy`}>
          {` ${_label('refund_message_line4')},`}
          </Text>
          <Text style={[travelerStyle.mpText, { color: colors.azure }]} onPress={() => onLinkPress('user_agreement')} testID={`${id}_userAgreementPolicy`}>
          {` ${_label('refund_message_line5')},`}
          </Text>
          <Text style={travelerStyle.mpText}>
          {` ${_label('refund_message_line6')}, `}
          </Text>
          <Text style={[travelerStyle.mpText, { color: colors.azure }]} onPress={() => onLinkPress('terms_of_services')} testID={`${id}_termsOfService`}>{_label('refund_message_line7')}</Text>
        </Text>
      </View>
    </View>
  </View>
);

const styles = {
  refundAndCancellationContainer: {
    flexDirection: 'row',
    ...Platform.select({
      web: {
        width: '100%',
      },
    }),
  },
  textContainer: {
    paddingHorizontal: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  textContainerWeb: {
    paddingHorizontal: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
  },
};

RefundAndCancellation.propTypes = {
  refundAndCanChecked: PropTypes.bool.isRequired,
  onRefundAndCancellationClicked: PropTypes.func.isRequired,
  id: PropTypes.string,
};

export default RefundAndCancellation;
