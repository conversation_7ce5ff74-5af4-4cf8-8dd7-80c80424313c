import PropTypes from 'prop-types';
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';

import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {
  numAppendedWithRuppeeSymbolWithoutRoundWithoutSpace,
} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {TRAVEL_INSURANCE} from '../TravelerDetails.constant';
import { COMPONENT_INSURANCE } from '../TravelerDetailsActions';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import { showNewAdditionalPreferenceVariants } from '@mmt/rails/src/RailsAbConfig';

class TravelInsurance extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      travelerInsuranceOpted: this.initializeTravelInsurance(),
    };
  }

  initializeTravelInsurance() {
    return TRAVEL_INSURANCE.OPTED;
  }

  onTravelInsuranceOptionClick = (isTravelInsuranceOpted) => {
    this.setState({ travelerInsuranceOpted: isTravelInsuranceOpted });
    this.props.onTravelInsuranceCBClicked(isTravelInsuranceOpted);
  };

  onTravelInsuranceOptionOpted = () => {
    this.onTravelInsuranceOptionClick(TRAVEL_INSURANCE.OPTED);
  };

  onTravelInsuranceOptionNotOpted = () => {
    this.onTravelInsuranceOptionClick(TRAVEL_INSURANCE.NOT_OPTED);
  };

  renderOldVariant = () => {
    const { insuranceCharge, id } = this.props;
    const { travelerInsuranceOpted } = this.state;

    return (
      <View
        style={styles.container1}
        ref={(ref) => {
          this.props.captureRef(COMPONENT_INSURANCE, ref);
        }}
        testID={id}
      >
        <Text style={[{ fontSize: 22, color: colors.black }, fontStyle('black')]}>
          {_label('irctc_travel_insurance')}
        </Text>
        <CheckBox
          textLine1={_label('travel_insurance_cost_prompt', undefined, {
            insurance_amount: numAppendedWithRuppeeSymbolWithoutRoundWithoutSpace(
              !insuranceCharge ? '0.49' : insuranceCharge,
            ),
          })}
          isChecked={travelerInsuranceOpted === TRAVEL_INSURANCE.OPTED}
          onPress={() =>
            this.onTravelInsuranceOptionClick(
              travelerInsuranceOpted === TRAVEL_INSURANCE.OPTED
                ? TRAVEL_INSURANCE.NOT_OPTED
                : TRAVEL_INSURANCE.OPTED,
            )
          }
          customStyles={styles.checkBoxContainer}
          customLine1Style={[styles.checkboxLabel, fontStyle('regular'), getLineHeight(14)]}
          testIds={{
            btnId: `${id}_checkbox`,
          }}
        />
      </View>
    );
  };

  renderNewVariant = () => {
    const { insuranceCharge, onTncClicked, id } = this.props;
    const { travelerInsuranceOpted } = this.state;

    return (
      <View
        style={styles.container2}
        ref={(ref) => {
          this.props.captureRef(COMPONENT_INSURANCE, ref);
        }}
        testID={id}
      >
        <Text style={[styles.headerStyle, fontStyle('black'), getLineHeight(16)]}>
          {_label('irctc_travel_insurance')}
        </Text>
        <View style={styles.spacer} />
        <Text style={[styles.wouldYouText, fontStyle('regular'), getLineHeight(14)]}>
          {_label('travel_insurance_cost_prompt', undefined, {
            insurance_amount: numAppendedWithRuppeeSymbolWithoutRoundWithoutSpace(
              !insuranceCharge ? '0.49' : insuranceCharge,
            ),
          })}
          {/* Would you like to take IRCTC travel insurance for {numAppendedWithRuppeeSymbolWithoutRoundWithoutSpace(this.props.insuranceCharge)}/person? */}
        </Text>
        <View style={styles.spacer} />
        <View style={styles.optionBox}>
          <TouchableRipple testID={`${id}_yesOption`} onPress={this.onTravelInsuranceOptionOpted}>
            <View style={styles.optionContainer}>
              <RadioButton
                isSelected={travelerInsuranceOpted === TRAVEL_INSURANCE.OPTED}
                onPress={this.onTravelInsuranceOptionOpted}
                radioSize={18}
              />
              <Text style={[styles.radioText, fontStyle('regular'), getLineHeight(14)]}>
                {_label('travel_insurance_accept')}
                <Text
                  onPress={onTncClicked}
                  style={[styles.insuranceTerms, fontStyle('regular')]}
                  testID={`${id}_yesOption_tnc`}
                >
                  {' '}
                  {_label('travel_insurance_tnc')}
                </Text>
              </Text>
            </View>
          </TouchableRipple>

          <TouchableRipple
            testID={`${id}_noOption`}
            onPress={this.onTravelInsuranceOptionNotOpted}
          >
            <View style={styles.optionContainer}>
              <RadioButton
                isSelected={travelerInsuranceOpted === TRAVEL_INSURANCE.NOT_OPTED}
                onPress={this.onTravelInsuranceOptionNotOpted}
                radioSize={18}
              />
              <Text style={[styles.radioText, fontStyle('regular'), getLineHeight(14)]}>
                {_label('travel_insurance_no')}
              </Text>
            </View>
          </TouchableRipple>
        </View>
        {travelerInsuranceOpted === TRAVEL_INSURANCE.NOT_SELECTED && (
          <Text style={[styles.errorText, fontStyle('regular')]}>
            {_label('travel_insurance_warning')}
          </Text>
        )}
      </View>
    );
  };

  render() {
    const showNewAdditionalPreference = showNewAdditionalPreferenceVariants();
    return (
      <View>
        {showNewAdditionalPreference === 0 ? this.renderOldVariant() : this.renderNewVariant()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container1: { backgroundColor: colors.white, paddingVertical: 20 },
  container2: { backgroundColor: colors.white },
  headerStyle: { fontSize: 22, color: colors.black, marginTop: 16 },
  wouldYouText: { color: colors.black, fontSize: 14 },
  optionContainer: { flexDirection: 'row', alignItems: 'center', paddingVertical: 10 },
  radioText: {
    color: colors.black,
    fontSize: 14,
    marginLeft: 10,
  },
  insuranceTerms: { color: colors.azure, fontSize: 14 },
  errorText: { color: colors.red, fontSize: 14 },
  optionBox: {
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
  },
  spacer: { height: 8 },
  checkBoxContainer: {
    alignSelf: 'flex-start',
    marginTop: 20,
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: 14,
    color: colors.black,
    marginLeft: 4,
  },
});

TravelInsurance.propTypes = {
  insuranceCharge: PropTypes.number.isRequired,
  onTncClicked: PropTypes.func.isRequired,
  onTravelInsuranceCBClicked: PropTypes.func.isRequired,
  travelerInsuranceOpted: PropTypes.number.isRequired,
  labels: PropTypes.object.isRequired,
  captureRef: PropTypes.func,
  id: PropTypes.string,
};

export default TravelInsurance;
