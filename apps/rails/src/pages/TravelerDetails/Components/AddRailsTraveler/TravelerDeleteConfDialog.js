import React from 'react';
import PropTypes from 'prop-types';
import {View, Text, StyleSheet, Modal} from 'react-native';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';
import MultiStyleVernacText from '../../../../vernacular/MultiStyleVernacText';

export default function TravelerDeleteConfDialog(props) {
  props = {
    ...props,
    modalVisible: typeof props.modalVisible === 'undefined' ? false : props.modalVisible,
    onCancelPress: typeof props.onCancelPress === 'undefined' ? _noop : props.onCancelPress,
  };

  const {traveler, modalVisible, onCancelPress, onDeleteTravellerAction} = props;
  const name = _get(traveler, 'basicInfo.name', {});
  const fullName = `${name.firstName || ''} ${name.lastName || ''}`;
  return (
    <Modal
    animationType="slide"
      visible={modalVisible}
      transparent
      onRequestClose={onCancelPress}
      testID="travelerDeleteConfDialog_modal"
    >
      <TouchableRipple onPress={onCancelPress} style={styles.dummyComponent}>
        <View style={styles.dummyComponent} testID={props?.id} />
      </TouchableRipple>
      <View style={styles.container} testID="travelerDeleteConfDialog_container">
        <Text style={[styles.confirmationHeadingText, fontStyle('light'), getLineHeight(34)]}> {_label('are_you_sure')} </Text>
        <MultiStyleVernacText
            content = {_label('do_you_want_to_delete')}
            params = {{name:  fullName}}
            defaultContentStyle = {[styles.confirmationContent, fontStyle('regular'), getLineHeight(14)]}
            textStyles = {{
              name : [fontStyle('bold'), getLineHeight(14)],
            }}
            viewStyle = {styles.confirmationContentContainer}
            contentHorizontal
        />
        <View style={styles.buttonsContainer}>
          <TouchableRipple onPress={onCancelPress}>
            <View testID={`${props?.id}_cancelButton`}>
              <Text style={[styles.buttonText, styles.cancelButtonText, fontStyle('regular'), getLineHeight(16)]}> {_label('cancel', { uppercase: true })} </Text>
            </View>
          </TouchableRipple>
          <TouchableRipple onPress={() => onDeleteTravellerAction(traveler, onCancelPress)}>
            <View testID={`${props?.id}_deleteButton`}>
              <Text style={[styles.buttonText, styles.deleteButtonText, fontStyle('bold'), getLineHeight(16)]}> {_label('$delete', { uppercase: true })} </Text>
            </View>
          </TouchableRipple>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  dummyComponent: {
    flex: 1,
    backgroundColor: colors.grey40,
  },
  container: {
    padding: 24,
    backgroundColor: colors.white,
  },
  confirmationHeadingText: {
    fontSize: 34,
    color: colors.black,
    letterSpacing: 0,
    marginLeft: -5,
  },
  confirmationContentContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 20,
    paddingRight: 64,
    paddingBottom: 60,
  },
  confirmationContent: {
    fontSize: 14,
    letterSpacing: 0,
    color: colors.defaultTextColor,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 6,
  },
  buttonText: {
    fontSize: 16,
    letterSpacing: 0,
  },
  cancelButtonText: {
    color: colors.azure,
  },
  deleteButtonText: {
    color: colors.red,
  },
});

TravelerDeleteConfDialog.propTypes = {
  traveler: PropTypes.object.isRequired,
  modalVisible: PropTypes.bool,
  onCancelPress: PropTypes.func,
  onDeleteTravellerAction: PropTypes.func.isRequired,
  id: PropTypes.string,
};
