import React from 'react';
import { Actions } from '../../../../navigation';
import PropTypes from 'prop-types';
import { StyleSheet, Text, View } from 'react-native';
import { backIcon as backIconIos } from '@mmt/legacy-commons/Helpers/displayHelper';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';
import { isApp, isIos } from '../../../../Utils/device';
import Header from '@Frontend_Ui_Lib_App/Header';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';

export const SIZE_LARGE = 'large';
export const SIZE_NORMAL = 'normal';
import backIconAndroid from '@mmt/legacy-assets/src/iconBack.webp';
const backIcon = isIos() ? backIconIos : backIconAndroid;

export const tripHeaderNormalHeight = 56;
export const tripHeaderLargeHeight = 108;

const addEditRailTravelerHeaderAndroidDefaultPropOnPressCloseHandler = () => Actions.pop();

const AddEditRailTravelerHeaderAndroid = ({
  title,
  onPressCloseHandler = addEditRailTravelerHeaderAndroidDefaultPropOnPressCloseHandler,
  showDeleteButton,
  onPressDelete,
  id,
}) => (
  <Header
    customStyles={{
      wrapperStyle: styles.headerWrapperStyles,
      titleStyle: [styles.titleTextStyle, fontStyle('bold'), getLineHeight(18)],
    }}
    wrapperTestId={id}
    leftIcon={{
      icon: backIcon,
      testId: `${id}_backButton`,
      onPress: onPressCloseHandler,
      customStyles: {
        iconStyle: styles.backIcon,
      },
    }}
    title={title}
    rightComponent={
      showDeleteButton && isApp() ? (
        <TouchableRipple onPress={onPressDelete}>
          <View testID={`${id}_deleteButton`}>
            <Text style={styles.deleteText}> {_label('$delete', { uppercase: true })} </Text>
          </View>
        </TouchableRipple>
      ) : (
        <></>
      )
    }
  />
);

AddEditRailTravelerHeaderAndroid.propTypes = {
  title: PropTypes.string.isRequired,
  onPressCloseHandler: PropTypes.func,
  showDeleteButton: PropTypes.bool.isRequired,
  onPressDelete: PropTypes.func.isRequired,
  id: PropTypes.string,
};

const addEditRailTravelerHeaderIosDefaultPropOnPressCloseHandler = () => Actions.pop();

const AddEditRailTravelerHeaderIos = ({
  title,
  onPressCloseHandler = addEditRailTravelerHeaderIosDefaultPropOnPressCloseHandler,
  showDeleteButton,
  onPressDelete,
  id,
}) => (
  <Header
    customStyles={{
      wrapperStyle: styles.container,
      titleStyle: [styles.titleTextStyle, fontStyle('bold'), getLineHeight(18)],
    }}
    wrapperTestId={id}
    leftIcon={{
      icon: backIconIos,
      testId: `${id}_backButton`,
      onPress: onPressCloseHandler,
    }}
    title={title}
    rightComponent={
      showDeleteButton ? (
        <TouchableRipple onPress={onPressDelete}>
          <View testID={`${id}_deleteButton`}>
            <Text style={styles.deleteText}> {_label('$delete', { uppercase: true })} </Text>
          </View>
        </TouchableRipple>
      ) : (
        <></>
      )
    }
  />
);

AddEditRailTravelerHeaderIos.propTypes = {
  title: PropTypes.string.isRequired,
  onPressCloseHandler: PropTypes.func,
  showDeleteButton: PropTypes.bool.isRequired,
  onPressDelete: PropTypes.func.isRequired,
  id: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    height: 56,
    flexDirection: 'row',
    paddingHorizontal: 16,
    alignItems: 'center',
    paddingVertical: 0,
    ...getPlatformElevation(5),
    justifyContent: 'space-between',
    marginHorizontal: 0,
    marginVertical: 0,
  },
  titleTextStyle: {
    fontSize: 18,
    color: colors.black04,
  },
  deleteText: {
    color: colors.red6,
  },
  backIcon: {
    width: 16,
    height: 16,
  },
  headerWrapperStyles: {
    marginHorizontal: 0,
    marginVertical: 0,
    paddingVertical: 0,
    flexDirection: 'row',
    height: 56,
    paddingHorizontal: 16,
    ...getPlatformElevation(5),
    alignItems: 'center',
  },
});

export default isIos() ? AddEditRailTravelerHeaderIos : AddEditRailTravelerHeaderAndroid;
