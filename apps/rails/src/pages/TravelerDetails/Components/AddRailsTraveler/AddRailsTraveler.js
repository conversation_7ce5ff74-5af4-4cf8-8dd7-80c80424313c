/* eslint-disable @typescript-eslint/no-misused-promises */

import React from 'react';
import isNull from 'lodash/isNull';
import isEmpty from 'lodash/isEmpty';
import isUndefined from 'lodash/isUndefined';
import trim from 'lodash/trim';

import PropTypes from 'prop-types';
import { Keyboard, ScrollView, Text, TouchableWithoutFeedback, View, StyleSheet } from 'react-native';

import AddEditRailTravelerHeader from './AddEditRailTravelerHeader';
import travelerStyle from '../../TravelerDetailsCSS';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  isAgeAndGenderSpecified,
  isAgeWithinRange,
  isChild,
  isInfant,
  isNameLengthInRange,
  isSrCitizen,
  isValidName,
  validateForLadiesQuota,
  RailsListingUtilsClass,
  validateForSeniorCitizenQuota,
} from '../../TravelerDetailsUtils';
import { GenderList, isTransgender } from '../../../Constants/Gender';
import { RailsDropDownOverlayContainer } from '../../../Common/RailsDropdown';
import SrCitizenConcession from '../../../Constants/SrCitizenConcession';
import { countries } from '../../../Constants/Country';
import Berth from '../../../Constants/Berth';
import Meal from '../../../Constants/MealNew';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import DisabledCTAButton from '../../../User/Common/DisabledCTAButton';
import CtaButton from '../../../User/Common/CtaButton';
// import { isLadiesQuota } from '../../../Types/QuotaType';
import {
  removeEventFromEvar47or97Variable,
  trackGenericEvar47or97Event,
  trackTravellerPageEvent,
} from '../../../../railsAnalytics';
import RAIL_EVENTS from '../../../../RailsOmnitureTracker';
import TabbedRadio from '@mmt/legacy-commons/Common/Components/Radio/TabbedRadio';
import RadioGroup from '@mmt/legacy-commons/Common/Components/Radio/RadioGroup';
import Label from '@mmt/legacy-commons/Common/Components/Label/label';
import ChildBerth from './ChildBerth';
import WarningBox from '../WarningBox';
import TravelerDeleteConfDialog from './TravelerDeleteConfDialog';
import { KeyboardAvoidingViewWrapper } from '../../../CreateNewAccount/CreateNewAccount';
import InternationalTravelerFields from './InternationalTravelerFields';
import {fontStyle, isOnlyEnglishCharacters} from '../../../../vernacular/VernacularUtils';
import { _label } from '../../../../vernacular/AppLanguage';

import InputField from '@Frontend_Ui_Lib_App/InputField';
import { getLineHeight } from 'packages/legacy-commons/Common/Components/Vernacular/Utils';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { showAddMoreTravellerFlow } from '@mmt/rails/src/RailsAbConfig';
import { AddMoreTravellerFlow } from '@mmt/rails/src/RailsAbConstants';
import {
  SAVE_AND_ADD_MORE,
  SAVE,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  TRAVELLER_DETAILS_SAVED,
  ERR_PASSPORT_REQUIRED,
  ERR_DOB_REQUIRED,
  ERR_AGE_DOB_MISMATCH,
  ERR_PASSPORT_INVALID,
  PDT_TRAVELLER_NATIONALITY_EXPAND,
  PDT_TRAVELLER_NATIONALITY_COLLAPSE,
  PDT_TRAVELLER_NATIONALITY_CHOSEN_PREFIX,
} from '@mmt/rails/src/Utils/RailsConstant';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import FullScreenDropdown from '../../FullScreenDropdown';
import { COUNTRIES } from '../../DisplayDropdown';

export default class AddRailsTraveler extends React.Component {
  constructor(props) {
    super(props, AddRailsTraveler);
    this.isTravellerBeingAdded = false;
    this.state = {
      travelerId: '',
      nameError: null,
      fullName: '',
      ageError: null,
      age: '',
      genderError: null,
      gender: null,
      childBerthFlag: false,
      showChildBerthFlag: false,
      showSrCitizen: false,
      showBerth: true,
      meal: null,
      seniorCitizenConcession: null,
      nationality: null,
      passportNumber: '',
      dateOfBirth: null,
      passportError: null,
      dobError: null,
      enableCTA: false,
      addOrSaveLabel: null,
      ctaLabel: null,
      addOrEditFlag: true,
      pageTitle: props.labels.addTravelers,
      selectedBirth: {},
      mealPreferenceArray: [],
      id: 'travelerDetailsPage_travelerDetails_onAddEditTravelerDetailsButtonClick',
      editTravellerClicked: false,
    };
    this.railsListingUtilObj = new RailsListingUtilsClass(props.railsListing);
  }

  initDefaultState = () => {
    this.setState({
      travelerId: new Date().getTime(),
      fullName: '',
      age: '',
      gender: null,
      childBerthFlag: false,
      showChildBerthFlag: false,
      meal: Meal.NO,
      seniorCitizenConcession: SrCitizenConcession.FULL_CONCESSION,
      nationality: countries.IN,
        passportNumber: '',
        dateOfBirth: '',
        passportError: null,
        dobError: null,
      addOrSaveLabel: _label('add', { uppercase: true }),
      ctaLabel: _label('add', { uppercase: true }),
      addOrEditFlag: true,
      pageTitle: _label('traveller_details'),
      showDeleteButton: false,
      selectedBirth: Berth.NO_BERTH_PREF,
      mealPreferenceArray: [Meal.NO.key],
    }, () => this._enableCTA());
  };

  componentDidMount() {
    this._enableCTA();
    const addMoreTravellerFlowValue = showAddMoreTravellerFlow();
    const pageName = TRAVELERS_PAGE_TRACKING_KEY_NEW;

    if (addMoreTravellerFlowValue === AddMoreTravellerFlow.SHOWN) {
      removeEventFromEvar47or97Variable(
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADD_MORE_TRAVELLERS_SHOWN,
      );
      trackGenericEvar47or97Event(
        pageName,
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADD_MORE_TRAVELLERS_SHOWN,
      );
    } else {
      removeEventFromEvar47or97Variable(
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADD_MORE_TRAVELLERS_NOT_SHOWN,
      );
      trackGenericEvar47or97Event(
        pageName,
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ADD_MORE_TRAVELLERS_NOT_SHOWN,
      );
    }
  }

  UNSAFE_componentWillMount() {
    const pax = this.props.traveler;
    if (isNull(pax) || isEmpty(pax) || isUndefined(pax)) {
      this.initDefaultState();
    } else {
      let showBerth;
      const { age } = pax.basicInfo;
      if (isInfant(age) || isChild(age)) {
        showBerth = pax.basicInfo.childBerthFlag;
      } else {
        showBerth = true;
      }
      let nationalityToSet = countries.IN;
      if (pax.basicInfo.nationality) {
        const nat = pax.basicInfo.nationality;
        if (typeof nat === 'string') {
          const match = COUNTRIES.find((c) => (c.name || '').toLowerCase() === nat.toLowerCase());
          if (match) {
            nationalityToSet = { name: match.name, code: match.code };
          }
        } else if (nat && nat.code && nat.name) {
          nationalityToSet = nat;
        }
      }
      const dobEpoch = pax?.dateOfBirth;
      const monthAbbr = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      const dobDisplay =
        typeof dobEpoch === 'number' && !isNaN(dobEpoch)
          ? `${new Date(dobEpoch).getDate()} - ${
              monthAbbr[new Date(dobEpoch).getMonth()]
            } - ${new Date(dobEpoch).getFullYear()}`
          : '';

      this.setState({
        travelerId: pax.basicInfo.travelerId,
        fullName: pax.fullName,
        age: pax.isAgeSpecified ? `${pax.basicInfo.age}` : '',
        gender: pax.isGenderSpecified ? pax.basicInfo.gender : null,
        childBerthFlag: this.props.childBerthMandatory ? true : pax.basicInfo.childBerthFlag,
        showChildBerthFlag: pax.isAgeSpecified ? (isChild(pax.basicInfo.age) ||
                            isInfant(pax.basicInfo.age)) : pax.basicInfo.childBerthFlag,
        showBerth,
        selectedBirth: pax.isberthMismatch ? Berth.NO_BERTH_PREF : pax.basicInfo.berth,
        meal: pax.basicInfo.meal,
        seniorCitizenConcession: pax.basicInfo.seniorCitizenConcession,
          nationality: nationalityToSet,
          passportNumber: pax?.passportNumber || '',
          dateOfBirth: dobEpoch ? { display: dobDisplay, epoch: dobEpoch } : null,
        showSrCitizen: isAgeAndGenderSpecified(pax) && isSrCitizen(pax.basicInfo.age, pax.basicInfo.gender),
        addOrSaveLabel: _label('save', { uppercase: true }),
        ctaLabel: _label('save', { uppercase: true }),
        addOrEditFlag: false,
        pageTitle: _label('edit_traveller', { uppercase: true }),
        showDeleteButton: true,
        mealPreferenceArray: pax.basicInfo.mealPreferenceArray,
          editTravellerClicked: !pax.isAgeSpecified,
      }, () => this._enableCTA());
    }
  }

  _onBirthPreferenceChange = (selectedBirth) => {
    Keyboard.dismiss();
    this.setState({
      selectedBirth,
    });
  };

  _onChildBerthFlagSelected = (childBerthFlag) => {
    let showBerth = false;
    if (childBerthFlag) {
      showBerth = true;
      trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_CHILD_FULL_BERTH);
    }
    this.setState({
      childBerthFlag,
      showBerth,
    });
  };

  _onGenderSelected = (gender) => {
    let showSrCitizen = false;
    let genderError = null;
    Keyboard.dismiss();
    if (validateForLadiesQuota(this.props.selectedQuota, this.state.age, gender)) {
      const genderError = isTransgender(gender)
        ? _label('ladies_quota_transgender_age')
        : _label('ladies_quota_male_age');
      showShortToast(genderError);
      return;
    }
    if (
      this.props.selectedQuota?.code === 'SS' &&
      !validateForSeniorCitizenQuota(this.props.selectedQuota, this.state.age, gender)
    ) {
      const genderError = _label('not_sr_citizen_travelling_in_sr_citizen_quota_warning');
      showShortToast(genderError);
      return;
    }
    if (isSrCitizen(this.state.age, gender)) {
      showSrCitizen = true;
    }
    this.setState({
      gender,
      showSrCitizen,
      genderError,
    }, () => this._enableCTA());
  };

  _isValidAge = (age) => {
    const isValidAge = /^[0-9]*$/.test(age);
    return isValidAge;
  };

  _onChangeAge = (text) => {
    if (this.state.editTravellerClicked) {
      this.setState({ editTravellerClicked: false });
    }
    let ageError = null;
    let showChildBerthFlag = false;
    let childBerthFlag = false;
    let showSrCitizen = false;
    let showBerth = true;
    let { seniorCitizenConcession } = this.state;
    const age = trim(text);

    if (!isOnlyEnglishCharacters(age)) {
      ageError = _label('type_in_english');
    }
    if (!this._isValidAge(age)) {
      ageError = _label('invalid_age');
    }
    if (!isAgeWithinRange(age)) {
      ageError = _label('age_range');
    }

    if (isChild(age)) {
      showChildBerthFlag = true;
      childBerthFlag = true;
      showSrCitizen = false;
      showBerth = true;
    } else if (isInfant(age)) {
      showChildBerthFlag = true;
      childBerthFlag = false;
      showBerth = false;
    } else if (isSrCitizen(age, this.state.gender)) {
      showSrCitizen = true;
      showBerth = true;
      seniorCitizenConcession = SrCitizenConcession.FULL_CONCESSION;
    }


    this.setState({
      ageError,
      age,
      showChildBerthFlag,
      childBerthFlag,
      showSrCitizen,
      showBerth,
      seniorCitizenConcession,
      ctaLabel: this.state.addOrSaveLabel,
    }, () => this._enableCTA());
  };

  _getCtaLabelText = (label, age) => {
    if (isInfant(age)) {
      return _label('infant_cta', undefined, { label });
    } else if (isChild(age)) {
      return _label('child_cta', undefined, { label });
    }
    return _label('adult_cta', undefined, { label });
  };

  _onChangeName = (text) => {
    let nameError = null;
    let fullName = text;
    if (text?.split(' ')?.length > 3){
      fullName = fullName.trim();
    }

    if (!isOnlyEnglishCharacters(fullName)) {
      nameError = _label('type_in_english');
    } else if (!isValidName(fullName)) {
      nameError = _label('no_numeric');
    } else if (!isNameLengthInRange(fullName)) {
      let minError = _label('min_allowed_chars');
      let maxError = _label('max_allowed_chars');
      nameError = fullName.length < 3 ? minError : maxError;
      if (nameError === maxError) {
        trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_LONGNAME_ERROR);
      }
    }
    this.setState({
      nameError,
      fullName,
    }, () => this._enableCTA());
  };

  _enableCTA = () => {
    const {
      nameError, ageError, genderError, fullName, age, gender,
    } = this.state;

    if (!isNull(nameError) || !isNull(ageError) || !isNull(genderError) ||
        isEmpty(fullName) || isEmpty(age) || isEmpty(gender)) {
      this.setState({ enableCTA: false });
    } else {
      this.setState({ enableCTA: true });
    }
  };

  _onMealChanged = (data) => {
    if (data.id === 'V') {
      trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_FOOD_VEG);
    } else if (data.id === 'N') {
      trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_FOOD_NON_VEG);
    }
    this._onFieldChanged('meal')(data);
  };

  _onSrConcessionChange = (data) => {
    if (data.id === 'true') {
      trackTravellerPageEvent(
        RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_SENIOR_CITIZEN_CONCESSION_APPLIED,
      );
    } else {
      trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_SENIOR_CITIZEN_CONCESSION_NOT_APPLIED);
    }
    this._onFieldChanged('seniorCitizenConcession')(data);
  };

  _onFieldChanged = fieldName => (newValue) => {
    this.setState({
      [fieldName]: newValue.obj,
    });
  };

  _validateForm = () => {
    const { age, gender, fullName, nationality, passportNumber, dateOfBirth } = this.state;
    const isIndianNationality = nationality?.code === 'IN';

    // Basic validations for all nationalities
    if (validateForLadiesQuota(this.props.selectedQuota, age, gender)) {
      showShortToast(_label('ladies_quota_warning'));
      return false;
    }
    if (
      this.props.selectedQuota?.code === 'SS' &&
      !validateForSeniorCitizenQuota(this.props.selectedQuota, age, gender)
    ) {
      showShortToast(_label('not_sr_citizen_travelling_in_sr_citizen_quota_warning'));
      return false;
    } else if (!isNameLengthInRange(fullName?.trim())) {
      showShortToast(_label('min_allowed_chars'));
      this.setState(
        {
          nameError: _label('min_allowed_chars'),
          fullName: fullName?.trim(),
        },
        () => this._enableCTA(),
      );
      return false;
    }

    if (!isIndianNationality) {
      const passportMissing = !passportNumber;
      const dobMissing = !dateOfBirth;
      if (passportMissing || dobMissing) {
        this.setState({
          passportError: passportMissing ? ERR_PASSPORT_REQUIRED : null,
          dobError: dobMissing ? ERR_DOB_REQUIRED : null,
        });
        return false;
      }

      if (dateOfBirth?.epoch && age) {
        const dobDate = new Date(dateOfBirth.epoch);
        const today = new Date();
        let computedAge = today.getFullYear() - dobDate.getFullYear();
        const m = today.getMonth() - dobDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < dobDate.getDate())) {
          computedAge--;
        }
        const numericAge = parseInt(age, 10);
        if (!Number.isNaN(numericAge) && numericAge !== computedAge) {
          this.setState({ ageError: ERR_AGE_DOB_MISMATCH });
          return false;
        }
      }
    }

    return true;
  };

  onPressDeleteOnHeader = () => {
    this.setState({
      showDeleteConfirmationDialog: !this.state.showDeleteConfirmationDialog,
    });
  };

  _onDone = async () => {
    if (!this._validateForm() || this.isTravellerBeingAdded) {
      return;
    }
    this.isTravellerBeingAdded = true;
    const {
      travelerId,
      fullName,
      age,
      gender,
      childBerthFlag,
      meal,
      seniorCitizenConcession,
      nationality,
      addOrEditFlag,
      selectedBirth,
      mealPreferenceArray,
    } = this.state;

    const isIndianNationality = nationality?.code === 'IN';

    const traveler = {
      travelerId,
      name: trim(fullName),
      age: parseInt(age, 10),
      gender,
      nationality,
      ...(isIndianNationality
        ? {}
        : {
            dateOfBirth: this.state.dateOfBirth?.epoch ?? null,
            passportNumber: this.state.passportNumber || null,
          }),
      childBerthFlag: (isChild(age) || isInfant(age)) ? childBerthFlag : false,
      berth: (isChild(age) || isInfant(age)) && !childBerthFlag ? Berth.NO_BERTH : selectedBirth,
      meal,
      seniorCitizenConcession: isSrCitizen(age, gender) ? seniorCitizenConcession : SrCitizenConcession.NO_CONCESSION,
      mealPreferenceArray,
    };

    if (isChild(age)) {
      trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_CHILD_INCLUDED);
    }
    const addTravelerMetaData = {};
    addTravelerMetaData.addOrEditFlag = addOrEditFlag;
    addTravelerMetaData.traveler = traveler;
    try {
      await this.props.onAddEditTraveler(addTravelerMetaData);
    } finally {
      this.isTravellerBeingAdded = false;
    }
  };

  _onSaveAndAddTraveler = async () => {
    if (this.isTravellerBeingAdded) {
      return;
    }
    await this._onDone();
    this.props.onAddTraveler();
    const message = _label(TRAVELLER_DETAILS_SAVED).replace('{{fullName}}', this.state.fullName);
    showShortToast(message);
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_SAVEADDMORE,
    );
  };

  render() {
    const {
      nameError,
      fullName,
      ageError,
      age,
      gender,
      childBerthFlag,
      meal,
      seniorCitizenConcession,
      showSrCitizen,
      showBerth,
      showChildBerthFlag,
      enableCTA,
      pageTitle,
      selectedBirth,
      showDeleteButton,
      showDeleteConfirmationDialog,
      id,
    } = this.state;
    const {
      railsListing,
      mealOptions,
      berthOptions,
      srOptions,
      foodChoiceEnabled,
      childBerthMandatory,
      traveler,
      onDeleteTravellerAction,
      selectedQuota,
    } = this.props;

    const srCitizenConcessionNA =
      selectedQuota?.code === 'TQ' || !railsListing.seniorCitizenApplicable;
    const shouldShowMealPreference =
      foodChoiceEnabled && !isNaN(age) && age !== '' && (age > 11 || (isChild && childBerthFlag));
    const srCitizenConcessionWarning = selectedQuota?.code === 'TQ' ? _label('senior_citizen_not_applicable_tatkal') : _label('senior_citizen_not_applicable_train');
    const addMoreTravellerFlowValue = showAddMoreTravellerFlow();
    const isAddMoreTravellerFlowShown = addMoreTravellerFlowValue === AddMoreTravellerFlow.SHOWN;
    const isButtonDisabled = !enableCTA || this.isTravellerBeingAdded;
    return (
      <KeyboardAvoidingViewWrapper>
        <RailsDropDownOverlayContainer id={id}>
          {showDeleteConfirmationDialog && (
            <TravelerDeleteConfDialog
              modalVisible={showDeleteConfirmationDialog}
              traveler={traveler}
              onCancelPress={this.onPressDeleteOnHeader}
              onDeleteTravellerAction={onDeleteTravellerAction}
              id={`${id}_onDeleteButtonClicked`}
            />
          )}
          <ScrollView keyboardShouldPersistTaps="always" testID="add_traveller_scrollView">
            <AddEditRailTravelerHeader
              size="normal"
              title={pageTitle}
              onPressCloseHandler={this.closeHandler}
              showDeleteButton={showDeleteButton}
              onPressDelete={this.onPressDeleteOnHeader}
              id={`${id}_header`}
            />
            <TouchableWithoutFeedback
              onPress={Keyboard.dismiss}
              testID="add_traveller_dismiss_keyboard"
            >
              <View
                style={[travelerStyle.container, { backgroundColor: colors.white }]}
                testID="add_traveller_container"
              >
                <View
                  style={[travelerStyle.paddingHorizontal16, travelerStyle.marginTop16]}
                  testID="add_traveller_form_container"
                >
                  <View testID="add_traveller_name_section">
                    <InputField
                      testID={`${id}_name`}
                      label={_label('name')}
                      placeholder={_label('enter_full_name')}
                      placeholderTextColor={colors.disabledButton}
                      inputProps={{
                        returnKeyType: 'next',
                        autoCapitalize: 'words',
                        onSubmitEditing: () => {
                          Keyboard.dismiss();
                        },
                        maxLength: 16,
                      }}
                      value={fullName}
                      onChangeText={this._onChangeName}
                      isError={!!nameError}
                      errorMessage={nameError}
                      customStyle={{
                        wrapperStyle: styles.inputWrapperStyle,
                        inputFieldStyle: [
                          styles.inputFieldStyle,
                          fontStyle('bold'),
                          getLineHeight(16),
                        ],
                        labelStyle: nameError
                          ? { ...styles.labelStyle, ...styles.labelErrorStyle }
                          : styles.labelStyle,
                      }}
                    />
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: 20,
                      }}
                    >
                      <View style={{ flex: 1, marginRight: 16 }}>
                        <InputField
                          testID={`${id}_age`}
                          label={_label('age')}
                          placeholder={_label('enter_age')}
                          placeholderTextColor={colors.disabledButton}
                          inputProps={{
                            returnKeyType: 'next',
                            keyboardType: 'numeric',
                            onSubmitEditing: () => {
                              Keyboard.dismiss();
                            },
                            maxLength: 3,
                          }}
                          value={age}
                          onChangeText={this._onChangeAge}
                          isError={!!ageError || this.state.editTravellerClicked}
                          errorMessage={
                            this.state.editTravellerClicked ? _label('age_not_specified') : ageError
                          }
                          customStyle={{
                            inputFieldStyle: [
                              styles.inputFieldStyle,
                              fontStyle('bold'),
                              getLineHeight(16),
                            ],
                            labelStyle: styles.labelStyle,
                          }}
                        />
                      </View>
                      <TabbedRadio
                        label={_label('gender')}
                        options={GenderList}
                        keyResolver={(item = {}) => item.key}
                        displayTextResolver={(item = {}) => _label(item.displayText)}
                        onSelect={this._onGenderSelected}
                        selected={gender}
                        selectedValueResolver={(item = {}) => item.value}
                        id={`${id}_gender`}
                      />
                    </View>

                    {showChildBerthFlag && !(!isInfant(age) && childBerthMandatory) && (
                      <ChildBerth
                        isInfant={isInfant(age)}
                        onSelect={this._onChildBerthFlagSelected}
                        childBerthMandatory={childBerthMandatory}
                        childBerthFlag={childBerthFlag}
                        id={`${id}_childBerth`}
                      />
                    )}

                    {showBerth && (
                      <View>
                        <Label label={_label('berth_preference')} />
                        <Text style={[styles.berthPreferenceAdditionalText, fontStyle('regular')]}>
                          {_label('berth_confirmation_guarantee')}
                        </Text>
                        <RadioGroup
                          options={[...berthOptions, Berth.NO_BERTH_PREF]}
                          keyResolver={(item = {}) => item.id}
                          displayTextResolver={(item = {}) => _label(item.text || item.value)}
                          onSelect={this._onBirthPreferenceChange}
                          selected={selectedBirth}
                          valueResolver={(item = {}) => item.obj}
                          selectedValueResolver={(item = {}) => item.key || item.id}
                          id={`${id}_berthPreference`}
                        />
                      </View>
                    )}

                    {shouldShowMealPreference && (
                      <View>
                        <RadioGroup
                          label={_label('meal_preference')}
                          options={mealOptions}
                          onSelect={this._onMealChanged}
                          selected={meal}
                          keyResolver={(item = {}) => item.id}
                          selectedValueResolver={(item = {}) => item.id || item.key}
                          displayTextResolver={(item = {}) => _label(item.text)}
                          containerStyle={{ marginTop: 10 }}
                          id={`${id}_meal`}
                        />
                      </View>
                    )}

                    {showSrCitizen && (
                      <View>
                        <RadioGroup
                          label={_label('sr_ctzn_concession')}
                          options={srOptions}
                          onSelect={this._onSrConcessionChange}
                          selected={
                            srCitizenConcessionNA
                              ? SrCitizenConcession.NO_CONCESSION
                              : seniorCitizenConcession
                          }
                          keyResolver={(item = {}) => item.id}
                          selectedValueResolver={(item = {}) => item.text || item.value}
                          displayTextResolver={(item = {}) => _label(item.text)}
                          containerStyle={{ marginTop: 10, flexDirection: 'column' }}
                          disabled={srCitizenConcessionNA}
                          id={`${id}_seniorCitizenConcession`}
                          optionStyle={{ lineHeight: 18 }}
                        />
                      </View>
                    )}
                    {srCitizenConcessionNA && showSrCitizen && (
                      <View style={styles.srCitizenTatkalWarningContainer}>
                        <Text style={[styles.srCitizenTatkalWarningText, fontStyle('regular')]}>
                          {srCitizenConcessionWarning}
                        </Text>
                      </View>
                    )}
                    <FullScreenDropdown
                      label={_label('nationality')}
                      nationality_indian={_label('indian')}
                      selectedValue={this.state.nationality?.code}
                      onExpand={() =>
                        this.props.logTravellerPageBottomSheetEvents(
                          PDT_TRAVELLER_NATIONALITY_EXPAND,
                        )
                      }
                      onCollapse={() =>
                        this.props.logTravellerPageBottomSheetEvents(
                          PDT_TRAVELLER_NATIONALITY_COLLAPSE,
                        )
                      }
                      onCountrySelect={(country) => {
                        this.props.logTravellerPageBottomSheetEvents(
                          `${PDT_TRAVELLER_NATIONALITY_CHOSEN_PREFIX}${
                            country?.name || country?.label || ''
                          }`,
                        );
                        this.setState({
                          nationality: {
                            code: country.code,
                            name: country.name || country.label,
                          },
                          ...(country && country.code === 'IN'
                            ? {
                                passportNumber: '',
                                dateOfBirth: '',
                                passportError: null,
                                dobError: null,
                              }
                            : {}),
                        });
                      }}
                    />

                    {this.state.nationality && this.state.nationality.code !== 'IN' && (
                      <InternationalTravelerFields
                        passportNumber={this.state.passportNumber}
                        dateOfBirth={this.state.dateOfBirth}
                        passportError={this.state.passportError}
                        dobError={this.state.dobError}
                        onPassportChange={(text) => {
                          const raw = (text || '').toUpperCase();
                          const validShape = /^[A-Z0-9]{6,9}$/.test(raw);
                          const hasDigit = /\d/.test(raw);
                          const isValid = validShape && hasDigit;
                          this.setState({
                            passportNumber: raw,
                            passportError: isValid ? null : ERR_PASSPORT_INVALID,
                          });
                        }}
                        onDateOfBirthChange={(selected) => {
                          this.setState(
                            {
                              dateOfBirth: selected,
                              dobError: null,
                            },
                            () => this._enableCTA(),
                          );
                        }}
                        id={id}
                      />
                    )}

                    {Boolean(age) && this.railsListingUtilObj.isInfant(age) && !childBerthFlag && (
                      <View style={{ marginTop: 20 }}>
                        <WarningBox
                          cardStyle={travelerStyle.greenBoxWarning}
                          textStyle={[
                            travelerStyle.warningBox,
                            travelerStyle.latoRegularTextGreen12,
                          ]}
                          value={`${_label('child_seat')}. ${_label('age_proof_required_warning')}`}
                        />
                      </View>
                    )}
                  </View>

                  <View style={{ height: 80 }} />
                </View>
              </View>
            </TouchableWithoutFeedback>
          </ScrollView>
          {isAddMoreTravellerFlowShown && this.state.addOrEditFlag ? (
            <View style={styles.addMoreTravellerContainer} testID="add_traveller_buttons_container">
              <View
                style={styles.addMoreTravellerLeft}
                testID="add_traveller_save_and_add_more_container"
              >
                {isButtonDisabled ? (
                  <DisabledCTAButton
                    label={_label(SAVE_AND_ADD_MORE, { uppercase: true })}
                    btnStyle={styles.disabledSaveAndMoreButton}
                    textStyle={[{ color: colors.white }]}
                    testID="add_traveller_save_and_add_more_disabled"
                  />
                ) : (
                  <TouchableOpacity
                    style={[
                      styles.editPassengerButton,
                      this.isTravellerBeingAdded && styles.disabledSaveAndMoreButton,
                    ]}
                    onPress={this._onSaveAndAddTraveler}
                    disabled={this.isTravellerBeingAdded}
                    testID="add_traveller_save_and_add_more_button"
                  >
                    <Text
                      style={styles.editPassengerButtonText}
                      testID="add_traveller_save_and_add_more_text"
                    >
                      {_label(SAVE_AND_ADD_MORE, { uppercase: true })}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
              <View style={styles.addMoreTravellerRight} testID="add_traveller_save_container">
                {isButtonDisabled ? (
                  <DisabledCTAButton
                    label={_label(SAVE, { uppercase: true })}
                    btnStyle={styles.disabledSaveButton}
                    textStyle={[{ color: colors.white }]}
                    testID="add_traveller_save_disabled"
                    color={['#989898', '#989898']}
                  />
                ) : (
                  <CtaButton
                    label={_label(SAVE, { uppercase: true })}
                    onPress={this._onDone}
                    btnStyle={styles.saveButton}
                    disabled={this.isTravellerBeingAdded}
                    textStyle={styles.ctaButtonText}
                    testID="add_traveller_save_button"
                  />
                )}
              </View>
            </View>
          ) : (
            <View style={styles.buttonContainer} testID={`${id}_saveButton`}>
              {isButtonDisabled ? (
                <DisabledCTAButton
                  label={_label(SAVE, { uppercase: true })}
                  btnStyle={styles.disabledSaveButton}
                  textStyle={[{ color: colors.white }]}
                  color={['#989898', '#989898']}
                />
              ) : (
                <CtaButton
                  label={_label(SAVE, { uppercase: true })}
                  onPress={this._onDone}
                  btnStyle={[
                    styles.saveButton,
                    !this.state.addOrEditFlag && styles.editModeSaveButton,
                  ]}
                  disabled={this.isTravellerBeingAdded}
                  textStyle={styles.ctaButtonText}
                />
              )}
            </View>
          )}
        </RailsDropDownOverlayContainer>
      </KeyboardAvoidingViewWrapper>
    );
  }
}

const styles = StyleSheet.create({
  berthPreferenceAdditionalText: {
    color: colors.lightTextColor,
    fontSize: 12,
    marginTop: -5,
  },
  srCitizenTatkalWarningContainer: {
    backgroundColor: colors.creamWhite,
    padding: 5,
    marginBottom: 20,
    marginTop: -10,
  },
  srCitizenTatkalWarningText: {
    color: colors.lightYello,
    fontSize: 12,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    padding: 10,
    backgroundColor: colors.white,
  },
  labelStyle: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.bold,
  },
  inputFieldStyle: {
    fontFamily: fonts.bold,
    paddingVertical: 14,
  },
  labelErrorStyle: {
    color: colors.red,
  },
  inputWrapperStyle: {
    marginBottom: 20,
  },
  addMoreTravellerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  addMoreTravellerLeft: {
    flex: 1,
    marginRight: 8,
    marginBottom: 10,
  },
  addMoreTravellerRight: {
    flex: 1,
    marginLeft: 8,
    marginBottom: 10,
  },
  disabledSaveAndMoreButton: {
    borderWidth: 0,
    borderRadius: 4,
    marginTop: 10,
    backgroundColor: colors.grey,
    paddingHorizontal: 0,
  },
  disabledSaveButton: {
    borderWidth: 0,
    borderRadius: 4,
    marginTop: 10,
  },
  disabledButtonText: [{ color: colors.white, paddingHorizontal: 0 }],
  saveButton: {
    borderWidth: 0,
    borderRadius: 4,
    marginTop: 10,
  },
  editPassengerButton: {
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    marginTop: 10,
    backgroundColor: colors.white,
    borderColor: colors.primary,
  },
  editPassengerButtonText: {
    fontSize: 16,
    color: colors.primary,
    lineHeight: 19.2,
    fontWeight: '700',
  },
  editModeSaveButton: {
    backgroundColor: colors.primary,
    borderColor: colors.white,
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: 12,
  },
});

AddRailsTraveler.propTypes = {
  mealOptions: PropTypes.array.isRequired,
  berthOptions: PropTypes.array.isRequired,
  srOptions: PropTypes.array.isRequired,
  nationalityOptions: PropTypes.array.isRequired,
  traveler: PropTypes.object,
  selectedQuota: PropTypes.object,
  foodChoiceEnabled: PropTypes.bool.isRequired,
  onAddEditTraveler: PropTypes.func.isRequired,
  childBerthMandatory: PropTypes.bool.isRequired,
  railsListing: PropTypes.object,
  labels: PropTypes.object.isRequired,
  onDeleteTravellerAction: PropTypes.func.isRequired,
  onAddTraveler: PropTypes.func.isRequired,
  logTravellerPageBottomSheetEvents: PropTypes.func,
  id: PropTypes.string,
};

AddRailsTraveler.defaultProps = {
  selectedQuota: {},
  railsListing: {},
};
