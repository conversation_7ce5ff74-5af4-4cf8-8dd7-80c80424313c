
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';

import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import Label from '@mmt/legacy-commons/Common/Components/Label/label';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../../vernacular/VernacularUtils';

export default function ChildBerth(props) {
  props = {
    ...props,
    childBerthMandatory:
      typeof props.childBerthMandatory === 'undefined' ? false : props.childBerthMandatory,
    childBerthFlag: typeof props.childBerthFlag === 'undefined' ? false : props.childBerthFlag,
    isInfant: typeof props.isInfant === 'undefined' ? false : props.isInfant,
  };

  const { childBerthMandatory, onSelect, childBerthFlag, isInfant } = props;
  const pointerEvents = (!isInfant && childBerthMandatory) ? 'none' : 'auto';
  const radioContainerStyle = (!isInfant && childBerthMandatory) ? { opacity: 0.5 } : {};
  return (
    <View testID={props?.id}>
      <Label label={_label('reservation_for_child')} />
      {
        (!isInfant && childBerthMandatory) &&
        <Text style={styles.childBerthMandatoryText} testID="childBerth_compulsaryBerth">
          {_label('compulsary_berth')}
        </Text>
      }
      <View pointerEvents={pointerEvents} style={radioContainerStyle}>
        <TouchableRipple feedbackColor={colors.transparent} onPress={() => onSelect(true)}>
          <View style={styles.radioContainer} testID={`${props?.id}_reserveBerth`}>
            <RadioButton isSelected={childBerthFlag} onPress={() => onSelect(true)} />
            <View style={styles.labelContainer}>
              <Text style={[styles.labelText, fontStyle('regular'), getLineHeight(14)]}>
                {_label('reserve_berth')}
              </Text>
              <Text style={[styles.additionalText, fontStyle('regular'), getLineHeight(12)]}>
                {_label('full_ticket_price')}
              </Text>
            </View>
          </View>
        </TouchableRipple>
      </View>
      <View
        pointerEvents={pointerEvents}
        style={radioContainerStyle}
        testID="childBerth_dontReserveBerth"
      >
        <TouchableRipple
          feedbackColor={colors.transparent}
          onPress={() => onSelect(false)}
          testID="childBerth_dontReserveBerth_touchableRipple"
        >
          <View style={styles.radioContainer} testID={`${props?.id}_dontReserveBerth`}>
            <RadioButton
              isSelected={!childBerthFlag}
              onPress={() => onSelect(false)}
              testID="childBerth_dontReserveBerth_radioButton"
            />
            <View style={styles.labelContainer}>
              <Text
                style={[styles.labelText, fontStyle('regular'), getLineHeight(14)]}
                testID="childBerth_dontReserveBerth_text"
              >
                {_label('dont_reserve_berth')}
              </Text>
              <Text
                style={[styles.additionalText, fontStyle('regular'), getLineHeight(12)]}
                testID="childBerth_dontReserveBerth_additionalText"
              >
                {isInfant ? _label('no_ticket_price') : _label('half_ticket_price')}
              </Text>
            </View>
          </View>
        </TouchableRipple>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  childBerthMandatoryText: {
    color: colors.yello,
    marginTop: -10,
    marginBottom: 10,
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  labelContainer: {
    marginLeft: 12,
  },
  labelText: {
    color: colors.black,
    fontSize: 14,
  },
  additionalText: {
    fontSize: 12,
  },
});

ChildBerth.propTypes = {
  childBerthMandatory: PropTypes.bool,
  onSelect: PropTypes.func.isRequired,
  childBerthFlag: PropTypes.bool,
  isInfant: PropTypes.bool,
  id: PropTypes.string,
};
