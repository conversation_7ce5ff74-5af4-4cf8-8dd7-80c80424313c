import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import RailsAssets from '@mmt/rails/src/Utils/Assets/RailsAssets';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import { _label } from '../../../../vernacular/AppLanguage';

const { height: screenHeight } = Dimensions.get('window');
const ITEM_HEIGHT = 50;

interface DateOfBirthBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (date: string) => void;
  initialDate: string | null;
}

const DateOfBirthBottomSheet = ({
  visible,
  onClose,
  onConfirm,
  initialDate = null,
}: DateOfBirthBottomSheetProps) => {
  const [selectedDay, setSelectedDay] = useState(6);
  const [selectedMonth, setSelectedMonth] = useState('Jun');
  const [selectedYear, setSelectedYear] = useState(1988);

  const dayRef = useRef(null);
  const monthRef = useRef(null);
  const yearRef = useRef(null);

  const currentYear = useMemo(() => new Date().getFullYear(), []);
  const years = useMemo(
    () => Array.from({ length: currentYear - 1900 + 1 }, (_, i) => 1900 + i),
    [currentYear],
  );

  const days = useMemo(() => Array.from({ length: 31 }, (_, i) => i + 1), []);

  const months = useMemo(
    () => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    [],
  );

  useEffect(() => {
    const parseInitialValue = (value) => {
      if (!value) {
        return null;
      }
      if (value instanceof Date && !isNaN(value)) {
        return {
          day: value.getDate(),
          month: months[value.getMonth()],
          year: value.getFullYear(),
        };
      }
      if (typeof value === 'string') {
        const match = value.match(/\s*(\d{1,2})\s*-\s*([A-Za-z]{3})\s*-\s*(\d{4})\s*/);
        if (match) {
          const day = Number(match[1]);
          const monthAbbr = match[2].slice(0, 1).toUpperCase() + match[2].slice(1, 3).toLowerCase();
          const year = Number(match[3]);
          if (months.includes(monthAbbr)) {
            return { day, month: monthAbbr, year };
          }
        }
        const d = new Date(value);
        if (!isNaN(d)) {
          return { day: d.getDate(), month: months[d.getMonth()], year: d.getFullYear() };
        }
      }
      return null;
    };

    const parsed = parseInitialValue(initialDate);
    if (parsed) {
      setSelectedDay(parsed.day);
      setSelectedMonth(parsed.month);
      setSelectedYear(parsed.year);
      return;
    }
    // Default selection when no initialDate provided
    setSelectedDay(6);
    setSelectedMonth('Jun');
    setSelectedYear(1988);
  }, [initialDate, months]);

  useEffect(() => {
    if (!visible) {
      return;
    }
    const dayIndex = days.indexOf(selectedDay);
    const monthIndex = months.indexOf(selectedMonth);
    const yearIndex = years.indexOf(selectedYear);

    dayRef.current?.scrollToOffset({
      offset: Math.max(0, dayIndex) * ITEM_HEIGHT,
      animated: false,
    });
    monthRef.current?.scrollToOffset({
      offset: Math.max(0, monthIndex) * ITEM_HEIGHT,
      animated: false,
    });
    yearRef.current?.scrollToOffset({
      offset: Math.max(0, yearIndex) * ITEM_HEIGHT,
      animated: false,
    });
  }, [visible, days, months, years, selectedDay, selectedMonth, selectedYear]);

  const handleConfirm = () => {
    const monthIndex = months.indexOf(selectedMonth);
    const safeMonthIndex = monthIndex >= 0 ? monthIndex : 0;
    const date = new Date(selectedYear, safeMonthIndex, selectedDay);
    const formattedDate = `${selectedDay} - ${selectedMonth} - ${selectedYear}`;
    const epochMilliseconds = date.getTime();
    onConfirm(formattedDate, date, epochMilliseconds);
    onClose();
  };

  // Clamp invalid dates (e.g., 31 Apr -> 30 Apr, 30 Feb -> 28/29 Feb) and auto-scroll day list
  useEffect(() => {
    const monthIndex = months.indexOf(selectedMonth);
    if (monthIndex < 0) {
      return;
    }
    const maxDays = new Date(selectedYear, monthIndex + 1, 0).getDate();
    if (selectedDay > maxDays) {
      const correctedDay = maxDays;
      setSelectedDay(correctedDay);
      const correctedIndex = days.indexOf(correctedDay);
      // Smoothly scroll day list to corrected index
      requestAnimationFrame(() => {
        dayRef.current?.scrollToOffset({
          offset: Math.max(0, correctedIndex) * ITEM_HEIGHT,
          animated: true,
        });
      });
    }
  }, [selectedMonth, selectedYear, selectedDay, months, days]);

  const renderPickerColumn = (data, selectedValue, onSelect, keyExtractor, listRef) => {
    const selectedIndex = data.indexOf(selectedValue);
    return (
      <View style={styles.pickerColumn}>
        <FlatList
          ref={listRef}
          data={data}
          keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
          snapToInterval={ITEM_HEIGHT}
          snapToAlignment="start"
          bounces={false}
          decelerationRate="fast"
          onMomentumScrollEnd={(e) => {
            const { y } = e.nativeEvent.contentOffset;
            const centerIndex = Math.round(y / ITEM_HEIGHT);
            const clampedIndex = Math.max(0, Math.min(centerIndex, data.length - 1));
            const value = data[clampedIndex];
            listRef.current?.scrollToOffset({ offset: clampedIndex * ITEM_HEIGHT, animated: true });
            if (value !== undefined) {
              onSelect(value);
            }
          }}
          renderItem={({ item, index }) => {
            const distance = Math.abs(index - selectedIndex);
            const shadeStyle =
              distance === 0
                ? styles.selectedPickerText
                : distance === 1
                ? styles.nearPickerText
                : styles.farPickerText;
            return (
              <TouchableOpacity style={styles.pickerItem} onPress={() => onSelect(item)}>
                <Text style={[styles.pickerText, shadeStyle, fontStyle('medium')]}>{item}</Text>
              </TouchableOpacity>
            );
          }}
          ListHeaderComponent={<View style={{ height: ITEM_HEIGHT * 2 }} />}
          ListFooterComponent={<View style={{ height: ITEM_HEIGHT * 2 }} />}
          initialScrollIndex={Math.max(0, data.indexOf(selectedValue))}
          getItemLayout={(data, index) => ({
            length: ITEM_HEIGHT,
            offset: ITEM_HEIGHT * index,
            index,
          })}
        />
      </View>
    );
  };

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={styles.overlay}>
        <TouchableOpacity style={styles.backdrop} activeOpacity={1} onPress={onClose} />
        <View style={styles.bottomSheet}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, fontStyle('bold')]}>{_label('date_of_birth')}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Image source={RailsAssets.cancelIcon} style={styles.closeImage} />
            </TouchableOpacity>
          </View>

          <View style={styles.pickerContainer}>
            <View pointerEvents="none" style={styles.centerIndicator} />
            {renderPickerColumn(
              days,
              selectedDay,
              setSelectedDay,
              (item) => item.toString(),
              dayRef,
            )}

            {/* Month Picker */}
            {renderPickerColumn(months, selectedMonth, setSelectedMonth, (item) => item, monthRef)}

            {/* Year Picker */}
            {renderPickerColumn(
              years,
              selectedYear,
              setSelectedYear,
              (item) => item.toString(),
              yearRef,
            )}
          </View>

          {/* Selected Date Display */}
          <View style={styles.selectedDateContainer}>
            <Text style={[styles.selectedDateText, fontStyle('medium')]}>
              {selectedDay} - {selectedMonth} - {selectedYear}
            </Text>
          </View>

          <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
            <Text style={[styles.confirmButtonText, fontStyle('bold')]}>
              {_label('confirm').toUpperCase()}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: colors.greyTransparent2,
  },
  backdrop: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34,
    maxHeight: screenHeight * 0.7,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  title: {
    fontSize: 18,
    color: colors.black,
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeImage: {
    width: 24,
    height: 24,
  },
  pickerContainer: {
    flexDirection: 'row',
    height: 250,
    paddingHorizontal: 35,
    paddingVertical: 0,
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: 4,
  },
  pickerItem: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    borderRadius: 8,
  },
  pickerText: {
    fontSize: 16,
    color: colors.textGrey,
  },
  selectedPickerText: {
    color: colors.black,
    fontWeight: 'bold',
  },
  nearPickerText: {
    opacity: 0.6,
  },
  farPickerText: {
    opacity: 0.3,
  },
  centerIndicator: {
    position: 'absolute',
    left: 54,
    right: 46,
    height: 50,
    top: (250 - 50) / 2,
    backgroundColor: colors.greyTransparentBackground,
    borderRadius: 12,
  },
  selectedDateContainer: {
    backgroundColor: colors.lightGrey,
    marginHorizontal: 20,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  selectedDateText: {
    fontSize: 18,
    color: colors.black,
  },
  confirmButton: {
    backgroundColor: colors.azure || '#4285F4',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    color: colors.white,
  },
});

export default DateOfBirthBottomSheet;
