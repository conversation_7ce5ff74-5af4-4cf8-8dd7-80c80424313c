import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import InputField from '@Frontend_Ui_Lib_App/InputField';
import { fontStyle } from '../../../../vernacular/VernacularUtils';
import { getLineHeight } from 'packages/legacy-commons/Common/Components/Vernacular/Utils';
import { _label } from '../../../../vernacular/AppLanguage';
import DateOfBirthBottomSheet from './DateOfBirthBottomSheet';
import { IRCTC_DOB_MANDATORY, IRCTC_PASSPORT_MANDATORY } from '../../../../Utils/RailsConstant';

interface InternationalTravelerFieldsProps {
  passportNumber: string;
  dateOfBirth: string;
  passportError: string;
  dobError: string;
  onPassportChange: (text: string) => void;
  onDateOfBirthChange: (date: string) => void;
  id: string;
}

const InternationalTravelerFields = ({
  passportNumber,
  dateOfBirth,
  passportError,
  dobError,
  onPassportChange,
  onDateOfBirthChange,
  id = 'international_traveler_fields',
}: InternationalTravelerFieldsProps) => {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleDateConfirm = (formattedDate, dateObject, epochMilliseconds) => {
    onDateOfBirthChange({ display: formattedDate, epoch: epochMilliseconds });
    setShowDatePicker(false);
  };

  return (
    <View style={styles.container}>
      <InputField
        testID={`${id}_passport`}
        placeholder={_label('passport_number')}
        placeholderTextColor={passportError ? colors.red : colors.disabledButton}
        inputProps={{
          returnKeyType: 'done',
          autoCapitalize: 'characters',
          maxLength: 20,
        }}
        value={passportNumber}
        onChangeText={onPassportChange}
        isError={!!passportError}
        errorMessage={passportError}
        customStyle={{
          wrapperStyle: styles.inputWrapperStyle,
          inputFieldStyle: [styles.inputFieldStyle, fontStyle('bold'), getLineHeight(16)],
        }}
      />
      {!passportError && (
        <Text style={[styles.helperText, fontStyle('regular')]}>
          {IRCTC_PASSPORT_MANDATORY}
        </Text>
      )}

      <View style={styles.dobContainer}>
        <TouchableOpacity
          style={[styles.dobInput, dobError && styles.dobInputError]}
          onPress={() => setShowDatePicker(true)}
        >
          <Text
            style={[
              styles.dobText,
              !(dateOfBirth && dateOfBirth.display) &&
                (dobError ? styles.dobPlaceholderError : styles.dobPlaceholder),
              fontStyle('bold'),
              getLineHeight(16),
            ]}
          >
            {dateOfBirth?.display || _label('date_of_birth').toUpperCase()}
          </Text>
        </TouchableOpacity>
        {dobError && <Text style={styles.errorText}>{dobError}</Text>}
      </View>
      {!dobError && (
        <Text style={[styles.helperText, fontStyle('regular')]}>
          {IRCTC_DOB_MANDATORY}
        </Text>
      )}

      <DateOfBirthBottomSheet
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onConfirm={handleDateConfirm}
        initialDate={dateOfBirth?.display || dateOfBirth}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  inputWrapperStyle: {
    marginBottom: 0,
  },
  inputFieldStyle: {
    fontFamily: fonts.bold,
    paddingVertical: 14,
  },
  helperText: {
    fontSize: 12,
    color: colors.textGrey,
    marginTop: 4,
    marginBottom: 5,
    marginLeft: 8,
  },
  dobContainer: {
    marginBottom: 0,
    marginTop: 8,
  },
  dobInput: {
    borderWidth: 1,
    borderColor: colors.lightGrey,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 14,
    marginTop: 8,
    backgroundColor: colors.white,
  },
  dobInputError: {
    borderColor: colors.red,
  },
  dobText: {
    fontSize: 16,
    color: colors.black,
  },
  dobPlaceholder: {
    color: colors.disabledButton,
  },
  dobPlaceholderError: {
    color: colors.red,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    marginTop: 4,
  },
});

export default InternationalTravelerFields;
