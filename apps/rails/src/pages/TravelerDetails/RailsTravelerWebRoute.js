/* eslint-disable */
import React from 'react';
import {View, Text} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import fecha from 'fecha';
import url from 'url';
import {connect} from 'react-redux';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import {withRouterState} from '../../../../../../web/WebRouter';
import TravelerDetails from './Containers/TravelerDetailsContainer';
import railsTravelerReducer from './TravelerDetailsReducer';
import RailsTravellerDeeplink from "./RailsTravellerDeeplink";
import railsListingReducer from "../NewListing/RailsListingReducer";
import railsReviewReducer from '../Review/RailsReviewReducer';
import isEmpty from "lodash/isEmpty";
import AddRailsTraveler from "./Containers/AddRailsTravelerContainer";

class RailsTravelerWeb extends React.Component {
  constructor(props){
    super(props);
    const urlObj = url.parse(window.location.href, window.location.search);
    const {query = {}} = urlObj;
    this.state = {
      childProps: {
        originStation: {
          cityName: query.from_city,
          code: query.from,
          stationName: query.from_station
        },
        destinationStation: {
          cityName: query.to_city,
          code: query.to,
          stationName: query.toStation
        },
        departureDate: fecha.parse(query.departure, 'YYYYMMDD'),
        trainNumber: query.trainNumber,
        classCode: query.classCode,
        quota: query.quota
      }
    }
  }

  render() {
    if (this.props.prefillFlag) {
      const {originStation, destinationStation, departureDate, trainNumber, classCode, quota} = this.state.childProps;
      const deeplinkData = {
        from: originStation.code,
        to: destinationStation.code,
        date: fecha.format(departureDate, 'YYYYMMDD'),
        trainNumber,
        classCode,
        quota
      };
      return (
        <RailsTravellerDeeplink {...deeplinkData}/>
      );
    }
    return (
      <TravelerDetails {...this.state.childProps} />
    )
  }
}

injectAsyncReducer('railsListing', railsListingReducer);
injectAsyncReducer('railsTraveler', railsTravelerReducer);
injectAsyncReducer('railsReview', railsReviewReducer);

const mapStateToProps = (state, ...ownProps) => {
  const {railsListing: {selectedTrainInfo}} = state;
  const prefillFlag =  isEmpty(selectedTrainInfo);
  return {
    prefillFlag,
    ...ownProps
  };
};

export const RailsTravelerContainer = connect(mapStateToProps, null)(RailsTravelerWeb);

const RailsTravelerRoutes = () => (
  <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
    <Switch>
      <Route exact path="/railways/search/railsTravelerPage" component={withRouterState(RailsTravelerContainer)} />
      <Route exact path="/railways/search/railsTravelerPage/addTraveller" component={withRouterState(AddRailsTraveler)} />
    </Switch>
  </View>
);

export default withRouter(RailsTravelerRoutes);
