import React, { useEffect, useState, useRef } from 'react';
import { NativeModules, Platform, View } from 'react-native';
import { connect } from 'react-redux';
import WebView from 'react-native-webview';
import isEmpty from 'lodash/isEmpty';
import CommonHeader from '@mmt/legacy-commons/Common/Components/Header/CommonHeader';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { Actions } from '../../navigation';
import { _label } from '../../vernacular/AppLanguage';
import { IRCTC_CREATE_ACCOUNT_URL } from '../../RailsConfig';
import { fetchUserAddressDataBasedOnUserIP } from '../../Utils/RailsAPIRepository';
import { configKeys } from 'apps/rails/src/configStore/Common/constants';
import { getConfigStore } from 'apps/rails/src/configStore/Common/RailsConfigStoreUtils';
import { trackEvar22Event } from 'apps/rails/src/railsAnalytics';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { TRAVELERS_PAGE_TRACKING_KEY_NEW } from 'apps/rails/src/Utils/RailsConstant';
import { updateUsername } from 'apps/rails/src/pages/User/UserVerification/UserVerificationActions';
import { logIRCTCUsernameCreatedSuccessfully } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtRailsIRCTCusernameForm';
import PropTypes from 'prop-types';

const HEADER_TEXT = _label('create_irctc_account');

const errorFieldMapping = {
  userName: 'UN',
  usrPwd: 'P',
  cnfUsrPwd: 'CP',
  prefLang: 'PL',
  secque: 'SQ',
  secAns: 'SA',
  firstName: 'FN',
  middleName: 'MN',
  lastname: 'LN',
  occupation: 'SO',
  dob: 'DOB',
  martitalS: 'MU',
  resCountry: 'C',
  gender: 'G',
  email: 'E',
  isd: 'CC',
  mobile: 'M',
  nationality: 'N',
  resAddress1: 'DN',
  resAddress2: 'SL',
  resAddress3: 'AL',
  resPinCode: 'PC',
  resCity: 'CTY',
  resState: 'ST',
  resPostOff: 'PO',
  officeSameAsRes: 'CROA',
  captcha: 'CAP',
  termCondition: 'TNC',
};

/* Omniture Constants */
const IRCTC_USERID_FORM_ERROR = 'IRCTC_UserID_FORM_ERROR_';
const IRCTC_USERID_FORM_2V_ERROR = 'IRCTC_UserID_FORM_2V_ERROR_';
const IRCTC_USERID_NOT_ENTERED_BOTH_OTP = 'IRCTC_UserID_FORM_2V_ERROR_OTP_NOT_ENTERED';
const IRCTC_USERID_ENTERED_INCORRECT_OTP = 'IRCTC_UserID_FORM_2V_ERROR_OTP_M_E';
const IRCTC_USERID_ENTERED_INCORRECT_EMAIL_OTP = 'IRCTC_UserID_FORM_2V_ERROR_OTP_E';
const IRCTC_USERID_CREATED_SUCCESSFULLY = 'IRCTC_UserID_FORM_Success';
const IRCTC_VERIFY_OTP_BUTTON_CLICKED = 'IRCTC_UserID_FORM_Verify_OTP_Clicked';
const IRCTC_SUBMIT_CLICKED = 'IRCTC_UserID_FORM_Submit_Clicked';

const CreateIRCTCAccountWebView = ({ searchContext, setUserNameToRedux }) => {
  const [userPersonalDetails, setUserPersonalDetails] = useState({});
  const [irctcUrl, setIrctcUrl] = useState(IRCTC_CREATE_ACCOUNT_URL);
  const [jsInjectionScriptFromConfigStore, setJsInjectionScriptFromConfigStore] = useState(null);

  let createIrctcAccountRef = useRef();

  const { UserSessionModule } = NativeModules;

  useEffect(() => {
    async function userDetails() {
      try {
        let userPersonalDetails = {};

        const isUserLoggedIn = await UserSessionModule?.isUserLoggedIn();

        const userAddressFromGstin = await fetchUserAddressDataBasedOnUserIP();
        const userAddressData = userAddressFromGstin?.data;

        if (isUserLoggedIn) {
          const userDetails = await UserSessionModule.getUserDetails();

          if (Platform.OS === 'android') {
            let alternateUserDetails = userDetails;

            if (typeof userDetails === 'string') {
              alternateUserDetails = JSON.parse(userDetails);
            }

            var {
              cupFname,
              cupLname,
              cupEmailid,
              cupGender,
              state: cupState,
              country: cupCountry,
            } = alternateUserDetails;

            const { verifiedMobileNumber } = alternateUserDetails;
            var androidMobile = verifiedMobileNumber[0]?.mobileNumber;
          }

          const { FirstName, LastName, Email, Phone, gender, country, state } = userDetails;

          userPersonalDetails = {
            firstName: FirstName || cupFname,
            lastName: LastName || cupLname,
            email: Email || cupEmailid,
            phone: Phone || androidMobile,
            state: state || cupState,
            country: country || cupCountry,
            gender: gender?.toLowerCase() || cupGender?.toLowerCase(),
          };
        }

        if (userAddressFromGstin?.success) {
          const addressDetails = userAddressData?.locationHierarchy;
          const primaryAddress = addressDetails?.area[0];
          const secondaryAddress = addressDetails?.area[1];

          const streetLane = primaryAddress?.name;
          const areaLocality = secondaryAddress?.name;
          const city = addressDetails?.city[0].name;
          const postalCode = primaryAddress?.postal_code;

          userPersonalDetails = {
            ...userPersonalDetails,
            streetLane,
            areaLocality,
            city,
            pincode: postalCode,
          };
        }
        setUserPersonalDetails(userPersonalDetails);
      } catch (e) {
        throw new Error(e);
      }
    }
    userDetails();

    async function getIrctcUrl() {
      const irctcUrl = await getConfigStore(configKeys.RAILS_IRCTC_USER_REGISTRATION);
      const scriptFromConfig = await getConfigStore(configKeys.RAILS_IRCTC_NEW_USER_REGISTERATION, false);
      setIrctcUrl(irctcUrl);
      setJsInjectionScriptFromConfigStore(scriptFromConfig?.script);
    }
    getIrctcUrl();
  }, []);

  const _onMessage = (event) => {
    let messageData;
    try {
      messageData = JSON.parse(event.nativeEvent.data);

      if (messageData.element === 'formErrorsAfterRegisterClick') {
        const errorFields = messageData.data;
        let errorStr = '';

        errorFields.forEach((field, id) => {
          if (id === errorFields.length - 1) {
            errorStr = errorStr + errorFieldMapping[field];
          } else {
            errorStr = `${errorStr + errorFieldMapping[field]}_`;
          }
        });

        const finalEval22Value = IRCTC_USERID_FORM_ERROR + errorStr;
        trackEvar22Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, finalEval22Value);
      }
      if (messageData.element === 'formErrorsAfterOkClick') {
        const errorFields = messageData.data;
        const errorItems = Object.keys(errorFields);
        let errorStr = '';

        errorItems?.forEach((field) => {
          if (errorFields[field]) {
            errorStr = `${errorStr + errorFieldMapping[field]}_`;
          }
        });

        let finalEval22Value = IRCTC_USERID_FORM_2V_ERROR + errorStr;
        finalEval22Value = finalEval22Value.slice(0, -1);
        trackEvar22Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, finalEval22Value);
      }
      if (messageData.element === 'userNotEnteredBothOtp') {
        trackEvar22Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERID_NOT_ENTERED_BOTH_OTP);
      }
      if (messageData.element === 'userEnteredBothIncorrectMobileEmailOtp') {
        trackEvar22Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERID_ENTERED_INCORRECT_OTP);
      }
      if (messageData.element === 'userEnteredBothIncorrectEmailOtp') {
        trackEvar22Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERID_ENTERED_INCORRECT_EMAIL_OTP);
      }
      if (messageData.element === 'successfulUserNameCreation') {
        const userName = messageData.data;
        setUserNameToRedux(userName);
        logIRCTCUsernameCreatedSuccessfully(searchContext);
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERID_CREATED_SUCCESSFULLY);
        // Actions.pop(); // Commenting code to bring back user to Rails Funnel from IRCTC username creation page
      }
      if (messageData.element === 'submitButtonClicked') {
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_SUBMIT_CLICKED);
      }
      if (messageData.element === 'otpVerifyButtonClicked') {
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_VERIFY_OTP_BUTTON_CLICKED);
      }
    } catch (e) {
      console.log(e);
    }
  };

  const _injectJavaScriptCode = () => {
    const {
      firstName,
      lastName,
      email,
      phone,
      gender,
      state,
      streetLane,
      areaLocality,
      pincode,
    } = userPersonalDetails;

    const backUpScript = `
        const loginElement = document.querySelector('.userReg3 > div > span:last-of-type');
        if(loginElement) {
          loginElement.style.display = 'none';
        }

        function postDataToReactNative(elementName, val) {
          const postData = {
                  element: elementName,
                  data: val,
                }
          window.ReactNativeWebView.postMessage(JSON.stringify(postData));  
        }
        
        setTimeout(function() {
            const adIntervalId = setInterval(() => {
              const googleAds = document.querySelectorAll("[id^='google_ads_iframe_']");
              if(googleAds?.length > 0) {
              googleAds.forEach(ad => ad.style.display = 'none')
              }
            }, 500);

            setTimeout(() => {
              var adIntervalId2 = setInterval(() => {
                const googleAds2 = document.querySelectorAll("[id^='google_ads_iframe_']");
                if(googleAds2?.length > 0) {
                  googleAds2.forEach(ad => ad.style.display = 'none')
                }
              }, 500);
            }, 15000);

            const firstName = document.getElementById('firstName');
            const areaLocalityEl = document.getElementById('resAddress3');

            // If First Name Element or Area Locality Element is present then we can infer that the form is OLD form
            if(firstName && areaLocalityEl) {
              if('${firstName}' !== 'undefined') {
                firstName.value = '${firstName}';
                firstName.dispatchEvent(new Event('input', { bubbles: true }));
                firstName.dispatchEvent(new Event('change', { bubbles: true }));
              }

              const lastName = document.getElementById('lastname');
              if('${lastName}' !== 'undefined') lastName.value = '${lastName}';

              const email = document.getElementById('email');
              if('${email}' !== 'undefined') {
                email.value = '${email}';
                email.dispatchEvent(new Event('input', { bubbles: true }));
                email.dispatchEvent(new Event('change', { bubbles: true }));
              }

              const mobile = document.getElementById('mobile');
              if(${phone}) {
                mobile.value = '${phone}';
                mobile.dispatchEvent(new Event('input', { bubbles: true }));
                mobile.dispatchEvent(new Event('change', { bubbles: true }));
              }

              const country = document.getElementsByTagName("select")[1];
              if(country) {
                country.value = '94';
                country.dispatchEvent(new Event('change', { bubbles: true }));
              }
              
              const maleGenderElement = document.querySelector("[aria-label='Male']");
              const femaleGenderElement = document.querySelector("[aria-label='Female']");
              const transGenderElement = document.querySelector("[aria-label='Transgender']");

              if('${gender}' === 'male') {
                  maleGenderElement.click();
              } else if('${gender}' === 'female'){
                  femaleGenderElement.click(); 
              } else transGenderElement.click();
              
              const streetLaneEl = document.getElementById('resAddress2');
              if('${streetLane}' !== 'undefined') streetLaneEl.value = '${streetLane}';

              const areaLocalityEl = document.getElementById('resAddress3');
              if('${areaLocality}' !== 'undefined') areaLocalityEl.value = '${areaLocality}';

              const stateEl = document.getElementById('resState');
              if('${state}' !== 'undefined') stateEl.value = '${state}';

              const phoneEl = document.getElementById('resPhone');
              if('${phone}' !== 'undefined') {
                phoneEl.value = '${phone}';
                phoneEl.dispatchEvent(new Event('input', { bubbles: true }));
                phoneEl.dispatchEvent(new Event('change', { bubbles: true }));
              }

              const pincodeEl = document.querySelector('[formControlName="resPinCode"]')
              if('${pincode}' !== 'undefined' && pincodeEl) {
                pincodeEl.value = '${pincode}';
                pincodeEl.dispatchEvent(new Event('input', { bubbles: true }));
                pincodeEl.dispatchEvent(new Event('change', { bubbles: true }));
              }

              const cityEl = document.querySelector('[formControlName="resCity"]');
              setTimeout(() => {
                if (cityEl && cityEl.tagName === 'SELECT') {
                  let optionsCount = cityEl.options.length;
                  if (optionsCount > 0) {
                    cityEl.selectedIndex = optionsCount - 1;
                    cityEl.dispatchEvent(new Event('change', { bubbles: true }));
                  }
                }
              }, 1500);

              const postOfficeEl = document.querySelector('[formControlName="resPostOff"]');
              setTimeout(() => {
                if (postOfficeEl && postOfficeEl.tagName === 'SELECT') {
                  let optionsCount = postOfficeEl.options.length;
                  if (optionsCount > 0) {
                    postOfficeEl.selectedIndex = optionsCount - 1;
                    postOfficeEl.dispatchEvent(new Event('change', { bubbles: true }));
                  }
                }
              }, 2000);

              const residenceToOfficeAddrEl = document.querySelector('[formControlName="officeSameAsRes"]');
              if(residenceToOfficeAddrEl) {
                residenceToOfficeAddrEl.click();
              }

              function registerClickCallback() {
                const errorMessageEls = document.querySelectorAll('.error-msg');
                alert(errorMessageEls.length)

                if(errorMessageEls.length > 0) {
                let errorFields = [];
                  errorMessageEls.forEach((el) => {
                    const errorEl = el;
                    const sibling = errorEl.previousElementSibling;
                    if(sibling) {
                      let val = sibling.getAttribute('id') || sibling.getAttribute('formcontrolname');            
                      if(val) { 
                        errorFields.push(val);
                      } else {
                        let currentElement = sibling;
                        while (currentElement) {
                          if (currentElement.querySelector('input')) {
                              const inputElement = currentElement.querySelector('input');
                              val = inputElement.getAttribute('id') || inputElement.getAttribute('formcontrolname');
                              errorFields.push(val);
                              break;
                          }
                          currentElement = currentElement.previousElementSibling;
                        }
                      }
                    }
                  }); 
                  if(errorFields.length > 0) {
                    postDataToReactNative('formErrorsAfterRegisterClick', errorFields);
                  }
                } else {
                  alert('No Error Message Found'); 
                }
              }   

              const addressBar = document.querySelector('.userReg3 ul li:nth-child(3)');

              const observer = new MutationObserver(function(mutations) {
                let addressBarClassNames = addressBar.className;

                const isAddressBarFocused = addressBarClassNames.includes('ui-state-active');
                if(isAddressBarFocused) {
                  const checkRegisterButtonInterval = setInterval(function() {
                    const registerButton = document.querySelector('button[type="submit"][label="Register"]');
                    if (registerButton) {
                        registerButton.addEventListener('click', registerClickCallback);
                        clearInterval(checkRegisterButtonInterval);
                    }
                  }, 500);
                }
              });

              observer.observe(addressBar, {
                attributes: true,
              });
            } else {
              // Prefilling new form
                const newFormFullName = document.getElementById('fullName');
                const newFormEmail = document.getElementById('email');
                const newFormMobile = document.getElementById('mobile'); 
                const newFormUserName = document.getElementById('userName');
          
                if('${firstName}' !== 'undefined'){
                  newFormFullName.value = '${firstName} ${lastName}';
                  newFormFullName.dispatchEvent(new Event('input', { bubbles: true }));
                  newFormFullName.dispatchEvent(new Event('change', { bubbles: true }));
                }
                if('${email}' !== 'undefined'){
                  newFormEmail.value = '${email}';
                  newFormEmail.dispatchEvent(new Event('input', { bubbles: true }));
                  newFormEmail.dispatchEvent(new Event('change', { bubbles: true }));
                }
                if('${phone}' !== 'undefined'){
                  newFormMobile.value = '${phone}';
                  newFormMobile.dispatchEvent(new Event('input', { bubbles: true }));
                  newFormMobile.dispatchEvent(new Event('change', { bubbles: true }));
                } 

                const registerButtonNew = document.querySelector('.search_btn.train_Search')

                function otpVerifyButtonHandler() {
                postDataToReactNative('otpVerifyButtonClicked', null);
                  setTimeout(() => {
                    const strongElements = document.querySelectorAll('strong');
                    const otpErrorElement = Array.from(strongElements).find(el => 
                        el.textContent.includes('Please enter OTP received on provided Mobile number and Email ID') ||
                        el.textContent.includes('Please enter correct OTP received on provided Mobile number and Email ID') ||
                        el.textContent.includes('Please enter OTP received on provided Email ID') ||
                        el.textContent.includes('OTP limit exceeded')
                    );
                    if(otpErrorElement) {
                      const errorText = otpErrorElement?.textContent;
                      if(errorText.includes('Please enter OTP received on provided Mobile number and Email ID')) {
                        postDataToReactNative('userNotEnteredBothOtp');
                      }
                      if(errorText.includes('Please enter correct OTP received on provided Mobile number and Email ID')) {
                        postDataToReactNative('userEnteredBothIncorrectMobileEmailOtp');
                      }
                      if(errorText.includes('Please enter OTP received on provided Email ID')) {
                        postDataToReactNative('userEnteredBothIncorrectEmailOtp');
                      }
                    } else {
                      const strongElements2 = document.querySelectorAll('strong');
                      const successMessageElement = Array.from(strongElements2).find(el =>
                          el.textContent.includes('Your IRCTC User ID has been created successfully')
                      );

                      if(successMessageElement) {
                        alert('IRCTC username created successfully');
                        // get the value present in the username field
                        postDataToReactNative('successfulUserNameCreation', newFormUserName.value);
                        return;
                      }
                    }
                  }, 500);
                }

                function okButtonClickHandler () {
                  setTimeout(() => {
                    const errorMapping = {
                      mobile: false,
                      email: false,
                      captcha: false,
                      userName: false,
                    }
                    let strongElements = document.querySelectorAll('strong');
                    let errorStrongTag = Array.from(strongElements).find(el => 
                        el.textContent.includes('Provided E-Mail is already associated with another IRCTC Account') ||
                        el.textContent.includes('Provided Mobile Number is already associated with another IRCTC Account') ||
                        el.textContent.includes('User Id already exists') ||
                        el.textContent.includes('Invalid Captcha')
                    );
                    if(errorStrongTag) {
                      const errorText = errorStrongTag?.textContent?.toLowerCase();
                      const hasCaptcha = errorText.includes('captcha');

                      if(hasCaptcha) {
                        errorMapping.captcha = true;
                        postDataToReactNative('formErrorsAfterOkClick', errorMapping);
                        return;
                      }
                      const hasMobile = errorText.includes('mobile');
                      const hasEmail = errorText.includes('e-mail') || text.includes('email');
                      const hasUserId = errorText.includes('user id');

                      if(hasMobile) errorMapping.mobile = true;
                      if(hasCaptcha) errorMapping.captcha = true;
                      if(hasEmail) errorMapping.email = true;
                      if(hasUserId) errorMapping.userName = true;
 
                      postDataToReactNative('formErrorsAfterOkClick', errorMapping);
                    } 
                    else {
                      const emailOtp = document.querySelector('input[name="enterOTPMobile"]');
                      const mobileOtp = document.querySelector('input[name="enterOTPEmail"]');
                      const verifyOtpButton = document.querySelector('button.btn.btn-primary');

                      if(emailOtp || mobileOtp) {
                        verifyOtpButton.addEventListener('click', otpVerifyButtonHandler);
                      }
                    }
                  }, 500);
                }
                
                function newRegisterButtonClickHandler() {
                  postDataToReactNative('submitButtonClicked', null);
                  
                  const errorMessageEls = document.querySelectorAll('.error-msg');
                  const userNameError = document.querySelector('[cclass="error-msg"]');
                  let errorFields = [];

                  if(userNameError) {
                    const userNameId = userNameError.previousElementSibling.getAttribute('id');
                    errorFields.push(userNameId);
                  }
                
                  if(errorMessageEls.length > 0) {
                    errorMessageEls.forEach((el) => {
                      const errorEl = el;
                      const sibling = errorEl.previousElementSibling;
                      if(sibling) {
                        let val = sibling.getAttribute('id') || sibling.getAttribute('formcontrolname');  
                        if(val) errorFields.push(val);
                      }
                    })
                  }

                  if(errorFields.length > 0) {
                      postDataToReactNative('formErrorsAfterRegisterClick', errorFields);
                  }

                  if(errorFields.length === 0) {
                    setInterval(() => {
                      const confirmationPopup = document.querySelector('app-registration-confirmationpage');
                      if(confirmationPopup) {
                        const buttons = document.querySelectorAll('button.search_btn.train_Search.regCnfBtn');
                        const okButton = buttons[0];
                        const cancelButton = buttons[1];

                        okButton.addEventListener('click', okButtonClickHandler);
                      }
                    }, 500); 
                  }
                }
                registerButtonNew.addEventListener('click', newRegisterButtonClickHandler);
            }
            
           setTimeout(function() {
            clearInterval(adIntervalId);
            clearInterval(adIntervalId2);
           }, 60000);
        }, 1000);
        true;
      `;

    if (!isEmpty(userPersonalDetails)) {
      let script = jsInjectionScriptFromConfigStore ? jsInjectionScriptFromConfigStore : backUpScript;

      if (jsInjectionScriptFromConfigStore) {
        script = script
        ?.replaceAll('{firstName}', firstName ?? undefined)
        ?.replaceAll('{lastName}', lastName ?? undefined)
        ?.replaceAll('{email}', email ?? undefined)
        ?.replaceAll('{phone}', phone ?? undefined);
      }

      createIrctcAccountRef?.injectJavaScript(script);
    }
  };
  return (
    <View style={{ flex: 1 }}>
      <CommonHeader
        headerText={HEADER_TEXT}
        imgSrc={backIcon}
        backPressHandler={() => Actions.pop()}
      />
      <WebView
        source={{ uri: irctcUrl }}
        startInLoadingState
        javaScriptEnabled
        onLoad={_injectJavaScriptCode}
        ref={(e) => (createIrctcAccountRef = e)}
        onMessage={_onMessage}
      />
    </View>
  );
};

CreateIRCTCAccountWebView.propTypes = {
  searchContext: PropTypes.object,
  setUserNameToRedux: PropTypes.func,
};

const mapStateToProps = (state, ownProps) => {
  const { railsListing } = state;
  const { departureDate, originStation, destinationStation } = railsListing;

  const searchContext = {
    departureDate,
    originStation,
    destinationStation,
  };

  return {
    ...ownProps,
    departureDate,
    originStation,
    destinationStation,
    searchContext,
  };
};

const mapDispatchToProps = (dispatch) => ({
  setUserNameToRedux: (userName) => {
    dispatch(updateUsername(userName));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(CreateIRCTCAccountWebView);
