import * as actions from './TravelerDetailsActions';
import findIndex from 'lodash/findIndex';
import slice from 'lodash/slice';
import ReservationChoiceType from '../Constants/ReservationChoiceType';
import {MAX_TRAVELER_GENERAL} from './TravelerDetailsUtils';
import {isIRCTCTatkalTime} from '../../Utils/RailsConstant';
import {ACTION_SET_PPBOOKING_TRAVELER_DATA} from '../Review/RailsReviewActions';

import {
  RAILOFY_TYPE,
  updatePreserveFcSelection,
  updateRailofyData,
  updateSelectionValue,
} from './railofyUtils';
import uniqBy from 'lodash/fp/uniqBy';
import { ACTION_SET_DATA_FOR_TRAVELERS_PAGE } from '../NewListing/RailsListingActions';

const railofyInitialState = {
  isLoading: true,
  loaded: false,
  railofyType: RAILOFY_TYPE.NONE, // @TODOSFCTG use enum
  freeCancellationData: undefined,
  tripGuaranteeData: undefined,
  selectedIndex: -1,
  isFCSelected: false,
  isTGSelected: false,
  premiumAmount: 0,
  railofyErrorMessage: '',
  zcShown: false,
  railofyShown: false,
  railsConfirmationGuaranteeOption: 1,
  hasError: false,
  cnfmGuaranteeOpted: undefined,
  defaultPremiumAmount: 0,
  isTGPlusSelected: false,
  tgType: null,
  tgDiscountPremium: 0,
  fcDiscountPremium: 0,
  tgDiscountValue: 0,
  fcDiscountValue: 0,
  isTgDiscounted: false,
  isFcDiscounted: false,
};

const initialState = {
  applicableBerthTypes: [],
  foodChoiceEnabled: false,
  selectedQuota: null,
  totalCollectibleAmount: 0.0,

  contactDetails: {
    isFetching: true,
  },
  selectedReservationChoice: ReservationChoiceType.None,
  reservationChoice: [],
  isLoggedIn: false,
  userDetails: null,
  travelers: [],
  selectedTravelers: [],
  viewAllTravelers: false,
  showViewAll: false,
  loadingDuplicateBookingAndReview:false,
  maxTravelersAllowed: MAX_TRAVELER_GENERAL,
  warning: {
    showChildWithoutBerthWarning: false,
    showIrctcTatkalTimeWarning: isIRCTCTatkalTime(),
    showMaxTravelerWarningForTatkal: false,
    showMaxSrCitizenTravelerWarning: false,
  },
  travelerInsuranceOpted: 1,
  refundAndCanChecked: true,
  travelAdvisoryChecked: undefined,
  travelAdvisoryDetails: null,
  showTravelAdvisorySection:true,
  considerAutoUpgradation: false,
  header: '',
  classValue: null,
  showBoardingStationChange: false,
  boardingStationList: [],
  selectedPickupStation: null,
  mealOptions: [],
  berthOptions: [],
  srOptions: [],
  nationalityOptions: [],
  searchKey: '',
  showPwdReminder: true,
  userNameModalVisible: false,
  couponData: undefined,
  recommendedCoupons:[],
  couponErrorMessage:'',
  confirmBookCheckbox: false,
  psgnDestinationAddress: {
    address: '',
    street: '',
    colony: '',
    pinCode: '',
    city: '',
    state: '',
    postOffice: '',
    postOfficeList: [],
  },
  reviewAvailablityChange: false,
  hotelCrossSellBannerText: '',
  showIRCTCUsernameError: false,
  fcTgPreserveSelection: null,
  railofy: railofyInitialState,
  displayFcBottomSheet: false,
  displayDownTimeBottomSheet: false,
  errorFlow: null,
  errorFlowCode: null,
  isBnppOpted: false,
  optionalGstDetailsEnabled: false,
  gstDetailsError: false,
  gstDetails: {
    gstIn: '',
    nameOnGst: '',
  },
  railsGuestSelectedTravelersList: [],
  railsGuestTravelersList: {},
  railsLoggedInSelectedTravelersList: [],
  railsLoggedInTravelersList: {},
  availabilitySubscription: {
    showAvailSubscrnBottomSheet: false,
    isAvailSubscrnEnabled: false,
    showSubscriptionWidget: false,
  },
  displayAvailDepletionBottomSheet: false,
  displayAvailDepletionStrip: false,
  showProfileIncompleteFlow: false,
  irctcProfileCompletedFromWebView: false,
  showIrctcComponentLoader: false,
  displayVegPreferenceBottomSheet: false,
  irctcUserNames: [],
  isInventoryDepletionBottomSheetVisible: false,
  inventoryDepletionDismissedTrains: {},
};

export default (state = initialState, action) => {
  switch (action.type) {
    case actions.ACTION_INIT_STATE:
    case actions.SET_ALERT_LOADER: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_USER_DETAILS: {
      const {
        isLoggedIn, userDetails, contactDetails,
      } = action.data;
      return {
        ...state,
        isLoggedIn,
        userDetails,
        contactDetails,
      };
    }
      case actions.ACTION_SET_VEG_PREFERENCE:{
      return {
        ...state,
        ...action.data,
      }
    }
    case actions.ACTION_ON_LOAD_TRAVELER_PAGE: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_ON_CONTACT_DETAILS_EDITED: {
      const {data: contactDetails} = action;
      return {
        ...state,
        contactDetails,
      };
    }
    case actions.ACTION_ON_RESERVATION_CHOICE_CLICKED: {
      const {selectedReservationChoice} = action.data;
      return {
        ...state,
        selectedReservationChoice,
      };
    }
    case actions.ACTION_ON_VIEW_ALL_CLICKED: {
      const {viewAllTravelers} = action.data;
      return {
        ...state,
        viewAllTravelers,
      };
    }
    case actions.ACTION_ON_TRAVELER_SELECT: {
      const {data: {selectedTravelers, warning}} = action;
      return {
        ...state,
        selectedTravelers,
        warning,
      };
    }
    case actions.ACTION_ON_INACTIVE_TRAVELER_SELECTED: {
      const {traveler} = action.data;
      const {travelers} = state;

      const index = findIndex(travelers, (value => value.travelerId === traveler.travelerId));
      const before = slice(travelers, 0, index);
      const after = slice(travelers, index + 1, travelers.length);
      const newTravelers = [...before, traveler, ...after];
      return {
        ...state,
        travelers: newTravelers,
      };
    }
    case actions.ACTION_SET_TRAVEL_INSURANCE: {
      const {travelerInsuranceOpted} = action.data;
      return {
        ...state,
        travelerInsuranceOpted,
      };
    }

    case actions.ACTION_ON_TRAVEL_ADVISORY_CLICKED : {
      const {travelAdvisoryChecked} = action.data;
      return {
        ...state,
        travelAdvisoryChecked,
      };
    }

    case actions.ACTION_ON_REFUND_AND_CANCELLATION_CLICKED : {
      const {refundAndCanChecked} = action.data;
      return {
        ...state,
        refundAndCanChecked,
      };
    }
    case actions.ACTION_ON_CONSIDER_AUTO_UPGRADATION_CLICKED: {
      const {considerAutoUpgradation} = action.data;
      return {
        ...state,
        considerAutoUpgradation,
      };
    }
    case actions.ACTION_ON_BERTH_VALIDATION : {
      const {warning} = action.data;
      return {
        ...state,
        warning,
      };
    }
    case actions.ACTION_ON_BERTH_MISMATCH_FOUND: {
      const {traveler} = action.data;
      const {travelers, selectedTravelers} = state;

      const index = findIndex(travelers, (value => value.travelerId === traveler.travelerId));
      const before = slice(travelers, 0, index);
      const after = slice(travelers, index + 1, travelers.length);
      const newTravelers = [...before, traveler, ...after];

      return {
        ...state,
        travelers: newTravelers,
        selectedTravelers: [...selectedTravelers, traveler.travelerId],
      };
    }
    case actions.ACTION_SET_TRAVELER_HEADER: {
      const {header} = action.data;
      return {
        ...state,
        header,
      };
    }
    case actions.ACTION_LOAD_BOARDING_STATION: {
      const {
        classValue,
        showBoardingStationChange,
        boardingStationList,
        selectedPickupStation,
      } = action.data;
      return {
        ...state,
        classValue,
        showBoardingStationChange,
        boardingStationList,
        selectedPickupStation,
      };
    }
    case actions.ACTION_ON_BOARDING_STATION_SELECTED: {
      const {selectedPickupStation} = action.data;
      return {
        ...state,
        selectedPickupStation,
      };
    }
    case actions.ACTION_LOAD_PREFERENCES: {
      const {
        mealOptions,
        berthOptions,
        srOptions,
        nationalityOptions,
      } = action.data;
      return {
        ...state,
        mealOptions,
        berthOptions,
        srOptions,
        nationalityOptions,
      };
    }
    case actions.ACTION_ON_ADD_EDIT_TRAVELER: {
      const
        {
          selectedTravelers, travelers, warning, showViewAll,
        } = action.data;
      return {
        ...state,
        selectedTravelers,
        travelers,
        warning,
        showViewAll,
      };
    }
    case ACTION_SET_PPBOOKING_TRAVELER_DATA: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_ON_CONTACT_DETAILS_VALIDATION_FAILURE: {
      const {data: contactDetails} = action;
      return {
        ...state,
        contactDetails,
      };
    }
    case actions.UPDATE_OPTIONAL_GST_DETAILS: {
      const { dataPoint, value } = action?.data;
      return {
        ...state,
        [dataPoint]: value,
      };
    }
    case actions.ACTION_TOGGLE_MODAL_VISIBILITY: {
      return {
        ...state,
        userNameModalVisible: !state.userNameModalVisible,
      };
    }
    case actions.ACTION_SET_SHOW_PWD_REMINDER_SCREEN: {
      const {data: {showPwdReminder}} = action;
      return {
        ...state,
        showPwdReminder,
      };
    }
    case actions.ACTION_SET_COUPON_DATA: {
      return {
        ...state,
        couponData: action.data,
      };
    }
    case actions.ACTION_REMOVE_COUPON_DATA:{
      return {
        ...state,
        couponData: undefined,
      };
    }
    case actions.ACTION_ADD_RECOMMENDED_COUPONS: {
      const coupon = action.payload;
      if (coupon.errorDetails) {
        return {...state, couponErrorMessage: coupon.errorDetails.errorMessage};
      }
      return {
        ...state,
        recommendedCoupons: uniqBy('couponCode', [
          ...state.recommendedCoupons,
          ...coupon,
        ]),
        couponErrorMessage: '',
      };
    }
    case actions.ACTION_ADD_COUPON_DATA: {
      const coupon = action.data;
      let isPresent = false;
      if (coupon.errorDetails) {
        return {...state, couponErrorMessage: coupon.errorDetails.errorMessage};
      }
      state.recommendedCoupons.forEach((item) => {
        if (item.couponCode === coupon.couponCode) {
        isPresent = true;
        }
      });
      if (isPresent) {
        return {...state};
      }
      return {...state, recommendedCoupons: [...state.recommendedCoupons, coupon], couponErrorMessage: ''};
    }

    case actions.ACTION_ON_CONFIRM_BOOK_CHECKBOX_CLICKED: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_DELETE_TRAVELLER: {
      const {travelers, selectedTravelers} = action.data;
      return {
        ...state,
        travelers,
        selectedTravelers,
      };
    }
    case actions.ACTION_CLEAR_SELECTED_TRAVELRS: {
      const {selectedTravelers} = action.data;
      return {
        ...state,
        selectedTravelers,
      };
    }
    case actions.ACTION_ADD_DESTINATION_ADDRESS: {
      const {psgnDestinationAddress} = action.data;
      return {
        ...state,
        psgnDestinationAddress:{...psgnDestinationAddress},
      };
    }
    case actions.ACTION_SHOW_DUPLICATE_CONFIRMATION: {
      const {showAlert, alertHeader, alertMessage,loading = false} = action.data;
      return {
        ...state,
        duplicateBookingConfirmation: {
          showAlert,
          alertHeader,
          alertMessage,
          loading,
        },
      };
    }
    case actions.ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_SHOW_IRCTC_USERNAME_ERROR: {
      return {
        ...state,
        showIRCTCUsernameError: action.data,
      };
    }
    case actions.ACTION_FETCH_RAILOFY_DATA_START: {
      return {
        ...state,
        railofy: {
          ...state.railofy,
          isLoading: true,
          hasError: false,
        },
      };
    }
    case actions.ACTION_FETCH_RAILOFY_DATA_FAILED:
      return {
        ...state,
        railofy: {
          ...state.railofy,
          isLoading: false,
        },
      };
    case actions.FC_TG_BOTTOMSHEET_CTA_CLICKED:
    case actions.ACTION_REVIEW_AVAILABILITY_CHANGE: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_IRCTC_USERNAMES_LIST: {
      return {
        ...state,
        irctcUserNames: action.data,
      };
    }
    case actions.ACTION_FETCH_RAILOFY_DATA_SUCCESS: {
      return {
        ...state,
        railofy: {
          ...state.railofy,
          isLoading: false,
          loaded: true,
          hasError: false,
          ...updateRailofyData(action.payload),
        },
      };
    }
    case actions.ACTION_UPDATE_RAILOFY_USER_RESPONSE: {
      return {
        ...state,
        railofy: {
          ...state.railofy,
          hasError: false,
          selectedIndex: action.index,
          premiumAmount: action.premiumAmount,
          ...updateSelectionValue(state.railofy, action.index),
        },
        ...updatePreserveFcSelection(state.railofy, action.index),
      };
    }
    case actions.ACTION_UPDATE_FC_TG_USER_RESPONSE: {
      return {
        ...state,
        railofy: {
          ...state.railofy,
          ...action.data,
        },
      };
    }
    case actions.ACTION_SHOW_RAILOFY_WIDGET_ERROR: {
      return {
        ...state,
        railofy: {
          ...state.railofy,
          hasError: true,
        },
      };
    }
    case ACTION_SET_DATA_FOR_TRAVELERS_PAGE:
    case actions.ACTION_CLEAR_PREVIOUS_RAILOFY_DATA: {
      return {
        ...state,
        recommendedCoupons: [],
        railofy: {
          ...railofyInitialState,
        },
      };
    }
    case actions.ACTION_FREE_CANCELLATION_ALERT: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_BOOKING_REVIEW_ERROR_FLOW:
    case actions.ACTION_DISPLAY_AVAIL_DEPLETION_TRAVELER:
    case actions.ACTION_DOWNTIME_BOTTOMSHEET: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_BOOKING_REVIEW_ERROR_FLOW_CODE: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_TG_ALERT: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_BNPP_SET_SELECTION: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.SET_BNPP_INTERACTED: {
      return {
        ...state,
        bnppInteracted: action.data,
      };
    }

    case actions.ACTION_ON_AVAIL_SUBSCRN: {
      return {
        ...state,
        availabilitySubscription: {
          ...state.availabilitySubscription,
          ...action.data,
        },
      };
    }
    case actions.ACTION_SET_IRCTC_PROFILE_STATUS:
    case actions.ACTION_INIT_SCHEDULE_TATKAL_TRAVELER:
    case actions.ACTION_SET_RAILS_GUEST_SELECTED_TRAVELERS_LIST:
    case actions.ACTION_SET_RAILS_GUEST_TRAVELERS_LIST:
    case actions.ACTION_SET_RAILS_LOGGED_IN_SELECTED_TRAVELERS_LIST:
    case actions.ACTION_SET_RAILS_LOGGED_IN_TRAVELERS_LIST: {
      return {
        ...state,
        ...action.data,
      };
    }
    case actions.ACTION_TOGGLE_INVENTORY_DEPLETION_BOTTOMSHEET: {
      return {
        ...state,
        ...action.data,
        ...(action.trainNumber && !action.data.isInventoryDepletionBottomSheetVisible && {
          inventoryDepletionDismissedTrains: {
            ...state.inventoryDepletionDismissedTrains,
            [action.trainNumber]: true
          }
        })
      }
    }
    default: {
      return state;
    }
  }
};

// @TODOSFCTG - extract & create new reducer function for fctg.
