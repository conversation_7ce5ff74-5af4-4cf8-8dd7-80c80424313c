import {connect} from 'react-redux';
import {
  onContactDetailsValidationFailed,
  saveContactCallBackHandler,
} from '../TravelerDetailsActions';
import ContactDetailsView from '@mmt/legacy-commons/Common/Components/ContactDetails/NewContactDetails';
import { _label } from '../../../vernacular/AppLanguage';
import { validateEmail, validateMobile } from '../TravelerDetailsUtils';

const mapStateToProps = (state) => {
  const {railsTraveler: {contactDetails}} = state;
  return ({
    contactDetails,
    labels: {
      email: _label('email_id'),
      emailPlaceholder: _label('sample_email'),
      phone: _label('phone_number'),
      phonePlaceholder: _label('sample_phone'),
      changeButtonText: _label('change', { uppercase: true }),
    },
    validateEmail: validateEmail,
    validateMobile: validateMobile,
  });
};

const mapDispatchToProps = dispatch => ({
  onAdd: contactDetails => dispatch(saveContactCallBackHandler(contactDetails)),
  onValidationFailed: fieldName => dispatch(onContactDetailsValidationFailed(fieldName)),
});

export default connect(mapStateToProps, mapDispatchToProps)(ContactDetailsView);
