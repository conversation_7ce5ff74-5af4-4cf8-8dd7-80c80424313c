import {connect} from 'react-redux';
import AddRailsTraveler from '../Components/AddRailsTraveler';
import {
  onAddEditTraveler,
  onAddTraveler,
  onDeleteTravellerAction,
} from '../TravelerDetailsActions';
import { logTravellerPageBottomSheetEvents } from '../../Review/RailsReviewActions';

const getLabels = texts => ({
  primaryDetails: texts.trv_primary_details,
  enterName: texts.trv_enter_name,
  enterAge: texts.trv_enter_age,
  enterGender: texts.trv_gender,
  genderMale: texts.trv_male,
  genderFemale: texts.trv_female,
  nationality: texts.trv_nationality,
  change: texts.trv_change,
  halfTicket: texts.trv_half_ticket_price_will_be_charged,
  trv_name:texts.trv_name,
  trv_age:texts.trv_age,
  trv_berth_preference:texts.trv_berth_preference,
  trv_india:texts.trv_india,
  nationality_indian:texts.nationality_indian,
  children_under5_warning:texts.children_under5_warning,
  reserve_berth:texts.reserve_berth,
});

const mapStateToProps = (state, ownProps) => {
  const {
    railsTraveler: {
      mealOptions,
      berthOptions,
      srOptions,
      nationalityOptions,
      selectedQuota,
      foodChoiceEnabled,
    },
    railsListing,
    railsListing: {
      childBerthMandatory,
    },
    railsVernacular: {
      texts,
    },
  } = state;
  return ({
    ...ownProps,
    mealOptions,
    berthOptions,
    srOptions,
    nationalityOptions,
    selectedQuota,
    foodChoiceEnabled,
    childBerthMandatory,
    railsListing,
    labels: getLabels(texts),
  });
};

const mapDispatchToProps = dispatch => ({
  onAddEditTraveler: addTravelerMetaData => dispatch(onAddEditTraveler(addTravelerMetaData)),
  onDeleteTravellerAction: (traveler, onCancelPress) =>
    dispatch(onDeleteTravellerAction(traveler, onCancelPress)),
  onAddTraveler: () => dispatch(onAddTraveler()),
  logTravellerPageBottomSheetEvents: (eventValue) =>
    dispatch(logTravellerPageBottomSheetEvents(eventValue)),
});

export default connect(mapStateToProps, mapDispatchToProps)(AddRailsTraveler);
