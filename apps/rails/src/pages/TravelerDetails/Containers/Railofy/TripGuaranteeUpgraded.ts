import { connect } from 'react-redux';
import {
  updateFCTGUserResponse,
  updateRailofyUserResponse,
  updateTgSelection,
} from '../../TravelerDetailsActions';
import TripGuaranteeV3 from '../../Components/AssuredConfirmation/TripGuaranteeV3';

function mapStateToProps({ railsTraveler }, ownProps) {
  const { railofy, selectedTravelers, couponData = {}, displayTgBottomSheet } = railsTraveler;
  const { tgVersion } = ownProps;
  const { selectedIndex, tripGuaranteeData, hasError, isTGSelected, tgPlusData, isTGPlusSelected, isFCSelected  } =
    railofy;
  const { ancillaryDiscountDetails } = couponData;

  const tgDiscount = {
    discountedPremium: ancillaryDiscountDetails?.ancillaryDetails?.tg?.discountedPremium,
  };

  return {
    numberOfPassengers: Math.max(selectedTravelers.length, 1),
    assuredConfirmationData: { ...tripGuaranteeData }, // @TODOSFCTG - variable name rewrite
    selectedIndex,
    hasError,
    ancillaryDiscountDetails,
    tgDiscount,
    isTGSelected,
    displayTgBottomSheet,
    tgPlusData,
    isTGPlusSelected,
    isFCSelected,
    tgVersion,
  };
}

function mapDispatchToProps(dispatch) {
  return {
    updateUserResponse: (index: number, premium: number = 0) => {
      dispatch(updateRailofyUserResponse(index, premium));
    },
    updateFCTGUserResponse: (
      index: number,
      premiumAmount: number = 0,
      type: string,
      toast: string,
    ) => {
      dispatch(updateFCTGUserResponse(index, premiumAmount, type, toast));
    },
    updateTgSelection: (index: number, premiumAmount: number = 0) => {
      dispatch(updateTgSelection(index, premiumAmount));
    },
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(TripGuaranteeV3);
