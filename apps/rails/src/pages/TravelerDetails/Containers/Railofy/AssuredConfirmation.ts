import { connect } from 'react-redux';
import AssuredConfirmation from '../../Components/AssuredConfirmation';
import { updateFCTGUserResponse, updateRailofyUserResponse, updateTgSelection } from '../../TravelerDetailsActions';

function mapStateToProps({ railsTraveler }) {
  const { railofy, selectedTravelers, couponData = {}, displayTgBottomSheet } = railsTraveler;
  const { selectedIndex, tripGuaranteeData, hasError, isTGSelected } = railofy;

  const { ancillaryDiscountDetails } = couponData;
  const tgDiscount = {
    discountedPremium: ancillaryDiscountDetails?.ancillaryDetails?.tg?.discountedPremium,
  };

  return {
    numberOfPassengers: Math.max(selectedTravelers.length,1),
    assuredConfirmationData: { ...tripGuaranteeData }, // @TODOSFCTG - variable name rewrite
    selectedIndex,
    hasError,
    ancillaryDiscountDetails,
    isTGSelected,
    displayTgBottomSheet,
    tgDiscount,
  };
}

function mapDispatchToProps(dispatch) {
  return {
    updateUserResponse: (index: number, premium: number = 0) => {
      // @TODOSFCTG - add omniture tracker
      dispatch(updateRailofyUserResponse(index, premium));
    },
    updateFCTGUserResponse: (index: number, premiumAmount: number = 0, type: string, toast: string) => {
      dispatch(updateFCTGUserResponse(index, premiumAmount, type, toast ));
    },
    updateTgSelection: (index: number, premiumAmount: number = 0 ) => {
      dispatch(updateTgSelection(index, premiumAmount));
    },
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(AssuredConfirmation);
