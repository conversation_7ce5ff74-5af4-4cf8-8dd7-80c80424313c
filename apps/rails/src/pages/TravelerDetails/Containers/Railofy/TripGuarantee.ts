import { connect } from 'react-redux';
import { TripGuaranteeV2 } from '../../Components/AssuredConfirmation';
import {
  updateFCTGUserResponse,
  updateRailofyUserResponse,
  updateTgSelection,
} from '../../TravelerDetailsActions';
import {
  logTravellerPageAddOnEvents,
  logTravellerPageBottomSheetEvents,
} from '../../../Review/RailsReviewActions';

function mapStateToProps({ railsTraveler }) {
  const { railofy, selectedTravelers, couponData = {}, displayTgBottomSheet } = railsTraveler;
  const { selectedIndex, tripGuaranteeData, hasError, isTGSelected, isTgDiscounted } = railofy;

  const tgDiscountValue = tripGuaranteeData?.discountValue ?? 0;
  const tgDiscountPremium = tripGuaranteeData?.discountedPremium ?? 0;

  const { ancillaryDiscountDetails } = couponData;
  const tgDiscount = {
    ancillaryDiscountedPremium: ancillaryDiscountDetails?.ancillaryDetails?.tg?.discountedPremium,
    bundleDiscountedPremium:
      ancillaryDiscountDetails?.ancillaryDetails?.tg?.bundleDiscountedPremium,
  };

  return {
    numberOfPassengers: Math.max(selectedTravelers.length, 1),
    assuredConfirmationData: { ...tripGuaranteeData }, // @TODOSFCTG - variable name rewrite
    selectedIndex,
    hasError,
    ancillaryDiscountDetails,
    isTGSelected,
    displayTgBottomSheet,
    tgDiscountValue,
    tgDiscountPremium,
    isTgDiscounted,
    tgDiscount,
  };
}

function mapDispatchToProps(dispatch) {
  return {
    updateUserResponse: (index: number, premium: number = 0) => {
      dispatch(updateRailofyUserResponse(index, premium));
    },
    updateFCTGUserResponse: (
      index: number,
      premiumAmount: number = 0,
      type: string,
      toast: string,
    ) => {
      dispatch(updateFCTGUserResponse(index, premiumAmount, type, toast));
    },
    updateTgSelection: (index: number, premiumAmount: number = 0) => {
      dispatch(updateTgSelection(index, premiumAmount));
    },
    logTravellerPageAddOnEvents: (railofyValuesTrackingParams: unknown, eventValue: unknown) =>
      dispatch(logTravellerPageAddOnEvents(railofyValuesTrackingParams, eventValue)),
    logTravellerPageBottomSheetEvents: (eventValue: unknown) =>
      dispatch(logTravellerPageBottomSheetEvents(eventValue)),
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(TripGuaranteeV2);
