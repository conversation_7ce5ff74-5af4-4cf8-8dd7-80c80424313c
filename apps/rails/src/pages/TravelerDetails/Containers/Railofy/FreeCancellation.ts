import { connect } from 'react-redux';
import FreeCancellation from '../../Components/FreeCancellation/FreeCancellation';
import { updateFCTGUserResponse, updateRailofyUserResponse } from '../../TravelerDetailsActions';
import { trackFreeCancellationSelection } from '../../railofyOmniture';
import { logTravellerPageAddOnEvents } from '../../../Review/RailsReviewActions';

function mapStateToProps({ railsTraveler }) {
  const { railofy, couponData = {} } = railsTraveler;
  const {
    freeCancellationData,
     selectedIndex,
     hasError,
     fcUpgradeOption,
     isFCSelected,
    tgPlusData = null,
    fcDiscountPremium,
    isFcDiscounted,
  } = railofy;
  const { ancillaryDiscountDetails, fcDiscount } = couponData;
  const preserveFcSelection = railsTraveler?.preserveFcSelection;
  const isTgPlusShown = !!tgPlusData;
  return {
    freeCancellationData,
    selectedIndex,
    fcUpgradeOption,
    hasError,
    ancillaryDiscountDetails,
    preserveFcSelection,
    isFCSelected,
    isTgPlusShown,
    fcDiscountPremium,
    isFcDiscounted,
    fcDiscount,
  };
}

function mapDispatchToProps(dispatch) {
  return {
    updateUserResponse: (index: number, premiumAmount: number = 0) => {
      trackFreeCancellationSelection(index);
      dispatch(updateRailofyUserResponse(index, premiumAmount));
    },
    updateFCTGUserResponse: (index: number, premiumAmount: number = 0, type: string, toast: string) => {
      dispatch(updateFCTGUserResponse(index, premiumAmount, type, toast));
    },
    logTravellerPageAddOnEvents: (railofyValuesTrackingParams: unknown, eventValue: unknown) =>
      dispatch(logTravellerPageAddOnEvents(railofyValuesTrackingParams, eventValue)),
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(FreeCancellation);
