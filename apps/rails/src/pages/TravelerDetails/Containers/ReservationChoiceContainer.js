import {connect} from 'react-redux';
import ReservationChoice from '../Components/ReservationChoice';
import {onReservationChoiceClicked} from '../TravelerDetailsActions';
import { logTravellerGenericClickEvents } from '../../Review/RailsReviewActions';

const mapStateToProps = (state, ownProps) => {
  const {railsTraveler: {selectedReservationChoice, reservationChoice}} = state;
  return ({
    travelerCount: ownProps.travelerCount,
    selectedReservationChoice,
    reservationChoice,
  });
};

const mapDispatchToProps = (dispatch) => ({
  onReservationChoiceClicked: (item) => dispatch(onReservationChoiceClicked(item)),
  logTravellerGenericClickEvents: (eventValue) =>
    dispatch(logTravellerGenericClickEvents(eventValue)),
});
export default connect(mapStateToProps, mapDispatchToProps)(ReservationChoice);
