import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { View, StyleSheet, Text, Image } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { getConfigStore } from '../../../configStore/Common/RailsConfigStoreUtils';
import {
  RAILS_ERROR_RETRY_COUNT,
  setDataToAsyncStorage,
  getDataFromAsynStorage,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  BACK_TO_LISTING,
  CLOSE_BOTTOMSHEET,
  RETRY,
  CONTINUE,
  WEBVIEW,
  SET_ALERT,
  MOB_RAIL_TRAVELLER_ARP_SET_ALERT,
  GOT_IT,
  REVIEW_ERROR_CODE_ARP_BOTTOMSHEET,
  REVIEW_ERROR_CODE_TATKAL_BOTTOMSHEET,
  ARP_REVIEW_ERROR_EVAR99,
  TATKAL_REVIEW_ERROR_EVAR99,
  DEEPLINK,
  RAILS_BOOKING_REVIEW_INCOMPLETE_IRCTC_PROFILE,
  IRCTC_PROFILE_INCOMPLETE_REVIEW_ERROR_EVAR99,
  RETRY_ALTERNATE_AVAILABILITY_BOOKING,
  RAILS_BOOKING_REVIEW_PASSWORD_EXPIRED,
  RESET_PASSWORD_REVIEW_ERROR_EVAR99,
} from '@mmt/rails/src/Utils/RailsConstant';
import LinearGradient from 'react-native-linear-gradient';
import { Actions } from '@mmt/rails/src/navigation/railsNavigation';
import { colors, gradient } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';
import {
  TRAVELLER_CLICK_EVENTS,
  TRAVELLER_PDT_EVENTS,
} from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import {
  removeEventFromEvar99Variable,
  trackGenericEvar99Event,
} from '@mmt/rails/src/railsAnalytics';
import TimerComponent from './TimerComponent';
import GenericModule from '@mmt/core/native/GenericModule';
import {
  isPasswordResetBottomSheet,
  renderIrctcProfileIncomplete,
  PasswordResetBottomSheet,
  CTAType,
  ConfigType,
} from './ReviewErrorBottomSheetHelpers';

interface GradientButtonProps {
  text: string;
  onPress: () => void;
}

interface ReviewDowntimeBottomSheetProps {
  errorFlow: {
    title: string;
    subTitle: string;
    ctas: Array<CTAType>;
    templateType: string;
    iconUrl: string;
    errorCode?: string;
    configKey?: string;
    timer?: {
      startTime: number;
      endTime: number;
    };
    notificationText?: string;
  };
  reviewCall: (val?: boolean) => void;
  onClose: (val: unknown) => void;
  setErrorFlowCode: (val: unknown) => void;
  showLoader: boolean;
  logTravellerPageBottomSheetEvents: (eventValue: string) => void;
  setAlert: () => Promise<void>;
  isAlertLoading: boolean;
  setAlertApiSuccess: boolean;
  irctcUserName?: string;
  mobileNumber?: string;
  email?: string;
}

const GradientButton = ({ text, onPress }: GradientButtonProps) => (
  <TouchableRipple onPress={onPress}>
    <LinearGradient
      start={{ x: 1.0, y: 0.0 }}
      end={{ x: 0.0, y: 1.0 }}
      colors={gradient.lightBlue}
      style={[styles.checkAvlBtnGradient, styles.mar10Bt]}
      testID="gradient-button"
    >
      <Text style={styles.clickableLink} testID={`gradient-button-text-${text}`}>
        {text}
      </Text>
    </LinearGradient>
  </TouchableRipple>
);

const PlainButton = ({ text, onPress }: GradientButtonProps) => (
  <TouchableRipple onPress={onPress} testID={`plain-button-container-${text}`}>
    <View style={[styles.checkAvlBtn, styles.mar10Bt]} testID={`plain-button-${text}`}>
      <Text style={styles.clickableLinkAzure} testID={`plain-button-text-${text}`}>
        {text}
      </Text>
    </View>
  </TouchableRipple>
);

const ReviewDowntimeBottomSheet = ({
  onClose,
  errorFlow,
  reviewCall,
  showLoader,
  setErrorFlowCode,
  logTravellerPageBottomSheetEvents,
  setAlert,
  isAlertLoading,
  setAlertApiSuccess,
  irctcUserName,
  mobileNumber = '',
  email = '',
}: ReviewDowntimeBottomSheetProps) => {
  const { title, subTitle, ctas, iconUrl, timer, notificationText, errorCode } = errorFlow || {};
  const { TRAVELLER } = RAIL_EVENTS;
  const {
    MOB_RAIL_TRAVELLERS_ROUTE_EXTENSION_GO_BACK,
    MOB_RAIL_TRAVELLERS_ROUTE_EXTENSION_CONTINUE,
    RAILS_TRAVELLER_VISIT_LATER_TRACKING,
    MOB_RAIL_TRAVELLERS_ERROR_LINK,
  } = TRAVELLER;

  const isIrctcProfileIncomplete = errorCode === RAILS_BOOKING_REVIEW_INCOMPLETE_IRCTC_PROFILE;
  const [config, setConfig] = useState<ConfigType | null>(null);

  useEffect(() => {
    if (isIrctcProfileIncomplete && errorFlow?.configKey) {
      void getConfigStore(errorFlow?.configKey).then((response) => {
        setConfig(response);
        return response;
      });
    }
  }, [isIrctcProfileIncomplete, errorFlow?.configKey]);

  useEffect(() => {
    switch (errorCode) {
      case REVIEW_ERROR_CODE_ARP_BOTTOMSHEET:
        removeEventFromEvar99Variable(ARP_REVIEW_ERROR_EVAR99);
        trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, ARP_REVIEW_ERROR_EVAR99);
        break;
      case REVIEW_ERROR_CODE_TATKAL_BOTTOMSHEET:
        removeEventFromEvar99Variable(TATKAL_REVIEW_ERROR_EVAR99);
        trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, TATKAL_REVIEW_ERROR_EVAR99);
        break;
      case RAILS_BOOKING_REVIEW_INCOMPLETE_IRCTC_PROFILE:
        removeEventFromEvar99Variable(IRCTC_PROFILE_INCOMPLETE_REVIEW_ERROR_EVAR99);
        trackGenericEvar99Event(
          TRAVELERS_PAGE_TRACKING_KEY_NEW,
          IRCTC_PROFILE_INCOMPLETE_REVIEW_ERROR_EVAR99,
        );
        break;
      case RAILS_BOOKING_REVIEW_PASSWORD_EXPIRED:
        removeEventFromEvar99Variable(RESET_PASSWORD_REVIEW_ERROR_EVAR99);
        trackGenericEvar99Event(
          TRAVELERS_PAGE_TRACKING_KEY_NEW,
          RESET_PASSWORD_REVIEW_ERROR_EVAR99,
        );
        break;
      default:
        break;
    }
  }, [errorCode]);
  const [timerStart] = useState(() => Math.floor(Date.now() / 1000));

  const isRouteExtension = ctas?.find(
    (cta: CTAType) => cta.code === RETRY_ALTERNATE_AVAILABILITY_BOOKING,
  );

  const onSetAlertClicked = () => {
    trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, MOB_RAIL_TRAVELLER_ARP_SET_ALERT);
    setAlert();
  };

  const closeBottomSheet = () => {
    onClose(null);
  };
  const ctaType = useCallback(
    (text: string, type: string, code: string, deeplink: string, index: number) => {
      const onPress = async () => {
        if (!isRouteExtension) {
          trackClickEventProp61(
            TRAVELERS_PAGE_TRACKING_KEY_NEW,
            `rail_error_${text.replace(/\s+/g, '_').toLowerCase()}`,
          );
        }

        let count = 0;
        if (type === RETRY) {
          const currentCount = await getDataFromAsynStorage(RAILS_ERROR_RETRY_COUNT);
          count = currentCount + 1;
        }
        if (text === GOT_IT) {
          trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, 'mob_rail_traveller_arp_got_it');
        }
        await setDataToAsyncStorage(RAILS_ERROR_RETRY_COUNT, count);
        switch (type) {
          case BACK_TO_LISTING:
            isRouteExtension &&
              trackClickEventProp61(
                TRAVELERS_PAGE_TRACKING_KEY_NEW,
                MOB_RAIL_TRAVELLERS_ROUTE_EXTENSION_GO_BACK,
              );
            Actions.pop();
            onClose(null);
            break;
          case CLOSE_BOTTOMSHEET:
            onClose(null);
            break;
          case RETRY:
            trackClickEventProp61(
            TRAVELERS_PAGE_TRACKING_KEY_NEW,
            RAILS_TRAVELLER_VISIT_LATER_TRACKING
            )
            setErrorFlowCode(code);
            reviewCall();
            break;
          case CONTINUE:
            isRouteExtension &&
              trackClickEventProp61(
                TRAVELERS_PAGE_TRACKING_KEY_NEW,
                MOB_RAIL_TRAVELLERS_ROUTE_EXTENSION_CONTINUE,
              );
            setErrorFlowCode(code);
            reviewCall(true);
            break;
          case WEBVIEW:
            trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, MOB_RAIL_TRAVELLERS_ERROR_LINK);
            Actions.openWebView({
              url: deeplink,
              headerText: 'MakeMyTrip',
              headerIcon: backIcon,
            });
            onClose(null);
            break;
          case SET_ALERT:
            onSetAlertClicked();
            break;
          case DEEPLINK:
            GenericModule.openDeepLink(deeplink);
            onClose(null);
            break;
          default:
            onClose(null);
            break;
        }
      };
      /* eslint-disable */
      return index === 0 ? (
        <GradientButton text={text} onPress={onPress} />
      ) : (
        <PlainButton text={text} onPress={onPress} />
      );
    },
    [onClose, reviewCall],
  );
  useEffect(() => {
    trackGenericEvar99Event(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ERROR_HANDLING_BS,
    );
  }, []);
  useEffect(() => {
    if (logTravellerPageBottomSheetEvents) {
      logTravellerPageBottomSheetEvents(TRAVELLER_CLICK_EVENTS.REVIEW_ERROR_BOTTOM_SHEET);
    }
  }, [logTravellerPageBottomSheetEvents]);

  useEffect(() => {
    if (logTravellerPageBottomSheetEvents && isPasswordResetBottomSheet(errorCode)) {
      logTravellerPageBottomSheetEvents(TRAVELLER_PDT_EVENTS.TRAVELLER_RESET_PWD_BS_LOADED.event_value);
    }
  }, [logTravellerPageBottomSheetEvents, errorCode]);
if (isAlertLoading) {
  return (
    <View style={styles.container} testID="loader-container">
      <View style={styles.loader} testID="loader">
        <Spinner size={30} color={colors.azure} />
      </View>
    </View>
  );
}

const isSetAlertBottomsheet = () => {
  return errorCode === REVIEW_ERROR_CODE_ARP_BOTTOMSHEET && setAlertApiSuccess;
};
  return (
    <View style={styles.container}>
      {showLoader || isAlertLoading ? (
        <View style={styles.loader} testID="loader">
          <Spinner size={30} color={colors.azure} />
        </View>
      ) : isIrctcProfileIncomplete ? (
        renderIrctcProfileIncomplete({
          errorFlow,
          onClose,
          irctcUserName,
          config,
          ctas,
          ctaType,
        })
      ) : isPasswordResetBottomSheet(errorCode) ? (
        <PasswordResetBottomSheet
          errorFlow={errorFlow}
          onClose={onClose}
          irctcUserName={irctcUserName}
          mobileNumber={mobileNumber}
          email={email}
          ctaType={ctaType}
          config={config}
          ctas={ctas}
          logTravellerPageBottomSheetEvents={logTravellerPageBottomSheetEvents}
        />
      ) : (
        <>
          <View style={styles.widthHundred} testID="review_error_bottomsheet_width_hundred">
            <View style={styles.subContainer} testID="review_error_bottomsheet_sub_container">
              {!(timer && timer.startTime != null && timer.endTime != null) && (
                <View style={styles.verticallyCenter} testID="review_error_bottomsheet_vertically_center">
                  <Image
                    source={{ uri: iconUrl }}
                    style={styles.errorFilled}
                    resizeMode="contain"
                    testID="review_error_bottomsheet_error_filled"
                  />
                </View>
              )}
              <View style={styles.titleContainer} testID="review_error_bottomsheet_title_container">
                <View style={[styles.titleBlock, styles.textContainer]} testID="review_error_bottomsheet_title_block">
                    <Text style={[styles.title, fontStyle('latoBlack')]} testID="review_error_bottomsheet_title">{title}</Text>
                  <Text style={[styles.subTitle, fontStyle('regular')]} testID="review_error_bottomsheet_sub_title">{subTitle}</Text>
                </View>
                {timer && timer.startTime != null && timer.endTime != null && (
                  <View style={styles.timerContainer} testID="review_error_bottomsheet_timer_container">
                    <TimerComponent
                      duration={Math.abs(timer.startTime - timer.endTime)}
                      startTime={timerStart}
                      onClose={closeBottomSheet}
                    />
                  </View>
                )}
              </View>
            </View>
            {notificationText && (
              <View style={[styles.notificationContainer, styles.mar24Bt]} testID="review_error_bottomsheet_notification_container">
                <Image
                  source={{
                    uri: 'https://go-assets.ibcdn.com/u/MMT/images/1750312049765-notification_black_icon.png',
                  }}
                  style={styles.notificationIcon}
                  testID="review_error_bottomsheet_notification_icon"
                />
                <Text style={styles.notificationTitle} testID="review_error_bottomsheet_notification_title">{notificationText}</Text>
              </View>
            )}
            {isSetAlertBottomsheet() ? (
              <View testID="review_error_bottomsheet_got_it_container">{ctaType(GOT_IT, CLOSE_BOTTOMSHEET, 'null', 'null', 0)}</View>
            ) : (
              <View testID="review_error_bottomsheet_ctas_container">
                {ctas?.map((cta: CTAType, index: number) =>
                  ctaType(cta?.text, cta?.action, cta?.code, cta?.deeplink, index),
                )}
              </View>
            )}
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 16,
    lineHeight: 22,
    color: colors.black,
  },
  verticallyCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  subTitle: {
    fontSize: 16,
    lineHeight: 22,
    color: colors.defaultTextColor,
    paddingRight: 25,
  },
  clickableLink: {
    color: colors.white,
    fontFamily: 'Lato',
    fontStyle: 'normal',
    lineHeight: 22,
    fontSize: 16,
    fontWeight: '900',
    textAlign: 'center',
  },
  clickableLinkAzure: {
    color: colors.azure,
    fontFamily: 'Lato',
    fontStyle: 'normal',
    lineHeight: 22,
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },
  notificationTitle: {
    color: colors.textGrey,
    fontStyle: 'normal',
    lineHeight: 15,
    fontSize: 12,
    fontWeight: '400',
    textAlign: 'center',
  },
  notificationContainer: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 8,
    justifyContent: 'flex-start',
    backgroundColor: colors.creamWhite,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: -12,
  },
  notificationIcon: {
    height: 16,
    width: 16,
    marginRight: 8,
  },
  checkAvlBtnGradient: {
    paddingVertical: 12.5,
    paddingHorizontal: 15,
    borderRadius: 8,
    justifyContent: 'center',
  },
  checkAvlBtn: {
    paddingVertical: 12.5,
    paddingHorizontal: 15,
    borderRadius: 8,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.azure,
  },
  mar10Bt: {
    marginBottom: 10,
  },
  mar24Bt: {
    marginBottom: 24,
  },
  widthHundred: {
    width: '100%',
  },
  container: {
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    overflow: 'hidden',
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 16,
  },
  subContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  textContainer: {
    flex: 1,
  },
  errorFilled: {
    height: 32,
    width: 24,
    marginRight: 10,
  },
  loader: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleBlock: {
    flex: 3,
  },
  timerContainer: {
    marginLeft: 2,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});

const mapStateToProps = (state: unknown) => {
  const {
    railsTraveler: { isAlertLoading = false } = {},
    railsReview: { setAlertApiSuccess = false } = {},
    railsUserVerification: { irctcUserName = '' } = {},
  } = state;

  return {
    isAlertLoading,
    setAlertApiSuccess,
    irctcUserName,
  };
};

export default connect(mapStateToProps)(ReviewDowntimeBottomSheet);
