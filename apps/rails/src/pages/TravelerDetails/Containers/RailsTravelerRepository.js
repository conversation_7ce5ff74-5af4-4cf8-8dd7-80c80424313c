import Traveler from '../Components/Traveler';
import { getGenderConst } from '../../Constants/Gender';
import { COUNTRIES } from '../DisplayDropdown';
import { normalizeNationality } from '../../../Utils/RailsConstant';
import { loadTravelersFromServer, saveTravelerInServer } from './RailsUserServiceRepository';
import { isAgeSpecifiedInTraveler, isChild } from '../TravelerDetailsUtils';
import includes from 'lodash/includes';
import sortBy from 'lodash/sortBy';
import find from 'lodash/find';
import isNull from 'lodash/isNull';
import isUndefined from 'lodash/isUndefined';
import values from 'lodash/values';
import difference from 'lodash/difference';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import forEach from 'lodash/forEach';
import pick from 'lodash/pick';
import { isMweb } from '../../../Utils/device';
import {
  setRailsGuestSelectedTravelersList,
  setRailsGuestTravelersList,
  setRailsLoggedInSelectedTravelersList,
  setRailsLoggedInTravelersList,
} from '../TravelerDetailsActions';

const RAILS_LOGGED_IN_TRAVELERS_LIST = 'RAILS_LOGGED_IN_TRAVELERS_LIST';
const RAILS_GUEST_TRAVELERS_LIST = 'RAILS_GUEST_TRAVELERS_LIST';

const RAILS_LOGGED_IN_SELECTED_TRAVELERS_LIST = 'RAILS_LOGGED_IN_SELECTED_TRAVELERS_LIST';
const RAILS_GUEST_SELECTED_TRAVELERS_LIST = 'RAILS_GUEST_SELECTED_TRAVELERS_LIST';

const initCachedTravelersMetaData = async (travelers, railsTraveler, getState) => {
  const newTravelers = [];
  const selectedTravelers = loadSelectedTravelersFromCache(railsTraveler, getState);
  const {
    applicableBerthTypes,
    foodChoiceEnabled,
    selectedQuota,
    classValue,
    seniorCitizenApplicable,
    applicableFoodTypes,
  } = railsTraveler;
  for (let i = 0; i < travelers.length; i++) {
    const t = travelers[i];
    const nationalityNormalized = normalizeNationality(t.nationalit, COUNTRIES);
    const traveler = new Traveler(
      t.travelerId,
      t.name,
      t.age,
      t.gender,
      t.childBerthFlag,
      applicableBerthTypes,
      foodChoiceEnabled,
      selectedQuota,
      classValue,
      includes(selectedTravelers, t.travelerId),
      seniorCitizenApplicable,
      t.berth,
      t.meal,
      t.seniorCitizenConcession,
      nationalityNormalized,
      t.mealPreferenceArray,
      applicableFoodTypes,
      false,
    );
    newTravelers.push(traveler);
  }
  return sortBy(newTravelers, [(traveller) => traveller.fullName.toLowerCase()]);
};

const initServerTravelersMetaData = (travelers, railsTraveler, cachedTravelers, getState) => {
  const newTravelers = [];
  const selectedTravelers = loadSelectedTravelersFromCache(railsTraveler, getState);
  const {
    applicableBerthTypes,
    foodChoiceEnabled,
    selectedQuota,
    classValue,
    seniorCitizenApplicable,
    applicableFoodTypes,
  } = railsTraveler;
  for (let i = 0; i < travelers.length; i++) {
    const t = travelers[i];
    const isAgeSpecified = isAgeSpecifiedInTraveler(t.age);
    const childBerthFlag = isAgeSpecified && isChild(t.age);
    const cachedT = find(cachedTravelers, (value) => value?.travelerId === t?.travellerId);
    let traveler;
    if (isNull(cachedT) || isUndefined(cachedT)) {
      // Normalize nationality from server string to { code, name }
      const nationalityNormalized = normalizeNationality(t.nationality, COUNTRIES);

      traveler = new Traveler(
        t.travellerId,
        t.name,
        t.age,
        getGenderConst(t.gender),
        childBerthFlag,
        applicableBerthTypes,
        foodChoiceEnabled,
        selectedQuota,
        classValue,
        includes(selectedTravelers, t.travellerId),
        seniorCitizenApplicable,
        t.berth,
        t.meal,
        t.seniorCitizenConcession,
        nationalityNormalized,
        t.mealPreferenceArray,
        applicableFoodTypes,
      );
      if (t?.dateOfBirth) {
        traveler.dateOfBirth = t.dateOfBirth;
      }
      if (Array.isArray(t?.travelDocuments) && t.travelDocuments.length > 0) {
        const passportDocs = t.travelDocuments.filter((d) => d?.docType === 'PASSPORT');
        const firstPassport = passportDocs.length > 0 ? passportDocs[0] : null;
        if (firstPassport?.docNumber) {
          traveler.passportNumber = firstPassport.docNumber;
        }
      }
      newTravelers.push(traveler);
    } else {
      newTravelers.push(cachedT);
    }
  }
  return newTravelers;
};

const getTravelersFromCache = (key, railsTraveler, getState) => {
  try {
    let travelers = [];
    const {
      railsTraveler: {
        railsGuestSelectedTravelersList,
        railsGuestTravelersList,
        railsLoggedInSelectedTravelersList,
        railsLoggedInTravelersList,
      } = {},
    } = getState();

    switch (key) {
      case RAILS_LOGGED_IN_TRAVELERS_LIST: {
        travelers = values(railsLoggedInTravelersList);
        break;
      }
      case RAILS_GUEST_TRAVELERS_LIST: {
        travelers = values(railsGuestTravelersList);
        break;
      }
      case RAILS_LOGGED_IN_SELECTED_TRAVELERS_LIST: {
        travelers = values(railsLoggedInSelectedTravelersList);
        break;
      }
      case RAILS_GUEST_SELECTED_TRAVELERS_LIST: {
        travelers = values(railsGuestSelectedTravelersList);
      }
    }
    return initCachedTravelersMetaData(travelers, railsTraveler, getState);
  } catch (e) {
    return [];
  }
};

const validateTravelerInCache = async (travelers, railsTraveler, getState, dispatch) => {
  try {
    const cachedTraveler = await getTravelersFromCache(
      RAILS_LOGGED_IN_TRAVELERS_LIST,
      railsTraveler,
      getState,
    );
    const newTravelers = difference(travelers, cachedTraveler);
    const allTravelers = [...newTravelers, ...cachedTraveler];
    const travelersObject = allTravelers.reduce(
      (acc, trv) => ({
        [trv.travelerId]: trv.basicInfo,
        ...acc,
      }),
      {},
    );
    dispatch(setRailsLoggedInTravelersList(travelersObject));
  } catch (err) {
    console.log('Error in validateTravelerInCache : ', err);
  }
};

const getTravelersFromServer = async (railsTraveler, cachedTravelers, getState, dispatch) => {
  let travelers = [];
  const { userDetails } = railsTraveler;
  const mmtAuth = get(userDetails, 'mmtAuth', null);
  if (!isEmpty(mmtAuth)) {
    let travelersFromServer;
    if (isMweb()) {
      // No need to fetch travelers, its already fetched during userDetails
      travelersFromServer = userDetails.travelers;
    } else {
      travelersFromServer = await loadTravelersFromServer(mmtAuth);
    }
    const activeTravelers = travelersFromServer.filter(
      (item) => item.status.toLowerCase() !== 'deleted',
    );
    const serverTravelers = initServerTravelersMetaData(
      activeTravelers,
      railsTraveler,
      cachedTravelers,
      getState,
    );
    validateTravelerInCache(serverTravelers, railsTraveler, getState, dispatch);
    travelers = serverTravelers;
  } else {
    travelers = getTravelersFromCache(RAILS_LOGGED_IN_TRAVELERS_LIST, railsTraveler, getState);
  }
  return sortBy(travelers, [(traveller) => traveller.fullName.toLowerCase()]);
};

const loadLoggedInTravelersFromCache = async (railsTraveler, getState, dispatch) => {
  let travelers;
  let cachedTravelers;
  try {
    cachedTravelers = getTravelersFromCache(
      RAILS_LOGGED_IN_TRAVELERS_LIST,
      railsTraveler,
      getState,
    );
    if (isEmpty(railsTraveler?.travelers)) {
      travelers = await getTravelersFromServer(railsTraveler, cachedTravelers, getState, dispatch);
    } else {
      travelers = cachedTravelers;
    }
  } catch (e) {
    console.log('Error in loadLoggedInTravelersFromCache : ', e);
    travelers = cachedTravelers;
  }
  return travelers;
};

const loadGuestTravelersFromCache = (railsTraveler, getState) => {
  let travelers = [];
  try {
    travelers = getTravelersFromCache(RAILS_GUEST_TRAVELERS_LIST, railsTraveler, getState);
  } catch (e) {
    console.log('Error in loadGuestTravelersFromCache : ', e);
  }
  return travelers;
};

export const loadRailsTravelers = async (railsTraveler, getState, dispatch) => {
  let traveler = [];
  try {
    const { isLoggedIn } = railsTraveler;
    if (isLoggedIn) {
      traveler = await loadLoggedInTravelersFromCache(railsTraveler, getState, dispatch);
    } else {
      traveler = loadGuestTravelersFromCache(railsTraveler, getState);
    }
  } catch (e) {
    console.log(`Error in loadRailsTravelers : ${e}`);
  }
  return traveler;
};

export const getTravelersListFromId = (selectedTravelers, travelers) => {
  const travelersList = [];
  if (isUndefined(travelers)) {
    return travelersList;
  }
  try {
    forEach(selectedTravelers, (travelerId) => {
      for (let i = 0; i < travelers.length; i++) {
        if (travelers[i].travelerId === travelerId) {
          travelersList.push(travelers[i]);
        }
      }
    });
  } catch (e) {
    console.log('Error in getTravelersListFromId', e);
  }
  return travelersList;
};


export const loadSelectedTravelersFromCache = (railsTraveler, getState) => {
  try {
    const {
      railsTraveler: { railsGuestSelectedTravelersList, railsLoggedInSelectedTravelersList } = {},
    } = getState();

    if (railsTraveler.isLoggedIn) {
      // List of selected travelers Id
      return railsLoggedInSelectedTravelersList;
    } else {
      // List of selected travelers Id
      return railsGuestSelectedTravelersList;
    }
  } catch (e) {
    console.log(`Error in loadSelectedTravelersFromCache${e}`);
  }
};

const saveTravelersInCache = async (key, travelers, dispatch) => {
  try {
    switch (key) {
      case RAILS_LOGGED_IN_TRAVELERS_LIST: {
        dispatch(setRailsLoggedInTravelersList(travelers));
        break;
      }
      case RAILS_GUEST_TRAVELERS_LIST: {
        dispatch(setRailsGuestTravelersList(travelers));
      }
    }
  } catch (e) {
    console.log(`saveTravelersInCache :${e}`);
  }
};

const bulkSaveTravelersInCache = async (key, travelers, dispatch) => {
  const basicInfoObj = {};
  for (let i = 0; i < travelers.length; i++) {
    basicInfoObj[travelers[i].basicInfo.travelerId] = travelers[i].basicInfo;
  }
  await saveTravelersInCache(key, basicInfoObj, dispatch);
};

const getTravelerArrayOfBasicInfo = (travelers) => {
  const basicInfoArr = [];
  forEach(travelers, (traveler) => basicInfoArr.push(pick(traveler, 'basicInfo')));
  return basicInfoArr;
};

export const storeRailsTravelersInCache = async (isLoggedIn, travelers, dispatch) => {
  try {
    const key = isLoggedIn ? RAILS_LOGGED_IN_TRAVELERS_LIST : RAILS_GUEST_TRAVELERS_LIST;
    const travelersBasicInfo = getTravelerArrayOfBasicInfo(travelers);
    await bulkSaveTravelersInCache(key, travelersBasicInfo, dispatch);
  } catch (e) {
    console.log(`Error in storeRailsTravelersInCache${e}`);
  }
};

export const getIdListFromTravelers = (selectedTravelers) => {
  const idList = [];
  forEach(selectedTravelers, (traveler) => idList.push(pick(traveler, 'travelerId').travelerId));
  return idList;
};

export const storeRailsSelectedTravelersInCache = (isLoggedIn, selectedTravelers, dispatch) => {
  try {
    if (isLoggedIn) {
      dispatch(setRailsLoggedInSelectedTravelersList(selectedTravelers));
    } else {
      dispatch(setRailsGuestSelectedTravelersList(selectedTravelers));
    }
  } catch (e) {
    console.log(`Error in storeRailsSelectedTravelersInCache :${e}`);
  }
};

export const sortTravelersBySelection = (travelers, selectedTravelerList) => {
  let sortTList = travelers;
  try {
    const nonSelectedT = difference(travelers, selectedTravelerList);

    sortTList = [...selectedTravelerList, ...nonSelectedT];
  } catch (e) {
    console.log('Error in sortTravelersBySelection', e);
  }
  return sortTList;
};

export const updateTravelerInServer = async (traveler, mmtAuth, addOrEditFlag) => {
  try {
    if (isEmpty(mmtAuth)) {
      return null;
    }
    const savedTraveler = await saveTravelerInServer(traveler, mmtAuth, addOrEditFlag);
    if (!isNull(savedTraveler)) {
      console.log('Update success.');
    } else {
      console.log('Update failure.');
    }
    return savedTraveler;
  } catch (e) {
    console.log('Error in updateTravelerInServer : ', e);
    return null;
  }
};
