import {connect} from 'react-redux';
import TravelInsurance from '../Components/TravelInsurance';
import {onTncClicked, onTravelInsuranceCBClicked, captureRef} from '../TravelerDetailsActions';

const mapStateToProps = (state) => {
  const {
    railsTraveler: {
      travelerInsuranceOpted,
    },
    railsListing: {
      insuranceCharge,
    },
  } = state;
  return {
    travelerInsuranceOpted,
    insuranceCharge,
  };
};

const mapDispatchToProps = dispatch => ({
  onTravelInsuranceCBClicked: isTravelInsuranceOpted => dispatch(onTravelInsuranceCBClicked(isTravelInsuranceOpted)),
  onTncClicked: (e) => onTncClicked(e),
  captureRef: (componentName, myScrollRef) => dispatch(captureRef(componentName, myScrollRef)),
});

export default connect(mapStateToProps, mapDispatchToProps)(TravelInsurance);
