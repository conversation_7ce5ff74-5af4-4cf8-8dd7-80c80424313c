/* eslint-disable */
import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, TextInput, Keyboard } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { fontStyle, getLineHeight } from '@mmt/rails/src/vernacular/VernacularUtils';
import { colors, gradient } from '@mmt/legacy-commons/Styles/globalStyles';
import { isValidEmail } from '@mmt/legacy-commons/Helpers/validationHelpers';
import {
  retrievePassword,
  JSON_FIELD,
  STATUS_API_SUCCESS,
  STATUS_LIMIT_EXCEEDED,
} from '@mmt/rails/src/Utils/UserRepository';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import NewIrctcModalStyle from '../../NewUserFlow/NewModalStyleSheet';
import ASSETS from '../../../Utils/Assets/RailsAssets';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  CLOSE_BOTTOMSHEET,
  GO_BACK_TO_BS,
  RAILS_BOOKING_REVIEW_PASSWORD_EXPIRED,
  CALL_API,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  MOB_RAIL_TRAVELLERS_RESET_PWD,
  MOB_RAIL_TRAVELLERS_RECEIVE_PWD,
  MOB_RAIL_TRAVELLERS_CLOSE_PWD,
  MOB_RAIL_TRAVELLERS_BACK_PWD,
  MOB_RAIL_TRAVELLERS_PWD_CROSS,
} from '@mmt/rails/src/Utils/RailsConstant';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { PASSWORD_RESET_CONSTANTS } from '../TravelerDetails.constant';

export interface CTAType {
  text: string;
  action: string;
  code: string;
  deeplink: string;
}

export interface ConfigType {
  travellerBs?: {
    title: string;
    profileCompletionTitle: string;
    profileCompletionSteps: string[];
    footerText: string;
  };
  successText?: string;
  errorText?: string;
}

interface HelperProps {
  errorCode?: string;
  errorFlow?: {
    title: string;
    subTitle: string;
    ctas?: Array<CTAType>;
  };
  onClose: (val: unknown) => void;
  config: ConfigType | null;
  ctas?: Array<CTAType>;
  ctaType?: (
    text: string,
    action: string,
    code: string,
    deeplink: string,
    index: number,
  ) => React.ReactNode;
  irctcUserName?: string;
  mobileNumber?: string;
  email?: string;
  reviewCall?: (val?: boolean) => void;
  setErrorFlowCode?: (val: unknown) => void;
  logTravellerPageBottomSheetEvents?: (eventValue: string) => void;
}

export const isPasswordResetBottomSheet = (errorCode?: string): boolean => {
  return errorCode === RAILS_BOOKING_REVIEW_PASSWORD_EXPIRED;
};

const GradientButton = ({ text, onPress }: { text: string; onPress: () => void }) => (
  <TouchableRipple onPress={onPress}>
    <LinearGradient
      start={{ x: 1.0, y: 0.0 }}
      end={{ x: 0.0, y: 1.0 }}
      colors={gradient.lightBlue}
      style={[helperStyles.checkAvlBtnGradient, helperStyles.mar10Bt]}
      testID="gradient-button"
    >
      <Text style={helperStyles.clickableLink} testID={`gradient-button-text-${text}`}>
        {text}
      </Text>
    </LinearGradient>
  </TouchableRipple>
);

export const PasswordResetBottomSheet: React.FC<HelperProps> = ({
  errorFlow,
  onClose,
  irctcUserName,
  mobileNumber = '',
  email = '',
  ctaType,
  ctas,
  logTravellerPageBottomSheetEvents,
}) => {
  const hasResetPasswordAction = ctas?.some((cta: CTAType) => cta?.action === CALL_API);

  const [passwordResetState, setPasswordResetState] = useState({
    inputData: mobileNumber || '',
    enableCTA: false,
    isLoading: false,
    pwdRequested: false,
    mobileChecked: true,
    error: '',
    showClearIcon: !!(mobileNumber || ''),
    isPrefilled: !!(mobileNumber || ''),
  });

  const [resetPasswordConfig, setResetPasswordConfig] = useState<{
    title: string;
    subtitle: string;
    description: string;
    ctas: CTAType[];
  } | null>(null);
  const [showSuccessBottomSheet, setShowSuccessBottomSheet] = useState(false);

  const { inputData, mobileChecked, enableCTA: currentEnableCTA } = passwordResetState;

  useEffect(() => {
    const enableCTA =
      (mobileChecked && inputData.length === 10) || (!mobileChecked && isValidEmail(inputData));

    if (enableCTA !== currentEnableCTA) {
      setPasswordResetState((prev) => ({ ...prev, enableCTA }));
    }
  }, [inputData, mobileChecked, currentEnableCTA]);

  const handlePasswordInputChange = (input: string) => {
    const { mobileChecked } = passwordResetState;
    const enableCTA =
      (mobileChecked && input.length === 10) || (!mobileChecked && isValidEmail(input));

    setPasswordResetState({
      ...passwordResetState,
      inputData: input,
      enableCTA,
      error: '',
      showClearIcon: false,
      isPrefilled: false,
    });
  };

  const handleClearInput = () => {
    trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, MOB_RAIL_TRAVELLERS_PWD_CROSS);
    if (logTravellerPageBottomSheetEvents) {
      logTravellerPageBottomSheetEvents(TRAVELLER_CLICK_EVENTS.TRAVELLER_CROSS_INPUT_AFTER_ERROR);
    }

    setPasswordResetState({
      ...passwordResetState,
      inputData: '',
      enableCTA: false,
      error: '',
      showClearIcon: false,
      isPrefilled: true,
    });
  };

  const handleMobileEmailToggle = () => {
    const newMobileChecked = !passwordResetState.mobileChecked;
    const newInputData = newMobileChecked ? mobileNumber || '' : email || '';
    setPasswordResetState({
      ...passwordResetState,
      mobileChecked: newMobileChecked,
      inputData: newInputData,
      error: '',
      isLoading: false,
      enableCTA: false,
      isPrefilled: !!newInputData,
    });
  };

  const handleRetrievePassword = async () => {
    Keyboard.dismiss();
    const { inputData, mobileChecked, enableCTA } = passwordResetState;

    if (!enableCTA || !irctcUserName) {
      return;
    }

    trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, MOB_RAIL_TRAVELLERS_RESET_PWD);
    if (logTravellerPageBottomSheetEvents) {
      logTravellerPageBottomSheetEvents(TRAVELLER_CLICK_EVENTS.TRAVELLER_RESET_PWD_AFTER_ERROR);
    }

    setPasswordResetState({ ...passwordResetState, isLoading: true });

    const status = await retrievePassword(irctcUserName, inputData, mobileChecked, true);

    if (!status || Object.keys(status).length === 0) {
      setPasswordResetState({
        ...passwordResetState,
        isLoading: false,
        enableCTA: false,
        pwdRequested: false,
        error: mobileChecked
          ? _label('mobile_number_unregistered_irctc')
          : _label('email_unregistered_irctc'),
      });
      return;
    }

    if (status[JSON_FIELD.status] === STATUS_API_SUCCESS) {
      if (status.resetPasswordConfig) {
        setPasswordResetState({
          ...passwordResetState,
          isLoading: false,
          pwdRequested: true,
          error: '',
        });
        setResetPasswordConfig(status.resetPasswordConfig);
        setShowSuccessBottomSheet(true);
      } else {
        setPasswordResetState({
          ...passwordResetState,
          isLoading: false,
          error: '',
        });
        setTimeout(() => {
          onClose(null);
        }, 3000);
      }
    } else if (status[JSON_FIELD.status] === STATUS_LIMIT_EXCEEDED) {
      setPasswordResetState({
        ...passwordResetState,
        isLoading: false,
        enableCTA: false,
        pwdRequested: false,
        error: status[JSON_FIELD.message] || _label('too_many_password_retry'),
      });
    } else {
      setPasswordResetState({
        ...passwordResetState,
        isLoading: false,
        enableCTA: false,
        pwdRequested: false,
        error:
          status[JSON_FIELD.message] ||
          (mobileChecked
            ? _label('mobile_number_unregistered_irctc')
            : _label('email_unregistered_irctc')),
      });
    }
  };

  const enhancedCtaType = (
    text: string,
    type: string,
    code: string,
    deeplink: string,
    index: number,
  ) => {
    const onPress = () => {
      if (type === CALL_API) {
        handleRetrievePassword();
      } else if (type === GO_BACK_TO_BS) {
        const prop61Event =
          index === 0 ? MOB_RAIL_TRAVELLERS_RECEIVE_PWD : MOB_RAIL_TRAVELLERS_BACK_PWD;
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, prop61Event);

        if (logTravellerPageBottomSheetEvents) {
          const pdtEvent =
            index === 0
              ? TRAVELLER_CLICK_EVENTS.TRAVELLER_RECEIVE_PWD_AFTER_ERROR
              : TRAVELLER_CLICK_EVENTS.TRAVELLER_GO_BACK_AFTER_ERROR;
          logTravellerPageBottomSheetEvents(pdtEvent);
        }

        setShowSuccessBottomSheet(false);
        setResetPasswordConfig(null);
        setPasswordResetState({
          ...passwordResetState,
          pwdRequested: false,
          isLoading: false,
          error: '',
          showClearIcon: true,
          isPrefilled: true,
        });
      } else if (type === CLOSE_BOTTOMSHEET) {
        const prop61Event =
          index === 0 ? MOB_RAIL_TRAVELLERS_RECEIVE_PWD : MOB_RAIL_TRAVELLERS_CLOSE_PWD;
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, prop61Event);

        if (logTravellerPageBottomSheetEvents) {
          const pdtEvent =
            index === 0
              ? TRAVELLER_CLICK_EVENTS.TRAVELLER_RECEIVE_PWD_AFTER_ERROR
              : TRAVELLER_CLICK_EVENTS.TRAVELLER_CLOSE_PWD_AFTER_ERROR;
          logTravellerPageBottomSheetEvents(pdtEvent);
        }
        onClose(null);
      } else if (ctaType) {
        return ctaType(text, type, code, deeplink, index);
      }
    };

    if (type === CALL_API) {
      return (
        <TouchableRipple onPress={onPress} testID={`gradient-button-${text}`}>
          <LinearGradient
            start={{ x: 1.0, y: 0.0 }}
            end={{ x: 0.0, y: 1.0 }}
            colors={!passwordResetState.enableCTA ? [colors.grey, colors.grey] : gradient.lightBlue}
            style={[helperStyles.checkAvlBtnGradient, helperStyles.mar10Bt]}
            testID={`gradient-button-${text}`}
          >
            {!passwordResetState.isLoading ? (
              <Text style={helperStyles.clickableLink} testID={`gradient-button-text-${text}`}>
                {text}
              </Text>
            ) : (
              <Spinner size={20} color={colors.white} />
            )}
          </LinearGradient>
        </TouchableRipple>
      );
    }

    return index === 0 ? (
      <GradientButton text={text} onPress={onPress} />
    ) : (

      <TouchableRipple onPress={onPress} testID={`gradient-button-${text}`}>
        <View
          style={[helperStyles.checkAvlBtn, helperStyles.mar10Bt]}
          testID={`gradient-button-${text}`}
        >
          <Text style={helperStyles.clickableLinkAzure} testID={`gradient-button-text-${text}`}>
            {text}
          </Text>
        </View>
      </TouchableRipple>
    );
  };

  return (
    <View style={helperStyles.widthHundred} testID="password-reset-bottom-sheet-container">
      {showSuccessBottomSheet && resetPasswordConfig ? (
        <View
          style={helperStyles.widthHundred}
          testID="password-reset-bottom-sheet-success-container"
        >
          <View style={helperStyles.subContainer}>
            <View
              style={[helperStyles.titleBlock, helperStyles.textContainer]}
              testID="password-reset-bottom-sheet-success-title-row"
            >
              <View
                style={helperStyles.passwordTitleRow}
                testID="password-reset-bottom-sheet-success-title-row"
              >
                <Image
                  source={{ uri: errorFlow?.iconUrl }}
                  style={helperStyles.errorIcon}
                  resizeMode="contain"
                  testID="password-reset-bottom-sheet-success-icon"
                />
                <Text
                  style={[helperStyles.title, fontStyle('latoBlack')]}
                  testID="password-reset-bottom-sheet-success-title"
                >
                  {resetPasswordConfig.title}
                </Text>
              </View>
              <Text
                style={[helperStyles.subTitle, helperStyles.subtitleNoPadding]}
                testID="password-reset-bottom-sheet-success-sub-title"
              >
                {resetPasswordConfig.subtitle}
              </Text>
            </View>
          </View>

          <View
            style={[helperStyles.inputContainer, helperStyles.successInputContainer]}
            testID="password-reset-bottom-sheet-success-input-container"
          >
            <View
              style={helperStyles.inputWrapper}
              testID="password-reset-bottom-sheet-success-input-wrapper"
            >
              <View
                style={[helperStyles.pnrTextInput, helperStyles.successInputBox]}
                testID="password-reset-bottom-sheet-success-input-box"
              >
                <Text
                  style={[helperStyles.successInputText, { color: colors.black }]}
                  testID="password-reset-bottom-sheet-success-input-text"
                >
                  {passwordResetState.inputData}
                </Text>
              </View>
              <Text
                style={[helperStyles.successFloatingLabel, fontStyle('bold')]}
                testID="password-reset-bottom-sheet-success-floating-label"
              >
                {passwordResetState.mobileChecked
                  ? PASSWORD_RESET_CONSTANTS.MOBILE_LABEL
                  : PASSWORD_RESET_CONSTANTS.EMAIL_LABEL}
              </Text>
            </View>
          </View>

          {resetPasswordConfig.description && (
            <Text
              style={[helperStyles.description, fontStyle('regular')]}
              testID="password-reset-bottom-sheet-success-description"
            >
              {resetPasswordConfig.description}
            </Text>
          )}

          <View
            style={helperStyles.ctaContainer}
            testID="password-reset-bottom-sheet-success-ctas-container"
          >
            {resetPasswordConfig.ctas?.map((cta: CTAType, index: number) =>
              enhancedCtaType(cta?.text, cta?.action, cta?.code, cta?.deeplink, index),
            )}
          </View>
        </View>
      ) : (
        <View style={helperStyles.widthHundred} testID="password-reset-bottom-sheet-container">
          {!passwordResetState.pwdRequested ? (
            <>
              <View
                style={helperStyles.subContainer}
                testID="password-reset-bottom-sheet-title-row"
              >
                <View
                  style={[helperStyles.titleBlock, helperStyles.textContainer]}
                  testID="password-reset-bottom-sheet-title-row"
                >
                  <View
                    style={helperStyles.titleRowWithIcon}
                    testID="password-reset-bottom-sheet-title-row"
                  >
                    <Image
                      source={{ uri: errorFlow?.iconUrl }}
                      style={helperStyles.errorIcon}
                      resizeMode="contain"
                      testID="password-reset-bottom-sheet-icon"
                    />
                    <Text
                      style={[helperStyles.title, fontStyle('latoBlack')]}
                      testID="password-reset-bottom-sheet-title"
                    >
                      {errorFlow?.title}
                    </Text>
                  </View>
                  <Text
                    style={[helperStyles.subTitle, helperStyles.subTitleNoPadding]}
                    testID="password-reset-bottom-sheet-sub-title"
                  >
                    {errorFlow?.subTitle}
                  </Text>
                </View>
              </View>

              {hasResetPasswordAction && (
                <View
                  style={[helperStyles.radioContainer, helperStyles.radioContainerOverride]}
                  testID="password-reset-bottom-sheet-radio-container"
                >
                  <TouchableRipple onPress={handleMobileEmailToggle}>
                    <View
                      style={helperStyles.radioOption}
                      testID="password-reset-bottom-sheet-radio-option"
                    >
                      <RadioButton
                        isSelected={passwordResetState.mobileChecked}
                        onPress={handleMobileEmailToggle}
                        radioSize={18}
                        testID="password-reset-bottom-sheet-radio-button"
                      />
                      <Text
                        style={[helperStyles.radioText, fontStyle('medium')]}
                        testID="password-reset-bottom-sheet-radio-text"
                      >
                        {_label('on_mobile_number', { capitalize: true })}
                      </Text>
                    </View>
                  </TouchableRipple>

                  <TouchableRipple onPress={handleMobileEmailToggle}>
                    <View
                      style={helperStyles.radioOption}
                      testID="password-reset-bottom-sheet-radio-option"
                    >
                      <RadioButton
                        isSelected={!passwordResetState.mobileChecked}
                        onPress={handleMobileEmailToggle}
                        radioSize={18}
                        testID="password-reset-bottom-sheet-radio-button"
                      />
                      <Text
                        style={[helperStyles.radioText, fontStyle('medium')]}
                        testID="password-reset-bottom-sheet-radio-text"
                      >
                        {_label('on_email_id', { capitalize: true })}
                      </Text>
                    </View>
                  </TouchableRipple>
                </View>
              )}

              {hasResetPasswordAction && (
                <View
                  style={helperStyles.inputContainer}
                  testID="password-reset-bottom-sheet-input-container"
                >
                  <View
                    style={helperStyles.normalInputWrapper}
                    testID="password-reset-bottom-sheet-input-wrapper"
                  >
                    <TextInput
                      style={[
                        helperStyles.pnrTextInput,
                        helperStyles.normalPasswordInput,
                        passwordResetState.error
                          ? helperStyles.normalPasswordInputError
                          : passwordResetState.inputData.length >= 10
                          ? helperStyles.normalPasswordInputInactive
                          : helperStyles.normalPasswordInputActive,
                        passwordResetState.inputData.length === 10
                          ? helperStyles.normalPasswordInputWithClear
                          : helperStyles.normalPasswordInputNormalPadding,
                        passwordResetState.inputData.length >= 10 && { color: colors.black },
                      ]}
                      selectionColor="#000000"
                      placeholderTextColor="transparent"
                      keyboardType={passwordResetState.mobileChecked ? 'numeric' : 'email-address'}
                      maxLength={passwordResetState.mobileChecked ? 10 : 254}
                      onChangeText={handlePasswordInputChange}
                      value={passwordResetState.inputData}
                      returnKeyType="done"
                      onSubmitEditing={() => Keyboard.dismiss()}
                    />
                    <Text
                      style={[
                        helperStyles.normalFloatingLabel,
                        passwordResetState.error
                          ? helperStyles.normalFloatingLabelError
                          : passwordResetState.inputData.length >= 10
                          ? helperStyles.normalFloatingLabelInactive
                          : helperStyles.normalFloatingLabelActive,
                        fontStyle('bold'),
                      ]}
                      testID="password-reset-bottom-sheet-floating-label"
                    >
                      {passwordResetState.mobileChecked
                        ? PASSWORD_RESET_CONSTANTS.MOBILE_LABEL
                        : PASSWORD_RESET_CONSTANTS.EMAIL_LABEL}
                    </Text>
                    {passwordResetState.isPrefilled &&
                      passwordResetState.inputData &&
                      (passwordResetState.mobileChecked
                        ? passwordResetState.inputData.length === 10
                        : isValidEmail(passwordResetState.inputData)) && (
                        <TouchableOpacity
                          style={helperStyles.normalClearIcon}
                          onPress={handleClearInput}
                          testID="password-reset-bottom-sheet-clear-icon"
                        >
                          <Image
                            source={ASSETS.closeIcon}
                            style={helperStyles.closeIcon}
                            testID="password-reset-bottom-sheet-close-icon"
                          />
                        </TouchableOpacity>
                      )}
                  </View>
                  {passwordResetState.error && (
                    <Text
                      style={helperStyles.errorText}
                      testID="password-reset-bottom-sheet-error-text"
                    >
                      {passwordResetState.error}
                    </Text>
                  )}
                </View>
              )}

              <View
                style={helperStyles.ctaContainer}
                testID="password-reset-bottom-sheet-ctas-container"
              >
                {errorFlow?.ctas?.map((cta: CTAType, index: number) =>
                  enhancedCtaType(cta?.text, cta?.action, cta?.code, cta?.deeplink, index),
                )}
              </View>
            </>
          ) : null}
        </View>
      )}
    </View>
  );
};

export const renderIrctcProfileIncomplete = ({
  errorFlow,
  onClose,
  irctcUserName,
  config,
  ctas,
  ctaType,
}: HelperProps): React.ReactNode => {
  return (
    <View style={helperStyles.widthHundred} testID="irctc-profile-incomplete-container">
      <View
        style={helperStyles.irctcProfileContainer}
        testID="irctc-profile-incomplete-content-container"
      >
        <View style={helperStyles.titleRow} testID="irctc-profile-incomplete-title-row">
          <Text
            style={[helperStyles.mainTitle, fontStyle('latoBlack')]}
            testID="irctc-profile-incomplete-title"
          >
            {errorFlow?.title}
          </Text>
          <TouchableOpacity
            style={helperStyles.closeButton}
            onPress={() => {
              onClose(false);
            }}
            activeOpacity={0.7}
            testID="irctc-profile-incomplete-close-button"
          >
            <Image
              source={ASSETS.closeIcon}
              style={helperStyles.closeIcon}
              testID="irctc-profile-incomplete-close-icon"
            />
          </TouchableOpacity>
        </View>
        <Text
          style={[helperStyles.mainSubTitle, fontStyle('regular')]}
          testID="irctc-profile-incomplete-sub-title"
        >
          {errorFlow?.subTitle}
        </Text>
        <View style={helperStyles.userNameBox}>
          <Text
            style={[helperStyles.irctcLabel, fontStyle('bold')]}
            testID="irctc-profile-incomplete-user-name-label"
          >
            IRCTC User Name
          </Text>
          <Text
            style={[helperStyles.userName, fontStyle('black')]}
            testID="irctc-profile-incomplete-user-name"
          >
            {irctcUserName || ''}
          </Text>
        </View>
        {config?.travellerBs && (
          <View
            style={helperStyles.statusContainer}
            testID="irctc-profile-incomplete-status-container"
          >
            <View style={helperStyles.statusRow} testID="irctc-profile-incomplete-status-row">
              <Image
                source={ASSETS.greenTick}
                style={helperStyles.statusIcon}
                testID="irctc-profile-incomplete-success-icon"
              />
              <Text
                style={[helperStyles.successText, fontStyle('regular')]}
                testID="irctc-profile-incomplete-success-text"
              >
                {config?.successText?.replace('{{irctcUsername}}', irctcUserName || '')}
              </Text>
            </View>
            <View style={helperStyles.statusRow} testID="irctc-profile-incomplete-error-row">
              <Image
                source={ASSETS.errorInfoIcon}
                style={helperStyles.statusIcon}
                testID="irctc-profile-incomplete-error-icon"
              />
              <Text
                style={[helperStyles.irctcErrorText, fontStyle('regular')]}
                testID="irctc-profile-incomplete-error-text"
              >
                {config?.errorText}
              </Text>
            </View>
          </View>
        )}
        {config?.travellerBs && (
          <View
            style={[NewIrctcModalStyle.profileCompletionWrapper, helperStyles.completionSection]}
            testID="irctc-profile-incomplete-completion-wrapper"
          >
            <View style={NewIrctcModalStyle.arrowUp} />
            <View
              style={NewIrctcModalStyle.profileCompletionContainer}
              testID="irctc-profile-incomplete-completion-container"
            >
              <Text
                style={NewIrctcModalStyle.profileCompletionTitle}
                testID="irctc-profile-incomplete-completion-title"
              >
                {config?.travellerBs.title}
              </Text>
              <View
                style={NewIrctcModalStyle.profileCompletionHeader}
                testID="irctc-profile-incomplete-completion-header"
              >
                <LinearGradient
                  colors={[colors.white, colors.white, colors.lightRed4]}
                  style={NewIrctcModalStyle.profileCompletionStepsTitle}
                  start={{ x: 0, y: 1 }}
                  end={{ x: 1, y: 0 }}
                  testID="irctc-profile-incomplete-completion-steps-title"
                >
                  <Text
                    style={NewIrctcModalStyle.profileCompletionText}
                    testID="irctc-profile-incomplete-completion-steps-text"
                  >
                    {config?.travellerBs.profileCompletionTitle}
                  </Text>
                </LinearGradient>
                <View
                  style={NewIrctcModalStyle.profileStepWrapper}
                  testID="irctc-profile-incomplete-completion-steps-wrapper"
                >
                  {config?.travellerBs.profileCompletionSteps?.map(
                    (item: string, index: number) => (
                      <View
                        key={index}
                        style={[
                          NewIrctcModalStyle.profileStepContainer,
                          index === 0 && NewIrctcModalStyle.firstProfileStepContainer,
                        ]}
                        testID={`irctc-profile-incomplete-completion-steps-${index}`}
                      >
                        <View
                          style={NewIrctcModalStyle.redTickGradient}
                          testID={`irctc-profile-incomplete-completion-steps-${index}-icon`}
                        >
                          <Image
                            source={ASSETS.redTick}
                            style={{ width: 28, height: 28 }}
                            testID={`irctc-profile-incomplete-completion-steps-${index}-icon`}
                          />
                        </View>
                        <Text
                          style={NewIrctcModalStyle.profileStepText}
                          testID={`irctc-profile-incomplete-completion-steps-${index}-text`}
                        >
                          {item ?? ''}
                        </Text>
                      </View>
                    ),
                  )}
                </View>
              </View>
            </View>
          </View>
        )}
        {config?.travellerBs && (
          <View
            style={helperStyles.warningSection}
            testID="irctc-profile-incomplete-warning-section"
          >
            <Image
              source={ASSETS.updatesIcon}
              style={{ width: 16, height: 16 }}
              testID="irctc-profile-incomplete-warning-icon"
            />
            <Text
              style={[NewIrctcModalStyle.footerText, fontStyle('regular'), getLineHeight(12)]}
              testID="irctc-profile-incomplete-warning-text"
            >
              {config?.travellerBs.footerText}
            </Text>
          </View>
        )}
      </View>
      <View testID="irctc-profile-incomplete-ctas-container">
        {ctas?.map((cta: CTAType, index: number) =>
          ctaType?.(cta?.text, cta?.action, cta?.code, cta?.deeplink, index),
        )}
      </View>
    </View>
  );
};

const helperStyles = StyleSheet.create({
  widthHundred: {
    width: '100%',
  },
  subContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  titleBlock: {
    flex: 3,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    lineHeight: 24,
    color: colors.black,
    fontWeight: '700',
    marginHorizontal: 10,
    marginRight: 8,
  },
  subTitle: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.defaultTextColor,
    fontWeight: '400',
  },
  subTitleNoPadding: {
    paddingRight: 0,
  },
  titleRowWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  exclamationIconOrange: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.orange,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  exclamationTextWhite: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  radioContainer: {
    flexDirection: 'row',
    marginLeft: 0,
    marginTop: 16,
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 40,
    paddingVertical: 8,
  },
  radioText: {
    color: colors.black,
    fontSize: 16,
    marginLeft: 12,
    fontWeight: '500',
  },
  inputContainer: {
    marginTop: 12,
    flex: 1,
  },
  pnrTextInput: {
    fontFamily: 'Lato',
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 16.8,
    textAlign: 'left',
    color: colors.primary,
    height: 48,
    borderColor: colors.primary,
    borderWidth: 1,
    borderRadius: 10,
    paddingLeft: 12,
    paddingBottom: 0,
  },
  errorText: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.4,
    textAlign: 'left',
    color: colors.red,
    marginTop: 4,
  },
  closeIcon: {
    width: 24,
    height: 24,
    backgroundColor: colors.transparent,
    borderRadius: 0,
  },
  checkAvlBtnGradient: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 52,
  },
  checkAvlBtn: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.transparent,
    minHeight: 48,
  },
  mar10Bt: {
    marginBottom: 12,
  },
  clickableLink: {
    color: colors.white,
    fontFamily: 'Lato',
    fontStyle: 'normal',
    lineHeight: 22,
    fontSize: 16,
    fontWeight: '900',
    textAlign: 'center',
  },
  clickableLinkAzure: {
    color: colors.azure,
    fontFamily: 'Lato',
    fontStyle: 'normal',
    lineHeight: 20,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  mainTitle: {
    fontSize: 20,
    lineHeight: 24,
    color: colors.black,
    marginBottom: 16,
  },
  mainSubTitle: {
    fontSize: 16,
    lineHeight: 20,
    color: colors.textGrey,
    marginTop: 4,
    marginBottom: 16,
  },
  irctcProfileContainer: {
    paddingBottom: 40,
  },
  irctcLabel: {
    fontSize: 12,
    lineHeight: 12,
    fontWeight: '700',
    color: colors.grey,
    letterSpacing: 0,
    marginBottom: 4,
  },
  userNameBox: {
    backgroundColor: colors.gray12,
    borderWidth: 1,
    borderColor: colors.lightSilver,
    paddingVertical: 5.5,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 0,
    justifyContent: 'center',
    minHeight: 48,
  },
  userName: {
    fontSize: 16,
    lineHeight: 19,
    color: colors.black,
    fontWeight: '900',
  },
  statusContainer: {
    marginTop: 8,
    gap: 8,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusIcon: {
    width: 12,
    height: 12,
  },
  successText: {
    fontSize: 12,
    lineHeight: 14,
    color: colors.green,
    flex: 1,
  },
  irctcErrorText: {
    fontSize: 12,
    lineHeight: 14,
    color: colors.errorRed,
    flex: 1,
  },
  completionSection: {
    marginTop: 8,
    alignSelf: 'flex-start',
    marginLeft: 0,
  },
  warningSection: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'stretch',
    gap: 6,
    padding: 12,
    backgroundColor: colors.lightGrey,
    borderRadius: 12,
    marginTop: 16,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: 12,
  },
  closeButton: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  passwordTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  exclamationIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.orange,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  errorIcon: {
    width: 28,
    height: 28,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 1,
  },
  exclamationText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '700',
  },
  subtitleNoPadding: {
    paddingRight: 0,
  },
  description: {
    fontSize: 14,
    color: colors.mediumGrey,
    textAlign: 'left',
    marginTop: 8,
    marginBottom: 8,
    paddingHorizontal: 20,
  },
  radioContainerOverride: {
    marginTop: 2,
    marginBottom: 0,
  },
  inputWrapper: {
    position: 'relative',
  },
  successInputContainer: {
    marginTop: 8,
  },
  successInputBox: {
    paddingTop: 28,
    paddingBottom: 8,
    borderColor: colors.lightSilver,
    backgroundColor: colors.gray12,
    paddingHorizontal: 16,
    height: 56,
    justifyContent: 'center',
  },
  successInputText: {
    color: colors.black,
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 16,
    letterSpacing: 0,
    textAlignVertical: 'center',
  },
  successFloatingLabel: {
    position: 'absolute',
    top: 8,
    left: 16,
    color: colors.grey,
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 14,
    letterSpacing: 0,
    textAlignVertical: 'center',
    backgroundColor: colors.transparent,
    zIndex: 1,
  },
  ctaContainer: {
    marginTop: 26,
  },
  normalInputWrapper: {
    position: 'relative',
  },
  normalPasswordInput: {
    paddingTop: 28,
    paddingBottom: 8,
    paddingHorizontal: 16,
    height: 56,
    color: colors.primary,
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 16,
    letterSpacing: 0,
    textAlignVertical: 'center',
  },
  normalPasswordInputActive: {
    borderColor: colors.primary,
    backgroundColor: colors.lightBlue32,
  },
  normalPasswordInputInactive: {
    borderColor: colors.lightSilver,
    backgroundColor: colors.gray12,
  },
  normalPasswordInputError: {
    borderColor: colors.red,
    backgroundColor: colors.backgroundRed3,
  },
  normalPasswordInputWithClear: {
    paddingRight: 40,
  },
  normalPasswordInputNormalPadding: {
    paddingRight: 16,
  },
  normalFloatingLabel: {
    position: 'absolute',
    top: 8,
    left: 16,
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 14,
    letterSpacing: 0,
    textAlignVertical: 'center',
    backgroundColor: colors.transparent,
    zIndex: 1,
  },
  normalFloatingLabelActive: {
    color: colors.primary,
  },
  normalFloatingLabelInactive: {
    color: colors.grey,
  },
  normalFloatingLabelError: {
    color: colors.red,
  },
  normalClearIcon: {
    position: 'absolute',
    right: 12,
    top: '50%',
    transform: [{ translateY: -7 }],
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
});
