import {Platform} from 'react-native';
import fetch2 from '../../../fetch2';
import railsConfig from '../../../RailsConfig';
import isEmpty from 'lodash/isEmpty';
import toUpper from 'lodash/toUpper';
import { deviceType } from "../../../Utils/device";
import { getDeviceDetails } from '@mmt/legacy-commons/Helpers/genericHelper';

const getHeader = async (mmtAuth) => {
  const deviceContext = await getDeviceDetails();
  const deviceId = deviceContext?.deviceId;
  const headers = {
    'Content-Type': 'application/json',
    Authorization: 'h4nhc9jcgpAGIjp',
    'user-identifier': JSON.stringify({
      type: 'mmt-auth',
      deviceId: deviceId,
      os: deviceType(),
      osVersion: Platform.Version,
      appVersion: 'appVersion',
      imie: 'imie',
      ipAddress: 'ipAddress',
      timeZone: 'timeZone',
      value: mmtAuth,
    }),
  };

  return headers;
};
export const loadTravelersFromServer = async (mmtAuth) => {
  const headers = await getHeader(mmtAuth);
  const obj = {
    query: [
      [
        { name: 'extendedUser' },
        {
          name: 'associatedTravellers',
          keys: ['travellerId', 'name', 'age', 'gender', 'status', 'nationality', 'dateOfBirth'],
          filters: [
            {
              name: 'status',
              value: 'ACTIVE',
            },
          ],
        },
      ],
      [
        { name: 'extendedUser' },
        {
          name: 'associatedTravellers',
          filters: [
            {
              name: 'status',
              value: 'ACTIVE',
            },
          ],
        },
        {
          name: 'travelDocuments',
          keys: ['id', 'docType', 'docNumber'],
        },
      ],
    ],
  };

  let response = {};
  try {
    response = await fetch2(railsConfig.fetchUserDetailsURL, {
      method: 'POST',
      body: JSON.stringify(obj),
      headers,
    });
  } catch (e) {
    console.log('Error in getTravelersFromServer : ', e);
  }

  const data = await response.json();
  const {
    result: {
      extendedUser: {
        associatedTravellers,
      },
    },
  } = data;
  return associatedTravellers;
};


export const saveTravelerInServer = async (traveler, mmtAuth, addOrEditFlag) => {
  if (isEmpty(mmtAuth)) {
    return null;
  }

  const headers = await getHeader(mmtAuth);

  let travelerRequestObject = {};
  const isIndianNationality = traveler?.basicInfo?.nationality?.code === 'IN';

  if (addOrEditFlag) {
    travelerRequestObject = {
      name: traveler.basicInfo.name,
      age: parseInt(traveler.basicInfo.age, 10),
      gender: toUpper(traveler.basicInfo.gender.key),
      nationality: traveler?.basicInfo?.nationality?.name || null,
      ...(isIndianNationality
        ? {}
        : {
            dateOfBirth: traveler?.dateOfBirth ?? null,
            travelDocuments: traveler?.passportNumber
              ? [
                  {
                    docType: 'PASSPORT',
                    docNumber: traveler.passportNumber,
                  },
                ]
              : [],
          }),
    };
  } else {
    travelerRequestObject = {
      name: traveler.basicInfo.name,
      age: parseInt(traveler.basicInfo.age, 10),
      gender: toUpper(traveler.basicInfo.gender.key),
      travellerId: traveler.travelerId,
      nationality: traveler.basicInfo.nationality?.name || null,
      ...(isIndianNationality
        ? {}
        : {
            dateOfBirth: traveler.dateOfBirth ?? null,
            travelDocuments: traveler.passportNumber
              ? [
                  {
                    docType: 'PASSPORT',
                    docNumber: traveler.passportNumber,
                  },
                ]
              : [],
          }),
    };
  }
  const updateObj = {
    query: [
      [{
        name: 'extendedUser',
        values: {
          associatedTravellers: [travelerRequestObject],
        },
      }],
    ],
  };
  try {
    const response = await fetch2(railsConfig.updateUserDetailsURL, {
      method: 'POST',
      body: JSON.stringify(updateObj),
      headers,
    });
    const data = await response.json();
    const {
      result: {
        extendedUser: {
          associatedTravellers: [
            savedTraveler,
          ],
        },
      },
    } = data;
    return savedTraveler;
  } catch (e) {
    console.log('Error in saveTravelerInServer : ', e);
    return null;
  }
};

export const deleteTravelerFromServer = async (payload, mmtAuth) => {
  if (isEmpty(mmtAuth)) {
    return null;
  }
  const headers = await getHeader(mmtAuth);
  return fetch2(railsConfig.updateUserDetailsURL, {
    method: 'POST',
    body: JSON.stringify(payload),
    headers,
  });
};
