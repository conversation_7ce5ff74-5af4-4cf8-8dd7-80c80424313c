import {connect} from 'react-redux';

import TravelerDetails from '../TravelerDetails';
import {
  captureRef,
  loadTravelerPage,
  onBoardingStationClicked,
  onHardBackPress,
  onReviewClicked,
  toggleModalVisibility,
  onRefundAndCancellationClicked,
  dismissDuplicateBookingModal,
  initUserDetails,
  updateFcSelection,
  closeFcBottomSheet,
  closeTgBottomSheet,
  displayDownTimeBottomSheet,
  displayErrorBottomSheet,
  setBottomSheetErrorCode,
  clearTravelerHeaderText,
  displayAvailSubscrnBottomSheet,
  displayAvailDepletionTravelerBottomSheet,
  setBnppInteracted,
  updateAvailabilitySubscription,
  toggleInventoryDepletionBottomSheet,
  setAlert,
} from '../TravelerDetailsActions';
import {
  clearRoutedThroughReview,
  logTravellerPageBottomSheetEvents,
  logTravellerPageAddOnEvents,
  logTravellerBoardingStationChangeClick,
} from '../../Review/RailsReviewActions';
import {
  saveConfirmationGuaranteeConfig, saveFreeCancellationConfig, refreshListingPage,
} from '../../NewListing/RailsListingActions';
import { _label } from '../../../vernacular/AppLanguage';
import { RAILOFY_TYPE, getAvailabilityStatus } from '../railofyUtils';
import { trackFcBottomSheetSelection } from '../railofyOmniture';
import _isEmpty from 'lodash/isEmpty';
import { getIfCanShowBNPP } from 'apps/rails/src/RailsAbConfig';

const getLabels = () => ({
  irctcPassword: _label('irctc_password'),
  notRequested: _label('not_requested'),
  requestNewPassword: _label('request_new_password'),
  contactDetailsHeader: _label('contact_details_header'),
  contactName: _label('name'),
  contactEmailId: _label('email_id'),
  contactPhoneNumber: _label('phone_number'),
  morePreferences: _label('more_preferences'),
  reservationChoice: _label('reservation_choice'),
  edit: _label('edit'),
  station: _label('station'),
});

function shouldAddPremiumAmount(railofyType, fcUpgradeOption) {
  return railofyType === RAILOFY_TYPE.FC && fcUpgradeOption === 3;
}

const mapStateToProps = (state, ownProps) => {
  const {
    railsTraveler: {
      header,
      contactDetails,
      totalCollectibleAmount,
      showBoardingStationChange,
      selectedPickupStation,
      boardingStationList,
      selectedQuota,
      classValue,
      userNameModalVisible,
      refundAndCanChecked,
      travelAdvisoryChecked,
      totalFare,
      selectedTravelers,
      psgnDestinationAddress,
      loadingDuplicateBookingAndReview,
      showTravelAdvisorySection,
      duplicateBookingConfirmation,
      selectedBookingDate,
      railofyResponse,
      fcTgPreserveSelection,
      irctcUserNames,
      railofy,
      railofy: {
        freeCancellationData = {},
        tripGuaranteeData = {},
        fcUpgradeOption,
        premiumAmount,
        railofyType,
        bnppResponse,
        isLoading,
        isFcDiscounted,
        isTGSelected,
        isFCSelected,
      },
      displayFcBottomSheet,
      displayTgBottomSheet,
      isBnppOpted,
      displayDownTimeBottomSheet,
      errorFlow,
      availabilitySubscription: {
        showAvailSubscrnBottomSheet,
        showSubscriptionWidget,
        isAvailSubscrnEnabled,
      } = {},
      displayAvailDepletionBottomSheet,
      displayVegPreferenceBottomSheet,
      couponData = {},
      isInventoryDepletionBottomSheetVisible,
      inventoryDepletionDismissedTrains,
    },
    railsListing: {
      selectedTrainInfo,
      captureAddress,
      travelAdvisoryDetails,
      confirmationGuaranteeConfig = {},
      freeCancellationConfig = {},
      railofyZcShown,
      showBnpp,
      doCacheAtClient,
      travelplexChatConfig,
      travelplexTickers,
    },
    railsUserVerification: {irctcUserName},
    railsReview: {
      loadingReview,
      routedThroughReview,
      refreshRailofyStatusChange,
    },
  } = state;
  const fcDiscountValue = freeCancellationData?.freeCancellationPremiumDetails?.discountValue ?? 0;
  const tgDiscountValue = tripGuaranteeData?.discountValue ?? 0;
  const fcDiscountPremium =
    freeCancellationData?.freeCancellationPremiumDetails?.discountedPremium ?? 0;
  const tgDiscountPremium = tripGuaranteeData?.discountedPremium ?? 0;
  let canShowBNPP = showBnpp && getIfCanShowBNPP();
  if (canShowBNPP && !isLoading){
    canShowBNPP = !_isEmpty(bnppResponse);
  }
  const { ancillaryDiscountDetails } = couponData;
  const { status: selectedAvlStatus, isUsingUpdatedAvailability } = getAvailabilityStatus(railofy, selectedBookingDate);
  return ({
    header,
    loadingReview,
    contactDetails,
    selectedTrainInfo: selectedTrainInfo || {},
    totalCollectibleAmount,
    totalFare,
    selectedTravelers,
    showBoardingStationChange,
    selectedPickupStation,
    boardingStationList,
    irctcUserName,
    selectedQuota,
    classValue,
    userNameModalVisible,
    loadingDuplicateBookingAndReview,
    refundAndCanChecked,
    availabilityObject: selectedBookingDate,
    selectedAvlStatus,
    isUsingUpdatedAvailability,
    captureAddress,
    psgnDestinationAddress,
    travelAdvisoryDetails,
    travelAdvisoryChecked,
    showTravelAdvisorySection,
    duplicateBookingConfirmation,
    labels: getLabels(),
    confirmationGuaranteeConfig,
    freeCancellationConfig,
    railofyZcShown,
    railofyResponse,
    routedThroughReview,
    refreshRailofyStatusChange,
    fcTgPreserveSelection,
    bookNowFare: shouldAddPremiumAmount(railofyType, fcUpgradeOption) ? (totalFare + premiumAmount) : totalFare,
    freeCancellationData,
    displayFcBottomSheet,
    displayTgBottomSheet,
    displayDownTimeBottomSheet,
    errorFlow,
    canShowBNPP,
    doCacheAtClient,
    isBnppOpted,
    bnppResponse,
    railofy,
    tgDiscountPremium,
    fcDiscountPremium,
    tgDiscountValue,
    fcDiscountValue,
    isFcDiscounted,
    showAvailSubscrnBottomSheet,
    showSubscriptionWidget,
    isAvailSubscrnEnabled,
    displayAvailDepletionBottomSheet,
    displayVegPreferenceBottomSheet,
    irctcUserNames,
    ancillaryDiscountDetails,
    isTGSelected,
    isFCSelected,
    travelplexChatConfig,
    travelplexTickers,
    isInventoryDepletionBottomSheetVisible,
    inventoryDepletionDismissedTrains,
    ...ownProps,
  });
};


const mapDisptachToProps = dispatch => ({
  toggleModalVisibility: () => { dispatch(toggleModalVisibility()); },
  loadTravelerPage: () => dispatch(loadTravelerPage()),
  onHardBackPress: () => dispatch(onHardBackPress()),
  onReviewClicked: (psgnDestinationAddress, skipDuplicateCheck, freeCancellation, assuredConfirmation) =>
    dispatch(onReviewClicked(psgnDestinationAddress, skipDuplicateCheck, freeCancellation, assuredConfirmation)),
  onBoardingStationClicked: station => dispatch(onBoardingStationClicked(station)),
  captureRef: (componentName, myScrollRef) => dispatch(captureRef(componentName, myScrollRef)),
  onRefundAndCancellationClicked: () => dispatch(onRefundAndCancellationClicked()),
  dismissDuplicateBookingModal: () => dispatch(dismissDuplicateBookingModal()),
  saveConfirmationGuaranteeOptionOnTraveller: (railsConfirmationGuaranteeOption) =>
    dispatch(saveConfirmationGuaranteeConfig(railsConfirmationGuaranteeOption)),
  saveFreeCancellationOptionOnTraveller: (freeCancellationEnabled) =>
    dispatch(saveFreeCancellationConfig(freeCancellationEnabled)),
  refreshListingPage: () => dispatch(refreshListingPage()),
  initUserDetails : () => dispatch(initUserDetails()),
  clearRoutedThroughReview : () => dispatch(clearRoutedThroughReview),
  updateFcSelection: (index, premiumAmount = 0 ) => {
    trackFcBottomSheetSelection(index);
    dispatch(updateFcSelection(index, premiumAmount));
  },
  closeFcBottomSheet: () => dispatch(closeFcBottomSheet()),
  closeTgBottomSheet: () => dispatch(closeTgBottomSheet()),
  displayDownTimeBottomSheetFunction: (val) => dispatch(displayDownTimeBottomSheet(val)),
  displayErrorBottomSheetFunction: (val) => dispatch(displayErrorBottomSheet(val)),
  setBottomSheetErrorCode: (errorCode) => dispatch(setBottomSheetErrorCode(errorCode)),
  clearTravelerHeaderText: () => dispatch(clearTravelerHeaderText()),
  displayAvailSubscrnBottomSheet: (val) => dispatch(displayAvailSubscrnBottomSheet(val)),
  displayAvailDepletionTravelerBottomSheet: (val) =>
    dispatch(displayAvailDepletionTravelerBottomSheet(val)),
  setBnppInteracted: (val) => dispatch(setBnppInteracted(val)),
  updateAvailabilitySubscription: () => dispatch(updateAvailabilitySubscription()),
  logTravellerPageBottomSheetEvents: (eventValue) =>
    dispatch(logTravellerPageBottomSheetEvents(eventValue)),
  logTravellerPageAddOnEvents: (railofyValuesTrackingParams, eventValue) =>
    dispatch(logTravellerPageAddOnEvents(railofyValuesTrackingParams, eventValue)),
  logTravellerBoardingStationChangeClick: (eventValue) =>
    dispatch(logTravellerBoardingStationChangeClick(eventValue)),
  toggleInventoryDepletionBottomSheet: (isVisible, trainNumber) =>
    dispatch(toggleInventoryDepletionBottomSheet(isVisible, trainNumber)),
  setAlert: () => dispatch(setAlert()),
});

export default connect(mapStateToProps, mapDisptachToProps)(TravelerDetails);
