import {connect} from 'react-redux';
import RailsOffers from '../Components/RailsOffers';
import {
  setCouponData,
  addCouponToRecommendedList,
  couponsApplied,
  couponsRemoved,
} from '../TravelerDetailsActions';

const mapStateToProps = (state, ownProps) => {
  const {
    railsLanding: {cmp},
    railsTraveler: {
      couponData,
      recommendedCoupons,
      selectedQuota,
      classValue,
      totalCollectibleAmount,
      selectedTravelers,
      travelers,
      totalFare,
      baseFare,
      railofy: {
        railofyType,
        defaultPremiumAmount,
        freeCancellationData,
        tripGuaranteeData,
        isFCSelected,
        isTGSelected,
      },
    },
    railsVernacular:{texts},
    railsListing: {
      selectedTrainInfo,
      childBerthMandatory,
      seniorCitizenApplicable,
    },
  } = state;

  const fcDiscountPremium = freeCancellationData?.cancellationOptions[0]?.insuranceAmount ?? 0;
  const tgDiscountPremium = tripGuaranteeData?.preferenceInfoRevamped[0]?.premiumPerPerson ?? 0;

  const couponDataForAncillary = {
    fcDetails: {
      premium: fcDiscountPremium,
      opted: isFCSelected ?? false,
    },
    tgDetails: {
      premium: tgDiscountPremium,
      opted: isTGSelected ?? false,
    },
  };

  return {
    ...ownProps,
    couponData,
    recommendedCoupons,
    selectedQuota,
    classValue,
    selectedTrainInfo,
    totalCollectibleAmount,
    totalFare,
    childBerthMandatory,
    seniorCitizenApplicable,
    selectedTravelers,
    travelers,
    cmp,
    texts,
    baseFare,
    fcTgAvailableType: railofyType,
    fcTgPrice: defaultPremiumAmount,
    couponDataForAncillary,
  };
};

const mapDispatchToProps = (dispatch) => ({
  setCouponData: (couponData) => {
    if (couponData?.ancillaryDiscountDetails?.ancillaryType) {
      dispatch(couponsApplied(couponData?.ancillaryDiscountDetails?.ancillaryType));
    }
    dispatch(setCouponData(couponData));
  },
  couponsRemoved: (ancillaryType) => {
    dispatch(couponsRemoved(ancillaryType));
  },
  addCouponToRecommendedList: (couponData) => { dispatch(addCouponToRecommendedList(couponData)); },
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsOffers);
