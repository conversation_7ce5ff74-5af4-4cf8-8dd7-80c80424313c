import React, { useState, useEffect } from 'react';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { Text, StyleSheet } from 'react-native';
import { ZERO_MINS } from '@mmt/rails/src/Utils/RailsConstant';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import PropTypes from 'prop-types';

const TimerApp = ({ timerSeconds }) => {
  const [seconds, setSeconds] = useState(0);

  useEffect(() => {
    setSeconds(timerSeconds);
  }, [timerSeconds]);

  useEffect(() => {
    // Function to update the timer every second
    const updateTimer = setInterval(() => {
      setSeconds((prevSeconds) => prevSeconds - 1);
    }, 1000);

    // Cleanup function to clear the interval when the component is unmounted
    return () => clearInterval(updateTimer);
  }, []); // Empty dependency array ensures that the effect runs only once when the component mounts

  const formatTime = (timeInSeconds) => {
    if (timeInSeconds <= 0) {
      return ZERO_MINS;
    }
    const minutes = Math.floor(timeInSeconds / 60);
    const remainingSeconds = timeInSeconds % 60;
    return ` ${String(minutes).padStart(2, '0')} mins ${String(remainingSeconds).padStart(
      2,
      '0',
    )} seconds`;
  };

  return (
    <Text style={[styles.timerText, fontStyle('black'), { color: colors.black }]}>
      {formatTime(seconds)}
    </Text>
  );
};

TimerApp.propTypes = {
  timerSeconds: PropTypes.number,
};

const styles = StyleSheet.create({
  timerText: {
    fontSize: 16,
    lineHeight: 16,
    textAlign: 'center',
    marginBottom: 10,
    color: colors.defaultTextColor,
  },
});

export default TimerApp;
