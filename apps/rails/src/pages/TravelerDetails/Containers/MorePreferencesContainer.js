import {connect} from 'react-redux';
import MorePreferences from '../Components/MorePreferences';
import {
  onConfirmBookCheckboxClicked,
  onConsiderAutoUpgradationClicked,
  onRefundAndCancellationClicked,
  onTravelInsuranceCBClicked,
} from '../TravelerDetailsActions';
import { logTravellerGenericClickEvents } from '../../Review/RailsReviewActions';

const mapStateToProps = (state, ownprops) => {
  const {
    railsTraveler: {
      refundAndCanChecked, considerAutoUpgradation, confirmBookCheckbox,
    },
  } = state;

  return ({
    travelerCount: ownprops.travelerCount,
    refundAndCanChecked,
    considerAutoUpgradation,
    confirmBookCheckbox,
  });
};
const mapDispatchToProps = dispatch => ({
  onTravelInsuranceCBClicked: () => dispatch(onTravelInsuranceCBClicked()),
  onRefundAndCancellationClicked: () => dispatch(onRefundAndCancellationClicked()),
  onConsiderAutoUpgradationClicked: () => dispatch(onConsiderAutoUpgradationClicked()),
  onConfirmBookCheckboxClicked: () => dispatch(onConfirmBookCheckboxClicked()),
  logTravellerGenericClickEvents: (eventValue) =>
    dispatch(logTravellerGenericClickEvents(eventValue)),
});
export default connect(mapStateToProps, mapDispatchToProps)(MorePreferences);
