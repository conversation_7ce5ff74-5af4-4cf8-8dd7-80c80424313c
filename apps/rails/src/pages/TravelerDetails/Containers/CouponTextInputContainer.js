import {connect} from 'react-redux';
import CouponTextInput from '../Components/CouponTextInput';

const mapStateToProps = (state, ownProps) => {
  const {
    railsLanding: {
      cmp,
    },
    railsTraveler: {
      selectedQuota,
      classValue,
      totalCollectibleAmount,
      selectedTravelers,
      travelers,
      totalFare,
      baseFare,
      railofy: {
        railofyType,
        defaultPremiumAmount,
        freeCancellationData,
        tripGuaranteeData,
        isFCSelected,
        isTGSelected,
      },
    },
    railsListing: {
      selectedTrainInfo,
      childBerthMandatory,
      seniorCitizenApplicable,
    },
  } = state;

  const fcDiscountPremium = freeCancellationData?.cancellationOptions[0]?.insuranceAmount ?? 0;
  const tgDiscountPremium = tripGuaranteeData?.preferenceInfoRevamped[0]?.premiumPerPerson ?? 0;

  const couponDataForAncillary = {
    fcDetails: {
      premium: fcDiscountPremium,
      opted: isFCSelected ?? false,
    },
    tgDetails: {
      premium: tgDiscountPremium,
      opted: isTGSelected ?? false,
    },
  };

  return {
    ...ownProps,
    selectedQuota,
    classValue,
    selectedTrainInfo,
    totalCollectibleAmount,
    totalFare,
    baseFare,
    childBerthMandatory,
    seniorCitizenApplicable,
    totalFare,
    baseFare,
    selectedTravelers,
    travelers,
    cmp,
    fcTgAvailableType: railofyType,
    fcTgPrice: defaultPremiumAmount,
    couponDataForAncillary,
    isFCSelected,
    isTGSelected,
  };
};

export default connect(mapStateToProps, null)(CouponTextInput);
