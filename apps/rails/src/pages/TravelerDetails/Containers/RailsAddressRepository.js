import {setDataToAsyncStorage,getDataFromAsynStorage} from '../../../Utils/RailsConstant';

export const getAddressForDtnCode = async (code) => {
  const storedAddress = await getDataFromAsynStorage(`ADDRESS_${code}`);
  if (storedAddress) {
    return storedAddress;
  }
  return {};
};

export const setAddressForDtnCode = async (code, address) => {
  try {
    await setDataToAsyncStorage(`ADDRESS_${code}`, address);
  } catch (e) {
    console.log('error', e);
  }
};
