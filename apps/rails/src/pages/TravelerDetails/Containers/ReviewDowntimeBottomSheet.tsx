import React, { useEffect } from 'react';
import { View, StyleSheet, Text, Image, Platform } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import Timer from './Timer';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import { fontStyle } from '../../../vernacular/VernacularUtils';
import ASSETS from '../../../Utils/Assets/RailsAssets';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import { getTimeDifference } from '@mmt/rails/src/Utils/RisUtils';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

import cancel from '@mmt/legacy-assets/src/rails/cross_icon.webp';
import PropTypes from 'prop-types';

const ReviewDowntimeBottomSheet = ({ onClose, logTravellerPageBottomSheetEvents }) => {
  useEffect(() => {
    if (logTravellerPageBottomSheetEvents) {
      logTravellerPageBottomSheetEvents(TRAVELLER_CLICK_EVENTS.REVIEW_DOWNTIME_BOTTOM_SHEET);
    }
  }, [logTravellerPageBottomSheetEvents]);
  return (
    <View style={styles.container}>
      <Image style={styles.errorImg} source={ASSETS.maintenanceError} />
      <TouchableRipple style={styles.cancelButton} onPress={() => onClose(false)}>
        <Image source={cancel} style={Platform.OS === 'android' && styles.cancelButton} />
      </TouchableRipple>
      <Text style={[styles.title, fontStyle('bold')]}>{_label('irctc_error')}</Text>
      <Text style={styles.errorInfo}>
        {_label('back_in')}
        <Timer timerSeconds={getTimeDifference()} />
      </Text>
      <Text style={styles.errorInfoSubTitle}>
        {_label('please_come_back')}
        <Text style={[fontStyle('bold'), { color: colors.defaultTextColor }]}> 12:20 AM{'\n'}</Text>
        <Text>{_label('apology')}</Text>
      </Text>
      <TouchableRipple onPress={() => onClose(false)}>
        <Text style={[styles.clickableLink, fontStyle('black')]}>{_label('okay_got_it')}</Text>
      </TouchableRipple>
    </View>
  );
};
ReviewDowntimeBottomSheet.propTypes = {
  onClose: PropTypes.func,
  logTravellerPageBottomSheetEvents: PropTypes.func,
};

export default ReviewDowntimeBottomSheet;

const styles = StyleSheet.create({
  errorImg: {
    height: 155,
    width: 155,
    marginBottom: 40,
  },
  title: {
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '700',
    fontSize: 20,
    marginBottom: 16,
    lineHeight: 22,
    color: colors.black,
  },
  errorInfo: {
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 16,
    textAlign: 'center',
    marginBottom: 10,
    color: colors.defaultTextColor,
  },
  errorInfoSubTitle: {
    fontFamily: 'Lato',
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 20,
    color: colors.black,
  },
  clickableLink: {
    color: colors.azure,
    fontFamily: 'Lato',
    fontStyle: 'normal',
    lineHeight: 22,
    fontSize: 16,
    fontWeight: '700',
  },
  container: {
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    overflow: 'hidden',
    marginBottom: 10,
  },
  cancelButton: {
    height: 45,
    width: 45,
    position: 'absolute',
    top: 10,
    left: '95%',
  },
});
