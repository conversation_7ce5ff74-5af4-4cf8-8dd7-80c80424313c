import {connect} from 'react-redux';
import slice from 'lodash/slice';
import RailsTravelersList from '../Components/RailsTravelersList';
import {
  captureRef,
  onAddTraveler,
  onEditTraveler,
  onTravelerSelected,
  onViewAllClicked,
} from '../TravelerDetailsActions';

export const MIN_TRAVELER_TO_SHOW = 3;


const mapStateToProps = (state, ownProps) => {
  const {
    railsTraveler:
      {
        viewAllTravelers, selectedTravelers = [], warning, showViewAll,
      },
    railsListing: {
      childBerthMandatory,
      seniorCitizenApplicable,
    },
    railsListing,
  } = state;
  let {railsTraveler: {travelers = []}} = state;

  travelers = viewAllTravelers
    ? travelers
    : selectedTravelers.length > MIN_TRAVELER_TO_SHOW
    ? slice(travelers, 0, selectedTravelers.length)
    : slice(travelers, 0, MIN_TRAVELER_TO_SHOW);

  return ({
    travelers,
    viewAllTravelers,
    selectedTravelers,
    warning,
    showViewAll,
    childBerthMandatory,
    seniorCitizenApplicable,
    railsListing,
    ...ownProps,
  });
};

const mapDispatchToProps = dispatch => ({
  onViewAllClicked: () => dispatch(onViewAllClicked()),
  onTravelerSelected: traveler => dispatch(onTravelerSelected(traveler)),
  onAddTraveler: () => dispatch(onAddTraveler()),
  onEditTraveler: traveler => dispatch(onEditTraveler(traveler)),
  captureRef: (componentName, myRef) => dispatch(captureRef(componentName, myRef)),
});


export default connect(mapStateToProps, mapDispatchToProps)(RailsTravelersList);
