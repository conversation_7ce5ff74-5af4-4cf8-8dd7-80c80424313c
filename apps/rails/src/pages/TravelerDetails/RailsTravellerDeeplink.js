import React from 'react';
import isEmpty from 'lodash/isEmpty';
import {connect} from 'react-redux';
import {
  removeFCKey,
} from '../../Utils/RailsConstant';
import {
  getPokusConfigWaitingPromise,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TravelerDetails from './Containers/TravelerDetailsContainer';
import {setDataForTravellers} from './TravelerDetailsActions';
import fecha from 'fecha';
import PropTypes from 'prop-types';

class RailsTravellerDeeplink extends React.Component {
  async UNSAFE_componentWillMount() {
    const { departure, deep_link_intent_url, deeplink} = this.props;
    try {
      await getPokusConfigWaitingPromise();
    } catch (e) {
    }
    if (deeplink || deep_link_intent_url?.includes('railsTravellerDeeplink')) {
      await removeFCKey();
    }
    if (departure && deep_link_intent_url || deeplink) {
      const departureDate = fecha.parse(departure, 'YYYYMMDD');
      const deeplinkData = {
        ...this.props,
        date: fecha.format(departureDate, 'YYYYMMDD'),
        departureDate: fecha.format(departureDate, 'YYYYMMDD'),
      };
      this.props.setDataForTravellers(deeplinkData);
    } else {
      this.props.setDataForTravellers(this.props);
    }
  }
  render() {
    if (this.props.loading) {
      return (
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <Spinner size={30} color={colors.azure} />
        </View>
      );
    }
    return <TravelerDetails postPaymentBooking pnrNumber={this.props.pnrNumber} {...this.props} />;
  }
}

RailsTravellerDeeplink.propTypes = {
  departure: PropTypes.string,
  deep_link_intent_url: PropTypes.string,
  deeplink: PropTypes.bool,
  setDataForTravellers: PropTypes.func,
  loading: PropTypes.bool,
  pnrNumber: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
};

const mapStateToProps = (state, ownProps) => {
  const {railsListing: {selectedTrainInfo}} = state;
  const loading = isEmpty(selectedTrainInfo);
  return {
    loading,
    ...ownProps,
  };
};

const mapDispatchToProps = dispatch => ({
  setDataForTravellers: (props) => { dispatch(setDataForTravellers(props)); },
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsTravellerDeeplink);
