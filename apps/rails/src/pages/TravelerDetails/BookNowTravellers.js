import React from 'react';
import PropTypes from 'prop-types';
import { StyleSheet, Text, View, Platform } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from 'packages/legacy-commons/Styles/globalStyles';
import TouchableRipple from 'packages/legacy-commons/Common/Components/TouchableRipple';
import { fontStyle, getLineHeight } from '../../vernacular/VernacularUtils';
import AvailDepletionStrip from '../NewListing/Components/AvailDepletion/AvailDepletionStrip';

const enabledColors = [colors.lightBlue, colors.darkBlue];
const disabledColors = ['#989898', '#989898'];
export const appendRupeeSymbol = amount => (`₹ ${amount}`);


class BookNowTravellers extends React.Component {
  render() {
    const {
      onBookNowClicked,
      showLoader,
      buttonText,
      BookNowButtonEnabled,
      payNowContainerStyle,
      hideShadow,
      canShowAvailDepletStrip = false,
    } = this.props;
    const btnColor = showLoader ? { color: colors.transparent } : { color: colors.white };
    return (
      <>
        {canShowAvailDepletStrip && <AvailDepletionStrip />}
        <LinearGradient
          style={hideShadow ? {} : styles.gradientContainer}
          colors={['rgba(180,180,180,84)', 'rgba(255,255,255,84)']}
          start={{ x: 0.0, y: 1.0 }}
          end={{ x: 0.0, y: 0.0 }}
          testID="book_now_travelers_gradient_background"
        />
        <View
          style={[styles.payNowsection, payNowContainerStyle]}
          testID="book_now_travelers_container"
        >
          <TouchableRipple
            onPress={onBookNowClicked}
            feedbackColor={colors.transparent}
            style={{ width: '100%' }}
            testID="book_now_travelers_touchable"
          >
            <LinearGradient
              style={[styles.linearGrad, this.props.buttonContainer]}
              colors={BookNowButtonEnabled ? enabledColors : disabledColors}
              start={{
                x: 0,
                y: 2,
              }}
              end={{
                x: 1,
                y: 1,
              }}
              testID="book_now_travelers_gradient"
            >
              <View testID="book_now_travelers_button">
                <View style={styles.buttonBlue}>
                  <Text
                    style={[
                      btnColor,
                      styles.buttonTextStyle,
                      fontStyle('black'),
                      getLineHeight(16),
                    ]}
                  >
                    {buttonText}
                  </Text>
                </View>
              </View>
              {showLoader && (
                <View
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    zIndex: 50,
                  }}
                  testID="book_now_travelers_loader_overlay"
                >
                  <Spinner
                    size={30}
                    color={colors.white}
                    testID="book_now_travelers_loader_spinner"
                  />
                </View>
              )}
            </LinearGradient>
          </TouchableRipple>
        </View>
      </>
    );
  }
}

BookNowTravellers.propTypes = {
    onBookNowClicked: PropTypes.func.isRequired,
    buttonText: PropTypes.string.isRequired,
    BookNowButtonEnabled: PropTypes.bool,
    id: PropTypes.string,
    showLoader: PropTypes.bool,
    payNowContainerStyle: PropTypes.object,
    hideShadow: PropTypes.bool,
    canShowAvailDepletStrip: PropTypes.bool,
    buttonContainer: PropTypes.object,
};

BookNowTravellers.defaultProps = {
    perSeatText: null,
    BookNowButtonEnabled: true,
};

const styles = StyleSheet.create({
    textContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },

    buttonText: {
        color: colors.white,
        fontSize: 16,
        minWidth: 125,
        textAlign: 'center',
    },
    buttonBlue: {
        alignItems: 'center',
        justifyContent: 'center',
        height: 44,
        borderBottomRightRadius: 100,
        borderTopRightRadius: 100,
    },

    payNowsection: {
        backgroundColor: colors.white,
        justifyContent: 'center',
        paddingHorizontal: 16,
        alignItems: 'center',
        paddingTop: 12,
        paddingBottom: 16,
    },
    linearGrad: {
        width: '100%',
        borderRadius: 8,
        alignItems: 'center',
        paddingHorizontal: 44,
    },
    buttonTextStyle: {
        fontSize: 16,
        paddingTop: 9.5,
        paddingBottom: 11.5,

    },
    gradientContainer: {
        position: 'absolute',
        bottom: Platform.select({ android: 60, ios: 45 }),
        width: '100%',
        height: 18,
    },
});


export default BookNowTravellers;
