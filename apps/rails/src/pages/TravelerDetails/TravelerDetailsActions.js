import _keyBy from 'lodash/keyBy';
import isEmpty from 'lodash/isEmpty';
import filter from 'lodash/filter';
import compact from 'lodash/compact';
import isNull from 'lodash/isNull';
import find from 'lodash/find';
import get from 'lodash/get';
import { Platform, Vibration } from 'react-native';
import { COUPON_TYPE } from '@mmt/rails/src/pages/TravelerDetails/railofyUtils';
import { setDataInStorage, removeDataFromStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import {
  AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES,
  YEAR_MONTH_DATE_FORMAT,
  irctcAccountUserNameAPI,
  MULTI_SELECT,
  RAILS_FC_TG_SOCIAL_PROOFING,
  IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS,
  AVAILABILITY_ALERT_SET_SUCCESSFULLY,
  AVAILABILITY_ALERT_REMOVED_SUCCESSFULLY,
  AVAILABILITY_DEPLETION_TRACKING_DETAILS,
} from '@mmt/rails/src/Utils/RailsConstant';
import { logTravellersPageLoadPdtEvent } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import findIndex from 'lodash/findIndex';
import { Actions } from '../../navigation';
import fecha from 'fecha';
import {
  TGS_Traveller_Container,
  TGS_Tickets_Container,
} from '../RIS/PnrStatus/TGSUtils/Constants';
import { getCommonHeaders } from '@mmt/legacy-commons/Helpers/genericHelper';
import {
  getCommonPdtData, getCommonSearchContextData,
} from '../../PdtAnalytics/PdtHelper/RailsPdtUtils.js';
import {
  saveContactDetails,
} from '@mmt/legacy-commons/Common/Components/ContactDetails/contactDetailsUtils';
import { getUserDetails, isUserLoggedIn, shouldShowGSTNWidget} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import ReservationChoiceType, {
  getRsChoiceByValue,
  ReservationChoiceValuesDefault,
  ReservationChoiceValuesWithoutLowerBerth,
} from '../Constants/ReservationChoiceType';
import {
  getTravelersListFromId,
  loadRailsTravelers,
  loadSelectedTravelersFromCache,
  sortTravelersBySelection,
  storeRailsSelectedTravelersInCache,
  storeRailsTravelersInCache,
  updateTravelerInServer,
} from './Containers/RailsTravelerRepository';
import {
  berthValidation,
  convertDateFormat,
  getInfantCount,
  getMaxTravelersAllowedForBooking,
  getMaxTravelerWarningText,
  getNameObj,
  getSearchKey,
  highlightTravelerForError,
  isAgeAndGenderSpecified,
  isInfant,
  isTravelerSelected,
  showBerthMismatchWarning,
  showBoardingStationChangeOption,
  stringCompare,
  generateOrUpdateIteneraryId,
  shouldShowVegPreference,
} from './TravelerDetailsUtils';
import { showShortToast, showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { getClassType } from '../Types/ClassType';
import railsConfig from '../../RailsConfig';
import {
  DATE_MONTH_YEAR_FORMAT,
  DOUBLE_DIGIT_DAY_WITH_MONTH_FORMAT,
  getDataFromAsynStorage,
  isIRCTCTatkalTime,
  MAX_INFANT_ALLOWED,
  RAILS_IRCTC_SHOW_PASSWORD_REMINDER_SCREEN,
  RAILS_IS_PRE_FC_SELECTED,
  setDataToAsyncStorage,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  RAILS_ERROR_RETRY_COUNT,
  TRAVELLER_PDT_ADDON_DETAILS,
} from '../../Utils/RailsConstant';
import { getHeaderTitleForTravelersAndReview } from '../../Utils/HeaderContentUtil';
import { assuredConfirmationConfigurationApi, getBoardingStation } from '../../Utils/RailsAPIRepository';
import { getMealPreferenceOptions1 } from '../Constants/MealNew';
import { getBerthPreferenceOptions } from '../Constants/Berth';
import { getNationalityOptions } from '../Constants/Country';
import { getSrCitizenOptions } from '../Constants/SrCitizenConcession';
import Traveler from './Components/Traveler';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import QuotaType, { isTatkalQuota } from '../Types/QuotaType';
import {
  trackTravellerPageEvent,
  trackTravellerSeatInfo,
  trackGenericEvar47or97Event,
  removeEventFromEvar47or97Variable,
  updateEvar47or97Variable,
  travelerLoadRemoveEvar47or97Events,
  updateEvar99Variable,
  trackGenericEvar99Event,
  removeEventFromEvar99Variable,
} from '../../railsAnalytics';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import RAIL_EVENTS, { FCTG_TOGETHER } from '../../RailsOmnitureTracker';
import {
  updateUsername,
  updateVerificationFlow,
  updateConfigKeyResponseForAadharVerification,
} from '../User/UserVerification/UserVerificationActions';
import {
  fetchBookingReview,
  getRailsLandingData,
  setAlertApiSuccess,
} from '../Review/RailsReviewActions';
import {
  ACTION_RAILS_LISTING_COMPLETE,
  ACTION_RAILS_LISTING_INIT_STATE,
  ACTION_SET_DATA_FOR_TRAVELERS_PAGE,
  getAvailabilityResponse,
  getAvlDayList,
  getDefaultAvlStatus,
  getDefaultPostHeaders,
  getDepartureDateAndTime,
  getOrderedQuotaList,
  getQuotaClassRate,
  isBookNowEnabled,
  setLandingData,
  trackEvents,
} from '../NewListing/RailsListingActions';
import fetch2 from '../../fetch2';
import { addMinutes } from '../NewListing/Utils/RailListingUtils';
import RailsPdtTravellersHelper from '../../PdtAnalytics/PdtHelper/RailsPdtTravellersHelper';
import { deleteTravelerFromServer } from './Containers/RailsUserServiceRepository';
import { botmanSensorHeader } from '@mmt/legacy-commons/Native/NetworkModule';
import { getTrainType } from '../Types/TrainTypes';
import { getAddressForDtnCode } from './Containers/RailsAddressRepository';
import { parseValidateUserResponse, STATUS_API_SUCCESS, STATUS_USER_PROFILE_INCOMPLETE } from '../../Utils/UserRepository';
import { skywalkerSearchConnector } from '../../Skywalker/connector';
import { _label } from '../../vernacular/AppLanguage';
import { bookMode, channelCode, isMweb } from '../../Utils/device';
import { RAILOFY_TYPE, getTgType } from './railofyUtils';
import {
  getIfToShowFCTGTogetherPrompt,
  getRailsAvailDepletion,
  getRailsAvailabilitySubscription,
  getRailsTGVersion,
  getTravelerFreeCancellationBottomSheet,
  getTravellerPdtConfig,
  showSocialProofingFc,
  showSocialProofingTg,
  RailsAbConfig,
  railsCouponExperiment,
  showProfileIncompleteFlowPokus,
  getRailsFcTgTogetherValue,
} from '../../RailsAbConfig';
import { gstinSubmit } from '@mmt/legacy-commons/Common/Components/GSTIN';
import { setFreeCancellationOption } from '../RailsLandingPage/Store/RailsLandingPageActions';
import { isValidGstNumber } from 'packages/legacy-commons/Helpers/validationHelpers';
import { uploadGrafanaMetrics } from '../../Utils/RailsGrafanaTracker';
import { trackClickEventProp61 } from '../RailsBusHomePage/Analytics';
import { USER_CONSENT_ERROR } from '@mmt/legacy-commons/Common/Components/GSTIN/Helpers/constants';

export const ACTION_ON_AVAIL_SUBSCRN = '@rails/ACTION_ON_AVAIL_SUBSCRN';
export const SET_ALERT_LOADER = '@rails/SET_ALERT_LOADER';
export const ACTION_ON_CONTACT_DETAILS_EDITED = '@rails/ACTION_ON_CONTACT_DETAILS_EDITED';
export const ACTION_ON_LOAD_TRAVELER_PAGE = '@rails/ACTION_ON_LOAD_TRAVELER_PAGE';
export const ACTION_ON_RESERVATION_CHOICE_CLICKED = '@rails/ACTION_ON_RESERVATION_CHOICE_CLICKED';
export const ACTION_USER_DETAILS = '@rails/ACTION_USER_DETAILS';
export const ACTION_ON_VIEW_ALL_CLICKED = '@rails/ACTION_ON_VIEW_ALL_CLICKED';
export const ACTION_ON_TRAVELER_SELECT = '@rails/ACTION_ON_TRAVELER_SELECT';
export const ACTION_SHOW_TRAVELER_COUNT_EXCEED_WARNING =
  '@rails/ACTION_SHOW_TRAVELER_COUNT_EXCEED_WARNING';
export const ACTION_ON_MAX_TRAVELER_SELECTION_LIMIT_EXCEEDED =
  '@rails/ACTION_ON_MAX_TRAVELER_SELECTION_LIMIT_EXCEEDED';
export const ACTION_ON_MAX_TRAVELER_SELECTION_IN_RANGE =
  '@rails/ACTION_ON_MAX_TRAVELER_SELECTION_IN_RANGE';
export const ACTION_ON_INACTIVE_TRAVELER_SELECTED = '@rails/ACTION_ON_INACTIVE_TRAVELER_SELECTED';
export const ACTION_ON_MALE_SELECTED_IN_LADIES_QUOTA =
  '@rails/ACTION_ON_MALE_SELECTED_IN_LADIES_QUOTA';
export const ACTION_SET_TRAVEL_INSURANCE = '@rails/ACTION_SET_TRAVEL_INSURANCE';
export const ACTION_ON_REFUND_AND_CANCELLATION_CLICKED =
  '@rails/ACTION_ON_REFUND_AND_CANCELLATION_CLICKED';
export const ACTION_ON_TRAVEL_ADVISORY_CLICKED = '@rails/ACTION_ON_TRAVEL_ADVISORY_CLICKED';
export const ACTION_ON_CONSIDER_AUTO_UPGRADATION_CLICKED =
  '@rails/ACTION_ON_CONSIDER_AUTO_UPGRADATION_CLICKED';
export const ACTION_ON_BERTH_VALIDATION_FAILURE = '@rails/ACTION_ON_BERTH_VALIDATION_FAILURE';
export const ACTION_ON_BERTH_VALIDATION_SUCCESS = '@rails/ACTION_ON_BERTH_VALIDATION_SUCCESS';
export const ACTION_ON_BERTH_VALIDATION = '@rails/ACTION_ON_BERTH_VALIDATION';
export const ACTION_ON_BERTH_MISMATCH_FOUND = '@rails/ACTION_ON_BERTH_MISMATCH_FOUND';
export const ACTION_INIT_STATE = '@rails/ACTION_INIT_STATE';
export const ACTION_DEFAULT_ACTION = '@rails/ACTION_DEFAULT_ACTION';
export const ACTION_SET_TRAVELER_HEADER = '@rails/ACTION_SET_TRAVELER_HEADER';
export const ACTION_LOAD_BOARDING_STATION = '@rails/ACTION_LOAD_BOARDING_STATION';
export const ACTION_ON_BOARDING_STATION_SELECTED = '@rails/ACTION_ON_BOARDING_STATION_SELECTED';
export const ACTION_LOAD_PREFERENCES = '@rails/ACTION_LOAD_PREFERENCES';
export const ACTION_ON_ADD_EDIT_TRAVELER = '@rails/ACTION_ON_ADD_EDIT_TRAVELER';
export const ACTION_INFANT_COUNT_EXCEEDED = '@rails/ACTION_INFANT_COUNT_EXCEEDED';
export const ACTION_ON_CONTACT_DETAILS_VALIDATION_FAILURE =
  '@rails/ACTION_ON_CONTACT_DETAILS_VALIDATION_FAILURE';
export const ACTION_ON_CAPTURE_REF = '@rails/ACTION_ON_CAPTURE_REF';
export const ACTION_SET_SHOW_PWD_REMINDER_SCREEN = '@rails/ACTION_SET_SHOW_PWD_REMINDER_SCREEN';
export const ACTION_TOGGLE_MODAL_VISIBILITY = '@rails/ACTION_TOGGLE_MODAL_VISIBILITY';
export const ACTION_SET_COUPON_DATA = '@rails/ACTION_SET_COUPON_DATA';
export const ACTION_REMOVE_COUPON_DATA = '@rails/ACTION_REMOVE_COUPON_DATA';
export const ACTION_ON_CONFIRM_BOOK_CHECKBOX_CLICKED =
  '@rails/ACTION_ON_CONFIRM_BOOK_CHECKBOX_CLICKED';
export const ACTION_DELETE_TRAVELLER = '@rails/ACTION_DELETE_TRAVELLER';
export const ACTION_CLEAR_SELECTED_TRAVELRS = '@rails/ACTION_CLEAR_SELECTED_TRAVELRS';
export const ACTION_ADD_RECOMMENDED_COUPONS = 'ACTION_ADD_RECOMMENDED_COUPONS';
export const ACTION_ADD_COUPON_DATA = 'ACTION_ADD_COUPON_DATA';
export const ACTION_ADD_DESTINATION_ADDRESS = 'ACTION_ADD_DESTINATION_ADDRESS';
export const ACTION_SHOW_DUPLICATE_CONFIRMATION = '@rails/ACTION_SHOW_DUPLICATE_CONFIRMATION';
export const ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW = '@rails/ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW';
export const ACTION_SHOW_IRCTC_USERNAME_ERROR = '@rails/ACTION_SHOW_IRCTC_USERNAME_ERROR';
export const ACTIONS_OPTED_FOR_FREE_CANCELLATION = '@rails/ACTIONS_OPTED_FOR_FREE_CANCELLATION';
export const ACTIONS_RAILS_ASSURED_CONFIRMATION = '@rails/ACTIONS_RAILS_ASSURED_CONFIRMATION'; // railofy
export const ACTIONS_ADDITIONAL_ASSURED_CONFIRMATION_DETAILS = '@rails/ADDITIONAL_ASSURED_CONFIRMATION_DETAILS';
export const CLEAR_RAILOFY_ZC_OPTED_DETAILS = '@rails/CLEAR_CNFM_DETAILS_ZC_OPTED';
export const ACTION_SAVE_RAILOFY_RESPONSE = '@rails/SAVE_ASSURED_CONFIRMATION_RESPONSE';
export const ACTION_CLEAR_RAILOFY_RESPONSE = '@rails/CLAER_ASSURED_CONFIRMATION_RESPONSE';
export const ACTION_FETCH_RAILOFY_DATA_START = '@rails/ACTION_FETCH_RAILOFY_DATA_START';
export const ACTION_FETCH_RAILOFY_DATA_SUCCESS = '@rails/ACTION_FETCH_RAILOFY_DATA_SUCCESS';
export const ACTION_FETCH_RAILOFY_DATA_FAILED = '@rails/ACTION_FETCH_RAILOFY_DATA_FAILED';
export const ACTION_UPDATE_RAILOFY_USER_RESPONSE = '@rails/ACTION_UPDATE_RAILOFY_USER_RESPONSE';
export const ACTION_SHOW_RAILOFY_WIDGET_ERROR = '@rails/ACTION_SHOW_RAILOFY_WIDGET_ERROR';
export const ACTION_CLEAR_OLD_RAILOFY_DATA = '@rails/ACTION_CLEAR_OLD_RAILOFY_DATA';
export const ACTION_FREE_CANCELLATION_ALERT = '@rails/ACTION_FREE_CANCELLATION_ALERT';
export const ACTION_DOWNTIME_BOTTOMSHEET = '@rails/ACTION_DOWNTIME_BOTTOMSHEET';
export const ACTION_BOOKING_REVIEW_ERROR_FLOW = '@rails/ACTION_BOOKING_REVIEW_ERROR_FLOW';
export const ACTION_BOOKING_REVIEW_ERROR_FLOW_CODE = '@rails/ACTION_BOOKING_REVIEW_ERROR_FLOW_CODE';
export const ACTION_TG_ALERT = '@rails/ACTION_TG_ALERT';
export const ACTION_UPDATE_FC_TG_USER_RESPONSE = '@rails/ACTION_UPDATE_FC_TG_USER_RESPONSE';
export const ACTION_BNPP_SET_SELECTION = '@rails/ACTION_BNPP_SET_SELECTION';
export const SET_BNPP_INTERACTED = '@rails/SET_BNPP_INTERACTED';
export const UPDATE_OPTIONAL_GST_DETAILS = 'UPDATE_OPTIONAL_GST_DETAILS';
export const ACTION_REVIEW_AVAILABILITY_CHANGE = '@rails/ACTION_REVIEW_AVAILABILITY_CHANGE';
export const ACTION_IRCTC_USERNAMES_LIST = '@rails/ACTION_IRCTC_USERNAMES_LIST';
export const ACTION_DISPLAY_AVAIL_DEPLETION_TRAVELER = '@rails/ACTION_DISPLAY_AVAIL_DEPLETION_TRAVELER';
export const ACTION_TOGGLE_INVENTORY_DEPLETION_BOTTOMSHEET = '@rails/ACTION_TOGGLE_INVENTORY_DEPLETION_BOTTOMSHEET'
export const ACTION_ON_MAX_TRAVELER_IN_SR_CITIZEN_QUOTA_EXCEEDED =
  '@rails/ACTION_ON_MAX_TRAVELER_IN_SR_CITIZEN_QUOTA_EXCEEDED';
export const ACTION_ON_MAX_TRAVELER_IN_SR_CITIZEN_IN_RANGE =
  '@rails/ACTION_ON_MAX_TRAVELER_IN_SR_CITIZEN_IN_RANGE';

export const ACTION_SET_RAILS_GUEST_SELECTED_TRAVELERS_LIST = '@rails/ACTION_SET_RAILS_GUEST_SELECTED_TRAVELERS_LIST';
export const ACTION_SET_RAILS_GUEST_TRAVELERS_LIST = '@rails/ACTION_SET_RAILS_GUEST_TRAVELERS_LIST';

export const ACTION_SET_RAILS_LOGGED_IN_SELECTED_TRAVELERS_LIST = '@rails/ACTION_SET_RAILS_LOGGED_IN_SELECTED_TRAVELERS_LIST';
export const ACTION_SET_RAILS_LOGGED_IN_TRAVELERS_LIST = '@rails/ACTION_SET_RAILS_LOGGED_IN_TRAVELERS_LIST';
export const FC_TG_BOTTOMSHEET_CTA_CLICKED = '@rails/FC_TG_BOTTOMSHEET_CTA_CLICKED';
export const ACTION_INIT_SCHEDULE_TATKAL_TRAVELER = '@rails/ACTION_INIT_SCHEDULE_TATKAL_TRAVELER';
export const ACTION_SET_IRCTC_PROFILE_STATUS = '@rails/ACTION_SET_IRCTC_PROFILE_STATUS';
export const ACTION_TRAVELER_NAME_LESS_THAN_THREE_CHARS='@rails/ACTION_TRAVELER_NAME_LESS_THAN_THREE_CHARS';
export const ACTION_SET_VEG_PREFERENCE = '@rails/ACTION_SET_VEG_PREFERENCE';

export const COMPONENT_TRAVELER = 'traveler';
export const COMPONENT_CONTACT_CARD = 'contact';
export const COMPONENT_TRAVELER_PAGE_SCROLL_VIEW = 'scrollView';
export const COMPONENT_INSURANCE = 'insurance';
export const COMPONENT_DEST_ADDRESSLINE1 = 'destination_address_line1';
export const COMPONENT_DEST_PINCODE = 'destination_pincode';
export const COMPONENT_DEST_POSTOFFICE = 'destination_postoffice';
export const COMPONENT_TRAVEL_ADVISORY = 'travel_advisory';
export const COMPONENT_IRCTC_USERNAME = 'irctc_username';
export const FREE_CANCELLATION = 'FREE_CANCELLATION';
export const ASSURED_CONFIRMATION = 'ASSURED_CONFIRMATION'; // // railofy
export const DUMMY_COMPONENT = 'dummy_component';

let _scrollPos = 0;
export let refs = [];
let _refs = [];

const initWarning = (selectedTravelerList, maxTravelersAllowed, selectedQuota) => {
  const warning = {};
  warning.showChildWithoutBerthWarning =
    selectedTravelerList.length > 0 && berthValidation(selectedTravelerList);

  // last screen GN quota , 6 pax selected, current screen TQ quota
  switch (selectedQuota.id) {
    case 'TQ':
      warning.showMaxTravelerWarningForTatkal = selectedTravelerList.length > maxTravelersAllowed;
      break;
    case 'SS':
      warning.showMaxSrCitizenTravelerWarning = selectedTravelerList.length > maxTravelersAllowed;
      break;
  }
  warning.showIrctcTatkalTimeWarning = isTatkalQuota(selectedQuota) && isIRCTCTatkalTime();
  return warning;
};

export const onBoardingStationClicked = (station) => {
  trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_BOARDING_POINT_CHANGED);
  return {
    type: ACTION_ON_BOARDING_STATION_SELECTED,
    data: {
      selectedPickupStation: station.obj,
    },
  };
};
/* =====Start : Reservation Choice===== */

export const onReservationChoiceClicked = (selectedReservationChoice) => {
  if (selectedReservationChoice.id === '1') {
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_SAME_COACH_SELECTED);
  } else if (selectedReservationChoice.id === '2') {
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDPREF_ONE_LOWER_BERTH);
  } else if (selectedReservationChoice.id === '3') {
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADDPREF_TWO_LOWER_BERTH);
  }
  return {
    type: ACTION_ON_RESERVATION_CHOICE_CLICKED,
    data: {
      selectedReservationChoice: getRsChoiceByValue(selectedReservationChoice.id),
    },
  };
};
export const loadReservationChoice = (applicableBerthTypes) => {
  try {
    for (let i = 0; i < applicableBerthTypes.length; i++) {
      if (applicableBerthTypes[i] === 'LB') {
        return ReservationChoiceValuesDefault;
      }
    }
    return ReservationChoiceValuesWithoutLowerBerth;
  } catch (e) {
    console.log('Error in loadReservationChoice : ', e);
  }
};

/* =====End : Reservation Choice===== */

export const loadUserDetails = async (isLoggedIn) => {
  if (!isLoggedIn) {
    return null;
  }
  return getUserDetails();
};

export const onViewAllClicked = () => (dispatch, getState) => {
  const { railsTraveler: { viewAllTravelers } = {} } = getState();
  dispatch({
    type: ACTION_ON_VIEW_ALL_CLICKED,
    data: { viewAllTravelers: !viewAllTravelers },
  });
};

const validateOnSelect = (railsTraveler, traveler) => {
  const { selectedTravelers, maxTravelersAllowed, travelers } = railsTraveler;
  const isSelected = isTravelerSelected(selectedTravelers, traveler);
  const selTravCount = isSelected ? selectedTravelers.length : selectedTravelers.length + 1;
  const infantCount = getInfantCount(selectedTravelers, travelers);
  const characterCountLesserThanThree = traveler?.fullName?.length < 3;

  if (characterCountLesserThanThree) {
    return ACTION_TRAVELER_NAME_LESS_THAN_THREE_CHARS;
  }
  if (!isAgeAndGenderSpecified(traveler)) {
    return ACTION_ON_INACTIVE_TRAVELER_SELECTED;
  } else if (selTravCount > maxTravelersAllowed) {
    return ACTION_SHOW_TRAVELER_COUNT_EXCEED_WARNING;
  } else if (
    !isSelected &&
    isInfant(traveler.basicInfo.age) &&
    infantCount === MAX_INFANT_ALLOWED
  ) {
    return ACTION_INFANT_COUNT_EXCEEDED;
  } else if (!isSelected && traveler.isMaleTravelingInLadiesQuota) {
    showShortToast(_label('ladies_quota_male_age'));
    return ACTION_ON_INACTIVE_TRAVELER_SELECTED;
  } else if (traveler.isMaleTravelingInLadiesQuota) {
    return ACTION_ON_TRAVELER_SELECT;
  } else if (traveler.isNotSrCitizenTravellingInSrCitizenQuota) {
    if (!isSelected) {
      showShortToast(_label('not_sr_citizen_travelling_in_sr_citizen_quota_warning'));
      return ACTION_ON_INACTIVE_TRAVELER_SELECTED;
    }
    return ACTION_ON_TRAVELER_SELECT;
  } else if (!isSelected && traveler.isberthMismatch) {
    return ACTION_ON_BERTH_MISMATCH_FOUND;
  }
  return ACTION_DEFAULT_ACTION;
};

const validateAfterSelect = (railsTraveler, selectedTravelers) => {
  const { travelers, maxTravelersAllowed, warning } = railsTraveler;
  const selectedTravelersList = getTravelersListFromId(selectedTravelers, travelers);

  if (selectedTravelers.length > 0 && berthValidation(selectedTravelersList)) {
    return ACTION_ON_BERTH_VALIDATION_FAILURE;
  } else if (
    warning.showChildWithoutBerthWarning &&
    (selectedTravelers.length === 0 || !berthValidation(selectedTravelersList))
  ) {
    return ACTION_ON_BERTH_VALIDATION_SUCCESS;
  } else if (selectedTravelers.length > maxTravelersAllowed) {
    return ACTION_ON_MAX_TRAVELER_SELECTION_LIMIT_EXCEEDED;
  } else if (selectedTravelers.length <= maxTravelersAllowed) {
    return ACTION_ON_MAX_TRAVELER_SELECTION_IN_RANGE;
  }
  return ACTION_DEFAULT_ACTION;
};

export const onPreferenceChangedtoVeg = () => async (dispatch, getState) => {
  const { railsTraveler } = getState();
  const { selectedTravelers = [], travelers = [] } = railsTraveler;
  const displayVegPreference = shouldShowVegPreference(selectedTravelers, travelers);
  dispatch(setPreferenceToVeg(displayVegPreference));
};

// On Travelleer selected function
export const onTravelerSelected = (traveler) => async (dispatch, getState) => {
  const { railsTraveler } = getState();
  let {
    railsTraveler: {
      selectedTravelers = [],
      warning,
      selectedQuota,
      travelers = [],
      isLoggedIn,
    } = {},
  } = getState();
  if (!warning.showMaxTravelerWarningForTatkal && !warning.showMaxSrCitizenTravelerWarning) {
    const actionToDispatchOnSelect = validateOnSelect(railsTraveler, traveler);

    let newTraveler = null;
    switch (actionToDispatchOnSelect) {
      case ACTION_TRAVELER_NAME_LESS_THAN_THREE_CHARS: {
        showShortToast(_label('name_character_limit_msg'));
        return;
      }
      case ACTION_ON_INACTIVE_TRAVELER_SELECTED: {
        // Onselect only
        newTraveler = highlightTravelerForError(traveler, true);
        dispatch({
          type: ACTION_ON_INACTIVE_TRAVELER_SELECTED,
          data: {
            traveler: newTraveler,
          },
        });
        return;
      }
      case ACTION_SHOW_TRAVELER_COUNT_EXCEED_WARNING: {
        if (selectedQuota.id === 'SS') {
          showShortToast(_label('sr_citizen_max_traveller'));
        } else {
          showShortToast(getMaxTravelerWarningText(railsTraveler));
        }
        return;
      }
      case ACTION_ON_MALE_SELECTED_IN_LADIES_QUOTA: {
        showShortToast(_label('only_female_passengers'));
        return;
      }
      case ACTION_ON_BERTH_MISMATCH_FOUND: {
        newTraveler = showBerthMismatchWarning(traveler);
        dispatch({
          type: ACTION_ON_BERTH_MISMATCH_FOUND,
          data: {
            traveler: newTraveler,
          },
        });
        break;
      }
      case ACTION_INFANT_COUNT_EXCEEDED: {
        showShortToast(_label('max_infants_allowed', undefined, { count: MAX_INFANT_ALLOWED }));
        return;
      }
      default: {
        newTraveler = null;
      }
    }
  }
  const isSelected = isTravelerSelected(selectedTravelers, traveler);
  if (isSelected) {
    selectedTravelers = filter(selectedTravelers, (value) => value !== traveler.travelerId);
  } else {
    selectedTravelers = [...selectedTravelers, traveler.travelerId];
  }

  const actionToDispatchAfterSelect = validateAfterSelect(
    railsTraveler,
    selectedTravelers,
    selectedQuota,
  );
  switch (actionToDispatchAfterSelect) {
    case ACTION_ON_BERTH_VALIDATION_FAILURE: {
      warning.showChildWithoutBerthWarning = true;
      break;
    }
    case ACTION_ON_BERTH_VALIDATION_SUCCESS: {
      warning.showChildWithoutBerthWarning = false;
      break;
    }
    case ACTION_ON_MAX_TRAVELER_SELECTION_LIMIT_EXCEEDED: {
      if (selectedQuota.id === 'SS') {
        warning.showMaxSrCitizenTravelerWarning = true;
      } else {
      warning.showMaxTravelerWarningForTatkal = true;
      }
      break;
    }
    case ACTION_ON_MAX_TRAVELER_SELECTION_IN_RANGE: {
      if (selectedQuota.id === 'SS') {
        warning.showMaxSrCitizenTravelerWarning = false;
      } else {
      warning.showMaxTravelerWarningForTatkal = false;
      }
      break;
    }
    default: {
      warning.showMaxTravelerWarningForTatkal = true;
      break;
    }
  }
  await dispatch({
    type: ACTION_ON_TRAVELER_SELECT,
    data: {
      selectedTravelers,
      warning,
    },
  });
  updateLocalStorage(isLoggedIn, travelers, selectedTravelers, dispatch);

  if (selectedTravelers && selectedTravelers.length < 2) {
    dispatch({
      type: ACTION_ON_RESERVATION_CHOICE_CLICKED,
      data: {
        selectedReservationChoice: ReservationChoiceType.None,
      },
    });
  }
  await dispatch(onPreferenceChangedtoVeg());
  await dispatch(reApplyCoupon);
};


export const setAlert = () => async (dispatch, getState) => {
  try {
    dispatch(setAlertLoader(true));
    const {
      railsTraveler: { selectedQuota, classValue, userDetails } = {},
      railsListing: {
        selectedTrainInfo: { frmStnCode, toStnCode, departureDateAndTime, trainNumber } = {},
      } = {},
    } = getState();
    const payload = {
      srcStation: frmStnCode,
      dstnStation: toStnCode,
      departureDate: fecha.format(departureDateAndTime, YEAR_MONTH_DATE_FORMAT),
      trainNumber,
      journeyClass: classValue?.code,
      quota: selectedQuota?.code,
      subscriptionType: 'ARP_BOOKING',
    };
    const mmtAuth = get(userDetails, 'mmtAuth', null);

    const res = await fetch2(railsConfig.setAlert, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mmt-auth': mmtAuth,
      },
      body: JSON.stringify(payload),
    });
    const data = await res.json();
    showShortToast(data?.textDetails?.title || 'Something went wrong', 'bottom', 'light');
    dispatch(setAlertApiSuccess(true));
    dispatch(setAlertLoader(false));
  } catch (error) {
    showShortToast(error?.errorMessage || 'Something went wrong');
    dispatch(setAlertLoader(false));
  }
};

export const reApplyCoupon = async () => {
  // console.log('@buddy reapply coupon called');
  // const {railsTraveler: {couponData}} = getState();
  // console.log('@buddy reapply coupon called 2');
  // let couponText = null;
  // if (!couponData || !couponData.couponCode || couponData.status !== 'success') {
  //   return;
  // }
  // else {
  //   couponText = couponData.couponCode;
  // }
  //  dispatch({
  //   type: ACTION_REMOVE_COUPON_DATA,
  //   data: {}
  // });
  //
  // const {
  //   railsTraveler:
  //     {
  //       selectedQuota,
  //       classValue,
  //       totalCollectibleAmount,
  //       selectedTravelers,
  //       travelers
  //     },
  //   railsListing: {
  //     selectedTrainInfo, childBerthMandatory,
  //     seniorCitizenApplicable
  //   },
  //   railsVernacular: {texts}
  // } = getState();
  //
  // const passengerList = getTrvListForCouponApply(selectedTravelers, travelers, selectedQuota, childBerthMandatory, seniorCitizenApplicable);
  // const response = await validateCoupon(couponText, selectedQuota, classValue, selectedTrainInfo, totalCollectibleAmount, passengerList);
  // const error_label = texts.coupon_removed_as_no_longer_applicable || 'Coupon removed as it is no longer applicable';
  // if (!response || response.errorDetails || response.status !== 'success') {
  //   showShortToast(error_label);
  //   trackTravellerPageEvent('mob_rail_travellers_coupon_invalid');
  //   return;
  // }
  // dispatch({
  //   type: ACTION_SET_COUPON_DATA,
  //   data: response
  // });
  // trackTravellerPageEvent('mob_rail_travellers_coupon_success');
};

export const getTravelerHeaderText = () => (disptach, getState) => {
  const {
    railsTraveler: { selectedBookingDate },
    railsListing: { selectedTrainInfo },
  } = getState();

  const titleText = getHeaderTitleForTravelersAndReview(selectedTrainInfo);
  let subtitleText = '';
  try {
    subtitleText = convertDateFormat(
      selectedBookingDate.availablityDate,
      DATE_MONTH_YEAR_FORMAT,
      DOUBLE_DIGIT_DAY_WITH_MONTH_FORMAT,
    );
  } catch (e) {
    console.log('Error in getTravelerHeaderText', e);
  }
  disptach({
    type: ACTION_SET_TRAVELER_HEADER,
    data: {
      header: titleText.concat(' | ').concat(subtitleText),
    },
  });
};

const initState = (
  applicableFoodTypes,
  seniorCitizenApplicable,
  applicableBerthTypes,
  foodChoiceEnabled,
  selectedQuota,
  classValue,
  totalCollectibleAmount,
  showPwdReminder,
  foodDetails,
  totalFare,
  travelerInsuranceOpted,
  originStation,
  destinationStation,
  selectedBookingDate,
  baseFare,
  originalSelectedBookingDate,
) => ({
  type: ACTION_INIT_STATE,
  data: {
    applicableFoodTypes,
    seniorCitizenApplicable,
    applicableBerthTypes,
    foodChoiceEnabled: stringCompare(foodChoiceEnabled, 'true'),
    foodDetails,
    selectedQuota,
    classValue,
    totalCollectibleAmount: parseFloat(totalCollectibleAmount),
    showPwdReminder,
    selectedPickupStation: null,
    couponData: undefined,
    totalFare: parseFloat(totalFare),
    baseFare: parseFloat(baseFare),
    travelerInsuranceOpted,
    originStation,
    destinationStation,
    selectedBookingDate,
    originalSelectedBookingDate,
    isBnppOpted: false,
    reviewAvailablityChange: false,
    bottomSheetCtaClicked: false,
  },
});
const getInitializedContactDetails = (userDetails, contactDetailsBeforeUpdate) => {
  let contactDetails = { name: '', email: '', mobile: '', hasCompleteFields: false, isInternationalNumber: false };
  try {
    if (contactDetailsBeforeUpdate) {
      contactDetails.name = contactDetailsBeforeUpdate.name || contactDetails.name;
      contactDetails.email = contactDetailsBeforeUpdate.email || contactDetails.email;
      contactDetails.mobile = contactDetailsBeforeUpdate.mobile || contactDetails.mobile;
      contactDetails.hasCompleteFields =
          contactDetailsBeforeUpdate.hasCompleteFields || contactDetails.hasCompleteFields;
      contactDetails.isInternationalNumber =
          contactDetailsBeforeUpdate.isInternationalNumber || contactDetails.isInternationalNumber;
    }
    if (userDetails) {
      if (userDetails.mobile && userDetails.mobile.mobileNumber) {
        contactDetails.mobile = userDetails.mobile.mobileNumber;
      }
      if (userDetails.email) {
        contactDetails.email = userDetails.email;
      }
      contactDetails.name = compact([userDetails.firstName, userDetails.lastName]).join(' ');
      if (userDetails.mobile && userDetails.mobile.countryCode && Number(userDetails.mobile.countryCode) !== 91)
      {
        contactDetails.isInternationalNumber = true;
      }
    }
    contactDetails.hasCompleteFields = !(isEmpty(contactDetails.email) || isEmpty(contactDetails.mobile));
    return contactDetails;
  } catch (e) {
    console.log('error', e);
    return contactDetails;
  }
};


export const initUserDetails = () => async (dispatch,getState) => {
  try {

    // write the clean logic to copy userDetails.mobile.mobileNumber , userDetails.email , userDetails.mobile.countryCode
    const isLoggedIn = await isUserLoggedIn();
    const userDetails = await loadUserDetails(isLoggedIn);
    const {railsTraveler} = getState();
    let contactDetailsBeforeUpdate =
          (railsTraveler && railsTraveler.contactDetails) ? (railsTraveler.contactDetails) : null;
    let contactDetails = getInitializedContactDetails(userDetails, contactDetailsBeforeUpdate);
    dispatch({
      type: ACTION_USER_DETAILS,
      data: {
        isLoggedIn,
        userDetails,
        contactDetails,
      },
    });
  } catch (e) {
    console.log('error', e);
  }
};


const loadBoardingStation = () => async (dispatch, getState) => {
  let classValue = null;
  let boardingStationList = [];
  let showBoardingStationChange = false;
  let selectedPickupStation = null;
  try {
    const {
      railsListing: { selectedClassType, selectedAvlStatus, selectedTrainInfo },
    } = getState();

    classValue = getClassType(selectedClassType);
    showBoardingStationChange = showBoardingStationChangeOption(selectedAvlStatus);

    if (showBoardingStationChange) {
      boardingStationList = await getBoardingStation(selectedTrainInfo, classValue);
      selectedPickupStation = boardingStationList
        ? selectedTrainInfo.boardingStation
          ? boardingStationList.find(
              (station) => station.stationCode === selectedTrainInfo.boardingStation.code,
            )
          : boardingStationList[0]
        : null;
    }
  } catch (e) {
    console.log('Error in loadBoardingStation :', e);
  }
  dispatch({
    type: ACTION_LOAD_BOARDING_STATION,
    data: {
      classValue,
      showBoardingStationChange,
      boardingStationList,
      selectedPickupStation,
    },
  });
};

const setListingAvailabilityData = async (landingData, classType, trainInfo, quotaCode) => {
  const { departureDate } = landingData;
  const { avlClasses } = trainInfo;
  const response = await getAvailabilityResponse(classType, trainInfo, quotaCode, departureDate,null,null,false);
  if (!isEmpty(response.errorMessage)) {
    throw new Error(_label('something_went_wrong'));
  }

  const { avlDayList, originalAvlDayList } = getAvlDayList(response);
  const formattedDate = fecha.format(departureDate, 'DD-MM-YYYY');
  const selectedAvlStatus = getDefaultAvlStatus(avlDayList, formattedDate);
  const selectedBookingDate = avlDayList[0];
  const {
    insuranceCharge,
    totalCollectibleAmount,
    totalFare,
    baseFare,
    travelAdvisoryDetails,
  } = response;
  if (!isBookNowEnabled(selectedBookingDate)) {
    throw new Error(_label('something_went_wrong'));
  }
  return {
    selectedTrainInfo: trainInfo,
    selectedClassType: classType,
    allAvailableClasses: avlClasses,
    selectedBookingDate,
    selectedAvlStatus,
    avlDayList,
    ...response.bkgCfg,
    insuranceCharge,
    totalCollectibleAmount,
    totalFare,
    baseFare,
    displayStationChangeAlert: false,
    travelAdvisoryDetails,
    originalSelectedBookingDate: originalAvlDayList[0],
    displayConfirmOptionsBottomSheet: false,
  };
};

export const getTrainsListForDeeplink = async (from, to, date) => {
  try {
    const fromCode = from.code;
    const toCode = to.code;
    const deptDate = fecha.format(date, 'YYYYMMDD');
    const searchUrl = `${railsConfig.searchUrlNewListing}/${fromCode}/${toCode}/${deptDate}`;
    const header = await botmanSensorHeader();
    const res = await fetch2(searchUrl, {
      headers: header,
    });
    if (res.status === 200) {
      const responseJson = await res.json();
      if (isEmpty(responseJson)) {
        throw new Error('response json is null');
      }
      if (!isEmpty(responseJson.errorMessage) || !isEmpty(responseJson.error)) {
        throw new Error(_label('something_went_wrong'));
      } else {
        if (isEmpty(responseJson.trainBtwnStnsList)) {
          throw new Error('no trains found');
        }
        const trainsList = responseJson.trainBtwnStnsList.map((train) => {
          const departureDateAndTime = getDepartureDateAndTime(train, date);
          const arrivalDateAndTime = addMinutes(departureDateAndTime, train.duration);
          return {
            ...train,
            departureDateAndTime,
            arrivalDateAndTime,
          };
        });
        const { quotaList } = responseJson;
        const trainsListObject = trainsList.reduce((acc, item) => {
          const { avlClasses } = item;
          const selectedQuota = QuotaType.GN;
          const quotaClassRate = getQuotaClassRate(quotaList, avlClasses);
          return {
            ...acc,
            [item.trainNumber]: {
              selectedQuota,
              quotaClassRate,
              selectedClassType: avlClasses[0],
              selectedDate: null,
              error: false,
              errorMessage: '',
              availabilityResponse: null,
              isLoading: false,
            },
          };
        }, {});

        const availableQuotaList = getOrderedQuotaList(responseJson.quotaList);
        const clientConfig = isEmpty(responseJson.clientConfig)
          ? null
          : responseJson.clientConfig;
        return {
          searchData: trainsList,
          availableQuotaList,
          trainsListObject,
          selectedTrainInfo: null,
          showClassAvailability: false,
          clientConfig,
        };
      }
    }
  } catch (e) {
    console.log('error is 1', e);
    throw new Error(_label('something_went_wrong') + ' Error Code: ' + e?.errorCode );
  }
};

const setListingDataForTravellers = async (landingData, props) => {
  let trainInfo;
  const { originStation, destinationStation, departureDate } = landingData;
  const { trainNumber, classCode: classType, quota: quotaCode, alternate } = props;
  const trainsListData = await getTrainsListForDeeplink(
    originStation,
    destinationStation,
    departureDate,
  );

  trainInfo = trainsListData.searchData.find((item) => item.trainNumber === trainNumber);
  if (alternate) {
    const boardingStation = { code: props.boardingStnCode, name: props.boardingStnName };
    const droppingStation = { code: props.destStnCode, name: props.destStnName };
    trainInfo = {
      ...trainInfo,
      boardingStation,
      droppingStation,
    };
  }
  const listingData = await setListingAvailabilityData(
    landingData,
    classType,
    trainInfo,
    quotaCode,
  );
  return {
    trainsListData,
    listingData,
  };
};

export const setDataForTravellers = (props) => async (dispatch) => {
  try {
    const { from, to, date, pageSource } = props;
    const departureDate = fecha.parse(date, 'YYYYMMDD');
    const landingData = await getRailsLandingData(from, to, departureDate);
    removeDataFromStorage(TRAVELLER_PDT_ADDON_DETAILS);
    dispatch(setLandingData(landingData));
    const { originStation, destinationStation } = landingData;
    dispatch({
      type: ACTION_RAILS_LISTING_INIT_STATE,
      data: {
        originStation,
        destinationStation,
        departureDate,
        pageSource,
      },
    });
    const { trainsListData, listingData } = await setListingDataForTravellers(landingData, props);
    dispatch({
      type: ACTION_RAILS_LISTING_COMPLETE,
      data: trainsListData,
    });
    dispatch({
      type: ACTION_SET_DATA_FOR_TRAVELERS_PAGE,
      data: listingData,
    });
    if (props.alternate) {
      trackTravellerPageEvent('mob_rail_travellers_nearby_fromRIS');
    }
    dispatch(trackEvents);
  } catch (e) {
    if (props.pageSource === 'CT_BUS') {
      Actions.pop();
      return;
    }
    Actions.rails({
      type: 'replace',
      postPaymentBooking: true,
    });
    showShortToast(_label('something_went_wrong_retry') + ' Error Code: ' + e?.errorCode );
    console.log('error is 2', e);
  }
};

let _previousTravelersPageLink = null;
export const reviewAvailablityChange = () => {
  return {
    type: ACTION_REVIEW_AVAILABILITY_CHANGE,
    data: { reviewAvailablityChange: true },
  };
};

export const updateIrctcUserNames = (updatedListOfUsernames) => async (dispatch) => {
  dispatch({
    type: ACTION_IRCTC_USERNAMES_LIST,
    data: updatedListOfUsernames,
  });
};

export const loadTravelerPage = () => async (dispatch, getState) => {
  try {
    if (isMweb()) {
      if (_previousTravelersPageLink === window.location.href) {
        return;
      }
      _previousTravelersPageLink = window.location.href;
    }
    _scrollPos = 0;
    refs = [];
    await setDataToAsyncStorage(RAILS_ERROR_RETRY_COUNT, 0);
    const {
      railsListing: {
        applicableFoodTypes,
        seniorCitizenApplicable,
        applicableBerthTypes,
        foodChoiceEnabled,
        selectedQuota,
        totalCollectibleAmount,
        selectedClassType,
        selectedTrainInfo,
        foodDetails,
        totalFare,
        baseFare,
        departureDate,
      },
      railsUserVerification: { irctcUserName },
    } = getState();
    travelerLoadRemoveEvar47or97Events();
    removeEventFromEvar99Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_ERROR_HANDLING_BS);
    updateEvar99Variable(`${IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_PROFILE_INCOMPLETE_FLOW_EVAR99_EVENT}${showProfileIncompleteFlowPokus()}`);
    let { originStation, destinationStation,
          selectedBookingDate, originalSelectedBookingDate } = getState().railsListing;
    await generateOrUpdateIteneraryId(originStation.code, destinationStation.code, departureDate);
    const {railsLanding} = getState();
    const searchContextData = getCommonSearchContextData(railsLanding);
    const data = {
      selectedTrainInfo,
      selectedBookingDate,
      selectedQuota,
      selectedClassType,
      totalCollectibleAmount,
      ...searchContextData,
    };
    const classValue = getClassType(selectedClassType);
    let showPwdReminder = false; // await getDataFromStorage(RAILS_IRCTC_SHOW_PASSWORD_REMINDER_SCREEN);
    if (isNull(showPwdReminder)) {
      showPwdReminder = true;
    }

    const travelerInsuranceOpted = 1;
    dispatch(
      initState(
        applicableFoodTypes,
        seniorCitizenApplicable,
        applicableBerthTypes,
        foodChoiceEnabled,
        selectedQuota,
        classValue,
        totalCollectibleAmount,
        showPwdReminder,
        foodDetails,
        totalFare,
        travelerInsuranceOpted,
        originStation,
        destinationStation,
        selectedBookingDate,
        baseFare,
        originalSelectedBookingDate,
      ),
    );
    dispatch(showHideIRCTCUsernameError(false));
    dispatch(loadBoardingStation());
    const pdtConfig = await getTravellerPdtConfig();
    if (!pdtConfig) {
      trackPdtTravellersLoad(data);
    }
    if (!irctcUserName) {
      const userName = await irctcAccountUserNameAPI();
      const isValidUserName = await dispatch(isValidIrctcUserName(userName));
      if (isValidUserName) {
        dispatch(updateUsername(userName));
      }
    } else if (irctcUserName) {
      const isValidUserName = await dispatch(isValidIrctcUserName(irctcUserName));
      if (!isValidUserName) {
        dispatch(updateUsername(null));
      }
    }
    await dispatch(initUserDetails());

    const { railsTraveler } = getState();
    const { psgnDestinationAddress } = railsTraveler;

    // check for session
    const reservationChoice = loadReservationChoice(applicableBerthTypes || []);
    const maxTravelersAllowed = getMaxTravelersAllowedForBooking(selectedQuota);
    const newSearchKey = getSearchKey(originStation, destinationStation);
    let selectedTravelers = [];
    let travelers = [];
    let selectedTravelerList = [];
    if (!isEmpty(newSearchKey) && stringCompare(railsTraveler.searchKey, newSearchKey)) {
      selectedTravelers = loadSelectedTravelersFromCache(railsTraveler, getState);
    } else {
      selectedTravelers = [];
      storeRailsSelectedTravelersInCache(railsTraveler.isLoggedIn, [], dispatch);
    }
    const travelersList = await loadRailsTravelers(railsTraveler, getState, dispatch);

    selectedTravelerList = getTravelersListFromId(selectedTravelers, travelersList);
    travelers = sortTravelersBySelection(travelersList, selectedTravelerList);
    const address = await getAddressForDtnCode(selectedTrainInfo.toStnCode);
    const newaddress = { ...psgnDestinationAddress, ...address };

    const warning = initWarning(selectedTravelerList, maxTravelersAllowed, selectedQuota);


    dispatch({
      type: ACTION_ON_LOAD_TRAVELER_PAGE,
      data: {
        reservationChoice,
        maxTravelersAllowed,
        travelers,
        selectedTravelers,
        warning,
        viewAllTravelers: false,
        travelerInsuranceOpted,
        refundAndCanChecked: true,
        considerAutoUpgradation: false,
        selectedReservationChoice: ReservationChoiceType.None,
        searchKey: newSearchKey,
        showViewAll: travelers.length > 3,
        confirmBookCheckbox: false,
        psgnDestinationAddress: newaddress,
      },
    });

    dispatch(getTravelerHeaderText());
    dispatch(loadPreferences());
    uploadGrafanaMetrics();
    dispatch(fetchRailofyDataIfNeeded());
    dispatch(onPreferenceChangedtoVeg());
    // dispatch(showTravelAdvisory(true));
    const isLoggedIn = await isUserLoggedIn();
    if (isLoggedIn && getRailsAvailabilitySubscription() !== 0) {
      dispatch(getAvailabilitySubscription());
    } else {
      trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_NOT_SHOWN);
    }
    const { railsUserVerification: { irctcUserName: username } = {} } = getState() || {};
    if (username) {
      trackGenericEvar99Event(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_USERNAME_PREFILLED,
      );
    } else {
      trackGenericEvar99Event(
        TRAVELERS_PAGE_TRACKING_KEY_NEW,
        IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_USERNAME_NOT_PREFILLED,
      );
    }
  } catch (e) {
    console.error('err', e);
  }
};

export const setShowIrctcProfileCompletionFlow = (val, irctcProfileCompletionFlow = false) => (dispatch) => {
  try {
    dispatch({
      type: ACTION_SET_IRCTC_PROFILE_STATUS,
      data: {
        showProfileIncompleteFlow: val,
        irctcProfileCompletedFromWebView: (irctcProfileCompletionFlow && !val) || false,
      },
    });
  } catch (e) {
    console.error('error in setting irctc profile completion status', e);
  }
};

export const checkIrctcProfileCompletionStatus = () => async (dispatch, getState) => {
  try {
    const { railsUserVerification: { irctcUserName } = {} } = getState() || {};
    dispatch({
      type: ACTION_SET_IRCTC_PROFILE_STATUS,
      data: {
        showIrctcComponentLoader: true,
      },
    });
    await dispatch(isValidIrctcUserName(irctcUserName, true));
    dispatch({
      type: ACTION_SET_IRCTC_PROFILE_STATUS,
      data: {
        showIrctcComponentLoader: false,
      },
    });
    scrollToErrorComponent(COMPONENT_IRCTC_USERNAME, refs);
  } catch (e) {
    console.error('error in checking irctc profile completion status', e);
  }
};

export const isValidIrctcUserName =
  (userName, irctcProfileCompletionFlow = false) =>
  async (dispatch, getState) => {
    if (userName) {
      try {
        const { railsTraveler: { selectedQuota = null } = {} } = getState() || {};
        const headers = {
          quota: selectedQuota?.code,
        };
        const res = await fetch2(railsConfig.validateUserId, {
          method: 'POST',
          headers,
          body: JSON.stringify({ userName }),
        });
        if (res.status === 200) {
          const response = await res.json();
          const newConfigKey = response?.verificationFlow?.configKey;
          dispatch(updateConfigKeyResponseForAadharVerification(null));
          if (newConfigKey) {
            const configKeyResponse = await getConfigStore(newConfigKey);
            if (!isEmpty(configKeyResponse)) {
              dispatch(updateConfigKeyResponseForAadharVerification(configKeyResponse));
            }
          }
          const userIdStatusObject = parseValidateUserResponse(response);
          dispatch(setShowIrctcProfileCompletionFlow(
            userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE, irctcProfileCompletionFlow));
          dispatch(updateVerificationFlow(response?.verificationFlow ?? {}));
        if (userIdStatusObject.status === STATUS_API_SUCCESS ||
            userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE) {
          handleIrctcUsernameOmniture(userIdStatusObject, irctcProfileCompletionFlow);
          return true;
        }
      }
      return false;
    } catch (e) {
      console.log('error in validating username with irctc.' + 'Error Code: ' + e?.errorCode );
      return false;
    }
  }
  return false;
};

const handleIrctcUsernameOmniture = (userIdStatusObject, irctcProfileCompletionFlow) => {
  const isProfileIncomplete = userIdStatusObject.status === STATUS_USER_PROFILE_INCOMPLETE;
  const isApiSuccess = userIdStatusObject.status === STATUS_API_SUCCESS;

  const eventKey = isProfileIncomplete
    ? IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_PROFILE_INCOMPLETE_STATUS_EVAR99
    : IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.IRCTC_PROFILE_COMPLETE_STATUS_EVAR99;
  updateEvar47or97Variable(eventKey);

  if (!irctcProfileCompletionFlow) {return;}

  if (isApiSuccess) {
    trackTravellerPageEvent(
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.SUCCESSFUL_PROFILE_COMPLETION_CLICK_EVENT,
    );
    updateEvar47or97Variable(
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.SUCCESSFUL_PROFILE_COMPLETION_EVAR97_EVENT
    );
  } else {
    trackTravellerPageEvent(
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.UNSUCCESSFUL_PROFILE_COMPLETION_CLICK_EVENT,
    );
  }
};

export const saveContactCallBackHandler = (contactDetails) => (dispatch) => {
  dispatch({
    type: ACTION_ON_CONTACT_DETAILS_EDITED,
    data: contactDetails,
  });
  saveContactDetails(contactDetails, 'RAILS');
};

export const onAddTraveler = () => (dispatch, getState) => {
  trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADD_TRAVELLER);
  const { railsTraveler } = getState();
  if (railsTraveler.selectedTravelers.length < railsTraveler.maxTravelersAllowed) {
    Actions.addTraveler();
  } else {
    showShortToast(getMaxTravelerWarningText(railsTraveler));
  }
};

export const onEditTraveler = (traveler) => (dispatch, getState) => {
  const { railsTraveler } = getState();
  trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_EDIT_TRAVELLER);
  if (
    railsTraveler.selectedTravelers.length < railsTraveler.maxTravelersAllowed ||
    isTravelerSelected(railsTraveler.selectedTravelers, traveler)
  ) {
    Actions.addTraveler({ traveler });
  } else {
    showShortToast(getMaxTravelerWarningText(railsTraveler));
  }
};

export const onHardBackPress = () => (dispatch, getState) => {
  const { railsTraveler: { travelers, selectedTravelers, isLoggedIn } = {} } = getState();
  updateLocalStorage(isLoggedIn, travelers, selectedTravelers, dispatch);
};

export const updateLocalStorage = async (isLoggedIn, travelers, selectedTravelers, dispatch) => {
  try {
    await storeRailsTravelersInCache(isLoggedIn, travelers, dispatch);
    storeRailsSelectedTravelersInCache(isLoggedIn, selectedTravelers, dispatch);
  } catch (e) {
    console.log('Error in updateLocalStorage : ', e);
  }
};

export const onTncClicked = (e) => {
  if (isMweb() && e) {
    e.preventDefault();
  }
  Actions.openWebView({
    url: railsConfig.insuranceTnC,
    headerText: 'Insurance Terms & Conditions',
    headerIcon: backIcon,
  });
};

export const onTravelInsuranceCBClicked = (istravelInsuranceOpted) => (dispatch) => {
  trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADD_INSURANCE);
  dispatch({
    type: ACTION_SET_TRAVEL_INSURANCE,
    data: {
      travelerInsuranceOpted: istravelInsuranceOpted,
    },
  });
};

export const onRefundAndCancellationClicked = () => (dispatch, getState) => {
  const { railsTraveler: { refundAndCanChecked } = {} } = getState();
  dispatch({
    type: ACTION_ON_REFUND_AND_CANCELLATION_CLICKED,
    data: {
      refundAndCanChecked: !refundAndCanChecked,
    },
  });
};

export const onTravelAdvisoryClicked = () => (dispatch, getState) => {
  const { railsTraveler: { travelAdvisoryChecked } = {} } = getState();
  dispatch({
    type: ACTION_ON_TRAVEL_ADVISORY_CLICKED,
    data: {
      travelAdvisoryChecked: !travelAdvisoryChecked,
    },
  });
};

export const onConfirmBookCheckboxClicked = () => (dispatch, getState) => {
  const { railsTraveler: { confirmBookCheckbox } = {} } = getState();
  dispatch({
    type: ACTION_ON_CONFIRM_BOOK_CHECKBOX_CLICKED,
    data: {
      confirmBookCheckbox: !confirmBookCheckbox,
    },
  });
};

export const handleGstDetailsChange = (value, dataPoint) => (dispatch) => {
  dispatch({
    type: UPDATE_OPTIONAL_GST_DETAILS,
    data: {
      value,
      dataPoint,
    },
  });
};

export const onConsiderAutoUpgradationClicked = () => (dispatch, getState) => {
  const {
    railsTraveler: { considerAutoUpgradation },
  } = getState();
  dispatch({
    type: ACTION_ON_CONSIDER_AUTO_UPGRADATION_CLICKED,
    data: {
      considerAutoUpgradation: !considerAutoUpgradation,
    },
  });
};

const verifySelectedTravelerInfo = (seletedTravelers) => {
  for (let i = 0; i < seletedTravelers.length; i++) {
    const traveler = seletedTravelers[i];
    if (
      traveler.highlightTravelerForError ||
      traveler.isMaleTravelingInLadiesQuota ||
      !isAgeAndGenderSpecified(traveler)
    ) {
      return false;
    }
  }
  return true;
};

export const verifyTravelerDetails = (
  selectedTravelers,
  travelers,
  maxTravelersAllowed,
  warning,
  displayVegPreferenceBottomSheet,
) => {
  try {
    const selectedTravelerList = getTravelersListFromId(selectedTravelers, travelers);

    if (
      selectedTravelers.length !== 0 &&
      selectedTravelers.length <= maxTravelersAllowed &&
      !warning.showChildWithoutBerthWarning &&
      !warning.showMaxTravelerWarningForTatkal &&
      !warning.showMaxSrCitizenTravelerWarning &&
      !displayVegPreferenceBottomSheet &&
      verifySelectedTravelerInfo(selectedTravelerList)
    ) {
      return true;
    }
  } catch (e) {
    console.log('Error in verifyTravelerDetails : ', e);
  }
  return false;
};

export const onTravelerPageScroll = (pos) => {
  _scrollPos = pos;
};

export const getComponentRef = (refs, component) =>
  find(refs, (ref) => ref.component === component);

export const scrollToErrorComponent = (component, refs = _refs) => {
  try {
    const scrollComp = getComponentRef(refs, COMPONENT_TRAVELER_PAGE_SCROLL_VIEW);
    const scrollRef = scrollComp ? scrollComp.ref : null;

    const comp = getComponentRef(refs, component) || getComponentRef(refs, DUMMY_COMPONENT);
    const compRef = comp.ref;

    if (isEmpty(scrollRef)) {
      return;
    }

    compRef?.measure?.((tfx, tfy, twidth, theight, tpx, tpy) => {
      scrollRef?.getNativeScrollRef?.()?.measure?.((fx, fy, width, height, px, py) => {
        scrollRef?.scrollTo?.({ y: _scrollPos + tpy - py });
      });
    });
  } catch (error) {
    console.error(error);
  }
};

export const scrollToBottom = (refs = _refs) => {
  const scrollComp = getComponentRef(refs, COMPONENT_TRAVELER_PAGE_SCROLL_VIEW);
  const scrollRef = scrollComp ? scrollComp.ref : null;
  if (scrollRef) {
    scrollRef?.scrollToEnd?.({ animated: true });
  }
};

export const toggleModalVisibility = () => ({
  type: ACTION_TOGGLE_MODAL_VISIBILITY,
  data: null,
});

export const showHideIRCTCUsernameError = (val) => ({
  type: ACTION_SHOW_IRCTC_USERNAME_ERROR,
  data: val,
});

export const getDuplicateBookingPayload = (getState) => {
  const {
    railsTraveler: { selectedTravelers, travelers, contactDetails } = {},
    railsListing: { departureDate, selectedTrainInfo } = {},
  } = getState();

  const originStationCode = selectedTrainInfo.frmStnCode;
  const destinationStationCode = selectedTrainInfo.toStnCode;

  const travellersById = _keyBy(travelers, 'travelerId');

  const departureMonth =
    departureDate.getMonth() + 1 < 10
      ? `0${departureDate.getMonth() + 1}`
      : departureDate.getMonth() + 1;
  const departureDate2digit =
    departureDate.getDate() < 10 ? `0${departureDate.getDate()}` : departureDate.getDate();

  return {
    bookerContactDetails: {
      email: contactDetails.email,
      mobileNumber: contactDetails.mobile,
    },
    journeyDetails: {
      tripDetails: {
        journeyDate: `${departureDate.getFullYear()}${departureMonth}${departureDate2digit}`,
        fromStnCode: originStationCode,
        destStnCode: destinationStationCode,
      },
      trainDetails: {
        trainNumber: selectedTrainInfo.trainNumber,
      },
    },
    passengerInfoList: selectedTravelers.map((travId) => {
      const travellerObj = travellersById?.[travId];
      return {
        passengerName: travellerObj?.fullName ?? '',
        passengerGender: travellerObj?.basicInfo?.gender?.value ?? 'M',
      };
    }),
  };
};

export const dismissDuplicateBookingModal = () => (dispatch) => {
  dispatch({
    type: ACTION_SHOW_DUPLICATE_CONFIRMATION,
    data: {},
  });
};

// eslint-disable-next-line max-len
export const onReviewClicked = (psgnDestinationAddress, skipDuplicateBookingCheck, freeCancellation = {}, assuredConfirmation = {}) => async (dispatch, getState) => {
  const {
    railsTraveler: {
      selectedTravelers,
      travelers,
      maxTravelersAllowed,
      warning,
      contactDetails,
      refundAndCanChecked,
      classValue,
      selectedQuota,
        bottomSheetCtaClicked,
        railofy: {
          tgPlusData,
        selectedIndex,
        railofyType,
        isFCSelected,
        isTGSelected,
        tripGuaranteeData,
        freeCancellationData,
          fcTgSelectionType,
        },
      optionalGstDetailsEnabled,
      gstDetails: { gstIn, nameOnGst },
      showProfileIncompleteFlow,
    } = {},
    railsUserVerification: { irctcUserName, verificationFlow },
  } = getState();

  const {
    railsTraveler,
    gstinReducer,
    railsListing: {
      captureAddress,
      selectedClassType,
      selectedTrainInfo,
      selectedBookingDate,
    },
  } = getState();

  const { availablityStatus } = selectedBookingDate ?? {};
    const { trainName = '', trainNumber = '' } = selectedTrainInfo || {};
  if (freeCancellation && freeCancellation.freeCancellationEnabled && freeCancellation.freeCancellationData) {
    const { freeCancellationEnabled, optedForFreeCancellation} = freeCancellation;
    dispatch({
      type: ACTIONS_OPTED_FOR_FREE_CANCELLATION,
      data: {
        freeCancellationEnabled,
        freeCancellationInsuranceOpted: optedForFreeCancellation,
        fcVariant: freeCancellation.fcVariant,
      },
    });
  }

  if (assuredConfirmation && assuredConfirmation.railsConfirmationGuaranteeOption && (typeof assuredConfirmation.railofyShown !== 'undefined')) {
    dispatch({
      type: ACTIONS_RAILS_ASSURED_CONFIRMATION,
      data: {
        cnfmGuaranteeDetail: assuredConfirmation,
      },
    });
  }

  if (!irctcUserName) {
      const userName = await irctcAccountUserNameAPI();
    if (!userName) {
        dispatch(showHideIRCTCUsernameError(true));
        scrollToErrorComponent(COMPONENT_IRCTC_USERNAME, refs);
        showShortToast(_label('enter_irctc_username'));
        return;
    }
    dispatch(updateUsername(userName));
  } else {
    const mandateIrctcProfileCompletion = ((showProfileIncompleteFlowPokus() === 2) && showProfileIncompleteFlow);
    if (mandateIrctcProfileCompletion) {
      scrollToErrorComponent(COMPONENT_IRCTC_USERNAME, refs);
      showShortToast(_label('complete_profile_to_proceed'));
      return;
    }
      if (verificationFlow?.blockBooking) {
      scrollToErrorComponent(COMPONENT_IRCTC_USERNAME, refs);
      showShortToast((verificationFlow?.message ?? _label('something_went_wrong')));
      return;
    }
  }
    dispatch(onPreferenceChangedtoVeg());


  // verifyDuplicateBooking(getState);

  if (selectedTravelers.length > maxTravelersAllowed) {
    scrollToErrorComponent(COMPONENT_TRAVELER, refs);
    showShortToast(getMaxTravelerWarningText(railsTraveler));
    return;
  }

  if (getInfantCount(selectedTravelers, travelers) > MAX_INFANT_ALLOWED) {
    scrollToErrorComponent(COMPONENT_TRAVELER, refs);
    showShortToast(_label('max_infants_allowed', undefined, { count: MAX_INFANT_ALLOWED }));
    return;
  }

  if (warning.showChildWithoutBerthWarning) {
    scrollToErrorComponent(COMPONENT_TRAVELER, refs);
      showShortToast(_label('irctc_child_berth_restriction'));
      return;
    }

    if (warning.showMaxTravelerWarningForTatkal) {
      scrollToErrorComponent(COMPONENT_TRAVELER, refs);
    showShortToast(getMaxTravelerWarningText(railsTraveler));
    return;
  }

    if (warning.showMaxSrCitizenTravelerWarning) {
      scrollToErrorComponent(COMPONENT_TRAVELER, refs);
      showShortToast(_label('sr_citizen_max_traveller'));
      return;
    }

  if (!verifyTravelerDetails(selectedTravelers, travelers, maxTravelersAllowed, warning)) {
    scrollToErrorComponent(COMPONENT_TRAVELER, refs);
    showShortToast(_label('select_valid_traveller'));
    return;
  }


  if (isNull(contactDetails) || isEmpty(contactDetails) || !contactDetails.hasCompleteFields) {
    scrollToErrorComponent(COMPONENT_CONTACT_CARD, refs);
    showShortToast(_label('fill_contact_details'));
    return;
  }

  if (captureAddress) {
    if (!psgnDestinationAddress.pinCode) {
      showShortToast(_label('fill_pin_code'));
      scrollToErrorComponent(COMPONENT_DEST_PINCODE, refs);
      return;
    }
    if (!psgnDestinationAddress.pinCode.match(/^\d{6}$/)) {
      showShortToast('Enter a correct 6-digit pincode');
      scrollToErrorComponent(COMPONENT_DEST_PINCODE, refs);
      return;
    }
    if (!psgnDestinationAddress.postOffice) {
      showShortToast(_label('invalid_pin_code_error'));
      scrollToErrorComponent(COMPONENT_DEST_PINCODE, refs);
      return;
    }
    if (!psgnDestinationAddress.postOffice) {
      showShortToast(_label('enter_valid_pincode'));
      scrollToErrorComponent(COMPONENT_DEST_PINCODE, refs);
      return;
    }
    if (!psgnDestinationAddress.pinCode.match(/^\d{6}$/)) {
      showShortToast('Enter a correct 6-digit pincode');
      scrollToErrorComponent(COMPONENT_DEST_PINCODE, refs);
      return;
    }
    if (!psgnDestinationAddress.postOffice) {
      showShortToast('Please enter a valid pincode');
      scrollToErrorComponent(COMPONENT_DEST_PINCODE, refs);
      return;
    }
    if (!psgnDestinationAddress.address) {
      showShortToast(_label('fill_address_line', undefined, { line: 1 }));
      scrollToErrorComponent(COMPONENT_DEST_ADDRESSLINE1, refs);
      return;
    }
    if (!psgnDestinationAddress.city) {
      showShortToast(_label('select_city'));
      return;
    }
    if (!psgnDestinationAddress.state) {
      showShortToast(_label('select_state'));
      return;
    }
    // if (!psgnDestinationAddress.postOffice) {
    //   scrollToErrorComponent(COMPONENT_DEST_POSTOFFICE, refs);
    //   showShortToast('Please select Post Office');
    //   return;
    // }
    dispatch({
      type: ACTION_ADD_DESTINATION_ADDRESS,
      data: { psgnDestinationAddress },
    });
  }

  if (!refundAndCanChecked) {
    showShortToast(_label('accept_canc_refund_policy'));
    return;
  }
  trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_CONTINUE);
  const numberOfPassengers = selectedTravelers.length;
  const commonData = `${trainName}|${trainNumber}|${selectedQuota?.code}|${classValue.code}|${availablityStatus}|${numberOfPassengers}`;
  trackTravellerSeatInfo(TRAVELERS_PAGE_TRACKING_KEY_NEW,commonData);

  const isGSTINwidgetShown = await shouldShowGSTNWidget();
  const gstinResponse = await gstinSubmit(gstinReducer, dispatch);

  if (isGSTINwidgetShown && gstinResponse?.error) {
    dispatch({
      type: ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW,
      data: { loadingDuplicateBookingAndReview: false },
    });
    scrollToBottom(refs);
      if (gstinResponse?.errorMsg === USER_CONSENT_ERROR) {
        trackClickEventProp61(
          TRAVELERS_PAGE_TRACKING_KEY_NEW,
          RAIL_EVENTS.TRAVELLER.MOB_RAILS_TRAVELLER_CONSENT_NOT_GIVEN,
        );
      }
    return;
  }
  const gstDetailsError =
    optionalGstDetailsEnabled &&
    (!isValidGstNumber(gstIn) || nameOnGst?.slice()?.trim()?.length === 0);
  dispatch(handleGstDetailsChange(gstDetailsError, 'gstDetailsError'));
    if (optionalGstDetailsEnabled && gstDetailsError) {
    showShortToast(_label('fill_gst_details'));
    scrollToBottom(refs);
    return;
    }
    const showFCTGTogetherPrompt = getIfToShowFCTGTogetherPrompt();
    const fcTgTogetheMultiSelect = fcTgSelectionType === MULTI_SELECT;
    const currentTgVersion = await getRailsTGVersion();
    const isTgPlus = currentTgVersion !== 1 && tgPlusData;
    const shouldDisplayFcBottomSheet = (condition, data) => condition && data?.header;
    const shouldDisplayTgBottomSheet = (condition, data) =>
      condition && data?.availabilityText && !isTgPlus;

    if (
      selectedIndex === -1 &&
      [1, 2].includes(showFCTGTogetherPrompt) &&
      !(isTGSelected || isFCSelected) &&
      !bottomSheetCtaClicked
    ) {
      if (shouldDisplayFcBottomSheet(showFCTGTogetherPrompt === 1, freeCancellationData)) {
        dispatch(displayFcBottomSheet());
        return;
      } else if (shouldDisplayTgBottomSheet(showFCTGTogetherPrompt === 2, tripGuaranteeData)) {
        dispatch(displayTgBottomSheet());
        return;
      }
    }

    if (
      selectedIndex !== -1 &&
      fcTgTogetheMultiSelect &&
      !bottomSheetCtaClicked &&
      (isTGSelected || isFCSelected) &&
      !(isTGSelected && isFCSelected)
    ) {
      if (shouldDisplayFcBottomSheet(isTGSelected, freeCancellationData)) {
        dispatch(displayFcBottomSheet());
        return;
      } else if (shouldDisplayTgBottomSheet(isFCSelected, tripGuaranteeData)) {
        dispatch(displayTgBottomSheet());
        return;
      }
    }
    // fctg
    if (selectedIndex === -1 && !bottomSheetCtaClicked) {
      if (railofyType === RAILOFY_TYPE.FC) {
        const showFcBottomSheet = getTravelerFreeCancellationBottomSheet();
        if (showFcBottomSheet) {
          dispatch(displayFcBottomSheet());
        } else {
          scrollToErrorComponent(FREE_CANCELLATION, refs);
          showShortToast(_label('select_an_option_to_proceed'));
        }
        return;
      }
      if (railofyType === RAILOFY_TYPE.TG) {
        scrollToErrorComponent(ASSURED_CONFIRMATION, refs);
        showShortToast(_label('select_an_option_to_proceed'));
        return;
      }
  }
    dispatch({
      type: ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW,
      data: { loadingDuplicateBookingAndReview: true },
    });

  if (!irctcUserName) {
      const userName = await irctcAccountUserNameAPI();

    if (!userName) {

        dispatch(showHideIRCTCUsernameError(true));
        scrollToErrorComponent(COMPONENT_IRCTC_USERNAME, refs);
        showShortToast(_label('enter_irctc_username'));
        return;
    }
    dispatch(updateUsername(userName));
  }

  if (railsTraveler.showPwdReminder) {
    Actions.railsPasswordReminder();
  } else {
    //skywalker start
    //raillisting (selectedClassType)-> selectedTrainInfo -> departureDateAndTime , arrivalDateAndTime, frmStnCode, toStnCode, trainNumber
    try {
      const skyWalkerData = {
        fromCity: selectedTrainInfo.frmStnCode,
        toCity: selectedTrainInfo.toStnCode,
        fromDateTime: fecha.format(selectedTrainInfo.departureDateAndTime, 'DD-MM-YYYY HH:mm:ss'),
        toDateTime: fecha.format(selectedTrainInfo.arrivalDateAndTime, 'DD-MM-YYYY HH:mm:ss'),
        paxCount: railsTraveler.selectedTravelers.length,
        cabinClass: selectedClassType,
        trainNumber: selectedTrainInfo.trainNumber,
      };
      skywalkerSearchConnector(skyWalkerData, 'review');
    } catch (e) {
      console.log('Error in skywalker: ', e);
    }
    //skywalker end
      dispatch(fetchBookingReview());
  }
};

export const loadPreferences = () => (dispatch, getState) => {
  const { railsTraveler: { applicableBerthTypes, applicableFoodTypes } = {} } = getState();
  let mealOptions = {};
  let berthOptions = {};
  let srOptions = {};
  let nationalityOptions = {};
  try {
    mealOptions = getMealPreferenceOptions1(applicableFoodTypes);
    berthOptions = getBerthPreferenceOptions(applicableBerthTypes);
    srOptions = getSrCitizenOptions();
    nationalityOptions = getNationalityOptions();
  } catch (e) {
    console.log('Error in loadPreferences :', e);
  }
  dispatch({
    type: ACTION_LOAD_PREFERENCES,
    data: {
      mealOptions,
      berthOptions,
      srOptions,
      nationalityOptions,
    },
  });
};

export const couponsRemoved = (ancillaryType) => async (dispatch, getState) => {
  const {
    railsTraveler: {
      railofy: {
        isTGSelected: cnfmGuaranteeOptFlag,
        isFCSelected: freeCancellationInsuranceOptedFlag,
        fcTgSelectionType,
        isFcDiscounted,
        isTgDiscounted,
        isTGPlusSelected: isTGPlusSelectedVal,
      },
    } = {},
  } = getState();
  const tgVersion = await getRailsTGVersion();
  const fcTgTogetheMultiSelect = fcTgSelectionType === MULTI_SELECT;
  let fcTgObj = {};
  if (ancillaryType === COUPON_TYPE.FC_TG_TOGETHER) {
    fcTgObj = {
      freeCancellationInsuranceOpted: false,
      isFCSelected: false,
      isTGSelected: false,
      isTgDiscounted: false,
      isFcDiscounted: false,
      cnfmGuaranteeOpted: false,
      isTGPlusSelected: false,
    };
    dispatch(setFreeCancellationOption(false));
  } else if (ancillaryType === COUPON_TYPE.FC_ONLY) {
    fcTgObj = {
      freeCancellationInsuranceOpted: false,
      isFCSelected: false,
      isTGSelected: false,
      cnfmGuaranteeOpted: false,
      isTGPlusSelected: false,
      railofyType: RAILOFY_TYPE.FC,
      tgType: getTgType(RAILOFY_TYPE.FC, tgVersion),
    };
    setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, true);
    if (fcTgTogetheMultiSelect) {
      fcTgObj.cnfmGuaranteeOpted = cnfmGuaranteeOptFlag;
      fcTgObj.isTGSelected = cnfmGuaranteeOptFlag;
      fcTgObj.isTgDiscounted = fcTgObj.freeCancellationInsuranceOpted && !isFcDiscounted;
      fcTgObj.isFcDiscounted = fcTgObj.cnfmGuaranteeOpted && !fcTgObj.isTgDiscounted;
      fcTgObj.isTGPlusSelected = isTGPlusSelectedVal;
    }
    dispatch(setFreeCancellationOption(false));
  } else if (ancillaryType === COUPON_TYPE.TG_ONLY) {
    fcTgObj = {
      cnfmGuaranteeOpted: false,
      isTGSelected: false,
      isFCSelected: false,
      freeCancellationInsuranceOpted: false,
      isTGPlusSelected: false,
      railofyType: RAILOFY_TYPE.TG,
      tgType: getTgType(RAILOFY_TYPE.TG, tgVersion),
    };
    setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, false);
    if (fcTgTogetheMultiSelect) {
      fcTgObj.freeCancellationInsuranceOpted = freeCancellationInsuranceOptedFlag;
      fcTgObj.isFCSelected = freeCancellationInsuranceOptedFlag;
      fcTgObj.isFcDiscounted = fcTgObj.cnfmGuaranteeOpted && !isTgDiscounted;
      fcTgObj.isTgDiscounted = fcTgObj.freeCancellationInsuranceOpted && !fcTgObj.isFcDiscounted;
    }
    dispatch(setFreeCancellationOption(false));
  }
  dispatch({
    type: ACTION_UPDATE_FC_TG_USER_RESPONSE,
    data: {
      hasError: false,
      ...fcTgObj,
    },
  });
};

export const couponsApplied = (ancillaryType) => async (dispatch, getState) => {
  const {
    railsTraveler: {
      railofy: {
        isTGSelected: cnfmGuaranteeOptFlag,
        isFCSelected: freeCancellationInsuranceOptedFlag,
        fcTgSelectionType,
        isFcDiscounted,
        isTgDiscounted,
        isTGPlusSelected: isTGPlusSelectedVal,
      },
    } = {},
  } = getState();
  const tgVersion = await getRailsTGVersion();
  const fcTgTogetheMultiSelect = fcTgSelectionType === MULTI_SELECT;
  let fcTgObj = {};
  if (ancillaryType === COUPON_TYPE.FC_TG_TOGETHER) {
    fcTgObj = {
      freeCancellationInsuranceOpted: true,
      isFCSelected: true,
      isTGSelected: true,
      cnfmGuaranteeOpted: true,
      isTGPlusSelected: false,
    };
    dispatch(setFreeCancellationOption(true));
  } else if (ancillaryType === COUPON_TYPE.FC_ONLY) {
    fcTgObj = {
      freeCancellationInsuranceOpted: true,
      isFCSelected: true,
      isTGSelected: false,
      cnfmGuaranteeOpted: false,
      isTGPlusSelected: false,
      railofyType: RAILOFY_TYPE.FC,
      tgType: getTgType(RAILOFY_TYPE.FC, tgVersion),
    };
    setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, true);
    if (fcTgTogetheMultiSelect) {
      fcTgObj.cnfmGuaranteeOpted = cnfmGuaranteeOptFlag;
      fcTgObj.isTGSelected = cnfmGuaranteeOptFlag;
      fcTgObj.isTgDiscounted = fcTgObj.freeCancellationInsuranceOpted && !isFcDiscounted;
      fcTgObj.isFcDiscounted = fcTgObj.cnfmGuaranteeOpted && !fcTgObj.isTgDiscounted;
      fcTgObj.isTGPlusSelected = isTGPlusSelectedVal;
    }
    dispatch(setFreeCancellationOption(true));
  } else if (ancillaryType === COUPON_TYPE.TG_ONLY) {
    fcTgObj = {
      cnfmGuaranteeOpted: true,
      isTGSelected: true,
      isFCSelected: false,
      freeCancellationInsuranceOpted: false,
      isTGPlusSelected: false,
      railofyType: RAILOFY_TYPE.TG,
      tgType: getTgType(RAILOFY_TYPE.TG, tgVersion),
    };
    setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, false);
    if (fcTgTogetheMultiSelect) {
      fcTgObj.freeCancellationInsuranceOpted = freeCancellationInsuranceOptedFlag;
      fcTgObj.isFCSelected = freeCancellationInsuranceOptedFlag;
      fcTgObj.isFcDiscounted = fcTgObj.cnfmGuaranteeOpted && !isTgDiscounted;
      fcTgObj.isTgDiscounted = fcTgObj.freeCancellationInsuranceOpted && !fcTgObj.isFcDiscounted;
    }
    dispatch(setFreeCancellationOption(false));
  }

  dispatch({
    type: ACTION_UPDATE_FC_TG_USER_RESPONSE,
    data: {
      hasError: false,
      ...fcTgObj,
    },
  });
};

export const onAddEditTraveler = (addTravelerMetaData) => async (dispatch, getState) => {
  const {
    railsTraveler: {
      applicableBerthTypes,
      foodChoiceEnabled,
      selectedQuota,
      classValue,
      selectedTravelers,
      travelers,
      maxTravelersAllowed,
      isLoggedIn,
      userDetails,
      seniorCitizenApplicable,
      applicableFoodTypes,
    } = {},
  } = getState();

  const { traveler, addOrEditFlag } = addTravelerMetaData;
  try {
    const name = getNameObj(traveler.name);
    const newTraveler = new Traveler(
      traveler.travelerId,
      name,
      traveler.age,
      traveler.gender,
      traveler.childBerthFlag,
      applicableBerthTypes,
      foodChoiceEnabled,
      selectedQuota,
      classValue,
      true,
      seniorCitizenApplicable,
      traveler.berth,
      traveler.meal,
      traveler.seniorCitizenConcession,
      traveler.nationality,
      traveler.mealPreferenceArray,
      applicableFoodTypes,
      true,
    );

    if (traveler.passportNumber) {
      newTraveler.passportNumber = traveler.passportNumber;
    }
    if (traveler?.dateOfBirth) {
      newTraveler.dateOfBirth = traveler?.dateOfBirth;
    }

    const mmtAuth = get(userDetails, 'mmtAuth', null);
    if (isLoggedIn) {
      const savedTraveler = await updateTravelerInServer(newTraveler, mmtAuth, addOrEditFlag);
      if (!isNull(savedTraveler)) {
        newTraveler.travelerId = savedTraveler.travellerId;
        newTraveler.basicInfo.travelerId = savedTraveler.travellerId;
      }
    }

    // Add new/old traveler to selected array
    let newSelectedTravelers = filter(
      selectedTravelers,
      (value) => value !== newTraveler.travelerId,
    );
    newSelectedTravelers = [...newSelectedTravelers, newTraveler.travelerId];

    // Add new/old traveler to travelers array
    let newTravelers = filter(travelers, (value) => value.travelerId !== newTraveler.travelerId);
    newTravelers = [...newTravelers, newTraveler];

    const selectedTravelerList = getTravelersListFromId(newSelectedTravelers, newTravelers);
    newTravelers = sortTravelersBySelection(newTravelers, selectedTravelerList);

    const warning = initWarning(selectedTravelerList, maxTravelersAllowed, selectedQuota);
    dispatch({
      type: ACTION_ON_ADD_EDIT_TRAVELER,
      data: {
        selectedTravelers: newSelectedTravelers,
        travelers: newTravelers,
        warning,
        showViewAll: newTravelers.length > 3,
      },
    });

    updateLocalStorage(isLoggedIn, newTravelers, newSelectedTravelers, dispatch);
    Actions.pop();
    trackTravellerPageEvent(
      addOrEditFlag
        ? RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_ADD_TRAVELLER_SUCCESS
        : RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_EDIT_TRAVELLER_SAVED,
    );
  } catch (e) {
    console.log('Error in onAddEditTraveler : ', e);
  }
  await dispatch(reApplyCoupon);
};

export const onContactDetailsValidationFailed = (fieldName) => (dispatch, getState) => {
  let { railsTraveler: { contactDetails } = {} } = getState();
  contactDetails = {
    ...contactDetails,
    hasCompleteFields: false,
    [fieldName]: '',
  };

  dispatch({
    type: ACTION_ON_CONTACT_DETAILS_VALIDATION_FAILURE,
    data: contactDetails,
  });

  saveContactDetails(contactDetails, 'RAILS');
};

// TODO
// if-else block is unnecessary. remove if-else blcok and do sanity
export const captureRef = (componentName, ref) => () => {
  let reference = {};
  if (componentName === COMPONENT_TRAVELER) {
    reference = {
      component: COMPONENT_TRAVELER,
      ref,
    };
  } else if (componentName === COMPONENT_INSURANCE) {
    reference = {
      component: COMPONENT_INSURANCE,
      ref,
    };
  } else if (componentName === COMPONENT_IRCTC_USERNAME) {
    reference = {
      component: COMPONENT_IRCTC_USERNAME,
      ref,
    };
  } else if (componentName === COMPONENT_CONTACT_CARD) {
    reference = {
      component: COMPONENT_CONTACT_CARD,
      ref,
    };
  } else if (componentName === COMPONENT_TRAVELER_PAGE_SCROLL_VIEW) {
    reference = {
      component: COMPONENT_TRAVELER_PAGE_SCROLL_VIEW,
      ref,
    };
  } else if (componentName === COMPONENT_DEST_ADDRESSLINE1) {
    reference = {
      component: COMPONENT_DEST_ADDRESSLINE1,
      ref,
    };
  } else if (componentName === COMPONENT_DEST_PINCODE) {
    reference = {
      component: COMPONENT_DEST_PINCODE,
      ref,
    };
  } else if (componentName === COMPONENT_DEST_POSTOFFICE) {
    reference = {
      component: COMPONENT_DEST_POSTOFFICE,
      ref,
    };
  } else if (componentName === COMPONENT_TRAVEL_ADVISORY) {
    reference = {
      component: COMPONENT_TRAVEL_ADVISORY,
      ref,
    };
  } else if (componentName === FREE_CANCELLATION) {
    reference = {
      component: FREE_CANCELLATION,
      ref,
    };
  } else if (componentName === ASSURED_CONFIRMATION) {
    reference = {
      component: ASSURED_CONFIRMATION,
      ref,
    };
  }
  else if (componentName === TGS_Traveller_Container)
  {
    reference = {
      component: TGS_Traveller_Container,
      ref,
    };
  }
  else if (componentName === TGS_Tickets_Container)
  {
    reference = {
      component: TGS_Tickets_Container,
      ref,
    };
  } else if (componentName === DUMMY_COMPONENT) {
    reference = {
      component: DUMMY_COMPONENT,
      ref,
    };
  }

  const index = findIndex(refs, (r) => r.component === reference.component);
  if (index !== -1) {
    refs[index] = reference;
  } else {
    refs = [...refs, reference];
  }
  _refs = refs;
};

export const showPasswordReminderScreen = (selected) => async (dispatch) => {
  dispatch({
    type: ACTION_SET_SHOW_PWD_REMINDER_SCREEN,
    data: {
      showPwdReminder: selected,
    },
  });

  await setDataToAsyncStorage(RAILS_IRCTC_SHOW_PASSWORD_REMINDER_SCREEN, selected);
};

export const setCouponData = (couponData) => ({
  type: ACTION_SET_COUPON_DATA,
  data: couponData,
});

export const addCouponToRecommendedList = (couponData) => ({
  type: ACTION_ADD_COUPON_DATA,
  data: couponData,
});

export const addRecommendedCoupons =
  (fcTgData = {}) =>
  async (dispatch, getState) => {
  const cmpId = get(getState(), 'railsLanding.cmp', null);
  const {
    railsListing: {
      selectedTrainInfo,
      selectedQuota,
      selectedClassType,
      totalFare,
      baseFare,
    },
  } = getState();
  const travelStartDate = fecha.format(selectedTrainInfo.departureDateAndTime, 'DD-MM-YYYY');
  const {
    toStnCode: travelDestinationCity,
    frmStnCode: travelDepartureCity,
    trainNumber: trainNo,
  } = selectedTrainInfo;
  const bookingQuota = selectedQuota.value;
  const selectedClass = selectedClassType;
  const trainType = getTrainType(selectedTrainInfo.trainType[0]).id;
  const transactionAmountPreTax = Number(baseFare);
  const transactionAmount = Number(totalFare);

  const body = {
    bookingDevice: channelCode(),
    travelDestinationCity,
    travelStartDate,
    travelDepartureCity,
    travellerCount: 1,
    bookMode: bookMode(),
    trainDetails: [
      {
        trainNo,
        bookingQuota,
        class: selectedClass,
        trainType,
        travelStartDate,
        trainDepartureCity: travelDepartureCity,
        trainDestinationCity: travelDestinationCity,
        transactionAmountPreTax,
        transactionAmount,
      },
    ],
      fcDetails: {
        premium: fcTgData?.fcPremium,
        opted: fcTgData?.isFcSelected,
      },
      tgDetails: {
        premium: fcTgData?.tgPremium,
        opted: fcTgData?.isTGSelected,
      },
    pokusState: {
      [RailsAbConfig.railsCouponExperiment]: railsCouponExperiment() || 0,
        [RailsAbConfig.railsFcTgTogether]: getRailsFcTgTogetherValue() || 0,
    },
    ...(cmpId && { cmpId }),
  };
  try {
    const promiseRecommendedCoupon = fetch2(railsConfig.recommendCouponURL, {
      body: JSON.stringify(body),
      headers: {
        ...(await getCommonHeaders()),
        Accept: '*/*',
      },
      method: 'POST',
    });
    // eslint-disable-next-line
    promiseRecommendedCoupon
      .then((res) => res.json())
      .then((r) => {
          const coupons = r?.couponDetails?.[body?.trainDetails?.[0]?.trainNo];
        dispatch({
          type: ACTION_ADD_RECOMMENDED_COUPONS,
          payload: coupons,
        });
      });
  } catch (error) {
    const coupons = [];
    dispatch({
      type: ACTION_ADD_RECOMMENDED_COUPONS,
      payload: coupons,
    });
  }
};

/**
 *
 * Pdt related actions
 *
 */

export const trackPdtTravellersLoad = async (data) => {
  const commonPdt = await getCommonPdtData();
  const pdtData = {
    ...commonPdt,
    searchData: data,
  };
  RailsPdtTravellersHelper.trackPageLoad(pdtData);
};

/**
 * Traveler delete action
 * @param {object} traveler
 */
export const onDeleteTravellerAction = (traveler) => async (dispatch, getState) => {
  try {
    const { railsTraveler: { isLoggedIn, selectedTravelers = [] } = {} } = getState();
    if (isLoggedIn) {
      const mmtAuth = get(getState(), 'railsTraveler.userDetails.mmtAuth', null);
      const payload = {
        query: [
          [
            {
              values: {
                associatedTravellers: [
                  {
                    emergCont: false,
                    status: 'DELETED',
                    travellerId: traveler.travelerId,
                  },
                ],
              },
              name: 'extendedUser',
            },
          ],
        ],
      };
      const response = await deleteTravelerFromServer(payload, mmtAuth); // api call
      if (!response || response.status !== 200) {
        showShortToast(_label('something_went_wrong_retry_or_cancel'));
        return;
      }
    }
    const travelers = get(getState(), 'railsTraveler.travelers', []);
    const updatedTravelerList = travelers.filter((trv) => trv.travelerId !== traveler.travelerId);
    const updatedTravelersByTravelerId = _keyBy(updatedTravelerList, 'travelerId');
    const newSelectedTravelers = selectedTravelers.filter(
      (trv) => updatedTravelersByTravelerId[trv],
    );
    updateLocalStorage(isLoggedIn, updatedTravelerList, newSelectedTravelers, dispatch);
    dispatch({
      type: ACTION_DELETE_TRAVELLER,
      data: {
        travelers: updatedTravelerList,
        selectedTravelers: newSelectedTravelers,
      },
    });
    await dispatch(reApplyCoupon);
    Actions.travelers();
  } catch (err) {
    console.error(err);
    showShortToast(_label('something_went_wrong_retry_or_cancel'));
  }
};

export const clearSelectedTravelersListAction = () => (dispatch, getState) => {
  const { railsTraveler: { isLoggedIn } = {} } = getState();
  dispatch({
    type: ACTION_CLEAR_SELECTED_TRAVELRS,
    data: {
      selectedTravelers: [],
    },
  });
  storeRailsSelectedTravelersInCache(isLoggedIn, [], dispatch);
};

// export const clearRailofyZcOptions = () => (dispatch)=> {
//   dispatch({
//     type: CLEAR_RAILOFY_ZC_OPTED_DETAILS,
//   });
// };

// export const saveRailofyResponse = (response) => (dispatch) => {
//   dispatch({
//     type: ACTION_SAVE_RAILOFY_RESPONSE,
//     data: {
//       railofyResponse: response
//     },
//   });
// }

// export const clearRailofyResponse = () => (dispatch) => {
//   dispatch({
//     type: ACTION_CLEAR_RAILOFY_RESPONSE,
//   });
// }
const fetchRailofyDataStart = () => ({
  type: ACTION_FETCH_RAILOFY_DATA_START,
});

const fetchRailofyDataSuccess = (response) => ({
  type: ACTION_FETCH_RAILOFY_DATA_SUCCESS,
  payload: response,
});

const fetchRailofyDataError = (errorResponse) => ({
  type: ACTION_FETCH_RAILOFY_DATA_FAILED,
  payload: errorResponse,
});

export const pdtLoggingTraveller = (response, data) => async (dispatch, getState) => {
  try {
    const state = getState();
    const baseFare = get(state, 'railsListing.baseFare', '');
    const avlDayList = get(state, 'railsListing.avlDayList[0]', '');
    const boardingStation = get(state, 'railsTraveler.selectedPickupStation', '');

    const { originStation, destinationStation, departureDate, selectedTrainInfo } =
      state.railsListing;
    const searchContext = {
      originStation,
      destinationStation,
      departureDate,
    };
    const predictionPercentage = selectedTrainInfo?.tbsAvailability?.find(
      (tbs) => tbs.quota === data.quota,
    )?.predictionPercentage;

    const tgVersion = await getRailsTGVersion();
    const isFCPreSelected = await getDataFromAsynStorage(RAILS_IS_PRE_FC_SELECTED);
    const responseMappings = [
      {
        responseKey: 'confirmationGuaranteeResponse',
        addOnType: 'TG',
        id: 'TG',
        unitPricePath: 'premiumTextRevamped[1].text',
      },
      {
        responseKey: 'freeCancellationResponse',
        addOnType: 'FC',
        id: 'FC',
        unitPricePath: 'cancellationOptions[0].insuranceAmount',
        isPreSelected: isFCPreSelected,
        refundAmountPath: 'cancellationOptions[0].refundAmount',
      },
      {
        responseKey: 'bnppResponse',
        addOnType: 'BNPP',
        id: 'BNPP',
        unitPricePath: 'partialAmount',
      },
    ];

    if (tgVersion !== 1 && response.confirmationGuaranteeResponse && response.tgPlusResponse) {
      responseMappings.push({
        responseKey: 'tgPlusResponse',
        addOnType: 'TG_PLUS',
        id: 'TG_PLUS',
        unitPricePath: 'premiumPerPerson',
      });
    }

    const addOnDetails = [];
    for (const mapping of responseMappings) {
      const mappedResponse = get(response, mapping.responseKey, {});

      if (!isEmpty(mappedResponse) && !isNull(mappedResponse)) {
        let unitPrice = get(mappedResponse, mapping.unitPricePath);
        unitPrice = parseFloat(unitPrice.toString().replace(/₹|\/person/g, ''));
        let refundAmount = null;
        if (mapping.refundAmountPath) {
          refundAmount = get(mappedResponse, mapping.refundAmountPath);
          refundAmount = parseFloat(refundAmount?.toString().replace(/₹|\/person/g, ''));
        }

        if (!isNull(unitPrice)) {
          addOnDetails.push({
            addontype: mapping.addOnType,
            id: mapping.id,
            currency: 'INR',
            includedunits: -1,
            unitprice: unitPrice,
            unittype: 'INR',
            heading: mapping.addOnType,
            selected: null,
            available: true,
            is_preselected: mapping.isPreSelected || false,
            refund_amount: refundAmount,
            probablity: predictionPercentage ?? null,
          });
        }
      }
    }

    setDataInStorage(TRAVELLER_PDT_ADDON_DETAILS, addOnDetails);
    logTravellersPageLoadPdtEvent(
      'TRAVELLERS_LOAD',
      selectedTrainInfo,
      searchContext,
      addOnDetails,
      { ...data, baseFare, ...avlDayList, boardingStation },
    );
  } catch (e) {}
};

export const fetchRailofyDataIfNeeded = (
  hasStatusChangedFCtoTG = false,
  hasStatusChangedTGtoFC = false,
) => {
  return async (dispatch, getState) => {
    removeDataFromStorage(TRAVELLER_PDT_ADDON_DETAILS);
    const state = getState();
    const railofyZcShown = get(state, 'railsListing.railofyZcShown', {});

    if (hasStatusChangedFCtoTG) {
      railofyZcShown.railofyShown = true;
    } else if (hasStatusChangedTGtoFC) {
      railofyZcShown.railofyShown = false;
      railofyZcShown.zcShown = true;
    }

    const railsFCTogether = await getRailsFcTgTogetherValue();
    removeEventFromEvar47or97Variable(`${FCTG_TOGETHER}2`);
    removeEventFromEvar47or97Variable(`${FCTG_TOGETHER}3`);

    // if (railofyLoaded)s {
    //   return;
    // } @todosfctg update this condition
    dispatch(fetchRailofyDataStart());

    const railsConfirmationGuaranteeOption = get(state, 'railsListing.confirmationGuaranteeConfig.railsConfirmationGuaranteeOption', 1);
    const destination = get(state, 'railsListing.selectedTrainInfo.toStnCode', {});
    const source = get(state, 'railsListing.selectedTrainInfo.frmStnCode', {});
    const classCode = get(state, 'railsListing.selectedClassType', 'SL');
    const quota = get(state, 'railsListing.selectedQuota.code', 'GN');
    const doj = get(state, 'railsListing.selectedTrainInfo.departureDateAndTime', undefined);
    const departureTime = get(state, 'railsListing.selectedTrainInfo.departureTime', undefined);
    const trainNumber = get(state, 'railsListing.selectedTrainInfo.trainNumber', '');

    const data = {
      destination,
      source,
      classCode,
      quota,
      doj: doj ? fecha.format(doj, 'YYYYMMDD') : null,
      trainNumber,
      railofyZcShown,
      departureTime,
    };
    const pdtConfig = await getTravellerPdtConfig();
    const isFCPreSelected = await getDataFromAsynStorage(RAILS_IS_PRE_FC_SELECTED);
    // eslint-disable-next-line
    assuredConfirmationConfigurationApi(data, railsConfirmationGuaranteeOption).then((response) => {

      const fcSocialProofing = showSocialProofingFc();
      const tgSocialProofing = showSocialProofingTg();

      if (fcSocialProofing === 1 || tgSocialProofing === 1) {
        const evarValues = response?.omnitureTracking?.evar97 || [];
        const storedData = getDataFromAsynStorage(RAILS_FC_TG_SOCIAL_PROOFING);
        removeEventFromEvar47or97Variable(storedData);

        if (Array.isArray(evarValues) && evarValues.length > 0) {
          evarValues.forEach((evarValue) => {
            if (evarValue) {
              updateEvar47or97Variable(evarValue);
            }
          });
          setDataToAsyncStorage(RAILS_FC_TG_SOCIAL_PROOFING, evarValues);
        }
      }
      if (response?.availabilityDepletionTracking) {
        const availabilityDepletionData = {
          trainNumber,
          classCode,
          quota,
          availabilityDepletionTracking: response?.availabilityDepletionTracking,
        };
        setDataInStorage(AVAILABILITY_DEPLETION_TRACKING_DETAILS, availabilityDepletionData);
      }
      if (
        response?.confirmationGuaranteeResponse !== null ||
        response?.freeCancellationResponse !== null ||
        response?.bnppResponse !== null ||
        response?.tgPlusResponse !== null ||
        response?.availabilityDepletion !== null
      ) {
        updateEvar47or97Variable(`${FCTG_TOGETHER}${railsFCTogether}`);

        if (pdtConfig) {
          dispatch(pdtLoggingTraveller(response, data));
        }
        dispatch(
          fetchRailofyDataSuccess({
            ...response,
            fcUpgradeOption: 1,
            isFCPreSelected,
          }),
        );

        const fcTgData = {
          fcPremium:
            response?.freeCancellationResponse?.cancellationOptions[0]?.insuranceAmount ?? 0,
          tgPremium:
            response?.confirmationGuaranteeResponse?.preferenceInfoRevamped[0]?.premiumPerPerson,
          isFcSelected: isFCPreSelected ?? false,
          isTGSelected: false,
        };
        dispatch(addRecommendedCoupons(fcTgData));

        if (
          !isEmpty(response?.availabilityDepletion) &&
          (getRailsAvailDepletion() === 1 || getRailsAvailDepletion() === 2)
        ) {
          dispatch(changeDisplayAvailDepletionStrip(true));
          switch (getRailsAvailDepletion()) {
            case 1:
              updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_V1);
              break;
            case 2:
              updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_V2);
              break;
            default:
              break;
          }
          if (response?.availabilityDepletion?.isDynamicTrain) {
            updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_DYNAMIC_TRAIN_PERSAUSION);
          }
          if (response?.availabilityDepletion?.inventorySellOutInDays) {
            trackTravellerPageEvent(
              `${RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_SELLOUT}_${response?.availabilityDepletion?.inventorySellOutInDays}`,
            );
          }
        }
        trackGenericEvar47or97Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, '');
        return;
      }
      trackGenericEvar47or97Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, '');
      dispatch(fetchRailofyDataError(response));
    });
  };
};

export const updateRailofyUserResponse = (index, premiumAmount) => {
  return (dispatch, getState) => {
    const {
      railsTraveler: {
        couponData = {},
        railofy: { railofyType },
      },
    } = getState();

    if (
      couponData?.ancillaryDiscountDetails?.ancillaryType === COUPON_TYPE.FC_ONLY &&
      index === 1 &&
      railofyType === RAILOFY_TYPE.FC
    ) {
      showLongToast(`Coupon ${couponData?.couponCode} Removed!`);
      dispatch(setCouponData(undefined));
    }

    dispatch({
      type: ACTION_UPDATE_RAILOFY_USER_RESPONSE,
      index,
      premiumAmount,
    });
    if (railofyType === RAILOFY_TYPE.FC){
      setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, premiumAmount > 0);
      dispatch(setFreeCancellationOption(premiumAmount > 0));
    }
  };
};

export const updateFCTGUserResponse =
  (index, premiumAmount, type, toast) => async (dispatch, getState) => {
    let fcTgObj = {};
    const {
      railsTraveler: {
        couponData,
        railofy: {
          isTGSelected: cnfmGuaranteeOptFlag,
          isFCSelected: freeCancellationInsuranceOptedFlag,
          fcTgSelectionType,
          isFcDiscounted,
          isTgDiscounted,
          isTGPlusSelected: isTGPlusSelectedVal,
        },
      } = {},
    } = getState();
    const tgVersion = getRailsTGVersion();
    const fcTgTogetheMultiSelect = fcTgSelectionType === MULTI_SELECT;
    const isFCPreSelected = await getDataFromAsynStorage(RAILS_IS_PRE_FC_SELECTED);
    fcTgObj.isFCPreSelected = isFCPreSelected;
    switch (type) {
      case RAILOFY_TYPE.TG:
        fcTgObj = {
          cnfmGuaranteeOpted: premiumAmount > 0,
          isTGSelected: premiumAmount > 0,
          isFCSelected: false,
          freeCancellationInsuranceOpted: false,
          isTGPlusSelected: false,
          railofyType: RAILOFY_TYPE.TG,
          tgType: getTgType(RAILOFY_TYPE.TG, tgVersion),
        };
        setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, false);
        if (fcTgTogetheMultiSelect) {
          fcTgObj.freeCancellationInsuranceOpted = freeCancellationInsuranceOptedFlag;
          fcTgObj.isFCSelected = freeCancellationInsuranceOptedFlag;
          fcTgObj.isFcDiscounted = fcTgObj.cnfmGuaranteeOpted && !isTgDiscounted;
          fcTgObj.isTgDiscounted =
            fcTgObj.freeCancellationInsuranceOpted && !fcTgObj.isFcDiscounted;
        }
        dispatch(setFreeCancellationOption(false));
        break;

      case RAILOFY_TYPE.FC:

        fcTgObj = {
          freeCancellationInsuranceOpted: premiumAmount > 0,
          isFCSelected: premiumAmount > 0,
          isTGSelected: false,
          cnfmGuaranteeOpted: false,
          isTGPlusSelected: false,
          railofyType: RAILOFY_TYPE.FC,
          tgType: getTgType(RAILOFY_TYPE.FC, tgVersion),
        };
        setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, premiumAmount > 0);
        if (fcTgTogetheMultiSelect) {
          fcTgObj.cnfmGuaranteeOpted = cnfmGuaranteeOptFlag;
          fcTgObj.isTGSelected = cnfmGuaranteeOptFlag;
          fcTgObj.isTgDiscounted = fcTgObj.freeCancellationInsuranceOpted && !isFcDiscounted;
          fcTgObj.isFcDiscounted = fcTgObj.cnfmGuaranteeOpted && !fcTgObj.isTgDiscounted;
          fcTgObj.isTGPlusSelected = isTGPlusSelectedVal;
        }
        dispatch(setFreeCancellationOption(premiumAmount > 0));
        break;

      case RAILOFY_TYPE.TGP:
        fcTgObj = {
          cnfmGuaranteeOpted: false,
          isTGSelected: false,
          isFCSelected: false,
          freeCancellationInsuranceOpted: false,
          isTGPlusSelected: premiumAmount > 0,
          railofyType: RAILOFY_TYPE.TGP,
          tgType: getTgType(RAILOFY_TYPE.TGP, tgVersion),
        };
        if (fcTgTogetheMultiSelect) {
          fcTgObj.freeCancellationInsuranceOpted = freeCancellationInsuranceOptedFlag;
          fcTgObj.isFCSelected = freeCancellationInsuranceOptedFlag;
          fcTgObj.isFcDiscounted = false;
          fcTgObj.isTgDiscounted =
            fcTgObj.freeCancellationInsuranceOpted && !fcTgObj.isFcDiscounted;
        }
        setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, false);
        dispatch(setFreeCancellationOption(false));
        break;

      case RAILOFY_TYPE.TGPV3:
        let isTGPlusSelected = index === 0;
        let isTGSelected = !isTGPlusSelected;
        let railofyType = index === 1 ? RAILOFY_TYPE.TG : RAILOFY_TYPE.TGP;
        let cnfmGuaranteeOpted = index === 1;
        fcTgObj = {
          cnfmGuaranteeOpted,
          isFCSelected: false,
          freeCancellationInsuranceOpted: false,
          isTGSelected,
          isTGPlusSelected,
          railofyType,
          tgType: getTgType(railofyType, tgVersion), // have to fix it
        };
        setDataToAsyncStorage(RAILS_IS_PRE_FC_SELECTED, false);
        dispatch(setFreeCancellationOption(false));
        break;

    }
    const ancillaryType = couponData?.ancillaryDiscountDetails?.ancillaryType;
    if (ancillaryType) {
      const shouldRemoveCoupon =
        ancillaryType === COUPON_TYPE.FC_TG_TOGETHER ||
        (ancillaryType === COUPON_TYPE.FC_ONLY && index === 1 && type === RAILOFY_TYPE.FC) ||
        (ancillaryType === COUPON_TYPE.TG_ONLY &&
          index === 1 &&
          (type === RAILOFY_TYPE.TG || type === RAILOFY_TYPE.TGP)) ||
        !fcTgTogetheMultiSelect;
      if (shouldRemoveCoupon) {
        showLongToast(`Coupon ${couponData?.couponCode} Removed!`);
        dispatch(setCouponData(undefined));
      }
    }
    const toastChecker =
      toast &&
      premiumAmount > 0 &&
      (fcTgObj?.cnfmGuaranteeOpted ||
        fcTgObj?.freeCancellationInsuranceOpted ||
        fcTgObj?.isTGPlusSelected);
    if (toastChecker && !fcTgTogetheMultiSelect) {
      showLongToast(toast);
    }

    dispatch({
      type: ACTION_UPDATE_FC_TG_USER_RESPONSE,
      data: {
        hasError: false,
        selectedIndex: index,
        premiumAmount,
        ...fcTgObj,
      },
    });

};

export const updateFcSelection = (index, premiumAmount) => {
  return (dispatch) => {
    // dispatch(updateRailofyUserResponse(index, premiumAmount));
    dispatch(updateFCTGUserResponse(index, premiumAmount, RAILOFY_TYPE.FC));
    dispatch(closeFcBottomSheet(true));
    dispatch(closeTgBottomSheet(true));
  };
};

export const updateTgSelection = (index, premiumAmount) => {
  return (dispatch) => {
    dispatch(updateFCTGUserResponse(index, premiumAmount, RAILOFY_TYPE.TG));
    dispatch(closeTgBottomSheet(true));
    dispatch(closeFcBottomSheet(true));
  };
};

export const displayFcBottomSheet = () => ({
  type: ACTION_FREE_CANCELLATION_ALERT,
  data: {
    displayFcBottomSheet: true,
  },
});

export const closeFcBottomSheet = (bottomSheetCtaClicked = false) => ({
  type: ACTION_FREE_CANCELLATION_ALERT,
  data: {
    displayFcBottomSheet: false,
    bottomSheetCtaClicked,
    },
});

export const displayDownTimeBottomSheet = (val) => ({
  type: ACTION_DOWNTIME_BOTTOMSHEET,
  data: {
    displayDownTimeBottomSheet: val,
  },
});

export const displayErrorBottomSheet = (value) => ({
  type: ACTION_BOOKING_REVIEW_ERROR_FLOW,
  data: {
    errorFlow: value,
  },
});


export const setBottomSheetErrorCode = (value) => ({
  type: ACTION_BOOKING_REVIEW_ERROR_FLOW_CODE,
  data: {
    errorFlowCode: value,
  },
});

export const displayTgBottomSheet = () => ({
  type: ACTION_TG_ALERT,
  data: {
    displayTgBottomSheet: true,
  },
});

export const closeTgBottomSheet = (bottomSheetCtaClicked = false) => ({
  type: ACTION_TG_ALERT,
  data: {
    displayTgBottomSheet: false,
    bottomSheetCtaClicked,
  },
});

export const setBnppSetSelection = (isBnppOpted) => ({
  type: ACTION_BNPP_SET_SELECTION,
  data: {
    isBnppOpted,
  },
});

export const setBnppInteracted = (bnppInteracted) => ({
  type: SET_BNPP_INTERACTED,
  data: {
    bnppInteracted,
  },
});

export const setRailsGuestSelectedTravelersList = (value) => ({
  type: ACTION_SET_RAILS_GUEST_SELECTED_TRAVELERS_LIST,
  data: {
    railsGuestSelectedTravelersList: value,
  },
});

export const setRailsGuestTravelersList = (value) => ({
  type: ACTION_SET_RAILS_GUEST_TRAVELERS_LIST,
  data: {
    railsGuestTravelersList: value,
  },
});

export const setRailsLoggedInSelectedTravelersList = (value) => ({
  type: ACTION_SET_RAILS_LOGGED_IN_SELECTED_TRAVELERS_LIST,
  data: {
    railsLoggedInSelectedTravelersList: value,
  },
});

export const setRailsLoggedInTravelersList = (value) => ({
  type: ACTION_SET_RAILS_LOGGED_IN_TRAVELERS_LIST,
  data: {
    railsLoggedInTravelersList: value,
  },
});

export const clearTravelerHeaderText = () => (disptach) => {
  disptach({
    type: ACTION_SET_TRAVELER_HEADER,
    data: {
      header: '',
    },
  });
};

export const displayAvailSubscrnBottomSheet = (value) => (dispatch) => {
  dispatch({
    type: ACTION_ON_AVAIL_SUBSCRN,
    data: {
      showAvailSubscrnBottomSheet: value,
    },
  });
};

export const setAlertLoader = (isLoading) => (dispatch) => {
  dispatch({
    type: SET_ALERT_LOADER,
    data: {
      isAlertLoading: isLoading,
    },
  });
};

const fetchAvailabilitySubscription = (actionType) => async (dispatch, getState) => {
  try {
    const {
      railsTraveler: {
        selectedQuota,
        classValue,
        availabilitySubscription: { isAvailSubscrnEnabled } = {},
        selectedBookingDate: { availablityDate, availablityStatus, availablityType } = {},
      } = {},
      railsListing: {
        selectedTrainInfo: { frmStnCode, toStnCode, departureDateAndTime, trainNumber } = {},
      } = {},
    } = getState() || {};
    const body = {
      srcStation: frmStnCode,
      dstnStation: toStnCode,
      departureDate: fecha.format(departureDateAndTime, YEAR_MONTH_DATE_FORMAT),
      trainNumber,
      journeyClass: classValue?.code,
      quota: selectedQuota?.code,
      availabilityDetails: {
        availablityDate,
        availablityStatus,
        availablityType,
      },
      ...(actionType === AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES.UPDATE && {
        subscribe: !isAvailSubscrnEnabled,
      }),
    };
    const response = await fetch2(
      actionType === AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES.INITIALISE
        ? railsConfig.getAvailabilitySubscrnStatus
        : railsConfig.updateAvailabilitySubscrnStatus,
      {
        method: 'POST',
        headers: getDefaultPostHeaders(),
        body: JSON.stringify(body),
      },
    );
    const res = await response.json();
    if (res?.error?.errorMessage && actionType === AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES.UPDATE) {
      showShortToast(res?.error?.errorMessage);
      trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_NOT_SHOWN);
    } else if (actionType === AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES.INITIALISE) {
      dispatch({
        type: ACTION_ON_AVAIL_SUBSCRN,
        data: {
          isAvailSubscrnEnabled: res?.subscriptionDetails?.subscribed || false,
          showSubscriptionWidget: res?.showSubscriptionWidget || false,
        },
      });
      if (!res?.showSubscriptionWidget) {
        trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_NOT_SHOWN);
      } else if (getRailsAvailabilitySubscription() === 1) {
        trackTravellerPageEvent(`${RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_SHOWN}v1`);
      } else {
        trackTravellerPageEvent(`${RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_SHOWN}v2`);
      }
    } else if (
      actionType === AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES.UPDATE &&
      res?.subscriptionDetails
    ) {
      dispatch({
        type: ACTION_ON_AVAIL_SUBSCRN,
        data: {
          isAvailSubscrnEnabled: res?.subscriptionDetails?.subscribed || false,
        },
      });
      if (isAvailSubscrnEnabled) {
        trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_UNSUBSCRIBED);
      } else {
        trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_ALERT_SUBSCRIBED);
      }
      const isSubscribing = !isAvailSubscrnEnabled;
      if (Platform.OS === 'android') {
        const message = isSubscribing
          ? AVAILABILITY_ALERT_SET_SUCCESSFULLY
          : AVAILABILITY_ALERT_REMOVED_SUCCESSFULLY;
        showShortToast(message);
      } else if (Platform.OS === 'ios') {
        Vibration.vibrate([0, 50]);
      }
    }
  } catch (e) {
    showShortToast(_label('something_went_wrong_try_again_later'));
    console.error('Error in fetchAvailabilitySubscription : ', e);
  }
};

export const getAvailabilitySubscription = () => async (dispatch) => {
  dispatch(fetchAvailabilitySubscription(AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES.INITIALISE));
};

export const updateAvailabilitySubscription = () => async (dispatch) => {
  dispatch(fetchAvailabilitySubscription(AVAILABILITY_SUBSCRIPTION_REQUEST_TYPES.UPDATE));
};

export const displayAvailDepletionTravelerBottomSheet = (value) => (dispatch) => {
  dispatch({
    type: ACTION_DISPLAY_AVAIL_DEPLETION_TRAVELER,
    data: {
      displayAvailDepletionBottomSheet: value,
    },
  });
  if (value) {
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.RAILS_AVAIL_DEPLETION_BS_SHOWN);
  }
};

export const changeDisplayAvailDepletionStrip = (value) => (dispatch) => {
  dispatch({
    type: ACTION_DISPLAY_AVAIL_DEPLETION_TRAVELER,
    data: {
      displayAvailDepletionStrip: value,
    },
  });
};

export const setPreferenceToVeg = (value) => (dispatch) => {
  dispatch({
    type: ACTION_SET_VEG_PREFERENCE,
    data: {
      displayVegPreferenceBottomSheet: value,
    },
  });
};

export const toggleInventoryDepletionBottomSheet = (isVisible, trainNumber = null) => (dispatch) => {
  dispatch({
    type: ACTION_TOGGLE_INVENTORY_DEPLETION_BOTTOMSHEET,
    data: {
      isInventoryDepletionBottomSheetVisible: isVisible,
    },
    trainNumber: trainNumber
  });
};
