import { Dimensions, Platform } from 'react-native';
import fecha from 'fecha';

export const HOLIDAY_LIST = 'https://rails-cbe.goibibo.com/v1/calendar/availability_calendar/__source__/__destination__?from_date=__fromDate__&to_date=__toDate__&flavour=mweb';
const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const MONTHS = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

export const ARP_DAY = 122;
const deviceSize = (Dimensions && Dimensions.get('window')) || {};
const os = Platform.OS;

export const width = deviceSize.width || '100vw';
export const height = deviceSize.height || '100vh';
export const realWidth = height > width ? width : height;

export function addDays(days = 1, theDate) {
  const newDate = new Date();
  newDate.setHours(0, 0, 0);
  const changeDate = theDate || newDate;
  return new Date(changeDate.getTime() + (days * 24 * 60 * 60 * 1000));
}

export const sf = (font = 14) => {
  if (typeof font !== 'number') {
    return font;
  }
  /* Scale Font */
  if (os === 'mobile') {
    return Math.round(font * 0.65);
  }
  if (realWidth <= 320) {
    return Math.round(font * 0.60);
  }

  if (realWidth < 768) {
    return Math.round(font * 0.65);
  }
  // const scaledFontSize = Math.round((font * realWidth) / 768);
  return font;
};

export const getDateFormat = (date: Date,formatTypeString: string) => {
  const year = date.getFullYear();
  const month =  date.getMonth();
  const _date = date.getDate();
  let builtDate = '';
  if (formatTypeString === 'YYYY-MM-DD'){
    builtDate += year;
    if (month + 1 <= 9 ){builtDate = builtDate + '-' + ('0' + (month + 1));}
    else {builtDate = builtDate + '-' + (month + 1);}
    if (_date <= 9 ){builtDate = builtDate + '-' + ('0' + _date);}
    else {builtDate = builtDate + '-' + _date;}
  }
  else if (formatTypeString === 'D MMM ddd, YY'){
    const day = DAYS[date.getDay()];
    const formatedYr = year.toString().slice(2);
    builtDate = `${_date} ${MONTHS[month]} ${day}, ${formatedYr}`;
  }
  else if (formatTypeString === 'YYYYMMDD'){
    builtDate += year;
    if (month + 1 <= 9 ){builtDate = builtDate + ('0' + (month + 1));}
    else {builtDate = builtDate + (month + 1);}
    if (_date <= 9 ){builtDate = builtDate + ('0' + _date);}
    else {builtDate = builtDate + _date;}
  }
  return builtDate;
};

export function getDates(startDate, endDate) {
  const dates = [];
  const stDate = startDate.replace(/(\d\d\d\d)(\d\d)(\d\d)/g, '$1-$2-$3');
  const enDate = endDate.replace(/(\d\d\d\d)(\d\d)(\d\d)/g, '$1-$2-$3');
  let currentDate = addDays(1, new Date(stDate));
  while (currentDate < new Date(enDate)) {
    dates.push(getDateFormat(currentDate, 'YYYYMMDD'));
    currentDate = addDays(1, currentDate);
  }
  return dates;
}

export function getMonths(numberOfDaysToAdd) {
  const value = [];
  const today = new Date();
  const minimumDate = new Date();
  const maximumDate = addDays(
    numberOfDaysToAdd === -1 ? 365 : numberOfDaysToAdd,
    today,
  );
  let minMonth = minimumDate.getMonth();
  let minYear = minimumDate.getFullYear();
  const maxMonth = maximumDate.getMonth();
  const maxYear = maximumDate.getFullYear();
  do {
    value.push({ month: minMonth, year: minYear });
    if (minMonth === maxMonth && minYear === maxYear) {
      break;
    }
    if (minMonth === 11) {
      minMonth = 0;
      minYear += 1;
    } else {
      minMonth += 1;
    }
  } while (true);
  return value;
}

export function getFromToDate() {
  const today = new Date();
  const _today = new Date();
  const arpDay = new Date(_today.setDate(_today.getDate() + ARP_DAY));
  const fromDate = fecha.format(today, 'YYYYMMDD');
  const toDate = fecha.format(arpDay, 'YYYYMMDD');
  return {
    fromDate: fromDate,
    toDate: toDate,
    fromDateObj: today.setHours(0, 0, 0, 0),
    toDateObj: arpDay.setHours(0, 0, 0, 0),
  };
}
