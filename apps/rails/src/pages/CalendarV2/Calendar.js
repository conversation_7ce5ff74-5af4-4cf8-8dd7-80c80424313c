import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { CalendarCommon } from '@RN_UI_Lib/mmt-grounds';
import fetch2 from 'apps/rails/src/fetch2';
import { getDateFormat, getDates, width, sf, getMonths, getFromToDate, ARP_DAY, HOLIDAY_LIST } from './calendar.utils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import fecha from 'fecha';
import PropTypes from 'prop-types';

const CalLegendComp = () => (
  <View style={styles.avlLgndWrp}>
      <View style={styles.lgndItm}>
          <View style={[styles.lgndDot,styles.lowAvlDot]}/>
          <Text style={styles.lgndTxt}>Low availability</Text>
      </View>
      <View style={styles.lgndItm}>
          <View style={[styles.lgndDot,styles.medAvlDot]}/>
          <Text style={styles.lgndTxt}>Medium availability</Text>
      </View>
      <View style={styles.lgndItm}>
          <View style={[styles.lgndDot,styles.highAvlDot]}/>
          <Text style={styles.lgndTxt}>High availability</Text>
      </View>
  </View>
);

const CalendarWeekHeader = ({ WeekHeader = [], WeekEndHeader = [] }) => (
  <View style={styles.calHead}>
    {WeekHeader.map((item) => (
      <View key={'weekday'} style={styles.calHeadCell}>
        <Text style={styles.weekname}>{item}</Text>
      </View>
    )) || []}
    {WeekEndHeader.map((item) => (
      <View key={'weekend'} style={styles.calHeadCell}>
        <Text style={styles.weeknameColor}>{item}</Text>
      </View>
    )) || []}
  </View>
);

const DayComp = ({
  date, dateStr, onselectionchange, disabled, mode, checkin, checkout, isHoliday, avlStatus,
}) => {
  const selected = getDateFormat(checkin, 'YYYYMMDD');
  const nextSelected = getDateFormat(checkout, 'YYYYMMDD');
  const getDiffDate = getDates(selected, nextSelected);
  const isDate = getDiffDate.indexOf(dateStr) !== -1;
  const wrapper = [styles.calDateViewWrap,...(dateStr === '' ? [styles.greyBox] : [])];
  const dateViewWrapper = [styles.calDateView];
  const calDateWrapper = [styles.calDate];
  const holidayTxtStyles = [styles.holidayName];
  const naTxtStyles = [styles.naTxt];
  switch (true) {
    case (selected === dateStr): {
      const wr = mode === 'checkout' ? {} : {};
      wrapper.push(wr);
      dateViewWrapper.push(styles.selDateView);
      calDateWrapper.push(styles.selDateColor);
      holidayTxtStyles.push(styles.holidayNameActv);
      naTxtStyles.push(styles.naTxtActv);
      break;
    }
    case (mode !== 'checkout' && nextSelected === dateStr): {
      wrapper.push(styles.dateRangeEnd);
      dateViewWrapper.push(styles.selDateView);
      calDateWrapper.push(styles.selDateColor);
      break;
    } case isDate: {
      wrapper.push(styles.dateRange);
      break;
    }
    case disabled: {
      calDateWrapper.push(styles.prevDate);
      break;
    }
  }
  const avlDot = [styles.avlDotStyles, (avlStatus && date ? { backgroundColor: avlStatus } : {})];
  return (
    <TouchableOpacity
      activeOpacity={disabled ? 1 : 0.5}
      onPress={() => {
        if (!disabled) {
          onselectionchange({ dateStr });
        }
      }}
    >
      <View style={wrapper}>
        <View style={dateViewWrapper}>
          {
            (isHoliday) && (
              <Text style={holidayTxtStyles} numberOfLines={1}>{isHoliday}</Text>
            )
          }
          <Text style={calDateWrapper}>{date}</Text>
          {
              avlStatus === 'NA' && (
                  <Text style={naTxtStyles}>--</Text>
              )
          }
          {
              avlStatus !== 'NA' && (
                  <View style={avlDot}/>
              )
          }
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  avlLgndWrp: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  lgndItm: {
      flexDirection: 'row',
      alignItems: 'center',
  },
  lgndDot: {
      width: 4,
      height: 4,
      borderRadius: 2,
      marginRight: 4,
  },
  lowAvlDot: {
      backgroundColor: colors.red24,
  },
  medAvlDot: {
      backgroundColor: colors.goldenYellow16,
  },
  highAvlDot: {
      backgroundColor: colors.lightGreen23,
  },
  lgndTxt: {
      fontSize: 11,
      color: colors.blackRussian,
  },
  calDateViewWrap: {
    width: (width  - 7) / 7,
    height: width / 7,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    flex: 1,
    marginRight: 1,
    marginBottom: 1,
  },
  greyBox: {
      backgroundColor: colors.grey11,
  },
  calDateView: {
    width: width / 7 - sf(2),
    height: width / 7 - sf(2),
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    margin: sf(5),
    borderRadius: sf(35),
    padding: 3,
  },
  selDateView: {
    backgroundColor: colors.azure,
    width: width / 7 - sf(2),
    height: width / 7 - sf(2),
    borderRadius: 4,
  },
  dateRange: {
    backgroundColor: colors.azureBlue8,
  },
  dateRangeEnd: {
    backgroundColor: colors.azureBlue7,
    borderTopRightRadius: sf(100),
    borderBottomRightRadius: sf(100),
  },
  calDate: {
    textAlign: 'center',
    color: colors.blackRussian,
    fontSize: 12,
  },
  prevDate: {
    color: colors.gradientLightGrey,
  },
  holidayName: {
    fontSize: 8,
    color: colors.gradientLightGrey,
    position: 'absolute',
    top: 2,
  },
  holidayNameActv: {
      color: colors.white,
  },
  selDateColor: {
    color: colors.white,
    fontWeight: 'bold',
  },
  avlDotStyles: {
    width: 4,
    height: 4,
    borderRadius: 4,
    position: 'absolute',
    bottom: 10,
  },
  naTxt: {
      color: colors.lighterBlue2,
      position: 'absolute',
      bottom: 4,
  },
  naTxtActv: {
    color: colors.white,
  },
  calHead: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    paddingVertical: 9,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  calHeadCell: {
    width: width / 7,
  },
  weekname: {
    fontSize: 10,
    color: colors.gradientLightGrey,
    textAlign: 'center',
    fontWeight: '700',
  },
  weeknameColor: {
    fontSize: 10,
    color: colors.gradientLightGrey,
    textAlign: 'center',
    fontWeight: '700',
  },
});


function Calendar (props) {
  const { source, destination, departureDate, onDateClick } = props;

  const [calendarData, setCalendarData] = useState({
    holidays: {},
    availability: {},
  });

  useEffect(() => {
    if (source && destination) {
      const { fromDate, toDate } = getFromToDate();
      const url = HOLIDAY_LIST
        .replace('__source__', source)
        .replace('__destination__', destination)
        .replace('__fromDate__', fromDate)
        .replace('__toDate__', toDate);
      fetch2(url)
        .then(res => res.json())
        .then(data => updateCalendar(data))
        .catch(err => console.error(err.message));
    }
  }, [source, destination]);

  const updateCalendar = (data) => {
    if (data.code === '200') {
      console.log(data);
      const holidays = data.response.holiday_set;
      const availability = data.response.date_set;
      setCalendarData({
        ...calendarData,
        holidays,
        availability,
      });
    }
  };

  const { toDateObj, fromDateObj } = getFromToDate();

  return (
    <CalendarCommon
      value={getMonths(ARP_DAY)}
      checkInDate={departureDate}
      onlyCheckin={true}
      holidaysData={calendarData.holidays}
      onDateSelection={(selectedDate) => onDateClick(selectedDate)}
      renderWeekHeader={() => <CalendarWeekHeader WeekHeader={['MON', 'TUE', 'WED', 'THU', 'FRI']} WeekEndHeader={['SAT', 'SUN']}/>}
      renderLegendItem={() => <CalLegendComp />}
      maxDate
      renderDayItem={({
          date,
          dateStr,
          onselectionchange,
          checkin,
          checkout,
          isHoliday,
          dateInt,
      }) => (
          <DayComp
              date={date}
              dateStr={dateStr}
              disabled={(() => {
                if (date) {
                  const currentDate = fecha.parse(dateStr, 'YYYYMMDD').setHours(0, 0, 0, 0);
                  return !(currentDate >= fromDateObj && currentDate <= toDateObj);
                }
                return true;
              })()}
              onselectionchange={onselectionchange}
              checkin={checkin}
              checkout={checkout}
              isHoliday={isHoliday}
              avlStatus={calendarData.availability[dateInt]}
      />
      )}
    />
  );
}

DayComp.propTypes = {
  source: PropTypes.string,
  destination: PropTypes.string,
  departureDate: PropTypes.instanceOf(Date),
  onDateClick: PropTypes.func,
  avlStatus: PropTypes.string,
  isHoliday: PropTypes.bool,
  checkin: PropTypes.instanceOf(Date),
  checkout: PropTypes.instanceOf(Date),
  mode: PropTypes.string,
  disabled: PropTypes.bool,
  onselectionchange: PropTypes.func,
  dateStr: PropTypes.string,
  date: PropTypes.string,
};

Calendar.propTypes = {
  source: PropTypes.string,
  destination: PropTypes.string,
  departureDate: PropTypes.instanceOf(Date),
  onDateClick: PropTypes.func,
};

CalendarWeekHeader.propTypes = {
  WeekHeader: PropTypes.array,
  WeekEndHeader: PropTypes.array,
};



export default Calendar;
