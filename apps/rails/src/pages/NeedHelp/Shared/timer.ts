import { getDataFromAsynStorage, setDataToAsyncStorage } from '../../../Utils/RailsConstant';
import { types } from '../Interfaces';
import { getMmtId } from '../../PostPayment/PostPaymentUtils';

const getKey = (bookingId: string): string => `TIMER_${bookingId}`;

export const updateTimer = async () : Promise<void> => {
  const bookingId = await getMmtId();
  try {
    const timer = await getDataFromAsynStorage(getKey(bookingId));
    if (!timer) {
      const time = Date.now();
      setDataToAsyncStorage(getKey(bookingId), JSON.stringify(time));
    }
  } catch (error) {
    console.error('Error while updating timer');
  }
};

export const getLatestTimer = async (): Promise<types.TimerResponse> => {
  const bookingId = await getMmtId();
  try {
    const time = await getDataFromAsynStorage(getKey(bookingId));
    if (time) {
      const storedTime = JSON.parse(time);
      return {
        success: true,
        time: storedTime,
      };
    }
    throw new Error('Time has not been set yet.');
  } catch (error: unknown) {
    console.trace('Error while fetching timer', error.message);
    return {
      success: false,
      time: Date.now(),
    };
  }
};
/* eslint-disable */
export const clearTimer = async (): Promise<void> => {
  try {
    const bookingId = await getMmtId();
    if (typeof window === 'object') {
      localStorage.removeItem(getKey(bookingId));
      sessionStorage.removeItem(getKey(bookingId));
    }
  } catch (error) {
    console.trace('Could not delete the timer detail', error.message);
  }
};
