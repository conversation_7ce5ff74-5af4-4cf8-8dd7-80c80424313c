import { types } from '../Interfaces';
import { LABELS } from './labels';

export const ROUTE_SOURCE = 'needHelp';

export const PAGE_ID = {
  BOOKING_DETAILS: 'bookingDetails',
  FORGOT_IRCTC_PASSWORD: 'getNewPassword',
  DIFFERENT_IRCTC_USERNAME: 'getNewUsername',
  CAPTCHA: 'captcha',
  CANCEL_BOOKING: 'cancelBooking',
  WRONG_CREDENTIALS: 'wrongCredentials',
  OTHER_ISSUES: 'otherIssues',
};

export const HELP_OPTION_LIST: types.HelpOption[] = [
  {
    pageId: PAGE_ID.FORGOT_IRCTC_PASSWORD,
    pageTitle: LABELS.FORGOT_IRCTC_PASSWORD,
    pageType: 'linked',
    linkedPage: {
      linkHref: 'railsForgotIrctcPassword',
      linkTitle: LABELS.GET_NEW_PASSWORD,
      extraParams: {
        from: ROUTE_SOURCE,
      },
    },
    isActive: false,
    eventName: '',
  },
  {
    pageId: PAGE_ID.DIFFERENT_IRCTC_USERNAME,
    pageTitle: LABELS.DIFFERENT_IRCTC_USERNAME,
    pageType: 'accordion',
    isActive: false,
    eventName: 'rail_need_help_changeusername_clicked',
  },
  {
    pageId: PAGE_ID.CAPTCHA,
    pageTitle: LABELS.CAPTCHA,
    pageType: 'accordion',
    isActive: false,
    eventName: 'rail_need_help_captcha_dropdown_clicked',
  },
  {
    pageId: PAGE_ID.CANCEL_BOOKING,
    pageTitle: LABELS.CANCEL_BOOKING,
    pageType: 'accordion',
    isActive: false,
    eventName: 'rail_need_help_cancel_dropdown_clicked',
  },
  {
    pageId: PAGE_ID.WRONG_CREDENTIALS,
    pageTitle: LABELS.WRONG_CREDENTIALS,
    pageType: 'accordion',
    isActive: false,
    eventName: 'rail_need_help_wrongcredentials_dropdown_clicked',
  },
  {
    pageId: PAGE_ID.OTHER_ISSUES,
    pageTitle: LABELS.OTHER_ISSUES,
    pageType: 'accordion',
    isActive: false,
    eventName: 'rail_need_help_feedback_dropdown_clicked',
  },
];

export const STATUS = {
  N: 'NOT STARTED',
  D: 'DONE',
  S: 'STARTED',
};

export const TEXT_AREA_QUESTIONS = ['OTHER_ISSUES_CAN_01'];

export const BUTTON_SHAPE = {
  CONTINUE_BOOKING: 'oval',
};

export const DATE_FORMAT = {
  SHORT: 'DD MMM',
  FULL: 'DD/MM/YYYY',
};

export const NEED_HELP_PAGE_TRACKING_ID = 'rail_need_help_landing';

export const CTA_TRACKING_ID = {
  CONTINUE_BOOKING: 'rail_need_help_continue_booking_clicked',
  CHANGE_USERNAME: 'rail_need_help_landing_backbtn_clicked',
  FORGOT_PASSWORD: 'rail_need_help_forgotpassword_submit_clicked',
  FEEDBACK: 'rail_need_help_submit_feedbackbtn_clicked',
  FORGOT_USERNAME: 'rail_need_help_forgotusername_verifybtn_clicked',
  CANCEL_BOOKING: 'rail_need_help_cancelbookingbtn_clicked',
};

export const NEED_HELP_ROUTE_SOURCE = ROUTE_SOURCE;


