import { useState, useEffect } from 'react';

import http from '../../../http/http';
import { getMmtId } from '../../PostPayment/PostPaymentUtils';
import { API_MAPPING } from './';

const getUrl = async (pageId: string, slashParam?: string) => {
  if (slashParam === 'bookingId') {
    const mmtId = await getMmtId();
    return `${API_MAPPING.GET[pageId]}${mmtId}`;
  }
  return API_MAPPING.GET[pageId];
};
const cachedResponse: unknown = {};

export const useFetchData = (pageId: string, slashParam?: string) => {
  const [isLoading, setLoading] = useState(true);
  const [response, setResponse] = useState(null);

  useEffect(() => {
    async function getData() {
      const url = await getUrl(pageId, slashParam);
      if (cachedResponse[url]) {
        setResponse(cachedResponse[url]);
        setLoading(false);
      } else {
        const data = await http.get(url);
        cachedResponse[url] = data;
        setResponse(data);
        setLoading(false);
      }
    }
    getData();
  }, [pageId, slashParam]);
  return { response, isLoading };
};
