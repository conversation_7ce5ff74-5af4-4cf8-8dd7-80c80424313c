import get from 'lodash/get';
import { getCommonHeaders } from '@mmt/legacy-commons/Helpers/genericHelper';
import { getDataFromAsynStorage, RAILS_MMT_ID } from '../../../Utils/RailsConstant';
import fetch2 from 'apps/rails/src/fetch2';
import { types } from '../Interfaces';
import { API_MAPPING } from './apiMapping';
import { config } from './';
import { _label } from '../../../vernacular/AppLanguage';

import { trackClickEventProp61, trackPageVisits } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

export const getBookingId = async () => {
  return await getDataFromAsynStorage(RAILS_MMT_ID);
};

export const hasLinkedPage = (option: types.HelpOption): boolean => {
 const hasLinkedPage = option.pageType === 'linked';
 return hasLinkedPage;
};
export const getLinkTitle = (option: types.HelpOption): string =>  {
  const linkTitle = get(option, 'linkedPage.linkTitle', '');
  return linkTitle;
};

export const getLinkProps = (option: types.HelpOption): object => {
  return {
    ...option.linkedPage,
  };
};

export const submitData = async (pageId: string, feedbackText: string) => {
  const url = API_MAPPING.POST[pageId];
  const mmtId = await getBookingId();
  const response = await postCall(url, { feedbackText, mmtId });
  if (response.status === 200) {
    return {
      status: 'success',
      message: _label('thank_you_for_feedback'),
    };
  } else {
    return {
      status: 'success',
      message: _label('error_in_feedback'),
    };
  }
};

export const postCall = async (apiUrl, params = {}) => {
  const baseHeaders = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  };
  const commonHeader = await getCommonHeaders(baseHeaders);
  const body = JSON.stringify(params);
  const response = await fetch2(apiUrl, {
    method: 'POST',
    headers: commonHeader,
    body,
  });
  return response;
};

export const isTextAreaQuestion = (questionId: string) => {
  return config.TEXT_AREA_QUESTIONS.indexOf(questionId) > -1;
};

const fakeSubmit = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 'success',
        message: _label('get_password_success'),
      });
    }, 1500);
  });
};

export const changePassword = async (): Promise<unknown> => {
  return await fakeSubmit();
};

export const stripTrailingSpaces = (str: string): string => {
  return str.replace(/ +$/, '');
};

export const trackClick = (id:string): void => {
  trackClickEventProp61(config.NEED_HELP_PAGE_TRACKING_ID, id);
};

export const trackPage = (): void => {
  trackPageVisits(config.NEED_HELP_PAGE_TRACKING_ID, {});
};

