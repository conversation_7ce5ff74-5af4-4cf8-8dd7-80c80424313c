interface OptionsTextListEntity {
  id: string;
  text: string;
}

export interface CancelBooking {
  headerText: string;
  bodyTextList?: (string)[] | null;
  optionsTextList?: (OptionsTextListEntity)[] | null;
  buttonText: string;
}

export interface WrongCredentials {
  headerText: string;
  bodyList?: (string)[] | null;
  buttonText: string;
}

export interface OtherIssues {
  headerText: string;
  buttonText: string;
}

interface AwarenessDetails {
  awarenessText: string;
  awarenessHeader: string;
}

interface FromStnOrBoardingStnOrResvnUptoStnOrDestStn {
  code: string;
  name: string;
  cityCode: string;
  primaryFlag: number;
  active: number;
}

interface PsgnDetailListEntity {
  bookingBerthCode?: null;
  bookingBerthNo: number;
  bookingCoachId?: null;
  bookingStatus?: null;
  bookingStatusIndex: number;
  concessionOpted: boolean;
  currentBerthCode?: null;
  currentBerthNo: number;
  currentStatus?: null;
  currentStatusIndex: number;
  currentCoachId?: null;
  passengerAge: number;
  passengerBedrollChoice: boolean;
  passengerBerthChoice?: null;
  passengerConcession?: null;
  passengerGender: string;
  passengerIcardFlag: boolean;
  passengerName: string;
  passengerNetFare: number;
  passengerSerialNumber: number;
  childBerthFlag: boolean;
  passengerNationality: string;
  passengerFoodChoice?: null;
}

interface BookingConfig {
  applicableBerthTypes?: (string)[] | null;
  bedRollFlagEnabled: boolean;
  childBerthMandatory: boolean;
  foodChoiceEnabled: boolean;
  idRequired: boolean;
  lowerBerthApplicable: boolean;
  maxARPDays: number;
  maxChildAge: number;
  maxIdCardLength: number;
  maxInfants: number;
  maxNameLength: number;
  maxPassengerAge: number;
  maxPassengers: number;
  maxRetentionDays: number;
  minIdCardLength: number;
  minNameLength: number;
  minPassengerAge: number;
  newTimeTable: boolean;
  seniorCitizenApplicable: boolean;
  srctnwAge: number;
  srctznAge: number;
  srctzTAge: number;
  validIdCardTypes?: (string)[] | null;
}

export interface BookingDetails {
  email: string;
  mobile: string;
  journeyQuota: string;
  journeyClass: string;
  irctcServiceTax: number;
  couponCode?: null;
  couponDiscount: number;
  pgCharge: number;
  created: string;
  updated: string;
  fromStn: FromStnOrBoardingStnOrResvnUptoStnOrDestStn;
  boardingStn: FromStnOrBoardingStnOrResvnUptoStnOrDestStn;
  resvnUptoStn: FromStnOrBoardingStnOrResvnUptoStnOrDestStn;
  destStn: FromStnOrBoardingStnOrResvnUptoStnOrDestStn;
  distance: number;
  numberOfAdults?: null;
  numberOfChildren?: null;
  numberOfPassenger?: null;
  lang: string;
  boardingDate: string;
  destArrvDate: string;
  journeyDate: string;
  bookingDate?: null;
  departureTime: string;
  arrivalTime: string;
  trainNumber: string;
  trainName: string;
  reservationId?: null;
  reservationCharge: number;
  superfastCharge: number;
  tatkalFare: number;
  serviceTax: number;
  mmtBaseFare: number;
  irctcServiceCharge: number;
  mmtServiceFee: number;
  totalCollectibleAmount: number;
  freeCancellationCharge: number;
  psgnDetailList?: (PsgnDetailListEntity)[] | null;
  gstFlag?: null;
  railBookingStatus: string;
  payStatus: string;
  travelInsuranceOpted: boolean;
  bookMode: string;
  errorMessage?: null;
  bookingConfig: BookingConfig;
  reservationChoice: string;
  autoUpgradationSelected: boolean;
  payInfo?: null;
  hotelCrossSellConfig?: null;
  hotelCrossSellEnabledOnThankYouPage: boolean;
  pnrNumber?: null;
  mmtId: string;
  awarenessDetails: AwarenessDetails;
  activeVoucherResponse?: null;
}
