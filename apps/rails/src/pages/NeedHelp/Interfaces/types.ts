export type PageType = 'accordion' | 'linked';

export interface LinkedPage {
  linkTitle: string;
  linkHref: string;
  extraParams: object | null;
}

export interface HelpOption {
  pageId: string;
  pageTitle: string;
  pageType: PageType;
  linkedPage?: LinkedPage;
  isActive: boolean;
  eventName: string;
}

export interface NeedHelpProps {
  optionList: HelpOption[]
}

export interface TimerResponse {
  success: boolean;
  time: number;
}
