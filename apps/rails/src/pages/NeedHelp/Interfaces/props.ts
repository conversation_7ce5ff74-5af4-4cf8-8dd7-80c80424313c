import { types } from './';

export interface ComponentSwitcher {
  pageId: string;
}

export interface OptionTitle {
  title: string;
  linkHref?: string;
  linkTitle?: string;
  isActive: boolean;
  extraParams?: object | null;
}

export interface OptionWithAccordion {
  option: types.HelpOption;
  updateActiveState: unknown;
}

export interface OptionWithLink {
  option: types.HelpOption;
}

export interface Spacer {
  v: number;
}

export interface TextFeedback {
  buttonText: string;
  placeholder: string;
  callback: unknown;
  showTextInput: boolean;
  pageId: string;
}

export interface FeedbackResponse {
  message: string;
}

export interface CountdownTimer {
  totalTimeInSeconds: number;
  fromTimeInMilliseconds: number;
  buffer: number;
}

export interface PageTitle {
  showTimer: boolean;
  title: string;
}
