import { ImageStyle, TextStyle, ViewStyle } from 'react-native';

export interface NeedHelp {
  pageWrapper: ViewStyle;
  content: ViewStyle;
  bottomCTAWrapper: ViewStyle;
}

export interface OptionTitle {
  accordionTitle: TextStyle;
  optionWrapper: ViewStyle;
  linkWrapper: ViewStyle;
  linkText: TextStyle & ViewStyle;
  activeAccordionTitle: TextStyle;
  activeOptionWrapper: ViewStyle;
}

export interface OptionLinkWrapper {
  optionLinkWrapper: ViewStyle;
}

export interface BookingDetails {
  sectionTitleWrapper: ViewStyle;
  sectionTitle: TextStyle;
}

export interface BookingInfoCard {
  accordionContent: ViewStyle;
  row: ViewStyle;
  headingLevel1: TextStyle;
  headingLevel2: TextStyle;
  headingLevel3: TextStyle;
  lightHeadingLevel1: TextStyle;
  lightHeadingLevel2: TextStyle;
  border: ViewStyle;
}

export interface CancelBooking {
  textInputContainer: ViewStyle;
  mainBodyText: ViewStyle;
}

export interface Loader {
  accordionContent: ViewStyle;
}

export interface OtherIssues {
  textInputContainer: ViewStyle;
  mainBodyText: TextStyle;
}

export interface PageTitle {
  pageTitleWrapper: ViewStyle;
  pageTitle: TextStyle;
}

export interface WhatIsCaptcha {
  mainBodyText: TextStyle;
  helpText: TextStyle;
  captchaImage: ImageStyle;
}

export interface WrongCredentials {
  headerText: TextStyle;
  mainBodyText: TextStyle;
}

export interface FeedbackResponse {
  contentWrapper: ViewStyle;
  mainBodyText: TextStyle;
}

export interface ComponentSwitcher {
  contentWrapper: ViewStyle;
}

export interface CountdownTimer {
  clockImage: ImageStyle;
  timerDisplay: ViewStyle;
  timerDisplayText: TextStyle;
}

export interface ChangeUsername {
  headerText: TextStyle;
  mainBodyText: TextStyle;
  helpText: TextStyle;
}
