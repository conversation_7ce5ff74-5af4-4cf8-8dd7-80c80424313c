import { FlexStyle, ViewStyle, ImageStyle } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

const flexHVCenter: FlexStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

export const pageWrapper: ViewStyle = {
  flex: 1,
};

export const content: ViewStyle = {
  paddingLeft: 18,
  paddingRight: 18,
};

export const pageTitleWrapper: ViewStyle = {
  ...flexHVCenter,
  flexDirection: 'row',
  justifyContent: 'space-between',
  height: 80,
  paddingLeft: 18,
  paddingRight: 18,
};

export const backArrowIcon: ViewStyle = {
  marginRight: 20,
  width: 24,
  height: 24,
};

export const titleWrapper: ViewStyle = {
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
};

export const titleCard: ViewStyle = {
  ...flexHVCenter,
  height: 47,
  marginLeft: 16,
  borderBottomColor: colors.grayBg,
};

export const sectionTitleWrapper: ViewStyle = {
  borderBottomColor: colors.lightGrey,
  borderBottomWidth: 1,
  padding: 16,
};

export const optionWrapper: ViewStyle = {
  borderRadius: 4.5,
  borderWidth: 1,
  backgroundColor: colors.grey13,
  borderColor: colors.lightGrey,
  alignItems: 'center',
};

export const activeOptionWrapper: ViewStyle = {
  borderBottomWidth: 0,
  borderBottomRightRadius: 0,
  borderBottomLeftRadius: 0,
};

export const optionLinkWrapper: ViewStyle = {
  ...optionWrapper,
};

export const linkWrapper: ViewStyle = {
  width: '100%',
  justifyContent: 'space-between',
  position: 'relative',
  flexDirection: 'row',
  marginLeft: 26,
};

export const linkText: ViewStyle = {
  position: 'absolute',
  right: 30,
};

export const accordionContent: ViewStyle = {
  padding: 16,
  backgroundColor: colors.grey13,
  minHeight: 160,
};

export const accordionContentWithBorder: ViewStyle = {
  ...accordionContent,
  borderWidth: 1,
  borderColor: colors.lightGrey,
  borderRadius: 4.5,
};

export const accordionContentWithoutTopBorder: ViewStyle = {
  ...accordionContentWithBorder,
  borderTopWidth: 0,
  borderTopRightRadius: 0,
  borderTopLeftRadius: 0,
};

export const textInputContainer: ViewStyle = {
  borderRadius: 4,
  borderColor: colors.lightGrey,
  borderWidth: 1,
  marginTop: 0,
  backgroundColor: colors.white,
  maxHeight: 137,
  padding: 5,
  marginBottom : 8,
  height: 140,
};

export const captchaImage: ViewStyle = {
  width: 254,
  height: 94,
  marginVertical: 16,
  display: 'flex',
  alignSelf: 'center',
};

export const radioOption: ViewStyle = {
  backgroundColor: colors.white,
  borderColor: colors.lightGrey,
};

export const row: ViewStyle = {
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
};

export const border: ViewStyle = {
  width: 50,
  height: 1,
  borderColor: colors.lightGrey,
  borderWidth: 1,
};

export const bottomCTAWrapper: ViewStyle = {
  height: 74,
  bottom: 0,
  backgroundColor: '#ffffff',
  left: 0,
  right: 0,
  paddingLeft: 18,
  paddingRight: 18,
};

export const shadow: ViewStyle = {
  elevation: 1,
  shadowColor: '#e7e7e7',
  shadowRadius: 7,
};
export const topShadow: ViewStyle = {
  ...shadow,
  shadowOffset: {
    height: 1,
    width: 0,
  },
};

export const bottomShadow: ViewStyle = {
  ...shadow,
  shadowOffset: {
    height: 1,
    width: 0,
  },
};

export const clockImage: ImageStyle = {
  width: 15,
  height: 15,
  alignItems: 'center',
  marginRight: 8,
};

export const timerDisplay: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  height: 36,
  width: 116,
  borderBottomRightRadius: 100,
  borderTopRightRadius: 100,
  paddingLeft: 9,
};
