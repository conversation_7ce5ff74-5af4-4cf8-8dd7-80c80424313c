import { TextStyle } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../../vernacular/VernacularUtils';

export const getPageTitleFontStyle = () => {
  return fontStyle('bold');
};

export const getSectionTitleFontStyle = () => {
  return {...getPageTitleFontStyle()};
};

export const getAccordionTitleFontStyle = () => {
  return fontStyle('regular');
};

export const getLinkTextFontStyle = () => {
    return fontStyle('bold');
};

export const getActiveAccordionTitleFontStyle = () => {
  return fontStyle('bold');
};

export const getMainBodyTextFontStyle = () => {
  return fontStyle('regular');
};

export const getHelpTextFontStyle = () => {
  return {...getMainBodyTextFontStyle()};
};

export const getHeaderTextFontStyle = () => {
  return {...getMainBodyTextFontStyle()};
};

export const getHeadingLevel1FontStyle = () => {
  return fontStyle('black');
};

export const getLightHeadingLevel1FontStyle = () => {
  return fontStyle('regular');
};

export const getHeadingLevel2FontStyle = () => {
  return fontStyle('bold');
};

export const getLightHeadingLevel2FontStyle = () => {
  return fontStyle('regular');
};

export const getHeadingLevel3FontStyle = () => {
  return fontStyle('regular');
};

export const getTimerDisplayTextFontStyle = () => {
  return fontStyle('bold');
};

export const getTransactionExpiredFontStyle = () => {
  return fontStyle('regular');
};

export const pageTitle: TextStyle = {
  fontSize: 18,
  color: colors.defaultTextColor,
};

export const sectionTitle: TextStyle = {
  ...pageTitle,
  color: colors.black,
};

export const accordionTitle: TextStyle = {
  fontSize: 13.5,
  color: colors.black,
};

export const linkText: TextStyle = {
  width: 78,
  color: colors.azure,
  fontSize: 13.5,
};

export const activeAccordionTitle: TextStyle = {
  fontSize: 20,
  color: colors.black,
};

export const mainBodyText: TextStyle = {
  color: colors.black,
  fontSize: 14.2,
};

export const helpText: TextStyle = {
  ...mainBodyText,
  color: colors.lightTextColor,
};

export const radioOption: TextStyle = {
  color: colors.black,
};

export const headerText: TextStyle = {
  ...mainBodyText,
  fontSize: 15,
};

export const headingLevel1: TextStyle = {
  fontSize: 16,
  color: colors.black,
};

export const lightHeadingLevel1: TextStyle = {
  fontSize: 16,
  color: '#9c9c9c',
};

export const headingLevel2: TextStyle = {
  fontSize: 12,
  color: colors.black,
};

export const lightHeadingLevel2: TextStyle = {
  fontSize: 11,
  color: '#9c9c9c',
};

export const headingLevel3: TextStyle = {
  fontSize: 14,
  color: colors.black,
};

export const timerDisplayText: TextStyle = {
  color: colors.white,
  fontSize: 16,
  backgroundColor: 'transparent',
  lineHeight: 12,
};

export const transactionExpired: TextStyle = {
  color: colors.white,
  fontSize: 12,
  lineHeight: 12,
};
