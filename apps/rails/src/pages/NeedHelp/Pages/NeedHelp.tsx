import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { hasLinkedPage } from '../Shared/utils';
import {
  OptionWithAccordion,
  OptionWithLink,
  PageTitle,
  Spacer,
  BookingDetails,
} from '../Components';
import { viewStyle } from '../Styles';
import { styleTypes } from '../Interfaces';
import BtnGeneric from '../../PostPayment/Components/BtnGeneric';
import { Actions } from '../../../navigation';
import { config, LABELS, utils } from '../Shared';

const styles = StyleSheet.create<styleTypes.NeedHelp>({
  pageWrapper: {
    ...viewStyle.pageWrapper,
  },
  content: {
    ...viewStyle.content,
  },
  bottomCTAWrapper: {
    ...viewStyle.bottomCTAWrapper,
    ...viewStyle.topShadow,
  },
});

const NeedHelp: React.FC = () => {
  const [options, updateActiveOption] = React.useState(config.HELP_OPTION_LIST);

  const updateOptions = (pageId: string, isOpen: boolean) => {
    const updatedOptions = options.map(item => {
      if (item.pageId === pageId) {
        return {
          ...item,
          isActive: isOpen,
        };
      }
      return item;
    });
    updateActiveOption(updatedOptions);
  };

  React.useEffect(() => {
      utils.trackPage();
  }, []);

  return (
    <View style={styles.pageWrapper}>
      <PageTitle title={LABELS.NEED_HELP} showTimer />
      <BookingDetails />
      <ScrollView contentContainerStyle={styles.content}>
        {
          options.map(option => {
            return (
              <React.Fragment key={option.pageId}>
                {
                  hasLinkedPage(option)
                  ? <OptionWithLink option={option} key={option.pageId} />
                  : <OptionWithAccordion option={option} updateActiveState={updateOptions} />
                }
                <Spacer v={10} />
              </React.Fragment>
            );
          })
        }
      </ScrollView>
      <View style={styles.bottomCTAWrapper}>
        <BtnGeneric
          clickAction={() => {
            utils.trackClick(config.CTA_TRACKING_ID.CONTINUE_BOOKING);
            Actions.openIrctcPage();
          }}
          btnText={ LABELS.CONTINUE_BOOKING }
          shape={ config.BUTTON_SHAPE.CONTINUE_BOOKING }
        />
      </View>
    </View>
  );
};

export default NeedHelp;
