import React from 'react';
import Accordion from '@mmt/legacy-commons/Common/Components/Accordion';
import { viewStyle } from '../Styles';
import { OptionTitle,ComponentSwitcher } from './';
import { utils } from '../Shared';
import { propTypes } from '../Interfaces';

const OptionWithAccordion: React.FC<propTypes.OptionWithAccordion> = ({
  option,
  updateActiveState,
}): React.ReactElement => {
    return (
    <Accordion
      key={option.pageId}
      headerStyle={{
        ...viewStyle.optionWrapper,
        ...(option.isActive ? viewStyle.activeOptionWrapper : {}),
      }}
      renderHeader={() => (
        <OptionTitle
          title={option.pageTitle}
          linkHref={utils.getLinkTitle(option)}
          isActive={option.isActive}
        />
      )}      renderContent={() => <ComponentSwitcher pageId={option.pageId} /> }
      defaultCollapsed={!option.isActive}
      accordionOpenCallback={() => {
        utils.trackClick(option.eventName);
        updateActiveState(option.pageId, true);
      }}
      accordingCloseCallback={() => {
        updateActiveState(option.pageId, false);
      }}
    />
  );
};

export default OptionWithAccordion;
