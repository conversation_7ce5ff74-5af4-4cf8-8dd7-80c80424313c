import React from 'react';
import { View, StyleSheet } from 'react-native';
import { propTypes, styleTypes } from '../Interfaces';
import { config } from '../Shared';
import {
  WhatIsCaptcha,
  CancelBooking,
  OtherIssues,
  WrongCredentials,
  ChangeUsername,
} from './';
import { viewStyle } from '../Styles';

const styles = StyleSheet.create<styleTypes.ComponentSwitcher>({
  contentWrapper: {
    ...viewStyle.accordionContentWithoutTopBorder,
  },
});

const getComponentFromPageId = (pageId: string): React.ReactElement => {
  switch (pageId) {
    case config.PAGE_ID.CAPTCHA:
      return <WhatIsCaptcha />;
    case config.PAGE_ID.CANCEL_BOOKING:
      return <CancelBooking />;
    case config.PAGE_ID.OTHER_ISSUES:
      return <OtherIssues />;
    case config.PAGE_ID.WRONG_CREDENTIALS:
      return <WrongCredentials />;
    case config.PAGE_ID.DIFFERENT_IRCTC_USERNAME:
      return <ChangeUsername />;
    default: return null;
  }
};
const ComponentSwitcher: React.FC<propTypes.ComponentSwitcher> = ({ pageId }): React.ReactElement => {
  return (
    <View style={styles.contentWrapper}>
      { getComponentFromPageId(pageId) }
    </View>
  );
};

export default ComponentSwitcher;
