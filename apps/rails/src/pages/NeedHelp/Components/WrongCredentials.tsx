import React from 'react';
import { Text, StyleSheet } from 'react-native';
import BtnGeneric from '../../PostPayment/Components/BtnGeneric';

import Loader from '../../Common/Loader';
import { Spacer } from './';
import { config, useFetchData } from '../Shared';
import { textStyle } from '../Styles';
import { Actions } from '../../../navigation';
import { styleTypes } from '../Interfaces';

const styles = StyleSheet.create<styleTypes.WrongCredentials>({
  headerText: {
    ...textStyle.headerText,
  },
  mainBodyText: {
    ...textStyle.mainBodyText,
  },
});

const WrongCredentials: React.FC = (): React.ReactElement => {
  const { isLoading, response } = useFetchData(config.PAGE_ID.WRONG_CREDENTIALS);

  if (isLoading) {
    return (
      <Loader />
    );
  }
  return (
    <React.Fragment>
      <Text style={[styles.headerText, textStyle.getHeaderTextFontStyle()]}>{response.headerText}</Text>
      <Spacer v={10} />
      {
        response.bodyList.map(item =>
            <Text
                key={item}
                style={[styles.mainBodyText,textStyle.getMainBodyTextFontStyle()]}
            >{item}</Text>)
      }
      <Spacer v={30} />
      <BtnGeneric
        disabled={false}
        btnText={response.buttonText}
        clickAction={() => { Actions.railsForgotPassword({ from: config.ROUTE_SOURCE });}}
        showBtnLoader={false}
      />
    </React.Fragment>
  );
};

export default WrongCredentials;
