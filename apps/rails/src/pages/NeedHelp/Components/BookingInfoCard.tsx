import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { format } from 'fecha';
import isEmpty from 'lodash/isEmpty';

import Loader from '../../Common/Loader';
import { Spacer, FeedbackResponse } from './';
import { textStyle, viewStyle } from '../Styles';
import { getClassType } from '../../Types/ClassType';
import QuotaType from '../../Types/QuotaType';
import { styleTypes } from '../Interfaces';
import { config, LABELS, useFetchData } from '../Shared';
import { getDataFromAsynStorage, RAILS_CURR_AVL } from '../../../Utils/RailsConstant';
import { _label } from '../../../vernacular/AppLanguage';

const styles = StyleSheet.create<styleTypes.BookingInfoCard>({
  accordionContent: {
    ...viewStyle.accordionContent,
  },
  row: {
    ...viewStyle.row,
  },
  headingLevel1: {
    ...textStyle.headingLevel1,
  },
  headingLevel2: {
    ...textStyle.headingLevel2,
  },
  headingLevel3: {
    ...textStyle.headingLevel3,
  },
  lightHeadingLevel1: {
    ...textStyle.lightHeadingLevel1,
  },
  lightHeadingLevel2: {
    ...textStyle.lightHeadingLevel2,
  },
  border: {
    ...viewStyle.border,
  },
});


const BookingInfoCard: React.FC = (): React.ReactElement => {
  const { isLoading, response } = useFetchData(config.PAGE_ID.BOOKING_DETAILS, 'bookingId');
  const [currAvail, setCurrAvail] = React.useState('');

  React.useEffect(() => {
    async function getCurrAvail() {
      const currAvail = await getDataFromAsynStorage(RAILS_CURR_AVL);
      setCurrAvail(currAvail);
    }
    getCurrAvail();
  }, []);

  const getOnlyDate = (date: string) => {
    const d = new Date(date);
    return format(d, config.DATE_FORMAT.SHORT);
  };

  const getStationDetails = (stn: Station) => {
    return `${stn.name} (${stn.code})`;
  };


  const getJsDate = (d: number, t: string): object => {
    const hm = t.split(':').map(Number);
    const date = new Date(d);
    date.setHours(hm[0]);
    date.setMinutes(hm[1]);
    return date;
  };

  const minutesToHM = (m: number): string => {
    const hours = Math.floor(m / 60);
    const minutes = m % 60;
    return `${hours}h ${minutes}m`;
  };

  const lastUpdatedAt = (date: number) => {
    return format(new Date(date), config.DATE_FORMAT.FULL);
  };

  if (isLoading) {
    return (
      <View style={styles.accordionContent}>
        <Loader />
      </View>
    );
  }

  if (isEmpty(response) || (response && response.error)) {
    return <FeedbackResponse message={ LABELS.API_FAILURE_MESSAGE } />;
  }

  const {
    trainName,
    trainNumber,
    journeyClass,
    journeyQuota,
    updated,
    boardingDate,
    departureTime,
    arrivalTime,
    destArrvDate,
    fromStn,
    destStn,
  } = response;

  const getQuotaType = (journeyQuota: string): string => {
    return QuotaType[journeyQuota].value;
  };

  const getJourneyTime = (): string => {
    const journeyTime = Math.abs(
      getJsDate(destArrvDate, arrivalTime) -
      getJsDate(boardingDate, departureTime),
    );
    return minutesToHM(
      journeyTime / (1000 * 60),

    );
  };

  return (
    <View style={styles.accordionContent}>
      <View style={styles.row}>
        <Text style={[styles.headingLevel1, textStyle.getHeadingLevel1FontStyle()]}>{trainName}</Text>
        <Text style={[styles.lightHeadingLevel1, textStyle.getLightHeadingLevel1FontStyle()]}>{`#${trainNumber}`}</Text>
      </View>
      <Spacer v={10} />
      <View style={styles.row}>
        <Text style={[styles.headingLevel2, textStyle.getHeadingLevel2FontStyle()]}>
          {_label(getClassType(journeyClass).value)}</Text>
        <Text style={[styles.headingLevel2, textStyle.getHeadingLevel2FontStyle()]}>{currAvail}</Text>
      </View>
      <Spacer v={4} />
      <View style={styles.row}>
        <Text style={[styles.lightHeadingLevel2, textStyle.getLightHeadingLevel2FontStyle()]}>
          {getQuotaType(journeyQuota)}
        </Text>
        <Text style={[styles.lightHeadingLevel2, textStyle.getLightHeadingLevel2FontStyle()]}>
          {lastUpdatedAt(updated)}
        </Text>
      </View>
      <Spacer v={23} />
      <View style={styles.row}>
        <Text style={[styles.headingLevel3, textStyle.getHeadingLevel3FontStyle()]}>{departureTime},
        <Text style={[styles.lightHeadingLevel2, textStyle.getLightHeadingLevel2FontStyle()]}>
          {getOnlyDate(boardingDate)}</Text></Text>
        <View style={styles.border} />
        <Text style={[styles.lightHeadingLevel2, textStyle.getLightHeadingLevel2FontStyle()]}>{getJourneyTime()}</Text>
        <View style={styles.border} />
        <Text style={[styles.headingLevel3, textStyle.getHeadingLevel3FontStyle()]}>{arrivalTime},
        <Text style={[styles.lightHeadingLevel2, textStyle.getLightHeadingLevel2FontStyle()]}>
          {getOnlyDate(destArrvDate)}</Text></Text>
      </View>
      <Spacer v={5} />
      <View style={styles.row}>
        <Text
        style={[styles.lightHeadingLevel2, textStyle.getLightHeadingLevel2FontStyle()]}>{getStationDetails(fromStn)}
        </Text>
        <Text
        style={[styles.lightHeadingLevel2, textStyle.getLightHeadingLevel2FontStyle()]}
        >{getStationDetails(destStn)}</Text>
      </View>
    </View>
  );
};

interface Station {
  name: string;
  code: string;
}

export default BookingInfoCard;
