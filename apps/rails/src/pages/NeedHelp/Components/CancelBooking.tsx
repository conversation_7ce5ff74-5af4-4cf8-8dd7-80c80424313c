import React from 'react';
import { Text, StyleSheet } from 'react-native';
import Loader from '../../Common/Loader';
import BtnGeneric from '../../PostPayment/Components/BtnGeneric';
import OptionWithRadio from '../../PostPayment/Components/OptionWithRadio';
import { viewStyle, textStyle } from '../Styles';
import { Spacer, FeedbackResponse } from './';
import { utils, config, useFetchData, LABELS } from '../Shared';
import { styleTypes } from '../Interfaces';
import SimpleTextInput from '@mmt/legacy-commons/Common/Components/Inputs/SimpleTextInput';
import {checkEnglishKeyboard} from '../../../vernacular/VernacularUtils';

const styles = StyleSheet.create<styleTypes.CancelBooking>({
  textInputContainer: {
    ...viewStyle.textInputContainer,
  },
  mainBodyText: {
    ...textStyle.mainBodyText,
  },
});

const CancelBooking: React.FC = (): React.ReactElement => {
  const { isLoading, response } = useFetchData(config.PAGE_ID.CANCEL_BOOKING);
  const [ selected, setSelected ] = React.useState('');
  const [ value, setValue ] = React.useState('');
  const [englishErrorMessage, setEnglishErrorMessage] = React.useState<string>('');
  const [ feedbackPostData, setFeedbackPostData ] = React.useState({
    submission: config.STATUS.N,
    message: '',
    status: '',
  });

  const handleRadioClick = (id: string) => {
    return (): void => {
      setSelected(id);
    };
  };

  const handleTextChange = (text: string): void => {
    setValue(text);
    checkEnglishKeyboard(null,text,null,setEnglishErrorMessage);
  };

  const getFeedbackData = (): string => {
    const feedback = response.optionsTextList.find((item) => item.id === selected);
    if (utils.isTextAreaQuestion(selected)) {
      return `${feedback.text} - ${value}`;
    }
    return feedback.text;
  };

  const handleSubmit = async () => {
    utils.trackClick(config.CTA_TRACKING_ID.CANCEL_BOOKING);
    setFeedbackPostData({
      ...feedbackPostData,
      submission: config.STATUS.S,
    });
    const { message, status } = await utils.submitData(config.PAGE_ID.CANCEL_BOOKING, getFeedbackData());
    setFeedbackPostData({
      submission: config.STATUS.D,
      message,
      status,
    });
  };

  if (isLoading) {
    return <Loader />;
  }

  if (feedbackPostData.submission === config.STATUS.D) {
    return <FeedbackResponse message={feedbackPostData.message} />;
  }

  const disableButton = () => {
    return (
      utils.isTextAreaQuestion(selected) && !utils.stripTrailingSpaces(value) ||
      feedbackPostData.submission === config.STATUS.S ||
      selected === ''
    );
  };

  return (
    <React.Fragment>
      <Text style={[styles.mainBodyText, textStyle.getMainBodyTextFontStyle()]}>
        { response.bodyTextList }
      </Text>
      <Spacer v={10} />
      {
        response.optionsTextList.map((item) => {
          return (
            <OptionWithRadio
              optionStyles={{...viewStyle.radioOption }}
              textStyle={{...textStyle.radioOption}}
              val={item.text}
              key={item.id}
              selected={selected === item.id}
              handleRadioClick={handleRadioClick(item.id)}
            />
          );
        })
      }
      {
        utils.isTextAreaQuestion(selected) &&
        <SimpleTextInput
          maxLength={300}
          multiline
          value={value}
          inputStyle={styles.textInputContainer}
          placeholder={LABELS.FEEDBACK_PLACEHOLDER}
          onChangeText={handleTextChange}
          numberOfLines={6}
          scrollEnabled
          error = {englishErrorMessage}
        />
      }
      <BtnGeneric
        btnText={response.buttonText}
        showBtnLoader={feedbackPostData.submission === config.STATUS.S}
        clickAction={handleSubmit}
        disabled={disableButton()}
      />
      <Spacer v={20} />
    </React.Fragment>
  );
};

export default CancelBooking;
