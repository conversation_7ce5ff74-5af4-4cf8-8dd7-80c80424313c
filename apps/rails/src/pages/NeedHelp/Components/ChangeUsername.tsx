import React from 'react';
import { Text, StyleSheet } from 'react-native';
import { config, useFetchData, utils } from '../Shared';
import Loader from '../../Common/Loader';
import { textStyle } from '../Styles';
import BtnGeneric from '../../PostPayment/Components/BtnGeneric';
import { Spacer, FeedbackResponse } from './';
import { styleTypes } from '../Interfaces';

const styles = StyleSheet.create<styleTypes.ChangeUsername>({
  headerText: {
    ...textStyle.headerText,
  },
  mainBodyText: {
    ...textStyle.mainBodyText,
  },
  helpText: {
    ...textStyle.helpText,
  },
});

const ChangeUsername: React.FC = (): React.ReactElement => {
  const { isLoading, response } = useFetchData(config.PAGE_ID.DIFFERENT_IRCTC_USERNAME);
  const [feedbackPostData, setFeedbackPostData] = React.useState({
    submission: config.STATUS.N,
    status: '',
    message: '',
  });

  const handleClick = async () => {
    utils.trackClick(config.CTA_TRACKING_ID.FORGOT_USERNAME);
    setFeedbackPostData({
      ...feedbackPostData,
      submission: config.STATUS.S,
    });
    const { message, status } = await utils.changePassword();
    setFeedbackPostData({
      submission: config.STATUS.D,
      message,
      status,
    });
  };

  if (isLoading) {
    return <Loader />;
  }

  if (feedbackPostData.submission === config.STATUS.D) {
    return <FeedbackResponse message={feedbackPostData.message} />;
  }

  const { bodyList, buttonText, footerText, headerText } = response;
  return (
    <React.Fragment>
      <Text style={[styles.headerText, textStyle.getHeaderTextFontStyle()]}>{headerText}</Text>
      <Spacer v={10} />
      {
        bodyList.map((text: string) => (
          <React.Fragment key={text.replace(/ /g, '')}>
            <Text
              style={[styles.mainBodyText, textStyle.getMainBodyTextFontStyle()]}>
              { text }
            </Text>
            <Spacer v={10} />
          </React.Fragment>
        ))
      }
      <Text style={[styles.helpText, textStyle.getHelpTextFontStyle()]}>
        { footerText }
      </Text>
      <BtnGeneric
        disabled={false}
        btnText={feedbackPostData.submission === config.STATUS.S ? '' : buttonText}
        clickAction={handleClick}
        showBtnLoader={feedbackPostData.submission === config.STATUS.S}
      />
    </React.Fragment>
  );
};

export default ChangeUsername;
