import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';

import { CountdownTimer } from './';
import { LABELS, timer, utils } from '../Shared';
import { textStyle, viewStyle } from '../Styles';
import { propTypes, styleTypes } from '../Interfaces';
import backArrowIcon from '@mmt/legacy-assets/src/backArrowBlack.webp';
import { Actions } from '../../../navigation';
import { TimerResponse } from '../Interfaces/types';

const styles = StyleSheet.create<styleTypes.PageTitle>({
  pageTitleWrapper: {
    ...viewStyle.pageTitleWrapper,
    ...viewStyle.bottomShadow,
  },
  pageTitle: {
    ...textStyle.pageTitle,
  },
  backArrowIcon: {
    ...viewStyle.backArrowIcon,
  },
  titleContainer: {
    ...viewStyle.titleWrapper,
  },
});

const PageTitle: React.FC<propTypes.PageTitle> = ({ showTimer = true, title }):React.ReactElement => {
  const [fromTimeInMilliseconds, setFromTime] = React.useState(Date.now());

  React.useEffect(() => {
    async function getTime() {
      const latestTime: TimerResponse = await timer.getLatestTimer();
      const { time, success } = latestTime;
      if (success) {
        setFromTime(time);
      }
    }
    getTime();
  }, []);

  return (
    <View accessibilityLabel={LABELS.NEED_HELP} style={styles.pageTitleWrapper}>
      <View style={styles.titleContainer}>
        <TouchableOpacity
          onPress={() => {
              utils.trackClick('rail_need_help_landing_backbtn_clicked');
              Actions.pop();
            }
          }
        >
          <Image style={styles.backArrowIcon} source={backArrowIcon} />
        </TouchableOpacity>
        <Text style={[styles.pageTitle, textStyle.getPageTitleFontStyle()]}>
          {title}
        </Text>
      </View>
      {
        showTimer && <CountdownTimer
          buffer={5}
          totalTimeInSeconds={10 * 60}
          fromTimeInMilliseconds={fromTimeInMilliseconds}
        />
      }
      </View>
  );
};

export default PageTitle;
