import React from 'react';
import { StyleSheet } from 'react-native';

import Loader from '../../Common/Loader';
import { textStyle, viewStyle } from '../Styles';
import { FeedbackResponse } from './';
import BtnGeneric from '../../PostPayment/Components/BtnGeneric';
import { styleTypes } from '../Interfaces';
import { config, utils, useFetchData, LABELS } from '../Shared';
import SimpleTextInput from '@mmt/legacy-commons/Common/Components/Inputs/SimpleTextInput';
import {checkEnglishKeyboard} from '../../../vernacular/VernacularUtils';

const styles = StyleSheet.create<styleTypes.OtherIssues>({
  textInputContainer: {
    ...viewStyle.textInputContainer,
  },
  mainBodyText: {
    ...textStyle.mainBodyText,
  },
});

const OtherIssues: React.FC = (): React.ReactElement => {
  const { isLoading, response } = useFetchData(config.PAGE_ID.OTHER_ISSUES);
  const [feedbackPostData, setFeedbackPostData] = React.useState({
    submission: config.STATUS.N,
    status: '',
    message: '',
  });
  const [ value, setValue ] = React.useState('');
  const [englishErrorMessage, setEnglishErrorMessage] = React.useState<string>('');

  const handleTextChange = (text: string): void => {
    setValue(text);
    checkEnglishKeyboard(null,text,null,setEnglishErrorMessage);
  };

  const handleSubmit = async () => {
    utils.trackClick(config.CTA_TRACKING_ID.FEEDBACK);
    setFeedbackPostData({
      ...feedbackPostData,
      submission: config.STATUS.S,
    });
    const { message, status } = await utils.submitData(config.PAGE_ID.OTHER_ISSUES, value);
    setFeedbackPostData({
      submission: config.STATUS.D,
      message,
      status,
    });
  };

  const disableButton = () => {
    return (
      feedbackPostData.submission === config.STATUS.S ||
      !utils.stripTrailingSpaces(value)
    );
  };

  if (isLoading) {
    return <Loader />;
  }

  if (feedbackPostData.submission === config.STATUS.D) {
    return <FeedbackResponse message={feedbackPostData.message} />;
  }
  return (
    <React.Fragment>
      <SimpleTextInput
        maxLength={300}
        multiline
        value={value}
        inputStyle={styles.textInputContainer}
        placeholder={LABELS.FEEDBACK_PLACEHOLDER}
        onChangeText={handleTextChange}
        numberOfLines={6}
        scrollEnabled
        error = {englishErrorMessage}
      />
      <BtnGeneric
        btnText={response.buttonText}
        showBtnLoader={feedbackPostData.submission === config.STATUS.S}
        clickAction={handleSubmit}
        disabled={disableButton()}
      />
    </React.Fragment>
  );
};

export default OtherIssues;
