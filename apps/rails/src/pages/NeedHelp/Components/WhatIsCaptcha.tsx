import React from 'react';
import { Image, Text, StyleSheet } from 'react-native';
import { Spacer } from './';
import { textStyle, viewStyle } from '../Styles';
import { LABELS } from '../Shared';
import { styleTypes } from '../Interfaces';

import whatIsCaptcha from '@mmt/legacy-assets/src/what_is_captcha.webp';

const styles = StyleSheet.create<styleTypes.WhatIsCaptcha>({
  mainBodyText: {
    ...textStyle.mainBodyText,
  },
  helpText: {
    ...textStyle.helpText,
  },
  captchaImage: {
    ...viewStyle.captchaImage,
  },
});

const WhatIsCaptcha: React.FC = (): React.ReactElement => {
  return (
    <React.Fragment>
      <Text style={[styles.mainBodyText, textStyle.getMainBodyTextFontStyle()]}>
        { LABELS.CAPTCHA_LINE_1 }
      </Text>
      <Spacer v={20} />
      <Text style={[styles.mainBodyText, textStyle.getMainBodyTextFontStyle()]}>
        { LABELS.CAPTCHA_LINE_2 }
      </Text>
      <Spacer v={20} />
      <Image source={whatIsCaptcha} style={styles.captchaImage} />
      <Text style={[styles.helpText, textStyle.getHelpTextFontStyle()]}>
        { LABELS.CAPTCHA_LINE_3 }
      </Text>
      <Spacer v={20} />
    </React.Fragment>
  );
};

export default WhatIsCaptcha;
