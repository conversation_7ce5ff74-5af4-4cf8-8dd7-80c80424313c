import React, { useEffect, useState } from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { propTypes, styleTypes } from '../Interfaces';
import { textStyle, viewStyle } from '../Styles';
import { _label } from '../../../vernacular/AppLanguage';

import clock from '@mmt/legacy-assets/src/ic-clock-stroke.webp';

const getTimerColors = (seconds) => {
  if (seconds >= 1) {
    return [colors.orange, colors.radicalRed];
  }
  return [colors.grey, colors.grey];
};

const styles = StyleSheet.create<styleTypes.CountdownTimer>({
  clockImage: {
    ...viewStyle.clockImage,
  },
  timerDisplay: {
    ...viewStyle.timerDisplay,
  },
  timerDisplayText: {
    ...textStyle.timerDisplayText,
  },
  transactionExpired: {
    ...textStyle.transactionExpired,
  },
});

const secondsToTime = (secs: number): object => {
  const hours = Math.floor(secs / (60 * 60));
  const divisor_for_minutes = secs % (60 * 60);
  const minutes = Math.floor(divisor_for_minutes / 60);
  const divisor_for_seconds = divisor_for_minutes % 60;
  const seconds = Math.ceil(divisor_for_seconds);

  const obj = {
    'h': hours,
    'm': minutes,
    's': seconds,
  };
  return obj;
};

const CountdownTimer: React.FC<propTypes.CountdownTimer> = ({
  totalTimeInSeconds,
  fromTimeInMilliseconds,
  buffer,
}): React.ReactElement => {
  let timerInstance: unknown = null;
  const startAt = (buffer = 0) => {
    return totalTimeInSeconds - buffer;
  };

  const getStartAt = (): number => {
    if (fromTimeInMilliseconds) {
      const now = Date.now();
      const elapsedTime = Math.abs(fromTimeInMilliseconds - now);
      return startAt(buffer) - (elapsedTime / 1000);
    }
    return startAt(buffer);
  };

  const [time, updateTime] = useState(secondsToTime(getStartAt()));

  const [seconds, updateSeconds] = useState(getStartAt());

  useEffect(() => {
    if (seconds > 0) {
      timerInstance = setTimeout(countDown, 1000);
    }
    return () => {
      clearTimeout(timerInstance);
    };
  });

  const countDown = (): void => {
    // Remove one second, set state so a re-render happens.
    const nextTick = Math.ceil(seconds) - 1;
    updateTime(secondsToTime(nextTick));
    updateSeconds(nextTick);
  };

  const appendZero = (num: number): string => {
    return num < 10 ? `0${num}` : `${num}`;
  };

  const { m, s } = time;
  return (
    <LinearGradient
      style={{
          overflow: 'hidden',
          borderRadius: 100,
          alignItems: 'center',
        }}
      colors={getTimerColors(seconds)}
      start={{
          x: 0,
          y: 2,
        }}
      end={{
          x: 1,
          y: 1,
        }}
    >
    <View style={styles.timerDisplay}>
      <Image style={styles.clockImage} source={clock} />
      <Text style={[styles.timerDisplayText, textStyle.getTimerDisplayTextFontStyle()]}>
        {
          seconds >= 1
          ? `${appendZero(m)}m: ${appendZero(s)}s`
          : <Text style={[styles.transactionExpired, textStyle.getTransactionExpiredFontStyle()]}>{_label('transaction_expired')}</Text>
        }
      </Text>
    </View>
    </LinearGradient>
  );
};


export default CountdownTimer;
