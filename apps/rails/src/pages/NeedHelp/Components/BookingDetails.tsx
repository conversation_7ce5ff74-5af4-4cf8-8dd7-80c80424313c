import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

import Accordion from '@mmt/legacy-commons/Common/Components/Accordion';
import { BookingInfoCard, Spacer } from './';
import { styleTypes } from '../Interfaces';
import { LABELS, utils } from '../Shared';
import { textStyle, viewStyle } from '../Styles';

const styles = StyleSheet.create<styleTypes.BookingDetails>({
  sectionTitleWrapper: {
    ...viewStyle.sectionTitleWrapper,
  },
  sectionTitle: {
    ...textStyle.sectionTitle,
  },
});

const BookingDetails: React.FC = (): React.ReactElement => {
  return (
    <React.Fragment>
      <View style={styles.sectionTitleWrapper}>
        <Accordion
          renderHeader={() => <Text style={[styles.sectionTitle, textStyle.getSectionTitleFontStyle()]}>
            { LABELS.BOOKING_DETAILS }</Text>}
          renderContent={() => <BookingInfoCard /> }
          defaultCollapsed
          accordionOpenCallback={() => {
            utils.trackClick('rail_need_help_bookingdetails_dropdown_clicked');
          }}
        />
      </View>
      <Spacer v={10} />
    </React.Fragment>
  );
};

export default BookingDetails;
