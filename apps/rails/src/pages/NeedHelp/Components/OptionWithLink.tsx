import React from 'react';
import { View, StyleSheet } from 'react-native';
import { viewStyle } from '../Styles';
import { utils } from '../Shared';
import { OptionTitle, Spacer } from './';
import { propTypes, styleTypes } from '../Interfaces';

const styles = StyleSheet.create<styleTypes.OptionLinkWrapper>({
  optionLinkWrapper: {
    ...viewStyle.optionLinkWrapper,
  },
});

const OptionWithLink: React.FC<propTypes.OptionWithLink> = ({ option }): React.ReactElement => {
  return (
    <View style={ styles.optionLinkWrapper }>
      <OptionTitle title={option.pageTitle} isActive={option.isActive} {...utils.getLinkProps(option) }/>
      <Spacer v={10} />
    </View>
  );
};

export default OptionWithLink;
