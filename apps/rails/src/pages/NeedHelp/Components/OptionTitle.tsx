import React from 'react';
import { View, Text, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { textStyle, viewStyle } from '../Styles';
import { propTypes, styleTypes } from '../Interfaces';
import { Actions } from '../../../navigation/railsNavigation';

const styles = StyleSheet.create<styleTypes.OptionTitle>({
  accordionTitle: {
    ...textStyle.accordionTitle,
  },
  optionWrapper: {
    ...viewStyle.titleCard,
  },
  linkWrapper: {
    ...viewStyle.linkWrapper,
  },
  linkText: {
    ...textStyle.linkText,
    ...viewStyle.linkText,
  },
  activeAccordionTitle: {
    ...textStyle.activeAccordionTitle,
  },
  activeOptionWrapper: {
    ...viewStyle.activeOptionWrapper,
  },
});

const OptionTitle: React.FC<propTypes.OptionTitle> = ({
  title,
  linkTitle,
  linkHref,
  extraParams,
  isActive,
}): React.ReactElement => {
  const onPress = (linkHref: string | undefined, extraParams: object | null | undefined) => {
    return () => {
      if (linkHref) {
        const fn = Actions[linkHref];
        fn({ ...extraParams });
      }
    };
  };
  return (
    <TouchableWithoutFeedback
      accessibilityLabel={title}
      onPress={onPress(linkHref, extraParams)}
    >
      <View
        style={
          [
            styles.optionWrapper,
            linkTitle ? styles.linkWrapper : null,
            isActive ? styles.activeOptionWrapper : null,
          ]
        }
      >
      <Text
        style={
                      isActive ?
                      [styles.activeAccordionTitle, textStyle.getActiveAccordionTitleFontStyle()]
                      : [styles.accordionTitle, textStyle.getAccordionTitleFontStyle()]
        }
      >
        { title }
      </Text>
      {
        linkTitle
        ? <Text style={[styles.linkText, textStyle.getLinkTextFontStyle()]}>{linkTitle}</Text>
        : null
      }
      </View>
    </TouchableWithoutFeedback>
  );
};

export default OptionTitle;
