import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

import { Spacer } from './';
import { propTypes, styleTypes } from '../Interfaces';
import { textStyle, viewStyle } from '../Styles';

const styles = StyleSheet.create<styleTypes.FeedbackResponse>({
  contentWrapper: {
    ...viewStyle.accordionContent,
  },
  mainBodyText: {
    ...textStyle.mainBodyText,
  },
});

const FeedbackResponse: React.FC<propTypes.FeedbackResponse> = ( { message }):React.ReactElement => {
  return (
    <View style={styles.contentWrapper}>
      <Spacer v={50} />
      <Text style={[styles.mainBodyText, textStyle.getMainBodyTextFontStyle()]}>{message}</Text>
    </View>
  );
};

export default FeedbackResponse;
