import React, { useState } from 'react';
import { View, TouchableOpacity, Animated, StyleSheet, Text, Image } from 'react-native';
import { useAnimatedBottom } from 'apps/rails/src/pages/Common/useAnimatedBottom';
import { colors } from 'packages/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import isEmpty from 'lodash/isEmpty';

import closeIcon from '@mmt/legacy-assets/src/reviewClose.webp';
import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up.webp';
import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down.webp';
import fareBreakupIcon from '@mmt/legacy-assets/src/FC_fareBreakup_icon.webp';
import tripGuaranteeIcon from '@mmt/legacy-assets/src/TG_fareBreakup_icon.webp';

const LineBreak = () => <View style={styles.lineBreak} />;

const FareBreakup = ({
  paymentFareBreakup,
  closeBottomSheet,
}: {
  paymentFareBreakup: unknown;
  closeBottomSheet: () => void;
  bnppSelected: boolean;
}) => {
  const {
    discountsDescRows,
    feeAndTaxesDescRows,
    optedAddOnsDescRows,
    ticketFareBreakupDescRows,
    ticketFareDesc,
    totalFareDesc,
  } = paymentFareBreakup;

  const [showBaseFare, setShowBaseFare] = useState(false);

  const contentRows = (data: unknown, isBaseFare: boolean = false, isDiscountFare: boolean = false) => {
    return data?.map((fare: unknown, index: number) => (
      <View key = {index} style={styles.flexRowContainer2}>
        <Text style={isBaseFare ? styles.alignStartText : styles.alignStartNormal}>
          {fare?.mainText?.toLowerCase().includes('trip guarantee') ? (
            <Image style={styles.imageIcon} source={tripGuaranteeIcon} />
          ) : fare?.mainText?.toLowerCase().includes('free cancellation') ? (
            <Image style={styles.imageIcon} source={fareBreakupIcon} />
          ) : (
            <></>
          )}
          {fare?.mainText}
        </Text>
        <Text style={styles.alignEndText}>
          {isDiscountFare && '- '}
          {fare?.displayAmount}
        </Text>
      </View>
    ));
  };

  const totalAmount = (dataArray: unknown, isDiscountText: boolean = false) => {
    const amount = dataArray.reduce((acc: number, curr: unknown) => {
      return acc + curr.amount;
    }, 0);

    let discountText = '';
    if (isDiscountText) {
      discountText = '- ';
    }
    return `${discountText}₹${Math.floor(amount)}`;
  };

  const baseFareAmountText = ticketFareDesc?.mainText;
  const baseFareAmount = ticketFareDesc?.displayAmount;
  const baseFareBreakupComponent = contentRows(ticketFareBreakupDescRows, true);
  const fareSummary = _label('fare_summary');

  const feeAndTaxesText = _label('fee_and_taxes');
  const feeAndTaxesComponent = contentRows(feeAndTaxesDescRows);
  const feesAndTaxesTotal = totalAmount(feeAndTaxesDescRows);

  const addOnsText = _label('add_ons');
  const addOnsComponent = contentRows(optedAddOnsDescRows);
  const addOnsTotal = totalAmount(optedAddOnsDescRows);

  const discountsText = _label('discounts');
  const discountComponent = contentRows(discountsDescRows, false, true);
  const discountTotal = totalAmount(discountsDescRows, true);

  return (
    <View style={styles.fareBreakupContainer}>
      <View style={[styles.flexRowContainer, { marginBottom: 7 }]}>
        <Text style={styles.headerText}>{fareSummary}</Text>
        <View style={styles.closeIconContainer}>
          <TouchableRipple onPress={closeBottomSheet} style={{ maxWidth: '10' }}>
            <Image style={styles.closeIcon} source={closeIcon} />
          </TouchableRipple>
        </View>
      </View>
      <View style={[styles.flexRowContainer, { marginBottom: -10 }]}>
        <Text style={styles.otherHeaderText}>
          {baseFareAmountText}
          <TouchableOpacity onPress={() => setShowBaseFare(!showBaseFare)}>
            <Image style={styles.arrowIcon} source={showBaseFare ? arrowUp : arrowDown} />
          </TouchableOpacity>
        </Text>
        <Text style={styles.alignEndTextFare}>{baseFareAmount}</Text>
      </View>
      {showBaseFare && baseFareBreakupComponent}
      <LineBreak />
      <View style={styles.flexRowContainer}>
        <Text style={styles.otherHeaderText}>{feeAndTaxesText}</Text>
        <Text style={styles.alignEndTextFare}>{feesAndTaxesTotal}</Text>
      </View>
      {feeAndTaxesComponent}
      <LineBreak />
      {addOnsComponent.length > 0 && (
        <>
          <View style={styles.flexRowContainer}>
            <Text style={styles.otherHeaderText}>{addOnsText}</Text>
            <Text style={styles.alignEndTextFare}>{addOnsTotal}</Text>
          </View>
          {addOnsComponent}
          <LineBreak />
        </>
      )}
      {discountsDescRows.length > 0 && (
        <>
          <View style={styles.flexRowContainer}>
            <Text style={styles.otherHeaderText}>{discountsText}</Text>
            <Text style={[styles.alignEndTextFare, styles.discountTotalText]}>{discountTotal}</Text>
          </View>
          {discountComponent}
          <LineBreak />
        </>
      )}
      <View style={styles.totalPayableContainer}>
        <Text style={styles.totalPayableText}>{totalFareDesc?.mainText}</Text>
        <Text style={styles.totalPayableFare}>{totalFareDesc?.displayAmount}</Text>
      </View>
    </View>
  );
};
const RailsReviewFareBreakupBottomSheet = ({
  toggleBottomSheet,
  showFareBreakupBottomSheet,
  fullPaymentFareBreakup,
  bnppPartialFareBreakup,
  bnppData,
  bnppSelected,
}: {
  toggleBottomSheet: () => void;
  showFareBreakupBottomSheet: boolean;
  fullPaymentFareBreakup: unknown;
  bnppPartialFareBreakup: unknown;
  bnppData: unknown;
  bnppSelected: boolean;
}) => {
  const bottom = useAnimatedBottom(showFareBreakupBottomSheet, 200);
  const payString = `${_label('pay')} `;
  const avoidAutoCancellationString = ` ${_label('avoid_auto_cancellation')}`;

  if (isEmpty(bnppPartialFareBreakup) || isEmpty(fullPaymentFareBreakup)) {
    return null;
  }

  return (
    <View style={styles.fullFlex}>
      <TouchableOpacity style={styles.bottomTouchable} onPress={toggleBottomSheet}>
        <View />
      </TouchableOpacity>
      <View style={styles.bottomSheetContentContainer} pointerEvents="box-none">
        <Animated.View style={[styles.bottomSheetBackground, { bottom }]}>
          <FareBreakup
            paymentFareBreakup={bnppSelected ? bnppPartialFareBreakup : fullPaymentFareBreakup}
            closeBottomSheet={toggleBottomSheet}
            bnppSelected={bnppSelected}
          />
          {bnppSelected && (
            <View style={styles.bnppBottomStrip}>
              <Text style={styles.bnppText}>
                {payString}
                <Text
                  style={styles.bnppTextBold}
                >{`₹${bnppData?.remainingAmount} by ${bnppData?.dueDate}`}</Text>
                {avoidAutoCancellationString}
              </Text>
            </View>
          )}
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  fullFlex: {
    flex: 1,
    zIndex: 300,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 50,
    backgroundColor: colors.lightBlack,
  },
  bottomTouchable: {
    ...StyleSheet.absoluteFillObject,
  },
  bottomSheetContentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomSheetBackground: {
    width: '100%',
    justifyContent: 'flex-end',
  },
  fareBreakupContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    backgroundColor: colors.white,
    marginVertical: 0,
    flex: 0,
    paddingBottom: 38,
    zIndex: 10,
  },
  lineBreak: {
    borderBottomWidth: 0.5,
    borderBottomColor: colors.lightGrey,
    marginHorizontal: 13,
    paddingHorizontal: 10,
    marginVertical: 17,
  },
  closeIconContainer: {
    flex: 1,
    marginRight: 10,
  },
  closeIcon: {
    height: 24,
    width: 24,
    alignSelf: 'flex-end',
    marginRight: 5,
    marginTop: -5,
  },
  flexRowContainer: {
    flexDirection: 'row',
  },
  flexRowContainer2: {
    flexDirection: 'row',
    lineHeight: 22,
    paddingBottom: 10,
  },
  totalPayableContainer: {
    flexDirection: 'row',
    marginBottom: -10,
  },
  headerText: {
    alignSelf: 'flex-start',
    flex: 1,
    marginRight: 10,
    fontWeight: 'bold',
    fontSize: 18,
    marginLeft: 10,
    lineHeight: 22,
    color: colors.black,
  },
  otherHeaderText: {
    alignSelf: 'flex-start',
    flex: 1,
    marginRight: 10,
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 10,
    marginBottom: 15,
    color: colors.black,
  },
  totalPayableText: {
    alignSelf: 'flex-start',
    flex: 1,
    marginRight: 10,
    fontWeight: 'bold',
    fontSize: 18,
    marginLeft: 10,
    marginBottom: 15,
    color: colors.black,
  },
  totalPayableFare: {
    alignSelf: 'flex-end',
    marginRight: 15,
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 15,
    color: colors.black,
  },
  alignStartText: {
    alignSelf: 'flex-start',
    flex: 1,
    marginRight: 10,
    marginLeft: 35,
    marginBottom: -6,
    color: colors.textGrey,
  },
  alignStartNormal: {
    alignSelf: 'flex-start',
    flex: 1,
    marginRight: 10,
    marginLeft: 10,
    fontSize: 14,
    color: colors.textGrey,
  },
  alignEndText: {
    alignSelf: 'flex-end',
    marginRight: 15,
    marginBottom: -6,
    color: colors.textGrey,
  },
  alignEndTextFare: {
    alignSelf: 'flex-end',
    marginRight: 15,
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 15,
    color: colors.black,
  },
  discountTotalText: {
    color: colors.lightGreen16,
  },
  arrowIcon: {
    width: 20,
    height: 20,
    marginLeft: 2,
    marginTop: 20,
    marginBottom: -5,
    resizeMode: 'contain',
    tintColor: colors.grey,
    alignItems: 'center',
  },
  imageIcon: {
    width: 18,
    height: 18,
    marginRight: 8,
    marginTop: -3,
    resizeMode: 'contain',
    alignItems: 'center',
  },
  bnppBottomStrip: {
    backgroundColor: colors.creamWhite,
    color: colors.lightYello,
    height: 40,
    paddingLeft: 15,
    paddingVertical: 8,
    marginTop: -30,
    zIndex: 10,
  },
  bnppText: {
    color: colors.lightYello,
    fontColor: colors.lightYello,
    fontSize: 12.5,
  },
  bnppTextBold: {
    color: colors.lightYello,
    fontColor: colors.lightYello,
    fontSize: 12.5,
    fontWeight: 'bold',
  },
});

export default RailsReviewFareBreakupBottomSheet;
