import _isEmpty from 'lodash/isEmpty';
import _forEach from 'lodash/forEach';
import fecha from 'fecha';
import _find from 'lodash/find';
import _isNull from 'lodash/isNull';
import get from 'lodash/get';
import {
  logTravellersPageLoadPdtEvent,
  searchContextMapper,
} from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing';
import { getDataFromStorage, setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { getMmtId } from '@mmt/rails/src/pages/PostPayment/PostPaymentUtils';
import {
  AVAILABILITY_STATUS,
  AVAILABILITY_STATUS_CHANGE_OMNITURE_EVENTS,
  irctcAccountUserNameAPI,
  MULTI_SELECT,
} from '@mmt/rails/src/Utils/RailsConstant';
import {
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  REVIEW_PAGE_TRACKING_KEY,
  RAILS_DOWNTIME_TRIGGERED,
  REVIEW_DOWNTIME_ERROR_CODE,
  RAILS_BOOKING_REVIEW_ERROR_07_CODE,
  RAILS_BOOKING_REVIEW_ERROR_08_CODE,
} from 'apps/rails/src/Utils/RailsConstant';
import _isUndefined from 'lodash/isUndefined';
import { Actions } from '../../navigation';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  DATE_MONTH_YEAR_FORMAT,
  DOUBLE_DIGIT_DAY_WITH_MONTH_FORMAT,
  RAILS_MMT_ID,
  setDataToAsyncStorage,
  getDataFromAsynStorage,
  REVIEW_BUTTON_CLICK_COUNT,
  RAILS_ERROR_RETRY_COUNT,
  TRAVELLER_PDT_ADDON_DETAILS,
} from '../../Utils/RailsConstant';
import {
  ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW,
  displayErrorBottomSheet,
  displayDownTimeBottomSheet,
} from 'apps/rails/src/pages/TravelerDetails/TravelerDetailsActions';
import {
  convertDateFormat,
  getNameObj,
  isChild,
  isInfant,
  isSrCitizen,
} from '../TravelerDetails/TravelerDetailsUtils';
import {getBookingReview} from '../../Utils/RailsAPIRepository';
import { isTatkalQuota, getQuota } from '../Types/QuotaType';
import {
  trackReviewErrorPage,
  trackTravellerBookNow,
  trackAndSetDataForReviewPageLoad,
  trackTravellerPageEvent,
  removeEventFromEvar47or97Variable,
  trackGenericEvar47or97Event,
  trackReviewAvailibilityChange,
  trackGenericEvar99Event,
  removeEventFromEvar99Variable,
} from '../../railsAnalytics';
import {railsConfig} from '../../RailsConfig';
import Traveler from '../TravelerDetails/Components/Traveler';
import {getBerthType} from '../Constants/Berth';
import {getMealObject} from '../Constants/MealNew';
import {getSrCitizenObject} from '../Constants/SrCitizenConcession';
import {allCountries} from '../Traveller/Config/AllCountries';
import {getClassType} from '../Types/ClassType';
import ReservationChoiceType, {getRsChoiceByValue} from '../Constants/ReservationChoiceType';
import getAutoCompleteCities from '../../Utils/CityRepository';
import fetch2 from '../../fetch2';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {AbConfigKeyMappings, getPokusConfig} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {PokusLobs} from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import {updateUsername} from '../User/UserVerification/UserVerificationActions';
import {getGenderfromValue} from '../Constants/Gender';
import { tuneRailsReviewLoadTracker } from '../../Utils/railsTuneTracker';
import {addMinutes} from '../NewListing/Utils/RailListingUtils';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

import RailsPdtReviewHelper from '../../PdtAnalytics/PdtHelper/RailsPdtReviewHelper';
import { ERROR_FLOW, TRAVEL_INSURANCE } from '../TravelerDetails/TravelerDetails.constant';
import {openPayment } from '../../Utils/paymentUtils';
import { NativeModules } from 'react-native';

import {
  getComIds,
  getCommonPdtData, getCommonSearchContextData,
} from '../../PdtAnalytics/PdtHelper/RailsPdtUtils';
import { trackShownAndOptedValues } from '../../Utils/railofyUtils';
import { firebaseReviewTracker } from '../../Analytics/Firebase/util';
import { _label } from '../../vernacular/AppLanguage';
import { bookMode, isMweb } from '../../Utils/device';
import { getHeaderTitleForTravelersAndReview } from '../../Utils/HeaderContentUtil';
import { COUPON_TYPE, RAILOFY_TYPE } from '../TravelerDetails/railofyUtils';
import {
  getRailsTGVersion,
  getRailsErrorHandlingConfig,
  getTravellerPdtConfig,
  getRouteExtensionBottomSheetVariants,
  getIfCanShowBNPP,
  getSeatLockReviewVariants,
  RailsAbConfig,
  showRailsReviewIrctcBs,
  showSocialProofingFc,
  showSocialProofingTg,
  railsCouponExperiment,
  getRailsReviewErrorAlertsTimer,
  getTgRacPremiumVariant,
} from '../../RailsAbConfig';
import { uploadGrafanaMetrics } from '../../Utils/RailsGrafanaTracker';
import {
  fetchRailofyDataIfNeeded,
  loadUserDetails,
} from '../TravelerDetails/TravelerDetailsActions';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import {
  PDT_SCHEMA_ID,
  PDT_SCHEMA_NAME,
  TRAVELLER_PDT_EVENTS,
  REVIEW_PAGE_PDT_EVENTS,
} from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { generateUUID } from '@mmt/navigation/src/util';
import {
  COMMON_PDT_CONSTANTS,
  NEW_PDT_EVENT_TRACKING_CONTEXT,
} from '../../PdtAnalytics/PdtAnalyticsV2/CommonPdtConstants';
import { getJourneyId, logRailsEventToPDT } from '../../PdtAnalytics/PdtAnalyticsV2/PdtV2Utils';
import { addDays } from '@mmt/legacy-commons/Helpers/dateHelpers';

const ERROR_MESSAGE_AVAILABILITY_STATUS = 'visit_later';
const DEFAULT_FOOD_CHOICE = 'D';
export const ACTION_BOOKING_REVIEW_LOAD = '@rails/ACTION_BOOKING_REVIEW_LOAD';
export const ACTION_BOOKING_REVIEW_SUCCESS = '@rails/ACTION_BOOKING_REVIEW_SUCCESS';
export const ACTION_BOOKING_REVIEW_FAILURE = '@rails/ACTION_BOOKING_REVIEW_FAILURE';
export const ACTION_UNLOAD_BOOKING_REVIEW = '@rails/ACTION_UNLOAD_BOOKING_REVIEW';
export const ACTION_SET_REVIEW_HEADER = '@rails/ACTION_SET_REVIEW_HEADER';
export const ACTION_SET_PPBOOKING_LANDING_DATA = '@rails/ACTION_SET_PPBOOKING_LANDING_DATA';
export const ACTION_SET_PPBOOKING_LISTING_DATA = '@rails/ACTION_SET_PPBOOKING_LISTING_DATA';
export const ACTION_SET_PPBOOKING_TRAVELER_DATA = '@rails/ACTION_SET_PPBOOKING_TRAVELER_DATA';
export const ACTION_SET_RAILOFY_FREECANCELLATION_DATA = '@rails/ACTION_SET_RAILOFY_FREECANCELLATION_DATA';
export const ACTION_SET_ROUTED_THROUGH_REVIEW = '@rails/ACTION_SET_ROUTED_THROUGH_REVIEW';
export const ACTION_CLEAR_ROUTED_THROUGH_REVIEW = '@rails/ACTION_CLEAR_ROUTED_THROUGH_REVIEW';
export const ACTION_CHANGE_REFRESH_RAILOFY_STATUS_CHANGE = '@rails/ACTION_CHANGE_REFRESH_RAILOFY_STATUS_CHANGE';
export const ACTION_BNPP_RESPONSE_UPDATE = '@rails/ACTION_BNPP_RESPONSE_UPDATE';
export const ACTION_SHOWN_REVIEW_BNPP = '@rails/ACTION_SHOWN_REVIEW_BNPP';
export const ACTION_SHOW_PAYNOW_LOADER = '@rails/ACTION_SHOW_PAYNOW_LOADER';
export const ACTION_HIDE_PAYNOW_LOADER = '@rails/ACTION_HIDE_PAYNOW_LOADER';
export const ACTION_SET_ALERT_API_SUCCESS = '@rails/ACTION_SET_ALERT_API_SUCCESS';
export const ACTION_REVIEW_VERIFICATION_FLOW = '@rails/ACTION_REVIEW_VERIFICATION_FLOW';

export const setRoutedThroughReview = (dispatch) => {
    dispatch({
        type : ACTION_SET_ROUTED_THROUGH_REVIEW,
        data : null,
    });
};

export const clearReviewVerificationFlow = () => ({
  type: ACTION_REVIEW_VERIFICATION_FLOW,
  data: null,
});
export const clearRoutedThroughReview = (dispatch) => {
    dispatch({
        type : ACTION_CLEAR_ROUTED_THROUGH_REVIEW,
        data : null,
    });
};

export const actionRefreshRailofyStatusChange = (dispatch,getState) => {
  const {refreshRailofyStatusChange, routedThroughReview} = getState().railsReview;
  if (routedThroughReview) {
    dispatch({
      type: ACTION_CHANGE_REFRESH_RAILOFY_STATUS_CHANGE,
      data: !refreshRailofyStatusChange,
    });
  }
};
const getReviewHeaderText = () => (disptach, getState) => {
  console.log('getTravelerHeaderText');
  const {
    railsTraveler: { selectedBookingDate },
    railsListing: { selectedTrainInfo },
  } = getState();

  const titleText = getHeaderTitleForTravelersAndReview(selectedTrainInfo);
  let subtitleText = '';
  try {
    subtitleText = convertDateFormat(selectedBookingDate.availablityDate,
          DATE_MONTH_YEAR_FORMAT, DOUBLE_DIGIT_DAY_WITH_MONTH_FORMAT);
  } catch (e) {
    console.log('Error in getTravelerHeaderText', e);
  }
  disptach({
    type: ACTION_SET_REVIEW_HEADER,
    data: {
      header: `${titleText} | ${subtitleText}`,
    },
  });
};

export const getPassengerList = (
  selectedTravelers,
  travelers,
  selectedQuota,
  childBerthMandatory,
  seniorCitizenApplicable,
) => {
  const passengerList = [];
  let count = 1;

  _forEach(selectedTravelers, (value) => {
    const traveler = _find(travelers, t => t.travelerId === value);
    if (!isInfant(traveler.basicInfo.age) || (isInfant(traveler.basicInfo.age) && traveler.basicInfo.childBerthFlag)) {
      const passenger = {
        passengerAge: traveler.basicInfo.age,
        passengerBedrollChoice: false,
        passengerBerthChoice: traveler.basicInfo.berth.key === 'NONE' ? '' : traveler.basicInfo.berth.key,
        passengerGender: traveler.basicInfo.gender.value,
        passengerIcardFlag: false,
        passengerName: traveler.fullName,
        passengerSerialNumber: count,
        passengerNationality: traveler.basicInfo.nationality.code,
        childBerthFlag: traveler.basicInfo.childBerthFlag,
        bedRollFlagEnabled: false,
        ...(traveler?.dateOfBirth && {
          psgnConcDOB: fecha.format(new Date(traveler?.dateOfBirth), 'YYYYMMDD'),
        }),
        ...(traveler?.passportNumber && { passengerCardNumber: traveler?.passportNumber }),
        ...(traveler?.basicInfo?.nationality?.code !== 'IN' && !_isEmpty(traveler?.passportNumber) && {passengerCardType: 'PASSPORT'}),
        passengerIcardFlag:
          (traveler?.basicInfo?.nationality?.code !== 'IN' &&
            !_isEmpty(traveler?.passportNumber)) ||
          false,
      };
      if (traveler.isFoodChoiceEnabled) {
        passenger.passengerFoodChoice = traveler.basicInfo.meal.key;
      }
      if (isChild(traveler.basicInfo.age) && childBerthMandatory) {
        passenger.childBerthFlag = true;
      }
      if (seniorCitizenApplicable) {
        passenger.concessionOpted =
            isSrCitizen(traveler.basicInfo.age, traveler.basicInfo.gender) && !isTatkalQuota(selectedQuota) ?
          traveler.basicInfo.seniorCitizenConcession.key : false;
      }
      count += 1;
      passengerList.push(passenger);
    }
  });
  return passengerList;
};

export const getReservationChoice = (selectedReservationChoice, confirmBookCheckbox) => {
  if (confirmBookCheckbox) {
    if (selectedReservationChoice.value === ReservationChoiceType.None.value) {
      return ReservationChoiceType.BookConfirmAndNone.value;
    } else if (selectedReservationChoice.value === ReservationChoiceType.BookIfAllBerthsAllottedInSameCoach.value) {
      return ReservationChoiceType.BookConfirmAndBookIfAllBerthsAllottedInSameCoach.value;
    } else if (selectedReservationChoice.value === ReservationChoiceType.BookIfAtLeastOneLowerBerthsAllotted.value) {
      return ReservationChoiceType.BookConfirmAndBookIfAtLeastOneLowerBerthsAllotted.value;
    } else if (selectedReservationChoice.value === ReservationChoiceType.BookIfTwoLowerBerthsAllotted.value) {
      return ReservationChoiceType.BookConfirmAndBookIfTwoLowerBerthsAllotted.value;
    }
  } else {
    return selectedReservationChoice.value;
  }
};

export const railofyValuesTrackingParams = {
  fcShown: false,
  fcOpted: 'NA',
  railofyShown: false,
  railofyOpted: 'NA',
  pageName: 'mob_rail_review',
  tgpShown: false,
  tgpOpted: 'NA',
};

const pdtLoggingTravellerOnClick = (railofyValuesTrackingParams) => async (dispatch, getState) => {
  try {
    const state = getState();
    const baseFare = railofyValuesTrackingParams.totalFare;
    const avlDayList = get(state, 'railsListing.avlDayList[0]', '');
    const boardingStation = get(state, 'railsTraveler.selectedPickupStation', '');
    const destination = get(state, 'railsListing.selectedTrainInfo.toStnCode', {});
    const source = get(state, 'railsListing.selectedTrainInfo.frmStnCode', {});
    const classCode = get(state, 'railsListing.selectedClassType', 'SL');
    const quota = get(state, 'railsListing.selectedQuota.code', 'GN');
    const doj = get(state, 'railsListing.selectedTrainInfo.departureDateAndTime', undefined);
    const departureTime = get(state, 'railsListing.selectedTrainInfo.departureTime', undefined);
    const trainNumber = get(state, 'railsListing.selectedTrainInfo.trainNumber', '');
    const contactDetails = get(state, 'railsTraveler.contactDetails', []);
    const travelers = get(state, 'railsTraveler.travelers', []);

    const data = {
      destination,
      source,
      classCode,
      quota,
      doj: doj ? fecha.format(doj, 'YYYYMMDD') : null,
      trainNumber,
      departureTime,
    };

    const { originStation, destinationStation, departureDate, selectedTrainInfo } =
      state.railsListing;

    const searchContext = {
      originStation,
      destinationStation,
      departureDate,
    };

    const { railofyOpted, fcOpted, tgpOpted, bnppOpted } = railofyValuesTrackingParams;
    const addOnDetails = (await getDataFromStorage(TRAVELLER_PDT_ADDON_DETAILS)) || [];

    // Updated
    const adOnTypes = { TG: railofyOpted, FC: fcOpted, TG_PLUS: tgpOpted, BNPP: bnppOpted };
    const addOnDetailsKeys = [...addOnDetails];
    addOnDetailsKeys?.forEach((x, i) => {
      if (adOnTypes[x.addontype] !== '') {
        addOnDetailsKeys[i].selected = true;
        x.selected = true;
      }
    });

    setDataInStorage(TRAVELLER_PDT_ADDON_DETAILS, addOnDetailsKeys);
    const { eComId = 'NULL', mComId = 'NULL' } = await getComIds(
      contactDetails.email,
      contactDetails.mobile,
    );
    const travellerInfo = travelers.map((traveler) => ({
      is_primary: false,
      fname: traveler?.basicInfo?.name?.firstName ?? 'NULL',
      lname: traveler?.basicInfo?.name?.lastName ?? 'NULL',
      title: traveler?.basicInfo?.gender?.key,
      mobile_com_id: mComId,
      email_com_id: eComId,
      mob_id: mComId,
      gender: traveler?.basicInfo?.gender?.value,
      type: 'travellerInfo',
      meal_preference: traveler?.basicInfo?.meal?.value,
    }));

    logTravellersPageLoadPdtEvent(
      'TRAVELLERS_ON_REVIEW_CLICK',
      selectedTrainInfo,
      searchContext,
      addOnDetailsKeys,
      { ...data, baseFare, ...avlDayList, boardingStation },
      travellerInfo,
    );
  } catch (e) {}
};

export const logTravellerGenericClickEvents = (eventValue) => async (dispatch, getState) => {
  try {
    const state = getState();
    const { originStation, destinationStation, departureDate } = state.railsListing;
    const searchContext = {
      originStation,
      destinationStation,
      departureDate,
    };

    const eventDetail = {
      event_id: generateUUID(),
      event_timestamp: Date.now(),
      ...TRAVELLER_PDT_EVENTS?.[eventValue],
      components: {
        id: 'traveller_page_component',
      },
    };

    const pdtLoggerObj = {
      page_context: {
        lob: 'rails',
        lob_category: 'rails',
        page_name: 'travellers',
        page_type: COMMON_PDT_CONSTANTS.PAGE_TYPE_FUNNEL,
        funnel_step: 'review',
      },
      event_tracking_context: {
        ...(await NEW_PDT_EVENT_TRACKING_CONTEXT()),
        template_id: PDT_SCHEMA_ID,
        topic_name: PDT_SCHEMA_NAME,
        ...(await getJourneyId(searchContext)),
      },
      search_context: searchContextMapper(searchContext),
      event_detail: eventDetail,
    };
    logRailsEventToPDT(pdtLoggerObj);
    return pdtLoggerObj;
  } catch (e) {
    console.error('Error in logging Traveller Click PDT event', e);
  }
};

export const logTravellerBoardingStationChangeClick =
  (eventValue) => async (dispatch, getState) => {
    try {
      const state = getState();
      const boardingStationList = get(state, 'railsTraveler.boardingStationList', []);
      const boardingStation = get(state, 'railsTraveler.selectedPickupStation', '');
      const selectedTrainInfo = get(state, 'railsListing.selectedTrainInfo', '');
      const { originStation, destinationStation, departureDate } = state.railsListing;
      const searchContext = {
        originStation,
        destinationStation,
        departureDate,
      };

      const eventDetail = {
        event_id: generateUUID(),
        event_timestamp: Date.now(),
        ...TRAVELLER_PDT_EVENTS?.[eventValue],
        components: {
          id: 'traveller_page_component',
          content_details: boardingStationList?.map((station, index) => {
            const day = departureDate
              ? fecha.format(
                  addDays(
                    new Date(departureDate),
                    station.dayCount - boardingStationList[0]?.dayCount,
                  ),
                  'D MMM',
                )
              : '';
            const epochTimestamp =
              fecha.parse(`${day} ${station.departureTime}`, 'D MMM HH:mm').getTime() / 1000;

            return {
              id: `station_${station.stationCode}_${index + 1}`,
              type: 'boarding_station_detail',
              position: {
                v: index + 1,
                h: 0,
              },
              timestamp: epochTimestamp,
              from: {
                location: {
                  code: station?.stationCode,
                  name: station?.stationName,
                  country_code: 'IN',
                  country: 'India',
                },
              },
              to: {
                location: {
                  code: destinationStation?.code,
                  name: destinationStation?.stationName,
                  country_code: 'IN',
                  country: 'India',
                },
              },
            };
          }),
          product_list: [
            {
              id: '',
              boarding_station_selected: boardingStation?.stationName,
              train_number: selectedTrainInfo?.trainNumber,
              train_name: selectedTrainInfo?.trainName,
            },
          ],
        },
      };

      const pdtLoggerObj = {
        page_context: {
          lob: 'rails',
          lob_category: 'rails',
          page_name: 'travellers',
          page_type: COMMON_PDT_CONSTANTS.PAGE_TYPE_FUNNEL,
          funnel_step: 'review',
        },
        event_tracking_context: {
          ...(await NEW_PDT_EVENT_TRACKING_CONTEXT()),
          template_id: PDT_SCHEMA_ID,
          topic_name: PDT_SCHEMA_NAME,
          ...(await getJourneyId(searchContext)),
        },
        search_context: searchContextMapper(searchContext),
        event_detail: eventDetail,
      };
      logRailsEventToPDT(pdtLoggerObj);
      return pdtLoggerObj;
    } catch (e) {
      console.error('Error in logging Traveller Click PDT event', e);
  }
};

export const logReviewGenericClickEvents = () => async (dispatch, getState) => {
  try {
    const state = getState();
    const { originStation, destinationStation, departureDate } = state.railsListing;
    const searchContext = {
      originStation,
      destinationStation,
      departureDate,
    };

    const eventDetail = {
      event_id: generateUUID(),
      event_timestamp: Date.now(),
      ...REVIEW_PAGE_PDT_EVENTS?.REVIEW_PAGE_PAY_NOW_CLICKED,
      components: {
        id: 'review_page_component',
      },
    };

    const pdtLoggerObj = {
      page_context: {
        lob: 'rails',
        lob_category: 'rails',
        page_type: COMMON_PDT_CONSTANTS.PAGE_TYPE_FUNNEL,
        page_name: 'review',
        funnel_step: 'payment',
      },
      event_tracking_context: {
        ...(await NEW_PDT_EVENT_TRACKING_CONTEXT()),
        template_id: PDT_SCHEMA_ID,
        topic_name: PDT_SCHEMA_NAME,
        ...(await getJourneyId(searchContext)),
      },
      search_context: searchContextMapper(searchContext),
      event_detail: eventDetail,
    };

    logRailsEventToPDT(pdtLoggerObj);
    return pdtLoggerObj;
  } catch (e) {
    console.error('Error in logging Review Click PDT event', e);
  }
};

export const logTravellerPageBottomSheetEvents = (eventValue) => async (dispatch, getState) => {
  try {
    const state = getState();
    const { originStation, destinationStation, departureDate } = state.railsListing;
    const searchContext = {
      originStation,
      destinationStation,
      departureDate,
    };

    const eventDetail = {
      event_id: generateUUID(),
      event_timestamp: Date.now(),
      ...TRAVELLER_PDT_EVENTS?.[eventValue],
      components: {
        id: 'traveller_page_component',
      },
    };

    const pdtLoggerObj = {
      page_context: {
        lob: 'rails',
        lob_category: 'rails',
        page_type: COMMON_PDT_CONSTANTS.PAGE_TYPE_FUNNEL,
        page_name: 'travellers',
        funnel_step: 'review',
      },
      event_tracking_context: {
        ...(await NEW_PDT_EVENT_TRACKING_CONTEXT()),
        template_id: PDT_SCHEMA_ID,
        topic_name: PDT_SCHEMA_NAME,
        ...(await getJourneyId(searchContext)),
      },
      search_context: searchContextMapper(searchContext),
      event_detail: eventDetail,
    };
    logRailsEventToPDT(pdtLoggerObj);
    return pdtLoggerObj;
  } catch (e) {
    console.error('Error in logging Traveller Page Bottom Sheet PDT event', e);
  }
};

export const logTravellerPageAddOnEvents =
  (railofyValuesTrackingParams, eventValue) => async (dispatch, getState) => {
    try {
      const state = getState();
      const { originStation, destinationStation, departureDate } = state.railsListing;
      const searchContext = {
        originStation,
        destinationStation,
        departureDate,
      };

      const eventDetail = {
        event_id: generateUUID(),
        event_timestamp: Date.now(),
        ...TRAVELLER_PDT_EVENTS?.[eventValue],
        components: {
          id: 'traveller_page_component',
        },
      };

      const { railofyOpted, fcOpted, tgpOpted, bnppOpted } = railofyValuesTrackingParams;
      const addOnDetails = (await getDataFromStorage(TRAVELLER_PDT_ADDON_DETAILS)) || [];
      const adOnTypes = { TG: railofyOpted, FC: fcOpted, TG_PLUS: tgpOpted, BNPP: bnppOpted };
      const addOnDetailsKeys = [...addOnDetails];
      addOnDetailsKeys?.forEach((x, i) => {
        if (adOnTypes[x.addontype] !== '') {
          addOnDetailsKeys[i].selected = true;
          x.selected = true;
        }
      });

      const pdtLoggerObj = {
        page_context: {
          lob: 'rails',
          lob_category: 'rails',
          page_type: COMMON_PDT_CONSTANTS.PAGE_TYPE_FUNNEL,
          page_name: 'travellers',
          funnel_step: 'review',
        },
        event_tracking_context: {
          ...(await NEW_PDT_EVENT_TRACKING_CONTEXT()),
          template_id: PDT_SCHEMA_ID,
          topic_name: PDT_SCHEMA_NAME,
          ...(await getJourneyId(searchContext)),
        },
        search_context: searchContextMapper(searchContext),
        event_detail: eventDetail,
        addon_details: addOnDetailsKeys,
      };
      logRailsEventToPDT(pdtLoggerObj);
      return pdtLoggerObj;
    } catch (e) {
      console.error('Error in logging Traveller Addon PDT event', e);
    }
  };

const fetchBookingReviewRequest = async (railsTraveler, railsListing, irctcUserName, railsReview, gstinReducer) => {
  let userName = null;
  if (_isEmpty(irctcUserName)) {
    userName = await irctcAccountUserNameAPI();
  } else {
    userName = irctcUserName;
  }
  const isRailsPostPaymentFailureEnabled = false;
  const
    {
      selectedReservationChoice, contactDetails, selectedPickupStation, considerAutoUpgradation,
      travelerInsuranceOpted, classValue, selectedQuota, selectedTravelers, travelers,
      couponData, confirmBookCheckbox, psgnDestinationAddress, cnfmGuaranteeDetail,
      originalSelectedBookingDate : {
        availablityStatus,
      },
      // railofyResponse,
      isBnppOpted,
      railofy,
    gstDetails,
    optionalGstDetailsEnabled,
    reviewAvailablityChange,
    errorFlowCode,
    railofy: { bnppResponse: bnppResponseTravelerPage },
    } = railsTraveler;
  const
    {
      selectedTrainInfo,
      childBerthMandatory,
      seniorCitizenApplicable,
    showBnpp,
    } = railsListing;
  const {routedThroughReview} = railsReview;
  const travelInsuranceOpted = travelerInsuranceOpted === TRAVEL_INSURANCE.OPTED;

  const {
    ancillaryDiscountDetails = null,
  } = couponData || {};

  const { ancillaryType = null, ancillaryDetails = null } = ancillaryDiscountDetails || {};
  const reservationChoice = getReservationChoice(selectedReservationChoice, confirmBookCheckbox);

  const tempPsgnDestinationAddress = { ...psgnDestinationAddress };
  tempPsgnDestinationAddress.pinCode = Number(tempPsgnDestinationAddress.pinCode);
  delete tempPsgnDestinationAddress.postOfficeList;


  const parentBnppPokus = getIfCanShowBNPP();
  const bnppVariantNumber = Number(getSeatLockReviewVariants());
  const bnppShownInTravellerPage =
    bnppVariantNumber === 1 &&
    getIfCanShowBNPP() &&
    showBnpp &&
    !_isEmpty(bnppResponseTravelerPage);
  const bnppShownInReviewPage =
    bnppVariantNumber !== 1 && parentBnppPokus && showBnpp && !_isEmpty(bnppResponseTravelerPage);

  let bnppRequest = null;

  if (bnppShownInTravellerPage) {
    if (railofy?.bnppResponse?.partialAmount){
      bnppRequest = {
        bnppShown: railofy?.bnppShown || true,
        bnppOpted : isBnppOpted,
        bnppEligible: true,
        partialAmount: isBnppOpted ? railofy?.bnppResponse?.partialAmount * selectedTravelers.length ?? 0 : 0,
      };
    }
  }

  if (bnppShownInReviewPage) {
    bnppRequest = {
      bnppShown: false,
      bnppOpted: false,
      bnppEligible: true,
      partialAmount: 0,
    };
  }

  const bookingReviewRequest = {
    psgnDestinationAddress:{...tempPsgnDestinationAddress},
    reviewAvailablityChange,
    source: isMweb() ? 'RNW' : 'RN',
    gatewayChargeEnabled: true,
    alternateAvailabilityBooking: (!!selectedTrainInfo.boardingStation
      && !!selectedTrainInfo.droppingStation),
    reservationChoice,
    wsUserLogin: userName,
    mobileNumber: contactDetails.mobile,
    email: contactDetails.email,
    boardingStation: _isEmpty(selectedPickupStation) ?
      ((selectedTrainInfo.boardingStation && selectedTrainInfo.boardingStation.code) ||
                  selectedTrainInfo.frmStnCode) : selectedPickupStation.stationCode,
    ...(selectedTrainInfo.droppingStation?.code && {
      droppingStation: selectedTrainInfo.droppingStation.code,
    }),
    autoUpgradationSelected: considerAutoUpgradation,
    travelInsuranceOpted,
    bookMode: bookMode(),
    railsPostPaymentFailureEnabled: isRailsPostPaymentFailureEnabled,
    clusterTrain: selectedTrainInfo.clusterTrain,
    journeyDetails: {
      source: selectedTrainInfo.frmStnCode,
      destination: selectedTrainInfo.toStnCode,
      class: classValue.code,
      quota: selectedQuota?.code,
      doj: fecha.format(selectedTrainInfo.departureDateAndTime, 'YYYYMMDD'),
      trainDetails:
        {
          trainNumber: selectedTrainInfo.trainNumber,
          departureTime: selectedTrainInfo.departureTime,
        },
    },
    passengerList: getPassengerList(
      selectedTravelers,
      travelers,
      selectedQuota,
      childBerthMandatory,
      seniorCitizenApplicable,
    ),
    ...(optionalGstDetailsEnabled && { gstDetails }),
    listingAvailabilityStatus: availablityStatus,
    bnppInfo: bnppRequest,
    // wasTgApplicable : ( !_isEmpty(railofyResponse) && !_isEmpty(railofyResponse.confirmationGuaranteeResponse)),
    routedThroughReview,
    routedThroughReviewAndTgOptedIn : (routedThroughReview && cnfmGuaranteeDetail
                                        && cnfmGuaranteeDetail.cnfmGuaranteeOpted &&
                                       cnfmGuaranteeDetail.railofyShown) ? true : false,
  };

  const {
    railofyType,
    railsConfirmationGuaranteeOption,
    railofyShown,
    zcShown,
    selectedIndex,
    cnfmGuaranteeOpted,
    freeCancellationInsuranceOpted,
    fcUpgradeOption,
    freeCancellationData,
    tripGuaranteeData,
    defaultPremiumAmount,
    premiumAmount,
    hasError,
    bnppResponse,
    tgType,
    isTGPlusSelected,
    isFcDiscounted = false,
    isTgDiscounted = false,
    fcTgSelectionType,
  } = railofy;
  const tgDiscountPremium = tripGuaranteeData?.discountedPremium ?? 0;
  const fcDiscountPremium =
    freeCancellationData?.freeCancellationPremiumDetails?.discountedPremium ?? 0;

  const travellerPageSnapshot = {
    fcTgData : {
      hasError,
      railofyShown,
      zcShown,
      defaultPremiumAmount,
      premiumAmount,
      cnfmGuaranteeOpted,
      freeCancellationInsuranceOpted,
      freeCancellationData,
      tripGuaranteeData,
    },
    couponData,
    bnppData: {
      ...bnppRequest,
      bnppResponse: bnppResponse,
    },
  };
  Object.assign(bookingReviewRequest, {travellerPageSnapshot});

  railofyValuesTrackingParams.fcShown = zcShown;
  railofyValuesTrackingParams.railofyShown = railofyShown;
  railofyValuesTrackingParams.railofyOpted = cnfmGuaranteeOpted;
  railofyValuesTrackingParams.fcOpted = freeCancellationInsuranceOpted;
  railofyValuesTrackingParams.tgpShown = getRailsTGVersion() !== 1 && railofyShown;
  railofyValuesTrackingParams.tgpOpted = isTGPlusSelected;

  if (railofyType === RAILOFY_TYPE.FC) {
    bookingReviewRequest.freeCancellationInsuranceDetail = {
      freeCancellationInsuranceOpted,
      fcVariant: fcUpgradeOption,
    };
  }
  bookingReviewRequest.fcTgSelectionType = fcTgSelectionType;
  if (railofyType === RAILOFY_TYPE.TG || railofyType === RAILOFY_TYPE.TGP || railofyType === RAILOFY_TYPE.TGPV3) {
    bookingReviewRequest.cnfmGuaranteeDetail = {
      zcShown,
      railofyShown,
      railsConfirmationGuaranteeOption,
      selectedIndex,
      cnfmGuaranteeOpted: cnfmGuaranteeOpted || isTGPlusSelected,
    };
  }

  if (fcTgSelectionType === MULTI_SELECT) {
    bookingReviewRequest.cnfmGuaranteeDetail = {
      zcShown,
      railofyShown,
      railsConfirmationGuaranteeOption,
      selectedIndex,
      cnfmGuaranteeOpted: cnfmGuaranteeOpted || isTGPlusSelected,
      isDiscounted: ancillaryType
        ? !!(
            ancillaryType === COUPON_TYPE.FC_ONLY &&
            cnfmGuaranteeOpted &&
            Boolean(ancillaryDetails?.tg?.bundleDiscountedPremium)
          )
        : tgDiscountPremium > 0 && isTgDiscounted && cnfmGuaranteeOpted,
    };
    bookingReviewRequest.freeCancellationInsuranceDetail = {
      freeCancellationInsuranceOpted,
      fcVariant: fcUpgradeOption,
      isDiscounted: ancillaryType
        ? !!(ancillaryType === COUPON_TYPE.TG_ONLY && freeCancellationInsuranceOpted &&
          Boolean(ancillaryDetails?.fc?.bundleDiscountedPremium))
        : fcDiscountPremium > 0 && isFcDiscounted && freeCancellationInsuranceOpted,
    };
  }

  if (railofyShown) {
    bookingReviewRequest.cnfmGuaranteeDetail = {
      ...bookingReviewRequest.cnfmGuaranteeDetail,
      tgType,
    };
  }

  if (couponData) {
    bookingReviewRequest.couponCode = couponData.couponCode;
    bookingReviewRequest.cdfTransactionKey = couponData.cdfTransactionKey;
    trackTravellerPageEvent('mob_rail_travellers_coupon_applied');
  }

  if (gstinReducer?.state) {
    bookingReviewRequest.gstinBillingAddress = {
      state: gstinReducer.state,
    };
  }

  if (errorFlowCode) {
    bookingReviewRequest.errorFlowCode = errorFlowCode;
  }

  trackShownAndOptedValues(railofyValuesTrackingParams);
  return bookingReviewRequest;
};

export const getInsuranceCharge = jsonResponse => (parseFloat(jsonResponse.travelInsuranceCharge) +
                    parseFloat(jsonResponse.travelInsuranceServiceTax)).toFixed(2);

export const checkPaymentsCallWithUniqueMmtId = async () => {
  let clickCount = await getDataFromAsynStorage(REVIEW_BUTTON_CLICK_COUNT);
  if (_isEmpty(clickCount)) {
    clickCount = {};
  }
  const mmtID = await getMmtId();
  clickCount[mmtID] = true;
  setDataToAsyncStorage(REVIEW_BUTTON_CLICK_COUNT, clickCount);
};

export const checkForMultipleReviewClickCount = (newCheckoutApi = false,
              isScheduleTatkalBooking = false) => async (dispatch) => {
  const clickCount = await getDataFromAsynStorage(REVIEW_BUTTON_CLICK_COUNT);
  const mmtID = await getMmtId();
  if (!clickCount?.[mmtID] || newCheckoutApi || isScheduleTatkalBooking) {
    dispatch(openPayment());
  } else {
    dispatch(fetchBookingReview(true));
  }
};

export const fetchBookingReview =
  (skipReviewPageDueToClickCount = false) =>
  async (dispatch, getState) => {
  const {
    railsReview,
    railsListing,
    railsTraveler,
    railsUserVerification: {irctcUserName},
    railsLanding: {cmp: bookingSource},
    bnplReducer: { railsBnplAmount },
    gstinReducer,
  } = getState();
  try {
    const _bookingReviewRequest = await fetchBookingReviewRequest(railsTraveler, railsListing,
          irctcUserName,railsReview, gstinReducer);
      const pokusState = {
        [RailsAbConfig?.railsReviewIrctcBs]: showRailsReviewIrctcBs() || 0,
        [RailsAbConfig?.railsSocialProofingFreeCancellation]: showSocialProofingFc() || 0,
        [RailsAbConfig?.railsSocialProofingTripGuarantee]: showSocialProofingTg() || 0,
        [RailsAbConfig.railsCouponExperiment]: railsCouponExperiment() || 0,
        [RailsAbConfig.railsReviewErrorAlertsTimer]: getRailsReviewErrorAlertsTimer() || 0,
        [RailsAbConfig.tgRacPremiumVariant]: getTgRacPremiumVariant() || false,
    };
      const bookingReviewRequest = {
        ..._bookingReviewRequest,
        bnplEligibleAmount: railsBnplAmount,
        pokusState,
      };

    dispatch({
      type: ACTION_BOOKING_REVIEW_LOAD,
      data: {
        bookingReviewRequest,
        },
      });
      dispatch(setAlertApiSuccess(false));

      const jsonResponse = await getBookingReview(
        bookingReviewRequest,
        railsTraveler,
        bookingSource,
      );
      uploadGrafanaMetrics();
      const showRailsError = await getRailsErrorHandlingConfig();
      const showRouteExtensionBottomSheetPokus = getRouteExtensionBottomSheetVariants();

      const { errorMessage = null, mmtId = null, paymentInfo = null } = jsonResponse ?? {};
      dispatch(trackPdtAfterLoadReviewPage(jsonResponse));
      dispatch(reviewStatus(jsonResponse, railsListing, railsTraveler, bookingReviewRequest));
      if (jsonResponse?.errorFlow && showRailsError) {
        if (
          jsonResponse?.errorDetails?.errorCode === 'RAILS_BOOKING_REVIEW_AADHAR_VERIFICATION_ERROR'
        ) {
          trackGenericEvar99Event(
            REVIEW_PAGE_TRACKING_KEY,
            RAIL_EVENTS.REVIEW.RAILS_REVIEW_AADHAR_LINK_CLICKED,
          );
        }
        const ctas = jsonResponse?.errorFlow?.ctas;
        const isRouteExtension = ctas?.find(
          (cta) => cta?.code === 'RETRY_ALTERNATE_AVAILABILITY_BOOKING',
        );

        if (isRouteExtension) {
          removeEventFromEvar47or97Variable(
            RAIL_EVENTS.TRAVELLER.RAILS_TRAVELER_RE_BOTTOMSHEET_VARIANT_1,
          );
          removeEventFromEvar47or97Variable(
            RAIL_EVENTS.TRAVELLER.RAILS_TRAVELER_RE_BOTTOMSHEET_VARIANT_2,
          );

          if (showRouteExtensionBottomSheetPokus === 1) {
            trackGenericEvar47or97Event(
              TRAVELERS_PAGE_TRACKING_KEY_NEW,
              RAIL_EVENTS.TRAVELLER.RAILS_TRAVELER_RE_BOTTOMSHEET_VARIANT_1,
            );
          } else if (showRouteExtensionBottomSheetPokus === 2) {
            trackGenericEvar47or97Event(
              TRAVELERS_PAGE_TRACKING_KEY_NEW,
              RAIL_EVENTS.TRAVELLER.RAILS_TRAVELER_RE_BOTTOMSHEET_VARIANT_2,
            );
          }
        }

          if (errorMessage) {
            trackReviewErrorPage(errorMessage);
          } else if (_isEmpty(mmtId) || _isEmpty(paymentInfo?.checkoutId)) {
            trackReviewErrorPage(ERROR_MESSAGE_AVAILABILITY_STATUS);
          }

          const count = await getDataFromAsynStorage(RAILS_ERROR_RETRY_COUNT);
        const errorFlowObject = {
          ...jsonResponse?.errorFlow,
          errorCode: jsonResponse?.errorDetails?.errorCode,
        };
          if (count >= 2) {
            errorFlowObject.ctas = errorFlowObject.ctas.filter((cta) => cta.action !== 'RETRY');
          }

          dispatch(displayErrorBottomSheet(errorFlowObject));

          dispatch({
            type: ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW,
            data: { loadingDuplicateBookingAndReview: false },
          });
          return;
      }

      if (jsonResponse?.errorDetails?.errorCode === REVIEW_DOWNTIME_ERROR_CODE) {
        dispatch(displayErrorBottomSheet(null));

        if (errorMessage) {
          trackReviewErrorPage(errorMessage);
        } else if (_isEmpty(mmtId) || _isEmpty(paymentInfo?.checkoutId)) {
          trackReviewErrorPage(ERROR_MESSAGE_AVAILABILITY_STATUS);
        }
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, RAILS_DOWNTIME_TRIGGERED);
        dispatch(displayDownTimeBottomSheet(true));
        dispatch({
          type: ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW,
          data: { loadingDuplicateBookingAndReview: false },
        });
        return;
      }

      dispatch(displayErrorBottomSheet(null));

      dispatch({
        type: ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW,
        data: { loadingDuplicateBookingAndReview: false },
    });

      const { showReview = false } = jsonResponse ?? {};
      getReviewAvailibilityStatusChangeEvent(railsTraveler, jsonResponse);
    const {UserSessionModule} = NativeModules;
    if (UserSessionModule && UserSessionModule?.refreshUserData) {
      UserSessionModule?.refreshUserData();
    }

      const foodSelected = bookingReviewRequest.passengerList.some((passenger) => {
        return passenger.passengerFoodChoice && passenger.passengerFoodChoice !== 'D';
      });
      if (foodSelected) {
        removeEventFromEvar99Variable(RAIL_EVENTS.REVIEW.RAILS_REVIEW_MEALS_SELECTED);
        trackGenericEvar99Event(
          REVIEW_PAGE_TRACKING_KEY,
          RAIL_EVENTS.REVIEW.RAILS_REVIEW_MEALS_SELECTED,
        );
      }
    const isNullResponseFailed = _isNull(jsonResponse);
    const errorResponse = !_isNull(jsonResponse) && (!_isUndefined(jsonResponse.errorMessage)
              || _isEmpty(jsonResponse.mmtId)
              || _isEmpty(jsonResponse.paymentInfo)
              || _isEmpty(jsonResponse.paymentInfo.checkoutId));

    const isBookingFailed = isNullResponseFailed || errorResponse;
      dispatch({
        type: ACTION_REVIEW_VERIFICATION_FLOW,
        data: jsonResponse?.reviewVerificationFlow ?? null,
      });

    let skipReviewPage = await getPokusConfig(PokusLobs.RAIL, AbConfigKeyMappings.skipReviewPage, false);
      trackTravellerBookNow(`${AbConfigKeyMappings.skipReviewPage}_${skipReviewPage}`);
      if (skipReviewPageDueToClickCount || (skipReviewPage && !showReview && !isBookingFailed)) {
        dispatch(openPayment());
      } else {
        dispatch(getReviewHeaderText());
        Actions.railsReviewPage();
      }

      if (
        !_isEmpty(jsonResponse?.interventions?.irctcPasswordBottomSheet) &&
        Boolean(jsonResponse?.interventions?.irctcPasswordBottomSheet?.eligible)
      ) {
        trackGenericEvar47or97Event(
          REVIEW_PAGE_TRACKING_KEY,
          `${RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_EVENT}_${jsonResponse?.interventions?.irctcPasswordBottomSheet?.display}`,
        );
      }

      const { railofy } = railsTraveler;

      const {
        cnfmGuaranteeOpted: railofyOpted,
        freeCancellationInsuranceOpted: fcOpted,
        isTGPlusSelected: tgpOpted,
      } = railofy;
      const bnppOpted = railsTraveler?.isBnppOpted;
      const pdtConfig = await getTravellerPdtConfig();
      if (pdtConfig) {
        dispatch(
          pdtLoggingTravellerOnClick({
            railofyOpted,
            fcOpted,
            tgpOpted,
            bnppOpted,
            totalFare: jsonResponse?.totalFare,
          }),
        );
      }
      if (jsonResponse?.eligibleAddOns?.bnppData) {
        dispatch({
          type: ACTION_BNPP_RESPONSE_UPDATE,
          data: jsonResponse?.eligibleAddOns?.bnppData,
        });
      }
      uploadGrafanaMetrics();
  } catch (e) {
    trackReviewErrorPage(ERROR_MESSAGE_AVAILABILITY_STATUS);
    dispatch({
      type: ACTION_SHOW_LOADER_FOR_DUPLICATE_AND_REVIEW,
      data: { loadingDuplicateBookingAndReview: false },
    });
    dispatch({
      type: ACTION_BOOKING_REVIEW_FAILURE,
      data: {
        bookingReviewSuccess: false,
        errorMessage: _label(ERROR_MESSAGE_AVAILABILITY_STATUS),
      },
    });
      dispatch(displayErrorBottomSheet(ERROR_FLOW));
    }
};

export const reviewStatus =  (jsonResponse, railsListing, railsTraveler,bookingReviewRequest) => {
  if (_isNull(jsonResponse)) {
    trackReviewErrorPage(ERROR_MESSAGE_AVAILABILITY_STATUS);
    return ({
      type: ACTION_BOOKING_REVIEW_FAILURE,
      data: {
        bookingReviewSuccess: false,
        errorMessage: _label(ERROR_MESSAGE_AVAILABILITY_STATUS),
      },
    });
  } else if (!_isUndefined(jsonResponse?.errorMessage)) {
    trackReviewErrorPage(jsonResponse?.errorMessage);
    return ({
      type: ACTION_BOOKING_REVIEW_FAILURE,
      data: {
        bookingReviewSuccess: false,
        errorMessage: jsonResponse?.errorMessage,
      },
    });
  }
  if (!_isUndefined(jsonResponse?.errorMessage)
    || _isEmpty(jsonResponse?.mmtId)) {
    trackReviewErrorPage(ERROR_MESSAGE_AVAILABILITY_STATUS);
    return ({
      type: ACTION_BOOKING_REVIEW_FAILURE,
      data: {
        bookingReviewSuccess: false,
        errorMessage: _label(ERROR_MESSAGE_AVAILABILITY_STATUS),
      },
    });
  }

  trackAndSetDataForReviewPageLoad(getReviewPageCommonData(jsonResponse, railsListing, railsTraveler));
  tuneRailsReviewLoadTracker(getTuneDataForReview(jsonResponse, railsListing, railsTraveler));
  setDataToAsyncStorage(RAILS_MMT_ID, jsonResponse?.mmtId);
  const reviewData = {bookingReviewRequest,jsonResponse};
  const { originStation, destinationStation, departureDate } = railsListing;
  firebaseReviewTracker({originStation, destinationStation, departureDate},reviewData);
  return ({
    type: ACTION_BOOKING_REVIEW_SUCCESS,
    data: {
      reviewTimeStamp: Date.now(),
      bookingReviewSuccess: true,
      errorMessage: null,
      bookingReviewResponse: jsonResponse,
      currentAvailabilityStatus: jsonResponse?.avlDayList[0]?.availablityStatus,
      baseFare: jsonResponse?.mmtBaseFare,
      gstPrice: 0,
      mmtServiceCharge: jsonResponse?.mmtServiceFee,
      totalCollectibleAmount: jsonResponse?.totalCollectibleAmount,
      paymentInfo: jsonResponse?.paymentInfo,
      insuranceCharge: getInsuranceCharge(jsonResponse),
      durationFromBoardingStation: jsonResponse?.durationFromBoardingStation,
      dueAmount: jsonResponse?.dueAmount,
      dueDate: jsonResponse?.dueDate,
      displayIrctcPassBottomsheet:
        jsonResponse?.interventions?.irctcPasswordBottomSheet?.display || 0,
      isIrctcProfileComplete: jsonResponse?.interventions?.isIrctcProfileComplete ?? true,
    },
  });

};

export const getReviewPageCommonData = (jsonResponse, railsListing, railsTraveler) => {
  const {
    selectedTrainInfo: { trainNumber, trainName },
  } = railsListing;
  const {selectedTravelers, classValue, selectedQuota} = railsTraveler;
  const numberOfPassengers = selectedTravelers.length;

  return `${trainName}|${trainNumber}|${selectedQuota?.code}|${classValue.code}|${jsonResponse.avlDayList[0].availablityStatus}|${numberOfPassengers}`;
};

export const getTuneDataForReview = (jsonResponse, railsListing, railsTraveler) => {
  try {
    const {selectedTrainInfo} = railsListing;
    const {trainNumber, duration, departureTime} = selectedTrainInfo;
    const {selectedTravelers, classValue, selectedQuota} = railsTraveler;
    const {originStation, destinationStation, selectedBookingDate} = railsTraveler;

    const numberOfPassengers = selectedTravelers.length;
    const departureDateAndTime = fecha.parse(selectedBookingDate.availablityDate, 'DD-MM-YYYY');
    const departureTimeArray = departureTime.split(':');
    departureDateAndTime.setHours(departureTimeArray[0]);
    departureDateAndTime.setMinutes(departureTimeArray[1]);
    const arrivalDateAndTime = addMinutes(departureDateAndTime, duration);
    const departureFormat = fecha.format(departureDateAndTime, 'DD-MM-YYYY HH:mm');
    const arrivalFormat = fecha.format(arrivalDateAndTime, 'DD-MM-YYYY HH:mm');
    const seatAvailability = jsonResponse.avlDayList[0].availablityStatus;

    const fromTo = `${originStation.cityName}|${destinationStation.cityName}`;
    const date = selectedBookingDate.availablityDate;
    const trainNameDuration = `${trainNumber}|${departureFormat}|${arrivalFormat}`;
    const paxCountClassQuotaAvail = `${numberOfPassengers}|${selectedQuota?.code}|${classValue.code}|${seatAvailability}`;
    const price = jsonResponse.totalCollectibleAmount;
    const mmtId = jsonResponse.mmtId;
    return {
      fromTo, date, trainNameDuration, paxCountClassQuotaAvail, price, mmtId,
    };
  } catch (e) {
    console.log('error is ', e);
  }
};
export const trackPdtAfterLoadReviewPage = (listingResponse) => async (dispatch, getState) => {
  const { railsTraveler, railsListing, railsLanding } = getState();
  if (!railsTraveler || !railsListing)
    {
      return;
    }
  const {
    selectedPickupStation, contactDetails,
  } = railsTraveler;
  const {
    selectedTrainInfo, selectedBookingDate, selectedQuota,
    selectedClassType, totalCollectibleAmount,
  } = railsListing;
  let listingErrorCodeString = null;
  if (listingResponse?.errorDetails?.errorCode) {
    switch (listingResponse.errorDetails.errorCode) {
      case REVIEW_DOWNTIME_ERROR_CODE:
        listingErrorCodeString = 'IRCTC_SCHEDULED_DOWNTIME_WINDOW';
        break;
      case RAILS_BOOKING_REVIEW_ERROR_07_CODE:
        listingErrorCodeString = 'IRCTC_TATKAL_AC_BLOCKED_WINDOW';
        break;
      case RAILS_BOOKING_REVIEW_ERROR_08_CODE:
        listingErrorCodeString = 'IRCTC_TATKAL_NONAC_BLOCKED_WINDOW';
        break;
      default:
        break;
    }
  }
  const selectedTbsCard =
      selectedTrainInfo.tbsAvailability.find(
          (element) => ( (element.className === selectedClassType) && (element.quota === selectedQuota.id)
          ));
  if (!(getState().railsLanding))
    {
      return;
    }
  const searchContextData = getCommonSearchContextData(getState().railsLanding);
  const searchData = {
    selectedTrainInfo,
    selectedBookingDate,
    selectedQuota,
    selectedClassType,
    totalCollectibleAmount,
    selectedPickupStation,
    ...searchContextData,
  };
  if (!(getState().railsReview)) {
    return;
  }
  const { bookingReviewRequest } = getState().railsReview;
  if (!bookingReviewRequest)
    {
      return;
    }
  let cnfmGuaranteeOpted = null;
  let  railsConfirmationGuaranteeOption = null;
  let  railofyShown = false;
  if (bookingReviewRequest && bookingReviewRequest.cnfmGuaranteeDetail) {
    cnfmGuaranteeOpted = bookingReviewRequest.cnfmGuaranteeDetail.cnfmGuaranteeOpted;
    railsConfirmationGuaranteeOption = bookingReviewRequest.cnfmGuaranteeDetail.railsConfirmationGuaranteeOption;
    railofyShown = bookingReviewRequest.cnfmGuaranteeDetail.railofyShown;
  }
  const {freeCancellationInsuranceDetail = null , railofyResponse = null} = getState().railsTraveler;
  let confirmation_gurantee_available = false;
  let zero_cancellation_available = false;
  let confirmation_gurantee_opted = false;
  let zero_cancellation_opted = false;
  let confirmation_premium_variant = null;
  let confirmation_gurantee_premium_amount = null;
  let zero_cancellation_premium_amount = null;
  const confirmation_probablity = (selectedTbsCard) ? selectedTbsCard.predictionPercentage : null;
  if (railofyShown) {
    confirmation_premium_variant = railsConfirmationGuaranteeOption;
    confirmation_gurantee_available = true;
    if (cnfmGuaranteeOpted) {
      confirmation_gurantee_opted = true;
    }
  }
  if (freeCancellationInsuranceDetail) {
    zero_cancellation_available = freeCancellationInsuranceDetail.freeCancellationEnabled;
    zero_cancellation_opted = freeCancellationInsuranceDetail.freeCancellationInsuranceOpted;
  }
  if (railofyResponse){
    const {freeCancellationResponse,confirmationGuaranteeResponse} = railofyResponse;
    if (freeCancellationResponse) {
      zero_cancellation_premium_amount = freeCancellationResponse.cancellationOptions[0].insuranceAmount;
    }
    if (confirmationGuaranteeResponse) {
      confirmation_gurantee_premium_amount = (
          (confirmationGuaranteeResponse.preferenceInfoRevamped) ?
          (confirmationGuaranteeResponse.preferenceInfoRevamped[0].premiumPerPerson) :
          (0));
    }
  }
  const railofyAndFreeCancellationData = {
    confirmation_gurantee_available,
    zero_cancellation_available,
    confirmation_gurantee_opted,
    zero_cancellation_opted,
    confirmation_premium_variant,
    confirmation_gurantee_premium_amount,
    zero_cancellation_premium_amount,
    confirmation_probablity,
  };
  const extraData =  {selectedQuota,totalCollectibleAmount,selectedClassType,selectedTrainInfo};
  try {
    const fireBaseTrackingData = {
      railsLanding: {
        ...railsLanding,
        departureDate: fecha.format(railsLanding?.departureDate, 'DD-MM-YYYY'),
      },
      extraData,
    };
    setDataToAsyncStorage('fireBaseTrackingData', fireBaseTrackingData);
  } catch (error) {
    console.error('Error in saving fireBaseTrackingData', error);
  }
  const searchContextDataForThankYou = {
    selectedTrainInfo, selectedBookingDate, selectedQuota,
    selectedClassType, totalCollectibleAmount,
  };

  const pdtDataForThankYou = {
    searchContextData : {
      ...searchContextDataForThankYou,
      ...getCommonSearchContextData(railsLanding),
    },
    bookingData : {
      ...railofyAndFreeCancellationData,
    },
  };
  setDataToAsyncStorage('pdtDataForThankYou',pdtDataForThankYou);
  const data = {
    listingErrorCodeString,
    searchData,
    travellerData: {
      ...contactDetails,
      ...railofyAndFreeCancellationData,
    },
  };
  dispatch({
    type: ACTION_SET_RAILOFY_FREECANCELLATION_DATA,
    data : {
      railofyAndFreeCancellationData,
    },
  });
  trackPdtReviewLoad(data);
};
export const loadReviewPage = async (dispatch) => {
    await dispatch(fetchBookingReview());
};
export const unloadReviewPage = (hasStatusChangedFCtoTG, hasStatusChangedTGtoFC ) => (dispatch) => {
  if (hasStatusChangedFCtoTG || hasStatusChangedTGtoFC) {
    dispatch(fetchRailofyDataIfNeeded(hasStatusChangedFCtoTG, hasStatusChangedTGtoFC));
  }
  dispatch({
    type: ACTION_UNLOAD_BOOKING_REVIEW,
  });
};


export const getRailsLandingData = async (originStationCode, destStationCode, departureDate) => {
  try {
    const originStationList = await getAutoCompleteCities({text : originStationCode});
    const destinationStationList = await getAutoCompleteCities({text : destStationCode});
    return {
      originStation: originStationList.find(station => station.code.toLowerCase() === originStationCode.toLowerCase()),
      destinationStation:
          destinationStationList.find(station => station.code.toLowerCase() === destStationCode.toLowerCase()),
      departureDate,
    };
  } catch (e) {
    throw new Error('getAutoCompleteCities API Failed');
  }
};

const getTravelersData = async (
  boardingStation, traveler, applicableBerthTypes,
  foodChoiceEnabled, selectedQuota, classValue,
  contactDetails, selectedReservationChoice,
  considerAutoUpgradation, travelerInsuranceOpted,
  totalCollectibleAmount,
  seniorCitizenApplicable,
  applicableFoodTypes,
) => {
  /*
  Adding index to id of traveler so that each id of traveler is unique as many get
   created at the same time.
   */
  const travelerArray = traveler.map((t, index) => new Traveler(
    new Date().getTime() + index, getNameObj(t.name), t.age, getGenderfromValue(t.gender), t.childBerthFlag,
        applicableBerthTypes,
        foodChoiceEnabled,
        selectedQuota,
        classValue,
        true,
        seniorCitizenApplicable,
        getBerthType(t.berth),
        getMealObject(t.meal, applicableFoodTypes),
        getSrCitizenObject(t.seniorCitizenConcession),
    allCountries.find(obj => obj.code === t.nationality),
  ));

  const selectedTravelers = travelerArray.map(t => t.travelerId);

  const isLoggedIn = await isUserLoggedIn();
  const userDetails = await loadUserDetails(isLoggedIn);

  return {
    selectedPickupStation: boardingStation,
    travelers: travelerArray,
    contactDetails,
    selectedQuota,
    classValue,
    selectedReservationChoice: getRsChoiceByValue(selectedReservationChoice),
    considerAutoUpgradation,
    travelerInsuranceOpted,
    totalCollectibleAmount,
    selectedTravelers,
    isLoggedIn,
    userDetails,
  };
};

const getListingData = (
  {originStation, destinationStation, departureDate},
  selectedTrainInfo, bookingConfig,
) => ({
  originStation,
  destinationStation,
  departureDate,
  selectedTrainInfo,
  selectedBookingDate:
      {
        availablityDate: fecha.format(departureDate, 'DD-MM-YYYY'),
        availablityStatus: 'GNWL10/WL10',
      },
  ...bookingConfig,
  childBerthMandatory: bookingConfig.childBerthMandatory === 'true',
  seniorCitizenApplicable: bookingConfig.seniorCitizenApplicable === 'true',
});

export const getDateObject = (journeyDate, departureTime) => {
  const splitDate = journeyDate.split('T')[0].split('-');
  const splitTime = departureTime.split(':');
  return (new Date(splitDate[0], splitDate[1] - 1, splitDate[2], splitTime[0], splitTime[1]));
};

export const setAllReqData = (_data, mmtId) => async (dispatch, getState) => {
  try {
    const res = await fetch2(railsConfig.ppfReview + mmtId);

    if (!_isUndefined(res.status) && res.status !== 200) {
      throw new Error('wrong Status code');
    }

    const response = await res.json();

    if (!_isUndefined(response.status) && response.status !== 200) {
      throw new Error('wrong Status code');
    }

    const {
      railsUserVerification: { irctcUserName },
      railsListing: { applicableFoodTypes },
    } = getState();
    if (irctcUserName) {
      dispatch(updateUsername(irctcUserName));
    } else {
      const userName = await irctcAccountUserNameAPI();
      dispatch(updateUsername(userName));
    }

    const {
      fromStn: {code: originStationCode},
      destStn: {code: destinationStationCode},
      journeyDate,
      boardingStn, psgnDetailList,
      bookingConfig,
      journeyQuota: selectedQuota,
      journeyClass: classValue,
      email, mobile, reservationChoice: selectedReservationChoice,
      autoUpgradationSelected: considerAutoUpgradation, travelInsuranceOpted: travelerInsuranceOpted,
      totalCollectibleAmount,
    } = response;
    const departureDate = fecha.parse(journeyDate.split('T')[0], 'YYYY-MM-DD');
    const boardingStation = {
      stationCode: boardingStn.code,
      stationName: boardingStn.name,
    };
    const {applicableBerthTypes, foodChoiceEnabled, seniorCitizenApplicable} = bookingConfig;
    const selectedTrainInfo = {
      arrivalTime: response.arrivalTime,
      duration: 1020,
      frmStnCode: response.fromStn.code,
      frmStnName: response.fromStn.name,
      toStnCode: response.destStn.code,
      toStnName: response.destStn.name,
      trainName: response.trainName,
      trainNumber: response.trainNumber,
      departureDateAndTime: getDateObject(response.journeyDate, response.departureTime),
      arrivalDateAndTime: getDateObject(response.destArrvDate, response.arrivalTime),
      departureTime: response.departureTime,
    };
    const contactDetails = {
      email,
      mobile,
    };
    const traveler = psgnDetailList.map(psgn => ({
      name: psgn.passengerName,
      age: psgn.passengerAge,
      gender: psgn.passengerGender,
      childBerthFlag: psgn.childBerthFlag,
      berth: psgn.passengerBerthChoice,
      meal: _isEmpty(psgn.passengerFoodChoice) ? DEFAULT_FOOD_CHOICE : psgn.passengerFoodChoice,
      seniorCitizenConcession: psgn.concessionOpted,
      nationality: psgn.passengerNationality,
    }));
    const railsLanding = await getRailsLandingData(originStationCode, destinationStationCode, departureDate);
    const railsListing = getListingData(railsLanding, selectedTrainInfo, bookingConfig);
    const railsTraveler = await getTravelersData(
      boardingStation, traveler, applicableBerthTypes, foodChoiceEnabled,
      getQuota(selectedQuota), getClassType(classValue), contactDetails, selectedReservationChoice,
      considerAutoUpgradation, travelerInsuranceOpted, totalCollectibleAmount,
      seniorCitizenApplicable,
      applicableFoodTypes,
    );

    dispatch({
      type: ACTION_SET_PPBOOKING_LANDING_DATA,
      data: railsLanding,
    });
    dispatch({
      type: ACTION_SET_PPBOOKING_LISTING_DATA,
      data: railsListing,
    });
    dispatch({
      type: ACTION_SET_PPBOOKING_TRAVELER_DATA,
      data: railsTraveler,
    });
    // todo uncomment it once it is decided when will u go to the forgot password flow
    // if (_.includes(data, 1)) {
    //   Actions.retrievePassword({
    //     userName,
    //     type: 'replace',
    //     postPaymentBooking: true
    //   });
    // } else {
    Actions.railsReviewPage({
      type: 'replace',
      postPaymentBooking: true,
    });
    // }
  } catch (e) {
    console.log('error is', e);
    Actions.rails({type: 'replace', postPaymentBooking: true});
    setTimeout(() => {
      showShortToast(_label('something_went_wrong_retry') + ' Error Code: ' + e?.errorCode);
    }, 0);
  }
};

export const setBnppShownOnReviewPage = (showBnpp) => (dispatch) => {
  dispatch({
    type: ACTION_SHOWN_REVIEW_BNPP,
    data: showBnpp,
  });
};


export const showPageLoaderAction = () => (dispatch) => {
  dispatch({
    type: ACTION_SHOW_PAYNOW_LOADER,
    data: null,
  });
};

export const hidePageLoaderAction = () => (dispatch) => {
  dispatch({
    type: ACTION_HIDE_PAYNOW_LOADER,
    data: null,
  });
};

export const setAlertApiSuccess = (success) => (dispatch) => {
  dispatch({
    type: ACTION_SET_ALERT_API_SUCCESS,
    data: { setAlertApiSuccess: success },
  });
};

/**
 *
 * Pdt related actions
 *
 */

export const trackPdtReviewLoad = async (data) => {
  let {searchData, travellerData} = data;
  let temp = {...travellerData};
  const {eComId = '', mComId = ''} = await getComIds(temp.email, temp.mobile);
  temp.email = eComId;
  temp.mobile = mComId;
  travellerData = {...travellerData,...temp};
  const commonPdt = await getCommonPdtData();
  const pdtData = {
    ...commonPdt,
    searchData,
    travellerData,
    listingErrorCodeString: data?.listingErrorCodeString,
  };
  RailsPdtReviewHelper.trackPageLoad(pdtData);
};

const getReviewAvailibilityStatusChangeEvent = (railsTraveler, bookingResponse) => {
  const travellerAvailabilityStatus =
    railsTraveler?.originalSelectedBookingDate?.availablityStatus?.toUpperCase() || '';
  const bookingAvailabilityStatus =
    bookingResponse?.avlDayList?.[0]?.availablityStatus?.split('/') || [];
  const currentAvailabilityStatus =
    bookingAvailabilityStatus?.[1]?.toUpperCase() ||
    bookingAvailabilityStatus?.[0]?.toUpperCase() ||
    '';
  if (
    travellerAvailabilityStatus?.includes(AVAILABILITY_STATUS.AVAILABLE) &&
    currentAvailabilityStatus?.includes(AVAILABILITY_STATUS.WL)
  ) {
    trackReviewAvailibilityChange(AVAILABILITY_STATUS_CHANGE_OMNITURE_EVENTS.AVL_TO_WL);
  } else if (
    travellerAvailabilityStatus?.includes(AVAILABILITY_STATUS.RAC) &&
    currentAvailabilityStatus?.includes(AVAILABILITY_STATUS.WL)
  ) {
    trackReviewAvailibilityChange(AVAILABILITY_STATUS_CHANGE_OMNITURE_EVENTS.RAC_TO_WL);
  } else if (
    travellerAvailabilityStatus?.includes(AVAILABILITY_STATUS.AVAILABLE) &&
    currentAvailabilityStatus?.includes(AVAILABILITY_STATUS.RAC)
  ) {
    trackReviewAvailibilityChange(AVAILABILITY_STATUS_CHANGE_OMNITURE_EVENTS.AVL_TO_RAC);
  }
};
