import React from 'react';
import { View, TouchableOpacity, Animated, StyleSheet } from 'react-native';
import { useAnimatedBottom } from 'apps/rails/src/pages/Common/useAnimatedBottom';
import BookNowPayPartial from 'apps/rails/src/pages/TravelerDetails/Components/BookNowPayPartial/BookNowPayPartial';
import { getSeatLockReviewVariants } from 'apps/rails/src/RailsAbConfig';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

const styles = StyleSheet.create({
  fullFlex: {
    flex: 1,
    zIndex: 300,
    position: 'absolute',
    top:0,
    left:0,
    right:0,
    bottom:50,
    backgroundColor: colors.lightBlack,
  },
  bottomTouchable: {
    ...StyleSheet.absoluteFillObject,
  },
  bottomSheetContentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomSheetBackground: {
    width: '100%',
    justifyContent: 'flex-end',
  },
});

const RailsReviewBnppBottomSheet = ({
  showBNPPBottomSheet,
  toggleBottomSheet,
}: {
  showBNPPBottomSheet: boolean;
  toggleBottomSheet: () => void;
}) => {
  const bottom = useAnimatedBottom(showBNPPBottomSheet, 200);
  const seatLockReviewVariant = Number(getSeatLockReviewVariants());

  if (seatLockReviewVariant !== 2) {
    return null;
  }

  if (!showBNPPBottomSheet) {
    return null;
  }

  return (
    <View style={styles.fullFlex} testID="bnpp_bottomsheet_container">
      <TouchableOpacity
        style={styles.bottomTouchable}
        onPress={toggleBottomSheet}
        testID="bnpp_bottomsheet_touchable"
      >
        <View />
      </TouchableOpacity>
      <View
        style={styles.bottomSheetContentContainer}
        pointerEvents="box-none"
        testID="bnpp_bottomsheet_content_container"
      >
        <Animated.View style={[styles.bottomSheetBackground, { bottom }]}>
          <BookNowPayPartial isInBottomSheet={true} closeBottomSheet={toggleBottomSheet} />
        </Animated.View>
      </View>
    </View>
  );
};

export default RailsReviewBnppBottomSheet;
