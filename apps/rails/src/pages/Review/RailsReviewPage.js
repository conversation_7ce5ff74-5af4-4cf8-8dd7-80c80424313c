import PropTypes from 'prop-types';
import { Actions } from '../../navigation';
import isEmpty from 'lodash/isEmpty';
import React, { useState, useEffect } from 'react';
import ScreenWrapper from '@mmt/rails/src/pages/Review/Components/ReviewScreenWrapper';
import { ScrollView, StyleSheet, Text, View, BackHandler, Image, Modal, TouchableWithoutFeedback } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import ReviewVerificationFlow from './Components/ReviewVerificationFlow';
import CustomizedTripHeader from '@mmt/legacy-commons/Common/Components/Header/CustomizedTripHeader';
import TrainDetailsCard from '../Constants/TrainDetailsCard';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import BookNowButton from '../Common/BookNow';
import fecha from 'fecha';
import {
  numAppendedWithRuppeeSymbolWithoutRound,
  roundToTwoDec,
} from '@mmt/legacy-commons/Common/utils/NumberUtils';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TravelerCard from '../TravelerDetails/Components/TravelerCard';
import ReviewTimer from './Components/ReviewTimer';
import {
  REVIEW_PAGE_CONST,
  EXPAND_CONST,
  COLLAPSE_CONST,
  TRAVELERS_TG_ELIGIBILITY_CHANGE,
  TRAVELERS_FC_ELIGIBILITY_CHANGE,
  TICKET_CHARGE,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  AVAILABILITY_STATUS,
} from '../../Utils/RailsConstant';
import BookingReviewError from './Components/BookingReviewError';
import RailsToolTip from '../Common/RailsToolTip';
import WalletBanner from '../Common/WalletBanner';
import {RailsListingUtilsClass} from '../TravelerDetails/TravelerDetailsUtils';
import {pushReviewIntoGtm} from "../../GtmConfig/gtmConfig";
import {
  removeEventFromEvar99Variable,
  trackGenericEvar99Event,
  trackReviewPageBackPress,
  trackReviewPageClickEvent,
} from '../../railsAnalytics';
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText'
import {getRailsLanguageByKey, TAM, _label} from "../../vernacular/AppLanguage";
import {fontStyle, getLineHeight} from "../../vernacular/VernacularUtils";
import { isMweb } from '../../Utils/device';
import { 
  getRailsAvlToWlMessage,
  getReviewPageBackButtonResistanceBottomsheet, 
  getReviewPagePriceBreakupCollapsed,
  getSeatLockReviewVariants,
  showProfileIncompleteFlowPokus,
  showRailsReviewIrctcBs,
} from '../../RailsAbConfig';
import { RailsSimpleBottomSheetContent } from '../TravelerDetails/Components/Common/RailsSimpleBottomSheetContent';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import RAIL_EVENTS  from '../../RailsOmnitureTracker';
import { BookNowPayPartialStrip } from '../TravelerDetails/Components/BookNowPayPartial';
import BookNowPayPartial from 'apps/rails/src/pages/TravelerDetails/Components/BookNowPayPartial/BookNowPayPartial';
import RailsReviewBnppBottomSheet from './RailsReviewBnppBottomSheet';
import RailsReviewFareBreakupBottomSheet from './RailsReviewFareBreakupBottomSheet';
import { REVIEW_PAGE_TRACKING_KEY, SEATLOCK_REVIEW_TRACKING_EVENTS } from 'apps/rails/src/Utils/RailsConstant';
import { reviewPDTEvent } from '../../PdtAnalytics/PdtAnalyticsV2/PdtRailsReview/index';
import { getRailsReviewNewPdtLoggingOnOff } from '../../../src/RailsAbConfig';
import IrctcPassEducationBottomsheet from './Components/IrctcPassEducationBottomsheet/IrctcPassEducationBottomsheet';
import RailRetrievePasswordContainer from '../Common/RailRetrievePasswordContainer';
import UserNameModal from '../NewUserFlow/UserNameModal';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../configStore/Common/constants';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { TRAVELLER_CLICK_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import IrctcProfileIncompleteBottomsheet from './Components/IrctcProfileIncompleteBottomsheet/IrctcProfileIncompleteBottomsheet';
import ErrorBoundary from 'apps/rails/src/pages/GenericErrorBoundary';
import { PAGE } from '../../types/grafanaTracking.types';

const { REVIEW } = RAIL_EVENTS;

import clickableDownArrow from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';
import clickableUpArrow from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import clickableTicketDownArrow from '@mmt/legacy-assets/src/arrow_downGrey.webp';
import clickableTicketUpArrow from '@mmt/legacy-assets/src/arrow_upGrey.webp';
import BottomSheetModalTrain from '../Common/BottomSheetModalTrain';

const Travelers = ({
  travelers, page, railsListingUtilObj, id,
}) => travelers.map((traveler, index) =>
  (<TravelerCard
    key={traveler.travelerId}
    compactSize={false}
    traveler={traveler}
    page={page}
    railsListingUtilObj={railsListingUtilObj}
      containerStyle={{ paddingVertical: 8 }}
    id={`${id}_${index}`}
  />));

export const AvailabilityToolTip = ({ position, dismissAction, id }) => (
  <RailsToolTip
    dismiss={dismissAction}
    orientation="bottom"
    layout={position}
    id={id}
  >
    <View >
      <Text style={{
        fontSize: 14,
        ...fontStyle('regular'),
        ...getLineHeight(14),
        color: colors.white,
      }}
      >
        {_label('availability_has_changed')}
      </Text>
    </View>
  </RailsToolTip>);

const PriceBreakup = ({ response, id }) => {
  const [displayTicketFareBreakupSection, setDisplayTicketFareBreakupSection] = useState(false);
  const [displayFareBreakupSection, setDisplayFareBreakupSection] = useState(true);
  if (isEmpty(response)) {
    return null;
  }
/* eslint-disable */
  useEffect(() => {
    setDisplayFareBreakupSection(!getReviewPagePriceBreakupCollapsed());
  }, []);

  const {
    totalCollectibleAmount = 0,
    paymentFareBreakup: { fareDescRows = [], ticketFareBreakupDescRows = [] } = {},
  } = response;
  let hasTotalPriceNode = false;
  const totalPrice = _label('total_price',{capitalize : true});

  if (fareDescRows.length > 1) {
    hasTotalPriceNode = fareDescRows[fareDescRows.length - 1]?.mainText === totalPrice;
  }

  const onPressHandler = () => {
    const omnitureKeyMapping = displayFareBreakupSection ? COLLAPSE_CONST : EXPAND_CONST;
    const eventName = `rails_review_fare_breakup_${omnitureKeyMapping}`;
    trackClickEventProp61(REVIEW_PAGE_CONST, eventName);

    setDisplayFareBreakupSection(!displayFareBreakupSection);
    setDisplayTicketFareBreakupSection(false);
  };
  const onTicketPressHandler = () => {
    setDisplayTicketFareBreakupSection(!displayTicketFareBreakupSection);
  };

  return (
    <Card
      style={{
        borderRadius: 2,
        marginHorizontal: 0,
        marginBottom: 8,
        paddingBottom: displayFareBreakupSection ? 16 : 0,
      }}
      showBorder
      testID="price_breakup_card"
    >
      <View testID={id}>
        <TouchableRipple onPress={onPressHandler}>
          <View style={[styles.priceBreakupContainer, displayFareBreakupSection && styles.priceBreakupBorderBottom ]}>
            <Text style={[fontStyle('light'), styles.subSectionHeader]}>{_label('price_breakup', { capitalize: true })}</Text>
            <Image source={displayFareBreakupSection ? clickableUpArrow : clickableDownArrow} style={styles.arrowImg} testID={`${id}_arrow`}/>
          </View>
        </TouchableRipple>
        {displayFareBreakupSection &&
          <View testID="price_breakup_content">
            {fareDescRows.map((priceRow = {}, index) => {
              const {
                mainText,
                displayAmount,
                textColor,
                fontFamily,
                fontSize,
              } = priceRow;
              const customStyle = {};
              if (textColor) {
                customStyle.color = textColor;
              }
              if (fontFamily) {
                customStyle.fontFamily = fontFamily;
              }
              if (fontSize) {
                customStyle.fontSize = fontSize;
              }
              const nodePriceStyle = [styles.priceLabel, fontStyle('regular'), getLineHeight(14), customStyle];
              if (mainText === totalPrice) {
                const finalPriceStyle = [styles.priceFinal, fontStyle('bold'), getLineHeight(18), customStyle];
                return (
                  <View style={styles.priceBreakupRow}>
                    <Text style={[styles.priceFinalLabel, fontStyle('bold'), getLineHeight(16)]}>{mainText}</Text>
                    <RupeeText style={finalPriceStyle} testID={`${id}_${mainText}`}>
                      {displayAmount}
                    </RupeeText>
                  </View>
                );
              }
              if (mainText === TICKET_CHARGE) {
                const nodePriceTicketStyle = [
                  styles.priceLabel,
                  fontStyle('bold'),
                  getLineHeight(14),
                  customStyle,
                ];
                return (
                  <View key={`priceRow_${index}`} testID={`ticket_charge_section_${index}`}>
                    <View style={styles.priceTicketBreakupContainer} testID={`ticket_charge_container_${index}`}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }} testID={`ticket_charge_header_${index}`}>
                      <Text style={[styles.priceLabel, fontStyle('regular'), getLineHeight(14)]} testID={`ticket_charge_label_${index}`}>
                        {mainText}
                      </Text>
                      <TouchableRipple onPress={onTicketPressHandler} style={styles.arrowTicketImg} testID={`ticket_charge_arrow_touchable_${index}`}>
                        <Image
                          source={
                            displayTicketFareBreakupSection
                              ? clickableTicketUpArrow
                              : clickableTicketDownArrow
                          }
                          style={styles.arrowTicketImg}
                          testID={`ticket_charge_arrow_${index}`}
                        />
                      </TouchableRipple>
                        </View>
                      <RupeeText style={nodePriceTicketStyle} testID={`${id}_${mainText}`}>
                        {displayAmount}
                      </RupeeText>
                    </View>
                    {displayTicketFareBreakupSection && (
                      <View testID={`ticket_charge_breakdown_${index}`}>
                        {ticketFareBreakupDescRows.map((priceRow = {}, breakdownIndex) => {
                          const { mainText, displayAmount, textColor, fontFamily, fontSize } =
                            priceRow;
                          const customStyle = {};
                          if (textColor) {
                            customStyle.color = textColor;
                          }
                          if (fontFamily) {
                            customStyle.fontFamily = fontFamily;
                          }
                          if (fontSize) {
                            customStyle.fontSize = fontSize;
                          }
                          const nodePriceStyle = [
                            styles.priceTicketLabel,
                            fontStyle('regular'),
                            getLineHeight(12),
                            customStyle,
                          ];
                          return (
                            <View style={styles.priceTicketBreakupRow} key={`priceRow_${breakdownIndex}`} testID={`ticket_breakdown_row_${breakdownIndex}`}>
                               <Text
                                style={[
                                  styles.priceTicketLabel,
                                  fontStyle('regular'),
                                  getLineHeight(12),
                                ]}
                                testID={`ticket_breakdown_label_${breakdownIndex}`}
                              >
                                {mainText}
                              </Text>
                              <RupeeText style={nodePriceStyle} testID={`${id}_${mainText}`}>
                                {displayAmount}
                              </RupeeText>
                            </View>
                          );
                        })}
                      </View>
                    )}
                  </View>
                );
              }

              return (
                <View style={styles.priceBreakupRow} key={`priceRow_${index}`} testID={`price_row_${index}`}>
                  <Text style={[styles.priceLabel, fontStyle('regular'), getLineHeight(14)]} testID={`price_label_${index}`}>{mainText}</Text>
                  <RupeeText style={nodePriceStyle} testID={`${id}_${mainText}`}>
                    {displayAmount}
                  </RupeeText>
                </View>
              );
            })}
            {!hasTotalPriceNode && (
              <View style={styles.priceBreakupRow} testID="fallback_total_price_row">
                <Text style={[styles.priceFinalLabel, fontStyle('bold'), getLineHeight(16)]}>{_label('total_price', { capitalize: true })}</Text>
                <RupeeText
                  testID={`${id}_${fareDescRows[fareDescRows?.length - 1]?.mainText}`}
                  style={styles.priceFinal}
                >
                  {numAppendedWithRuppeeSymbolWithoutRound(parseFloat(totalCollectibleAmount)
                    .toFixed(2))}
                </RupeeText>
              </View>
            )}
          </View>
        }
      </View>
    </Card>
  );
};

function getStatusChanges(statusChangeAlert) {
  let hasStatusChangedFCtoTG = false;
  let hasStatusChangedTGtoFC = false;

  if (statusChangeAlert && statusChangeAlert.trackingEvent) {
    if (statusChangeAlert.trackingEvent === TRAVELERS_TG_ELIGIBILITY_CHANGE)
      {hasStatusChangedFCtoTG = true;}
    else if (statusChangeAlert.trackingEvent === TRAVELERS_FC_ELIGIBILITY_CHANGE)
      {hasStatusChangedTGtoFC = true;}
  }

  return { hasStatusChangedFCtoTG, hasStatusChangedTGtoFC };
}

export default class RailsReviewPage extends React.Component {
  constructor(props) {
    super(props);
    if (isMweb()) {
      const {
        selectedTrainInfo,
        classValue,
        selectedQuota,
        totalCollectibleAmount,
      } = this.props;
      if (!isEmpty(selectedTrainInfo)){
        const payload = {
          source: selectedTrainInfo.frmStnCode,
          sourceStation: selectedTrainInfo.frmStnName,
          destination: selectedTrainInfo.toStnCode,
          destinationStation: selectedTrainInfo.toStnName,
          trainNumber: selectedTrainInfo.trainNumber,
          quota: selectedQuota?.text,
          class: classValue?.value,
          doj: selectedTrainInfo.departureDateAndTime,
          totalFare: totalCollectibleAmount,
          pageType: 'review',
        };
        pushReviewIntoGtm(payload);
      }
    }
    if (props.reviewErrorWeb) {
      // RNW pop to prev screen in case of direct refresh of review //
      Actions.pop();
    }
    this.state = {
      showToolTip: false,
      toolTipLayout: null,
      id: 'reviewPage',
      displayResistanceBottomSheet: false,
      showBnppBottomSheet: false,
      alreadyShownBnppBottomSheet: false,
      bnppReviewVariant: getSeatLockReviewVariants(),
      infoIconClicked: false,
      displayIrctcPassEducationBottomsheet: false,
      displayUsernameModal: false,
      displayRetrievePassModal: false,
      irctcEducationBsConfig: {},
      irctcProfileIncompleteBs: {},
      displayIrctcProfileIncompleteBottomsheet: false,
      alreadyShownIrctcProfileIncompleteBs: false,
      bookingReviewDefaultErrorTitle: _label('review_error'),
      showReviewVerificationFlow: false,
    };
    this.railsListingUtilObj = new RailsListingUtilsClass(props.railsListing);
  }

  async componentDidMount() {
    const travellerAvailabilityStatus =
      this.props.bookingReviewRequest.listingAvailabilityStatus?.toUpperCase() || '';
    const bookingAvailabilityStatus =
      this.props.bookingReviewResponse?.avlDayList?.[0]?.availablityStatus?.split('/') || [];
    const currentAvailabilityStatus =
      bookingAvailabilityStatus?.[1]?.toUpperCase() ||
      bookingAvailabilityStatus?.[0]?.toUpperCase() ||
      '';
    BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
    this.props.setBnppShownOnReviewPage(this.props.canShowReviewBnpp);
    const irctcEducationBsConfig = await getConfigStore(configKeys.RAILS_REVIEW_IRCTC_BS_CONFIG) || {};
    const irctcProfileIncompleteBs = 
    (await getConfigStore(configKeys.RAILS_IRCTC_PROFILE_INCOMPLETE_FLOW_CONFIG))?.reviewBs || {};
    const bookingReviewDefaultErrorTitle =
      (await getConfigStore(configKeys.RAILS_REVIEW_DEFAULT_ERROR_MESSAGE)) ||
      _label('review_error');
    this.setState({
      irctcEducationBsConfig,
      irctcProfileIncompleteBs,
      bookingReviewDefaultErrorTitle,
    });
    const pageName = TRAVELERS_PAGE_TRACKING_KEY_NEW;
    const passengerCountEvent = RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_PASSANGER_COUNT.replace(
      '<n>',
      this.props?.selectedTravelers?.length,
    );
    if (
      getRailsAvlToWlMessage() &&
      this.isAvailabilityChanged(travellerAvailabilityStatus, currentAvailabilityStatus)
    ) {
      this.showToastAvlToWl();
    }
    setTimeout(async () => {
      const logNewMeasurementPlatformPdt = await getRailsReviewNewPdtLoggingOnOff();
      if (logNewMeasurementPlatformPdt) {
        const state = this.props.store.getState();
        const { payload, reviewData } = reviewPDTEvent(state);
        this.props.logReviewEvent('page-rendered', payload, reviewData);
      }
    }, 1000);
    removeEventFromEvar99Variable(passengerCountEvent);
    trackGenericEvar99Event(pageName, passengerCountEvent);
  }

  componentWillUnmount() {

    let { hasStatusChangedFCtoTG, hasStatusChangedTGtoFC } = getStatusChanges(
      this.props?.bookingReviewResponse?.statusChangeAlert,
    );


    this.props.unloadReviewPage(hasStatusChangedFCtoTG, hasStatusChangedTGtoFC );
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!this.props.bookingReviewResponse && nextProps.bookingReviewResponse 
      && nextProps.selectedBookingDate.availablityType
      && nextProps.bookingReviewResponse.avlDayList[0].availablityType 
        !== nextProps.selectedBookingDate.availablityType) {
      this.setState({showToolTip: true});
    }
  }

  showToastAvlToWl = () => {
    showShortToast(_label('availability_updated_from_avl_to_wl'));
  };

  isAvailabilityChanged = (travellerAvailabilityStatus, currentAvailabilityStatus) => {
    return (
      travellerAvailabilityStatus?.includes(AVAILABILITY_STATUS.AVAILABLE) &&
      currentAvailabilityStatus?.includes(AVAILABILITY_STATUS.WL)
    );
  };
  _onTimerExpired =  () => {
    if (this.props?.isScheduleTatkalBooking) {
      showShortToast(_label('transaction_expired_try_again'));
      Actions.pop();
      return;
    }
     this.props.loadReviewPage();
  };

  removeAvailabilityStatusEvar99 = () => {
    const { bookingReviewResponse } = this.props;
    const currentAvailabilityStatus =
      bookingReviewResponse?.avlDayList?.[0]?.availablityStatus || '';
    if (currentAvailabilityStatus?.includes(AVAILABILITY_STATUS.AVAILABLE)) {
      const bookingStatus = currentAvailabilityStatus?.split('-') || [];
      if (currentAvailabilityStatus?.length === 1) {
        removeEventFromEvar99Variable(`${RAIL_EVENTS.REVIEW.RAILS_REVIEW_AVAILABILITY_STATUS}0`);
      } else {
        let avlCount = Math.floor(parseInt(bookingStatus?.[1], 10) / 10);
        if (avlCount > 9) {
          avlCount = 9;
        }
        removeEventFromEvar99Variable(
          `${RAIL_EVENTS.REVIEW.RAILS_REVIEW_AVAILABILITY_STATUS}${avlCount}`,
        );
      }
    } else if (currentAvailabilityStatus?.includes(AVAILABILITY_STATUS.WL)) {
      const bookingStatus = currentAvailabilityStatus?.split('/') || [];
      let wlCount = Math.floor(parseInt(bookingStatus?.[1]?.substring(2), 10) / 10);
      if (wlCount > 9) {
        wlCount = 9;
      }
      removeEventFromEvar99Variable(
        `${RAIL_EVENTS.REVIEW.RAILS_REVIEW_TATKALWAITLIST_STATUS}${wlCount}`,
      );
    }
  };

  _toggleReviewVerificationFlow = (bookNowClicked = false) => {
    this.setState({ showReviewVerificationFlow: false },()=>{
      if(bookNowClicked){
        this._onBookNowClicked();
      }
    });
  };

  onBackPress = () => {
    const { displayResistanceBottomSheet } = this.state;
    const getPokusValForResistanceBottomSheet = getReviewPageBackButtonResistanceBottomsheet();
    const passengerCountEvent = RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_PASSANGER_COUNT.replace(
      '<n>',
      this.props?.selectedTravelers?.length,
    );
    if(getPokusValForResistanceBottomSheet && !displayResistanceBottomSheet){
      this.setState({ displayResistanceBottomSheet: true });
      return true;
    }
    this.props.actionRefreshRailofyStatusChange();
    trackReviewPageBackPress();
    if (this.props.postPaymentBooking) {
      Actions.railsListing({type: 'replace', postPaymentBooking: true});
    } else {
      this.props.onHardBackPress();
      Actions.pop();
    }
    this.removeAvailabilityStatusEvar99();
    removeEventFromEvar99Variable(passengerCountEvent);
    this.props?.logTravellerGenericClickEvents(
      TRAVELLER_CLICK_EVENTS.TRAVELLERS_ON_REVIEW_BACK_CLICK,
    );
    return true;
  };

  _onAvailabilityLayout = (event) => {
    const {
      x, y, width: w, height: h,
    } = event.nativeEvent.layout;
    this.setState({
      toolTipLayout: {
        x, y, w, h,
      },
    });
  };

  _goBackButtonClickedOnSimpleBottomSheet = (e) => {
    e?.preventDefault();
    trackClickEventProp61(REVIEW_PAGE_CONST, 
      REVIEW?.RAIL_REVIEW_PAGE_BACK_BUTTON_RESISTANCE_BOTTOMSHEET_TRACKER_GOBACK);
    this.setState({ displayResistanceBottomSheet: false });
    this.onBackPress();
  };

  _continueBookingButtonClickedOnSimpleBottomSheet = (e) => {
    e?.preventDefault();
    trackClickEventProp61(REVIEW_PAGE_CONST, 
      REVIEW?.RAIL_REVIEW_PAGE_BACK_BUTTON_RESISTANCE_BOTTOMSHEET_TRACKER_CONTINUE);
    this.setState({ displayResistanceBottomSheet: false });
  };

  _backgroundClickOnSimpleBottomSheet = () => {
    this.setState({ displayResistanceBottomSheet: false });
  };

  _showIrctcProfileIncompleteBottomsheet = (val) => {
    if (val) {
      this.setState({ displayIrctcProfileIncompleteBottomsheet: val, alreadyShownIrctcProfileIncompleteBs: true });
    } else {
      this.setState({ displayIrctcProfileIncompleteBottomsheet: val });
    }
  };

  _toggleBnppBottomSheet = () => {
    this.setState({ showBnppBottomSheet: !this.state.showBnppBottomSheet });
  };

  _onBookNowClicked = (e, proceedWithoutBs = false) => {
    this.props?.logReviewGenericClickEvents();
    if (!isEmpty(this.props?.reviewVerificationFlow)) {
      this.setState({ showReviewVerificationFlow: true });
      return;
    }
    
    if (
      showProfileIncompleteFlowPokus() === 1 &&
      !this.state?.alreadyShownIrctcProfileIncompleteBs &&
      !isEmpty(this.state?.irctcProfileIncompleteBs) &&
      !this.props?.isIrctcProfileComplete
    ) {
      this._showIrctcProfileIncompleteBottomsheet(true);
      return;
    }
    if (this.state?.displayIrctcProfileIncompleteBottomsheet) {
      this._showIrctcProfileIncompleteBottomsheet(false);
    }
    const showIrctcBsPokus = showRailsReviewIrctcBs() || 0;
    if (
      Boolean(showIrctcBsPokus) &&
      !proceedWithoutBs &&
      Boolean(this.props?.displayIrctcPassBottomsheet) &&
      !isEmpty(this.state?.irctcEducationBsConfig) &&
      !this.state?.alreadyShownIrctcProfileIncompleteBs
    ) {
      this._onSetDisplayIrctcEducationBottomsheet(true);
      trackReviewPageClickEvent(RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_BS_LOAD);
      return;
    }
    if (this.state?.displayIrctcPassEducationBottomsheet) {
      this._onSetDisplayIrctcEducationBottomsheet(false);
    }

    const isEmptyEligibleAddOns = isEmpty(this.props.bookingReviewResponse?.eligibleAddOns);
    const isEmptyBnppReviewAddOn = isEmpty(
      this.props.bookingReviewResponse?.eligibleAddOns?.bnppData,
    );

    if (!this.props.canShowReviewBnpp || isEmptyEligibleAddOns || isEmptyBnppReviewAddOn) {
      this.props.checkForMultipleReviewClickCount(false, this.props?.isScheduleTatkalBooking);
      return;
    }

    if (this.state.infoIconClicked) {
      this.setState({ infoIconClicked: false });
    }

    let newCheckoutApi = false;
    if (this.state.bnppReviewVariant !== 1) {
      newCheckoutApi = true;
    }

    if (
      Number(this.state.bnppReviewVariant) === 2 &&
      !this.props.bnppInteracted &&
      !this.state.displayIrctcPassEducationBottomsheet &&
      !this.state?.alreadyShownIrctcProfileIncompleteBs
    ) {
      if (!this.state.alreadyShownBnppBottomSheet) {
        this.setState({ showBnppBottomSheet: true });
      }

      this.setState({ alreadyShownBnppBottomSheet: true });

      if (this.state.alreadyShownBnppBottomSheet) {
        this.props.checkForMultipleReviewClickCount(newCheckoutApi, this.props?.isScheduleTatkalBooking);
        this.setState({ showBnppBottomSheet: false });
      }
    } else {
      this.props.checkForMultipleReviewClickCount(newCheckoutApi, this.props?.isScheduleTatkalBooking);
    }
  };

  _onInfoIconClicked = () => {
    if (!this.state.infoIconClicked) {
      trackClickEventProp61(
        REVIEW_PAGE_TRACKING_KEY,
        SEATLOCK_REVIEW_TRACKING_EVENTS.SEATLOCK_REVIEW_BOTTOM_SHEET_INFO,
      );
    }
    this.setState({ infoIconClicked: !this.state.infoIconClicked });
  };

  _onSetDisplayIrctcEducationBottomsheet = (val) => {
    this.setState({ displayIrctcPassEducationBottomsheet: val });
  };

  _onEditIrctcUsernameClicked = (val) => {
    this.setState({ displayUsernameModal: val, displayIrctcPassEducationBottomsheet: !val });
  };

  _onEditUsernameSuccess = () => {
    this.props?.loadReviewPage?.();
    this._onSetDisplayIrctcEducationBottomsheet(true);
  };

  _onRetrievePasswordButtonClicked = (val) => {
    this.setState({ displayRetrievePassModal: val, displayIrctcPassEducationBottomsheet: !val });
  };

  render() {
    const {
      selectedTrainInfo,
      selectedTravelers,
      classValue,
      selectedQuota,
      paxCount,
      bookingReviewResponse,
      selectedAvlStatus,
      loadingReview,
      bookingReviewSuccess,
      selectedPickupStation,
      boardingStationList,
      labels,
      canShowReviewBnpp,
      isBnppOpted,
      showPayNowLoader,
      irctcUserName,
      displayIrctcPassBottomsheet,
      reviewVerificationFlow,
    } = this.props;
    const {
      displayResistanceBottomSheet,
      showBnppBottomSheet,
      infoIconClicked,
      displayUsernameModal,
      displayRetrievePassModal,
      displayIrctcPassEducationBottomsheet,
      irctcEducationBsConfig = {},
      displayIrctcProfileIncompleteBottomsheet,
      irctcProfileIncompleteBs = {},
    } = this.state;

    if (this.props.reviewErrorWeb) {
      return null;
    }

    const reviewSummaryData = {
      classValue,
      selectedQuota,
      paxCount,
    };

    const [first] = boardingStationList;
    const diffDays = selectedPickupStation && first && first.dayCount && 
    selectedPickupStation.dayCount ? selectedPickupStation.dayCount - first.dayCount : 0;
    const selectedDate = new Date(selectedTrainInfo.departureDateAndTime);
    selectedDate.setDate(selectedDate.getDate() + diffDays);

    const buttonArrayForResistanceBottomSheet = [
      {
        title: _label('go_back', { uppercase: true }),
        func: this._goBackButtonClickedOnSimpleBottomSheet,
      },
      {
        title: _label('continue_booking', { uppercase: true }),
        func: this._continueBookingButtonClickedOnSimpleBottomSheet,
      },
    ];

    const resistanceBottomSheetContent = [
      {
        line: 'hey',
        blackTextColorClass: true,
      },
      {
        line: 'people_looking_for_for_a_ticket',
        blackTextColorClass: true,
      },
      {
        line: 'book_before_too_late',
        blackTextColorClass: false,
      },
    ];

    return (
      <ErrorBoundary page = {PAGE.REVIEW}>
      <ScreenWrapper>
      <View style={{ flex: 1, backgroundColor: colors.grayBg }} testID={this.state.id}>
        <CustomizedTripHeader
          title = {_label('review_booking', {capitalize : true})}
          subTitle={this.props.header}  // Date Related Conversion
          onPressBackHandler={this.onBackPress}
          id={`${this.state.id}_header`}
          size={getRailsLanguageByKey('cookieCode') === TAM ? 'medium' : 'nomral'}
        />
        {loadingReview &&
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
            <Spinner size={30} color={colors.azure} />
        </View>
        }
        {!loadingReview && bookingReviewSuccess &&
        <ScrollView style={{flex: 1}}>
          <View>
            <ReviewTimer
              startTime={this.props.reviewTimeStamp}
              onDone={this._onTimerExpired}
              id={`${this.state.id}_timerCard`}
            />
            <View style={{backgroundColor: colors.white, paddingHorizontal: 16, paddingBottom: 16}} testID={`${this.state.id}_trainDetailsCard`}>
              <TrainDetailsCard
                labels= {labels}
                selectedTrainInfo={selectedTrainInfo}
                showBoardingStationChange={false}
                reviewSummaryData={reviewSummaryData}
                selectedPickupStation={this.props.selectedPickupStation}
                showPickChange
                durationFromBoardingStation={this.props.durationFromBoardingStation}
                boardingStationList={boardingStationList}
                selectedAvlStatus={selectedAvlStatus}
                selectedQuota={selectedQuota}
                availabilityObject={bookingReviewResponse.avlDayList[0]}
                classValue={classValue}
                onAvailabilityLayout={this._onAvailabilityLayout}
                page={REVIEW_PAGE_CONST}
                id={this.state.id}
                  reviewAvailablityChange={this.props.reviewAvailablityChange}
                statusChangeAlert = { bookingReviewResponse.statusChangeAlert }
              />
              {
                selectedPickupStation &&
                <View style={{ marginTop: 10 }} testID={`${this.state.id}_boardingStation`}>
                  <Text style={[{color: colors.black}, fontStyle('bold')]}>{_label('boarding_station',{capitalize : true})}</Text>
                  <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                    <Text style={{color: colors.defaultTextColor}}>
                      <Text style={[{color: colors.defaultTextColor}, fontStyle('bold')]}>{selectedPickupStation.stationCode}
                      </Text>
                      {` / ${selectedPickupStation.stationName}`}
                    </Text>
                    {selectedPickupStation.departureTime &&
                      <Text style={{color: colors.defaultTextColor}}>
                        <Text style={[{color: colors.defaultTextColor}, fontStyle('bold')]}>
                          {` ${fecha.format(fecha.parse(selectedPickupStation.departureTime, 'HH:mm'), 'h:mm A')}`}
                        </Text>
                        {` (${fecha.format(selectedDate, 'D MMM')})`}
                      </Text>
                    }
                  </View>
                </View>
              }
              {
                this.state.showToolTip && this.state.toolTipLayout &&
                  <AvailabilityToolTip
                    position={{x: this.state.toolTipLayout.x + 160, y: this.state.toolTipLayout.y + 20}}
                    dismissAction={() => this.setState({showToolTip: false})}
                  id={`${this.state.id}_availabilityToolTip`}
                  />
              }
              <Card
                style={{
                  marginTop: 16,
                  borderRadius: 2,
                  paddingHorizontal: 16,
                  paddingVertical: 16,
                }}
                showBorder
                elevation={4}
              >
                <Travelers
                  travelers={selectedTravelers}
                  page="railsReview"
                  railsListingUtilObj={this.railsListingUtilObj}
                  labels={labels}
                  id={`${this.state.id}_traveller`}
                />
              </Card>
            </View>
            {this.props.dueAmount > 0 && <BookNowPayPartialStrip type="REVIEW" />}
            {canShowReviewBnpp && (
              <View>
                <BookNowPayPartial />
                </View>
              )}
              {(!canShowReviewBnpp || isEmpty(bookingReviewResponse?.eligibleAddOns?.bnppData)) && (
                <PriceBreakup
                  response={bookingReviewResponse}
                  id={`${this.state.id}_priceBreakup`}
                />
            )}
            <WalletBanner funnel="review" id={`${this.state.id}_walletBanner`} />
          </View>
        </ScrollView>
        }
        {this.props.dueAmount > 0 && !loadingReview && bookingReviewSuccess && (
          <BookNowPayPartialStrip
            type="REVIEW_BOTTOM"
            dueDate={this.props.dueDate}
            dueAmount={this.props.dueAmount}
          />
        )}
        {canShowReviewBnpp && showBnppBottomSheet && (
          <RailsReviewBnppBottomSheet
            toggleBottomSheet={this._toggleBnppBottomSheet}
            showBNPPBottomSheet={this.state.showBnppBottomSheet}
          />
        )}

        {infoIconClicked && (
          <RailsReviewFareBreakupBottomSheet
            toggleBottomSheet={this._onInfoIconClicked}
            showFareBreakupBottomSheet={this.state.infoIconClicked}
            fullPaymentFareBreakup={bookingReviewResponse?.paymentFareBreakup}
            bnppPartialFareBreakup={bookingReviewResponse?.eligibleAddOns?.bnppData?.paymentFareBreakup}
            bnppData={bookingReviewResponse?.eligibleAddOns?.bnppData}
            bnppSelected={this.props.isBnppOpted}
          />
        )}

        {!loadingReview && bookingReviewSuccess &&
        <BookNowButton
          BookNowButtonEnabled
          onBookNowClicked={this._onBookNowClicked}
          showLoader={this.props.loadingReview}
          totalCollectibleAmount={`${roundToTwoDec(bookingReviewResponse.totalCollectibleAmount)}`}
          buttonText={_label('pay_and_book')}
          perSeatText=""
          id={`${this.state.id}_bookNowContainer`}
          onInfoIconClicked={this._onInfoIconClicked}
          expandedFareBottomSheet={infoIconClicked}
          seatlockShownOnReview={canShowReviewBnpp}
          isBnppOpted={isBnppOpted}
          totalAmountOnBnppSelected={`${roundToTwoDec(
            bookingReviewResponse?.eligibleAddOns?.bnppData?.partialAmount,
            )}`}
          showPayNowLoader={showPayNowLoader}
            eligibleAddOns={bookingReviewResponse?.eligibleAddOns}
        />
        }

        {!loadingReview && !bookingReviewSuccess &&
        <BookingReviewError
          onBack={this.onBackPress}
          errorMessage={this.props.errorMessage}
          id={`${this.state.id}_reviewError`}
                bookingReviewDefaultErrorTitle={this.state.bookingReviewDefaultErrorTitle}
        />
        }
        {displayResistanceBottomSheet &&
          <RailsSimpleBottomSheetContent
            buttonArray={buttonArrayForResistanceBottomSheet}
            outsideClick={this._backgroundClickOnSimpleBottomSheet}
            content={resistanceBottomSheetContent}
          />
        }
        {displayIrctcProfileIncompleteBottomsheet && !isEmpty(irctcProfileIncompleteBs) && (
          <BottomSheetModalTrain
            onTouchOutside={() => {this._showIrctcProfileIncompleteBottomsheet(false);}}
            additionalContainerStyle={styles.bottomSheetContainer}
            testID="irctc_profile_incomplete_bottomsheet_modal"
          >
            <IrctcProfileIncompleteBottomsheet
              userName={irctcUserName}
              config={irctcProfileIncompleteBs}
              onBookNowClick={this._onBookNowClicked}
            />
          </BottomSheetModalTrain>
        )}
        {displayIrctcPassEducationBottomsheet && !isEmpty(irctcEducationBsConfig) &&
          <BottomSheetModalTrain
            onTouchOutside={() => {this._onSetDisplayIrctcEducationBottomsheet?.(false);}}
            additionalContainerStyle={styles.bottomSheetContainer}
            testID="irctc_pass_education_bottomsheet_modal"
          >
            <IrctcPassEducationBottomsheet
              config={irctcEducationBsConfig}
              userName={irctcUserName}
              displayBottomsheet={this._onSetDisplayIrctcEducationBottomsheet}
              onEditUsernameClick={this._onEditIrctcUsernameClicked}
              onRetrievePasswordClick={this._onRetrievePasswordButtonClicked}
              onBookNowClick={this._onBookNowClicked}
              displayVariant={displayIrctcPassBottomsheet}
            />
          </BottomSheetModalTrain>
        }
        {displayUsernameModal &&
          <UserNameModal
            userName={irctcUserName}
            modalVisible={displayUsernameModal}
            setModalVisibility={this._onEditIrctcUsernameClicked}
            from="review"
            id={`${this.state?.id}_onUsernameClicked`}
            successCallback={this._onEditUsernameSuccess}
          />
        }
        {displayRetrievePassModal &&
          <Modal
            transparent
            visible={displayRetrievePassModal}
            style={styles.bottomSheetGreyContainer}
          >
            <TouchableWithoutFeedback onPress={() => {this._onRetrievePasswordButtonClicked?.(false);}}>
              <View style={styles.bottomSheetGreyContainer} />
            </TouchableWithoutFeedback>
            <View style={{flex: 1}}>
              <RailRetrievePasswordContainer
                dismiss={() => { this._onRetrievePasswordButtonClicked?.(false); }}
                irctcUserName={irctcUserName}
                id={`${this.state?.id}_onNewPasswordButtonClicked`}
              />
            </View>
          </Modal>
        }
        {this.state?.showReviewVerificationFlow && (
           <BottomSheetModalTrain additionalContainerStyle={styles.bottomSheetContainer} testID="review_verification_flow_bottomsheet_modal">
            <ReviewVerificationFlow onClose={this._toggleReviewVerificationFlow} reviewVerificationFlow={reviewVerificationFlow}  />
            </BottomSheetModalTrain>
          )}
      </View>
      </ScreenWrapper>
      </ErrorBoundary>
    );
  }
}

RailsReviewPage.propTypes = {
  loadReviewPage: PropTypes.func.isRequired,
  unloadReviewPage: PropTypes.func.isRequired,
  loadingReview: PropTypes.bool.isRequired,
  reviewTimeStamp: PropTypes.number,
  bookingReviewSuccess: PropTypes.bool.isRequired,
  errorMessage: PropTypes.string,
  selectedPickupStation: PropTypes.object,
  hotelCrossSellBannerText: PropTypes.string,
  cdnTncUrl: PropTypes.string,
  hotelsDeepLinkMobileApp: PropTypes.string,
  durationFromBoardingStation: PropTypes.string,
  isBnppOpted: PropTypes.bool,
  dueAmount: PropTypes.number,
  dueDate: PropTypes.string,
  selectedTrainInfo: PropTypes.object,
  classValue: PropTypes.object,
  selectedQuota: PropTypes.object,
  setBnppShownOnReviewPage: PropTypes.func,
  canShowReviewBnpp: PropTypes.bool,
  logReviewEvent: PropTypes.func,
  bookingReviewResponse: PropTypes.object,
  selectedBookingDate: PropTypes.object,
  isIrctcProfileComplete: PropTypes.bool,
  logReviewGenericClickEvents: PropTypes.func,
  displayIrctcPassBottomsheet: PropTypes.bool,
  totalCollectibleAmount: PropTypes.number,
  reviewErrorWeb: PropTypes.bool,
  railsListing: PropTypes.object,
  store: PropTypes.object,
  isScheduleTatkalBooking: PropTypes.bool,
  actionRefreshRailofyStatusChange: PropTypes.func,
  postPaymentBooking: PropTypes.bool,
  onHardBackPress : PropTypes.func,
  logTravellerGenericClickEvents: PropTypes.func,
  checkForMultipleReviewClickCount: PropTypes.func,
  selectedTravelers: PropTypes.array,
  paxCount: PropTypes.number,
  selectedAvlStatus: PropTypes.string,
  boardingStationList: PropTypes.array,
  labels: PropTypes.object,
  showPayNowLoader: PropTypes.bool,
  bnppReviewVariant: PropTypes.number,
  header: PropTypes.string,
  reviewAvailablityChange: PropTypes.bool,
  bnppInteracted: PropTypes.bool,
  irctcUserName: PropTypes.string,
};

RailsReviewPage.defaultProps = {
  reviewTimeStamp: -1,
  errorMessage: null,
  selectedPickupStation: null,
  hotelCrossSellBannerText: '',
  cdnTncUrl: '',
  hotelsDeepLinkMobileApp: '',
  durationFromBoardingStation: '',
};

AvailabilityToolTip.propTypes = {
  position: PropTypes.object,
  dismissAction: PropTypes.func,
  id: PropTypes.string,
};

PriceBreakup.propTypes = {
  response: PropTypes.object,
  id: PropTypes.string,
};


const styles = StyleSheet.create({
  subSectionHeader: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.defaultTextColor,
  },
  priceBreakupContainer: {
    flexDirection: 'row',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    color: colors.defaultTextColor,
  },
  priceTicketBreakupContainer: {
    flexDirection: 'row',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 16,
    paddingRight: 16,
    paddingVertical: 8,
    color: colors.defaultTextColor,
    marginTop: 8,
  },
  priceBreakupBorderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  arrowImg: {
    width: 24,
    height: 24,
  },
  arrowTicketImg: {
    width: 15,
    height: 15,
  },

  priceBreakupRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  priceTicketBreakupRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 2,
    paddingLeft: 32,
    paddingRight: 16,
  },
  priceLabel: {
    color: colors.black,
    fontSize: 14,
  },
  priceTicketLabel: {
    color: colors.textGrey,
    fontSize: 12,
  },
  priceFinalLabel: {
    color: colors.defaultTextColor,
    fontSize: 16,
  },
  priceFinal: {
    color: colors.black,
    fontSize: 18,
  },
  bottomSheetContainer: {
    zIndex: 9999,
  },
  bottomSheetGreyContainer: {
    opacity: 0.7,
  },
});

