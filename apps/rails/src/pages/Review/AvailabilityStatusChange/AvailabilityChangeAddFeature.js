import React, { useEffect } from 'react';
import { Image,Text, View } from 'react-native';
import {StatusChangeStyles, handleClick, statusChangeTextStyles} from './AvailabilityChangeUtils';
import AvailabilityChangeTopBar from './AvailabilityChangeTopBar';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { saveRailofyZcShownInLS } from '../../../Utils/railofyUtils';
import { _label } from '../../../vernacular/AppLanguage';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

const { tgIcon } = ASSETS;
import fcIcon from '@mmt/legacy-assets/src/fc_icon.webp';
import greenTickIcon from '@mmt/legacy-assets/src/greeTickIcon.webp';
import PropTypes from 'prop-types';

export const StatusChangeCardAddFeature = (props) => {
  const {showFCIcon,availabilityStatusChange, newFeaturePersuasion, routeToTravelersPage,
    setRoutedThroughReview, railofyZcShown, saveRailofyZcShown,actionRefreshRailofyStatusChange} = props;
  const [newFeatureMessage1, newFeatureMessage2 = ''] = newFeaturePersuasion.content;
  const icon = (showFCIcon) ? fcIcon : tgIcon;
  useEffect(() => {
    saveRailofyZcShownInLS(railofyZcShown);
    saveRailofyZcShown(railofyZcShown);
    setRoutedThroughReview();
  },[]);
  return (
    <View style = {StatusChangeStyles.AvailabilityChangeAddFeatureContainer}>
      <AvailabilityChangeTopBar availabilityStatusChange = {availabilityStatusChange} showFCIcon={showFCIcon}/>
      <View style = {StatusChangeStyles.AvailabilityChangeAddFeatureBodyContainer}>
        <View style = {StatusChangeStyles.AvailabilityChangeAddFeatureMidPortion}>
          <Image
            source = {icon}
            style = {StatusChangeStyles.AvailabilityChangeAddFeatureIcon}
          />
          <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureBodyText, 
                          statusChangeTextStyles.getAddFeatureBodyTextFontStyle()]}>
            {newFeatureMessage1}
            <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureBodyTextSpecial, 
                            statusChangeTextStyles.getAddFeatureBodyTextSpecialFontStyle()]}>
              {newFeatureMessage2}
            </Text>
          </Text>
        </View>
        <View style = {StatusChangeStyles.AvailabilityChangeAddFeatureLowerBox}>
          <View style={StatusChangeStyles.AvailabilityChangeAddFeatureBoxTopView}>
            <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureBoxTopText, 
                            statusChangeTextStyles.getAddFeatureBoxTopTextFontStyle()]}>
              {newFeaturePersuasion.box.topText}
            </Text>
            <Text style={[StatusChangeStyles.AvailabilityChangeAddFeatureBoxTopValue, 
                          statusChangeTextStyles.getAddFeatureBoxTopValueFontStyle()]}>
              {newFeaturePersuasion.box.topValue}
            </Text>
          </View>
          <View style={StatusChangeStyles.AvailabilityChangeAddFeatureBoxBottomView}>
            <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureBoxBottomText, 
                           statusChangeTextStyles.getAddFeatureBoxBottomTextFontStyle()]}>
              {newFeaturePersuasion.box.bottomText}
            </Text>
            <Text style={[StatusChangeStyles.AvailabilityChangeAddFeatureBoxBottomValue, 
                          statusChangeTextStyles.getAddFeatureBoxBottomValueFontStyle()]}>
              {newFeaturePersuasion.box.bottomValue}
            </Text>
            <Image
              source = {greenTickIcon}
              style = {StatusChangeStyles.AvailabilityChangeAddFeatureBoxTickIcon}
            />
          </View>
        </View>
        <View style={StatusChangeStyles.AvailabilityChangeAddFeatureClickButton}>
          <TouchableRipple onPress={() => handleClick(routeToTravelersPage,actionRefreshRailofyStatusChange)}>
            <Text style={[StatusChangeStyles.AvailabilityChangeAddFeatureBottomText, 
                          statusChangeTextStyles.getAddFeatureBottomTextFontStyle()]}>
              {_label('go_back_to_offer',{ uppercase : true })}
            </Text>
          </TouchableRipple>
        </View>
      </View>
    </View>
  );
};

StatusChangeCardAddFeature.propTypes = {
  showFCIcon: PropTypes.bool,
  availabilityStatusChange: PropTypes.object,
  newFeaturePersuasion: PropTypes.object,
  routeToTravelersPage: PropTypes.func,
  setRoutedThroughReview: PropTypes.func,
  railofyZcShown: PropTypes.bool,
  saveRailofyZcShown: PropTypes.func,
  actionRefreshRailofyStatusChange: PropTypes.func,
  newFeaturePersuasion: PropTypes.shape({
    content: PropTypes.arrayOf(PropTypes.string),
    box: PropTypes.shape({
      topText: PropTypes.string,
      topValue: PropTypes.string,
      bottomText: PropTypes.string,
      bottomValue: PropTypes.string,
    }),
  }),
}

export default StatusChangeCardAddFeature;
