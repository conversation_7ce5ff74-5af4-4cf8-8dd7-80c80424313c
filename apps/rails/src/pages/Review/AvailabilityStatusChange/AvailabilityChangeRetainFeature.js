import React from 'react';
import { Image,Text, View } from 'react-native';
import {StatusChangeStyles, statusChangeTextStyles} from './AvailabilityChangeUtils';
import {_label} from '../../../vernacular/AppLanguage';
import AvailabilityChangeRetainFeatureIcon from '@mmt/legacy-assets/src/error_icon.webp';
import PropTypes from 'prop-types';

const StatusChangeCardRetainFeature = (props) => {
  const {availabilityStatusChange} = props;
  const [messagePart1, messagePart2,messagePart3, messagePart4 = ''] = availabilityStatusChange;
  return (
    <View style={StatusChangeStyles.AvailabilityChangeRetainFeatureContainer}>
      <Image
        source={AvailabilityChangeRetainFeatureIcon}
        style={StatusChangeStyles.AvailabilityChangeRetainFeatureIcon}
       />
      <View style={StatusChangeStyles.AvailabilityChangeRetainFeatureTextBox}>
        <Text style = {[StatusChangeStyles.AvailabilityChangeRetainFeatureTextCommon, 
          statusChangeTextStyles.getRetainFeatureCommonFontStyle()]}>
          {messagePart1}
          <Text style = {[StatusChangeStyles.AvailabilityChangeRetainFeatureTextSpecial, 
            statusChangeTextStyles.getRetainFeatureSpecialFontStyle()]}>
            {`${messagePart2} ${_label('to',{capitalize:true})} ${messagePart3}`}
          </Text>
          {messagePart4}
        </Text>
      </View>
    </View>
  );
};
StatusChangeCardRetainFeature.propTypes = {
  availabilityStatusChange: PropTypes.func,
};

export default StatusChangeCardRetainFeature;
