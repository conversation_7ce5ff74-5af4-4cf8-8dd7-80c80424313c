import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { Image,Text, View } from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {StatusChangeStyles, statusChangeTextStyles} from './AvailabilityChangeUtils';
import arrowIconWhite from '@mmt/legacy-assets/src/cosmos_android_ui_icons_one_way_white.webp';
import arrowIconBlack from '@mmt/legacy-assets/src/cosmos_android_ui_icons_one_way_black.webp';
import PropTypes from 'prop-types';

const AvailabilityChangeTopBarFC = (props) => {
  const {availabilityStatusChange} = props;
  const arrowIcon = arrowIconBlack;
  const [alertMessagePart1, alertMessagePart2,alertMessagePart3] = availabilityStatusChange;
  return (
    <View style={StatusChangeStyles.AvailabilityChangeAddFeatureTopBarFC}>
      <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureTextCommonFC, 
                      statusChangeTextStyles.getAddFeatureCommonFCFontStyle()]}>
        {alertMessagePart1}
        <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureTextSpecialFC, 
                        statusChangeTextStyles.getAddFeatureSpecialFCFontStyle()]}>
          {alertMessagePart2 + ' '}
          <Image source={arrowIcon}/>
          {' ' + alertMessagePart3}
        </Text>
      </Text>
    </View>
  );
};

const AvailabilityChangeTopBarTG = (props) => {
  const {availabilityStatusChange} = props;
  const arrowIcon = arrowIconWhite;
  const [alertMessagePart1, alertMessagePart2,alertMessagePart3] = availabilityStatusChange;
  return (
    <View>
      <LinearGradient
        colors={[colors.lavender, colors.governorBay]}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
        style={StatusChangeStyles.AvailabilityChangeAddFeatureTopBarTG}
      >
        <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureTextCommonTG, 
                        statusChangeTextStyles.getAddFeatureCommonTGFontStyle()]}>
          {alertMessagePart1}
          <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureTextSpecialTG, 
                          statusChangeTextStyles.getAddFeatureSpecialTGFontStyle()]}>
            {alertMessagePart2 + ' '}
            <Image source={arrowIcon}/>
            {' ' + alertMessagePart3}
          </Text>
        </Text>
      </LinearGradient>
    </View>
  );
};
const AvailabilityChangeTopBar = (props) => {
  const {showFCIcon,availabilityStatusChange} = props;
  if (showFCIcon) {
        return <AvailabilityChangeTopBarFC availabilityStatusChange={availabilityStatusChange}/>;
  } else {
      return <AvailabilityChangeTopBarTG availabilityStatusChange={availabilityStatusChange}/>;
  }
};

AvailabilityChangeTopBar.propTypes = {
  availabilityStatusChange: PropTypes.arrayOf(PropTypes.string),
  showFCIcon: PropTypes.bool,
};

AvailabilityChangeTopBarFC.propTypes = {
  availabilityStatusChange: PropTypes.arrayOf(PropTypes.string),
};

AvailabilityChangeTopBarTG.propTypes = {
  availabilityStatusChange: PropTypes.arrayOf(PropTypes.string).isRequired,
};


export default AvailabilityChangeTopBar;
