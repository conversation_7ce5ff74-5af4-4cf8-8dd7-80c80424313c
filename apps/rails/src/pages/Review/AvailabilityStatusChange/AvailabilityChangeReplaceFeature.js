import React, { useEffect } from 'react';
import { Image,Text, View } from 'react-native';
import {StatusChangeStyles, handleClick, statusChangeTextStyles} from './AvailabilityChangeUtils';
import AvailabilityChangeTopBar from './AvailabilityChangeTopBar';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { saveRailofyZcShownInLS } from '../../../Utils/railofyUtils';
import { _label } from '../../../vernacular/AppLanguage';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';

const { tgIcon } = ASSETS;
import fcIcon from '@mmt/legacy-assets/src/fc_icon.webp';
import greenTickIcon from '@mmt/legacy-assets/src/greeTickIcon.webp';
import PropTypes from 'prop-types';

export const StatusChangeCardReplaceFeature = (props) => {
  const {showFCIcon, availabilityStatusChange, featureSelectionChange,
    newFeaturePersuasion, routeToTravelersPage, setRoutedThroughReview, 
    railofyZcShown, saveRailofyZcShown,actionRefreshRailofyStatusChange} = props;
  const [newFeatureMessage1, newFeatureMessage2 = ''] = newFeaturePersuasion.content;
  const icon = (showFCIcon) ? fcIcon : tgIcon;
  useEffect(() => {
    saveRailofyZcShownInLS(railofyZcShown);
    saveRailofyZcShown(railofyZcShown);
    setRoutedThroughReview();
  },[]);
  return (
    <View
      style={StatusChangeStyles.AvailabilityChangeReplaceFeatureContainer}
      testID="availability_change_replace_feature_container"
    >
      <AvailabilityChangeTopBar availabilityStatusChange = {availabilityStatusChange} showFCIcon={showFCIcon}/>
      <View style = {StatusChangeStyles.AvailabilityChangeAddFeatureBodyContainer}>
        <View
          style={StatusChangeStyles.AvailabilityChangeAddFeatureMidPortion}
          testID="availability_change_replace_feature_mid_portion"
        >
          <Image
            source={icon}
            style = {StatusChangeStyles.AvailabilityChangeAddFeatureIcon}
            testID="availability_change_replace_feature_icon"
          />
          <View style={{ flex: 1 }}>
            <Text style = {[StatusChangeStyles.AvailabilityChangeReplaceFeatureBodyText, 
                statusChangeTextStyles.getReplaceFeatureBodyTextFontStyle(),
              ]}
              testID="availability_change_replace_feature_body_text"
            >
              {featureSelectionChange.content}
            </Text>
            <Text style = {[StatusChangeStyles.AvailabilityChangeReplaceFeatureBodyExtraText, 
                statusChangeTextStyles.getReplaceFeatureBodyTextFontStyle(),
              ]}
              testID="availability_change_replace_feature_body_extra_text"
            >
              {newFeatureMessage1}
              <Text style = {[StatusChangeStyles.AvailabilityChangeReplaceFeatureBodyExtraTextSpecial, 
                  statusChangeTextStyles.getReplaceFeatureExtraTextSpecialFontStyle(),
                ]}
                testID="availability_change_replace_feature_body_extra_text_special"
              >
                {newFeatureMessage2}
              </Text>
            </Text>
          </View>
        </View>
        <View style = {StatusChangeStyles.AvailabilityChangeAddFeatureLowerBox}>
          <View style={StatusChangeStyles.AvailabilityChangeAddFeatureBoxTopView}>
            <Text
              style={[
                StatusChangeStyles.AvailabilityChangeAddFeatureBoxTopText,
                statusChangeTextStyles.getAddFeatureBoxTopTextFontStyle(),
              ]}
              testID="availability_change_replace_feature_box_top_text"
            >
              {newFeaturePersuasion.box.topText}
            </Text>
            <Text style={[StatusChangeStyles.AvailabilityChangeAddFeatureBoxTopValue, 
                statusChangeTextStyles.getAddFeatureBoxTopValueFontStyle(),
              ]}
              testID="availability_change_replace_feature_box_top_value"
            >
              {newFeaturePersuasion.box.topValue}
            </Text>
          </View>
          <View style={StatusChangeStyles.AvailabilityChangeAddFeatureBoxBottomView}>
            <Text style = {[StatusChangeStyles.AvailabilityChangeAddFeatureBoxBottomText, 
                statusChangeTextStyles.getAddFeatureBoxBottomTextFontStyle(),
              ]}
              testID="availability_change_replace_feature_box_bottom_text"
            >
              {newFeaturePersuasion.box.bottomText}
            </Text>
            <Text style={[StatusChangeStyles.AvailabilityChangeAddFeatureBoxBottomValue, 
                statusChangeTextStyles.getAddFeatureBoxBottomValueFontStyle(),
              ]}
              testID="availability_change_replace_feature_box_bottom_value"
            >
              {newFeaturePersuasion.box.bottomValue}
            </Text>
            <Image
              source = {greenTickIcon}
              style = {StatusChangeStyles.AvailabilityChangeAddFeatureBoxTickIcon}
              testID="availability_change_replace_feature_box_tick_icon"
            />
          </View>
        </View>
        <View style={StatusChangeStyles.AvailabilityChangeAddFeatureClickButton}>
          <TouchableRipple
            onPress={() => handleClick(routeToTravelersPage, actionRefreshRailofyStatusChange)}
            testID="availability_change_replace_feature_click_button"
          >
            <Text style={[StatusChangeStyles.AvailabilityChangeAddFeatureBottomText, 
                statusChangeTextStyles.getAddFeatureBottomTextFontStyle(),
              ]}
              testID="availability_change_replace_feature_bottom_text"
            >
              {_label('go_back_to_offer',{ uppercase : true })}
            </Text>
          </TouchableRipple>
        </View>
      </View>
    </View>
  );
};

StatusChangeCardReplaceFeature.propTypes = {
  showFCIcon: PropTypes.bool,
  availabilityStatusChange: PropTypes.object,
  featureSelectionChange: PropTypes.object,
  newFeaturePersuasion: PropTypes.object,
  routeToTravelersPage: PropTypes.func,
  setRoutedThroughReview: PropTypes.func,
  railofyZcShown: PropTypes.bool,
  saveRailofyZcShown: PropTypes.func,
  actionRefreshRailofyStatusChange: PropTypes.func
};

export default StatusChangeCardReplaceFeature;
