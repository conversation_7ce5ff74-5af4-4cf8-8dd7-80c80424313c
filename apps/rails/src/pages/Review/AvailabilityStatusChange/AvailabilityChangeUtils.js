import { StyleSheet, Dimensions } from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { trackReviewPageStatusChangeBackClick } from '../../../railsAnalytics';
import {Actions} from '../../../navigation/railsNavigation';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';
import { isIos, isMweb } from '../../../Utils/device';
export const getScreenWidth = () => isIos() ? Dimensions.get('screen').width : Dimensions.get('window').width;
const railofyZcShownForTgOffer = {
  railofyShown : true,
  zcShown : false,
};
const railofyZcShownForFcOffer = {
  railofyShown : false,
  zcShown : true,
};

export const StatusChangeType = {
  AVL_TO_WLNONTG_1 : {
      value : 'STATUS_CHANGE_FROM_AVAILABLE_TO_WAITING_WITHOUT_TRIP_GUARANTEE_RETAIN_FREE_CANCELLATION_FEATURE',
      id : 'AVL_TO_WLNONTG_1',
    showFCIcon : true,
    railofyZcShown : {
      ...railofyZcShownForFcOffer,
    },
  },
  AVL_TO_WLNONTG_13 : {
    value : 'STATUS_CHANGE_FROM_AVAILABLE_TO_WAITING_WITHOUT_TRIP_GUARANTEE_ADD_FREE_CANCELLATION_FEATURE_AND_CONVINCE',
    id : 'AVL_TO_WLNONTG_13',
    showFCIcon : true,
    railofyZcShown : {
      ...railofyZcShownForFcOffer,
    },
  },
  AVL_TO_WLTG_123 : {
    value : 'STATUS_CHANGE_FROM_AVAILABLE_TO_WAITING_WITH_TRIP_GUARANTEE_REPLACE_FREE_CANCELLATION_WITH_TRIP_GUARANTEE',
    id : 'AVL_TO_WLTG_123',
    showFCIcon : false,
    railofyZcShown : {
      ...railofyZcShownForTgOffer,
    },
  },
  AVL_TO_WLTG_13 : {
    value : 'STATUS_CHANGE_FROM_AVAILABLE_TO_WAITING_WITH_TRIP_GUARANTEE_REPLACE_FREE_CANCELLATION_WITH_TRIP_GUARANTEE_AND_CONVINCE',
    id : 'AVL_TO_WLTG_13',
    showFCIcon : false,
    railofyZcShown : {
      ...railofyZcShownForTgOffer,
    },
  },
  WLTG_TO_AVL_123 : {
    value : 'STATUS_CHANGE_FROM_WAITING_WITH_TRIP_GUARANTEE_TO_AVAILABLE_REPLACE_TRIP_GUARANTEE_WITH_FREE_CANCELLATION',
    id : 'WLTG_TO_AVL_123',
    showFCIcon : true,
    railofyZcShown : {
      ...railofyZcShownForFcOffer,
    },
  },
  WLTG_TO_AVL_13 : {
    value : 'STATUS_CHANGE_FROM_WAITING_WITH_TRIP_GUARANTEE_TO_AVAILABLE_REPLACE_TRIP_GUARANTEE_WITH_FREE_CANCELLATION_AND_CONVINCE',
    id : 'WLTG_TO_AVL_13',
    showFCIcon : true,
    railofyZcShown : {
      ...railofyZcShownForFcOffer,
    },
  },
  WLNONTG_TO_AVL_1 : {
    value : 'STATUS_CHANGE_FROM_WAITING_WITHOUT_TRIP_GUARANTEE_TO_AVAILABLE_RETAIN_FREE_CANCELLATION',
    id : 'WLNONTG_TO_AVL_1',
    showFCIcon : true,
    railofyZcShown : {
      ...railofyZcShownForFcOffer,
    },
  },
  WLNONTG_TO_AVL_13 : {
    value : 'STATUS_CHANGE_FROM_WAITING_WITHOUT_TRIP_GUARANTEE_TO_AVAILABLE_ADD_FREE_CANCELLATION_AND_CONVINCE',
    id : 'WLNONTG_TO_AVL_13',
    showFCIcon : true,
    railofyZcShown : {
      ...railofyZcShownForFcOffer,
    },
  },
};

const TextStyle = {
  fontSize : getScreenWidth() > 360 ? 14 : 12,
  lineHeight: getScreenWidth() > 360 ? getLineHeight(14).lineHeight : getLineHeight(12).lineHeight,
  color : colors.lightYello,
};

export const statusChangeTextStyles = {
    getTextStyleFontStyle : () => {
        return fontStyle('regular');
    },
    getRetainFeatureCommonFontStyle : () => {
      return {...statusChangeTextStyles.getTextStyleFontStyle()};
    },
    getRetainFeatureSpecialFontStyle : () => {
        return {...fontStyle('bold')};
    },
    getAddFeatureCommonTGFontStyle : () => {
        return {...statusChangeTextStyles.getTextStyleFontStyle()};
    },
    getAddFeatureSpecialTGFontStyle : () => {
      return {...fontStyle('bold')};
    },
    getAddFeatureCommonFCFontStyle : () => {
      return {...statusChangeTextStyles.getTextStyleFontStyle()};
    },
    getAddFeatureSpecialFCFontStyle : () => {
      return {...fontStyle('bold')};
    },
    getAddFeatureBodyTextFontStyle : () => {
      return {...fontStyle('medium')};
    },
    getAddFeatureBodyTextSpecialFontStyle : () => {
      return {...fontStyle('bold')};
    },
    getReplaceFeatureBodyTextFontStyle : () => {
      return {...fontStyle('bold')};
    },
    getAddFeatureBoxTopTextFontStyle : () => {
      return fontStyle('regular');
    },
    getAddFeatureBoxTopValueFontStyle : () => {
      return fontStyle('regular');
    },
    getAddFeatureBoxBottomTextFontStyle : () => {
      return fontStyle('bold');
    },
    getAddFeatureBoxBottomValueFontStyle : () => {
      return fontStyle('bold');
    },
    getAddFeatureBottomTextFontStyle : () => {
        return fontStyle('black');
    },
    getReplaceFeatureExtraTextFontStyle : () => {
      return fontStyle('regular');
    },
    getReplaceFeatureExtraTextSpecialFontStyle : () => {
      return fontStyle('bold');
    },
};
export const StatusChangeStyles = StyleSheet.create({
  AvailabilityChangeRetainFeatureContainer : {
    paddingTop : 12,
    paddingBottom : 12,
    paddingLeft : 18,
    paddingRight : 18,
    marginTop : 5,
    marginBottom : 5,
    backgroundColor : colors.creamWhite,
    flexDirection : 'row',
  },
  AvailabilityChangeRetainFeatureIcon : {
    width : 24,
    height : 24,
  },
  AvailabilityChangeRetainFeatureTextBox : {
    marginLeft : 12,
    flexShrink : 1,
  },
  AvailabilityChangeRetainFeatureTextCommon : {
    ...TextStyle,
  },
  AvailabilityChangeRetainFeatureTextSpecial : {
      ...TextStyle,
  },
  AvailabilityChangeAddFeatureTextCommonTG : {
    ...TextStyle,
    color : colors.white,
  },
  AvailabilityChangeAddFeatureTextSpecialTG : {
    ...TextStyle,
    color :colors.white,
  },
  AvailabilityChangeAddFeatureTextCommonFC : {
    ...TextStyle,
    color : colors.black,
  },
  AvailabilityChangeAddFeatureTextSpecialFC : {
    ...TextStyle,
    color :colors.black,
  },
  AvailabilityChangeAddFeatureBodyText : {
    ...TextStyle,
    flexShrink: 1,
    color : colors.defaultTextColor,
  },
  AvailabilityChangeAddFeatureBodyTextSpecial : {
    ...TextStyle,
    flexShrink: 1,
    color : colors.defaultTextColor,
  },
  AvailabilityChangeReplaceFeatureBodyText : {
    ...TextStyle,
    color : colors.defaultTextColor,
  },
  AvailabilityChangeAddFeatureContainer : {
    borderRadius : 4.5,
    backgroundColor : colors.white,
    marginVertical : 10,
    elevation : 2,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  AvailabilityChangeAddFeatureTopBarTG : {
    width : '100%',
    borderRadius : 4,
    paddingLeft : 14,
    paddingVertical : 8,
  },
  AvailabilityChangeAddFeatureTopBarFC : {
    width : '100%',
    borderRadius : 4,
    paddingLeft : 14,
    paddingVertical : 8,
    backgroundColor: colors.lighterBlue,
  },

  AvailabilityChangeAddFeatureBodyContainer : {
    paddingTop : 12,
    paddingLeft : 11,
    paddingRight : 13,
  },
  AvailabilityChangeAddFeatureIcon : {
    width : 53,
    height : 52,
    marginRight : 12,
  },
  AvailabilityChangeAddFeatureLowerBox : {
    marginTop : 15,
    borderRadius : 4,
    borderWidth : 0.5,
    borderColor : colors.gray10,
  },
  AvailabilityChangeAddFeatureBoxTopView : {
    marginTop : 10,
    marginLeft:12,
    flexDirection : 'row',
  },
  AvailabilityChangeAddFeatureBoxTopText : {
    fontSize : 12.9,
    ...getLineHeight(12.9),
    color : colors.defaultTextColor,
    marginRight : 'auto',
  },
  AvailabilityChangeAddFeatureBoxTopValue : {
    fontSize : 12.9,
    ...getLineHeight(12.9),
    color : colors.defaultTextColor,
    marginRight : 12,
  },
  AvailabilityChangeAddFeatureBoxBottomView : {
    marginTop : 7,
    marginLeft:12,
    marginBottom : 10,
    flexDirection : 'row',
  },
  AvailabilityChangeAddFeatureBoxBottomText : {
    fontSize : 12.9,
    ...getLineHeight(12.9),
    color : colors.black,
    marginRight : 'auto',
  },
  AvailabilityChangeAddFeatureBoxBottomValue : {
    fontSize : 15.1 ,
    ...getLineHeight(15.1),
    color : colors.lightGreen4,
    marginRight : 5,
  },
  AvailabilityChangeAddFeatureMidPortion : {
    flexDirection: 'row',
    marginLeft : 'auto',
  },

  AvailabilityChangeAddFeatureBoxTickIcon : {
    width : 13 ,
    height : 14,
    marginTop : 2,
    marginRight:12,
  },
  AvailabilityChangeAddFeatureBottomText : {
    marginLeft : 'auto',
    marginRight : 12,
    fontSize : 14,
    ...getLineHeight(14),
    color : colors.azure,
  },
  AvailabilityChangeReplaceFeatureContainer : {
    borderRadius : 4.5,
    backgroundColor : colors.white,
    marginVertical : 10,
    elevation : 2,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  AvailabilityChangeReplaceFeatureBodyExtraText : {
    fontSize : getScreenWidth() > 360 ? 14 : 12,
    lineHeight: getScreenWidth() > 360 ? getLineHeight(14).lineHeight : getLineHeight(12).lineHeight,
    color : colors.defaultTextColor,
    marginTop : 10,
  },
  AvailabilityChangeReplaceFeatureBodyExtraTextSpecial : {
    fontSize : getScreenWidth() > 360 ? 14 : 12,
    lineHeight: getScreenWidth() > 360 ? getLineHeight(14).lineHeight : getLineHeight(12).lineHeight,
    color : colors.defaultTextColor,
  },
  AvailabilityChangeAddFeatureClickButton : {
    flex:1,
    paddingTop :15,
    paddingBottom :20,
  },
});

export const handleClick = (routeToTravelersPage,actionRefreshRailofyStatusChange) => {
  trackReviewPageStatusChangeBackClick();
  if (isMweb()) {
    Actions.travelers({...routeToTravelersPage});
  } else {
    actionRefreshRailofyStatusChange();
   Actions.travelers({});
  }
};

