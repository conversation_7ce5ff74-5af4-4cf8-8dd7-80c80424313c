import React, { useEffect } from 'react';
import {connect} from 'react-redux';
import _isEmpty from 'lodash/isEmpty';
import {StatusChangeType} from './AvailabilityChangeUtils';
import AvailabilityChangeReplaceFeature from './AvailabilityChangeReplaceFeature';
import AvailabilityChangeRetainFeature from './AvailabilityChangeRetainFeature';
import AvailabilityChangeAddFeature from './AvailabilityChangeAddFeature';
import {trackReviewPageStatusChange} from '../../../railsAnalytics';
import { actionRefreshRailofyStatusChange, setRoutedThroughReview } from '../RailsReviewActions';
import {saveRailofyZcShown} from '../../NewListing/RailsListingActions';
import PropTypes from 'prop-types';
const AvailabilityStatusChange = (props) => {
  const {availabilityStatusChange = null, featureSelectionChange = null, 
         newFeaturePersuasion = null,cardToDisplay = null, 
         trackingEvent = null} =
    {...props.statusChangeAlert};
  if (_isEmpty(cardToDisplay)) {
    return null;
  }
// eslint-disable-next-line
  useEffect(() => {
    trackReviewPageStatusChange(trackingEvent);
  },[trackingEvent]);

    switch (cardToDisplay) {
      case StatusChangeType.AVL_TO_WLNONTG_1.id:
      case StatusChangeType.WLNONTG_TO_AVL_1.id:
        return <AvailabilityChangeRetainFeature availabilityStatusChange = {availabilityStatusChange}/>;
      case StatusChangeType.AVL_TO_WLNONTG_13.id:
      case StatusChangeType.WLTG_TO_AVL_13.id:
      case StatusChangeType.WLNONTG_TO_AVL_13.id:
      case StatusChangeType.AVL_TO_WLTG_13.id :
        return <AvailabilityChangeAddFeature 
        showFCIcon = {StatusChangeType[cardToDisplay].showFCIcon} 
        availabilityStatusChange = {availabilityStatusChange}
        newFeaturePersuasion = {newFeaturePersuasion} 
        routeToTravelersPage={props.routeToTravelersPage}
        setRoutedThroughReview = {props.setRoutedThroughReview}
        railofyZcShown = {StatusChangeType[cardToDisplay].railofyZcShown}
        saveRailofyZcShown = {props.saveRailofyZcShown}
        actionRefreshRailofyStatusChange = {props.actionRefreshRailofyStatusChange}
        />;
      case StatusChangeType.WLTG_TO_AVL_123.id :
      case StatusChangeType.AVL_TO_WLTG_123.id:
        return <AvailabilityChangeReplaceFeature 
        showFCIcon = {StatusChangeType[cardToDisplay].showFCIcon} 
        availabilityStatusChange = {availabilityStatusChange}
        featureSelectionChange = {featureSelectionChange} 
        setRoutedThroughReview = {props.setRoutedThroughReview}
        newFeaturePersuasion = {newFeaturePersuasion} 
        routeToTravelersPage={props.routeToTravelersPage}
        railofyZcShown = {StatusChangeType[cardToDisplay].railofyZcShown}
        saveRailofyZcShown = {props.saveRailofyZcShown}
        actionRefreshRailofyStatusChange = {props.actionRefreshRailofyStatusChange}/>;
      default:
        break;
    }
  return null;
};

const mapDispatchToProps = (dispatch) => ({
  setRoutedThroughReview : () => dispatch(setRoutedThroughReview),
  saveRailofyZcShown : (railofyZcShown) => dispatch(saveRailofyZcShown(railofyZcShown)),
  actionRefreshRailofyStatusChange : () => dispatch(actionRefreshRailofyStatusChange),
});

const mapStateToProps = (state) => {
  const { railsListing: { selectedTrainInfo = null, departureDate = null, 
    selectedClassType = null, selectedQuota = null } } = state;
  const {frmStnName = null, frmStnCode = null, toStnName = null, 
    toStnCode = null, trainNumber = null} = {...selectedTrainInfo};
  const routeToTravelersPage = {
    originStation: {
      cityName: frmStnName,
      code: frmStnCode,
      stationName: frmStnName,
    },
    destinationStation: {
      cityName: toStnName,
      code: toStnCode,
      stationName: toStnName,
    },
    departureDate,
    trainNumber: trainNumber,
    classCode: selectedClassType,
    quota: selectedQuota?.code,
    inNormalFlow: true,
  };
  return {
    routeToTravelersPage,
  };
};

AvailabilityStatusChange.propTypes = {
  routeToTravelersPage: PropTypes.object,
  setRoutedThroughReview: PropTypes.func,
  saveRailofyZcShown: PropTypes.func,
  actionRefreshRailofyStatusChange: PropTypes.func,
  statusChangeAlert: PropTypes.object,
};

export default connect(mapStateToProps,mapDispatchToProps)(AvailabilityStatusChange);
