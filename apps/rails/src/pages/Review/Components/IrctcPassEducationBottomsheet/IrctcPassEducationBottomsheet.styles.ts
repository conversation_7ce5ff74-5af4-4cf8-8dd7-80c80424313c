import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
    bottomSheetContainer: {
        backgroundColor: colors.white,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        width: '100%',
    },
    contentContainer: {
        padding: 20,
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: 30,
    },
    headerText: {
        fontSize: 18,
        fontWeight: '700',
        lineHeight: 22,
        color: colors.black,
        alignSelf: 'flex-end',
    },
    crossIcon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
    },
    warningContainer: {
        backgroundColor: colors.creamWhite,
        borderRadius: 16,
        padding: 14,
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 15,
        gap: 12,
        flexShrink: 1,
    },
    warningText: {
        fontSize: 14,
        fontWeight: '400',
        lineHeight: 17,
        color: colors.textGrey,
        flexShrink: 1,
    },
    irctcInfoContainer: {
        marginVertical: 20,
        gap: 20,
        flexDirection: 'column',
    },
    irctcInfoTextContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    irctcInfoText: {
        fontSize: 12,
        fontWeight: '400',
        lineHeight: 14.5,
        color: colors.greyText1,
    },
    irctcUsernameText: {
        fontSize: 14,
        fontWeight: '700',
        lineHeight: 17,
        color: colors.black,
        flexShrink: 1,
    },
    editButtonText: {
        fontSize: 14,
        fontWeight: '400',
        lineHeight: 17,
        color: colors.primary,
    },
    irctcPasswordText: {
        fontSize: 14,
        fontWeight: '500',
        lineHeight: 17,
        color: colors.brown2,
        flexShrink: 1,
    },
    blueButtonContainer:{
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 20,
    },
    blueButtonText1: {
        fontSize: 12,
        fontWeight: '500',
        lineHeight: 14.5,
        color: colors.primary,
        textAlign: 'left',
    },
    blueButtonText2: {
        fontSize: 12,
        fontWeight: '500',
        lineHeight: 14.5,
        color: colors.primary,
        textAlign: 'right',
    },
    separator: {
        height: 1,
        backgroundColor: colors.lightSilver,
    },
    bottomContainer: {
        flexDirection: 'column',
        gap: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    fullWidthContainer: {
        width: '100%',
    },
    ctaButtonContainer: {
        borderRadius: 8,
        padding: 16,
        width: '100%',
    },
    ctaButtonTextEnabled: {
        fontSize: 16,
        fontWeight: '700',
        lineHeight: 19,
        color: colors.white,
        textAlign: 'center',
    },
    ctaButtonTextDisabled: {
        fontSize: 16,
        fontWeight: '700',
        lineHeight: 19,
        color: colors.black,
        opacity: 0.3,
        textAlign: 'center',
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    checkBoxIconContainer: {
        height: 18,
        width: 18,
    },
    title: {
        color: colors.textGrey,
        fontSize: 14,
        fontWeight: '400',
        lineHeight: 17,
        marginLeft: 10,
    },
  });
