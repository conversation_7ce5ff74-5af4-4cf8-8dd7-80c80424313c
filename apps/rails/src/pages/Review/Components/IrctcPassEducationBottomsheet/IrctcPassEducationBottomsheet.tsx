import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { styles } from './IrctcPassEducationBottomsheet.styles';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import LinearGradient from 'react-native-linear-gradient';
import CheckBox from 'react-native-checkbox';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import { Actions } from 'apps/rails/src/navigation';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { trackReviewPageClickEvent, updateEvar99Variable } from 'apps/rails/src/railsAnalytics';
import RAIL_EVENTS from 'apps/rails/src/RailsOmnitureTracker';

const enabledColors = [colors.lightBlue, colors.darkBlue];
const disabledColors = [colors.grey37, colors.grey37];

interface IrctcPassEducationBottomsheetProps {
  userName: string;
  displayVariant: number;
  displayBottomsheet: (val: boolean) => void;
  onEditUsernameClick: (val: boolean) => void;
  onRetrievePasswordClick: (val: boolean) => void;
  onBookNowClick: (e: unknown, proceedWithoutBs: boolean) => void;
  config: unknown;
}

function IrctcPassEducationBottomsheet(props: IrctcPassEducationBottomsheetProps) {
  const { config = {} } = props || {};
  const [isChecked, setIsChecked] = useState(props?.displayVariant === 1 ? false : true);

  const onCreateNewClick = () => {
    Actions.openCreateIRCTCAccountWebView({
      from: 'review',
    });
    trackReviewPageClickEvent(RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_CREATE_ACCOUNT_CLICK);
  };

  const onGetNewPasswordClick = () => {
    props?.onRetrievePasswordClick?.(true);
    trackReviewPageClickEvent(RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_RETRIEVE_PASS_CLICK);
  };

  const onBookNowClick = (e: unknown) => {
    if (isChecked) {
      props?.onBookNowClick?.(e, true);
      trackReviewPageClickEvent(RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_PROCEED_TO_PAYMENT_CLICK);
      updateEvar99Variable(RAIL_EVENTS.REVIEW.IRCTC_BOTTOMSHEET_PROCEED_TO_PAYMENT_EVENT);
    }
  };

  const onCheckBoxClick = () => {
    setIsChecked(!isChecked);
    trackReviewPageClickEvent(RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_CHECKBOX_CLICK);
  };

  const onEditUsernameClick = () => {
    props?.onEditUsernameClick?.(true);
    trackReviewPageClickEvent(RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_EDIT_USERNAME_CLICK);
  };

  const dismissBottomsheet = () => {
    props?.displayBottomsheet?.(false);
    trackReviewPageClickEvent(RAIL_EVENTS.REVIEW.IRCTC_PASS_BOTTOMSHEET_CROSS_CLICK);
  };

  return (
    <>
      <View style={styles.bottomSheetContainer} testID="irctc_pass_education_bottomsheet_container">
        <View style={styles.contentContainer}>
          <View style={styles.headerContainer}>
            <Text style={styles.headerText}>{config?.headerText}</Text>
            <TouchableOpacity
              onPress={dismissBottomsheet}
              testID="irctc_pass_education_bottomsheet_close_button"
            >
              <Image source={ASSETS.crossIcon} style={styles.crossIcon} />
            </TouchableOpacity>
          </View>
          <View style={styles.warningContainer}>
            <Image source={{uri: config?.mainIconUrl}} style={styles.crossIcon} />
            <Text style={styles.warningText}>{config?.warningText}</Text>
          </View>
          <View style={styles.irctcInfoContainer}>
            {props?.userName && (
              <View style={styles.irctcInfoTextContainer}>
                <Text style={styles.irctcInfoText}>{_label('irctc_username', {uppercase: true})}</Text>
                <Text style={styles.irctcUsernameText}>{props?.userName}</Text>
                <TouchableRipple
                  onPress={onEditUsernameClick}
                  testID="irctc_pass_education_bottomsheet_edit_username_button"
                >
                  <Text style={styles.editButtonText}>{_label('edit')}</Text>
                </TouchableRipple>
              </View>
            )}
            <View style={styles.irctcInfoTextContainer}>
              <Text style={styles.irctcInfoText}>{_label('irctc_password', {uppercase: true})}</Text>
              <Text style={styles.irctcPasswordText}>{config?.passwordText}</Text>
            </View>
          </View>
          <View style={styles.blueButtonContainer}>
            <TouchableRipple
              onPress={onGetNewPasswordClick}
              testID="irctc_pass_education_bottomsheet_get_new_password_button"
            >
              <Text style={styles.blueButtonText1}>{_label('get_new_password')}</Text>
            </TouchableRipple>
            <TouchableRipple
              onPress={onCreateNewClick}
              testID="irctc_pass_education_bottomsheet_create_irctc_account_button"
            >
              <Text style={styles.blueButtonText2}>{_label('create_irctc_account')}</Text>
            </TouchableRipple>
          </View>
        </View>
        <View style={styles.separator} />
        <View style={styles.contentContainer}>
          <View style={styles.bottomContainer}>
            {(props?.displayVariant === 1) &&
              <TouchableRipple
                onPress={() => {
                  setIsChecked(!isChecked);
                }}
                testID="irctc_pass_education_bottomsheet_checkbox"
              >
                <View
                  style={styles.checkboxContainer}
                  testID="irctc_pass_education_bottomsheet_checkbox_container"
                >
                  <CheckBox
                    label=""
                    onChange={onCheckBoxClick}
                    style={styles.checkBoxIconContainer}
                    checked={isChecked}
                    checkedImage={ASSETS.checkBoxActive}
                    uncheckedImage={ASSETS.checkBoxInactive}
                  />
                  <Text style={styles.title}>{config?.checkBoxLabel}</Text>
                </View>
              </TouchableRipple>
            }
            <TouchableRipple
              onPress={onBookNowClick}
              style={styles.fullWidthContainer}
              testID="irctc_pass_education_bottomsheet_book_now_button"
            >
              <LinearGradient
                style={styles.ctaButtonContainer}
                colors={isChecked ? enabledColors : disabledColors}
                start={{ x: 0, y: 2 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={styles.ctaButtonTextEnabled}>{config?.ctaButtonText}</Text>
              </LinearGradient>
            </TouchableRipple>
          </View>
        </View>
      </View>
    </>
  );
}

export default IrctcPassEducationBottomsheet;
