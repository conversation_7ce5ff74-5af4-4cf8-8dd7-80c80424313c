import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getLineHeight, fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';

export const styles = StyleSheet.create({
  bottomSheetContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    width: '100%',
    padding: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    gap: 4,
    alignItems: 'center',
  },
  headerText: {
    ...fontStyle('medium'),
    ...getLineHeight(24),
    fontWeight: '900',
    color: colors.black,
  },
  mainIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  contentContainer: {
    marginVertical: 24,
    gap: 12,
  },
  p: {
    ...fontStyle('medium'),
    ...getLineHeight(16),
    fontWeight: '400',
    color: colors.black,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  b: {
    color: colors.black,
    fontWeight: '700',
  },
  warningText: {
    ...fontStyle('regular'),
    ...getLineHeight(14),
    fontWeight: '700',
    color: colors.red17,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  bottomContainer: {
    gap: 12,
    marginBottom: 10,
  },
  fullWidthContainer: {
    width: '100%',
  },
  ctaButtonContainer1: {
    borderRadius: 8,
    padding: 12,
    width: '100%',
  },
  ctaButtonContainer2: {
    borderRadius: 8,
    padding: 12,
    width: '100%',
    borderWidth: 1,
    borderColor: colors.azure,
  },
  ctaButtonText1: {
    ...fontStyle('medium'),
    ...getLineHeight(16),
    fontWeight: '900',
    color: colors.white,
    textAlign: 'center',
  },
  ctaButtonText2: {
    ...fontStyle('medium'),
    ...getLineHeight(16),
    fontWeight: '900',
    color: colors.azure,
    textAlign: 'center',
  },
});
