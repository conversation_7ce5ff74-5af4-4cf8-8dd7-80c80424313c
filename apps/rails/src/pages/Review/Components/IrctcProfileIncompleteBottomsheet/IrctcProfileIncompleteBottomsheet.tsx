import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import React, { useEffect } from 'react';
import { Image, Text, View } from 'react-native';
import { styles } from './IrctcProfileIncompleteBottomsheet.styles';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import LinearGradient from 'react-native-linear-gradient';
import HTMLView from 'react-native-htmlview';
import { Actions } from '@mmt/rails/src/navigation';
import { trackGenericEvar99Event, trackReviewPageClickEvent } from '../../../../railsAnalytics';
import { IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS, REVIEW_PAGE_TRACKING_KEY } from '../../../../Utils/RailsConstant';

const enabledColors = [colors.lightBlue, colors.darkBlue];

interface props {
  onBookNowClick: () => void;
  config: unknown;
  userName: string;
}

function IrctcProfileIncompleteBottomsheet(props: props) {

  useEffect(() => {
    trackReviewPageClickEvent(IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.REVIEW.BOTTOMSHEET_LOAD);
  }, []);

  const handleCompleteProfileClick = () => {
    Actions.openCompleteIrctcProfileWebView('replace');
    trackReviewPageClickEvent(IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.REVIEW.COMPLETE_PROFILE_CLICK);
    trackGenericEvar99Event(REVIEW_PAGE_TRACKING_KEY, 
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.REVIEW.COMPLETE_PROFILE_CLICK_EVAR99);
  };

  const handleBookNowClick = () => {
    props?.onBookNowClick?.();
    trackReviewPageClickEvent(IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.REVIEW.PROCEED_WITHOUT_COMPLETE_CLICK);
  };

  return (
    <>
      <View
        style={styles.bottomSheetContainer}
        testID="irctc_profile_incomplete_bottomsheet_container"
      >
        <View style={styles.headerContainer}>
          <Image source={ASSETS.errorInfoIcon} style={styles.mainIcon} />
          <Text style={styles.headerText}>{props?.config?.title}</Text>
        </View>
        <View
          style={styles.contentContainer}
          testID="irctc_profile_incomplete_bottomsheet_content_container"
        >
          <HTMLView
            value={
              props?.config?.description?.replace('{{irctcUsername}}', props?.userName ?? '') || ''
            }
            stylesheet={styles}
          />
          <Text style={styles.warningText}>{props?.config?.warningText}</Text>
        </View>
        <View style={styles.bottomContainer}>
          <TouchableRipple
            onPress={handleCompleteProfileClick}
            style={styles.fullWidthContainer}
            testID="irctc_profile_incomplete_bottomsheet_complete_profile_button"
          >
            <LinearGradient
              style={styles.ctaButtonContainer1}
              colors={enabledColors}
              start={{ x: 0, y: 2 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.ctaButtonText1}>{_label('complete_profile_now')}</Text>
            </LinearGradient>
          </TouchableRipple>
          <TouchableRipple
            onPress={handleBookNowClick}
            style={styles.ctaButtonContainer2}
            testID="irctc_profile_incomplete_bottomsheet_proceed_without_completing_button"
          >
            <Text style={styles.ctaButtonText2}>{_label('proceed_without_completing')}</Text>
          </TouchableRipple>
        </View>
      </View>
    </>
  );
}

export default IrctcProfileIncompleteBottomsheet;
