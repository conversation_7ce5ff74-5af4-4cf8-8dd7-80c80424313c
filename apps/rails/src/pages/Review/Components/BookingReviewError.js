import React, {Component} from 'react';
import {Text, View} from 'react-native';
import PropTypes from 'prop-types';
import SaveOrSubmitButton from '../../Traveller/Components/SaveOrSubmitButton';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from '../../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../../vernacular/VernacularUtils';

export default class BookingReviewError extends Component {
  render() {
    return (

      <View style={{ flex: 1, justifyContent: 'center' }} testID={this.props?.id}>
        <View style={{ padding: 10, gap: 10 }}>
          <Text
            style={{
              fontSize: 22,
              ...fontStyle('bold'),
              ...getLineHeight(18),
              marginHorizontal: 80,
              color: colors.black,
            textAlign: 'center',
          }}>
            {this.props?.bookingReviewDefaultErrorTitle || _label('review_error')}
          </Text>
          <Text style={{fontSize: 14,...fontStyle('regular'), ...getLineHeight(14),color:colors.defaultTextColor,
            textAlign: 'center'}}>{this.props.errorMessage}</Text>
        </View>
        <View style={{justifyContent: 'center', flexDirection: 'row'}}>
          <SaveOrSubmitButton
            enableButton
            onSaveOrSubmit={this.props.onBack}
            buttonText={_label('back', { uppercase: true })}
            id={`${this.props?.id}_backButton`}
          />
        </View>
      </View>
    );
  }
}

BookingReviewError.propTypes = {
  errorMessage: PropTypes.string.isRequired,
  onBack: PropTypes.func.isRequired,
  id: PropTypes.string,
  bookingReviewDefaultErrorTitle: PropTypes.string,
};
