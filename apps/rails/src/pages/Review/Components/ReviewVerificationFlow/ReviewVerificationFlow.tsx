import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { styles } from './ReviewVerificationFlow.styles';
import { showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { trackGenericEvar47or97Event } from '@mmt/rails/src/railsAnalytics';
import { fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import ASSETS from '../../../../Utils/Assets/RailsAssets';
import isEmpty from 'lodash/isEmpty';
import LinearGradient from 'react-native-linear-gradient';
import fetch2 from '../../../../fetch2';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import CustomOTPInput from './CustomOTPInput';
import railsConfig from '../../../../RailsConfig';
import {
  RAILS_VERIFICATION_FLOW_CONSTANTS as CONSTANTS,
  RAILS_VERIFICATION_FLOW_OMNITURE_EVENTS as OMNITURE_EVENTS,
  REVIEW_PAGE_CONST,
  RAILS_VERIFICATION_FLOW_USER_VERIFIED,
} from '../../../../Utils/RailsConstant';
import { _label } from '../../../../vernacular/AppLanguage';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

interface CTA {
  action: string;
  text: string;
  code: string;
  backgroundColor: string;
  textColor: string;
  backgroundColorGradient?: string[];
  omnitureEvent?: string;
}

interface FlowData {
  templateType?: string;
  title: string;
  subTitle?: string;
  titleTextColor?: string;
  subTitleTextColor?: string;
  notificationText?: string;
  notificationTextBgColor?: string;
  consent?: string;
  ctas: CTA[];
  iconUrl?: string;
  disclaimer?: string;
  message?: string;
  resendOtp?: boolean;
  attemptAlert?: string;
  resendText?: string;
  timerToResendOTP?: number;
  otpError?: string;
  consentChecked?: boolean;
}

interface ReviewVerificationFlowProps {
  onClose?: (bookNowClicked?: boolean) => void;
  irctcUserName?: string;
  irctcBookingId?: string;
  mmtId?: string;
  clearReviewVerificationFlow?: () => void;
  reviewVerificationFlow?: FlowData;
}

const ReviewVerificationFlow: React.FC<ReviewVerificationFlowProps> = ({
  onClose,
  irctcUserName = '',
  irctcBookingId = '',
  mmtId = '',
  clearReviewVerificationFlow,
  reviewVerificationFlow,
}) => {
  const [isConsentChecked, setIsConsentChecked] = useState(false);
  const [currentFlow, setCurrentFlow] = useState<FlowData | null>(null);
  const [otpValue, setOtpValue] = useState('');
  const [otpError, setOtpError] = useState('');
  const [timer, setTimer] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [resendAttempts, setResendAttempts] = useState(0);

  useEffect(() => {
    setCurrentFlow(reviewVerificationFlow || null);
    setIsConsentChecked(reviewVerificationFlow?.consentChecked || false);
  }, [reviewVerificationFlow]);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, CONSTANTS.TIMER_INTERVAL);
    }
    return () => clearInterval(interval);
  }, [timer]);

  const generateAadhaarOTP = async () => {
    try {
      const response = (await fetch2(railsConfig.generateAadhaarOTP, {
        method: 'POST',
        headers: {
          organization: CONSTANTS.ORGANIZATION,
          'Content-Type': CONSTANTS.CONTENT_TYPE,
        },
        body: JSON.stringify({
          wsUserLogin: irctcUserName,
          aadhaarConsent: true,
          clientTxnId: irctcBookingId,
          bookingId: mmtId,
        }),
      })) as unknown as Response;

      return await response.json();
    } catch (error) {
      console.error(CONSTANTS.TEXT.ERROR_GENERATING_OTP, error);
      showLongToast(_label('sorry_something_wrong'));
      return currentFlow;
    }
  };

  const verifyAadhaarOTP = async (otp: string) => {
    try {
      const response = (await fetch2(railsConfig.verifyAadhaarOTP, {
        method: 'POST',
        headers: {
          organization: CONSTANTS.ORGANIZATION,
          'Content-Type': CONSTANTS.CONTENT_TYPE,
        },
        body: JSON.stringify({
          clientTxnId: irctcBookingId,
          wsUserLogin: irctcUserName,
          captchaAns: otp,
          bookingId: mmtId,
        }),
      })) as unknown as Response;

      const data = await response.json();
      if (data.otpError) {
        const errorFlowData = JSON.parse(JSON.stringify(currentFlow));
        setOtpError(data.otpError);
        return errorFlowData;
      }

      return data;
    } catch (error) {
      console.error(CONSTANTS.TEXT.ERROR_VERIFYING_OTP, error);
      showLongToast(_label('sorry_something_wrong'));
      return currentFlow;
    }
  };

  const handleGenerateOTP = async () => {
    if (!isConsentChecked && currentFlow?.consent) {
      return;
    }

    setIsLoading(true);
    try {
      const otpFlowData = await generateAadhaarOTP();
      setCurrentFlow(otpFlowData);
      setTimer(otpFlowData.timerToResendOTP || CONSTANTS.DEFAULT_TIMER);
      setOtpError('');
      setOtpValue('');
    } catch (error) {
      console.error(CONSTANTS.TEXT.ERROR_GENERATING_OTP, error);
      showLongToast(_label('sorry_something_wrong'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitOTP = async () => {
    if (!otpValue || otpValue.length !== CONSTANTS.OTP_LENGTH) {
      setOtpError(currentFlow?.otpError || CONSTANTS.TEXT.INVALID_OTP_ERROR);
      return;
    }

    setIsLoading(true);
    setOtpError('');

    try {
      const verificationResult = await verifyAadhaarOTP(otpValue);
      setCurrentFlow(verificationResult);
    } catch (error) {
      if (__DEV__) {
        console.error(CONSTANTS.TEXT.ERROR_VERIFYING_OTP, error);
      }
      setOtpError(error.message || CONSTANTS.TEXT.GENERIC_ERROR);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    setCurrentFlow(reviewVerificationFlow || null);
    setOtpValue('');
    setOtpError('');
    setTimer(0);
  };

  const handleContinueBooking = () => {
    clearReviewVerificationFlow?.();
    onClose?.(true);
    trackGenericEvar47or97Event(REVIEW_PAGE_CONST, RAILS_VERIFICATION_FLOW_USER_VERIFIED);
  };

  const handleClose = () => {
    setCurrentFlow(reviewVerificationFlow || null);
  };

  const handleCloseBottomSheet = () => {
    onClose?.();
  };

  const handleCTAPress = (cta: CTA) => {
    let omnitureEvent = '';
    switch (cta.action) {
      case CONSTANTS.ACTIONS.GENERATE_AADHAAR_OTP:
        omnitureEvent = OMNITURE_EVENTS.AADHAAR_GENERATE_OTP_CLICK;
        break;
      case CONSTANTS.ACTIONS.SUBMIT_AADHAAR_OTP:
        omnitureEvent = OMNITURE_EVENTS.AADHAAR_VERIFY_OTP;
        break;
      case CONSTANTS.ACTIONS.CLOSE_BOTTOMSHEET:
      case CONSTANTS.ACTIONS.CLOSE:
        omnitureEvent =
          currentFlow?.templateType === CONSTANTS.TEMPLATE_TYPES.VERIFY_AADHAAR_OTP_FLOW
            ? OMNITURE_EVENTS.AADHAAR_VERIFY_OTP_CLOSE
            : OMNITURE_EVENTS.AADHAAR_GENERATE_OTP_CLOSE;
        break;
      case CONSTANTS.ACTIONS.BACK:
        omnitureEvent = OMNITURE_EVENTS.AADHAAR_VERIFY_OTP_CLOSE;
        break;
      case CONSTANTS.ACTIONS.CONTINUE_BOOKING:
        omnitureEvent = OMNITURE_EVENTS.AADHAAR_VERIFY_OTP_SUCCESSFUL;
        break;
      default:
        omnitureEvent = `${cta.action}_${cta.code}`;
    }

    if (omnitureEvent) {
      trackClickEventProp61(REVIEW_PAGE_CONST, omnitureEvent);
    }

    switch (cta.action) {
      case CONSTANTS.ACTIONS.CLOSE_BOTTOMSHEET:
        handleCloseBottomSheet();
        break;
      case CONSTANTS.ACTIONS.GENERATE_AADHAAR_OTP:
        void handleGenerateOTP();
        break;
      case CONSTANTS.ACTIONS.BACK:
        handleBack();
        break;
      case CONSTANTS.ACTIONS.SUBMIT_AADHAAR_OTP:
        void handleSubmitOTP();
        break;
      case CONSTANTS.ACTIONS.CONTINUE_BOOKING:
        handleContinueBooking();
        break;
      case CONSTANTS.ACTIONS.CLOSE:
        handleClose();
        break;
      default:
        handleCloseBottomSheet();
    }
  };

  const handleResendOTP = async () => {
    if (timer > 0) {
      return;
    }

    // Track resend attempt with counter
    const newAttemptCount = resendAttempts + 1;
    setResendAttempts(newAttemptCount);
    trackClickEventProp61(
      REVIEW_PAGE_CONST,
      `${OMNITURE_EVENTS.AADHAAR_VERIFY_OTP_RESENT}_${newAttemptCount}_Attempts`,
    );

    setIsLoading(true);
    try {
      const otpFlowData = await generateAadhaarOTP();
      setCurrentFlow(otpFlowData);
      setTimer(otpFlowData.timerToResendOTP || CONSTANTS.DEFAULT_TIMER);
      setOtpError('');
      setOtpValue('');
    } catch (error) {
      if (__DEV__) {
        console.error(CONSTANTS.TEXT.ERROR_RESENDING_OTP, error);
      }
      // Track technical error
      trackClickEventProp61(REVIEW_PAGE_CONST, OMNITURE_EVENTS.AADHAAR_VERIFY_OTP_TECHNICAL_ERROR);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleConsent = () => {
    const newCheckedState = !isConsentChecked;
    setIsConsentChecked(newCheckedState);

    const omnitureEvent = newCheckedState
      ? OMNITURE_EVENTS.AADHAAR_VERIFY_LINK_OTP_CHECK
      : OMNITURE_EVENTS.AADHAAR_VERIFY_LINK_OTP_UNCHECK;
    trackClickEventProp61(REVIEW_PAGE_CONST, omnitureEvent);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleOtpChange = (text: string) => {
    const numericText = text.replace(/[^0-9]/g, '');
    if (numericText.length <= CONSTANTS.OTP_LENGTH) {
      setOtpValue(numericText);
      if (otpError) {
        setOtpError('');
      }
    }
  };

  if (!currentFlow) {
    return null;
  }

  const isOtpFlow = currentFlow.templateType === CONSTANTS.TEMPLATE_TYPES.VERIFY_AADHAAR_OTP_FLOW;
  const isGenerateOtpFlow = currentFlow.ctas.some(
    (cta) => cta.action === CONSTANTS.ACTIONS.GENERATE_AADHAAR_OTP,
  );
  const isSimpleFlow = currentFlow.templateType === CONSTANTS.TEMPLATE_TYPES.SIMPLE;

  return (
    <KeyboardAvoidingView
      style={styles.keyboardContainer}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      enabled={Platform.OS === 'ios'}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          {currentFlow.iconUrl && (
            <Image source={{ uri: currentFlow.iconUrl }} style={styles.icon} resizeMode="contain" />
          )}
          {isOtpFlow && (
            <TouchableOpacity
              onPress={() => {
                trackClickEventProp61(REVIEW_PAGE_CONST, OMNITURE_EVENTS.AADHAAR_VERIFY_OTP_CLOSE);
                setCurrentFlow(reviewVerificationFlow || null);
              }}
            >
              <Image
                source={{
                  uri: CONSTANTS.BACK_ARROW_URL,
                }}
                style={styles.icon}
                resizeMode="contain"
              />
            </TouchableOpacity>
          )}
          <Text
            style={[styles.title, { color: currentFlow.titleTextColor }, fontStyle('latoBlack')]}
          >
            {currentFlow.title}
          </Text>
        </View>

        {currentFlow.subTitle && (
          <Text
            style={[
              styles.subTitle,
              { color: currentFlow.subTitleTextColor },
              fontStyle('regular'),
            ]}
          >
            {currentFlow.subTitle}
          </Text>
        )}

        {!isOtpFlow && !isSimpleFlow && currentFlow.notificationText && (
          <View
            style={[
              styles.notificationBox,
              { backgroundColor: currentFlow.notificationTextBgColor },
            ]}
          >
            <Text style={[styles.notificationText, fontStyle('regular')]}>
              {currentFlow.notificationText}
            </Text>
          </View>
        )}

        {isOtpFlow && (
          <CustomOTPInput
            value={otpValue}
            onChangeText={handleOtpChange}
            onClear={() => {
              setOtpValue('');
              setOtpError('');
              trackClickEventProp61(REVIEW_PAGE_CONST, OMNITURE_EVENTS.AADHAAR_VERIFY_OTP_CROSS);
            }}
            error={otpError}
            placeholder={CONSTANTS.TEXT.OTP_PLACEHOLDER}
            label={CONSTANTS.TEXT.OTP_LABEL}
            maxLength={CONSTANTS.OTP_LENGTH}
          />
        )}

        {isOtpFlow && (
          <View style={styles.resendSection}>
            <Text style={[styles.attemptAlert, fontStyle('regular')]}>
              {currentFlow.attemptAlert}
            </Text>
            <TouchableOpacity
              onPress={() => void handleResendOTP()}
              disabled={timer > 0 || isLoading}
              style={timer > 0 || isLoading ? styles.disabledButton : null}
            >
              <Text
                style={[
                  styles.resendButtonText,
                  timer === 0 && styles.resendButtonTextEnabled,
                  fontStyle('bold'),
                ]}
              >
                {CONSTANTS.TEXT.RESEND}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {isOtpFlow && (
          <Text style={[styles.resendText, fontStyle('regular')]}>
            {currentFlow.resendText} {formatTime(timer)}
          </Text>
        )}

        {isGenerateOtpFlow && currentFlow.consent && (
          <View style={styles.checkboxContainer}>
            <CheckBox
              label=""
              isChecked={isConsentChecked}
              onPress={toggleConsent}
              size={24}
              checkedImage={ASSETS.checkBoxActive}
              uncheckedImage={ASSETS.checkBoxInactive}
            />
            <Text style={[styles.consentText, fontStyle('regular')]}>{currentFlow.consent}</Text>
          </View>
        )}

        <View style={styles.ctaContainer}>
          {currentFlow?.ctas?.map((cta, index) => {
            const isSubmitOtp = cta.action === CONSTANTS.ACTIONS.SUBMIT_AADHAAR_OTP;
            const isGenerateOtp = cta.action === CONSTANTS.ACTIONS.GENERATE_AADHAAR_OTP;
            const isDisabled =
              (isGenerateOtp && !isConsentChecked && !!currentFlow?.consent) ||
              (isSubmitOtp &&
                (!otpValue || otpValue.length !== CONSTANTS.OTP_LENGTH || !!otpError));

            if (!isEmpty(cta.backgroundColorGradient)) {
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => handleCTAPress(cta)}
                  disabled={isDisabled || isLoading}
                >
                  <LinearGradient
                    colors={
                      isDisabled
                        ? [colors.disabledBtnBg, colors.disabledBtnBg]
                        : cta.backgroundColorGradient || []
                    }
                    start={{ x: 0.0, y: 0.0 }}
                    end={{ x: 1.0, y: 0.0 }}
                    style={[
                      styles.ctaButton,
                      cta.action === CONSTANTS.ACTIONS.BACK ? styles.backButton : null,
                      isDisabled ? styles.disabledButton : null,
                    ]}
                  >
                    {isLoading && (isSubmitOtp || isGenerateOtp) ? (
                      <ActivityIndicator color={cta.textColor} />
                    ) : (
                      <Text
                        style={[styles.ctaText, { color: cta.textColor }, fontStyle('semiBold')]}
                      >
                        {cta.text}
                      </Text>
                    )}
                  </LinearGradient>
                </TouchableOpacity>
              );
            }
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.ctaButton,
                  { backgroundColor: cta.backgroundColor },
                  cta.action === CONSTANTS.ACTIONS.BACK ? styles.backButton : null,
                  isDisabled ? styles.disabledButton : null,
                ]}
                onPress={() => handleCTAPress(cta)}
                disabled={isDisabled || isLoading}
              >
                {isLoading && (isSubmitOtp || isGenerateOtp) ? (
                  <ActivityIndicator color={cta.textColor} />
                ) : (
                  <Text style={[styles.ctaText, { color: cta.textColor }, fontStyle('semiBold')]}>
                    {cta.text}
                  </Text>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ReviewVerificationFlow;
