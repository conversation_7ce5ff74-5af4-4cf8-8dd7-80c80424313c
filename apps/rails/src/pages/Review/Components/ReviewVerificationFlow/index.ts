import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ReviewVerificationFlow from './ReviewVerificationFlow';
import { clearReviewVerificationFlow } from '../../RailsReviewActions';

const mapStateToProps = (state, ownProps) => {
  const {
    railsUserVerification: { irctcUserName = '' },
    railsReview,
  } = state;

  const {
    reviewVerificationFlow = null,
    bookingReviewResponse: { irctcBookingId = '', mmtId = '' },
  } = railsReview;
  return {
    irctcUserName,
    reviewVerificationFlow,
    irctcBookingId,
    mmtId,
    ...ownProps,
  };
};

const actionCreators = {
  clearReviewVerificationFlow,
};

const mapDispatchToProps = (dispatch) => bindActionCreators(actionCreators, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(ReviewVerificationFlow);
