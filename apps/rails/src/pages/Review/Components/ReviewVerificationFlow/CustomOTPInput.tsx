import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';
import { fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface CustomOTPInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onClear: () => void;
  error?: string;
  placeholder?: string;
  label?: string;
  maxLength?: number;
}

const CustomOTPInput: React.FC<CustomOTPInputProps> = ({
  value,
  onChangeText,
  onClear,
  error,
  placeholder = 'Enter 6 digit OTP',
  label = 'ENTER 6 DIGIT OTP',
  maxLength = 6,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const hasValue = value.length > 0;
  const showLabel = isFocused || hasValue;

  return (
    <View style={styles.container}>
      <View
        style={[
          isFocused ? styles.textInputContainerFocused : styles.textInputContainer,
          !!error && {
            borderColor: colors.red17,
            backgroundColor: colors.backgroundRed3,
          },
        ]}
      >
        {showLabel && (
          <Text
            style={[
              isFocused ? styles.textInputHeaderFocused : styles.textInputHeader,
              fontStyle('bold'),
              !!error && { color: colors.red17 },
            ]}
          >
            {label}
          </Text>
        )}

        <View style={styles.inputWrapper}>
          <TextInput
            style={[
              styles.textInputStyle,
              fontStyle('black'),
              { fontSize: 16, lineHeight: 19.2 },
              !showLabel && { paddingVertical: 8 },
            ]}
            value={value}
            onChangeText={onChangeText}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            keyboardType="numeric"
            maxLength={maxLength}
            placeholder={placeholder}
            placeholderTextColor={colors.lightTextColor}
            autoCorrect={false}
            autoCapitalize="none"
          />

          {hasValue && (
            <TouchableOpacity style={styles.clearButton} onPress={onClear}>
              <Text style={styles.clearButtonText}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {error && <Text style={[styles.errorText, fontStyle('regular')]}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  textInputContainer: {
    marginTop: 16,
    backgroundColor: colors.smokeWhite,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.lightSilver,
    paddingHorizontal: 12,
    paddingTop: 5.5,
    paddingBottom: 3,
  },
  textInputContainerFocused: {
    marginTop: 16,
    backgroundColor: colors.lighterBlue,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.primary,
    paddingHorizontal: 12,
    paddingTop: 5.5,
    paddingBottom: 3,
  },
  textInputHeader: {
    fontSize: 12,
    color: colors.lightSilver,
    marginBottom: 5,
  },
  textInputHeaderFocused: {
    fontSize: 12,
    color: colors.primary,
    marginBottom: 5,
  },
  inputWrapper: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  textInputStyle: {
    fontSize: 16,
    padding: 0,
    flex: 1,
  },
  clearButton: {
    position: 'absolute',
    right: 12,
    top: -12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.gray3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: 'bold',
  },
  errorText: {
    color: colors.red26,
    fontSize: 14,
    marginTop: 8,
    fontWeight: '400',
  },
});

export default CustomOTPInput;
