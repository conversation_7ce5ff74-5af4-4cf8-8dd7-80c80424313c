import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet, Platform } from 'react-native';

export const styles = StyleSheet.create({
  keyboardContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: Platform.OS === 'android' ? 0 : 20,
  },
  scrollContainer: {
    flexShrink: 1,
  },
  scrollContentContainer: {
    flexGrow: 0,
  },
  container: {
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 20,
    justifyContent: 'flex-start',
  },
  contentContainer: {
    flex: 0,
    justifyContent: 'flex-start',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  icon: {
    width: 26,
    height: 26,
    marginRight: 12,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 18,
    flex: 1,
    lineHeight: 22,
  },
  subTitle: {
    fontSize: 16,
    lineHeight: 20,
    marginBottom: 8,
  },
  notificationBox: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  notificationText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.darkCharcoal,
  },
  consentText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.darkCharcoal,
    marginLeft: 12,
    flex: 1,
    flexWrap: 'wrap',
  },
  consentContainer: {
    marginBottom: 24,
  },
  ctaContainer: {
    gap: 12,
    marginTop: 8,
    paddingBottom: Platform.OS === 'android' ? 16 : 0,
  },
  ctaButton: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  ctaText: {
    fontSize: 16,
    textAlign: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  backButton: {
    borderWidth: 1,
    borderColor: colors.cyan3,
  },
  // OTP Flow Styles
  otpSection: {
    marginBottom: 16,
  },
  otpLabel: {
    fontSize: 12,
    color: colors.lightGrey4,
    marginBottom: 8,
    fontWeight: '400',
    letterSpacing: 1,
  },
  otpInputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  otpInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.lightSilver,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 14,
    paddingRight: 50,
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'left',
    letterSpacing: 2,
    backgroundColor: colors.smokeWhite,
    minHeight: 52,
  },
  clearButton: {
    position: 'absolute',
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.gray3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: 'bold',
  },
  otpInputError: {
    borderColor: colors.red6,
    borderWidth: 2,
  },
  otpInputFocused: {
    borderColor: colors.cyan3,
    borderWidth: 2,
  },
  errorText: {
    color: colors.red6,
    fontSize: 14,
    marginTop: 8,
    fontWeight: '400',
  },
  attemptAlert: {
    fontSize: 12,
    color: colors.greyText1,
    marginBottom: 8,
    fontWeight: '400',
  },
  resendSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resendText: {
    fontSize: 12,
    color: colors.greyText1,
    marginBottom: 10,
  },
  resendButtonText: {
    fontSize: 12,
    color: colors.grey41,
  },
  resendButtonTextEnabled: {
    color: colors.cyan3,
  },
  // Loader styles
  loaderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },
  loader: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.azure,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderSpinner: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 4,
    borderColor: colors.white4,
    borderTopColor: colors.white,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    paddingRight: 8,
  },
});
