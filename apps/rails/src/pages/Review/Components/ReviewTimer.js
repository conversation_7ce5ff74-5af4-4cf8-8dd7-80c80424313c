import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {AppState, Image, StyleSheet, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {RAILS_BOOKING_TIMER_START_TIME, setDataToAsyncStorage} from '../../../Utils/RailsConstant';
import {_label} from '../../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../../vernacular/VernacularUtils';

const timerColors = [colors.goldenYellow, colors.yello];
let startTime;
export const IRCTC_BOOKING_TIME_LIMIT = 600;
import clock from '@mmt/legacy-assets/src/ic-clock-stroke.webp';

let appStateListener;
export default class ReviewTimer extends Component {
  state = {
    elapseTime: IRCTC_BOOKING_TIME_LIMIT,
    appState: AppState.currentState,
  };

  async UNSAFE_componentWillMount() {
    if (this.props.startTime === -1) {
      return;
    }
    startTime = this.props.startTime / 1000;
    this.interval = setInterval(() => {
      const time = this.state.elapseTime - 1;
      if (time <= 0) {
        clearInterval(this.interval);
        this.props.onDone();
      }
      this.setState({
        elapseTime: time,
      });
    }, 1000);
    appStateListener = AppState.addEventListener('change', this._updateReviewTimer);
    await setDataToAsyncStorage(RAILS_BOOKING_TIMER_START_TIME, startTime);
  }

  _updateReviewTimer = (newAppState) => {
    if ((this.state.appState === 'inactive' || this.state.appState === 'background') && newAppState === 'active') {
      const newElapsedTime = IRCTC_BOOKING_TIME_LIMIT - (Math.floor(((new Date().getTime()) / 1000) - startTime));
      this.setState({
        appState: newAppState,
        elapseTime: newElapsedTime < 0 ? 0 : newElapsedTime,
      });
    } else {
      this.setState({
        appState: newAppState,
      });
    }
  };
  _append0 = number => (number > 9 ? number : `0${number}`);

  _getBeautifiedElapsedTime = () => {
    const hrs = this._append0(Math.floor(this.state.elapseTime / 60));
    const mins = this._append0(Math.floor(this.state.elapseTime % 60));

    return `${hrs}m:${mins}s`;
  };

  componentWillUnmount() {
    if (this.interval) {
      clearInterval(this.interval);
    }
    appStateListener?.remove();
  }

  render() {
    return (
      <View style={{ marginTop: 2 }} testID={this.props?.id}>
        <View style={{backgroundColor: colors.creamWhite, padding: 10, flexDirection: 'row'}}>
          <View style={{flex: 1}}>
            <Text style={[styles.TextStyle, fontStyle('light'), getLineHeight(12)]}>{_label('review_timer_text')}</Text>
          </View>
          <View style={[styles.timerPositionStyle, fontStyle('bold')]}>
            <LinearGradient
              style={{
                  overflow: 'hidden',
                  borderRadius: 100,
                  alignItems: 'center',
                }}
              colors={timerColors}
              start={{
                  x: 0,
                  y: 2,
                }}
              end={{
                  x: 1,
                  y: 1,
                }}
            >


              <View style={styles.timerDisplay} testID={`${this.props?.id}_clock`}>
                <Image style={styles.clockImage} source={clock} />
                <Text style={[styles.timerDisplayText, getLineHeight(16)]}>{this._getBeautifiedElapsedTime()}</Text>

              </View>


            </LinearGradient>
          </View>
        </View>
      </View>
    );
  }
}

ReviewTimer.propTypes = {
  onDone: PropTypes.func.isRequired,
  id: PropTypes.string,
  startTime: PropTypes.number,
};

const styles = StyleSheet.create({

  clockImage: {
    width: 15,
    height: 15,
    alignItems: 'center',

  },

  timerDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    height: 25,
    width: 100,
    borderBottomRightRadius: 100,
    borderTopRightRadius: 100,
  },
  timerDisplayText: {
    color: colors.white,
    fontSize: 16,
    backgroundColor: colors.transparent,
  },

  timerPositionStyle: {
    marginHorizontal: 10,
  },

  TextStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'wrap',
    fontSize: 12,
    lineHeight: 20,
    color: colors.defaultTextColor,
  },
});

