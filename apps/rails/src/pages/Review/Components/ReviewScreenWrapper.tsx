import React, { useEffect } from 'react';
import { useScreenProfiler } from '@mmt/core/native/PerfLogger/screenProfiler';
import { SCREENER_INTERACTIVE } from '@mmt/rails/src/Utils/RailsConstant';
import PropTypes from 'prop-types';

function ReviewScreenWrapper({ children }) {
  const { updateState } = useScreenProfiler();

  useEffect(() => {
    updateState(SCREENER_INTERACTIVE);
  }, [updateState]);

  return <>{children}</>;
}

ReviewScreenWrapper.propTypes = {
  children: PropTypes.node,
};

export default ReviewScreenWrapper;
