import {connect} from 'react-redux';
import _compact from 'lodash/compact';
import RailsReviewPage from './RailsReviewPage';
import {
  actionRefreshRailofyStatusChange,
  loadReviewPage,
  unloadReviewPage,
  checkForMultipleReviewClickCount,
  setBnppShownOnReviewPage,
  logTravellerGenericClickEvents,
  logReviewGenericClickEvents,
} from './RailsReviewActions';
import {
  onHardBackPress,
  reviewAvailablityChange,
} from '../TravelerDetails/TravelerDetailsActions';
import {isAdult} from '../TravelerDetails/Components/Traveler';
import {openPayment} from '../../Utils/paymentUtils';
import isEmpty from 'lodash/isEmpty';
import forEach from 'lodash/forEach';
import { getSeatLockReviewVariants, getIfCanShowBNPP } from 'apps/rails/src/RailsAbConfig';
import { logReviewEvent } from '../../PdtAnalytics/PdtAnalyticsV2/PdtRailsReview/index';

const getLabels = texts => ({
  timerText: texts.review_timer_text,
  boardingStation: texts.review_boarding_station,
  payAndBook: texts.review_pay_and_book,
  updatedNow: texts.retry_updated_now,
  availingSrConcession:texts.trv_availing_sr_citizen_concession,
  noBerthPreference:texts.trv_no_berth_preferences,
  priceBreakup:texts.review_price_breakup,
  baseFare:texts.review_base_fare,
  totalPrice:texts.review_total_price,
});

const mapStateToProps = (state, ownProps) => {
  const {railsListing} = state;
  if (!railsListing || isEmpty(railsListing.selectedTrainInfo)) {
    // RNW return to prev screen on direct refrech of review //
    return {
      reviewErrorWeb: true,
      loadingReview: false,
      bookingReviewSuccess: false,
    };
  }
  const {
    railsTraveler: {
      totalCollectibleAmount,
      selectedTravelers,
      travelers,
      selectedPickupStation,
      classValue,
      selectedQuota,
      boardingStationList,
      originStation,
      destinationStation,
      selectedBookingDate,
      isBnppOpted,
      bnppInteracted,
      railofy: { bnppResponse: bnppResponseTravelerPage },
    },
    railsListing: {
      selectedTrainInfo,
      hotelCrossSellBannerText,
      cdnTncUrl,
      hotelsDeepLinkMobileApp,
      childBerthMandatory,
      showBnpp,
    },
    railsReview: {
      bookingReviewRequest,
      bookingReviewResponse,
      loadingReview,
      reviewTimeStamp,
      bookingReviewSuccess,
      errorMessage,
      header,
      currentAvailabilityStatus,
      durationFromBoardingStation,
      dueAmount,
      dueDate,
      showPayNowLoader,
      displayIrctcPassBottomsheet = 0,
      isIrctcProfileComplete = true,
      reviewVerificationFlow,
    },
    railsVernacular: {
      texts,
    },
    railsUserVerification: { irctcUserName = '' },
  } = state;

  let paxCount = {
    adult: 0,
    child: 0,
  };
  const travelerIdMap = {};
  travelers.forEach((trv) => {
    travelerIdMap[trv.travelerId] = trv;
  });
  const selectedTravelersObj = _compact(selectedTravelers.map(trvId => travelerIdMap[trvId]));
  forEach(selectedTravelersObj, (trv) => {
    if (isAdult(trv)) {
      paxCount = {...paxCount, adult: paxCount.adult + 1};
    } else {
      paxCount = {...paxCount, child: paxCount.child + 1};
    }
  });

  const bnppReviewVariantPokus = getSeatLockReviewVariants();
  const bnppShowPokus = getIfCanShowBNPP();
  const canShowReviewBnpp =
    showBnpp && bnppShowPokus && bnppReviewVariantPokus > 1 && !isEmpty(bnppResponseTravelerPage);

  return {
    loadingReview,
    header,
    selectedTrainInfo,
    originStation,
    destinationStation,
    totalCollectibleAmount,
    classValue,
    selectedQuota,
    paxCount,
    bookingReviewRequest,
    bookingReviewResponse,
    selectedTravelers: selectedTravelersObj,
    reviewTimeStamp,
    selectedAvlStatus: currentAvailabilityStatus.replace('/', ' | '),
    selectedBookingDate,
    bookingReviewSuccess,
    errorMessage,
    selectedPickupStation,
    boardingStationList,
    hotelCrossSellBannerText,
    cdnTncUrl,
    hotelsDeepLinkMobileApp,
    childBerthMandatory,
    durationFromBoardingStation,
    dueAmount,
    dueDate,
    reviewErrorWeb: false,
    labels: getLabels(texts),
    railsListing,
    canShowReviewBnpp,
    isBnppOpted,
    bnppReviewVariant: bnppReviewVariantPokus,
    showPayNowLoader,
    bnppInteracted,
    irctcUserName,
    displayIrctcPassBottomsheet,
    isIrctcProfileComplete,
    reviewVerificationFlow,
    ...ownProps,
  };
};

const mapDispatchToProps = (dispatch) => ({
  openPayment: () => dispatch(openPayment()),
  loadReviewPage: () => {
    dispatch(loadReviewPage);
  },
  unloadReviewPage: (hasStatusChangedFCtoTG, hasStatusChangedTGtoFC ) =>
    dispatch(unloadReviewPage(hasStatusChangedFCtoTG, hasStatusChangedTGtoFC )),
  onHardBackPress: () => dispatch(onHardBackPress()),
  reviewAvailablityChange: () => {
    dispatch(reviewAvailablityChange());
  },
  checkForMultipleReviewClickCount: (newCheckoutApi, isScheduleTatkalBooking) => 
    dispatch(checkForMultipleReviewClickCount(newCheckoutApi, isScheduleTatkalBooking)),
  actionRefreshRailofyStatusChange : () => dispatch(actionRefreshRailofyStatusChange),
  setBnppShownOnReviewPage: (showBnpp) => dispatch(setBnppShownOnReviewPage(showBnpp)),
  logReviewEvent: (eventvalue, searchContext, reviewData) =>
    dispatch(logReviewEvent(eventvalue, searchContext, reviewData)),
  logTravellerGenericClickEvents: (eventValue) =>
    dispatch(logTravellerGenericClickEvents(eventValue)),
  logReviewGenericClickEvents: (eventValue) => dispatch(logReviewGenericClickEvents(eventValue)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RailsReviewPage);
