import * as actions from './RailsReviewActions';

const initialState = {
  loadingReview: false,
  bookingReviewSuccess: false,
  header: '',
  reviewTimeStamp: -1,
  currentAvailabilityStatus: '',
  durationFromBoardingStation: '',
  railofyAndFreeCancellationData : {
    confirmation_gurantee_available : null,
    zero_cancellation_available : null,
    confirmation_gurantee_opted : null,
    zero_cancellation_opted : null,
    confirmation_premium_variant : null,
    confirmation_gurantee_premium_amount : null,
    zero_cancellation_premium_amount : null,
    confirmation_probablity : null,
  },
  routedThroughReview : false,
  dueAmount: null,
  dueDate: null,
  refreshRailofyStatusChange : false,
  bnppResponse: null,
  showPayNowLoader: false,
  bnppShownOnReview: false,     // boolean value has no significance , an inversion in this variable at anytime will trigger API call
  displayIrctcPassBottomsheet: 0,
  isIrctcProfileComplete: true,
  setAlertApiSuccess: false,
  reviewVerificationFlow: null,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case actions.ACTION_UNLOAD_BOOKING_REVIEW: {
      return {
        ...initialState,
        railofyAndFreeCancellationData : state.railofyAndFreeCancellationData,
        routedThroughReview : state.routedThroughReview,
        refreshRailofyStatusChange : state.refreshRailofyStatusChange,
      };
    }
    case actions.ACTION_BOOKING_REVIEW_LOAD: {
      const {bookingReviewRequest} = action.data;
      return {
        ...state,
        loadingReview: true,
        bookingReviewRequest,
        bookingReviewSuccess: false,
      };
    }
    case actions.ACTION_BOOKING_REVIEW_SUCCESS: {
      const {
        bookingReviewSuccess,
        errorMessage,
        bookingReviewResponse,
        currentAvailabilityStatus,
        baseFare,
        gstPrice,
        mmtServiceCharge,
        totalCollectibleAmount,
        paymentInfo,
        insuranceCharge,
        reviewTimeStamp,
        durationFromBoardingStation,
        dueAmount,
        dueDate,
        displayIrctcPassBottomsheet,
        isIrctcProfileComplete,
      } = action.data;
      return {
        ...state,
        loadingReview: false,
        reviewTimeStamp,
        bookingReviewSuccess,
        errorMessage,
        bookingReviewResponse,
        currentAvailabilityStatus,
        baseFare,
        gstPrice,
        mmtServiceCharge,
        totalCollectibleAmount,
        paymentInfo,
        insuranceCharge,
        durationFromBoardingStation,
        dueAmount,
        dueDate,
        displayIrctcPassBottomsheet,
        isIrctcProfileComplete,
      };
    }
    case actions.ACTION_BOOKING_REVIEW_FAILURE: {
      const {bookingReviewSuccess, errorMessage} = action.data;
      return {
        ...state,
        bookingReviewSuccess,
        errorMessage,
        loadingReview: false,
      };
    }
    case actions.ACTION_SET_REVIEW_HEADER: {
      const {header} = action.data;
      return {
        ...state,
        header,
      };
    }
    case actions.ACTION_SET_RAILOFY_FREECANCELLATION_DATA: {
      const {railofyAndFreeCancellationData} = action.data;
      return {
        ...state,
        railofyAndFreeCancellationData,
      };
    }
    case actions.ACTION_SET_ROUTED_THROUGH_REVIEW: {
      return {
        ...state,
        routedThroughReview: true,
      };
    }
    case actions.ACTION_REVIEW_VERIFICATION_FLOW: {
      return {
        ...state,
        reviewVerificationFlow: action.data,
      };
    }
    case actions.ACTION_CLEAR_ROUTED_THROUGH_REVIEW : {
      return {
        ...state,
        routedThroughReview: false,
      };
    }
    case actions.ACTION_CHANGE_REFRESH_RAILOFY_STATUS_CHANGE : {
      return {
        ...state,
        refreshRailofyStatusChange : action.data,
      };
    }
    case actions.ACTION_BNPP_RESPONSE_UPDATE: {
      return {
        ...state,
        bnppResponse: action.data,
      };
    }
    case actions.ACTION_SHOWN_REVIEW_BNPP: {
      return {
        ...state,
        bnppShownOnReview: action.data,
      };
    }

    case actions.ACTION_SHOW_PAYNOW_LOADER: {
      return {
        ...state,
        showPayNowLoader: true,
      };
    }

    case actions.ACTION_HIDE_PAYNOW_LOADER: {
      return {
        ...state,
        showPayNowLoader: false,
      };
    }
    case actions.ACTION_SET_ALERT_API_SUCCESS: {
      return {
        ...state,
        ...action.data,
      };
    }

    default:
      return state;
  }
};
