import {StyleSheet} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {fontStyle} from '../../../vernacular/VernacularUtils';

export const bookingSummaryTextStyle = {
    getSeatAvailableStyleFontStyle : () => {
        return fontStyle('bold');
    },
    getSeatNotAvailableStyleFontStyle : () => {
        return fontStyle('bold');
    },
};
const BookingSummaryStyle = StyleSheet.create({

  TimerStyle: {
    flex: 1,
    margin: 20,
    backgroundColor: colors.lightPink,
    paddingTop: 30,
  },
  BookingSummaryStyle: {
    flex: 1,
    margin: 20,
    backgroundColor: colors.white,
    paddingBottom: 50,
  },
  PriceBreakUpStyle: {
    flex: 1,
    margin: 20,
    backgroundColor: colors.white,
    paddingBottom: 50,
  },

  SeatAvailableStyle: {
    position: 'absolute',
    right: 0,
    marginRight: 20,
    color: colors.cyan,
    fontSize: 12,
  },

  SeatNotAvailableStyle: {
    position: 'absolute',
    right: 0,
    marginRight: 20,
    color: colors.yello,
    fontSize: 12,
  },
  circle: {
    width: 7,
    height: 7,
    borderRadius: 100 / 2,
    backgroundColor: colors.lightGrey,
  },
  JourneyStartEndStyle: {

    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',


  },
});

export default BookingSummaryStyle;
