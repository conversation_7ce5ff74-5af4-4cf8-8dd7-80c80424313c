import { getDataFromAsynStorage, IRCTC_PASSWORD, removeDataFromAsyncStorage, setDataToAsyncStorage } from '../../Utils/RailsConstant';
import RailsModule from '@mmt/legacy-commons/Native/RailsModule';
import { DeviceEventEmitter } from 'react-native';
import { showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { _label } from '../../vernacular/AppLanguage';

let passwordReader = null;
const { initializePasswordReader } = RailsModule;

const listenToNativeEventForPassword = (callback) => {
  return DeviceEventEmitter.addListener(IRCTC_PASSWORD, (irctcPassword) => {
    if (callback) {
      callback(irctcPassword);
    } else {
      setDataToAsyncStorage(IRCTC_PASSWORD, irctcPassword)
        .then(() => {
          showLongToast(_label('password_read_success'));
        })
        .catch((error) => {
          console.error('Failed to save IRCTC password:', error);
          showLongToast(_label('password_read_failure') || 'Failed to save password');
        });
    }
  });
};
/* eslint-disable */
class IrctcPasswordReader {
  constructor() {
    this.eventListener = null;
    if (!passwordReader) {
      passwordReader = this;
    }
    return passwordReader;
  }

  subscribeIrctcSms(callback) {
    try {
      this.cleanUp();
      if (initializePasswordReader) {
        initializePasswordReader();
      }
      if (this.eventListener === null) {
        this.eventListener = listenToNativeEventForPassword(callback);
      }
    } catch (error) {
      console.error('Could not subscribe.', error.message);
    }
  }

  async getPassword() {
    const password = await getDataFromAsynStorage(IRCTC_PASSWORD);
    if (password) {
      return password;
    }
    return '';
  }

  cleanUp() {
    if (this.eventListener) {
      this.eventListener.remove();
      this.eventListener = null;
    }
    removeDataFromAsyncStorage(IRCTC_PASSWORD, null);
  }
}

export default IrctcPasswordReader;
