
import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Platform,
  Image,
} from 'react-native';
import { TravelPlexBot } from '@travelplex/react-native';
import ASSETS from '../../Utils/Assets/RailsAssets';
import PropTypes from 'prop-types';
import { Actions } from '../../navigation';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  trackTravellerPageEvent,
  trackEnterUserNameEvent,
  removeEventFromEvar47or97Variable,
  trackGenericEvar99Event,
  updateEvar47or97Variable,
  removeEventFromEvar99Variable,
} from '../../railsAnalytics';
import RAIL_EVENTS from '../../RailsOmnitureTracker';
import UserNameModal from '../NewUserFlow/UserNameModal';
import { COMPONENT_IRCTC_USERNAME } from '../TravelerDetails/TravelerDetailsActions';
import RailRetrievePasswordContainer from '../Common/RailRetrievePasswordContainer';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle, getLineHeight} from '../../vernacular/VernacularUtils';
import IrctcPasswordReader from './irctcPasswordReader';
import {
  getRailsIrctcAccountToast,
  showProfileIncompleteFlowPokus,
  showNewIrctcBottomSheet,
  showNewIrctcText,
  showSavedIds,
  showIrctcIngressFlow,
} from '../../RailsAbConfig';
import {
  INGRESS_FLOW,
  irctcSavedIds,
} from '../../RailsAbConstants';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import { isIos } from '../../Utils/device';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
  updateAccountUserNameAPI,
  fetchIrctcAccountUsernames,
  IRCTC_SAVED_IDS_LOAD_CLICKED_EVAR99,
  IRCTC_USERNAME_CREATE_CLICKED_EVAR99,
} from '../../../src/Utils/RailsConstant';
import IrctcEducationBottomSheet from '../TravelerDetails/Components/IrctcEducationBottomSheet/IrctcEducationBottomsheet';
import { TRAVELLER_CLICK_EVENTS } from '@mmt/rails/src/PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';
import { IRCTC_FETCH_PASSWORD_INFO } from 'apps/rails/src/Utils/RailsConstant';
import { configKeys } from '../../configStore/Common/constants';
import { getConfigStore } from '../../configStore/Common/RailsConfigStoreUtils';
import isEmpty from 'lodash/isEmpty';
import IrctcProfileIncompleteComponent from './IrctcProfileIncomplete/IrctcProfileIncompleteComponent';
import IrctcCardLoader from './IrctcCardLoader';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import BottomSheetModalTrain from '../Common/BottomSheetModalTrain';

export class IrctcAccountDetailsCard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible: false,
      showPasswordReminderModal: false,
      showInfoBottomSheet: false,
      showNewIrctcTextValue: 0,
      showIrctcBottomSheetValue: 0,
      irctcProfileIncompleteFlowConfig: {},
      irctcUserNames: [],
      selectedUsername: null,
      irctcUserNameNew: props.irctcUserName,
      showChatBot: false,
      irctcWidgetConfig: {
        displayText: _label('travel_plex_ingress_display_text'),
        promptText: _label('travel_plex_ingress_prompt_text'),
      },
    };
    this.irctcPasswordReader = new IrctcPasswordReader();
    this.chatBotRef = React.createRef();
  }

  fetchIrctcUserName = async () => {
    try {
      const irctcUserName = await fetchIrctcAccountUsernames();
      this.setState({ irctcUserNames: irctcUserName });
    } catch (error) {
      console.error('Error fetching IRCTC User Name:', error);
    }
  };

  fetchIrctcWidgetConfig = async () => {
    try {
      const config = await getConfigStore(configKeys.RAILS_IRCTC_WIDGET_INGRESS_CONFIG);
      if (config && config.displayText && config.promptText) {
        this.setState({
          irctcWidgetConfig: {
            displayText: config.displayText,
            promptText: config.promptText,
          },
        });
      }
    } catch (error) {
      console.error('Error fetching IRCTC Widget Config:', error);
    }
  };

  componentDidUpdate(prevProps) {
    if (
      prevProps.irctcUserName !== this.props.irctcUserName &&
      this.state.irctcUserNameNew !== this.props.irctcUserName
    ) {
      this.setState({
        irctcUserNameNew: this.props.irctcUserName,
      });
    }
  }

  handleIngressFlowClick = () => {
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.TRAVELLER.RAILS_IRCTC_INGRESS_FLOW_CLICKED,
    );
    trackGenericEvar99Event(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.TRAVELLER.RAILS_IRCTC_INGRESS_CLICKED,
    );
    this.props?.logTravellerGenericClickEvents?.(
      TRAVELLER_CLICK_EVENTS.TRAVELLER_IRCTC_CARD_INGRESS_FLOW,
    );
    this.setState({ showChatBot: true }, () => {
      setTimeout(() => {
        this.chatBotRef?.current?.expand({
          chatConfig: this.buildChatConfig(),
          initialUserPrompt: this.state.irctcWidgetConfig.promptText,
        });
      }, 100);
    });
  };

  async componentDidMount() {
    await this.fetchIrctcUserName();
    await this.fetchIrctcWidgetConfig();
    const showIrctcSavedIds = showSavedIds();
    const userLoggedIn = await isUserLoggedIn();
    this.setState({ userLoggedIn });
    const showNewIrctcTextValue = showNewIrctcText();
    const showIrctcBottomSheetValue = showNewIrctcBottomSheet();
    const irctcProfileIncompleteFlowConfig =
    await getConfigStore(configKeys.RAILS_IRCTC_PROFILE_INCOMPLETE_FLOW_CONFIG);
    this.setState({ showNewIrctcTextValue, showIrctcBottomSheetValue, irctcProfileIncompleteFlowConfig });
    const { irctcUserNames } = this.state;
    this.setState({ irctcUserNames: irctcUserNames?.slice(0, 5) });
    removeEventFromEvar47or97Variable(
      RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_VARIANT1_SHOWN,
    );
    removeEventFromEvar47or97Variable(
      RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_VARIANT2_SHOWN,
    );
    removeEventFromEvar47or97Variable(
      RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_NOTSHOWN,
    );
    removeEventFromEvar47or97Variable(
      RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_TEXT_SHOWN,
    );
    removeEventFromEvar47or97Variable(
      RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_TEXT_NOTSHOWN,
    );
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_SAVED_IDS_NOT_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_SAVED_IDS_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_IRCTC_INGRESS_FLOW_NOT_SHOWN);
    removeEventFromEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_IRCTC_INGRESS_FLOW_SHOWN);
    removeEventFromEvar99Variable(RAIL_EVENTS.TRAVELLER.RAILS_IRCTC_INGRESS_CLICKED);

    if (showIrctcSavedIds === 1 && userLoggedIn) {
      updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_SAVED_IDS_SHOWN);
      if (irctcUserNames.length > 1) {
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLER_SAVEDID_LOADED);
      }
    } else {
      updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_SAVED_IDS_NOT_SHOWN);
    }

    const ingressFlowValue = showIrctcIngressFlow();
    if (ingressFlowValue === INGRESS_FLOW.SHOWN) {
      updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_IRCTC_INGRESS_FLOW_SHOWN);
    } else {
      updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_IRCTC_INGRESS_FLOW_NOT_SHOWN);
    }

    const eventMapping = {
      0: RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_NOTSHOWN,
      1: RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_VARIANT1_SHOWN,
      2: RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_VARIANT2_SHOWN,
    };

    if (showIrctcBottomSheetValue in eventMapping) {
      updateEvar47or97Variable(eventMapping[showIrctcBottomSheetValue]);
    }

    if (showNewIrctcTextValue === 1) {
      updateEvar47or97Variable(RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_TEXT_SHOWN);
    } else {
      updateEvar47or97Variable(
        RAIL_EVENTS.TRAVELLER.RAILS_TRAVELLER_IRCTC_BOTTOMSHEET_TEXT_NOTSHOWN,
      );
    }
  }

  _onChangeClicked = () => {
    trackClickEventProp61(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      RAIL_EVENTS.USERFLOW.mob_rail_travellers_change_IRCTC_username,
    );
    this._setModalVisibility(true);
  };

  _retrievePassword = () => {
    trackTravellerPageEvent(RAIL_EVENTS.TRAVELLER.MOB_RAIL_TRAVELLERS_REQUEST_PASSWORD);
  };

  _setModalVisibility = (visibility) => {
    this.setState({ modalVisible: visibility });
  };

  onForgotUsernamePress = () => {
    trackEnterUserNameEvent(
      RAIL_EVENTS.USERFLOW.mob_rail_travellers_irctc_userid_popup_RN_get_username,
    );
    Actions.railsForgotUsername({
      from: 'travelers',
    });
  };

  onCreateNewPress = () => {
    trackEnterUserNameEvent(
      RAIL_EVENTS.USERFLOW.mob_rail_travellers_irctc_userid_popup_RN_create_account,
    );
    trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_USERNAME_CREATE_CLICKED_EVAR99);

    const showIrctcAccountCreatingToast = getRailsIrctcAccountToast();

    if (showIrctcAccountCreatingToast) {
      return Actions.openCreateIRCTCAccountWebView({
        from: 'travelers',
      });
    }

    Actions.railsCreateAccountPage({
      from: 'travelers',
    });
  };

  _showInfoBottomSheet = () => {
    this.setState({ showInfoBottomSheet: true });
  };

  _hideInfoBottomSheet = () => {
    this.setState({ showInfoBottomSheet: false });
  };

  _handleUsernameSelect = async (username) => {
    const { selectedUsername } = this.state;
    if (selectedUsername === username) {
      return;
    }
    const newSelectedUsername = username;
    this.props.setUserNameToRedux(newSelectedUsername);
    updateAccountUserNameAPI(newSelectedUsername);
    trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLER_SAVED_ID);
    trackGenericEvar99Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, IRCTC_SAVED_IDS_LOAD_CLICKED_EVAR99);
  };

  _handleUpdatedUsenames = (newUsernames) => {
    const recentUsernames = newUsernames ? newUsernames.slice(0, 5) : [];
    this.setState({ irctcUserNames: recentUsernames });
  };

  onViewStateChange = (viewState) => {
    if (viewState === 'collapsed') {
      this.setState({ showChatBot: false });
    }
  };

  buildChatConfig = () => {
    const { travelplexChatConfig, irctcUserName } = this.props;

    const baseConfig = travelplexChatConfig || {
      context: {
        lob: 'RAILS',
        lobCategory: null,
        view: 'TRAVELLERS',
        project: 'ris_chatbot',
        prevPage: null,
        platform: Platform.OS,
      },
      contextMetadata: {
        pageContext: {
          lob: 'rails',
          lobCategory: 'rails',
          pageName: 'travellers',
          prevPageName: 'listing',
        },
        searchContext: {},
      },
      expertMetadata: {
        source: 'irctc_help',
      },
    };
    return {
      ...baseConfig,
      expertMetadata: {
        ...baseConfig.expertMetadata,
        irctcUsername: irctcUserName || '',
      },
    };
  };

  render() {
    const {
      irctcUserName,
      showIRCTCUsernameError,
      showProfileIncompleteFlow = false,
      irctcProfileCompletedFromWebView = false,
      showIrctcComponentLoader = false,
      setShowIrctcProfileCompletionFlow = () => {//
      },
    } = this.props;
    const { showNewIrctcTextValue, showIrctcBottomSheetValue, irctcUserNames, irctcUserNameNew, userLoggedIn}
    = this.state;
    const textBoxBorder = showIRCTCUsernameError ? stylesConst.errorBox : {};
    const shouldShowWhyRequired =
      showNewIrctcTextValue === 1;
    const shouldShowIngressFlow = showIrctcIngressFlow() === INGRESS_FLOW.SHOWN;
    const chatConfig = this.buildChatConfig();
    const showIrctcSavedIds = showSavedIds();
    const showIrctcSavedValues = irctcSavedIds;
    const primaryUserNames = this.state.irctcUserNames || [];
    if (showIrctcComponentLoader) {
      return (
        <IrctcCardLoader />
      );
    }
    return (
      <View
        style={{ backgroundColor: colors.white }}
        ref={(ref) => {
          this.props.captureRef(COMPONENT_IRCTC_USERNAME, ref);
        }}
        testID={this.props?.id}
      >
        <View style={stylesConst.container} testID="irctc_account_details_card_container">
          <View style={{ paddingTop: 14 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={[stylesConst.title, textStyle.getTitleFontStyle(), getLineHeight(22)]}>
                {_label('irctc_username')}
              </Text>
              {shouldShowWhyRequired ? (
                <TouchableOpacity
                  onPress={() => {
                    trackClickEventProp61(
                      TRAVELERS_PAGE_TRACKING_KEY_NEW,
                      RAIL_EVENTS.USERFLOW.MOB_RAIL_TRAVELLERS_IRCTC_WHY_REQUIRED,
                    );
                    this._showInfoBottomSheet();
                  }}
                  testID="irctc_account_details_card_why_required_button"
                >
                  <Text
                    style={[
                      getLineHeight(14.4),
                      {
                        color: colors.azure,
                        fontSize: 12,
                        marginLeft: 12,
                        marginTop: 4,
                        fontWeight: '700',
                      },
                    ]}
                  >
                    {_label('Why is this required?')}
                  </Text>
                </TouchableOpacity>
              ) : null}
            </View>
          </View>

          <View style={{ paddingBottom: 20 }}>
            <TouchableOpacity
              onPress={() => this._onChangeClicked()}
              testID="irctc_account_details_card_change_button"
            >
              <React.Fragment>
                <View
                  style={[stylesConst.irctcUserNameContainer, textBoxBorder]}
                  testID={`${this.props?.id}_username`}
                >
                  <Text
                    style={[
                      stylesConst.irctcUserNameContainerText,
                      textStyle.getIrctcUserNameContainerTextFontStyle(),
                      getLineHeight(12),
                    ]}
                  >
                    {_label('username', { uppercase: true })}
                  </Text>
                  {!irctcUserName && (
                    <Text
                      style={[
                        stylesConst.enterText,
                        textStyle.getEnterTextFontStyle(),
                        getLineHeight(16),
                      ]}
                    >
                      {_label('enter_irctc_username')}
                    </Text>
                  )}
                  {!!irctcUserName && (
                    <View style={stylesConst.changeContainer}>
                      <Text
                        style={[
                          stylesConst.enterText,
                          textStyle.getEnterTextFontStyle('en'),
                          { color: colors.black },
                          getLineHeight(16),
                        ]}
                      >
                        {irctcUserName}
                      </Text>
                      <Text
                        style={[
                          stylesv2.buttonText,
                          textStyle.getButtonTextFontStyle(),
                          { marginRight: 15 },
                          getLineHeight(12),
                        ]}
                      >
                        {_label('change', { uppercase: true })}
                      </Text>
                    </View>
                  )}
                </View>
              </React.Fragment>
            </TouchableOpacity>
            {!!showIRCTCUsernameError && (
              <Text
                style={[
                  stylesConst.errorText,
                  textStyle.getErrorTextFontStyle(),
                  getLineHeight(12),
                ]}
              >
                {_label('please_fill_irctc_username')}
              </Text>
            )}
            {showIrctcBottomSheetValue === 0 && !!irctcUserName && (
              <>
                <View>
                  {irctcUserNames &&
                    irctcUserNames.length > 0 &&
                    showIrctcSavedIds === showIrctcSavedValues.SHOWN &&
                    userLoggedIn && (
                      <View style={stylesSavedIds.savedIdsContainer}>
                        <Text style={stylesSavedIds.savedIdsText}>{_label('saved_ids')}</Text>
                        <View style={stylesSavedIds.savedIdsList}>
                          {primaryUserNames?.map((user) => (
                            <TouchableOpacity
                              key={user.id}
                              style={[
                                stylesSavedIds.savedIdButton,
                                {
                                  borderColor:
                                    irctcUserNameNew === user.username
                                      ? colors.primary
                                      : colors.lightGray,
                                  backgroundColor:
                                    irctcUserNameNew === user.username
                                      ? colors.lighterBlue
                                      : colors.white,
                                },
                              ]}
                              onPress={() => {
                                setShowIrctcProfileCompletionFlow(false);
                                this._handleUsernameSelect(user.username);
                              }}
                            >
                              <Text
                                style={[
                                  stylesSavedIds.savedIdText,
                                  {
                                    color:
                                      irctcUserNameNew === user.username
                                        ? colors.primary
                                        : colors.black,
                                    fontWeight: irctcUserNameNew === user.username ? '900' : '400',
                                  },
                                ]}
                              >
                                {user.username}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      </View>
                    )}
                </View>
                <IrctcProfileIncompleteComponent
                  showProfileIncompleteFlow={showProfileIncompleteFlow}
                  username={irctcUserName}
                  irctcProfileCompletedFromWebView={irctcProfileCompletedFromWebView}
                  config={
                    this.props?.aadharConfigKeyResponse ||
                    this.state?.irctcProfileIncompleteFlowConfig
                  }
                  verificationFlow={this.props?.verificationFlow}
                />
                {!(
                  showProfileIncompleteFlowPokus() &&
                  showProfileIncompleteFlow &&
                  !isEmpty(this.state?.irctcProfileIncompleteFlowConfig)
                ) && (
                  <View>
                    <Text
                      style={[
                        stylesConst.newPasswordText,
                        textStyle.getNewPasswordTextFontStyle(),
                        getLineHeight(14),
                      ]}
                    >
                      {_label('irctc_password')}
                    </Text>
                    <Text
                      style={[
                        stylesConst.infoText,
                        textStyle.getInfoTextFontStyle(),
                        getLineHeight(12),
                      ]}
                    >
                      {IRCTC_FETCH_PASSWORD_INFO}
                    </Text>
                    <TouchableOpacity
                      style={stylesv2.button}
                      onPress={() => {
                        trackClickEventProp61(
                          TRAVELERS_PAGE_TRACKING_KEY_NEW,
                          RAIL_EVENTS.USERFLOW.mob_rail_travellers_get_new_irctc_password,
                        );
                        this.props?.logTravellerGenericClickEvents?.(
                          TRAVELLER_CLICK_EVENTS.GET_NEW_IRCTC_PASSWORD,
                        );
                        this.irctcPasswordReader?.subscribeIrctcSms?.();
                        this.setState({ showPasswordReminderModal: true });
                      }}
                      testID={`${this.props?.id}_newPasswordButton`}
                    >
                      <Text
                        style={[
                          stylesv2.buttonText,
                          textStyle.getButtonTextFontStyle(),
                          getLineHeight(12),
                        ]}
                        testID={`${this.props?.id}_newPasswordButton`}
                      >
                        {_label('get_new_password', { capitalize: true })}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </>
            )}
            {(showIrctcBottomSheetValue === 1 || showIrctcBottomSheetValue === 2) &&
              !!irctcUserName && (
                <>
                  <View>
                    {irctcUserNames &&
                      irctcUserNames.length > 0 &&
                      showIrctcSavedIds === showIrctcSavedValues.SHOWN &&
                      userLoggedIn && (
                        <View style={stylesSavedIds.savedIdsContainer}>
                          <Text style={stylesSavedIds.savedIdsText}>{_label('saved_ids')}</Text>
                          <View style={stylesSavedIds.savedIdsList}>
                            {primaryUserNames?.map((user) => (
                              <TouchableOpacity
                                key={user.id}
                                style={[
                                  stylesSavedIds.savedIdButton,
                                  {
                                    borderColor:
                                      irctcUserNameNew === user.username
                                        ? colors.primary
                                        : colors.lightGray,
                                    backgroundColor:
                                      irctcUserNameNew === user.username
                                        ? colors.lighterBlue
                                        : colors.white,
                                  },
                                ]}
                                onPress={() => {
                                  setShowIrctcProfileCompletionFlow(false);
                                  this._handleUsernameSelect(user.username);
                                }}
                              >
                                <Text
                                  style={[
                                    stylesSavedIds.savedIdText,
                                    {
                                      color:
                                        irctcUserNameNew === user.username
                                          ? colors.primary
                                          : colors.black,
                                      fontWeight:
                                        irctcUserNameNew === user.username ? '900' : '400',
                                    },
                                  ]}
                                >
                                  {user.username}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </View>
                        </View>
                      )}
                  </View>

                  <IrctcProfileIncompleteComponent
                    showProfileIncompleteFlow={showProfileIncompleteFlow}
                    username={irctcUserName}
                    irctcProfileCompletedFromWebView={irctcProfileCompletedFromWebView}
                    config={
                      this.props?.aadharConfigKeyResponse ||
                      this.state?.irctcProfileIncompleteFlowConfig
                    }
                    verificationFlow={this.props?.verificationFlow}
                  />
                  {!(
                    showProfileIncompleteFlowPokus() &&
                    showProfileIncompleteFlow &&
                    !isEmpty(this.state?.irctcProfileIncompleteFlowConfig)
                  ) && (
                    <View>
                      <Text
                        style={[stylesConst.newLabelText1, getLineHeight(16.8), { fontSize: 14 }]}
                      >
                        {_label('irctc_post_validation_info')}
                      </Text>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text
                          style={[stylesv2.buttonText1, getLineHeight(14.4), { marginRight: 8 }]}
                        >
                          {_label('forgot_password_traveller_page')}
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            trackClickEventProp61(
                              TRAVELERS_PAGE_TRACKING_KEY_NEW,
                              RAIL_EVENTS.USERFLOW.mob_rail_travellers_get_new_irctc_password,
                            );
                            this.props?.logTravellerGenericClickEvents?.(
                              TRAVELLER_CLICK_EVENTS.GET_NEW_IRCTC_PASSWORD,
                            );
                            this.irctcPasswordReader.subscribeIrctcSms();
                            this.setState({ showPasswordReminderModal: true });
                          }}
                          testID={`${this.props?.id}_newPasswordButton_1`}
                        >
                          <Text
                            style={[
                              stylesv2.buttonText1,
                              { color: colors.azure },
                              getLineHeight(14.4),
                            ]}
                            testID={`${this.props?.id}_newPasswordButton_1`}
                          >
                            {_label('get_a_new_password_now')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                      {shouldShowIngressFlow && (
                        <View>
                          <TouchableOpacity
                            style={stylesConst.myraHelpContainer}
                            onPress={this.handleIngressFlowClick}
                            testID={`${this.props?.id}_myraHelpButton`}
                          >
                            <View style={stylesConst.myraHelpInnerContainer}>
                              <Image source={ASSETS.travelPlexIcon} style={stylesConst.botIcon} />
                              <Text style={stylesConst.myraHelpText}>
                                {this.state.irctcWidgetConfig.displayText}
                              </Text>
                              <Image
                                source={ASSETS.blueArrow}
                                style={stylesConst.arrowText}
                                resizeMode="contain"
                              />
                            </View>
                          </TouchableOpacity>
                        </View>
                      )}
                      {shouldShowIngressFlow && this.state.showChatBot && (
                        <TravelPlexBot
                          ref={this.chatBotRef}
                          onViewStateChange={this.onViewStateChange}
                          chatConfig={chatConfig}
                        />
                      )}
                    </View>
                  )}
                </>
              )}
            {!!this.state.showPasswordReminderModal && (
              <View>
                <Modal
                  transparent
                  visible={this.state.showPasswordReminderModal}
                  onRequestClose={() => {
                    this.setState({ showPasswordReminderModal: false });
                  }}
                >
                  <TouchableWithoutFeedback
                    onPress={() => {
                      this.setState({ showPasswordReminderModal: false });
                    }}
                    testID={`${this.props?.id}_newPasswordButton_2`}
                  >
                    <View style={{ flex: 1, backgroundColor: colors.lightBlack }} />
                  </TouchableWithoutFeedback>
                  <View style={{ backgroundColor: colors.white, height: 300 }}>
                    <RailRetrievePasswordContainer
                      dismiss={() => {
                        this.setState({ showPasswordReminderModal: false });
                      }}
                      irctcUserName={irctcUserName}
                      id={`${this.props?.id}_onnewPasswordButtonClicked`}
                    />
                  </View>
                  {isIos() && <KeyboardSpacer />}
                </Modal>
              </View>
            )}
            {!irctcUserName && (
              <React.Fragment>
                <TouchableOpacity style={stylesv2.button} onPress={this.onCreateNewPress}>
                  <Text
                    style={[
                      stylesv2.buttonText,
                      textStyle.getButtonTextFontStyle(),
                      getLineHeight(12),
                    ]}
                    testID={`${this.props?.id}_createNewAccountButton`}
                  >
                    {_label('create_new_irctc_account', { uppercase: true })}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity style={stylesv2.button} onPress={this.onForgotUsernamePress}>
                  <Text
                    style={[
                      stylesv2.buttonText,
                      textStyle.getButtonTextFontStyle(),
                      getLineHeight(12),
                    ]}
                    testID={`${this.props?.id}_forgotUsernameButton`}
                  >
                    {_label('forgot_username', { uppercase: true })}
                  </Text>
                </TouchableOpacity>
                {shouldShowIngressFlow && (
                  <View>
                    <TouchableOpacity
                      style={stylesConst.myraHelpContainer}
                      onPress={this.handleIngressFlowClick}
                      testID={`${this.props?.id}_myraHelpButton_noUsername`}
                    >
                      <View style={stylesConst.myraHelpInnerContainer}>
                        <Image source={ASSETS.travelPlexIcon} style={stylesConst.botIcon} />
                        <Text style={stylesConst.myraHelpText}>
                          {this.state.irctcWidgetConfig.displayText}
                        </Text>
                        <Image
                          source={ASSETS.blueArrow}
                          style={stylesConst.arrowText}
                          resizeMode="contain"
                        />
                      </View>
                    </TouchableOpacity>
                  </View>
                )}

                {shouldShowIngressFlow && this.state.showChatBot && (
                  <TravelPlexBot
                    ref={this.chatBotRef}
                    onViewStateChange={this.onViewStateChange}
                    chatConfig={chatConfig}
                  />
                )}
              </React.Fragment>
            )}
            {/* pokus controlled end*/}
          </View>
        </View>
        {!!this.state.modalVisible && (
          <UserNameModal
            userName={irctcUserName}
            modalVisible={this.state.modalVisible}
            setModalVisibility={this._setModalVisibility}
            from="travelers"
            handleUpdatedUsenames={this._handleUpdatedUsenames}
            id={`${this.props?.id}_onUsernameClicked`}
          />
        )}
        {!!this.state.showInfoBottomSheet && (
          <BottomSheetModalTrain
            additionalContainerStyle={{ zIndex: 9999 }}
            testID="irctc_education_bottomsheet_modal"
          >
            <IrctcEducationBottomSheet
              visible={this.state.showInfoBottomSheet}
              onClose={this._hideInfoBottomSheet}
            />
          </BottomSheetModalTrain>
        )}
      </View>
    );
  }
}

const textStyle = {
  getButtonTextFontStyle : () => {
    return fontStyle('bold');
  },
  getIrctcAccountFontStyle : (forceFontCode = undefined) => {
    return fontStyle('bold', forceFontCode);
  },
  getTitleFontStyle : () => {
    return fontStyle('black');
  },
  getTextFontStyle : () => {
    return fontStyle('bold');
  },
  getIrctcMsgTextFontStyle : () => {
    return fontStyle('regular');
  },
  getIrctcUserNameContainerTextFontStyle: () => {
    return fontStyle('bold');
  },
  getEnterTextFontStyle: (forceFontCode) => {
    return fontStyle('black',forceFontCode);
  },
  getErrorTextFontStyle: () => {
    return fontStyle('regular');
  },
  getNewPasswordTextFontStyle : () => {
    return fontStyle('black');
  },
  getInfoTextFontStyle : () => {
    return fontStyle('regular');
  },
};

const stylesv2 = StyleSheet.create({
  button: {
    paddingVertical: 10,
  },
  buttonText: {
    fontSize: 12,
    color: colors.azure,
  },
  buttonText1: {
    marginBottom: 6,
    marginTop: 8,
    marginLeft: 4,
    fontSize: 14,
    color: colors.textGrey,
    fontWeight: '700',
  },
});

const stylesConst = {
  container: {
    flex: 1,
    marginRight: 16,
    marginLeft: 16,
  },
  myraHelpContainer: {
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 100,
    backgroundColor: colors.blue11,
  },
  myraHelpInnerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
    paddingHorizontal: 4,
    justifyContent: 'space-between',
  },
  myraHelpText: {
    flex: 1,
    fontSize: 12,
    color: colors.blue10,
    fontWeight: '700',
    fontFamily: 'Lato',
    marginLeft: 8,
    lineHeight: 14.4,
  },
  arrowText: {
    width: 24,
    height: 24,
    alignSelf: 'center',
  },
  botIcon: {
    width: 30,
    height: 30,
    backgroundColor: colors.blue11,
    marginLeft: 2,
  },
  irctcAccount: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
    color: colors.textGrey,
  },
  changeButton: {
    color: colors.azure,
    lineHeight: 18,
    paddingLeft: 12,
  },
  title: {
    fontSize: 22,
    lineHeight: 26,
    letterSpacing: 0,
    marginTop: 5,
    color: colors.black,
  },
  text: {
    fontSize: 12,
    color: colors.black,
  },
  irctcMsgContainer: {
    backgroundColor: colors.creamWhite,
    marginTop: 10,
    borderRadius: 2,
    paddingVertical: 5,
    paddingHorizontal: 8,
  },
  irctcMsgText: {
    fontSize: 10,
    color: colors.lightYello,
  },
  irctcUserNameContainer: {
    backgroundColor: colors.grey13,
    borderRadius: 5,
    borderWidth:1,
    borderColor: colors.grey12,
    paddingVertical: 8,
    paddingLeft: 16,
    marginTop: 10,
    marginBottom: 5,
  },
  irctcUserNameContainerText: {
    fontSize: 12,
    color: colors.lightTextColor,
    marginBottom: 5,
  },
  enterText: {
    color: colors.lightTextColor,
    fontSize: 16,
  },
  changeContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    lineHeight: 16,
  },
  newPasswordText: {
    color: colors.black,
    fontSize: 14,
    marginBottom: 10,
    marginTop: 15,
  },
  newLabelText1: {
    color: colors.brown2,
    marginBottom: 10,
    marginTop: 8,
    fontWeight: '400',
    marginLeft: 4,
  },
  infoText: {
    color: colors.defaultTextColor,
    fontSize: 12,
    marginBottom: 20,
  },
  errorBox: {
    borderWidth: 1,
    borderColor: colors.red,
    borderStyle: 'solid',
  },
};

const stylesSavedIds = StyleSheet.create({
  savedIdsContainer: {
    marginTop: 12,
    flexDirection: 'row',
    marginHorizontal: 10,
    width: '100%',
    flex: 1,
  },
  savedIdsText: {
    fontSize: 14,
    lineHeight: 16.8,
    fontWeight: '400',
    fontFamily: 'Lato',
    marginRight: 10,
  },
  savedIdsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: -10,
    width: '100%',
    flex: 1,
  },
  savedIdButton: {
    borderWidth: 1,
    padding: 10,
    margin: 5,
    borderRadius: 10,
    borderColor: colors.lightGray,
    backgroundColor: colors.white,
  },
  savedIdText: {
    fontSize: 12,
    lineHeight: 12,
    fontFamily: 'Lato',
  },
});

IrctcAccountDetailsCard.propTypes = {
  irctcUserName: PropTypes.string,
  labels: PropTypes.object.isRequired,
  captureRef: PropTypes.func,
  id: PropTypes.string,
  logTravellerGenericClickEvents: PropTypes.func,
  showIRCTCUsernameError: PropTypes.bool,
  showProfileIncompleteFlow: PropTypes.bool,
  irctcProfileCompletedFromWebView: PropTypes.bool,
  showIrctcComponentLoader: PropTypes.bool,
  setUserNameToRedux: PropTypes.func,
  setShowIrctcProfileCompletionFlow: PropTypes.func,
  aadharConfigKeyResponse: PropTypes.object,
  irctcProfileIncompleteFlowConfig: PropTypes.object,
  verificationFlow: PropTypes.object,
  travelplexChatConfig: PropTypes.object,
};
IrctcAccountDetailsCard.defaultProps = {
  captureRef: null,
  irctcUserName: null,
};

export default IrctcAccountDetailsCard;
