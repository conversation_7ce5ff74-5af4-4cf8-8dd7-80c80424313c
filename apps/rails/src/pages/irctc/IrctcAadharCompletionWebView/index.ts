import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import IrctcAadharCompletionWebview from './IrctcAadharCompletionWebview';
import { logAadharWebviewPageEventsToPdt } from 'apps/rails/src/PdtAnalytics/PdtAnalyticsV2/AadharWebview';
import { checkIrctcProfileCompletionStatus } from '../../TravelerDetails/TravelerDetailsActions';
import { initRailsLanding } from '../../RailsLandingPage/Store/RailsLandingPageActions';

const mapStateToProps = (state: unknown, ownProps: unknown) => {
  const {
    railsUserVerification: { irctcUserName = '' },
  } = state;
  return {
    irctcUserName,
    ...ownProps,
  };
};

const actionCreators = {
  logAadharWebviewPageEventsToPdt,
  checkIrctcProfileCompletionStatus,
  initRailsLanding,
};

const mapDispatchToProps = (dispatch: unknown) => bindActionCreators(actionCreators, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(IrctcAadharCompletionWebview);
