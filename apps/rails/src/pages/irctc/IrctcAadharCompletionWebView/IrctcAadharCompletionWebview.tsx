import React, { useEffect, useRef, useState } from 'react';
import { BackHandler, View } from 'react-native';
import CommonHeader from '@mmt/legacy-commons/Common/Components/Header/CommonHeader';
import { WebView, WebViewMessageEvent } from 'react-native-webview';
import { Actions } from '../../../navigation';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { getConfigStore } from '../../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../../configStore/Common/constants';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import isEmpty from 'lodash/isEmpty';
import { ITENERARY_DATA, removeDataFromAsyncStorage } from '../../../Utils/RailsConstant';

interface Props {
  checkIrctcProfileCompletionStatus: () => void;
  source?: string;
  irctcUserName?: string;
  logAadharWebviewPageEventsToPdt: (data: unknown, source?: string) => void;
  initRailsLanding: () => void;
}

interface Config {
  title?: string;
  url?: string;
  toastMessage?: string;
  script?: string;
}

function IrctcAadharCompletionWebview({
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  checkIrctcProfileCompletionStatus = () => {},
  source = '',
  irctcUserName = '',
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  logAadharWebviewPageEventsToPdt = () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  initRailsLanding = () => {},
}: Props) {
  const [config, setConfig] = useState<Config>({});
  const completeIrctcProfileRef = useRef<WebView>(null);

  useEffect(() => {
    async function _loadConfig() {
      const config = (await getConfigStore(configKeys.RAILS_AADHAR_WEBVIEW_CONFIG)) || {};
      setConfig(config);
    }
    _loadConfig();
    BackHandler.addEventListener('hardwareBackPress', onBackPress);

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const _injectJavaScriptCode = () => {
    if (!isEmpty(config) && !isEmpty(config?.script)) {
      const script: string = config?.script?.replaceAll('{irctcUserName}', irctcUserName ?? '');
      completeIrctcProfileRef.current?.injectJavaScript?.(script);
    }
    const toastMessage = config?.toastMessage;
    if (toastMessage) {
      showShortToast(toastMessage);
    }
  };

  const onBackPress = () => {
    checkIrctcProfileCompletionStatus?.();
    removeDataFromAsyncStorage(ITENERARY_DATA);
    initRailsLanding();
    Actions.pop();
    return true;
  };

  const onMessage = (event: WebViewMessageEvent) => {
    try {
      const data = JSON.parse(event?.nativeEvent?.data);
      if (data?.eventDetails?.event_value) {
        logAadharWebviewPageEventsToPdt(data, source);
      } else if (data?.action === 'AADHAR_VERIFIED') {
        checkIrctcProfileCompletionStatus?.();
        data?.toastMessage && showShortToast(data?.toastMessage);
        removeDataFromAsyncStorage(ITENERARY_DATA);
        initRailsLanding();
        Actions.pop();
      }
    } catch (e) {
      console.error('Error in getting event data');
    }
  };

  return (
    <>
      <View style={{ flex: 1 }}>
        <CommonHeader
          headerText={config?.title || 'MakeMyTrip'}
          imgSrc={backIcon}
          backPressHandler={onBackPress}
        />
        <WebView
          source={{ uri: config?.url || 'https://www.irctc.co.in/nget/train-search' }}
          startInLoadingState
          javaScriptEnabled
          onLoadEnd={_injectJavaScriptCode}
          ref={completeIrctcProfileRef}
          onMessage={onMessage}
          incognito={true}
        />
      </View>
    </>
  );
}

export default IrctcAadharCompletionWebview;
