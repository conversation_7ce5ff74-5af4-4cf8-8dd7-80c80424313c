import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { _label } from '../../vernacular/AppLanguage';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getLineHeight, fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';

function IrctcCardLoader() {
  return (
    <View style={styles.irctcContainer}>
      <Text style={styles.irctcHeaderText}>{_label('irctc_username')}</Text>
      <View style={[styles.container, styles.container1]} />
      <View style={[styles.container, styles.container2]} />
      <View style={[styles.container, styles.container3]} />
      <View style={[styles.container, styles.container4]} />
    </View>
  );
}

const styles = StyleSheet.create({
  irctcContainer: {
    flexDirection: 'column',
    padding: 16,
    gap: 12,
  },
  irctcHeaderText: {
    ...getLineHeight(22),
    ...fontStyle('black'),
    letterSpacing: 0,
    color: colors.black,
  },
  container: {
    backgroundColor: colors.grayBg,
    height: 30,
    borderRadius: 8,
  },
  container1: {
    width: '100%',
    height: 48,
  },
  container2: {
    width: '60%',
  },
  container3: {
    width: '100%',
  },
  container4: {
    width: '70%',
  },
});

export default IrctcCardLoader;
