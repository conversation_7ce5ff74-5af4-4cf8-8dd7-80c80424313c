import { StyleSheet } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';

const styles = StyleSheet.create({
  additionalTextIcon: {
    width: 12,
    height: 12,
    alignSelf: 'center',
  },
  additionalTextWrapper: {
    flexDirection: 'column',
    gap: 4,
    marginTop: 4,
  },
  additionalTextContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  errorText: {
    ...fontStyle('regular'),
    ...getLineHeight(12),
    color: colors.red,
    fontWeight: '400',
  },
  successText: {
    ...fontStyle('regular'),
    ...getLineHeight(12),
    color: colors.lightGreen16,
    fontWeight: '400',
  },
  profileCompletionWrapper: {
    marginTop: 10,
    backgroundColor: colors.lightRed4,
    borderRadius: 16,
    position: 'relative',
  },
  profileCompletionContainer: {
    padding: 12,
    borderRadius: 12,
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  profileCompletionTitle: {
    ...fontStyle('regular'),
    fontWeight: '400',
    ...getLineHeight(14),
    color: colors.black,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  completeButtonCta: {
    ...fontStyle('regular'),
    fontWeight: '700',
    ...getLineHeight(12),
    color: colors.azure,
    marginRight: 8,
  },
  arrowUp: {
    left: 11,
    top: -8,
    width: 0,
    height: 0,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderBottomWidth: 15,
    borderRightWidth: 13,
    borderTopWidth: 0,
    borderLeftWidth: 13,
    borderBottomColor: colors.lightRed4,
    borderRightColor: colors.transparent,
    borderTopColor: colors.transparent,
    borderLeftColor: colors.transparent,
    position: 'absolute',
  },
  redTickGradient: {
    height: 28,
    width: 28,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.lightRed5,
    borderRadius: 14,
  },
  redTickIcon: {
    width: 28,
    height: 28,
  },
});

export default styles;
