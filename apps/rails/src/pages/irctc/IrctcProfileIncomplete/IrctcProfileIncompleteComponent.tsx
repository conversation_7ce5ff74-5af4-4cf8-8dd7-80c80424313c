import React from 'react';
import { Image, Text, View } from 'react-native';
import ASSETS from '@mmt/rails/src/Utils/Assets/RailsAssets';
import styles from './IrctcProfileIncomplete.styles';
import { getLineHeight, fontStyle } from '@mmt/rails/src/vernacular/VernacularUtils';
import { _label } from '@mmt/rails/src/vernacular/AppLanguage';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { Actions } from '@mmt/rails/src/navigation';
import { trackGenericEvar99Event, trackTravellerPageEvent } from '../../../railsAnalytics';
import {
  IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS,
  IRCTC_TATKAL_AADHAR_NOT_VERIFIED_ERROR_CODE,
  TRAVELERS_PAGE_TRACKING_KEY_NEW,
} from '../../../Utils/RailsConstant';
import { showProfileIncompleteFlowPokus } from '@mmt/rails/src/RailsAbConfig';
import isEmpty from 'lodash/isEmpty';

interface props {
  username: string;
  config: Config;
  irctcProfileCompletedFromWebView: boolean;
  showProfileIncompleteFlow: boolean;
  verificationFlow: {
    errorCode: string;
  };
}

interface Config {
  successText?: string;
  successText2?: string;
  errorText?: string;
  travellerInlineTitle?: string;
}

interface Config {
  successText?: string;
  successText2?: string;
  errorText?: string;
  travellerInlineTitle?: string;
}

function IrctcProfileIncompleteComponent(props: props) {
  const handleCompleteProfileClick = () => {
    if (props?.verificationFlow?.errorCode === IRCTC_TATKAL_AADHAR_NOT_VERIFIED_ERROR_CODE) {
      Actions.openIrctcAadharCompletionWebView();
    } else {
      Actions.openCompleteIrctcProfileWebView();
    }
    trackTravellerPageEvent(
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.COMPLETE_PROFILE_CLICK_INLINE,
    );
    trackGenericEvar99Event(
      TRAVELERS_PAGE_TRACKING_KEY_NEW,
      IRCTC_PROFILE_INCOMPLETE_FLOW_EVENTS.TRAVELLER.COMPLETE_PROFILE_CLICK_EVAR99,
    );
  };
  if (!showProfileIncompleteFlowPokus() ||
    (!props?.showProfileIncompleteFlow && !props?.irctcProfileCompletedFromWebView) || isEmpty(props?.config)) {
    return null;
  }
  return (
    <>
      <View
        style={styles.additionalTextWrapper}
        testID="irctc_profile_incomplete_additional_text_wrapper"
      >
        <View style={styles.additionalTextContainer}>
          <Image source={ASSETS.greenTick} style={styles.additionalTextIcon} />
          <Text style={[styles.successText, fontStyle('regular'), getLineHeight(12)]}>
            {props?.config?.successText?.replace('{{irctcUsername}}', props?.username ?? '') || ''}
          </Text>
        </View>
        <View style={styles.additionalTextContainer}>
          <Image
            source={
              props?.irctcProfileCompletedFromWebView ? ASSETS.greenTick : ASSETS.errorInfoIcon
            }
            style={styles.additionalTextIcon}
          />
          <Text
            style={[
              props?.irctcProfileCompletedFromWebView ? styles.successText : styles.errorText,
              fontStyle('regular'),
              getLineHeight(12),
            ]}
          >
            {props?.irctcProfileCompletedFromWebView ? props?.config?.successText2 : props?.config?.errorText}
          </Text>
        </View>
      </View>
      {!props?.irctcProfileCompletedFromWebView &&
        <View style={styles.profileCompletionWrapper}>
          <View style={styles.arrowUp} />
          <View style={styles.profileCompletionContainer}>
            <Text style={styles.profileCompletionTitle}>{props?.config?.travellerInlineTitle}</Text>
            <TouchableRipple onPress={handleCompleteProfileClick}>
              <Text style={styles.completeButtonCta}>{_label('complete_now')}</Text>
            </TouchableRipple>
          </View>
        </View>
      }
    </>
  );
}

export default IrctcProfileIncompleteComponent;
