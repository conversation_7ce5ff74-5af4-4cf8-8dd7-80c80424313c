import React from 'react';
import {View} from 'react-native';
import {WebView} from 'react-native-webview';
import { Actions } from '../../navigation';
import PropTypes from 'prop-types';
import CommonHeader from '@mmt/legacy-commons/Common/Components/Header/CommonHeader';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';


class IrctcWebView extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      canGoBack: false,
    };
  }

  onHardBackPress = () => {
    const {canGoBack} = this.state;
    if (canGoBack && this._webViewRef !== null) {
      this._webViewRef.goBack();
      return canGoBack;
    } else {
      Actions.pop();
      return true;
    }
  };

  _checkIfCallbackUrl = url => url.indexOf(this.props.callbackUrl) > -1;

  _onShouldStartLoadWithRequest = (request) => {
    console.log('request', request);

    if (this._checkIfCallbackUrl(request.url)) {
      if (this._webViewRef !== null) {
        this._webViewRef.stopLoading();
        this.props.callbackFunc(request.url);
        return false;
      }
    }
    return true;
  };

  _onNavigationStateChange = (navState) => {
    this.setState({
      canGoBack: navState.canGoBack,
    });
    this._onShouldStartLoadWithRequest(navState);
  };

  render() {
    return (
      <View style={{
        flex: 1,
      }}
      >
        <CommonHeader
          headerText={this.props.headerText}
          imgSrc={backIcon}
          backPressHandler={this.onHardBackPress}
        />
        <WebView
          ref={(e) => {
            this._webViewRef = e;
          }}
          onNavigationStateChange={this._onNavigationStateChange}
          onShouldStartLoadWithRequest={this._onShouldStartLoadWithRequest}
          source={{uri: this.props.url}}
          style={{flex: 1}}
          javaScriptEnabled
        />
      </View>
    );
  }
}

IrctcWebView.propTypes = {
  url: PropTypes.string.isRequired,
  callbackUrl: PropTypes.string.isRequired,
  callbackFunc: PropTypes.func.isRequired,
  headerText: PropTypes.string,
};

IrctcWebView.navigationOptions = {
  header: null,
};


export default IrctcWebView;
