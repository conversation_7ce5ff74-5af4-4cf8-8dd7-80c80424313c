import React from 'react';
import {FlatList, Text, View} from 'react-native';
import { Actions } from '../../navigation';
import { importedStyles, selfStyles } from './style';
import TouchableRipple from '@mmt/ui/components/touchableRipple';
import RailInfoPageHeader from '../Common/RailInfoPageHeader';
import { DateDetails } from '../RisLiveTrainStatusForm/Interfaces';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';

interface TrainDatePickerProps {
  title: {
    normal: string;
    bold: string;
  };
  subtitle: string
  onDateSelected: (date: DateDetails) => void;
  data: DateDetails[];
}

const TrainDatePicker = (props: TrainDatePickerProps) => {

  const onDateSelected = (date: DateDetails) => {
    if (date.valid) {
      props?.onDateSelected(date);
      Actions.pop();
    } else {
      showShortToast('Train is not running on selected date');
    }
  };

  const onBackButtonPressed = () => {
    Actions.pop();
  };

    return (
      <View style={importedStyles.flex1}>
        <RailInfoPageHeader
          onIconClick={onBackButtonPressed}
          title={props.title}
          subtitle={props.subtitle}
        />
        <FlatList
          data={props?.data}
          keyboardShouldPersistTaps="handled"
          renderItem={({ item }) => (
            <TouchableRipple onPress={() => onDateSelected(item)}>
              <View style={importedStyles.datePickerSectionItemContainer}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={
                    item.valid
                      ? selfStyles.datePickerValidSectionTitle
                      : selfStyles.datePickerInvalidSectionTitle
                  }
                >
                  {item.dateTitle}
                </Text>
                <Text
                  style={
                    item.valid
                      ? selfStyles.datePickerValidSectionSubtitle
                      : selfStyles.datePickerInvalidSectionSubtitle
                  }
                >
                  {item.dateFormatted}
                </Text>
              </View>
            </TouchableRipple>
          )}
          keyExtractor={(item) => item.date.toString()}
        />
      </View>
    );

};

export default TrainDatePicker;
