import { StyleSheet, TextStyle } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

import importedStyles from '../RailsBusHomePage/Styles';
export { importedStyles };

interface SelfStyles {
  datePickerInvalidSectionSubtitle: TextStyle;
  datePickerValidSectionSubtitle: TextStyle;
  datePickerInvalidSectionTitle: TextStyle;
  datePickerValidSectionTitle: TextStyle;
}

export const selfStyles: SelfStyles = StyleSheet.create({
  datePickerValidSectionTitle: {
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.black,
  },
  datePickerInvalidSectionTitle: {
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.black + '4D',
  },
  datePickerValidSectionSubtitle: {
    fontFamily: fonts.regular,
    fontSize: 16,
    color: colors.lightTextColor,
  },
  datePickerInvalidSectionSubtitle: {
    fontFamily: fonts.regular,
    fontSize: 16,
    color: colors.lightTextColor + '4D',
  },
});
