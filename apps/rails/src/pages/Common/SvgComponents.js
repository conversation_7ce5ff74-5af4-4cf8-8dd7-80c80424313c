import PropTypes from 'prop-types';
import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Rect } from 'react-native-svg';

export const  PercentGreenBg = () => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.618.725c1.375-1.523 3.901-.477 3.796 1.572a2.18 2.18 0 002.29 2.289c2.048-.105 3.094 2.42 1.571 3.796a2.18 2.18 0 000 3.236c1.523 1.375.477 3.901-1.572 3.796a2.18 2.18 0 00-2.289 2.29c.105 2.048-2.42 3.094-3.796 1.571a2.18 2.18 0 00-3.236 0c-1.375 1.523-3.901.477-3.797-1.572a2.18 2.18 0 00-2.288-2.288c-2.05.104-3.095-2.422-1.572-3.797a2.18 2.18 0 000-3.236C-.798 7.007.248 4.48 2.297 4.586a2.18 2.18 0 002.288-2.29C4.481.249 7.007-.797 8.382.726a2.18 2.18 0 003.236 0z"
        fill="#1A7971"
      />
      <Path
        d="M9.538 7.114h0a1 1 0 00-1.268-.649l1.268.649zm0 0a1.029 1.029 0 01-.637 1.298m.637-1.298l-.637 1.298m0 0a1.002 1.002 0 01-1.268-.647l1.268.647zm-1.268-.648a1.03 1.03 0 01.637-1.298l-.637 1.298zM13.438 12.42c0 .573-.456 1.026-1.005 1.026a1.016 1.016 0 01-1.005-1.025c0-.572.457-1.025 1.005-1.025.549 0 1.005.452 1.005 1.025z"
        stroke="#fff"
        strokeWidth={1.16667}
        strokeLinecap="round"
      />
      <Path
        d="M7.785 13.547l5.423-7.082"
        stroke="#fff"
        strokeWidth={1.26389}
        strokeLinecap="round"
      />
    </Svg>
  );
};

export const CrossGreen = () =>  {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={12}
      height={12}
      viewBox="0 0 12 12"
      fill="none"
    >
      <Path
        d="M1.417 1.417l9.204 9.204M10.583 1.417L1.38 10.62"
        stroke="#1A7971"
        strokeLinecap="round"
      />
    </Svg>
  );
};

export const Curve = ({style = {}}) => {
  return (
    <Svg viewBox="0 0 5 38" fill="none" style={{height: 60, aspectRatio: 5 / 38, ...style}}>
      <G clipPath="url(#clip0_698_62)">
        <Path
          d="M0 38C3.18581 32.3713 5 25.8934 5 19C5 12.1066 3.18581 5.62867 0 0V38Z"
          fill="white"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M-0.373143 -0.21757C-0.493302 -0.14956 -0.535578 0.00298155 -0.467568 0.123141C2.69764 5.7154 4.5 12.1511 4.5 19C4.5 25.8489 2.69764 32.2846 -0.467568 37.8769C-0.535578 37.997 -0.493302 38.1496 -0.373143 38.2176C-0.252984 38.2856 -0.100442 38.2433 -0.0324321 38.1231C3.17399 32.4581 5 25.9378 5 19C5 12.0622 3.17399 5.54193 -0.0324321 -0.123145C-0.100442 -0.243305 -0.252984 -0.28558 -0.373143 -0.21757Z"
          fill="#D8D8D8"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_698_62">
          <Rect width="5" height="38" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};


Curve.propTypes = {
  style: PropTypes.object,
};





