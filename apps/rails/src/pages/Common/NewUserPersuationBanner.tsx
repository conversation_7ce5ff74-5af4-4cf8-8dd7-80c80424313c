import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import HTMLView from 'react-native-htmlview';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { PercentGreenBg, CrossGreen } from './SvgComponents';
import { LISTING_PAGE_CONST, LANDING_PAGE_CONST } from '../../Utils/RailsConstant';
import { trackNewListingFilterEvent, trackRailsLandingRecentSearches } from '../../railsAnalytics';
import useConfigStore from '../../configStore/Common/RailsConfigStoreUtils';
import { configKeys } from '../../configStore/Common/constants';
import {getHydraSegment} from '../../Utils/RailsAPIRepository';
import isEmpty from 'lodash/isEmpty';
import { getRailsNuLpBanner } from '../../RailsAbConfig';


interface NewUserPersuationProps {
    page:string;
    id?: string;
}

const NewUserPersuation = ({page, id}:NewUserPersuationProps) => {
    const [isVisible , setIsVisible] = useState<boolean>(false);
  const newUserPersuasion = useConfigStore(configKeys.NEW_USER_PERSUATION);
  const persuasionMsg = newUserPersuasion?.PersuasionMessage;
  const msgStyle = newUserPersuasion?.MessageStyle;

  useEffect(() => {
    const checkNewUser = async () => {
        const visiblityPokus = getRailsNuLpBanner();
        try {
            const response = await getHydraSegment();
            const isNew = !(isEmpty(response?.data?.segments));
            setIsVisible((!visiblityPokus) && isNew);
        } catch (error) {
            console.log('Error in fetching hydra from PWA: ',error);
        }
    };
    checkNewUser();
    if (page === LISTING_PAGE_CONST) {trackNewListingFilterEvent('rails_nu_banner_listing_shown');}
    if (page === LANDING_PAGE_CONST) {trackRailsLandingRecentSearches('rails_nu_banner_landing_shown');}
}, []);

    const onCloseClick = () => {
        setIsVisible(!isVisible);
        trackNewListingFilterEvent('rails_nu_banner_landing_close_click');
      };

    if ( !isVisible ||  !persuasionMsg ) {return null;}
    return (
        <View style={styles.container} testID={id}>
            <View style={getBgStyleSheet(page)}>
                <View style={styles.imgContainer}>
                    <PercentGreenBg/>
                </View>
                <View style={getTextStyleSheet(page)}>
                    <HTMLView value={persuasionMsg} stylesheet={msgStyle}/>
                </View>
                {page === LISTING_PAGE_CONST &&
                    <TouchableOpacity onPress={() => {onCloseClick();}}>
                        <CrossGreen/>
                    </TouchableOpacity>}
            </View>
        </View>
    );
};


const getBgStyleSheet = (page: string) => {
    switch (page) {
        case LISTING_PAGE_CONST:
            return styles.bannerBgListing;
        case LANDING_PAGE_CONST:
            return styles.bannerBgLanding;
    }
};

const getTextStyleSheet = (page: string) => {
    switch (page) {
        case LISTING_PAGE_CONST:
            return styles.htmlContainerListing;
        case LANDING_PAGE_CONST:
            return styles.htmlContainerLanding;
    }
};

const styles = StyleSheet.create({
    bannerBgListing: {
        backgroundColor: colors.cruise,
        paddingVertical: 8,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    bannerBgLanding: {
        backgroundColor: colors.cruise,
        paddingVertical: 8,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        marginLeft: 15,
        marginRight: 15,
        marginTop: 13,
        marginBottom: -5,
        borderRadius: 4,
    },
    container: {
        backgroundColor: colors.white,
        zIndex: 1000,
    },
    htmlContainerLanding: {
        flexBasis: '75%',
        marginBottom: 3,
    },
    htmlContainerListing: {
        flexBasis: '75%',
        marginTop: 8,
        marginBottom: 3,
    },
    imgContainer: {
        marginLeft: 15,
        flexBasis: '10%',
    },
});

export default NewUserPersuation;
