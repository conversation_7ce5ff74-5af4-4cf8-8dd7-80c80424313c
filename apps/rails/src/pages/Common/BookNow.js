import React from 'react';
import PropTypes from 'prop-types';
import {
  StyleSheet,
  Text,
  View,
  TouchableWithoutFeedback,
  Image,
} from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import isEmpty from 'lodash/isEmpty';
import LinearGradient from 'react-native-linear-gradient';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';

const enabledColors = ['#53b2ff', '#065af3'];
const disabledColors = ['#989898', '#989898'];
import RupeeText from '@mmt/legacy-commons/Common/Components/RupeeText';
import infoIcon from '@mmt/legacy-assets/src/info_iconV3.webp';
import arrowUp from '@mmt/legacy-assets/src/ic_arrow_blue_up.webp';

export const appendRupeeSymbol = (amount) => `₹ ${amount}`;

class BookNowButton extends React.Component {
  render() {
    const {
      totalCollectibleAmount: totalAmount,
      buttonText,
      perSeatText,
      onBookNowClicked,
      BookNowButtonEnabled,
      showLoader,
      onInfoIconClicked,
      expandedFareBottomSheet,
      seatlockShownOnReview,
      isBnppOpted,
      totalAmountOnBnppSelected,
      showPayNowLoader,
      eligibleAddOns,
    } = this.props;
    const btnColor = (showLoader || showPayNowLoader) ? { color: colors.transparent } : { color: colors.white };
    const bnppText = isBnppOpted ? _label('paying_partial_amount') : _label('paying_full_amount');
    const totalAmountToBePaid = (isBnppOpted && seatlockShownOnReview) ? totalAmountOnBnppSelected : totalAmount;
    const showInfoIcon = seatlockShownOnReview && eligibleAddOns?.bnppData?.paymentFareBreakup;

    return (
      <View style={styles.payNowsection} testID={this.props?.id}>
        <View style={styles.textContainer}>
          <View>
            <RupeeText style={[styles.payNowPrice, fontStyle('bold'), getLineHeight(22)]}>
              <Text style={styles.rupeeSumbol}>{appendRupeeSymbol(totalAmountToBePaid)}</Text>
              {showInfoIcon && (
                <TouchableWithoutFeedback onPress={onInfoIconClicked}>
                  <View style={styles.bnppIconsStyles}>
                    <View style={styles.fareContainer}>
                      <Image
                        source={expandedFareBottomSheet ? arrowUp : infoIcon}
                        style={styles.arrowIcon}
                      />
                      {isBnppOpted && (
                        <Image source={ASSETS.seatLockGreyIcon} style={styles.bnppIcon} />
                      )}
                    </View>
                  </View>
                </TouchableWithoutFeedback>
              )}
            </RupeeText>
            {!isEmpty(perSeatText) && (
              <Text style={[styles.perSeatText, fontStyle('regular'), getLineHeight(10)]}>
                {perSeatText}
              </Text>
            )}
            {seatlockShownOnReview && <Text style={styles.partialFullAmountText}>{bnppText}</Text>}
          </View>
        </View>
        <View>
        <TouchableRipple onPress={onBookNowClicked} feedbackColor={colors.transparent} disabled={showPayNowLoader}>
          <LinearGradient
            style={styles.linearGrad}
            colors={BookNowButtonEnabled ? enabledColors : disabledColors}
            start={{
                x: 0,
                y: 2,
              }}
            end={{
                x: 1,
                y: 1,
              }}
          >
              <View testID={`${this.props?.id}_button`}>
              <View style={styles.buttonBlue}>
                <Text style={[{...styles.buttonTextStyle, ...btnColor}, fontStyle('bold'), getLineHeight(16)]}>
                  {buttonText}
                </Text>
              </View>
            </View>
            {(showLoader || showPayNowLoader) &&  <View style={{
              flex: 1, alignItems: 'center', justifyContent: 'center',
              position: 'absolute',
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              zIndex:50,
            }}>
                  <Spinner size={20} color="#fff" />
            </View>
            }
          </LinearGradient>
          </TouchableRipple>
        </View>
      </View>
    );
  }
}

BookNowButton.propTypes = {
  onBookNowClicked: PropTypes.func.isRequired,
  totalCollectibleAmount: PropTypes.string.isRequired,
  buttonText: PropTypes.string.isRequired,
  perSeatText: PropTypes.string,
  BookNowButtonEnabled: PropTypes.bool,
  id: PropTypes.string,
  showLoader: PropTypes.bool,
  onInfoIconClicked: PropTypes.func,
  expandedFareBottomSheet: PropTypes.bool,
  seatlockShownOnReview: PropTypes.bool,
  isBnppOpted: PropTypes.bool,
  totalAmountOnBnppSelected: PropTypes.string,
  showPayNowLoader: PropTypes.bool,
  eligibleAddOns: PropTypes.shape({
    bnppData: PropTypes.shape({
      paymentFareBreakup: PropTypes.object,
    }),
  }),
};

BookNowButton.defaultProps = {
  perSeatText: null,
  BookNowButtonEnabled: true,
};

const styles = StyleSheet.create({
  textContainer: {
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  perSeatText: {
    color: colors.white,
    fontSize: 10,
  },
  payNowPrice: {
    color: colors.white,
    flexDirection: 'column',
  },
  buttonBlue: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 41,
    borderBottomRightRadius: 100,
    borderTopRightRadius: 100,
    maxWidth: 200,
  },

  payNowsection: {
    backgroundColor: colors.defaultTextColor,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
    zIndex: 300,
  },
  linearGrad: {
    overflow: 'hidden',
    borderRadius: 100,
    alignItems: 'center',
    paddingHorizontal: 44,
  },
  buttonTextStyle: {
    color: colors.white,
    borderRadius: 100,
    textAlign: 'center',
  },
  fareContainer: {
    flexDirection: 'row',
    marginLeft: 50,
    marginBottom: 1,
  },
  arrowIcon: {
    width: 18,
    height: 18,
    marginLeft: -35,
    resizeMode: 'contain',
    tintColor: colors.white,
  },
  bnppIcon: {
    width: 18,
    height: 18,
    marginLeft: 8,
    resizeMode: 'contain',
    tintColor: colors.darkGrey2,
  },
  partialFullAmountText: {
    color: colors.white,
    fontSize: 12,
  },
  bnppIconsStyles: {
    marginLeft: 15,
  },
  rupeeSumbol: {
    marginRight: 4,
  },
});


export default BookNowButton;
