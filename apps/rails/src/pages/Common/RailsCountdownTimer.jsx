import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import ProgressCircle from 'react-native-progress-circle';

function RailsCountdownTimer({
  hoursMinSecs = { hours: 1, minutes: 20, seconds: 40 },
  timesUpCallback,
  shouldReset = false,
  timerWrapperStyle = {},
  timerTextStyle = {},
}) {
  const { hours = 0, minutes = 0, seconds = 60 } = hoursMinSecs;
  const [[hrs, mins, secs], setTime] = useState([hours, minutes, seconds]);
  const tick = () => {
    if (hrs === 0 && mins === 0 && secs === 0) {
      timesUpCallback && timesUpCallback();
      shouldReset && reset();
    } else if (mins === 0 && secs === 0) {
      setTime([hrs - 1, 59, 59]);
    } else if (secs === 0) {
      setTime([hrs, mins - 1, 59]);
    } else {
      setTime([hrs, mins, secs - 1]);
    }
  };

  const reset = () => setTime([parseInt(hours), parseInt(minutes), parseInt(seconds)]);

  useEffect(() => {
    const timerId = setInterval(() => tick(), 1000);
    return () => clearInterval(timerId);
  });

  return (
    <ProgressCircle
      percent={((seconds - secs) / seconds) * 100}
      radius={50}
      borderWidth={8}
      color="#3399FF"
      shadowColor="#999"
      bgColor="#fff"
    >
      <View style={timerWrapperStyle}>
        <Text style={timerTextStyle}>
          {`${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}s`}
        </Text>
      </View>
    </ProgressCircle>
  );
}

export default RailsCountdownTimer;

RailsCountdownTimer.propTypes = {
  hoursMinSecs: PropTypes.shape({
    hours: PropTypes.number,
    minutes: PropTypes.number,
    seconds: PropTypes.number,
  }),
  timesUpCallback: PropTypes.func,
  shouldReset: PropTypes.bool,
  timerWrapperStyle: PropTypes.object,
  timerTextStyle: PropTypes.object,
};
