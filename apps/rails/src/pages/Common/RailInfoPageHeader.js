import React from 'react';
import { Platform, Text, View } from 'react-native';
import { Actions } from '../../navigation';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import Header from '@Frontend_Ui_Lib_App/Header';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';

//const backIcon = require('../../../../Assets/ic_back_arrow.webp');
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import {fontStyle} from '../../vernacular/VernacularUtils';
import LanguageSwitcher from './LanguageSwitcher';
export const HEADER_HEIGHT = 56;

const railInfoPageHeaderDefaultPropTitle = {
  normal: '',
  bold: '',
};

const railInfoPageHeaderDefaultPropOnIconClick = () => {
  Actions.pop();
};

const RailInfoPageHeader = ({
  title = railInfoPageHeaderDefaultPropTitle,
  subtitle = '',
  boldSubtitle = '',
  onIconClick = railInfoPageHeaderDefaultPropOnIconClick,
  extraHeight = 0,
  extraPaddingBottom = 0,
  headerHeight = HEADER_HEIGHT,
  showLanguageSwitcher = false,
  id,
}) => {

  return (
    <Header
      wrapperTestId={id}
      customStyles={{
        wrapperStyle: {
          ...styles.container,
          height: headerHeight + extraHeight,
          paddingBottom: extraPaddingBottom,
          elevation: 0,
          shadowOpacity: 0,
          shadowOffset: { width: 0, height: 0 },
          shadowRadius: 0,
        },
      }}
      leftIcon={{
        icon: backIcon,
        customStyles: {
          iconStyle: styles.icon,
          iconContainerStyle: { ...styles.iconPadding, zIndex: 1000 },
        },
        onPress: onIconClick,
      }}
      leftComponent={
        <View style={[styles.titleHeaderView, { flex: 1 }]}>
          {!isEmpty(title) && (
            <Text style={[styles.title, fontStyle('bold')]}>
              {`${title?.normal || ''} `}
              <Text style={[styles.titleBlack, fontStyle('bold')]}>{title?.bold}</Text>
            </Text>
          )}
          {(subtitle?.length > 0 || boldSubtitle?.length > 0) && (
            <Text
              style={[styles.subtitle, fontStyle('regular')]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {subtitle}
              <Text style={[styles.boldSubtitle, fontStyle('bold')]}>{boldSubtitle}</Text>
            </Text>
          )}
        </View>
      }
      rightComponent={
        showLanguageSwitcher ? (
          <LanguageSwitcher
            pageId="rails_search_landing" // TODO Vernac fix page ids in constants
            fullWidthIcon
            showHindiTooltip={false}
          />
        ) : (
          <></>
        )
      }
    />
  );
};

RailInfoPageHeader.propTypes = {
  title: PropTypes.object,
  subtitle: PropTypes.string,
  boldSubtitle: PropTypes.string,
  onIconClick: PropTypes.func,
  extraHeight: PropTypes.number,
  extraPaddingBottom: PropTypes.number,
  headerHeight: PropTypes.number,
  showLanguageSwitcher: PropTypes.bool,
  id: PropTypes.string,
};

const styles = {
  container: {
    flexDirection: 'row',
    height: HEADER_HEIGHT,
    marginVertical: 0,
    marginHorizontal: 0,
    paddingHorizontal: 0,
    paddingVertical: 0,
    marginBottom: 2,
    alignItems: 'center',
    zIndex: 1,
    elevation: 0,
    shadowOpacity: 0,
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 0,
  },
  titleHeaderView: {
    ...Platform.select({
      ios: {
        flex:1,
        alignItems:'center',
        marginRight:48,
      },
    }),
  },
  iconPadding: {
    width: 48,
    height: 48,
    marginRight: 0,
  },
  icon: {
    width: 15,
    height: 15,
    margin: 16,
    ...Platform.select({
      ios: {
        width: 18,
    height: 20,
    margin: 16,
      },
    }),

  },
  title: {
    color: colors.black,
    fontSize: 16,
  },
  titleOldVersion: {  // this is mainly added for header fix, when it would be properly handled pls remove this
    color: colors.defaultTextColor,
    fontSize: 18,
    fontFamily: fonts.bold,
  },
  titleBlack: {
    color: colors.black,
    fontSize: 16,
  },
  subtitle: {
    color: colors.lightTextColor,
    fontSize: 12,
  },
  boldSubtitle: {
    color: colors.defaultTextColor,
    fontSize: 12,
  },
  changeLanguageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    // zIndex:10,
    // elevation:10,
    backgroundColor: '#fff',
    marginHorizontal:4,
    height:50,
    marginVertical:4,
    borderRadius: 10,

  },
  changeLanguageIcon: {
    height: 24,
    width: 24,
  },
  changeLanguageLabel: {
    fontSize: 12,
    lineHeight: 24,
    paddingHorizontal: 2,
    marginBottom: 6,
    fontFamily: fonts.bold,
    color: colors.azure,
  },
  overlay:{
    flex:1,
    justifyContent: 'center',
    paddingRight: 10,
  },
  overlayContainer:{
    position:'absolute',
    top:0,bottom:0,left:0,right:0,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
};

export default RailInfoPageHeader;
