import React from 'react';
import fecha from 'fecha';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import PropTypes from 'prop-types';

export default class Example extends React.Component {
  onBlur = () => {
    const date = this._dateRef.value;
    const dateObject = fecha.parse(date, 'YYYY-MM-DD');
    this.props.callbackDob(dateObject);
  };

  render() {
    return (
      <div>
        <div>
          {this.props.children}
        </div>
        <input
          type="date"
          min={fecha.format(this.props.minDate, 'YYYY-MM-DD')}
          max={fecha.format(this.props.maxDate, 'YYYY-MM-DD')}
          onBlur={this.onBlur}
          ref={ref => this._dateRef = ref}
          style={{
            backgroundColor: colors.transparent,
            borderBottomColor: colors.black,
            borderBottomWidth: 1,
            width: '50%',
            borderTopWidth: 0,
            borderLeftWidth: 0,
            borderRightWidth: 0,
            paddingTop: 16,
          }}
        />
      </div>
    );
  }
}

Example.propTypes = {
  callbackDob: PropTypes.func,
  children: PropTypes.node,
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
};
