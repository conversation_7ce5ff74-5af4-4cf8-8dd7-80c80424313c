import React from 'react';
import PropTypes from 'prop-types';
import { Image, StyleSheet, Text, View } from 'react-native';
import { View as AnimatableView } from 'react-native-animatable';
import { colors, normalisePx } from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';

import closeIcon from '@mmt/legacy-assets/src/close-white.webp';

export const Tooltip = ({
                     style, content, prefix, orientation, theme, tipStyle,
                     textStyle, contentStyle, dismiss, hideFunction,
                 }) => {
    let tipColor = {};
    let backGroundColor = {};
    if (theme === 'DARK') {
        if (orientation === 'top') {
            tipColor = {
                borderBottomColor: colors.black,
            };
        } else {
            tipColor = {
                borderTopColor: colors.black,
            };
        }
        backGroundColor = {
            backgroundColor: colors.black,
        };
    }

    return (
        <View style={{
            ...style,
        }}
        >
            {orientation === 'top' &&
            <View style={[styles.tooltipsArrowTop, tipColor, tipStyle]} />}
            <View style={[styles.tooltipsInnerContainer, backGroundColor]}>
                <View style={[styles.content, contentStyle]}>
                    <Text style={[styles.text,fontStyle(('regular')), textStyle, getLineHeight(14)]}>
                        {prefix && <Text style={[fontStyle('bold'), getLineHeight(14)]}>{prefix}</Text>}
                        {content}
                    </Text>
                    {dismiss &&
                    <TouchableRipple onPress={hideFunction}>
                        <Image source={closeIcon} style={{height: 24, width: 24}} />
                    </TouchableRipple>}
                </View>
            </View>
            {orientation === 'bottom' &&
            <View style={[styles.tooltipsArrowBottom, tipColor, tipStyle]} />}
        </View>
    );
};

Tooltip.propTypes = {
    styles: PropTypes.object,
    orientation: PropTypes.string,
    tipStyle: PropTypes.object,
    theme: PropTypes.string,
    content: PropTypes.string.isRequired,
    textStyle: PropTypes.object,
    contentStyle: PropTypes.object,
    hideFunction: PropTypes.func,
    dismiss: PropTypes.bool,
    style: PropTypes.object,
    prefix: PropTypes.string,
};

Tooltip.defaultProps = {
    orientation: 'top',
    styles: {},
    tipStyle: {},
    contentStyle: {},
    textStyle: {},
};

export default Tooltip;

const toolTipOutAnim = {
    0: {
        opacity: 1,
        scale: 1,
    },
    0.33: {
        opacity: 0.5,
        scale: 0.8,
    },
    0.66: {
        opacity: 0.3,
        scale: 0.8,
    },
    1: {
        opacity: 0,
        scale: 0,
    },
};

export const AnimatedToolTip = ({
                                    style, content, orientation, theme, tipStyle, textStyle, contentStyle,
                                }) => (
    <AnimatableView animation={toolTipOutAnim} duration={2000} easing={'ease-in-back'}>
        <Tooltip
            style={style}
            content={content}
            orientation={orientation}
            theme={theme}
            tipStyle={tipStyle}
            textStyle={textStyle}
            contentStyle={contentStyle}
        />
    </AnimatableView>
);

AnimatedToolTip.propTypes = {
    styles: PropTypes.object,
    orientation: PropTypes.string,
    tipStyle: PropTypes.object,
    theme: PropTypes.string,
    content: PropTypes.string.isRequired,
    textStyle: PropTypes.object,
    contentStyle: PropTypes.object,
    style: PropTypes.object,
};

const styles = StyleSheet.create({
    tooltipsInnerContainer: {
        backgroundColor: colors.lightSeaGreenTextColor,
        flexDirection: 'row',
        borderRadius: 5,
        borderColor: colors.transparent,
    },
    tooltipsArrowTop: {
        width: 0,
        height: 0,
        marginLeft: normalisePx(16),
        backgroundColor: colors.transparent,
        borderStyle: 'solid',
        borderLeftWidth: normalisePx(10),
        borderRightWidth: normalisePx(10),
        borderBottomWidth: normalisePx(8),
        borderLeftColor: colors.transparent,
        borderRightColor: colors.transparent,
        borderBottomColor: colors.lightSeaGreenTextColor,
        marginBottom: -0.5,
    },
    tooltipsArrowBottom: {
        width: 0,
        height: 0,
        marginLeft: normalisePx(16),
        backgroundColor: colors.transparent,
        borderStyle: 'solid',
        borderLeftWidth: normalisePx(10),
        borderRightWidth: normalisePx(10),
        borderTopWidth: normalisePx(8),
        borderLeftColor: colors.transparent,
        borderRightColor: colors.transparent,
        borderTopColor: colors.lightSeaGreenTextColor,
        marginTop: -0.5,
    },
    content: {
        paddingHorizontal: normalisePx(16),
        paddingVertical: normalisePx(10),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    text: {
        fontSize: 14,
        lineHeight: 14,
        color: colors.white,
    },
});
