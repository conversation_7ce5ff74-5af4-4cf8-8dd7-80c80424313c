import React from 'react';
import {Dimensions, TouchableWithoutFeedback, Image, StyleSheet, View, Platform} from 'react-native';
import PropTypes from 'prop-types';
import isNil from 'lodash/isNil';
import inRange from 'lodash/inRange';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';

import closeIcon from '@mmt/legacy-assets/src/cross_white.webp';
class RailsToolTip extends React.Component {
  _onContentLayout = ({nativeEvent}) => {
    const {
      x, y, height, width,
    } = nativeEvent.layout;
    this._bounds = {
      top: y, left: x, bottom: y + height, right: x + width,
    };
  };

  _onPress = ({nativeEvent}) => {
    const {pageX, pageY} = nativeEvent;
    const {
      top, left, bottom, right,
    } = this._bounds;
    const inBounds = inRange(pageY, top, bottom) && inRange(pageX, left, right);
    if (!inBounds) {
      this.props.dismiss();
    }
  };

  render() {
    const {layout} = this.props;
    if (isNil(layout)) {
      return null;
    }
    const isBottom = this.props.orientation === 'bottom';
    let position = {left: layout.x};
    if (!isBottom) {
      position = {...position, bottom: (Dimensions.get('screen').height - layout.y)};
    } else {
      position = {...position, top: layout.y - 24};
    }
    return (
      <TouchableWithoutFeedback onPress={this._onPress}>
        <View style={styles.rootContainer} pointerEvents="box-none" testID={this.props?.id}>
          <View
            style={{
              flexDirection: 'column',
              backgroundColor: colors.transparent,
              position: 'absolute',
              ...position,
              ...getPlatformElevation(Platform.select({ios: 6, android: 12})),
            }}
            onLayout={this._onContentLayout}
          >
            {this.props.orientation === 'bottom' &&
            <View style={styles.arrowTop} />
            }
            <View style={styles.innerContainer}>
              <View style={styles.content}>
                {this.props.children}
              </View>
              <View style={styles.closeContainer}>
                <TouchableRipple
                  onPress={this.props.dismiss}
                  feedbackColor={colors.grayBgTransparent}
                >
                  <Image style={styles.close} source={closeIcon} />
                </TouchableRipple>
              </View>
            </View>
            {this.props.orientation === 'top' &&
            <View style={styles.arrowBottom} />
            }
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}

const innerContainerRightMargin = 10;

const styles = StyleSheet.create({
  rootContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.transparent,
  },
  innerContainer: {
    backgroundColor: colors.lightSeaGreenTextColor,
    flexDirection: 'row',
    borderRadius: 2,
    borderColor: colors.transparent,
    padding: 4,
    width: (Dimensions.get('screen').width - innerContainerRightMargin) * 0.55,
    marginRight: innerContainerRightMargin,
  },
  arrowTop: {
    width: 0,
    height: 0,
    marginLeft: '60%',
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderBottomWidth: 8,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderBottomColor: colors.lightSeaGreenTextColor,
  },
  arrowBottom: {
    width: 0,
    height: 0,
    marginLeft: 16,
    backgroundColor: colors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopWidth: 8,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderTopColor: colors.lightSeaGreenTextColor,
  },
  content: {
    padding: 8,
  },
  closeContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  close: {
    width: 10,
    height: 10,
    alignSelf: 'center',
    resizeMode: 'contain',
  },

});

RailsToolTip.propTypes = {
  children: PropTypes.node.isRequired,
  dismiss: PropTypes.func.isRequired,
  layout: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
  }),
  orientation: PropTypes.oneOf(['top', 'bottom']),
  id: PropTypes.string,
};
RailsToolTip.defaultProps = {
  layout: null,
  orientation: 'top',
};

export default RailsToolTip;
