// cloned from RailsInfoPageHeader
import React from 'react';
import { Image, Text, View, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Actions } from '../../navigation';
import PropTypes from 'prop-types';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {backIcon, newBackIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {fontStyle} from '../../vernacular/VernacularUtils';
import LanguageSwitcher from './LanguageSwitcher';
import { _label } from '../../vernacular/AppLanguage';
import {PAGE_ID} from '../../vernacular/tracking';
import { BUILD_FLAVOR } from '../../Utils/RailsConstant';
import { getIfCanEnableEnvSwitch } from '../../RailsAbConfig';

const railsNewLandingHeaderDefaultPropOnIconClick = () => {
  Actions.pop();
};

const RailsNewLandingHeader = (props) => {
  props = {
    ...props,
    title: typeof props.title === 'undefined' ? '' : props.title,
    onIconClick:
      typeof props.onIconClick === 'undefined'
        ? railsNewLandingHeaderDefaultPropOnIconClick
        : props.onIconClick,
    showLanguageSwitcher:
      typeof props.showLanguageSwitcher === 'undefined' ? false : props.showLanguageSwitcher,
  };

  const { title, onIconClick, showLanguageSwitcher, buildFlavor, isNewLanding = false, busStreakNotification } = props;

  const onLongPressEnvSwitch = () => {
    if (getIfCanEnableEnvSwitch()){
      Actions.openEnvSwitchPage();
    }
  };
  const backIconSource = isNewLanding ? newBackIcon : backIcon;
  const backIconStyles = isNewLanding ? styles.newBackIcon : styles.backIcon;
  const backIconContainer = isNewLanding ? styles.newBackIconContainer : styles.backIconContainer;
  const headerContainer = isNewLanding ? styles.newHeaderContainer : styles.headerContainer;
  const elevation = isNewLanding ? 0 : 3;
  const cardStyles = isNewLanding ? styles.isNewHeaderCard : styles.card;
  const headerText = isNewLanding ? '' : _label(title);
  return (
    <View style={headerContainer} testID="new_landing_header">
      <Card style={cardStyles} elevation={elevation} testID="new_landing_header_cards">
        <View style={styles.leftHeaderContainer}>
          <View style={styles.container}>
            <TouchableRipple onPress={onIconClick} testID="new_landing_header_back_icon">
              <View style={backIconContainer}>
                <Image source={backIconSource} style={backIconStyles} />
              </View>
            </TouchableRipple>
            <Text style={[styles.headerText, fontStyle('bold')]}>{headerText}</Text>
            {(__DEV__ || buildFlavor === BUILD_FLAVOR.CHARLES) && (
              <TouchableOpacity
                activeOpacity={1}
                style={styles.switchContainer}
                onLongPress={onLongPressEnvSwitch}
                testID="new_landing_header_env_switch"
              />
            )}
          </View>
          {showLanguageSwitcher ? (
            <LanguageSwitcher pageId={PAGE_ID.NEW_LANDING} fullWidthIcon showHindiTooltip={false} />
          ) : null}
        </View>
        {busStreakNotification && (
          <TouchableOpacity
            onPress={busStreakNotification.onClick}
            activeOpacity={0.4}
            testID="new_landing_header_bus_streak_notification"
          >
            <View style={styles.busStreakNotification}>
              <Image
                source={{ uri: busStreakNotification.icon }}
                style={styles.busStreakNotificationIcon}
                testID="new_landing_header_bus_streak_notification_icon"
              />
              {busStreakNotification.count > 0 && (
                <View
                  style={styles.busStreakNotificationTextWrapper}
                  testID="new_landing_header_bus_streak_notification_text_wrapper"
                >
                  <Text
                    style={styles.busStreakNotificationText}
                    testID="new_landing_header_bus_streak_notification_text"
                  >
                    {busStreakNotification.count}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        )}
      </Card>
    </View>
  );
};

RailsNewLandingHeader.propTypes = {
  title: PropTypes.string,
  onIconClick: PropTypes.func,
  showLanguageSwitcher: PropTypes.bool,
  buildFlavor: PropTypes.string,
  busStreakNotification: PropTypes.shape({
    icon: PropTypes.string.isRequired,
    count: PropTypes.number.isRequired,
    onClick: PropTypes.func.isRequired,
  }),
  isNewLanding: PropTypes.bool,
};

const styles = StyleSheet.create({
  headerContainer: {
    position: 'relative',
  },
  newHeaderContainer: {
    position: 'relative',
    height: 36,
  },
  card: {
    height: 60,
    marginHorizontal: 0,
    marginTop: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  isNewHeaderCard: {
    height: 36,
    marginHorizontal: 0,
    marginTop: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  headerText: {
    fontSize: 18,
    letterSpacing: 0,
    color: colors.black,
  },
  backIconContainer: {
    height: '100%',
    width: 48,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 2,
    backgroundColor: colors.white,
  },
  newBackIconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 2,
    backgroundColor: colors.white,
    paddingLeft: 16,
  },
  backIcon: {
    height: 15,
    width: 15,
  },
  newBackIcon: {
    height: 20,
    width: 20,
  },
  switchContainer: {
    marginHorizontal: 24,
    width: 100,
    height: 40,
  },
  leftHeaderContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  busStreakNotification: {
    alignSelf: 'center',
    marginRight: 23,
  },
  busStreakNotificationIcon: {
    width: 18,
    height: 19,
    resizeMode: 'cover',
  },
  busStreakNotificationTextWrapper: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.red17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  busStreakNotificationText: {
    fontFamily: fonts.regular,
    fontSize: 12,
    textAlign: 'center',
    color: colors.white,
    ...Platform.select({
      android: { top: -1 },
    }),
  },
});

export default RailsNewLandingHeader;
