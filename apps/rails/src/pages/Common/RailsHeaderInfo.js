import PropTypes from 'prop-types';
import React from 'react';
import {Text, View} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { getLineHeight } from '../../vernacular/VernacularUtils';
import { railsConfirmOptionsV2 } from '@mmt/rails/src/RailsAbConfig';
import { ConfirmOptionsV2 } from '../../RailsAbConstants';

const showConfirmedOptionsV2 = ( railsConfirmOptionsV2() === ConfirmOptionsV2.SHOWN);

const RailsHeaderInfo = props => (
  <View style={{backgroundColor: showConfirmedOptionsV2 ? colors.lightGreen1 : colors.cruise }} testID={props?.id}>
    <View style={{margin: 10}}>
      <Text style={{
        color: showConfirmedOptionsV2 ? colors.textGrey : colors.green,
        fontSize: 12,
        ...getLineHeight(12),
      }}
      >
        {props.message}
      </Text>
    </View>
  </View>
);

export default RailsHeaderInfo;

RailsHeaderInfo.propTypes = {
  message: PropTypes.string.isRequired,
  id: PropTypes.string,
};
