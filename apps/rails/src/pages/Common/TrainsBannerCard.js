import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import HTMLView from 'react-native-htmlview';
import isEmpty from 'lodash/isEmpty';
import {Actions} from '../../navigation';
import {Image,Text, TouchableOpacity, View} from 'react-native';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {trackHotelCrossSellEvent} from '../../railsAnalytics';
import {LISTING_PAGE_CONST} from '../../Utils/RailsConstant';
import {_label} from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';

import hotelImage from '@mmt/legacy-assets/src/hotelimage.webp';
const DUMMY_CALLBACK_URL = 'accounts.google.com';

export default class TrainsBannerCard extends PureComponent {
  UNSAFE_componentWillMount() {
    if (this.props.pageName === LISTING_PAGE_CONST) {
      trackHotelCrossSellEvent('mob_rail_listing', 'mob_rail_listing_banner_displayed');
    } else {
      trackHotelCrossSellEvent('mob_rail_review', 'mob_rail_review_banner_displayed');
    }
  }

  render() {
    const {bannerStyle, hotelCrossSellBannerText, cdnTncUrl} = this.props;
    return (
      <View style={bannerStyle} testID={this.props?.id}>
        <Card style={bannerStyles.cardStyle}>
          <View style={bannerStyles.bannerCardContainer}>

            <View style={bannerStyles.textContainer}>
              <HTMLView
                value={`<q>${hotelCrossSellBannerText}</q>`}
                stylesheet={{
                  ...descriptionStyles,
                  q : {
                    ...descriptionStyles.q,
                    ...fontStyle('light'),
                  },
                  b : {
                    ...descriptionStyles.b,
                    ...fontStyle('bold'),
                  },
                }}
              />
              {!isEmpty(cdnTncUrl) &&
              <TouchableOpacity
                onPress={() => _onTermsAndCOnditionsClicked(cdnTncUrl, this.props.pageName)}>
                <Text
                  style={[bannerStyles.viewTncText, bannerTextStyle.getViewTncTextFontStyle(), getLineHeight(14)]}
                  testID={`${this.props?.id}_knowMoreButton`}
                >
                  {_label('know_more', {sentenceCase : true})}
                </Text>
              </TouchableOpacity>
              }
            </View>

            <View style={bannerStyles.imageContainer}>
              <Image
                style={bannerStyles.imgStyle}
                source={hotelImage}
              />
            </View>

          </View>
        </Card>
      </View>
    );
  }
}

TrainsBannerCard.propTypes = {
  bannerStyle: PropTypes.object,
  hotelCrossSellBannerText: PropTypes.string.isRequired,
  cdnTncUrl: PropTypes.string,
  hotelsDeepLinkMobileApp: PropTypes.string,
  pageName: PropTypes.string,
  id: PropTypes.string,
};

TrainsBannerCard.defaultProps = {
  bannerStyle: {},
  cdnTncUrl: '',
  hotelsDeepLinkMobileApp: '',
  pageName: '',
};

const descriptionStyles = {
  q: {
    color: colors.black,
    fontSize: 14,
  },
  b: {
    color: colors.black,
    fontSize: 14,
  },
};

const callBackFunc = () => {
  Actions.pop();
};
const _onTermsAndCOnditionsClicked = (viewCdfTnc, pageName) => {
  if (pageName === LISTING_PAGE_CONST) {
    trackHotelCrossSellEvent('mob_rail_listing', 'mob_rail_listing_banner_tnc_clicked');
  } else {
    trackHotelCrossSellEvent('mob_rail_review', 'mob_rail_review_banner_tnc_clicked');
  }
  Actions.irctcWebViewPage({
    url: viewCdfTnc,
    headerText: 'Terms & Conditions',
    callbackUrl: DUMMY_CALLBACK_URL,
    callbackFunc: callBackFunc,
  });
};

export const bannerTextStyle = {
    getBannerTextFontStyle : () => {
        return fontStyle('regular');
    },
    getViewTncTextFontStyle : () => {
        return fontStyle('regular');
    },
};
export const bannerStyles = {
  bannerCardContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  textContainer: {
    paddingRight: 20,
    width: '70%',
  },
  bannerText: {
    flexWrap: 'wrap',
    fontSize: 14,
    letterSpacing: 0,
    color: colors.black,
  },
  viewTncText: {
    fontSize: 14,
    color: colors.azure,
    fontWeight: 'bold',
  },
  imageContainer: {
    right: 10,
  },
  bannerStyle: {
    marginHorizontal: 16,
    marginVertical: 16,
  },
  bannerMarginTop: {
    marginTop: 16,
  },
  cardStyle: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    backgroundColor: '#f0fdfc',
  },
  imgStyle: {
    width: 96,
    height: 48,
  },
};
