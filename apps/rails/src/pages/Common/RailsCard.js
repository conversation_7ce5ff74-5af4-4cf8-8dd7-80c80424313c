import React from 'react';
import {PixelRatio, StyleSheet, View} from 'react-native';
import PropTypes from 'prop-types';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';


class RailsCard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      style: this.getStyles(props.style),
    };
  }

  getStyles = (customStyle, elevation = this.props.elevation) => {
    const {showBorder} = this.props;
    const borderProps = showBorder ? {
      borderWidth: 1 / PixelRatio.getPixelSizeForLayoutSize(1),
      borderColor: '#CCC',
    } : {};
    return StyleSheet.flatten([{
      backgroundColor: colors.white,
      borderRadius: 2,
      ...borderProps,
      ...getPlatformElevation(elevation),
      ...customStyle,
    }]);
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({
      style: this.getStyles(nextProps.style, nextProps.elevation),
    });
  }

  render() {
    return (<View style={this.state.style} pointerEvents="auto">
      {this.props.children}
    </View>);
  }
}

RailsCard.propTypes = {
  style: PropTypes.object,
  children: PropTypes.node,
  elevation: PropTypes.number,
  showBorder: PropTypes.bool,
};


RailsCard.defaultProps = {
  style: {},
  children: [],
  elevation: 2,
  showBorder: false,
};

export default RailsCard;
