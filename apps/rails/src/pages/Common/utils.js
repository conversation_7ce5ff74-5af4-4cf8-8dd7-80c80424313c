import {
    getPokusConfig,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import AbConfigKeyMappings, { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { Platform} from 'react-native';

export const getAllTypesAdsAb = async () => {
    const platform = Platform.OS === 'web' ? 'adsOnRailFunnelWebs' : 'adsOnRailFunnelApps';
    // const adsOption = {multi_banner: 1, snackbar: 1,adfeed:1, interstitial: 0}
        const adsOption = getPokusConfig(
        PokusLobs.RAIL,
        AbConfigKeyMappings[platform],
        {multi_banner: 0, snackbar: 0,adfeed:0, interstitial: 0},
    );
    const adType = {multi_banner:'BANNER', snackbar:'SNACKBAR', interstitial:'INTERSTITIAL',adfeed:'ADFEED'};
    const payloadArr = [];
    let adKeys = Object.keys(adType);
    for (let i of adKeys){
        if (adsOption[i]){
            payloadArr.push(adType[i]);
        }
    }
    const trackingPayload = payloadArr.join('|');
    adsOption.trackingPayload = trackingPayload;
    return adsOption;
};

//Return 900 for 9.0.0 or 9.0.0_RC1
export const getSimplifiedAppVersion = (appVersion) => {
    try {
        return Number(appVersion?.replace(/([A-Z])\w+/g, '')?.replace(/\./g, ''));
    } catch (e){
        return appVersion;
    }
};
