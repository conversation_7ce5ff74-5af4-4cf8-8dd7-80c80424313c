import React, { useEffect, useRef } from 'react';
import { ViewStyle } from 'react-native';
import LottieView from 'lottie-react-native';

interface LottieViewProps {
  lottieJson: unknown;
  play: boolean;
  style?: ViewStyle;
  loop?: boolean | number;
}

export default function LottieWrapper(props: LottieViewProps) {
  const { lottieJson, play, style = {} } = props;
  const animationRef = useRef<LottieView>(null);

  useEffect(() => {
    if (play) {
      animationRef?.current?.play();
    } else {
      animationRef.current?.reset();
    }
  }, [play]);

  return (
    <LottieView
      ref={animationRef}
      source={lottieJson}
      speed={1}
      style={style}
      loop={false}
    />
  );
}
