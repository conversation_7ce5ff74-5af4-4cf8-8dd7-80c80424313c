import React from 'react';
import { StyleSheet, Text, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { _label } from 'apps/rails/src/vernacular/AppLanguage';
import { fontStyle } from 'apps/rails/src/vernacular/VernacularUtils';

interface NewTagProps {
  gradientStyle?: ViewStyle;
  textStyle?: ViewStyle;
  tagText?: string;
}

const NewTag = (props: NewTagProps) => {
  return (
    <LinearGradient
      colors={[colors.lightBlue, colors.darkBlue]}
      start={{ x: 0.0, y: 0.0 }}
      end={{ x: 1.0, y: 0.0 }}
      style={[styles.container, props?.gradientStyle]}
    >
      <Text style={[styles.newTagText, fontStyle('regular'), props?.textStyle]}>
        {props.tagText ?? _label('$new', { uppercase: true })}
      </Text>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
    container: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    right: 15,
    top: -8,
    paddingHorizontal: 10,
    paddingVertical: 2,
  },
  newTagText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: '700',
  },
});

export default NewTag;
