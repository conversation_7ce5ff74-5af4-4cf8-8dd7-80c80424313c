/* eslint-disable */
import React, {Component, createContext} from 'react';
import {
  BackHandler,
  Dimensions,
  FlatList,
  Text,
  View,
  TouchableWithoutFeedback,
  StyleSheet,
} from 'react-native';
import PropTypes from 'prop-types';
import debounce from 'lodash/debounce';
import isEmpty from 'lodash/isEmpty';
import travelerStyle from '../TravelerDetails/TravelerDetailsCSS';
import {dropDownStyles} from '../Traveller/Styles/DropDownStyles';
import styles,{textStyle} from '../User/Common/UserVerificationCSS';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {getStnNameConcatenated} from '../NewListing/Components/NewTrainInfo';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import fecha from 'fecha';
import {addDays} from '@mmt/legacy-commons/Helpers/dateHelpers';
import { _label } from '../../vernacular/AppLanguage';
import {getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos, isMweb } from '../../Utils/device';
import { TRAVELLER_CLICK_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/PdtCtRailsListing/PdtCtConstants';

// Create React Context for dropdown functionality
const DropdownContext = createContext({
   
  showDropDown: () => {},
   
  onOptionSelected: () => {},
   
  hideDropDown: () => {},
});

const style = StyleSheet.create({
  viewStyle: {
    marginVertical: 2,
    marginTop: 8,
    backgroundColor: colors.white,
  },
  overlayStyle: {
    marginVertical: 0,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  boardingContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  boardingStationContainer: {
    flex:1,
    marginRight: 4,
  },
  bsChange: {
    flex:2,
    alignItems: 'flex-end',
  },
});
class RailsDropDown extends Component {
  _captureRef = (ref) => {
    this._ref = ref;
  };

  render() {
    const {showDropDown, hideDropDown, onOptionSelected} = this.context || {};
    const {
       selected, onSelect, options, overlayMode, selectedTrainInfo,
      boardingStationList, selectedPickupStation, stationCode, changeLabel,
    } = this.props;
    let boardingTime;
    let boardingStation;
    let boardingDay;
    if (selectedPickupStation && boardingStationList && selectedTrainInfo &&
      selectedTrainInfo.departureDateAndTime && selectedTrainInfo.departureTime) {
      boardingStation = this.props.boardingStationList
      .find(station => (station.stationCode === selectedPickupStation.stationCode));
      boardingDay = fecha.format(addDays(selectedTrainInfo.departureDateAndTime, boardingStation.dayCount - boardingStationList[0].dayCount), 'D MMM');
      boardingTime = fecha.format(fecha.parse(boardingStation.departureTime, 'HH:mm'), 'h:mm A');
    }

    return (
      <View nativeID="rails_drop_down" ref={this._captureRef} testID="rails_dropdown_container">
        <View style={[style.viewStyle, overlayMode && style.overlayStyle]} testID="rails_dropdown_view">
        {!this.props.fromAddressCard &&
          <TouchableRipple onPress={() => {
            this.props.logTravellerBoardingStationChangeClick?.(
              TRAVELLER_CLICK_EVENTS.TRAVELLER_BOARDING_STATION_CHANGE_CLICK,
            );
            if (overlayMode) {
              return;
            }
            this._ref && this._ref.measureInWindow((x, y, w, h) => {
              const position = {
                x, y, w, h,
              };
              showDropDown(this.props, position, onSelect);
            });
          }}
          testID="boarding_station_touchable"
          >
            <View style={style.boardingContainer} testID="boarding_station_content">
              <View style={style.boardingStationContainer} testID="boarding_station_label_container">
                <Text style={railsDropDownStyle.latoBoldText12} testID="boarding_station_label">
                  {_label('boarding_station', { capitalize: true })}
                </Text>
              </View>
              <View testID="boarding_station_details">
                <Text style={[
                  railsDropDownStyle.latoRegularBlackText12,
                  textStyle.getLatoRegularTextFontStyle(),
                  { flex: 1 },
                  getLineHeight(12),
                ]} testID="boarding_station_name">
                  {getStnNameConcatenated(selected)}{', '}{stationCode}
                </Text>
                <Text style={[
                  railsDropDownStyle.latoRegularText12,
                  textStyle.getLatoRegularTextFontStyle(),
                  getLineHeight(12),
                ]} testID="boarding_station_time">
                  {`${boardingTime}, ${boardingDay}`}
                </Text>
              </View>
              <View style={style.bsChange} testID="boarding_station_action_container">
                <Text
                  style={[travelerStyle.latoBoldTextAzure14, textStyle.getLatoBoldTextFontStyle(), getLineHeight(14)]}
                  testID={`${this?.props?.id}_${overlayMode ? 'save' : 'change'}Button`}
                >
                  { overlayMode ? _label('save') : changeLabel }
                </Text>
              </View>
            </View>
          </TouchableRipple>
        }
        </View>
        {overlayMode &&
          <View
            style={{ maxHeight: 400, backgroundColor: colors.transparent }}
            testID={`${this.props?.id}_onChangeClicked`}
          >
            <FlatList
              data={options}
              style={{backgroundColor: colors.white, borderRadius: 4}}
              keyboardShouldPersistTaps="handled"
              keyExtractor={item => item.id}
              testID="boarding_station_options_list"
              renderItem={({item, index}) => (
                <TouchableWithoutFeedback
                  testID={`${this.props?.id}_boardingstations_${index}`}
                  onPress={(e) => {
                  if (e && isMweb()) {
                    e.preventDefault();
                  }
                  onOptionSelected(item);
                  hideDropDown();
                }}
                  underlayColor="white"
                  activeOpacity={0.5}
                >
                  <View key={item.id} style={dropDownStyles.dropDownItemStyle} testID={`boarding_station_option_container_${index}`}>
                    <Text style={
                      [styles.latoRegularText14, textStyle.getLatoRegularTextFontStyle(), getLineHeight(12)]} testID={`boarding_station_option_text_${index}`}>
                      {item.text}</Text>
                  </View>
                </TouchableWithoutFeedback>
            )}
            />
          </View>
        }
      </View>
    );
  }
}

// Set the context type for the class component
RailsDropDown.contextType = DropdownContext;
RailsDropDown.propTypes = {
  adjustPosition: PropTypes.bool,
  fixedPosition: PropTypes.object,
  overlayMode: PropTypes.bool,
  selected: PropTypes.string,
  boardingTime: PropTypes.string,
  boardingDay: PropTypes.string,
  stationCode: PropTypes.string,
  boardingStation: PropTypes.string,
  selectedPickupStation: PropTypes.object,
  boardingStationList: PropTypes.array,
  selectedTrainInfo: PropTypes.object,
  defaultText: PropTypes.string,
  changeLabel: PropTypes.string,
  onSelect: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  })).isRequired,
  fromAddressCard: PropTypes.bool,
  maxHeight: PropTypes.number,
  id: PropTypes.string,
  logTravellerBoardingStationChangeClick: PropTypes.func,
};
RailsDropDown.defaultProps = {
  adjustPosition: true,
  fixedPosition: null,
  overlayMode: false,
  selected: 'None',
  changeLabel: 'Change',
};
export default RailsDropDown;


export class RailsDropDownOverlayContainer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDropDown: false,
    };
    this._repositionDropdownDebounced = debounce(this._repositionDropdown, 300);
  }


  onBackPress = () => {
    if (this.state.showDropDown) {
      this._hideDropDown();
    }
    return true;
  };

  handleBackPress = () => {
    BackHandler && BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
  };

  getContextValue = () => ({
    showDropDown: (dropDownProps, position, onSelectHandler) => {
      this.setState({
        showDropDown: true,
        dropDownProps,
        position,
        onSelectHandler,
      });
      this.handleBackPress();
    },
    onOptionSelected: (option) => {
      this.state.onSelectHandler(option);
      this.props.logTravellerBoardingStationChangeClick?.(
      TRAVELLER_CLICK_EVENTS.TRAVELLER_BOARDING_STATION_CHANGE_SAVED,
    );
    },
    hideDropDown: this._hideDropDown,
  });

  _hideDropDown = (e) => {
    if (e && isMweb()) {
      e.preventDefault();
    }
    this.setState({
      showDropDown: false,
    });
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPress);
  };


  _repositionDropdown = (layout, position) => {
    this._height = Dimensions.get('screen').height;
    const diff = position.y + layout.height - this._height + 24;
    if (diff > 0) {
      this.setState({
        repositioning: true,
        position: {
          ...position,
          y: position.y - diff - 24,
        },
      });
    }
  };


  _onLayout = ({nativeEvent: {layout}}) => {
    if (!this.state.dropDownProps.adjustPosition) {return;}
    const {position} = this.state;
    this._repositionDropdown(layout, position);
  };


  render() {
    this._height = Dimensions.get('screen').height;
    const {position, dropDownProps, showDropDown} = this.state;
    const {style = {}} = this.props;
    let marginTop = 0;
    let maxHeight = 0;
    if (showDropDown && position) {
      const {adjustPosition, fixedPosition} = dropDownProps;
      if (isEmpty(fixedPosition)) {
        marginTop = isIos() ? position.y - 24 : position.y;
      } else {
        marginTop = fixedPosition.y;
      }
      maxHeight = (adjustPosition ? this._height : this._height - marginTop - 24);
    }
    if (isNaN(maxHeight)) {
      maxHeight = 0;
    }
    return (
      <DropdownContext.Provider value={this.getContextValue()}>
        <View style={[{ flex: 1 }, style]} testID="rails_dropdown_container">
          {this.props.children}
        {showDropDown &&
        <TouchableWithoutFeedback onPress={this._hideDropDown} testID="rails_dropdown_overlay_touchable">
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            ...getPlatformElevation(4),
            backgroundColor: colors.gradientgrey,
          }}
          testID="rails_dropdown_overlay_container"
          >
            <View
              onLayout={this._onLayout}
              style={{
                marginTop,
                marginLeft: position.x,
                width: position.w,
                maxHeight,
                paddingHorizontal: 20,
              }}
              testID="rails_dropdown_overlay_view"
            >
              <RailsDropDown {...dropDownProps} overlayMode maxHeight={maxHeight} />
            </View>
          </View>
        </TouchableWithoutFeedback>
        }
        </View>
      </DropdownContext.Provider>
    );
  }
}


const railsDropDownStyle = {
  latoRegularText12: {
    fontSize: 12,
    letterSpacing: 0,
    color: colors.lightTextColor,
  },
  latoRegularBlackText12: {
    fontSize: 12,
    letterSpacing: 0,
    color: colors.black04,
  },
  latoBoldText12: {
    fontSize: 12,
    letterSpacing: 0,
    color: colors.black,
  },
};

RailsDropDownOverlayContainer.propTypes = {
  style: PropTypes.any,
  children: PropTypes.node,
  id: PropTypes.string,
  logTravellerBoardingStationChangeClick: PropTypes.func,
};
