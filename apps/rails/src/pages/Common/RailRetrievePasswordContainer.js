import React from 'react';
import RailsRetrievePasswordModal from './RailsRetrievePasswordModal';
import PropTypes from 'prop-types';
import BottomSheetModal from '@mmt/legacy-commons/Common/Components/Modals/BottomSheetModal';

const RailRetrievePasswordContainer = (props) =>{
  const _dismissModal = () => {
    props.dismiss(false, '');
  };

  return (
    <BottomSheetModal key="retrievePasswordModal" onTouchOutside={() => props.dismiss(false, '')}>
      <RailsRetrievePasswordModal {...props} dismiss={_dismissModal} />
    </BottomSheetModal>
  );
};

export default RailRetrievePasswordContainer;

RailRetrievePasswordContainer.propTypes = {
  dismiss: PropTypes.func,
};
