import PropTypes from 'prop-types';
import React, {Component} from 'react';
import isEmpty from 'lodash/isEmpty';
import {Image, Text, View} from 'react-native';
import { Actions } from '../../navigation';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import {_label} from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import fetch2 from '../../fetch2';

import walletImage from '@mmt/legacy-assets/src/wallet_persuasion.webp';

const LOB = 'RAILS';

const promoAdminData = {
  url: 'https://hcs-promo.makemytrip.com/makemytrip/promov3/pwa/pwa/query/',
  authorizationKey: 'cHdhOjEyMzQ1Ng',
};

class WalletBanner extends Component {
  constructor(props) {
    super(props);
    this.state = {
      promoObject: null,
    };
  }
  async componentDidMount() {
    try {
      const res = await fetch2(`${promoAdminData.url}?lob=${LOB.toLowerCase()}&funnel=${this.props.funnel}`, {
        headers: {
          authorization: promoAdminData.authorizationKey,
        },
      });
      const response = await res.json();
      const promoList = response.intentSequences.default;
      if (!isEmpty(promoList)) {
        this.setState({
          promoObject: response.promoMessagesMap[promoList[0]],
        });
      }
    } catch (e) {
      console.log('error in wallet banner ', e?.errorCode);
    }
  }

  onBannerClicked = () => {
    const {promoObject: {url}} = this.state;
    if (url) {
      Actions.openWebView({
        url,
        headerText: _label('offer_details',{capitalize : true}),
        headerIcon: backIcon,
      });
    }
  };

  render() {
    if (!this.state.promoObject) {
      return null;
    }
    return (
      <View style={{ borderWidth: 0.5, borderColor: colors.lightGray, marginBottom: 10 }}>
        <TouchableRipple onPress={this.onBannerClicked}>
          <View style={{ backgroundColor: colors.white }} testID={this.props?.id}>
            <View style={bannerStyles.bannerCardContainer}>
              <View style={bannerStyles.imageContainer}>
                <Image source={walletImage} style={bannerStyles.imgStyle} />
              </View>
              <View style={bannerStyles.textContainer}>
                <Text style={[bannerStyles.headerText, fontStyle('light'), getLineHeight(20)]}>{this.state.promoObject.pTl}</Text>
                <Text style={[bannerStyles.subHeaderText, fontStyle('bold'), getLineHeight(14)]}>{this.state.promoObject.pTx}</Text>
              </View>
            </View>
          </View>
        </TouchableRipple>
      </View>
    );
  }
}

export default WalletBanner;

const bannerStyles = {
  bannerCardContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
  },
  textContainer: {
    justifyContent: 'center',
    paddingVertical: 16,
    flex: 1,
  },
  headerText: {
    fontSize: 20,
    color: colors.defaultTextColor,
    marginBottom: 4,
  },
  subHeaderText: {
    fontSize: 14,
    color: colors.black,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 20,
  },
  imgStyle: {
    width: 32,
    height: 32,
  },
};

WalletBanner.propTypes = {
  funnel: PropTypes.string.isRequired,
  id: PropTypes.string,
};
