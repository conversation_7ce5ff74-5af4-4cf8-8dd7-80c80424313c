/* eslint-disable */
import React, { FC, ReactNode, useEffect } from 'react';

import PropTypes from 'prop-types';
import {
  View,
  Platform,
  StyleSheet,
  TouchableWithoutFeedback,
  StyleProp,
  ViewStyle,
  GestureResponderEvent,
  BackHandler,
  Modal,
} from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { colors, statusBarBootomHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

interface Props {
  children: ReactNode;
  onTouchOutside?: () => void;
  hardwareBackButtonClose?: () => void;
  additionalContainerStyle?: StyleProp<ViewStyle>;
  shouldNotShowBlurScreen?: boolean;
  safeAreaViewFooterColor?: string;
  testID?: string;
}

const { OS } = Platform;

const BottomSheetModalTrain: FC<Props> = ({
  children,
  onTouchOutside,
  additionalContainerStyle = {},
  hardwareBackButtonClose,
  shouldNotShowBlurScreen,
  safeAreaViewFooterColor = colors.white,
  testID = 'bottom_sheet_modal',
}) => {
  const onPressHandler = (e: GestureResponderEvent) => {
    if (OS === 'web') {
      e.preventDefault();
    }

    onTouchOutside && onTouchOutside();
  };

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (hardwareBackButtonClose) {
        hardwareBackButtonClose();
        return true;
      }

      return false;
    });

    return () => backHandler.remove();
  }, []);

  return (
    <View style={[styles.container, AtomicCss.flex1, additionalContainerStyle]} testID={`bottom_sheet_modal_container_${testID}`}>
      <Modal transparent visible={true} testID={`bottom_sheet_modal_${testID}`}>
      {!shouldNotShowBlurScreen && (
        <TouchableWithoutFeedback onPress={onPressHandler} testID={`modal_background_${testID}`}>
          <View style={AtomicCss.flex1} testID={`modal_background_view_${testID}`} />
        </TouchableWithoutFeedback>
      )}
        {children}
      <View style={[styles.footer, { backgroundColor: safeAreaViewFooterColor }]} testID={`bottom_sheet_modal_footer_${testID}`} />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.modalgrey,
    ...getPlatformElevation(OS === 'ios' ? 100 : 1000),
    zIndex: 9999,
  },
  footer: {
    height: statusBarBootomHeightForIphone,
  },
});

BottomSheetModalTrain.propTypes = {
  onTouchOutside: PropTypes.func,
  children: PropTypes.element.isRequired,
  additionalContainerStyle: PropTypes.object,
};

BottomSheetModalTrain.defaultProps = {
  onTouchOutside: () => {},
  additionalContainerStyle: {},
};

export default BottomSheetModalTrain;
