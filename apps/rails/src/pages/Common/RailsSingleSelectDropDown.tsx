import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import React, { FC, ReactElement, useRef, useState } from 'react';
import { FlatList, Image, Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import arrowDown from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';
interface Props {
  label: string;
  data: Array<{ label: string; value: string }>;
  onSelect: (item: { label: string; value: string }) => void;
  showValue?: boolean;
}

const RailsSingleSelectDropDown: FC<Props> = ({ label, data, onSelect, showValue }) => {
  const DropdownButton = useRef();
  const [visible, setVisible] = useState(false);
  const [selected, setSelected] = useState(undefined);
  const [dropdownTop, setDropdownTop] = useState(0);

  const toggleDropdown = (): void => {
    visible ? setVisible(false) : openDropdown();
  };

  const openDropdown = (): void => {
    DropdownButton?.current?.measure((_fx, _fy, _w, h, _px, py) => {
      setDropdownTop(py + h);
    });
    setVisible(true);
  };

  const onItemPress = (item): void => {
    setSelected(item);
    onSelect(item);
    setVisible(false);
  };

  const renderItem = ({ item }): ReactElement<unknown, unknown> => (
    <TouchableOpacity style={styles.item} onPress={() => onItemPress(item)}>
      <Text style={styles.itemRow}>{item.label}</Text>
      {showValue && <Text>{item.value}</Text>}
    </TouchableOpacity>
  );

  const renderDropdown = (): ReactElement<unknown, unknown> => {
    return (
      <Modal visible={visible} transparent animationType="none">
        <TouchableOpacity style={styles.overlay} onPress={() => setVisible(false)}>
          <View style={[styles.dropdown, { top: dropdownTop }]}>
            <FlatList
              data={data}
              renderItem={renderItem}
              keyExtractor={(item, index) => index.toString()}
              contentContainerStyle={styles.flatList}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  return (
    <TouchableOpacity ref={DropdownButton} style={styles.button} onPress={toggleDropdown}>
      {renderDropdown()}
      <Text style={styles.buttonText}>{(selected && selected.label) || label}</Text>
      <Image style={styles.arrowDownIcon} source={arrowDown} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    zIndex: 1,
    borderColor: colors.borderGray,
    borderWidth: 1,
    borderRadius: 8,
  },
  buttonText: {
    flex: 1,
    marginLeft: 16,
  },
  dropdown: {
    position: 'absolute',
    backgroundColor: colors.white,
    width: '100%',
    shadowColor: colors.black,
    shadowRadius: 4,
    shadowOffset: { height: 4, width: 0 },
    shadowOpacity: 0.5,
  },
  overlay: {
    width: '100%',
    height: '100%',
  },
  flatList: {
    marginTop: 2,
    backgroundColor: colors.grey3,
    marginHorizontal: 24,
    borderRadius: 4,
  },
  item: {
    marginHorizontal: 24,
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderColor: colors.dividerGrey,
  },
  arrowDownIcon: {
    width: 30,
    height: 30,
  },
  itemRow: {
    color: colors.black,
    fontSize: 14,
    fontWeight: '400',
  },
});

export default RailsSingleSelectDropDown;
