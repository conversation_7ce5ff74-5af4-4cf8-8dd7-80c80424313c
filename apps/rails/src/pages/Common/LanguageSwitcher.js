import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { connect } from 'react-redux';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple.ios';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getRailsLanguageByKey, shouldShowHindiTooltip, disableVernacular } from '../../vernacular/AppLanguage';
import changeLangIcon from '@mmt/legacy-assets/src/LanguageSwitcher.webp'; import get from 'lodash/get';
import { updateRailsLang } from '../../vernacular/vernacularActions';
import ToolTipCustomizable from './ToolTipCustomizable';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import {trackLanguageSwitch} from '../../vernacular/tracking';
import PropTypes from 'prop-types';

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    marginVertical: 4,
    backgroundColor: colors.white,
  },
  changeLanguageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 5,
    zIndex: 10,
    elevation: 10,
    backgroundColor: colors.lighterBlue,
    borderRadius: 5,
    height: 50,
    marginVertical: 4,
    marginRight: 10,
    width: 45,
  },
  fullWidthIcon: {
    flexDirection: 'row',
    width: 70,
    height: 30,
    paddingHorizontal: 5,
    paddingVertical: 8,
    borderRadius: 20,
  },
  changeLanguageIcon: {
    height: 18,
    width: 23,
    marginRight: 3,
    marginBottom: 5,
  },
  changeLanguageLabel: {
    lineHeight: 12,
    fontSize: 12,
    padding: 2,
    color: colors.azure,
  },
  railsHindiToolTipContentStyle : {
    paddingHorizontal : 10,
    marginHorizontal : 'auto',
    marginVertical: 'auto',
  },
  railsHindiToolTipTextStyle : {
    fontSize : 12,
    color : colors.white,
  },
  railsHindiToolTipTipStyle : {
    marginLeft : 'auto',
    marginRight : 'auto',
  },
});


function LanguageSwitcher({
  fullWidthIcon,
  lang,
  toggleLanguge,
  showHindiTooltip,
  isSeo = false,
  pageId,
}) {
  const [showTooltip, setTooltipVisibility] = React.useState(true);

  const hideTooltip = () => setTooltipVisibility(false);

  if (disableVernacular()) {
    return null;
  }

  return (
    <>
      <TouchableRipple
        onPress={() => {
          toggleLanguge(pageId, lang);
        }}
        style={StyleSheet.flatten(styles.button)}>
        <View style={[
          styles.changeLanguageContainer,
          fullWidthIcon && styles.fullWidthIcon,
        ]}>
          <Image style={styles.changeLanguageIcon} source={changeLangIcon} />
          <Text style={[styles.changeLanguageLabel, fontStyle('bold'), getLineHeight(13)]}>
            {getRailsLanguageByKey('altLabel')}
          </Text>
        </View>
      </TouchableRipple>
      {
        showTooltip && showHindiTooltip &&  shouldShowHindiTooltip(isSeo) &&
        <ToolTipCustomizable
          style={{
            position: 'absolute',
            width: 130,
            top: 50,
          }}
          orientation={'top'}
          content="अब हिन्दी में भी उपलब्ध"
          contentStyle={styles.railsHindiToolTipContentStyle}
          textStyle={styles.railsHindiToolTipTextStyle}
          tipStyle={styles.railsHindiToolTipTipStyle}
          dismiss
          hideFunction={hideTooltip}
         />
      }
    </>
  );
}

const mapStateToProps = (state) => {
  const lang = get(state, 'railsVernacular.lang', '');
  return {
    lang,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleLanguge: (pageName, fromLang) => {
      const toLang = getRailsLanguageByKey('altLang');
      trackLanguageSwitch({pageName, fromLang, toLang});
      dispatch(updateRailsLang(toLang));
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(LanguageSwitcher);

LanguageSwitcher.propTypes = {
  fullWidthIcon: PropTypes.bool,
  lang: PropTypes.string,
  toggleLanguge: PropTypes.func,
  showHindiTooltip: PropTypes.bool,
  isSeo: PropTypes.bool,
  pageId: PropTypes.string,
};
