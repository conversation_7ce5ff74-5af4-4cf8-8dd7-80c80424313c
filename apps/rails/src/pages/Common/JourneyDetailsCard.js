/* eslint-disable */
import React, {useState, useEffect} from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import {StyleSheet, Text, View, Image, ActivityIndicator} from 'react-native';
import { NewTrainInfo} from '../NewListing/Components/NewTrainInfo';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {getColorBasedOnAvailabilityType} from '../NewListing/Components/AvailabilityCard';
import AvailabilityStatusValues from '../Types/AvailabilityStatusValues';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { trackEvar22Event } from 'apps/rails/src/railsAnalytics';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

import {REVIEW_PAGE_CONST, TRAVELERS_PAGE_TRACKING_KEY_NEW} from '../../Utils/RailsConstant';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {getClassType} from '../Types/ClassType';
import {availabilityTypeColors} from '../../Utils/paymentUtils';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import { TATKAL_ALERT_STATUS } from '../ScheduleTatkalTravelerPage/constants/ScheduleTatkalTravelerPageConstants';
import LinearGradient from 'react-native-linear-gradient';
import { showAvailabilityToast } from '../../RailsAbConfig';
import { TRAVELLER_AVL_TOAST } from '../../RailsAbConstants';

const trainHeader = StyleSheet.create({
  container: {flexDirection: 'column', marginTop: 10},
  name: {
    fontSize: 16, color: colors.black, marginBottom: 2,
  },
  number: {
    fontSize: 16, color: colors.lightTextColor, marginBottom: 6,
  },
  selectedClass: {
    fontSize: 12, color: colors.black, marginBottom: 6,
  },
  availabilityStatus: {
    fontSize: 12,  color: colors.black, marginBottom: 6,
  },
  quota: {fontSize: 12,  color: colors.lightTextColor},
  separator: {
    height: 0.5,
    borderColor: colors.lightGrey,
    borderWidth: 0.5,
    marginTop: 12,
  },
  lastUpdated: {
    fontSize: 12,
    color: colors.lightTextColor,
  },
  availabilityStatusCard: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tatkalAlertText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: '700',
  },
  tatkalAlertCard: {
    backgroundColor: colors.goldenYellow14,
    borderRadius: 8,
    marginTop: 3,
    paddingHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const toShowQuota = (quota) => {
  const quotaToShow = ['TQ', 'LD'];
  let toShow = false;
  quotaToShow.forEach((value) => {
    if (value === quota.code) { toShow = true; }
  });
  return toShow;
};

const getAvailabilityTextColor = (availabilityText, availabilityType) => {
  if (!availabilityText) {return colors.black;}

  if (availabilityType === '1' || availabilityType === '2') {
    return colors.lightGreen16;
  } else if (availabilityType === AvailabilityStatusValues.NO_ROOM.value
    || availabilityType === AvailabilityStatusValues.NT.value
    || availabilityType?.startsWith(AvailabilityStatusValues.DEPARTED.value)) {
    return colors.lightTextColor;
  } else {
    return colors.goldenYellow18;
  }
};

export const TrainNameAndNumber = (props) => {
  props = {
    ...props,
    trainName: typeof props.trainName === "undefined" ? '' : props.trainName,
    trainNumber: typeof props.trainNumber === "undefined" ? '' : props.trainNumber
  };

  const {
    trainName,
    trainNumber,
    id,
  } = props;

  return (
    <View style={trainHeader.container}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}} testID={`${id}_trainNameAndNumber`}>
        <Text style={[trainHeader.name, fontStyle('black'), getLineHeight(16)]} testID={`${id}_trainName`}>{getTrainName(trainName.toLowerCase())}</Text>
        <Text style={[trainHeader.number, fontStyle('regular'), getLineHeight(16)]} testID={`${id}_trainNumber`}>{`#${trainNumber}`}</Text>
      </View>
    </View>
  );
};

TrainNameAndNumber.propTypes = {
  trainName: PropTypes.string,
  trainNumber: PropTypes.string,
  id: PropTypes.string,
};

export const TrainBookingInfo = (props) => {
  const {
    selectedQuota,
    classValue,
    selectedAvlStatus,
    availabilityObject,
    selectedTrainInfo,
    page,
    id,
    isUsingUpdatedAvailability
  } = props;
  
  const { showSpinner, currentStatus } = useAvailabilityTransition(selectedAvlStatus, isUsingUpdatedAvailability, selectedTrainInfo);
  let availabilityStyle;
  if (!isEmpty(availabilityObject)) {
    availabilityStyle = getColorBasedOnAvailabilityType(availabilityObject.availablityType);
  }
  let lastUpdatedOn = '';

  if (selectedTrainInfo && selectedTrainInfo.tbsAvailability) {
    const selectedTrainAvailibility = selectedTrainInfo.tbsAvailability
    .filter(x => x.classType === classValue.code && x.quota === selectedQuota.id);
    if (selectedTrainAvailibility && selectedTrainAvailibility.length > 0) {
      lastUpdatedOn = selectedTrainAvailibility[0].lastUpdatedOn;
    }
  }
  if (page === REVIEW_PAGE_CONST) {
    lastUpdatedOn = _label('updated_just_now');
  }
  if (selectedTrainInfo && selectedTrainInfo.lastUpdatedOn) {
    lastUpdatedOn = selectedTrainInfo.lastUpdatedOn;
  }

  return (
    <View>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}} testID={`${id}_trainBookingInfo`}>
        <Text style={[trainHeader.selectedClass, fontStyle('bold'), getLineHeight(12)]} testID={`${id}_selectedClass`}>{_label(classValue.value, { uppercase: true })}</Text>
        { selectedAvlStatus === TATKAL_ALERT_STATUS ?
        <TatkalAlertCard/> :
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {(() => {
            return showSpinner ? (
              <ActivityIndicator size="small" color="#757575" style={{marginTop: 4}} />
            ) : (
              <Text style={[trainHeader.availabilityStatus, fontStyle("bold"), {color: getAvailabilityTextColor(currentStatus, availabilityObject?.availablityType), marginTop: 4}, getLineHeight(12)]} testID={`${id}_availabilityStatus`}>{currentStatus}</Text>
            );
          })()}
        </View>
        }
      </View>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <Text style={[trainHeader.quota, fontStyle('regular'), getLineHeight(12)]} testID={`${id}_quota`}>{ _label(selectedQuota.text) }</Text>
        <Text style={[trainHeader.quota, fontStyle('regular'), getLineHeight(12)]} testID={`${id}_lastUpdatedOn`}>{lastUpdatedOn}</Text>
      </View>
    </View>
  );
};

export const ClassAndAvailInfo = (props) => {
  const {
    journeyClass,
    lastUpdatedOn,
    seatsAvailability: selectedAvlStatus,
    availabilityType,
    prettyPrintingAvailabilityStatus,
    totalFare: price,
  } = props;

  let availabilityStyle;
  /* eslint-disable */
  if (!isEmpty(availabilityType)) {
    availabilityStyle = getColorBasedOnAvailabilityType(availabilityType);
  }
  const availabilityColorCode = (availabilityTypeColors[availabilityType] || '#000000');

  return (
    <View>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <View>
          <Text style={[trainHeader.selectedClass, fontStyle('bold'), getLineHeight(12)]}>{_label(getClassType(journeyClass).value, { uppercase: true })}</Text>
          { selectedAvlStatus &&
          <Text style={[trainHeader.availabilityStatus, fontStyle("bold"),
            {color: getAvailabilityTextColor(prettyPrintingAvailabilityStatus || selectedAvlStatus, availabilityType), marginTop: 4}, getLineHeight(12)]}>{prettyPrintingAvailabilityStatus || selectedAvlStatus}</Text>
          }
        </View>
        <View style={{alignItems: 'flex-end'}}>
          <Text style={{fontSize: 16, color: colors.black, ...fontStyle('black'), ...getLineHeight(16)}}>₹{price}</Text>
        </View>
      </View>
      <View><Text style={[trainHeader.lastUpdated, fontStyle('regular'), getLineHeight(12)]}>{lastUpdatedOn || '' }</Text></View>
    </View>
  );
};

export const getTrainName = (trainName) => {
  const exp = trainName.split(' ');
  let key = '';
  exp.forEach((ch) => {
    key += `${ch.charAt(0).toUpperCase() + ch.slice(1)} `;
  });
  return key;
};

const JourneyInfo = ({trainData,id}) => (
  <NewTrainInfo style={{marginBottom: 20}} trainData={trainData} id={id} />
);

const styles = StyleSheet.create({
  departText: {
    fontSize: 14, fontWeight: 'bold',  color: colors.black
  },
  duration: {
    fontSize: 12,  color: colors.defaultTextColor, marginBottom: 10
  },
  durationWithoutMargin: {
    fontSize: 12,  color: colors.defaultTextColor
  },
  arrivalText: {
    fontSize: 14, fontWeight: 'bold', color: colors.black
  },
  circle: {
    height: 6, width: 6, borderWidth: StyleSheet.hairlineWidth * 1, borderRadius: 90, borderColor: colors.lightTextColor
  },
  line: {
    flex: 1, borderWidth: StyleSheet.hairlineWidth * 1, alignSelf: 'center', borderColor: colors.lightTextColor
  },
  frmStnCode: {
    fontSize: 14, fontWeight: 'bold', color: colors.black
  },
  frmStnName: {
    fontSize: 12, marginBottom: 10, color: colors.defaultTextColor
  },
  trainStops: {fontSize: 12, color: colors.defaultTextColor, marginBottom: 10},
  toStnCode: {
    fontSize: 14, fontWeight: 'bold',  color: colors.black
  },
  toStnName: {fontSize: 12, color: colors.defaultTextColor}
});

const useAvailabilityTransition = (selectedAvlStatus, isUsingUpdatedAvailability, selectedTrainInfo) => {
  const [displayState, setDisplayState] = useState({
    showSpinner: false,
    currentStatus: selectedAvlStatus,
    previousIsUsingUpdated: isUsingUpdatedAvailability,
    previousStatus: selectedAvlStatus
  });

  useEffect(() => {
    const justSwitchedToUpdated = isUsingUpdatedAvailability && !displayState.previousIsUsingUpdated;
    const isCnfOptionTrain = selectedTrainInfo?.boardingStation && selectedTrainInfo?.droppingStation;
    if (displayState.currentStatus !== selectedAvlStatus && isUsingUpdatedAvailability) {
        const prevStatus = displayState.currentStatus?.toLowerCase() || '';
        const newStatus = selectedAvlStatus?.toLowerCase() || '';
        
        let toastMessage = '';
        
        if (isCnfOptionTrain && showAvailabilityToast() === TRAVELLER_AVL_TOAST.SHOWN) {
        if (prevStatus.includes('available') && newStatus.includes('rac')) {
          toastMessage = _label('toast_text_avl_to_rac');
        } else if (prevStatus.includes('available') && newStatus.includes('wl')) {
          toastMessage = _label('toast_text_avl_to_wl');
        } else if (prevStatus.includes('rac') && newStatus.includes('wl')) {
          toastMessage = _label('toast_text_rac_to_wl');
        }
        }
        if (toastMessage) {
        showShortToast(toastMessage);
        trackEvar22Event(TRAVELERS_PAGE_TRACKING_KEY_NEW, `TRAINS:Traveller_${toastMessage}`);
        trackClickEventProp61(TRAVELERS_PAGE_TRACKING_KEY_NEW, `mob_rail_travellers_${toastMessage}`);
      }
    }

    if (justSwitchedToUpdated) {
      setDisplayState(prev => ({ 
        ...prev, 
        showSpinner: true, 
        previousIsUsingUpdated: isUsingUpdatedAvailability,
        previousStatus: prev.currentStatus
      }));
      setTimeout(() => {
        setDisplayState(prev => ({
          ...prev,
          showSpinner: false,
          currentStatus: selectedAvlStatus
        }));
      }, 1000);
    } else {
      setDisplayState(prev => ({
        ...prev,
        showSpinner: false,
        currentStatus: selectedAvlStatus,
        previousIsUsingUpdated: isUsingUpdatedAvailability,
        previousStatus: prev.currentStatus
      }));
    }
  }, [selectedAvlStatus, isUsingUpdatedAvailability]);

  return displayState;
};

const TatkalAlertCard = () => (
  <LinearGradient
    style={trainHeader.tatkalAlertCard}
    colors={[colors.goldenYellow14, colors.goldenYellow15]}
    start={{ x: 0, y: 0 }}
    end={{ x: 0, y: 1 }}
  >
    <Text style={trainHeader.tatkalAlertText}>
      {TATKAL_ALERT_STATUS}
    </Text>
  </LinearGradient>
);

export const JourneyDetailsCard = (props) => {
  const {
    trainData, classValue, selectedQuota, selectedAvlStatus, boardingStationList, showBoardingStationChange, onAvailabilityLayout,
    availabilityObject, selectedTrainInfo, page, id, isUsingUpdatedAvailability
  } = props;
  const [viewDetails, toggleViewDetails] = useState(false);
  const { showSpinner, currentStatus } = useAvailabilityTransition(selectedAvlStatus, isUsingUpdatedAvailability, selectedTrainInfo);
  return (
    <View style={{ marginBottom: 10 }} testID={id}>
      { trainData &&
        <TrainNameAndNumber
          trainName={trainData.trainName}
          trainNumber={trainData.trainNumber}
          id={id}
        />
      }
      { viewDetails && classValue && selectedQuota && selectedAvlStatus &&
      <TrainBookingInfo
        classValue={classValue}
        selectedQuota={selectedQuota}
        selectedTrainInfo={selectedTrainInfo}
        selectedAvlStatus={selectedAvlStatus}
        showBoardingStationChange={showBoardingStationChange}
        availabilityObject={availabilityObject}
        page={page}
        id={id}
        isUsingUpdatedAvailability={isUsingUpdatedAvailability}
      />
      }
      { viewDetails &&
      <View style={{marginTop: 12, marginBottom: 20}} onLayout={onAvailabilityLayout}>
        <JourneyInfo trainData={trainData} boardingStationList={boardingStationList} id={id}/>
      </View> }
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <View>
          {
            !viewDetails && classValue && selectedQuota && selectedAvlStatus &&
            <View style={trainHeader.availabilityStatusCard}>
              <Text style={{...fontStyle('regular'), fontSize: 12, color: colors.black, paddingTop: 3, ...getLineHeight(12) }}>
                { `${_label(classValue.value, { uppercase: true })}  |  `}
                </Text>
                { selectedAvlStatus === TATKAL_ALERT_STATUS ?
                <TatkalAlertCard/> :
                <View style={{flexDirection: 'row', alignItems: 'center'}} testID={`${id}_availabilityStatus`}>
                  {(() => {
                    return showSpinner ? (
                      <ActivityIndicator size="small" color="#757575" style={{marginTop: 4}} />
                    ) : (
                      <Text style={{color: getAvailabilityTextColor(currentStatus, availabilityObject?.availablityType), marginTop: 4, ...fontStyle("bold"), fontSize: 12, ...getLineHeight(12)}}>
                        {currentStatus}
                      </Text>
                    );
                  })()}
                </View>
                }
                <Text>
                { toShowQuota(selectedQuota) && `  |  ${_label(selectedQuota.text)}`}
              </Text>
            </View>
          }
        </View>
        <TouchableRipple onPress={() => toggleViewDetails(!viewDetails)} testID={`${props?.id}_viewDetailsButton`}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{...fontStyle('bold'), color: colors.azure, ...getLineHeight(12)}}>
              {viewDetails ? _label('hide_details') : _label('view_details')}
            </Text>
            <Image
              source={require('@mmt/legacy-assets/src/ic_arrow_blue_down.webp')}
              style={[{width: 20, height: 20}, viewDetails ? {transform: [{rotate: '180deg'}]} : {}]}
            />
          </View>
        </TouchableRipple>
      </View>
      <View style={trainHeader.separator} />
    </View>
  );
};

JourneyDetailsCard.propTypes = {
  trainData: PropTypes.shape({
    trainName: PropTypes.string,
    trainNumber: PropTypes.string,
  }),
  classValue: PropTypes.shape({
    value: PropTypes.string,
    code: PropTypes.string,
  }),
  selectedQuota: PropTypes.shape({
    text: PropTypes.string,
    id: PropTypes.string,
  }),
  availabilityObject: PropTypes.shape({
    prettyPrintingAvailablityStatus: PropTypes.string,
    availablityType: PropTypes.string,
  }),
  selectedAvlStatus: PropTypes.string,
  boardingStationList: PropTypes.array,
  showBoardingStationChange: PropTypes.bool,
  onAvailabilityLayout: PropTypes.func,
  selectedTrainInfo: PropTypes.shape({
    tbsAvailability: PropTypes.arrayOf(
      PropTypes.shape({
        classType: PropTypes.string,
        quota: PropTypes.string,
        lastUpdatedOn: PropTypes.string,
      }),
    ),
    lastUpdatedOn: PropTypes.string,
  }),
  page: PropTypes.string,
  id: PropTypes.string,
};

ClassAndAvailInfo.propTypes = {
  journeyClass: PropTypes.string,
  lastUpdatedOn: PropTypes.string,
  seatsAvailability: PropTypes.string,
  availabilityType: PropTypes.string,
  prettyPrintingAvailabilityStatus: PropTypes.string,
  totalFare: PropTypes.number,
};

TrainBookingInfo.propTypes = {
  selectedQuota: PropTypes.shape({
    text: PropTypes.string,
    id: PropTypes.string,
  }),
  classValue: PropTypes.shape({
    value: PropTypes.string,
    code: PropTypes.string,
  }),
  selectedAvlStatus: PropTypes.string,
  availabilityObject: PropTypes.shape({
    prettyPrintingAvailablityStatus: PropTypes.string,
    availablityType: PropTypes.string,
  }),
  selectedTrainInfo: PropTypes.shape({
    tbsAvailability: PropTypes.arrayOf(
      PropTypes.shape({
        classType: PropTypes.string,
        quota: PropTypes.string,
        lastUpdatedOn: PropTypes.string,
      }),
    ),
    lastUpdatedOn: PropTypes.string,
  }),
  page: PropTypes.string,
  id: PropTypes.string,
};

JourneyInfo.propTypes = {
  trainData: PropTypes.object,
  id: PropTypes.string,
};


