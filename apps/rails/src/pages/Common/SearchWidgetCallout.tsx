import React from 'react';
import { Text, StyleSheet, Image, TouchableOpacity, View } from 'react-native';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

interface IconProps {
  url: string;
  altText: string;
}

interface SearchWidgetCalloutProps {
  data: {
    type: string;
    enabled: boolean;
    key: string;
    title: string;
    icon: IconProps;
    textColor: string;
    bgColor: string;
  };
  onPress?: () => void;
  testID?: string;
}

const SearchWidgetCallout: React.FC<SearchWidgetCalloutProps> = ({ 
  data,
  onPress,
  testID 
}) => {
  if (!data || !data.enabled) {
    return null;
  }

  return (
    <View style={styles.wrapper}>
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.8}
      testID={testID}
    >
      <View style={[styles.container, { backgroundColor: data.bgColor }]}>
          {data.icon && data.icon.url && data.icon.url !== 'cdnLink' && (
            <Image
              source={{ uri: data.icon.url }}
              style={styles.icon}
              accessibilityLabel={data.icon.altText || 'Callout icon'}
            />
          )}
          <Text style={[styles.title, { color: data.textColor }]} numberOfLines={2}>
            {data.title}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.white,
    zIndex: 1000,
  },
  container: {
    paddingHorizontal: 8,
    paddingVertical: 8,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 13,
    marginBottom: -5,
    borderRadius: 8,
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 8,
    resizeMode: 'contain',
  },
  title: {
    flex: 1,
    fontSize: 13,
    fontWeight: '400',
    fontFamily: 'Lato',
  },
});

export default SearchWidgetCallout;