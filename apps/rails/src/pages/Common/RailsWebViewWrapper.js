import React, {Component} from 'react';
import { View, BackHandler} from 'react-native';
import {WebView} from 'react-native-webview';
import PropTypes from 'prop-types';
import { Actions } from '../../navigation';
import CommonHeader from '@mmt/legacy-commons/Common/Components/Header/CommonHeader';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { isIos } from '../../Utils/device';

/**
 * generic class for opening web view page in react native
 * Parameters needed : url, header text,header icon
 */
class WebViewWrapper extends Component {
    static navigationOptions = {
        header: null,
    };

    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.onBackPress.bind(this);
        this.onEventReceived = this.onEventReceived.bind(this);
        this.state = {
            canGoBack: false,
        };
        this.timer = null;
    }

    UNSAFE_componentWillMount() {
        BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
        this.timer = setTimeout(() => {
            this.setState({invalidate: true});
        }, 300);
    }

    componentWillUnmount() {
        if (this.timer) {
            clearTimeout(this.timer);
        }
        BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
    }

    render() {
        const uri = this.props.url;
        return (
            <View style={{
                flex: 1,
                flexDirection: 'column',
            }}>
                <CommonHeader
                    headerText={this.props.headerText}
                    imgSrc={this.props.headerIcon}
                    whiteHeader={this.props.whiteHeader}
                    backPressHandler={this.onBackPress}
                />
                <WebView
                    style={{
                        flex: 1,
                        flexGrow: 1,
                    }}
                    ref={e => this._webView = e}
                    source={{
                        uri,
                        headers: this.props.headers,
                    }}
                    onNavigationStateChange={this.onNavigationStateChange}
                    startInLoadingState
                    sharedCookiesEnabled={this.props.sharedCookiesEnabled}
                    injectedJavaScript={this.props.injectedJavaScript || null}
                    onMessage={(event => this.onEventReceived(event))}
                />
            </View>
        );
    }

    onNavigationStateChange = (navState) => {
        this.setState({
            canGoBack: navState.canGoBack,
        });
    };
    //To-Do check the need for this method.
    //Added to stop getting bind error
    onEventReceived = (e)=>{
        console.log(e);
    };
    onBackPress = () => {
        if (this.props.navigation && this.props.navigation.canGoBack && this.props.navigation.canGoBack()) {
            this.props.navigation.goBack();
            return true;
        }
        if (this.state.canGoBack) {
            this._webView.goBack();
            return true;
        }
        const somethingPoped = Actions.pop();
        if (!somethingPoped) {
            if (isIos()) {
                ViewControllerModule.popViewController(this.props.rootTag);
            } else {
                BackHandler.exitApp();
            }
        }
        return true;
    };
}

WebViewWrapper.propTypes = {
    url: PropTypes.string.isRequired,
    headerText: PropTypes.string.isRequired,
    headerIcon: PropTypes.number.isRequired,
    whiteHeader: PropTypes.bool,
    headers: PropTypes.object,
    sharedCookiesEnabled: PropTypes.bool,
    injectedJavaScript: PropTypes.string,
    navigation: PropTypes.shape({
        canGoBack: PropTypes.func,
        goBack: PropTypes.func,
    }),
    rootTag: PropTypes.number,
};

WebViewWrapper.defaultProps = {
    whiteHeader: false,
    headers: {},
    sharedCookiesEnabled: false,
};

export default WebViewWrapper;
