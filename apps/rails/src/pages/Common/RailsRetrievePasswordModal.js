import React from 'react';
import {
  ActivityIndicator,
  Image,
  Keyboard,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { Actions } from '../../navigation';
import { connect } from 'react-redux';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import PropTypes from 'prop-types';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  JSON_FIELD,
  retrievePassword,
  STATUS_API_SUCCESS,
  STATUS_LIMIT_EXCEEDED,
} from '../../Utils/UserRepository';
import { trackPasswordReminderPageEvent, trackRequestNewPasswordEvent } from '../../railsAnalytics';
import isEmpty from 'lodash/isEmpty';
import LinearGradient from 'react-native-linear-gradient';
import { isValidEmail } from '@mmt/legacy-commons/Helpers/validationHelpers';
import { trackClickEventProp61 } from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import { _label } from '../../vernacular/AppLanguage';
import { fontStyle, checkEnglishKeyboard, getLineHeight } from '../../vernacular/VernacularUtils';
import { isIos, isMweb } from '../../Utils/device';
import { config } from '../NeedHelp/Shared';
import get from 'lodash/get';
import ASSETS from '../../Utils/Assets/RailsAssets';
import { GFT_PDT_EVENTS } from '../../PdtAnalytics/PdtAnalyticsV2/GFT/PdtGftConstants';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';
import InputField from '@Frontend_Ui_Lib_App/InputField';

import closeIcon from '@mmt/legacy-assets/src/ic_headerclose_grey.webp';

class RailsRetrievePasswordModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      inputData: this.props.mobile,
      enableCTA: false,
      isLoading: false,
      pwdRequested: false,
      mobileChecked: true,
      error: '',
    };
    this._isIphone = isIos();
  }

  _onChange = (input) => {
    let enableCTA = false;
    const { mobileChecked } = this.state;
    if (mobileChecked && input.length === 10) {
      enableCTA = true;
    } else if (!mobileChecked && isValidEmail(input)) {
      enableCTA = true;
    }
    this.setState({
      inputData: input,
      enableCTA,
    });
    checkEnglishKeyboard('error', input, this);
  };

  _mobileEmailChecked = () => {
    this.setState({
      mobileChecked: !this.state.mobileChecked,
      inputData: '',
      error: '',
      isLoading: false,
      enableCTA: false,
    });
  };

  _goToIrctcPage = () => {
    Actions.openIrctcPage();
  };

  _handleRetrievePasswordPress = () => {
    this._retrievePassword();
  };

  _retrievePassword = async () => {
    Keyboard.dismiss();
    const isNeedHelpPage = get(this, 'props.fromPage', '') === config.NEED_HELP_ROUTE_SOURCE;
    if (isNeedHelpPage) {
      trackClickEventProp61(
        'rail_need_help_forgotpassword_landing',
        config.CTA_TRACKING_ID.FORGOT_PASSWORD,
      );
    }
    trackRequestNewPasswordEvent(
      'mob_rail_travellers_password_reminder_forgot_password_request_clicked',
    );
    const { inputData, mobileChecked, enableCTA } = this.state;
    const { irctcForgotpassword } = this.props;
    const userName = this.props?.irctcUserName;
    if (!enableCTA) {
      return;
    }
    this.setState({ isLoading: true });
    /*To track forgot password flow*/
    const eventName = mobileChecked ? 'password_reset_via_mobile' : 'password_reset_via_email';
    const pageName = 'mob:funnel:rails:TravellersPage';
    trackClickEventProp61(pageName, eventName);
    const status = await retrievePassword(userName, inputData, mobileChecked);
    if (isEmpty(status)) {
      this.setState({
        isLoading: false,
        enableCTA: false,
        pwdRequested: false,
        error: mobileChecked
          ? _label('mobile_number_unregistered_irctc')
          : _label('email_unregistered_irctc'),
      });
      if (!irctcForgotpassword) {
        trackPasswordReminderPageEvent('mob_password_reminder_forgot_password_request_clicked');
      }
      return;
    }
    if (status[JSON_FIELD.status] === STATUS_API_SUCCESS) {
      this.setState({
        isLoading: false,
        pwdRequested: true,
        error: '',
      });

      if (!irctcForgotpassword) {
        setTimeout(() => {
          this.props.dismiss(false, this.state.inputData);
        }, 3000);
      } else {
        this.props.getPassword();
      }
      if (this.props?.logGftPdtEventsToPdt) {
        this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.GET_NEW_PASSWORD_SUCCESSFUL);
      }
      trackPasswordReminderPageEvent(
        'mob_rail_travellers_next_steps_password_requested_successfully',
      );
    } else if (status[JSON_FIELD.status] === STATUS_LIMIT_EXCEEDED) {
      this.setState({
        isLoading: false,
        enableCTA: false,
        pwdRequested: false,
        error: status[JSON_FIELD.message] || _label('too_many_password_retry'),
      });
      if (this.props?.logGftPdtEventsToPdt) {
        this.props.logGftPdtEventsToPdt(GFT_PDT_EVENTS.TOO_MANY_PASSWORD_RETRY);
      }
      trackPasswordReminderPageEvent('mob_password_reminder_forgot_password_request_clicked');
    } else {
      this.setState({
        isLoading: false,
        enableCTA: false,
        pwdRequested: false,
        error:
          status[JSON_FIELD.message] ||
          (mobileChecked
            ? _label('mobile_number_unregistered_irctc')
            : _label('email_unregistered_irctc')),
      });
      if (this.props?.logGftPdtEventsToPdt) {
        this.props.logGftPdtEventsToPdt(
          mobileChecked
            ? GFT_PDT_EVENTS.MOBILE_UNREGITERED_IRCTC
            : GFT_PDT_EVENTS.EMAIL_UNREGITERED_IRCTC,
        );
      }
      trackPasswordReminderPageEvent('mob_password_reminder_forgot_password_request_clicked');
    }
  };

  render() {
    const { mobileChecked } = this.state;
    const { irctcForgotpassword } = this.props;
    const simpleInputHeader = mobileChecked
      ? _label('enter_irctc_mobile_number')
      : _label('enter_email_registered_irctc');
    const passwordSentTo = mobileChecked
      ? _label('password_sent_to_mobile')
      : _label('password_sent_to_email');
    return (
      <View testID={this.props?.id}>
        {!this.state.pwdRequested && (
          <View
            style={{
              backgroundColor: colors.white,
              paddingBottom: 30,
            }}
            testID="retrievePasswordModal_container"
          >
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text
                style={
                  irctcForgotpassword
                    ? [rpm.headerStyleIrctc, fontStyle('regular'), getLineHeight(10)]
                    : [rpm.headerStyle, fontStyle('black'), getLineHeight(20)]
                }
                testID="retrievePasswordModal_header"
              >
                {_label('get_new_password', { capitalize: true })}
              </Text>
              {!irctcForgotpassword && (
                <TouchableOpacity
                  onPress={() => this.props.dismiss(false, '')}
                  testID="retrievePasswordModal_closeIcon"
                >
                  <Image
                    style={rpm.closeIcon}
                    source={closeIcon}
                    testID="retrievePasswordModal_closeIcon_image"
                  />
                </TouchableOpacity>
              )}
            </View>

            <View
              style={
                irctcForgotpassword
                  ? rpm.rbContainer
                  : [rpm.rbContainer, { marginTop: 10 }, getLineHeight(12)]
              }
            >
              <TouchableOpacity
                onPress={() => this._mobileEmailChecked()}
                testID="retrievePasswordModal_onMobileOption"
              >
                <View
                  style={{ flexDirection: 'row' }}
                  testID="retrievePasswordModal_onMobileOption_container"
                >
                  <RadioButton
                    isSelected={mobileChecked}
                    onPress={() => this._mobileEmailChecked()}
                    radioSize={18}
                    testID="retrievePasswordModal_onMobileOption_radioButton"
                  />
                  <Text
                    style={[rpm.rbMobile, fontStyle('medium'), getLineHeight(14)]}
                    testID="retrievePasswordModal_onMobileOption_text"
                  >
                    {_label('on_mobile_number', { capitalize: true })}
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => this._mobileEmailChecked()}
                testID="retrievePasswordModal_onEmailOption"
              >
                <View
                  style={rpm.rbEmailContainer}
                  testID="retrievePasswordModal_onEmailOption_container"
                >
                  <RadioButton
                    isSelected={!mobileChecked}
                    onPress={() => this._mobileEmailChecked()}
                    radioSize={18}
                  />
                  <Text style={[rpm.rbEmail, fontStyle('medium'), getLineHeight(14)]}>
                    {_label('on_email_id', { capitalize: true })}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            <View style={{ paddingHorizontal: 16 }}>
              <InputField
                label={simpleInputHeader}
                inputProps={{
                  returnKeyType: 'done',
                  autoFocus: true,
                  maxLength: mobileChecked ? 10 : 254,
                  keyboardType: mobileChecked ? 'numeric' : 'email-address',
                  onSubmitEditing: () => {
                    Keyboard.dismiss();
                  },
                }}
                value={this.state.inputData}
                onChangeText={this._onChange}
                isError={!!this.state.error}
                errorMessage={this.state.error}
                customStyle={{
                  wrapperStyle: rpm.inputWrapperStyle,
                  inputFieldStyle: [rpm.inputFieldStyle, fontStyle('bold'), getLineHeight(16)],
                  labelStyle: this.state.error
                    ? { ...rpm.labelStyle, ...rpm.labelErrorStyle }
                    : rpm.labelStyle,
                }}
                testID={`${this.props?.id}_inputField`}
              />

              <View>
                {!this.state.pwdRequested && (
                  <TouchableWithoutFeedback
                    onPress={this._handleRetrievePasswordPress}
                    testID={`${this.props?.id}_confirmButton`}
                  >
                    <View
                      style={{ alignSelf: 'center' }}
                      testID={`${this.props?.id}_confirmButton`}
                    >
                      <LinearGradient
                        start={{
                          x: 1.0,
                          y: 0.0,
                        }}
                        end={{
                          x: 0.0,
                          y: 1.0,
                        }}
                        colors={
                          !this.state.enableCTA ? ['#c2c2c2', '#c2c2c2'] : ['#065af3', '#53b2fe']
                        }
                        style={rpm.buttonStyle}
                      >
                        {!this.state.isLoading && (
                          <Text style={[rpm.btnTextStyle, fontStyle('black')]}>
                            {_label('confirm', { uppercase: true })}
                          </Text>
                        )}
                        {this.state.isLoading && (
                          <ActivityIndicator
                            styleAttr="Inverse"
                            color="white"
                            style={{ margin: 4, alignSelf: 'center' }}
                            size="small"
                          />
                        )}
                      </LinearGradient>
                    </View>
                  </TouchableWithoutFeedback>
                )}
              </View>
            </View>
            {this._isIphone && <KeyboardSpacer />}
          </View>
        )}

        {this.state.pwdRequested && (
          <View
            style={irctcForgotpassword ? rpm.pwdSuccessIrctcContainer : rpm.pwdSuccessContainer}
          >
            <Text style={[rpm.pwdHeader, fontStyle('bold'), getLineHeight(20)]}>
              {_label('new_password_sent', { capitalize: true })}
            </Text>
            <Text style={[rpm.pwdSubHeader, fontStyle('light'), getLineHeight(16)]}>
              <Text>{passwordSentTo} : </Text>
              <Text style={[rpm.pwdData, fontStyle('bold'), getLineHeight(16)]}>
                {this.state.inputData}
              </Text>
            </Text>
            <View style={rpm.pwdImage}>
              <Image
                style={irctcForgotpassword ? rpm.imageIrctc : rpm.image}
                source={ASSETS.icFpTick}
                testID="retrievePasswordModal_tickImage"
              />
            </View>
            <View>
              {irctcForgotpassword && (
                <TouchableWithoutFeedback
                  onPress={this._goToIrctcPage}
                  testID="retrievePasswordModal_continueButton"
                >
                  <View style={{ alignSelf: 'center' }}>
                    <LinearGradient
                      start={{
                        x: 1.0,
                        y: 0.0,
                      }}
                      end={{
                        x: 0.0,
                        y: 1.0,
                      }}
                      colors={['#065af3', '#53b2fe']}
                      style={rpm.continueButtonStyle}
                      testID="retrievePasswordModal_continueButton_linearGradient"
                    >
                      {!this.state.isLoading && (
                        <Text
                          style={[rpm.btnTextStyle, fontStyle('black')]}
                          testID="retrievePasswordModal_continueButton_text"
                        >
                          {_label('continue_booking', { capitalize: true })}
                        </Text>
                      )}
                    </LinearGradient>
                  </View>
                </TouchableWithoutFeedback>
              )}
            </View>
          </View>
        )}
      </View>
    );
  }
}

const rpm = {
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    paddingHorizontal: 16,
  },
  headerStyle: {
    marginLeft: 16,
    marginTop: 20,
    color: colors.black,
    fontSize: 20,
  },
  headerStyleIrctc: {
    marginLeft: 16,
    marginTop: 20,
    color: colors.azure,
    fontSize: 10,
  },
  closeIcon: {
    width: 30,
    height: 30,
    alignSelf: 'flex-end',
    marginTop: 15,
    marginRight: 16,
  },
  rbContainer: {
    flexDirection: 'row',
    marginLeft: 16,
    marginTop: 10,
  },
  rbMobile: {
    color: colors.black,
    fontSize: 14,
    alignSelf: 'center',
    marginLeft: 10,
  },
  rbEmailContainer: {
    flexDirection: 'row',
    marginLeft: 30,
  },
  rbEmail: {
    color: colors.black,
    fontSize: 14,
    alignSelf: 'center',
    marginLeft: 10,
  },
  inputHeader: {
    color: colors.defaultTextColor,
    fontSize: 12.5,
    marginLeft: 16,
    marginTop: 25,
    marginBottom: isMweb() ? 0 : -20,
  },
  buttonStyle: {
    width: 340,
    height: 40,
    marginTop: 20,
    justifyContent: 'center',
    borderRadius: 3.6,
  },
  continueButtonStyle: {
    width: 205,
    height: 40,
    marginTop: 85,
    justifyContent: 'center',
    borderRadius: 40,
  },
  inputStyle: {
    height: 40,
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 16,
    borderColor: colors.azure,
  },
  inputErrorStyle: {
    marginBottom: 10,
    borderColor: colors.red,
  },
  btnTextStyle: {
    color: colors.white,
    alignSelf: 'center',
  },
  pwdSuccessContainer: {
    backgroundColor: colors.white,
    paddingBottom: 10,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pwdSuccessIrctcContainer: {
    backgroundColor: colors.white,
    paddingBottom: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pwdHeader: {
    fontSize: 20,
    color: colors.black,
    marginTop: 25,
  },
  pwdSubHeader: {
    fontSize: 16,
    width: 270,
    color: colors.black,
    marginTop: 15,
  },
  pwdData: {
    fontSize: 16,
    color: colors.black,
    marginTop: 15,
  },
  pwdImage: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  image: {
    width: 50,
    height: 50,
  },
  imageIrctc: {
    width: 85,
    height: 85,
  },
  labelStyle: {
    fontSize: 14,
    fontFamily: fonts.bold,
  },
  inputFieldStyle: {
    fontFamily: fonts.bold,
  },
  labelErrorStyle: {
    color: colors.red,
  },
  inputWrapperStyle: {
    marginTop: 20,
  },
};
RailsRetrievePasswordModal.propTypes = {
  dismiss: PropTypes.func.isRequired,
  inputData: PropTypes.string,
  irctcUserName: PropTypes.string.isRequired,
  labels: PropTypes.object.isRequired,
  id: PropTypes.string,
  mobile: PropTypes.string,
  irctcForgotpassword: PropTypes.bool,
  logGftPdtEventsToPdt: PropTypes.func,
  getPassword: PropTypes.func,
};

RailsRetrievePasswordModal.defaultProps = {
  inputData: '',
};

const getLabels = (texts) => ({
  mobileNumberRegisteredWithIrctc: texts.ns_mobile_number_registered_with_irctc,
  getNewPassword: texts.ns_get_new_password,
});

const mapStateToProps = (state) => ({
  labels: getLabels(state.railsVernacular.texts),
  irctcUserName: state?.railsUserVerification?.irctcUserName,
});

export default connect(mapStateToProps)(RailsRetrievePasswordModal);
