import React, {PureComponent} from 'react';
import LinearGradient from 'react-native-linear-gradient';
import {Image, ScrollView, Text, View, BackHandler} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import {Actions} from '../../navigation';
import RetryObject from '../RetryBooking/constants/RetryObject';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos, isMweb } from '../../Utils/device';
import PropTypes from 'prop-types';

import closeIcon from '@mmt/legacy-assets/src/ic-headerclose-grey.webp';
class RailsImageCard extends PureComponent {
  constructor(props) {
    super(props);
    if (!this.props.refundType) {
      Actions.pop();
    }
  }
  goBack = () => {
    if (isIos()) {
      ViewControllerModule.popViewController(this.props.rootTag);
    } else if (isMweb()) {
      Actions.rails();
    } else {
      BackHandler.exitApp();
    }
  };

  render() {
    if (!this.props.refundType) {
      return null;
    }
    const {refundType} = this.props;
    const retryObject = RetryObject(this.props.totalCollectibleAmount)[refundType];
    return (
      <ScrollView style={styles.container}>
        <TouchableRipple onPress={this.goBack}>
          <View style={{
            height: 40,
            flexDirection: 'row',
            flex: 1,
            alignItems: 'flex-end',
          }}
          >
            <Image
              style={{height: 32, width: 32, marginLeft: 20}}
              source={closeIcon}
            />
          </View>
        </TouchableRipple>
        <View style={{paddingTop: 100, alignItems: 'center'}}>
          <Image
            source={retryObject.image}
          />
        </View>

        <View style={styles.alignItemsCenter}>
          <Text style={[styles.headerText, fontStyle('bold'), getLineHeight(22)]}>
            {retryObject.header}
          </Text>
        </View>
        <View style={{alignItems: 'center', paddingTop: 18}}>
          <Text style={[styles.subHeaderText, getLineHeight(16)]}>
            {retryObject.subHeader}
          </Text>
        </View>
        <View style={{alignItems: 'center', paddingTop: 18}}>
          <Text style={[styles.content, getLineHeight(14)]}>
            {retryObject.content}
          </Text>
        </View>
        <View style={{
          paddingTop: 132,
          alignItems: 'center',
        }}
        >
          <TouchableRipple onPress={this.goBack}>
            <View style={styles.linearGradContainer}>
              <LinearGradient
                style={styles.linearGrad}
                colors={['#53B2FE', '#065AF3']}
                start={{x: 0.0, y: 0.0}}
                end={{x: 1.0, y: 0.0}}
              >
                <View>
                  <Text style={[styles.proceedText, fontStyle('regular'), getLineHeight(16)]}>
                    { _label('book_train_tickets', { uppercase: true })}
                  </Text>
                </View>
              </LinearGradient>
            </View>
          </TouchableRipple>
        </View>
      </ScrollView>
    );
  }
}

const styles = {
  container: {
    backgroundColor: colors.white,
    flex: 1,
    marginHorizontal: 16,
    marginTop: 16,
  },
  alignItemsCenter: {
    alignItems: 'center',
  },
  content: {
    fontSize: 14,
    color: colors.defaultTextColor,
    textAlign: 'center',
  },
  subHeaderText: {
    fontSize: 16,
    color: colors.black,
    textAlign: 'center',
  },
  headerText: {
    fontSize: 22,
    color: colors.black,
    paddingTop: 20,
  },
  linearGradContainer: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: -16,
    elevation: 5,
  },
  linearGrad: {
    borderRadius: 100,
    height: 40,
    paddingHorizontal: 48,
    marginVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  proceedText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    backgroundColor: 'transparent',
  },
};


export default RailsImageCard;



RailsImageCard.propTypes = {
  refundType: PropTypes.string,
  rootTag: PropTypes.number,
  totalCollectibleAmount: PropTypes.number,
};

