import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Image, ScrollView, Text, View } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import CheckBox from 'react-native-checkbox';
import isEmpty from 'lodash/isEmpty';
import { Actions } from '../../navigation';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { colors, normalisePx } from '@mmt/legacy-commons/Styles/globalStyles';
import CtaButton from '../User/Common/CtaButton';
import Card from '@mmt/legacy-commons/Common/Components/Card';
import RailsRetrievePasswordModal from './RailsRetrievePasswordModal';
import { showPasswordReminderScreen } from '../TravelerDetails/TravelerDetailsActions';
import {
  trackPasswordReminderPageEvent,
  trackPasswordReminderPageLoad,
} from '../../railsAnalytics';
import UserNameModal from '../NewUserFlow/UserNameModal';
import { fetchBookingReview } from '../Review/RailsReviewActions';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import MultiStyleVernacText from '../../vernacular/MultiStyleVernacText';
import ASSETS from '../../Utils/Assets/RailsAssets';

import CB_ENABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-active.webp';
import CB_DISABLED_IMAGE from '@mmt/legacy-assets/src/ic-check-box-inactive.webp';

class RailsPasswordReminder extends React.Component {
  constructor(props) {
    super(props);
    if (isEmpty(this.props.mobile)) {
      Actions.pop();
    }
    this.state = {
      showPasswordReminderModal: false,
      mobile: this.props.mobile,
      pwdRequested: false,
      modalVisible: false,
      pwdReminderCheckbox: true,
      showLoader: false,
    };

    trackPasswordReminderPageLoad();
  }

  onHardBackPress = () => {
    if (this.state.showPasswordReminderModal) {
      this.setState({
        showPasswordReminderModal: false,
      });
    } else {
      Actions.pop();
    }
    return true;
  };

  _dismissModal = (selected, mobileText) => {
    const mobile = isEmpty(mobileText) ? this.state.mobile : mobileText;
    const pwdRequested = this.state.pwdRequested || !isEmpty(mobileText);
    this.setState({
      showPasswordReminderModal: selected,
      mobile,
      pwdRequested,
    });
  };

  _continueBooking = () => {
    this.props.showPasswordReminderScreen(!this.state.pwdReminderCheckbox);
    this.props.fetchBookingReview();
  };

  _onChangeClicked = () => {
    this._setModalVisibility(true);
  };

  toggleDontShowCheckbox = () => {
    trackPasswordReminderPageEvent('mob_rail_next_steps_dont_show_again_clicked');
    this.setState((prevState) => ({ pwdReminderCheckbox: !prevState.pwdReminderCheckbox }));
  };

  _setModalVisibility = (visibility) => {
    this.setState({ modalVisible: visibility });
  };
  componentDidUpdate(prevProps) {
    if (prevProps.loadingReview !== this.props.loadingReview && this.props.loadingReview === true) {
      this.setState({ showLoader: true });
    } else if (
      prevProps.loadingReview !== this.props.loadingReview &&
      this.props.loadingReview === false
    ) {
      this.setState({ showLoader: false });
    }
  }

  render() {
    const { irctcUserName, labels } = this.props;
    const { showPasswordReminderModal } = this.state;
    let forUserNameSentenceParts = labels.forUsername.split('$username');
    forUserNameSentenceParts.splice(1, 0, irctcUserName || '');
    forUserNameSentenceParts = forUserNameSentenceParts.filter((value) => value.trim().length > 0);
    return (
      <View style={{ flex: 1 }}>
        <ScrollView style={rpdStyle.container}>
          <TouchableRipple
            onPress={() => {
              Actions.pop();
            }}
          >
            <View style={rpdStyle.tripHeaderBackIconPadding}>
              <Image style={rpdStyle.tripHeaderBackIcon} source={backIcon} />
            </View>
          </TouchableRipple>

          <View style={rpdStyle.fdRow}>
            <View>
              <Text style={[rpdStyle.headerText, fontStyle('bold'), getLineHeight(24)]}>{labels.title}</Text>
              <Text style={[rpdStyle.subHeaderText, fontStyle('regular'), getLineHeight(12)]}>{labels.toBeCompletedIn10Mins}</Text>
            </View>
          </View>
          {this.state.showLoader && (
            <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
              <Spinner size={30} color="#008b8b" />
            </View>
          )}

          {!this.state.showLoader && (
            <View style={rpdStyle.stepsContainer}>
              <View style={{ flexDirection: 'row' }}>
                <View>{/* Here */}
                  <Text style={[rpdStyle.steps, fontStyle('semiBold','en'), getLineHeight(7)]}>1</Text>
                  <View style={rpdStyle.line} />
                </View>
                <View>
                  <Text style={[rpdStyle.textStyle, fontStyle('bold'),rpdStyle.paddingLeft16, getLineHeight(24)]}>
                    {labels.reviewBooking}
                  </Text>
                </View>
              </View>

              <View style={{ flexDirection: 'row' }}>
                <View>
                  <Text style={[rpdStyle.steps,fontStyle('semiBold','en'), getLineHeight(7)]}>2</Text>
                  <View style={rpdStyle.line} />
                </View>
                <View>
                  <Text style={[rpdStyle.textStyle,fontStyle('bold'), rpdStyle.paddingLeft16, getLineHeight(24)]}>
                    {labels.completePayment}
                  </Text>
                </View>
              </View>

              <View style={{ flexDirection: 'row' }}>
                <View>
                  <Text style={[rpdStyle.steps, fontStyle('semiBold','en'), getLineHeight(7)]}>3</Text>
                </View>

                <View style={rpdStyle.flex1}>
                  <Text style={[rpdStyle.textStyle,fontStyle('bold'), rpdStyle.paddingLeft16, getLineHeight(24)]}>
                    {labels.enterIrctcLoginPassword}
                  </Text>
                  <View style={[rpdStyle.fdRow, { paddingTop: 2 }]}>
                    {forUserNameSentenceParts.map((value, index) => {
                      if (value === irctcUserName) {
                        return (
                          <Text key={`username-${index}`}
                          style={[rpdStyle.subTextStyle, fontStyle('black'), getLineHeight(12)]}>
                            {value}
                          </Text>
                        );
                      }
                      return (
                        <Text key={`text-${index}`}
                        style={[rpdStyle.subTextStyle, fontStyle('black'), rpdStyle.paddingLeft16, getLineHeight(12)]}>
                        {value}
                        </Text>
                      );
                    })}
                    <TouchableRipple onPress={() => this._onChangeClicked()}>
                      <Text
                        style={[
                          rpdStyle.subTextStyle,
                          {
                            color: colors.azure,
                            paddingLeft: 2,
                            ...fontStyle('bold'),
                            ...getLineHeight(12),
                          },
                        ]}
                      >
                        {labels.change}
                      </Text>
                    </TouchableRipple>
                  </View>
                </View>
              </View>
            </View>
          )}
          {!this.state.showLoader && (
            <View style={rpdStyle.marginTop16}>
              <CtaButton
                label={labels.continueBooking}
                onPress={() => this._continueBooking()}
                btnStyle={{
                  borderWidth: 0,
                  height: 44,
                  paddingHorizontal: normalisePx(0),
                }}
                textStyle={{
                  color: colors.white,
                  fontSize: 16,
                }}
                withNextArrow
              />
            </View>
          )}
          {!this.state.showLoader && (
            <View style={rpdStyle.marginTop7}>
              <Text style={[rpdStyle.subTextStyle, fontStyle('regular'), getLineHeight(12)]}>{labels.pleaseKeepYourIrctcPasswordReady}</Text>
            </View>
          )}
          {!this.state.pwdRequested && !this.state.showLoader && (
            <View style={rpdStyle.marginTop35}>
              <PasswordReminderCard
                labels={labels}
                mobile={this.state.mobile}
                showRetrievePasswordModal={this._dismissModal}
              />
            </View>
          )}

          {this.state.pwdRequested && !this.state.showLoader && (
            <View style={rpdStyle.marginTop35}>
              <PasswordRequestedCard labels={labels} mobile={this.state.mobile} />
            </View>
          )}
          {!this.state.showLoader && (
            <View style={rpdStyle.marginTop20}>
              <CheckBox
                label={labels.dontShowThisAgain}
                checked={this.state.pwdReminderCheckbox}
                labelStyle={StyleSheet.flatten([rpdStyle.subTextStyle, fontStyle('regular'), getLineHeight(12)])}
                onChange={this.toggleDontShowCheckbox}
                checkedImage={CB_ENABLED_IMAGE}
                uncheckedImage={CB_DISABLED_IMAGE}
              />
            </View>
          )}
        </ScrollView>
        {showPasswordReminderModal && (
          <RailsRetrievePasswordModal
            dismiss={this._dismissModal}
            mobile={this.state.mobile}
            irctcUserName={this.props.irctcUserName}
          />
        )}
        {this.state.modalVisible && (
          <UserNameModal
            modalVisible={this.state.modalVisible}
            setModalVisibility={this._setModalVisibility}
            from="railsPasswordReminder"
            probableTo="railsReviewPage"
            replace
          />
        )}
      </View>
    );
  }
}

RailsPasswordReminder.propTypes = {
  irctcUserName: PropTypes.string,
  pwdRequested: PropTypes.bool,
  mobile: PropTypes.string,
  showPwdReminder: PropTypes.bool,
  showPasswordReminderScreen: PropTypes.func.isRequired,
  fetchBookingReview: PropTypes.func,
  loadingReview: PropTypes.bool,
  labels: PropTypes.shape({
    forUsername: PropTypes.string,
    title: PropTypes.string,
    toBeCompletedIn10Mins: PropTypes.string,
    reviewBooking: PropTypes.string,
    completePayment: PropTypes.string,
    enterIrctcLoginPassword: PropTypes.string,
    change: PropTypes.string,
    continueBooking: PropTypes.string,
    pleaseKeepYourIrctcPasswordReady: PropTypes.string,
    dontShowThisAgain: PropTypes.string,
  }),
};

RailsPasswordReminder.defaultProps = {
  pwdRequested: false,
  irctcUserName: '',
  mobile: '',
};

const PasswordReminderCard = ({showRetrievePasswordModal, labels }) => (
  <Card
    style={{
      marginHorizontal: 0,
      paddingHorizontal: 16,
      paddingBottom: 16,
      borderColor: colors.defaultTextColor,
      borderRadius: 5,
    }}
    showBorder
    elevation={1}
  >
    <Text style={[rpdStyle.textStyle, fontStyle('bold'),rpdStyle.marginTop16, getLineHeight(24)]}>{labels.forgotYourIrctcPassword}</Text>
    <Text style={[rpdStyle.subTextStyle, fontStyle('regular'), rpdStyle.marginTop4, { lineHeight: 18 }, getLineHeight(12)]}>
      {labels.wrongPwdWarning}
    </Text>
    <Text style={[rpdStyle.textInputTitle, fontStyle('regular'),rpdStyle.marginTop7, getLineHeight(10)]}>
      {labels.enterMobileNumberToGetNewPassword}
    </Text>

    <TouchableRipple onPress={() => showRetrievePasswordModal(true, '')}>
      <Text style={[rpdStyle.textStyle, fontStyle('bold'), rpdStyle.marginTop16, getLineHeight(24)]}>+91 </Text>
    </TouchableRipple>

    <View style={[rpdStyle.hLine, rpdStyle.marginTop7]} />
  </Card>
);

PasswordReminderCard.propTypes = {
  mobile: PropTypes.string,
  labels: PropTypes.object.isRequired,
  showRetrievePasswordModal: PropTypes.func.isRequired,
};

const PasswordRequestedCard = ({ mobile = '' }) => (
  <Card
    style={{
      marginHorizontal: 0,
      paddingHorizontal: 16,
      paddingBottom: 16,
      borderColor: colors.defaultTextColor,
      borderRadius: 5,
    }}
    showBorder
    elevation={1}
  >
    <Text style={[rpdStyle.textStyle, fontStyle('bold'), rpdStyle.marginTop16, getLineHeight(24)]}>{_label('new_password_requested')}</Text>
    <View>
      <View style={rpdStyle.fdRow}>
        <MultiStyleVernacText
            content = {_label('get_sms_on')}
            contentHorizontal = {true}
            params = {{number : mobile.toString()}}
            defaultContentStyle = {[rpdStyle.subTextStyle, { lineHeight: 18 }, getLineHeight(12)]}
            textStyles = {{
              number : [rpdStyle.subTextStyle, fontStyle('bold'), getLineHeight(12)],
            }}
            viewStyle = {
              [rpdStyle.fdRow, rpdStyle.marginTop4, { flexWrap: 'wrap', width: '90%'}]
            }
        />
        <View>
          <Image
            style={{
              width: 32,
              height: 32,
            }}
            source={ASSETS.icFpTick}
          />
        </View>
      </View>
    </View>
  </Card>
);

PasswordRequestedCard.propTypes = {
  mobile: PropTypes.string,
};

export const rpdStyle = {
  container: {
    flex: 1,
    margin: 16,
  },
  stepsContainer: {
    marginTop: 16,
    flex: 1,
  },
  flex1: {
    flex: 1,
  },
  fdRow: {
    flexDirection: 'row',
  },
  line: {
    borderLeftColor: colors.black04,
    borderLeftWidth: 1,
    marginLeft: 5,
    height: 25,
  },
  hLine: {
    borderBottomColor: colors.azure,
    borderBottomWidth: 1,
  },
  textInputTitle: {
    fontSize: 10,
    lineHeight: 12,
    letterSpacing: 0,
    color: colors.azure,
  },
  marginTop16: {
    marginTop: 16,
  },
  marginTop4: {
    marginTop: 4,
  },
  marginTop7: {
    marginTop: 7,
  },
  marginTop35: {
    marginTop: 35,
  },
  marginTop2: {
    marginTop: 2,
  },
  paddingLeft16: {
    paddingLeft: 16,
  },
  lineHeight12: { lineHeight: 12 },
  headerText: {
    fontSize: 24,
    letterSpacing: 0,
    color: colors.black,
  },
  subHeaderText: {
    fontSize: 12,
    letterSpacing: 0,
    color: colors.defaultTextColor,
  },
  tripHeaderBackIconPadding: {
    width: 48,
    height: 48,
    marginTop: 8,
  },
  tripHeaderBackIcon: {
    width: 16,
    height: 16,
  },
  clockImage: {
    width: 15,
    height: 15,
    alignItems: 'center',
  },
  timerDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    height: 25,
    width: 100,
    borderBottomRightRadius: 100,
    borderTopRightRadius: 100,
  },
  timerDisplayText: {
    color: colors.white,
    fontSize: 16,
    backgroundColor: 'transparent',
  },
  timerPositionStyle: {
    position: 'absolute',
    right: 0,
  },
  steps: {
    borderRadius: 6,
    width: 12,
    height: 12,
    backgroundColor: colors.black,
    color: colors.white,
    textAlign: 'center',
    overflow: 'hidden',
    lineHeight: 10,
    fontSize: 7,
  },
  textStyle: {
    fontSize: 14,
    color: colors.black,
  },
  subTextStyle: {
    fontSize: 12,
    color: colors.defaultTextColor,
  },
  marginTop20: {
    marginTop: 20,
  },
};

const getLabels = (texts) => ({
  title: texts.ns_next_steps,
  toBeCompletedIn10Mins: texts.ns_to_be_completed_in_10_mins,
  reviewBooking: texts.ns_review_booking,
  completePayment: texts.ns_complete_payment,
  enterIrctcLoginPassword: texts.ns_enter_irctc_login_password,
  forUsername: texts.ns_for_username_$username,
  change: texts.ns_change,
  continueBooking: texts.ns_continue_booking,
  pleaseKeepYourIrctcPasswordReady: texts.ns_please_keep_your_irctc_password_ready,
  forgotYourIrctcPassword: texts.ns_forgot_your_irctc_password,
  wrongPwdWarning: texts.ns_wrong_pwd_warning,
  enterMobileNumberToGetNewPassword: texts.ns_enter_mobile_number_to_get_new_password,
  dontShowThisAgain: texts.ns_dont_show_this_again,
  mobileNumberRegisteredWithIrctc: texts.ns_mobile_number_registered_with_irctc,
  getNewPassword: texts.ns_get_new_password,
});

const mapStateToProps = (state) => {
  const { railsTraveler } = state;
  if (!railsTraveler || !railsTraveler.contactDetails) {
    // RNW return null in case of refresh of password reminder page //
    return {
      irctcUserName: null,
      mobile: null,
    };
  }
  const {
    railsUserVerification: { irctcUserName },
    railsTraveler: { contactDetails },
    railsVernacular: { texts },
    railsReview: { loadingReview },
  } = state;
  return {
    irctcUserName,
    mobile: contactDetails.mobile,
    labels: getLabels(texts),
    loadingReview,
  };
};

const mapDisptachToProps = (dispatch) => ({
  showPasswordReminderScreen: (selected) => dispatch(showPasswordReminderScreen(selected)),
  fetchBookingReview: () => dispatch(fetchBookingReview()),
});

export default connect(mapStateToProps, mapDisptachToProps)(RailsPasswordReminder);
