import React from 'react';
import { Text, StyleSheet, View, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { Props } from './types';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyle, getLineHeight } from '../../../vernacular/VernacularUtils';
//Sometimes src/Common/Components/Buttons/RecBlueBtn is not working properly (BR-10705), So using our own button wrapper.
const RectangularGradientButton = (props: Props) => {
  const {
    label,
    onPress,
    disabled = false,
    btnImage,
    btnImageStyle,
    gradientStyle,
    alignImageAtTheEnd = false,
    labelSize = 16,
  } = props;

  return (
    <TouchableRipple
      onPress={onPress}
      disabled={disabled}
      testID="rectangular_gradient_button_touchable_ripple"
    >
      <LinearGradient
        colors={
          disabled ? [colors.lightGray, colors.lightGrey7] : [colors.lightBlue, colors.darkBlue]
        }
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        style={[styles.button, gradientStyle]}
        testID="rectangular_gradient_button_linear_gradient"
      >
        <View
          style={styles.contentContainer}
          testID="rectangular_gradient_button_content_container"
        >
          {btnImage && !alignImageAtTheEnd && (
            <Image
              style={[styles.buttonIcon, styles.rightSpace, btnImageStyle]}
              source={btnImage}
              testID="rectangular_gradient_button_content_container_image"
            />
          )}
          <Text
            style={[styles.label, fontStyle('black'), getLineHeight(labelSize)]}
            testID="rectangular_gradient_button_content_container_text"
          >
            {label}
          </Text>
          {btnImage && alignImageAtTheEnd && (
            <Image
              style={[styles.buttonIcon, styles.leftSpace, btnImageStyle]}
              source={btnImage}
              testID="rectangular_gradient_button_content_container_image_two"
            />
          )}
        </View>
      </LinearGradient>
    </TouchableRipple>
  );
};

const styles = StyleSheet.create({
  button: {
    height: 32,
    paddingHorizontal: 20,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    color: colors.white,
    fontSize: 16,
  },
  buttonIcon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftSpace: {
    marginLeft: 5,
  },
  rightSpace: {
    marginRight: 5,
  },
});

export default RectangularGradientButton;
