import PropTypes from 'prop-types';
import React, {Component} from 'react';
import fecha from 'fecha';
import isEmpty from 'lodash/isEmpty';
import {
  Animated,
  Keyboard,
  Modal,
  Text,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
} from 'react-native';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {addDays} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {openDatePicker} from '@mmt/legacy-commons/Native/BusRailsCalendarModule';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import { _label } from '../../vernacular/AppLanguage';
import {fontStyle,getLineHeight} from '../../vernacular/VernacularUtils';
import { isIos } from '../../Utils/device';
import DatePicker from 'react-native-date-picker';

class CalendarIos extends Component {
  state = {
    dob: this.props.maxDate,
    animatedValue: new Animated.Value(0.01),
    height: 0,
  };

  setDate = (newDate) => {
    this.setState({dob: newDate});
  };

  _setHeight = ({nativeEvent}) => {
    const {layout: {height}} = nativeEvent;
    this.setState({height}, () => {
      Animated.timing(
        this.state.animatedValue,
        {
          toValue: this.state.height,
          duration: 200,
        },
      ).start();
    });
  };

  render() {
    return (
      <Animated.View style={{height: this.state.animatedValue}}>
        <View onLayout={this._setHeight}>
          <View style={calendarIosStyles.container}>
            <TouchableRipple onPress={this.props.onCancelClick}>
              <View style={calendarIosStyles.cancelTextContainer}>
                <Text style={[calendarIosStyles.cancelText, fontStyle('regular'), getLineHeight(16)]}>{_label('cancel', { uppercase: true })}</Text>
              </View>
            </TouchableRipple>
            <TouchableRipple onPress={this.props.onConfirmClick(this.state.dob)}>
              <View style={calendarIosStyles.confirmTextContainer}>
                <Text style={[calendarIosStyles.confirmText, fontStyle('regular'), getLineHeight(16)]}>{_label('confirm', { uppercase: true })}</Text>
              </View>
            </TouchableRipple>
          </View>
          <View style={calendarIosStyles.calendarContainer}>
            <DatePicker
              date={this.state.dob}
              onDateChange={this.setDate}
              mode="date"
              minimumDate={this.props.minDate}
              maximumDate={this.props.maxDate}
            />
          </View>
        </View>
      </Animated.View>
    );
  }
}

const calendarIosStyles = StyleSheet.create({
  container: {flexDirection: 'row', justifyContent: 'space-around'},
  cancelTextContainer: {paddingVertical: 12, paddingHorizontal: 30},
  cancelText: {fontSize: 16, color: colors.defaultTextColor},
  confirmTextContainer: {paddingVertical: 12, paddingHorizontal: 30},
  confirmText: {fontSize: 16, color: colors.azure},
  calendarContainer: {paddingHorizontal: 16, paddingBottom: 20},
});

CalendarIos.propTypes = {
  maxDate: PropTypes.object.isRequired,
  minDate: PropTypes.object.isRequired,
  onCancelClick: PropTypes.func.isRequired,
  onConfirmClick: PropTypes.func.isRequired,
};


export default class Calendar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dob: null,
      modalVisible: false,
    };
  }

  _openDOBCalendar = async () => {
    const minDateStr = fecha.format(this.props.minDate, 'DD/MM/YYYY');
    const maxDateStr = fecha.format(this.props.maxDate, 'DD/MM/YYYY');

    let {dob} = this.state;
    if (isEmpty(dob)) {
      dob = addDays(this.props.maxDate, -1);
    }
    if (isEmpty(dob)) {
      const date = addDays(this.props.maxDate, -1);
      dob = fecha.format(date, 'DD/MM/YYYY');
    }
    const newDobStr = await openDatePicker({
      minDate: minDateStr,
      maxDate: maxDateStr,
      selectedDate: dob,
      title: this.props.title,
    });
    const dateObject = fecha.parse(newDobStr, 'DD/MM/YYYY');
    this.setState({
      dob: dateObject,
    });
    this.props.callbackDob(dateObject);
  };

  setModalVisibilityState = visibility => () => {
    if (visibility) {
      Keyboard.dismiss();
    }
    this.setState({modalVisible: visibility});
  };

  onConfirmClick = date => () => {
    this.setState({dob: date, modalVisible: false});
    this.props.callbackDob(date);
  };

  render() {
    return (
      <View>
        <Modal
          transparent
          visible={this.state.modalVisible}
          onRequestClose={this.setModalVisibilityState(false)}
        >
          <TouchableWithoutFeedback onPress={this.setModalVisibilityState(false)}>
            <View style={{flex: 1, backgroundColor: colors.lightBlack }} />
          </TouchableWithoutFeedback>
          <View style={{backgroundColor: colors.white}}>
            <CalendarIos
              onCancelClick={this.setModalVisibilityState(false)}
              onConfirmClick={this.onConfirmClick}
              minDate={this.props.minDate}
              maxDate={this.props.maxDate}
            />
          </View>
        </Modal>

        <TouchableRipple
          onPress={() => {
            if (isIos()) {
              this.setModalVisibilityState(true)();
            } else {
              this._openDOBCalendar();
            }
          }}
        >
          {this.props.children}
        </TouchableRipple>
      </View>
    );
  }
}

Calendar.propTypes = {
  callbackDob: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  maxDate: PropTypes.object.isRequired,
  minDate: PropTypes.object.isRequired,
  title: PropTypes.string,
};

Calendar.defaultProps = {
  title: 'Select Date of Birth',
};
