import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const InfoMessage = ({message, type}) => {
    const infoBgcolor = type === "success" ? styles.successBackground : styles.infoBackground;
    const infoTextcolor = type === "success" ? styles.greenText : styles.blueText;
    return(
        <View style={[styles.messageWrapper, infoBgcolor]}>
            <Text style={[styles.font14, styles.boldFont, infoTextcolor]}>{message}</Text>
        </View>
    )
};

const styles = StyleSheet.create({
    messageWrapper: {
        paddingHorizontal: 16,
        paddingVertical: 10,
    },
    infoBackground: {
        backgroundColor: '#eaf5ff',
        elevation: -3,
        borderTopWidth: 1,
        borderTopColor: '#d8d8d8',
        borderBottomWidth: 1,
        borderBottomColor: "#d8d8d8",
    },
    blueText: {
        color: '#0c58b4',
    },
    successBackground: {
        backgroundColor: '#e6fff9',
        elevation: -3,
        borderTopWidth: 1,
        borderTopColor: '#d8d8d8',
        borderBottomWidth: 1,
        borderBottomColor: "#d8d8d8",
    },
    greenText: {
        color: '#007e7d',
    },
})

export default InfoMessage;