import fecha from 'fecha';
import queryString from 'query-string';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import _ from 'lodash';
import { Platform, NativeModules } from 'react-native';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { VISA_ROUTE_KEYS } from './Navigation';
import AsyncStorage from '@react-native-async-storage/async-storage';


import {
  DEFAULT_PRIMARY_NAME,
  PASSENGER_BOOKING_STATUS,
  DOCUMENT_TYPE,
  DEFAULT_ADULT_AGE,
  ADULT_AGE_EXTENSION,
  TRAVELLER,
  TRAVELLERS,
  DATE_FORMAT_DISPLAY,
  GENDER,
  MARRIED,
  FORM_DATA_TYPE,
  DATE_FORMAT_CALENDAR,
  DEFAULT_DOC_EXTENSION,
  STATUS_CODE_SUCCESS,
  ADULT,
  PAGENAME,
  PARENTCURLYBRACES,
  DIGIT_REGEXP,
  PASSENGER_STATUS,
  VISA_DOCUMENT_TYPE, DATE_FORMAT_COMMON
} from './VisaConstants';

import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import styles from './css/VisaMainCss';
import { downloadFile, downloadFileWithHeaders } from '@mmt/legacy-commons/Common/utils/fileUtils';
import { isValidURL } from '@mmt/legacy-commons/Helpers/validationHelpers';
import { updateRecentSearchHistory } from '@mmt/legacy-commons/Native/GenericModule';
import { VisaNavigation } from './Navigation';

const WEEKDAY = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

export const getFlightDepArrivalText = (depDate, arrDate) => {
  try {
    let dateStr;
    if (depDate) {
      dateStr = `Departs ${getDisplayDate(depDate)}`;
    }
    if (arrDate) {
      dateStr += ` - Return ${getDisplayDate(arrDate)}`;
    }
    return dateStr;
  } catch (e) {
    return '';
  }
};

export const getHotelCheckInOutDate = (checkInDate, checkOutDate) => {
  try {
    let dateStr;
    if (checkInDate) {
      dateStr = getDisplayDate(checkInDate);
    }
    if (checkOutDate) {
      dateStr += ` - ${getDisplayDate(checkOutDate)}`;
    }
    return dateStr;
  } catch (e) {
    return '';
  }
};

export const getPendingBookingDates = (depDate, arrDate) => {
  try {
    let dateStr;
    if (depDate) {
      dateStr = getDisplayDate(depDate);
    }
    if (arrDate) {
      dateStr += ` - ${getDisplayDate(arrDate)}`;
    }
    return dateStr;
  } catch (e) {
    return '';
  }
};

export const getCurrentDate = () => fecha.format(new Date(), DATE_FORMAT_DISPLAY);

export const addDaysToDate = (days) => {
  const date = new Date();
  return date.setDate(date.getDate() + days);
};

export const getVisaStartDate = (date, format) => `${fecha.format(new Date(date), format)}`;

export const getSearchCalenderDate = () => {
  const date = new Date();
  date.setDate(new Date().getDate() + 1);
  return date;
};

export const getDateFromDate = (date) => {
  if (date) {
    return fecha.format(new Date(date), 'DD');
  }
  return '';
};

export const getMonthYearFromDate = (date) => {
  if (date) {
    return fecha.format(new Date(date), 'MMM YYYY');
  }
  return '';
};

export const getWeekDayFromDate = (date) => {
  if (date) {
    return WEEKDAY[new Date(date).getDay()];
  }
  return '';
};

export const getHotelRooms = rooms => (rooms > 1 ? `${rooms} Rooms` : rooms === 1 ? '1 Room' : '');

export const getSavings = (totalPrice, discountedPrice) => {
  const discount = totalPrice - discountedPrice;
  const discountPercent = Math.floor((discount / totalPrice) * 100);
  return `${discount} (${discountPercent}%)`;
};

export const getFormattedDate = (timeStamp, format) => {
  if (timeStamp) {
    return fecha.format(timeStamp, format);
  }
  return '';
};

export const getDisplayDate = (date) => {
  if (date) {
    return fecha.format(getDate(date), DATE_FORMAT_DISPLAY);
  }
  return '';
};

export const getDate = (dateStr) => {
  if (dateStr.indexOf('-') !== -1) {
    const splitDate = dateStr.split('-');
    const date = new Date(parseInt(splitDate[2]), parseInt(splitDate[1]) - 1, parseInt(splitDate[0]));
    return date;
  }
  return dateStr;
};


export const getFullName = (firstName, middleName, lastName) => {
  let fullName = '';
  if (firstName) {
    fullName += _.capitalize(firstName);
    if (middleName) {
      fullName += ` ${_.capitalize(middleName)}`;
    }
    if (lastName) {
      fullName += ` ${_.capitalize(lastName)}`;
    }
  } else if (middleName) {
    fullName += _.capitalize(middleName);
    if (lastName) {
      fullName += ` ${_.capitalize(lastName)}`;
    }
  } else if (lastName) {
    fullName += _.capitalize(lastName);
  }
  return _.capitalize(fullName);
};

export const getPaxObject = (paxName, paxAge, paxAgeMorY, paxId, paxType, isDefault = false, gender) => ({
  name: paxName,
  age: String(paxAge),
  ageMorY: paxAgeMorY,
  id: String(paxId),
  status: PASSENGER_BOOKING_STATUS.PASSENGER_INITIATED,
  docs: [],
  type: paxType,
  isDefaultAge: isDefault,
  gender
});

export const getDocumentList = () => [{ type: String(DOCUMENT_TYPE.PASSPORT_FRONT), uploaded: false },
{ type: String(DOCUMENT_TYPE.PASSPORT_BACK), uploaded: false },
{ type: String(DOCUMENT_TYPE.PHOTOGRAPH), uploaded: false },
{ type: String(DOCUMENT_TYPE.ETICKET), uploaded: false },
{ type: String(DOCUMENT_TYPE.HOTEL_VOUCHERS), uploaded: false }
];

export const getPendingPaxObject = elem => ({
  name: elem.name,
  age: String(DEFAULT_ADULT_AGE),
  id: String(elem.paxIdx),
  ageMorY: ADULT_AGE_EXTENSION,
  status: elem.status,
  docs: getPendingDocumentList(elem),
  type: ADULT
});

export const getPendingDocumentList = (elem) => {
  const docList = [];
  if (elem.documents) {
    elem.documents.forEach((doc) => {
      docList.push({ type: String(doc.type), uploaded: doc.uploaded });
    });
  }
  return docList;
};

export const getNextPaxIdForDocUpload = (params) => {
  if (params.paxList.length === 1) {
    return { id: -1, status: -1 };
  }

  const paxPendingList = _.filter(params.paxList, row =>
    row.status === PASSENGER_BOOKING_STATUS.PASSENGER_INITIATED
    || row.status === PASSENGER_BOOKING_STATUS.DOCUMENTS_PENDING);

  if (!_.isEmpty(paxPendingList)) {
    return paxPendingList[0];
  }

  const paxVerifiedList = _.filter(params.paxList, row =>
    row.status === PASSENGER_BOOKING_STATUS.DOCUMENTS_UPLOADED);

  if (!_.isEmpty(paxVerifiedList)) {
    return paxVerifiedList[0];
  }

  return { id: -1, status: -1 };
};

export const updateDocumentStatusForPax = (params, uploaded) => {
  if (params && params.paxList) {
    const paxObj = params.paxList[params.paxList.findIndex(row => row.id === params.paxId)];
    if (paxObj) {
      updatePaxStatus(paxObj);
      paxObj.docs.forEach((doc) => {
        const tempDoc = doc;
        tempDoc.uploaded = uploaded;
      });
    }
  }
};

export const updateDocumentStatusForPaxFromDocResponse = (params, uploaded, docResponse) => {
  const paxObj = params.paxList[params.paxList.findIndex(row => row.id === params.paxId)];
  updatePaxStatus(paxObj);
  paxObj.docs.forEach((doc) => {
    const tempDoc = doc;
    tempDoc.uploaded = uploaded;
  });
};

export const getCurrentPassenger = visaParams =>
  visaParams.paxList[visaParams.paxList.findIndex(row => row.id === visaParams.paxId)];

export const getTravellerCount = () => this.state.passengers.length;

export const capitalize = str => str.charAt(0).toUpperCase() + str.slice(1);

// If paxId is passed, its not for Primary(its for other pax)
export const getPrimaryTraveller = (paxList, paxId = 1) => {
  const arrayIndex = (paxId - 1); // paxId starts from 1, 2 and so on...
  if (!_.isEmpty(paxList) && paxList.length > arrayIndex) {
    return paxList[arrayIndex].name;
  }
  return DEFAULT_PRIMARY_NAME;
};

export const getTravellerText = (paxList) => {
  if (paxList) {
    return `${paxList.length} ${paxList.length > 1
      ? capitalize(TRAVELLERS) : capitalize(TRAVELLER)}`;
  }
  return '';
};

export const formatNavSubTitle = ((param1, param2, param3, param2Key = 'Entry') => {
  let subTitle = '';
  if (param1) {
    subTitle += param1;
    if (param2) {
      subTitle += ` | ${param2Key} ${param2}`;
    }
    if (param3) {
      subTitle += ` | ${param3}`;
    }
  } else if (param2) {
    subTitle += `${param2Key} ${param2}`;
    if (param3) {
      subTitle += ` | ${param3}`;
    }
  } else if (param3) {
    subTitle += param3;
  }
  return subTitle;
});

export const getUpdatedTravellerPriceBreakUp = (chargesLabels_, paxCount, updatedPaxCount) => {
  const chargesLabels = chargesLabels_.map((_element) => {
    const element = ({ ..._element });
    const splitArr = element.value_.split(' ');
    if (splitArr.length > 1) {
      element.value_ = ((splitArr[1] / paxCount) * updatedPaxCount);
    }
    return element;
  });
  return chargesLabels;
};

export const updatePaxStatus = (pax) => {
  const temp = pax;
  switch (pax.status) {
    case PASSENGER_BOOKING_STATUS.PASSENGER_INITIATED:
    case PASSENGER_BOOKING_STATUS.DOCUMENTS_PENDING:
      temp.status = PASSENGER_BOOKING_STATUS.DOCUMENTS_UPLOADED;
      break;
    case PASSENGER_BOOKING_STATUS.DOCUMENTS_UPLOADED:
      temp.status = PASSENGER_BOOKING_STATUS.DOCUMENTS_VERIFIED;
      break;
    default:
      break;
  }
};


export const createParamsForNextTraveller = (visaForm, visaParams, nextPaxId, status) => {
  const params = visaParams;
  params.paxId = nextPaxId;
  const firstName = visaForm[visaForm.findIndex(row => row.key === 'firstName_')].selectedVal;
  const lastName = visaForm[visaForm.findIndex(row => row.key === 'lastName_')].selectedVal;
  params.paxName = `${firstName} ${lastName}`;
  params.operation = status;
};

export const extractValue = (visaForm, key) => visaForm.fieldValues[key];

export const createParamsForNextTravellerNew = (visaForm, visaParams, nextPaxId, status) => {
  const params = visaParams;
  params.paxId = nextPaxId;
  // index is 0, because addmore value is false for these keys
  const firstName = extractValue(visaForm, 'firstName')[0].value;
  const lastName = extractValue(visaForm, 'lastName')[0].value;
  params.paxName = `${firstName} ${lastName}`;
  params.operation = status;
};

export const getDocUploadedPaxCount = (paxList) => {
  let count = 0;
  paxList.forEach((pax) => {
    if (pax.status === PASSENGER_BOOKING_STATUS.DOCUMENTS_UPLOADED ||
      pax.status === PASSENGER_BOOKING_STATUS.DOCUMENTS_VERIFIED) {
      count += 1;
    }
  });
  return count;
};

export const validateLastName = (elem, formValidity) => {
  const newElem = elem;
  const formValid = formValidity;
  newElem.errorLabel = '';
  if (newElem.selectedVal) {
    if (!isValidPattern(newElem.pattern, newElem.selectedVal)) {
      newElem.errorLabel = `${newElem.label} is Invalid`;
      formValid.valid = false;
    } else if (!isValidLength(newElem.length, newElem.selectedVal)) {
      newElem.errorLabel =
        `${newElem.label} should be in between ${newElem.length.min} to ${newElem.length.max} characters`;
      formValid.valid = false;
    }
  }
};

export const validateSpouse = (elem, formValidity, visaForm) => {
  let genderValue = '';
  let maritalStatus = '';
  visaForm.forEach((formElem) => {
    switch (formElem.key) {
      case 'maritalStatus_':
        maritalStatus = formElem.selectedVal;
        break;
      case 'gender_':
        genderValue = formElem.selectedVal;
        break;
      default:
        break;
    }
  });
  if ((genderValue && genderValue.toLowerCase() === GENDER.FEMALE.toLowerCase())
    && maritalStatus && maritalStatus.toLowerCase() === MARRIED.toLowerCase()) {
    updateErrorValue(elem, formValidity);
  }
};
export const updateErrorValue = (elem, formValidity) => {
  const newElem = elem;
  const formValid = formValidity;
  newElem.errorLabel = '';
  if (_.isEmpty(newElem.selectedVal)) {
    newElem.errorLabel = `${newElem.label} can not be left blank`;
    formValid.valid = false;
  } else if (!isValidPattern(newElem.pattern, newElem.selectedVal)) {
    newElem.errorLabel = `${newElem.label} is Invalid`;
    formValid.valid = false;
  } else if (!isValidLength(newElem.length, newElem.selectedVal)) {
    newElem.errorLabel =
      `${newElem.label} should be in between ${newElem.length.min} to ${newElem.length.max} characters`;
    formValid.valid = false;
  }
};

export const isValidPattern = (pattern, val) => {
  if (pattern) {
    return pattern.test(val);
  }
  return true;
};

export const isNotValidGSTIN = (gstin) => {
  if (!_.isEmpty(gstin)) {
    const regex = /^(0[1-9]|[1-2][0-9]|3[0-6])[A-Z]{5}\d{4}[A-Z]\d[A-Z][A-Z\d]$/;
    return !isValidPattern(regex, gstin);
  }
  return true;
};

export const isValidLength = (lengthObj, val) => {
  if (lengthObj) {
    return val.length >= lengthObj.min && val.length <= lengthObj.max;
  }
  return true;
};


export const validateDynamicForm = (master, visaForm) => {
  let isValid = true;
  if (!visaForm) return isValid;
  const keys = Object.keys(visaForm);
  keys.forEach((key) => {
    if (master[key]) {
      let validLen = 0;
      visaForm[key].forEach((elem) => {
        if (elem.value === '') {
          isValid = isValid && !master[key].mandatory && validateDynamicForm(master, elem.dependents);
        } else {
          isValid = isValid && isValidPattern(new RegExp(master[key].validations[elem.parent]), elem.value)
            && validateDynamicForm(master, elem.dependents);
        }
        if (isValid) validLen += 1;
      });
      if (master[key].mandatory && !validLen) {
        isValid = false;
      }
    }
  });
  return isValid;
};
/**
 *
 * @param {*} fields form fields
 * @param {*} values form field's values
 */
export const validateDynamicFormV2 = (fields, values) => {
  let isValid = true;
  if (!values) return isValid;
  const keys = Object.keys(values);
  keys.forEach((key) => {
    if (fields[key]) {
      let validLen = 0;
      values[key].forEach((elem) => {
        if (elem.value === '') {
          isValid = isValid && !fields[key].mandatory && validateDynamicFormV2(fields, elem.dependents);
        } else {
          isValid = isValid
            && (fields[key].validation ? isValidPattern(new RegExp(fields[key].validation), elem.value): true)
            && validateDynamicFormV2(fields, elem.dependents);
        }
        if (isValid) validLen += 1;
      });
      if (fields[key].mandatory && !validLen) {
        isValid = false;
      }
    }
  });
  return isValid;
};

export const validateForm = (visaForm) => {
  const formValidity = { valid: true };
  visaForm.forEach((elem) => {
    switch (elem.key) {
      case 'firstName_':
      case 'placeOfBirth_':
      case 'nationality_':
      case 'motherName_':
      case 'fatherName_':
      case 'profession_':
      case 'passportNo_':
      case 'maritalStatus_':
      case 'gender_':
      case 'religion_':
      case 'countryOfBirth_':
      case 'ppPlaceOfIssue_':
      case 'dateOfBirth_':
      case 'ppDateOfIssue_':
      case 'ppDateOfExpiry_':
      case 'TravelStartDate_':
      case 'TravelEndDate_':
        updateErrorValue(elem, formValidity);
        break;
      case 'spouseName_':
        validateSpouse(elem, formValidity, visaForm);
        break;
      case 'lastName_':
        validateLastName(elem, formValidity);
        break;
      default:
        break;
    }
  });
  return formValidity.valid;
};


export const launchForm = (visaFormObj, params) => {
  VisaNavigation.push(VISA_ROUTE_KEYS.FORM_NEW_PREVIEW,{
    visaForm: visaFormObj,
    visaParams: params
  })
};


export const redirectToFormPreview = (visaFormObj, params) => {
  launchForm(visaFormObj, params);
};


export const onDocumentUploadDone = (data, visaParams, pageName, trackingPageName) => {
  const params = _.cloneDeep(visaParams);
  params.visaBookingId = String(data.visaBookingId);
  params.page = pageName;
  updateDocumentStatusForPax(params, true);
  if (pageName !== PAGENAME.DOCUMENT_VERIFICATION) {
    VisaNavigation.push(VISA_ROUTE_KEYS.OPEN_DOC_VERIFICATION,{
      visaParams: params
    })
  }
  VisaTrackingHelper.trackVisaClickEvent(trackingPageName, 'verify_now');
};

export const onEticketAttachment = (data, visaParams, pageName) => {
  const params = _.cloneDeep(visaParams);
  params.visaBookingId = String(data.visaBookingId);
  params.paxId = String(data.paxId);
  params.page = pageName;
  VisaNavigation.push(VISA_ROUTE_KEYS.UPLOAD_ETICKET,{
    visaParams: params
  })
};

export const scanDocuments = (visaParams, pageName) => {
  const params = _.cloneDeep(visaParams);
  params.page = pageName;
  VisaModule.scanDocuments(params);
};


export const getVisaParamsFromPendingBooking = (visaParams, booking) => {
  const params = _.cloneDeep(visaParams);
  params.startDate = booking.fromDate;
  params.endDate = booking.toDate;
  params.destCountry = booking.countryName;
  params.paxList = [];
  booking.visaPassengers.forEach((elem) => {
    params.paxList.push(getPassenger(elem));
  });
  params.visaId = String(booking.visaId);
  params.visaBookingId = String(booking.bookingId);
  params.payRefId = booking.payRefId;
  params.cameraSelfie = booking.cameraSelfie;
  params.locusCode = booking.locusCode;
  params.locusType = booking.locusType;
  return params;
};


export const getPassenger = elem => ({
  name: elem.name,
  age: String(DEFAULT_ADULT_AGE),
  id: String(elem.paxIdx),
  ageMorY: ADULT_AGE_EXTENSION,
  status: getPaxTypeByStatus(elem.status),
  docs: getDocuments(elem),
  type: ADULT
});

export const getDocuments = (elem) => {
  const docList = [];
  elem && elem.documents && elem.documents.forEach((doc) => {
    docList.push({ type: doc.type, uploaded: doc.uploaded });
  });
  return docList;
};

export const getPaxTypeByStatus = (status) => {
  switch (status) {
    case PASSENGER_STATUS.PASSENGER_INITIATED.status:
      return PASSENGER_STATUS.PASSENGER_INITIATED.type;
    case PASSENGER_STATUS.DOCUMENTS_PENDING.status:
      return PASSENGER_STATUS.DOCUMENTS_PENDING.type;
    case PASSENGER_STATUS.DOCUMENTS_UPLOADED.status:
      return PASSENGER_STATUS.DOCUMENTS_UPLOADED.type;
    case PASSENGER_STATUS.DOCUMENTS_VERIFIED.status:
      return PASSENGER_STATUS.DOCUMENTS_VERIFIED.type;
    case PASSENGER_STATUS.DOCUMENTS_VERFICATION_COMPLETE.status:
      return PASSENGER_STATUS.DOCUMENTS_VERFICATION_COMPLETE.type;
    default:
      return PASSENGER_STATUS.PASSENGER_INITIATED.type;
  }
}

export const getDocumentTypeByName = (name) => {
  switch (name) {
    case VISA_DOCUMENT_TYPE.PASSPORT_FRONT.name:
      return VISA_DOCUMENT_TYPE.PASSPORT_FRONT.type;
    case VISA_DOCUMENT_TYPE.PASSPORT_BACK.name:
      return VISA_DOCUMENT_TYPE.PASSPORT_BACK.type;
    case VISA_DOCUMENT_TYPE.PHOTOGRAPH.name:
      return VISA_DOCUMENT_TYPE.PHOTOGRAPH.type;
    case VISA_DOCUMENT_TYPE.ETICKET.name:
      return VISA_DOCUMENT_TYPE.ETICKET.type;
    case VISA_DOCUMENT_TYPE.HOTEL_VOUCHERS.name:
      return VISA_DOCUMENT_TYPE.HOTEL_VOUCHERS.type;
    case VISA_DOCUMENT_TYPE.FORM.name:
      return VISA_DOCUMENT_TYPE.FORM.type;
    default:
      return VISA_DOCUMENT_TYPE.PASSPORT_FRONT.type;
  }
}

export const populateVisaFormFields = (visaForm, paxData) => {
  const visaFormUnfilled = [];
  visaForm.forEach((elem) => {
    switch (elem.key) {
      case 'firstName_':
        updateValue(visaFormUnfilled, elem, paxData.firstName_);
        break;
      case 'lastName_':
        updateValue(visaFormUnfilled, elem, paxData.lastName_);
        break;
      case 'maritalStatus_':
        updateValue(visaFormUnfilled, elem, paxData.maritalStatus_);
        break;
      case 'gender_':
        updateValue(visaFormUnfilled, elem, paxData.gender_);
        break;
      case 'religion_':
        updateValue(visaFormUnfilled, elem, paxData.religion_);
        break;
      case 'dateOfBirth_':
        updateValue(visaFormUnfilled, elem, paxData.dateOfBirth_);
        break;
      case 'countryOfBirth_':
        updateValue(visaFormUnfilled, elem, paxData.countryOfBirth_);
        break;
      case 'placeOfBirth_':
        updateValue(visaFormUnfilled, elem, paxData.placeOfBirth_);
        break;
      case 'motherName_':
        updateValue(visaFormUnfilled, elem, paxData.motherName_);
        break;
      case 'fatherName_':
        updateValue(visaFormUnfilled, elem, paxData.fatherName_);
        break;
      case 'spouseName_':
        updateValue(visaFormUnfilled, elem, paxData.spouseName_);
        break;
      case 'profession_':
        updateValue(visaFormUnfilled, elem, paxData.profession_);
        break;
      case 'passportNo_':
        updateValue(visaFormUnfilled, elem, paxData.passportNo_);
        break;
      case 'ppDateOfIssue_':
        updateValue(visaFormUnfilled, elem, paxData.ppDateOfIssue_);
        break;
      case 'ppDateOfExpiry_':
        updateValue(visaFormUnfilled, elem, paxData.ppDateOfExpiry_);
        break;
      case 'ppPlaceOfIssue_':
        updateValue(visaFormUnfilled, elem, paxData.ppPlaceOfIssue_);
        break;
      case 'travelStartDate_':
        updateValue(visaFormUnfilled, elem, paxData.travelStartDate_);
        break;
      case 'travelEndDate_':
        updateValue(visaFormUnfilled, elem, paxData.travelEndDate_);
        break;
      default:
        break;
    }
  });
  return visaFormUnfilled;
};

export const updateValue = (visaFormUnfilled, elem, value) => {
  const newElem = elem;
  newElem.selectedVal = value;
  if (!value) {
    visaFormUnfilled.push(elem);
  } else if (elem.type === FORM_DATA_TYPE.DATE) {
    newElem.selectedVal = fecha.format(getDate(newElem.selectedVal), DATE_FORMAT_CALENDAR);
  }
};

export const getVisaFormSubTitle = visaParams => formatNavSubTitle(
  visaParams.destCountry,
  getDisplayDate(visaParams.startDate),
  getPrimaryTraveller(visaParams.paxList)
);


export const getDocExtension = (name) => {
  const splitArr = name.split('.');
  return (splitArr.length > 1 ? splitArr[1] : DEFAULT_DOC_EXTENSION);
};

export const getScreenTitleForEmptyTraveller = (paxList, paxId) => {
  const traveller = getPrimaryTraveller(paxList, paxId);
  if (_.isEmpty(traveller)) {
    return (`${capitalize(TRAVELLER)} ${paxId}/${paxList.length}`);
  }
  return traveller;
};

export const isResponseValid = response =>
  response && (response.statusCode_ === STATUS_CODE_SUCCESS || response.statusCode_ === '');

export const isVisaResponseValid = response =>
  response && (response.status === STATUS_CODE_SUCCESS || response.status === '');

export const isCountryCodeValid = response =>
  response && response !== 0;


export const replaceDocument = (doc, visaParams, page) => {
  const params = visaParams;
  const paxObj = params.paxList[
    params.paxList.findIndex(row => Number(row.id) === Number(visaParams.paxId))];
  const docObj = paxObj.docs[paxObj.docs.findIndex(row => row.type === doc.type)];
  if (docObj) {
    docObj.uploaded = false;
    params.page = page;
    VisaModule.scanDocuments(params);
  } else {
    showShortToast('Unable to replace image, Try again');
  }
};

export const getHypotheticalParentNode = (parent, element) => {
  if (element.url === '') {
    if (element.validations[parent] === '' || element.validations[parent]) {
      return parent;
    }
    return '';
  } else if (element.url.includes(PARENTCURLYBRACES)) {
    return parent;
  } else if (!element.url.includes('&parent=')) {
    return '';
  }
  const splittedVal = element.url.split('=');
  return splittedVal[splittedVal.length - 1];
};

export const isGreater = (a, b) => {
  const _a = a.split('.');
  const _b = b.split('.');
  let i = 0;
  let j = 0;
  while (i < _a.length && j < _b.length) {
    if (Number(_a[i]) > Number(_b[j])) { return true; } else if (Number(_a[i]) < Number(_b[j])) { return false; }
    i += 1;
    j += 1;
  }
  if (i !== _a.length) return true;
  if (j !== _b.length) return false;
  return false;
};

export const isNextKeyGood = (key, closestElemLabel, label) => {
  if (isGreater(key, label) && isGreater(closestElemLabel, key)) {
    return true;
  }
  return false;
};

export const getIndex = (jsonKey) => {
  const keys = jsonKey.split('.');
  const lastKeyWithIndex = keys[keys.length - 1];
  const index = lastKeyWithIndex.split('[')[1];
  return index.split(']')[0];
};

export const setErrorField = (mandatory, val, errorLabel) => {
  if (mandatory && val === '') {
    return 'Cannot leave this blank';
  }
  return errorLabel;
};

export const getSingleMultiInputStyle = (disabled) => {
  if (disabled) { return styles.tabActiveDisabled; }
  return styles.tabActive;
};

export const needToUploadHotelVoucher = (visaParams) => {
  const index = visaParams.paxList[getCurrentPassenger(visaParams).id - 1].docs.findIndex(row => row.type === 'HOTEL_VOUCHER');
  return index !== -1;
};

export const HeaderBodyStyle = {
  COSMOS_HEADER_BODY_STYLE: {
    justifyContent: 'center',
    height: 48,
    marginRight: Platform.select({
      ios: 48,
      android: 0
    }),
    flex: Platform.select({
      ios: 1,
      android: 0
    })
  },
  TRIP_HEADER_BODY_STYLE: {
    alignItems: 'center',
    flex: 1,
    marginRight: 48
  }
};
export const HeaderTitleStyle = {
  COSMOS_HEADER_TITLE_STYLE: {
    color: colors.black,
    fontSize: 18,
    padding: 0,
    alignSelf: 'center',
    alignItems: 'center',
    fontFamily: fonts.bold,
    textAlign: Platform.select({
      ios: 'center',
      android: 'left'
    })
  },
  TRIP_HEADER_TITLE_STYLE: {
    color: colors.black,
    fontSize: 18,
    fontFamily: fonts.bold,
    marginTop: 18,
    lineHeight: 22,
    maxWidth: 300
  }
};

export const HeaderBodyStyleNew = {
  COSMOS_HEADER_BODY_STYLE: {
    justifyContent: 'center',
    marginRight: Platform.select({
      ios: 48,
      android: 0
    }),
    flex: Platform.select({
      ios: 1,
      android: 0
    })
  },
  TRIP_HEADER_BODY_STYLE: {
    alignItems: 'center',
    flex: 1,
    marginRight: 48
  }
};
export const HeaderTitleStyleNew = {
  COSMOS_HEADER_TITLE_STYLE: {
    color: colors.black,
    fontSize: 18,
    padding: 0,
    alignSelf: 'center',
    alignItems: 'center',
    fontFamily: fonts.bold,
    textAlign: Platform.select({
      ios: 'center',
      android: 'left'
    })
  },
  TRIP_HEADER_TITLE_STYLE: {
    color: colors.black,
    fontSize: 18,
    fontFamily: fonts.bold,
    marginTop: 18,
    lineHeight: 22,
    maxWidth: 300
  }
};

export const getPendingCardInfoLabel = (processingTime, applyBefore) => ({
  first: `It takes ${processingTime} working days to get visa. To get visa on time, upload documents and`,
  second: `apply before ${applyBefore}`
});

export const isValidAdultAge = (age) => {
  if (isOnlyDigits(age)) {
    const val = parseInt(age);
    return val > 0 && val < 999;
  }
  return false;
};


export const isValidChildAge = (age) => {
  if (isOnlyDigits(age)) {
    const val = parseInt(age);
    return val > 0 && val <= 11;
  }
  return false;
};

export const isOnlyDigits = numberStr => isValidPattern(DIGIT_REGEXP, numberStr);

export const isValidResponse = response => response && response.status === STATUS_CODE_SUCCESS;

export const appendAgeOfTravellers = (paxList) => {
  let customerAgeCollection = '';
  paxList.forEach((item) => {
    customerAgeCollection = `${customerAgeCollection + item.age.toString()},`;
  });
  customerAgeCollection = customerAgeCollection.slice(0, -1);
  return customerAgeCollection;
}

export const shouldAttach = (formats) => {
  for (const str of formats) {
    switch (str.trim().toLowerCase()) {
      case 'jpg':
      case 'jpeg':
      case 'png':
        return false;
    }
  }
  return true;
}
/**
 * Fills common values of child form from parent form values and return new child values
 * @param {*} parentForm
 * @param {*} childForm
 * @param {*} commonFormKeys keys common b/w both forms
 */
export const getFormResponse   =  (parentForm , childForm, commonFormKeys=[])  => {
  commonFormKeys.forEach(key => {
    if (parentForm.formSchema.fieldValues[key]) {
      childForm.formSchema.fieldValues[key] = parentForm.formSchema.fieldValues[key];
    }
  });
  return childForm.formSchema.fieldValues;
}

export const downloadVisaDocument = (url, fileName, headers , callback  = filePath => {})  => {
  downloadFileWithHeaders(url, fileName, headers, callback);
}
export const handleVisaSampleUrl = (sampleUrl, fileName)  => {
  if (!sampleUrl || !isValidURL(sampleUrl)) {
    // not a valid url, hence do nothing
    return;
  }
  if (!fileName) {
    fileName = 'SAMPLE_VISA_DOCUMENT'
  }
  downloadFile(sampleUrl, fileName);
}
export const makeURLUnique = (url)  => {
  return `${url}?timeStamp=${Date.now()}`;
}

export const updateRecentSearchHistoryForCommon = (prams) => {
  try {
    const { countryCode, destCountry, paxList, startDate, endDate} = prams || {};
    const lob = 'VISA';
    let deeplink = 'mmyt://visa/';
    const dateStart = fecha.parse(startDate, DATE_FORMAT_COMMON);
    const dateEnd = fecha.parse(endDate, DATE_FORMAT_COMMON);
    const data = {
      lob: 'VISA',
      to: destCountry,
      toCode: countryCode,
      description: (paxList && paxList.length > 1) ? paxList.length + ' Travellers' : (paxList.length ? paxList.length + ' Traveller' : ''),
      startDate: dateStart.getTime(),
      returnDate: dateEnd.getTime(),
      deeplink,
    };
    updateRecentSearchHistory(lob, data);
  } catch (e) {
    console.log(e);
  }
};
export const getVisaParamsFromDeepLink = (deeplink) => {
  try {
    const props = queryString.parseUrl(deeplink).query
    if (_.isEmpty(props)) {
      // no data in deeplink
      return {};
    }
    const countryCode = props.countryCode;
    const fromDate = props.fromDate;
    const toDate = props.toDate;
    const paxAge = props.paxAge;
    const locusCode = props.locusCode;
    const params = {};
    params.isFromSearch = false;
    params.infoOnly = false;
    params.startDate = fromDate;
    params.endDate = toDate;
    params.countryCode = countryCode;
    params.destCountry = "";
    params.cameraSelfie = false;
    params.locusCode = locusCode;
    params.paxId = String(1);
    params.paxList = [];
    const paxAgeList = paxAge?.split(',') || []
    paxAgeList.forEach((pax,index)=>{
      params.paxList.push(getPaxObject(`Traveller ${index + 1}`, DEFAULT_ADULT_AGE, ADULT_AGE_EXTENSION, index+1, ADULT));
    })
    params.bookingType = "others";
    return params;
  } catch (error) {
    return {};
  }
}
/**
 *
 * @param {*} deeplink
 * @returns true is deeplink is having some query params otherwise false
 */
export const isValidDeepLink = (deeplink) => {
  try {
    const props = queryString.parseUrl(deeplink).query
    if (_.isEmpty(props)) {
      // no data in deeplink
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
export const formatAmount = (amount = 0, addCurrencySymbol = true, currencySymbol = '₹') => {
  return addCurrencySymbol ? `${currencySymbol} ${amount} ` : `${amount}`
}

export const getSessionID = async (key, value, timeout) => {
  const getValue = await AsyncStorage.getItem(key);
  if (getValue == null) {
    AsyncStorage.setItem(key, value);
    setTimeout(() => {
      AsyncStorage.removeItem(key);
    }, timeout);
  }
  const currentValue = await AsyncStorage.getItem(key);
  return currentValue;
};

export const generateGUID = () => {
  const s4 = () =>
    Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  const guid = `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4() + s4() + s4()}`;
  return guid;
};

export const shouldShowGdprBlockerScreen = async () => {
  const { UserSessionModule } = NativeModules;
  const isGdprRegion = await UserSessionModule.needConsent();
  const isCorporate = await UserSessionModule.isCorporateUser();
  return isGdprRegion && !isCorporate;
}

export const getGdprData = (gdprText) => {
  const { title, header, goBackToHomePage, contactUs } = gdprText || {};

  return {
    "title": title,
    "header": header,
    "shouldCloseOnCross": true,
    "cta": [
      {
        "text": goBackToHomePage,
        "link": "mmyt://app/home/<USER>",
        "trackingKey": "home"
      },
      {
        "text": contactUs,
        "link": "mmyt://app/callus/?01244628747",
        "trackingKey": "contactus"
      }
    ]
  }

}

export const fetchPassportSubtitle = (passport) => {
  var subtitle = '';
  if(passport.supportedType && passport.supportedType.length > 0){
    subtitle = 'Choose from any of ';
    passport.supportedType.map((type) => {
      subtitle += type + ', ';
    });
    subtitle = subtitle.substring(0, subtitle.length - 2);
    subtitle = subtitle + ' formats';
  }
  return subtitle;
}

export const fetchtitle = (category) => {
    switch (category.toUpperCase()) {
      case "PASSPORT":
        return "Passport Details";
      case "PHOTOGRAPH":
      case "IDENTITY":
        return "Add Photograph";
      default:
        return category;
    }
}

export const getImagePath = (imageLink) => {
  switch(imageLink) {
   case 'warningImage':
    return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/warnning_icon.png'};
   case 'submitImage':
      return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/check_circle_icon.png'};
   case 'errorImage':
      return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/cancel_icon.png'};
  case 'uploadIcon':
      return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/uploadIcon.png'};
  case 'cameraIcon':
      return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/camera.png'};
  case 'greenTick':
      return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/greenTick.png'};
  case 'sample':
      return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/sample.png'};
  case 'closeIcon':
    return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/sampleIcon.png'};
  case 'rightArrow':
    return {uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/chevron_right_icon.png'};
  default:
      return ''; 
  }
}
