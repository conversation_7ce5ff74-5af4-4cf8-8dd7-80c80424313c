export default class VisaDataHolder {

  static sInstance = null;

  constructor() {
    this.visaParams = {};
    this.pdt_event_details  = {};
    this.booking_created_timestamp = '';
  }

  static getInstance() {
    if (VisaDataHolder.sInstance == null) {
      VisaDataHolder.sInstance = new VisaDataHolder();
    }
    return this.sInstance;
  }

  getVisaParams() { return this.visaParams; }
  getPdtEventDetails() {return this.pdt_event_details}
  getBookingTimeStamp() {return this.booking_created_timestamp}

  setVisaParams(params) { this.visaParams = params; }
  setPdtEventDetails(params) { this.pdt_event_details = params; }
  setBookingTimeStamp(params) { this.booking_created_timestamp = params; }
}
