export const TYPE_OF_EVENTS = {
  PAGE_LOAD: 'PageLoad',
  POPUP_LOAD: 'PopupLoad',
  CARD_CLICK: 'CardClick',
  BUTTON_CLICK: 'ButtonClick',
  LINK_CLICK: 'LinkClick',
  POPUP_BUTTON_CLICK: 'PopupButtonClick',
  PURPOSE_OF_TRAVEL_LANDING_POPUP_UPLOAD: 'purpose_of_travel_landing_popupload',
  SCHENGEN_MULTICOUNTRY_POPUP_UPLOAD: 'schengenmulticountry_popupload',
  EVISA_INFO_POPUP_UPLOAD: 'evisainfo_popupload/stickerinfo_popupload',
  CALENDAR_POPUP_LOADED: 'calendar_popup_loaded',
  ERROR_PAGE_LOAD: 'error_page_load'
};

export const NAME_OF_EVENTS = {
  BACK_SEARCHPAGE: 'back_searchpage',
  CLOSE_CALENDAR: 'close_calendar',
  DONE_CALENDAR: 'done_calendar',
  SKIP_DOC: 'click_skip_doc',
  SKIP_DOC_POPUP_LOAD: 'skip_doc_popup_load_submitby',
  PENDING_DOCUMENT_POPUP_SHOWN: 'pending_document_popup_shown',
  PENDING_DOCUMENT_POPUP_CLOSED: 'pending_document_popup_closed',
  PENDING_DOCUMENT_POPUP_PRIMARY_CTA_CLICKED: 'pending_document_popup_primarycta_clicked',
  PENDING_DOCUMENT_POPUP_SECONDARY_CTA_CLICKED: 'pending_document_popup_secondarycta_clicked',
  CLOSE_SKIP_DOC: 'click_close_skip_doc',
  ADD_NOW_SKIP_DOC: 'click_add_now_skip_doc',
  CLICK_BULK_UPLOAD: 'click_bulk_upload',
  CONTINUE_DOC_SUBMIT_CLICK: 'continue_doc_submit_click',
  ADD_LATER_DOC_UPLOAD: 'click_add_later_doc_upload',
  SELECT_DOC_TYPE_POPUP_LOAD: 'doc_type_popup_load',
  SELECT_DOC_TYPE_POPUP_CLOSE: 'close_select_doc_bulkupload',
  SELECT_ADD_DOC_BULK_UPLOAD: 'select_addDoc_bulk_upload',
  SELECT_ADD_IMAGE_BULK_UPLOAD: 'select_addImage_bulk_upload',
  CLOSE_VISA_DELIVERY_INFO: 'click_close_visa_delivery_info',
  GOT_IT_VISA_DELIVERY_INFO: 'click_got_it_visa_delivery_info',
  CLOSE_PLAN_FARE_BREAKUP: 'click_close_plan_fare_breakup',
  TOTAL_FARE_BREAKUP: 'click_total_fare_breakup',
  TOTAL_FARE_BREAKUP_POPUP_LOAD: 'total_fare_breakup_popup_loaded',
  TOTAL_FARE_BREAKUP_POPUP_CLOSE: 'close_total_fare_breakup',
  EDIT_TRAVELLER_EMAIL: 'click_edit_traveler_email',
  EDIT_TRAVELLER_MOBILE: 'click_edit_traveler_mobile_number',
  VISA_DELIVERY_INFO_LOAD: 'visa_delivery_info_load',
  REVIEW_CARD_PRICE_INFO_LOAD: 'reviewcard_price_popup_load',
  CLICK_REVIEW_CARD_PRICE_INFO: 'click_reviewcard_price_info',
  CLICK_VISA_DELIVERY_INFO: 'click_visa_delivery_info',
  CLICK_CONTINUE_BULK_IMAGE_UPLOAD: 'click_continue_bulk_image_upload',
  CLICK_ADD_MORE_BULK_UPLOAD: 'click_add_more_bulk_upload',
  CLICK_EDIT: 'click_edit',
  CLICK_DOCUMENT_CHECKLIST: 'click_document_checklist',
  CLICK_REMOVE_IMAGE: 'click_remove_image',
  CONFIRM_REMOVAL_POPUP_LOAD: 'confirm_removal_popup_load',
  CLICK_REMOVE_CONFIRM: 'click_remove_confirm_image',
  CLICK_REMOVE_CANCEL: 'click_remove_cancel_image',
  CLICK_REMOVE_CLOSE: 'click_remove_close_image',
  CANCEL_ANALYSE_BULK: 'cancel_analyse_bulk',
  BULK_UPLOAD_RESULT_PAGE_LOADED: 'doc_classifier_result_loaded',
  CLICK_GO_TO_MYTRIPS: 'click_go_to_mytrips',
  ADD_TO_OTHERS_POPUP_LOADED: 'add_to_others_popup_loaded',
  REPLACE_IMAGE_POPUP_LOADED: 'replace_popup_loaded',
}

export const TRACKING_CONSTANTS = {
  M_V118: 'm_v118',
  M_V119: 'm_v119',
  M_V31: 'm_v31',
  M_V9: 'm_v9',
  M_V46: 'm_v46', 
  M_V4: 'm_v4',
  M_V5: 'm_v5',
  M_V22: 'm_v22',
  M_V16: 'm_v16',
  EVAR_16 : 'evar16',
  evar_119 : 'evar119'
}
