import { Platform, NativeModules } from 'react-native';
import { getCurrentRoute } from '../Navigation';
import { VISA_ROUTE_KEYS } from '../Navigation/visaPageKeys';
import GenericTrackerModule from '@mmt/legacy-commons/Native/GenericTrackerModule';

const { GoMMTCommonModule } = NativeModules;

export const PIXEL_EVENT_NAMES = {
    VISA_DETAIL_PAGE: 'Visa_detail',
    VISA_PLAN_SELECTION_PAGE: 'Visa_plan_selection',
    VISA_REVIEW_PAGE: 'Visa_Initiated_Checkout',
    VISA_THANKYOU_PAGE: 'Visa_Purchase',
}

const ROUTE_TO_PIXEL_EVENT_MAP = {
    [VISA_ROUTE_KEYS.DETAIL]: PIXEL_EVENT_NAMES.VISA_DETAIL_PAGE,
    [VISA_ROUTE_KEYS.SELECT_PLANS]: PIXEL_EVENT_NAMES.VISA_PLAN_SELECTION_PAGE,
    [VISA_ROUTE_KEYS.REVIEW]: PIXEL_EVENT_NAMES.VISA_REVIEW_PAGE,
    [VISA_ROUTE_KEYS.THANK_YOU]: PIXEL_EVENT_NAMES.VISA_THANKYOU_PAGE,
};

const getPixelEventName = () => {
    const currentRoute = getCurrentRoute();

    return ROUTE_TO_PIXEL_EVENT_MAP[currentRoute] || '';
};

const filterEmptyOrUndefinedKeys = (rawObject) => {
    const data = Object.entries(rawObject).reduce(
        (acc, [key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                acc[key] = value;
            }
            return acc;
        },
        {}
    );
    return data;
};

export const sendPixelTrackingEvent = (trackingData) => {
    const eventName = getPixelEventName();

    // Early return if no valid event name
    if (!eventName) {
        return;
    }

    // Build data object, filtering out undefined/null/empty values
    const rawData = {
        date_a: trackingData.dateOfEntry,
        date_b: trackingData.dateOfExit,
        destination: trackingData.countryName,
        pax_price: trackingData.paxPrice,
        price: trackingData.totalPrice,
        num_adults: trackingData.totalAdults,
        num_children: trackingData.totalChildren,
        platform: Platform.OS,
    };

    // Filter out undefined, null, empty strings, and zero values
    const data = filterEmptyOrUndefinedKeys(rawData);
    // Only send if we have meaningful data
    if (Object.keys(data).length === 0) {
        return;
    }

    try {
        if (GenericTrackerModule?.trackFaceBook) {
            GenericTrackerModule.trackFaceBook(eventName, data);
        }
        if(Platform.OS === 'android'){
            if (!GoMMTCommonModule || !GoMMTCommonModule.sendAppsFlyerEvent) {
                return;
            }
            if (GoMMTCommonModule?.sendAppsFlyerEvent) {
                GoMMTCommonModule.sendAppsFlyerEvent(eventName, JSON.stringify(data));
            }
        }else {
            if (GenericTrackerModule?.sendAppsFlyerEvent) {
                const eventData = {
                    event_name: eventName,
                    ...data
                };
                GenericTrackerModule.sendAppsFlyerEvent(eventData);
            }
        }
        if (GenericTrackerModule?.trackFirebase) {
            GenericTrackerModule.trackFirebase(eventName, data);
        }
    } catch {
        return;
    }
};