import {
  getPokusConfigExpDetailsListV2,
  getPokusExpVarientKey,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import VisaDataHolder from '../../Utils/VisaDataHolder';
import { AFFILIATES, CAMPAIGN, FUNNEL_SOURCE, VISA, VISA_PDT_SCHEMA_CONSTANTS } from './constants';
import { getPdtId, pdtIdNames } from './pdtDataHolder';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import store from '@mmt/legacy-commons/AppState/Store';
import { generateUUID } from '@mmt/navigation/src/util';
import { isEmpty } from 'lodash';

const getStateParams = () => {
  const state = store.getState();
  const { visaV2CommonReducers = {}, visaPdtLoggerReducer = {} } = state || {};
  const { visaDetails = {} } = visaV2CommonReducers || {};
  const { countryDetails = {} } = visaDetails || {};
  const {
    page_context = {},
    traveller_info = {},
    search_context = {},
    event_detail = {},
  } = visaPdtLoggerReducer || {};
  const {
    travel_purpose_opted = false,
    pax = {},
    visa_type = '',
    from_date_time = '',
    to_date_time = '',
    search_type = '',
    advance_purchase = '',
    journey_type = '',
  } = search_context || {};
  const { components: { booking_info = {} } = {} } = event_detail || {};
  return {
    visaType: visa_type,
    sub_page_name: page_context?.sub_page_name || '',
    destinationLocationDetails: {
      locus: {
        locus_id: countryDetails?.code,
        locus_type: 'country',
        country: countryDetails?.name,
      },
      location: {
        name: countryDetails?.name,
        type: 'country',
        code: countryDetails?.code,
        country_code: countryDetails?.code,
        country: countryDetails?.name,
      },
    },
    pax,
    travel_purpose_opted,
    from_date_time,
    to_date_time,
    search_type,
    journey_type,
    advance_purchase,
    traveller_info,
    booking_info,
  };
};

export const initializePageContext = () => {
  const currentPage = VisaDataHolder.getInstance().getCurrentPDTPageName();
  const prevPage = VisaDataHolder.getInstance().getPrevPageName(currentPage);
  const funnel_step = VisaDataHolder.getInstance().getCurrentPDTFunnelStep();
  const { visaType = '', sub_page_name = '' } = getStateParams();
  const subFunnel = VisaDataHolder.getInstance().getSubFunnel();
  return {
    lob: VISA,
    ...(!!visaType && { lob_category: visaType }), // e-visa, sticker-visa
    page_name: currentPage,
    prev_page_name: prevPage,
    sub_page_name,
    funnel_step: funnel_step,
    navigation: '',
    travel_store: {
      store_id: subFunnel,
    }
  };
};

export const initializeExperimentalDetails = () => {
  const expList = getPokusConfigExpDetailsListV2(PokusLobs.VISA);
  const expVariant = getPokusExpVarientKey(PokusLobs.VISA);
  const experimentalDetails = {
    valid_exp_list: expList,
    variant_keys: expVariant,
    honoured_exp_list: [],
  };
  return experimentalDetails;
};

export const initializeEventTrackingContext = () => {
  const eventTrackingContext = {
    env: __DEV__ ? 'dev' : 'prod',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    request_id: generateUUID(),
    journey_id: getPdtId({ pdtId: pdtIdNames.JOURNEYID }),
    template_id: VISA_PDT_SCHEMA_CONSTANTS.TEMPLATE_ID,
    topic_name: VISA_PDT_SCHEMA_CONSTANTS.TOPIC_NAME,
    bu: VISA,
    organisation: AFFILIATES.MMT,
    funnel_source: FUNNEL_SOURCE,
    traffic_source: VisaDataHolder.getInstance().getTrafficSource(),
    funnel_entry: VisaDataHolder.getInstance().getTrafficSource(),
  };
  return eventTrackingContext;
};

export const initializeCampaignDetails = () => {
  const campaignDetails = {
    campaign_name: CAMPAIGN,
    campaign_key: VisaDataHolder.getInstance().getCmp(),
  };
  return campaignDetails;
};

export const initializeSearchContext = () => {
  const {
    travel_purpose_opted,
    destinationLocationDetails,
    pax,
    from_date_time,
    to_date_time,
    visaType,
    search_type,
    journey_type,
    advance_purchase,
  } = getStateParams();
  const srcLocation = {
    name: 'India',
    type: 'country',
    country_code: 'IN',
    country: 'India',
    code: 'IN',
  };
  const sourceLocationDetails = {
    locus: {
      locus_id: 'IN',
      locus_type: 'country',
      country: 'India',
    },
    location: srcLocation,
  };
  const searchContext = {
    travel_purpose_opted: !!travel_purpose_opted,
    from: sourceLocationDetails,
    pax: pax || {},
    from_date_time: from_date_time || '',
    to_date_time: to_date_time || '',
    visa_type: visaType || '',
    search_type: search_type || '',
    journey_type: journey_type || '',
    advance_purchase: advance_purchase || '',
  };
  if (destinationLocationDetails.locus.locus_id) {
    searchContext.to = destinationLocationDetails;
  }
  return searchContext;
};

export const initializeEventDetails = () => {
  const { booking_info = {} } = getStateParams();
  const components = {
    // product_list: [],
    // content_details: [],
    // share_details: [],
    ...(!isEmpty(booking_info) && { booking_info }),
  };
  const eventDetails = {
    event_name: '',
    event_type: '',
    event_value: '',
    event_id: generateUUID(),
    event_timestamp: Date.now(),
    ...(!isEmpty(components) && { components }),
  };
  return eventDetails;
};

export const initializeTravellerInfo = () => {
  const { traveller_info } = getStateParams();
  return traveller_info;
};

export const initializeErrorDetails = () => {
  const errorCodeObject = [
    {
      code: null, // string, e.g., "visa002"
      message: null, // string, e.g., "Duration can’t exceed 180 days"
      severity: null, // string, e.g., null
      source: null, // string, e.g., null
    },
  ];
  return errorCodeObject;
};
