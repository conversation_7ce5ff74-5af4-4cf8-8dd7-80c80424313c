import { generateUUID } from 'packages/navigation/src/util';

export const pdtIdNames = {
  REQUESTID: 'requestId',
  JOURNEYID: 'journeyId',
};
export let pdtIds = {
  [pdtIdNames.JOURNEYID]: '',
  [pdtIdNames.REQUESTID]: '',
};

let prevSearchCriteria = '';
export const clearPdtIds = ({ pdtId }) => {
  pdtIds[pdtId] = '';
};

export const setPdtId = ({ pdtId, value }) => {
  pdtIds[pdtId] = value;
};

export const getPdtId = ({ pdtId }) => {
  return pdtIds[pdtId];
};

export const updatePDTJourneyId = ({ searchCriteria }) => {
  if (prevSearchCriteria && searchCriteria === prevSearchCriteria) return;
  else {
    prevSearchCriteria = searchCriteria;
    setPdtId({ pdtId: pdtIdNames.JOURNEYID, value: generateUUID() });
  }
};

export const updatePDTRequestId = () => {
  setPdtId({ pdtId: pdtIdNames.REQUESTID, value: generateUUID() });
};


