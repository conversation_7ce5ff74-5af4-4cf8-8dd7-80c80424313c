import { NativeModules } from 'react-native';
import store from '@mmt/legacy-commons/AppState/Store';
import { generateUUID } from '@mmt/navigation/src/util';
import {
  initializePageContext,
  initializeEventTrackingContext,
  initializeErrorDetails,
  initializeExperimentalDetails,
  initializeCampaignDetails,
  initializeTravellerInfo,
  initializeSearchContext,
  initializeEventDetails,
} from './context';
import { 
  updateInitialLoggerDataAction,
} from '../../Actions/pdtLoggerActions';
import VisaDataHolder from '../../Utils/VisaDataHolder';
import { PDT_EVENT_TYPES } from './constants';

function cleanObject(obj) {
  let keysToIgnore = ['honoured_exp_list'];
  if (Array.isArray(obj)) {
      return obj
          .map(value => cleanObject(value))
          .filter(value => value !== null && value !== "" && !(Array.isArray(value) && value.length === 0));
  } else if (typeof obj === 'object' && obj !== null) {
      const cleanedObj = Object.entries(obj)
          .reduce((acc, [key, value]) => {
              const cleanedValue = cleanObject(value);

              // Do not remove keys if they are in keysToIgnore, even if the value is empty
              if (keysToIgnore.includes(key)) {
                  acc[key] = value;
              } else if (cleanedValue !== null && cleanedValue !== "" && !(Array.isArray(cleanedValue) && cleanedValue.length === 0)) {
                  acc[key] = cleanedValue;
              }
              
              return acc;
          }, {});

      return Object.keys(cleanedObj).length > 0 ? cleanedObj : null;
  } else {
      return obj;
  }
}

const addExtraData = (loggerObj) => {
  const newLoggerObj = loggerObj;
  if(loggerObj?.traveller_info?.length > 0) {
    const updatedTravellerInfo = loggerObj.traveller_info.map(element => {
      if(!element?.title){
        element.title = '';
      }
      if(!element?.mob_id){
        element.mob_id = '';
      }
      return element
    });
    newLoggerObj['traveller_info'] = updatedTravellerInfo;
  }
  return newLoggerObj;
}

const logEventToPDTNative = (loggerObj) => {
  const { PdtV2Module } = NativeModules;
  if (!PdtV2Module?.trackEvent) {
    return;
  }
  try {
    const logobj = cleanObject(loggerObj);
    // adding some extra data which is mandatory as per schema
    const updatedLoggerData = addExtraData(logobj);
    PdtV2Module.trackEvent({ payload: JSON.stringify(updatedLoggerData) })
  } catch (e) {
    console.log("Error in logEventTo PDT: ", e);
  }
};

export const logEventToPDT = (logObj) => {
  try {
    logEventToPDTNative(logObj);
  } catch (e) {}
};

const dispatch = store.dispatch;

export const getLoggerObject = () => {
  const state = store.getState();
  const { visaPdtLoggerReducer = {} } = state || {};
  const loggerObject = visaPdtLoggerReducer;
  return loggerObject;
}

export const initializeLoggerObject = () => {
  const loggerObject = {
    page_context: initializePageContext(),
    event_tracking_context: initializeEventTrackingContext(),
    event_detail: initializeEventDetails(),
    traveller_info: initializeTravellerInfo(),
    search_context: initializeSearchContext(),
    error_details_list: initializeErrorDetails(),
    experiment_details: initializeExperimentalDetails(),
    campaign_details: initializeCampaignDetails(),
  };
  dispatch(updateInitialLoggerDataAction(loggerObject));
  return loggerObject;
}

export const visaPdtEventsInitilizer = () => {
  const loggerObject = initializeLoggerObject();
  loggerObject.event_detail.event_value = 'PAGE_LOAD';
  loggerObject.event_detail.event_name = PDT_EVENT_TYPES.pageRenedered?.event_name;
  loggerObject.event_detail.event_type = PDT_EVENT_TYPES.pageRenedered?.event_type;
  logEventToPDT(loggerObject);
};

export const logPDTEvent = (loggerData = {}) => {
  const { actionType = {}, eventValue = '' } = loggerData || {};
  const loggerObject = getLoggerObject();
  loggerObject.event_tracking_context.request_id = generateUUID();
  loggerObject.event_detail.event_id = generateUUID();
  loggerObject.event_detail.event_timestamp = Date.now();
  loggerObject.event_detail.event_value = eventValue || '';
  loggerObject.event_detail.event_name = actionType?.event_name || '';
  loggerObject.event_detail.event_type = actionType?.event_type || '';
  const currentPage = VisaDataHolder.getInstance().getCurrentPDTPageName();
  if(currentPage !== loggerObject.page_context.page_name) {
    const prevPage = VisaDataHolder.getInstance().getPrevPageName(currentPage);
    loggerObject.page_context.page_name = currentPage;
    loggerObject.page_context.prev_page_name = prevPage;
    loggerObject.page_context.travel_store.store_id = VisaDataHolder.getInstance().getSubFunnel();
    // loggerObject.page_context.funnel_step = currentPage;
  }
  logEventToPDT(loggerObject);
};
