
export const VISA = 'visa';
export const FUNNEL_SOURCE = 'mmt';
export const CAMPAIGN = 'campaign';

export const AFFILIATES = {
  MMT: 'MMT',
  GI: 'GI',
};

// export const VISA_PDT_SCHEMA_CONSTANTS = {
//   TEMPLATE_ID : 98557, //98057,
//   TOPIC_NAME : 'base_event_visa_logging',//'mmt_holidays_b2c_pdt_client_logging',
// };

export const VISA_PDT_SCHEMA_CONSTANTS = {
  TEMPLATE_ID : 158725,
  TOPIC_NAME : 'mmt_visa_b2c_pdt_client_logging',
};
export const PDT_EVENT_NAMES = {
  CONTENT_SEEN: 'content-seen',
  BUTTON_CLICKED: 'button-clicked',
  VALUE_SELECTED: 'value-selected',
  PAGE_RENEDERED: 'page-rendered',
  FILTER_APPLIED: 'filter-applied',
  FILTER_ALL_CLEARED: 'filter_all-cleared',
  SEARCH_CLICKED: 'search-clicked',
  CONTENT_CLICKED: 'content-clicked',
};


export const EVENT_TYPES = {
  USER_ACTION: 'action',
  SYSTEM: 'system',
  LIFE_CYCLE: 'life_cycle',
};


export const eventDetailType = ({ type, name }) => {
  return {
    event_name: name,
    event_type: type,
  };
};

export const PDT_EVENT_TYPES = {
  contentSeen: eventDetailType({
    type: EVENT_TYPES.SYSTEM,
    name: PDT_EVENT_NAMES.CONTENT_SEEN
  }),
  
  contentClicked: eventDetailType({
    type: EVENT_TYPES.USER_ACTION,
    name: PDT_EVENT_NAMES.CONTENT_CLICKED,
  }),
  
  valueSelected: eventDetailType({
    type: EVENT_TYPES.USER_ACTION,
    name: PDT_EVENT_NAMES.VALUE_SELECTED,
  }),
  
  searchClicked: eventDetailType({
    type: EVENT_TYPES.USER_ACTION,
    name: PDT_EVENT_NAMES.SEARCH_CLICKED,
  }),
  
  pageRenedered: eventDetailType({
    type: EVENT_TYPES.SYSTEM,
    name: PDT_EVENT_NAMES.PAGE_RENEDERED
  }),
  
  filterApplied: eventDetailType({
    type: EVENT_TYPES.USER_ACTION,
    name: PDT_EVENT_NAMES.FILTER_APPLIED,
  }),
  
  filterAllCleard: eventDetailType({
    type: EVENT_TYPES.USER_ACTION,
    name: PDT_EVENT_NAMES.FILTER_ALL_CLEARED,
  }),
  
  buttonClicked: eventDetailType({
    type: EVENT_TYPES.USER_ACTION,
    name: PDT_EVENT_NAMES.BUTTON_CLICKED,
  }),
};
