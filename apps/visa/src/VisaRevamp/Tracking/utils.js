import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import VisaDataHolder from '../Utils/VisaDataHolder';
import { TRACKING_CONSTANTS } from './constants';
import { getCurrentRoute } from '../Navigation';
import store from '@mmt/legacy-commons/AppState/Store';
import fecha from 'fecha';

const getStateParams = () => {
  const state = store.getState();
  const { visaV2CommonReducers = {} } = state || {};
  const { visaDetails = {}, trackingParams = {} } = visaV2CommonReducers || {};
  const { countryDetails = {}, dateOfEntry = '', dateOfExit = '' } = visaDetails || {};
  const {
    price: initialPrice = '',
    potId = '',
    finalPrice = '',
    errorMessage = '',
    bookingId = '',
  } = trackingParams || {};
  return {
    countryName: countryDetails?.name,
    potId,
    price: finalPrice || initialPrice || '',
    dateOfEntry,
    dateOfExit,
    errorMessage,
    bookingId,
  };
};

const getDataHolderParams = () => {
  const currentPage = VisaDataHolder.getInstance().getCurrentPageName();
  const prevPage = VisaDataHolder.getInstance().getPrevPageName(currentPage);
  return { currentPage, prevPage };
};
export const visaTrackClickEvent = ({ eventName = '', eventType = '', params = {} }) => {
  const { currentPage = '', prevPage = '' } = getDataHolderParams();
  const {
    countryName = '',
    potId = '',
    price = '',
    dateOfEntry = '',
    dateOfExit = '',
    errorMessage = '',
    bookingId = '',
  } = getStateParams();
  let dateOfArrival = '';
  let dateOfDeparture = '';
  try {
    dateOfArrival = params.dateOfEntry || fecha.format(dateOfEntry, 'DD-MM-YYYY') || '';
    dateOfDeparture = params.dateOfExit || fecha.format(dateOfExit, 'DD-MM-YYYY') || '';
  } catch {
    // do nothing
  }
  let visaParams = {
    [TRACKING_CONSTANTS.M_V119]: eventName,
    [TRACKING_CONSTANTS.M_V118]: eventType,
    ...((countryName || params?.countryName) && {
      [TRACKING_CONSTANTS.M_V31]: countryName || params.countryName || '',
    }),
    ...(potId && {
      [TRACKING_CONSTANTS.M_V9]: potId || '',
    }),
    ...(price && {
      [TRACKING_CONSTANTS.M_V46]: price || '',
    }),
    ...((params?.dateOfEntry || dateOfEntry) && {
      [TRACKING_CONSTANTS.M_V4]: dateOfArrival,
    }),
    ...((params?.dateOfExit || dateOfExit) && {
      [TRACKING_CONSTANTS.M_V5]: dateOfDeparture,
    }),
    ...((!!params?.errorMessage || !!errorMessage) && {
      [TRACKING_CONSTANTS.M_V22]: params.errorMessage || errorMessage,
    }),
    ...(bookingId && {
       [TRACKING_CONSTANTS.M_V16]: bookingId,
    }),
    ...(bookingId && {
       [TRACKING_CONSTANTS.EVAR_16]: bookingId,
    }),
    ...((params.newParam)&& {
      [TRACKING_CONSTANTS.evar_119]: params.newParam,
    }),
  };
  const { pageName = '' } = params;
  VisaTrackingHelper.trackVisaClickEventV2({
    pageName: pageName ? `${currentPage}:${pageName}` : currentPage,
    clickEvent: eventName,
    previousPage: prevPage,
    extraParams: visaParams,
  });
};
