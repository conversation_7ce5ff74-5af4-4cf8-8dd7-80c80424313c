import React from 'react';
import Button from '@Frontend_Ui_Lib_App/Button';
import { fontStyles } from '../../../Styles/fonts';
import { paddingStyles } from '../../../Styles/Spacing';
import { buttonColors } from '../../../Styles/colors';
const ButtonWpr = ({
  disabled,
  buttonText,
  buttonSize,
  buttonType,
  buttonWidth,
  endIcon,
  startIcon,
  buttonBgColors,
  customStyle,
  onButtonPress,
  children = null,
}) => {
  return (
    <Button
      disabled={disabled}
      buttonBgColors={
        buttonBgColors
          ? buttonBgColors
          : [buttonColors.primaryBtnColor, buttonColors.secondaryBtnColor]
      }
      buttonSize={buttonSize ? buttonSize : 'md'}
      buttonText={buttonText}
      buttonType={buttonType ? buttonType : 'fill'}
      buttonWidth={buttonWidth ? buttonWidth : '100%'}
      customStyle={{
        buttonStyle: { ...paddingStyles.pv16 },
        ...(customStyle
          ? customStyle
          : {
              buttonTextStyle: { ...fontStyles.labelLargeBold },
            }),
      }}
      startIcon={startIcon ? startIcon : ''}
      endIcon={endIcon ? endIcon : ''}
      onButtonPress={onButtonPress}
      useAngle
    >
      {children}
    </Button>
  );
};

export default ButtonWpr;
