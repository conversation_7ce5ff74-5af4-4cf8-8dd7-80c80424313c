import React from 'react';
import { StyleSheet } from 'react-native';
import Accordion from '@Frontend_Ui_Lib_App/Accordion';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';

const AccordionWpr = ({ title, icon, onPress, children, titleStyle }) => {
    return (
        <Accordion
            title={title}
            icon={icon}
            onPress={onPress}
            children={children}
            customStyles={{titleStyle: {...styles.title, ...titleStyle}}}
        />
    )
}

const styles = StyleSheet.create({
    title: {
        
        ...fontStyles.labelBaseBold,
        color: colors.primaryBlue
    }
})

export default AccordionWpr;