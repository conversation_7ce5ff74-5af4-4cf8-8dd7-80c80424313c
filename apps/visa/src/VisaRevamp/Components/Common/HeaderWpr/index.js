import React from 'react';
import { StyleSheet } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';

/* Icons */
const backIcon = require('@mmt/legacy-assets/src/back.webp');
const crossIcon = require('@mmt/legacy-assets/src/crossIcon.webp');

/* Components */
import Header from '@Frontend_Ui_Lib_App/Header';

const HeaderWrapper = ({
  titleText,
  descText,
  leftIconSource,
  clickHandler,
  wrapperStyle,
  titleStyle,
  descriptionStyle,
  iconStyle,
  rightComponent,
  titleNoOfLines,
  rightIcons,
  actionTextData, 
  actionHandler,
}) => {
  return (
    <Header
      title={titleText}
      actionText={{
        onPress: actionHandler,
        text: actionTextData,
        customStyles: {
          textStyle: {
            ...fontStyles.labelBaseBold,
            color: colors.primaryBlue,
          }
        },
      }}
      description={descText}
      leftIcon={{
        icon: leftIconSource ? leftIconSource : backIcon,
        onPress: clickHandler,
        customStyles: {
          iconStyle: [
            { width: 14, height: 14, tintColor: colors.lightGray, backgroundColor: 'transparent' },
            iconStyle,
          ],
          iconContainerStyle: {
            padding: 6,
            paddingLeft: 0,
            marginRight: 10
          }
        },
      }}
      customStyles={{
        wrapperStyle: [getPlatformElevation(3), { zIndex: 2, paddingVertical: 12  }, wrapperStyle],
        titleStyle: [styles.titleText, titleStyle],
        descriptionStyle: [descriptionStyle],
      }}
      rightComponent={rightComponent}
      titleNoOfLines={titleNoOfLines ? titleNoOfLines : 1}
      rightIcons={rightIcons}
    />
  );
};

const styles = StyleSheet.create({
  titleText: {
    fontWeight: 700,
  },
});

export default HeaderWrapper;
