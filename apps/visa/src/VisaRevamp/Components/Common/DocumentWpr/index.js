import { StyleSheet, View, Image, TouchableOpacity, PermissionsAndroid, Platform, Linking, Text } from 'react-native';
import React, { useState, useEffect } from 'react';
import { colors } from '../../../Styles/colors';
import { borderRadius } from '../../../Styles/borderRadius';
import { fontStyles } from '../../../Styles/fonts';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { getImagePath, getSImagePath } from '../../../Utils/VisaUtils';
import { isEmpty } from 'lodash';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import Button from '../ButtonWpr';
import HeaderWpr from './HeaderWpr';
import ErrorMessage from './ErrorMessage';
import { launchImageLibrary, launchCamera } from 'react-native-image-picker';
import { cameraRollNode, checkIsCameraPermissionGranted } from '../../../Pages/UploadDocument/utils';
import { uploadClassifierFile } from '../../../Utils/NetworkUtils';
// import DocumentPicker from 'react-native-document-picker';
import { DocumentPickerOptions, pick, types } from '@react-native-documents/picker';
import AddDocumentsImage from '../BottomSheetComponents/AddDocumentsImage';
import BottomSheetWpr from '../BottomSheetWpr';
import Seperator from '../Seperator';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_SUB_PAGE_NAMES, VIEW_STATES } from '../../../constants';
import ProgressBar from '@Frontend_Ui_Lib_App/ProgressBar';
import LineLoaderComp from '../../../Components/Common/Loader/LineLoader';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { ALERTS } from '../../../textStrings';

const getErrorMessage = (item) => {
  let criticalError = "";
  if (item.prevError) {
    if (item.prevError.criticalError && !(item.uploaded)) {
      criticalError = item.prevError.criticalError;
    }
  }
  return criticalError;
}

const getWarningMessage = (item) => {
  let softError = "";
  if (item.prevError) {
    if (item.prevError.softError && (item.uploaded)) {
      softError = item.prevError.softError;
    }
  }
  return softError;
}

const VISA_ERRORMESSAGES = {
  IMAGE_ERROR: 'Please fix errors in the image',
  WARNING_ERROR: 'We recommend that you fix the below errors',
}

const getButtonName = (savedDocs, type) => {
  let doc = savedDocs;
  if (doc && doc.uploaded) {
    return 'REPLACE';
  }
  return 'UPLOAD';
}

const DocumentWrapper = ({
  isError,
  isSuccess,
  title,
  subTitle,
  type,
  data,
  onupload,
  imageUrl,
  handleSampleOverlay,
  bookingId,
  paxIndex,
  docSetStatusList,
  setDocStatusList,
  callTracking,
  paxName,
}) => {
  const [uploadedImages, setUploadedImages] = useState(null);
  const [openBulkUploadDetail, setOpenBulkUploadDetail] = useState(false);
  const [viewState, setViewState] = useState(VIEW_STATES.SUCCESS);
  const [qcStatusHandling, setQcStatusHandling] = useState(data?.qcStatus);

  const toggleModal = () => {
    setOpenBulkUploadDetail(!openBulkUploadDetail);
  };

  const closeShowBulkUpload = () => {
    visaTrackClickEvent({
      eventName: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_CLOSE,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_CLOSE,
    });
    setOpenBulkUploadDetail(false);
  }

  const handleOpencamera = async (bookingId, paxIndex, type) => {
    const isAndroid = Platform.OS === 'android';
    if (isAndroid) {
      const havingPermission = await checkIsCameraPermissionGranted();
      if (havingPermission) {
        handlecamera(bookingId, paxIndex, type);
      } else {
        showLongToast('To enable camera permission . Go to: "Settings > Apps & Notifications > Makemytrip > Permissions" and enable camera permission');
        Linking.openSettings();
      }
    } else {
      handlecamera(bookingId, paxIndex, type)
    }

  }

  const handlecamera = async (bookingId, paxIndex, type) => {
    typeof callTracking === 'function' && callTracking('CAMERA', type);
    let setImages = [];

    let options = {
      storageOptions: {
        skipBackup: true,
        path: "images",
      },
    };
    launchCamera(options, async (res) => {
      let image = res.assets[0];
      let request = {};
      const newImageUri = image.uri
      request.data = {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      };
      request.documentName = image.fileName;
      request.format = "image/jpeg";
      request.bookingId = bookingId;
      request.paxId = paxIndex;
      request.docId = 1;
      request.docType = type;
      fetchData(request, null, image.uri);
      setImages.push(res);
    });
  }

  const openDocGallery = async (bookingId, paxIndex, type) => {
    openPhoneDocGallery(bookingId, paxIndex, type);
  }

  const openPhoneDocGallery = (bookingId, paxIndex, type) => {
    pick({
      type: [types.pdf],
      allowMultiSelection: false,
    }).then((results) => {
      for (const res of results) {
        let request = {};
        request.data = {
          uri: res.uri,
          type: res.type,
          name: res.name,
        };
        request.documentName = res.name;
        request.format = res.type;
        request.bookingId = bookingId;
        request.paxId = paxIndex;
        request.docId = 1;
        request.docType = type;
        fetchData(request, null, res.uri);
        setImages.push(res);
      }
    });
  };

  const openGallery = async (bookingId, paxIndex, type) => {
    openPhoneGallery(1, bookingId, paxIndex, type);
  }

  const openPhoneGallery = (selectionLimit, bookingid, paxIndex, type) => {
    const imageGalleryParams = {
      mediaType: 'photo',
      selectionLimit: 1,
    };
    // trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, 'upload');

    launchImageLibrary(imageGalleryParams, async (res) => {
      return selectImages(res.assets || [], bookingid, paxIndex, type);
    });
  }

  const selectImages = (images, bookingid, paxIndex, type) => {
    let setImages = [];
    images.forEach((image) => {
      const compatNode = cameraRollNode(null, image);
      let request = {};
      request.data = {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      };
      request.documentName = image.fileName;
      request.format = image.type;
      request.bookingId = bookingid;
      request.paxId = paxIndex;
      request.docId = 1;
      request.docType = type;
      fetchData(request, compatNode);
      setImages.push(compatNode);
    });
  };

  const openUpload = (bookingId, paxIndex, type, subTitle, action = '') => {
    if (subTitle && subTitle.length > 0 && subTitle.includes('pdf')) {
      setOpenBulkUploadDetail(true);
    }
    else {
      openGallery(bookingId, paxIndex, type);
    }
    typeof callTracking === 'function' && callTracking(action, type);
  }

  const visaOmnitureTracking = () => {
    visaTrackClickEvent({
      eventName: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD,
      eventType: TYPE_OF_EVENTS.POPUP_LOAD,
    })
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.contentSeen,
      eventValue: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD,
    });
  };

  async function fetchData(request, compatNode, uri) {
    setViewState(VIEW_STATES.LOADING);
    const response = await uploadClassifierFile(request, false);
    if (response) {
      setQcStatusHandling('')
      let newDocuments = {
        type: request.docType,
        url: compatNode ? compatNode.node.image.uri : uri,
        rulesResp: response.rulesResp,
        uploaded: response.uploaded,
        messages: response.messages,
      }
      let newDocSetStatusList = [];
      docSetStatusList.map((doc) => {
        if (doc.type === request.docType) {
          doc = doc;
          doc.uploaded = true;
          if (!doc.prevError) {
            doc.prevError = {};
            if (response.uploaded && response.rulesResp) {
              doc.prevError = { "prevError": { "criticalError": null, "softError": response.rulesResp } };
            } else if (!response.uploaded && response.rulesResp) {
              doc.prevError = { "prevError": { "criticalError": response.rulesResp, "softError": null } };
            }
          } else {
            if (response.uploaded && response.rulesResp) {
              doc.prevError = { "criticalError": null, "softError": response.rulesResp };
            } else if (!response.uploaded && response.messages) {
              doc.prevError = { "criticalError": response.messages, "softError": null };
              doc.uploaded = false;
            }
          }
          newDocSetStatusList.push(doc);
        }
        else {
          newDocSetStatusList.push(doc);
        }
      })
      if (response) {
        if (response.firstName && response.lastName) {
          if (paxName != (response.firstName + " " + response.lastName))
            showShortToast(ALERTS.PASSENGER_INFO_UPDATED);
        }
      }
      setDocStatusList(newDocSetStatusList);
      setUploadedImages(newDocuments);
      closeShowBulkUpload();
      setViewState(VIEW_STATES.SUCCESS);
    }
  }

  const isSuccessOrError = (data) => {
    if (data.uploaded) {
      return true;
    }
    (getWarningMessage(data) && getWarningMessage(data).length > 0) || (getErrorMessage(data) && getErrorMessage(data).length > 0) ? isError : isSuccess;
  }

  const renderSuccessView = () => {
    return (
      <View style={styles.photoWrapper}>
        {!uploadedImages && (
          <>
            <HeaderWpr title={title} subTitle={subTitle} isError={(getErrorMessage(data) && getErrorMessage(data).length > 0)} isSuccess={isSuccessOrError(data)}
              isWarn={(getWarningMessage(data) && getWarningMessage(data).length > 0)} qcStatusHandling={qcStatusHandling}/>
          </>
        )}
        {uploadedImages && (
          <>
            <HeaderWpr title={title} subTitle={subTitle} isError={!uploadedImages.uploaded} isSuccess={uploadedImages.uploaded && uploadedImages.rulesResp === null}
              isWarn={uploadedImages.uploaded && uploadedImages.rulesResp != null} qcStatusHandling={qcStatusHandling}/>
          </>
        )}
        <Seperator strokeDasharray={0} />
        <View style={paddingStyles.pa16}>
          <View style={styles.middleSectioAPP}>
            {data.type.includes("PHOTOGRAPH") &&
              <View style={styles.profileImgWrapper}>
                {uploadedImages && (
                  <>
                    <Image
                      style={styles.profileImgStyle}
                      source={{ uri: uploadedImages.url }}
                    />
                  </>
                )}
                {
                  !uploadedImages &&
                  <Image
                    style={styles.profileImgStyle}
                    source={imageUrl ? imageUrl : getSImagePath(data.sampleDocUrl)}
                  />
                }
                <View style={{ position: 'relative' }}>
                  <TouchableOpacity style={styles.sampleIconWrapper} onPress={() => handleSampleOverlay('sample')}>
                    <Image style={styles.sampleIconStyle} source={getImagePath('sample')} />
                  </TouchableOpacity>
                </View>
              </View>

            }
            {!uploadedImages && !data.type.includes("PHOTOGRAPH") &&
              <Image
                style={styles.ImgStyle}
                source={imageUrl ? imageUrl : getSImagePath(data.sampleDocUrl)}
              />
            }
            {uploadedImages && !data.type.includes("PHOTOGRAPH") &&
              <Image
                style={styles.ImgStyle}
                source={{ uri: uploadedImages.url }}
              />
            }
          </View>
          {!isEmpty(getErrorMessage(data)) && <ErrorMessage
            warning={false}
            title={VISA_ERRORMESSAGES.IMAGE_ERROR}
            msg={getErrorMessage(data)}
          />}
          {!isEmpty(getWarningMessage(data)) && <ErrorMessage
            warning={true}
            title={VISA_ERRORMESSAGES.WARNING_ERROR}
            msg={getWarningMessage(data)}
          />}
          {qcStatusHandling === 'REJECTED' && <ErrorMessage
            warning={false}
            title={data?.qcRejectionText}
            msg={[data?.qcrejectionreason]}
          />}
          <View style={styles.BtnSectionAPP}>
            <Button
              disabled={data?.disableUpload}
              buttonSize="xl"
              buttonText={getButtonName(data, type)}
              buttonType="outline"
              customStyle={{
                buttonTextStyle: [data?.disableUpload ? null : AtomicCss.azure, AtomicCss.blackFont, AtomicCss.font16],
                buttonWrapperStyle: {
                  width: '48%',
                },
              }}
              startIcon={getImagePath(data?.disableUpload ? 'disableUploadIcon' : 'uploadIcon')}
              onButtonPress={() => openUpload(bookingId, paxIndex, type, subTitle, getButtonName(data, type))}
            />
            < Button
              disabled={data?.disableUpload}
              buttonSize="xl"
              buttonText="CAMERA"
              buttonType="outline"
              customStyle={{
                buttonTextStyle: [data?.disableUpload ? null : AtomicCss.azure, AtomicCss.blackFont, AtomicCss.font16],
                buttonWrapperStyle: {
                  width: '48%',
                },
              }}
              startIcon={getImagePath(data?.disableUpload ? 'disableCameraIcon' : 'cameraIcon')}
              onButtonPress={() => handleOpencamera(bookingId, paxIndex, type)}
            />
          </View>
          {openBulkUploadDetail && (
            <BottomSheetWpr
              title={'Select Type'}
              visible={toggleModal}
              isCrossIcon={true}
              onDismiss={closeShowBulkUpload}
              bottomsheetName={PDT_SUB_PAGE_NAMES.DOC_TYPE_POPUP}
              callTracking={visaOmnitureTracking}
              setVisible={toggleModal}
              titleContainerStyles={{
                ...paddingStyles.ph20,
                ...paddingStyles.pv16,
                borderBottomColor: colors.grayBorder,
                borderBottomWidth: 1,
              }}
              containerStyles={{
                backgroundColor: colors.lightGray2,
                paddingHorizontal: 0,
                paddingVertical: 0,
              }}
              children={
                <AddDocumentsImage openDocGallery={() => openDocGallery(bookingId, paxIndex, type)} openGallery={() => openGallery(bookingId, paxIndex, type)}></AddDocumentsImage>
              }
            />
          )}

        </View>
      </View>
    )
  };

  const renderBulkLoadingView = () => {
    return (
      <View style={styles.centeredView}>
        <View style={styles.darkenedBackground}>
          <View style={styles.modalView}>
            <Image source={getImagePath('uploadDocImage')} style={styles.iconStyle} />
            <Text style={styles.modalText}>Adding Document</Text>
            <LineLoaderComp />
          </View>
        </View>
      </View>
    );
  };

  switch (viewState) {
    case VIEW_STATES.LOADING:
      return renderBulkLoadingView();
    case VIEW_STATES.SUCCESS:
      return renderSuccessView();
    case VIEW_STATES.ERROR:
      return renderErrorView();
  }

};

export default DocumentWrapper;

const styles = StyleSheet.create({

  photoWrapper: {
    // backgroundColor: colors.white,
    // ...borderRadius.br8,
    // borderWidth: 1,
    // borderColor: colors.transparent
  },
  middleSectioAPP: {
    // margin: 10,
    height: 160,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.lightBlueBg,
    borderRadius: 8,
  },
  profileImgStyle: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
  },
  profileImgWrapper: {
    width: 145,
    height: 135,
    justifyContent: 'center',
  },
  passportWrapper: {
    justifyContent: 'center',
  },
  ImgStyle: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  errorContainer: {
    // paddingHorizontal: 16,
    marginBottom: 16,
  },
  BtnSectionAPP: {
    // paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  playBtnWrapper: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#008CFF',
    backgroundColor: '#fff',
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    justifyContent: 'center',
  },
  playIconStyle: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  passportStyle: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  errorBorder: {
    borderColor: 'red',
  },
  profileWrapper: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 4,
    justifyContent: 'center',
  },
  sampleIconWrapper: {
    width: 42,
    height: 30,
    marginTop: -30,
    alignSelf: 'center'
  },
  sampleIconStyle: {
    width: 42,
    height: 38,
  },
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  darkenedBackground: {
    backgroundColor: colors.white, // Semi-transparent black background
    justifyContent: 'center',
    alignItems: 'center',
    height: '30%',
    width: '50%',
  },
  modalView: {
    backgroundColor: colors.white,
    borderRadius: 16,
    ...paddingStyles.pa60,
    alignItems: 'center',
    elevation: 5,
    width: 244,
    height: 105,
  },
  modalText: {
    ...marginStyles.mb24,
    ...fontStyles.labelMediumBold,
    color: colors.gray,
  },
  iconStyle: {
    width: 32,
    height: 32,
    ...marginStyles.mb12,
  },
});
