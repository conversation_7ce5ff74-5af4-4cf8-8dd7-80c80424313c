import { StyleSheet, Text, Image, View } from "react-native";
import React from "react";
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import {
  getImagePath
} from '../../../Utils/VisaUtils';

const HeaderWpr = ({ title, subTitle, isError, isSuccess, isWarn,  qcStatusHandling}) => {

  return (
    <View style={styles.headerWrapperAPP}>
      {isError && qcStatusHandling !== 'REJECTED' && <Image source={getImagePath('errorImage')} style={styles.iconStyle} />}
      {isSuccess && qcStatusHandling !== 'REJECTED' && <Image source={getImagePath('submitImage')} style={styles.iconStyle} />}
      {(isWarn  || qcStatusHandling === 'REJECTED') && <Image source={getImagePath('warningImage')} style={styles.iconStyle} />}
      <View style={styles.textWrapper}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subTitle}>{subTitle}</Text>
      </View>
      {qcStatusHandling &&
        <View style={styles.rejectionWrapper}>
          <Text style={qcStatusHandling === 'REJECTED' ? styles.rejectedText : styles.approvedText}>{qcStatusHandling}</Text>
          <Image source={getImagePath(qcStatusHandling === 'REJECTED' ? 'rejectedImage' : 'acceptedImage')} style={styles.rejectedIconStyle} />
        </View>
      }
    </View>
  );
};

const styles = StyleSheet.create({
  headerWrapperAPP: {
    ...paddingStyles.pa16,
    flexDirection: 'row',
  },
  iconStyle: {
    width: 22,
    height: 22,
    ...marginStyles.mr8,
  },
  rejectedIconStyle: {
    width: 20,
    height: 20,
  },
  title: {
    ...fontStyles.labelMediumBold,
    ...marginStyles.mb2,
    color: colors.black,
  },
  subTitle: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
  },
  rejectedText: {
    ...paddingStyles.pr4,
    // ...fontStyles.labelMediumBold,
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.red,
  },
  approvedText: {
    ...paddingStyles.pr4,
    // ...fontStyles.labelMediumBold,
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.greenDark,
  },
  rejectionWrapper: {
    flexDirection: 'row',
    color: colors.red,
    marginLeft: 'auto',
},
});
export default HeaderWpr;