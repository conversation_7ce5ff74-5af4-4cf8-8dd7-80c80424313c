import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';

import TagsContainer from '../Tags';
import { VISA_TYPE_CODES } from '../../../constants';
import { isEmpty } from 'lodash';
import LinearGradient from 'react-native-linear-gradient';

const SectionContainer = ({
  title = '',
  hideHeader = false,
  subTitle = '',
  component,
  showTag = false,
  hideBorder = false,
  sectionSpacing = {},
  tagType = '',
  tagDetails = {},
  bgColors = null,
  titleStyles = {},
  countryName = '',
}) => {
  return (
    <LinearGradient
      colors={bgColors ? bgColors : [colors.white, colors.white]}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 0.5 }}
      style={[
        styles.sectionContainer,
        isEmpty(sectionSpacing) ? {} : sectionSpacing,
        hideHeader ? paddingStyles.pt0 : {},
      ]}
    >
      {hideHeader ? null : (
        <View
          style={[
            styles.headerSection,
            hideBorder ? { borderBottomWidth: 0 } : { ...marginStyles.mb20 },
          ]}
        >
          <View style={styles.container}>
            {!!title && <Text style={[styles.title, titleStyles]}>{title}</Text>}
            {!!subTitle && <Text style={styles.subTitle}>{subTitle}</Text>}
          </View>
          <View>
            {showTag && (
              <TagsContainer
                tagType={tagType}
                tagDetails={tagDetails}
                countryName={countryName}
                from={title}
              />
            )}
          </View>
        </View>
      )}
      {component}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  sectionContainer: {
    backgroundColor: colors.white,
    paddingVertical: 32,
    ...marginStyles.mb8,
  },
  headerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...paddingStyles.pb16,
    ...paddingStyles.ph16,
    borderBottomWidth: 1,
    borderBottomColor: colors.grayBorder,
  },
  container: {
    flex: 1,
  },
  title: {
    ...fontStyles.labelLargeBlack,
    color: colors.black,
  },
  subTitle: {
    color: colors.gray,
    ...fontStyles.labelBaseRegular,
    ...marginStyles.mt4,
  },
});
export default SectionContainer;
