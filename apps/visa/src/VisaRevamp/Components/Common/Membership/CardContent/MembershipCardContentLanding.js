import React from 'react';
import { Pressable, Text, View } from 'react-native';
import styles from '../styles/MembershipCardStyles';
import { noop } from 'lodash';
import { getTextStyles } from '../utils/MembershipUtils';
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';

// Custom function to render text with both highlighting (*) and strikethrough (||)
const renderFormattedText = (text, highlightedStyle, normalStyle, numberOfLines) => {
  if (!text) return null;

  // Split text by both separators while keeping the separators
  const parts = text.split(/([\*\|]{1,2})/);
  const result = [];
  let isHighlighted = false;
  let isStrikethrough = false;

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    
    if (part === '*') {
      isHighlighted = !isHighlighted;
      continue;
    } else if (part === '||') {
      isStrikethrough = !isStrikethrough;
      continue;
    } else if (part === '|') {
      // Skip single pipes that are part of ||
      continue;
    }

    if (part) {
      let textStyle = normalStyle;
      
      if (isHighlighted && isStrikethrough) {
        // Both highlighted and strikethrough
        textStyle = [highlightedStyle, { textDecorationLine: 'line-through' }];
      } else if (isHighlighted) {
        // Only highlighted
        textStyle = highlightedStyle;
      } else if (isStrikethrough) {
        // Only strikethrough
        textStyle = [normalStyle, { textDecorationLine: 'line-through' }];
      }

      result.push(
        <Text key={i} style={textStyle}>
          {part}
        </Text>
      );
    }
  }

  return (
    <Text style={normalStyle} numberOfLines={numberOfLines}>
      {result}
    </Text>
  );
};

const MembershipCardContent = (props) => {
  const {
    onKnowMorePress = noop,
    cta = '',
    headerText = '',
    headerText2 = '',
  } = props || {};
  return (
    <>
      {headerText && (
        <Pressable onPress={cta ? () => onKnowMorePress(cta) : noop}>
          <View style={styles.landingHeaderLineHeight}>
            {headerText2 ? (
              renderFormattedText(
                headerText2,
                styles.messageTextHighlighted,
                styles.messageText,
                3
              )
            ) : (
              <HighlightedText
                str={headerText}
                highlightedTxtStyle={styles.messageTextHighlighted}
                normalTxtStyle={styles.messageText}
                separator="*"
                numberOfLines={3}
              />
            )}
            {cta && (
              <Text style={styles.knowMoreText}>{cta}</Text>
            )}
          </View>
        </Pressable>
      )}
    </>
  );
};

export default MembershipCardContent;
