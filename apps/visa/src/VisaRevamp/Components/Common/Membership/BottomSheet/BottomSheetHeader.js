import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity, Platform } from 'react-native';
import CrossIcon from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import styles from './styles';
import FastImage from 'react-native-fast-image';


const noop = () => { }; // Define a named no-op function

/**
 * Renders the header component for the bottom sheet.
 *
 * @component
 * @returns {JSX.Element} The rendered header component.
 * @param props
 */
const BottomSheetHeader = (props) => {
  const { headerImage = '', togglePopup = noop } = props || {};
  
  const isAndroidClient = () => Platform.OS === 'android';
  const platformAndroid = isAndroidClient();
  const priority = platformAndroid ? FastImage.priority.high : FastImage.priority.normal;

  return (
    <View style={styles.bottomSheetHeader}>
      <TouchableOpacity onPress={togglePopup} style={styles.crossIconWrapper}>
        <Image style={styles.headerCrossIcon} source={CrossIcon} />
      </TouchableOpacity>
      <View>
        <FastImage
          style={styles.image}
          source={{ uri: headerImage, priority: priority }}
          resizeMode={'contain'}
        />
      </View>
    </View>
  );
};

export default React.memo(BottomSheetHeader);
