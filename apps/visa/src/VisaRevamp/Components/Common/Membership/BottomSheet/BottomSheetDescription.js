import React from 'react';
import { View, Text } from 'react-native';
import DetailsCard from './DetailsCard';
import styles from './styles';
import { noop } from 'lodash';

const BottomSheetDescription = ({
  headerText = '',
  modalBody = {},
  borderGradient = '',
  handleTermConditionClick = noop,
}) => {
  return (
    <View style={styles.bottomSheetDescCont}>
      <View style={styles.descHeadingCont}>
        <Text style={styles.descHeadingText}>{headerText}</Text>
      </View>
      <DetailsCard {...{ modalBody, borderGradient }} />
    </View>
  );
};

export default React.memo(BottomSheetDescription);
