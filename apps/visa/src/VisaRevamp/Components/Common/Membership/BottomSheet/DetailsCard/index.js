import React from 'react';
import { Image, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles.js';
import { getBottomSheetDescriptionGCListMargin, validateBorderGradientColors } from '../../utils/MembershipUtils';
import { colors } from '../../../../../Styles/colors';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';

/**
 * Renders a details card component.
 *
 * @param {Object} props - The component props.
 * @returns {JSX.Element} The rendered details card component.
 */
const DetailsCard = ({ modalBody = {}, borderGradient = '' }) => {
  return (
    <LinearGradient
      colors={
        validateBorderGradientColors(borderGradient)
          ? borderGradient.split(',')
          : [colors.black, colors.black]
      }
      start={{ x: 0, y: 1 }}
      end={{ x: 0, y: 0 }}
      style={styles.gradientBorder}
    >
      <LinearGradient
        colors={
          [colors.white, colors.white]
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.container}
      >
        <View style={styles.detailsCardCont}>
          {/* Upper section of the details card */}
          <View
            style={[
              styles.upperSectionDetailsCard
            ]}
          >
            {/* Icon for the "Refer & Earn" section */}
            {modalBody.imageUrl && (
              <View style={styles.referEarnImgCont}>
                <Image source={{ uri: modalBody.imageUrl }} resizeMode='contain' style={styles.referEarnImg} />
              </View>
            )}
            <View style={[styles.upperSectionDetailsCardDesc, modalBody.imageUrl ? {} : { marginLeft: 0, width: '100%' }]}>
              {modalBody.title && (
                <View>
                  <Text style={styles.bottomSheetDetailCardHeadingGreen}>{modalBody.title}</Text>
                </View>
              )}
              {modalBody.description && (
                <View style={{ ...marginStyles.mt4 }}>
                  <Text style={styles.subHeaderTxt}>{modalBody.description}</Text>
                </View>
              )}
              <View style={getBottomSheetDescriptionGCListMargin(modalBody.title, modalBody.description)}>
                {/* Render the default gift cards */}
                {modalBody?.details?.map((item, index) => (
                  <View style={styles.giftCardFlex} key={index + item?.order}>
                    <View style={styles.giftCardIconTxtFlex}>
                      {/* Icon for each gift card */}
                      {item?.imageUrl && (
                        <Image source={{ uri: item?.imageUrl }} resizeMode='contain' style={styles.giftCardImgStyle} />
                      )}
                      {/* Text for each gift card */}
                      <Text style={styles.subHeaderTxt}>{item?.specialFee?.title}</Text>
                    </View>
                    <View>
                      {/* Price of each gift card */}
                      <Text style={styles.subHeaderTxt}>{item?.specialFee?.description}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </View>
          <View style={styles.lowerSectionDetailsCard}>
            <View style={styles.lowerSectionDetailsCardDesc}>
                <Text style={styles.subHeaderTxt}>{modalBody?.totalFare?.text}</Text>
                <Text style={styles.bottomSheetDetailCardHeading}>{modalBody?.totalFare?.fare}</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </LinearGradient>
  );
};

export default React.memo(DetailsCard);
