import React, { useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import BottomSheetDescription from './BottomSheetDescription';
import ButtonWpr from '../../ButtonWpr';
import BottomSheetHeader from './BottomSheetHeader';
import { noop } from 'lodash';
import styles from './styles';

const MMTBlackBottomSheet = (props) => {
  const { mmtBlackData = {}, bottomSheetDetail = {}, mmtBlackBucketDetail = {}, togglePopup = noop, ctaButtonClick = noop, mmtBlackPdtEvents = noop, handleTermConditionClick = noop, trackClickEvent = noop } = props || {};
  const { overlayHeaderImage, headerText, modalBody, tnc, borderGradient, ctaButton } = bottomSheetDetail || {};

  useEffect(() => {
  }, []);

  return (
    <View style={styles.topcontainer}>
      <BottomSheetHeader
        headerImage={mmtBlackData?.premiumServices?.otherDetails?.imageUrl ? mmtBlackData?.premiumServices?.otherDetails?.imageUrl : mmtBlackData?.premiumServices?.imageUrl}
        togglePopup={togglePopup}
      />
      <ScrollView showsVerticalScrollIndicator={false}>
        <BottomSheetDescription
          headerText={mmtBlackData?.premiumServices?.otherDetails?.header}
          borderGradient={mmtBlackData?.premiumServices?.borderGradient}
          modalBody={mmtBlackData?.premiumServices?.otherDetails?.description}
        />
      </ScrollView>
      <View style={styles.submitbtnContainer}>
        <ButtonWpr
          buttonText={'GOT IT'}
          buttonSize={{ fontSize: 14 }}
          onButtonPress={togglePopup}
        />
        {/* <SubmitButton
          text={ctaButton?.text}
          ctaButtonClick={ctaButtonClick}
        /> */}
      </View>
    </View>
  );
};

export default React.memo(MMTBlackBottomSheet);
