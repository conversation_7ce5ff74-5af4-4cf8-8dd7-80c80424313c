import { colors } from '../../../../Styles/colors';
import { fontStyles } from '../../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { Dimensions, StyleSheet } from 'react-native';
const {height} = Dimensions.get('window');

const styles = StyleSheet.create({
  topcontainer:{
    maxHeight:height-250
  },
  container: {
    width: '100%',
    height: 24, // Adjust this height if needed
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  image: {
    height:40,
    width: 220,
    alignSelf: 'flex-start',
  },
  bottomSheetHeader: {
    display: 'flex',
    flexDirection: 'row',
    paddingBottom: 16,
    paddingTop: 24,
    borderBottomWidth: 1,
    borderBottomColor: colors.grayBorder,
    paddingLeft: 16,
    paddingRight: 16,
  },
  headerCrossIcon: {
    height: 24,
    width: 24,
    alignSelf: 'center',
    tintColor: colors.lightGray,
  },
  headerImg: {
    marginRight: 4,
  },
  headerDetailsCont: {
    marginLeft: 16,
  },
  headerText: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 2,
  },
  mmtBlackTxt: {
    color: colors.gray,
    ...fontStyles.labelMediumBlack,
  },
  tierTxt: {
    color: colors.black,
    ...fontStyles.labelMediumBold,
    marginLeft: 2,
  },
  bottomSheetDescCont: {
    marginLeft: 16,
    marginRight: 16,
    marginTop: 16,
  },
  descHeadingCont: {
    marginBottom: 12,
  },
  descHeadingText: {
    color : colors.gray,
    ...fontStyles.labelMediumBold,
    fontSize: 14,
    emphasis: true,

  },
  descHeading: {
    marginBottom: 4,
  },
  descHeadingTxt: {},
  subHeadingCont: {
    marginBottom: 12,
  },
  subHeadingTxt: {
    color: colors.black,
    ...fontStyles.labelBaseRegular,
  },
  crossIconWrapper: {
    justifyContent: 'center',
  },
  submitbtnContainer:{
    paddingTop: 20,
    marginBottom: 24,
    marginHorizontal: 16
  }
});

export default styles;
