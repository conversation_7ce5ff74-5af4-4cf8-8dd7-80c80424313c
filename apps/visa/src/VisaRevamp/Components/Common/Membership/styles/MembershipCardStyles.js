import { StyleSheet } from 'react-native';
import { colors } from '../../../../Styles/colors';
import { fontStyles } from '../../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

export default StyleSheet.create({
  gradientBorder: {
    borderRadius: 13,
    padding: 1,
  },
  container: {
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    paddingTop: 18,
    paddingBottom: 12,
    borderRadius: 12,
  },
  title: {
    ...fontStyles.labelBaseBlack,
    marginBottom: 12,
    color: colors.black,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 4,
    flexWrap: 'wrap',
  },
  bullet: {
    height: 8,
    width: 8,
    marginTop: 4,
    // alignSelf: 'center',
  },
  itemText: {
    flex: 1,
    marginLeft: 8,
  },
  knowMoreText: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
    "emphasize": false,
    fontSize: 12,
  },
  cardImage: {
    height: 28,
    resizeMode: 'contain',
    position: 'absolute',
    top: -13,
    zIndex: 3,
    ...marginStyles.ml12,
    width: 170,
  },
  landingHeaderLineHeight: {
    lineHeight: 18,
  },
  messageTextHighlighted: {
    ...fontStyles.labelBaseBold,
    color: colors.green,
    fontSize: 14,
    lineHeight: 18,
  },
  messageText : {
    ...fontStyles.labelBase,
    color: colors.gray,
    lineHeight: 18,
  }
});
