import React, { useEffect } from 'react';
import { Image, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles/MembershipCardStyles';
import MembershipCardContentLanding from './CardContent/MembershipCardContentLanding';
import { colors } from '../../../Styles/colors';
import { validateBorderGradientColors } from './utils/MembershipUtils';
//import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import { noop } from 'lodash';
//import { PDTConstants } from '../../../Review/HolidayReviewConstants';
import { PDT_SUB_PAGE_NAMES,CONTENT_TYPES } from '../../../constants';

/**
 * Renders a membership card component.
 *
 * @param {Object} props - The component props.
 * @param {Object} props.sectionDetails - The details of the card section.
 * @param {Object} props.gradientStyle - The style for the gradient.
 * @param {Function} props.onKnowMorePress - The function to be called when "Know More" is pressed.
 * @param {Object} props.containerStyles - The styles for the container.
 * @returns {JSX.Element} The rendered membership card component.
 */
const MembershipCard = (props) => {
  // Destructure the props and provide default values if they are not provided
  const {
    sectionDetails = {},
    gradientStyle = {},
    onKnowMorePress = noop,
    containerStyles = {},
    mmtBlackPdtEvents = noop,
    trackMemberShipLoadEvent = noop,
    sectionOrder = '',
    mmtBlackBucketDetail = {},
    contentType = '',
    sendLoadEvents = true,
  } = props || {};

  // useEffect hook to perform side effects when the component mounts
  useEffect(() => {
    // Call the mmtBlackPdtEvents function with the actionType and value
    
  }, []);

  // Render the membership card component
  return (
    <View style={containerStyles}>
      {/* Render an overlay image if sectionDetails.overlayImage exists */}
      {sectionDetails?.premiumServices && sectionDetails?.premiumServices?.imageUrl && (
        <Image source={{ uri: sectionDetails?.premiumServices?.imageUrl }} style={styles.cardImage} />
      )}
      {/* Render a linear gradient border */}
      <LinearGradient
        colors={
          // Validate and split the border gradient colors
          validateBorderGradientColors(sectionDetails?.premiumServices?.borderGradient)
            ? sectionDetails?.premiumServices?.borderGradient?.split(',')
            : [colors.black, colors.black]
        }
        start={{ x: 0, y: 1 }}
        end={{ x: 0, y: 0 }}
        style={[styles.gradientBorder, gradientStyle]}
      >
        {/* Render a linear gradient container */}
        <LinearGradient
          colors={[colors.white, colors.white]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.container, gradientStyle, sectionDetails?.premiumServices?.imageUrl ? {} : {paddingTop:8, paddingBottom:8}]}
          accessible
          accessibilityLabel="MMTBLACK card"
        >
          {/* Render the content of the membership card */}
          {contentType === CONTENT_TYPES.LANDING ? (
            <MembershipCardContentLanding
              cta={sectionDetails?.premiumServices?.knowMore ? 'Know More':''}
              headerText={sectionDetails?.premiumServices?.description}
              onKnowMorePress={onKnowMorePress}
            />
          ) : (
            <MembershipCardContentLanding
              cta={sectionDetails?.premiumServices?.knowMore ? 'Know More':''}
              headerText={sectionDetails?.premiumServices?.description}
              headerText2={sectionDetails?.premiumServices?.slashDescription}
              onKnowMorePress={onKnowMorePress}
            />
          )}
        </LinearGradient>
      </LinearGradient>
    </View>
  );
};

export default MembershipCard;
