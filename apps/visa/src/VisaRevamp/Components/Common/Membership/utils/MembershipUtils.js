import { CARD_TYPES, MEMBERSHIP_CARD_TYPE } from './constants';
import { colors } from '../../../../Styles/colors';
import { getFontFamily, normaliseFont }  from '../../../../Styles/fonts';


// Function to get text styles based on the provided textStyle object
export const getTextStyles = (textStyle) => {
  const styles = {
    ...getFontFamily(textStyle?.bold? '700' : '400'),
  };
  if (textStyle?.color) {
    styles.color = textStyle.color;
  } else {
    styles.color = colors.black;
  }
  if (textStyle?.bold) {
    styles.fontWeight = '700';
  } else {
    styles.fontWeight = '400';
  }
  if (textStyle?.emphasize) {
    styles.fontStyle = 'italic';
  } else {
    styles.fontStyle = 'normal';
  }
  if (textStyle?.font) {
    styles.fontSize = normaliseFont(+textStyle.font);
  } else {
    styles.fontSize = normaliseFont(14);
  }
  if (textStyle?.lineHeight) {
    styles.lineHeight = +textStyle?.lineHeight;
  } 
  return styles;
};

// Function to get review gradient colors based on the provided cardType
export const getReviewGradientColors = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return [colors.reviewGoldDark, colors.reviewGoldLight];
    case CARD_TYPES.PLATINUM:
      return [colors.platinumDark, colors.platinumLight];
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [colors.green, colors.lightGreen];
    default:
      return [colors.grayDark, colors.grayLight];
  }
};

// Function to get gradient colors based on the provided cardType
export const getGradientColors = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return [colors.goldDark, colors.goldLight];
    case CARD_TYPES.PLATINUM:
      return [colors.platinumDark, colors.platinumLight];
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [colors.green, colors.lightGreen];
    default:
      return [colors.grayDark, colors.grayLight];
  }
};

// Function to get review inner gradient color based on the provided cardType
export const getReviewInnerGradientColor = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return [colors.reviewGoldLight, colors.white];
    case CARD_TYPES.PLATINUM:
      return [colors.platinumLight, colors.white];
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [colors.white, colors.white, colors.fadedGreen];
    case CARD_TYPES.INNER_Gold:
      return [colors.goldDark, colors.goldLight];
    default:
      return [colors.white, colors.white];
  }
};

// Function to get inner gradient color based on the provided cardType
export const getInnerGradientColor = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [colors.white, colors.white, colors.white];
    default:
      return [colors.white, colors.white];
  }
};

// Function to get member color based on the provided cardType
export const getMemberColor = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return colors.goldLight;
    case CARD_TYPES.PLATINUM:
      return colors.grayLight;
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return colors.grayLight;
    default:
      return colors.grayLight;
  }
};



// Function to validate border gradient colors
export const validateBorderGradientColors = borderGradient => {
  if (typeof borderGradient !== 'string') return false;

  return borderGradient
    .split(',')
    .every(color => isValidColor(color.trim()));
};

// Function to check if a color is valid
const isValidColor = (color) => {
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return colorRegex.test(color);
};

export const getBottomSheetDescriptionGCListMargin = (header, subHeader) => {
  if(!header && !subHeader) {
    return { marginTop: -10 };
  }
  else if(!subHeader) {
    return { marginTop: -4 };
  }
  return {};
}

export const getMemberShipCardTypeWithOrder = (membershipData) => {
    let cardsWithOrder = '';
    membershipData?.cards?.forEach((card, index) => {
      if(index !== 0) {
        cardsWithOrder += '_';
      };
      if(card.type === MEMBERSHIP_CARD_TYPE.MMT_BLACK) {
        cardsWithOrder +=  `MMTBLACK_${index+1}`;
      }
      if(card.type === MEMBERSHIP_CARD_TYPE.PZN) {
        const pznCardType = card?.personalizationDetail?.section?.type;
        cardsWithOrder +=  `PZN_${pznCardType}_${index+1}`;
      }
    })
    return cardsWithOrder;
}
