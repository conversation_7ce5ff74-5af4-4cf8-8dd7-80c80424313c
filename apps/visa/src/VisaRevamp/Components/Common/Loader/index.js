import React, { useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { colors } from '../../../Styles/colors';

const CircleLoaderComp = ({ colorCode = ''}) => {
  return (
    <View style={{ flexGrow: 1, padding: 16, justifyContent: 'center', alignItems: 'center' }}>
      <Spinner
        size={40}
        strokeWidth={3}
        progressPercent={90}
        speed={1.5}
        color={colorCode || colors.primaryBlue}
      />
    </View>
  );
};
export default CircleLoaderComp;
