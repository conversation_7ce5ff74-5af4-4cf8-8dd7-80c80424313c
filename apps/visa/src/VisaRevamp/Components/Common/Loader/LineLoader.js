import React, { useState } from 'react';
import { View } from 'react-native';
import LineLoader from '@Frontend_Ui_Lib_App/LineLoader';
const LineLoaderComp = () => {
  return (
    <View
      style={{
        flex: 1,
        padding: 16,
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <LineLoader customStyles={{}}
        animationTiming={1}
        finalScaleValue={1.5}
        lineTestID="animated-line"
        loaderColor="#008cff"
        loaderWidth={50}
        sliderContainerWidth={200}
        wprTestID="line-wpr"
      />
    </View>
  );
};
export default LineLoaderComp;
