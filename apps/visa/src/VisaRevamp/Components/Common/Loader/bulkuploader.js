import React, { useEffect } from 'react';
import { Modal, Text, View, Image, StyleSheet } from 'react-native';
import ProgressBar from '@Frontend_Ui_Lib_App/ProgressBar';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { getImagePath } from '../../../Utils/VisaUtils';
import LineLoader from '@Frontend_Ui_Lib_App/LineLoader';

const BulkUploader = ({
    modalVisible,
    setModalVisible,
    loaderText,
    loaderSubText,
    iconName = 'uploadDocImage',
    showCancelButton = false,
    onCancelPress
}) => {

    useEffect(() => {
        let timer;
        if (modalVisible) {
            timer = setTimeout(() => {
                setModalVisible(false);
            }, 3000);
        }
        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [modalVisible]);

    return (
        <View style={styles.centeredView}>
            <Modal
                animationType="none"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => {
                    setModalVisible(false)
                }}>
                <View style={styles.centeredView}>
                    <View style={styles.darkenedBackground}>
                        <View style={styles.modalView}>
                            <Image
                                source={getImagePath(iconName)}
                                style={styles.iconStyle}
                            />
                            <Text style={styles.modalText}>{loaderText}</Text>
                            <Text style={styles.modalSubText}>{loaderSubText}</Text>
                            <LineLoader customStyles={{}}
                                animationTiming={1}
                                finalScaleValue={1.5}
                                lineTestID="animated-line"
                                loaderColor="#008cff"
                                loaderWidth={50}
                                sliderContainerWidth={200}
                                wprTestID="line-wpr"
                            />
                            {showCancelButton && <Text style={styles.modalCancelText} onPress={onCancelPress}>CANCEL</Text>}
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
};


export default BulkUploader;

const styles = StyleSheet.create({
    containerHome: {
        flex: 1,
        backgroundColor: colors.lightGray2,
    },
    footer: {
        position: "absolute",
        bottom: 0,
        width: "100%"
    },
    centeredView: {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: "transparent",
    },
    darkenedBackground: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
        justifyContent: 'center',
        alignItems: 'center',
        height: "100%",
        width: "100%"
    },
    modalView: {
        backgroundColor: colors.white,
        borderRadius: 16,
        ...paddingStyles.pa20,
        alignItems: 'center',
        elevation: 5,
        width: 244,
        height: 195
    },
    modalText: {
        ...marginStyles.mb10,
        ...fontStyles.labelMediumBold,
        color: colors.gray,
    },
    modalSubText: {
        ...marginStyles.mb10,
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
    },
    modalCancelText: {
        color: colors.primaryBlue,
        ...marginStyles.ma16,
        ...fontStyles.labelSmallRegular,
    },
    iconStyle: {
        width: 32,
        height: 32,
        ...marginStyles.mb12
    },
    snackBarLayout: {
        wrapperStyle: {
            width: 340,
            height: 48,
            ...marginStyles.ml16,
            justifyContent: "space-between",
            alignItems: "center"
        },
        contentStyle: {
            ...fontStyles.labelBaseRegular,
            color: colors.white,
        }

    }
})