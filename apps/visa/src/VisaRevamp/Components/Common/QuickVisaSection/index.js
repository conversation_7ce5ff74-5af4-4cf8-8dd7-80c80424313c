import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ImageBackground,
  ScrollView,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { borderRadius } from '../../../Styles/borderRadius';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';
import TagsContainer from '../Tags';
import { isEmpty } from 'lodash';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';

const QuickVisaSection = ({ details = {}, handleOpenPOT = () => {} }) => {
  const { data: visaProcessDurationData = [] } = details || {};
  if (isEmpty(visaProcessDurationData)) {
    return null;
  }
  const renderCard = ({ item, idx }) => {
    const lastIndex = visaProcessDurationData.length - 1;
    const lastRow = idx === lastIndex ? styles.lastIndex : null;
    const {
      countryName = '',
      description = '',
      visaServiceName = '',
      backgroundImgLink = '',
    } = item || {};

    const handleTracking = () => {
      visaTrackClickEvent({
        eventName: `${idx}_${countryName?.replace(' ', '_')}_${details?.header?.replace(' ', '_')}`,
        eventType: TYPE_OF_EVENTS.CARD_CLICK,
      });

      logPDTEvent({
        actionType: PDT_EVENT_TYPES.valueSelected,
        eventValue: 'click_country_visa_less_than_a_week',
      });
    };
    const handleClick = () => {
      handleTracking();
      handleOpenPOT(item);
    };
    return (
      <TouchableOpacity
        onPress={handleClick}
        style={[styles.contentBoxWrapper, lastRow]}
        key={`process-${idx}`}
        activeOpacity={1}
      >
        {!!backgroundImgLink && (
          <ImageBackground
            source={{ uri: backgroundImgLink }}
            style={styles.backgroundImage}
            resizeMode="contain"
          />
        )}
        <View style={styles.innerContentWrapper}>
          <View style={marginStyles.mb8}>
            <TagsContainer
              tagType={visaServiceName}
              size={'small'}
              countryName={countryName}
              from={details?.header}
            />
          </View>
          <Text style={styles.countryText}>{countryName}</Text>
          <Text style={styles.descriptionText}>{description}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <View style={styles.contentWrapper}>
      <ScrollView
        style={styles.containerStyles}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
      >
        {visaProcessDurationData?.map((item, idx) => renderCard({ item, idx }))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  contentWrapper: {
    ...paddingStyles.ph16,
  },
  containerStyles: {
    marginHorizontal: -16,
  },
  countryText: {
    ...fontStyles.labelLargeBold,
    color: colors.black,
    ...marginStyles.mb6,
    lineHeight: 24,
    width: '80%',
  },
  contentBoxWrapper: {
    backgroundColor: colors.white,
    ...borderRadius.borderRadius8,
    ...marginStyles.ml16,
    position: 'relative',
    width: 180,
    height: 195,
    borderWidth: 1,
    borderColor: colors.grayBorder,
  },
  innerContentWrapper: {
    ...paddingStyles.pa16,
  },
  lastIndex: {
    ...marginStyles.mr16,
  },
  descriptionText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
    ...marginStyles.mb8,
    width: '80%',
  },
  backgroundImage: {
    width: 130,
    height: 127,
    position: 'absolute',
    right: 0,
    bottom: 0,
    borderBottomRightRadius: 8,
    overflow: 'hidden',
  },
});

export default QuickVisaSection;
