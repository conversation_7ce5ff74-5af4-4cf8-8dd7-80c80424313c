/* eslint-disable import/no-unresolved */
import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
// import styles from './style';
import { capitalize } from '../../../Utils';
import { colors } from '../../../Styles/colors';

const RenderWeekHeader = ({ WeekHeader, WeekEndHeader }) => {
  const fullWeekArr = [...WeekHeader, ...WeekEndHeader];
  return (
    <View style={styles.weekHeaderRow}>
      {fullWeekArr.map((day, index) => (
        <Text key={`${day}_${index}`} style={styles.headerText}>
          {capitalize(day)}
        </Text>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  weekHeaderRow: {
    marginBottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderBottomColor: colors.grayBorder,
    borderBottomWidth: 0.5,
    borderTopColor: colors.grayBorder,
    borderTopWidth: 0.5,
    height: 40,
    alignItems: 'center',
  },
});
export default RenderWeekHeader;
