/* eslint-disable import/no-unresolved */
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { prepareDisabledDays } from '../../../Utils';
// import { addDays } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { DEVICE_WINDOW, getScreenWidth } from '../../../Utils';

/* Components */
import RenderWeekHeader from './WeekHeader';
import CalendarCommon from '@RN_UI_Lib/CalendarCommon';

function addDays(days = 1, theDate: Date) {
  const newDate = new Date();
  newDate.setHours(0, 0, 0);
  const changeDate = theDate || newDate;
  return new Date(changeDate.getTime() + days * 24 * 60 * 60 * 1000);
}

const CELL_HEIGHT = 68;
const MONTH_HEADER_HEIGHT = 54;

const CalendarComponent = ({ startDate, onDateRangeSelection, dateOfEntry, dateOfExit, maxSearchDays = -1 }) => {
  const getMonthsToRender = (endDate) => {
    const value = [];
    const today = new Date();
    const minimumDate = new Date();
    const maximumDate = addDays(maxSearchDays === -1 || maxSearchDays === 0 ? 365 : maxSearchDays, today);
    let minMonth = minimumDate.getMonth();
    let minYear = minimumDate.getFullYear();
    const maxMonth = maximumDate.getMonth();
    const maxYear = maximumDate.getFullYear();
    do {
      value.push({ month: minMonth, year: minYear });
      if (minMonth === maxMonth && minYear === maxYear) {
        break;
      }
      if (minMonth === 11) {
        minMonth = 0;
        minYear += 1;
      } else {
        minMonth += 1;
      }
    } while (true);
    return value;
  };

  const renderWeekHeader = (WeekHeader, WeekEndHeader) => (
    <RenderWeekHeader WeekHeader={WeekHeader} WeekEndHeader={WeekEndHeader} />
  );

  return (
    <View style={styles.calendarContainer}>
      <CalendarCommon
        onlyCheckin={false} // This is needed since default is true
        value={getMonthsToRender()}
        checkInDate={new Date(dateOfEntry)}
        {...(dateOfExit ? { checkOutDate: new Date(dateOfExit) } : {})}
        minDate={new Date(startDate)}
        cellHeight={CELL_HEIGHT}
        customMonthTitleStyles={styles.monthRow}
        customYearLabelStyles={styles.monthText}
        customMonthLabelStyles={styles.monthText}
        customDateWprStyles={{
          ...fontStyles.labelBaseRegular,
          color: colors.black,
        }}
        renderWeekHeader={renderWeekHeader}
        showRangeBars={true}
        onDateRangeSelection={onDateRangeSelection}
        monthHeaderHeight={MONTH_HEADER_HEIGHT}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  calendarContainer: {
    flex: 1,
    width: DEVICE_WINDOW.width,
  },
  headerText: {
    ...fontStyles.labelSmallRegular,
    color: colors.lightGray,
  },
  monthRow: {
    borderBottomColor: colors.grayBorder,
    borderBottomWidth: 0.5,
  },
  monthText: {
    color: colors.gray,
    ...fontStyles.labelMediumBold,
  },
});
export default CalendarComponent;
