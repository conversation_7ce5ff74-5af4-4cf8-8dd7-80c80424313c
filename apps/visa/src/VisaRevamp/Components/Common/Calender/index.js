import React, { useState, useEffect } from 'react';
import fecha, { differenceInDays } from 'fecha';
import { Image, Platform, View, StyleSheet, Text } from 'react-native';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { addDays, today, diffDays } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { isEmpty } from 'lodash';
import { paddingStyles, marginStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { colors, iconColors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import store from 'packages/legacy-commons/AppState/Store';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';
import { resetSubPageNameAction, updateSubPageNameAction } from '../../../Actions/pdtLoggerActions';
import BottomSheetWpr from '../BottomSheetWpr';
import ConfirmTravelDetails from '../BottomSheetComponents/ConfirmTravelDates';

/* Components */
import CalendarComponent from './CalendarComponent';
import Card from '@mmt/legacy-commons/Common/Components/Card/index';
import HeaderWpr from '../HeaderWpr';
import ButtonWpr from '../ButtonWpr';
import InputBox from './InputBox';
import { PDT_SUB_PAGE_NAMES } from '../../../constants';
import { ALERTS, BUTTON_CTA_MAP, PAGE_HEADERS, STRING_MAP } from '../../../textStrings';
import { formatDateRange } from './utils';

/* Icons */
const crossIcon = require('@mmt/legacy-assets/src/cross_tg.webp');

const VisaCalendar = (props) => {
  const {
    onDone,
    onCalendarBack,
    calenderDetails,
  } = props || {};

  const dispatch = store.dispatch;
  const {
    minSearchDays = '',
    maxDuration = '',
    maxSearchDays = '',
    maxDurationPersuasion = '',
    dateRange = '',
  } = calenderDetails || {};

  const startDate = addDays(today(), minSearchDays);
  const endDate = addDays(startDate, dateRange || 1);
  const [dateOfEntry, setDateOfEntry] = useState(startDate);
  const [dateOfExit, setDateOfExit] = useState(endDate);
  const [confirmTravelDates, setConfirmTravelDates] = useState(false);

  const formattedDateRange = formatDateRange(dateOfEntry, dateOfExit);
  const formattedDateRangeDefault = formatDateRange(startDate, endDate);
  

  useEffect(() => {
    dispatch(updateSubPageNameAction(PDT_SUB_PAGE_NAMES.CALENDAR));
    visaTrackClickEvent({
      eventName: `${minSearchDays}`,
      eventType: TYPE_OF_EVENTS.CALENDAR_POPUP_LOADED,
    });
    logPDTEvent({
      eventValue: 'calendar_loaded',
      actionType: PDT_EVENT_TYPES.contentSeen,
    });
    return () => {
      dispatch(resetSubPageNameAction());
    };
  }, []);

  const _onDone = () => {
    if(formattedDateRangeDefault !== formattedDateRange) {
      onDone(dateOfEntry, dateOfExit);
    } else {
      handleOpenConfirmTravelDatesBottomSheet();
    }
    // onDone(dateOfEntry, dateOfExit);
    // handleOpenConfirmTravelDatesBottomSheet();
  };

  const handleOpenConfirmTravelDatesBottomSheet = () => {
    setConfirmTravelDates(true);
  };
  const handleCloseConfirmTravelDatesBottomSheet = () => {
    setConfirmTravelDates(false);
  };

  const onDateRangeSelection = (date, dateOfEntry = '', dateOfExit = '') => {
    const maxDate = addDays(dateOfEntry, maxDuration);
    setDateOfEntry(dateOfEntry);
    if (dateOfExit <= maxDate) {
      setDateOfExit(dateOfExit);
    } else {
      showShortToast(ALERTS.DATE_EXCEEDED?.replace('{maxDuration}', maxDuration));
    }
  };
  const activeNoOfDays = Math.abs(diffDays(today(), new Date(dateOfEntry))) + maxDuration;

  return (
    <View style={styles.calendarContainer}>
      <HeaderWpr
        titleText={PAGE_HEADERS.CALENDAR}
        leftIconSource={crossIcon}
        iconStyle={{ tintColor: colors.lightGray, width: 25, height: 15 }}
        clickHandler={onCalendarBack}
      />
      <CalendarComponent
        onDateRangeSelection={onDateRangeSelection}
        startDate={startDate}
        dateOfEntry={dateOfEntry}
        dateOfExit={dateOfExit}
        maxSearchDays={maxSearchDays}
      />

      <View style={[styles.footerContainer]}>
        <View style={styles.visaInfoContainer}>
          <Text style={styles.visaInfoText} numberOfLines={2}>
            {maxDurationPersuasion ||
              STRING_MAP.CALENDAR_DATE_PERSUASION?.replace('{maxDuration}', maxDuration)}
          </Text>
        </View>
        <View style={styles.footerSection}>
          <View style={styles.calendarDateContainer}>
            <InputBox placeholderText={STRING_MAP.DATE_OF_DEPARTURE} date={dateOfEntry} />
            <InputBox placeholderText={STRING_MAP.DATE_OF_ARRIVAL} date={dateOfExit} />
          </View>

          <ButtonWpr
            buttonText={BUTTON_CTA_MAP.DONE}
            onButtonPress={_onDone}
            disabled={!(dateOfEntry && dateOfExit)}
          />
        </View>
      </View>
      {confirmTravelDates && (
        <BottomSheetWpr
          isCrossIcon
          title={STRING_MAP.CONFIRM_TRAVEL_DATES}
          visible={confirmTravelDates}
          setVisible={setConfirmTravelDates}
          onDismiss={handleCloseConfirmTravelDatesBottomSheet}
          titleStyle={{
            fontSize: 18,
          }}
          children={<ConfirmTravelDetails
              travelDates={formattedDateRange}
              onConfirm={() => {
                onDone(dateOfEntry, dateOfExit);
              }}
              onChangeDates={() => {
                handleCloseConfirmTravelDatesBottomSheet();
              }}
            />}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray,
    justifyContent: 'space-between',
    position: 'relative',
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
  },
  calendarContainer: {
    flex: 1,
    backgroundColor: '#f2f2f2',
    justifyContent: 'space-between',
    position: 'relative',
  },
  calendarDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerContainer: {
    width: '100%',
  },
  footerSection: {
    backgroundColor: colors.white,
    width: '100%',
    ...paddingStyles.pa16,
    elevation: 8,
    // height: 180,
  },
  headerWrapperStyle: {
    shadowColor: 'transparent',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  visaInfoContainer: {
    backgroundColor: colors.fadedGreen,
    ...paddingStyles.ph16,
    ...paddingStyles.pv8,
    alignItems: 'center',
  },
  visaInfoText: {
    ...fontStyles.labelSmallBold,
    color: colors.green,
  },
});

export default VisaCalendar;
