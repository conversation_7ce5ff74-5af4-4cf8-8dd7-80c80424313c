function ordinal(n) {
    const s = ["th", "st", "nd", "rd"], v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
  }
  
  function monthName(d, style = "long") {
    return d.toLocaleString("en-GB", { month: style });
  }
  
  export function formatDateRange(startDate, endDate, { monthStyle = "long" } = {}) {
    const s = new Date(startDate);
    const e = new Date(endDate);
  
    const sameYear  = s.getFullYear() === e.getFullYear();
    const sameMonth = sameYear && s.getMonth() === e.getMonth();
    const sameDay   = sameMonth && s.getDate() === e.getDate();
  
    if (sameDay) {
      return `${ordinal(s.getDate())} ${monthName(s, monthStyle)}`;
    }
  
    if (sameMonth) {
      return `${ordinal(s.getDate())}–${ordinal(e.getDate())} ${monthName(s, monthStyle)}`;
    }
  
    if (sameYear) {
      return `${ordinal(s.getDate())} ${monthName(s, monthStyle)}–${ordinal(e.getDate())} ${monthName(e, monthStyle)}`;
    }
  
    // Different years
    return `${ordinal(s.getDate())} ${monthName(s, monthStyle)} ${s.getFullYear()}–${ordinal(e.getDate())} ${monthName(e, monthStyle)} ${e.getFullYear()}`;
  }