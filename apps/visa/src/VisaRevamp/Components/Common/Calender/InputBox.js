import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { fontStyles } from '../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { colors } from '../../../Styles/colors';
import { getMonthMmm } from 'packages/legacy-commons/Common/utils/dateTimeUtil';
import fecha from 'fecha';
import { isValidDate } from '../../../Utils';
import { STRING_MAP } from '../../../textStrings';
export const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const InputBox = ({ placeholderText, selectedDateObj, selectedDate, date = '' }) => {
  const renderDateFormat = () => {
    let parsedDate, dateOfMonth, month, dayOfWeek, year;

    try {
      parsedDate = new Date(date);
      dateOfMonth = fecha.format(parsedDate, 'Do');
      month = fecha.format(parsedDate, 'MMM');
      dayOfWeek = fecha.format(parsedDate, 'ddd');
      year = fecha.format(parsedDate, 'YYYY');
    } catch (error) {
      console.error('Error parsing or formatting date:', error);
      // Handle error appropriately, such as setting default values or showing a user-friendly message
      dateOfMonth = month = dayOfWeek = year = 'Invalid Date';
    }

    return (
      <>
        <Text style={styles.dayText}>
          {dateOfMonth} {month}
        </Text>
        <Text style={styles.yearText}>
          {dayOfWeek}, {year}
        </Text>
      </>
    );
  };
  return (
    <View style={styles.dateInput}>
      <Text style={styles.title}>{placeholderText}</Text>
      <View style={styles.dateContainer}>
        {isValidDate(date) ? (
          renderDateFormat()
        ) : (
          <Text style={styles.addDateText}>{STRING_MAP.CALENDAR_DATE_PLACEHOLDER}</Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dateInput: {
    borderColor: colors.midLightBlue,
    borderWidth: 1,
    borderRadius: 8,
    ...paddingStyles.pl16,
    ...paddingStyles.pv8,
    ...marginStyles.mr8,
    ...marginStyles.mb8,
    flex: 1,
  },
  title: {
    ...fontStyles.labelSmallBold,
    color: colors.primaryBlue,
  },
  dateContainer: {
    ...marginStyles.mt4,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  dayText: {
    ...fontStyles.labelMediumBold,
    color: colors.black,
  },
  yearText: {
    ...fontStyles.labelSmallRegular,
    color: colors.black,
    ...marginStyles.ml4,
  },
  addDateText: {
    ...fontStyles.labelSmallRegular,
    color: colors.lightGray,
  },
});

export default InputBox;
