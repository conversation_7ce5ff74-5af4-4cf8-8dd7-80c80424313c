import { View, Text, StyleSheet } from 'react-native'
import React from 'react'
import ImageHolder from '../../../Components/Common/ImageHolder';
import { colors } from '../../../Styles/colors';
import { borderRadiusValues } from '../../../Styles/borderRadius';
import { fontStyles } from '../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';

export default function index({text, imageURL, customTextStyle={}, customContainerStyle={}}) {
  return (
    <View style={[styles.activePersuation, customContainerStyle]}>
      {imageURL && <ImageHolder imageUrl={imageURL} style={styles.icon18} />}
        <Text style={[styles.persuationText, imageURL ? {} : styles.textCenter, customTextStyle]}>{text}</Text>
    </View>
  )
}
const styles = StyleSheet.create({
  activePersuation:{
    backgroundColor: colors.fadedGreen,
    ...paddingStyles.pv10,
    ...paddingStyles.ph16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomLeftRadius:borderRadiusValues.br8,
    borderBottomRightRadius:borderRadiusValues.br8,
  },
  persuationText:{
    ...fontStyles.labelSmallBold,
    color: colors.greenDark,
    flex:1,
  },
  textCenter: {
    textAlign: 'center',
  },
  icon18: {
    width:18,
    height:18,    
    ...marginStyles.mr12,
    ...marginStyles.mt2,
  },
});
