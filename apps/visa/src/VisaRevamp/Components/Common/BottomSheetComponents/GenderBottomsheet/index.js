import React, { useState } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { fontStyles } from '../../../../Styles/fonts';
import { colors } from '../../../../Styles/colors';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';

import RadioWpr from '../../RadioWpr';
import ButtonWpr from '../../ButtonWpr';
import { BUTTON_CTA_MAP } from '../../../../textStrings';

const GenderBottomsheet = ({ onSelect, handleCloseBottomSheet, defaultOption = '' }) => {
  const genderList = ['Male', 'Female'];
  const defaultIndex =
    genderList.map((gender) => gender.toLowerCase()).indexOf(defaultOption?.toLowerCase()) || 0;
  const [selectedIndex, setSelectedIndex] = useState(defaultIndex === -1  ? 0 : defaultIndex);

  const handleRadioSelected = (index) => {
    setSelectedIndex(index);
  };

  const handleConfirm = () => {
    const selectedGender = genderList[selectedIndex];
    onSelect(selectedGender);
    handleCloseBottomSheet(false);
  };

  const renderRadioCard = ({ item, idx }) => {
    const lastIndex = idx === genderList.length - 1;
    const isSelected = idx === selectedIndex;

    return (
      <View style={[styles.topSection, lastIndex ? styles.lastIndexStyle : null]} key={idx}>
        <RadioWpr
          isSelected={isSelected}
          onPress={() => handleRadioSelected(idx)}
          alignMiddle={false}
          customStyle={styles.radioStyle}
          radioSize={18}
          children={<RadioTextWrapper item={item} />}
        ></RadioWpr>
      </View>
    );
  };
  return (
    <View style={styles.container}>
      {genderList.map((item, idx) => renderRadioCard({ item, idx }))}
      <View style={marginStyles.mb24}>
        <ButtonWpr
          disabled={selectedIndex === null}
          buttonText={BUTTON_CTA_MAP.DONE}
          solidBtn={true}
          customStyle={buttonCustomStyles}
          onButtonPress={handleConfirm}
        />
      </View>
    </View>
  );
};
const RadioTextWrapper = ({ item }) => {
  return (
    <View style={styles.contentWrapper}>
      <Text style={styles.genderText}>{item}</Text>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    ...marginStyles.mt10,
    // ...marginStyles.mb20,
  },
  topSection: {
    borderBottomWidth: 1,
    borderBottomColor: colors.grayBorder,
    ...paddingStyles.pv18,
  },
  genderText: {
    ...fontStyles.labelBaseBlack,
    color: colors.black,
  },
  contentWrapper: {
    ...marginStyles.ml14,
    flex: 1,
  },
  radioStyle: {
    marginTop: 1,
  },
  lastIndexStyle: {
    borderBottomWidth: 0,
    ...marginStyles.mb20,
  },
});
const buttonCustomStyles = StyleSheet.create({
  buttonTextStyle: {
    ...fontStyles.labelLargeBold,
    width: '100%',
    textAlign: 'center',
  },
});
export default GenderBottomsheet;
