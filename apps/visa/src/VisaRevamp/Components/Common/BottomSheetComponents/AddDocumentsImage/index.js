import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import React from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { getImagePath } from '../../../../Utils/VisaUtils';
import arrowRight from '@mmt/legacy-assets/src/arrow_blue_right.webp';
import { STRING_MAP } from '../../../../textStrings';

function UploadRow({
    icon,
    content,
    onPress
}) {
    return (
        <TouchableOpacity onPress={() => onPress()}>
            <View style={styles.uploadRow}>
                <View style={styles.uploadRowColumn}>
                    <Image
                        source={icon}
                        style={styles.icon}
                    />
                    <Text style={styles.uploadRowText}>{content}</Text>
                </View>
                <TouchableOpacity style={styles.arrowContainer} onPress={() => onPress()}>
                    <Image
                        source={arrowRight}
                        style={styles.icon}
                    />
                </TouchableOpacity>
            </View>
        </TouchableOpacity>
    )
}

const AddDocumentsImage = ({ openDocGallery, openGallery, openCamera, tagText, supportedFormats, maxSize }) => {
    return (
        <View style={styles.visaInfoContainerWrapper}>
            {
                (tagText || supportedFormats || maxSize) && (
                    <View style={styles.visaInfoContainer}>
                        {/* <Image
                            source={getImagePath('tipIcon')}
                            style={styles.icon}
                        /> */}
                        {
                            tagText && <Text style={styles.visaInfoTextTitle}>{tagText ? tagText :STRING_MAP.ADD_DOC_UPLOAD_IMAGE_PERSUASION}</Text>
                        }
                        {
                            (supportedFormats || maxSize) && <Text style={styles.visaInfoText}>{supportedFormats && `Supported formats - ${supportedFormats}`} ; {` Max Size - ${maxSize}`}</Text>
                        }
                    </View>
                )
            }
            {
                openCamera &&
                <UploadRow
                    icon={getImagePath('cameraIconV2')}
                    content='Take a photo'
                    onPress={() => openCamera()}
                />
            }
            <View style={styles.borderStyle}>
                <UploadRow
                    icon={getImagePath('pictureIcon')}
                    content='Pick from gallery'
                    onPress={() => {openGallery()}}
                />
            </View>
            <View style={{...marginStyles.mb16}}>
                <UploadRow
                    icon={getImagePath('docIcon')}
                    content='Upload a document'
                    onPress={() => openDocGallery()}
                />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    visaInfoContainerWrapper: {
        ...paddingStyles.ph16,
        ...paddingStyles.pb16,
    },
    visaInfoTextTitle: {
        ...fontStyles.labelSmallBold,
        color: colors.yellow,
        // ...paddingStyles.pl8,
        // flex: 1,
    },
    visaInfoText: {
        ...fontStyles.labelSmallRegular,
        color: colors.yellow,
    },
    visaInfoContainer: {
        backgroundColor: colors.fadedYellow,
        ...paddingStyles.ph12,
        ...paddingStyles.pv8,
        ...marginStyles.mt16,
        flexDirection: 'column',
        gap: 2,
        borderRadius: 8,
    },
    borderStyle: {
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: colors.grayBorder,
    },
    uploadRow: {
        flexDirection: 'row',
        ...paddingStyles.pa8,
        ...paddingStyles.pv20,
        justifyContent: 'space-between',
        alignItems: "center",
    },
    icon: {
        width: 24,
        height: 24,
        resizeMode: 'contain'
    },
    uploadRowText: {
        ...fontStyles.labelMediumBold,
        ...marginStyles.ml8,
        color: colors.black,
        alignSelf: 'center',

    },
    uploadRowColumn: {
        flexDirection: "row",
    },
});

export default AddDocumentsImage;
