import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { fontStyles } from '../../../../Styles/fonts';
import { colors } from '../../../../Styles/colors';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { visaTrackClickEvent } from '../../../../Tracking/utils';
import { TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { PDT_EVENT_TYPES } from '../../../../Tracking/pdt/constants';

/* Components */
import ButtonWpr from '../../../../Components/Common/ButtonWpr';
import VisaDataHolder from '../../../../Utils/VisaDataHolder';
import { BUTTON_CTA_MAP, STRING_MAP } from '../../../../textStrings';
import { logPDTEvent } from '../../../../Tracking/pdt/logger';

const ConfirmTravelDetails = ({
  destDetails = {},
  openDestDetailPage = () => {},
  onDismiss = () => {},
  onConfirm = () => {},
  onChangeDates = () => {},
  travelDates = '',
}) => {
  const [selectedIndex, setSelectedIndex] = useState(null);
  const countryInfo = destDetails?.countryInfo || {};

  const handleTracking = () => {
    const eventType = `confirmtraveldates_${VisaDataHolder.getInstance().getCurrentPageName()}_popupload`;
    visaTrackClickEvent({
      eventType,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.contentSeen,
      eventValue: 'ctd_popup_loaded',
    })
  };
  
  const handleConfirmClickTracking = () => {
    visaTrackClickEvent({
      eventName: `confirm_travel_dates_CTD`,
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      eventValue: 'confirm_CTD',
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }
  
  const handleChangeDatesClickTracking = () => {
    visaTrackClickEvent({
      eventName: `change_dates_CTD`,
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      eventValue: 'change_dates_CTD',
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }

  const handleConfirm = () => {
    handleConfirmClickTracking();
    onConfirm();
  };

  const handleChangeDates = () => {
    handleChangeDatesClickTracking();
    onChangeDates();
  };

  useEffect(() => {
    handleTracking();
  }, []);

  console.log('travelDates', travelDates);

  return (
    <View style={styles.container}>
        <View style={styles.contentWrapper}>
          <View style={styles.listContentWrapper}>
            <Text style={styles.descriptionText}>You've selected <Text style={styles.descriptionTextHighlight}>{travelDates}</Text> as your travel dates. Please confirm, as incorrect dates may impact your visa validity.</Text>
          </View>
          <View style={marginStyles.mb16}>
            <ButtonWpr
              buttonText={BUTTON_CTA_MAP.CONFIRM}
              solidBtn={true}
              customStyle={buttonCustomStyles}
              onButtonPress={handleConfirm}
            />
          </View>
          <View style={marginStyles.mb20}>
            <ButtonWpr
              buttonText={BUTTON_CTA_MAP.CHANGE_DATES}
              solidBtn={false}
              buttonType={'outline'}
              customStyle={buttonCustomStyles}
              onButtonPress={handleChangeDates}
            />
          </View>
        </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pt16,
    ...marginStyles.mt16,
    borderTopWidth: 1,
    borderTopColor: colors.grayBorder,
    marginHorizontal: -20,
  },
  contentWrapper: {
    ...paddingStyles.ph20,
  },
  listContentWrapper: {
    ...paddingStyles.pb20,
  },
  descriptionText: {
    ...fontStyles.labelMediumRegular,
    color: colors.gray,
  },
  descriptionTextHighlight: {
    ...fontStyles.labelMediumBold,
    color: colors.gray,
  },
});

const buttonCustomStyles = StyleSheet.create({
  buttonTextStyle: {
    ...fontStyles.labelLargeBold,
    width: '100%',
    textAlign: 'center',
  },
});

export default ConfirmTravelDetails;