import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { fontStyles } from '../../../../Styles/fonts';
import { colors } from '../../../../Styles/colors';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { borderRadius } from 'apps/visa/src/VisaRevamp/Styles/borderRadius';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { visaTrackClickEvent } from '../../../../Tracking/utils';
import { TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { PDT_EVENT_TYPES } from '../../../../Tracking/pdt/constants';

/* Components */
import RadioWpr from '../../../../Components/Common/RadioWpr';
import ButtonWpr from '../../../../Components/Common/ButtonWpr';
import AccordionWpr from '../../../../Components/Common/AccordionWpr';
import MultiCountryAsk from './MultiCountryAsk';
import VisaDataHolder from '../../../../Utils/VisaDataHolder';
import { BUTTON_CTA_MAP, STRING_MAP } from '../../../../textStrings';
import { logPDTEvent } from '../../../../Tracking/pdt/logger';

const PurposeofTravel = ({
  destDetails = {},
  openDestDetailPage = () => {},
  schengenMultiCountryInfo = {},
  onDismiss = () => {},
  searchId = '',
}) => {
  // const dispatch = store.dispatch;
  const { isSchengen = false, purposeOfTravelDetails = {}, name = '' } = destDetails || {};
  const { title = '', subTitle = '', description = [] } = purposeOfTravelDetails || {};
  const countryInfo = {
    code: destDetails?.countryCode ? destDetails?.countryCode : destDetails?.code,
    name: destDetails?.name ? destDetails?.name : destDetails?.countryName,
  };
  const showStep = () => {
    if (description?.length > 1 && isSchengen) {
      return true;
    }
    return false;
  };

  const stepText = () => {
    if (description?.length > 1 && isSchengen && showSchengen) {
      return STRING_MAP.POT_STEP2;
    }
    return STRING_MAP.POT_STEP1;
  };

  const [selectedIndex, setSelectedIndex] = useState(description?.length == 1 ? 0 : null);
  const [showSchengen, setShowSchengen] = useState(false);

  const handleTracking = () => {
    const eventType = `purposeoftravel_${VisaDataHolder.getInstance().getCurrentPageName()}_popupload`;
    visaTrackClickEvent({
      eventType,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.contentSeen,
      eventValue: 'pot_popup_loaded',
    })
  };

  useEffect(() => {
    handleTracking();
  }, []);

  const handlePOTSelected = (index) => {
    logPDTEvent({
      eventValue: 'select_POT',
      actionType: PDT_EVENT_TYPES.valueSelected,
    })
    setSelectedIndex(index);
  };

  const handleConfirmClickTracking = () => {
    visaTrackClickEvent({
      eventName: `${description[selectedIndex]?.title?.replaceAll(' ', '_')}_purposeoftravel`,
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      eventValue: 'confirm_POT',
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }
  const handleConfirm = () => {
    handleConfirmClickTracking();
    if (destDetails?.isSchengen) {
      setShowSchengen(true);
    } else {
      onDismiss();
      openDestDetailPage({
        potId: description[selectedIndex]?.id,
      });
    }
  };

  const renderPOTCard = ({ item, idx }) => {
    const lastIndex = idx === description.length - 1;
    const isSelected = idx === selectedIndex;
    return (
      <View style={[styles.topSection, lastIndex ? styles.lastIndexStyle : null]} key={idx}>
        <RadioWpr
          isSelected={isSelected}
          onPress={() => handlePOTSelected(idx)}
          alignMiddle={false}
          customStyle={styles.radioStyle}
          radioSize={16}
          children={<RadioTextWrapper item={item} />}
        ></RadioWpr>
      </View>
    );
  };

  return (
    <View style={marginStyles.mt20}>
      {showStep() && <Text style={styles.stepText}>{stepText()}</Text>}
      {description?.length > 1 && !showSchengen && (
        <View>
          {title && <Text style={styles.headlineText}>{title}</Text>}
          {subTitle && <Text style={styles.countryText}>{subTitle}</Text>}
          <View style={styles.listContentWrapper}>
            {description.map((item, idx) => renderPOTCard({ item, idx }))}
          </View>
          <View style={marginStyles.mb24}>
            <ButtonWpr
              disabled={selectedIndex === null}
              buttonText={BUTTON_CTA_MAP.CONFIRM}
              solidBtn={true}
              customStyle={buttonCustomStyles}
              onButtonPress={handleConfirm}
            />
          </View>
        </View>
      )}
      {(showSchengen || description?.length == 1) && (
        <MultiCountryAsk
          searchId={searchId}
          destDetails={destDetails}
          description={description}
          selectedIndex={selectedIndex}
          onDismiss={onDismiss}
          openDestDetailPage={openDestDetailPage}
          schengenMultiCountryInfo={schengenMultiCountryInfo}
        />
      )}
    </View>
  );
};
const RadioTextWrapper = ({ item }) => {
  const { title = '', subTitle = '' } = item;
  return (
    <View style={styles.contentWrapper}>
      <Text style={styles.titleText}>{title}</Text>
      <Text style={styles.listText}>{subTitle}</Text>
    </View>
  );
};
const styles = StyleSheet.create({
  topImage: {
    width: 80,
    height: 80,
    borderRadius: 50,
    marginTop: -60,
    marginBottom: 20,
    backgroundColor: colors.lightSkyBlue,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imgStyle: {
    width: 36,
    height: 24,
  },
  lastIndexStyle: {
    ...marginStyles.mb0,
  },
  radioStyle: {
    // marginTop:6,
  },
  topSection: {
    borderWidth: 1,
    borderColor: colors.grayBorder,
    ...borderRadius.borderRadius8,
    ...paddingStyles.pa16,
    ...marginStyles.mb12,
  },
  contentWrapper: {
    ...marginStyles.ml8,
    flex: 1,
  },
  stepText: {
    ...fontStyles.labelSmallBold,
    color: colors.darkBlue5,
    textTransform: 'uppercase',
    ...marginStyles.mb16,
  },
  headlineText: {
    ...fontStyles.headingMedium,
    color: colors.black,
    ...marginStyles.mb8,
  },
  countryText: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
    ...marginStyles.mb24,
  },
  titleText: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
    ...marginStyles.mb8,
  },
  listText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
    lineHeight: 16,
    ...marginStyles.mb4,
  },
  listContentWrapper: {
    ...paddingStyles.pb48,
  },
  descriptionText: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
  },
  loadMoreContainer: {
    marginTop: 10,
  },
  title: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
    ...marginStyles.mb16,
    ...paddingStyles.pt6,
  },
});

const buttonCustomStyles = StyleSheet.create({
  buttonTextStyle: {
    ...fontStyles.labelLargeBold,
    width: '100%',
    textAlign: 'center',
  },
});

export default PurposeofTravel;
