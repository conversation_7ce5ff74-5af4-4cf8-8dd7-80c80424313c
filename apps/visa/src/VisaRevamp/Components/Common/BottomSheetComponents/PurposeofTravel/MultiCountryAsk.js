import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { fontStyles } from '../../../../Styles/fonts';
import { colors } from '../../../../Styles/colors';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { borderRadius } from 'apps/visa/src/VisaRevamp/Styles/borderRadius';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import store from 'packages/legacy-commons/AppState/Store';
import { visaTrackClickEvent } from '../../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../../Tracking/constants';
import { PDT_EVENT_TYPES } from '../../../../Tracking/pdt/constants';
import { logPDTEvent } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { resetSubPageNameAction, updateSubPageNameAction } from 'apps/visa/src/VisaRevamp/Actions/pdtLoggerActions';
import { PDT_SUB_PAGE_NAMES } from 'apps/visa/src/VisaRevamp/constants';
import { BUTTON_CTA_MAP, STRING_MAP } from 'apps/visa/src/VisaRevamp/textStrings';
import { TEXT_LINK_CTA } from '../../../../textStrings';

/* Components */
import RadioWpr from '../../../../Components/Common/RadioWpr';
import ButtonWpr from '../../../../Components/Common/ButtonWpr';
import AccordionWpr from '../../../../Components/Common/AccordionWpr';
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';

const MultiCountryAsk = (props) => {
  const dispatch = store.dispatch;
  const {
    searchId,
    schengenMultiCountryInfo,
    selectedIndex,
    destDetails,
    description,
    onDismiss,
    openDestDetailPage,
  } = props || {};

  const {
    title: multiCountryTitle,
    callToAction,
    description: multiCountryDescription,
  } = schengenMultiCountryInfo || {};

  const schengenDescription = multiCountryDescription?.replace(
    '{country}',
    destDetails?.name ? destDetails?.name : destDetails?.countryName,
  );

  const countryInfo = {
    code: destDetails?.countryCode ? destDetails?.countryCode : destDetails?.code,
    name: destDetails?.name ? destDetails?.name : destDetails?.countryName,
  };

  const [callToActionText, setcallToActionText] = useState(
    schengenMultiCountryInfo?.callToActionText || '',
  );

  const handleTracking = () => {
    visaTrackClickEvent({
      eventType: `schengenmulticountry_popupload`,
      params: {
        countryName: countryInfo?.name,
      },
    });
    dispatch(updateSubPageNameAction(PDT_SUB_PAGE_NAMES.MULTI_COUNTRY_POPUP));
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.contentSeen,
      eventValue: `schengen_multi_country_load`,
    })
  };
  useEffect(() => {
    handleTracking();
    return () => {
      dispatch(resetSubPageNameAction());
    };
  }, []);

  const handleKnowMoreTracking = (isShowMoreClicked) => {
    const prefix = isShowMoreClicked ? 'knowMore' : 'viewLess';
    const suffix = '_schengenmulticountry';
    visaTrackClickEvent({
      eventName: `${prefix}${suffix}`,
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      eventValue:`${prefix}${suffix}`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }
  
  const toggleKnowMore = () => {
    if (callToActionText === TEXT_LINK_CTA.SHOW_LESS) {
      handleKnowMoreTracking(false)
      setcallToActionText(schengenMultiCountryInfo?.callToActionText);
    } else {
      handleKnowMoreTracking(true)
      setcallToActionText(TEXT_LINK_CTA.SHOW_LESS);
    }
  };

  const handleNO = () => {
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: 'click_no_schengen_multi_country',
    })
    visaTrackClickEvent({
      eventName: 'no_schengenmulticountry',
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    onDismiss();
    if (description?.length == 1) {
      openDestDetailPage({ potId: description[0]?.id });
    } else {
      openDestDetailPage({
        potId: description[selectedIndex]?.id,
      });
    }
  };

  const handleYes = () => {
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: 'click_yes_schengen_multi_country',
    })
    visaTrackClickEvent({
      eventName: 'yes_schengenmulticountry',
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    onDismiss();
    VisaNavigation.push(VISA_ROUTE_KEYS.MULTICOUNTRY_PAGE, {
      countryInfo,
      pot: description[selectedIndex]?.id,
      searchId,
    });
  };

  return (
    <View>
      {!!multiCountryTitle && <Text style={styles.headlineText}>{multiCountryTitle}</Text>}
      {!!schengenDescription && <Text style={styles.descriptionText}>{schengenDescription}</Text>}
      {callToAction && callToActionText === TEXT_LINK_CTA.SHOW_LESS && (
        <View style={styles.loadMoreContainer}>
          <HighlightedText
            str={callToAction}
            highlightedTxtStyle={styles.descriptionTextHighlighted}
            normalTxtStyle={styles.descriptionText}
            separator="*"
          />
        </View>
      )}
      {!!callToActionText && (
        <View>
          <TouchableOpacity onPress={toggleKnowMore}>
            <Text style={styles.title}>{callToActionText}</Text>
          </TouchableOpacity>
        </View>
      )}
      <View style={[marginStyles.mb12, marginStyles.mt30]}>
        <ButtonWpr buttonText={BUTTON_CTA_MAP.NO} buttonType="outline" onButtonPress={handleNO} />
      </View>
      <View style={marginStyles.mb24}>
        <ButtonWpr
          buttonText={BUTTON_CTA_MAP.YES}
          solidBtn={true}
          customStyle={buttonCustomStyles}
          onButtonPress={handleYes}
        />
      </View>
    </View>
  );
};
const RadioTextWrapper = ({ item }) => {
  const { title = '', subTitle = '' } = item;
  return (
    <View style={styles.contentWrapper}>
      <Text style={styles.titleText}>{title}</Text>
      <Text style={styles.listText}>{subTitle}</Text>
    </View>
  );
};
const styles = StyleSheet.create({
  headlineText: {
    ...fontStyles.headingMedium,
    color: colors.black,
    ...marginStyles.mb8,
  },

  titleText: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
    ...marginStyles.mb8,
  },
  listText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
    lineHeight: 16,
    ...marginStyles.mb4,
  },

  descriptionText: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
  },
  descriptionTextHighlighted: {
    ...fontStyles.labelBaseBold,
    color: colors.gray,
  },
  loadMoreContainer: {
    marginTop: 10,
  },
  title: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
    ...marginStyles.mb16,
    ...paddingStyles.pt6,
  },
});

const buttonCustomStyles = StyleSheet.create({
  buttonTextStyle: {
    ...fontStyles.labelLargeBold,
    width: '100%',
    textAlign: 'center',
  },
});

export default MultiCountryAsk;
