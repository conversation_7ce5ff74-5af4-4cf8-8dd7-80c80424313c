import React, { useEffect } from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { marginStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';

/* Components */
import TickIcon from 'apps/visa/src/VisaRevamp/Components/Common/TickIcon';
import BottomSheetWpr from '../BottomSheetWpr';
import ButtonWpr from '../ButtonWpr';
import { PDT_SUB_PAGE_NAMES } from '../../../constants';
import { BUTTON_CTA_MAP } from '../../../textStrings';

const DeliveryInfoBottomSheet = ({
  receivingTextInfo = {},
  openDeliveryInfo = false,
  setDeliveryInfo,
  visaPlan = '',
  closeDeliveryInfoBottomSheet = () => {},
}) => {
  const { header, title, description } = receivingTextInfo || {};

  const handleLoadTracking = () => {
    visaTrackClickEvent({
      eventName: `${NAME_OF_EVENTS.VISA_DELIVERY_INFO_LOAD}_${visaPlan}`,
      eventType: TYPE_OF_EVENTS.POPUP_LOAD,
    });
  };

  const handleClick = () => {
    closeDeliveryInfoBottomSheet(NAME_OF_EVENTS.GOT_IT_VISA_DELIVERY_INFO)
  }
  return (
    <BottomSheetWpr
      isCrossIcon
      bottomsheetName={PDT_SUB_PAGE_NAMES.DELIVERY_INFO}
      visible={openDeliveryInfo}
      setVisible={setDeliveryInfo}
      title={header}
      callTracking={handleLoadTracking}
      onDismiss={() => closeDeliveryInfoBottomSheet(NAME_OF_EVENTS.CLOSE_VISA_DELIVERY_INFO)}
    >
      <View style={styles.container}>
        <TickIcon bgColor={colors.darkBlue5} />
        <View style={{ flex: 1 }}>
          <Text style={styles.deliveryHeading}>{title}</Text>
          <Text style={styles.deliveryDescription}>{description}</Text>
        </View>
      </View>
      <ButtonWpr
        buttonText={BUTTON_CTA_MAP.GOT_IT}
        onButtonPress={handleClick}
      />
    </BottomSheetWpr>
  );
};

const styles = StyleSheet.create({
  container: {
    ...marginStyles.mv30,
    flexDirection: 'row',
    alignItems: 'base-line',
  },
  deliveryHeading: {
    ...fontStyles.labelMediumBlack,
    color: colors.black,
    flex: 1,
    ...marginStyles.mb8,
  },
  deliveryDescription: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
    flex: 1,
  },
});

export default DeliveryInfoBottomSheet;
