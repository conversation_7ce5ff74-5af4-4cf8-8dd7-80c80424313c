import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import { fontStyles } from '../../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { colors } from '../../../../Styles/colors';
import { visaTrackClickEvent } from '../../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../../Tracking/constants';
import { logPDTEvent } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';

const SavedTravellerItem = ({ item, index, handleSelectTraveller, isLastChild }) => {
  const { firstName, lastName, age, gender, travellerId = '' } = item || {};
  const [isChecked, setIsChecked] = useState(false);
  const handleClick = () => {
    if (!isChecked) {
      visaTrackClickEvent({
        eventName: `traveler${index}_${firstName}_${age}`,
        eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      });
      logPDTEvent({
        eventValue: `select_pax_special_exception`,
        actionType: PDT_EVENT_TYPES.valueSelected,
      });
    }
    setIsChecked(!isChecked);
    handleSelectTraveller({ id: travellerId, isChecked: !isChecked });
  };
  const borderBottomStyle = isLastChild ? null : styles.borderBottom;
  return (
    <View key={`traveller-${travellerId}-${index}`}>
      <CheckBox
        isChecked={item?.specialrequest}
        activeColor="#2276e3"
        borderColor="#647a97"
        boxRadius={4}
        customStyles={[styles.listItemContainer, borderBottomStyle]}
        customLine1Style={styles.travellerName}
        customLine2Style={styles.travellerAge}
        onPress={handleClick}
        textLine1={`${firstName} ${lastName}`}
        textLine2={`${age}yrs (${gender.slice(0, 1)})`}
        tickSize={10}
      />
    </View>
  );
};

const SavedTravellerList = ({ setSelectedTravellers, travellersList }) => {
  const selectedTravellers = travellersList.filter((traveller) => traveller?.selected);
  const handleSelectTraveller = ({ id, isChecked }) => {
    const updatedTravellersList = travellersList.map((traveller, i) => {
      if (id === traveller?.travellerId) {
        return {
          ...traveller,
          specialrequest: isChecked,
        };
      }
      return traveller;
    });

    setSelectedTravellers(updatedTravellersList);
  };

  return (
    <View style={styles.travellerList}>
      {selectedTravellers.map((traveller, index) => (
        <SavedTravellerItem
          item={traveller}
          index={index}
          handleSelectTraveller={handleSelectTraveller}
          isLastChild={selectedTravellers.length - 1 === index}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  travellerList: {
    borderColor: colors.grayBorder,
    borderWidth: 1,
    borderRadius: 10,
  },
  listItemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  travellerName: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
  },
  travellerAge: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
  },
  borderBottom: {
    borderBottomColor: colors.grayBorder,
    borderBottomWidth: 1,
  },
});

export default SavedTravellerList;
