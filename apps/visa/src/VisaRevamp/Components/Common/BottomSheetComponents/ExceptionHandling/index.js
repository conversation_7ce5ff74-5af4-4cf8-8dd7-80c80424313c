import React, { useEffect, useState } from 'react';
import { Image, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { fontStyles } from '../../../../Styles/fonts';
import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { visaTrackClickEvent } from '../../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../../Tracking/constants';
import { logPDTEvent } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';

/* Icons */
import iconTickWhite from '@mmt/legacy-assets/src/tick_white.webp';

/* Components */
import ButtonWpr from '../../ButtonWpr';
import BottomSheetWpr from '../../BottomSheetWpr';
import SavedTravellerList from './SavedTraveller';
import Seperator from '../../Seperator';
import { PDT_SUB_PAGE_NAMES } from '../../../../constants';
import { BUTTON_CTA_MAP, STRING_MAP, TEXT_LINK_CTA } from '../../../../textStrings';

const TickIcon = () => {
  return (
    <View style={tickIconStyles.tickIconContainer}>
      <Image source={iconTickWhite} style={tickIconStyles.tickIcon} />
    </View>
  );
};

const ExcetionHandlingComponent = ({
  exception = {},
  countryName = '',
  travellersList = [],
  setSelectedTravellers = () => {},
  confirmBooking = () => {},
}) => {
  const itemsToShow = 2;
  const [showMore, setShowMore] = useState(false);
  const toggleLoadMore = () => {
    if (!showMore) {
      visaTrackClickEvent({
        eventName: `special_exception_know_more`,
        eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      });
      logPDTEvent({
        eventValue: `click_know more_exception`,
        actionType: PDT_EVENT_TYPES.buttonClicked,
      });
    }

    setShowMore(!showMore);
  };

  const { countryImageLink, name, description, subheader, listHeader = '' } = exception || {};

  useEffect(() => {
    visaTrackClickEvent({
      eventName: `special_exception_${countryName}`,
      eventType: TYPE_OF_EVENTS.POPUP_LOAD,
    });
    logPDTEvent({
      eventValue: `special_exception_${countryName}_loaded`,
      actionType: PDT_EVENT_TYPES.contentSeen,
    });
  }, []);

  const handleContinue = () => {
    visaTrackClickEvent({
      eventName: `continue_traveler_exception`,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });
    logPDTEvent({
      eventValue: `click_continue_exception_page`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });
    confirmBooking();
  };
  return (
    <View>
      <Text style={[styles.bottomsheetSubtitle, marginStyles.mt10]}>{description}</Text>
      {subheader.slice(0, showMore ? subheader.length : itemsToShow).map((item, index) => (
        <View key={index} style={styles.infoContainer}>
          <TickIcon bgColor={colors.green} />
          <Text style={styles.checkMarkText}>{item}</Text>
        </View>
      ))}
      {subheader?.length > itemsToShow && (
        <TouchableOpacity onPress={toggleLoadMore}>
          <Text style={styles.knowMoreText}>
            {showMore ? TEXT_LINK_CTA.KNOW_LESS : TEXT_LINK_CTA.KNOW_MORE}
          </Text>
        </TouchableOpacity>
      )}
      <View style={marginStyles.mv16}>
        <Seperator strokeDasharray={0} />
      </View>
      <Text style={styles.bottomsheetSubtitle}>{listHeader || STRING_MAP.TRAVELLER_EXCEPTION_DESC}</Text>
      <View style={styles.travellerListContainer}>
        <SavedTravellerList
          travellersList={travellersList}
          setSelectedTravellers={setSelectedTravellers}
        />
      </View>
      <View style={{ marginTop: 32 }}>
        <ButtonWpr buttonText={BUTTON_CTA_MAP.CONTINUE} onButtonPress={handleContinue} />
      </View>
    </View>
  );
};
const ExceptionHandlingBottomSheet = ({
  exception,
  value = false,
  openShowException = () => {},
  closeShowException = () => {},
  ...rest
}) => {
  const { countryImageLink = '', name = '' } = exception || {};
  return (
    value && (
      <BottomSheetWpr
        visible={value}
        bottomsheetName={PDT_SUB_PAGE_NAMES.SPECIAL_EXCEPTION}
        title={name}
        isCrossIcon={true}
        topIcon={{ uri: countryImageLink }}
        setVisible={value ? closeShowException : openShowException}
        onDismiss={closeShowException}
        children={<ExcetionHandlingComponent exception={exception} {...rest} />}
      />
    )
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'base-line',
  },
  bottomsheetSubtitle: {
    ...fontStyles.labelMediumBold,
    color: colors.black,
    marginBottom: 12,
    marginRight: 43,
  },
  checkMarkText: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
    marginBottom: 4,
  },
  knowMoreText: {
    ...fontStyles.labelBaseBold,
    ...marginStyles.mt6,
    color: colors.primaryBlue,
    borderBottomColor: colors.grayBorder,
    borderBottomWidth: 1,
  },
});

const tickIconStyles = StyleSheet.create({
  tickIcon: {
    width: 6,
    height: 6,
  },
  tickIconContainer: {
    width: 13,
    height: 13,
    borderRadius: 7,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.green,
    ...marginStyles.mr10,
    ...marginStyles.mt4,
  },
});

export default ExceptionHandlingBottomSheet;
