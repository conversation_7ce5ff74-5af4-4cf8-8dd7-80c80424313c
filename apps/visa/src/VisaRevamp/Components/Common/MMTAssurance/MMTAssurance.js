import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native'
import React from 'react';
import { colors } from '../../../Styles/colors';
import { borderRadius } from '../../../Styles/borderRadius';
import { fontStyles } from '../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import arrowIcon from '@mmt/legacy-assets/src/arrow_downGrey.webp';
import ImageHolder from '../../../Components/Common/ImageHolder';
import AssuranceList from "./AssuranceList";
import { CONTENT_TYPES } from '../../../constants';
import LinearGradient from 'react-native-linear-gradient';

export default function index({ visualPersuasion, handleClick }) {
  return (
    <TouchableOpacity style={styles.linearGradientContainer} onPress={handleClick}>
        <View style={styles.headerWrapper}>
            <ImageHolder imageUrl={visualPersuasion?.imageUrl} style={[styles.icon26, styles.imageWrapper]} />
            <View style={styles.titleWrapper}>
                <Text style={styles.assuranceText}>{visualPersuasion?.header}</Text>
            </View>
        </View>
        <LinearGradient
        colors={['#D3E7FF', '#ffffff']}
        start={{ x: 1, y: 0 }}
        end={{ x: .75, y: 1 }}
        style={styles.mmtAssuranceCardWrapper}
        >
          <AssuranceList 
            details={visualPersuasion?.otherDetails} 
            showTitlesOnly={true} 
            dots={true} 
            handleClick={handleClick} 
          />
        </LinearGradient>
    </TouchableOpacity>
  )
}
const styles = StyleSheet.create({
  mmtAssuranceCardWrapper: {
    ...borderRadius.borderRadius16,
    ...paddingStyles.ph16,
    ...paddingStyles.pb12,
    ...paddingStyles.pt20,
    ...marginStyles.mb16,
    borderWidth: 1,
    borderColor: colors.darkBlue,
    position:'relative',
    zIndex:1,
  },
  
  headerWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position:'absolute',
    top:-11,
    left:16,
    right:16,
    zIndex:2,
  },

  imageWrapper:{
    position:'absolute',
    top:-14,
    left:0,
    right:0,
    zIndex:2,
  },

  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    backgroundColor: colors.white,
    position:'relative',
    zIndex:-1,
  },

  assuranceText: {
    ...fontStyles.labelSmallBold,
    color: colors.darkBlue2,
    borderWidth: 1,
    borderColor: colors.darkBlue2,
    borderRadius:12,
    ...paddingStyles.pv2,
    ...paddingStyles.pr8,
    paddingLeft:32,
  },
  icon26: {
    width: 28,
    height: 28,
    ...marginStyles.mr12,
  },
  linearGradientContainer: {
    position: 'relative',
    marginTop: 16,
  },

});