import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native'
import React from 'react';
import { colors } from '../../../Styles/colors';
import { borderRadius } from '../../../Styles/borderRadius';
import { fontStyles } from '../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import arrowIcon from '@mmt/legacy-assets/src/arrow_downGrey.webp';
import ImageHolder from '../../../Components/Common/ImageHolder';
import AssuranceList from "./AssuranceList";
import { CONTENT_TYPES } from '../../../constants';

export default function index({ visualPersuasion, handleClick, contentType }) {
  const getHeightStyle = () => {
    const pointsCount = visualPersuasion?.otherDetails?.length || 0;
    if (pointsCount <= 3) {
      return { height: pointsCount * 40 + 80 }; // 40px per point + 80px for header/padding
    }
    return {}; // auto height for more than 3 points
  };

  return (
    <View style={[styles.mmtAssuranceWrapper, getMarginStyle(contentType)]}>
      <TouchableOpacity onPress={handleClick}>
        <View style={styles.headerWrapper}>
          <View style={styles.titleWrapper}>
            <ImageHolder imageUrl={visualPersuasion?.imageUrl} style={styles.icon32} />
            <Text style={styles.assuranceText}>{visualPersuasion?.header}</Text>
          </View>
          <Image source={arrowIcon} style={styles.arrowIconStyle} />
        </View>
        <View style={[styles.assuranceListWrapper, getHeightStyle()]}>
          <AssuranceList details={visualPersuasion?.otherDetails} showTitlesOnly={true} />
        </View>
      </TouchableOpacity>
    </View>
  )
}
const getMarginStyle = (contentType) => {
  return contentType === CONTENT_TYPES.DETAIL 
    ? marginStyles.ma16 
    : marginStyles.mv16;
};
const styles = StyleSheet.create({
  mmtAssuranceWrapper: {
    backgroundColor: colors.midLightBlue,
    borderWidth: 1,
    borderColor: colors.grayBorder,
    ...borderRadius.borderRadius16,
  },
  headerWrapper: {
    ...paddingStyles.pt12,
    ...paddingStyles.pb10,
    ...paddingStyles.ph16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: colors.white,
  },
  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  assuranceText: {
    ...fontStyles.labelLargeBold,
    color: colors.black,
  },
  assuranceListWrapper: {
    ...paddingStyles.pa16,
  },
  icon32: {
    width: 32,
    height: 32,
    ...marginStyles.mr12,
  },
  arrowIconStyle: {
    width: 16,
    height: 16,
    transform: [{ rotate: '270deg' }],
    tintColor: colors.primaryBlue,
  },
  flexOne: {
    flex: 1,
  },

});