import { View, Text, StyleSheet, TouchableOpacity,NativeModules,Image } from 'react-native';
import React from 'react';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';
import { marginStyles } from '../../../Styles/Spacing';
import { getImagePath } from '../../../Utils/VisaUtils';
import ImageHolder from '../../../Components/Common/ImageHolder';

export default function AssuranceList({ details, showTitlesOnly, isModel, dots, handleClick }) {

    const handleTNC = (clickurl) => {
        const { GenericModule } = NativeModules;
        GenericModule.openWebView({ url: clickurl });
      };

    return (
        <View style={isModel ? marginStyles.mt20 : null}>
            {details && details.map((item) => (
                <View key={item.order} style={[styles.listWrapper, styles[showTitlesOnly ? 'title' : 'titleDescription']]}>
                    {!dots ? <ImageHolder imageUrl={item.imageUrl} style={styles.icon24} /> : <View style={styles.dot} />}
                    <View style={[styles.flexOne, dots ? null : marginStyles.mb12]}>
                        <View style={styles.assuranceTitleWrapper}>
                            {item.header && <Text style={dots ? styles.assuranceTitleSmall : styles.assuranceTitle}>{item.header}</Text>}
                            {dots && details.indexOf(item) === details.length - 1 && <TouchableOpacity onPress={handleClick}><Text style={styles.assuranceKnowMore}>Know More</Text></TouchableOpacity>}
                        </View>
                        {!showTitlesOnly && item.description && <Text style={styles.assuranceDescription}>{item.description}</Text>}
                        {!showTitlesOnly && item.callToAction && 
                        item.callToAction?.text && item.callToAction?.redirectUrl &&
                        <TouchableOpacity style={styles.termsWrapper} onPress={() => handleTNC(item.callToAction?.redirectUrl)} >
                            <Text style={styles.linkText}>{item.callToAction?.text}</Text>
                            <Image style={styles.arrowIconStyle} source={getImagePath('rightArrow')} />
                        </TouchableOpacity>
                        }
                    </View>
                </View>
            ))}
        </View>
    );
}

const styles = StyleSheet.create({
    listWrapper: {
        flexDirection: 'row',
        // ...marginStyles.mb12,
    },
    title:{
        alignItems: 'center',
    },
    titleDescription:{
        alignItems: 'flex-start',
    },
    assuranceTitleSmall: {
        ...fontStyles.labelSmallRegular,
        color: colors.black,
        ...marginStyles.mb2,
    },
    assuranceTitleWrapper:{
        flexDirection: 'row',
        alignItems: 'center',
    },
    assuranceKnowMore:{
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
        ...marginStyles.ml8,
    },
    assuranceTitle: {
        ...fontStyles.labelBaseBold,
        color: colors.black,
    },
    assuranceDescription: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        ...marginStyles.mt8,
        ...marginStyles.mb12,
    },
    termsWrapper:{
        flexDirection: 'row',
        alignItems: 'center',
        ...marginStyles.mb12,
    },
    linkText:{
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    arrowIconStyle: {
        width: 16,
        height: 16,
        tintColor: colors.primaryBlue,
    },
    icon24: {
        width: 24,
        height: 24,
        ...marginStyles.mr12,
        ...marginStyles.mt2,
    },
    flexOne: {
        flex: 1,
    },
    dot:{
        width:6,
        height:6,
        backgroundColor: colors.darkGray,
        borderRadius: 25,
        ...marginStyles.mr12,
        marginTop:-2,
    }
});