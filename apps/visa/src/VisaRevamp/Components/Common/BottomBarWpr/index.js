import React from 'react';
import BottomBar from '@Frontend_Ui_Lib_App/BottomBar';
import ButtonWpr from '../ButtonWpr';
import { colors } from '../../../Styles/colors';
import { StyleSheet } from 'react-native';

const BottomBarWpr = ({
  title = '',
  titleIcon,
  description = '',
  description2 = '',
  onTitleIconPress,
  titleIconStyle,
  titleRightComponent,
  rightComponent = () => {},
  containerStyles = {},
}) => {
  const renderRightComponent = () => {
    return rightComponent();
  };
  return (
    <BottomBar
      customStyles={{
        containerStyle: { ...styles.container, ...containerStyles },
        description1Style: { color: colors.disableGrayBg },
        titleStyle: { fontWeight: '700' },
        titleIconStyle: titleIconStyle,
      }}
      description1={description}
      description2={description2}
      rightComponent={rightComponent()}
      title={title}
      titleIcon={titleIcon}
      titleRightComponent={titleRightComponent}
      onTitleIconPress={onTitleIconPress}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: colors.darkGray,
  },
});
export default BottomBarWpr;
