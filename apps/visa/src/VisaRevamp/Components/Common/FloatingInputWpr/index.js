import React from 'react';
import { Platform, StyleSheet } from 'react-native';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';

const FloatingInputWpr = ({
  value,
  label,
  focusBgColor,
  focusBorderColor,
  focusLabelColor,
  onChangeText,
  onFocus,
  onBlur,
  isError = false,
  errorMessage = '',
  requiredText='',
}) => {
  return (
    <FloatingInput
      requiredText={requiredText}
      isError={isError}
      errorMessage={errorMessage}
      customStyle={{
        inputFieldStyle: styles.inputFieldStyle,
        labelStyle: styles.labelStyle,

      }}
      endIcon={null}
      focusBgColor={focusBgColor ? focusBgColor : ''}
      focusBorderColor={focusBorderColor ? focusBorderColor : ''}
      focusLabelColor={focusLabelColor ? focusLabelColor : ''}
      label={label ? label : 'FULL NAME'}
      labelAnimationLeftValue={32}
      labelAnimationTopValue={Platform.OS === 'ios' ? 14 : 12}
      onBlur={onBlur}
      onChangeText={onChangeText}
      onFocus={onFocus}
      testId=""
      value={value}
    />
  );
};
const styles = StyleSheet.create({
  inputFieldStyle: {
    ...fontStyles.labelMediumBlack,
    color: colors.black,
    backgroundColor: colors.lightGray3,
    ...Platform.select({
      ios: {
        paddingTop: 16,
      }
    })
    // paddingTop:16,
  },
  labelStyle:{
    ...fontStyles.labelBaseBold,
    color: colors.lightGray,
  }
});

export default FloatingInputWpr;
