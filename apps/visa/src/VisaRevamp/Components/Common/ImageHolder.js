import React, { useState } from 'react';
import { View, Image } from 'react-native';
import { isEmpty } from 'lodash';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_dest_default.webp';

const ImageHolder = ({
  style,
  imageUrl = '',
  defaultImage,
  resizeMode,
  containerStyles = {},
  ...rest
}) => {
  const [showDefaultImage, setShowDefaultImage] = useState(false);
  const onLoadError = () => {
    setShowDefaultImage(true);
  };

  const extraProps = {
    ...(style?.tintColor && { tintColor: style.tintColor }),
  };

  return (
    <View style={containerStyles}>
      <Image
        style={style}
        onError={onLoadError}
        source={
          showDefaultImage
            ? genericCardDefaultImage
            : defaultImage
            ? defaultImage
            : { uri: imageUrl }
        }
        resizeMode={resizeMode}
        {...extraProps}
      />
    </View>
  );
};

export default ImageHolder;
