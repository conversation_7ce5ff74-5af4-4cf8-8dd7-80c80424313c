
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { marginStyles } from '../../../../Styles/Spacing';

const PROGRESS_STATUS = {
  COMPLETED: 'completed',
  PROGRESS: 'progress',
  INACTIVE: 'inactive',
  PENDING: 'pending',
}

function getStatusArray(currentIdx, pendingIdx) {
  const statusArray = Array(4).fill(PROGRESS_STATUS.INACTIVE);

  for (let idx = 0; idx < statusArray.length; idx++) {
      if (idx < currentIdx) {
          statusArray[idx] = PROGRESS_STATUS.COMPLETED;
      } else if (idx === currentIdx) {
          statusArray[idx] = PROGRESS_STATUS.PROGRESS;
      }

      if (pendingIdx.includes(idx)) {
          statusArray[idx] = PROGRESS_STATUS.PENDING;
      }
  }

  return statusArray;
}

const ProgressBar = ({ stepIndex = 1, pendingIndex = []}) => {
  const stepData = getStatusArray(stepIndex - 1, pendingIndex);
  return (
    <View style={styles.progressBarContainer}>
      {stepData.map((step, idx) => {
        const lastIndex = stepData.length - 1;
        const lastRow = idx === lastIndex ? styles.lastIndex : null;
        return (
          <View
            key={idx}
            style={[
              styles.progressBar,
              step === PROGRESS_STATUS.COMPLETED && styles.completed,
              step === PROGRESS_STATUS.PROGRESS && styles.progress,
              step === PROGRESS_STATUS.INACTIVE && styles.inactive,
              step === PROGRESS_STATUS.PENDING && styles.pending,
              lastRow,
            ]}
          />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  progressBarContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...marginStyles.mt12,
    ...marginStyles.mb8,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: 'lightgray',
    marginRight: 4,
    borderRadius: 2,
  },
  completed: {
    backgroundColor: '#007e7d',
  },
  progress: {
    backgroundColor: '#96c4c4',
  },
  inactive: {
    backgroundColor: '#f2f2f2',
  },
  pending: {
    backgroundColor: '#FFEDD1',
  },
  lastIndex: {
    ...marginStyles.mr0,
  },
});

export default ProgressBar;
