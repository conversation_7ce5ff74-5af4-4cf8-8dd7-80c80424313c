import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image } from 'react-native';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';

/* Components */
import ProgressBar from './ProgressBar';
import { TEXT_LINK_CTA } from '../../../textStrings';

const SelectPlanHeader = ({
  header,
  country = '',
  journeyDate = '',
  stepIndex,
  skipPage = false,
  pendingIndex = [],
  actionHandler = () => { },
  showProgressBar = true
}) => {
  return (
    <View style={styles.selectPlanWrapper}>
      <View style={styles.headingWrapper}>
        <Text style={styles.titleHeading}>{header}</Text>
        {skipPage && (
          <TouchableOpacity onPress={actionHandler}>
            <Text style={styles.textLink}>{TEXT_LINK_CTA.ADD_LATER}</Text>
          </TouchableOpacity>
        )}
      </View>
      <Text style={styles.subText}>
        {country} • {journeyDate}
      </Text>
      {showProgressBar &&
        <>
          <ProgressBar stepIndex={stepIndex} pendingIndex={pendingIndex} />
          <View>
            <Text style={styles.stepText}>
              STEP {stepIndex} OF 4
              {/* {pendingIndex.length > 0 && <Text style={styles.stepText}> • DOCUMENTS PENDING</Text>} */}
            </Text>
          </View>
        </>
      }
    </View>
  );
};

const styles = StyleSheet.create({
  selectPlanWrapper: {
    backgroundColor: colors.white,
    ...paddingStyles.ph16,
    ...paddingStyles.pb16,
    elevation: 4,
    zIndex: 1,
    ...getPlatformElevation(3),
  },
  headingWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...marginStyles.mb4,
  },
  titleHeading: {
    ...fontStyles.headingRegular,
    color: colors.black,
  },
  textLink: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
  },
  subText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
  },
  stepText: {
    ...fontStyles.labelBaseBold,
    color: colors.gray,
  },
});

export default SelectPlanHeader;
