
import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet } from 'react-native';

const WINDOW_SIZE = 40; // Size of each item in pixels
const DEFAULT_SCROLL_DURATION = 800; // Duration of each scroll animation in milliseconds
const INITIAL_DELAY = 1000;

const CircularCarousel = (props) => {
  const {
    components, // Pass an array of components (Text, View, Image)
    SCROLL_DURATION = DEFAULT_SCROLL_DURATION,
    windowSize = WINDOW_SIZE,
    containerStyle,
    itemStyle,
    maxIterations, // New prop for limiting iterations
    animationCount = 0,
  } = props || {};

  const extendedComponents = [...components, components[0]]; // Add first component again for looping

  // Create an Animated Value to control the vertical position of items
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Function to animate a single item scroll
    const animateItem = (toValue, duration, withDelay = false) => {
      const springAnimation = Animated.spring(animatedValue, {
        toValue,
        duration,
        friction: 4, // Lower friction for more bounce
        tension: 20, // Lower tension for a slower, more relaxed animation
        useNativeDriver: true, // Use native driver for better performance
      });

      return springAnimation;
    };

    // Function to reset the carousel to its initial position
    const resetCarousel = Animated.timing(animatedValue, {
      toValue: 0,
      duration: 0, // Instant reset
      useNativeDriver: true,
    });

    // Function to set up and start the entire carousel animation
    const animateCarousel = () => {
      // Create animations for all components except the last (which is a duplicate of the first)
      const animations = extendedComponents
        .slice(0, -1)
        .map((_, index) => animateItem(-windowSize * index, SCROLL_DURATION));

      // Add the final animation and reset to complete the loop
      animations.push(
        animateItem(-windowSize * (extendedComponents.length - 1), SCROLL_DURATION, false),
        resetCarousel,
      );

      // If maxIterations is provided, loop for a finite number of iterations; otherwise, loop infinitely
      Animated.loop(
        Animated.sequence(animations),
        { iterations: maxIterations || -1 }, // If maxIterations is undefined or 0, loop infinitely
      ).start();
    };

    // Kick off the carousel animation after the initial delay
    setTimeout(() => {
      if (animationCount < 1) {
        animateCarousel();
      }
    }, INITIAL_DELAY);
  }, []); // Empty dependency array ensures this effect runs only once on mount

  return (
    <View style={[containerStyle, styles.carouselWindow]}>
      <Animated.View
        style={[{ height: windowSize }, { transform: [{ translateY: animatedValue }] }]}
      >
        {extendedComponents.map((component, index) => (
          <View key={`item-${index}`} style={itemStyle}>
            {component}
          </View>
        ))}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  carouselWindow: {
    overflow: 'hidden',
  },
});

export default CircularCarousel;