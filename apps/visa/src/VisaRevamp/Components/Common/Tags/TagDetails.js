import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/fonts';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';

/* Icons */
import IconTick from '@mmt/legacy-assets/src/ic_tick2.png';

/* Components */
import ButtonWpr from '../ButtonWpr';
import ImageHolder from 'apps/visa/src/VisaRevamp/Components/Common/ImageHolder';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';
import { handleDeeplinkClick } from '../../../Utils/DeeplinkUtils';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { BUTTON_CTA_MAP } from '../../../textStrings';

const TagDetails = ({ tagType = '', details = {}, toggleModal = () => {}, countryName = '' }) => {
  const { data = [], callToActionText = '', callToAction = '' } = details || {};
  const tagTypeLowerCase = tagType.toLocaleLowerCase()?.replace(' ', '')
  const handleTracking = () => {
    visaTrackClickEvent({
      eventType: `${tagTypeLowerCase}_popupload`,
      params: {
        countryName,
      },
    });
    logPDTEvent({
      eventValue: 'visa_info_loaded',
      actionType: PDT_EVENT_TYPES.contentSeen,
    })
  };
  
  useEffect(() => {
    handleTracking();
  }, []);

  const renderItem = ({ item }) => {
    const { header, description } = item || {};
    return (
      <View style={styles.itemContainer}>
        <View style={styles.imageContainer}>
          <ImageHolder defaultImage={IconTick} style={styles.imageIcon} />
        </View>
        <View style={{ ...marginStyles.ml12 }}>
          <Text style={styles.title}>{header}</Text>
          <Text style={styles.description}>{description}</Text>
        </View>
      </View>
    );
  };

  const handleButtonClickTracking = (prefix) => {
    visaTrackClickEvent({
      eventName: `${prefix}_${tagTypeLowerCase}info`,
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
      params: {
        countryName,
      },
    });
    logPDTEvent({
      eventValue: `${prefix}_${tagTypeLowerCase}info`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }
  
  const onPress = () => {
    handleButtonClickTracking('learnmore');
    toggleModal();
    handleDeeplinkClick(callToAction);
  };

  const handleButtonPress = () => {
    handleButtonClickTracking('gotit')
    toggleModal();
  };

  return (
    <View>
      <FlatList data={data} renderItem={renderItem} contentContainerStyle={styles.container} />
      {callToAction && (
        <TouchableOpacity style={marginStyles.ml30} onPress={onPress}>
          <Text style={styles.actionText}>{callToActionText}</Text>
        </TouchableOpacity>
      )}
      <View style={styles.btnContainer}>
        <ButtonWpr
          buttonText={BUTTON_CTA_MAP.GOT_IT}
          onButtonPress={handleButtonPress}
          buttonWidth={'100%'}
          customStyle={{
            buttonTextStyle: { ...fontStyles.labelMediumBlack, color: colors.white },
          }}
        />
      </View>
    </View>
  );
};

const styles = {
  container: {
    ...paddingStyles.pt30,
    flexDirection: 'column',
    gap: 16
  },
  itemContainer: {
    flexDirection: 'row',
  },
  imageContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.darkBlue2,
  },
  imageIcon: {
    width: 15,
    height: 15,
    tintColor: colors.white,
    top: 2.5,
    left: 2.5,
  },
  title: {
    ...fontStyles.labelMediumBlack,
    color: colors.black,
    flex: 1,
    ...marginStyles.mb6,
  },
  description: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
    // ...marginStyles.mt6,
    flex: 1,
  },
  actionText: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
  },
  btnContainer: { alignItems: 'center', ...marginStyles.mt30, ...marginStyles.mb16 },
};

export default TagDetails;
