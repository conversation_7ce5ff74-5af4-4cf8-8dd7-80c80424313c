import React, { useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { PDT_SUB_PAGE_NAMES, VISA_TYPE_CODES } from '../../../constants';
import { connect } from 'react-redux';
import { isEmpty } from 'lodash';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';

/* Icons */
import infoIcon from '@mmt/legacy-assets/src/Info.webp';

/* Components */
import ImageHolder from '../ImageHolder';
import BottomSheetWpr from '../BottomSheetWpr';
import TagDetails from './TagDetails';
import { logPDTEvent } from '../../../Tracking/pdt/logger';


const TagComponent = ({
  visaTypeDetailsState = [],
  visaTypeDetails = [],
  tagType = '',
  size = '',
  countryName = '',
  from = '',
}) => {
  const visaTypeData = visaTypeDetailsState || visaTypeDetails || [];
  if (isEmpty(visaTypeData)) {
    return [];
  }
  const tagDetails = visaTypeData?.find((item) => item?.name === tagType)?.details;
  const [openTagDetail, setOpenTagDetails] = useState(false);
  const {
    title,
    callToActionText,
    callToAction,
    backgroundColour,
    textColour,
    imgLink = '',
  } = tagDetails || {};
  const textStyle = size === 'small' ? styles.textSmall : styles.textBase;

  const toggleModal = () => {
    setOpenTagDetails(!openTagDetail);
  };

  const handleTracking = () => {
    const eventName = `${countryName}_${tagType
      ?.toLocaleLowerCase()
      ?.replace(' ', '')}info_${from}`;
    visaTrackClickEvent({
      eventName,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      params: {
        countryName,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.contentClicked,
      eventValue:`click_${tagType?.toLocaleLowerCase()}_info`
    })
  }
  const openTagDetails = () => {
    handleTracking();
    setOpenTagDetails(!openTagDetail);
  };

  const handleCloseBottomSheetTracking = () => {
    const eventName = `close_${tagType?.toLocaleLowerCase()?.replace(' ', '')}info`;
    visaTrackClickEvent({
      eventName,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      params: {
        countryName,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: `close_${tagType?.toLocaleLowerCase()}_info`
    })
  }
  const closeTagDetailsModal = () => {
    handleCloseBottomSheetTracking()
    setOpenTagDetails(false);
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.visaTagWrapper, { backgroundColor: backgroundColour }]}
        onPress={openTagDetails}
      >
        <Text style={[textStyle, { color: textColour }]}>{tagType}</Text>
        {imgLink && (
          <ImageHolder
            imageUrl={imgLink}
            style={{ ...styles.visaIconStyle, tintColor: textColour }}
          />
        )}
      </TouchableOpacity>
      {openTagDetail && (
        <BottomSheetWpr
          isCrossIcon
          visible={openTagDetail}
          setVisible={toggleModal}
          title={title}
          onDismiss={closeTagDetailsModal}
          bottomsheetName={PDT_SUB_PAGE_NAMES.VISA_TAG_DETAILS}
        >
          <TagDetails
            tagType={tagType}
            details={tagDetails}
            toggleModal={toggleModal}
            countryName={countryName}
          />
        </BottomSheetWpr>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  visaTagWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 4,
    ...paddingStyles.ph8,
    ...paddingStyles.pv2,
    alignSelf: 'flex-start',
  },
  visaIconStyle: {
    width: 12,
    height: 12,
    ...marginStyles.mt2,
  },
  textBase: {
    ...fontStyles.labelBaseBold,
    ...marginStyles.mr6,
    ...marginStyles.mt2,
    lineHeight: 18,
  },
  textSmall: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.mr6,
    ...marginStyles.mt2,
    lineHeight: 18,
  },
});

const mapStateToProps = (state) => {
  return {
    visaTypeDetailsState: state.visaV2CommonReducers?.visaTypeDetails || {},
  };
};
export default connect(mapStateToProps)(TagComponent);
