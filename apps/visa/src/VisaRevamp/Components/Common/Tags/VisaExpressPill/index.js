import React from 'react';
import { StyleSheet, Text, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/fonts';
import { colors } from '../../../../Styles/colors';
import { borderRadius } from '../../../../Styles/borderRadius';
import { IMAGE_KEYS, getImagePath } from '../../../../Utils/VisaUtils';
import { STRING_MAP } from '../../../../textStrings';

const VisaExpress = ({ isBorder }) => {
  return (
    <LinearGradient
      colors={['#f5f4fd', '#f1dbf5']}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      style={[styles.visaTypeWrapper, isBorder ? styles.tagBorder : null]}
    >
      <Image
        source={getImagePath(IMAGE_KEYS.expressTimer)}
        style={styles.visaIconStyle}
        resizeMode="contain"
      />
      <Text style={styles.expressText}>{STRING_MAP.EXPRESS_VISA_PILL_STR}</Text>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  visaTypeWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    ...borderRadius.borderRadius8,
    ...paddingStyles.ph8,
    ...paddingStyles.pv2,
    alignSelf: 'flex-start',
  },
  visaIconStyle: {
    width: 20,
    height: 14,
    ...marginStyles.mr6,
  },
  expressText: {
    ...fontStyles.labelSmallBold,
    color: '#4a2fb5',
  },
  tagBorder: {
    borderWidth: 1,
    borderColor: '#4a2fb5',
    borderRadius: 12,
  },
});

export default VisaExpress;
