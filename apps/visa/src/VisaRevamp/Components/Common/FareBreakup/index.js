import React from 'react';
import { StyleSheet, View, Text, ScrollView, Dimensions } from 'react-native';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import InfoMessage from './InfoMessage';
import isEmpty from 'lodash/isEmpty';
import SchengenNote from '../../../Pages/Detail/Components/SchengenNote';

const BreakupDetails = ({ priceInfoDetails, convenienceFeeTitle = '' }) => {
  const { priceInfo = [] } = priceInfoDetails || {};
  const renderPriceListInfo = (info, index) => {
    return (
      <Text style={styles.priceSubText} key={`Info-${index}`}>
        {info}
      </Text>
    );
  };

  const renderPriceInfo = (details, idx) => {
    const isLastItem = idx === priceInfo.length - 1;
    const { additionalInfo = '', infoKey = [], value = '', key = '' } = details || {};
    const lastRow = isLastItem ? styles.lastRowBorder : null;
    return (
      <View style={[styles.priceRowContainer, lastRow]} key={`Details-${idx}`}>
        <View style={[styles.priceRowWrapper]}>
          <View style={styles.flexOne}>
            {!!key && <Text style={[styles.priceText, marginStyles.mb8]}>{key}</Text>}
            {infoKey?.map((info, index) => renderPriceListInfo(info, index))}
          </View>
          {value && <Text style={[styles.priceText, styles.textRight]}>{value}</Text>}
        </View>

        {!!additionalInfo && <InfoMessage message={additionalInfo || ''} />}
      </View>
    );
  };

  return (
    <View>
      {!isEmpty(convenienceFeeTitle) && (
        <View style={{...marginStyles.mt16}}>
          <Text style={styles.convenienceFeeText}>{convenienceFeeTitle}</Text>
        </View>
      )}
      <ScrollView style={styles.fareBreakupWrapper} showsVerticalScrollIndicator={false}>
        {priceInfo.map((details, idx) => renderPriceInfo(details, idx))}
        {
          (priceInfoDetails?.schengenAppointmentNote) && (
            <View style={{...marginStyles.mt6}}>
              <SchengenNote message={priceInfoDetails?.schengenAppointmentNote} />
            </View>
          )
        }
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  fareBreakupWrapper: {
    maxHeight: Dimensions.get('window').height * 0.5,
    ...marginStyles.mv20,
  },
  priceRowContainer: {
    borderBottomWidth: 1,
    borderBottomColor: colors.grayBorder,
    ...marginStyles.mb16,
    ...paddingStyles.pb16,
  },
  priceRowWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  flexOne: {
    flex: 1,
  },
  priceText: {
    ...fontStyles.labelMediumBold,
    color: colors.black,
  },
  priceSubText: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
  },
  lastRowBorder: {
    borderBottomWidth: 0,
    ...marginStyles.mb0,
    ...paddingStyles.pb0,
  },
  convenienceFeeText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
    backgroundColor: colors.fadedGreen,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
});

export default BreakupDetails;
