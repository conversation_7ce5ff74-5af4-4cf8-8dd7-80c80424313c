import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import DrumRollCalendar from '@Frontend_Ui_Lib_App/DrumRollCalendar';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import LinearGradient from 'react-native-linear-gradient';
import { paddingStyles } from '../../../Styles/Spacing';
import { borderRadius } from '../../../Styles/borderRadius';
import { BUTTON_CTA_MAP } from '../../../textStrings';

const DrumRollCalendarWpr = ({
  maxYear,
  minYear,
  handleCloseBottomSheet,
  onDismiss,
  onSave,
  isFloating,
  selectedDate = '',
}) => {
  return (
    <DrumRollCalendar
      showWrapStyle={false}
      onSave={(date, month, year) => {
        onSave(date, month, year);
      }}
      initialDate={selectedDate?.day}
      initialMonth={selectedDate?.month}
      initialYear={selectedDate?.year}
      txtStyleActive={styles.txtStyleActiveStyle}
      txtStyleCta={styles.txtStyleActiveStyle}
      scrlContainer={styles.scrlContainer}
      maxYear={maxYear}
      minYear={minYear}
      isFloating={isFloating}
      children={
        <LinearGradient
          colors={['#53B2FE', '#065AF3']}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 1 }}
          style={styles.btnWrapper}
        >
          <Text style={styles.ctaText}>{BUTTON_CTA_MAP.SAVE}</Text>
        </LinearGradient>
      }
    />
  );
};
const styles = StyleSheet.create({
  txtStyleActiveStyle:{
    color: colors.primaryBlue,
  },
  scrlContainer:{
    width: 100,
    height: 80,
  },
  btnWrapper:{
    ...paddingStyles.ph20,
    ...paddingStyles.pv12,
    ...borderRadius.borderRadius8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  ctaText:{
    color: colors.white, 
    ...fontStyles.labelMediumBlack,
  }
});

export default DrumRollCalendarWpr;
