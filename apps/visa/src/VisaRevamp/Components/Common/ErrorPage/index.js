import React, { useEffect } from 'react';
import { View, Image, Text, StyleSheet, TouchableOpacity } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import store from 'packages/legacy-commons/AppState/Store';
import {
  NAME_OF_EVENTS,
  TRACKING_CONSTANTS,
  TYPE_OF_EVENTS,
} from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { setErrorMessage } from '../../../Actions/visaActions';
import { STRING_MAP, TEXT_LINK_CTA } from '../../../textStrings';

/* Components */
import HeaderWrapper from '../HeaderWpr';
import RedirectLink from '../RedirectLink';
import { set } from 'lodash';

/* Icons */
const errorIcon = require('@mmt/legacy-assets/src/detailError.webp');

const ErrorPage = ({ handleBackClick = () => { }, handleRefreshClick = () => { }, errorMessage = '' }) => {

  function getStateParams() {
    const state = store.getState();
    const { visaV2CommonReducers = {} } = state || {};
    const { trackingParams = {} } = visaV2CommonReducers || {};
    let errorMessageText = trackingParams?.errorMessage;
    if (errorMessageText == 'only child booking') {
      errorMessageText = 'A child visa can only be processed with at least one parent’s visa application. Please add a parent’s application to proceed.';
    } else {
      errorMessageText = errorMessage;
    }
    return errorMessageText;
  };

  function showRefresh() {
    const state = store.getState();
    const { visaV2CommonReducers = {} } = state || {};
    const { trackingParams = {} } = visaV2CommonReducers || {};
    let errorMessageText = trackingParams?.errorMessage;
    if (errorMessageText == 'only child booking') {
      return false;
    }
    return true;
  };

  useEffect(() => {
    visaTrackClickEvent({
      eventType: TYPE_OF_EVENTS.ERROR_PAGE_LOAD,
    });
    return (() => {
      store.dispatch(setErrorMessage(''));
    })
  })
  return (
    <View style={styles.container}>
      <HeaderWrapper
        wrapperStyle={{
          ...getPlatformElevation(0),
        }}
        clickHandler={handleBackClick}
      />
      <View style={styles.contentContainer}>
        <Image source={errorIcon} style={styles.errorImage} resizeMode="contain" />
        <View style={styles.errorTextContainer}>
          <Text style={styles.errorHeadingText}>{STRING_MAP.ERROR_TITLE}</Text>
          <Text style={styles.errorMessage}>{getStateParams() || STRING_MAP.ERROR_MESSAGE}</Text>
          {showRefresh()  && <TouchableOpacity style={styles.linkWrapper} onPress={handleRefreshClick}>
            <Text style={styles.textName}>{TEXT_LINK_CTA.REFRESH}</Text>
          </TouchableOpacity>
          }
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  errorImage: {
    width: 196,
    height: 196,
    ...marginStyles.mv10,
  },
  errorTextContainer: {
    ...marginStyles.ml20,
    width: '100%',
    alignItems: 'center',
  },
  errorHeadingText: {
    ...fontStyles.labelLargeBlack,
    color: colors.black,
    textAlign: 'center',
    ...marginStyles.mv20,
  },
  errorMessage: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
    width: '50%',
    textAlign: 'center',
  },
  linkWrapper: {
    ...paddingStyles.pt30,
    alignItems: 'center',
  },
  textName: {
    color: colors.primaryBlue,
    ...fontStyles.labelMediumBold,
  },
});
export default ErrorPage;
