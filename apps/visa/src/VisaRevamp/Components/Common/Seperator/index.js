import React from 'react';
import Svg, { Line } from 'react-native-svg';
import { colors } from '../../../Styles/colors';

const Seperator = ({
  color = colors.lightGray, // Default color of the line
  strokeWidth = 1, // Default stroke width, adjust this to change the "height" of the stroke
  strokeDasharray = '2, 2', // Default dash pattern, creates the dots/dashes
  length = '100%', // Default length of the line
  height = '1', // Default height of the SVG container
  orientation = 'horizontal', // Default orientation
}) => {
  // Ensure the container's height is greater than or equal to strokeWidth for horizontal lines.
  // This prevents the stroke from being clipped.
  // For vertical lines, ensure the container's width is sufficient to accommodate the strokeWidth.
  // Comments above guide the appropriate sizing of the SVG container based on the orientation and strokeWidth.
  // ex: <Seperator orientation={'vertical'} height={'100%'}/>

  // Adjust x2 and y2 based on orientation
  const x2 = orientation === 'horizontal' ? length : '0';
  const y2 = orientation === 'vertical' ? height : '0';

  return (
    <Svg height={height} width={length}>
      <Line
        x1="0"
        y1="0"
        x2={x2}
        y2={y2}
        stroke={color}
        strokeWidth={strokeWidth}
        strokeDasharray={strokeDasharray}
      />
    </Svg>
  );
};

export default Seperator;
