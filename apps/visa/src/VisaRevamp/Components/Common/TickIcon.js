import React from 'react';
import { View, Image, StyleSheet } from 'react-native';
import { colors } from '../../Styles/colors';
import { marginStyles } from '../../Styles/Spacing';
import iconTickWhite from '@mmt/legacy-assets/src/tick_gold_noBckGrnd.webp';

const TickIcon = ({ bgColor = colors.black }) => {
  return (
    <View style={[styles.tickIconContainer, { backgroundColor: bgColor }]}>
      <Image source={iconTickWhite} style={styles.tickIcon} resizeMode="contain" />
    </View>
  );
};

const styles = StyleSheet.create({
  tickIcon: {
    width: 10,
    height: 10,
    tintColor: colors.white
  },
  tickIconContainer: {
    width: 16,
    height: 16,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    ...marginStyles.mr8,
    ...marginStyles.mt2,
  },
});

export default TickIcon;
