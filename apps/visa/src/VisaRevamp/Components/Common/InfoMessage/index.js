import React from 'react';
import { StyleSheet, View, Text, Image } from 'react-native';
import HTMLView from 'react-native-htmlview';
import infoIcon from '@mmt/legacy-assets/src/info_grey.webp';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { borderRadius } from 'apps/visa/src/VisaRevamp/Styles/borderRadius';

/* Components */
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';

const InfoMessage = ({ message, index }) => {
  return (
    <View style={styles.infoMessageWrapper} key={`Item-${index}`}>
      <Image source={infoIcon} style={styles.iconStyle} />
      <View style={styles.flexOne}>
        <HighlightedText
          str={message}
          highlightedTxtStyle={styles.messageTextHighlighted}
          normalTxtStyle={styles.messageText}
          separator="*"
          numberOfLines={2}
        />
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  infoMessageWrapper: {
    flexDirection: 'row',
    ...paddingStyles.pa16,
    ...marginStyles.mb6,
    borderWidth: 1,
    borderColor: colors.grayBorder,
    ...borderRadius.borderRadius8,
  },
  flexOne: {
    flex: 1,
  },
  iconStyle: {
    width: 14,
    height: 14,
    tintColor: colors.gray,
    ...marginStyles.mr8,
    ...marginStyles.mt2,
  },
  messageTextHighlighted: {
    ...fontStyles.labelSmallBold,
    color: colors.yellow,
  },
  messageText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
  },
});
export default InfoMessage;