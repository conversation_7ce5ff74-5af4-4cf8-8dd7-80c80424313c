import React from 'react';
import { StyleSheet, View, Text, Image, ScrollView, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';
import { borderRadius } from '../../../Styles/borderRadius';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../../../Navigation';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';

const VisaArticles = ({ details, country }) => {
  const { data: learnAboutVisaData = [] } = details || {};

  const renderCard = ({ item, idx }) => {
    const {
      id,
      pageId,
      title,
      readTime,
      description,
      callToActionText,
      thumbnailUrl = '',
    } = item || {};
    const lastIndex = learnAboutVisaData.length - 1;
    const lastRow = idx === lastIndex ? styles.lastIndex : null;

    const handleTracking = () => {
      visaTrackClickEvent({
        eventName: `${country?.name ? `${country?.name}_` : ''}learnMore_${idx}`,
        eventType: TYPE_OF_EVENTS.CARD_CLICK,
      });
      logPDTEvent({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        eventValue: 'read_more_click',
      });
    };

    const handleClick = () => {
      handleTracking();
      VisaNavigation.push(VISA_ROUTE_KEYS.LEARN_MORE, {
        learningId: id,
        pageId,
        ...(country && { country }),
      });
    };
    return (
      <View style={[styles.contentBoxWrapper, lastRow]} key={idx}>
        {!!thumbnailUrl && <Image source={{ uri: thumbnailUrl }} style={styles.imageStyle} />}
        {!!title && <Text style={styles.itemText}>{title}</Text>}
        {!!readTime && <Text style={styles.durationText}>{readTime}</Text>}
        {!!description && (
          <Text style={styles.descriptionText} numberOfLines={3}>
            {description}
          </Text>
        )}
        <TouchableOpacity onPress={handleClick} style={{ marginTop: 'auto' }}>
          <Text style={styles.linkText}>{callToActionText}</Text>
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <View style={styles.contentWrapper}>
      <ScrollView
        style={styles.containerStyles}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
      >
        {learnAboutVisaData
          ?.sort((a, b) => a.sequence - b.sequence)
          ?.map((item, idx) => renderCard({ item, idx }))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  contentWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pb16,
  },
  containerStyles: {
    marginHorizontal: -16,
  },
  headingWrapper: {
    ...marginStyles.mt30,
    ...marginStyles.mb16,
  },
  titleText: {
    ...fontStyles.labelLargeBlack,
    color: colors.black,
  },
  itemText: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
    ...marginStyles.mb2,
  },
  durationText: {
    ...fontStyles.labelSmallRegular,
    color: colors.lightGray,
    ...marginStyles.mb6,
  },
  descriptionText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
    ...marginStyles.mb12,
  },
  linkText: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
  },
  contentBoxWrapper: {
    backgroundColor: colors.white,
    ...paddingStyles.pa16,
    ...borderRadius.borderRadius8,
    ...marginStyles.ml16,
    position: 'relative',
    width: 235,
    borderWidth: 1,
    borderColor: colors.grayBorder,
  },
  lastIndex: {
    ...marginStyles.mr16,
  },
  imageStyle: {
    width: '100%',
    height: 136,
    ...marginStyles.mb12,
  },
});

export default VisaArticles;
