import React from 'react';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton';

const RadioWpr = ({
  radioSize,
  radioBgColor,
  onPress,
  radioBtnLabel,
  inactiveColor,
  activeColor,
  isSelected,
  children,
  customStyle,
  alignMiddle,
}) => {
  return (
    <RadioButton
      activeColor={activeColor ? activeColor : '#2276e3'}
      borderGap={8}
      customButtonTextStyle={{}}
      customStyle={[customStyle]}
      isSelected={isSelected}
      alignMiddle={alignMiddle ? alignMiddle : ''}
      // disabled
      inactiveColor={inactiveColor ? inactiveColor : '#647A97'}
      label={radioBtnLabel ? radioBtnLabel : ''}
      onPress={onPress}
      radioBgColor={radioBgColor ? radioBgColor : '#fff'}
      radioSize={radioSize ? radioSize : 16}
      testIds={{
        btnId: '',
        labelId: '',
      }}
    >
      {children}
    </RadioButton>
  );
};

export default RadioWpr;
