import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text, Image } from 'react-native';
import arrowIcon from '@mmt/legacy-assets/src/arrow_downGrey.webp';
import { marginStyles, paddingStyles } from 'apps/visa/src/VisaRevamp/Styles/Spacing';
import { fontStyles } from 'apps/visa/src/VisaRevamp/Styles/fonts';
import { colors } from 'apps/visa/src/VisaRevamp/Styles/colors';
import ImageHolder from 'apps/visa/src/VisaRevamp/Components/Common/ImageHolder';

const RedirectLink = ({ text, handleClick, showArrowIcon = false }) => {
  return (
    <TouchableOpacity style={styles.linkWrapper} onPress={handleClick}>
      <Text style={styles.textName}>{text}</Text>
      {showArrowIcon && (
        <ImageHolder style={styles.arrowRight} defaultImage={arrowIcon} resizeMode={'contain'} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  linkWrapper: {
    ...paddingStyles.pt10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowRight: {
    width: 16,
    height: 16,
    transform: [{ rotate: '270deg' }],
    tintColor: colors.primaryBlue,
    ...marginStyles.mt4,
  },
  textName: {
    ...fontStyles.labelSmallBold,
    color: colors.primaryBlue,
    flex: 1,
  },
});

export default RedirectLink;
