import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import Dropdown from '@Frontend_Ui_Lib_App/Dropdown';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';

const DropdownWpr = ({
  label,
  onSelect,
  onEndIconPress,
  value,
  isMandatory,
  dropdownWrapperStyle,
  endIcon,
  isFloating,
  isError = false,
  errorMessage = '',
}) => {
  return (
    <Dropdown
      isError={isError}
      errorMessage={errorMessage}
      label={label ? label : ''}
      requiredText={isMandatory && <Text style={styles.mandatory}>*</Text>}
      value={value}
      onSelect={(selectedOption) => {
        onSelect(selectedOption);
      }}
      isFloating={isFloating}
      endIcon={endIcon}
      onEndIconPress={onEndIconPress}
      customStyle={{
        dropdownWrapperStyle:[ styles.inputWrapperDropDown, dropdownWrapperStyle],
        labelStyle: styles.label,
        endIconStyle: styles.iconArrowDown,
        dropdownValueStyle: styles.dropdownValueStyle,
      }}
    />
  );
};
const styles = StyleSheet.create({
  inputWrapperDropDown: {
    paddingTop: 16
  },
  iconArrowDown: {
    width: 24,
    height: 24,
    tintColor: colors.lightGray,
  },
  label: {
    ...fontStyles.labelBaseBold,
    color: colors.lightGray,
  },
  dropdownValueStyle: {
    ...fontStyles.labelMediumBlack,
    color: colors.black,
  },
  mandatory: {
    color: '#891f1a',
  },
});

export default DropdownWpr;
