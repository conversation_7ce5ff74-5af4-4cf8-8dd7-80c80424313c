import React, { useState, useEffect } from 'react';
import { Image, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';
import { TEXT_LINK_CTA } from '../../../textStrings';

const CountrySelection = ({
  title,
  subTitle,
  subHeader,
  countryDetails,
  onSelectCountry,
  selectedCountryName,
  setDisabled,
  updateSelectedCountry,
  countryInfo = {}, // only needed for tracking
}) => {
  const [selectedCountry, setSelectedCountry] = useState(selectedCountryName || null);

  const handleSelectCountryTracking = (country) => {
    visaTrackClickEvent({
      eventName: `${country?.name}_select_schengenfirstcountry`,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: `select_schengenfirstcountry`,
    });
  };
  const handleSelect = (country) => {
    handleSelectCountryTracking(country);
    setSelectedCountry(country.name);
    if (onSelectCountry) {
      onSelectCountry(country.name);
    }
    updateSelectedCountry(country.name);
    setDisabled(false);
  };

  useEffect(() => {
    visaTrackClickEvent({
      eventType: TYPE_OF_EVENTS.PAGE_LOAD,
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      eventValue: '',
    });
    if (selectedCountry) {
      updateSelectedCountry(selectedCountry);
    }
  }, []);

  const renderCountry = ({ item = {}, index = '' }) => {
    const { name = '', icon = '' } = item || {};
    return (
      <TouchableOpacity
        key={`${item?.name}-${index}`}
        style={[
          stylesCountrySelection.flexRow,
          selectedCountry === name && stylesCountrySelection.buttonSelectedContainer,
        ]}
        onPress={() => handleSelect(item)}
        activeOpacity={0.8}
      >
        <View style={stylesCountrySelection.columnContainerTwo}>
          {!!icon && <Image source={{ uri: icon }} style={stylesCountrySelection.flagStyle} />}
          <Text style={stylesCountrySelection.countryText}>{name}</Text>
        </View>

        <View style={stylesCountrySelection.buttonContainer}>
          <View style={stylesCountrySelection.selectButtonStyle}>
            <Text style={stylesCountrySelection.selectButtonText}>
              {selectedCountry === name ? TEXT_LINK_CTA.SELECTED : TEXT_LINK_CTA.SELECT}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <View style={stylesCountrySelection.container}>
      {!!title && <Text style={stylesCountrySelection.title}>{title}</Text>}
      {!!subHeader && <Text style={stylesCountrySelection.subHeader}>{subHeader}</Text>}
      <Text style={stylesCountrySelection.subTitle}>Country</Text>
      {countryDetails.map((item, index) => renderCountry({ item, index }))}
    </View>
  );
};

export default CountrySelection;

const stylesCountrySelection = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    ...paddingStyles.ph20,
    width: '100%',
  },
  title: {
    ...fontStyles.headingRegular,
    color: colors.black,
    ...marginStyles.mt20,
  },
  subHeader: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
    ...marginStyles.mt10,
  },
  subTitle: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
    ...marginStyles.mt20,
    ...marginStyles.mb10,
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...marginStyles.mt16,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.grayBorder,
    paddingHorizontal: 16,
  },
  columnContainerTwo: {
    flexDirection: 'row',
    ...marginStyles.mv14,
  },
  countryText: {
    ...fontStyles.labelMediumBold,
    color: colors.black,
    ...marginStyles.mt2,
  },
  flagStyle: {
    width: 36,
    height: 24,
    marginRight: 10,
    borderRadius: 3,
  },
  buttonContainer: {
    alignItems: 'flex-end',
    justifyContent: 'space-around',
  },
  selectButtonText: {
    ...fontStyles.labelSmallBold,
    color: colors.primaryBlue,
  },
  buttonSelectedContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.primaryBlue,
    backgroundColor: colors.lightBlueBg,
  },
  loadMoreContainer: {
    marginTop: 10,
  },
  loadMoreText: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
  },
  loadMoreButton: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  arrowDown: {
    width: 100,
    height: 20,
    marginLeft: 80,
  },
});
