import React, { useState, useEffect } from 'react';
import { Image, ScrollView, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { marginStyles } from '../../../Styles/Spacing';
import { paddingStyles } from '../../../Styles/Spacing';

/* Icons */
const arrowDown = require('@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp');
import checklistIcon from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';
import GreenTick from '@mmt/legacy-assets/src/ic-tick-green.webp';

/* Components */
import Counter from '@Frontend_Ui_Lib_App/Counter';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';
import { STRING_MAP } from '../../../textStrings';

const SelectNights = ({
  title,
  subtitle,
  setDisabled,
  countryList,
  updateCounterCountries,
  countriesToShow,
  handleLoadMore,
  showLoadMore,
  data,
  countryInfo,
}) => {
  const counterMinVal = 0;
  const counterMaxVal = data.maxNights ? data.maxNights : 15;
  const [counters, setCounters] = useState({});

  const setCountersData = (countryList) => {
    const data = countryList?.reduce((acc, item) => {
      acc[item?.name] = '0';
      return acc;
    }, {});
    setCounters(data);
  };

  const handleCounterTracking = (change) => {
    const changeType = change === 1 ? 'add' : 'subtract';
    const eventName = `${countryInfo?.name}_${changeType}night_schengenmultinight`;

    visaTrackClickEvent({
      eventName,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: `${changeType}night_schengenmultinight`,
    });
  };
  const handleCounterChange = (name, change) => {
    handleCounterTracking(change);

    setCounters((prevCounters) => {
      const parsedValue = parseInt(prevCounters?.[name], 10);
      const currentCounterValue = isNaN(parsedValue) ? 0 : parsedValue;
      let newCounterValue = currentCounterValue + change;
      newCounterValue = Math.max(counterMinVal, Math.min(counterMaxVal, newCounterValue));
      const updatedCounters = {
        ...prevCounters,
        [name]: String(newCounterValue),
      };
      updateCounterCountries(countryList, updatedCounters);
      setDisabled(false);
      return updatedCounters;
    });
  };

  const renderRow = (item, index) => {
    return (
      <View style={stylesNightsSelector.flexRow} key={`Row-${index}-${item?.name}`}>
        <View key={index} style={stylesNightsSelector.columnContainerTwo}>
          {item?.icon && (
            <Image source={{ uri: item.icon }} style={stylesNightsSelector.flagStyle} />
          )}
          {item?.name && <Text style={stylesNightsSelector.countryText}>{item.name}</Text>}
        </View>
        <View style={stylesNightsSelector.counterContainer}>
          <Counter
            counterValue={counters[item.name]}
            decrementHandler={() => handleCounterChange(item.name, -1)}
            incrementHandler={() => handleCounterChange(item.name, +1)}
            customContainerStyle={stylesNightsSelector.counter}
          />
        </View>
      </View>
    );
  };

  const renderLoadMore = () => {
    return (
      <View style={stylesNightsSelector.loadMoreContainer}>
        <TouchableOpacity style={stylesNightsSelector.loadMoreButton} onPress={handleLoadMore}>
          <Text style={stylesNightsSelector.loadMoreText}>{data.callToText}</Text>
          <Image source={checklistIcon} style={stylesNightsSelector.arrowDown} />
        </TouchableOpacity>
      </View>
    );
  };

  useEffect(() => {
    updateCounterCountries(countryList, counters);
  }, []);

  useEffect(() => {
    setCountersData(countryList);
  }, [countryList]);

  return (
    <View style={stylesNightsSelector.container}>
      <ScrollView>
        {title && <Text style={stylesNightsSelector.title}>{title}</Text>}
        {subtitle && <Text style={stylesNightsSelector.subTitle}>{subtitle}</Text>}
        <View style={stylesNightsSelector.columnContainerOne}>
          <Text style={stylesNightsSelector.columnText}>{STRING_MAP.MULT_COUNTRY_SELECT_TITLE}</Text>
          <Text style={stylesNightsSelector.columnText}>{STRING_MAP.MULTI_COUNTRY_SELECT_NIGHTS_TITLE}</Text>
        </View>
        {countryList
          ?.slice(0, countriesToShow)
          ?.sort((a, b) => a.sequence - b.sequence)
          .map((item, index) => renderRow(item, index))}
        {showLoadMore && countryList.length > countriesToShow && renderLoadMore()}
      </ScrollView>
    </View>
  );
};

export default SelectNights;

export const stylesNightsSelector = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    ...paddingStyles.ph20,
    width: '100%',
  },
  title: {
    ...fontStyles.headingRegular,
    color: colors.black,
    marginBottom: 10,
    marginTop: 20,
  },
  subTitle: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
  },
  columnContainerOne: {
    flexDirection: 'row',
    marginTop: 32,
    marginBottom: 16,
    marginRight: 5,
    justifyContent: 'space-between',
  },
  columnContainerTwo: {
    flexDirection: 'row',
    marginBottom: 24,
    alignItems: 'center',
  },
  columnText: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
  },
  countryText: {
    ...fontStyles.labelMediumBold,
    color: colors.black,
  },
  flagStyle: {
    width: 32,
    height: 26,
    marginRight: 10,
    borderRadius: 3,
  },
  counterContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.grayBorder,
    marginBottom: 10,
    padding: 2,
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...marginStyles.mv10,
  },
  loadMoreContainer: {
    marginTop: 10,
  },
  loadMoreText: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
  },
  loadMoreButton: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  arrowDown: {
    width: 20,
    height: 20,
  },
  counter: {
    width: 110,
    height: 45,
  },
});
