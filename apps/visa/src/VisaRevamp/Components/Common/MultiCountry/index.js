import React, { useState, useEffect } from 'react';
import { ScrollView, View, StyleSheet, Text } from 'react-native';
import Persuasion from './Persuasion';
import SelectNights from './SelectNights';
import CountrySelection from './CountrySelection';
import CountrySearchFooter from './CountrySearchFooter';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';
import { paddingStyles } from '../../../Styles/Spacing';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../Navigation';
import { fetchMultiCountryFLow } from '../../../Utils/NetworkUtils';
import { VIEW_STATES } from '../../../constants';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { isEmpty } from 'lodash';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';

/* Components */
import PersuasionWpr from '../../../Common/PersuasionWpr';
import HeaderWpr from '../HeaderWpr';
import LineLoader from '../Loader';
import ErrorPage from '../ErrorPage';

const MultiCountry = ({ countryInfo = {}, pot = '', navigation, searchId = '' }) => {
  const [viewState, setViewState] = useState(VIEW_STATES.LOADING);
  const [schengenDetails, setSchengenDetails] = useState({});
  const [rules, setRules] = useState({});
  const [countryDetailList, setCountryDetailList] = useState([]);
  const [countryList, setCountryList] = useState([]);
  const [countriesToShow, setCountriesToShow] = useState(0);
  const [showLoadMore, setShowLoadMore] = useState(true);
  const [currentComponent, setCurrentComponent] = useState('NightsSelector');
  const [disabled, setDisabled] = useState(true);
  const selectedCountry = countryDetailList.find((country) => country.code === countryInfo?.code);

  const renderErrorView = () => {
    return <ErrorPage handleBackClick={handleBackClick} />;
  };

  const handleTracking = () => {
    visaTrackClickEvent({
      eventName: '',
      eventType: TYPE_OF_EVENTS.PAGE_LOAD,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      eventValue: '',
    })
  }

  useEffect(() => {
    async function fetchData() {
      const response = await fetchMultiCountryFLow(countryInfo?.code, pot);
      if (!response) {
        setViewState(VIEW_STATES.ERROR);
        return;
      } else if (response) {
        setViewState(VIEW_STATES.SUCCESS);
        setSchengenDetails(response);
        setRules(response.data);
        setCountryDetailList(response.countries.popular);
        setCountriesToShow(response.countries.popular.length);
        setCountryDetailList([...response.countries.popular, ...response.countries.remaining]);
      }
    }
    fetchData();
    handleTracking()
  }, []);

  const getScreens = (rules) => {
    let nightsData = {};
    let visitFirstData = {};
    for (let key in rules) {
      if (rules.hasOwnProperty(key)) {
        if (rules[key].name === 'travelItinerary') {
          nightsData = rules[key];
        }
        if (rules[key].name === 'visitFirst') {
          visitFirstData = rules[key];
        }
      }
    }
    if (!isEmpty(nightsData) && currentComponent === 'NightsSelector') {
      return (
        <SelectNights
          data={nightsData}
          title={nightsData.description}
          subtitle={nightsData.subheader}
          setDisabled={setDisabled}
          countryList={countryDetailList}
          updateCounterCountries={updateCounterCountries}
          countriesToShow={countriesToShow}
          handleLoadMore={handleLoadMore}
          showLoadMore={showLoadMore}
          countryInfo={countryInfo}
        />
      );
    } else if (!isEmpty(visitFirstData) && currentComponent === 'CountrySelection') {
      return (
        <CountrySelection
          data={visitFirstData}
          title={visitFirstData.description}
          subtitle={visitFirstData.subheader}
          setDisabled={setDisabled}
          countryDetails={countryList}
          updateSelectedCountry={updateSelectedCountry}
          countryInfo={countryInfo}
        />
      );
    }
  };

  const updateSelectedCountry = (countryName) => {
    const updatedCountries = countryList?.map((country) => ({
      ...country,
      selected: country.name === countryName,
    }));
    setCountryList(updatedCountries);
  };

  const handleLoadMore = () => {
    let remaining = schengenDetails.countries?.remaining || [];
    const newCountriesToShow = countriesToShow + remaining.length;
    setCountriesToShow(newCountriesToShow);
    setShowLoadMore(newCountriesToShow < countryDetailList.length);
  };

  const updateCounterCountries = (countryDetailList, counters) => {
    const updatedCountries = countryDetailList
      ?.filter((country) => {
        const nights = parseInt(counters[country.name], 10);
        return nights > 0;
      })
      ?.map((country) => ({
        ...country,
        nights: parseInt(counters[country.name]),
      }));
    setCountryList(updatedCountries);
  };

  const findCountryWithMaxNights = (data) => {
    // Clean and convert 'nights' to integer if it's a valid number
    let cleanedData = data?.map((item) => ({
      ...item,
      nights: isNaN(parseInt(item.nights)) ? 0 : parseInt(item.nights),
    }));
    // Find the entry with the maximum 'nights'
    cleanedData.reduce(
      (max, current) => (current.nights > max.nights ? current : max),
      cleanedData,
    );
    return cleanedData;
  };

  const handleSubmit = (detailPage = false, countryCode) => {
    if (detailPage) {
      VisaNavigation.push(VISA_ROUTE_KEYS.DETAIL, {
        searchId,
        countryCode: countryCode,
        potId: pot,
      });
    } else {
      if (findCountryWithMaxNights(countryList).length == 1) {
        VisaNavigation.push(VISA_ROUTE_KEYS.DETAIL, {
          searchId,
          countryCode: findCountryWithMaxNights(countryList)[0].code,
          potId: pot,
        });
      } else if (currentComponent === 'CountrySelection') {
        let country = countryList.find((country) => country.selected);
        VisaNavigation.push(VISA_ROUTE_KEYS.DETAIL, {
          searchId,
          countryCode: country.code,
          potId: pot,
        });
      } else {
        const maxNights = findCountryWithMaxNights(countryList) ? findCountryWithMaxNights(countryList)[0].nights : 0;
        const countriesWithMaxNights = countryList.filter(country => country.nights === maxNights);
        setCountryList(countriesWithMaxNights);
        setCurrentComponent('CountrySelection');
      }
    }
  };

  const handleBackClick = () => {
    VisaNavigation.pop();
  };

  const handleClickTracking = (prefix) => {
    visaTrackClickEvent({
      eventName: `${prefix}_schengenmultinight`,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: `${prefix}_schengenmultinight`,
    })
  }
  const handleSkip = () => {
    handleClickTracking('skip')
    handleSubmit(true, countryInfo?.code);
  };

  const renderLoadingView = () => {
    return (
      <View style={styles.container}>
        <HeaderWpr
          clickHandler={() => handleBackClick()}
          titleText={schengenDetails.header}
          descText={schengenDetails.subheader}
          actionTextData="SKIP"
          actionHandler={handleSkip}
        />
        <Persuasion headerText={schengenDetails.headNote} />
        <ScrollView>{getScreens(rules, schengenDetails)}</ScrollView>
        <CountrySearchFooter
          currentComponent={currentComponent}
          selectedCountry={selectedCountry}
          updatedCountry={countryList}
          handleSubmit={handleSubmit}
          disabled={disabled}
          countryInfo={countryInfo}
          eligibility={countryList.length > 0 ? true : false}
        />
      </View>
    );
  };

  switch (viewState) {
    case VIEW_STATES.LOADING:
      return <LineLoader />;
    case VIEW_STATES.ERROR:
      return renderErrorView();
    case VIEW_STATES.SUCCESS:
      return renderLoadingView();
  }
};

export default MultiCountry;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  containerText: {
    marginRight: 35,
  },
  stepsHeader: {
    ...fontStyles.labelSmallBold,
    colors: colors.darkBlue2,
    marginTop: 34,
    marginBottom: 12,
  },
  title: {
    ...fontStyles.labelMediumBlack,
    marginTop: 8,
    marginBottom: 8,
  },
  subTitle: {
    ...fontStyles.labelBaseBlack,
    color: colors.gray,
    marginBottom: 8,
  },
  blueText: {
    ...fontStyles.labelBaseBold,
    color: colors.primaryBlue,
    marginBottom: 48,
  },
  buttonContainer: {
    marginBottom: 16,
  },
  topImage: {
    height: 80,
    width: 80,
    marginTop: -66,
  },
  wrapperStyle: {
    width: '100%',
    borderRadius: 0,
    alignContent: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    height: 60,
  },
  contentStyle: {
    ...fontStyles.labelSmallBold,
    color: colors.green,
    textAlign: 'center',
    marginHorizontal: '16%',
  },
});
