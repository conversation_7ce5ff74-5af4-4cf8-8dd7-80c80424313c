import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '../../../Styles/colors';
import { fontStyles } from '../../../Styles/fonts';
import { paddingStyles } from '../../../Styles/Spacing';

const GreenHeader = ({ headerText = '' }) => {
  return (
    <View style={stylesGreenHeader.container}>
      <Text style={stylesGreenHeader.title}>{headerText}</Text>
    </View>
  );
};

export default GreenHeader;

const stylesGreenHeader = StyleSheet.create({
  container: {
    backgroundColor: colors.fadedGreen,
    justifyContent: 'center',
    ...paddingStyles.pv16,
  },
  title: {
    ...fontStyles.labelSmallBold,
    color: colors.green,
    textAlign: 'center',
    marginHorizontal: '16%',
  },
});
