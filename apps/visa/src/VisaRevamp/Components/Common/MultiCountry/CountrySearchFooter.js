import React, { useState, useEffect } from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';

/* Icons */
import GreenTick from '@mmt/legacy-assets/src/ic-tick-green.webp';

/* Components */
import ButtonWpr from '../ButtonWpr/index';
import { logPDTEvent } from '../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../Tracking/pdt/constants';
import { set } from 'lodash';
import { BUTTON_CTA_MAP, STRING_MAP } from '../../../textStrings';

const CountrySearchFooter = ({
  selectedCountry = {},
  updatedCountry,
  handleSubmit,
  disabled,
  countryInfo = {},
  eligibility = false,
  currentComponent,
}) => {

  const [openApply, setOpenApply] = useState(false);
  const [showConfirm, setShowConfirm] = useState(true);

  useEffect(() => {
    if (updatedCountry && updatedCountry.length > 1) {
      if (checkAllNightsEqual(updatedCountry)) {
        setOpenApply(false);
        setShowConfirm(eligibility);
      } else {
        setOpenApply(false);
        setShowConfirm(eligibility);
      }
    } else {
      setOpenApply(false);
      setShowConfirm(eligibility);
    }
  }, [updatedCountry, eligibility, checkAllNightsEqual]);

  const findCountryWithMaxNights = (data) => {
    // Clean and convert 'nights' to integer if it's a valid number
    let cleanedData = data.map((item) => ({
      ...item,
      nights: isNaN(parseInt(item.nights)) ? 0 : parseInt(item.nights),
    }));
    // Find the entry with the maximum 'nights'
    cleanedData.reduce(
      (max, current) => (current.nights > max.nights ? current : max),
      cleanedData,
    );
    let selectedCountryList = cleanedData;
    selectedCountryList = selectedCountryList.filter((item) => item.name == selectedCountry?.name);
    if (selectedCountryList && selectedCountryList.length > 0)
      selectedCountry.nights = selectedCountryList[0].nights;
    cleanedData = cleanedData.filter((item) => item.name != selectedCountry?.name);
    return cleanedData;
  };

  const getCountryWithMaxNights = (updatedCountry) => {
    let countrieswithoutSelected = [];
    let country = [];
    if (updatedCountry) {
      if (updatedCountry && updatedCountry.length > 0) {
        countrieswithoutSelected = findCountryWithMaxNights(updatedCountry);
        country = countrieswithoutSelected.filter((item) => item.nights == Math.max(...countrieswithoutSelected.map((item) => item.nights)));
        return country;
      }
    }
  };

  const getFirstCountry = (updatedCountry) => {
    return updatedCountry.filter((country) => country.selected);
  }

  const checkAllNightsEqual = (updatedCountry) => {
    let countryCountEqual = true;
    if (updatedCountry) {
      if (updatedCountry && updatedCountry.length > 0) {
        updatedCountry.forEach((item) => {
          if (item.nights != updatedCountry[0].nights) {
            countryCountEqual = false;
          }
        });
      }
    }
    return countryCountEqual;
  };

  const isConfirm = () => {
    if (currentComponent === 'CountrySelection') {
      let firstCountry = getFirstCountry(updatedCountry);
      if (firstCountry.length > 0) {
        return true;
      }
      else {
        return false;
      }
    }
    else if (getCountryWithMaxNights(updatedCountry)) {
      return (
        getCountryWithMaxNights(updatedCountry) &&
        getCountryWithMaxNights(updatedCountry).length > 0 ||
        selectedCountry &&
        selectedCountry.nights > 0
      );
    }
    else {
      return false;
    }
  };


  const trackEvent = (prefix) => {
    visaTrackClickEvent({
      eventName: `${prefix}_schengenmultinight`,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      params: {
        countryName: countryInfo?.name,
      },
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: `${prefix}_schengenmultinight`,
    })
  }
  const handleApplyAndConfirm = () => {
    trackEvent('apply');
    handleSubmit(true, currentComponent === 'CountrySelection' ? getFirstCountry(updatedCountry)[0]?.code : getCountryWithMaxNights(updatedCountry)[0].code);
  };

  const handleConFirm = () => {
    trackEvent('confirm');
    if(currentComponent === 'CountrySelection'){
      let firstCountry = getFirstCountry(updatedCountry);
      if(selectedCountry && firstCountry && firstCountry.length > 0 && selectedCountry.code === firstCountry[0].code){ 
        handleSubmit(true, selectedCountry.code);
      }
      else{
        setOpenApply(true);
        setShowConfirm(false);
      }
    }
    else if (updatedCountry && updatedCountry.length > 1) {
      if (checkAllNightsEqual(updatedCountry)) {
        handleSubmit(false, selectedCountry.code);
      }
      else {
        if (getCountryWithMaxNights(updatedCountry).length > 0 &&
          selectedCountry.nights > getCountryWithMaxNights(updatedCountry)[0].nights) {
          handleSubmit(true, selectedCountry.code);
        }
        else if (selectedCountry.nights === getCountryWithMaxNights(updatedCountry)[0].nights) {
          handleSubmit(false, selectedCountry.code);
        }
        else if (selectedCountry.nights < getCountryWithMaxNights(updatedCountry)[0].nights) {
          setOpenApply(true);
          setShowConfirm(false);
        }
        else {
          setOpenApply(false);
        }
      }
    } else if (updatedCountry && updatedCountry.length === 1) {
      if (selectedCountry.code === updatedCountry[0].code) {
        handleSubmit(true, selectedCountry.code);
      }
      else {
        setOpenApply(true);
        setShowConfirm(false);
      }
    }
  };

  const handleContinue = () => {
    trackEvent('continue');
    handleSubmit(true, selectedCountry.code);
  };

  return (
    <View style={stylesSCPage.buttonContainer}>
      {openApply && updatedCountry && updatedCountry.length > 0 && (
        <View style={stylesSCPage.greenCountryBanner}>
          <View style={stylesSCPage.flexRow}>
            <Image source={GreenTick} style={stylesSCPage.checkMark} />
            <Text style={stylesSCPage.bannerHead}>{STRING_MAP.MULTI_COUNTRY_APPLY_FOR}</Text>
          </View>
          <View style={stylesSCPage.flexRow}>
            <Image
              source={{ uri: currentComponent === 'CountrySelection' ? getFirstCountry(updatedCountry)[0]?.icon : getCountryWithMaxNights(updatedCountry)[0]?.icon }}
              style={stylesSCPage.flagStyle}
            />
            <View style={stylesSCPage.columnContainer}>
              <Text style={stylesSCPage.countryText}>
                {currentComponent === 'CountrySelection' ? getFirstCountry(updatedCountry)[0]?.name : getCountryWithMaxNights(updatedCountry)[0]?.name}
              </Text>
              <Text style={stylesSCPage.countryTextSubtitle}>
                {currentComponent === 'CountrySelection' ? getFirstCountry(updatedCountry)[0]?.appointmentText : getCountryWithMaxNights(updatedCountry)[0]?.appointmentText}
              </Text>
            </View>
          </View>
        </View>
      )}

      {showConfirm && (
        <ButtonWpr
          buttonText={BUTTON_CTA_MAP.CONFIRM}
          onButtonPress={handleConFirm}
          disabled={!isConfirm()}
        />
      )}

      {openApply && updatedCountry && updatedCountry.length > 0 && (
        <View style={{ margin: 8, width: '100%' }}>
          <ButtonWpr
            buttonText={
              currentComponent === 'CountrySelection' ? `${STRING_MAP.MULTI_COUNTRY_APPLY_FOR}${' '}${getFirstCountry(updatedCountry)[0]?.name.toUpperCase()}`: 
              `${STRING_MAP.MULTI_COUNTRY_APPLY_FOR} ${getCountryWithMaxNights(updatedCountry)[0].name.toUpperCase()}`}
            onButtonPress={handleApplyAndConfirm}
            disabled={disabled}
          />
          <View style={{ marginTop: 8 }}>
            <ButtonWpr
              buttonText={BUTTON_CTA_MAP.CONTINUE_WITH + ' ' + selectedCountry?.name}
              buttonType="outline"
              onButtonPress={handleContinue}
            />
          </View>
        </View>
      )}
    </View>
  );
};

export default CountrySearchFooter;
const stylesSCPage = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  buttonContainer: {
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: 'red',
    // ...getPlatformElevation(5),
    //Todo add elevation on container
  },
  greenCountryBanner: {
    backgroundColor: colors.fadedGreen,
    padding: 16,
    width: '100%',
    marginBottom: 16,
    borderRadius: 5,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  checkMark: {
    width: 12,
    height: 12,
    marginRight: 5,
    marginTop: 3,
  },
  bannerHead: {
    color: colors.green,
    ...fontStyles.labelSmallBold,
    marginBottom: 13,
    letterSpacing: 2,
  },
  flagStyle: {
    width: 32,
    height: 26,
    borderRadius: 3,
    marginRight: 19,
  },
  columnContainer: {
    // flexDirection: 'column',
    flex: 1,
  },
  countryText: {
    ...fontStyles.labelMediumBold,
    color: colors.black,
  },
  countryTextSubtitle: {
    color: colors.gray,
    ...fontStyles.labelBaseRegular,
    marginTop: 4,
  },
});
