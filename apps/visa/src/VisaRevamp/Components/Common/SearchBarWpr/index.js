import React, { useState } from 'react';
import SearchBar from '@Frontend_Ui_Lib_App/SearchBar';
const SearchBarWpr = ({
  customStyles,
  inputValue,
  inputRef,
  placeholder,
  placeholderTextColor,
  onChangeText,
  leftAction,
  leftActionBtnHandler,
  onFocus,
}) => {
  return (
    <SearchBar
      customStyles={customStyles ? customStyles : ''}
      inputValue={inputValue}
      onChangeText={onChangeText ? onChangeText : (e) => setSearchValue(e.searchValue)}
      placeholder={placeholder ? placeholder : ''}
      placeholderTextColor={placeholderTextColor ? placeholderTextColor : ''}
      inputRef={inputRef}
      leftActionBtnHandler={leftActionBtnHandler ? leftActionBtnHandler : ''}
      leftAction={
        leftAction
          ? leftAction
          : {
              isDisabled: false,
              icon: 'https://cdn-icons-png.flaticon.com/512/54/54481.png',
            }
      }
      onFocus={onFocus}
    />
  );
};

export default SearchBarWpr;
