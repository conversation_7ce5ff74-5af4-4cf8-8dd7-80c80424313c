import { StyleSheet } from 'react-native';
import { colors } from '../../../Styles/colors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/fonts';

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    ...marginStyles.mb16,
    borderRadius: 16,
  },
  header: {
    ...paddingStyles.pa16,
    borderBottomWidth: 1,
    borderBottomColor: colors.grayBorder,
  },
  title: {
    ...fontStyles.labelLargeBlack,
    color: colors.black,
  },
  body: {
    ...paddingStyles.ph16,
    ...marginStyles.mv16,
  },
  inputFieldWrapperStyle: {
    ...marginStyles.mt12,
  },
  inputWrapperStyle: {
    width: '100%',
    ...marginStyles.mr16,
  },
  inputFieldContainer: {
    flexDirection: 'row',
    flex: 1,
    ...marginStyles.mt12,
  },
  buttonCustomStyles: {
    buttonWrapperStyle: {
      flex: 1,
      alignSelf: 'flex-start'
    },
    buttonTextStyle: {
      ...fontStyles.labelSmallBold,
      textAlign: 'center',
    },
  },
  buttonLoaderCustomStyles: {
    buttonWrapperStyle: {
      flex: 1,
    },
  },
  inputFieldStyle: {
    borderWidth: 1,
    borderColor: colors.grayBorder,
    backgroundColor: colors.lightGray3,
  },
  actionTextStyle: {
    color: colors.lightGray4,
    ...fontStyles.labelBaseBold,
    lineHeight: 18,
  },
  errorMessageStyle: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.mt6,
  },
  row: {},
  rowTitle: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
    ...marginStyles.mb6,
  },
  rowDesc: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
  },
  suggDescRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    ...marginStyles.mt6,
  },
  suggDesc: {
    flex: 1,
  },
  suggDescDistance: {
    ...fontStyles.labelSmallBold,
    color: colors.green,
    ...marginStyles.mb4,
  },
  suggCta: {
    ...fontStyles.labelSmallRegular,
    color: colors.primaryBlue,
  },
  iconWrapper: {
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.lightBlueBg,
    borderRadius: 18,
    marginRight: 10,
  },
  icon: {
    width: 18,
    height: 18,
  },
  greenText: {
    color: colors.green,
  },
  footerRow: {
    borderBottomWidth: 0,
  },
  addressName: {
    ...fontStyles.labelSmallBold,
    color: colors.black,
    ...marginStyles.mt10,
    ...marginStyles.mb6,
  },
  footerCta: {
    ...fontStyles.labelSmallBold,
    color: colors.primaryBlue,
  },
  asterisk: {
    color: 'red',
    position: 'relative',
  },
});

export default styles;
