import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Keyboard } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import styles from './style';
import { isValidPincode } from '../../../Utils';
import { colors } from '../../../Styles/colors';
import { checkPincodeDetails } from '../../../Utils/NetworkUtils';
import { paddingStyles } from '../../../Styles/Spacing';

/* Components */
import ButtonWpr from '../ButtonWpr';
import InputField from '@Frontend_Ui_Lib_App/InputField/lib/InputField';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { ALERTS } from '../../../textStrings';

const PINCODE_STATUS = {
  ERROR: 'PINCODE_INVALID',
  SUCCESS_AVAILABLE: 'PASSPORT_DELIVERY_AVAILABLE',
  SUCCESS_UNAVAILABLE: 'PASSPORT_DELIVERY_UNAVAILABLE',
  VAC: 'VAC_AVAILABLE',
  ADDRESS_UNAVAILABLE: 'ADDRESS_UNAVAILABLE',
};

const Pincode = ({
  pincodeDetails = {},
  bookingId = '',
  checkPincode = () => {},
  pincodeResponse = {},
  pincodeValueRef = null,
  isPincodeFetching = false,
}) => {
  const { header = '', description = '', PlaceholderText = [] } = pincodeDetails || {};
  const [actionTextColor, setActionTextColor] = useState(colors.primaryBlue);
  const [isFocused, setIsFocused] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [inputMessage, setInputMessage] = useState();
  const [status, setPincodeStatus] = useState();

  const handleSubmit = () => {
    Keyboard.dismiss();
    checkPincode(inputValue);
  };

  useEffect(() => {
    if (!_isEmpty(pincodeResponse)) {
      const { status, type, response, message } = pincodeResponse;

      if (status) {
        switch (type) {
          case PINCODE_STATUS.SUCCESS_AVAILABLE:
          case PINCODE_STATUS.SUCCESS_UNAVAILABLE:
          case PINCODE_STATUS.ERROR:
            setPincodeStatus(type);
            setInputMessage(response);
            break;
          case PINCODE_STATUS.ADDRESS_UNAVAILABLE:
            setPincodeStatus(PINCODE_STATUS.ERROR);
            setInputMessage(message);
            break;
        }
      } else {
        setPincodeStatus(PINCODE_STATUS.ERROR);
        setInputMessage(response);
      }
      visaTrackClickEvent({
        eventName: `${type}_loaded`,
        eventType: TYPE_OF_EVENTS.POPUP_LOAD,
      });
    }
  }, [pincodeResponse]);

  const handleActionTextColor = (isFocused) => {
    if (!isFocused) {
      setPincodeStatus();
    }
    setActionTextColor(
      status === PINCODE_STATUS.ERROR
        ? colors.lightGray4
        : isFocused
        ? colors.primaryBlue
        : colors.lightGray4,
    );
  };

  const handleInputValue = (val) => {
    setPincodeStatus();
    if (val !== inputValue) {
      setInputValue(val);
      if (pincodeValueRef) {
        pincodeValueRef.current = val;
      }
    }
  };

  const errorMessageColor =
    status === PINCODE_STATUS.ERROR
      ? colors.red
      : status === PINCODE_STATUS.SUCCESS_AVAILABLE
      ? colors.green
      : status === PINCODE_STATUS.SUCCESS_UNAVAILABLE
      ? colors.yellow
      : colors.primaryBlue;
  return (
    <View style={styles.row}>
      {header && (
        <Text style={styles.rowTitle}>
          {header}<Text style={styles.asterisk}>*</Text>
        </Text>
      )}
      {description && <Text style={styles.rowDesc}>{description}</Text>}

      <InputField
        actionText={isPincodeFetching ? 'Fetching' : 'Check'}
        onActionTextPress={
          !!inputValue
            ? handleSubmit
            : () => {
                showShortToast(ALERTS.PINCODE_VALID);
              }
        }
        customStyle={{
          actionTextStyle: {
            ...styles.actionTextStyle,
            color: !!inputValue
              ? isPincodeFetching
                ? colors.yellow
                : actionTextColor
              : colors.gray,
          },
          inputFieldWrapperStyle: styles.inputFieldWrapperStyle,
          wrapperStyle: styles.inputWrapperStyle,
          errorMessageStyle: {
            ...styles.errorMessageStyle,
            color: errorMessageColor,
          },
        }}
        onChangeText={handleInputValue}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={isFocused ? '' : PlaceholderText[0]}
        placeholderTextColor={colors.lightGray}
        value={inputValue}
        isError={status?.length > 0}
        {...(!!inputMessage && { errorMessage: inputMessage })}
        errorBgColor={status === PINCODE_STATUS.ERROR ? colors.lightRedBg : colors.lightBlueBg}
        errorBorderColor={status === PINCODE_STATUS.ERROR ? colors.red : colors.primaryBlue}
        inputProps={{
          inputMode: 'numeric',
          keyboardType: 'number-pad',
        }}
      />
    </View>
  );
};

export default Pincode;
