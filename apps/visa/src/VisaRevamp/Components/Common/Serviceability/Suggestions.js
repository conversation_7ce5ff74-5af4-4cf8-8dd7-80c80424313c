import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import styles from './style';
import { openMaps } from '../../../Utils';
import { marginStyles } from '../../../Styles/Spacing';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';

const Suggestions = ({ data, borderStyle }) => {
  const { centre, distance, date, address, callToText, callToAction = '' } = data;
  const handleMapClick = () => {
    visaTrackClickEvent({
      eventName: 'click_view_direction',
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK
    })
    openMaps(callToAction);
  };
  return (
    <View style={[styles.row, borderStyle]}>
      {!!centre && <Text style={styles.rowTitle}>{centre}</Text>}
      <View style={styles.suggescRow}>
        <View style={styles.suggDesc}>
          {!!(distance || date) && <Text style={styles.suggDescDistance}>{distance || date}</Text>}
          {!!address && <Text style={styles.rowDesc}>{address}</Text>}
        </View>
        {!!callToText && !!callToAction && (
          <TouchableOpacity activeOpactiy={1} onPress={handleMapClick} style={marginStyles.mt10}>
            <Text style={styles.suggCta}>{callToText}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default Suggestions;
