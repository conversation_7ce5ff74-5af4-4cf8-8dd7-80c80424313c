import React, { useEffect, useState } from 'react';
import { View, Text } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import { colors } from '../../../Styles/colors';
import styles from './style';
import { fontStyles } from '../../../Styles/fonts';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';

/* Components */
import InputField from '@Frontend_Ui_Lib_App/InputField/lib/InputField';
import { ALERTS } from '../../../textStrings';

const Address = ({ customerAddress = {}, addressRef = '' }) => {
  const title = 'Your address details';
  const { city = '', state = '' } = customerAddress || {};
  const initialCity = `${city || ''}${!!city && !!state ? ', ' : ''}${state || ''}`;
  const [address, setAddress] = React.useState('');
  const [cityState, setCity] = React.useState(initialCity);

  /* if pincode is changed we get new city and state */
  useEffect(() => {
    setCity(initialCity);
  }, [customerAddress?.city || customerAddress?.state]);

  const form = {
    fields: [
      {
        placeholder: 'Enter address here',
        value: address,
        autoFocus: true,
        key: 'address',
      },
      {
        key: 'city',
        placeholder: 'City, State',
        value: cityState,
        isDisabled: !!city && !!state,
      },
    ],
  };

  const handleTextChange = ({ value, key }) => {
    if (key === 'address') {
      setAddress(value);
      addressRef.current = {
        ...addressRef.current,
        address: value || '',
      };
      return;
    } else if (key === 'city') {
      setCity(value);
      addressRef.current = {
        ...addressRef.current,
        district: value || '',
      };
      return;
    }
  };

  const renderFormItem = (inputData, index) => {
    const { label, placeholder, value, isDisabled = false, key = '' } = inputData;

    const onChangeText = (value) => {
      if (value?.length > 250) {
        showShortToast(ALERTS.ADDRESS_INPUT_LIMIT);
        return;
      }
      handleTextChange({ value, key });
    };
    return (
      <View key={`Input-${index}`}>
        <InputField
          disabled={isDisabled}
          value={value}
          onChangeText={onChangeText}
          customStyle={{
            inputFieldWrapperStyle: styles.inputFieldWrapperStyle,
            wrapperStyle: {
              width: '100%',
            },
            inputFieldStyle: {
              ...(isDisabled && {
                ...fontStyles.labelMediumBlack,
                backgroundColor: colors.lightGray3,
                color: colors.grayBorder,
              }),
            },
          }}
          placeholder={placeholder}
          placeholderTextColor={colors.lightGray}
        />
      </View>
    );
  };
  return (
    <View style={styles.row}>
      {title && (
        <Text style={styles.rowTitle}>
          {title}
          <Text style={styles.asterisk}>*</Text>
        </Text>
      )}
      {form?.fields?.map((inputData, index) => renderFormItem(inputData, index))}
    </View>
  );
};

export default Address;
