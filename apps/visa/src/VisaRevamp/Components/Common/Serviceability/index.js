import React, { useState } from 'react';
import { View, Text } from 'react-native';
import _isEmpty from 'lodash/isEmpty';

import styles from './style';
import { checkPincodeDetails } from '../../../Utils/NetworkUtils';

/* Components */
import Seperator from '../Seperator';
import PassportPickupInfo from './PassportPickupInfo';
import Suggestions from './Suggestions';
import Address from './Address';
import Pincode from './Pincode';
import { paddingStyles } from '../../../Styles/Spacing';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';

const Serviceability = ({
  details = {},
  bookingId = '',
  addressRef = '',
  pincodeValueRef = '',
}) => {
  const { header, data } = details || {};
  const pincodeDetails = data.find((item) => item.type === 'PINCODE') || {};
  const [pincodeDetailsLoading, setPincodeDetailsLoading] = useState();
  const [pincodeResponse, setPincodeResponse] = useState();
  const {
    customerAddress = {},
    applicationAddress = [],
    courierAddress = {},
    title = '',
    note = '',
  } = pincodeResponse?.data || {};

  const checkPincode = async (inputValue) => {
    setPincodeDetailsLoading(true);
    visaTrackClickEvent({
      eventName: `click_check_pickup`,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });

    const response = await checkPincodeDetails({
      pincode: inputValue,
      bookingId,
    });

    setPincodeResponse(response);
    pincodeValueRef.current = inputValue;
    addressRef.current = {
      isAddressNeeded: !_isEmpty(response?.data?.customerAddress),
      district: `${response?.data?.customerAddress?.city}${
        response?.data?.customerAddress?.state ? `,${response?.data?.customerAddress?.state}` : ''
      }`,
    };
    setPincodeDetailsLoading(false);
  };

  const renderSeperator = () => {
    if (!pincodeResponse) {
      return null;
    }
    return (
      <View style={paddingStyles.pv16}>
        <Seperator />
      </View>
    );
  };

  const renderApplicationAddress = () => {
    return applicationAddress?.map((suggestion, index) => {
      const isLastIndex = index === applicationAddress.length - 1;
      return (
        <>
          <Suggestions data={suggestion} />
          {!isLastIndex && renderSeperator()}
        </>
      );
    });
  };
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{header}</Text>
      </View>
      <View style={styles.body}>
        {pincodeDetails && (
          <Pincode
            checkPincode={checkPincode}
            pincodeResponse={pincodeResponse}
            pincodeDetails={pincodeDetails}
            pincodeValueRef={pincodeValueRef}
            isPincodeFetching={pincodeDetailsLoading}
          />
        )}
        {applicationAddress?.length > 0 && (
          <>
            {renderSeperator()}
            {renderApplicationAddress()}
          </>
        )}
        {
          <>
            {(!!title || !!note || !_isEmpty(courierAddress)) && renderSeperator()}
            <PassportPickupInfo title={title} note={note} courierAddress={courierAddress} />
          </>
        }
        {!_isEmpty(customerAddress) && (
          <>
            {renderSeperator()}
            <Address customerAddress={customerAddress} addressRef={addressRef} />
          </>
        )}
      </View>
    </View>
  );
};

export default Serviceability;
