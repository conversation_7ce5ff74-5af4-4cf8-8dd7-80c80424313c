import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import styles from './style';
import { openMaps } from '../../../Utils';
import { marginStyles } from '../../../Styles/Spacing';
import { visaTrackClickEvent } from '../../../Tracking/utils';
import { TYPE_OF_EVENTS } from '../../../Tracking/constants';

const PassportPickupInfo = ({ title = '', note = '', courierAddress = {} }) => {
  const { centre = '', address = '', callToAction = '', callToText = '' } = courierAddress || {};
  const handleMapClick = () => {
    visaTrackClickEvent({
      eventName: 'click_view_direction',
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK
    })
    openMaps(callToAction);
  };
  return (
    <View style={[styles.row, styles.footerRow]}>
      {!!title && <Text style={[styles.rowTitle, styles.greenText]}>{title}</Text>}
      {!!note && <Text style={styles.rowDesc}>{note}</Text>}
      {!!centre && <Text style={styles.addressName}>{centre}</Text>}
      {!!address && <Text style={styles.rowDesc}>{address}</Text>}
      {!!callToAction && callToText && (
        <TouchableOpacity onPress={handleMapClick}>
          {!!callToText && <Text style={styles.footerCta}>{callToText}</Text>}
        </TouchableOpacity>
      )}
    </View>
  );
};

export default PassportPickupInfo;
