import { Platform } from "react-native";
import { VISA_ROUTE_KEYS } from "./Navigation";

export const PLATFORM_IOS = 'ios';
export const PLATFORM_ANDROID = 'android';
export const PLATFORM_WEB = 'web';

export const USER_PLATFORM = {
  IOS: Platform.OS === PLATFORM_IOS,
  ANDROID: Platform.OS === PLATFORM_ANDROID,
  WEB: Platform.OS === PLATFORM_WEB
};
export const MAP_URL_SCHEME = 'http://maps.apple.com/?q=';
export const HARDWARE_BACK_PRESS = 'hardwareBackPress';
export const STATUS_CODE_SUCCESS = 1;
export const BASEURL = 'https://visa-cb.makemytrip.com/';
export const BASE_URL_SERVER = 'http://**************:6098/';
export const COMMID_ENCRYPTION_API_ENDPOINT =
  'https://visa-cb.makemytrip.com/userService/encodeDecode';
export const DEFAULT_REGION = 'in';

export const API_URLS = {
  LANDING: 'v2/landing',
  DETAIL: 'v2/details',
  SAVED_TRAVELLERS: 'v2/travellers/getuserdetails',
  ADD_UPDATE_TRAVELLER: 'v2/travellers/updateuserdetails',
  SELECT_PLANS: 'v2/createvisaplans',
  THANK_YOU: 'v2/thankyou',
  SEARCH_PAGE: 'v2/search',
  FARE_BREAKUP: 'v2/farebreakup',
  CONFIRM_VISA_PLAN: 'v2/confirmvisaplans',
  REVIEW: 'v2/reviewbooking',
  LEARNINGS: 'v2/learning',
  MULTI_COUNTRY: 'v2/schengen',
  SAVE_PARTIAL_FORM: 'v2/savePartialForm',
  BOOKING: 'v2/getbooking',
  PASSPORT_SAFETY_TIPS: 'v2/passportsecure',
  CLASSIFIER_UPLOAD: 'v2/classifier/upload',
  GET_CLASSIFIER_DATA: 'v2/classifier/images',
  UPLOAD_DATA: 'api/v3/uploadFile',
  IS_PASSPORT_PRESENT: 'api/isPassportPresent',
  UPLOAD_PASSPORT: 'api/uploadPassport',
  MULTI_PAX_UPLOAD: 'api/multiPaxUpload',
  PINCODE: 'v2/zipcode',
  CONFIRM_CLASSIFIER_DATA: 'v2/classifier/confirm',
  COMPLETE_CLASSIFIER_DATA: 'v2/classifier/complete',
  ASSIGN_CLASSIFIER_DATA: 'v2/classifier/assign',
  DELETE_CLASSIFIER_DATA: 'v2/classifier/delete',
  CANCEL_CLASSIFIER_DATA: 'v2/classifier/cancel',
  SAVE_DOCUMENT: 'v2/savedocument',
  SAVE_DOCUMENTS: 'v2/savedocuments',
  GET_FORM: 'v2/getForm',
  MMTBLACK:'v2/premiumServices',
  DELETE_DOCUMENT: 'api/deleteDocument',
  UPDATE_PAX_NAME: 'api/updatePaxName',
};

export const PlatformAuthCode = {
  IOS: 'Basic bWFrZW15dHJpcElvczpWMXNhRnVubjNs',
  ANDROID: 'Basic bWFrZW15dHJpcEFuZHJvaWQ6VjFzYUZ1bm4zbA==',
  MWEB: 'Basic bWFrZW15dHJpcE13ZWI6VjFzYUZ1bm4zbA==',
};

export const VISA_TYPE_CODES = {
  E_VISA: 'E-VISA',
  STICKER_VISA: 'STICKER-VISA',
  EXPRESS_VISA: 'EXPRESS_VISA',
};


export const VIEW_STATES = {
  LOADING: 'LOADING',
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
  SHOW_DETAIL: 'SHOW_DETAIL',
  SHOW_BULK_UPLOAD: 'SHOW_BULK_UPLOAD',
}

export const EVENTS = {
  LANDING: {},
  DETAIL: {
    LOGIN_EVENT_LANDING: 'login_event_landing',
  },
  REVIEW: {
    PAYMENT_EVENT_REVIEW: 'payment_event_review',
  }
}


export const PAX_TYPES = {
  ADULT: 'ADULT',
  CHILD: 'CHILD',
  INFANT: 'INFANT',
}


export const STEP_PAGE_MAP = {
  1: VISA_ROUTE_KEYS.ADD_TRAVELLER,
  2: VISA_ROUTE_KEYS.SELECT_PLANS,
  3: VISA_ROUTE_KEYS.ADD_DOCUMENTS,
  4: VISA_ROUTE_KEYS.REVIEW,
}

export const FORM_DATA_TYPE_NEW = {
  TEXT: 'text',
  DATE: 'date',
  RADIO: 'radio',
  MULTI: 'multiselect',

  GROUP: 'group',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  DROPDOWN: 'dropdown'
};

export const VISA_PAYMENT_OPTION_TYPES = {
  FULL: 'FULL',
  PARTIAL: 'PARTIAL'
}

export const PDT_SUB_PAGE_NAMES = {
  DELIVERY_INFO: 'delivery_info',
  SPECIAL_EXCEPTION: 'special_exception',
  POT_POPUP: 'pot_selection',
  MULTI_COUNTRY_POPUP: 'schengen_country_selection',
  CALENDAR: 'calendar_popup',
  VISA_TAG_DETAILS: 'visa_tag_details',
  FARE_BREAKUP: 'fare_breakup',
  SKIP_DOCUMENTS: 'skip_documents',
  DOC_TYPE_POPUP: 'documents_type_popup',
  CONFIRM_REMOVAL_POPUP: 'confirm_removal_popup',
  MULTI_PAX_UPLOAD_POPUP: 'multi_pax_upload_popup',
  IDENTIFY_DOCUMENTS: 'identify_document',
  ASSIGN_TRAVELLERS: 'assign_travellers',
  DOC_GUIDELINES_POPUP: 'doc_guidelines_popup',
}

export const CONTENT_TYPES = {
  LANDING: 'landing',
  DETAIL: 'detail',
  REVIEW: 'review',
};
