import React, { PureComponent } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import styles from '../../../../css/VisaMainCss';

class VisaHeader extends PureComponent {
  render() {
    return (
      <View style={[styles.stickyHeaderMain, styles.mar0Bt]}>
        <View style={styles.topStatusBar}>
          <View style={styles.stickyBarWrapperDocument}>

            <TouchableOpacity
              onPress={this.props.onBackPressed}
              style={[styles.padding5]}
            >
              <Image style={[styles.mar3Bt, styles.backarrow]} source={backIcon} />
            </TouchableOpacity>

            <View style={styles.stickyTextWrapper}>
              {this.props.title && <Text numberOfLines={1} ellipsizeMode="tail" style={[styles.font18, styles.BlackText, styles.lightFont, styles.mar3Bt]}>
                {this.props.title}
              </Text>}
              {this.props.title == undefined && <Text numberOfLines={1} ellipsizeMode="tail" style={[styles.font18, styles.BlackText, styles.lightFont, styles.mar3Bt]}>
                Application form
              </Text>}
              <Text style={[styles.defaultGrey, styles.font11, styles.regularFont]}>
                {this.props.subTitle}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  }
}

VisaHeader.propTypes = {
  onBackPressed: PropTypes.func.isRequired,
  subTitle: PropTypes.string.isRequired
};


export default VisaHeader;
