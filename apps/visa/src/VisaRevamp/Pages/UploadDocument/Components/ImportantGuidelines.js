import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { getSImagePath, getImagePath } from '../../../Utils/VisaUtils';
import { colors } from '../../../Styles/colors';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/fonts';


const getList = (data) => {
    return data;
}

const ImportantGuidelines = ({ data, title, subTitle, bgColor, sampleImage }) => {
    var list = getList(data);
    return (
        <LinearGradient
            start={{ x: 1.35, y: 1.15 }}
            end={{ x: 0.5, y: 1.35 }}
            colors={bgColor === 'black' ? ['#000000', '#000000'] : ['#ffeba7', '#ffffff']}
            style={styles.guidelinesWrapper}>
            <View style={styles.contentWrapper}>
                {title && <Text style={styles.guidelineHeading}>{title}</Text>}
                {subTitle && <Text style={styles.guidelineSubHeading}>{subTitle}</Text>}
            </View>
            <View style={styles.listWrapper}>
                {list?.length > 0 && list.map((point) => (
                    <View style={styles.pointWrapper}>
                        {/* {bgColor ? <Image source={getImagePath('greenTick')} style={styles.tickIconStyle} /> : <Image source={getImagePath('greenTick')} style={styles.tickIconStyle} />} */}
                        <Text style={[bgColor ? colors.white : colors.defaultTextColor]}>• </Text>
                        <Text style={[styles.guidelinesStyleText, bgColor ? colors.white : colors.defaultTextColor]}
                        >{point}</Text>
                    </View>
                ))}
                {sampleImage &&

                    <View style={styles.sampleImageContainerStyle}>
                        <Image source={getSImagePath(sampleImage)} style={styles.sampleImageStyle} />
                    </View>
                }
            </View>
        </LinearGradient>
    )
};

const styles = StyleSheet.create({
    guidelinesWrapper: {
        backgroundColor: colors.white,
        padding: 16,
        borderRadius: 8,
        marginBottom: 16,
    },
    contentWrapper: {
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: colors.grayBorder,
        paddingBottom: 16,
    },
    listWrapper: {
        padding: 16,
    },
    sampleImageStyle: {
        width: 148,
        height: 140,
        borderRadius: 8
    },
    guidelineHeading: {
        ...fontStyles.labelBaseBold,
        ...marginStyles.mb8,
        color: colors.black,
    },
    guidelineSubHeading: {
        ...fontStyles.labelSmallRegular,
        color: colors.black,
    },
    pointWrapper: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    tickIconStyle: {
        width: 16,
        height: 16,
        marginRight: 10,
        marginTop: 4,
    },
    sampleImageContainerStyle: {
        ...marginStyles.mt10,
        alignItems: 'center'
    },
    guidelinesStyleText: {
        flex: 1,
        fontStyles: fontStyles.labelBaseRegular,
        color: colors.black,
    }
});
export default ImportantGuidelines;