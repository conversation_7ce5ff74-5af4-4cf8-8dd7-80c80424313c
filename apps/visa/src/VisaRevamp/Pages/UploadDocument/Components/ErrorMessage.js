import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const ErrorMessage = ({ warning, msg, title }) => {
  return (
    <View style={styles.errorWrapper}>
      <Text
        style={[
          AtomicCss.font14,
          AtomicCss.boldFont,
          AtomicCss.blackText,
          AtomicCss.marginBottom10,
        ]}>
        {title}
      </Text>
      <View >
        {
          msg &&
          msg.map((m) => {
            return (
              <View style={[styles.listWrapper, AtomicCss.marginBottom8]}>
                <View style={[warning ? styles.bullet : styles.bulletred]} />
                <Text
                  style={[
                    AtomicCss.font12,
                    AtomicCss.boldFont,
                    warning ? styles.warningText : styles.redText
                  ]}
                >
                  {m}
                </Text>
              </View>
            );
          })
        }
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  errorWrapper: {
    borderBottomWidth: 1,
    borderStyle: 'dashed',
    borderBottomColor: '#d8d8d8',
    paddingBottom: 16,
    paddingTop: 10,
    paddingHorizontal: 16,
  },
  listWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  bullet: {
    width: 6,
    height: 6,
    backgroundColor: '#cf8100',
    borderRadius: 10,
    marginTop: 4,
    marginRight: 8,
  },
  bulletred: {
    width: 6,
    height: 6,
    backgroundColor: '#eb2026',
    borderRadius: 10,
    marginTop: 4,
    marginRight: 8,
  },
  warningText: {
    color: '#cf8100',
  },
  redText: { color: '#eb2026' }
});

export default ErrorMessage;
