import { View, Text, StyleSheet, ScrollView, Image, BackHandler, TouchableOpacity, Alert } from 'react-native';
import { Asset, ImageLibraryOptions, launchImageLibrary } from 'react-native-image-picker';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../Navigation';
import { getMultipleApplicationFormResponse, uploadClassifierFile } from '../../Utils/NetworkUtils';
// import DocumentPicker from 'react-native-document-picker';
import { DocumentPickerOptions, pick, types } from '@react-native-documents/picker';
import { visaTrackClickEvent } from '../../Tracking/utils';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from '../../Tracking/constants';
import fecha from 'fecha';
import { VISA } from '../../Tracking/pdt/constants';
import store from '@mmt/legacy-commons/AppState/Store';

export const getPDTBookingInfoData = (response, pendingStatus = null) => {
    const state = store.getState();
    const { visaV2CommonReducers = {} } = state || {};
    const { visaDetails = {} } = visaV2CommonReducers || {};
    const { dateOfEntry = '', dateOfExit = '' } = visaDetails || {};
    const { bookingID = '',country = '',pricing = {}, journeyDate = '',isDocPageMandatory } = response || {};
    const { totalPrice = '' } = pricing || {};
    const numericValue = parseInt(totalPrice.replace(/[^\d]/g, ''));
    let from_date_time = '';
    let to_date_time = '';
    let booking_date = '';
    try {
      from_date_time = dateOfEntry ? fecha.format(dateOfEntry, 'DD-MM-YYYY') : '';
      to_date_time = dateOfExit ? fecha.format(dateOfExit, 'DD-MM-YYYY') : '';
      booking_date = fecha.format(Date.now(), 'DD-MM-YYYY');
    } catch (e) {
      console.log('Error in formatting date', e);
    }
    const booking_info = {
      booking_id: bookingID,
      booking_lob: VISA,
      ...(!!from_date_time && { from_date_time }),
      ...(!!to_date_time && { to_date_time }),
      ...(!!booking_date && { booking_date }),
      price: numericValue,
      currency: 'INR',
      origin: 'INDIA',
      destination: country,
      visa_delivery_date: '',
      payment_type: '',
      visa_plan: '',
      visa_type: '',
      document_submit_status: isDocPageMandatory? "MANDATORY": (pendingStatus ? pendingStatus : "NON_MANDATORY"),
    };
    return booking_info;
  };

export const isPassengerDocsUploaded = (travellerData) => {
    let maxdoc = 0;
    travellerData?.catStatusList?.forEach((category) => {
        category.docStatusList?.forEach((doc) => {
            maxdoc = maxdoc + 1;
        });
    });

    if (travellerData.documents && travellerData.documents.length === maxdoc) {
        return 'COMPLETE';
    }
    else if (travellerData.documents && travellerData.documents.length === 0) {
        return 'PENDING';
    }
    else if (travellerData.documents && travellerData.documents.length < maxdoc) {
        return 'INCOMPLETE';
    }
    else {
        return 'PENDING';
    }
};

export const allDocsUploaded = (visaAddDocumentData) => {
    let alldocs = true;
    visaAddDocumentData.passengers.forEach(travellerData => {
        let maxdoc = 0;
        travellerData?.catStatusList?.forEach((category) => {
            category.docStatusList?.forEach((doc) => {
                maxdoc = maxdoc + 1;
            });
        });
        if (!travellerData.documents) {
            alldocs = false
        }
        if (travellerData.documents && travellerData.documents.length === 0) {
            alldocs = false
        }
        if (travellerData.documents && travellerData.documents.length < maxdoc) {
            alldocs = false
        }
    })
    return alldocs
};

export const allFormsFilled = async (visaAddDocumentData) => {
    let response = {}
    let allFormsDataFilled = true;
    response.status = allFormsDataFilled;

    const forms = [];

    const fetchPromises = visaAddDocumentData.passengers.map(async (travellerData) => {
        const form = await fetchApplicationData(visaAddDocumentData, travellerData);
        if (form) {
            forms.push(form);
        }
    });
    await Promise.all(fetchPromises);
    if (forms.length === visaAddDocumentData.passengers.length) {
        for (const form of forms) {
            allFormsDataFilled = validateDynamicFormV2(form.formSchema.fields, form.formSchema.fieldValues);
            if (!allFormsDataFilled) {
                response.status = allFormsDataFilled;
                break;
            }
        }
    } else {
        allFormsDataFilled = true;
        return response;
    }
    return response
}

async function fetchApplicationData(visaAddDocumentData, travellerData) {
    const response = await getMultipleApplicationFormResponse(visaAddDocumentData.bookingID, travellerData.paxIndex);
    if (response && response.forms[0]) {
        return response.forms[0];
    }
    return null;
}

export const validateDynamicFormV2 = (fields, values) => {
    let isValid = true;
    if (!values) return isValid;
    const keys = Object.keys(values);
    keys.forEach((key) => {
        if (fields[key]) {
            let validLen = 0;
            values[key].forEach((elem) => {
                if (elem.value === '') {
                    isValid = isValid && !fields[key].mandatory && validateDynamicFormV2(fields, elem.dependents);
                } else {
                    isValid = isValid
                        && (fields[key].validation ? isValidPattern(new RegExp(fields[key].validation), elem.value) : true)
                        && validateDynamicFormV2(fields, elem.dependents);
                }
                if (isValid) validLen += 1;
            });
            if (fields[key].mandatory && !validLen) {
                isValid = false;
            }
        }
    });
    return isValid;
};

export const isValidPattern = (pattern, val) => {
    if (pattern) {
      return pattern.test(val);
    }
    return true;
  };


export const getUploadStatus = (category) => {
    let uploaded = true;
    let criticalError = false;
    let infoError = false;
    if (category.docStatusList) {
        category.docStatusList.forEach((doc) => {
            if (doc.uploaded === false) {
                uploaded = false
            }
            if (doc.prevError && !doc.prevError.softError && (doc.uploaded)) {
                infoError = true;
            }
            if (doc.prevError && doc.prevError.criticalError && !(doc.uploaded)) {
                criticalError = true;
            }
        });
    }
    if (uploaded) {
        return 'SUCCESS';
    }
    if (criticalError) {
        return 'ERROR';
    }
    if (infoError) {
        return 'INFO';
    }
}
export const getCategoryStatus = (category) => {
    let categoryName = '';
    let uploaded = true;
    let docStatusListinProcess = false;
    let criticalError = false;
    let infoError = false;
    if (category.docStatusList) {
        let docStatusListCount = category.docStatusList.length;
        let docStatusListUploaded = 0;

        category.docStatusList.forEach((doc) => {
            if (doc.uploaded === false) {
                uploaded = false;
                infoError = true;
            }
        });
    }
    if (uploaded) {
        return 'SUCCESS';
    }
    else {
        return 'INFO';
    }
}


export const getCategoryDescription = (category) => {
    let categoryName = '';
    let uploaded = true;
    let docStatusListinProcess = false;
    
    if (category.docStatusList) {
        let docStatusListCount = category.docStatusList.length;
        let docStatusListUploaded = 0;
        category.docStatusList.forEach((doc) => {
            if (doc.uploaded === false) {
                uploaded = false
            } else {
                docStatusListUploaded = docStatusListUploaded + 1;
            }
            if (docStatusListCount > docStatusListUploaded && docStatusListUploaded > 0) {
                docStatusListinProcess = true;
            }
        });
    }
    if (uploaded) {
        return 'SUCCESS';
    } else {
        categoryName = category.categoryDescriptionBefore;
        if (docStatusListinProcess) {
            categoryName = category.categoryDescriptionInProcess
        }
    }
    return categoryName;
}

export const formatNavSubTitle = ((param1, param2, param3, param2Key = 'Entry') => {
    let subTitle = '';
    if (param1) {
        subTitle += param1;
        if (param2) {
            subTitle += ` | ${param2Key} ${param2}`;
        }
        if (param3) {
            subTitle += ` | ${param3}`;
        }
    } else if (param2) {
        subTitle += `${param2Key} ${param2}`;
        if (param3) {
            subTitle += ` | ${param3}`;
        }
    } else if (param3) {
        subTitle += param3;
    }
    return subTitle;
});

export const getDisplayDate = (date) => {
    if (date) {
        return fecha.format(getDate(date), DATE_FORMAT_DISPLAY);
    }
    return '';
};

export const getFormResponse = (parentForm, childForm, commonFormKeys = []) => {
    commonFormKeys.forEach(key => {
        if (parentForm.formSchema.fieldValues[key]) {
            childForm.formSchema.fieldValues[key] = parentForm.formSchema.fieldValues[key];
        }
    });
    return childForm.formSchema.fieldValues;
}

export const openDocGallery = (visaAddDocumentData, setOpenBulkUploadDetail, setTriggerEffect) => {
    openPhoneDocGallery(visaAddDocumentData.maximumDocumentsAllowed, visaAddDocumentData.bookingID, setOpenBulkUploadDetail, setTriggerEffect);
    visaTrackClickEvent({
        eventName: NAME_OF_EVENTS.SELECT_ADD_DOC_BULK_UPLOAD,
        eventType: TYPE_OF_EVENTS.CARD_CLICK,
    });
}

const openPhoneDocGallery = (selectionLimit, bookingid, setOpenBulkUploadDetail, setTriggerEffect) => {
    let selectedImages = [];
    pick({
        type: [types.pdf],
        allowMultiSelection: true,
    }).then((results) => {
        if (results.length > selectionLimit) {
            Alert.alert('Limit Exceeded', `You can only select up to ${selectionLimit} documents.`);
        } else {
            for (const res of results) {
                let request = {};
                request.data = {
                    uri: res.uri,
                    type: res.type,
                    name: res.name,
                };
                request.documentName = res.name;
                request.format = res.type;
                request.bookingId = bookingid;
                uploadClassifierFile(request, true).then((response) => {
                    selectedImages.push(res);
                    setOpenBulkUploadDetail(false);
                    if (selectedImages.length === results.length) {
                        VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD, { bookingId: visaAddDocumentData.bookingID, visaAddDocumentData: visaAddDocumentData, setTriggerEffect: setTriggerEffect });
                    }
                });
            }
        }
    }).catch((error) => {

    });
}
