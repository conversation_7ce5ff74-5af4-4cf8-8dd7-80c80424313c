import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  BackHandler,
  TouchableOpacity,
  Alert,
  Platform,
  DeviceEventEmitter
} from 'react-native';
import { createVisaBooking, uploadClassifierFile, getMultipleApplicationFormResponse } from '../../Utils/NetworkUtils';
import { colors } from '../../Styles/colors';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { HARDWARE_BACK_PRESS, PDT_SUB_PAGE_NAMES, VIEW_STATES } from '../../constants';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { fontStyles } from '../../Styles/fonts';
import { openPhoneDocGallery, cameraRollNode } from '../../Utils/uploadutils';
import arrowRight from '@mmt/legacy-assets/src/arrow_blue_right.webp';
import { Asset, ImageLibraryOptions, launchImageLibrary } from 'react-native-image-picker';
// import DocumentPicker from 'react-native-document-picker';
import { DocumentPickerOptions, pick,types } from '@react-native-documents/picker';
import { logPDTEvent, visaPdtEventsInitilizer } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../Navigation';
import { getImagePath } from '../UploadDocument/utils';
import { visaTrackClickEvent } from '../../Tracking/utils';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from '../../Tracking/constants';
import store from 'packages/legacy-commons/AppState/Store';
import { getIsMyraEnabled } from '../../Utils/VisaPokusUtils';
import MyraFloatingCard from '../../MyraChat/MyraFloatingCard';
/* Components */
import UploadAllDocument from './Components/UploadAllDocuments';
import Passenger from './Components/Passenger';
import PassengerSkipDoc from './Components/PassengerSkipDoc';
import BottomBarWpr from '../../Components/Common/BottomBarWpr';
import VisaStepsHeader from '../../Components/Common/VisaStepsHeader';
import HeaderWrapper from '../../Components/Common/HeaderWpr';
import ErrorPage from '../../Components/Common/ErrorPage';
import LineLoader from '../../Components/Common/Loader';
import BottomSheetWpr from '../../Components/Common/BottomSheetWpr';
import ButtonWpr from '../../Components/Common/ButtonWpr';
import ProgressBar from '@Frontend_Ui_Lib_App/ProgressBar';
import VisaDataHolder from '../../Utils/VisaDataHolder';
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { PAGE_HEADERS, STRING_MAP } from '../../textStrings';
import { isEmpty } from 'lodash';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { ALERTS } from '../../textStrings';
import { allDocsUploaded, validateDynamicFormV2, allFormsFilled, getPDTBookingInfoData } from './utils';
import LineLoaderComp from '../../Components/Common/Loader/LineLoader';
import InfoList from './Components/InfoList';
import { getIsEnableSkipDoc } from '../../Utils/VisaPokusUtils';
import { initAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { resetBookingInfoAction, updateBookingInfoAction } from '../../Actions/pdtLoggerActions';
import { setThankyouBookingID } from '../../Actions/visaV2Actions';

const VisaAddDocumentContainer = ({ apiUrl = '', bookingId = '', fromMIMA = false }) => {
  const [abConfigLoaded, setAbConfigLoaded] = useState(false);
  const [viewState, setViewState] = useState(VIEW_STATES.LOADING);
  const [visaAddDocumentData, setVisaAddDocumentData] = useState({});
  const [openTagDetail, setOpenTagDetails] = useState(false);
  const [openBulkUploadDetail, setOpenBulkUploadDetail] = useState(false);
  const [triggerEffect, setTriggerEffect] = useState(false);
  const [skipDocDisplayed, setSkipDocDisplayed] = useState(false);
  const [paxFormStatus, setPaxFormStatus] = useState({});
  const [skipDocumentModal, setSkipDocumentModal] = useState(false);
  const [skipDocInfo, setSkipDocInfo] = useState({});
  const [toggleSaveConsent, setToggleSaveConsent] = useState(false);

  const handleFormStatusChange = (index, status) => {
    setPaxFormStatus(prevState => ({
      ...prevState,
      [index]: status
    }));
  };

  const getPaxFormStatus = (index) => {
    return paxFormStatus[index] || '';
  };
  const dispatch = store.dispatch;

  const handleBack = () => {
    if (fromMIMA) {
      const somethingPopped = VisaNavigation.canGoBack();
      if (!somethingPopped) {
        DeviceEventEmitter.emit('REFRESH_PAGE');
        if (Platform.OS === 'ios') {
          ViewControllerModule.popViewController(1);
        } else {
          BackHandler.exitApp();
        }
      } else {
        VisaNavigation.pop();
      }
    } else {
      VisaNavigation.pop();
    }
    return true;
  };

  async function fetchData() {
    setViewState(VIEW_STATES.LOADING);
    const response = await createVisaBooking({ apiUrl, bookingId });
    if (!response) {
      setViewState(VIEW_STATES.ERROR);
      return;
    } else if (response) {
      if (!response.documentPageRequired) {
        VisaNavigation.push(VISA_ROUTE_KEYS.REVIEW, {
          bookingId: response.bookingID,
          documentPageRequired: response.documentPageRequired,
        });
      }
      // Add this after line 115, before the store.dispatch call
      const passengersNeedingActionTemp = response?.passengers?.filter((passenger) => {
        return passenger.status !== "DOCUMENTS_UPLOADED" || passenger.visaFormData?.filled === false;
      }) || [];

      const movetoDetailTemp = passengersNeedingActionTemp.some((passenger) => passenger.status !== "DOCUMENTS_UPLOADED");
      const movetoFormTemp = passengersNeedingActionTemp.some((passenger) => passenger.visaFormData?.filled === false);

      // Use the same conditional logic pattern as your existing code
      let pendingStatus = null;
      if (movetoDetailTemp && movetoFormTemp) {
        pendingStatus = 'document_form_pending';
      } else if (movetoDetailTemp) {
        pendingStatus = 'document_pending';
      } else if (movetoFormTemp) {
        pendingStatus = 'details_pending';
      }
      store.dispatch(updateBookingInfoAction(getPDTBookingInfoData(response, pendingStatus)));
      store.dispatch(setThankyouBookingID(response.bookingID));
      visaTrackClickEvent({ eventType: TYPE_OF_EVENTS.PAGE_LOAD });
      visaPdtEventsInitilizer();
      setVisaAddDocumentData(response || {});
      setOpenTagDetails(response.skipDocument || false);
      setSkipDocInfo(response.skipDocumentInfo || {});
      setViewState(VIEW_STATES.SUCCESS);
    }
  }
  // const enableSkipDoc = getIsEnableSkipDoc() || false;

  useEffect(() => {
    fetchData();
    const subscription = BackHandler.addEventListener(HARDWARE_BACK_PRESS, handleBack);
    return () => {
      store.dispatch(resetBookingInfoAction());
      store.dispatch(setThankyouBookingID(''));
      subscription.remove();
    };
  }, [triggerEffect]);
  const initializeAbConfig = async () => {
    try {
      await initAbConfig();
      setAbConfigLoaded(true);
    } catch (error) {
      setAbConfigLoaded(true);
    }
  };

  useEffect(() => {
    initializeAbConfig();
  }, []);

  if (!abConfigLoaded) {
    return (
      <View>
        <LineLoaderComp />
      </View>
    );
  }

  const visaOmnitureTracking = (eventName = '') => {
    visaTrackClickEvent({
      eventName,
      eventType: TYPE_OF_EVENTS.POPUP_LOAD,
    })
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.contentSeen,
      eventValue: eventName,
    })
  }

  const PLATFORM = {
    IOS: 'ios',
    ANDROID: 'android',
    WEB: 'web',
  };

  const renderLoadingView = () => {
    return <LineLoader />;
  };

  const renderErrorView = () => {
    return <ErrorPage handleBackClick={handleBack} handleRefreshClick={fetchData()} />;
  };

  const closeShowException = (eventName = '') => {
    setSkipDocumentModal(false);
    setOpenTagDetails(false);
    setSkipDocDisplayed(true);
    !!eventName && visaTrackClickEvent({
      eventName,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    })
    let eventValue = '';
    switch (eventName) {
      case NAME_OF_EVENTS.CLOSE_SKIP_DOC:
        eventValue = NAME_OF_EVENTS.CLOSE_SKIP_DOC;
        break;
      case NAME_OF_EVENTS.CONTINUE_DOC_SUBMIT_CLICK:
        eventValue = NAME_OF_EVENTS.CONTINUE_DOC_SUBMIT_CLICK;
        break;
      case NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_CLOSE:
        eventValue = 'close_bulk_upload_doc_type_popup';
        break;
      case NAME_OF_EVENTS.SKIP_DOC:
        eventValue = 'click_skip_doc';
        break;
      case NAME_OF_EVENTS.ADD_NOW_SKIP_DOC:
        eventValue = 'click_add_documents_now';
        break;
      case NAME_OF_EVENTS.ADD_LATER_DOC_UPLOAD:
        eventValue = 'click_add_later';
        break;
      case NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_PRIMARY_CTA_CLICKED:
        eventValue = 'pending_document_popup_primarycta_clicked';
        break;
      case NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_SECONDARY_CTA_CLICKED:
        eventValue = 'pending_document_popup_secondarycta_clicked';
        break;
      case NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_CLOSED:
        eventValue = 'pending_document_popup_closed';
        break;
      default:
        break;
    }
    !!eventValue && logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue,
    })
  };

  const closeShowBulkUpload = (eventName = '') => {
    closeShowException(eventName);
    setOpenBulkUploadDetail(false);
    !!eventName && visaTrackClickEvent({
      eventName,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventValue: eventName,
    });
  }

  const checkAllFormsNotFilled = async () => {
    let formsnotfilled = false;
    if (allDocsUploaded(visaAddDocumentData)) {
      visaAddDocumentData.passengers.forEach(pax => {
        if (!getPaxFormStatus(pax.paxIndex)) {
          formsnotfilled = true;
        }
      });
    }
    if (formsnotfilled) {
      const res = await allFormsFilled(visaAddDocumentData)
      if (res) {
        if (!res.status) {
          formsnotfilled = true;
          showShortToast(ALERTS.APPLICATION_FORM_NOT_FILLED);
          return formsnotfilled;
        }
      }
    } else {
      return formsnotfilled;
    }
  }

  const openReview = async (eventName = '', pageName = '') => {
    closeShowException(eventName);

    if (fromMIMA) {

      const checkStatus = await checkAllFormsNotFilled()
      if (checkStatus) {
      }
      else if (!checkStatus) {
        const somethingPopped = VisaNavigation.canGoBack();
        if (!somethingPopped) {
          if (Platform.OS === 'ios') {
            ViewControllerModule.popViewController(1);
          } else {
            BackHandler.exitApp();
          }
        } else {
          VisaNavigation.pop();
        }
      }
    } else {
      VisaNavigation.push(VISA_ROUTE_KEYS.REVIEW, {
        bookingId: visaAddDocumentData.bookingID,
        pageSource: pageName,
        setTriggerEffect: setTriggerEffect,
      });
    }
  };

  const filteredPassengersData = visaAddDocumentData?.passengers?.map((passenger) => ({
    ...passenger,
    catStatusList: passenger?.catStatusList?.filter(category =>
      !category?.docStatusList?.every(doc =>
        doc?.disableUpload === true && doc?.qcStatus === ""
      )
    )
  }));

  const allDocumentsUploadedNFilled = filteredPassengersData?.every((passenger) =>
    passenger?.catStatusList?.every((category) =>
      category?.docStatusList?.every((doc) => doc?.uploaded)
    ) && passenger?.visaFormData?.filled
  );
  const passengersNeedingAction = visaAddDocumentData?.passengers?.filter((passenger) => {
    return passenger.status !== "DOCUMENTS_UPLOADED" || passenger.visaFormData?.filled === false;
  }) || [];

  // Define passengersNeedingForms at component level so it can be passed to PassengerSkipDoc
  const passengersNeedingForms = passengersNeedingAction?.filter(passenger =>
    passenger.visaFormData?.filled === false
  ).sort((a, b) => a.paxIndex - b.paxIndex);

  const movetoDetail = passengersNeedingAction.some((passenger) => passenger.status !== "DOCUMENTS_UPLOADED");
  const movetoForm = passengersNeedingAction.some((passenger) => passenger.visaFormData?.filled === false);
  const allNeedOnlyForms = passengersNeedingAction.length > 0 && passengersNeedingAction.every((passenger) => passenger.visaFormData?.filled === false);// if(passengersNeedingAction.length > 0 && passengersNeedingAction.length < 3){
  if (movetoDetail && movetoForm) {
    skipDocInfo.text = skipDocInfo.textBoth || '';
    skipDocInfo.dynamicSubText = skipDocInfo.subTextBoth || '';
    skipDocInfo.btnName = skipDocInfo.submitDocument || '';
  } else if (movetoDetail) {
    skipDocInfo.text = skipDocInfo.textDocument || '';
    skipDocInfo.dynamicSubText = skipDocInfo.subTextDocument || '';
    skipDocInfo.btnName = skipDocInfo.submitDocument || '';
  } else if (movetoForm) {
    skipDocInfo.text = skipDocInfo.textDetails || '';
    skipDocInfo.dynamicSubText = skipDocInfo.subTextDetails || '';
    skipDocInfo.btnName = skipDocInfo.submitDetails || '';
  }

  const renderBookingButton = () => {
    return (
      <ButtonWpr
        disabled={false}
        // disabled={fromMIMA ? false : isMandatory ? allDocumentsUploadedNFilled ? false : true : enableSkipDoc ? false : !allDocumentsUploadedNFilled}
        buttonText={'CONTINUE'}
        buttonSize={{ fontSize: 14 }}
        onButtonPress={() => {
          fromMIMA ? openReview(NAME_OF_EVENTS.CONTINUE_DOC_SUBMIT_CLICK) : allDocumentsUploadedNFilled ? openReview(NAME_OF_EVENTS.CONTINUE_DOC_SUBMIT_CLICK) : setSkipDocumentModal(true);
        }}
      />
    );
  };

  const openBulkUpload = () => {
    if (
      visaAddDocumentData.bulkUploadEnabled &&
      visaAddDocumentData.bulkUploadPdfEnabled &&
      visaAddDocumentData.maximumDocumentsAllowed > 0
    ) {
      setOpenBulkUploadDetail(true);
    } else {
      openGallery(visaAddDocumentData, setOpenBulkUploadDetail);
    }
    visaTrackClickEvent({
      eventName: NAME_OF_EVENTS.CLICK_BULK_UPLOAD,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });
    logPDTEvent({
      eventValue: 'add_classifier_clicked',
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });
  }
  const openPhoneDocGallery = async (selectionLimit, bookingid) => {
    let selectedImages = [];

    pick({
      type: [types.pdf],
      allowMultiSelection: true,
    })
      .then((results) => {
        if (results.length > selectionLimit) {
          Alert.alert('Limit Exceeded', `You can only select up to ${selectionLimit} documents.`);
        } else {
          for (const res of results) {
            let request = {};
            request.data = {
              uri: res.uri,
              type: res.type,
              name: res.name,
            };
            request.documentName = res.name;
            request.format = res.type;
            request.bookingId = bookingid;
            setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
            uploadClassifierFile(request, true).then((response) => {
              selectedImages.push(res);
              closeShowBulkUpload();
              setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
              if (selectedImages.length === results.length) {
                setViewState(VIEW_STATES.SUCCESS);
                VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD, {
                  bookingId: visaAddDocumentData.bookingID,
                  visaAddDocumentData: visaAddDocumentData
                });
              }
            });
          }
        }
      })
      .catch((error) => { });
  };

  const openGallery = async () => {
    let limit = visaAddDocumentData.maximumDocumentsAllowed;
    openPhoneGallery(limit, visaAddDocumentData.bookingID, visaAddDocumentData);
    visaTrackClickEvent({
      eventName: NAME_OF_EVENTS.SELECT_ADD_IMAGE_BULK_UPLOAD,
      eventType: TYPE_OF_EVENTS.CARD_CLICK,
    });
    logPDTEvent({
      eventValue: NAME_OF_EVENTS.SELECT_ADD_IMAGE_BULK_UPLOAD,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }

  const openPhoneGallery = (selectionLimit, bookingid, visaAddDocumentData) => {
    const imageGalleryParams = {
      mediaType: 'photo',
      selectionLimit: selectionLimit
    };
    launchImageLibrary(imageGalleryParams, async (res) => {
      return selectImages(res.assets || [], bookingid, visaAddDocumentData);
    });
  }

  const selectImages = (images, bookingid) => {
    let selectedImages = [];
    images.forEach((image) => {
      const compatNode = cameraRollNode(null, image);
      let request = {};
      request.data = {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      };
      request.documentName = image.fileName;
      request.format = image.type;
      request.bookingId = bookingid;
      setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
      uploadClassifierFile(request, true).then((response) => {
        selectedImages.push(compatNode);
        closeShowBulkUpload();
        setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
        if (selectedImages.length === images.length) {
          setViewState(VIEW_STATES.SUCCESS);
          VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD, {
            bookingId: visaAddDocumentData.bookingID,
            visaAddDocumentData: visaAddDocumentData,
            setTriggerEffect: setTriggerEffect,
          });
        }
      });
    });
  };

  const openDocGallery = () => {
    let limit = visaAddDocumentData.maximumDocumentsAllowed;
    openPhoneDocGallery(limit, visaAddDocumentData.bookingID);
    visaTrackClickEvent({
      eventName: NAME_OF_EVENTS.SELECT_ADD_DOC_BULK_UPLOAD,
      eventType: TYPE_OF_EVENTS.CARD_CLICK,
    });
    logPDTEvent({
      eventValue: NAME_OF_EVENTS.SELECT_ADD_DOC_BULK_UPLOAD,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }

  const toggleModal = () => {
    if (!openBulkUploadDetail) {
      visaTrackClickEvent({
        eventName: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD,
        eventType: TYPE_OF_EVENTS.POPUP_LOAD,
      });
    }
    setOpenBulkUploadDetail(!openBulkUploadDetail);
  };

  const toggleShowExceptionModal = () => {
    setOpenTagDetails(!openTagDetail);
  };

  const renderBulkLoadingView = () => {
    return (
      <View style={styles.centeredView}>
        <View style={styles.darkenedBackground}>
          <View style={styles.modalView}>
            <Image source={getImagePath('uploadDocImage')} style={styles.iconStyle} />
            <Text style={styles.modalText}>Adding Documents</Text>
            <LineLoaderComp />
          </View>
        </View>
      </View>
    );
  };

  const checkPaxDocStatus = () => {
    let pendingStatus = true;
    visaAddDocumentData.passengers.forEach((item) => {
      if (item.documents && item.documents.length > 0) {
        pendingStatus = false;
      }
    });
    return pendingStatus;
  }

  const handleActionHandler = () => {
    setSkipDocumentModal(true);
    // if(enableSkipDoc){
    //   setSkipDocumentModal(true);
    // }else {
    //   openReview(NAME_OF_EVENTS.ADD_LATER_DOC_UPLOAD);
    // }
  };

  const handleAddDocumentsNow = () => {
    closeShowException(NAME_OF_EVENTS.ADD_NOW_SKIP_DOC);
    const firstPax = visaAddDocumentData.passengers?.sort((a, b) => a.paxIndex - b.paxIndex)[0];
    VisaNavigation.push(VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP, {
      bookingId: visaAddDocumentData?.bookingID,
      paxIndex: firstPax?.paxIndex,
      index: 0,
      setPaxFormStatus: setPaxFormStatus,
      setTriggerEffect: setTriggerEffect,
      visaData: visaAddDocumentData,
      openReview: openReview
    });
  }
  const handleAddDocumentsNowSkipDoc = () => {
    closeShowException(NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_PRIMARY_CTA_CLICKED);
    const firstPax = passengersNeedingAction?.sort((a, b) => a.paxIndex - b.paxIndex)[0];
    VisaNavigation.push(VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP, {
      bookingId: visaAddDocumentData?.bookingID,
      paxIndex: firstPax?.paxIndex,
      index: 0,
      setPaxFormStatus: setPaxFormStatus,
      setTriggerEffect: setTriggerEffect,
      visaData: visaAddDocumentData,
      openReview: openReview
    });
  }

  const handleAddDocumentsNowForm = () => {
    closeShowException(NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_PRIMARY_CTA_CLICKED);
    // Use the passengersNeedingForms defined at component level
    const currentPaxDetails = passengersNeedingForms[0];
    // const currentPaxDetails = passengersNeedingAction?.sort((a, b) => a.paxIndex - b.paxIndex)[0];
    VisaNavigation.push(VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED_REVAMP, {
      data: currentPaxDetails,
      subTitle: visaAddDocumentData?.country ? `${visaAddDocumentData?.country} | ${visaAddDocumentData?.journeyDate} | ${currentPaxDetails?.name}` : '',
      bookingId: visaAddDocumentData?.bookingID,
      paxIndex: currentPaxDetails?.paxIndex,
      setPaxFormStatus: setPaxFormStatus,
      setTriggerEffect: setTriggerEffect,
      setToggleSaveConsent: setToggleSaveConsent,
      // NEW: Add these props for multi-passenger form flow
      allPassengersNeedingForms: passengersNeedingForms,
      currentFormIndex: 0,
      totalFormsNeeded: passengersNeedingForms.length,
      visaAddDocumentData: visaAddDocumentData,
      openReview: openReview
    });
  }

  const handleCloseSkipDocumet = () => {
    closeShowException(NAME_OF_EVENTS.CLOSE_SKIP_DOC)
  }
  const isMandatory = visaAddDocumentData?.isDocPageMandatory ? visaAddDocumentData?.isDocPageMandatory : false;

  const renderSuccessView = () => {
    const skipDocument = visaAddDocumentData.skipDocument || false;

    const { country, journeyDate, bulkUploadEnabled, skipDocumentInfo = {}, bulkUploadPersuasion = {} } = visaAddDocumentData || {};
    const limit = visaAddDocumentData.maximumDocumentsAllowed;
    return (
      <View style={styles.mainContainer}>
        <ScrollView
          vertical={true}
          showsVerticalScrollIndicator={false}
          style={styles.datacontentWrapper}
        >
          <HeaderWrapper wrapperStyle={getPlatformElevation(0)} clickHandler={handleBack} />
          {!fromMIMA && (
            <VisaStepsHeader
              skipPage={isMandatory ? false : !allDocumentsUploadedNFilled}
              header={PAGE_HEADERS.ADD_DOCUMENTS}
              actionHandler={handleActionHandler}
              country={country}
              journeyDate={journeyDate}
              stepIndex={3}
            />
          )}
          {fromMIMA && (
            <VisaStepsHeader
              header={PAGE_HEADERS.ADD_DOCUMENTS}
              country={country}
              journeyDate={journeyDate}
              showProgressBar={false}
            />
          )}
          <View style={[styles.dataWrapper, !bulkUploadEnabled ? marginStyles.mt10 : {}]}>
            {visaAddDocumentData.passengers
              ?.sort((a, b) => a.paxIndex - b.paxIndex)
              .map((item, index) => {
                return (
                  <Passenger
                    index={index}
                    data={item}
                    bookingId={visaAddDocumentData.bookingID}
                    setTriggerEffect={setTriggerEffect}
                    visaAddDocumentData={visaAddDocumentData}
                    subtitle={country + ' | ' + journeyDate + ' | ' + item.name}
                    setPaxFormStatus={setPaxFormStatus}
                    fetchData={fetchData}
                    openReview={openReview}
                    fromMIMA={fromMIMA}
                  />
                );
              })}
            {bulkUploadEnabled && limit > 0 && (
              <UploadAllDocument
                bulkUploadPersuasion={bulkUploadPersuasion}
                onapply={openBulkUpload}
                setTriggerEffect={setTriggerEffect}
              />
            )}
          </View>
        </ScrollView>
        {getIsMyraEnabled() && (
          <MyraFloatingCard footerHeight={50} pageName="AddDocuments" bookingId={visaAddDocumentData?.bookingID} />
        )}
        <View style={styles.bottomBarWrapper}>
          {visaAddDocumentData?.isDocPageMandatory && visaAddDocumentData?.docPageMandatoryText && (
            <View style={styles.mandatoryNoteContainer}>
              <HighlightedText
                str={visaAddDocumentData?.docPageMandatoryText}
                highlightedTxtStyle={{ ...fontStyles.labelSmallBlack, color: colors.gray }}
                normalTxtStyle={{ ...fontStyles.labelSmallRegular, color: colors.gray }}
                separator="*"
                numberOfLines={1}
              />
            </View>
          )}
          <BottomBarWpr
            title={
              fromMIMA ? (
                ''
              ) : (
                <View style={styles.priceContainer}>
                  <Text style={styles.priceText}>{visaAddDocumentData.pricing?.totalVisaCost}</Text>
                  <Text style={styles.priceText2}>
                    {visaAddDocumentData.pricing?.totalServiceFee}
                  </Text>
                </View>
              )
            }
            description={fromMIMA ? '' : 'Total for: ' + visaAddDocumentData.pricing?.totalPax}
            rightComponent={renderBookingButton}
          />
        </View>
        {/* {skipDocument && openTagDetail && !skipDocDisplayed && checkPaxDocStatus() && (
          <BottomSheetWpr
            visible={openTagDetail}
            setVisible={setOpenTagDetails}
            isCrossIcon={true}
            onDismiss={handleCloseSkipDocumet}
            callTracking={() =>
              visaOmnitureTracking(
                `${NAME_OF_EVENTS.SKIP_DOC_POPUP_LOAD}${skipDocumentInfo?.visaApplicationDate
                  ? `_${skipDocumentInfo?.visaApplicationDate}`
                  : ''
                }`,
              )
            }
            topIcon={{ uri: visaAddDocumentData.countryFlag }}
            bottomsheetName={PDT_SUB_PAGE_NAMES.SKIP_DOCUMENTS}
            children={
              <View>
                <Text style={styles.bottomsheetTitle}>
                  {skipDocumentInfo?.text}
                </Text>
                <View style={styles.subTitleContainer}>
                  <HighlightedText
                    str={skipDocumentInfo?.subText}
                    highlightedTxtStyle={styles.bottomsheetSubtitleHighlighted}
                    normalTxtStyle={styles.bottomsheetSubtitle}
                    separator="*"
                    numberOfLines={2}
                  />
                </View>
                <InfoList />
                <View style={marginStyles.mt8}>
                  <ButtonWpr
                    buttonText={'Add Documents Later'}
                    buttonType="outline"
                    onButtonPress={() => openReview(NAME_OF_EVENTS.SKIP_DOC)}
                  />
                </View>
                <View style={marginStyles.mv16}>
                  <ButtonWpr buttonText="ADD DOCUMENTS NOW" onButtonPress={() => closeShowException(NAME_OF_EVENTS.ADD_NOW_SKIP_DOC)} />
                </View>
              </View>
            }
          />
        )} */}

        {skipDocumentModal && (
          <BottomSheetWpr
            visible={skipDocumentModal}
            setVisible={setSkipDocumentModal}
            isCrossIcon={true}
            onDismiss={() => closeShowException(NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_CLOSED)}
            callTracking={() =>
              visaOmnitureTracking(
                `${NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_SHOWN}${skipDocumentInfo?.visaApplicationDate
                  ? `_${skipDocumentInfo?.visaApplicationDate}`
                  : ''
                }`,
              )
            }
            titleContainerStyles={{
              paddingRight: 0,
            }}
            topIcon={{ uri: visaAddDocumentData.countryFlag }}
            bottomsheetName={PDT_SUB_PAGE_NAMES.SKIP_DOCUMENTS}
            children={
              <View>

                <Text style={styles.bottomsheetTitle}>{skipDocInfo?.text}</Text>
                <View style={{ flexDirection: 'column', gap: 16, ...marginStyles.mt18 }}>


                  {skipDocInfo?.dynamicSubText && (
                    <View>
                      <HighlightedText
                        str={skipDocInfo?.dynamicSubText}
                        highlightedTxtStyle={styles.bottomsheetSubtitleHighlighted}
                        normalTxtStyle={styles.bottomsheetSubtitle}
                        separator="*"
                      />
                    </View>
                  )}

                  <View style={styles.subTitleContainer}>
                    <HighlightedText
                      str={skipDocInfo?.subText}
                      highlightedTxtStyle={styles.bottomsheetSubtitleHighlighted}
                      normalTxtStyle={styles.bottomsheetSubtitle}
                      separator="*"
                      numberOfLines={2}
                    />
                  </View>
                </View>

                {passengersNeedingAction.length > 0 && passengersNeedingAction.length < 3 && (
                  <View>
                    {passengersNeedingAction.map((passenger, index) => (
                      <PassengerSkipDoc
                        key={index}
                        data={passenger}
                        index={index}
                        needsDocuments={passenger.status !== "DOCUMENTS_UPLOADED"}
                        needsForm={passenger.visaFormData?.filled === false}
                        setPaxFormStatus={setPaxFormStatus}
                        setTriggerEffect={setTriggerEffect}
                        visaAddDocumentData={visaAddDocumentData}
                        fromMIMA={fromMIMA}
                        bookingId={visaAddDocumentData.bookingID}
                        subtitle={country + ' | ' + journeyDate + ' | ' + passenger.name}
                        setSkipDocumentModal={setSkipDocumentModal}
                        onClickPax={movetoForm && movetoDetail ? false : movetoForm ? true : false}
                        setToggleSaveConsent={setToggleSaveConsent}
                        passengersNeedingForms={passengersNeedingForms}

                      />
                    ))}
                  </View>
                )}
                <View style={styles.buttonContainer}>
                  <View>
                    <ButtonWpr
                      buttonText={skipDocInfo?.btnName}
                      onButtonPress={() => movetoForm && movetoDetail ? handleAddDocumentsNowSkipDoc() : movetoForm ? handleAddDocumentsNowForm() : handleAddDocumentsNowSkipDoc()}
                    />
                  </View>
                  {
                    !isMandatory &&
                    <View>
                      <ButtonWpr
                        buttonText={movetoForm && !movetoDetail ? 'Add Details Later' : 'Add Documents Later'}
                        buttonType="outline"
                        onButtonPress={() => openReview(NAME_OF_EVENTS.PENDING_DOCUMENT_POPUP_SECONDARY_CTA_CLICKED)}
                      />
                    </View>
                  }
                </View>
              </View>
            }
          />
        )}

        {openBulkUploadDetail && (
          <BottomSheetWpr
            visible={toggleModal}
            isCrossIcon={true}
            onDismiss={() => closeShowBulkUpload(NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_CLOSE)}
            callTracking={() => visaOmnitureTracking(NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD)}
            setVisible={toggleModal}
            title={'Select Type'}
            bottomsheetName={PDT_SUB_PAGE_NAMES.DOC_TYPE_POPUP}
            children={
              <View>
                <View style={styles.visaInfoContainer}>
                  <Image source={getImagePath('tipIcon')} style={styles.icon} />
                  <Text style={styles.visaInfoText}>
                    {STRING_MAP.ADD_DOC_UPLOAD_IMAGE_PERSUASION}
                  </Text>
                </View>
                <UploadRow
                  icon={getImagePath('addDoc')}
                  content="Add Documents"
                  onPress={() => openDocGallery()}
                />
                <View style={styles.borderStyle}>
                  <UploadRow
                    icon={getImagePath('addImage')}
                    content="Add Images"
                    onPress={() => openGallery()}
                  />
                </View>
              </View>
            }
          />
        )}
      </View>
    );
  };

  switch (viewState) {
    case VIEW_STATES.LOADING:
      return renderLoadingView();
    case VIEW_STATES.SUCCESS:
      return renderSuccessView();
    case VIEW_STATES.ERROR:
      return renderErrorView();
    case VIEW_STATES.SHOW_BULK_UPLOAD:
      return renderBulkLoadingView();
  }
};

function UploadRow({ icon, content, onPress }) {
  return (
    <TouchableOpacity onPress={() => onPress()}>
      <View style={styles.uploadRow}>
        <View style={styles.uploadRowColumn}>
          <Image source={icon} style={styles.icon} />
          <Text style={styles.uploadRowText}>{content}</Text>
        </View>
        <TouchableOpacity style={styles.arrowContainer} onPress={() => onPress()}>
          <Image source={arrowRight} style={styles.icon} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.lightGray6,
    justifyContent: 'space-between',
  },
  datacontentWrapper: {
    ...paddingStyles.pb16,
  },
  dataWrapper: {
    ...paddingStyles.pt16,
  },
  bottomsheetTitle: {
    ...fontStyles.headingMedium,
    color: colors.black,
    ...marginStyles.mt10,
  },
  subTitleContainer: {
    // ...marginStyles.mt16,
    marginBottom: 18,
    // marginRight: 43,
  },
  bottomsheetSubtitle: {
    ...fontStyles.labelBaseRegular,
    color: colors.gray,
  },
  bottomsheetSubtitleHighlighted: {
    ...fontStyles.labelBaseBlack,
    color: colors.gray,
    // color: colors.yellow,
  },
  bottomBarWrapper: {
    width: '100%',
    zIndex: 0,
    marginTop: 'auto',
  },
  container: {
    ...paddingStyles.pa8,
  },
  header: {
    ...fontStyles.labelMediumBold,
    borderBottomWidth: 1,
    borderColor: colors.grayBorder,
    ...paddingStyles.pb16,
  },
  uploadRow: {
    flexDirection: 'row',
    ...paddingStyles.pa8,
    ...paddingStyles.pv20,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  icon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  uploadRowText: {
    ...fontStyles.labelMediumBold,
    fontSize: 16,
    ...marginStyles.ml8,
    color: colors.black,
  },
  uploadRowColumn: {
    flexDirection: 'row',
  },
  borderStyle: {
    borderTopWidth: 1,
    borderColor: colors.grayBorder,
    ...marginStyles.mb16,
  },
  arrowContainer: {
    backgroundColor: 'transparent',
    width: 24,
    height: 24,
  },
  toggleButtonContainer: {
    ...marginStyles.mt40,
    width: 320,
    ...marginStyles.mh40,
  },
  togglePageContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  darkenedBackground: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
  },
  modalView: {
    backgroundColor: colors.white,
    borderRadius: 16,
    ...paddingStyles.pa20,
    alignItems: 'center',
    elevation: 5,
    width: 244,
    height: 145,
  },
  modalText: {
    ...marginStyles.mb24,
    ...fontStyles.labelMediumBold,
    color: colors.gray,
  },
  iconStyle: {
    width: 32,
    height: 32,
    ...marginStyles.mb12,
  },
  snackBarLayout: {
    wrapperStyle: {
      width: 340,
      height: 48,
      ...marginStyles.ml16,
      justifyContent: 'space-between',
      alignItems: 'center',
    },
  },
  contentStyle: {
    ...fontStyles.labelBaseRegular,
    color: colors.white,
  },
  visaInfoText: {
    ...fontStyles.labelSmallBold,
    color: colors.green,
    ...paddingStyles.pl8,
    flex: 1,
  },
  visaInfoContainer: {
    backgroundColor: colors.fadedGreen,
    ...paddingStyles.ph10,
    ...paddingStyles.pv6,
    ...marginStyles.mt16,
    flexDirection: 'row',
  },
  messageHighlightedText: {
    ...fontStyles.labelSmallBold,
    color: colors.yellow,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceText: {
    ...fontStyles.labelLargeBold,
    color: colors.white,
  },
  priceText2: {
    ...fontStyles.labelSmallRegular,
    color: colors.disableGrayBg,
    marginLeft: 4,
  },
  mandatoryNoteContainer: {
    backgroundColor: colors.fadedYellow,
    paddingHorizontal: 16,
    paddingVertical: 8
  },
  buttonContainer: {
    paddingBottom: Platform.OS === 'ios' ? 10 : 0,
    gap: 12,
    ...marginStyles.mt18,
  },
});
export default VisaAddDocumentContainer;
