import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableHighlight,
  Image,
  Animated,
  Easing,
  DeviceEventEmitter,
} from 'react-native';
import { colors } from '../../../../Styles/colors';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/fonts';
import { borderRadius } from '../../../../Styles/borderRadius';
import {
  isPassengerDocsUploaded,
  getCategoryDescription,
  getUploadStatus,
  getCategoryStatus,
} from '../../utils';
import { getImagePath } from '../../../../Utils/VisaUtils';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { logPDTEvent } from '../../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../../Tracking/pdt/constants';
import { STRING_MAP } from '../../../../textStrings';

const launch = (
  bookingId,
  paxIndex,
  index,
  visaAddDocumentData,
  setTriggerEffect,
  setPaxFormStatus,
  openReview,
  fromMIMA,
  setSkipDocumentModal,
) => {
  visaTrackClickEvent({
    eventName: `click_upload_pax_${paxIndex}`,
    eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
  });
  logPDTEvent({
    eventValue: `pending_document_popup_paxcta_clicked_${paxIndex}`,
    actionType: PDT_EVENT_TYPES.buttonClicked,
  });
  setSkipDocumentModal(false);
  VisaNavigation.push(VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP, {
    bookingId: bookingId,
    paxIndex: paxIndex,
    index: index,
    setPaxFormStatus: setPaxFormStatus,
    setTriggerEffect: setTriggerEffect,
    visaData: visaAddDocumentData,
    openReview: openReview,
    fromMIMA: fromMIMA,
  });
};

const showFormScreen = (data, subtitle, bookingId, setPaxFormStatus, setTriggerEffect,setSkipDocumentModal,setToggleSaveConsent,passengersNeedingForms,visaAddDocumentData,openReview) => {
  visaTrackClickEvent({
    eventName: `click_${data.name}_visa_form`,
    eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
  });
  logPDTEvent({
    eventValue: `pending_document_popup_paxcta_clicked_${data.paxIndex}`,
    actionType: PDT_EVENT_TYPES.buttonClicked,
  });
  setSkipDocumentModal(false);
  // Reorder the array to place the current passenger at index 0
  const reorderedPassengers = [...passengersNeedingForms];
  const currentPassengerIndex = reorderedPassengers.findIndex(passenger => passenger.paxIndex === data.paxIndex);

  if (currentPassengerIndex > 0) {
    // Remove the current passenger from its current position
    const [currentPassenger] = reorderedPassengers.splice(currentPassengerIndex, 1);
    // Add it to the beginning of the array
    reorderedPassengers.unshift(currentPassenger);
  }
  VisaNavigation.push(VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED_REVAMP, {
    data: data,
    subTitle: subtitle,
    bookingId: bookingId,
    paxIndex: data.paxIndex,
    setPaxFormStatus: setPaxFormStatus,
    setTriggerEffect: setTriggerEffect,
    setToggleSaveConsent: setToggleSaveConsent,
    allPassengersNeedingForms: reorderedPassengers,
    currentFormIndex: 0,
    totalFormsNeeded: reorderedPassengers.length,
    visaAddDocumentData: visaAddDocumentData,
    openReview: openReview,
  });
};
// New component for document status indicators
const DocumentStatusIndicator = ({ status }) => {
  if (status === 'SUCCESS') {
    return (
      <View style={styles.statusIndicator}>
        <Image source={getImagePath('submitImage')} style={styles.statusIcon} />
      </View>
    );
  } else if (status === 'INFO' || status === 'ERROR') {
    return (
      <View style={styles.statusIndicator}>
        <Image
          source={getImagePath('warningImage')}
          style={[styles.statusIcon, { tintColor: colors.yellow }]}
        />
      </View>
    );
  } else {
    return <View style={styles.bulletPoint} />;
  }
};

const PassengerSkipDoc = ({
  data,
  bookingId,
  subtitle,
  setTriggerEffect,
  index,
  setPaxFormStatus,
  visaAddDocumentData,
  openReview,
  fromMIMA,
  setSkipDocumentModal,
  needsDocuments,
  needsForm,
  onClickPax,
  setToggleSaveConsent,
  passengersNeedingForms,
}) => {
  const { name = ''} = data || {};
  return (
    <View style={styles.cardWrapper} key={`${index}-${name}`}>
      <TouchableHighlight
        underlayColor="#f1f1f1"
        style={{ ...paddingStyles.pt16 }}
        key={`${index}-${name}`}
        onPress={() => onClickPax?showFormScreen(data, subtitle, bookingId, setPaxFormStatus, setTriggerEffect,setSkipDocumentModal,setToggleSaveConsent,passengersNeedingForms,visaAddDocumentData,openReview):
          launch(
            bookingId,
            data.paxIndex,
            index,
            visaAddDocumentData,
            setTriggerEffect,
            setPaxFormStatus,
            openReview,
            fromMIMA,
            setSkipDocumentModal,
          )
        }
      >
        <View>
        <View style={styles.headerWrapper}>
          <View style={styles.completeIconContainer}>
            <Image source={getImagePath('warningImage')} style={styles.submitIconStyle} />
          </View>

          <Text style={styles.headingWrapper}>{name}</Text>
          <Text style={styles.detailsText}>Add Details</Text>
          <Image style={styles.rightArrowStyle} source={getImagePath('rightArrow')} />
        </View>

      <View style={styles.documentListContainer}>
        <View style={styles.documentItemContent}>
          <View style={styles.bulletPoint} />

          <Text style={styles.documentText}>
            {needsDocuments && needsForm
              ? 'Documents & Traveller details pending'
              : needsDocuments
              ? 'Documents pending'
              : 'Traveller details pending'}
          </Text>
        </View>
      </View>
      </View>
      </TouchableHighlight>
    </View>
  );
};

const styles = StyleSheet.create({
  cardWrapper: {
    backgroundColor: colors.white,
    // ...paddingStyles.pb16,
    ...marginStyles.mb16,
    ...borderRadius.borderRadius16,
    borderWidth: 1,
    borderColor: colors.grayBorder,
  },
  headerWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.grayBorder,
    ...paddingStyles.pb12,
    ...paddingStyles.ph16,
  },
  headingWrapper: {
    ...fontStyles.labelLargeBlack,
    flex: 1,
    color: colors.black,
  },
  detailsText: {
    ...fontStyles.labelSmallBold,
    color: colors.primaryBlue,
    ...marginStyles.ml4,
  },
  checkmarkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.green,
    justifyContent: 'center',
    alignItems: 'center',
    ...marginStyles.mr8,
  },
  checkmarkIcon: {
    width: 14,
    height: 14,
    tintColor: colors.white,
  },
  documentListContainer: {
    ...paddingStyles.pt12,
  },
  bulletPoint: {
    color: colors.gray,
    backgroundColor: colors.gray,
    width: 4,
    height: 4,
    borderRadius: 2,
    ...marginStyles.ml2,
    ...marginStyles.mr8,
  },
  headingWrapper: {
    ...fontStyles.labelLargeBlack,
    flex: 1,
    color: colors.black,
  },
  subHeadingWrapper: {
    ...fontStyles.labelSmallRegular,
    color: colors.black,
    ...paddingStyles.ph14,
  },
  title: {
    flex: 1,
    color: colors.black,
    ...fontStyles.labelBaseBold,
    ...marginStyles.mb4,
  },
  subtitle: {
    flex: 1,
    color: colors.gray,
    ...fontStyles.labelSmallRegular,
  },
  documentListContainer: {
    ...paddingStyles.pt12,
    flexDirection: 'row',
    flexWrap: 'wrap',
    ...paddingStyles.ph16,
    ...paddingStyles.pb16,
    columnGap: 12,
    rowGap: 8,
  },
  documentItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusIcon: {
    width: 14,
    height: 14,
  },
  documentText: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
    ...marginStyles.ml4,
  },
  documentWrapper: {
    ...paddingStyles.pv12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.grayBorder,
  },
  successText: {
    color: colors.green,
  },
  warningText: {
    color: colors.yellow,
  },
  lastIndexStyle: {
    marginBottom: 0,
    borderBottomWidth: 0,
    paddingBottom: 0,
  },
  rightArrowStyle: {
    width: 20,
    height: 20,
  },
  flexOne: {
    flex: 1,
  },
  contentWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    ...paddingStyles.ph16,
  },
  regularFont: {
    ...fontStyles.labelBaseRegular,
  },
  submitIconStyle: {
    width: 20,
    height: 20,
    ...marginStyles.mr8,
  },
  qcAcceptedStatusText: {
    ...fontStyles.labelBaseBold,
    color: colors.green,
  },
  qcRejectedStatusText: {
    ...fontStyles.labelBaseBold,
    color: colors.red,
  },
  qcAcceptedStatusIcon: {
    width: 20,
    height: 20,
    ...marginStyles.mr8,
  },
});

export default PassengerSkipDoc;
