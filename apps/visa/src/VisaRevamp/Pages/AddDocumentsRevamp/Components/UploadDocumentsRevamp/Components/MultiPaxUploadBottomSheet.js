import React from 'react';
import { StyleSheet, Text, View, Dimensions,TouchableOpacity } from 'react-native';
import { colors } from '../../../../../Styles/colors';
import { fontStyles } from '../../../../../Styles/fonts';
import ButtonWpr from '../../../../../Components/Common/ButtonWpr';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
const MultiPaxUploadBottomSheet = ({
  handleConfirm,
  onClose,
  descriptionText,
  primaryButtonText,
  secondaryButtonText,
  paxIndex,
  passengers,
  assignTravellersCard,
  selectedPaxIndexes,
  setSelectedPaxIndexes,
  uploadDocCount,
  category,
}) => {

  const handleSelectPax = (paxIndex) => {
    if(selectedPaxIndexes?.includes(paxIndex)) {
      setSelectedPaxIndexes(selectedPaxIndexes?.filter(index => index !== paxIndex));
    } else {
      setSelectedPaxIndexes([...selectedPaxIndexes, paxIndex]);
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.descriptionTextWrapper}>
        <Text style={styles.descriptionText}>{descriptionText}</Text>
      </View>
      <View style={styles.CheckBoxColumnContainer}>
        {passengers?.length > 1 &&
          passengers?.map(
            (pax) => {
              let maxUploaded = false;
              // let documentUploadedCount = 0;
              // documentUploadedCount = category?.docStatusList?.reduce((acc, doc) => {
              //   const documentUrls = pax?.documents?.find(document => document?.type === doc?.type)?.urls || [];
              //   return acc + (documentUrls?.length || 0);
              // }, 0);
              // maxUploaded = documentUploadedCount >= category?.maxUpload ? true : ((documentUploadedCount + uploadDocCount) > category?.maxUpload) && uploadDocCount > 0 ? true : false;
              // const messageText = maxUploaded ? documentUploadedCount >= category?.maxUpload ? '(Maximum Uploaded)' : `(Only ${Math.abs(documentUploadedCount - uploadDocCount)} document allowed)` : '';
              return pax?.paxIndex !== paxIndex && (
                <View style={{flexDirection: 'row', alignItems: 'center', gap: 10, justifyContent: 'space-between', width: '100%', paddingRight: 25}}>
                  <TouchableOpacity
                    style={[styles.CheckBoxContainer, maxUploaded && {opacity: 0.3, pointerEvents: 'none'}]}
                    key={pax?.paxIndex}
                    onPress={() => handleSelectPax(pax?.paxIndex)}
                    activeOpacity={0.7}
                  >
                    <CheckBox
                      isChecked={selectedPaxIndexes?.includes(pax?.paxIndex)}
                      activeColor={maxUploaded ? colors.gray : colors.primaryBlue}
                      borderColor={maxUploaded ? colors.gray : colors.primaryBlue}
                      disabled={maxUploaded}
                      onPress={() => handleSelectPax(pax?.paxIndex)}
                      boxRadius={4}
                      size={18}
                      tickSize={10}
                    />
                    <View>
                      <Text style={styles.PassengerText}>{pax?.name}</Text>
                    </View>
                  </TouchableOpacity>
                  {/* <View>
                    <Text style={styles.messageText}>{messageText}</Text>
                  </View> */}
                </View>
              )
            }
          )}
      </View>

      <View style={styles.buttonContainer}>
        <View style={{ flex: 1 }}>
          <ButtonWpr
            buttonText={secondaryButtonText}
            buttonType="outline"
            onButtonPress={onClose}
          />
        </View>
        <View style={{ flex: 1 }}>
          <ButtonWpr buttonText={primaryButtonText} onButtonPress={handleConfirm} />
        </View>
      </View>
    </View>
  );
};

const { height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    maxHeight: height * 0.7,
    width: '100%',
    flexDirection: 'column',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
    ...paddingStyles.pa16,
    ...marginStyles.mb16,
  },
  descriptionTextWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv20,
  },
  descriptionText: {
    ...fontStyles.labelBaseRegular,
    color: colors.black,
  },
  CheckBoxContainer: {
    borderRadius: 8,
    ...marginStyles.ml14,
    flexDirection: 'row',
    gap: 11,
  },
  PassengerText: {
    ...fontStyles.labelBaseRegular,
    color: colors.black,
},
CheckBoxColumnContainer:{
    flexDirection: 'column',
    gap: 22,
    marginBottom: 16,
    alignItems: 'left',
    ...paddingStyles.pl10,
    ...paddingStyles.pt8,
  },
  messageText: {
    fontSize: 10,
    fontWeight: '400',
    color: colors.black,
    whiteSpace: 'wrap',
}
});

export default MultiPaxUploadBottomSheet;
