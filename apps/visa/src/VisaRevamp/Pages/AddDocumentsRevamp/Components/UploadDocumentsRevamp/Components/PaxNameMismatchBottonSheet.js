import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  Dimensions
} from 'react-native';
import { colors } from '../../../../../Styles/colors';
import { fontStyles } from '../../../../../Styles/fonts';
import ButtonWpr from '../../../../../Components/Common/ButtonWpr';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing';
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';

const PaxNameMismatchBottomSheet = ({ descriptionText, description2Text, primaryButtonText, secondaryButtonText, tertiaryButtonText, primaryButtonHandler, secondaryButtonHandler, tertiaryButtonHandler }) => {
  return (
    <View style={styles.container}>
      <View style={styles.descriptionTextWrapper}>
        <HighlightedText  
          str={descriptionText}
          highlightedTxtStyle={styles.highlightedDescriptionText}
          normalTxtStyle={styles.descriptionText}
          separator="*"
          numberOfLines={3}
        />
        {
          description2Text &&
            <View style={{...paddingStyles.pt12}}>
              <HighlightedText  
                str={description2Text}
                highlightedTxtStyle={styles.highlightedDescriptionText}
                normalTxtStyle={styles.descriptionText}
                separator="*"
              />
            </View>
        }
      </View>
      <View style={styles.buttonContainer}>
        {
          primaryButtonText &&
            <ButtonWpr
              buttonText={primaryButtonText}
              onButtonPress={primaryButtonHandler}
              customStyle={{
                buttonTextStyle: {
                  ...fontStyles.labelMediumBold
                }
              }}
            />
        }
        {secondaryButtonText &&
          <ButtonWpr
            buttonText={secondaryButtonText}
            buttonType="outline"
            onButtonPress={secondaryButtonHandler}
            customStyle={{
              buttonTextStyle: {
                ...fontStyles.labelMediumBold
              }
            }}
          />
        }
        {
          tertiaryButtonText &&
          <ButtonWpr
            buttonText={tertiaryButtonText}
            buttonType="text"
            customStyle={{
              buttonTextStyle: {
                ...fontStyles.labelBaseBold,
                color: colors.primaryBlue,
                ...paddingStyles.pt8
              }
            }}
            onButtonPress={tertiaryButtonHandler}
          />
        }
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'column',
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 12,
    ...paddingStyles.ph16,
    ...paddingStyles.pt10,
    ...paddingStyles.pb24,
    ...marginStyles.mt8,
    backgroundColor: colors.white, 
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // Android shadow
    elevation: 8,
  },
  descriptionTextWrapper: {
    ...paddingStyles.ph20,
    ...paddingStyles.pv20,
  },
  descriptionText: {
    ...fontStyles.labelBaseRegular,
    color: colors.black,
  },
  highlightedDescriptionText: {
    ...fontStyles.labelBaseBold,
    color: colors.black,
  },
});

export default PaxNameMismatchBottomSheet;
