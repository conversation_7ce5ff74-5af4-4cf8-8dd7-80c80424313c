import React from 'react';
import {
    View,
    Text,
    Image,
    StyleSheet,
    Modal
  } from 'react-native';

import { getImagePath } from '@mmt/visa/src/VisaRevamp/Utils/VisaUtils';
import LineLoaderComp from '@mmt/visa/src/VisaRevamp/Components/Common/Loader/LineLoader'
import { colors } from '@mmt/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from '@mmt/visa/src/VisaRevamp/Styles/Spacing';
import { fontStyles } from '@mmt/visa/src/VisaRevamp/Styles/fonts';

export default function UploadingModal ({docCount, visible}) {
  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
    >
      <View style={styles.centeredView}>
        <View style={styles.darkenedBackground}>
          <View style={styles.modalView}>
            <Image source={getImagePath('documentIcon')} style={styles.iconStyle} />
            <Text style={styles.modalText}>Uploading {docCount} document</Text>
            <LineLoaderComp />
          </View>
        </View>
      </View>
    </Modal>
  )
};

const styles = StyleSheet.create({
    centeredView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'transparent',
    },
    darkenedBackground: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
    },
    modalView: {
        backgroundColor: colors.white,
        borderRadius: 16,
        ...paddingStyles.pa30,
        alignItems: 'center',
        elevation: 5,
        width: 244,
        height: 160,
    },
    iconStyle: {
        width: 32,
        height: 32,
        ...marginStyles.mb16,
    },
    modalText: {
        ...marginStyles.mb16,
        ...fontStyles.labelMediumBold,
        color: colors.gray,
    },
});