import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React from 'react';
import { paddingStyles, marginStyles } from '../../../../../../Styles/Spacing';
import { colors } from '../../../../../../Styles/colors';
import { fontStyles } from '../../../../../../Styles/fonts';
import { DeviceEventEmitter } from 'react-native';

const ConfirmationButtomSheet = ({ descriptionText, primaryButtonText, secondaryButtonText, handleConfirm, onClose }) => {
  const handlePrimaryAction = () => {
    DeviceEventEmitter.emit('REPLACE_DOCUMENT_DECISION', true);
    handleConfirm();
  };

  const handleSecondaryAction = () => {
    DeviceEventEmitter.emit('REPLACE_DOCUMENT_DECISION', false);
    onClose();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.descriptionText}>{descriptionText}</Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={handlePrimaryAction}
        >
          <Text style={[styles.buttonText, styles.primaryButtonText]}>{primaryButtonText}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={handleSecondaryAction}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>{secondaryButtonText}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pa20,
  },
  descriptionText: {
    ...fontStyles.labelBaseRegular,
    color: colors.black,
    ...marginStyles.mb20,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: colors.primaryBlue,
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: colors.primaryBlue,
  },
  buttonText: {
    ...fontStyles.labelLargeMedium,
  },
  primaryButtonText: {
    color: colors.white,
  },
  secondaryButtonText: {
    color: colors.primaryBlue,
  },
});

export default ConfirmationButtomSheet; 