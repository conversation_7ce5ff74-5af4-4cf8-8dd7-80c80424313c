import React from 'react';
import {
    View,
    ScrollView,
    Text,
    Image,
    StyleSheet,
    TouchableOpacity,
    Dimensions
  } from 'react-native';

import { getImagePath } from '@mmt/visa/src/VisaRevamp/Utils/VisaUtils';
import { colors } from '@mmt/visa/src/VisaRevamp/Styles/colors';
import { paddingStyles } from '@mmt/visa/src/VisaRevamp/Styles/Spacing';
import { fontStyles } from '@mmt/visa/src/VisaRevamp/Styles/fonts';
import BottomSheetWpr from '../../../../../Components/Common/BottomSheetWpr';
import { PDT_SUB_PAGE_NAMES } from '../../../../../constants';
import LinearGradient from 'react-native-linear-gradient';
import { PAGE_HEADERS } from '../../../../../textStrings';

export default function AllPassengerBottomSheet ({visible, setVisible, handleClose, passengers, handlePassengerSelect, dynamicHeight}) {
  return (
    <BottomSheetWpr
        title={<View><Text style={[fontStyles.labelMediumBold, paddingStyles.pt10, {color: colors.black}]}>{PAGE_HEADERS.SELECT_TRAVELLERS}</Text></View>}
        visible={visible}
        isCrossIcon={true}
        onDismiss={handleClose}
        bottomsheetName={PDT_SUB_PAGE_NAMES.CONFIRM_REMOVAL_POPUP}
        setVisible={setVisible}
        containerStyles={{
            padding: 0,
        }}
        titleContainerStyles={{
            ...paddingStyles.ph20,
            ...paddingStyles.pv16,
            borderBottomColor: colors.grayBorder,
            borderBottomWidth: 1,
        }}
        children={
            <AllPassengerList
                passengers={passengers}
                handlePassengerSelect={handlePassengerSelect}
                handleClose={handleClose}
                dynamicHeight={dynamicHeight}
            />
        }
    />
  )
};

const AllPassengerList = ({passengers, handlePassengerSelect, handleClose, dynamicHeight = Dimensions.get('window').height * 0.7}) => {
    const PAX_LINEAR_GRADIENT_COLORS = [
        ['#F5515F', '#9F0469'],
        ['#FF7F3F', '#FF3E5E'],
        ['#43E1A8', '#219393'],
        ['#3023AE', '#C86DD7'],
    ];

    const handlePaxSelect = (pax, index) => {
        handlePassengerSelect(pax, index);
        handleClose();
    }

    return (
        <ScrollView
            style={styles2(dynamicHeight).allPassengerListContainer}
            showsVerticalScrollIndicator={false}
        >
            {
                passengers?.map((passenger, index) => {
                    const lastItem = index === passengers?.length - 1;
                    const options = { day: '2-digit', month: 'short', year: 'numeric' };
                    const formattedDate = passenger?.dateOfBirth && new Date(Number(passenger?.dateOfBirth)).toLocaleDateString('en-GB', options).replace(/ /g, ' ') || '';
                    const passengerInfo = [passenger?.gender && passenger?.gender?.charAt(0).toUpperCase() + passenger?.gender?.slice(1).toLowerCase(), passenger?.age && passenger?.age + 'y', formattedDate];
                    const passengerInfoString = passengerInfo.filter(info => info !== null && info !== undefined && info !== '').join(', ');
                    return (
                        <TouchableOpacity style={[styles.passengerListItem, lastItem && {borderBottomWidth: 0, marginBottom: 30}]} onPress={() => handlePaxSelect(passenger?.paxIndex, index)}>
                            <View style={styles.passengerListItemLeft}>
                                <LinearGradient
                                    colors={PAX_LINEAR_GRADIENT_COLORS[index % PAX_LINEAR_GRADIENT_COLORS.length || 0]}
                                    start={{ x: 0.0, y: 0.0 }}
                                    end={{ x: 1.0, y: 0.0 }}
                                    style={styles.passengerAvatar}
                                >
                                    <Text style={styles.passengerAvatarText}>
                                        {passenger?.name?.split(' ')[0]?.[0]?.toUpperCase() || ''}
                                        {passenger?.name?.split(' ')[1]?.[0]?.toUpperCase() || ''}
                                    </Text>
                                </LinearGradient>
                                <View style={styles.passengerNameWrapper}>
                                    <Text style={styles.passengerNameText}>{passenger?.name}</Text>
                                    <Text style={styles.passengerAgeText}>{passengerInfoString}</Text>
                                </View>
                            </View>
                            <View style={{flex: 1}}>
                                <Image source={getImagePath('rightArrow')} style={styles.rightArrow} />
                            </View>
                        </TouchableOpacity>
                    )
                })
            }
        </ScrollView>
    );
};

const styles2 = (dynamicHeight) => StyleSheet.create({
    allPassengerListContainer: {
        ...paddingStyles.ph16,
        ...paddingStyles.pb24,
        flexDirection: 'column',
        maxHeight: dynamicHeight
    },
});

const styles = StyleSheet.create({
    passengerListItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 28,
        paddingRight: 28,
        borderBottomWidth: 1,
        borderBottomColor: colors.grayBorder,
        width: '100%',
    },
    passengerListItemLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8
    },
    passengerAvatar: {
        width: 40,
        height: 40,
        borderRadius: 20,
        flexShrink: 0,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.grayBorder,     
    },
    passengerAvatarText: {
        ...fontStyles.labelMediumBlack,
        color: colors.white,
    },
    passengerNameWrapper: {
        flexDirection: 'column',
        gap: 4,
        flex: 1,
    },
    passengerNameText: {
        ...fontStyles.labelMediumBlack,
        color: colors.black,
        whiteSpace: 'wrap',
    },
    passengerAgeText: {
        ...fontStyles.labelBaseRegular,
        color: colors.gray,
    },
    rightArrow: {
        width: 28,
        height: 28,
        resizeMode: 'contain',
    }
});