import React from 'react';
import { 
  StyleSheet, 
  Text, 
  View,
  Dimensions
} from 'react-native';
import { colors } from '../../../../../Styles/colors';
import { fontStyles } from '../../../../../Styles/fonts';
import ButtonWpr from '../../../../../Components/Common/ButtonWpr';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing';

const ConfirmationBottomSheet = ({ handleConfirm, onClose, descriptionText, primaryButtonText, secondaryButtonText }) => {
  return (
    <View style={styles.container}>
      <View style={styles.descriptionTextWrapper}>
        <Text style={styles.descriptionText}>{descriptionText}</Text>
      </View>
      <View style={styles.buttonContainer}> 
       {secondaryButtonText && <View style={{flex:1}}>
            <ButtonWpr
                buttonText={secondaryButtonText}
                buttonType="outline"
                onButtonPress={onClose}
            />
        </View>}
        <View style={{flex:1}}>
            <ButtonWpr
                buttonText={primaryButtonText}
                onButtonPress={handleConfirm}
            />
        </View>
      </View>
    </View>
  );
};

const { height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    maxHeight: height * 0.7,
    width: '100%',
    flexDirection: 'column',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
    ...paddingStyles.ph16,
    ...paddingStyles.pb8,
    ...marginStyles.mb16,
  },
  descriptionTextWrapper: {
    ...paddingStyles.ph20,
    ...paddingStyles.pv20,
  },
  descriptionText: {
    ...fontStyles.labelBaseBold,
    color: colors.gray,
  },
});

export default ConfirmationBottomSheet;