import { StyleSheet, Text, View, TouchableOpacity, Platform, Linking, Image, Animated, Alert } from 'react-native'
import React, {useEffect, useState, useRef} from 'react';
import { paddingStyles, marginStyles } from '../../../../../../Styles/Spacing';
import { colors } from '../../../../../../Styles/colors';
import { fontStyles } from '../../../../../../Styles/fonts';

import { launchImageLibrary,launchCamera } from 'react-native-image-picker';
import { cameraRollNode, checkIsCameraPermissionGranted } from '../../../../../../Pages/UploadDocument/utils';
import { isPassportPresent, uploadPassport, deleteDocument, multiPaxUpload, updatePaxName } from '../../../../../../Utils/NetworkUtils';
import { DocumentPickerOptions, pick, types } from '@react-native-documents/picker';
import AddDocumentsImage from '../../../../../../Components/Common/BottomSheetComponents/AddDocumentsImage';
import BottomSheetWpr from '../../../../../../Components/Common/BottomSheetWpr';
import { PDT_SUB_PAGE_NAMES, VIEW_STATES } from '@mmt/visa/src/VisaRevamp/constants';
import { showLongToast, showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import GuidelinesBottomSheet from '../GuidelinesBottomSheet';
import CategoryNotUploaded from './CategoryNotUploaded';
import CategoryUploaded from './CategoryUploaded';
import ConfirmationButtomSheet from '../ConfirmationBottomSheet';
import MultiPaxUploadBottomSheet from '../MultiPaxUploadBottomSheet';
import PaxNameMismatchBottomSheet from '../PaxNameMismatchBottonSheet';
import { logPDTEvent } from '../../../../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../../../../Tracking/pdt/constants';
import { NAME_OF_EVENTS } from '@mmt/visa/src/VisaRevamp/Tracking/constants';
import { DeviceEventEmitter } from 'react-native';
import { PAGE_HEADERS } from '../../../../../../textStrings';

export default function CategoryCard({ 
  category, 
  allData, 
  setData, 
  bookingId, 
  paxIndex, 
  type, 
  uploaded, 
  documents, 
  setIsUploading, 
  setUploadDocCount, 
  uploadDocCount,
  setViewState,
  uploadErrorState,
  setUploadErrorState,
  fetchData,
  categoryType,
  disableUpload,
  fromMIMA
}) {
  const [openUploadModal, setOpenUploadModal] = useState(false);
  const [showGuidelines, setShowGuidelines] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [replaceModalVisible, setReplaceModalVisible] = useState(false);
  // Global state to coordinate multiple uploads when popup is required
  const [globalPopupRequired, setGlobalPopupRequired] = useState(false);
  const [activeDocument, setActiveDocument] = useState({ docIndex: null, docType: null });
  const [openMultiPaxUploadModal, setOpenMultiPaxUploadModal] = useState(false);
  const [selectedPaxIndexes, setSelectedPaxIndexes] = useState([]);
  const [multiPaxRequest, setMultiPaxRequest] = useState({});
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertContent, setAlertContent] = useState({ title: '', subTitle: '' });
  const alertContentRef = useRef({ title: '', subTitle: '' });
  const [paxNameMismatchVisible, setPaxNameMismatchVisible] = useState(false);
  const [paxNameMatchesVisible, setPaxNameMatchesVisible] = useState(false);
  const [paxNameUpdate, setPaxNameUpdate] = useState({
    selectedPaxupdatedName: {
      firstName: '',
      lastName: ''
    },
    selectedPaxOldName: {
      firstName: '',
      lastName: ''
    },
    matchingExistingPaxName: {
      firstName: '',
      lastName: ''
    }
  });

  let nameChanged = false;

  const handleCloseMismatchModal = async(type) => {
    setIsUploading(false);
    setPaxNameMismatchVisible(false);
    setPaxNameMatchesVisible(false);
    logPDTEvent({
      eventValue: `click_close_${type}_modal`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
    const request = {
      bookingId: bookingId,
      paxId: paxIndex,
      firstName: paxNameUpdate?.selectedPaxOldName?.firstName,
      lastName: paxNameUpdate?.selectedPaxOldName?.lastName,
    }
    await updatePaxName(request);
    fetchData();
  }

  const handleUploadPassportAgain = () => {
    DeviceEventEmitter.emit('NAME_MISMATCH_DECISION', true);
    setPaxNameMismatchVisible(false);
    setPaxNameMatchesVisible(false);
    logPDTEvent({
      eventValue: `click_upload_passport_again`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }
  const handleUpdateNameasperPassport = () => {
    DeviceEventEmitter.emit('NAME_MISMATCH_DECISION', false);
    setPaxNameMismatchVisible(false);
    setPaxNameMatchesVisible(false);
    logPDTEvent({
      eventValue: `click_update_name_as_per_passport`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }
  const handleUploadPassportForExistingPax = () => {
    DeviceEventEmitter.emit('NAME_MATCH_DECISION', true);
    setPaxNameMismatchVisible(false);
    setPaxNameMatchesVisible(false);
    logPDTEvent({
      eventValue: `click_upload_passport_for_existing_pax`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }

  const handleIsUploading = (bool) => {
    setIsUploading(bool);
  }

  const handleOpenModal = () => {
    setOpenUploadModal(true);
    logPDTEvent({
      eventValue: `click_add_${category?.category}`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  };

  const handleCloseModal = () => {
    logPDTEvent({
      eventValue: `click_close_doctype_select`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
      setOpenUploadModal(false);
  };

  const handleOpenGuidelines = () => {
      setShowGuidelines(true);
      logPDTEvent({
        eventValue: `click_view_guidelines_${category?.category}`,
        actionType: PDT_EVENT_TYPES.buttonClicked,
      })
  };

  const handleCloseMultiPaxUploadModal = async() => {
    logPDTEvent({
      eventValue: `click_skip_apply_to_all`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
    setOpenMultiPaxUploadModal(false);
    if(multiPaxRequest?.length > 0){
      handleUploadingState(multiPaxRequest?.length, false);
      let uploadStatus = false;
      for (const imgItem of multiPaxRequest) {
        let request = {};
        request.data = {
          uri: imgItem.uri,
          type: imgItem.type,
          name: imgItem.fileName || imgItem.name,
        };
        request.documentName = imgItem.fileName || imgItem.name;
        request.format = "image/jpeg";
        request.bookingId = bookingId;
        request.docId = 1;
        request.docType = category?.docStatusList?.length === 1 ? category?.docStatusList[0]?.type : type;
        request.paxIds = paxIndex;
        uploadStatus = await uploadFile(request, null, imgItem.uri, type, uploadDocCount);
      }
      handleUploadingClose(multiPaxRequest?.length, uploadStatus);
      setMultiPaxRequest({});
      setSelectedPaxIndexes([]);
    }
  }

  const handleConfirmMultiPaxUpload = async () => {
    // Determine the actual pax IDs that will be affected (same logic as your original)
    const finalPaxIds =
      selectedPaxIndexes?.length >= 1
        ? selectedPaxIndexes?.includes(paxIndex)
          ? selectedPaxIndexes?.join(',')
          : paxIndex + ',' + selectedPaxIndexes?.join(',')
        : paxIndex;

    // Count unique pax IDs that will be affected
    const affectedPaxIds = finalPaxIds
      .toString()
      .split(',')
      .map((id) => id.trim())
      .filter((id) => id);
    const affectedCount = affectedPaxIds.length;
    const totalPassengers = allData?.passengers?.length || 0;

    // Determine selection type based on actual affected passengers
    let paxSelectionType = 'none';
    if (affectedCount === totalPassengers && affectedCount > 0) {
      paxSelectionType = 'full';
    } else if (affectedCount > 0 && affectedCount < totalPassengers) {
      paxSelectionType = 'partial';
    }

    logPDTEvent({
      eventValue: `click_apply_apply_to_all_${paxSelectionType}`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });

    setOpenMultiPaxUploadModal(false);
    if (multiPaxRequest?.length > 0) {
      handleUploadingState(multiPaxRequest?.length, false);
      let uploadStatus = false;
      for (const imgItem of multiPaxRequest) {
        let request = {};
        request.data = {
          uri: imgItem.uri,
          type: imgItem.type,
          name: imgItem.fileName || imgItem.name,
        };
        request.documentName = imgItem.fileName || imgItem.name;
        request.format = 'image/jpeg';
        request.bookingId = bookingId;
        request.docId = 1;
        request.docType =
          category?.docStatusList?.length === 1 ? category?.docStatusList[0]?.type : type;
        request.paxIds =
          selectedPaxIndexes?.length >= 1
            ? selectedPaxIndexes?.includes(paxIndex)
              ? selectedPaxIndexes?.join(',')
              : paxIndex + ',' + selectedPaxIndexes?.join(',')
            : paxIndex;
        uploadStatus = await uploadFile(request, null, imgItem.uri, type, uploadDocCount);
      }
      handleUploadingClose(multiPaxRequest?.length, uploadStatus);
      setMultiPaxRequest({});
      setSelectedPaxIndexes([]);
    }
  }

  const handleCloseGuidelines = (closeMethod = '') => {
    logPDTEvent({
      eventValue: `click_${closeMethod}_guideline`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
    setShowGuidelines(false);
  };
  const handleCloseGuidelinesViaCross = () => {
    handleCloseGuidelines('cross');
};

const handleCloseGuidelinesViaDone = () => {
    handleCloseGuidelines('done');
};

  const handleOpenDeleteModal = () => {
      setDeleteModalVisible(true);
  };

  const handleCloseDeleteModal = () => {
    setDeleteModalVisible(false);
  };
  const handleConfirmDeleteDocument = async () => {
    const request = {
      bookingId: bookingId,
      paxIndex: paxIndex,
      docIndex: activeDocument?.docIndex,
      docType: activeDocument?.docType,
    };
    try {
      handleCloseDeleteModal();
      setViewState(VIEW_STATES.LOADING);
      const response = await deleteDocument(request);
      if (response?.status === 0) {
        handleDeletedUpdateState(activeDocument?.docType, activeDocument?.docIndex, paxIndex);
        fetchData();
        showShortToast(response?.message);
      } else if (response?.status === 1) {
        showShortToast('Error while deleting document');
      }
      setViewState(VIEW_STATES.SUCCESS);
    } catch (error) {
      console.error('Error deleting document:', error);
    }
  };

  const handleReplaceConfirmation = async() => {
    DeviceEventEmitter.emit('REPLACE_DOCUMENT_DECISION', true);
    setReplaceModalVisible(false);
    // Reset global popup state since user addressed the popup
    setGlobalPopupRequired(false);
    logPDTEvent({
      eventValue: `click_replace_image`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  };

  const handleDiscardConfirmation = () => {
    DeviceEventEmitter.emit('REPLACE_DOCUMENT_DECISION', false);
    setReplaceModalVisible(false);
    // Reset global popup state since user addressed the popup
    setGlobalPopupRequired(false);
    logPDTEvent({
      eventValue: `click_discard_replace_image`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  };

  const handleCloseReplaceModal = () => {
    setReplaceModalVisible(false);
    // Reset global popup state since user dismissed the popup
    setGlobalPopupRequired(false);
    logPDTEvent({
      eventValue: `click_close_replace_image`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }

  const handleCloseCrossMultiPaxUploadModal = () => {
    setOpenMultiPaxUploadModal(false);
    logPDTEvent({
      eventValue: `click_close_apply_to_all`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
  }

  const handleOpenAlert = (title, subTitle) => {
    const newAlertContent = { title, subTitle };
    setAlertContent(newAlertContent);
    alertContentRef.current = newAlertContent;
    setAlertVisible(true);
  };

  const handleCloseAlert = () => {
    setAlertVisible(false);
    const emptyContent = { title: '', subTitle: '' };
    setAlertContent(emptyContent);
    alertContentRef.current = emptyContent;
  };

  const getDocUploadLimit = () => {
    const errorCount = uploadErrorState?.[type+paxIndex]?.length || 0;
    const documentUploadedCount = category?.docStatusList?.reduce((acc, doc) => {
      const documentUrls = documents?.find(document => document?.type === doc?.type)?.urls || [];
      return acc + (documentUrls?.length || 0);
    }, 0) || 0;
    const totalCount = errorCount + documentUploadedCount;
    const remainingDocUpload = category?.maxUpload - totalCount;
    return (remainingDocUpload > 0 && remainingDocUpload <= category?.maxUpload) ? remainingDocUpload : 0;
  };

  const handleUpdateState = (paxId, documentType, url, urlIndex, criticalError, softError, paxName, uploadStatus, passportResponse) => {   
    setData(prevData => {
      const updatedPassengers = prevData?.passengers?.map(passenger => {
        if (passenger?.paxIndex == paxId) {
          // Update documents array
          const updatedDocuments = passenger?.documents || [];
          const existingDocIndex = updatedDocuments.findIndex(doc => doc?.type == documentType);
          
          if (existingDocIndex >= 0) {
            // Update existing document
            const existingUrls = updatedDocuments[existingDocIndex].urls || [];
            const urlIndexExists = existingUrls.findIndex(u => u.urlIndex === urlIndex);
            
            if (urlIndexExists >= 0) {
              // Update existing URL
              existingUrls[urlIndexExists] = {
                url: url + `?cb=${Date.now()}`,
                urlIndex: urlIndex,
                criticalError: criticalError,
                softError: softError,
              };
            } else {
              // Add new URL
              existingUrls.push({
                url: url + `?cb=${Date.now()}`,
                urlIndex: urlIndex,
                criticalError: criticalError,
                softError: softError,
              });
            }
            
            updatedDocuments[existingDocIndex] = {
              ...updatedDocuments[existingDocIndex],
              urls: existingUrls,
              type: documentType,
            };
          } else {
            // Add new document
            updatedDocuments.push({
              urls: [{
                url: url + `?cb=${Date.now()}`,
                urlIndex: urlIndex,
                criticalError: criticalError,
                softError: softError,
              }],
              type: documentType,
              name: '',
              index: 1,
            });
          }

          // Update category status
          const updatedCatStatusList = passenger?.catStatusList?.map(cat => {
            if (cat?.category?.replace(/ /g, "_") === type) {
              return {
                ...cat,
                docStatusList: cat?.docStatusList?.map(doc => {
                  if (doc?.type === documentType) {
                    if(doc?.qcStatus !== "" || doc?.qcRejectionText !== "" || doc?.qcRejectionReason !== "") {
                      return {
                        ...doc,
                        uploaded: uploadStatus,
                        qcStatus: "",
                        qcRejectionText: "",
                        qcRejectionReason: "",
                        prevError: {
                          criticalError: criticalError,
                          softError: softError,
                        }
                      };
                    }else {
                      return {
                        ...doc,
                        uploaded: uploadStatus,
                        prevError: {
                          criticalError: criticalError,
                          softError: softError,
                        }
                      };
                    }
                  }
                  return doc;
                }),
              };
            }
            return cat;
          });
          // if(paxName?.toLowerCase() !== passenger?.name?.toLowerCase() && type === 'PASSPORT') {
          //   nameChanged = true;
          //   Alert.alert(
          //     'Name Updated',
          //     `Traveller name has been set to ${paxName} as per the uploaded passport. Please re-upload if this is incorrect.`,
          //     [
          //       {
          //         text: 'OK',
          //         onPress: () => {
          //           if(passportResponse?.lastNameValidationDescription) {
          //             handleOpenAlert(
          //               passportResponse.lastNameValidationDescription.title,
          //               passportResponse.lastNameValidationDescription.subTitle
          //             );
          //           }else {
          //             showShortToast(`Document(s) were successfully uploaded and saved`);
          //           }
          //         }
          //       }
          //     ]
          //   );
          // }

          return {
            ...passenger,
            name: paxName || passenger?.name,
            documents: updatedDocuments,
            catStatusList: updatedCatStatusList,
          };
        }
        return passenger;
      });

      return {
        ...prevData,
        passengers: updatedPassengers,
      };
    });
  }

  const uploadFile = async (request, compatNode, uri, type, uploadCount) => {
    setTimeout(() => {
      setIsUploading(true);
    }, 600);
    switch (type) {
      case 'PASSPORT':
        try {
          const passportResponse = await isPassportPresent(request);
          const oldPaxName = allData?.passengers?.find(pax => pax?.paxIndex === paxIndex)?.name;
          const paxName = type === 'PASSPORT' ? passportResponse?.uploadDocumentResponse?.firstName + ' ' + passportResponse?.uploadDocumentResponse?.lastName : null;
          if(passportResponse === null) {
            return false;
          }
          // Check for lastNameValidationDescription and show alert
          // if(passportResponse?.lastNameValidationDescription) {
          //   handleIsUploading(false);
          //   handleOpenAlert(
          //       passportResponse.lastNameValidationDescription.title,
          //       passportResponse.lastNameValidationDescription.subTitle
          //   );
          //   // return false;
          // }
          if(passportResponse?.status === 1 || passportResponse?.uploadDocumentResponse?.status === 1) {
            setUploadErrorState(prevData => {
              const newData = { ...prevData };
              if (!newData[type+paxIndex]) {
                newData[type+paxIndex] = [];
              }
              
              const existingDocIndex = newData[type+paxIndex].findIndex(item => item?.url === uri);
              if (existingDocIndex >= 0) {
                newData[type+paxIndex][existingDocIndex] = {
                  docType: 'error',
                  type: type,
                  url: uri,
                  criticalError: passportResponse?.message || passportResponse?.uploadDocumentResponse?.urls[0]?.criticalError,
                };
              } else {
                newData[type+paxIndex].push({
                  docType: 'error',
                  type: type,
                  url: uri,
                  criticalError: passportResponse?.message || passportResponse?.uploadDocumentResponse?.urls[0]?.criticalError,
                });
              }
              return newData;
            });
            return false;
          } else {
            const isPopUpRequired = passportResponse?.isPopUpRequired;
            
            // Set global popup state if ANY execution detects popup required
            if (isPopUpRequired) {
              setGlobalPopupRequired(true);
            }
            // Skip stack trace for cleaner logs
            const handleReplaceUpload = async(response, id) => {
              if (response?.isPopUpRequired) {
                handleIsUploading(false);
                setTimeout(() => {
                  setReplaceModalVisible(true);
                }, 600);
                return new Promise((resolve) => {
                  const handleReplaceDecision = async (shouldReplace) => {
                    subscription.remove();
                    if (shouldReplace) {
                      handleUploadingState(uploadCount);
                      response.shouldReplace = shouldReplace;
                      try {
                        const uploadPassportResp = await uploadPassport(response);
                        if(uploadPassportResp?.status === 0) {
                          if (uploadPassportResp?.urls) {
                            uploadPassportResp?.urls?.length > 1 ?
                            fetchData()
                            :
                            uploadPassportResp?.urls?.forEach(item => {
                              handleUpdateState(id, item?.docType, item?.thumbnails, item?.docIndex, item?.criticalError, item?.softError, uploadPassportResp?.firstName + ' ' + uploadPassportResp?.lastName, uploadPassportResp?.uploaded, uploadPassportResp);
                            });
                          }
                          handleIsUploading(false);
                          resolve(true);
                        } else {
                          resolve(false);
                        }
                      } catch (error) {
                        console.error('Error uploading passport:', error);
                        resolve(false);
                      }
                    } else {
                      handleUploadingState(uploadCount);
                      response.shouldReplace = false;
                      try {
                        const uploadPassportResp = await uploadPassport(response);
                        if(uploadPassportResp?.status === 0) {
                          handleIsUploading(false);
                          resolve(true);
                        } else {
                          resolve(false);
                        }
                      } catch (error) {
                        console.error('Error uploading passport:', error);
                        resolve(false);
                      }
                    }
                  };
  
                  const subscription = DeviceEventEmitter.addListener(
                    'REPLACE_DOCUMENT_DECISION',
                    handleReplaceDecision
                  );
  
                  return () => subscription.remove();
                });
              } else {
                if(response?.uploadDocumentResponse?.urls) {
                  response?.uploadDocumentResponse?.urls?.length > 1 ?
                  fetchData()
                  :
                  response.uploadDocumentResponse?.urls.forEach(item => {
                    handleUpdateState(id, item?.docType, item?.thumbnails, item?.docIndex, item?.criticalError, item?.softError, paxName, response?.uploadDocumentResponse?.uploaded, response);
                  });
                }
                return true;
              }
            }
            if(oldPaxName?.split(' ').join('')?.toLowerCase() !== paxName?.split(' ').join('')?.toLowerCase() && passportResponse?.isPassportFrontPresent) {
              const existingPaxName = allData?.passengers?.find(pax => pax?.name?.toLowerCase() == paxName?.toLowerCase());
              setPaxNameUpdate(prevData => {
                return {
                  ...prevData,
                  selectedPaxupdatedName: {
                    firstName: passportResponse?.uploadDocumentResponse?.firstName,
                    lastName: passportResponse?.uploadDocumentResponse?.lastName
                  },
                  selectedPaxOldName: {
                    firstName: oldPaxName?.split(' ')[0],
                    lastName: oldPaxName?.split(' ').slice(1).join(' ')
                  },
                  matchingExistingPaxName: {
                    firstName: existingPaxName?.name?.split(' ')[0],
                    lastName: existingPaxName?.name?.split(' ').slice(1).join(' ')
                  }
                }
              });
              if(existingPaxName) {
                handleIsUploading(false);
                setTimeout(() => {
                  setPaxNameMatchesVisible(true);
                }, 600);
                return new Promise((resolve) => {
                  const handleNameMatchDecision = async (uploadPassport) => {
                    subscription.remove();
                    if (uploadPassport) {
                      handleUploadingState(uploadCount);
                      if(!passportResponse?.isPopUpRequired) {
                        if(passportResponse?.uploadDocumentResponse?.urls) {
                          // Wait for all delete operations to complete
                          const deletePromises = passportResponse.uploadDocumentResponse.urls.map(async(item) => {
                            const request = {
                              bookingId: bookingId,
                              paxIndex: paxIndex,
                              docIndex: item?.docIndex,
                              docType: item?.docType,
                            };
                            const deleteResponse = await deleteDocument(request);
                            if(deleteResponse?.status === 0) {
                              // handleDeletedUpdateState(item?.docType, item?.docIndex, paxIndex);
                              console.log('delete passport success');
                            }else {
                              console.log('delete passport failed');
                            }
                            return deleteResponse;
                          });
                          
                          // Wait for all deletions to complete
                          await Promise.all(deletePromises);
                        }
                      }
                      const updatePaxNameRequest = {
                        bookingId: bookingId,
                        paxId: paxIndex,
                        firstName: oldPaxName?.split(' ')[0],
                        lastName: oldPaxName?.split(' ').slice(1).join(' '),
                      };
                      const updatePaxNameResponse = await updatePaxName(updatePaxNameRequest);
                      if(updatePaxNameResponse?.status === 0) {
                        // showShortToast('Traveller name updated successfully');
                      }else {
                        // showShortToast('Failed to update traveller name');
                      }
                      request.paxId = existingPaxName?.paxIndex;
                      handleUploadingState(uploadCount);
                      const passportResponse2 = await isPassportPresent(request);
                      if(passportResponse2 === null) {
                        resolve(false);
                      }
                      if(passportResponse2?.status === 1 || passportResponse2?.uploadDocumentResponse?.status === 1) {
                        setUploadErrorState(prevData => {
                          const newData = { ...prevData };
                          if (!newData[type+existingPaxName?.paxIndex]) {
                            newData[type+existingPaxName?.paxIndex] = [];
                          }
                          
                          const existingDocIndex = newData[type+existingPaxName?.paxIndex].findIndex(item => item?.url === uri);
                          if (existingDocIndex >= 0) {
                            newData[type+existingPaxName?.paxIndex][existingDocIndex] = {
                              docType: 'error',
                              type: type,
                              url: uri,
                              criticalError: passportResponse2?.message || passportResponse2?.uploadDocumentResponse?.urls[0]?.criticalError,
                            };
                          } else {
                            newData[type+existingPaxName?.paxIndex].push({
                              docType: 'error',
                              type: type,
                              url: uri,
                              criticalError: passportResponse2?.message || passportResponse2?.uploadDocumentResponse?.urls[0]?.criticalError,
                            });
                          }
                          return newData;
                        });
                        resolve(false);
                      }else {
                        handleReplaceUpload(passportResponse2, existingPaxName?.paxIndex).then((res) => {
                          if(res) {
                            setUploadErrorState({});
                            fetchData();
                            resolve(true);
                          }else {
                            resolve(false);
                          }
                        });
                      }
                    }
                  };
  
                  const subscription = DeviceEventEmitter.addListener(
                    'NAME_MATCH_DECISION',
                    handleNameMatchDecision
                  );
  
                  return () => subscription.remove();
                });
              }else {
                handleIsUploading(false);
                setTimeout(() => {
                  setPaxNameMismatchVisible(true);
                }, 600);
                return new Promise((resolve) => {
                  const handleNameMicmatchDecision = async (uploadAgain) => {
                    subscription.remove();
                      if (uploadAgain) {
                        if(!isPopUpRequired && !globalPopupRequired) {
                          if(passportResponse?.uploadDocumentResponse?.urls && passportResponse?.uploadDocumentResponse?.urls?.length > 0) {
                            const deletePromises = passportResponse.uploadDocumentResponse.urls.map(async(item) => {
                              const request = {
                                bookingId: bookingId,
                                paxIndex: paxIndex,
                                docIndex: item?.docIndex,
                                docType: item?.docType,
                              };
                              const deleteResponse = await deleteDocument(request);
                              if(deleteResponse?.status === 0) {
                                console.log('delete passport success');
                              }else {
                                console.log('delete passport failed');
                              }
                              return deleteResponse;
                            });
                            
                            // Wait for all deletions to complete
                            await Promise.all(deletePromises);
                          }
                        }
                        const updatePaxNameRequest = {
                          bookingId: bookingId,
                          paxId: paxIndex,
                          firstName: oldPaxName?.split(' ')[0],
                          lastName: oldPaxName?.split(' ').slice(1).join(' '),
                        };
                        const updatePaxNameResponse = await updatePaxName(updatePaxNameRequest);
                      if(updatePaxNameResponse?.status === 0) {
                        // showShortToast('Traveller name updated successfully');
                      }else {
                        // showShortToast('Failed to update traveller name');
                      }
                      await fetchData();
                      setTimeout(() => {
                        setOpenUploadModal(true);
                      }, 600);
                    } else {
                      handleReplaceUpload(passportResponse, paxIndex).then((res) => {
                        if(res) {
                          if(passportResponse?.lastNameValidationDescription) {
                            const newAlertContent = {
                              title: passportResponse?.lastNameValidationDescription?.title,
                              subTitle: passportResponse?.lastNameValidationDescription?.subTitle
                            };
                            setAlertContent(newAlertContent);
                            alertContentRef.current = newAlertContent;
                          }
                          resolve(true);
                        }else {
                          resolve(false);
                        }
                      });
                    }
                  };
  
                  const subscription = DeviceEventEmitter.addListener(
                    'NAME_MISMATCH_DECISION',
                    handleNameMicmatchDecision
                  );
  
                  return () => subscription.remove();
                });
              }
            }else {
              let replaceUpload = await handleReplaceUpload(passportResponse, paxIndex);
              if(replaceUpload) {
                return true;
              }else {
                return false;
              }
            }
          }
        } catch (error) {
          handleIsUploading(false);
          console.error('Error uploading passport:', error);
          return false;
        }
        break;
      default:
        try {
          const appliedResponse = await multiPaxUpload(request);
          if(appliedResponse === null) {
            return false;
          } else if(appliedResponse?.status === 0) {
            if(Object.keys(appliedResponse?.paxUploadResponse)?.length > 0) {
              let uploadSuccess = true;
              Object.entries(appliedResponse?.paxUploadResponse).forEach(([key, value]) => {
                if(value?.status === 0) {
                  if(Object.keys(appliedResponse?.paxUploadResponse)?.length > 1) {
                    fetchData();
                  } else {
                    if(value?.urls?.length > 0) {
                      value?.urls?.forEach((item) => {
                        
                        if(item?.docType === type || item?.docType === categoryType) {
                          handleUpdateState(key, item?.docType || type, item?.thumbnails, item?.docIndex, item?.criticalError, item?.softError, null, value?.uploaded, null);
                        }else {
                          fetchData();
                        }
                      });
                    }
                    else{
                      uploadSuccess = false;
                    }
                  }
                } else {
                  uploadSuccess = false;
                  setUploadErrorState(prevData => {
                    const newData = { ...prevData };
                    if (!newData[type+paxIndex]) {
                      newData[type+paxIndex] = [];
                    }
                    
                    const existingDocIndex = newData[type+paxIndex].findIndex(item => item?.url === uri);
                    if (existingDocIndex >= 0) {
                      newData[type+paxIndex][existingDocIndex] = {
                        docType: 'error',
                        type: type,
                        url: uri,
                        criticalError: value?.messages,
                      };
                    } else {
                      newData[type+paxIndex].push({
                        docType: 'error',
                        type: type,
                        url: uri,
                        criticalError: value?.messages,
                      });
                    }
                    return newData;
                  });
                }
              });
              return uploadSuccess;
            }
            // return true;
          } else {
            setUploadErrorState(prevData => {
              const newData = { ...prevData };
              if (!newData[type+paxIndex]) {
                newData[type+paxIndex] = [];
              }
              
              const existingDocIndex = newData[type+paxIndex].findIndex(item => item?.url === uri);
              if (existingDocIndex >= 0) {
                newData[type+paxIndex][existingDocIndex] = {
                  docType: 'error',
                  type: type,
                  url: uri,
                  criticalError: appliedResponse?.message,
                };
              } else {
                newData[type+paxIndex].push({
                  docType: 'error',
                  type: type,
                  url: uri,
                  criticalError: appliedResponse?.message,
                });
              }
              return newData;
            });
            return false;
          }
          // setSelectedPaxIndexes([]);
        } catch (error) {
          handleIsUploading(false);
          console.error('Error uploading document inside catch:', error);
          return false;
        }
        break;
    };
  };

  const handleUploadingState = (docCount, isMultiPax = false) => {
    setOpenUploadModal(false);
    setUploadDocCount(docCount);
    if(!isMultiPax){
      setTimeout(()=> {
        handleIsUploading(true);
      }, 650)
    }
  };

  const handleUploadingClose = (docCount, uploadStatus) => {
    setTimeout(() => {
      handleIsUploading(() => false);
      if(uploadStatus) {
        if(!nameChanged) {
          showShortToast(`${docCount} document were successfully uploaded and saved`);
        }else {
          nameChanged = false;
        }
        // Show alert if there's alert content after successful upload
        if(alertContentRef.current?.title && alertContentRef.current?.subTitle) {
          setAlertVisible(true);
        } else {
          // console.log('❌ No alert content to show:', alertContentRef.current);
        }
      } else {
        showShortToast(`Error in uploading document`);
      }
    }, 600);
  }

  const handleDeletedUpdateState = (documentType, urlIndex, paxIndex) => {
    setData((prevData) => {
      const updatedPassengers = prevData?.passengers?.map((passenger) => {
        if (passenger?.paxIndex === paxIndex) {
          const updatedDocuments = passenger?.documents || [];
          const existingDocIndex = updatedDocuments.findIndex((doc) => doc?.type === documentType);
          let updatedCatStatusList = passenger?.catStatusList || [];

          if (existingDocIndex >= 0) {
            // Get the document
            const document = updatedDocuments[existingDocIndex];

            // If document has urls array
            if (document.urls && Array.isArray(document.urls)) {
              const urlToRemoveIndex = document.urls.findIndex(
                (urlObj) => urlObj.urlIndex === urlIndex,
              );
              if (urlToRemoveIndex >= 0) {
                if (document.urls.length <= 1) {
                  updatedDocuments.splice(existingDocIndex, 1);
                  // Update category status
                  updatedCatStatusList = passenger?.catStatusList?.map((cat) => {
                    if (cat?.category === type) {
                      return {
                        ...cat,
                        docStatusList: cat?.docStatusList?.map((doc) => {
                          if (doc?.type === documentType) {
                            return {
                              ...doc,
                              uploaded: false,
                              prevError: {
                                criticalError: null,
                                softError: null,
                              }
                            };
                          }
                          return doc;
                        }),
                      };
                    }
                    return cat;
                  });
                } else {
                  document.urls.splice(urlToRemoveIndex, 1);
                }
              }
            }
          }

          return {
            ...passenger,
            documents: updatedDocuments,
            catStatusList: updatedCatStatusList,
          };
        }
        return passenger;
      });

      return {
        ...prevData,
        passengers: updatedPassengers,
      };
    });
  };

  const handleOpencamera = async (bookingId, paxIndex, type) => {
    logPDTEvent({
      eventValue: `click_doctype_${type}`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
    const isAndroid = Platform.OS === 'android';
    if (isAndroid) {
      const havingPermission = await checkIsCameraPermissionGranted();
      if (havingPermission) {
        handlecamera(bookingId, paxIndex, type);
      } else {
        showLongToast('To enable camera permission . Go to: "Settings > Apps & Notifications > Makemytrip > Permissions" and enable camera permission');
        Linking.openSettings();
      }
    } else {
      handlecamera(bookingId, paxIndex, type)
    }
  }

  const handlecamera = async (bookingId, paxIndex, type) => {
    // typeof callTracking === 'function' && callTracking('CAMERA', type);
    let setImages = [];
    let uploadStatus = false;

    let options = {
      storageOptions: {
        mediaType: 'photo',
        cameraType: 'back',
        skipBackup: true,
        path: "images",
      },
    };
    launchCamera(options, async (res) => {
      let image = res.assets[0];
      let request = {};
      request.data = {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      };
      request.documentName = image.fileName;
      request.format = "image/jpeg";
      request.bookingId = bookingId;
      request.docId = 1;
      if(type === 'PASSPORT'){
        request.paxId = paxIndex;
        request.docType = type;
        handleUploadingState(1, false);
      }else {
        if(category?.assignTravellers && allData?.passengers?.length > 1){
          handleUploadingState(1, true);
          setMultiPaxRequest([image]);
          setTimeout(() => {
            setOpenMultiPaxUploadModal(true);
          }, 600);
          return;
        }else {
          setUploadDocCount(1);
          handleIsUploading(true);
          setOpenUploadModal(false);
          request.paxIds = paxIndex;
          request.docType = category?.docStatusList?.length === 1 ? category?.docStatusList[0]?.type : '';
        }
      }
      setImages.push(res);
      await uploadFile(request, null, image.uri, type, 1).then((res) => {  
        if(res) {
          handleIsUploading(false);
          if(!nameChanged) {
            showShortToast(`Document(s) were successfully uploaded and saved`);
          }else {
            nameChanged = false;
          }
          // Show alert if there's alert content after successful upload
          if(alertContentRef.current?.title && alertContentRef.current?.subTitle) {
            setAlertVisible(true);
          }
        } else {
          handleIsUploading(false);
          showShortToast(`Error in uploading document`);
        }
      });  
    })
  }

  const openDocGallery = async (bookingId, paxIndex, type, limit) => {
    logPDTEvent({
      eventValue: `click_doctype_${type}`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })

    pick({
      type: [types.pdf],
      allowMultiSelection: limit === 1 ? false : limit > 1 ? true : false,
      selectionLimit: limit < 1 ? 1 : limit === 1 ? 1 : limit
    }).then(async (results) => {
      let uploadStatus = false;
      let paxIndexes = "";
      if(type === 'PASSPORT'){
        paxIndexes = paxIndex;
        handleUploadingState(results.length, false);
      }else {
        if(category?.assignTravellers && allData?.passengers?.length > 1){
          handleUploadingState(results.length, true);
          setMultiPaxRequest(results);
          setTimeout(() => {
            setOpenMultiPaxUploadModal(true);
          }, 600);
          return;
        }else {
          paxIndexes = paxIndex;
          handleUploadingState(results.length, false);
        }
      }
      for (const res of results) {
        let request = {};
        request.data = {
          uri: res.uri,
          type: res.type,
          name: res.name,
        };
        request.documentName = res.name;
        request.format = res.type;
        request.bookingId = bookingId;
        if(type === 'PASSPORT'){
          request.paxId = paxIndexes;
          request.docType = type;
        }else {
          request.paxIds = paxIndexes;
          request.docType = category?.docStatusList?.length === 1 ? category?.docStatusList[0]?.type : '';
        }
        request.docId = 1;
        uploadStatus = await uploadFile(request, null, res.uri, type, results.length);
      }
      handleUploadingClose(results.length, uploadStatus);
    })
  }

  const openGallery = async (bookingId, paxIndex, type, limit) => {
    logPDTEvent({
      eventValue: `click_doctype_${type}`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
    const imageGalleryParams = {
      mediaType: 'photo',
      selectionLimit: limit < 1 ? 1 : limit === 1 ? 1 : limit
    };
    // trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, 'upload');
    launchImageLibrary(imageGalleryParams, async (res) => {
      if (res.didCancel) {
        console.log('User cancelled image picker');
      } else if (res.errorCode) {
        console.log('Error', res.errorMessage);
      } else if (res.assets && res.assets.length > 0) {
        selectImages(res.assets || [], bookingId, paxIndex, type);
      }
    });
  }

  const selectImages = async (images, bookingid, paxIndex, type) => {
    let setImages = [];
    let paxIndexes = "";
    if(type === 'PASSPORT'){
      paxIndexes = paxIndex;
      handleUploadingState(images.length, false);
    }else {
      if(category?.assignTravellers && allData?.passengers?.length > 1){
        handleUploadingState(images.length, true);
        setMultiPaxRequest(images);
        setTimeout(() => {
          setOpenMultiPaxUploadModal(true);
        }, 650);
        return;
      }else {
        paxIndexes = paxIndex;
        handleUploadingState(images.length, false);
      }
    }
    let uploadStatus = false;
    for (const image of images) {
      const compatNode = cameraRollNode(null, image);
      let request = {};
      request.data = {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      };
      request.documentName = image.fileName;
      request.format = image.type;
      request.bookingId = bookingid;
      if(type === 'PASSPORT'){
        request.paxId = paxIndexes;
        request.docType = type;
      }else {
        request.paxIds = paxIndexes;
        request.docType = category?.docStatusList?.length === 1 ? category?.docStatusList[0]?.type : '';
      }
      request.docId = 1;
      uploadStatus = await uploadFile(request, compatNode, image.uri, type, images.length);
      setImages.push(compatNode);
    }
    handleUploadingClose(images.length, uploadStatus);
  };
  const visaOmnitureTracking = () => {
    logPDTEvent({
      actionType: PDT_EVENT_TYPES.contentSeen,
      eventValue: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD,
    })
}
const visaOmnitureTrackingAddToOthers = () => {
  logPDTEvent({
    actionType: PDT_EVENT_TYPES.contentSeen,
    eventValue: NAME_OF_EVENTS.ADD_TO_OTHERS_POPUP_LOADED,
  })
}
const visaOmnitureTrackingReplaceImage = () => {
  logPDTEvent({
    actionType: PDT_EVENT_TYPES.contentSeen,
    eventValue: NAME_OF_EVENTS.REPLACE_IMAGE_POPUP_LOADED,
  })
}

const isCompleted = category?.docStatusList?.every((doc) => doc?.uploaded) || false;

const getSupportedFormats = () => {
  let newSupportedFormats = [];
  category?.docStatusList?.map((doc) => {
    newSupportedFormats = [...new Set([...newSupportedFormats, ...doc?.supportedType])]
  });
  return newSupportedFormats.join('/').toUpperCase();
}

const getMaxSize = () => {
  return category?.maxSize ? `${category?.maxSize > 1024 ? `${category?.maxSize / 1024} MB` : `${category?.maxSize} KB`}` : '1024 KB'
}

const renderAlertBottomSheet = () => {
  return (
    alertVisible &&
    <BottomSheetWpr
      title={alertContent.title}
      visible={alertVisible}
      isCrossIcon={true}
      onDismiss={handleCloseAlert}
      setVisible={setAlertVisible}
      containerStyles={{
        padding: 0
      }}
      titleContainerStyles={{
        ...paddingStyles.ph20,
        ...paddingStyles.pv16,
        borderBottomColor: colors.grayBorder,
        borderBottomWidth: 1,
      }}
      children={
        <ConfirmationButtomSheet
          descriptionText={alertContent.subTitle}
          primaryButtonText={"OKAY"}
          handleConfirm={handleCloseAlert}
          onClose={handleCloseAlert}
        />
      }
    />
  );
}

const renderPaxNameMismatchBottonSheet = () => {
  return (
    paxNameMismatchVisible &&
    <BottomSheetWpr
      title={"Name Mismatch Found"}
      isCrossIcon={true}
      onDismiss={() => handleCloseMismatchModal('paxNameMismatch')}
      visible={paxNameMismatchVisible}
      setVisible={() => handleCloseMismatchModal('paxNameMismatch')}
      containerStyles={{
        padding: 0
      }}
      titleStyle={{
        ...fontStyles.labelMediumBold
      }}
      titleContainerStyles={{
        ...paddingStyles.ph20,
        ...paddingStyles.pv16,
        borderBottomColor: colors.grayBorder,
        borderBottomWidth: 1,
      }}
      children={
        <PaxNameMismatchBottomSheet 
          descriptionText={`The name on the passport *${paxNameUpdate?.selectedPaxupdatedName?.firstName} ${paxNameUpdate?.selectedPaxupdatedName?.lastName}* doesn't match with name in traveller info`}
          description2Text={`We'll proceed with the name in traveller info (${paxNameUpdate?.selectedPaxOldName?.firstName} ${paxNameUpdate?.selectedPaxOldName?.lastName}) if you don't wish to upload the passport again`}
          primaryButtonText={"UPLOAD PASSPORT AGAIN"}
          secondaryButtonText={"UPDATE NAME AS PER PASSPORT"}
          primaryButtonHandler={handleUploadPassportAgain}
          secondaryButtonHandler={handleUpdateNameasperPassport}
        />
      }
    />
  )
}

const renderPaxNameMatchesBottonSheet = () => {
  return (
    paxNameMatchesVisible &&
    <BottomSheetWpr
      title={"Passport Matches a Different Traveller"}
      isCrossIcon={true}
      onDismiss={() => handleCloseMismatchModal('paxNameMatches_with_existing_pax')}
      visible={paxNameMatchesVisible}
      setVisible={() => handleCloseMismatchModal('paxNameMatches_with_existing_pax')}
      containerStyles={{
        padding: 0
      }}
      titleStyle={{
        ...fontStyles.labelMediumBold
      }}
      titleContainerStyles={{
        ...paddingStyles.ph20,
        ...paddingStyles.pv16,
        borderBottomColor: colors.grayBorder,
        borderBottomWidth: 1,
      }}
      children={
        <PaxNameMismatchBottomSheet 
          descriptionText={`The name on this passport matches *${paxNameUpdate?.matchingExistingPaxName?.firstName} ${paxNameUpdate?.matchingExistingPaxName?.lastName}*, but you are uploading for *${paxNameUpdate?.selectedPaxOldName?.firstName} ${paxNameUpdate?.selectedPaxOldName?.lastName}*`}
          primaryButtonText={`USE PASSPORT FOR ${paxNameUpdate?.matchingExistingPaxName?.firstName}`}
          primaryButtonHandler={handleUploadPassportForExistingPax}
        />
      }
    />
  )
}

  const renderUploadBottomSheet = () => {
    return (
      openUploadModal &&
      <BottomSheetWpr
          title={<View><Text style={[fontStyles.labelMediumBold, paddingStyles.pt10, {color: colors.black}]}>{PAGE_HEADERS.ADD_DOCUMENTS}</Text></View>}
          visible={openUploadModal}
          isCrossIcon={true}
          onDismiss={handleCloseModal}
          callTracking={visaOmnitureTracking}
          setVisible={setOpenUploadModal}
          titleContainerStyles={{
            ...paddingStyles.ph20,
            ...paddingStyles.pv16,
            borderBottomColor: colors.grayBorder,
            borderBottomWidth: 1,
          }}
          containerStyles={{
            backgroundColor: colors.lightGray2,
            paddingHorizontal: 0,
            paddingVertical: 0,
          }}
          children={
              <AddDocumentsImage
                openDocGallery={() => openDocGallery(bookingId, paxIndex, type, getDocUploadLimit())}
                openGallery={() => openGallery(bookingId, paxIndex, type, getDocUploadLimit())}
                openCamera={() => handleOpencamera(bookingId, paxIndex, type)}
                tagText={category?.categoryDescriptionBefore}
                supportedFormats={getSupportedFormats()}
                maxSize={getMaxSize()}
              ></AddDocumentsImage>
          }
      />
    )
  }

  const rendeGuidelineBottomSheet = () => {
    return (
      showGuidelines &&
      <BottomSheetWpr
          title={<View style={styles.guideLinesHeader}><Text style={fontStyles.labelMediumBold}>Guidelines for {category?.name} upload</Text></View>}
          visible={showGuidelines}
          isCrossIcon={true}
          onDismiss={handleCloseGuidelinesViaCross}
          bottomsheetName={PDT_SUB_PAGE_NAMES.DOC_GUIDELINES_POPUP}
          setVisible={setShowGuidelines}
          titleContainerStyles={{
              ...paddingStyles.pl16,
              ...paddingStyles.pr40,
              ...paddingStyles.pv8,
              backgroundColor: colors.white,
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
              borderBottomColor: colors.grayBorder,
              borderBottomWidth: 1,
          }}
          containerStyles={{
              backgroundColor: colors.lightGray2,
              paddingHorizontal: 0,
              paddingVertical: 0,
          }}
          children={
              <GuidelinesBottomSheet 
                  docStatusList={category?.docStatusList}
                  onClose={handleCloseGuidelinesViaDone}
              />
          }
      />
    )
  }
  const renderMultiPaxUploadBottomSheet = () => {
    if (!openMultiPaxUploadModal) return null;
    
    return (
      <BottomSheetWpr
        title={<View style={styles.multiPaxUploadTitle}><Text style={fontStyles.labelMediumBold}>{category?.assignTravellersCard?.assignDocumentHeader}</Text></View>}
        visible={openMultiPaxUploadModal}
        isCrossIcon={true}
        callTracking={visaOmnitureTrackingAddToOthers}
        onDismiss={handleCloseCrossMultiPaxUploadModal}
        bottomsheetName={PDT_SUB_PAGE_NAMES.MULTI_PAX_UPLOAD_POPUP}
        setVisible={setOpenMultiPaxUploadModal}
        titleContainerStyles={{
          ...paddingStyles.ph8,
          ...paddingStyles.pt4,
          // ...paddingStyles.pv4,
          backgroundColor: colors.white,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderBottomColor: colors.grayBorder,
          borderBottomWidth: 1,
        }}
        containerStyles={{
          backgroundColor: colors.white,
          paddingHorizontal: 0,
          paddingVertical: 0,
        }}
        children={
          <MultiPaxUploadBottomSheet
            descriptionText={category?.assignTravellersCard?.assignDocumentSubHeader}
            primaryButtonText={'APPLY'}
            secondaryButtonText={'SKIP'}
            handleConfirm={handleConfirmMultiPaxUpload}
            onClose={handleCloseMultiPaxUploadModal}
            setSelectedPaxIndexes={setSelectedPaxIndexes}
            selectedPaxIndexes={selectedPaxIndexes}
            paxIndex={paxIndex}
            passengers={allData?.passengers}
            assignTravellersCard={category?.assignTravellersCard}
            uploadDocCount={uploadDocCount}
            category={category}
          />
        }
      />
    );
  }

  const renderDeleteConfirmationBottomSheet = () => {
    return (
      deleteModalVisible &&
      <BottomSheetWpr
          title={"Confirm Removal"}
          visible={deleteModalVisible}
          isCrossIcon={true}
          onDismiss={handleCloseDeleteModal}
          bottomsheetName={PDT_SUB_PAGE_NAMES.CONFIRM_REMOVAL_POPUP}
          setVisible={setDeleteModalVisible}
          containerStyles={{
            padding: 0
          }}
          titleContainerStyles={{
            ...paddingStyles.ph20,
            ...paddingStyles.pv16,
            borderBottomColor: colors.grayBorder,
            borderBottomWidth: 1,
          }}
          children={
              <ConfirmationButtomSheet
                descriptionText={"Are you sure you want to remove this document?"}
                primaryButtonText={"REMOVE"}
                secondaryButtonText={"CANCEL"}
                handleConfirm={handleConfirmDeleteDocument} 
                onClose={handleCloseDeleteModal}
              />
          }
      />
    );
  }

  const renderNotUploaded = () => {
    return (
      <CategoryNotUploaded
        category={category}
        rendeGuidelineBottomSheet={rendeGuidelineBottomSheet}
        renderUploadBottomSheet={renderUploadBottomSheet}
        handleOpenGuidelines={handleOpenGuidelines}
        handleOpenModal={handleOpenModal}
        renderMultiPaxUploadBottomSheet={renderMultiPaxUploadBottomSheet}
        disableUpload={disableUpload}
        fromMIMA={fromMIMA}
      />
    )
  };

  const renderUploaded = () => {
    return (
      <CategoryUploaded
        category={category}
        rendeGuidelineBottomSheet={rendeGuidelineBottomSheet}
        renderUploadBottomSheet={renderUploadBottomSheet}
        handleOpenGuidelines={handleOpenGuidelines}
        handleOpenModal={handleOpenModal}
        documents={documents}
        type={type}
        paxIndex={paxIndex}
        isCompleted={isCompleted}
        hasNoError={uploadErrorState[type+paxIndex] && uploadErrorState[type+paxIndex].length > 0 ? false : true}
        uploadErrorState={uploadErrorState[type+paxIndex] || []}
        setUploadErrorState={setUploadErrorState}
        handleOpenDeleteModal={handleOpenDeleteModal}
        renderDeleteConfirmationBottomSheet={renderDeleteConfirmationBottomSheet}
        setActiveDocument={setActiveDocument}
        renderMultiPaxUploadBottomSheet={renderMultiPaxUploadBottomSheet}
        disableUpload={disableUpload}
      />
    );
  };

  return (
    <>
      {
        (uploadErrorState[type+paxIndex] && uploadErrorState[type+paxIndex].length > 0) || uploaded ? renderUploaded() : renderNotUploaded()
      }
      {renderAlertBottomSheet()}
      {renderPaxNameMismatchBottonSheet()}
      {renderPaxNameMatchesBottonSheet()}
      {
        replaceModalVisible &&
        <BottomSheetWpr
          title={"Replace Image"}
          visible={replaceModalVisible}
          isCrossIcon={true}
          callTracking={visaOmnitureTrackingReplaceImage}
          onDismiss={handleCloseReplaceModal}
          bottomsheetName={PDT_SUB_PAGE_NAMES.IDENTIFY_DOCUMENTS}
          setVisible={setReplaceModalVisible}
          containerStyles={{
            padding: 0
          }}
          titleContainerStyles={{
            ...paddingStyles.ph20,
            ...paddingStyles.pv16,
            borderBottomColor: colors.grayBorder,
            borderBottomWidth: 1,
          }}
          children={
            <ConfirmationButtomSheet
              descriptionText={"We noticed this image has already been uploaded. Are you sure you want to replace this file?"}
              primaryButtonText={"REPLACE"}
              secondaryButtonText={"DISCARD"}
              handleConfirm={() => handleReplaceConfirmation()}
              onClose={() => handleDiscardConfirmation()}
            />
          }
        />
      }
    </>
  );
}

const styles = StyleSheet.create({
    guideLinesHeader: {        
      ...paddingStyles.pv16,
    },
    multiPaxUploadTitle: {
      ...paddingStyles.pa16,
    }
})
