import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Image, Platform, Modal } from 'react-native'
import React, {useState, useRef, useEffect} from 'react';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing';
import { colors } from '../../../../Styles/colors';
import { fontStyles } from '../../../../Styles/fonts';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';

import HeaderWrapper from '../../../../Components/Common/HeaderWpr';
import ButtonWpr from '../../../../Components/Common/ButtonWpr';
import CheckBox from "@Frontend_Ui_Lib_App/CheckBox";
import CategoryCard from './Components/CategoryCard';
import UploadingModal from './Components/UploadingModal';
const backIcon = require('@mmt/legacy-assets/src/back.webp');


import { getImagePath } from '@mmt/visa/src/VisaRevamp/Utils/VisaUtils';
import { createVisaBooking, saveDocumentsConsent } from '@mmt/visa/src/VisaRevamp/Utils/NetworkUtils';
import LinearGradient from 'react-native-linear-gradient';
import AllPassengerBottomSheet from './Components/AllPassengerBottomSheet';
import { VIEW_STATES } from '@mmt/visa/src/VisaRevamp/constants';
import LineLoaderComp from '../../../../Components/Common/Loader';
import { visaTrackClickEvent } from '../../../../Tracking/utils';
import { logPDTEvent, visaPdtEventsInitilizer } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { TYPE_OF_EVENTS } from '../../../../Tracking/constants';
import { PDT_EVENT_TYPES } from '../../../../Tracking/pdt/constants';
import VisaDataHolder from '@mmt/visa/src/VisaRevamp/Utils/VisaDataHolder';
import { BUTTON_CTA_MAP } from '@mmt/visa/src/VisaRevamp/textStrings';

const UploadDocumentsRevamp = ({ bookingId, paxIndex, index, setTriggerEffect,setPaxFormStatus, visaData, openReview, fromMIMA}) => {
    const [data, setData] = useState(visaData); 
    const [selectedPassenger, setSelectedPassenger] = useState(paxIndex);
    const [allCtaPassenger, setAllCtaPassenger] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadDocCount, setUploadDocCount] = useState(0);
    const [uploadErrorState, setUploadErrorState] = useState({});
    const scrollViewRef = useRef(null);
    const vscrollViewRef = useRef(null);
    const [toggleSaveConsent, setToggleSaveConsent] = useState(false);
    const [viewState, setViewState] = useState(VIEW_STATES.LOADING);

    const PAX_LINEAR_GRADIENT_COLORS = [
        ['#F5515F', '#9F0469'],
        ['#FF7F3F', '#FF3E5E'],
        ['#43E1A8', '#219393'],
        ['#3023AE', '#C86DD7'],
    ];

    const paxDetails = data?.passengers?.find((passenger) => passenger?.paxIndex === selectedPassenger);

    const handleBack = () => {
      logPDTEvent({
        eventValue: `click_back_docupload`,
        actionType: PDT_EVENT_TYPES.buttonClicked,
      })
        setTriggerEffect((prev) => !prev);
        VisaNavigation.pop();
    };

    const handleAllCtaPassenger = () => {
      logPDTEvent({
        eventValue: `click_all_pax`,
        actionType: PDT_EVENT_TYPES.buttonClicked,
      })
      setAllCtaPassenger(!allCtaPassenger);
    }

    const filteredPassengersData = data?.passengers?.map((passenger) => ({
      ...passenger,
      catStatusList: passenger?.catStatusList?.filter(category => 
        !category?.docStatusList?.every(doc => 
          doc?.disableUpload === true && doc?.qcStatus === ""
        )
      )
    }));

    // const allDocumentsUploadedNFilled = filteredPassengersData?.every((passenger) => 
    //   passenger?.catStatusList?.every((category) => 
    //     category?.docStatusList?.every((doc) => doc?.uploaded)
    //   ) && passenger?.visaFormData?.filled
    // );

    // Find passengers who are NOT completed yet (either documents not uploaded OR form not filled)
    const remainingIncompletePax = filteredPassengersData?.filter((passenger) => {
      const allDocumentsUploaded = passenger?.catStatusList?.every((category) => 
        category?.docStatusList?.every((doc) => doc?.uploaded)
      );
      const formFilled = passenger?.visaFormData?.filled;
      
      // A passenger is incomplete if documents are not uploaded OR form is not filled
      return !allDocumentsUploaded || !formFilled;
    });

    const sortedPassengers = filteredPassengersData?.sort((a, b) => a.paxIndex - b.paxIndex);
    const isLastPassenger = remainingIncompletePax?.length <= 1 && remainingIncompletePax[0]?.paxIndex === selectedPassenger;
    const isOnlyOnePassenger = sortedPassengers?.length === 1;
    
    // Show CONTINUE if it's the only passenger OR last passenger, otherwise show FILL PENDING DETAILS
    const buttonText = isOnlyOnePassenger || isLastPassenger || remainingIncompletePax?.length === 0 ? BUTTON_CTA_MAP.CONTINUE : BUTTON_CTA_MAP.SAVE_AND_CONTINUE;

    // Check if selected passenger has completed all documents and filled visa form
    const indeOfSelectedPax = filteredPassengersData?.findIndex((passenger) => passenger?.paxIndex === selectedPassenger);
    const isSelectedPaxCompleted = filteredPassengersData[indeOfSelectedPax]?.catStatusList?.every((category) => 
      category?.docStatusList?.every((doc) => doc?.uploaded)
    ) && filteredPassengersData[indeOfSelectedPax]?.visaFormData?.filled;

    const allPassengers = data?.passengers?.sort((a,b) => a.paxIndex - b.paxIndex).map((passenger) => {
      return {
          paxIndex: passenger?.paxIndex,
          name: passenger?.name,
          age: passenger?.age,
          gender: passenger?.gender,
          dateOfBirth: passenger?.dateOfBirth,
      }
    });

    const handlePassengerSelect = (pax, index) => {
      logPDTEvent({
        eventValue: `click_pax_${pax}`,
        actionType: PDT_EVENT_TYPES.buttonClicked,
      })
      setSelectedPassenger(pax);
      scrollViewRef.current?.scrollTo({
          x: index * 120,
          animated: true
      });
      vscrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    };

    const handleNextPassenger = () => {
        const sortedPassengers = data?.passengers?.sort((a, b) => a.paxIndex - b.paxIndex);
        
        const currentIndex = sortedPassengers?.findIndex(p => p.paxIndex === selectedPassenger);
        
        if (currentIndex !== -1 && currentIndex < sortedPassengers.length - 1) {
            const nextPassenger = sortedPassengers[currentIndex + 1];
            logPDTEvent({
                eventValue: `click_next_passenger_${nextPassenger.paxIndex}`,
                actionType: PDT_EVENT_TYPES.buttonClicked,
            });
            handlePassengerSelect(nextPassenger.paxIndex, currentIndex + 1);
            
            // Scroll to top
            vscrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
        }
    };

    const handlePreviousPassenger = () => {
        const sortedPassengers = data?.passengers?.sort((a, b) => a.paxIndex - b.paxIndex);
        const currentIndex = sortedPassengers?.findIndex(p => p.paxIndex === selectedPassenger);
        
        if (currentIndex !== -1 && currentIndex > 0) {
            const previousPassenger = sortedPassengers[currentIndex - 1];
            logPDTEvent({
                eventValue: `click_previous_passenger_${previousPassenger.paxIndex}`,
                actionType: PDT_EVENT_TYPES.buttonClicked,
            });
            handlePassengerSelect(previousPassenger.paxIndex, currentIndex - 1);

            // Scroll to top
            vscrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
        }
    };

    const handleButtonPress = (actionType = '') => {
      // Log the specific button click event
      logPDTEvent({
        eventValue: `click_${actionType}_button`,
        actionType: PDT_EVENT_TYPES.buttonClicked,
      });
      
      // Handle different button actions based on button text
      if (buttonText === BUTTON_CTA_MAP.CONTINUE) {
        // CONTINUE button: Navigate to review page
        openReview('', 'categoryPage');
      } else if (buttonText === BUTTON_CTA_MAP.SAVE_AND_CONTINUE) {
        // FILL PENDING DETAILS button: Navigate to next passenger
        const nextPassenger = remainingIncompletePax?.sort((a, b) => a.paxIndex - b.paxIndex)[0];
        const indexOfNextPassenger = data?.passengers?.findIndex((passenger) => passenger?.paxIndex === nextPassenger?.paxIndex);
        handlePassengerSelect(nextPassenger.paxIndex, indexOfNextPassenger);
      }
    };
    
    // Specific handler functions for each button
    const handleSkipPress = () => {
      handleButtonPress('skip');
    };
    
    const handleContinuePress = () => {
      handleButtonPress('CONTINUE');
    };

    const showFormScreen = () => {
      // Use the selected passenger data
      const currentPaxDetails = paxDetails;
      // Check if there are multiple passengers needing forms
  const passengersNeedingForms = filteredPassengersData?.filter((passenger) => 
    passenger?.visaFormData?.filled === false
  ).sort((a, b) => a.paxIndex - b.paxIndex);
  
  const isMultiPassengerFormFlow = passengersNeedingForms.length > 1;
  const currentFormIndex = passengersNeedingForms.findIndex(p => p.paxIndex === selectedPassenger);
      
      visaTrackClickEvent({
        eventName: `click_${currentPaxDetails?.name}_visa_form`,
        eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      });
      
      logPDTEvent({
        eventValue: `click_add_details_${currentPaxDetails?.name}_visa_form`,
        actionType: PDT_EVENT_TYPES.buttonClicked,
      });
      
      VisaNavigation.push(VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED_REVAMP, {
        data: currentPaxDetails,
        subTitle: data?.country ? `${data?.country} | ${data?.journeyDate} | ${currentPaxDetails?.name}` : '',
        bookingId: bookingId,
        paxIndex: currentPaxDetails?.paxIndex,
        setPaxFormStatus: setPaxFormStatus,
        setTriggerEffect: setTriggerEffect,
        setToggleSaveConsent: setToggleSaveConsent,
        // Add multi-passenger flow props if applicable
    // ...(isMultiPassengerFormFlow && {
    //   allPassengersNeedingForms: passengersNeedingForms,
    //   currentFormIndex: currentFormIndex,
    //   totalFormsNeeded: passengersNeedingForms.length,
    //   visaAddDocumentData: data,
    //   openReview: openReview
    //     })
      });
    };

    const updateCheckbox = async (bool) => {
      setViewState(VIEW_STATES.LOADING);
      let request = {};
      let saveDetails = !bool;
      request = {
          "bookingId": bookingId,
          "paxIndex": selectedPassenger,
          "saveDocuments": saveDetails
      };
      const response = await saveDocumentsConsent({ request });
      if(response.status === 1) {
        setData((prevData) => {
          const updatedPassengers = prevData?.passengers?.map((passenger) => {
            if (passenger?.paxIndex === selectedPassenger) {
              return {
                ...passenger,
                saveConsent: {
                  ...passenger?.saveConsent,
                  saveDocumentConsent: saveDetails,
                },
              };
            }
            return passenger;
          });
          return {
            ...prevData,
            passengers: updatedPassengers,
          };
        });
        setViewState(VIEW_STATES.SUCCESS);
        const eventName = `click_save_passport_${paxDetails?.name}`;
        visaTrackClickEvent({
          eventName,
          eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        });
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: eventName,
        });
      }
    };

    const fetchData = async () => {
      const apiUrl = '';
      setViewState(VIEW_STATES.LOADING);
      const response = await createVisaBooking({ apiUrl, bookingId });
      if (!response) {
        setViewState(VIEW_STATES.ERROR);
        return;
      } else if (response) {
        setData(response || {});
      }
      setTimeout(() => {
        setViewState(VIEW_STATES.SUCCESS);
      }, 600);
    }

    useEffect(() => {
      VisaDataHolder.getInstance().setCurrentPage(VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP);
      fetchData();
    }, [toggleSaveConsent]);

    useEffect(() => {
      const timeout = setTimeout(() => {
        scrollViewRef.current?.scrollTo({ x: index * 120, animated: true });
      }, 100);
      
      return () => clearTimeout(timeout);
    }, []);

    useEffect(() => {
        let newData = data;
        newData?.passengers?.map((passenger) => {
          if(passenger?.paxIndex === selectedPassenger) {
            const docUploaded = passenger?.catStatusList?.filter((category) => {
              return category?.docStatusList?.every((doc) => doc?.uploaded)
            })
            if(docUploaded?.length === passenger?.catStatusList?.length){
              passenger.status = 'DOCUMENTS_UPLOADED';
            } else {
              if((docUploaded?.length > 0)) {
                passenger.status = 'DOCUMENTS_PENDING';
              }
            }
          }
        })
        setData(newData);
    }, [uploadDocCount, data]);

    return (
      <View style={styles.mainContainer}>
        <HeaderWrapper
          titleText={'Document Upload'}
          descText={
            <Text style={styles.stepText}>
              {data?.country} • {data?.journeyDate}
            </Text>
          }
          wrapperStyle={getPlatformElevation(0)}
          clickHandler={handleBack}
        />
        <View style={styles.passengerScrollViewContainer}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.passengerScrollViewStyle}
          >
            {filteredPassengersData?.sort((a,b) => a.paxIndex - b.paxIndex).map((passenger, index) => {
              const qcRejectedStatus = passenger?.catStatusList?.filter(category => {
                return category?.docStatusList?.some(doc => {
                  return doc?.qcStatus !== "" && doc?.qcStatus === "REJECTED"
                })
              });
              
              const qcApprovedStatus = passenger?.catStatusList?.filter(category => {
                return category?.docStatusList?.some(doc => {
                  return doc?.qcStatus !== "" && doc?.qcStatus === "APPROVED"
                })
              });

              const showQcRejectedStatus = qcRejectedStatus?.length > 0 ? true : false;
              const showQcApprovedStatus = qcRejectedStatus?.length > 0 ? false : qcApprovedStatus?.length === passenger?.catStatusList?.length ? true : false;
              
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => handlePassengerSelect(passenger?.paxIndex, index)}
                  style={[
                    styles.passengerContainer,
                    selectedPassenger === passenger?.paxIndex && styles.selectedPassengerContainer,
                  ]}
                >
                  <View style={styles.passengerAvatarOuterWrapper}>
                    <LinearGradient
                      colors={
                        PAX_LINEAR_GRADIENT_COLORS[index % PAX_LINEAR_GRADIENT_COLORS.length || 0]
                      }
                      start={{ x: 0.0, y: 0.0 }}
                      end={{ x: 1.0, y: 0.0 }}
                      style={styles.passengerAvatarWrapper}
                    >
                      <Text style={styles.passengerAvatarText}>
                        {passenger?.name?.split(' ')[0]?.[0]?.toUpperCase() || ''}
                        {passenger?.name?.split(' ')[1]?.[0]?.toUpperCase() || ''}
                      </Text>
                    </LinearGradient>
                    {
                      (showQcApprovedStatus || showQcRejectedStatus) ? null : (
                        <View style={styles.statusIconWrapper}>
                          {passenger?.status === 'DOCUMENTS_PENDING' ? (
                            <Image
                              source={getImagePath('warningImage')}
                              style={styles.completedIconStyle}
                            />
                          ) : passenger?.status === 'DOCUMENTS_UPLOADED' && passenger?.visaFormData?.filled ? (
                            <Image
                              source={getImagePath('submitImage')}
                              style={styles.completedIconStyle}
                            />
                          ) : passenger?.status === 'DOCUMENTS_UPLOADED' && !passenger?.visaFormData?.filled ? (
                            <Image
                              source={getImagePath('warningImage')}
                              style={styles.completedIconStyle}
                            />
                          ): null}
                        </View>
                      )
                    }
                  </View>
                  <View style={styles.passengerTextWrapper}>
                    <Text
                      numberOfLines={1}
                      ellipsizeMode="tail"
                      style={[
                        styles.passengerText,
                        selectedPassenger === passenger?.paxIndex && styles.selectedPassengerText,
                      ]}
                    >
                      {passenger?.name}
                    </Text>
                    {
                      showQcApprovedStatus && (
                        <Image source={getImagePath('approvedIcon')} style={styles.qcAcceptedStatusIcon} />
                      )
                    }
                    {
                      showQcRejectedStatus && (
                        <Image source={getImagePath('rejectedIcon')} style={styles.qcRejectedStatusIcon} />
                      )
                    }
                  </View>
                </TouchableOpacity>
              )
            })}
          </ScrollView>
          {data?.passengers?.length > (data?.allCTACount || 5) && (
            <View style={styles.allCtaPassenger}>
              <TouchableOpacity onPress={() => handleAllCtaPassenger()}>
                <Text style={styles.allCtaPassengerText}>All</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
        <ScrollView
            ref={vscrollViewRef}
            vertical
            showsVerticalScrollIndicator={false}
        >
            <View style={styles.categoriesScrollViewStyle}>
                {paxDetails?.catStatusList?.sort((a,b) => a.categoryOrder - b.categoryOrder).map((category, index) => {
                        const docUploaded = category.docStatusList.filter((doc) => {
                            return doc.uploaded
                        })
                        const disableUpload = category?.docStatusList?.every((doc) => {
                            return doc?.disableUpload && doc?.qcStatus === ""
                        })
                        return docUploaded.length > 0 ? (
                            <CategoryCard
                                key={index}
                                uploaded={true}
                                type={category?.category?.replace(/ /g, "_")}
                                bookingId={bookingId}
                                paxIndex={paxDetails?.paxIndex}
                                category={category}
                                allData={data}
                                setData={setData}
                                documents={paxDetails?.documents}
                                setIsUploading={setIsUploading}
                                setUploadDocCount={setUploadDocCount}
                                uploadDocCount={uploadDocCount}
                                setViewState={setViewState}
                                uploadErrorState={uploadErrorState}
                                setUploadErrorState={setUploadErrorState}
                                fetchData={fetchData}
                                categoryType={category?.docStatusList[0]?.type}    
                                disableUpload={disableUpload}
                                fromMIMA={fromMIMA}
                            />
                        ) : (
                            <CategoryCard
                                key={index}
                                uploaded={false}
                                type={category?.category?.replace(/ /g, "_")}
                                bookingId={bookingId}
                                paxIndex={paxDetails?.paxIndex}
                                category={category}
                                allData={data}
                                setData={setData}
                                documents={paxDetails?.documents}
                                setIsUploading={setIsUploading}
                                setUploadDocCount={setUploadDocCount}
                                uploadDocCount={uploadDocCount}
                                uploadErrorState={uploadErrorState}
                                setUploadErrorState={setUploadErrorState}
                                fetchData={fetchData}
                                categoryType={category?.docStatusList[0]?.type}
                                disableUpload={disableUpload}
                                fromMIMA={fromMIMA}
                            />
                        )
                    })
                }
                {paxDetails?.visaFormData && (
                  <View style={styles.categoryItemOuterWrapper}>
                    {
                      paxDetails?.status === 'PASSENGER_INITIATED' && paxDetails?.visaFormData?.filled === false ? null :
                      paxDetails?.visaFormData?.filled === false ?  
                      (<View style={styles.completeStatusWrapper}>
                        <Image
                          source={
                            getImagePath('warningImage')
                          }
                          style={styles.completedIconStyle}
                        />
                        <Text style={styles.pendingStatusText}>Pending</Text>
                      </View>)
                      :
                      paxDetails?.visaFormData?.filled === true ?
                      (<View style={styles.completeStatusWrapper}>
                        <Image
                          source={
                            getImagePath('submitImage')
                          }
                          style={styles.completedIconStyle}
                        />
                        <Text style={styles.completeStatusText}>Completed</Text>
                      </View>)
                      :null
                    }
                  
                    <Text style={styles.categoryTitle}>{paxDetails?.visaFormData?.title}</Text>
                    <Text style={styles.categoryDescription}>{paxDetails?.visaFormData?.subTitle}</Text>

                    <TouchableOpacity
                      style={[styles.addButton, { alignSelf: 'flex-end' }]}
                      onPress={showFormScreen}
                    >
                      <Text style={styles.addButtonText}>{paxDetails?.visaFormData?.filled ? 'EDIT DETAILS' : '+ ADD DETAILS'}</Text>
                    </TouchableOpacity>
                  </View>
                )}
                
                {/* Navigation buttons for next and previous passenger */}
                {data?.passengers?.length > 1 && (
                    <View style={styles.navigationButtonsContainer}>
                        <TouchableOpacity
                            style={[
                                styles.navigationButton,
                                selectedPassenger === Math.min(...data?.passengers?.map(p => p.paxIndex)) && styles.disabledNavigationButton
                            ]}
                            onPress={handlePreviousPassenger}
                            disabled={selectedPassenger === Math.min(...data?.passengers?.map(p => p.paxIndex))}
                        >
                            <Image
                                source={getImagePath('rightArrow')}
                                style={[
                                    styles.navigationIcon,
                                    styles.navigationIconRotate,
                                    selectedPassenger === Math.min(...data?.passengers?.map(p => p.paxIndex)) && styles.disabledNavigationIcon
                                ]}
                            />
                            <Text style={[
                                styles.navigationButtonText,
                                selectedPassenger === Math.min(...data?.passengers?.map(p => p.paxIndex)) && styles.disabledNavigationButtonText
                            ]}>
                                Previous Traveller
                            </Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity
                            style={[
                                styles.navigationButton,
                                selectedPassenger === Math.max(...data?.passengers?.map(p => p.paxIndex)) && styles.disabledNavigationButton
                            ]}
                            onPress={handleNextPassenger}
                            disabled={selectedPassenger === Math.max(...data?.passengers?.map(p => p.paxIndex))}
                        >
                            <Text style={[
                                styles.navigationButtonText,
                                selectedPassenger === Math.max(...data?.passengers?.map(p => p.paxIndex)) && styles.disabledNavigationButtonText
                            ]}>
                                Next Traveller
                            </Text>
                            <Image
                                source={getImagePath('rightArrow')}
                                style={[
                                    styles.navigationIcon,
                                    selectedPassenger === Math.max(...data?.passengers?.map(p => p.paxIndex)) && styles.disabledNavigationIcon
                                ]}
                            />
                        </TouchableOpacity>
                    </View>
                )}

                {paxDetails?.postDocumentDetails && !fromMIMA && (
                    <View>
                        <View>
                        <Text style={styles.postBookingHeader}>{paxDetails?.postDocumentDetails?.header}</Text>
                        </View>
                        <View style={styles.postBookingContainer}>
                        <Text style={styles.postBookingSubHeader}>
                            {paxDetails?.postDocumentDetails?.subHeader}
                        </Text>
                        <View style={styles.postDocListContainer}>
                            {paxDetails?.postDocumentDetails?.postDocList?.map((item, index) => (
                            <View key={index} style={styles.postDocListItem}>
                                <View style={styles.bulletPoint} />
                                <Text style={styles.postDocListText}>{item}</Text>
                            </View>
                            ))}
                        </View>
                        <Text style={styles.agentPostPersuasion}>
                            {paxDetails?.postDocumentDetails?.agentPostPersuasion}
                        </Text>
                        </View>
                    </View>
                )}
            </View>
        </ScrollView>
        <View style={styles.uploadDocumentStickyFooter}>
          {
              paxDetails?.saveConsent?.showDocumentConsentCard &&
              <View style={styles.saveForFutureContainer}>
                  <CheckBox
                      isChecked={paxDetails?.saveConsent?.saveDocumentConsent}
                      activeColor={colors.primaryBlue}
                      borderColor={colors.primaryBlue}
                      onPress={() => updateCheckbox(paxDetails?.saveConsent?.saveDocumentConsent)}
                      boxRadius={4}
                      size={18}
                      tickSize={10}
                  />
                  <View>
                      <Text style={styles.saveForFutureText}>{paxDetails?.saveConsent?.saveDocumentConsentHeader}</Text>
                  </View>
              </View>
          }
          <View style={styles.buttonContainer}>
            {/* {
                !allDocumentsUploadedNFilled && (
                <View style={styles.footerButton}>
                    <ButtonWpr
                        buttonText={'SKIP'}
                        buttonType="outline"
                        onButtonPress={handleSkipPress}
                    />
                </View>
                )
            } */}
            <ButtonWpr
                disabled={!isSelectedPaxCompleted}
                buttonText={buttonText}
                endIcon={buttonText !== BUTTON_CTA_MAP.CONTINUE ? <Image source={backIcon} style={[styles.buttonArrowIcon, isSelectedPaxCompleted ? {tintColor: colors.white} : {tintColor: colors.black, opacity: 0.3}]} /> : null}
                onButtonPress={handleContinuePress}
            />
          </View>
          </View>
          {
            allCtaPassenger && (
                <AllPassengerBottomSheet
                    passengers={allPassengers}
                    handlePassengerSelect={handlePassengerSelect}
                    visible={allCtaPassenger}
                    setVisible={setAllCtaPassenger}
                    handleClose={() => setAllCtaPassenger(false)}
                    dynamicHeight={(data?.allCTACount || 5) * 100}
                />
            )
          }
          {
            isUploading && (
                <UploadingModal
                    docCount={uploadDocCount}
                    visible={isUploading}
                />
            )
          }
          {
            viewState === VIEW_STATES.LOADING && (
              <Modal
                transparent={true}
                animationType="fade"
                visible={viewState === VIEW_STATES.LOADING ? true : false}
              >
                <View style={styles.darkenedBackground}>
                  <LineLoaderComp />
                </View>
              </Modal>
            )
          }
      </View>
    );
}

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: colors.lightGray6,
        justifyContent: 'space-between',
    },
    categoryItemOuterWrapper: {
        backgroundColor: colors.white,
        borderRadius: 8,
        ...paddingStyles.pt16,
        ...paddingStyles.pb12,
        ...paddingStyles.ph12,
        position: 'relative',
        flexDirection: 'column',
    },
    categoryTitle: {
        ...fontStyles.labelMediumBold,
        color: colors.black,
        ...marginStyles.mb8,
    },
    categoryDescription: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        width: '90%',
    },
    addButton: {
        borderWidth: 1,
        borderColor: colors.primaryBlue,
        borderRadius: 8,
        ...paddingStyles.pv8,
        ...paddingStyles.ph12,
        ...marginStyles.mt8,
    },
    addButtonText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    stepText: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
    },
    passengerScrollViewContainer: {
        flexDirection: 'row',
        backgroundColor: colors.white,
        position: 'relative',
        // iOS shadow
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 7 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
        // Android shadow
        elevation: 3,
        zIndex: 1,
    },
    passengerScrollViewStyle: {
        backgroundColor: colors.white,
        width: '100%',
        flexGrow: 0,
        flexDirection: 'row',
    },
    passengerContainer: {
        ...paddingStyles.pv8,
        ...paddingStyles.ph8,
        borderBottomWidth: 4,
        minWidth: 120,
        backgroundColor: colors.white,
        borderBottomColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: 8,
    },
    selectedPassengerContainer: {
        borderBottomColor: colors.primaryBlue,
    },
    passengerAvatarOuterWrapper: {
        position: 'relative',
    },
    passengerAvatarWrapper: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: colors.black,
        justifyContent: 'center',
        alignItems: 'center',
        flexShrink: 0,
    },
    passengerAvatarText: {
        ...fontStyles.labelSmallBlack,
        color: colors.white,
        textAlign: 'center',
    },
    passengerTextWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 2,
    },
    passengerText: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        width: 92,
        textAlign: 'center',
    },
    selectedPassengerText: {
        ...fontStyles.labelSmallBlack,
        color: colors.black,
    },
    statusIconWrapper: {
        position: 'absolute',
        right: -5,
        top: 20,
        zIndex: 1,
    },
    allCtaPassenger: {
        ...paddingStyles.pa12,
        borderLeftWidth: 1,
        borderLeftColor: colors.grayBorder,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.white,
    },
    allCtaPassengerText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
        textAlign: 'center',
    },
    categoriesScrollViewStyle: {
        flexGrow: 1,
        ...paddingStyles.ph16,
        ...paddingStyles.pt20,
        paddingBottom: 140,
        flexDirection: 'column',
        gap: 16,
    },
    uploadDocumentStickyFooter: {
        backgroundColor: colors.white,
        width: '100%',
        position: 'absolute',
        bottom: 0,
        zIndex: 0,
        ...Platform.select({
            ios: paddingStyles.pt16,
            android: paddingStyles.pv16,
            default: paddingStyles.pt16, // fallback
        }),
        ...paddingStyles.ph16,
        // iOS shadow (upward direction)
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 0.05,
        shadowRadius: 3,
        // Android shadow
        elevation: 4,
    },
    buttonContainer: {
        flexDirection: 'row',
        gap: 16,
    },
    skipButton: {
        flex: 1,
        height: 48,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.primaryBlue,
        borderRadius: 8,
    },
    continueButton: {
        flex: 1,
        height: 48,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.gray,
        borderRadius: 8,
    },
    skipButtonText: {
        ...fontStyles.labelLargeMedium,
        color: colors.primaryBlue,
    },
    continueButtonText: {
        ...fontStyles.labelLargeMedium,
        color: colors.white,
    },
    // footerButton: {
    //     flex: 1,
    // },
    saveForFutureContainer: {
        backgroundColor: colors.lightBlueBg,
        ...paddingStyles.pa8,
        borderRadius: 8,
        ...marginStyles.mb14,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    saveForFutureText: {
        ...fontStyles.labelBaseRegular,
        color: colors.black,
    },
    postBookingContainer: {
        width: '100%',
        backgroundColor: colors.white,
        borderRadius: 8,
        ...paddingStyles.pa16,
        // marginTop:36,
        // ...marginStyles.mh16,
        // marginBottom: 167,   
      },
      postBookingHeader: {
        ...fontStyles.headingBase,
        color: colors.lightGray,
        ...marginStyles.mb16,
        marginTop: 20
      },
      postBookingSubHeader: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        ...marginStyles.mb12,
      },
      postDocListContainer: {
        ...marginStyles.mb12,
      },
      postDocListItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
      },
      bulletPoint: {
        width: 6,
        height: 6,
        borderRadius: 3,
        backgroundColor: colors.gray,
        ...marginStyles.mt6,
        ...marginStyles.mr8,
        ...marginStyles.ml8,
      },
      postDocListText: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        flex: 1,
      },
      agentPostPersuasion: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
      },
    completedIconStyle: {
        width: 14,
        height: 14,
    },
    completeStatusWrapper: {
      flexDirection: 'row',
      gap: 4,
      alignItems: 'center',
      marginBottom: 4,
  },
  completeStatusText: {
      ...fontStyles.labelSmallBold,
      color: colors.green,
  },
  pendingStatusText: {
      ...fontStyles.labelSmallBold,
      color: colors.yellow,
  },
  darkenedBackground: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
  },
  qcAcceptedStatusIcon: {
    width: 16,
    height: 16,
  },
  qcRejectedStatusIcon: {
      width: 16,
      height: 16,
  },
  navigationButtonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
  },
  navigationButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 2,
  },
  disabledNavigationButton: {
      opacity: 0.2,
      pointerEvents: 'none',
  },
  navigationButtonText: {
      ...fontStyles.labelBaseBold,
      color: colors.primaryBlue,
  },
  disabledNavigationButtonText: {
      color: colors.gray,
  },
  navigationIcon: {
      width: 16,
      height: 16
  },
  navigationIconRotate: {
    transform: [{ rotate: '180deg' }],
  },
  disabledNavigationIcon: {
      tintColor: colors.gray,
  },
  buttonArrowIcon: {
    width: 14,
    height: 14,
    // tintColor: colors.white,
    transform: [{ rotate: '180deg' }],
    ...marginStyles.ml4,
  },
})

export default UploadDocumentsRevamp;