import React, {useState, useEffect} from 'react';
import { View, Image, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { getImagePath } from '@mmt/visa/src/VisaRevamp/Utils/VisaUtils';
import { colors } from '@mmt/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from '@mmt/visa/src/VisaRevamp/Styles/Spacing';
import { downloadImage } from '../../../../../AddDocuments/VisaImageDownloader';
import { fontStyles } from '../../../../../../Styles/fonts';
import { logPDTEvent } from '@mmt/visa/src/VisaRevamp/Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '@mmt/visa/src/VisaRevamp/Tracking/pdt/constants';
import LineLoader from '@Frontend_Ui_Lib_App/LineLoader';

export default function DocumentCard({url, thumbnailUrl, handleFullScreenImage, handleOpenDeleteModal, docId, docType, setActiveDocument, docError, setUploadErrorState, categoryType, paxIndex, qcDocStatus}) {
    const [image, setImage] = useState(null);
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageLoadError, setImageLoadError] = useState(false);

    async function fetchImages(item) {
        setImageLoaded(false);
        setImageLoadError(false);
        if (!item){
            setImage(null);
            setImageLoaded(true);
        }
        
        if (item?.includes('file://')) {
            // For local files, use the URI directly
            setImage(item);
            setImageLoaded(true);
        } else if (item?.startsWith('data:')) {
            // For base64 data
            setImage(item);
            setImageLoaded(true);
        } else {
            // For remote URLs, download and convert to base64
            const bustedUrl = `${item}?cb=${Date.now()}`;
            downloadImage(bustedUrl, {
                onImageLoading: () => { },
                onImageLoadingFailed: (error) => {
                    setImage(null);
                    setImageLoadError(true);
                },
                onImageQueued: () => { },
                onImageLoaded: (base64data) => {
                    setImage(base64data);
                    setImageLoaded(true);
                },
            });
        }
    }

    const handleDeleteDocument = () => {
        if(docType !== 'error') {
            logPDTEvent({
                eventValue: `click_delete_${docType}`,
                actionType: PDT_EVENT_TYPES.buttonClicked,
            })
            setActiveDocument({ docIndex: docId, docType: docType });
            handleOpenDeleteModal();
            
        } else {
            setUploadErrorState(prevData => {
                const newData = { ...prevData };
                if (newData[categoryType+paxIndex]) {
                    newData[categoryType+paxIndex] = newData[categoryType+paxIndex].filter((_, index) => index !== docId);
                    // If array is empty, remove the category type key
                    if (newData[categoryType+paxIndex].length === 0) {
                        delete newData[categoryType+paxIndex];
                    }
                }
                return newData;
            });
        }
    }

    useEffect(() => {
        const imageUrl = url?.includes('.pdf') ? thumbnailUrl : url;
        fetchImages(imageUrl);
    }, [url]);

    return (
        <View style={styles.uploadedDocumentItemImageWrapper}>
            {
                docType === 'error' ? (
                    <TouchableOpacity style={styles.errorImageWrapper} onPress={() => handleFullScreenImage(image, url?.includes('.pdf') ? thumbnailUrl : url)}>
                        <View style={styles.errorImageOverlay}>
                            <Text style={styles.errorImageOverlayText}>REJECTED</Text>
                        </View>
                        <Image
                            source={{ uri: image }}
                            style={styles.uploadedDocumentItemImage}
                            resizeMode="contain"
                        />
                    </TouchableOpacity>
                ):(
                    <TouchableOpacity style={styles.errorImageWrapper} onPress={() => image !== null ? handleFullScreenImage(image, url?.includes('.pdf') ? thumbnailUrl : url) : null}>
                        {
                            qcDocStatus?.qcStatus === "REJECTED" && (
                                <View style={styles.errorImageOverlay}>
                                    <Text style={styles.errorImageOverlayText}>REJECTED</Text>
                                </View>
                            )
                        }
                        {
                            imageLoaded ? (
                                image !== null ? (
                                    <Image
                                        source={{ uri: image }}
                                        style={styles.uploadedDocumentItemImage}
                                        resizeMode="contain"
                                    />
                                ) : (
                                    <View style={{ flexGrow: 1, padding: 16, justifyContent: 'center', alignItems: 'center' }}>
                                        <Text>No Document available</Text>
                                    </View>
                                )
                            ) : (
                                <View style={{ flexGrow: 1, padding: 16, justifyContent: 'center', alignItems: 'center' }}>
                                    {
                                        imageLoadError ? (
                                            <View>
                                                <Text>Failed to load image</Text>
                                                <TouchableOpacity onPress={() => fetchImages(url.includes('.pdf') ? thumbnailUrl : url)}>
                                                    <Text style={{color: colors.primaryBlue, ...fontStyles.labelBaseBold}}>Retry</Text>
                                                </TouchableOpacity>
                                            </View>
                                        ) : (
                                            image !== null ? (
                                                <LineLoader />
                                            ) : (
                                                <Text>No preview available</Text>
                                            )
                                        )
                                    }
                                </View>
                            )
                        }
                    </TouchableOpacity>
                )
            }
            {
                !qcDocStatus?.disableUpload && image !== null && (
                    <View style={styles.deleteButton}>
                        <TouchableOpacity onPress={() => handleDeleteDocument()}>
                        <Image
                            source={getImagePath('deleteIcon')}
                            style={styles.deleteIcon}
                        />
                        </TouchableOpacity>
                    </View>
                )
            }
            {
                qcDocStatus?.qcStatus === "" ?
                (docError?.criticalError || docError?.softError) && (
                    <View style={styles.errorTextWrapper}>
                        {
                            (docError?.softError && docError?.softError !== null || docType === 'error') && (
                                <Text style={styles.errorTextTitle}>Please fix errors in uploaded image</Text>       
                            )
                        }
                        {
                            docType === 'error' && docError?.criticalError && docError?.criticalError !== null ?
                            Array.isArray(docError?.criticalError) ? docError?.criticalError?.map((error) => (
                                <View style={styles.errorTextInnerWrapper}><View style={styles.errorTextIcon}></View><Text style={styles.criticalErrorText}>{error}</Text></View>
                            )) : <Text style={styles.criticalErrorText}>{docError?.criticalError}</Text>
                            : null
                        }
                        {
                            docError?.softError && docError?.softError !== null ?
                            Array.isArray(docError?.softError) ? docError?.softError?.map((error) => (
                                <Text style={styles.softErrorText}>{error}</Text>
                            )) : <Text style={styles.softErrorText}>{docError?.softError}</Text>
                            : null
                        }
                    </View>
                )
                :
                qcDocStatus?.qcRejectionText !== "" &&
                <View style={styles.errorTextWrapper}>
                    <Text style={styles.errorTextTitle}>{qcDocStatus?.qcRejectionText}</Text>
                    {
                        qcDocStatus?.qcrejectionreason !== "" &&
                        Array.isArray(qcDocStatus?.qcrejectionreason) ? qcDocStatus?.qcrejectionreason?.map((error) => (
                            <View style={styles.errorTextInnerWrapper}><View style={styles.errorTextIcon}></View><Text style={styles.criticalErrorText}>{error}</Text></View>
                        )) : <View style={styles.errorTextInnerWrapper}><View style={styles.errorTextIcon}></View><Text style={styles.criticalErrorText}>{qcDocStatus?.qcrejectionreason}</Text></View>
                    }
                </View>
            }
        </View>
    );
}

const styles = StyleSheet.create({
    uploadedDocumentItemImageWrapper: {
        height: 'auto',
        width: '100%',
        borderRadius: 8,
        backgroundColor: colors.lightGray2,
        borderWidth: 1,
        borderColor: colors.grayBorder,
        ...paddingStyles.pv12,
        paddingLeft: 16,
        paddingRight: 16,
        overflow: 'hidden',
        flexDirection: 'column',
        // alignItems: 'flex-start',
        justifyContent: 'flex-start',
    },
    uploadedDocumentItemImage: {
        width: '100%',
        height: 150,
    },
    deleteButton: {
        position: 'absolute',
        top: 12,
        right: 4,
        backgroundColor: colors.white,
        width: 24,
        height: 24,
        borderRadius: 24,
        ...paddingStyles.pa4,
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        // iOS shadow
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        // Android shadow
        elevation: 3,
    },
    deleteIcon: {
        width: 16,
        height: 16,
    },
    errorTextWrapper: {
        marginTop: 10,
        alignContent: 'flex-start',
        justifyContent: 'flex-start',
    },
    errorTextTitle: {
        color: colors.black,
        ...fontStyles.labelBaseBold,
    },
    errorTextInnerWrapper: {
        ...marginStyles.mv4,
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        gap: 8,
    },
    criticalErrorText: {
        color: colors.red,
        ...fontStyles.labelSmallRegular,
    },
    softErrorText: {
        color: colors.yellow,
        ...fontStyles.labelSmallRegular,
    },
    errorTextIcon: {
        width: 5,
        height: 5,
        backgroundColor: colors.red,
        borderRadius: 16,
        flexShrink: 0,
        marginTop: 7,
    },
    errorImageWrapper: {
        position: 'relative',
        width: 240,
        height: 150,
        alignSelf: 'center',
    },
    errorImageOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: colors.black,
        opacity: 0.5,
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    errorImageOverlayText: {
        color: colors.white,
        ...fontStyles.labelBaseBlack,
        zIndex: 1,
    },
});
