import React, {useState, useEffect} from 'react';
import { View, Text, Image, Animated, StyleSheet, TouchableOpacity } from 'react-native';
import { getImagePath } from '@mmt/visa/src/VisaRevamp/Utils/VisaUtils';
import { colors } from '@mmt/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from '@mmt/visa/src/VisaRevamp/Styles/Spacing';
import { fontStyles } from '@mmt/visa/src/VisaRevamp/Styles/fonts';
import { VISA_ROUTE_KEYS, VisaNavigation } from '@mmt/visa/src/VisaRevamp/Navigation';
import DocumentCard from './DocumentCard';
import { logPDTEvent } from '../../../../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../../../../Tracking/pdt/constants';

export default function CategoryUploaded({
  category,
  handleOpenModal,
  handleOpenGuidelines,
  renderUploadBottomSheet,
  rendeGuidelineBottomSheet,
  renderDeleteConfirmationBottomSheet,
  renderMultiPaxUploadBottomSheet,
  handleOpenDeleteModal,
  documents,
  type,
  paxIndex,
  isCompleted,
  hasNoError,
  uploadErrorState,
  setUploadErrorState,
  setActiveDocument,
  disableUpload
}) {
  // const [openCategory, setOpenCategory] = useState(isCompleted ? false : true);
  const [openCategory, setOpenCategory] = useState(true);
  const [rotateAnim] = useState(new Animated.Value(openCategory ? 1 : 0));

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['90deg', '-90deg'],
  });

  const documentUploadedCount = ((uploadErrorState?.length || 0) + category?.docStatusList?.reduce((acc, doc) => {
    const documentUrls = documents?.find(document => document?.type === doc?.type)?.urls || [];
    return acc + (documentUrls?.length || 0);
  }, 0)) || 0;
  const isMaxUpload = documentUploadedCount < category?.maxUpload ? false : true;

  const showAddMoreInfo = documentUploadedCount < category?.docStatusList?.length ? true : false;

  const showQcRejectedStatus = category?.docStatusList?.filter(doc => {
    return doc?.qcStatus !== "" && doc?.qcStatus === "REJECTED"
  });

  const showQcApprovedStatus = category?.docStatusList?.filter(doc => {
    return doc?.qcStatus !== "" && doc?.qcStatus === "APPROVED"
  });

  const handleFullScreenImage = (image, url) => {
    VisaNavigation.push(VISA_ROUTE_KEYS.IMAGE_VIEWER, {
      image: image,
      url: url,
    });
  };
  const handleArrowClick = () => {
    logPDTEvent({
      eventValue: `click_expand_${category?.name}`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    })
    setOpenCategory(!openCategory);
  };

  useEffect(() => {
    Animated.timing(rotateAnim, {
      toValue: openCategory ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [openCategory]);

  // Debug style calculation
  const calculatedOpacity = disableUpload ? 0.5 : 1;
  const calculatedPointerEvents = disableUpload ? 'none' : 'auto';

  return (
    <View style={[styles.categoryItemOuterWrapper, {opacity: calculatedOpacity, pointerEvents: calculatedPointerEvents}]}>
      <View style={styles.categoryItemTopWrapper}>
        <View style={styles.categoryContent}>
          {
            (!showQcApprovedStatus?.length > 0 && !showQcRejectedStatus?.length > 0) && (
              <View style={styles.completeStatusWrapper}>
                {
                  !hasNoError ?
                    <Image
                      source={isCompleted && hasNoError ? getImagePath('submitImage') : !hasNoError ? getImagePath('warningImage') : getImagePath('warningImage')}
                      style={styles.completedIconStyle}
                    />
                    :
                    <Image
                      source={isCompleted && hasNoError ? getImagePath('submitImage') : !hasNoError ? getImagePath('warningImage') : getImagePath('warningImage')}
                      style={styles.completedIconStyle}
                    />
                }
                {isCompleted && hasNoError ? (
                  <Text style={styles.completeStatusText}>Completed</Text>
                ) : 
                !hasNoError && !isCompleted ?
                <Text style={styles.pendingStatusText}>Pending</Text> :
                <Text style={styles.pendingStatusText}>Pending</Text>
                }
              </View>
            )
          }
          <Text style={styles.categoryTitle}>{category?.name}</Text>
          {!isCompleted && category?.description && (
            <Text style={styles.categoryDescription}>{category?.description}</Text>
          )}
        </View>
        {
          showQcApprovedStatus?.length === category?.docStatusList?.length ? (
            <View style={styles.qcAcceptedStatusWrapper}>
              <Text style={styles.qcAcceptedStatusText}>{showQcApprovedStatus?.[0]?.qcStatus?.charAt(0).toUpperCase() + showQcApprovedStatus?.[0]?.qcStatus?.slice(1).toLowerCase()}</Text>
              <Image source={getImagePath('approvedIcon')} style={styles.qcAcceptedStatusIcon} />
            </View>
          )
          :
          showQcRejectedStatus?.length > 0 ? (
            <View style={styles.qcRejectedStatusWrapper}>
              <Text style={styles.qcRejectedStatusText}>{showQcRejectedStatus?.[0]?.qcStatus?.charAt(0).toUpperCase() + showQcRejectedStatus?.[0]?.qcStatus?.slice(1).toLowerCase()}</Text>
              <Image source={getImagePath('rejectedIcon')} style={styles.qcRejectedStatusIcon} />
            </View>
          )
          :
          null
        }
        <TouchableOpacity onPress={handleArrowClick}>
          <Animated.Image
            style={[styles.rightArrowStyle, { transform: [{ rotate: rotateInterpolate }] }]}
            source={getImagePath('rightArrow')}
          />
        </TouchableOpacity>
      </View>
      {openCategory && (
        <View style={styles.uploadedSectionWrapper}>
          {category?.docStatusList?.sort((a,b) => a.order - b.order)?.map(
            (doc) => {
              return (doc?.uploaded && (documents && documents?.length > 0)) && (
                <View style={styles.uploadedDocumentItem}>
                  {category?.category?.toLowerCase() === 'PASSPORT'.toLowerCase() && (
                    <Text style={styles.uploadedDocumentTitle}>{doc?.name}</Text>
                  )}
                  {documents?.map((document) => {
                    return document?.type === doc?.type ? (
                      document?.urls && document?.urls?.length > 0 && (
                        document?.urls?.sort((a,b) => a.urlIndex - b.urlIndex)?.map((url) => {
                          return <DocumentCard
                            key={url?.urlIndex}
                            handleFullScreenImage={handleFullScreenImage}
                            handleOpenDeleteModal={handleOpenDeleteModal}
                            url={url?.url}
                            thumbnailUrl={url?.thumbnailUrl}
                            docId={url?.urlIndex}
                            docType={document?.type}
                            setActiveDocument={setActiveDocument}
                            docError={{
                              criticalError: url?.criticalError || doc?.prevError?.criticalError,
                              softError: url?.softError || doc?.prevError?.softError,
                            }}
                            qcDocStatus={{
                              qcStatus: doc?.qcStatus || "",
                              qcRejectionText: doc?.qcRejectionText || "",
                              qcrejectionreason: doc?.qcrejectionreason || "",
                              disableUpload: doc?.disableUpload
                            }}
                          />
                        })
                      )
                    ) : null;
                  })}
                </View>
              )
            }
          )}
          {
            uploadErrorState?.length > 0 && uploadErrorState?.map((error, index) => (
              <View style={styles.uploadedDocumentItem} key={index}>
                  <DocumentCard
                    key={index}
                    url={error?.url}
                    docType={error?.docType}
                    categoryType={category?.category?.replace(/ /g, "_")}
                    docId={index}
                    docError={{
                      criticalError: error?.criticalError,
                    }}
                    paxIndex={paxIndex}
                    setUploadErrorState={setUploadErrorState}
                    qcDocStatus={{
                      qcStatus: "",
                    }}
                    handleFullScreenImage={handleFullScreenImage}
                  />
                </View>
            ))
          }
          { showAddMoreInfo && (
            <View style={styles.notUploadedDocumentItem}>
              <Image source={getImagePath('filesIcon')} style={styles.notUploadedDocumentIcon} />
              <Text style={styles.notUploadedDocumentText}>
                You haven't added some documents yet.
              </Text>
            </View>
          )}
          {
            !isMaxUpload && (
              <TouchableOpacity
                style={isMaxUpload ? styles.addButtonDisabled : styles.addButton}
                onPress={() => handleOpenModal()}
                disabled={isMaxUpload}
              >
                <Text style={isMaxUpload ? styles.addButtonDisabledText : styles.addButtonText}>
                  + ADD
                </Text>
              </TouchableOpacity>
            )
          }
        </View>
      )}
      <View style={styles.categoryItemBottomWrapper}>
        <TouchableOpacity onPress={handleOpenGuidelines}>
          <Text style={styles.guidelineText}>View document list and guidelines</Text>
        </TouchableOpacity>
      </View>
      {renderUploadBottomSheet()}
      {rendeGuidelineBottomSheet()}
      {renderDeleteConfirmationBottomSheet()}
      {renderMultiPaxUploadBottomSheet()}
    </View>
  );
}

const styles = StyleSheet.create({
    categoryItemOuterWrapper: {
        backgroundColor: colors.white,
        borderRadius: 8,
        ...paddingStyles.pt16,
        ...paddingStyles.pb12,
        ...paddingStyles.ph12,
        position: 'relative',
        flexDirection: 'column',
    },
    categoryItemTopWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        gap: 8
    },
    categoryContent: {
        flexDirection: 'column',
        flex: 1,
    },
    categoryTitle: {
        ...fontStyles.labelMediumBold,
        color: colors.black,
    },
    categoryDescription: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        ...marginStyles.mt8,
    },
    rightArrowStyle: {
        width: 22,
        height: 22,
    },
    categoryItemBottomWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...marginStyles.mt12,
    },
    guidelineText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    addButton: {
        borderWidth: 1,
        borderColor: colors.primaryBlue,
        borderRadius: 8,
        width: '100%',
        alignItems: 'center',
        ...paddingStyles.pv8,
        ...paddingStyles.ph12,
    },
    addButtonDisabled: {
        width: '100%',
        alignItems: 'center',
        color: colors.gray,
        borderWidth: 1,
        borderRadius: 8,
        borderColor: colors.grayBorder,
        ...paddingStyles.pv8,
        ...paddingStyles.ph12,
        opacity: 0.5,
    },
    addButtonText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    addButtonDisabledText: {
        color: colors.gray,
      ...fontStyles.labelSmallBold,
    },
    notUploadedDocumentItem: {
        flexDirection: 'column',
        gap: 12,
        width: '80%',
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
    },
    notUploadedDocumentIcon: {
        width: 80,
        height: 80,
    },
    notUploadedDocumentText: {
        ...fontStyles.labelSmallRegular,
        color: colors.lightGray,
        width: 150,
        textAlign: 'center',
    },
    uploadedSectionWrapper: {
        flexDirection: 'column',
        gap: 24,
        marginTop: 24,
    },
    uploadedDocumentItem: {
        flexDirection: 'column',
        gap: 8,
    },
    uploadedDocumentTitle: {
        ...fontStyles.labelBaseBold,
        color: colors.black,
    },
    completeStatusWrapper: {
        flexDirection: 'row',
        gap: 4,
        alignItems: 'center',
        marginBottom: 4,
    },
    completeStatusText: {
        ...fontStyles.labelSmallBold,
        color: colors.green,
    },
    pendingStatusText: {
        ...fontStyles.labelSmallBold,
        color: colors.yellow,
    },
    completedIconStyle: {
        width: 14,
        height: 14,
    },
    qcAcceptedStatusWrapper: {
        flexDirection: 'row',
        gap: 4,
        alignItems: 'center',
        justifyContent: 'center',
        ...marginStyles.mt2,
    },
    qcRejectedStatusWrapper: {
        flexDirection: 'row',
        gap: 4,
        alignItems: 'center',
        justifyContent: 'center',
        ...marginStyles.mt2,
    },
    qcAcceptedStatusText: {
        ...fontStyles.labelBaseBold,
        color: colors.green,
    },
    qcRejectedStatusText: { 
        ...fontStyles.labelBaseBold,
        color: colors.red,
    },
    qcAcceptedStatusIcon: {
        width: 16,
        height: 16,
    },
    qcRejectedStatusIcon: {
        width: 16,
        height: 16,
    },
});
