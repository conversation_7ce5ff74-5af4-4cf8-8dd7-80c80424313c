import React, {useState, useEffect} from 'react';
import { View, Text, Image, Animated, StyleSheet, TouchableOpacity } from 'react-native';
import { getImagePath } from '@mmt/visa/src/VisaRevamp/Utils/VisaUtils';
import { colors } from '@mmt/visa/src/VisaRevamp/Styles/colors';
import { marginStyles, paddingStyles } from '@mmt/visa/src/VisaRevamp/Styles/Spacing';
import { fontStyles } from '@mmt/visa/src/VisaRevamp/Styles/fonts';
import { logPDTEvent } from '../../../../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../../../../Tracking/pdt/constants';
import LinearGradient from 'react-native-linear-gradient';

export default function CategoryNotUploaded({category, handleOpenModal, handleOpenGuidelines, renderUploadBottomSheet, rendeGuidelineBottomSheet, renderMultiPaxUploadBottomSheet, disableUpload, fromMIMA}) {
    const [openCategory, setOpenCategory] = useState(false);
    const [rotateAnim] = useState(new Animated.Value(openCategory ? 1 : 0));

    const rotateInterpolate = rotateAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ['90deg', '-90deg'],
      });
      const handleArrowClick = () => {
        logPDTEvent({
          eventValue: `click_expand_${category?.name}`,
          actionType: PDT_EVENT_TYPES.buttonClicked,
        })
        setOpenCategory(!openCategory);
      };

    useEffect(() => {
        Animated.timing(rotateAnim, {
        toValue: openCategory ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
        }).start();
    }, [openCategory]);

    // Debug style calculation
    const calculatedOpacity = disableUpload ? 0.5 : 1;
    const calculatedPointerEvents = disableUpload ? 'none' : 'auto';

    return (
        <View style={[styles.categoryItemOuterWrapper, {opacity: calculatedOpacity, pointerEvents: calculatedPointerEvents}]}>
            <View style={styles.categoryItemTopWrapper}>
                <View style={styles.categoryContent}>
                    <View style={styles.categoryTitleWrapper}>
                        <Text style={styles.categoryTitle}>{category?.name}</Text>
                        {
                            (fromMIMA || category?.additional) && (
                                <LinearGradient
                                    colors={
                                        ['#03BAE1','#3A7BD5']
                                    }
                                    start={{ x: 0.0, y: 0.0 }}
                                    end={{ x: 1.0, y: 0.0 }}
                                    style={styles.additionalDocumentTag}
                                    >
                                    <Text style={styles.additionalDocumentTagText}>
                                        NEW
                                    </Text>
                                </LinearGradient>   
                            )
                        }
                    </View>
                    {
                        category?.description && <Text style={styles.categoryDescription}>{category?.description}</Text>
                    }
                </View>
                <TouchableOpacity onPress={handleArrowClick}>
                    <Animated.Image
                        style={[
                        styles.rightArrowStyle,
                        { transform: [{ rotate: rotateInterpolate }] }
                        ]}
                        source={getImagePath('rightArrow')}
                    />
                </TouchableOpacity>
            </View>
            {
                openCategory && (
                <View style={styles.notUploadedDocumentItem}>
                    <Image source={getImagePath('filesIcon')} style={styles.notUploadedDocumentIcon} />
                    <Text style={styles.notUploadedDocumentText}>You haven't added any documents yet.</Text>
                </View>
                )
            }
            <View style={styles.categoryItemBottomWrapper}>
                <TouchableOpacity onPress={handleOpenGuidelines}>
                    <Text style={styles.guidelineText}>View document list and guidelines</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.addButton, {alignSelf: 'flex-end'}]} onPress={() => handleOpenModal()}>
                    <Text style={styles.addButtonText}>+ ADD</Text>
                </TouchableOpacity>
            </View>
            {
                (fromMIMA || category?.additional) && (
                    <View style={styles.additionalDocumentTextWrapper}>
                        <Image source={getImagePath('yellowinfoIcon')} style={styles.additionalDocumentIcon} />
                        <Text style={styles.additionalDocumentText}>
                            This is an additional document required for your visa application process
                        </Text>
                    </View>
                )
            }
            {renderUploadBottomSheet()}
            {rendeGuidelineBottomSheet()}
            {renderMultiPaxUploadBottomSheet()}
        </View>
    );
}

const styles = StyleSheet.create({
    categoryItemOuterWrapper: {
        backgroundColor: colors.white,
        borderRadius: 16,
        ...paddingStyles.pa16,
        position: 'relative',
        flexDirection: 'column',
    },
    categoryItemTopWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        gap: 8
    },
    categoryContent: {
        flexDirection: 'column',
        flex: 1,
    },
    categoryTitle: {
        ...fontStyles.labelMediumBold,
        color: colors.black,
    },
    categoryDescription: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        ...marginStyles.mt8,
    },
    rightArrowStyle: {
        width: 22,
        height: 22,
    },
    categoryItemBottomWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...marginStyles.mt8,
    },
    guidelineText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
        // ...marginStyles.mt16,
    },
    addButton: {
        borderWidth: 1,
        borderColor: colors.primaryBlue,
        borderRadius: 8,
        ...paddingStyles.pv8,
        ...paddingStyles.ph12,
    },
    addButtonText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    notUploadedDocumentItem: {
        flexDirection: 'column',
        gap: 12,
        width: '80%',
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        marginTop: 24,
        marginBottom: 24,
    },
    notUploadedDocumentIcon: {
        width: 80,
        height: 80,
    },
    notUploadedDocumentText: {
        ...fontStyles.labelSmallRegular,
        color: colors.lightGray,
        width: 150,
        textAlign: 'center',
    },
    categoryTitleWrapper: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 4,
    },
    additionalDocumentTag: {
        borderRadius: 16,
        ...paddingStyles.pv2,
        ...paddingStyles.ph8,
    },
    additionalDocumentTagText: {
        ...fontStyles.labelSmallBlack,
        color: colors.white,
    },
    additionalDocumentTextWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
        ...marginStyles.mt8,
    },
    additionalDocumentIcon: {
        width: 16,
        height: 16,
    },
    additionalDocumentText: {
        ...fontStyles.labelSmallRegular,
        color: colors.yellow,
    }
});
