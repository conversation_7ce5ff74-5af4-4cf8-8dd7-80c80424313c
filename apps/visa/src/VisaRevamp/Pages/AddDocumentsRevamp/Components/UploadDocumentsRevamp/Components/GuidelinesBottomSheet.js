import React from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  Image, 
  ScrollView,
  Dimensions
} from 'react-native';
import { buttonColors, colors } from '../../../../../Styles/colors';
import { fontStyles } from '../../../../../Styles/fonts';
import ButtonWpr from '../../../../../Components/Common/ButtonWpr';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing';

const GuidelinesBottomSheet = ({ docStatusList, onClose }) => {
  // Ensure docStatusList is an array
  const documents = Array.isArray(docStatusList) ? docStatusList.sort((a, b) => a.order - b.order) : [docStatusList];
  
  // No data to display
  if (!documents || documents.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.noDataText}>No guidelines available</Text>
      </View>
    );
  }

  
  
  const renderDocumentSection = (docItem, index) => {
        return (
      <View key={`doc-${index}`} style={styles.documentSection}>
        {/* {index > 0 && <View style={styles.divider} />} */}
        
        
        {/* Sample image section */}
        {docItem.sampleDocUrl && (
          <View style={styles.sampleSection}>
          <Text style={styles.sectionTitle}>
            Sample {docItem.name} Image
          </Text>
          <Text style={styles.formatText}>
            Image can be {docItem.supportedType?.join(', ')} formats
          </Text>
          {docItem.sampleDocUrl && (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: docItem.sampleDocUrl }}
                style={styles.sampleImage}
                resizeMode="cover"
              />
            </View>
          )}
        </View>
        )}
        
        
        {/* Guidelines section */}
        <View style={styles.guidelinesSection}>
          <Text style={styles.sectionTitle}>
            {docItem.detailedDescriptionHeader || 'Important Guidelines'}
          </Text>
          <Text style={styles.guidelineText}>
            {docItem.detailDescriptionSubHeader || 'Please read the guidelines carefully before selecting your documents'}
          </Text>
          
          {/* Display detailed instructions */}
          {docItem.detailedDescription && 
          <View style={styles.bulletContainer}>
            {docItem.detailedDescription.map((guideline, idx) =>(
              <View key={`guideline-${index}-${idx}`} style={styles.bulletItem}>
                <Text style={styles.bulletPoint}>•</Text>
                <Text style={styles.bulletText}>{guideline}</Text>
              </View>
            ))}
            </View>
          }
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.contentContainer} showsVerticalScrollIndicator={false}>
        {[...documents].map(renderDocumentSection)}
      </ScrollView>
      <View style={styles.buttonContainer}> 
      <ButtonWpr
        buttonText="DONE"
        onButtonPress={onClose}
      />
      </View>
    </View>
  );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    maxHeight: height * 0.7,
    width: '100%',
  },
  buttonContainer: {
    backgroundColor: colors.white,
    ...paddingStyles.ph16,
    ...paddingStyles.pv16,
    ...paddingStyles.pb24,
  },
  noDataText: {
    ...fontStyles.labelMediumRegular,
    color: colors.gray,
    textAlign: 'center',
  },
  contentContainer: {
    maxHeight: height * 0.6,
    ...paddingStyles.pt20,
    ...paddingStyles.ph16,
    ...paddingStyles.pb24,
    // height: 200,
    backgroundColor: colors.lightGray2,
  },
  documentSection: {
    ...marginStyles.mb24,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.grayBorder,

    borderRadius: 16,
    ...paddingStyles.pa20,
  },
  documentTitle: {
    ...fontStyles.labelLargeBold,
    color: colors.black,
    ...marginStyles.mb16,
  },
  divider: {
    borderWidth: 1,
    borderColor: colors.grayBorder,
    // backgroundColor: colors.lightGray,
    ...marginStyles.mb16,
  },
  sampleSection: {
    ...marginStyles.mb20,
  },
  sectionTitle: {
    ...fontStyles.labelMediumBold,
    color: colors.black,
    ...marginStyles.mb4,
  },
  formatText: {
    ...fontStyles.labelSmallRegular,
    color: colors.lightGray,
    ...marginStyles.mt8,
  },
  imageContainer: {
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 24,
    height: 200,
    width: '100%',
  },
  sampleImage: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.lightGray,
    resizeMode: 'cover',
  },
  guidelinesSection: {
    // ...marginStyles.mb20,
  },
  guidelineText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
    ...marginStyles.mb16,
  },
  bulletItem: {
    flexDirection: 'row',
    ...marginStyles.mb6,
    alignItems: 'flex-start',
  },
  bulletPoint: {
    ...marginStyles.mr8,
    ...fontStyles.labelSmallRegular,
    color: colors.black,
  },
  bulletText: {
    flex: 1,
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
  },
  bulletContainer:{
    borderTopWidth: 1,
    borderTopColor: colors.grayBorder,
    ...paddingStyles.pt16,
  }
});

export default GuidelinesBottomSheet;