import { StyleSheet } from 'react-native'
import { colors } from "../../../../../Styles/colors";
import { fontStyles } from '../../../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { borderRadius } from '../../../../../Styles/borderRadius';

const styles = StyleSheet.create({
    cardContainer: {
        backgroundColor: colors.white,
        borderWidth: 1,
        borderColor: colors.grayBorder,
        ...borderRadius.borderRadius8,
        width: 150,
        height: 176
    },
    headerCardContainer: {
        borderBottomWidth: 1,
        borderColor: colors.grayBorder,
        justifyContent: "space-between",
        flexDirection: "row",
        ...paddingStyles.pa8
    },
    headerCardTitle: {
        ...fontStyles.labelSmallRegular,
        color: colors.black,
        flexShrink: 2
    },
    editableText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    photoContainer: {
        backgroundColor: colors.lightGray2,
        ...borderRadius.borderRadius8,
        ...marginStyles.ma8,
        width: 130,
        height: 100
    },
    footerCardContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        ...paddingStyles.pb12,
        ...paddingStyles.ph12
    },
    assignableFooterContainer: {
        color: "transparent",
        // width:150, 
        height: 30
    },
    assignableFooter: {
        ...marginStyles.ml14,
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    errorFound: {
        ...fontStyles.labelSmallBold,
        color: colors.yellow,
    },
    viewTextFooter: {
        // ...textFreeStyle('small', colors.primaryBlue, 'bold'),
    },
    container: {
        backgroundColor: colors.white,
        ...paddingStyles.pa16,
        ...marginStyles.mt8,
    },
    sectionHeader: {
        flexDirection: "row",
    },
    title: {
        ...marginStyles.mb4,
        ...fontStyles.labelMediumBold,
        color: colors.black,
    },
    subtitle: {
        ...marginStyles.mb4,
        ...fontStyles.labelSmallBold,
        color: colors.yellow,
    },
    tabsDisplay: {
        height: "100%",
        flex: 1,
        ...marginStyles.mb30
    },
    sectionHeaderIcon: {
        width: 20,
        height: 20,
        ...marginStyles.mr8,
    },
    cardsLayout: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        ...marginStyles.mt12,
    },
    tabSectionColumn: {
        width: '49%',
        ...marginStyles.mr2,
        alignItems: "center",
        ...marginStyles.mb16,
        justifyContent: 'space-between',
    },
    photoCardImage: {
        width: "100%",
        height: "100%"
    },
    bottomsheetHeaderContainer: {
        ...paddingStyles.pb16,
        borderBottomWidth: 1,
        borderColor: colors.grayBorder,
    },
    header: {
        ...fontStyles.labelMediumBold,
        color: colors.black,
    },
    listContainer: {
        ...marginStyles.mt10
    },
    buttonContainer: {
        ...marginStyles.mt4,
        ...marginStyles.mb10
    },
    tabTitleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    bgBack: {
        width: 18,
        height: 18,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    bgDone: {
        backgroundColor: colors.green,
    },
    bgAction: {
        backgroundColor: colors.yellow,
    },
    bgRejected: {
        backgroundColor: colors.red,
    },
    tabTitle: {
        ...fontStyles.labelBaseRegular,
        fontSize: 14,
        marginRight: 4,
    },
    nosText: {
        ...fontStyles.labelMediumBold,
        fontSize: 14,
        color: colors.white,
        paddingBottom: 2,
    }
})

export default styles;