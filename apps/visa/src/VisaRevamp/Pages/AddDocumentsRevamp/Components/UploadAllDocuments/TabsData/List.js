
import React, { useState, useEffect } from 'react';
import { Image, View, Text, StyleSheet, ScrollView } from 'react-native';
import { colors } from '../../../../../Styles/colors';
import { borderRadius } from '../../../../../Styles/borderRadius';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing';
import { uploadClassifierData, deleteClassifierData } from '../../../../../Utils/NetworkUtils';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../../Navigation';
import { fontStyles } from '../../../../../Styles/fonts';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { downloadImage } from '../../../VisaImageDownloader';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { logPDTEvent } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';
import { ALERTS } from '../../../../../textStrings';

/* Components */
import ButtonWpr from '../../../../../Components/Common/ButtonWpr';
import RadioWpr from '../../../../../Components/Common/RadioWpr';
import ErrorMessage from '../../../../../Components/Common/DocumentWpr/ErrorMessage';
import ImportantGuidelines from '../../../../UploadDocument/Components/ImportantGuidelines';

export default function List({
    itemList = [],
    docList = [],
    uploadedData,
    assignDoc,
    setOpenAssign,
    assignType,
    status = '',
    updateDoc = false,
    errorScreen = false,
    setErrorAssign,
    url
}) {
    const [selectedTravellerId, setSelectedTravellerId] = useState();
    const [selectedDocId, setSelectedDocId] = useState();
    const [isDisabled, setDisabled] = useState(true);
    const [updateDocType, setUpdateDocType] = useState(updateDoc);
    const [assignTypeText, setAssignTypeText] = useState(assignType);
    const [selectedImages, setSelectedImages] = useState();


    async function fetchImages() {
        if (url) {
            downloadImage(url, {
                onImageLoading: () => { },
                onImageLoadingFailed: (error) => { },
                onImageQueued: () => { },
                onImageLoaded: (base64data) => {
                    setSelectedImages(base64data);
                }
            });
        }
    };

    useEffect(() => {
        fetchImages();
    }, []);

    const handleRadioButtonPress = (id) => {
        visaTrackClickEvent({
            eventName: `click_reassign_traveller_select_${id}_${status?.replace(' ', '_')}`,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: `click_reassign_traveller_select_${id}`,
        })
        setSelectedTravellerId(id);
        setDisabled(false);
    };

    const handleRadioButtonPressDoc = (id) => {
        visaTrackClickEvent({
            eventName: `click_identify_${id}_${status?.replace(' ', '_')}`,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: `click_identify_${id}`,
        })
        setSelectedDocId(id);
        setAssignTypeText("Identify Document Type");
        setDisabled(false);
    };

    const getWarningMessage = (assignDoc) => {
        let softError = [];
        if (assignDoc.errors) {
            assignDoc.errors.map((item) => {
                softError.push(item.output);
            });
        }
        return softError;
    }

    const deleteImage = async () => {
        visaTrackClickEvent({
            eventName: `${assignDoc.docId}_alert_remove_click`,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: `${assignDoc.docId}_alert_remove_click`,
        })
        request = {
            bookingId: uploadedData.bookingId,
            docId: assignDoc.docId
        }

        uploadedData.Response.actionNeeded.documents = uploadedData.Response.actionNeeded.documents.filter((item) => item.docId != assignDoc.docId);
        setErrorAssign(false);
        deleteClassifierData({ request }).then((res) => {
            showShortToast(ALERTS.DOCUMENT_REMOVED);
            VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM, {
                bookingId: uploadedData.bookingId,
                assignData: {}
            });
        });
    };

    const openConfirm = () => {
        visaTrackClickEvent({
            eventName: `${assignDoc.docId}_alert_continue_click`,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: `${assignDoc.docId}_alert_continue_click`,
        })
        setErrorAssign(false);
    };

    const openConfirmScreen = () => {
        const eventName = !updateDocType 
            ? `click_reassign_confirm_${selectedTravellerId}_${status?.replace(' ', '_')}` 
            : `click_confirm_identify_${selectedDocId}_${status?.replace(' ', '_')}`;
        visaTrackClickEvent({
            eventName,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: eventName,
        })
        let request = {}
        if (uploadedData) {
            let docType;
            if (selectedDocId) {
                docList.map((item) => {
                    if (item.id === selectedDocId) {
                        docType = item.type;
                    }
                })
            }
            request = {
                "bookingId": uploadedData.bookingId,
                "documentName": assignDoc.name,
                "documentId": assignDoc.docId,
                "documentUrl": assignDoc.url,
                "assignedDetails": {
                    "paxId": selectedTravellerId,
                    "paxName": itemList.find(item => item.paxId === selectedTravellerId)?.name,
                    "documentType": docType ? docType : assignDoc.docType
                }
            }
            setOpenAssign(false);
            showShortToast(request.documentName + ' ' + request.assignedDetails.documentType + ' assigned');
            uploadClassifierData({ request }).then((res) => {
                VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM, {
                    bookingId: uploadedData.bookingId,
                    assignData: res && res.Response ? res : {}
                });
            });
        }
    };

    const openDocumentType = () => {
        visaTrackClickEvent({
            eventName: 'click_change_document_type',
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: 'click_change_document_type',
        })
        setDisabled(true);
        setAssignTypeText("Identify Document Type");
        setUpdateDocType(true);
    };

    return (
        <View>
            <View style={styles.bottomsheetHeaderContainer}>
                <Text style={styles.header}>{assignTypeText}</Text>
            </View>
            <View style={styles.listContainer}>
                {!updateDocType && !errorScreen && <View style={styles.listContainer}>
                    {itemList?.length > 0 ? (
                        itemList?.map((item, index) => (
                            <View key={index} style={[styles.travellerItem, selectedTravellerId === item?.paxId ? styles.travellerItemActive : null]}>
                                <RadioWpr
                                    isSelected={selectedTravellerId === item?.paxId}
                                    onPress={() => handleRadioButtonPress(item?.paxId)}
                                >
                                    <View style={styles.itemContent}>
                                        <View>
                                            <Text style={styles.travellerName}>{item?.name}</Text>
                                            <Text style={styles.travellerDetails}>{item?.details}</Text>
                                        </View>
                                    </View>
                                </RadioWpr>
                            </View>
                        ))
                    ) : (
                        <Text>No items available</Text>
                    )}
                </View>
                }
                {updateDocType && !errorScreen &&
                    <View style={styles.listContainer}>
                        {docList?.length > 0 ? (
                            docList?.map((item, index) => (
                                <View key={index} style={[styles.travellerItem, selectedDocId === item?.id ? styles.travellerItemActive : null]}>
                                    <RadioWpr
                                        isSelected={selectedDocId === item?.id}
                                        onPress={() => handleRadioButtonPressDoc(item?.id)}
                                    >
                                        <View style={styles.itemContent}>
                                            <View>
                                                <Text style={styles.travellerName}>{item?.type}</Text>
                                                <Text style={styles.travellerDetails}>{item?.description}</Text>
                                            </View>
                                        </View>
                                    </RadioWpr>
                                </View>
                            ))
                        ) : (
                            <Text>No items available</Text>
                        )}
                    </View>
                }

                {!updateDocType && !errorScreen &&
                    <View style={styles.buttonContainer}>
                        <ButtonWpr
                            disabled={isDisabled}
                            buttonWidth={"98%"}
                            buttonText={"CHANGE DOCUMENT TYPE"}
                            onButtonPress={openDocumentType}
                            buttonType="outline"
                        />
                    </View>
                }
                {!errorScreen && <View style={styles.buttonContainer}>
                    <ButtonWpr
                        disabled={isDisabled}
                        buttonWidth={"98%"}
                        buttonText={"CONFIRM"}
                        onButtonPress={openConfirmScreen}
                    />
                </View>
                }
            </View>
            {errorScreen &&
                <View>
                        <View style={styles.documentWrapper}>
                            <View style={styles.cardWrapper}>
                                <View style={styles.photoWrapper}>
                                    <View style={styles.profileImgWrapper}>
                                        {selectedImages && selectedImages.length > 0 &&
                                            <Image
                                                style={styles.profileImgStyle}
                                                source={{ uri: selectedImages }}
                                                resizeMode="contain"
                                            />
                                        }
                                    </View>
                                </View>
                                <ErrorMessage
                                    warning={true}
                                    title={"Errors that might get your document rejected"}
                                    msg={getWarningMessage(assignDoc)}
                                />
                                {assignDoc.guidelines && (
                                    <ImportantGuidelines
                                        title={assignDoc.guidelines.header}
                                        subTitle={assignDoc.guidelines.subHeader}
                                        data={assignDoc.guidelines.details}
                                        sampleImage={assignDoc.guidelines.sampleDocUrl}
                                    />
                                )}


                            </View>
                        </View>
                        <View style={styles.buttonContainer}>
                            <ButtonWpr
                                buttonWidth={"98%"}
                                buttonText={"CONTINUE ANYWAY"}
                                onButtonPress={openConfirm}
                                buttonType="outline"
                            />
                        </View>
                        <View style={styles.buttonContainer}>
                            <ButtonWpr
                                buttonWidth={"98%"}
                                buttonText={"REMOVE DOCUMENT"}
                                onButtonPress={deleteImage}
                            />
                        </View>
                </View>
            }
        </View>
    )
}

const styles = StyleSheet.create({
    bottomsheetHeaderContainer: {
        ...paddingStyles.pb16,
        borderBottomWidth: 1,
        borderColor: colors.grayBorder,
    },
    travellerName: {
        ...marginStyles.ml8,
        ...fontStyles.labelSmallBold,
    },
    travellerDetails: {
        ...marginStyles.ml8,
    },
    travellerItem: {
        borderWidth: 1,
        ...borderRadius.borderRadius8,
        ...paddingStyles.pa12,
        ...marginStyles.mb12,
        borderColor: colors.grayBorder
    },
    travellerItemActive: {
        borderColor: colors.primaryBlue,
    },
    buttonContainer: {
        ...marginStyles.mt4,
        ...marginStyles.mb10
    },
    header: {
        ...fontStyles.labelMediumBold,
        color: colors.black,
    },
    listContainer: {
        ...marginStyles.mt10
    },
    documentWrapper: {
        ...paddingStyles.ph8,
        ...paddingStyles.pb16,
        ...paddingStyles.pt40,
    },
    cardWrapper: {
        backgroundColor: colors.white,
        ...borderRadius.borderRadius8,
        borderColor: colors.grayBorder,
    },
    photoWrapper: {
        ...borderRadius.br8,
        borderWidth: 1,
        borderColor: colors.transparent,
        height: 240,
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#eaf5ff',
    },
    profileImgWrapper: {
        width: 145,
        height: 235,
        justifyContent: 'center',
    },
    profileImgStyle: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
    },
    wrapperHeight: {
        zIndex: 0,
    },
})