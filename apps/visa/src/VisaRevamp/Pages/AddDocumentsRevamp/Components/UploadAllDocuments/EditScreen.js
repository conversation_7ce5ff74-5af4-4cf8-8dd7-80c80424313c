import React, { useRef, useEffect, useState } from 'react';
import { View, Image, StyleSheet, Text, Dimensions, BackHandler, PanResponder, Document } from 'react-native';
import { HARDWARE_BACK_PRESS, PDT_SUB_PAGE_NAMES, VIEW_STATES } from '../../../../constants';
import { deleteClassifierData } from '../../../../Utils/NetworkUtils';
import { colors } from '../../../../Styles/colors';
import { fontStyles } from '../../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { getImagePath } from '../../../../Utils/VisaUtils';

import ErrorPage from '../../../../Components/Common/ErrorPage';
import LineLoader from '../../../../Components/Common/Loader';
import HeaderWpr from '../../../../Components/Common/HeaderWpr';
import ButtonWpr from '../../../../Components/Common/ButtonWpr';
import { downloadImage } from '../../VisaImageDownloader';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import BottomSheetWpr from '../../../../Components/Common/BottomSheetWpr';
import List from '../UploadAllDocuments/TabsData/List';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { logPDTEvent, visaPdtEventsInitilizer } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';
import VisaDataHolder from 'apps/visa/src/VisaRevamp/Utils/VisaDataHolder';
import { ALERTS } from '../../../../textStrings';


const EditScreen = ({ bookingId, docid, url, titleText, confirm, setTriggerEffect, classifier, uploadedData, assignDoc, status = '' }) => {

    const [viewState, setViewState] = useState(VIEW_STATES.SUCCESS);
    const [selectedImages, setSelectedImages] = useState([]);
    const [openErrorSheet, setOpenErrorSheet] = useState(false);
    const [openAssign, setOpenAssign] = useState(false);

    async function fetchImages() {
        if (url) {
            downloadImage(url, {
                onImageLoading: () => { },
                onImageLoadingFailed: (error) => { },
                onImageQueued: () => { },
                onImageLoaded: (base64data) => {
                    setSelectedImages(prevImages => [...prevImages, { type: docid, base64data }]);
                }
            });
            visaTrackClickEvent({ eventType: TYPE_OF_EVENTS.PAGE_LOAD });
            visaPdtEventsInitilizer();
        }
    };

    const getImgUrl = (selectedImages, type) => {
        if (selectedImages) {
            const image = selectedImages.find(item => item.type === type);
            if (image) {
                return { uri: image.base64data };
            }
        }
    }

    const handleBack = () => {
        VisaNavigation.pop();
        return true;
    };

    const openAssignTraveller = () => {
        visaTrackClickEvent({
            eventName: `click_reassign_classifier_result_${titleText ? titleText : docid}_${status?.replace(' ', '_')}`,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: `click_reassign_classifier_result_${titleText ? titleText : docid}_${status?.replace(' ', '_')}`,
        })
        setOpenAssign(true)
    }

    useEffect(() => {
        VisaDataHolder.getInstance().setCurrentPage(VISA_ROUTE_KEYS.BULK_UPLOAD_EDIT);
        fetchImages();
        const subscription = BackHandler.addEventListener(HARDWARE_BACK_PRESS, handleBack);
        return () => {
            subscription.remove();
        };
    }, []);

    const renderLoadingView = () => {
        return <LineLoader />;
    };

    const renderErrorView = () => {
        return <ErrorPage handleBackClick={handleBack} />;
    };

    const handleBackClick = () => {
        VisaNavigation.pop();
    };

    const closeShowException = (eventName = '') => {
        visaTrackClickEvent({
            eventName,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: eventName,
        })
        setOpenErrorSheet(false);
        setOpenAssign(false);
    };

    const openDelete = () => {
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.CLICK_REMOVE_IMAGE,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: NAME_OF_EVENTS.CLICK_REMOVE_IMAGE,
        })
        setOpenErrorSheet(true);
    };

    const deleteImage = async () => {
        request = {
            bookingId: bookingId,
            docId: docid
        }
        const response = await deleteClassifierData({ request });
        setViewState(VIEW_STATES.LOADING);
        if (!response) {
            setViewState(VIEW_STATES.ERROR);
            return;
        } else if (response) {
            visaTrackClickEvent({
                eventName: NAME_OF_EVENTS.CLICK_REMOVE_CONFIRM,
                eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
            })
            logPDTEvent({
                actionType: PDT_EVENT_TYPES.buttonClicked,
                eventValue: NAME_OF_EVENTS.CLICK_REMOVE_CONFIRM,
            })
            setViewState(VIEW_STATES.SUCCESS);
            showShortToast(ALERTS.DOCUMENT_REMOVED);
            setTriggerEffect(prev => !prev);
            VisaNavigation.pop();
        }
    };

    const visaOmnitureTracking = () => {
        const eventName = openAssign
            ? `reassign_popup_load_classifier_result_${titleText ? titleText : docid}_${status?.replace(' ', '_')}`
            : NAME_OF_EVENTS.CONFIRM_REMOVAL_POPUP_LOAD;
        visaTrackClickEvent({
            eventName,
            eventType: TYPE_OF_EVENTS.POPUP_LOAD,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.contentSeen,
            eventValue: eventName,
        })
    }

    const rightIcons = [
        {
            icon: getImagePath('deleteIcon'),
            onPress: openDelete,
            customStyles: {
                iconStyle: styles.iconSearch
            }
        }
    ];

    const renderSuccessView = () => {
        return (
            <View style={styles.mainContainer}>
                <HeaderWpr
                    clickHandler={() => handleBackClick()}
                    titleText={titleText}
                    rightIcons={rightIcons}
                />
                <View style={styles.documentWrapper}>
                    {selectedImages && (
                        <Image style={styles.imgSearch}
                            source={getImgUrl(selectedImages, docid)} resizeMode="contain" >

                        </Image>
                    )}
                </View>

                {classifier &&
                    <View style={styles.outercontainer}>
                        <View style={styles.bottomcontainer}>
                            <View style={styles.footer}>
                                <Text style={styles.warningText}> Action Needed</Text>
                                <ButtonWpr
                                    buttonWidth={"88%"}
                                    buttonText={"Assign to traveller"}
                                    onButtonPress={openAssignTraveller}
                                    buttonType="outline"
                                />
                            </View>
                        </View>
                    </View>
                }
                {openErrorSheet &&
                    <View>
                        <BottomSheetWpr
                            visible={openErrorSheet}
                            setVisible={setOpenErrorSheet}
                            isCrossIcon={true}
                            onDismiss={() => closeShowException(NAME_OF_EVENTS.CLICK_REMOVE_CLOSE)}
                            callTracking={visaOmnitureTracking}
                            eventName={PDT_SUB_PAGE_NAMES.CONFIRM_REMOVAL_POPUP}
                            children={
                                <View>
                                    <View style={styles.bottomsheetHeaderContainer}>
                                        <Text style={styles.header}>Confirm Removal</Text>
                                    </View>
                                    <Text style={styles.bottomsheetTitle}>
                                        Are you sure you want to remove this document?
                                    </Text>
                                    <View style={styles.container}>
                                        <ButtonWpr
                                            buttonText={'CANCEL'}
                                            buttonType="outline"
                                            buttonWidth={"46%"}
                                            onButtonPress={() => closeShowException(NAME_OF_EVENTS.CLICK_REMOVE_CANCEL)}
                                        />
                                        <ButtonWpr
                                            customStyle={{
                                                buttonStyle: {
                                                    borderColor: colors.red,
                                                    ...paddingStyles.pv16
                                                },
                                                buttonTextStyle: {
                                                    color: colors.red,
                                                    ...fontStyles.labelLargeBold
                                                }
                                            }}
                                            buttonText={'REMOVE'}
                                            buttonType="outline"
                                            buttonWidth={"46%"}
                                            onButtonPress={() => deleteImage()}
                                        />
                                    </View>
                                </View>
                            }
                        />
                    </View>
                }
                {openAssign &&
                    <View>
                        <BottomSheetWpr
                            visible={openAssign}
                            setVisible={setOpenAssign}
                            isCrossIcon={true}
                            onDismiss={() => closeShowException(`click_reassign_close_${titleText ? titleText : docid}_${status?.replace(' ', '_')}`)}
                            callTracking={visaOmnitureTracking}
                            eventName={PDT_SUB_PAGE_NAMES.ASSIGN_TRAVELLERS}
                            children={
                                <View>
                                    <List
                                        itemList={uploadedData.travellers}
                                        docList={uploadedData.documents}
                                        uploadedData={uploadedData}
                                        assignDoc={assignDoc}
                                        setOpenAssign={setOpenAssign}
                                        status={status}
                                        assignType={'Assign to traveller'}
                                        updateDoc={false}
                                        bookingId={bookingId}
                                    />
                                </View>
                            }
                        />
                    </View>
                }
            </View>
        )
    };

    switch (viewState) {
        case VIEW_STATES.LOADING:
            return renderLoadingView();
        case VIEW_STATES.SUCCESS:
            return renderSuccessView();
        case VIEW_STATES.ERROR:
            return renderErrorView();
    }
}


export default EditScreen;

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: colors.lightGray2,
    },
    tabsDisplay: {
        height: "100%",
        flex: 1,
        ...marginStyles.mb30,
        ...marginStyles.mt16
    },
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...paddingStyles.pa16,
        backgroundColor: colors.white,
        shadowColor: colors.white,
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 1,
        shadowRadius: 1,
        elevation: 9,
    },
    containerRight: {
        alignItems: 'flex-end', // Align children to the right
    },
    iconSearch: {
        height: 24,
        width: 24
    },
    documentWrapper: {
        ...marginStyles.mt40,
        ...paddingStyles.pt40,
        backgroundColor: colors.lightGray2,
        borderColor: colors.transparent,
    },
    imgSearch: {
        height: 368,
    },
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...paddingStyles.pa16,
        backgroundColor: colors.white,
        shadowColor: colors.white,
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 1,
        shadowRadius: 1,
        elevation: 9,

    },
    bottomsheetTitle: {
        ...fontStyles.labelMediumBold,
        color: colors.gray,
        ...marginStyles.mt10,
    },
    bottomcontainer: {
        position: "absolute",
        bottom: 0,
        width: "100%",
        ...marginStyles.mb0
    },
    footer: {
        width: "100%",
        backgroundColor: "#fff", // Optional: Add background color
        padding: 10,
        alignItems: 'center',
    },
    outercontainer: {
        flex: 1,
    },
    warningText: {
        ...fontStyles.labelSmallBold,
        color: colors.yellow,
        ...paddingStyles.pb16,
    },
    bottomsheetHeaderContainer: {
        ...paddingStyles.pb16,
        borderBottomWidth: 1,
        borderColor: colors.grayBorder,
    },
    header: {
        ...fontStyles.labelMediumBold,
        color: colors.black,
    },
})