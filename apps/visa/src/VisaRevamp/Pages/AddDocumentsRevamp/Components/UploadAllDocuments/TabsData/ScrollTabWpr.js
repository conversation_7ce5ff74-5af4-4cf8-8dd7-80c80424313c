import React from 'react'
import { colors } from '../../../../../Styles/colors';
import { fontStyles } from '../../../../../Styles/fonts';
import {StyleSheet, View, Text} from 'react-native';
import TabSectionScroller from '@Frontend_Ui_Lib_App/TabSectionScroller/lib/TabSectionScroller';

const ScrollTabWpr = ({
    tabsListData,
    compsList
}) => {
    return (
        <View style={Platform.OS === 'web' ? { height: Dimensions.get('window').height } : {flex: 1}}>
            <TabSectionScroller
                tabsListData={tabsListData}
                compsList={compsList}
                tabsContainerHPadding={8}
                customTabsWprStyles={{
                    height:32,
                    alignItems: "center",
                    justifyContent: "space-between",
                    width: "100%",
                    elevation: 5,
                    shadowRadius: 5,
                    shadowOpacity: 1.0,
                    marginBottom: 4
                }}
                customTabStyles={{
                   marginHorizontal: 38,
                   paddingTop:2
                }}
                customTabTxtStyles={{
                    color: colors.defaultTextColor,
                    ...fontStyles.labelBaseRegular,
                    fontSize: 14
                }}
                customActiveTabTxtStyles={{
                    color: colors.primaryBlue,
                    ...fontStyles.labelMediumBold,
                    fontSize: 14,
                }}
                customActiveTabStyles={{
                    borderBottomWidth: 2,
                    borderColor: colors.primaryBlue,
                    ...fontStyles.labelMediumBold,
                }}
                customCompWprStyles={{
                    width: '100%'
                }}
            />
        </View>
    );
}

export default ScrollTabWpr;