import React, { useState, useEffect } from 'react';
import { Image, View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import styles from './styles';
import { downloadImage } from '../../../../AddDocuments/VisaImageDownloader';
import { colors } from "../../../../../Styles/colors";
import { fontStyles } from '../../../../../Styles/fonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { getImagePath } from '../../../../../Utils/VisaUtils';

const PhotoUploadCard = ({
    item,
    title,
    isEditable,
    photoToUpload,
    handleSnackBar,
    bottomText,
    onAssignableClick,
    classifier,
    isError,
    onErrorClick,
    onEdit
}) => {

    const [toggle, setToggle] = useState(false);
    const [selectedImages, setSelectedImages] = useState({});
    const [error, setError] = useState(false);

    const closeBottomsheet = () => {
        setToggle(false);
        handleSnackBar(true)
    }

    const getIconUri = () => {
        if (title && title.includes('pdf')) {
            return getImagePath('pdfImage');
        }
        else {
            return getImagePath('picImage');
        }
    }

    async function fetchImages(photoToUpload) {
        if (photoToUpload) {
            downloadImage(photoToUpload, {
                onImageLoading: () => { },
                onImageLoadingFailed: (error) => { },
                onImageQueued: () => { },
                onImageLoaded: (base64data) => {
                    setSelectedImages(base64data);
                }
            });
        }
    };

    useEffect(() => {
        fetchImages(photoToUpload);
    }, []);

    return (

        <View style={styles.cardContainer}>
            <View style={styles.headerCardContainer}>
                <Image style={stylesPhoto.imageStyle} source={getIconUri()} resizeMode="contain" />
                <Text style={styles.headerCardTitle} numberOfLines={1} ellipsizeMode="tail">{title}</Text>
                {isEditable === true ? <Text style={stylesPhoto.editableText} onPress={() => onEdit()}>Edit</Text> : <></>}
            </View>
            {selectedImages &&
                selectedImages.length > 0  &&
                <View style={styles.photoContainer}>
                    <Image
                        source={{ uri: selectedImages }}
                        style={styles.photoCardImage}
                        resizeMode="contain"
                    />
                </View>
            }
            {classifier &&
                <View>
                    {!isError &&
                        <View style={styles.footerCardContainer}>
                            <TouchableOpacity style={styles.assignableFooterContainer} onPress={() => onAssignableClick()}>
                                <Text style={stylesPhoto.editableText}> {bottomText} </Text>
                            </TouchableOpacity>
                        </View>
                    }
                    {isError === true &&
                        <View style={styles.footerCardContainer}>
                            <Text style={stylesPhoto.errorFound}> Errors found </Text>
                            <Text style={stylesPhoto.editableText} onPress={() => onErrorClick()}> View </Text>
                        </View>}
                    {isError === true && title === 'Documents Rejected' &&
                        <View style={styles.footerCardContainer}>
                            <TouchableOpacity style={styles.assignableFooterContainer} onPress={() => onErrorClick()}>
                                <Text style={stylesPhoto.editableText}> Errors found </Text>
                            </TouchableOpacity>
                        </View>}
                </View>
            }
        </View>

    )
}

export default PhotoUploadCard;

const stylesPhoto = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: colors.lightGray2,
    },
    tabsDisplay: {
        height: "100%",
        flex: 1,
        ...marginStyles.mb30,
        ...marginStyles.mt16
    },
    imageStyle: {
        width: 20,
        height: 20,
    },
    editableText: {
        ...fontStyles.labelSmallBold,
        color: colors.primaryBlue,
    },
    errorFound: {
        ...fontStyles.labelSmallBold,
        color: colors.yellow,
    },
});