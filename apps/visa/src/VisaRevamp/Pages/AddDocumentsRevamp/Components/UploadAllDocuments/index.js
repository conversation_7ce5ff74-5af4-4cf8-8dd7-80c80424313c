import React from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { fontStyles } from '../../../../Styles/fonts';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing';
import { borderRadius } from '../../../../Styles/borderRadius';
import { colors } from '../../../../Styles/colors';
import { IMAGE_KEYS, getImagePath } from '../../../../Utils/VisaUtils';
import { STRING_MAP, TEXT_LINK_CTA } from '../../../../textStrings';

/* Icons */
const backIcon = require('@mmt/legacy-assets/src/back.webp');
const arrowRightIcon = require('@mmt/legacy-assets/src/ic_arrow_oneway.webp');

/* Components */
import LinearGradient from 'react-native-linear-gradient';
import ImageHolder from '../../../../Components/Common/ImageHolder';

const UploadAllDocuments = ({ onapply, setTriggerEffect, bulkUploadPersuasion = {} }) => {
  const { header = '', subHeader = '' } = bulkUploadPersuasion || {};

  return (
    <TouchableOpacity onPress={() => onapply()}>
      <LinearGradient
        colors={['#f3f8fe', '#e8f1fe']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 0.5 }}
        style={styles.sectionContainer}
      >
        <View style={styles.imageContainer}>
          <ImageHolder
            imageUrl={getImagePath(IMAGE_KEYS.buldUploadIcon)?.uri}
            style={styles.documentIconStyle}
          />
        </View>
        <View style={styles.contentWrapper}>
          <Text style={styles.titleText}>
            {header || STRING_MAP.ADD_DOC_BULK_UPLOAD_PERSUASION_TITLE}
          </Text>
          <Text style={styles.subText}>
            {subHeader || STRING_MAP.ADD_DOC_BULK_UPLOAD_PERSUASION_DESC}
          </Text>
          <TouchableOpacity style={styles.linkWrapper} onPress={() => onapply(setTriggerEffect)}>
            <Text style={styles.linkText}>{TEXT_LINK_CTA.ADD_DOC_BULK_UPLOAD_ADD_NOW}</Text>
            <Image source={arrowRightIcon} style={styles.arrowIconStyle} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  sectionContainer: {
    borderWidth: 1,
    borderColor: '#cce8ff',
    ...paddingStyles.pa16,
    flexDirection: 'row',
    margin: 16,
    ...borderRadius.borderRadius16,
  },
  imageContainer: {
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    ...marginStyles.mr16,
    ...borderRadius.borderRadius16,
    ...paddingStyles.pa8,
  },
  documentIconStyle: {
    width: 48,
    height: 46,
  },
  contentWrapper: {
    flex: 1,
    ...marginStyles.mt4,
  },
  linkWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowIconStyle: {
    width: 12,
    height: 9,
    tintColor: colors.primaryBlue,
    ...marginStyles.mt4,
  },
  titleText: {
    ...fontStyles.labelMediumBlack,
    color: colors.black,
    ...marginStyles.mb2,
  },
  subText: {
    ...fontStyles.labelSmallRegular,
    color: colors.gray,
    ...marginStyles.mb6,
  },
  linkText: {
    ...fontStyles.labelSmallBold,
    color: colors.primaryBlue,
    ...marginStyles.mr4,
  },
});

export default UploadAllDocuments;
