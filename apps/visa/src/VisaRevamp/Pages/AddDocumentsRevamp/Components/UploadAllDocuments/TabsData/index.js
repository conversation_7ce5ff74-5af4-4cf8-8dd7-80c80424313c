import React from 'react';
import { View, Text } from 'react-native';
import TabSection from './TabSection';
import styles from './styles';
import ScrollTabWpr from './ScrollTabWpr';
import { getImagePath } from '../../../../../Utils/VisaUtils';
import { get } from 'lodash';


const getIconUri = (iconName) => {
    switch (iconName) {
        case 'Done':
            return getImagePath('submitImage');
        case 'Action Needed':
            return getImagePath('warningImage');
        case 'Rejected':
            return getImagePath('errorImage');
    }
}

const getTravellerName = (uploadedData, paxid) => {
    let travellerName = '';
    uploadedData.travellers.forEach((traveller) => {
        if (traveller.paxId === paxid) {
            travellerName = traveller.name;
        }
    });
    return travellerName;
}

const PageComponent = ({ data, handleSnackBar, uploadedData, classifier }) => {
    if (data && data.documents && data.documents.length > 0) {
        return (
            <>
                {
                    <TabSection
                        title={data.title}
                        subtitle={data.subTitle}
                        classifier={classifier}
                        iconUri={getIconUri(data.header)}
                        cardsData={data.documents}
                        travellerId={''}
                        status={data.header}
                        uploadedData={uploadedData}
                        bookingId={uploadedData.bookingId}
                    />
                }
            </>
        )
    }
    else if (data && data.travellers && data.travellers.length > 0) {
        return (
            <>
                {data.travellers.map((traveller, index) => (
                    <TabSection
                        key={index}
                        title={getTravellerName(uploadedData, traveller.paxId) + ' (' + traveller.documents.length +')' }
                        classifier={true}
                        iconUri={getIconUri('Done')}
                        cardsData={traveller.documents}
                        travellerId={traveller.paxId}
                        status={data.header}
                        uploadedData={uploadedData}
                        bookingId={uploadedData.bookingId}
                    />
                ))}
            </>
        );
    }
};

export default function Tabs({
    handleSnackBar, uploadedData, classifier
}) {

    const tabs = uploadedData.Response;
    let tabTitles = [];
    let tabItems = []

    Object.values(tabs).forEach((val, id) => {
        let type = val.header;
        if (val.header === 'Action Needed'){
            type = 'Action'
        }
        let docLen = 0;
        if ((val.travellers && val.travellers.length > 0)) {
            val.travellers.forEach((traveller) => {
                docLen = docLen + traveller.documents.length;
            });
            let tab ={
                "tabTitle": val.header,
                "type":type,
                "count":docLen
            }
            tabTitles.push(tab)
            tabItems.push(val);
        }
        else if ((val.documents && val.documents.length > 0)) {
            docLen = val.documents.length;
            let tab ={
                "tabTitle": val.header,
                "type":type,
                "count":docLen
            }
            tabTitles.push(tab)
            tabItems.push(val);
        } else {
            const keys = Object.keys(tabs);
            const key = keys[id];
            if (key === 'done') {
                let tab ={
                    "tabTitle": "Done",
                    "type":"Done",
                    "count":0
                }
                tabTitles.push(tab);
            } else if (key === 'actionNeeded') {
                let tab ={
                    "tabTitle": "Action Needed",
                    "type":"Action",
                    "count":0
                }
                tabTitles.push(tab);
            } else if (key === 'rejected') {
                let tab ={
                    "tabTitle": "Rejected",
                    "type":"Rejected",
                    "count":0
                }
                tabTitles.push(tab);
            }
        }
    });


    tabTitles1 = tabTitles.map((tab, index) => (
        <View key={index} style={styles.tabTitleContainer}>
          <Text style={styles.tabTitle}>{tab.tabTitle}</Text>
          <View style={[styles.bgBack, styles[`bg${tab.type}`]]}>
            <Text style={styles.nosText}>{tab.count}</Text>
          </View>
        </View>
      ));

    const GetPageCompProps = (id) => {
        return {
            handleSnackBar: handleSnackBar,
            data: id >= 0 ? tabItems[id] : {},
            uploadedData: uploadedData,
            classifier: classifier
        }
    }

    const createList = (tabItems) => {
        var list = []
        tabTitles.map((item, index) => {
            let mappedItems = false;
            let ix = -1;
            tabItems.map((tabItem, tabInx) => {
                if (tabItem.header === item.tabTitle) {
                    mappedItems = true;
                    ix = tabInx;
                }
            })
            if (mappedItems) {
                list.push(<PageComponent {...GetPageCompProps(ix)} />)
            } else {
                list.push(<PageComponent {...GetPageCompProps(-1)} />)
            }
        })
        return list;
    }

    return (
        <View style={styles.tabsDisplay}>
            <ScrollTabWpr
                tabsListData={tabTitles1}
                compsList={createList(tabItems)}
            />
        </View>
    )
}