import React, { useState, useEffect } from 'react';
import { Image, ScrollView, View, Text, StyleSheet } from 'react-native';
import PhotoUploadCard from './PhotoUploadCard';
import styles from './styles';
import { isEmpty, set } from 'lodash';
import BottomSheetWpr from '../../../../../Components/Common/BottomSheetWpr';
import List from './List';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../../Navigation';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { logPDTEvent } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';
import { PDT_SUB_PAGE_NAMES } from 'apps/visa/src/VisaRevamp/constants';

export default function TabSection({
    title,
    subtitle,
    cardsData,
    iconUri,
    handleSnackBar,
    classifier,
    travellerId = '',
    status = '',
    uploadedData,
    bookingId, visaAddDocumentData
}) {

    const [openAssign, setOpenAssign] = useState(false);
    const [openError, setErrorAssign] = useState(false);
    const [assignType, setAssignType] = useState();
    const [assignDoc, setAssignDoc] = useState({});
    const [editedDocId, setEditedDocId] = useState(0);
    const [displayData, setDisplayData] = useState([]);
    const [triggerEffect, setTriggerEffect] = useState(false);

    useEffect(() => {
        setDisplayData(cardsData);
    }, [cardsData]);

    useEffect(() => {
        if (triggerEffect) {
            let cardsData = displayData;
            if (classifier){
                cardsData = cardsData.filter((data) =>
                    data.docId != editedDocId
                );
            }else{
                cardsData = cardsData.filter((data) =>
                    data.documentId != editedDocId
                );
            }
            setDisplayData(cardsData);
            setTriggerEffect(false);
            if (classifier && cardsData && cardsData.length > 0) {
                VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM, {
                    bookingId: bookingId,
                    visaAddDocumentData: visaAddDocumentData,
                    state: 'loading'
                });
            } else {
                VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD, {
                    bookingId: bookingId,
                    visaAddDocumentData: visaAddDocumentData
                });
            }
        }
    }, [triggerEffect]);


    const getEventName = (assignType, item) => {
        const docId = item.documentId ? item.documentId : item.docId;
        const docType = item.documentType ? item.documentType : item.docType;
        switch (assignType) {
            case 'Identify Document':
                return {
                  CLICK: `click_identify_doc_${travellerId}_${docType ? docType : docId}_${status?.replace(' ', '_')}`,
                  POPUP: `identify_document_popup_load_${travellerId}_${docType ? docType : docId}_${status?.replace(' ', '_')}`,
                  CLOSE: `click_close_identify_${travellerId}_${docType ? docType : docId}_${status?.replace(' ', '_')}`,
                  SUBPAGENAME: `identify_doc_result`,
                }
            case 'Assign to traveller':
                return {
                  CLICK: `click_reassign_classifier_result_${travellerId}_${docType ? docType : docId}_${status?.replace(' ', '_')}`,
                  POPUP: `reassign_popup_load_classifier_result_${travellerId}_${docType ? docType : docId}_${status?.replace(' ', '_')}`,
                  CLOSE: `click_reassign_close_${travellerId}_${docType ? docType : docId}_${status?.replace(' ', '_')}`,
                  SUBPAGENAME: `reassign_classifier_result`,
                }
            case 'Errors found':
                return {
                  CLICK: `view_error_rejected_clicked`,
                  POPUP: `rejected_doc_error_popup_load_${travellerId}_${docType ? docType : docId}_<error_heading>`,
                  SUBPAGENAME: `error_rejected_doc_popup`,
                  CLOSE: `error_rejected_doc_close_click_${travellerId}_${docType ? docType : docId}_<error_heading>`,
                }
            default:
                return '';
        }
    }

    const closeShowException = () => {
        visaTrackClickEvent({
            eventName: getEventName(assignType, assignDoc).CLOSE,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: getEventName(assignType, assignDoc).CLOSE,
        })
        setOpenAssign(false);
        setErrorAssign(false);
    };

    const callClickEvent = (type, item) => {
        visaTrackClickEvent({
            eventName: getEventName(type, item).CLICK,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: getEventName(type, item).CLICK,
        })
    }

    const assignClick = (type, item) => {
        callClickEvent(type, item);
        setOpenAssign(true);
        setAssignType(type);
        setAssignDoc(item);
    };

    const onErrorClick = (type, item) => {
        callClickEvent(type, item);
        setErrorAssign(true);
        setAssignDoc(item);
    };

    const onEdit = (item) => {
        setTriggerEffect(false);
        setEditedDocId(item.documentId);
        const docId = item.documentId ? item.documentId : item.docId;
        const docType = item.documentType ? item.documentType : item.docType;
        const docUrl = item.documentThumbnailUrl ? item.documentThumbnailUrl : item.url;
        const eventName = classifier
          ? `click_edit_classifier_result_${travellerId}_${docType ? docType : docId}_${status?.replace(' ', '_')}`
          : `click_edit_${travellerId}_${docType ? docType : docId}`;
        visaTrackClickEvent({
          eventName,
          eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        });
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          eventValue: NAME_OF_EVENTS.CLICK_EDIT,
        });
        VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD_EDIT, {
            bookingId: bookingId,
            docid: docId,
            titleText: docType,
            url: docUrl,
            classifier: classifier,
            uploadedData: uploadedData,
            assignDoc: item,
            status: status,
            setTriggerEffect: setTriggerEffect,
        });
    }

    const visaOmnitureTracking = () => {
        visaTrackClickEvent({
            eventName: getEventName(assignType, assignDoc).POPUP,
            eventType: TYPE_OF_EVENTS.POPUP_LOAD,
        })
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.contentSeen,
          eventValue: getEventName(assignType, assignDoc).POPUP,
        })
    }

    const getName = (item, classifier) => {
        if (classifier) {
            return item.docType && !isEmpty(item.docType) ? item.docType : 'Unidentified';
        }
        else {
            return item.documentName;
        }
    }

    const getAssignableFooter = (item, classifier) => {
        if (classifier) {
            if (title === 'Documents Rejected' && item.callToText === 'View Errors') {
                return 'Errors found';
            }
            if (isEmpty(item.docType) || item.docType === 'Unidentified') {
                return 'Identify Document';
            } else if ((item.docType) && item.paxId === 0) {
                return 'Assign to traveller';
            } else if (item.errors && item.errors.length > 0) {
                return 'Errors found';
            }
            else {
                return '';
            }
        } else {
            return '';
        }
    }

    return (
        <View style={styles.container}>
            <View style={styles.sectionHeader}>
                <Image
                    source={iconUri}
                    style={styles.sectionHeaderIcon}
                />
                <View>
                    <Text style={styles.title}>{title}</Text>
                    <Text style={styles.subtitle}>{subtitle}</Text>
                </View>
            </View>
            <ScrollView>
                <View style={styles.cardsLayout}>
                    {
                        displayData.map((item, index) => (
                            <View key={index} style={styles.tabSectionColumn}>
                                <PhotoUploadCard
                                    bookingId={bookingId}
                                    item={item}
                                    title={getName(item, classifier)}
                                    photoToUpload={item.documentThumbnailUrl ? item.documentThumbnailUrl : item.thumbnailUrl}
                                    isEditable={true}
                                    onAssignableClick={() => assignClick(getAssignableFooter(item, classifier), item)}
                                    classifier={classifier}
                                    bottomText={getAssignableFooter(item, classifier)}
                                    isError={getAssignableFooter(item, classifier) === 'Errors found' ? true : false}
                                    onErrorClick={() => onErrorClick(getAssignableFooter(item, classifier), item)}
                                    setTriggerEffect={setTriggerEffect}
                                    onEdit={() => onEdit(item)}
                                />
                            </View>
                        ))}
                </View>
            </ScrollView>
            {openAssign &&
                <View>
                    <BottomSheetWpr
                        visible={openAssign}
                        setVisible={setOpenAssign}
                        isCrossIcon={true}
                        onDismiss={closeShowException}
                        callTracking={visaOmnitureTracking}
                        eventName={getEventName(assignType, assignDoc).SUBPAGENAME}
                        children={
                            <View>
                                <List
                                    itemList={uploadedData.travellers}
                                    docList={uploadedData.documents}
                                    uploadedData={uploadedData}
                                    assignDoc={assignDoc}
                                    setOpenAssign={setOpenAssign}
                                    assignType={assignType}
                                    status={status}
                                    updateDoc={assignType === 'Identify Document' ? true : false}
                                    bookingId={bookingId}
                                />
                            </View>
                        }
                    />
                </View>
            }
            {openError &&
                <View>
                    <BottomSheetWpr
                        visible={openError}
                        setVisible={setErrorAssign}
                        isCrossIcon={true}
                        onDismiss={closeShowException}
                        callTracking={visaOmnitureTracking}
                        eventName={getEventName(getAssignableFooter(assignDoc, classifier), assignDoc).SUBPAGENAME}
                        containerStyles={{ height: 700 }}
                        children={
                            <View>
                                <ScrollView showsVerticalScrollIndicator={false}>
                                    <List
                                        itemList={uploadedData.travellers}
                                        uploadedData={uploadedData}
                                        assignDoc={assignDoc}
                                        assignType={'Errors'}
                                        setErrorAssign={setErrorAssign}
                                        status={status}
                                        errorScreen={true}
                                        url={assignDoc.documentUrl ? assignDoc.documentUrl : assignDoc.url}
                                    />
                                </ScrollView>
                            </View>
                        }
                    />
                </View>
            }
        </View>
    )


}