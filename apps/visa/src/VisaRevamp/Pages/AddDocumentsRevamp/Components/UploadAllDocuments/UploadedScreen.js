import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, BackHandler } from 'react-native';
import { HARDWARE_BACK_PRESS, VIEW_STATES } from '../../../../constants';
import { getClassifierData, uploadClassifierFile } from '../../../../Utils/NetworkUtils';
import { colors } from '../../../../Styles/colors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { getImagePath } from '../../../../Utils/VisaUtils';
import { fontStyles } from '../../../..//Styles/fonts';
import arrowRight from '@mmt/legacy-assets/src/arrow_blue_right.webp'
import { launchImageLibrary } from 'react-native-image-picker';
import { openPhoneDocGallery, cameraRollNode } from '../../../../Utils/uploadutils';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { DocumentPickerOptions, pick, types } from '@react-native-documents/picker';
import { logPDTEvent, visaPdtEventsInitilizer } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';
import store from 'packages/legacy-commons/AppState/Store';
import { updateSubPageNameAction } from 'apps/visa/src/VisaRevamp/Actions/pdtLoggerActions';
import { PAGE_HEADERS, STRING_MAP } from '../../../../textStrings';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';

/* Components */
import BottomSheetWpr from '../../../../Components/Common/BottomSheetWpr';
import ErrorPage from '../../../../Components/Common/ErrorPage';
import BulkUploader from '../../../../Components/Common/Loader/bulkuploader';
import TabSection from './TabsData/TabSection';
import HeaderWpr from '../../../../Components/Common/HeaderWpr';
import ButtonWpr from '../../../../Components/Common/ButtonWpr';
import VisaDataHolder from 'apps/visa/src/VisaRevamp/Utils/VisaDataHolder';
import ProgressBar from '@Frontend_Ui_Lib_App/ProgressBar';
import LineLoaderComp from '../../../../Components/Common/Loader/LineLoader';


const UploadedScreen = ({ bookingId, visaAddDocumentData, setTriggerEffect }) => {

    const [viewState, setViewState] = useState(VIEW_STATES.LOADING);
    const [uploadedData, setUploadedData] = useState({});
    const [openBulkUploadDetail, setOpenBulkUploadDetail] = useState(false);

    const handleBack = () => {
        VisaNavigation.pop();
        return true;
    };

    async function fetchData() {
        const response = await getClassifierData({ bookingId });
        if (!response) {
            setViewState(VIEW_STATES.ERROR);
            return;
        } else if (response) {
            const eventName = `uploaded_image_${response?.documents?.length}`;
            visaTrackClickEvent({ eventName, eventType: TYPE_OF_EVENTS.PAGE_LOAD });
            visaPdtEventsInitilizer();
            setUploadedData(response);
            setViewState(VIEW_STATES.SUCCESS);
        }
    };

    useEffect(() => {
        VisaDataHolder.getInstance().setCurrentPage(VISA_ROUTE_KEYS.BULK_UPLOAD);
        fetchData();
        const subscription = BackHandler.addEventListener(HARDWARE_BACK_PRESS, handleBack);
        return () => {
            subscription.remove();
        };
    }, []);

    const renderLoadingView = () => {
        return <BulkUploader loaderText={"Adding Documents"} />;
    };

    const renderErrorView = () => {
        return <ErrorPage handleBackClick={handleBack} handleRefreshClick={fetchData()} />;
    };

    const renderBulkLoadingView = () => {
        return (
            <View style={styles.centeredView}>
                <View style={styles.darkenedBackground}>
                    <View style={styles.modalView}>
                        <Image source={getImagePath('uploadDocImage')} style={styles.iconStyle} />
                        <Text style={styles.modalText}>Adding Documents</Text>
                        <LineLoaderComp/>
                    </View>
                </View>
            </View>
        );
    };

    const handleBackClick = () => {
        VisaNavigation.pop();
    };

    const openConfirmScreen = () => {
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.CLICK_CONTINUE_BULK_IMAGE_UPLOAD,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: NAME_OF_EVENTS.CLICK_CONTINUE_BULK_IMAGE_UPLOAD,
        })
        VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM, {
            bookingId: bookingId,
            visaAddDocumentData : visaAddDocumentData
        });
    };

    const toggleModal = () => {
        setOpenBulkUploadDetail(!openBulkUploadDetail);
    };

    const closeShowBulkUpload = () => {
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_CLOSE,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        });
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_CLOSE,
        })
        setOpenBulkUploadDetail(false);
    }

    const visaOmnitureTracking = () => {
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD,
            eventType: TYPE_OF_EVENTS.POPUP_LOAD,
        })
        store.dispatch(updateSubPageNameAction(NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD));
        logPDTEvent({
          actionType: PDT_EVENT_TYPES.contentSeen,
          eventValue: NAME_OF_EVENTS.SELECT_DOC_TYPE_POPUP_LOAD,
        })
    }

    const bulkUpload = () => {
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.CLICK_ADD_MORE_BULK_UPLOAD,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: NAME_OF_EVENTS.CLICK_ADD_MORE_BULK_UPLOAD,
        })
        openBulkUpload()
    }

    const openBulkUpload = () => {
        if (
            visaAddDocumentData.bulkUploadEnabled &&
            visaAddDocumentData.bulkUploadPdfEnabled &&
            visaAddDocumentData.maximumDocumentsAllowed > 0
        ) {
            setOpenBulkUploadDetail(true);
        } else {
            openGallery();
        }
    }

    const openGallery = async () => {
        let bulkUploadLimit = visaAddDocumentData.maximumDocumentsAllowed - uploadedData.documents.length;
        openPhoneGallery(bulkUploadLimit, visaAddDocumentData.bookingID, visaAddDocumentData);
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.SELECT_ADD_IMAGE_BULK_UPLOAD,
            eventType: TYPE_OF_EVENTS.CARD_CLICK,
        });
        logPDTEvent({
            eventValue: NAME_OF_EVENTS.SELECT_ADD_IMAGE_BULK_UPLOAD,
            actionType: PDT_EVENT_TYPES.buttonClicked,
        })
    }

    const openPhoneGallery = (selectionLimit, bookingid, visaAddDocumentData) => {
        const imageGalleryParams = {
            mediaType: 'photo',
            selectionLimit: selectionLimit
        };
        launchImageLibrary(imageGalleryParams, async (res) => {
            return selectImages(res.assets || [], bookingid, visaAddDocumentData);
        });
    }

    const selectImages = (images, bookingid) => {
        let selectedImages = [];
        images.forEach((image) => {
            const compatNode = cameraRollNode(null, image);
            let request = {};
            request.data = {
                uri: image.uri,
                type: image.type,
                name: image.fileName,
            };
            request.documentName = image.fileName;
            request.format = image.type;
            request.bookingId = bookingid;
            setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
            uploadClassifierFile(request, true).then((response) => {
                selectedImages.push(compatNode);
                closeShowBulkUpload();
                setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
                if (selectedImages.length === images.length) {
                    setViewState(VIEW_STATES.SUCCESS);
                    VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD, {
                        bookingId: visaAddDocumentData.bookingID,
                        visaAddDocumentData: visaAddDocumentData,
                        setTriggerEffect: setTriggerEffect,
                    });
                }
            });
        });
    };

    const openDocGallery = () => {
        let bulkUploadLimit = visaAddDocumentData.maximumDocumentsAllowed - uploadedData.documents.length;
        openPhoneDocGallery(bulkUploadLimit, visaAddDocumentData.bookingID);
        visaTrackClickEvent({
          eventName: NAME_OF_EVENTS.SELECT_ADD_DOC_BULK_UPLOAD,
          eventType: TYPE_OF_EVENTS.CARD_CLICK,
        });
        logPDTEvent({
            eventValue: NAME_OF_EVENTS.SELECT_ADD_DOC_BULK_UPLOAD,
            actionType: PDT_EVENT_TYPES.buttonClicked,
        })
      }

      const openPhoneDocGallery = async (selectionLimit, bookingid) => {
        let selectedImages = [];
    
        pick({
          type: [types.pdf],
          allowMultiSelection: true,
        })
          .then((results) => {
            if (results.length > selectionLimit) {
              Alert.alert('Limit Exceeded', `You can only select up to ${selectionLimit} documents.`);
            } else {
              for (const res of results) {
                let request = {};
                request.data = {
                  uri: res.uri,
                  type: res.type,
                  name: res.name,
                };
                request.documentName = res.name;
                request.format = res.type;
                request.bookingId = bookingid;
                setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
                uploadClassifierFile(request, true).then((response) => {
                  selectedImages.push(res);
                  closeShowBulkUpload();
                  setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
                  if (selectedImages.length === results.length) {
                    setViewState(VIEW_STATES.SUCCESS);
                    VisaNavigation.push(VISA_ROUTE_KEYS.BULK_UPLOAD, {
                      bookingId: visaAddDocumentData.bookingID,
                      visaAddDocumentData: visaAddDocumentData
                    });
                  }
                });
              }
            }
          })
          .catch((error) => { });
      };

    const createCheckList = () => {
        let docList = []
        visaAddDocumentData.passengers.map((item) => {
            item.catStatusList.map((doc) => {
                doc.docStatusList.map((document) => {
                    let included = false
                    docList.map((doc) => {
                        if (doc.docType == document.type) {
                            included = true
                        }
                    })
                    if (!included) {
                        docList.push({
                            "docType": document.type,
                            "docName": document.name,
                            "sampleDocUrl": document.sampleDocUrl,
                            "subTitle": '',
                            "detailedDescription": "",
                            "order": docList.length + 1,
                            "descriptionHeading": "",
                            "showSampleDocImage": document.showSampleDocImage,
                            "showDetailedDescription": false,
                            "showSampleDocImageInDetailedDescription": document.showSampleDocImageInDetailedDescription,
                            "showInformativeDescription": document.showInformativeDescription,
                        })
                    }
                })
            })
        })

        let sampleData = {
            "status": 1,
            "countryName": visaAddDocumentData.country,
            "docStatusList": docList
        }
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.CLICK_DOCUMENT_CHECKLIST,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: NAME_OF_EVENTS.CLICK_DOCUMENT_CHECKLIST,
        })
        VisaNavigation.push(VISA_ROUTE_KEYS.SAMPLE_DOC, {
            sampleData: sampleData,
        });

    }

    function UploadRow({
        icon,
        content,
        onPress
    }) {
        return (
            <TouchableOpacity onPress={() => onPress()}>
                <View style={styles.uploadRow}>
                    <View style={styles.uploadRowColumn}>
                        <Image
                            source={icon}
                            style={styles.icon}
                        />
                        <Text style={styles.uploadRowText}>{content}</Text>
                    </View>
                    <TouchableOpacity style={styles.arrowContainer} onPress={() => onPress()}>
                        <Image
                            source={arrowRight}
                            style={styles.icon}
                        />
                    </TouchableOpacity>
                </View>
            </TouchableOpacity>
        )
    }

    const renderSuccessView = () => {
        return (
            <View style={styles.mainContainer}>
                <HeaderWpr
                    clickHandler={() => handleBackClick()}
                    titleText={PAGE_HEADERS.BULK_UPLOAD_SCREEN}
                    actionTextData="Document Checklist"
                    actionHandler={() => createCheckList()}
                    wrapperStyle={{
                        ...getPlatformElevation(0)
                    }}
                />
                <View style={styles.tabsDisplay}>
                    <TabSection
                        title={"Uploaded" + " (" + uploadedData.documents.length + ")"}
                        iconUri={getImagePath('submitImage')}
                        cardsData={uploadedData.documents}
                        classifier={false}
                        bookingId={bookingId}
                        setTriggerEffect={setTriggerEffect}
                        visaAddDocumentData={visaAddDocumentData}
                    />
                </View>
                <View style={styles.footer}>
                    {uploadedData && uploadedData.documents.length < visaAddDocumentData.maximumDocumentsAllowed &&
                        <View style={styles.container}>
                            <ButtonWpr
                                buttonWidth={"46%"}
                                buttonText={"+ Add More"}
                                onButtonPress={() => bulkUpload(visaAddDocumentData, setOpenBulkUploadDetail)}
                                buttonType="outline" />
                            <ButtonWpr
                                buttonWidth={"46%"}
                                buttonText={"CONTINUE"}
                                onButtonPress={openConfirmScreen}
                            />
                        </View>
                    }
                    {uploadedData && uploadedData.documents.length >= visaAddDocumentData.maximumDocumentsAllowed &&
                        <View style={styles.container}>
                            <ButtonWpr
                                buttonWidth={"98%"}
                                buttonText={"CONTINUE"}
                                onButtonPress={openConfirmScreen}
                            />
                        </View>
                    }
                </View>
                {openBulkUploadDetail && (
                    <BottomSheetWpr
                        title={"Select Type"}
                        visible={toggleModal}
                        isCrossIcon={true}
                        onDismiss={closeShowBulkUpload}
                        callTracking={visaOmnitureTracking}
                        setVisible={toggleModal}
                        children={
                            <View>
                                <View style={styles.visaInfoContainer}>
                                    <Image
                                        source={getImagePath('tipIcon')}
                                        style={styles.icon}
                                    />
                                    <Text style={styles.visaInfoText}>{STRING_MAP.ADD_DOC_UPLOAD_IMAGE_PERSUASION}
                                    </Text>
                                </View>
                                <UploadRow
                                    icon={getImagePath('addDoc')}
                                    content='Add Documents'
                                    onPress={() => openDocGallery()}
                                />
                                <View style={styles.borderStyle}>
                                    <UploadRow
                                        icon={getImagePath('addImage')}
                                        content='Add Images'
                                        onPress={() => openGallery(visaAddDocumentData, setOpenBulkUploadDetail)}
                                    />
                                </View>
                            </View>
                        }
                    />
                )}
            </View>

        )
    };

    switch (viewState) {
        case VIEW_STATES.LOADING:
            return renderLoadingView();
        case VIEW_STATES.SUCCESS:
            return renderSuccessView();
        case VIEW_STATES.ERROR:
            return renderErrorView();
        case VIEW_STATES.SHOW_BULK_UPLOAD:
            return renderBulkLoadingView();
    }
}


export default UploadedScreen;

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: colors.lightGray2,
    },
    tabsDisplay: {
        height: "100%",
        flex: 1,
        ...marginStyles.mb30,
        ...marginStyles.mt16
    },
    footer: {
        position: "absolute",
        bottom: 0,
        width: "100%"
    },
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...paddingStyles.pa16,
        backgroundColor: colors.white,
        shadowColor: colors.white,
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 1,
        shadowRadius: 1,
        elevation: 9,

    },
    containerRight: {
        alignItems: 'flex-end', // Align children to the right

    },
    uploadRow: {
        flexDirection: 'row',
        ...paddingStyles.pa8,
        ...paddingStyles.pv20,
        justifyContent: 'space-between',
        alignItems: "center",
    },
    icon: {
        width: 24,
        height: 24,
        resizeMode: 'contain'
    },
    uploadRowText: {
        ...fontStyles.labelMediumBold,
        ...marginStyles.ml8,
        color: colors.black,

    },
    uploadRowColumn: {
        flexDirection: "row",
    },
    visaInfoText: {
        ...fontStyles.labelSmallBold,
        color: colors.green,
        ...paddingStyles.pl8,
        flex: 1,
    },
    visaInfoContainer: {
        backgroundColor: colors.fadedGreen,
        ...paddingStyles.ph10,
        ...paddingStyles.pv6,
        ...marginStyles.mt16,
        flexDirection: 'row'
    },
    centeredView: {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'transparent',
    },
    darkenedBackground: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
    },
    modalView: {
        backgroundColor: colors.white,
        borderRadius: 16,
        ...paddingStyles.pa20,
        alignItems: 'center',
        elevation: 5,
        width: 244,
        height: 145,
    },
    modalText: {
        ...marginStyles.mb24,
        ...fontStyles.labelMediumBold,
        color: colors.gray,
    },
    iconStyle: {
        width: 32,
        height: 32,
        ...marginStyles.mb12,
      },
})