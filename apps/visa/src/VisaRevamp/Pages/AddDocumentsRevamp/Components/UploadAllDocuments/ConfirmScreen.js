import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, BackHandler } from 'react-native';
import { HARDWARE_BACK_PRESS, VIEW_STATES } from '../../../../constants';
import { confirmClassifierData, completelassifierData, cancelClassifierData } from '../../../../Utils/NetworkUtils';
import { colors } from '../../../../Styles/colors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { PAGE_HEADERS, STRING_MAP } from 'apps/visa/src/VisaRevamp/textStrings';
import { ALERTS, BUTTON_CTA_MAP } from '../../../../textStrings';
import { isEmpty } from 'lodash';
import VisaDataHolder from 'apps/visa/src/VisaRevamp/Utils/VisaDataHolder';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { logPDTEvent, visaPdtEventsInitilizer } from 'apps/visa/src/VisaRevamp/Tracking/pdt/logger';
import { NAME_OF_EVENTS, TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { PDT_EVENT_TYPES } from 'apps/visa/src/VisaRevamp/Tracking/pdt/constants';


/* Components */
import ErrorPage from '../../../../Components/Common/ErrorPage';
import BulkUploader from '../../../../Components/Common/Loader/bulkuploader';
import Tabs from './TabsData/index';
import HeaderWpr from '../../../../Components/Common/HeaderWpr';
import ButtonWpr from '../../../../Components/Common/ButtonWpr';

const ConfirmScreen = ({ bookingId, assignData = {}, state = '' }) => {

    const [viewState, setViewState] = useState(VIEW_STATES.LOADING);
    const [uploadedData, setUploadedData] = useState({});

    const handleBack = () => {
        VisaNavigation.pop();
        return true;
    };

    const trackPageLoad = () => {
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.BULK_UPLOAD_RESULT_PAGE_LOADED,
            eventType: TYPE_OF_EVENTS.PAGE_LOAD,
        });
        visaPdtEventsInitilizer();
    };

    async function fetchData() {
        const response = await confirmClassifierData({ bookingId });
        if (!response) {
            setViewState(VIEW_STATES.ERROR);
            return;
        } else if (response) {
            trackPageLoad();
            setUploadedData(response);
            setViewState(VIEW_STATES.SUCCESS);
        }
    };

    useEffect(() => {
        setViewState(VIEW_STATES.LOADING);
        VisaDataHolder.getInstance().setCurrentPage(VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM);
        if (!isEmpty(assignData)) {
            trackPageLoad();
            setUploadedData(assignData);
            setViewState(VIEW_STATES.SUCCESS);
        } else {
            fetchData();
        }
        const subscription = BackHandler.addEventListener(HARDWARE_BACK_PRESS, handleBack);
        return () => {
            subscription.remove();
        };
    }, []);

    const cancelPress = () => {
        visaTrackClickEvent({
            eventName: NAME_OF_EVENTS.CANCEL_ANALYSE_BULK,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: NAME_OF_EVENTS.CANCEL_ANALYSE_BULK,
        })
        const response = cancelClassifierData({ bookingId });
        VisaNavigation.pop();
        return true;
    };

    const renderLoadingView = () => {
        return <BulkUploader loaderText={"Analysing documents"}
            loaderSubText={STRING_MAP.BULK_UPLOAD_LOADING_TEXT}
            iconName='bulkUploadIcon'
            showCancelButton={true}
            onCancelPress={cancelPress} />;
    };

    const renderCompleteLoadingView = () => {
        return <BulkUploader loaderText={"Uploading documents"}
        />;
    };

    const renderErrorView = () => {
        return <ErrorPage handleBackClick={handleBack} handleRefreshClick={fetchData()} />;
    };

    const handleBackClick = () => {
        VisaNavigation.pop();
    };

    const openAddDocScreen = () => {
        visaTrackClickEvent({
            eventName: `click_confirm_classifier_result`,
            eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
        })
        logPDTEvent({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            eventValue: 'click_confirm_classifier_result',
        })
        setViewState(VIEW_STATES.SHOW_BULK_UPLOAD);
        completelassifierData({ bookingId }).then(() => {
            showShortToast(ALERTS.DOCUMENTS_SUCCESSFULLY_ASSIGNED);
            setViewState(VIEW_STATES.SUCCESS);           
            VisaNavigation.push(VISA_ROUTE_KEYS.ADD_DOCUMENTS, { bookingId });
        }).catch(() => {
            setViewState(VIEW_STATES.ERROR);
        });

    };

    const renderSuccessView = () => {
        return (
            <View style={styles.mainContainer}>
                <HeaderWpr
                    clickHandler={() => handleBackClick()}
                    titleText={PAGE_HEADERS.BULK_ULPOAD_RESULT}
                    wrapperStyle={{
                        ...getPlatformElevation(0)
                    }}
                />
                <Tabs uploadedData={uploadedData} classifier={true} />

                <View style={styles.footer}>
                    <View style={styles.container}>
                        <ButtonWpr
                            buttonWidth={"98%"}
                            buttonText={BUTTON_CTA_MAP.BULK_UPLOAD_CONFIRM}
                            onButtonPress={openAddDocScreen}
                        />
                    </View>
                </View>
            </View>
        )
    };

    switch (viewState) {
        case VIEW_STATES.LOADING:
            return renderLoadingView();
        case VIEW_STATES.SUCCESS:
            return renderSuccessView();
        case VIEW_STATES.ERROR:
            return renderErrorView();
        case VIEW_STATES.SHOW_BULK_UPLOAD:
            return renderCompleteLoadingView();
    }
}


export default ConfirmScreen;

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: colors.lightGray2,
    },
    tabsDisplay: {
        height: "100%",
        flex: 1,
        ...marginStyles.mb30,
        ...marginStyles.mt16
    },
    footer: {
        position: "absolute",
        bottom: 0,
        width: "100%"
    },
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...paddingStyles.pa16,
        backgroundColor: colors.white,
        shadowColor: colors.white,
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 1,
        shadowRadius: 1,
        elevation: 9,

    },
    containerRight: {
        alignItems: 'flex-end', // Align children to the right

    },
})