import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Image } from 'react-native'
import React, {useState, useRef} from 'react';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { colors } from '../../../../Styles/colors';
import { fontStyles } from '../../../../Styles/fonts';
import HeaderWrapper from '../../../../Components/Common/HeaderWpr';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';

const ImageViewer = ({ image, url }) => {

    const handleBack = () => {
        VisaNavigation.pop();
    };

    return (
        <View style={styles.mainContainer}>
            <HeaderWrapper
                descText={<Text style={styles.stepText}>{url?.split('/').pop().split('?')[0]}</Text>}
                wrapperStyle={getPlatformElevation(0)}
                descriptionStyle={{marginTop: 0}}
                clickHandler={handleBack}
            />
            <View style={styles.imageContainer}>
                <Image source={{uri: image}} style={styles.image} />
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: colors.lightGray6,
        justifyContent: 'space-between',
    },
    stepText: {
        ...fontStyles.labelBaseRegular,
        color: colors.black,
        whiteSpace: 'nowrap',
        margin: 0
    },
    imageContainer: {
        flex: 1,
        marginTop: 30,
        alignItems: 'center',
        ...paddingStyles.ph12,
    },
    image: {
        width: '100%',
        aspectRatio: 2/3,
        resizeMode: 'contain',
    },
})

export default ImageViewer;