import {Platform,Dimensions} from 'react-native';
import {fontSizes, fonts, normaliseFont} from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Common/Components/Card/getPlatformElevation';
import { colors } from  '../../../../Styles/colors';
import { fontStyles } from '../../../../Styles/fonts'
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

const styles = {
  flex1: {
    flex: 1
  },
  font8: {
    fontSize: 8
  },
  font14: {
    fontSize: 14
  },
  font18: {
    fontSize: 18
  },
  font16: {
    fontSize: 16
  },
  font10: {
    fontSize: 10
  },
  font11: {
    fontSize: 11
  },
  font12: {
    fontSize: 12
  },
  font13: {
    fontSize: 13
  },
  font15: {
    fontSize: 15
  },
  font20: {
    fontSize: 20
  },
  font22: {
    fontSize: 22
  },
  font34: {
    fontSize: 34
  },
  GreyBg: {
    backgroundColor: '#ddd'
  },
  whitebg: {
    backgroundColor: '#fff'
  },
  lightBlue: {
    backgroundColor: '#f1f7ff'
  },
  lightYellow: {
    backgroundColor: '#ffedd1'
  },
  mar10Top: {
    marginTop: 10
  },
  mar15Top: {
    marginTop: 15
  },
  pad2Top: {
    paddingTop: 2
  },
  pad4Top: {
    paddingTop: 4
  },
  pad5Top: {
    paddingTop: 5
  },
  pad10Top: {
    paddingTop: 10
  },
  pad15Top: {
    paddingTop: 15
  },
  pad20Top: {
    paddingTop: 20
  },
  mar18Bt: {
    marginBottom: 18
  },
  mar25Bt: {
    marginBottom: 25
  },
  mar20Bt: {
    marginBottom: 20
  },
  mar20Lt: {
    marginLeft: 20
  },
  mar22Bt: {
    marginBottom: 22
  },
  mar15Bt: {
    marginBottom: 15
  },
  mar10Bt: {
    marginBottom: 10
  },
  mar12Bt: {
    marginBottom: 12
  },
  mar0Bt: {
    marginBottom: 0
  },
  mar1Bt: {
    marginBottom: 1
  },
  mar5Bt: {
    marginBottom: 5
  },
  mar8Bt: {
    marginBottom: 8
  },
  mar3Bt: {
    marginBottom: 3
  },
  mar5Rt: {
    marginRight: 5
  },
  mar10Rt: {
    marginRight: 10
  },
  mar15Rt: {
    marginRight: 15
  },
  mar20Rt: {
    marginRight: 20
  },
  mar40Rt: {
    marginRight: 40
  },

  mar30Bt: {
    marginBottom: 30
  },
  mar35Bt: {
    marginBottom: 35
  },
  mar40Bt: {
    marginBottom: 40
  },
  mar2Tp: {
    marginTop: 2
  },
  mar4Tp: {
    marginTop: 4
  },
  mar10Tp: {
    marginTop: 10
  },
  lineHeight16: {
    lineHeight: 16
  },
  pad15Bt: {
    paddingBottom: 15
  },
  lineHeight18: {
    lineHeight: 18
  },
  lineHeight20: {
    lineHeight: 20
  },
  lineHeight25: {
    lineHeight: 25
  },
  Linethrough: {
    textDecorationLine: 'line-through'
  },
  flexRow: {
    flexDirection: 'row'
  },
  alignCenter: {
    alignItems: 'center'
  },
  borderbtm: {
    borderBottomWidth: 1,
    borderBottomColor: '#ddd'
  },
  borderbtmgrey: {
    borderBottomWidth: 1,
    borderBottomColor: '#9b9b9b'
  },
  borderbtmlightgray: {
    borderBottomWidth: 1,
    borderBottomColor: '#d6d6d6'
  },
  pad3Tp: {
    paddingTop: 3
  },
  pad5Tp: {
    paddingTop: 5
  },
  pad10Bt: {
    paddingBottom: 10
  },
  pad20Bt: {
    paddingBottom: 20
  },
  pad5Bt: {
    paddingBottom: 5
  },
  blueLink: {
    color: '#008cff'
  },
  blueTextLight: {
    color: '#5295cc'
  },

  DarkBlue: {
    color: '#1c5185'
  },

  blueTextLight1: {
    color: 'rgba(82,149,204,.6);'
  },

  bgWhite: {
    backgroundColor: '#ffffff'
  },
  boldFont: {
    fontFamily: fonts.bold
  },
  blackFont: {
    fontFamily: fonts.black
  },
  lightFont: {
    fontFamily: fonts.light
  },
  regularFont: {
    fontFamily: fonts.regular
  },
  greyColor: {
    color: '#9b9b9b'
  },
  greenText: {
    color: '#1a7971'
  },
  greenTxt1: {color: '#00a19c'},
  greenText2: {
    color: '#00a19c',
    fontFamily: fonts.bold
  },
  greenText3: {
    color: '#21bd99',
    fontFamily: fonts.bold
  },
  green: {
    color: '#28beb2'
  },
  BlueText: {
    color: '#5295cc'

  },
  BlueText1: {
    color: '#4a4a4a'

  },
  redText: {
    color: '#e53442'
  },
  darkYellowText: {
    color: '#cf8100'
  },
  WhiteTextLight: {
    color: '#d8d8d8'
  },
  WhiteText: {
    color: colors.white
  },
  BlackText: {
    color: colors.black
  },
  DefaultText: {
    color: colors.defaultTextColor
  },
  PromoText: {
    color: '#249995'
  },
  ErrorText: {
    color: '#e53442'
  },
  LightText: {
    color: colors.lightTextColor
  },
  CenterText: {
    alignSelf: 'center'
  },
  AlignSelfRight: {
    alignSelf: 'flex-end'
  },
  AlignCenter: {
    textAlign: 'center'
  },
  AlignRight: {
    textAlign: 'right'
  },
  AlignItemCenter: {
    alignItems: 'center'
  },
  fontWeightB: {
    fontWeight: 'bold'
  },
  MakeRelative: {
    position: 'relative'
  },
  spaceBetween: {
    justifyContent: 'space-between'
  },
  bgTransparent: {
    backgroundColor: 'transparent'
  },
  Italic: {
    fontStyle: 'italic'
  },

  CapsuleBtnFill: {
    paddingVertical: 10,
    borderRadius: 34
  },
  CapsuleBtnFillActive: {
    paddingVertical: 13,
    borderRadius: 34
  },
  marR90: {marginRight: 90},
  width90per: {width: '90%'},
  width110: {width: 110},
  width150: {width: 150},
  width35per: {width: '35%'},
  width60per: {width: '60%'},
  width100per: {width: '100%'},

  VisaMainContainer: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: '#f0f0f0'
  },
  VisaTravellerContainer: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: '#fff'
  },
  VisaCard: {
    backgroundColor: '#fff'
  },
  container: {
    // marginTop: -67
  },
  stickyHeader: {
    paddingTop: 14,
    paddingBottom: 14,
    width: '100%',
    elevation: 3,
    backgroundColor: '#fff',
    marginBottom: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 2
  },
  eTicketStickyBarWrapper: {
    paddingHorizontal: 15,
    paddingVertical: 15
  },
  stickyHeaderGradient: {
    paddingVertical: 20
  },
  stickyBarWrapper: {
    paddingHorizontal: 15
  },
  backarrow: {
    width: 16,
    height: 16,
    marginTop: 5,
    marginRight: 10
  },
  RightArrowBlue: {width: 15, height: 10},
  topStatusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 20,
    paddingRight: 20,
    alignItems: 'center'
  },
  TripsTopStripMain: {
    width: '100%',
    height: 120
  },

  TripsTopStrip: {
    height: 120,
    width: '100%',
    paddingTop: 30,
    paddingBottom: 30,
    paddingHorizontal: 20
  },

  bgWhiteWrapper: {
    paddingBottom: 30
  },
  UpcomingTripWrpper: {
    marginTop: -40
  },
  WhiteCardMain: {
    paddingHorizontal: 15,
    zIndex: 2,
    position: 'relative'
  },
  WhiteCard: {
    borderRadius: 4,
    backgroundColor: '#fff',
    paddingHorizontal: 14,
    paddingVertical: 14,
    elevation: 2,
    marginBottom: 5,
    marginTop: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.3,
    shadowRadius: 2

  },
  WhiteCardLeft: {
    width: '70%'
  },
  WhiteCardRight: {
    width: '25%'
  },
  VisaIcon: {
    width: 45,
    height: 44
  },
  HotelIcon: {
    width: 25,
    height: 20
  },
  flightIcon: {
    width: 25,
    height: 15
  },
  IconPosflt: {
    marginTop: 7,
    marginRight: 10
  },
  rndTripArrow: {
    width: 11,
    height: 12
  },
  oneWayArrow: {
    width: 10,
    height: 7
  },
  oneWayArrowPos: {
    marginHorizontal: 5,
    marginTop: 5
  },
  holidayIcon: {
    width: 15,
    height: 23
  },
  IconPos: {
    marginTop: 5,
    marginRight: 10
  },
  rndTripIcon: {
    marginHorizontal: 5,
    marginTop: 1
  },
  ApplyBeforeContainer: {
    flexDirection: 'row',
    borderLeftWidth: 3,
    borderLeftColor: '#e53442',
    paddingLeft: 10,
    marginLeft: -14
  },
  VisaApplyBtn: {
    width: 155,
    paddingHorizontal: 15,
    alignSelf: 'flex-end',
    marginTop: -23,
    elevation: 3,
    marginRight: 15,
    zIndex: 10,
    position: 'relative'
  },
  VisaApplyBtnTouch: {
    borderRadius: 34,
    elevation: 2,
    marginBottom: 3,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 2
  },
  GetVisaCard: {
    paddingTop: 25,
    paddingBottom: 20,
    paddingHorizontal: 30
  },
  ImageCircle: {
    width: 60,
    height: 60,
    position: 'relative',
    backgroundColor: '#b9dcef',
    borderRadius: 30
  },
  UploadIcon: {
    width: 32,
    height: 39,
    position: 'absolute',
    top: 10,
    left: 15
  },
  PayIcon: {
    width: 36,
    height: 30,
    position: 'absolute',
    top: 15,
    left: 11
  },
  GetVisaIcon: {
    width: 39,
    height: 38,
    position: 'absolute',
    top: 12,
    left: 10
  },

  GetVisaDtls: {
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  GetVisaDots: {
    marginTop: -40,
    color: '#0052ba'
  // marginHorizontal:15,
  },
  rtArrow: {
    width: 27,
    height: 7
  },
  HolidaysItemWrapper: {
    paddingVertical: 10
  },
  PackageTxtName: {
    width: 150,
    paddingLeft: 20
  },
  PackageListCard: {
    borderRadius: 4,
    backgroundColor: '#ffffff',
    elevation: 2,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 2,
    width: 225,
    marginBottom: 4,
    marginRight: 15,
    maxHeight: 70
  },
  HotDealCard: {
    borderRadius: 4,
    backgroundColor: '#ffffff',
    elevation: 2,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 2,
    width: 225,
    marginBottom: 4,
    marginRight: 15,
    marginLeft: 3
  },
  packImg: {
    width: 63,
    height: '100%',
    borderTopLeftRadius: 4,
    borderBottomLeftRadius: 4
  },
  packageDtls: {
    flex: 1,
    paddingVertical: 5,
    paddingHorizontal: 10
  },
  hotpackageDtls: {
    paddingHorizontal: 9,
    paddingVertical: 10
  },
  loginWrapper: {
    paddingVertical: 20,
    paddingHorizontal: 25,
    backgroundColor: '#fff'
  },
  blueArrow: {
    width: 13,
    height: 7
  },
  blueArrowPos: {
    marginTop: 5,
    marginLeft: 5
  },
  VisaSearchWrapper: {
    position: 'relative'
  },
  VisaSearchWrapperIOS: {
    zIndex: 1
  },
  SearchInputPlcHolder: {
    alignItems: 'center',
    paddingVertical: 20,
    backgroundColor: '#fff'

  },
  curved_bg: {
    width: '105%',
    height: 22,
    position: 'absolute',
    bottom: -22,
    left: -10,
    zIndex: 10
  },
  SearchInput: {
    width: '60%',
    borderBottomColor: '#ddd',
    borderBottomWidth: 1,
    padding: 0,
    paddingBottom: 7,
    textAlign: 'center'
  },
  SearchInputFocus: {
    borderBottomWidth: 1,
    borderBottomColor: '#008cff'
  },
  // Visa Traveller Widget Start
  CounterWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.3,
    shadowRadius: 2,
    borderRadius: 4,
    overflow: 'hidden',
    width: 135
  },
  counterVal: {
    fontSize: 22,
    color: '#000',
    fontFamily: fonts.bold,
    paddingHorizontal: 15,
    paddingVertical: 10
  },
  minus: {marginTop: -10},
  grey: {color: '#747474'},
  DoneRoundBtn: {
    width: 61, height: 61, alignSelf: 'center', alignItems: 'center', justifyContent: 'center', borderRadius: 100
  },
  mar15: {margin: 15},
  AddTravellerContainer: {
    width: '100%',
    elevation: 2,
    borderRadius: 4,
    alignSelf: 'center',
    ...getPlatformElevation(3),
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOpacity: 0.2,
        shadowRadius: 2,
        shadowOffset: {
          height: 2,
          width: 0
        }
      }
    })
  },
  TravelleWrap: {
    paddingTop: 20, paddingLeft: 15, paddingRight: 15, paddingBottom: 20
  },
  TravellerInputWrap: {position: 'relative'},
  TravellerInput: {
    height: 45, fontSize: 18, color: '#000', paddingRight: 20
  },
  TravellerCounterInput: {
    height: 45,
    fontSize: 18,
    color: '#000',
    paddingRight: 20,
    borderWidth: 1,
    borderColor: '#2082f4',
    borderRadius: 4,
    paddingHorizontal: 10,
    width: 80
  },
  InputText: {position: 'absolute', right: 5, top: 12},
  // Visa Doc Verification
  docVerificationPopUpKey: {
    color: colors.lightTextColor,
    fontSize: 12,
    marginRight: 15,
    width: '27%'
  },
  docVerificationPopUpValue: {
    color: colors.defaultTextColor,
    fontSize: 12,
    fontFamily: fonts.bold,
    flex: 1
  },
  // Visa Review
  reviewCardsHeading: {
    color: '#21bd99',
    fontFamily: fonts.bold,
    fontSize: 11,
    marginTop: 5
  },
  reviewGreenTitleAlignment: {
    flexDirection: 'row',
    marginBottom: 20
  },
  reviewCardsStep: {
    color: colors.white,
    fontFamily: fonts.bold
  },
  reviewCardKey: {
    color: colors.lightTextColor,
    fontSize: 11,
    marginBottom: 15
  },
  reviewCardValue: {
    color: colors.black,
    fontSize: 14
  },
  reviewGSTNKey: {
    fontSize: 12,
    color: colors.lightTextColor,
    marginBottom: 10
  },
  reviewInput: {
    color: colors.defaultTextColor,
    fontSize: 14,
    marginVertical: Platform.OS === 'ios' ? 10 :0 
  },
  reviewActionButton: {
    color: colors.white,
    alignSelf: 'center',
    backgroundColor: 'transparent',
    fontFamily: fonts.bold
  },
  // Visa Traveller Widget End
  // Document Info Page
  stickyHeaderMain: {
    paddingTop: 14,
    paddingBottom: 14,
    width: '100%',
    elevation: 5,
    backgroundColor: '#fff',
    marginBottom: 6
  },
  stickyHeaderForm: {
    paddingTop: 14,
    paddingBottom: 14,
    width: '100%',
    backgroundColor: '#fff',
    marginBottom: 6
  },
  stickyHeaderFaq: {
    paddingTop: 14,
    paddingBottom: 14,
    width: '100%',
    backgroundColor: '#fff',
    elevation: 2
  },
  stickyBarWrapperDocument: {
    flexDirection: 'row',
    width: '75%',
    alignItems: 'center'
  },
  stickyTextWrapper: {
    paddingLeft: 15
  },
  defaultGrey: {color: '#4a4a4a'},
  blueColor: {color: '#008cff'},
  blackColor: {color: '#000000'},
  lightpink: {color: '#4a4a4a'},
  greenTick: {width: 18, height: 18},
  topTick: {
    width: 29, height: 23, position: 'absolute', zIndex: 20, top: 0, right: 100
  },
  VisaSection: {
    paddingLeft: 16, paddingRight: 16, paddingTop: 20, paddingBottom: 25
  },
  VisaSectionContainer: {
    borderRadius: 4,
    ...getPlatformElevation(3),
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOpacity: 0.2,
        shadowRadius: 2,
        shadowOffset: {
          height: 2,
          width: 0
        }
      }
    })
  },
  VisaDocumentInfoFAQText: {
    color: colors.white, backgroundColor: colors.transparent, fontFamily: fonts.bold, fontSize: normaliseFont(14)
  },
  VisaSubSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0'
  },
  padd20: {padding: 20},
  gradientTooltip: {borderRadius: 4, elevation: 1},
  tooltip: {
    paddingTop: 17, paddingBottom: 14, paddingLeft: 17, paddingRight: 14, fontSize: 13
  },
  StepImg: {width: 50, height: 50},
  CardContent: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e4e4e4',
    flexDirection: 'row',
    padding: 15,
    elevation: 1
  },
  elevation1: {elevation: 1},
  bullets: {
    backgroundColor: '#000', height: 6, width: 6, borderRadius: 10, marginTop: 6
  },
  mar5Lt: {marginLeft: 5},
  topStatusCard: {
    paddingTop: 10,
    zIndex: 2,
    position: 'relative',
    elevation: 1
  },
  borderBtm: {
    borderBottomWidth: 1, borderBottomColor: '#ededed', paddingBottom: 10, marginBottom: 10
  },
  borderBtmPrice: {
    borderBottomWidth: 1, borderBottomColor: '#ededed', paddingBottom: 15, marginBottom: 15
  },
  buttonTextTop: {
    width: 170,
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-end',
    marginTop: -20,
    elevation: 4,
    marginRight: 40
  },
  borderbtmlightblue: {
    borderBottomWidth: 1,
    borderBottomColor: '#acd0e4'
  },
  marL15: {marginLeft: 15},
  marR15: {marginLeft: 15},
  mar12Tp: {
    marginTop: 12
  },
  justifyAndAlignCenter: {
    justifyContent: 'center', flexDirection: 'row'
  },
  payNowsection: {
    position: 'relative', borderColor: '#000000', borderWidth: 3, zIndex: 999
  },
  payNowWrapper: {
    backgroundColor: '#4a4a4a', flexDirection: 'row', padding: 10, justifyContent: 'space-between', position: 'relative', zIndex: 1
  },
  pricetxt: {width: 210, borderRightWidth: 1, borderRightColor: '#ccc'},
  payNowPrice: {fontSize: 18, color: colors.white, fontFamily: fonts.bold},
  payNowInfo: {color: colors.white, fontSize: 11},
  buttonText: {
    color: colors.white,
    borderRadius: 100,
    fontSize: 16,
    fontFamily: fonts.bold,
    paddingLeft: 28,
    paddingRight: 28,
    paddingTop: 12,
    paddingBottom: 12,
    minWidth: 125,
    textAlign: 'center'
  },
  slacedPrice: {
    fontSize: 11,
    color: '#fff',
    textDecorationLine: 'line-through',
    marginLeft: 10
  },
  row: {flexDirection: 'row'},
  buttonBlue: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 41,
    borderBottomRightRadius: 100,
    borderTopRightRadius: 100
  },
  buttonBlueTxt: {color: colors.white, fontSize: 16, fontFamily: fonts.bold},
  buttonWhite: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 41,
    borderBottomLeftRadius: 100,
    borderTopLeftRadius: 100,
    backgroundColor: '#fff'
  },
  buttonWhiteTxt: {fontSize: 16, color: '#008cff', fontFamily: fonts.bold},
  buttonWhiteTxtSmall: {
    fontSize: 14,
    color: '#008cff'
  },
  payFullBtn: {
    borderBottomRightRadius: 100,
    borderTopRightRadius: 100
  },
  PriceBreakupHead: {
    paddingTop: 18, paddingBottom: 18, paddingLeft: 20, paddingRight: 20
  },
  CollapsedContainer1: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#ededed'
  },
  CollapsedTapContainer1: {
    backgroundColor: 'transparent',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 15,
    flexDirection: 'row'
  },
  CollapsedTapDtlsContainer1: {
    backgroundColor: 'transparent',
    paddingHorizontal: 20
  },
  crossIcon: {width: 24, height: 24},
  PriceBreakupVisa: {
    paddingTop: 60, backgroundColor: 'rgba(0, 0, 0, 0.8)', position: 'absolute', width: '100%', height: '100%', elevation: 6, bottom: 70
  },
  PriceBreakupText: {
    backgroundColor: '#fff', justifyContent: 'flex-end', position: 'absolute', bottom: 0, width: '100%'
  },
  VisaInfoBreakupText: {
    paddingLeft: 20, paddingRight: 20, paddingTop: 16
  },
  pad15: {padding: 15},
  ApplyVisaContainer: {
    marginLeft: 16,
    borderRadius: 4,
    marginRight: 16,
    marginTop: -40,
    backgroundColor: '#fff',
    paddingLeft: 12,
    paddingRight: 12,
    paddingTop: 18,
    paddingBottom: 18,
    zIndex: 1,
    position: 'relative',

    ...getPlatformElevation(3),

    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOpacity: 0.2,
        shadowRadius: 2,
        shadowOffset: {
          height: 2,
          width: 0
        }
      }
    })
  },
  DocumentInfoTop: {marginTop: -80},
  // Document Info Page

  // landing status screen
  WhiteCrossIcon: {
    width: 24,
    height: 24
  },
  formIcon: {
    width: 16,
    height: 22,
    resizeMode: 'cover'
  },
  AllDocumentItem: {
    elevation: 1.5,
    marginTop: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
    backgroundColor: '#fff',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 2,
    minHeight: 34,
    alignItems: 'center'
  },
  GradientCardHeight: {
    height: 160
  },
  LandingStatusGradientCard: {
    minHeight: 130,
    width: '100%',
    paddingTop: 30,
    paddingBottom: 30,
    paddingHorizontal: 20
  },

  marTopneg50: {
    marginTop: -50
  },

  marTopneg60: {
    marginTop: -60
  },

  TravellerCountTag: {
    backgroundColor: '#1f1c2c',
    borderRadius: 70,
    width: 88,
    height: 20,
    justifyContent: 'center',
    position: 'relative',
    zIndex: 11,
    elevation: 3,
    marginLeft: 10
  },
  VerificationDtls: {
    paddingHorizontal: 10,
    paddingTop: 20,
    paddingBottom: 35
  },
  travellerIcon: {
    width: 18,
    height: 18,
    marginRight: 5
  },
  chatContainer: {
    elevation: 5,
    position: 'absolute',
    bottom: 40,
    right: 20
  },
  chatIcon: {
    width: 90,
    height: 90
  },
  VerificationStatus: {
    paddingHorizontal: 12,
    marginLeft: -10,
    borderLeftWidth: 3,
    borderLeftColor: '#e53442'
  },
  VerificationStatusTxt: {
    fontSize: 12,
    color: '#e53442',
    fontFamily: fonts.black
  },
  VerificationWhiteCard: {
    position: 'relative',
    zIndex: 0,
    marginTop: -10,
    marginRight: 0,
    width: 270
  },
  pad5lft: {
    paddingLeft: 5
  },
  CrousalWrapper: {
    marginLeft: 10,
    marginRight: 6
  },
  VerificationTravellerName: {
    width: '90%'
  },
  UploadVerificationCard: {
    paddingHorizontal: 15,
    paddingTop: 15,
    paddingBottom: 20,
    borderTopWidth: 1,
    borderTopColor: '#d8d8d8',
    borderBottomWidth: 1,
    borderBottomColor: '#d8d8d8'
  },
  greenTickIcon: {
    width: 22,
    height: 22,
    marginRight: 5,
    marginTop: -3
  },
  cardIcon: {
    width: 24,
    height: 24,
    marginTop: -2
  },
  hide: {
    display: 'none'
  },

  // Progressive Search Widget
  calDates: {
    fontSize: 36,
    color: '#000',
    marginRight: 5
  },
  dateInfoWrap: {
    paddingVertical: 25,
    paddingLeft: 30
  },
  dateInfoOuter: {
    backgroundColor: '#fff',
    borderColor: '#e7e7e7',
    borderBottomWidth: 1
  },
  FullPagePopUp: {
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 1111,
    elevation: 3
  },
  FullPageBGOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 0,
    backgroundColor: 'rgba(0,0,0,0.6)'
  },
  FullPagePopupContent: {
    position: 'relative',
    zIndex: 1
  },
  CurvedMainWrapper: {
    position: 'relative',
    height: 22
  },
  Progressivecurved_bg: {
    width: '105%',
    height: 22,
    position: 'absolute',
    top: 0,
    left: -10,
    zIndex: 10
  },
  borderbtm0: {
    borderBottomWidth: 0,
    borderBottomColor: 'transparent'
  },
  SearchBtnWrapper: {
    alignItems: 'center',
    position: 'relative',
    top: -45,
    zIndex: 10
  },
  SearchGradientBtn: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center'
  },

  // Type of Visa

  infoIcon: {
    width: 16,
    height: 16,
    marginRight: 5
  },
  TopInformation: {
    paddingHorizontal: 15,
    paddingVertical: 6
  },
  CollapsedMainWhiteWrapper: {
    borderTopWidth: 1,
    borderTopColor: '#d8d8d8',
    borderBottomWidth: 1,
    borderBottomColor: '#d8d8d8',
    backgroundColor: '#ffffff',
    paddingVertical: 10,
    paddingHorizontal: 10
  },
  RecommendedIcon: {
    width: 42,
    height: 42,
    resizeMode: 'contain'
  },
  minHt130: {
    minHeight: 130
  },

  CollapsedWrapper: {
    paddingHorizontal: 12
  },
  CollapsedContainer: {
    borderRadius: 4,
    elevation: 2,
    marginBottom: 5,
    marginTop: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.3,
    shadowRadius: 2,
    backgroundColor: '#fff'
  },

  CollapsedTapContainer: {
    backgroundColor: 'transparent',
    paddingHorizontal: 14,
    paddingVertical: 14,
    flexDirection: 'row'

  },
  CollapsedTapLeftSection: {
    width: '70%'

  },
  PreferedTagView: {
    borderRadius: 15,
    paddingHorizontal: 7,
    paddingVertical: 2,
    backgroundColor: '#4a4a4a1a',
    marginRight: 10
  },
  PreferedTag: {
    fontSize: 11,
    color: '#4a4a4a'
  },
  CollapsedTapRightSection: {
    width: '30%'
  },
  CollapsedTapDtlsContainer: {
    backgroundColor: 'transparent',
    paddingHorizontal: 12,
    paddingVertical: 15,
    margin: 0.5,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    marginBottom: 1,
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4


  },
  RadioWrapper: {
    width: 18
  },
  Radio: {
    width: 18,
    height: 18,
    backgroundColor: '#fff',
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#008cff',
    marginRight: 10
  },
  RadioInside: {
    width: 12,
    height: 12,
    backgroundColor: '#008cff',
    borderRadius: 20,
    overflow: 'hidden',
    marginLeft: 2,
    marginTop: 2
  },

  ChooseVisaDtls: {
    width: '90%',
    flex: 1
  },
  padHr0: {
    paddingHorizontal: 0
  },
  OtherVisaTxt: {
    fontSize: 22,
    color: '#4a4a4a',
    paddingHorizontal: 12,
    paddingTop: 5

  },
  // black Footer
  BlackFooter: {
    height: 70,
    backgroundColor: '#4a4a4a',
    padding: 15,
    zIndex: 9

  },
  FooterLeft: {
    flex: 1,
    borderRightWidth: 1,
    borderRightColor: '#ffffff',
    flexDirection: 'column'
  },
  FooterLeftWithoutSeparator: {
    flex: 1,
    flexDirection: 'column'
  },
  FooterLeftButtonOff: {
    flex: 1,
    flexDirection: 'column'
  },
  FooterRight: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-end'

  },
  FooterBtn: {
    paddingHorizontal: 20
  },
  WhiteInfoIcon: {
    width: 20,
    height: 20,
    marginTop: 10,
    marginRight: 20

  },
  BlueArrow: {
    width: 14,
    height: 12
  },
  lightGrey: {
    backgroundColor: '#f8f8f8'
  },
  TOVMainWrapper: {
    marginTop: 20
  },
  // upload Document
  UploadDocWrapper: {
    paddingHorizontal: 20,
    paddingVertical: 18
  },
  DocumentDtls: {
    marginHorizontal: -14,
    paddingHorizontal: 14
  },
  FltETicketDtls: {
    paddingVertical: 14,
    backgroundColor: '#cbe5f9',
    marginBottom: -14,
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4
  },
  AttachedIcon: {
    width: 15,
    height: 19
  },
  AttachedIconPress: {
    width: 25,
    height: 20
  },
  VisaPopup: {
    backgroundColor: '#fff',
    borderRadius: 4
  },
  VisaPopupHeader: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    justifyContent: 'space-between'
  },
  VisaPopupHeaderText: {
    color: '#e53442',
    fontSize: 16,
    fontFamily: fonts.bold
  },
  EticketIcon: {
    width: 38,
    height: 18
  },
  VisaPopupDtls: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'transparent'
  },
  padHr40: {
    paddingHorizontal: 40
  },
  VisaFlatGreyBtn: {
    backgroundColor: '#9b9b9b'
  },
  BtnText: {
    color: '#fff',
    textAlign: 'center',
    fontFamily: fonts.bold,
    paddingVertical: 17,
    backgroundColor: 'transparent'
  },
  TnCSection: {
    flexDirection: 'row',
    flex: 1,
    flexWrap: 'wrap',
    alignItems: 'center',
    paddingHorizontal: 8
  },
  // Visa TnC style
  TravellerHead: {
    fontSize: 21,
    color: colors.black,
    marginBottom: 12,
    fontFamily: fonts.light
  },
  formField: {
    borderRadius: 8, paddingHorizontal: 24, paddingBottom: 20, paddingTop: 8
  },
  formTxt: {
    marginBottom: 0,
    fontSize: 16,
    borderWidth: 0,
    color: colors.defaultTextColor,
    height: 38
  },
  addBtn: {
    fontSize: 14,
    fontFamily: fonts.bold,
    color: colors.white,
    backgroundColor: colors.azure,
    height: 34,
    paddingHorizontal: 24,
    alignSelf: 'flex-end',
    textAlign: 'center',
    borderRadius: 17,
    lineHeight: 25
  },
  addBtnDisable: {
    backgroundColor: '#9b9b9b'
  },
  infoTxt: {
    fontSize: 14,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular
  },
  formContainer: {
    paddingHorizontal: 12, paddingTop: 20, paddingBottom: 16, borderBottomWidth: 1, borderBottomColor: '#ccc'
  },
  ctaContainer: {
    marginTop: -16,
    marginRight: 36
  },
  marginTop: {marginTop: 20, marginBottom: 18},
  iconInfo: {width: 20, marginRight: 10, paddingTop: 5},
  infoSection: {flexDirection: 'row', marginHorizontal: 20},
  viewInfo: {
    fontSize: 14,
    color: '#0089ff',
    textDecorationLine: 'underline'
  },
  editIconSection: {flexDirection: 'row'},
  editTxt: {
    fontSize: 14, color: colors.lightTextColor, fontFamily: fonts.regular, flex: 1, marginTop: 5
  },
  editImage: {width: 20, marginRight: 6},
  formEditfield: {paddingHorizontal: 12},
  formEdit: {
    flexDirection: 'row', justifyContent: 'space-between', marginTop: 17, marginBottom: 12
  },
  userName: {fontSize: 16, color: colors.defaultTextColor, fontFamily: fonts.semiBold},
  emailId: {fontSize: 14, color: colors.defaultTextColor, fontFamily: fonts.regular},
  phoneNo: {
    fontSize: 14,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    marginTop: 10,
    marginBottom: 26
  },
  editDetail: {color: colors.linkBlue, fontSize: 14, fontFamily: fonts.semiBold},
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8
  },
  errorText: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.errorRed,
    marginHorizontal: 8
  },
  // Visa FAQ HTML style
  VisaFaqHTML: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap'
  },
  // Verify Document style
  VerifyDocWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'
  },
  VerifyDocItem: {
    width: '48%',
    borderRadius: 4,
    elevation: 2,
    marginBottom: 5,
    marginTop: 6,
    ...getPlatformElevation(3)
  },
  Height155: {
    height: 155
  },
  VerifyDocItemImage: {
    width: '100%',
    height: 135,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4

  },
  VerifyDocItemTitle: {
    paddingHorizontal: 10,
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    backgroundColor: 'transparent',
    marginTop: 3,
    padding: 8

  },
  pdfIcon: {
    width: 18,
    height: 18,
    marginRight: 5
  },
  VerifyPopupTopSection: {
    paddingVertical: 30,
    paddingHorizontal: 20,
    borderBottomColor: '#ededed',
    borderBottomWidth: 1
  },
  VerifyTopSectionTxt: {
    color: '#249995',
    fontSize: 14,
    fontFamily: fonts.bold,
    textAlign: 'center',
    marginTop: 17

  },
  VerifyDocBgGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center'
  },
  PdfWhiteIcon: {
    width: 41,
    height: 54,
    position: 'relative',
    top: 35
  },
  JPGWhiteIcon: {
    width: 47,
    height: 54,
    position: 'relative',
    top: 35
  },
  OpenPDFWrapper: {
    borderWidth: 2,
    borderColor: '#4a4a4a',
    padding: 20,
    position: 'relative'

  },
  PDFImageScreen: {
    width: '100%',
    height: 535,
    resizeMode: 'contain'

  },
  progressContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: '#ffffffd9',
    alignItems: 'center',
    justifyContent: 'center'
  },
  TravellerUploadDtls: {
    backgroundColor: '#fff',
    paddingTop: 0,
    paddingBottom: 20

  },
  ChooseTravellerDtls: {
    marginHorizontal: 15,
    marginTop: -50
  },

  SmileIcon: {
    width: 35,
    height: 36

  },
  TravellerTopGradient: {
    minHeight: 170,
    paddingTop: 40,
    paddingBottom: 50,
    paddingHorizontal: 30
  },
  VisaCheckBox: {
    width: 28,
    height: 28
  },
  // Visa Common
  breakUp: {
    color: '#4a4a4a',
    fontFamily: fonts.bold
  },
  // Visa Review Page Style
  ReviewTopSection: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 2,
    marginBottom: 5,
    backgroundColor: '#f0f0f0'
  },
  ReviewTopGradient: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 30


  },

  ReviewInnerPageDtls: {
    paddingHorizontal: 15,
    paddingVertical: 15
  },
  GreenCircle: {
    width: 20,
    height: 20,
    alignItems: 'center',
    backgroundColor: '#21bd99',
    borderRadius: 25,
    justifyContent: 'center',
    marginRight: 8

  },
  mar5Tp: {
    marginTop: 5
  },
  ReviewTravellerDtlsList: {
    borderBottomWidth: 1,
    borderBottomColor: '#ededed',
    paddingBottom: 10,
    marginBottom: 10,
    marginHorizontal: -14,
    paddingHorizontal: 14
  },
  ReviewFormField: {
    borderWidth: 1,
    borderColor: 'rgba(0,140,255,.5)',
    borderRadius: 4,
    paddingTop: 12,
    paddingBottom: 12,
    paddingHorizontal: 5
  },
  ReviewOfferDtls: {
    paddingVertical: 10
  },
  ReviewofferBorder: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 4,
    width: '35%',
    marginBottom: 5,
    textAlign: 'center'
  },
  PrompCodeWrapper: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 0
  },
  CrossIconBtn: {
    width: 16,
    height: 16
  },
  PromoCodeInput: {
    borderWidth: 1,
    borderColor: '#2082f4',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 4,
    width: '70%'
  },
  VisaCheckBoxMain: {
    borderTopColor: '#ddd',
    borderTopWidth: 1,
    backgroundColor: '#fff',
    paddingTop: 15,
    paddingHorizontal: 15
  },
  TNCTxt: {
    flex: 1,
    lineHeight: 20
  },
  TopGradientTxt: {
    marginTop: 10
  },
  // / add travellers section
  RemoveBtn: {
    width: 70, height: 70, alignItems: 'center', justifyContent: 'center', borderRadius: 50
  },
  EditTravellerOverlayMain: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)', position: 'absolute', width: '100%', height: '100%', elevation: 6, justifyContent: 'flex-end'
  },
  EditTravellerText: {paddingLeft: 24, paddingRight: 30, paddingVertical: 35},
  EditTravellerLink: {fontSize: 14, color: colors.azure, fontFamily: fonts.bold},
  addNameLater: {
    fontSize: 12, color: colors.lightTextColor, fontStyle: 'italic', marginBottom: 12
  },
  removeTxtHead: {
    fontSize: 14, color: '#000', fontFamily: fonts.bold, marginBottom: 18
  },
  marT20: {marginTop: 20},
  marT10: {marginTop: 10},
  EditTravellerInputWrap: {width: 80, position: 'relative'},
  EditTravellerInput: {
    borderWidth: 1, borderColor: '#2082f4', borderRadius: 4, fontSize: 18, color: '#000', paddingRight: 20
  },
  EditInputText: {position: 'absolute', right: 1, top: 15},
  AddBtn: {paddingHorizontal: 22, paddingVertical: 6, borderRadius: 20},
  travellerUploadStyle: {
    width: 14, height: 10, marginTop: 5, marginRight: 8
  },
  landingPaymentGradient: {
    fontFamily: fonts.bold,
    color: colors.white,
    fontSize: 12,
    lineHeight: 18,
    backgroundColor: 'transparent'
  },
  // Visa Form Section
  lightGreyBg: {backgroundColor: '#f2f2f2'},
  elevation3: {elevation: 3},
  checkAvlBtn: {
    paddingVertical: 18,
    width: '92%',
    marginHorizontal: 15,
    borderRadius: 3,
    overflow: 'hidden'
  },
  checkAvlBtnDisable: {
    paddingVertical: 18,
    width: '92%',
    marginHorizontal: 15,
    borderRadius: 3,
    overflow: 'hidden',
    backgroundColor: '#9b9b9b'
  },
  FormSection: {
    paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 25
  },
  MultipleFormSectionTitle: {
    marginBottom: 0,
    fontSize: 16,
    color: '#4a4a4a',
    fontFamily: fonts.bold
  },
  MultipleFormSection: {
    paddingHorizontal: 20, paddingBottom: 25
  },
  sectionTab: {
    backgroundColor: '#e3e7eb', paddingTop: 8, paddingBottom: 8, paddingLeft: 20, paddingRight: 20
  },
  textInput: {
    padding: 10,
    borderWidth: 1,
    borderColor: '#008cff',
    borderRadius: 4,
    color: '#4a4a4a',
    fontFamily: fonts.regular
  },
  textInputGray: {
    padding: 10,
    borderWidth: 1,
    borderColor: '#9B9B9B',
    borderRadius: 4,
    color: '#4a4a4a',
    fontFamily: fonts.regular
  },
  formErrorState: {borderColor: '#ff4959'},
  VisaRadio: {
    width: 21,
    height: 21,
    backgroundColor: '#fff',
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8
  },
  VisaRadioInside: {
    width: 11,
    height: 11,
    backgroundColor: '#008cff',
    borderRadius: 20,
    marginLeft: 4,
    marginTop: 4
  },
  VisaRadioInsideDisabled: {
    width: 11,
    height: 11,
    backgroundColor: '#9b9b9b',
    borderRadius: 20,
    marginLeft: 4,
    marginTop: 4
  },
  formErrorStateMsg: {
    backgroundColor: '#ff4959', alignSelf: 'flex-start', paddingHorizontal: 5, paddingVertical: 3, borderRadius: 14
  },
  FormDOB: {
    height: 46, borderWidth: 1, borderColor: '#008cff', fontSize: 14, width: 90, borderRadius: 4
  },
  GoHeadBtn: {
    alignSelf: 'center', flexDirection: 'row', elevation: 2, borderRadius: 34, paddingVertical: 10, paddingHorizontal: 32, marginTop: 70
  },
  visaFormSelectInputField: {
    ...getPlatformElevation(3),
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOpacity: 0.2,
        shadowRadius: 2,
        shadowOffset: {
          height: 2,
          width: 0
        }
      }
    })
  },
  ErrorDOB: {borderBottomWidth: 1, borderBottomColor: '#ff4959'},
  ValueTab: {
    alignSelf: 'flex-start',
    borderRadius: 4,
    elevation: 1,
    padding: 12,
    marginBottom: 15,
    marginRight: 15,
    ...getPlatformElevation(3),
    ...Platform.select({
      ios: {
        backgroundColor: '#fff',
        shadowColor: colors.black,
        shadowOpacity: 0.2,
        shadowRadius: 2,
        shadowOffset: {
          height: 2,
          width: 0
        }
      }
    })
  },
  OverlayFormContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)', position: 'absolute', width: '100%', height: '100%', elevation: 6, top: 65
  },
  OverlaySubText: {backgroundColor: '#fff', justifyContent: 'flex-end'},
  OverlaySection: {
    paddingLeft: 20, paddingRight: 20, paddingTop: 16, paddingBottom: 26
  },
  laterBtn: {
    alignSelf: 'center', elevation: 2, borderRadius: 34, paddingVertical: 10, paddingHorizontal: 15
  },
  tabActive: {backgroundColor: '#008cff'},
  tabActiveDisabled: {backgroundColor: '#9b9b9b'},
  tabActiveTxt: {color: '#fff'},
  preFilledSection: {paddingHorizontal: 20, paddingTop: 16, paddingBottom: 18},
  previewForm: {margin: 12, borderRadius: 4},

  // Thankyou page
  ThankYouTopGradient: {
    paddingHorizontal: 15,
    paddingVertical: 25,
    position: 'relative'
  },
  backArrowWrapper: {
    marginTop: 10,
    marginLeft: 10,
    width: 20,
    height: 20
  },
  Wtbackarrow: {
    width: 16,
    height: 16
  },
  thankuSuccessIcon: {
    width: 64,
    height: 38
  },
  ThankYouWhiteCard: {
    backgroundColor: '#fff',
    paddingHorizontal: 14,
    paddingVertical: 14,
    elevation: 1.5,
    marginBottom: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.3,
    shadowRadius: 2
  },
  thankyouApplyVisaDtls: {
    borderBottomColor: '#ededed',
    borderBottomWidth: 1,
    paddingBottom: 15
  },
  applyVisaProcessWrapper: {
    paddingVertical: 10,
    paddingHorizontal: 14
  },
  applyVisaProcessDtls: {
    color: '#00a19c',
    padding: 10,
    backgroundColor: '#c8f8f6'
  },
  VisaAppliactionProgressHeading: {
    elevation: 1,
    marginBottom: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    marginHorizontal: -14,
    marginVertical: -14,
    paddingHorizontal: 14,
    paddingVertical: 14,
    backgroundColor: '#fff'
  },
  progressIcon: {
    width: 20,
    height: 20,
    marginRight: 10
  },
  ThankYouProgressStats: {
    paddingVertical: 20,
    paddingHorizontal: 14
  },
  lineStatusgrey: {
    width: 3,
    height: 193,
    backgroundColor: 'rgba(155,155,155,.6)'

  },
  lineStatusfill: {
    width: 3,
    height: 50,
    backgroundColor: '#21bd99',
    position: 'absolute'
  },
  circleStatus: {
    width: 14,
    height: 14,
    backgroundColor: '#ffffff',
    borderColor: 'rgba(155,155,155,.6)',
    borderWidth: 2,
    borderRadius: 14,
    marginRight: 20

  },
  circleGreenStatus: {
    width: 20,
    height: 20,
    marginRight: 20,
    marginLeft: -3
  },
  ProgressBarDtls: {
    justifyContent: 'space-between',
    width: '90%',
    marginTop: -5,
    marginLeft: -8
  },
  PaymentIcon: {
    width: 17,
    height: 21
  },
  TotalPaymentDtls: {
    width: '90%',
    flex: 1

  },
  PaymentInfoCard: {
    paddingTop: 15,
    paddingBottom: 22,
    paddingHorizontal: 15
  },
  thankuFailedIcon: {
    width: 63,
    height: 35
  },
  RefundIcon: {
    width: 20,
    height: 18
  },
  RefundPaymentDtls: {
    paddingVertical: 20
  },
  RefundChooseInnerTxt: {
    width: '70%'
  },
  RefundInfoTxt: {
    paddingLeft: 25
  },
  PickAppCard: {
    paddingHorizontal: 10,
    paddingVertical: 15,
    backgroundColor: '#ffffff'
  },
  borderLRradius4: {
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4
  },
  btnBottom: {margin: 12, borderRadius: 3, elevation: 3},
  chkboxicon: {
    width: 28,
    height: 28,
    marginRight: 8
  },
  VisaContent: {
    width: '75%',
    justifyContent: 'center'
  },
  // error status
  errorBorder: {
    borderColor: '#e53442'
  },
  // Error screenPage
  errorDetails: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    flex: 1
  },
  errorImage1: {
    width: 196,
    height: 196
  },
  errorImage2: {
    width: 251,
    height: 158
  },
  errorImage3: {
    width: 107,
    height: 113
  },
  mar70Bt: {
    marginBottom: 70
  },
  // Visa tracker cssßß
  MamiCard: {
    backgroundColor: '#fff'
  },
  padding15Left: {paddingLeft: 15},
  darkGrey: {color: '#747474'},
  CardContentTracker: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e4e4e4',
    paddingTop: 18,
    paddingBottom: 25
  },
  trackerContainer: {
    padding: 16
  },
  trackerSubContainer: {paddingLeft: 17, position: 'relative'},
  trackingBall: {
    position: 'absolute',
    zIndex: 3,
    left: 2,
    width: 32,
    height: 32,
    borderWidth: 1,
    borderColor: '#00a19c',
    borderRadius: 50,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center'
  },
  visaTrackerText: {
    borderLeftWidth: 2, borderLeftColor: '#00a19c', paddingLeft: 40, position: 'relative', zIndex: 2, paddingBottom: 24
  },
  visaTrackerTextGrey: {
    borderLeftWidth: 2, borderLeftColor: '#e5e5e5', paddingLeft: 40, position: 'relative', zIndex: 2, paddingBottom: 24
  },
  visaTrackerTextError: {borderLeftColor: '#e5e5e5'},
  visaTrackerTextLast: {
    paddingLeft: 38, position: 'relative', zIndex: 2, paddingBottom: 10
  },
  darkGrey2: {color: '#3b385e'},
  activeText: {fontSize: 14, color: '#3b385e', fontFamily: fonts.black},
  activeTextStatus: {color: '#000'},
  activeTextError: {fontSize: 14, color: '#e53442', fontFamily: fonts.black},
  errorFill: {backgroundColor: '#ffeae1', borderColor: '#e53442'},
  trackingBallGrey: {borderColor: '#e5e5e5'},
  trackingBallLightCream: {borderColor: '#f1a525', backgroundColor: '#fff2dd'},
  visaTrackerTextlightCream: {borderColor: '#f1a525'},
  marL20Neg: {marginLeft: -20},
  marL15Neg: {marginLeft: -12},
  trackingInfoActiveGreen: {
    backgroundColor: '#cdf6e8', padding: 12, borderRadius: 4, position: 'relative', flex: 1
  },
  trackerCheck: {width: 12, height: 10},
  tipIcon: {width: 8, height: 12, marginTop: 12},
  lightCreamActiveBg: {
    backgroundColor: '#fff4e3', padding: 12, borderRadius: 4, position: 'relative', flex: 1
  },
  lightNonActiveBg: {
    padding: 12, borderRadius: 4, position: 'relative', flex: 1
  },
  trackingInfoActiveCream: {
    backgroundColor: '#ffeae1', padding: 12, borderRadius: 4, position: 'relative', flex: 1
  },
  replaceIcon: {
    width: 18,
    height: 18
  },
  overlayContainer:
  {
    justifyContent: 'flex-end',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 1000,
    elevation: 3
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 1
  },
  overlayContent: {
    backgroundColor: '#fff',
    paddingVertical: 40,
    paddingHorizontal: 20,
    position: 'relative',
    zIndex: 100,
    justifyContent: 'flex-end'
  },
  marginBottom15: {marginBottom: 15},
  marginBottom32: {marginBottom: 32},
  overlayHead: {fontSize: 34, color: '#000', fontFamily: fonts.light},
  rightContent:
    {
      fontSize: 14,
      color: '#4a4a4a',
      fontFamily: fonts.regular,
      lineHeight: 22
    },
  actionTextGrey: {color: '#9b9b9b', fontSize: 16, fontFamily: fonts.regular},
  JustifySpaceBetween: {justifyContent: 'space-between'},
  actionBlueText: {color: '#008cff', fontSize: 16, fontFamily: fonts.bold},
  marginBottom35: {marginBottom: 35},
  largeCapsuleBtn: {
    width: 289,
    height: 44,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 100,
    marginTop: 30,
    marginBottom: 5,
    elevation: 3,
    padding: 5
  },
  roundImg34: {
    width: 34,
    height: 34,
    borderRadius: 50
  },

  VisaAvailableWrapper: {
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    backgroundColor: '#fff',
    paddingBottom: 15
  },
  VisaInfoTopHeading: {
    flexDirection: 'row',
    paddingHorizontal: 14,
    justifyContent: 'space-between',
    paddingVertical: 14,
    marginBottom: 4
  },
  VisaInfoTopTxt: {
    width: '60%',
    flex: 1
  },
  VisaAvailableCrousalCard: {
    elevation: 2,
    // backgroundColor:'#fff',
    marginBottom: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    borderRadius: 4,
    width: 136,
    // height:190,
    marginRight: 7


  },
  VisaAvailabeImg: {
    width: 136,
    height: 80
  },
  VisaCountryDtls: {
    padding: 6
  },
  mar14lft: {
    marginLeft: 14
  },

  VisaTabbing: {
    alignSelf: 'flex-start',
    paddingLeft: 5,
    paddingRight: 10,
    paddingVertical: 6,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 34,
    elevation: 3,
    marginRight: 10,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.3,
    backgroundColor: '#fff'


  },
  flexWrap: {flexWrap: 'wrap'},
  ovalImg: {
    width: 34, height: 34, marginRight: 6, marginLeft: 2
  },
  arrowTab: {
    width: 13, height: 7, marginLeft: 5, marginTop: 2, marginRight: 5
  },
  visaTabBtn: {
    elevation: 3,
    borderRadius: 34,
    alignSelf: 'flex-start',
    paddingHorizontal: 18,
    paddingVertical: 14,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.3,
    backgroundColor: '#fff'
  },
  VisaAdded: {
    borderTopWidth: 1, borderTopColor: '#e7e7e7', paddingHorizontal: 20, paddingVertical: 15
  },
  widthHeight34: {
    width: 34,
    height: 34
  },
  rightArrowLandingInfo: {
    width: 13,
    height: 7,
    marginTop: 5,
    marginRight: 5
  },
  cancelIconWrapper: {
    width: 24,
    height: 24
  },
  cancelIcon: {
    resizeMode: 'contain', width: 24, height: 24
  },
  visaCountryWrapper: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
    paddingTop: 25,
    paddingBottom: 20
  },
  visaFormCloseIcon: {
    alignSelf: 'flex-end',
    width: 24,
    height: 24
  },
  visaFormCloseIconImage: {
    resizeMode: 'contain',
    width: 24,
    height: 24
  },
  cardBgForm: {
    borderRadius: 4,
    elevation: 4,
    backgroundColor: 'white',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 2,
    padding: 16,
    marginBottom: 10
  },
  cardVisaForm: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(151,151,151,.2)',
    marginBottom: 10,
    marginTop: 10,
    marginLeft: -16,
    marginRight: -16
  },
  singleDropDownInput: {
    flex: 1, width: '100%', borderColor: colors.grayBorder, borderWidth: 1, padding: 14, borderRadius: 4
  },
  singleDropDownInputDisabled: {
    flex: 1, width: '100%', borderColor: '#9b9b9b', borderWidth: 1, padding: 14, borderRadius: 4
  },
  width62Percent: {
    width: '62%'
  },
  padding5: {
    padding: Platform.OS === 'ios' ? 5 : 0
  },
  whiteTint: {
    tintColor: colors.white,
    width: 16,
    height: 16
  },
  visaHeaderContainer: {
    borderRadius: 1,
    marginBottom: 1,
    flexDirection: 'row',
    height: 60,
    padding: 0,
    marginVertical: 0,
    marginHorizontal: 0,
    ...getPlatformElevation(2)
  },
  visaHeaderContent: {
    flex: 1,
    flexDirection: 'row',
    height: 60
  },
  visaHeaderIconPadding: {
    width: 48,
    height: 48,
    flexDirection: 'column',
    alignSelf: 'flex-start',
    justifyContent: 'center',
    alignItems: 'center'
  },
  visaHeaderIcon: {
    width: 16,
    height: 16
  },
  visaHeaderTripHeaderSubTitle: {
    fontSize: 14,
    fontFamily: fonts.regular,
    marginTop: 3,
    color: colors.black,
    lineHeight: 17
  },
  headerWrapper: {
    borderBottomWidth: 1,
    borderBottomColor: '#d8d8d8',
    paddingBottom: 16,
    paddingTop: 16,
    marginBottom: 16
  },
  image: {
    width: 20,
    height: 20,
    marginRight: 10
  },
  searchSection: {
    borderWidth: 1,
    borderColor: colors.grayBorder,
    backgroundColor: colors.lightGray2,
    ...marginStyles.mb3,
    ...paddingStyles.ph8,
    ...paddingStyles.pa4,
  },
  searchHeader: {
    ...fontStyles.labelSmallBold,
    color: colors.lightGray,
    ...marginStyles.mb2,
  },
  searchValue: {
    ...fontStyles.labelMediumBlack,
    color:'#000000',
  },
  searchValueSmall : {
   // ...fontStyles.labelBaseBlack,
    color: colors.black,
   fontFamily: "Lato-Black", 
   fontSize: 14, 
   fontWeight: "900", 
   ...paddingStyles.pv0,
  },
  searchSectionRow: {
    ...paddingStyles.ph6,
  },
  searchSectionRowFull: {
    width: (Dimensions.get('screen').width - 32),
    ...paddingStyles.ph6,
  },
  colWrapper:{
    display:'flex',
    flexDirection:'column',
    flex:1,
  },
  flexRow:{
    display: 'flex',
    flexDirection: 'row',
  },
  filterIcon: {
    width: 10,
    height: 6,
    ...marginStyles.mt2,
    tintColor: colors.gray,
  },
};

export default styles;
