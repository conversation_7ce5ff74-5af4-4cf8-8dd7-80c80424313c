import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Image, StyleSheet } from 'react-native';
import { VIEW_STATES } from '../../../../constants';
import { KeyboardAwareView } from 'react-native-keyboard-aware-view';
import styles from './VisaMainCss';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { getMultipleApplicationFormResponse } from '../../../../Utils/NetworkUtils';

/* Components */
import ErrorPage from '../../../../Components/Common/ErrorPage';
import LineLoader from '../../../../Components/Common/Loader';


const rendercategoryName = (category, newcategory) => {
    if (category !== newcategory) {
        return true;
    }
    else {
        return false;
    };
}

const renderContent = (type) => {
    const inputViews = [];
    inputViews.push(
        <View style={[styles.mar15Top, style_sheet.flex1]}>
            <View
                style={[styles.searchSection, style_sheet.flexRow, style_sheet.alignCenter, styles.searchSectionRow]}>
                <View style={style_sheet.flex1}>
                    <Text style={[styles.searchHeader]}>{type.displayName.toUpperCase()}</Text>
                </View>
            </View>
        </View>
    )
    return inputViews;
}

const createMasterFields = (visaForm) => {
    let masterFields = [];
    let category = '';
    visaForm?.fieldsOrder?.forEach((element) => {
        let newcategory = visaForm.fields[element].category;
        if (rendercategoryName(category, newcategory)) {
            masterFields.push(
                <View style={styles.headerWrapper}>
                    <Text style={styles.MultipleFormSectionTitle}>{newcategory}</Text>
                    <Text style={[styles.font12, styles.regularFont, styles.defaultText]}>{visaForm.fields[element].categoryDescription}</Text>
                </View>
            );
        }
        category = visaForm.fields?.[element].category;
        masterFields.push(renderContent(visaForm.fields[element]));
    });
    return masterFields;
}

const VisaFormPreFilledComponent = ({ key, index, visaForm }) => {
    return (
        <View style={[styles.whitebg]}>
            <View style={styles.FormSection} >
                {createMasterFields(visaForm)}
            </View>
        </View>
    );

}

const style_sheet = StyleSheet.create({
    displayFlex: { display: 'flex' },
    flexRow: { flexDirection: 'row' },
    alignCenter: { alignItems: 'center' },
    flex1: { flex: 1 },

});

export default VisaFormPreFilledComponent;