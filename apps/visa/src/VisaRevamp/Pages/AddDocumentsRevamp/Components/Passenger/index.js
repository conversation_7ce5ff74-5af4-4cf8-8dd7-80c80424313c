import React,{useState,useEffect} from 'react';
import { StyleSheet, View, Text, TouchableHighlight, Image, Animated, Easing,DeviceEventEmitter } from 'react-native';
import { colors } from '../../../../Styles/colors';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/fonts';
import { borderRadius } from '../../../../Styles/borderRadius';
import { isPassengerDocsUploaded, getCategoryDescription, getUploadStatus ,getCategoryStatus} from '../../utils';
import { getImagePath } from '../../../../Utils/VisaUtils';
import { VisaNavigation, VISA_ROUTE_KEYS } from '../../../../Navigation';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import { TYPE_OF_EVENTS } from 'apps/visa/src/VisaRevamp/Tracking/constants';
import { logPDTEvent } from '../../../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../../../Tracking/pdt/constants';
import { STRING_MAP } from '../../../../textStrings';

const DocumentStatus = {
  COMPLETED: 'COMPLETED',  // All docs uploaded successfully
  WARNING: 'WARNING',      // Some docs have issues
  PENDING: 'PENDING'       // Docs not uploaded yet
};

const launch = (bookingId, paxIndex, index, visaAddDocumentData, setTriggerEffect,setPaxFormStatus,openReview,  fromMIMA) => {
  visaTrackClickEvent({
    eventName: `click_upload_pax_${paxIndex}`,
    eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
  })
  logPDTEvent({
    eventValue: `click_upload_pax_${paxIndex}`,
    actionType: PDT_EVENT_TYPES.buttonClicked,
  })
  VisaNavigation.push(VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP, {
    bookingId: bookingId,
    paxIndex: paxIndex,
    index: index,
    setPaxFormStatus: setPaxFormStatus,
    setTriggerEffect: setTriggerEffect,
    visaData: visaAddDocumentData,
    openReview: openReview,
    fromMIMA: fromMIMA
  });
}

const showFormScreen = (data, subtitle, bookingId, setPaxFormStatus, setTriggerEffect) => {
  visaTrackClickEvent({
    eventName: `click_${data.name}_visa_form`,
    eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
  })
  logPDTEvent({
    eventValue: 'visa_form_click',
    actionType: PDT_EVENT_TYPES.buttonClicked,
  })
  VisaNavigation.push(VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED_REVAMP, {
    data: data,
    subTitle: subtitle,
    bookingId: bookingId,
    paxIndex: data.paxIndex,
    setPaxFormStatus: setPaxFormStatus,
    setTriggerEffect: setTriggerEffect
  });
};
// New component for document status indicators
const DocumentStatusIndicator = ({ status }) => {
  if (status === 'SUCCESS') {
    return (
      <View style={styles.statusIndicator}>
        <Image source={getImagePath('submitImage')} style={styles.statusIcon} />
      </View>
    );
  } else if (status === 'INFO' || status === 'ERROR') {
    return (
      <View style={styles.statusIndicator}>
        <Image 
          source={getImagePath('warningImage')} 
          style={[styles.statusIcon, { tintColor: colors.yellow }]} 
        />
      </View>
    );
  } else {
    return (
      <View style={styles.bulletPoint} />
    );
  }
};
// Document Category Item component
const DocumentCategoryItem = ({ item, onPress, showBulletPoint }) => {
  const status = getCategoryStatus(item);

  const showQcRejectedStatus = item?.docStatusList?.filter(doc => {
    return doc?.qcStatus !== "" && doc?.qcStatus === "REJECTED"
  });
  
  return (
    <TouchableHighlight
      onPress={onPress}
      underlayColor="#f1f1f1"
      style={styles.documentItem}
    >
      <View style={styles.documentItemContent}>
        {showBulletPoint ? (
          <View style={styles.bulletPoint} />
        ) : (
          <DocumentStatusIndicator status={showQcRejectedStatus?.length > 0 ? "ERROR" : status} />
        )}
        <Text style={styles.documentText}>{item.name}</Text>
      </View>
    </TouchableHighlight>
  );
};

const Passenger = ({ data, bookingId, subtitle, setTriggerEffect, index, setPaxFormStatus,visaAddDocumentData, openReview, fromMIMA }) => {
  const { name = '',qcStatus = '' } = data || {};
  const isComplete = data?.catStatusList?.every((category) => 
        category?.docStatusList?.every((doc) => doc?.uploaded)
      ) && data?.visaFormData?.filled;
  const showBulletPoints = data?.status === 'PASSENGER_INITIATED';
  const showInfoIcon = (data?.status === 'DOCUMENTS_PENDING' || !data?.visaFormData?.filled) && !showBulletPoints;

  // this.labelAnim = new Animated.Value(
  //   (data?.visaFormData?.filled || data?.visaFormData?.defaultValue) ? 1 : 0
  // );

  return (
    <View style={styles.cardWrapper} key={`${index}-${name}`}>
      <TouchableHighlight
        underlayColor="#f1f1f1"
        style={{...paddingStyles.pt16}}
        key={`${index}-${name}`}
        onPress={() =>
          launch(
            bookingId,
            data.paxIndex,
            index,
            visaAddDocumentData,
            setTriggerEffect,
            setPaxFormStatus,
            openReview,
            fromMIMA
          )
        }
      >
        <View>
      <View style={styles.headerWrapper}>
               {qcStatus !== "" ? (
          <View style={styles.completeIconContainer}>
            <Image source={qcStatus === "REJECTED" ? getImagePath('rejectedIcon') : getImagePath('approvedIcon')} style={styles.qcAcceptedStatusIcon} />
          </View>
        ) : (
          <>
            {isComplete && (
              <View style={styles.completeIconContainer}>
                <Image source={getImagePath('submitImage')} style={styles.submitIconStyle} />
              </View>
            )}
            {showInfoIcon && (
              <View style={styles.completeIconContainer}>
                <Image source={getImagePath('warningImage')} style={styles.submitIconStyle} />
              </View>
            )}
          </>
        )}
        <Text style={styles.headingWrapper}>{name}</Text>
        {qcStatus !== "" ?(
          <Text style={qcStatus === "REJECTED" ? styles.qcRejectedStatusText : styles.qcAcceptedStatusText}>{qcStatus?.charAt(0).toUpperCase() + qcStatus?.slice(1).toLowerCase()}</Text>
        ):(
          <Text style={styles.detailsText}>Add Details</Text>
        )}
        <Image style={styles.rightArrowStyle} source={getImagePath('rightArrow')} />
      </View>
      
      <View style={styles.documentListContainer}>
        {data.catStatusList &&
          data.catStatusList
            .sort((a, b) => a.categoryOrder - b.categoryOrder)
            .map((item, idx) => (
              <DocumentCategoryItem 
                key={`${idx}-${item.name}`}
                item={item}
                showBulletPoint={showBulletPoints}
              />
            ))}
        
        <DocumentCategoryItem 
          item={{
            name: data?.visaFormData?.title || STRING_MAP.ADD_DOCUMENTS_VISA_FORM_TITLE,
            docStatusList: [{ uploaded: data?.visaFormData?.filled }]
          }}
          showBulletPoint={showBulletPoints}
        />
      </View>
      </View>
      </TouchableHighlight>
    </View>
  );
};

const styles = StyleSheet.create({
    cardWrapper: {
        backgroundColor: colors.white,
        // ...paddingStyles.pb16,
        ...marginStyles.mb16,
        ...borderRadius.borderRadius16,
        ...marginStyles.mh16,
    },
    headerWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        flex:1,
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        borderBottomColor: colors.grayBorder,
        ...paddingStyles.pb12,
        ...paddingStyles.ph16,
    },
    headingWrapper: {
      ...fontStyles.labelLargeBlack,
      flex: 1,
      color: colors.black,
  },
    checkmarkContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.green,
      justifyContent: 'center',
      alignItems: 'center',
      ...marginStyles.mr8,
  },
  checkmarkIcon: {
    width: 14,
    height: 14,
    tintColor: colors.white,
},
documentListContainer: {
  ...paddingStyles.pt12,
},
  bulletPoint: {
    color: colors.gray,
    backgroundColor: colors.gray,
    width: 6,
    height: 6,
    borderRadius: 3,
    ...marginStyles.ml2,
},
    headingWrapper: {
        ...fontStyles.labelLargeBlack,
        flex: 1,
        color: colors.black,
    },
    subHeadingWrapper: {
        ...fontStyles.labelSmallRegular,
        color: colors.black,
        ...paddingStyles.ph14,
    },
    title: {
        flex: 1,
        color: colors.black,
        ...fontStyles.labelBaseBold,
        ...marginStyles.mb4,
    },
    subtitle: {
        flex: 1,
        color: colors.gray,
        ...fontStyles.labelSmallRegular,
    },
    documentListContainer: {
      ...paddingStyles.pt12,
      ...paddingStyles.pb16,
      flexDirection: 'row',
      flexWrap: 'wrap',
      ...paddingStyles.ph16,
      columnGap: 12,
      rowGap: 8,
  },
  documentItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
},
statusIndicator: {
  width: 20,
  height: 20,
  borderRadius: 10,
  justifyContent: 'center',
  alignItems: 'center',
},
statusIcon: {
  width: 14,
  height: 14,
},
documentText: {
  ...fontStyles.labelBaseRegular,
  color: colors.black,
   ...marginStyles.ml4,
},
    documentWrapper: {
        ...paddingStyles.pv12,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        borderBottomColor: colors.grayBorder,
    },
    successText: {
        color: colors.green,
    },
    warningText: {
        color: colors.yellow,
    },
    lastIndexStyle: {
        marginBottom: 0,
        borderBottomWidth: 0,
        paddingBottom: 0,
    },
    rightArrowStyle: {
        width: 24,
        height: 24,
    },
    flexOne: {
        flex: 1,
    },
    contentWrapper: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        flex: 1,
        ...paddingStyles.ph16,
    },
    regularFont: {
        ...fontStyles.labelBaseRegular
    },
    submitIconStyle: {
        width: 20,
        height: 20,
        ...marginStyles.mr8,
    },
    qcAcceptedStatusText: {
      ...fontStyles.labelBaseBold,
      color: colors.green,
  },
  qcRejectedStatusText: { 
      ...fontStyles.labelBaseBold,
      color: colors.red,
  },
  qcAcceptedStatusIcon: {
    width: 20,
    height: 20,
    ...marginStyles.mr8,
},
detailsText: {
  ...fontStyles.labelSmallBold,
  color: colors.primaryBlue,
  ...marginStyles.ml4,
}
});

export default Passenger;
