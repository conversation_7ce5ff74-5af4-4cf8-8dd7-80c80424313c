import { View, Text, Image, StyleSheet } from 'react-native';
import React from 'react';
import { fontStyles } from '../../../Styles/fonts';
import { colors } from '../../../Styles/colors';
import { getImagePath } from '../../../Utils/VisaUtils';


export default function InfoList() {
    const items = [
        {
            icon: 'addLaterIcon',
            title: 'Add Later',
            subtitle: 'Add documents after completing your application',
        },
        {
            icon: 'supportIcon',
            title: 'Get 24x7 Expert Support',
            subtitle: 'Get expert support for any issues during or after document upload.',
        },
        {
            icon: 'reminderIcon',
            title: 'Get Personalised Reminders',
            subtitle: 'We’ll make sure you never miss a deadline.',
        },
    ];

    return (
        <View style={styles.infoListContainer}>
            {items.map((item, idx) => (
                <View key={item.title} style={styles.infoListContainerItem}>
                    <View style={{ alignItems: 'center', width: 40 }}>
                        <View
                            style={styles.iconContainer}
                        >
                            <Image source={getImagePath(item.icon)} style={{ width: 20, height: 20, resizeMode: 'contain' }} />
                        </View>
                        {idx < items.length - 1 && (
                            <View
                                style={styles.dottedLine}
                            />
                        )}
                    </View>
                    <View style={styles.textContentWrapper}>
                        <Text style={styles.textContentTitle}>{item.title}</Text>
                        <Text style={styles.textContentDescription}>{item.subtitle}</Text>
                    </View>
                </View>
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    infoListContainer: {
        marginBottom: 12,
    },
    infoListContainerItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    textContentWrapper: {
        flex: 1,
        marginLeft: 8,
        marginBottom: 16,
    },
    textContentTitle: {
        ...fontStyles.labelBaseBold,
        color: colors.black,
    },
    textContentDescription: {
        ...fontStyles.labelSmallRegular,
        color: colors.gray,
        marginTop: 2,
    },
    dottedLine: {
        width: 2,
        flex: 1,
        borderStyle: 'dotted',
        borderWidth: 1,
        borderColor: '#3A6FAE',
    },
    iconContainer: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: '#EAF5FF',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1,
    },
});