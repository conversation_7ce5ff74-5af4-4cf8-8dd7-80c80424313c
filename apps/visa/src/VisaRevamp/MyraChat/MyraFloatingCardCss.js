import { colors } from "../Styles/colors";

export default (footerHeight = 0) => {
  const styles = {
    floatingCardWrapper: {
      position: 'absolute',
      right: 20,
      bottom: 20 + footerHeight,
      width: 60,
      zIndex: 10000,
    },
    needHelpIconWrap: {
      width: '100%',
      height: 60,
      elevation: 3,
    },
    unreadBadge: {
      position: 'absolute',
      right: 0,
      top: 0,
      backgroundColor: colors.red,
      borderRadius: 10,
      color: colors.white,
      paddingHorizontal: 5,
      fontSize: 12,
      fontWeight: 'bold',
    },
    unreadBadgeCirlce: {
      position: 'absolute',
      right: 0,
      top: 0,
      backgroundColor: colors.red,
      width: 20,
      height: 20,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
  return styles;
};
