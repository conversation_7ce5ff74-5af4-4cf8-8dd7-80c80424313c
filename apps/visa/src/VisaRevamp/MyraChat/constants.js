import { IMAGE_KEYS, getImagePath } from '../Utils/VisaUtils';

export const MYRA_ICON = getImagePath(IMAGE_KEYS.myraChatIcon);

export const CHAT_BOT_EVENTS = {
  onChatMessageUpdate: 'CHAT_BOT__BRIDGE__ON_CHAT_MESSAGE_UPDATE',
  minimizeView: 'CHAT_BOT__BRIDGE__MINIMIZE_VIEW',
  closeView: 'CHAT_BOT__BRIDGE__CLOSE_VIEW',
  jsLoaded: 'CHAT_BOT__BRIDGE__JS_LOADED',
  appLoaded: 'CHAT_BOT__BRIDGE__APP_LOADED',
  triggerLogin: 'CHAT_BOT__BRIDGE__TRIGGER_LOGIN',
  triggerAnalytics: 'CHAT_BOT__BRIDGE__TRIGGER_ANALYTICS',
  getMetaData: 'CHAT_BOT__BRIDGE__GET_META_DATA',
};
