// Define the search context type
export interface SearchContext {
  to?: {
    location?: {
      name?: string;
    };
    locus?: {
      locus_id?: string;
    };
  };
}

// Define the page context type
export interface PageContext {
  branch?: string;
  page_name?: string;
  funnel_step?: string | number;
}

// Define the return type
export interface QueryDto {
  destinationCity?: string;
  branch: string;
  pageName?: string;
  funnelStep?: string | number;
  omniPageName?: string;
}
