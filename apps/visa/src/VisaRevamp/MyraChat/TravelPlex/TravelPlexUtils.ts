import { SearchContext, PageContext, QueryDto } from './types';

export const createQueryDto = (
  search_context?: SearchContext,
  page_context?: PageContext,
  attr2?: string // This is a tag destination
): QueryDto => {
  // Helper function to check if value is valid (not null, undefined, empty string, or "null" string)
  const isValidValue = (value: any): boolean =>
    value != null && value !== '' && value !== 'null' && value !== 'undefined';

  // Select the first valid destination value
  const destinationCity =
    (isValidValue(attr2) ? attr2 : null) ||
    (isValidValue(search_context?.to?.location?.name) ? search_context?.to?.location?.name : null) ||
    (isValidValue(search_context?.to?.locus?.locus_id) ? search_context?.to?.locus?.locus_id : null) ||
    '';

  return {
    destinationCity,
    branch: page_context?.branch || 'DOM',
    pageName: page_context?.page_name,
    funnelStep: page_context?.funnel_step,
    omniPageName: page_context?.page_name,
  };
};
