import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  BackHandler,
  Animated,
  Easing,
} from 'react-native';
import { WebView } from 'react-native-webview';
import isEmpty from 'lodash/isEmpty';
import FlightBookingModule from 'packages/legacy-commons/Native/FlightBookingModule';
// import SpinLoader from 'apps/post-sales/src/Common/SpinLoader';
import { setWebViewAuth } from '../Utils';
import CircleLoaderComp from '../Components/Common/Loader';

const MyraBot = ({
  url,
  chatUrl = '',
  handleCloseOverlay,
  visible,
  mountView,
  unMountWebView,
  onEventReceived,
}) => {
  const [auth, setAuth] = useState(null);
  const [loading, setLoading] = useState(true);
  const [canGoBack, setCanGoBack] = useState(false);

  const webViewRef = useRef(null);
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(500)).current;
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const fetchAuth = async () => {
      try {
        setLoading(true);
        const userInfo = await FlightBookingModule.getRequestHeader('', 'NEED_HELP');
        setAuth(userInfo?.mmtAuth);
      } catch (error) {
        console.error('Error fetching auth:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAuth();
  }, []);

  useEffect(() => {
    if (visible) {
      setIsAnimating(true);
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
      Animated.timing(translateYAnim, {
        toValue: 0,
        duration: 150,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } else {
      setIsAnimating(true);
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }).start();
      Animated.timing(translateYAnim, {
        toValue: 500,
        duration: 300,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }).start(() => {
        setIsAnimating(false);
      });
    }
  }, [visible]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', onBackPress);
    return () => backHandler.remove();
  }, [canGoBack, visible]);

  const onNavigationStateChange = (navState) => {
    setCanGoBack(navState.canGoBack);
  };

  const onBackPress = useCallback(() => {
    if (canGoBack) {
      webViewRef.current.goBack();
      return true;
    }

    if (visible) {
      handleCloseOverlay();
      return true;
    }
    unMountWebView();
    return false;
  }, [canGoBack, visible]);

  const onMessage = (event) => {
    const eventData = JSON.parse(event.nativeEvent.data);
    if (onEventReceived) {
      onEventReceived(eventData);
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: opacityAnim,
          transform: [{ translateY: translateYAnim }],
          display: isAnimating ? 'flex' : 'none',
        },
      ]}
    >
      {!isEmpty(url) && mountView && (
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          enabled
        >
          {loading ? ( null
            // <CircleLoaderComp />
          ) : (
            <WebView
              ref={webViewRef}
              source={{ uri: url }}
              style={styles.webView}
              showsVerticalScrollIndicator={false}
              injectedJavaScriptBeforeContentLoaded={setWebViewAuth(auth) || null}
              sharedCookiesEnabled={true}
              startInLoadingState
              onNavigationStateChange={onNavigationStateChange}
              onMessage={onMessage}
            />
          )}
        </KeyboardAvoidingView>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Semi-transparent background
    zIndex: 10000,
  },
  webView: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});

export default MyraBot;
