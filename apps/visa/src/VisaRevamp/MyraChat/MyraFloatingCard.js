import React, { useContext, useState, useRef, useCallback, useMemo } from 'react';
import { View, StyleSheet, Text, Platform } from 'react-native';
// import { ThemeContext } from '../../theme/theme.context';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import createStyle from './MyraFloatingCardCss';
import fecha from'fecha';
import { MYRA_ICON, CHAT_BOT_EVENTS } from './constants';
import MyraBot from './MyraBot';
import { TYPE_OF_EVENTS } from '../Tracking/constants';
import { visaTrackClickEvent } from '../Tracking/utils';
import {
  ChatBotViewState,
  OnActionProps,
  TravelPlexBot,
  TravelPlexBotProps,
  TravelPlexBotRef,
  TravelplexFloatingCta,
} from '@travelplex/react-native';


const MyraFloatingCard = ({ footerHeight = 0, pageName = 'DEFAULT', invalidateChatViewData = false,
  onTravelPlexDataUsed = () => { }, bookingId = '', region = '', tripType = '', fromDate = null, toDate = null
}) => {
  const tickers = ["Hi, I'm Myra"];
  const chatBotRef = useRef(null);

  const [travelPlexConfig, setTravelPlexConfig] = useState(chatConfig);
  const [showChatBot, setShowChatBot] = useState(false);
  // const { psTheme: theme } = useContext(ThemeContext);
  const styles = createStyle(footerHeight);
  const [mountWebView, setMountWebView] = useState(true);
  const [showChatBotOverlay, setShowChatBotOverlay] = useState(false);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);
  const botIconUrl = MYRA_ICON;

  const createChatID = (appendPrefix = true) => {
    const time = Date.now();
    var minm = 100000;
    var maxm = 999999;
    const randomNumber = Math.floor(Math.random() * (maxm - minm + 1)) + minm;
    const prefix = appendPrefix ? 'VISA:' : '';
    const result = prefix + 'chatId_' + time + '_' + randomNumber;
    return result;
  };
  const uniqChatId = createChatID(true);

  const search_context = {
    'depCityData': '',
  };
  const pageContext = {
    'lob': "VISA",
    'lobCategory': "VISA",
    'pageName': pageName,
    'prevPageName': '',
    'lobFunnel': '',
    'subpageName': '',
    'funnelStep': 'landing',
    'pgTimeInterval': '',
    'navigation': '',
    'subLob': 'MMT',
    'pageUrl': '',
  };

  const chatConfig = {
    "context": {
      "lob": "VISA",
      "lobCategory": "",
      "view": "VISA",
      "prevPage": null,
      "platform": "ios"
    },
    "expertMetadata": {},
    "contextMetadata": {
      "pageContext": {
        "lob": "VISA",
        "lobCategory": "VISA",
        "pageName": pageName,
        "prevPageName": null
      },
      "searchContext": {
        "bookingId": bookingId,
        "region": region,
        "tripType": tripType,
        "fromDateTime": {
          'timestamp': fromDate,
        },
        "toDateTime": {
          'timestamp': toDate,
        },
      }
    }
  }


  const openChat = useCallback(() => {
    setShowChatBot(true);
    chatBotRef.current?.expand({ chatConfig, invalidateChatViewData });
    // Call the callback to notify parent that TravelPlex data has been consumed
    // Only call when invalidateChatViewData is true, indicating new data was processed
    if (invalidateChatViewData) {
      onTravelPlexDataUsed();
    }
  }, [chatConfig, invalidateChatViewData, onTravelPlexDataUsed]);

  // Memoized callback for closing chat
  const closeChat = useCallback(() => {
    chatBotRef.current?.collapse();
  }, []);

  const openChatBotOverlay = () => {
    setMountWebView(true);
    openChat();
    visaTrackClickEvent({
      eventName: "myraChat",
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    })
    unreadMessageCount > 0 && setUnreadMessageCount(0);

  };

  const unMountWebView = () => {
    console.log('UnMounting Chat Bot Web View: ', pageName);
    setMountWebView(false);
  };

  const closeChatBotOverlay = () => {
    setShowChatBotOverlay(false);
  };

  const handleNewMessage = ({ type, payload = {} }) => {
    switch (type) {
      case CHAT_BOT_EVENTS.onChatMessageUpdate:
        const unreadCount = Number.isInteger(payload.unreadCount)
          ? payload.unreadCount
          : parseInt(payload.unreadCount, 10) || 0;
        !showChatBotOverlay && setUnreadMessageCount(unreadCount);
        break;
      case CHAT_BOT_EVENTS.minimizeView:
        closeChatBotOverlay();
        break;
      case CHAT_BOT_EVENTS.closeView:
        unMountWebView();
    }
  };

  const onViewStateChange = useCallback((viewState) => {
    setShowChatBot(viewState === 'expanded');
  }, []);

  const handleAction = useCallback((params) => {
    if (params?.actionType !== 'Analytics') {
      return;
    }
    if (!Array.isArray(params?.tracking)) {
      return;
    }
  }, [search_context, pageContext]);

  const chatBotWebView = () => {
    return (
      <View
        style={[
          StyleSheet.absoluteFillObject,
          {
            display: showChatBot ? 'flex' : 'none',
          },
        ]}
      >
        <TravelPlexBot
          onViewStateChange={onViewStateChange}
          ref={chatBotRef}
          onAction={handleAction}
          chatConfig={chatConfig}
        />
      </View>
    );
  };

  return (
    <>
      <View style={[styles.floatingCardWrapper]}>
        <TravelplexFloatingCta
          textScrollProps={{
            texts: tickers,
          }}
          onPress={openChatBotOverlay}
          shouldAnimate={true}
          showNewMessageIcon={unreadMessageCount > 0}
        />
      </View>
      {mountWebView && chatBotWebView()}
    </>
  );
};

export default MyraFloatingCard;
