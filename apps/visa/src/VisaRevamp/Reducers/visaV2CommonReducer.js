import { visaActionTypes } from '../Actions/visaActions';
import { visaV2CommonActions } from '../Actions/visaV2Actions';

const initialState = {
  visaTypeDetails: [],
  visaDetails: {
    dateOfEntry: '',
    dateOfExit: '',
    countryName: '',
  },
  trackingParams: {},
};

const visaV2CommonReducer = (state = initialState, action) => {
  switch (action.type) {
    case visaV2CommonActions.SET_VISA_TYPE_DETAILS:
      return {
        ...state,
        visaTypeDetails: action.payload,
      };
    case visaActionTypes.SET_VISA_DETAIL:
      return {
        ...state,
        visaDetails: {
          ...state.visaDetails,
          dateOfEntry: action.payload.dateOfEntry,
          dateOfExit: action.payload.dateOfExit,
          countryName: action.payload.countryName,
        }
      };
      case visaActionTypes.SET_VISA_COUNTRY_INFO:
        return {
          ...state,
          visaDetails: {
            ...state.visaDetails,
           countryDetails: {
            ...state?.visaDetails?.countryDetails || {},
            ...action.payload?.countryDetails || {}
          }, // countryDetails
          },
          trackingParams: {
            ...state?.trackingParams || {},
            ...action?.payload?.trackingParams || {},
          }
        };
      case visaActionTypes.SET_VISA_TRACKING_PARAMS: 
        return {
          ...state,
          trackingParams: {
            ...state?.trackingParams,
            ...action?.payload,
          }
        }
      case visaActionTypes.CLEAR_VISA_DETAILS:
        return {
          ...state,
          visaDetails: {},
          trackingParams: {},
        }
      case visaActionTypes.SET_ERROR_MESSAGE:
        return {
          ...state,
          trackingParams: {
            ...state?.trackingParams || {},
            errorMessage: action.payload.errorMessage,
          }
        }
      case visaV2CommonActions.SET_THANKYOU_BOOKING_ID:
        return {
          ...state,
          trackingParams: {
            ...state?.trackingParams || {},
            bookingId: action.payload,
          }
        }
    default:
      return state;
  }
};

export default visaV2CommonReducer;
