import { pdtLoggerActions } from '../Actions/pdtLoggerActions';

const initialState = {
  page_context: {
    sub_page_name: '',
  },
  event_tracking_context: {},
  search_context: {},
  traveller_info: [],
  event_detail: {
    components: {
      booking_info: {},
    },
  },
  error_details_list: {},
  experiment_details: {},
  campaign_details: {},
};

const visaPdtLoggerReducer = (state = initialState, action) => {
  switch (action.type) {
    case pdtLoggerActions.UPDATE_INITIAL_LOGGER_DATA:
      return {
        ...state,
        ...action.payload,
      };
    case pdtLoggerActions.UPDATE_SUB_PAGE_NAME:
      return {
        ...state,
        page_context: {
          ...state.page_context,
          sub_page_name: action.payload,
        },
      };
    case pdtLoggerActions.CLEAR_SUB_PAGE_NAME:
      return {
        ...state,
        page_context: {
          ...state.page_context,
          sub_page_name: '',
        },
      };
    case pdtLoggerActions.UPDATE_SEARCH_CONTEXT:
      return {
        ...state,
        search_context: {
          ...state.search_context,
          travel_purpose_opted: true,
          visa_type: action.payload.visa_type,
          search_type: action.payload.search_type,
          journey_type: action.payload.journey_type,
        },
      };
    case pdtLoggerActions.CLEAR_SEARCH_CONTEXT:
      return {
        ...state,
        page_context: {
          ...state.page_context,
          sub_page_name: '',
          lob_category: '',
        },
        search_context: {
          from: {
            location: { name: 'India', type: 'country', country_code: 'IN', country: 'India', code: 'IN' },
          }
        },
      };
    case pdtLoggerActions.UPDATE_TO_AND_FROM_DATE_TIME:
      return {
        ...state,
        search_context: {
          ...state.search_context,
          from_date_time: action.payload.fromDateTime,
          to_date_time: action.payload.toDateTime,
          advance_purchase: action.payload.advance_purchase,
        },
      };
    case pdtLoggerActions.CLEAR_TO_AND_FROM_DATE_TIME:
      return {
        ...state,
        search_context: {
          ...state.search_context,
          from_date_time: '',
          to_date_time: '',
          advance_purchase: '',
        },
      };
    case pdtLoggerActions.UPDATE_TRAVELLER_INFO:
      return {
        ...state,
        traveller_info: action.payload.traveller_info,
        search_context: {
          ...state.search_context,
          pax: action.payload.pax_details,
        },
      };
    case pdtLoggerActions.CLEAR_TRAVELLER_INFO:
      return {
        ...state,
        traveller_info: [],
        search_context: {
          ...state.search_context,
          pax: {},
        },
      };
    case pdtLoggerActions.UPDATE_BOOKING_INFO:
      return {
        ...state,
        event_detail: {
          ...state.event_detail,
          components: {
            ...state.event_detail.components,
            booking_info: action.payload,
          },
        },
      };
    case pdtLoggerActions.CLEAR_BOOKING_INFO:
      return {
        ...state,
        event_detail: {
          ...state.event_detail,
          components: {
            ...state.event_detail.components,
            booking_info: {},
          },
        },
      };
    default:
      return state;
  }
};

export default visaPdtLoggerReducer;
