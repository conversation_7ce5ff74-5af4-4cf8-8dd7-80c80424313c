import { RouteConfig } from '@mmt/navigation';
import { VISA_ROUTE_KEYS } from './visaPageKeys';
import { ReduxOptions } from '@mmt/navigation/src/types';
import store from '@mmt/legacy-commons/AppState/Store';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';

const reduxOptions: ReduxOptions = {
  // @ts-ignore
  storeFactory: () => store,
};

const visaRouteConfig: RouteConfig[] = [
  {
    key: VISA_ROUTE_KEYS.LANDING_REVAMP,
    component: () => require('../Pages/Landing').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.SAMPLE_DOC,
    component: () => require('../Pages/SampleDoc').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.SEARCH_PAGE,
    component: () => require('../Pages/SearchPage').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.DETAIL,
    component: () => require('../Pages/Detail').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.ADD_TRAVELLER,
    component: () => require('../Pages/AddTravellers').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.SELECT_PLANS,
    component: () => require('../Pages/SelectPlans').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.ADD_DOCUMENTS,
    component: () => require('../Pages/AddDocuments').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.ADD_DOCUMENTS_REVAMP,
    component: () => require('../Pages/AddDocumentsRevamp').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.ADD_TRAVELLER_FORM,
    component: () => require('../Pages/AddTravellers/AddTravellerForm').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS,
    component: () => require('../Pages/UploadDocument').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP,
    component: () => require('../Pages/AddDocumentsRevamp/Components/UploadDocumentsRevamp').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.REVIEW,
    component: () => require('../Pages/Review').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.FAQS,
    component: () => require('../Pages/Detail/Components/FAQs/AllFAQs').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.PASSPORT_SAFETY_TIPS,
    component: () => require('../Pages/Detail/Components/PassportSafetyTips').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.THANK_YOU,
    component: () => require('../Pages/ThankYou').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.MULTICOUNTRY_PAGE,
    component: () => require('../Components/Common/MultiCountry').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.LEARN_MORE,
    component: () => require('../Pages/LearnMore').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED,
    component: () => require('../Pages/AddDocuments/Components/VisaForm/MultipleVisaFormPreFilled').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED_REVAMP,
    component: () => require('../Pages/AddDocumentsRevamp/Components/VisaForm/MultipleVisaFormPreFilled').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.BULK_UPLOAD,
    component: () => require('../Pages/AddDocuments/Components/UploadAllDocuments/UploadedScreen').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM,
    component: () => require('../Pages/AddDocuments/Components/UploadAllDocuments/ConfirmScreen').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.BULK_UPLOAD_EDIT,
    component: () => require('../Pages/AddDocuments/Components/UploadAllDocuments/EditScreen').default,
    reduxOptions,
  },
  {
    key: VISA_ROUTE_KEYS.IMAGE_VIEWER,
    component: () => require('../Pages/AddDocumentsRevamp/Components/ImageViewer').default,
    reduxOptions,
  }

];

export default visaRouteConfig;
