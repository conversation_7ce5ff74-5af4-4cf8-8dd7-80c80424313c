import { navigation } from '@mmt/navigation';
export { VISA_ROUTE_KEYS } from './visaPageKeys';
export { navigation as VisaNavigation } from '@mmt/navigation';
/**
 *  function returns the name of current route in the navigation stack
 * 
 */
export const getCurrentRoute = () => {
  const state = navigation?.navigationRef?.dangerouslyGetState();
  const { routes } = state || {};
  let topRoute = null;
  if (routes?.length) {
    topRoute = routes[routes.length - 1];
  }

  return topRoute?.name;
};