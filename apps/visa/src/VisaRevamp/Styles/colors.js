export const colors = {
  black: '#000000',
  lightGray4: '#f0eeee',
  lightGray5: '#e6e6e6',
  lightGray6: '#f0f0f0',
  disableGrayBg: '#CBCBCB',
  lightGrayBorder: '#e8e8e8',
  extraLightGrayBorder: '#f4f4f4',
  darkGray: '#282828',
  red: '#EC2127',
  fadedRed: '#FCDADB',
  fadedGreen: '#E6FFF9',
  primaryBlue: '#008CFF',
  midLightBlue: '#CCE8FF',
  darkBlue: '#16477F',
  darkBlue5: '#0c58b4',
  lightBlueBg:'#EAF5FF',
  gold: '#EDD287',
  lightBeige: '#E6D7B7',
  purple: '#834CC5',
  orangeGradient: '#FF3E5E',
  orangeGradientDark: '#FF7F3F',
  lightRedBg: '#FFFAFA',
  skyBlue: '#D3E7FF',
  orange: '#FFE0CB',

  /* Colors used */
  white: '#FFFFFF',
  gray: '#4A4A4A',
  gray2: '#9b9b9b',
  grayBorder: '#D8D8D8',
  lightSkyBlue: '#f1f9fd',
  lightBlue: '#f3f8ff',
  lightBlue2: '#ecf5ff',
  lightGray: '#757575',
  lightGray2: '#F2F2F2',
  lightGray3: '#F6F6F6',
  darkBlue: '#2B379D',
  darkBlue2: '#0C58B4',
  lightPurple: '#eff1ff',
  darkPurple: '#4a2fb5',
  greenDark: '#1a7971',
  grayBorder: '#D8D8D8',
  disableGrayBg: '#CBCBCB',
  transparent: 'transparent',
  yellow: '#CF8100',
  fadedYellow: '#FFEDD1',
  green: '#007E7D',
  lightGreen: '#33d18f',
  mintGreen: "#b6ece2",
  darkPink:'#7243C0',
  
  
};

export const buttonColors = {
  primaryBtnColor: '#53B2FE',
  secondaryBtnColor: '#065AF3',
};

export const expressVisaColors = {
  primaryColor: '#f5f4fd',
  secondaryColor: '#f1dbf5',
};
export const lightBlueGradient = ['#f3f8fe', '#e8f1fe'];

export const formatColorCode = (colorCode) =>
  colorCode.startsWith('#') ? colorCode : `#${colorCode}`;
