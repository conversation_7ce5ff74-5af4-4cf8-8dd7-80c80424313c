import { PixelRatio, Dimensions } from 'react-native';

const BASE_WIDTH = 360;
const BASE_HEIGHT = 640;

const basedSizeTypes = {
  HEIGHT: 'height',
  WIDTH: 'width',
};

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const widthBaseScale = (SCREEN_WIDTH > 410 ? 411 : 360) / BASE_WIDTH;
const heightBaseScale = SCREEN_HEIGHT / BASE_HEIGHT;

function normalize(size, based = basedSizeTypes.WIDTH) {
  const newSize = based === basedSizeTypes.HEIGHT ? size * heightBaseScale : size * widthBaseScale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
}

//for width  pixel
const widthPixel = (size) => {
  return normalize(size, basedSizeTypes.WIDTH);
};
//for height  pixel
const heightPixel = (size) => {
  return normalize(size, basedSizeTypes.HEIGHT);
};
//for font  pixel
const fontPixel = (size) => {
  return heightPixel(size);
};
//for Margin and Padding vertical pixel
const pixelSizeVertical = (size) => {
  return heightPixel(size);
};
//for Margin and Padding horizontal pixel
const pixelSizeHorizontal = (size) => {
  return widthPixel(size);
};
export { widthPixel, heightPixel, fontPixel, pixelSizeVertical, pixelSizeHorizontal };
