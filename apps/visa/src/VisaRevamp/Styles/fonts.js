import { fonts } from 'packages/legacy-commons/Styles/globalStyles';
import { Platform } from 'react-native';
import memoizeOne from 'memoize-one';
import { getScreenWidth } from '../Utils';

const DEFAULT_FONT_INCREASE = 2;
const CURRENT_SCREEN_WITH = getScreenWidth();
export const DEFAULT_SCREEN_SIZE = 410;

const FONT_SIZES = {
  HEADING_MEDIUM: 'headingMedium',
  HEADING_REGULAR: 'headingRegular',
  HEADING_BASE: 'headingBase',
  LARGE: 'LARGE',
  MEDIUM: 'medium',
  BASE: 'base',
  SMALL: 'small',
};

const FONT_WEIGHTS = {
  REGULAR: 'regular',
  BLACK: 'black',
  BOLD: 'bold',
};

export const fontWeights = {
  [FONT_WEIGHTS.BLACK]: '900',
  [FONT_WEIGHTS.BOLD]: '700',
  [FONT_WEIGHTS.REGULAR]: '400',
};

export const fontSizes = {
  [FONT_SIZES.HEADING_MEDIUM]: 24,
  [FONT_SIZES.HEADING_REGULAR]: 22,
  [FONT_SIZES.HEADING_BASE]: 18,
  [FONT_SIZES.LARGE]: 18,
  [FONT_SIZES.MEDIUM]: 16,
  [FONT_SIZES.BASE]: 14,
  [FONT_SIZES.SMALL]: 12,
};

export const getFontFamily = memoizeOne((weight) => {
  return {
    ...Platform.select({
      ios: {
        fontFamily: fonts.regular,
      },
      android: {
        fontFamily:
          weight === FONT_WEIGHTS.BLACK
            ? fonts.black
            : weight === FONT_WEIGHTS.BOLD
            ? fonts.bold
            : fonts.regular,
      },
    }),
  };
});

export const headingStyles = memoizeOne((type) => {
  let fontSize;
  
  switch (type) {
    case FONT_SIZES.HEADING_MEDIUM:
      fontSize = fontSizes.headingMedium;
      break;
    case FONT_SIZES.HEADING_BASE:
      fontSize = fontSizes.headingBase;
      break;
    case FONT_SIZES.HEADING_REGULAR:
      fontSize = fontSizes.headingRegular;
      break;
    default:
      return {}; // return an empty object for unknown types
  }

  return {
    fontSize: normaliseFont(fontSize),
    fontWeight: fontWeights.black,
    ...getFontFamily(FONT_WEIGHTS.BLACK),
  };
});

export const normaliseFont = memoizeOne((fontSize) => {
  const screenSize = CURRENT_SCREEN_WITH;
  if (screenSize > DEFAULT_SCREEN_SIZE) {
    return fontSize + DEFAULT_FONT_INCREASE;
  }

  return fontSize;
});

export const labelStyles = memoizeOne(({ size, weight }) => {
  const fontSize = normaliseFont(fontSizes[size]);
  return {
    fontSize,
    fontWeight: fontWeights[weight],
    ...getFontFamily(weight),
    lineHeight: 19,
  };
});

export const fontStyles = {
  headingMedium: headingStyles(FONT_SIZES.HEADING_MEDIUM),

  headingRegular: headingStyles(FONT_SIZES.HEADING_REGULAR),

  headingBase: headingStyles(FONT_SIZES.HEADING_BASE),

  labelLargeRegular: labelStyles({ size: FONT_SIZES.LARGE, weight: FONT_WEIGHTS.REGULAR }),

  labelLargeBold: labelStyles({ size: FONT_SIZES.LARGE, weight: FONT_WEIGHTS.BOLD }),

  labelLargeBlack: labelStyles({ size: FONT_SIZES.LARGE, weight: FONT_WEIGHTS.BLACK }),

  labelMediumRegular: labelStyles({ size: FONT_SIZES.MEDIUM, weight: FONT_WEIGHTS.REGULAR }),

  labelMediumBold: labelStyles({ size: FONT_SIZES.MEDIUM, weight: FONT_WEIGHTS.BOLD }),

  labelMediumBlack: labelStyles({ size: FONT_SIZES.MEDIUM, weight: FONT_WEIGHTS.BLACK }),

  labelSmallRegular: labelStyles({ size: FONT_SIZES.SMALL, weight: FONT_WEIGHTS.REGULAR }),

  labelSmallBold: labelStyles({ size: FONT_SIZES.SMALL, weight: FONT_WEIGHTS.BOLD }),

  labelSmallBlack: labelStyles({ size: FONT_SIZES.SMALL, weight: FONT_WEIGHTS.BLACK }),

  labelBaseRegular: labelStyles({ size: FONT_SIZES.BASE, weight: FONT_WEIGHTS.REGULAR }),

  labelBaseBold: labelStyles({ size: FONT_SIZES.BASE, weight: FONT_WEIGHTS.BOLD }),

  labelBaseBlack: labelStyles({ size: FONT_SIZES.BASE, weight: FONT_WEIGHTS.BLACK }),
};
