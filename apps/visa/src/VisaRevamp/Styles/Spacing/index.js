/* SPACING

   Base:
     p = padding
     m = margin

   Modifiers:
     a = all
     h = horizontal
     v = vertical
     t = top
     r = right
     b = bottom
     l = left

*/

import { colors } from '../colors';

const spacing = {
  none: 0,
  two: 2,
  four: 4,
  six: 6,
  eight: 8,
  ten: 10,
  twelve: 12,
  fourteen: 14,
  sixteen: 16,
  eighteen: 18,
  twenty: 20,
  twentyFour: 24,
  twentySix: 26,
  thirty: 30,
  thirtyTwo: 32,
  thirtyFour: 34,
  fourty: 40,
  fortyEight: 48,
  seventy: 70,
};

const paddingStyles = {
  pa0: { padding: spacing.none },
  pa2: { padding: spacing.two },
  pa4: { padding: spacing.four },
  pa6: { padding: spacing.six },
  pa8: { padding: spacing.eight },
  pa10: { padding: spacing.ten },
  pa12: { padding: spacing.twelve },
  pa14: { padding: spacing.fourteen },
  pa16: { padding: spacing.sixteen },
  pa18: { padding: spacing.eighteen },
  pa20: { padding: spacing.twenty },
  pa24: { padding: spacing.twentyFour },
  pa30: { padding: spacing.thirty },
  pa40: { padding: spacing.fourty },

  pl0: { paddingLeft: spacing.none },
  pl2: { paddingLeft: spacing.two },
  pl4: { paddingLeft: spacing.four },
  pl6: { paddingLeft: spacing.six },
  pl8: { paddingLeft: spacing.eight },
  pl10: { paddingLeft: spacing.ten },
  pl12: { paddingLeft: spacing.twelve },
  pl14: { paddingLeft: spacing.fourteen },
  pl16: { paddingLeft: spacing.sixteen },
  pl18: { paddingLeft: spacing.eighteen },
  pl20: { paddingLeft: spacing.twenty },
  pl30: { paddingLeft: spacing.thirty },
  pl40: { paddingLeft: spacing.fourty },

  pr0: { paddingRight: spacing.none },
  pr2: { paddingRight: spacing.two },
  pr4: { paddingRight: spacing.four },
  pr6: { paddingRight: spacing.six },
  pr8: { paddingRight: spacing.eight },
  pr10: { paddingRight: spacing.ten },
  pr12: { paddingRight: spacing.twelve },
  pr14: { paddingRight: spacing.fourteen },
  pr16: { paddingRight: spacing.sixteen },
  pr18: { paddingRight: spacing.eighteen },
  pr20: { paddingRight: spacing.twenty },
  pr30: { paddingRight: spacing.thirty },
  pr40: { paddingRight: spacing.fourty },

  pb0: { paddingBottom: spacing.none },
  pb2: { paddingBottom: spacing.two },
  pb4: { paddingBottom: spacing.four },
  pb6: { paddingBottom: spacing.six },
  pb8: { paddingBottom: spacing.eight },
  pb10: { paddingBottom: spacing.ten },
  pb12: { paddingBottom: spacing.twelve },
  pb14: { paddingBottom: spacing.fourteen },
  pb16: { paddingBottom: spacing.sixteen },
  pb18: { paddingBottom: spacing.eighteen },
  pb20: { paddingBottom: spacing.twenty },
  pb24: { paddingBottom: spacing.twentyFour },
  pb30: { paddingBottom: spacing.thirty },
  pb40: { paddingBottom: spacing.fourty },
  pb48: { paddingBottom: spacing.fortyEight },

  pt0: { paddingTop: spacing.none },
  pt2: { paddingTop: spacing.two },
  pt4: { paddingTop: spacing.four },
  pt6: { paddingTop: spacing.six },
  pt8: { paddingTop: spacing.eight },
  pt10: { paddingTop: spacing.ten },
  pt12: { paddingTop: spacing.twelve },
  pt14: { paddingTop: spacing.fourteen },
  pt16: { paddingTop: spacing.sixteen },
  pt18: { paddingTop: spacing.eighteen },
  pt20: { paddingTop: spacing.twenty },
  pt24: { paddingTop: spacing.twentyFour },
  pt26: { paddingTop: spacing.twentySix },
  pt30: { paddingTop: spacing.thirty },
  pt40: { paddingTop: spacing.fourty },

  pv0: {
    paddingTop: spacing.none,
    paddingBottom: spacing.none,
  },
  pv2: {
    paddingTop: spacing.two,
    paddingBottom: spacing.two,
  },
  pv4: {
    paddingTop: spacing.four,
    paddingBottom: spacing.four,
  },
  pv6: {
    paddingTop: spacing.six,
    paddingBottom: spacing.six,
  },
  pv8: {
    paddingTop: spacing.eight,
    paddingBottom: spacing.eight,
  },
  pv10: {
    paddingTop: spacing.ten,
    paddingBottom: spacing.ten,
  },
  pv12: {
    paddingTop: spacing.twelve,
    paddingBottom: spacing.twelve,
  },

  pv14: {
    paddingTop: spacing.fourteen,
    paddingBottom: spacing.fourteen,
  },
  pv16: {
    paddingTop: spacing.sixteen,
    paddingBottom: spacing.sixteen,
  },
  pv18: {
    paddingTop: spacing.eighteen,
    paddingBottom: spacing.eighteen,
  },
  pv20: {
    paddingTop: spacing.twenty,
    paddingBottom: spacing.twenty,
  },
  pv30: {
    paddingTop: spacing.thirty,
    paddingBottom: spacing.thirty,
  },
  pv32: {
    paddingTop: spacing.thirtyTwo,
    paddingBottom: spacing.thirtyTwo,
  },
  ph0: {
    paddingLeft: spacing.none,
    paddingRight: spacing.none,
  },

  ph2: {
    paddingLeft: spacing.two,
    paddingRight: spacing.two,
  },

  ph4: {
    paddingLeft: spacing.four,
    paddingRight: spacing.four,
  },

  ph6: {
    paddingLeft: spacing.six,
    paddingRight: spacing.six,
  },

  ph8: {
    paddingLeft: spacing.eight,
    paddingRight: spacing.eight,
  },

  ph10: {
    paddingLeft: spacing.ten,
    paddingRight: spacing.ten,
  },

  ph12: {
    paddingLeft: spacing.twelve,
    paddingRight: spacing.twelve,
  },

  ph14: {
    paddingLeft: spacing.fourteen,
    paddingRight: spacing.fourteen,
  },
  ph16: {
    paddingLeft: spacing.sixteen,
    paddingRight: spacing.sixteen,
  },
  ph18: {
    paddingLeft: spacing.eighteen,
    paddingRight: spacing.eighteen,
  },
  ph20: {
    paddingLeft: spacing.twenty,
    paddingRight: spacing.twenty,
  },
  ph30: {
    paddingLeft: spacing.thirty,
    paddingRight: spacing.thirty,
  },
};

const marginStyles = {
  ma0: { margin: spacing.none },
  ma2: { margin: spacing.two },
  ma4: { margin: spacing.four },
  ma6: { margin: spacing.six },
  ma8: { margin: spacing.eight },
  ma10: { margin: spacing.ten },
  ma12: { margin: spacing.twelve },
  ma14: { margin: spacing.fourteen },
  ma16: { margin: spacing.sixteen },
  ma18: { margin: spacing.eighteen },
  ma20: { margin: spacing.twenty },
  ma30: { margin: spacing.thirty },
  ma40: { marign: spacing.fourty },

  ml0: { marginLeft: spacing.none },
  ml2: { marginLeft: spacing.two },
  ml4: { marginLeft: spacing.four },
  ml6: { marginLeft: spacing.six },
  ml8: { marginLeft: spacing.eight },
  ml10: { marginLeft: spacing.ten },
  ml12: { marginLeft: spacing.twelve },
  ml14: { marginLeft: spacing.fourteen },
  ml16: { marginLeft: spacing.sixteen },
  ml18: { marginLeft: spacing.eighteen },
  ml20: { marginLeft: spacing.twenty },
  ml24: { marginLeft: spacing.twentyFour },
  ml30: { marginLeft: spacing.thirty },
  ml34: { marginLeft: spacing.thirtyFour },
  ml40: { marignLeft: spacing.fourty },

  mr0: { marginRight: spacing.none },
  mr2: { marginRight: spacing.two },
  mr4: { marginRight: spacing.four },
  mr6: { marginRight: spacing.six },
  mr8: { marginRight: spacing.eight },
  mr10: { marginRight: spacing.ten },
  mr12: { marginRight: spacing.twelve },
  mr14: { marginRight: spacing.fourteen },
  mr16: { marginRight: spacing.sixteen },
  mr18: { marginRight: spacing.eighteen },
  mr20: { marginRight: spacing.twenty },
  mr30: { marginRight: spacing.thirty },
  mr40: { marginRight: spacing.fourty },

  mb0: { marginBottom: spacing.none },
  mb2: { marginBottom: spacing.two },
  mb4: { marginBottom: spacing.four },
  mb6: { marginBottom: spacing.six },
  mb8: { marginBottom: spacing.eight },
  mb10: { marginBottom: spacing.ten },
  mb12: { marginBottom: spacing.twelve },
  mb14: { marginBottom: spacing.fourteen },
  mb16: { marginBottom: spacing.sixteen },
  mb18: { marginBottom: spacing.eighteen },
  mb20: { marginBottom: spacing.twenty },
  mb24: { marginBottom: spacing.twentyFour },
  mb30: { marginBottom: spacing.thirty },
  mb40: { marginBottom: spacing.fourty },
  mb70: { marginBottom: spacing.seventy },

  mt0: { marginTop: spacing.none },
  mt2: { marginTop: spacing.two },
  mt4: { marginTop: spacing.four },
  mt6: { marginTop: spacing.six },
  mt8: { marginTop: spacing.eight },
  mt10: { marginTop: spacing.ten },
  mt12: { marginTop: spacing.twelve },
  mt14: { marginTop: spacing.fourteen },
  mt16: { marginTop: spacing.sixteen },
  mt18: { marginTop: spacing.eighteen },
  mt20: { marginTop: spacing.twenty },
  mt30: { marginTop: spacing.thirty },
  mt40: { marginTop: spacing.fourty },

  mv0: {
    marginTop: spacing.none,
    marginBottom: spacing.none,
  },
  mv2: {
    marginTop: spacing.two,
    marginBottom: spacing.two,
  },
  mv4: {
    marginTop: spacing.four,
    marginBottom: spacing.four,
  },
  mv6: {
    marginTop: spacing.six,
    marginBottom: spacing.six,
  },
  mv8: {
    marginTop: spacing.eight,
    marginBottom: spacing.eight,
  },
  mv10: {
    marginTop: spacing.ten,
    marginBottom: spacing.ten,
  },
  mv12: {
    marginTop: spacing.twelve,
    marginBottom: spacing.twelve,
  },
  mv14: {
    marginTop: spacing.fourteen,
    marginBottom: spacing.fourteen,
  },
  mv16: {
    marginTop: spacing.sixteen,
    marginBottom: spacing.sixteen,
  },
  mv18: {
    marginTop: spacing.eighteen,
    marginBottom: spacing.eighteen,
  },
  mv20: {
    marginTop: spacing.twenty,
    marginBottom: spacing.twenty,
  },
  mv30: {
    marginTop: spacing.thirty,
    marginBottom: spacing.thirty,
  },
  mv40: {
    marginTop: spacing.fourty,
    marginBottom: spacing.fourty,
  },

  mh0: {
    marginLeft: spacing.none,
    marginRight: spacing.none,
  },
  mh2: {
    marginLeft: spacing.two,
    marginRight: spacing.two,
  },
  mh4: {
    marginLeft: spacing.four,
    marginRight: spacing.four,
  },
  mh6: {
    marginLeft: spacing.six,
    marginRight: spacing.six,
  },
  mh8: {
    marginLeft: spacing.eight,
    marginRight: spacing.eight,
  },
  mh10: {
    marginLeft: spacing.ten,
    marginRight: spacing.ten,
  },
  mh12: {
    marginLeft: spacing.twelve,
    marginRight: spacing.twelve,
  },
  mh14: {
    marginLeft: spacing.fourteen,
    marginRight: spacing.fourteen,
  },
  mh16: {
    marginLeft: spacing.sixteen,
    marginRight: spacing.sixteen,
  },
  mh18: {
    marginLeft: spacing.eighteen,
    marginRight: spacing.eighteen,
  },
  mh20: {
    marginLeft: spacing.twenty,
    marginRight: spacing.twenty,
  },
  mh30: {
    marginLeft: spacing.thirty,
    marginRight: spacing.thirty,
  },
  mh40: {
    marginLeft: spacing.fourty,
    marginRight: spacing.fourty,
  },
};

const sectionBottomSpacing = marginStyles.mb30;

const sectionHeaderSpacing = marginStyles.mb16;

const largeHeightSeperator = {
  borderBottomWidth: 8,
  borderColor: colors.grayBorder,
};
const smallHeightSeperator = {
  borderBottomWidth: 1,
  borderColor: colors.grayBorder,
};

export { paddingStyles, marginStyles, largeHeightSeperator, smallHeightSeperator };
