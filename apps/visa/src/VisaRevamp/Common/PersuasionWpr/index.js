import React, { Children } from "react";
import { StyleSheet } from "react-native";
import Persuasion from "@Frontend_Ui_Lib_App/Persuasion";
import { marginStyles } from "../../Styles/Spacing";
const closeIcon = "https://cdn-icons-png.flaticon.com/512/6521/6521096.png";

const PersuasionWpr = ({
  gradientColor,
  content,
  wrapperStyle,
  startIcon,
  children,
  endIcon,
  startIconHolderStyle,
  startIconStyle,
  solidBgColor,
  contentStyle,
  angle,
  actionText,
  actionTextStyle
}) => {
  return (
    <Persuasion
      actionText={actionText ?? ""}        
      angle={90}
      bgColor={gradientColor ? gradientColor : solidBgColor}
      content={
        content
          ? content
          : ""
      }
      customStyle={{
        actionTextStyle: [styles.defaultActionTextStyle, actionTextStyle],
        contentStyle: [styles.defaultContentStyle, contentStyle],
        startIconHolderStyle: [styles.defaultStartIconStyle, startIconHolderStyle],
        startIconStyle: [styles.defaultStartIconStyle, startIconStyle],
        wrapperStyle: [styles.defaultWrapperStyle, wrapperStyle],
      }}
      endIcon={endIcon ? endIcon : null}
      startIcon={startIcon}
      testIds={{
        actionTextTestId: "",
        containerTestId: "",
        textTestId: "",
      }}
      gradientStart={{x: 0,y: 1}}
      gradientEnd={{x: 0, y: 1}}
      useAngle>
        {children}
    </Persuasion>

  );
};
const styles = StyleSheet.create({
    defaultContentStyle: {
      color:'#000000'
    },
    defaultActionTextStyle: {
      color: '#3543BF'
    },
    defaultContentStyle: {
      flex:1,
    },
    defaultStartIconStyle:{
      width:24, 
      height:24,
      ...marginStyles.mr8,
    },
    defaultWrapperStyle:{
      alignItems: "center", 
      borderRadius: 10, 
      width: 300
    }

});
export default PersuasionWpr;
