export const pdtLoggerActions = {
  UPDATE_INITIAL_LOGGER_DATA: 'UPDATE_INITIAL_LOGGER_DATA',
  UPDATE_SUB_PAGE_NAME: 'UPDATE_SUB_PAGE_NAME',
  CLEAR_SUB_PAGE_NAME: 'C<PERSON><PERSON>_SUB_PAGE_NAME',
  UPDATE_TRAVELLER_INFO: 'UPDATE_TRAVELLER_INFO',
  CLEAR_TRAVELLER_INFO: '<PERSON><PERSON><PERSON>_TRAVELLER_INFO',
  UPDATE_SEARCH_CONTEXT: 'UPDATE_SEARCH_CONTEXT',
  <PERSON><PERSON><PERSON>_SEARCH_CONTEXT: 'CLEAR_SEARCH_CONTEXT',
  UPDATE_TO_AND_FROM_DATE_TIME: 'UPDATE_TO_AND_FROM_DATE_TIME',
  C<PERSON>AR_TO_AND_FROM_DATE_TIME: 'CLEAR_TO_AND_FROM_DATE_TIME',
  UPDATE_BOOKING_INFO: 'UPDATE_BOOKING_INFO',
  CLEAR_BOOKING_INFO: 'CLEAR_BOOKING_INFO',
};

export const updateInitialLoggerDataAction = (initialLoggerData) => ({
  type: pdtLoggerActions.UPDATE_INITIAL_LOGGER_DATA,
  payload: initialLoggerData,
});

export const updateSubPageNameAction = (subPageName) => ({
  type: pdtLoggerActions.UPDATE_SUB_PAGE_NAME,
  payload: subPageName,
});

export const resetSubPageNameAction = () => ({
  type: pdtLoggerActions.CLEAR_SUB_PAGE_NAME,
});

export const updateTravellerInfoAction = (travellerInfo) => ({
  type: pdtLoggerActions.UPDATE_TRAVELLER_INFO,
  payload: travellerInfo,
});

export const resetTravellerInfoAction = () => ({
  type: pdtLoggerActions.CLEAR_TRAVELLER_INFO,
});

export const updateSearchContextAction = (searchContextData) => ({
  type: pdtLoggerActions.UPDATE_SEARCH_CONTEXT,
  payload: searchContextData,
});

export const resetSearchContextAction = () => ({
  type: pdtLoggerActions.CLEAR_SEARCH_CONTEXT,
});

export const updateToandFromDateTimeAction = (data) => ({
  type: pdtLoggerActions.UPDATE_TO_AND_FROM_DATE_TIME,
  payload: data,
});

export const resetToandFromDateTimeAction = () => ({
  type: pdtLoggerActions.CLEAR_TO_AND_FROM_DATE_TIME,
});

export const updateBookingInfoAction = (data) => ({
  type: pdtLoggerActions.UPDATE_BOOKING_INFO,
  payload: data,
});

export const resetBookingInfoAction = () => ({
  type: pdtLoggerActions.CLEAR_BOOKING_INFO,
});
