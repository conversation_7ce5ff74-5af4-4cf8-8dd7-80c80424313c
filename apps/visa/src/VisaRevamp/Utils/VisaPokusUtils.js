import { Platform } from 'react-native';
import getAbConfig, {
  AbConfigKeyMappings,
  getPokusConfig,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import memoizeOne from 'memoize-one';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';

export const PLATFORM_ANDROID = 'android';
export const PLATFORM_IOS = 'ios';
export const PLATFORM_PWA = 'pwa';

const currentPlatform =
  Platform.OS === 'android'
    ? PLATFORM_ANDROID
    : Platform.OS === 'ios'
    ? PLATFORM_IOS
    : PLATFORM_PWA;

export const getPokusConfigKey = memoizeOne((key) => {
  return `${key}_${currentPlatform}`;
});

const getExperimentValue = (key, defaultValue) => getPokusConfig(PokusLobs.VISA, key, defaultValue);

export const getIsVisaV2 = () => {
  return getExperimentValue(getPokusConfigKey(AbConfigKeyMappings.visa_funnel2), true);
};

export const getIsDocUploadV2 = () => {
  return getExperimentValue((AbConfigKeyMappings.newdocumentpage), false);
};
export const getIsEnableSkipDoc = () => {
  return getExperimentValue((AbConfigKeyMappings.enableskipdoc), false);
};
export const getIsMyraEnabled = () => {
  return getExperimentValue((AbConfigKeyMappings.myra_enable), false);
};

export const getIsVisaDefaultForm = () => {
  return getExperimentValue('skip_doc_visa',false);
};
