import { LANGUAGE_MAP } from '@mmt/legacy-commons/Common/constants/AppConstants';
import { getUserDetails , ProfileType } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { DEFAULT_REGION } from '../constants';
import { NativeModules } from 'react-native';


export const updateRecentSearchHistoryForCommon = async (prams) => {
    try {
      const { countryCode, deeplink } = prams || {};
      const userData = await getUserDetails();
      const lob_recentSearch = 'visa';
      const searchContext = {
        language: LANGUAGE_MAP.eng,
        region: DEFAULT_REGION,
        profile: userData.profileType || ProfileType.PERSONAL,
        timestamp: Date.now(),
        to: {
            locusV2Id: "",
            locusV2Type: "",
            locus: {
                country: countryCode
            }
      },
        meta: {
             deeplink,
        },
        lob: lob_recentSearch,
        lobCategory: lob_recentSearch,
      };
      updateRecentSeachContext({ lob: lob_recentSearch, searchContext });
    } catch (e) {
      console.log(e);
    }
  };
  export const updateRecentSeachContext = ({ lob, searchContext }) => {
    const {UserSessionModule} = NativeModules;
    if (!UserSessionModule || !UserSessionModule.updateRecentSearch) {
      return;
    }
    UserSessionModule.updateRecentSearch(lob, { searchContext });
  };