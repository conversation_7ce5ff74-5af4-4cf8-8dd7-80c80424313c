import fecha from 'fecha';
import isDate from 'lodash/isDate';
import url from 'url';
import { Platform, Dimensions, Linking } from 'react-native';
import { MAP_URL_SCHEME, STATUS_CODE_SUCCESS, USER_PLATFORM } from '../constants';
import { addDays, today } from '@mmt/legacy-commons/Helpers/dateHelpers';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { isArray } from 'lodash';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';
import { visaTrackClickEvent } from 'apps/visa/src/VisaRevamp/Tracking/utils';
import {
  NAME_OF_EVENTS,
  TRACKING_CONSTANTS,
  TYPE_OF_EVENTS,
} from 'apps/visa/src/VisaRevamp/Tracking/constants';

export const DEVICE_WINDOW =
  Platform.OS === 'ios' ? Dimensions.get('screen') : Dimensions.get('window');

export const getScreenWidth = () =>
  Platform.OS === 'ios' ? Dimensions.get('screen').width : Dimensions.get('window').width;

export const generateGUID = () => {
  const s4 = () =>
    Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  const guid = `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4() + s4() + s4()}`;
  return guid;
};

export const getSessionID = async (key, value, timeout) => {
  const getValue = await AsyncStorage.getItem(key);
  if (getValue == null) {
    AsyncStorage.setItem(key, value);
    setTimeout(() => {
      AsyncStorage.removeItem(key);
    }, timeout);
  }
  const currentValue = await AsyncStorage.getItem(key);
  return currentValue;
};

export const isValidResponse = (response) => response && response.status === STATUS_CODE_SUCCESS;

export const prepareDisabledDays = (dateArr = []) => {
  return dateArr.map((row) => {
    const rowDate = fecha.parse(row, 'YYYY-MM-DD').setHours(0, 0, 0, 0);
    return fecha.format(rowDate, 'YYYYMMDD');
  });
};

export const capitalize = (str) => {
  let updatedString = str;
  updatedString = updatedString.toLowerCase();
  return updatedString.charAt(0).toUpperCase() + updatedString.slice(1);
};

export const getDateForCalendar = () => addDays(today(), 0);

export const isValidDate = (date) => {
  const dateObj = new Date(date);
  const valiDate =
    isDate(dateObj) &&
    dateObj.toString() !== 'Invalid Date' &&
    new Date(date).getTime() >= today().getTime();
  return valiDate;
};

export function processSectionsData({ data = {} }) {
  const { order = [] } = data || {};
  if (!order?.length) {
    return [];
  }
  // Sort the order array by the sequence property
  order?.sort((a, b) => a.sequence - b.sequence);

  // Map over the sorted order array and return the corresponding section from the input
  return order.reduce((acc, section) => {
    const sectionData = data[section.name];
    if (!sectionData) {
      //Do not add section in list if not section data is present
      return acc;
    }
    acc.push({
      header: section.header,
      name: section.name,
      data: isArray(sectionData)
        ? sectionData?.sort?.((a, b) => a.sequence - b.sequence)
        : sectionData,
    });
    return acc;
  }, []);
}

export const isAndroidClient = () => Platform.OS === 'android';

export const isRawClient = () => Platform.OS === 'web';

export const isIosClient = () => Platform.OS === 'ios';

export const getPlatformIdentifier = () => (isRawClient() ? 'raw_mobile' : Platform.OS);

export const handleCallToActionPage = (pageKey, { apiUrl, ...rest }) => {
  const params = url.parse(apiUrl, true);
  VisaNavigation.push(pageKey, {
    apiUrl,
    ...params?.query,
    ...rest,
  });
};

export const getPageKeByUrl = (url) => {
  if (url.includes('sampleDoc')) {
    return VISA_ROUTE_KEYS.SAMPLE_DOC;
  } else if (url.includes('passportsecure')) {
    return VISA_ROUTE_KEYS.PASSPORT_SAFETY_TIPS;
  } else {
    return VISA_ROUTE_KEYS.LANDING_NEW;
  }
};
export const apiUrlClickHandler = ({ apiUrl = '', country = {} }) => {
  handleCallToActionPage(getPageKeByUrl(apiUrl), { apiUrl, country });
};

export const openMaps = (coordinates) => {
  const url = `${coordinates}`; // Define the URL here

  Linking.canOpenURL(url)
    .then((supported) => {
      if (!supported) {
        console.error(`Cannot handle url: ${url}`);
      } else {
        Linking.openURL(url);
      }
    })
    .catch((err) => console.error('An error occurred', err));
};

export const setWebViewAuth = (mmtAuth, isGcc) => {
  return `
    (function(){

      function getCookie(cname) {
        var name = cname + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(';');
        for(var i = 0; i <ca.length; i++) {
          var c = ca[i];
          while (c.charAt(0) == ' ') {
            c = c.substring(1);
          }
          if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
          }
        }
        return "";
      }
      var ccdeValue = getCookie('ccde');
      var auth = getCookie('mmt-auth');
      var shouldReload = false;
      if(auth != '${mmtAuth}') {
        document.cookie = 'mmt-auth=${mmtAuth}';
        if(!ccdeValue && ${isGcc}) {
          document.cookie = 'ccde=ae';
        }
        shouldReload = true;
      } else if(!ccdeValue && ${isGcc}) {
        document.cookie = 'ccde=ae';
        shouldReload = true;
      }
      if(!${isGcc} && ccdeValue === 'ae'){
        document.cookie = 'ccde=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        shouldReload = true;
      }
      if(shouldReload){
        location.reload();
      }
    })();
    true;
  `;
};

export const getAge = (dateOfBirth) => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  // If the current month is before the birth month or it's the birth month but the current day is before the birth day, subtract one year from the age.
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
};

export const dateObjToDateString = (dateTimeObj) => {
  if (!dateTimeObj) {
    return '';
  }
  const date = new Date(dateTimeObj); // Convert epoch time to Date object
  const day = date.getDate(); // Get day
  const month = date.toLocaleString('default', { month: 'long' }); // Get month in long format
  const year = date.getFullYear(); // Get year

  return `${day} ${month} ${year}`; // Format: "date month year"
};

export const dateStringToEpoch = (dateString) => {
  const date = fecha.parse(dateString, 'YYYY-MMM-DD'); // Convert the string into a Date object
  return date.getTime(); // Get the epoch time (milliseconds since Jan 1, 1970)
};

export const sendNetworkErrorEvent = ({ errorMessage = '' }) => {
  visaTrackClickEvent({
    eventName: 'error_occured',
    eventType: 'api_error',
    params: {
      errorMessage,
    },
  });
};

export const getVisaTypeCode = ({ visaTypeName = ''}) => {
  const refractoredName = visaTypeName.replace(/[^a-zA-Z]/g, '').toLowerCase();
  let code = ''
   switch(refractoredName) {
    case 'evisa':
      code = 'e-visa';
      break;
    case 'stickervisa':
      code = 'sticker-visa';
      break;
    default:
      code = ''
      break;
  }
  return code;
}