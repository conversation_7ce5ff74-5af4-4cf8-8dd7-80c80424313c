export const cameraRollNode = (data, dataNew) => {
    if (data) {
        return {
            node: {
                location: null,
                type: data.mimeType,
                timestamp: data.ctime.getTime(),
                image: {
                    fileSize: data.size,
                    filename: null,
                    height: null,
                    playableDuration: null,
                    uri: `file://${data.path}`,
                    width: null,
                },
            },
        };
    }
    return {
        node: {
            location: null,
            type: dataNew.type,
            timestamp: Date.now(),
            image: {
                fileSize: dataNew.fileSize ?? null,
                filename: dataNew.fileName,
                height: dataNew.height,
                playableDuration: dataNew.duration,
                uri: `${dataNew.uri}`,
                width: dataNew.width,
            },
        },
    };
};