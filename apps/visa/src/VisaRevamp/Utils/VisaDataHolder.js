import { getDataFromStorage, setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { isEmpty } from 'lodash';
import { VISA_ROUTE_KEYS, getCurrentRoute } from '../Navigation';
import VisaTrackingHelper from 'packages/legacy-commons/Helpers/VisaTrackingHelper';
import { _getUserStore } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

const SESSION_EXPIRE_TIME = 30;
let session = {};
export default class VisaDataHolder {
  static sInstance = null;

  constructor() {
    this.currentPage = '';
    this.cmp = '',
    this.traffic_source = '',
    this.pageMapOmni = new Map();
    this.prevPageMap = new Map();
    this.subFunnel = '';
    this.visaPixelDetails = {};
  }

  static getInstance() {
    if (VisaDataHolder.sInstance == null) {
      VisaDataHolder.sInstance = new VisaDataHolder();
    }
    return this.sInstance;
  }

  static clearData() {
    this.sInstance = new VisaDataHolder();
  }

  setCmp = (cmp) => {
    this.cmp = cmp;
  }
  getCmp = () => {  
    return this.cmp;
  }
  setTrafficSource = (traffic_source) => {
    this.traffic_source = traffic_source;
  }
  getTrafficSource = () => {
    return this.traffic_source;
  }
  setCurrentPage(pageKey) {
    const pageName = this.getOmniPageNameForKey(pageKey);
    if (pageName === this.currentPage) {
      return;
    }
    this.prevPageMap.set(pageName, this.currentPage);
    this.currentPage = pageName;
    if (!this.pageMapOmni.get(pageName)) {
      this.pageMapOmni.set(pageName, pageName);
    }
  }
  setSubFunnel = async () => {
    const { userCurrentStore } = await _getUserStore();
    this.subFunnel = userCurrentStore;
  };

  getSubFunnel = () => {
    return this.subFunnel;
  };

  setOmniPageName(pageName, omniPageName) {
    this.pageMapOmni.set(pageName, omniPageName);
  }

  getPrevPageOmni(pageName) {
    return this.pageMapOmni.get(this.prevPageMap.get(pageName));
  }
  getPrevPageName(pageName) {
    return this.prevPageMap.get(pageName);
  }

  getCurrentPageName() {
    return this.getOmniPageNameForKey(getCurrentRoute());
  }

  getCurrentPDTPageName() {
    const pageKey = this.currentPage;
    switch (pageKey) {
      case VISA_ROUTE_KEYS.SEARCH_PAGE:
        return VisaTrackingHelper.pageNameKeyMap.SEARCH;
      case VISA_ROUTE_KEYS.LANDING_NEW:
        return VisaTrackingHelper.pageNameKeyMap.LANDING;
      case VISA_ROUTE_KEYS.DETAIL:
        return VisaTrackingHelper.pageNameKeyMap.DETAILS;
      case VISA_ROUTE_KEYS.ADD_DOCUMENTS:
        return VisaTrackingHelper.pageNameKeyMap.ADD_DOCUMENTS;
      case VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS:
        return VisaTrackingHelper.pageNameKeyMap.UPLOAD_DOCUMENTS;
      case VISA_ROUTE_KEYS.BULK_UPLOAD:
        return VisaTrackingHelper.pageNameKeyMap.BULK_UPLOAD;
      case VISA_ROUTE_KEYS.REVIEW:
        return VisaTrackingHelper.pageNameKeyMap.REVIEW;
      case VISA_ROUTE_KEYS.ADD_TRAVELLER:
        return VisaTrackingHelper.pageNameKeyMap.ADD_TRAVELLER;
      case VISA_ROUTE_KEYS.SELECT_PLANS:
        return VisaTrackingHelper.pageNameKeyMap.SELECT_PLANS;
      case VISA_ROUTE_KEYS.MULTICOUNTRY_PAGE:
        return VisaTrackingHelper.pageNameKeyMap.MULTI_COUNTRY_NIGHTS;
      case VISA_ROUTE_KEYS.SAMPLE_DOC:
        return VisaTrackingHelper.pageNameKeyMap.SAMPLE_DOC;
      case VISA_ROUTE_KEYS.BULK_UPLOAD_EDIT:
        return VisaTrackingHelper.pageNameKeyMap.BULK_UPLOAD_EDIT;
      case VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM:
        return VisaTrackingHelper.pageNameKeyMap.BULK_UPLOAD_CONFIRM;
      case VISA_ROUTE_KEYS.ADD_DOCUMENTS_REVAMP:
        return VisaTrackingHelper.pageNameKeyMap.ADD_DOCUMENTS_REVAMP;
      case VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP:
        return VisaTrackingHelper.pageNameKeyMap.UPLOAD_DOCUMENTS_REVAMP;
      default:
        return pageKey;
    }
  }
  getCurrentPDTFunnelStep() {
    const pageKey = getCurrentRoute();
    switch (pageKey) {
      case VISA_ROUTE_KEYS.SEARCH_PAGE:
      case VISA_ROUTE_KEYS.LANDING_NEW:
      case VISA_ROUTE_KEYS.MULTICOUNTRY_PAGE:
        return 'landing';
      case VISA_ROUTE_KEYS.DETAIL:
      case VISA_ROUTE_KEYS.SAMPLE_DOC:
        return 'detail';
      case VISA_ROUTE_KEYS.ADD_DOCUMENTS:
      case VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS:
      case VISA_ROUTE_KEYS.ADD_TRAVELLER:
      case VISA_ROUTE_KEYS.SELECT_PLANS:
      case VISA_ROUTE_KEYS.REVIEW:
      case VISA_ROUTE_KEYS.THANK_YOU:
        return 'review'
      default:
        return pageKey;
    }
  }

  getOmniPageNameForKey = (pageKey) => {
    switch (pageKey) {
      case VISA_ROUTE_KEYS.SEARCH_PAGE:
        return VisaTrackingHelper.pageNameKeyMap.SEARCH;
      case VISA_ROUTE_KEYS.LANDING_NEW:
        return VisaTrackingHelper.pageNameKeyMap.LANDING;
      case VISA_ROUTE_KEYS.DETAIL:
        return VisaTrackingHelper.pageNameKeyMap.DETAILS;
      case VISA_ROUTE_KEYS.LEARN_MORE:
        return VisaTrackingHelper.pageNameKeyMap.LEARN_MORE;
      case VISA_ROUTE_KEYS.ADD_DOCUMENTS:
        return VisaTrackingHelper.pageNameKeyMap.ADD_DOCUMENTS;
      case VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS:
        return VisaTrackingHelper.pageNameKeyMap.UPLOAD_DOCUMENTS;
      case VISA_ROUTE_KEYS.BULK_UPLOAD:
        return VisaTrackingHelper.pageNameKeyMap.BULK_UPLOAD;
      case VISA_ROUTE_KEYS.REVIEW:
        return VisaTrackingHelper.pageNameKeyMap.REVIEW;
      case VISA_ROUTE_KEYS.ADD_TRAVELLER:
        return VisaTrackingHelper.pageNameKeyMap.ADD_TRAVELLER;
      case VISA_ROUTE_KEYS.SELECT_PLANS:
        return VisaTrackingHelper.pageNameKeyMap.SELECT_PLANS;
      case VISA_ROUTE_KEYS.MULTICOUNTRY_PAGE:
        return VisaTrackingHelper.pageNameKeyMap.MULTI_COUNTRY_NIGHTS;
      case VISA_ROUTE_KEYS.FAQS:
        return `${VisaTrackingHelper.pageNameKeyMap.DETAILS}:${VisaTrackingHelper.pageNameKeyMap.FAQS}`;
      case VISA_ROUTE_KEYS.SAMPLE_DOC:
        return VisaTrackingHelper.pageNameKeyMap.SAMPLE_DOC;
      case VISA_ROUTE_KEYS.BULK_UPLOAD_EDIT:
        return VisaTrackingHelper.pageNameKeyMap.BULK_UPLOAD_EDIT;
      case VISA_ROUTE_KEYS.BULK_UPLOAD_CONFIRM:
        return VisaTrackingHelper.pageNameKeyMap.BULK_UPLOAD_CONFIRM;
      case VISA_ROUTE_KEYS.THANK_YOU:
        return VisaTrackingHelper.pageNameKeyMap.THANKYOU;
      case VISA_ROUTE_KEYS.ADD_DOCUMENTS_REVAMP:
        return VisaTrackingHelper.pageNameKeyMap.ADD_DOCUMENTS_REVAMP;
      case VISA_ROUTE_KEYS.UPLOAD_DOCUMENTS_REVAMP:
        return VisaTrackingHelper.pageNameKeyMap.UPLOAD_DOCUMENTS_REVAMP;
      default:
        return pageKey;
    }
  };
}