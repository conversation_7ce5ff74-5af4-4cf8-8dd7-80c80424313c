import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';
import url from 'url';

export const handleDeeplinkClick = (deeplink) => {
  const params = url.parse(deeplink, true);
  if (deeplink.includes('learnings')) {
    VisaNavigation.push(VISA_ROUTE_KEYS.LEARN_MORE, {
      ...params?.query,
    });
  } else {
    GenericModule.openDeepLink(deeplink);
  }
};
