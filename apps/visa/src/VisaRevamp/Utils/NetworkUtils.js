import { head, isEmpty } from 'lodash';
import { Platform } from 'react-native';
import {
  generateGUID,
  getPlatformIdentifier,
  getSessionID,
  isValidResponse,
  sendNetworkErrorEvent,
} from './index';
import { BASEURL, PlatformAuthCode, API_URLS } from '../constants';
import NetworkModule from '@mmt/legacy-commons/Native/NetworkModule';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import store from 'packages/legacy-commons/AppState/Store';
import { setErrorMessage } from '../Actions/visaActions';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';

export const DEFAULT_TIMEOUT = 60000;
const sessionExpiryTime = 240000; // 4 mins expiry
let isLoggedIn = false;
let guestKey = null; // Store guest auth token

const getAuthValue = () => {
  if (Platform.OS === 'ios') {
    return PlatformAuthCode.IOS;
  } else if (Platform.OS === 'android') {
    return PlatformAuthCode.ANDROID;
  } else if (Platform.OS === 'web') {
    return PlatformAuthCode.MWEB;
  }
  return null;
};

// Function to set guest auth token
export const setGuestKey = (authToken) => {
  guestKey = authToken;
};

// Function to get guest auth token
export const getGuestKey = () => {
  return guestKey;
};

// Function to clear guest auth token
export const clearGuestKey = () => {
  guestKey = null;
};

export const handleResponse = async (response) => {
  if (response && response.ok) {
    const responseBody = await response.json();
    if (isValidResponse(responseBody)) {
      return responseBody;
    } else {
      store.dispatch(setErrorMessage(responseBody?.message));
    }
  } else {
    const responseBody = await response.json();
    // this is done for setting error message at default Error Page Load Event
    store.dispatch(setErrorMessage(responseBody?.message));
  }
};

export const handleResponseWithEvent = async (response) => {
  if (response && response.ok) {
    const responseBody = await response.json();
    if (isValidResponse(responseBody)) {
      return responseBody;
    }
  } else {
    const responseBody = await response.json();
    showShortToast(responseBody?.message);
    // send error event for api fail -- this does not load an error page
    sendNetworkErrorEvent({errorMessage: responseBody?.message});
  }
};
export const getHeaders = async () => {
  const headers = await NetworkModule.getHeaders();
  const getUUID = generateGUID();
  const sessionId = await getSessionID('visa_session_id', getUUID, sessionExpiryTime);
  headers['sessionId'] = sessionId;
  headers['Authorization'] = getAuthValue();
  headers['channel'] = getPlatformIdentifier();

  // Check if user is actually logged in
  const userLoggedIn = await isUserLoggedIn();

  if (!userLoggedIn) {
    // User is not logged in - clear any old auth and use guest key
    headers['mmt-auth'] = getGuestKey() || ''; // Use guest key or empty string
  }
  // If user is logged in, keep the existing mmt-auth from NetworkModule

  return headers;
};

export const timeout = (ms, promise) =>
  new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new Error('api timed out'));
    }, ms);
    promise.then(resolve, reject);
  });

export const getSampleDocData = async ({ apiUrl = '' }) => {
  try {
    let response = '';
    const headers = await getHeaders();

    if (apiUrl) {
      response = await fetch(apiUrl, {
        method: 'GET',
        headers,
      });
    } else {
      const url = `${BASEURL}${API_URLS.SAMPLE_DOC}`;
      response = await fetch(url, {
        method: 'GET',
        headers,
      });
    }
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const fetchVisaLandingDetails = async () => {
  try {
    const url = `${BASEURL}${API_URLS.LANDING}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers,
      }),
    );

    return await handleResponse(response);
  } catch (e) {
    return null;
  }
  return null;
};

export const createVisaBooking = async ({ bookingId = '', apiUrl = '' } = {}) => {
  try {
    let response = null;
    const headers = await getHeaders();

    if (apiUrl) {
      response = await timeout(
        DEFAULT_TIMEOUT,
        fetch(apiUrl, {
          method: 'GET',
          headers,
        }),
      );
    } else {
      let bookinginfo = '?bookingId=' + bookingId;
      const url = `${BASEURL}${API_URLS.BOOKING}${bookinginfo}`;
      response = await timeout(
        DEFAULT_TIMEOUT,
        fetch(url, {
          method: 'GET',
          headers,
        }),
      );
    }
    return await handleResponseWithEvent(response);
  } catch (e) {
    console.log('*** here in catch **', e)
    return null;
  }
  return null;
};
export const fetchVisaDetails = async ({ countryCode = '', searchId = '', potId = '' } = {}) => {
  try {
    const url = `${BASEURL}${API_URLS.DETAIL}`;
    const headers = await getHeaders();
    const request = {
      countryCode,
      searchId,
      purposeOfTravelId: Number(potId),
    };
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request),
    });
    return await handleResponse(response);
  } catch (e) {
    console.log('error while fetching visa details', e);
    return null;
  }
  return null;
};

export const fetchSavedTravellers = async () => {
  try {
    const url = `${BASEURL}${API_URLS.SAVED_TRAVELLERS}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers,
      }),
    );
    return await handleResponse(response);
  } catch (e) {
    return null;
  }
  return null;
};

export const addOrUpdateTravellers = async ({ travellerDetail }) => {
  try {
    const url = `${BASEURL}${API_URLS.ADD_UPDATE_TRAVELLER}`;
    const headers = await getHeaders();
    const request = {
      travellers: [travellerDetail],
    };
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );
    return await handleResponseWithEvent(response);
  } catch (e) {
    return null;
  }
  return null;
};

export const fetchPlans = async ({
  purposeOfTravel = '',
  passengers = [],
  fromDate = '',
  toDate = '',
  apiUrl = '',
  countryCode = '',
  searchId = '',
} = {}) => {
  try {
    const headers = await getHeaders();
    let response = null;
    if (!isEmpty(apiUrl)) {
      response = await timeout(
        DEFAULT_TIMEOUT,
        fetch(apiUrl, {
          method: 'GET',
          headers,
        }),
      );
    } else {
      const url = `${BASEURL}${API_URLS.SELECT_PLANS}`;
      const request = {
        countryCode,
        searchId,
        purposeOfTravelId: Number(purposeOfTravel),
        passengers,
        fromDate,
        toDate,
      };
      response = await timeout(
        DEFAULT_TIMEOUT,
        fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify(request),
        }),
      );
    }
    return await handleResponse(response)
  } catch (e) {
    console.log('** error', e);
    return e;
  }
  return null;
};

export const fetchThankYouDetails = async ({ bookingId = '' } = {}) => {
  try {
    // http://172.16.211.197:6098/v2/thankyou?bookingId=NV7C00061919828 sample url for testing
    const url = `${BASEURL}${API_URLS.THANK_YOU}${bookingId ? `?bookingId=${bookingId}` : ''}`;
    const headers = await getHeaders();
    const request = {
      bookingId,
    };

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers,
      }),
    );

    return await handleResponse(response)

    // if (response && response.ok) {
    //   const responseBody = await response.json(); //response
    //   if (isValidResponse(responseBody)) {
    //     return responseBody;
    //   }
    // }
  } catch (e) {
    return null;
  }
  return null;
};

export const fetchSearchCountries = async (searchId) => {
  try {
    const url = `${BASEURL}${API_URLS.SEARCH_PAGE}`;
    const headers = await getHeaders();
    const request = {
      searchId,
    };
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      if (responseBody?.status === 1) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const reviewBooking = async (bookingId) => {
  try {
    let response = null;
    const headers = await getHeaders();
    const booking = '?bookingId=' + bookingId;
    const url = `${BASEURL}${API_URLS.REVIEW}${booking}`;
    response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers,
      }),
    );
    return await handleResponse(response);
  } catch (e) {
    return null;
  }
  return null;
};

export const confirmVisaPlans = async ({
  bookingID,
  passengers,
  countryCode,
  fromDate,
  toDate,
}) => {
  try {
    const url = `${BASEURL}${API_URLS.CONFIRM_VISA_PLAN}`;
    const headers = await getHeaders();
    const request = {
      bookingid: bookingID,
      passengers,
      countryCode,
      fromDate,
      toDate,
    };

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    return await handleResponseWithEvent(response);
  } catch (e) {
    console.log('** error', e);
    return null;
  }
};

export const fetchMultiCountryFLow = async (countrycode, pot) => {
  try {
    let details = '/' + countrycode + '/' + pot;
    const url = `${BASEURL}${API_URLS.MULTI_COUNTRY}${details}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers,
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      if (responseBody?.status === 1) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
};

export const getLearningsData = async ({ learningId = '', pageId = '', countryCode = '' }) => {
  try {
    const url = `${BASEURL}${API_URLS.LEARNINGS}`;
    const headers = await getHeaders();
    const request = {
      learningId: Number(learningId),
      pageId: Number(pageId),
      ...(countryCode && { countryCode }),
    };

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    return await handleResponse(response);
  } catch (e) {
    console.log('** error', e);
    return null;
  }
};

export const savePartialFormResponse = async (request) => {
  try {
    const url = `${BASEURL}${API_URLS.SAVE_PARTIAL_FORM}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const passportTips = async ({ countryCode = '' }) => {
  try {
    const url = `${BASEURL}${API_URLS.PASSPORT_SAFETY_TIPS}${
      countryCode ? `?countryCode=${countryCode}` : ''
    }`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers,
      }),
    );
    return await handleResponse(response);
  } catch (e) {
    return null;
  }
};

export const checkPincodeDetails = async ({ bookingId = '', countryCode = '', pincode = '' }) => {
  try {
    const url = `${BASEURL}${API_URLS.PINCODE}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({ bookingId, ...(countryCode && { countryCode }), zipcode: pincode }),
      }),
    );

    return await handleResponseWithEvent(response);
  } catch (e) {
    console.log('error while fetching pincode', e);
    return null;
  }
};

export const getFileFormData = (request) => {
  const formData = new FormData();
  formData.append('data', request.data);
  formData.append('documentName', request.documentName);
  formData.append('bookingId', request.bookingId);
  formData.append('payRefId', request.bookingId);
  formData.append('format', request.format);
  formData.append('docType', request.docType);
  formData.append('docIndex', request.docId);
  formData.append('paxIndex', request.paxId);
  formData.append('docId', request.docId);
  formData.append('paxId', request.paxId);
  return formData;
};

export const getMultiPaxFileFormData = (request) => {
  const formData = new FormData();
  formData.append('data', request.data);
  formData.append('documentName', request.documentName);
  formData.append('bookingId', request.bookingId);
  formData.append('payRefId', request.bookingId);
  formData.append('format', request.format);
  formData.append('docType', request.docType);
  formData.append('docIndex', request.docId);
  formData.append('docId', request.docId);
  formData.append('paxIds', request.paxIds);
  return formData;
};

export const uploadClassifierFile = async (request, isClassifier) => {
  try {
    const formData = getFileFormData(request);
    let url = `${BASEURL}${API_URLS.UPLOAD_DATA}`;
    if (isClassifier) {
      url = `${BASEURL}${API_URLS.CLASSIFIER_UPLOAD}`;
    }
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: {
          ...headers,
          'content-type': 'multipart/form-data',
        },
        body: formData,
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const getClassifierData = async ({ bookingId }) => {
  try {
    const url = `${BASEURL}${API_URLS.GET_CLASSIFIER_DATA}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({ bookingId }),
      }),
    );

    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const downloadVisaImage = async (url) => {
  try {
    const headers = await getHeaders();
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });
    if (response) {
      const responseBody1 = await response.blob();
      return responseBody1;
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const confirmClassifierData = async ({ bookingId }) => {
  try {
    const url = `${BASEURL}${API_URLS.CONFIRM_CLASSIFIER_DATA}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({ bookingId }),
      }),
    );

    return await handleResponseWithEvent(response);
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const completelassifierData = async ({ bookingId }) => {
  try {
    const url = `${BASEURL}${API_URLS.COMPLETE_CLASSIFIER_DATA}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({ bookingId }),
      }),
    );

    return await handleResponseWithEvent(response);
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const cancelClassifierData = async ({ bookingId }) => {
  try {
    const url = `${BASEURL}${API_URLS.CANCEL_CLASSIFIER_DATA}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({ bookingId }),
      }),
    );

    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const uploadClassifierData = async ({ request }) => {
  try {
    const url = `${BASEURL}${API_URLS.ASSIGN_CLASSIFIER_DATA}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    return await handleResponseWithEvent(response);
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const deleteClassifierData = async ({ request }) => {
  try {
    const url = `${BASEURL}${API_URLS.DELETE_CLASSIFIER_DATA}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    return await handleResponseWithEvent(response);
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const saveDocumentData = async ({ request }) => {
  try {
    const url = `${BASEURL}${API_URLS.SAVE_DOCUMENT}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const saveDocumentsConsent = async ({ request }) => {
  try {
    const url = `${BASEURL}${API_URLS.SAVE_DOCUMENTS}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const getMultipleApplicationFormResponse = async (bookingId, paxIndex, defaultForm) => {
  try {
    let url = `${BASEURL}${API_URLS.GET_FORM}?paxIndex=${paxIndex}&payRefId=${bookingId}&defaultForm=${defaultForm}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers,
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const fetchMMTBlackDetails = async (request) => {
  try {
    const url = `${BASEURL}${API_URLS.MMTBLACK}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};
export const isPassportPresent = async (request) => {
  try {
    const formData = getFileFormData(request);
    let url = `${BASEURL}${API_URLS.IS_PASSPORT_PRESENT}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: {
          ...headers,
          'content-type': 'multipart/form-data',
        },
        body: formData,
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const uploadPassport = async (request) => {
  try {
    let url = `${BASEURL}${API_URLS.UPLOAD_PASSPORT}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: {
          ...headers,
          'content-type': 'application/json',
        },
        body: JSON.stringify(request),
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const deleteDocument = async (request) => {
  try {
    const url = `${BASEURL}${API_URLS.DELETE_DOCUMENT}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const multiPaxUpload = async (request) => {
  try {
    const formData = getMultiPaxFileFormData(request);
    let url = `${BASEURL}${API_URLS.MULTI_PAX_UPLOAD}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: {
          ...headers,
          'content-type': 'multipart/form-data',
        },
        body: formData,
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};

export const updatePaxName = async (request) => {
  try {
    let url = `${BASEURL}${API_URLS.UPDATE_PAX_NAME}`;
    const headers = await getHeaders();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    console.error('error', error);
  }
  return null;
};
