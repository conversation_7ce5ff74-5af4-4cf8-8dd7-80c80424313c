import { PAX_TYPES } from "../constants";

export const stringToDate = (dateString) => {
  const [day, month, year] = dateString.split('-');
  return new Date([month, day, year].join('-'));
};

export const getPaxCount = (paxIndex = [], travellers = {}) => {
  let adultCount = 0;
  let infantCount = 0;
  let childCount = 0;
  paxIndex.map((item, index) => {
    const { paxType = '' } = travellers[item] || {};
    if (paxType === PAX_TYPES.ADULT) {
      adultCount = adultCount + 1;
    } else if (paxType === PAX_TYPES.CHILD) {
      childCount = childCount + 1;
    } else if (paxType === PAX_TYPES.INFANT) {
      infantCount = infantCount + 1;
    }
  });
  let paxString = `${adultCount > 0 ? `${adultCount} Adult${adultCount > 1 ? 's' : ''}` : ''}${
    childCount > 0 ? `${childCount} Child${childCount > 1 ? 'ren' : ''}` : ''
  }${infantCount > 0 ? `${infantCount} Infant${infantCount > 1 ? 's' : ''}` : ''}`;

  return { adultCount, infantCount, childCount, paxString };
};

export const getPaxDetails = (paxIndex, travellers) => {
  const names = paxIndex.map((item, index) => {
    const { name = '' } = travellers[item] || {};
    return name;
  });
  let stringOfNames = names.join(',');
  return { stringOfNames, ...getPaxCount(paxIndex, travellers) };
};
