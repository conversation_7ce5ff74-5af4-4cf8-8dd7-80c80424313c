import { updateRecentSearchHistory } from '@mmt/legacy-commons/Native/GenericModule';

export const IMAGE_KEYS = {
  shareIcon: 'shareIcon',
  expressTimer: 'expressTimer',
  needHelpIcon: 'needHelpIcon',
  radioIcon: 'radioIcon',
  arrowDownWithBorder: 'arrowDown',
  myraChatIcon: 'myraChatIcon',
  shareIconWithBg: 'shareIconWithBg',
  backIconWithBg: 'backIconWithBg',
  buldUploadIcon: 'bulkUploadIcon',
  isCardIcon: 'isCardIcon',
  editIcon: 'editIcon',
  checkCircleGreenIcon: 'checkCircleGreenIcon',
  checkCirclePinkIcon: 'checkCirclePinkIcon',
  greyWarningIcon: 'greyWarningIcon',
  greyArrowIcon: 'greyArrowIcon',
  cameraIconV2: 'cameraIconV2',
  pictureIcon: 'pictureIcon',
  filesIcon: 'filesIcon',
  documentIcon: 'documentIcon',
  rejectedIcon: 'rejectedIcon',
  approvedIcon: 'approvedIcon',
  docIcon: 'docIcon',
  addLaterIcon: 'addLaterIcon',
  supportIcon: 'supportIcon',
  reminderIcon: 'reminderIcon',
  yellowinfoIcon: 'yellowinfoIcon',
};

export const FLAG_IMAGES = {
  1: [
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagUAE.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagThailand.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagSrilanka.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagSingapore.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagKenya.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagItaly.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagIndonesia.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagFrance.png',
  ],
  2: [
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagEgypt.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagDenmark.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagCzechRepublic.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagCambodia.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagBelgium.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagAzerbaijan.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagAustria.png',
    'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/flagAustralia.png',
  ],
};
export const getImagePath = (imageLink) => {
  switch (imageLink) {
    case 'warningImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/warnning_icon.png',
      };
    case 'submitImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/check_circle_icon.png',
      };
    case 'errorImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/cancel_icon.png',
      };
    case 'uploadIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/uploadIcon.png',
      };
      case 'disableUploadIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/<EMAIL>',
      };
    case 'cameraIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/camera.png',
      };
      case 'disableCameraIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/<EMAIL>',
      };
    case 'greenTick':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/greenTick.png',
      };
    case 'sample':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/sample.png',
      };
    case 'closeIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/sampleIcon.png',
      };
    case 'rightArrow':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/chevron_right_icon.png',
      };
    case IMAGE_KEYS.shareIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/shareIcon.png',
      };
    case IMAGE_KEYS.expressTimer:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/express_timer.png',
      };
    case IMAGE_KEYS.needHelpIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/needhelp.png',
      };
    case IMAGE_KEYS.radioIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/radioIcon.png',
      };
    case IMAGE_KEYS.arrowDownWithBorder:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/roundedArrowDown.png',
      };
    case IMAGE_KEYS.myraChatIcon:
      return {
        uri: 'https://imgak.mmtcdn.com/mima/images/mobile/myraBot.webp',
      };
    case IMAGE_KEYS.shareIconWithBg:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/share%20visa.png',
      };
    case IMAGE_KEYS.backIconWithBg:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/back%20visa.png',
      };
    case IMAGE_KEYS.buldUploadIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/visa%20bulk%20upload.png',
      };
    case IMAGE_KEYS.isCardIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/idCardIcon.png',
      };
    case 'pdfImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/basil_document_solid.png',
      };
    case 'picImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/image_filled.png',
      };
    case 'uploadDocImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/upload.png',
      };
    case 'addDoc':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/icon_container_swaps.png',
      };
    case 'addImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/icon_container_swaps.png',
      };
    case 'tipIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/tips_updates_outline.png',
      };
    case 'deleteIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/delete_outline.png',
      };
    case 'cropIcon':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/crop.png',
      };
    case IMAGE_KEYS.editIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Edit__.png',
      };
    case IMAGE_KEYS.checkCircleGreenIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Check_Circle.png',
      };
    case IMAGE_KEYS.checkCirclePinkIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Check%20Circle%20-%20Filled%201.png',
      };
    case IMAGE_KEYS.greyWarningIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/image.png',
      };
      case 'rejectedImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/document-rejected@2x%201.png',
      };
      case 'acceptedImage':
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/document-accepted@2x 1.png',
      };
      case IMAGE_KEYS.greyArrowIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Expand.png',
      };
    case IMAGE_KEYS.cameraIconV2:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/CameraV2.png',
      };
    case IMAGE_KEYS.pictureIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Picture.png',
      };
    case IMAGE_KEYS.filesIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Files.png',
      };
    case IMAGE_KEYS.documentIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/noun-scan-document-6394425%201.png',
      };
    case IMAGE_KEYS.rejectedIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/document-rejected@2x%201.png',
      };
    case IMAGE_KEYS.approvedIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/document-accepted@2x 1.png',
      };
    case IMAGE_KEYS.docIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/<EMAIL>',
      };
    case IMAGE_KEYS.addLaterIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Group%201244834335.png',
      };
    case IMAGE_KEYS.supportIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/iconoir_flash-solid.png',
      };
    case IMAGE_KEYS.reminderIcon:
      return {
        uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/overview_24dp_FILL0_wght400_GRAD0_opsz24%201.png',
      };
      case IMAGE_KEYS.yellowinfoIcon:
        return {
          uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Info - Outline.png',
        };
      case 'accountCircleIcon':
        return {
          uri: 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Account%20Circle.png',
        };
    default:
      return '';
  }
};

export const getSImagePath = (sampleImage) => {
  return { uri: sampleImage };
};

export const updateRecentSearchHistoryForCommon = (prams) => {
  try {
    const { countryCode, destCountry, paxList, startDate, endDate } = prams || {};
    const lob = 'VISA';
    let deeplink = 'mmyt://visa/';
    const dateStart = fecha.parse(startDate, DATE_FORMAT_COMMON);
    const dateEnd = fecha.parse(endDate, DATE_FORMAT_COMMON);
    const data = {
      lob: 'VISA',
      to: destCountry,
      toCode: countryCode,
      description:
        paxList && paxList.length > 1
          ? paxList.length + ' Travellers'
          : paxList.length
            ? paxList.length + ' Traveller'
            : '',
      startDate: dateStart.getTime(),
      returnDate: dateEnd.getTime(),
      deeplink,
    };
    updateRecentSearchHistory(lob, data);
  } catch (e) {
    console.log(e);
  }
};
