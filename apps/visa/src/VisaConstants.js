export const DATE_FORMAT_CALENDAR = 'DD/MM/YYYY';
export const DATE_FORMAT_COMMON = 'DD-MM-YYYY';
export const DATE_FORMAT_DISPLAY = 'DD MMM \'YY';
export const ADULT = 'adult';
export const INFANT = 'infant';
export const DEFAULT_ADULT_AGE = '25';
export const INFANT_AGE_LIMIT = 11;
export const ADULT_AGE_EXTENSION = 'y';
export const MONTH_AGE_EXTENSION = 'm';
export const AGE_EXT_YEARS = 'yrs';
export const AGE_EXT_MONTHS = 'months';
export const ONWARD = 'onward';
export const RETURN = 'return';
export const PRICE_BREAKUP = 'Price Breakup';
export const TRAVELLER = 'traveller';
export const TRAVELLERS = 'travellers';
export const ALL_DOCS_PAGE_TITLE = 'All Documents';
export const DEFAULT_DOC_EXTENSION = 'jpeg';
export const DEFAULT_PRIMARY_NAME = '';
export const DAY_SLASH_INDEX = 2;
export const MONTH_SLASH_INDEX = 5;
export const INT_MAX_VAL = 2147483646;
export const MIN_UNFILLED_COUNT = 10;
export const PHONE_NUMBER_LIMIT = 14;
export const STATUS_CODE_SUCCESS = 0;
export const PROMPT_OVERLAY_POSITIVE = 'yes';
export const PROMPT_OVERLAY_NEGETIVE = 'no';
export const VISAFORM_UPDATED_FIELD_URL = 'visaform_updated_field_url';
export const DATE_LENGTH = 10;
export const YES = 'yes';
export const TNC_ERROR_MESSAGE = 'Please select TnC to proceed';
export const LOADER_TIMEOUT = 5000;
export const DIGIT_REGEXP = new RegExp('^[0-9]+$');


export const BASEURL = 'https://visa-cb.makemytrip.com/api';
export const  COMMID_ENCRYPTION_API_ENDPOINT = 'https://visa-cb.makemytrip.com/userService/encodeDecode';


export const apiTimeouts = {
  landing: {
    LANDING: 30000,
    PERSUASION: 30000,
    USER_REVIEW: 3000,
    DELETE_BOOKING: 3000,
    INITIATE_BOOKING: 3000
  },
  detail: {
    DETAILS: 30000,
    INITIATE_BOOKING: 30000,
    DOCS_REQD: 30000,
    MPL: 30000,
    MPL_UPDATE: 30000
  },
  review: {
    REVIEW: 30000,
    PRE_PAYMENT: 30000,
    APPLY_COUPON: 30000,
  },
  allDocs: {
    BOOKING: 30000,
    UPLOAD_DOC: 500000,
  },
  uploadVisaMain: {
    BOOKING: 30000,
    FORM: 30000,
    UPLOAD_DOC: 500000,
    FETCH_FIELD_VALUE: 30000,
    REMOVE_DOC: 500000,
    SAVE_FORM: 30000,
    REPLICATE_DOCS: 30000,
    SAVE_PARTIAL_FORM: 30000,
    FETCH_PREPROCESS_DATA: 30000,
  },
  thankYou: {
    THANK_YOU: 30000,
  },
};

// Some of these pages are used in native side also, if making any change here then make sure native code doesn't break
export const PAGENAME = {
  LANDINGNEW: 'landingNew',
  LANDING: 'landing',
  PROGRESSIVE_SEARCH: 'search widget',
  TYPE_OF_VISA: 'type of Visa',
  DOCUMENT_REQUIRED: 'documents required',
  DOCUMENT_VERIFICATION: 'document verification',
  DOC_VIEWER: 'document viewer',
  ALL_FAQS: 'FAQ',
  EDIT_TRAVELLERS: 'edit travellers',
  UPLOAD_ETICKET: 'ticket upload',
  NEXT_TRAVELLER: 'next traveller',
  REVIEW: 'review',
  APPLICATION_FORM: 'application form',
  PASSENGER_DOCS: 'passenger Docs',
  COUNTRY_CODES: 'country code',
  TRAVEL_INFO: 'travel info',
  VISA_PROCESS: 'visa process',
  ADD_DOCUMENTS: 'add documents',
  APPLICATION_FORM_PREFILLED: 'application form prefilled',
  MIMA_VISA_BOOKING_DETAILS: 'VisaBookingDetail',
  ADD_DOCUMENTS_FILLFORM: "mob:funnel:visa:addDocuments_fillForm"
};


// Use this for tracking omniture events [pageName, eventName(to pass in prop_1)]
export const TRACKING_PAGENAME = {
  LANDING_NEW: ['mob:landing:visa:landing_new', 'landing_main'],
  LANDING_NEW_ERROR: ['mob:landing:visa:landing_new', 'landing_main_err'],
  LANDING: ['mob:funnel:visa:landing', 'landing_main'],
  LANDING_ERROR: ['mob:funnel:visa:landing', 'landing_main_error'],
  DESTINATION_PICKER: ['mob:funnel:visa:landing_new', 'destination_picker'],
  PROGRESSIVE_SEARCH: ['mob:funnel:visa:landing_new', 'search_widget'],
  TYPE_OF_VISA: ['mob:funnel:visa:details', 'type_of_Visa'],
  TYPE_OF_VISA_ERROR: ['mob:funnel:visa:details', 'type_of_Visa_err'],
  PRICE_BREAK_UP: ['mob:funnel:visa:details', 'price_break_up'],
  DOCUMENT_REQUIRED: ['mob:funnel:visa:details', 'documents_required'],
  DOCUMENT_REQUIRED_ERROR: ['mob:funnel:visa:details', 'documents_required_err'],
  DOCUMENT_VERIFICATION: ['mob:funnel:visa:details', 'document_verification'],
  DOCUMENT_VERIFICATION_ERROR: ['mob:funnel:visa:details', 'document_verification_err'],
  DOC_VIEWER: ['mob:funnel:visa:details', 'document_viewer'],
  ALL_FAQS: ['mob:funnel:visa:details', 'FAQs'],
  EDIT_TRAVELLERS: ['mob:funnel:visa:details', 'edit_travellers'],
  UPLOAD_ETICKET: ['mob:funnel:visa:upload_visa_main', 'ticket_upload'],
  NEXT_TRAVELLER: ['mob:funnel:visa:details', 'next_traveller'],
  REVIEW: ['mob:funnel:visa:review', 'review'],
  REVIEW_ERROR: ['mob:funnel:visa:review', 'review_err'],
  APPLICATION_FORM: ['mob:funnel:visa:upload_visa_main', 'application_form'],
  APPLICATION_FORM_PREFILLED: ['mob:funnel:visa:upload_visa_main', 'application_form_prefilled'],
  PASSENGER_DOCS: ['mob:funnel:visa:upload_visa_main', 'passenger_docs'],
  THANK_YOU: ['mob:funnel:visa:thankyou', 'thankyou'],
  THANK_YOU_ERROR: ['mob:funnel:visa:thankyou', 'thankyou_error'],
  DETAIL_VISA_TYPE: ['mob:funnel:visa:details', 'type_of_visa'],
  DETAIL_TRAVEL_INFO: ['mob:funnel:visa:details', 'traveller_info'],
  DETAIL_VISA_STEPS: ['mob:funnel:visa:details', 'steps_for_visa'],
  DOCUMENT_CHECKLIST: ['mob:funnel:visa:details', 'document_checklist'],
  ADD_DOCUMENTS_FILLFORM: ['mob:funnel:visa:upload_visa_main', 'add_documents_and_fill_form_main'],
};

export const PageErrorData = {
  LANDING:
  {
    title: 'Uh oh!',
    subTitle: 'It looks like our servers took too long.',
    suggestion: 'Try refreshing the page'
  },
  TYPE_OF_VISA:
  {
    title: 'Uh oh!',
    subTitle: 'Visa isn’t available for the duration selected. Please re-check your travel dates',
    suggestion: 'Try refreshing the page'
  },
  DOCUMENT_REQUIRED:
  {
    title: 'Uh oh!',
    subTitle: 'It looks like our servers took too long.',
    suggestion: 'Try refreshing the page'
  },
  REVIEW:
  {
    title: 'Uh oh!',
    subTitle: 'It looks like our servers took too long.',
    suggestion: 'Try refreshing the page'
  },
  PASSENGER_DOCS:
  {
    title: 'Uh oh!',
    subTitle: 'It looks like our servers took too long.',
    suggestion: 'Try refreshing the page'
  },
  VERIFY_DOCS:
  {
    title: 'Uh oh!',
    subTitle: 'It looks like our servers took too long.',
    suggestion: 'Try refreshing the page'
  },
  PREVIEW_FORM:
  {
    title: 'Uh oh!',
    subTitle: 'Unable to submit application form. Try again',
    suggestion: 'Try refreshing the page'
  }
};

export const EVENTS = {
  LANDING: {
    LOGIN_EVENT_LANDING: 'login_event_landing',
    ETICKET_EVENT_LANDING: 'eticket_event_landing',
    DOC_ATTACH_LATER_EVENT: 'document_attach_later_event',
    UPLOAD_FINISH_LANDING: 'upload_finish_landing',
    IMAGE_ATTACH_LATER_EVENT: 'image_upload_later_event'
  },
  PROGRESSIVE_SEARCH: {
    CALENDAR_EVENT_RECEIVED: 'calendar_event_received'
  },
  DOCUMENT_INFO: {
    LOGIN_EVENT_DOC_INFO: 'login_event_doc_info',
    ETICKET_EVENT_DOC_INFO: 'eticket_event_doc_info',
    UPLOAD_FINISH_EVENT_DOC_INFO: 'upload_finish_event_doc_info'
  },
  PASSSENGER_DOCS: {
    ETICKET_EVENT_PASSENGER_DOCS: 'eticket_event_passenger_docs',
    UPLOAD_FINISH_PASSENGER_DOCS: 'upload_finish_passenger_docs'
  },
  DOC_VERIFICATION: {
    ETICKET_EVENT_DOCUMENT_VERIFICATION: 'eticket_event_doc_verification',
    UPLOAD_FINISH_DOCUMENT_VERIFICATION: 'upload_finish_doc_verification'
  },
  UPLOAD_ETICKET: {
    ETICKET_DOCUMENT_ATTACHED_EVENT: 'eticket_doc_attached_event',
    HOTEL_VOUCHER_ATTACHED_EVENT: 'hotel_voucher_doc_attached_event'
  },
  NEXT_TRAVELLER_UPLOAD: {
    UPLOAD_FINISH_NEXT_TRAVELLER: 'upload_finish_next_traveller',
    ETICKET_EVENT_NEXT_TRAVELLER: 'eticket_event_next_traveller'
  },
  REVIEW: {
    PAYMENT_EVENT_REVIEW: 'payment_event_review'
  },
  ADD_DOCUMENTS: {
    DOCUMENT_SCANNED_EVENT : 'document_scanned_event',
    DOCUMENT_SCAN_FINISH_EVENT : 'document_scan_finish_event',
    DOCUMENT_ATTACHED_EVENT : 'document_attached_event'
  }

};


export const DOCTYPE = {
  FORM: 0,
  PASSPORT_FRONT: 1,
  PASSPORT_BACK: 2,
  PHOTOGRAPH: 3,
  ETICKET: 4
};

export const DEFAULT_VISA_RET_DAYS = 7;

export const MARRIED = 'Married';
export const GENDER = {
  MALE: 'Male',
  FEMALE: 'Female'
};

export const MONTHS = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August',
  'September', 'October', 'November', 'December'];

export const FORM_DATA_TYPE = {
  TEXT: 'text',
  DATE: 'date',
  RADIO: 'radio',
  MULTI: 'multi'
};

export const FORM_DATA_TYPE_NEW = {
  TEXT: 'text',
  DATE: 'date',
  RADIO: 'radio',
  MULTI: 'multiselect',

  GROUP: 'group',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  DROPDOWN: 'dropdown'
};

export const DOC_OP = {
  UPLOAD: 'UPLOAD',
  VERIFY: 'VERIFY'
};

export const BOOKING_STATUS = {
  BOOKING_INITIATED: 'BOOKING_INITIATED',
  PAYMENT_PENDING: 'PAYMENT_PENDING'
};

export const PASSENGER_STATUS = {
  PASSENGER_INITIATED: { status: 'PASSENGER_INITIATED', type: 0 },
  DOCUMENTS_PENDING: { status: 'DOCUMENTS_PENDING', type: 1 },
  DOCUMENTS_UPLOADED: { status: 'DOCUMENTS_UPLOADED', type: 2 },
  DOCUMENTS_VERIFIED: { status: 'DOCUMENTS_VERIFIED', type: 3 },
  DOCUMENTS_VERFICATION_COMPLETE: { status: 'DOCUMENTS_VERFICATION_COMPLETE', type: 4 },
};

export const VISA_DOCUMENT_TYPE = {
  PASSPORT_FRONT: { name: 'PASSPORT_FRONT', type: 0 },
  PASSPORT_BACK: { name: 'PASSPORT_BACK', type: 1 },
  PHOTOGRAPH: { name: 'PHOTOGRAPH', type: 2 },
  ETICKET: { name: 'ETICKET', type: 3 },
  HOTEL_VOUCHERS: { name: 'HOTEL_VOUCHERS', type: 4 },
  FORM: { name: 'FORM', type: 5 },
};

export const DOCUMENT_TYPE = {
  PASSPORT_FRONT: 0,
  PASSPORT_BACK: 1,
  PHOTOGRAPH: 2,
  ETICKET: 3,
  HOTEL_VOUCHERS: 4,
  FORM: 5
};

export const DOCUMENT_TYPE_STRING = {
  PPF: 'PPF',
  PPB: 'PPB',
  PHOTO: 'PHOTO',
  ETKT: 'ETKT',
  HTLVOUCHER: 'HTLVOUCHER'
};

export const PASSENGER_BOOKING_STATUS = {
  PASSENGER_INITIATED: 0,
  DOCUMENTS_PENDING: 1,
  DOCUMENTS_UPLOADED: 2,
  DOCUMENTS_VERIFIED: 3,
  DOCUMENTS_VERFICATION_COMPLETE: 4
};

export const landingActionTypes = {
  STATE_LOADING: 'VISA_LANDING_STATE_LOADING',
  STATE_NETWORK_ERROR: 'VISA_LANDING_STATE_NETWORK_ERROR',
  STATE_ERROR: 'VISA_LANDING_STATE_ERROR',
  STATE_SUCCESS: 'VISA_LANDING_STATE_SUCCESS',
  DELETE_PENDING_STATE_FAILURE: 'DELETE_PENDING_STATE_FAILURE',
  USER_NOT_LOGGED_IN: 'VISA_USER_NOT_LOGGED_IN',
  DELETE_PENDING_LOADER: 'DELETE_PENDING_LOADER',
  HIDE_DELETE_PENDING_BOOKING_OVERLAY: 'HIDE_DELETE_PENDING_BOOKING_OVERLAY',
  SHOW_DELETE_PENDING_BOOKING_OVERLAY: 'SHOW_DELETE_PENDING_BOOKING_OVERLAY'

};


export const typeOfVisaActionTypes = {
  STATE_LOADING: 'TYPE_VISA_LOADING',
  STATE_NETWORK_ERROR: 'TYPE_VISA_NETWORK_ERROR',
  STATE_ERROR: 'TYPE_VISA_ERROR',
  STATE_SUCCESS: 'TYPE_VISA_SUCCESS',
  PRICE_BREAKUP_TOGGLE: 'PRICE_BREAKUP_TOGGLE',
  UPDATE_VISA_SELECTION: 'UPDATE_VISA_SELECTION'
};

// VISA-EditTravellers
export const EditTraveller = {
  REMOVE_TRAVELLER_MSG: 'Pick Travellers you want to remove',
  ADD_LATER_MSG: 'Names of travellers can be added later',
  ADD_TRAVELLER_POPUP: '+ ADD TRAVELLER',

  ADD_NUMBER_OF_TRAVELLERS: 'ADD TRAVELLER',
  ADD_ACTION: 'ADD',
  INFANT_CHECKBOX_TITLE: 'Traveller is an infant',
  INFANT_AGE_ERROR_MSG: 'Age of infant cannot be greater than 11 months',
  AGE_ERROR_MSG: 'Please enter age to add',
  TRAVELLER_ERROR_MSG: 'Please select travellers to delete',
  ADD_TRAVELLER_POP_UP: 'Age is required to compute visa charges',

  ALL_TRAVELLERS_REMOVE_ERR_MSG: 'Cannot remove all travellers',
  REMOVE_TRAVELLER: 'Remove Traveller?',
  REMOVE_TRAVELLER_ACTION: 'REMOVE TRAVELLER',
  CANCEL_ACTION: 'CANCEL',

  ACTION_BUTTON: {
    REMOVE: 'REMOVE',
    DONE: 'DONE'
  }
};

// VISA-EditTravellers :: VisaDetailCard
export const VisaDetail = {

  INFO_ONLY: 'Information only for',
  APPLYING_VISA_FOR: 'Applying Visa for',
  VISA_TYPE: 'VISA TYPE',
  PROCESSING_TIME: 'PROCESSING TIME',
  VALIDITY: 'VALIDITY',
  TRAVELLERS: 'TRAVELLERS',
  EDIT_TRAVELLER: 'EDIT TRAVELLER'
};

// VISA-DocumentVerification
export const DocumentVerification = {
  VERIFY_DOCS_TITLE: 'Verify Documents',
  VERIFY_TRAVEL_DOCS: 'One Last Check...',
  PDF: 'pdf'
};

export const DocumentVerificationPopUp = {
  VISA_DOCS_OCR_MSG: 'Performing OCR…',
  VISA_GENERATING_FORM_MSG: 'Generating Visa Application form…'
};

// VISA-Review
export const VisaReview = {
  VISA_REVIEW_PAGE_TITLE: 'Review Details',

  WELL_DONE: 'Great Work!',
  TRAVELLERS_COMPLETE: 'Travellers Complete',
  REVIEW_INFORMATION_MSG: 'Review the below information before proceeding to pay.',

  STEP1: '1',
  VISA_INFORMATION: 'VISA INFORMATION',
  APPLYING_FOR: 'APPLYING FOR',
  DATE_OF_ENTRY: 'DATE OF ENTRY',
  DATE_OF_EXIT: 'DATE OF EXIT',
  TYPE_OF_VISA: 'TYPE OF VISA',
  VALIDITY: 'VALIDITY',
  PROCESSING_TIME: 'PROCESSING TIME',

  STEP2: '2',
  TRAVELLER_LIST: 'LIST OF TRAVELLER(s)',
  VIEW_ALL_DOCUMENT_AND_FORMS: 'VIEW ALL DOCUMENT AND FORMS',
  TRAVELLER_DETAILS: 'TRAVELLER DETAILS',

  STEP3: '3',
  PRIMARY_TRAVELLER_DETAILS: 'PRIMARY TRAVELLER DETAILS',
  PRIMARY_TRAVELLER_MSG: 'Confirm your contact details. All communication and E-Visa will be sent to this contact.',
  NOT_YOU: 'Want to change contact details?',
  GST_CHECKBOX_TITLE: 'I have a GSTIN number',
  EMAIL: 'EMAIL',
  PHONE_NUMBER: 'PHONE NUMBER',
  REGISTRATION_NUMBER: 'REGISTRATION NUMBER',
  REGISTERED_COMPANY_NAME: 'REGISTERED COMPANY NAME',
  REGISTERED_COMPANY_ADDRESS: 'REGISTERED COMPANY ADDRESS',

  STEP4: '4',
  PRICING_INFORMATION: 'PRICING INFORMATION',
  TOTAL_PRICE: 'TOTAL PRICE',
  OFFERS: 'OFFERS',
  HAVE_A_COUPON: 'Have a coupon code?',

  PROMO_CARD_QUESTION: 'Have your own code?',
  PROMO_CARD_HINT: 'Enter your code to avail discount'

};

export const ReviewGSTData = [
  {
    index: 0,
    id: 'gstIn',
    label: VisaReview.REGISTRATION_NUMBER,
    value: '',
    isError: false,
    errorMsg: ' GSTIN number not valid'
  },
  {
    index: 1,
    id: 'gstCompanyName',
    label: VisaReview.REGISTERED_COMPANY_NAME,
    value: '',
    isError: false,
    errorMsg: ' Empty Field'
  },
  {
    index: 2,
    id: 'gstCompanyAddress',
    label: VisaReview.REGISTERED_COMPANY_ADDRESS,
    value: '',
    isError: false,
    errorMsg: ' Empty Field'
  }
];


// ETICKET
export const EticketUpload = {
  ATTACH: 'attach',
  REMOVE: 'remove',
  ETICKET_ATTACHED: 'Eticket succesfully attached',
  ETICKET_ATTACH_FAILURE: 'Eticket attachment failure, Try again',
  ETICKET_REMOVED: 'Eticket succesfully removed',
  ETICKET_REMOVE_FAILURE: 'Unable to remove Eticket, Try again'
};


// HOTEL VOUCHERS
export const HotelVoucherUpload = {
  ATTACH: 'attach',
  REMOVE: 'remove',
  HOTEL_VOUCHER_ATTACHED: 'Hotel Voucher succesfully attached',
  HOTEL_VOUCHER_ATTACH_FAILURE: 'Hotel Voucher attachment failure, Try again',
  HOTEL_VOUCHER_REMOVED: 'Hotel Voucher succesfully removed',
  HOTEL_VOUCHER_REMOVE_FAILURE: 'Unable to remove Hotel Voucher, Try again'
};

export const MARITAL_STATUS_KEY = 'maritalStatus_';

export const PaxFormData = [
  {
    index: 1,
    key: 'firstName_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'First Name',
    length: { min: 2, max: 30 },
    pattern: /^[a-zA-Z][a-zA-Z0-9 \\-]*$/
  },
  {
    index: 2,
    key: 'lastName_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Last Name',
    pattern: /^[a-zA-Z][a-zA-Z0-9 \\-]*$/
  },
  {
    index: 3,
    key: MARITAL_STATUS_KEY,
    selectedVal: '',
    value: ['Married', 'Single', 'Divorced', 'Widow', 'Child', 'Deceased', 'Unspecific'],
    type: 'radio',
    label: 'Marital Status'
  },
  {
    index: 4,
    key: 'gender_',
    selectedVal: '',
    value:
      ['Male', 'Female'],
    type: 'radio',
    label: 'Gender'
  },
  {
    index: 5,
    key: 'nationality_',
    selectedVal: 'Indian',
    value: '',
    type: 'text',
    label: 'Nationality',
    placeHolder: '(Only for Indian Nationals)'
  },
  {
    index: 6,
    key: 'religion_',
    selectedVal: '',
    value: ['Hindu', 'Christian', 'Muslim', 'Sikh', 'Zoroastrian', 'Buddhist', 'Jewish', 'Others'],
    type: 'multi',
    label: 'Religion'
  },
  {
    index: 7,
    key: 'dateOfBirth_',
    selectedVal: '',
    value: '',
    type: 'date',
    label: 'Date of Birth',
    pattern: /^(3[01]|[12][0-9]|0?[1-9])\/(1[012]|0?[1-9])\/(\d{4})*$/,
    placeHolder: 'DD/MM/YYYY'
  },
  {
    index: 8,
    key: 'countryOfBirth_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Country of Birth',
    placeHolder: 'Eg. India',
    pattern: /^[a-zA-Z][a-zA-Z0-9 \\-]*$/
  },
  {
    index: 9,
    key: 'placeOfBirth_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Place of Birth',
    length: { min: 2, max: 30 },
    placeHolder: 'Eg. Delhi',
    pattern: /^[a-zA-Z][a-zA-Z0-9, \\s\\-]*$/
  },
  {
    index: 10,
    key: 'motherName_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Mother\'s Name',
    length: { min: 2, max: 30 },
    pattern: /^[a-zA-Z][a-zA-Z0-9 \\-]*$/
  },
  {
    index: 11,
    key: 'fatherName_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Father\'s Name',
    length: { min: 2, max: 30 },
    pattern: /^[a-zA-Z][a-zA-Z0-9 \\-]*$/
  },
  {
    index: 12,
    key: 'spouseName_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Spouse Name',
    length: { min: 2, max: 30 },
    placeHolder: 'Leave empty if Single',
    pattern: /^[a-zA-Z][a-zA-Z0-9 \\-]*$/
  },
  {
    index: 13,
    key: 'profession_',
    selectedVal: '',
    value: ['Retired', 'Business', 'Housewife', 'Student', 'Employee', 'None'],
    type: 'multi',
    label: 'Profession'
  },
  {
    index: 14,
    key: 'passportNo_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Passport Number',
    length: { min: 2, max: 50 },
    pattern: /^[a-zA-Z0-9]*$/
  },
  {
    index: 15,
    key: 'ppDateOfIssue_',
    selectedVal: '',
    value: '',
    type: 'date',
    label: 'Passport Issuance Date',
    pattern: /^(3[01]|[12][0-9]|0?[1-9])\/(1[012]|0?[1-9])\/(\d{4})*$/,
    placeHolder: 'DD/MM/YYYY'
  },
  {
    index: 16,
    key: 'ppDateOfExpiry_',
    selectedVal: '',
    value: '',
    type: 'date',
    label: 'Passport Expiry Date',
    pattern: /^(3[01]|[12][0-9]|0?[1-9])\/(1[012]|0?[1-9])\/(\d{4})*$/,
    placeHolder: 'DD/MM/YYYY'
  },
  {
    index: 17,
    key: 'ppPlaceOfIssue_',
    selectedVal: '',
    value: '',
    type: 'text',
    label: 'Passport Issuance Place',
    length: { min: 0, max: 20 },
    pattern: /^[a-zA-Z][a-zA-Z0-9, \\s\\-]*$/
  },
  {
    index: 18,
    key: 'travelStartDate_',
    selectedVal: '',
    value: '',
    type: 'date',
    label: 'Date of Entry',
    pattern: /^(3[01]|[12][0-9]|0?[1-9])\/(1[012]|0?[1-9])\/(\d{4})*$/,
    placeHolder: 'DD/MM/YYYY'
  },
  {
    index: 19,
    key: 'travelEndDate_',
    selectedVal: '',
    value: '',
    type: 'date',
    label: 'Date of Exit',
    pattern: /^(3[01]|[12][0-9]|0?[1-9])\/(1[012]|0?[1-9])\/(\d{4})*$/,
    placeHolder: 'DD/MM/YYYY'
  }
];

export const PAYMENT_STATUS = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILED'
};

export const loadNewVisaForm = true;

export const DEPENDENTS = 'dependents';
export const PARENTCURLYBRACES = '{parent}';
export const REMOVE_COUPON = 'REMOVE_COUPON';
export const APPLY_COUPON = 'APPLY_COUPON';

export const VOUCHERTYPE = {
  ETICKET: 'ETICKET',
  HOTEL_VOUCHER: 'HOTEL_VOUCHER'
};

export const LANDING_HEADER_HEIGHT = 60;


export const VOUCHERS_TEXT = 'VOUCHERS';
export const IDENTITY_TEXT = 'IDENTITY';

export const PASSPORT_DATA_VERIFY_TEXT = 'Your passport is the only source of truth. Enter the information exactly as mentioned in your current passport.';

export const OVERLAY_ACTIONS = {
  DELETE_BOOKING: 'DELETE_BOOKING'
};

export const DEFAULT_LANDING_CARDS_ORDER = ['RECOMMENDED_VISA_CARD', 'PENDING_VISA_BOOKINGS',
  'TOP_DESTINATIONS', 'VISA_ON_ARRIVAL_DESTINATIONS',
  'INFO_ONLY_DESTINATIONS', 'PERSUASION_ELEMENTS', 'USER_REVIEWS'];

export const PlatformAuthCode = {
  IOS: 'Basic bWFrZW15dHJpcElvczpWMXNhRnVubjNs',
  ANDROID: 'Basic bWFrZW15dHJpcEFuZHJvaWQ6VjFzYUZ1bm4zbA==',
  MWEB: 'Basic bWFrZW15dHJpcE13ZWI6VjFzYUZ1bm4zbA=='
}
export const HARDWARE_BACK_PRESS = 'hardwareBackPress';

export const VISA_ERRORMESSAGES = {
  IMAGE_ERROR :  'Please fix errors in the image',
  WARNING_ERROR : 'We recommend that you fix the below errors',
}