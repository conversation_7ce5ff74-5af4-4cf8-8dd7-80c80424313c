import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import crossIcon from '@mmt/legacy-assets/src/close-white.webp';
import styles from '../css/VisaMainCss';

export const ALL_FAQS = 'All FAQs';

export default class StickyHeader extends React.PureComponent {
  static navigationOptions = {
    header: null
  };

  render() {
    return (
      <LinearGradient start={{ x: 0.0, y: 1.5 }} end={{ x: 1.0, y: 0.0 }} colors={['#6798e7', '#6dd0f0']} style={styles.stickyHeaderFaq}>
        <View style={styles.topStatusBar}>
          <View style={styles.stickyBarWrapperDocument}>
            <TouchableOpacity onPress={this.props.closeFn}>
              <Image style={[styles.crossIcon]} source={crossIcon} />
            </TouchableOpacity>
            <View style={styles.stickyTextWrapper}>
              <Text style={[styles.font14, styles.WhiteText, styles.blackFont, styles.VisaDocumentInfoFAQText]}>
                {this.props.title || ALL_FAQS}
              </Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    );
  }
}


StickyHeader.propTypes = {
  title: PropTypes.string,
  closeFn: PropTypes.func.isRequired
};

