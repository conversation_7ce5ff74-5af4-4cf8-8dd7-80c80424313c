import React from 'react';
import {
  View,
  Text,
  TouchableOpacity
} from 'react-native';
import PropTypes from 'prop-types';
import {EditTraveller} from '../VisaConstants';
import styles from '../css/VisaMainCss';

export default class RemoveTraveller extends React.Component {
  shouldComponentUpdate(nextProps) {
    return !(this.props === nextProps);
  }

  remove = () => {
    this.props.removeTravellers(EditTraveller.ACTION_BUTTON.REMOVE);
  }

  render() {
    return (
      <View style={styles.EditTravellerOverlayMain}>
        <View style={[styles.EditTravellerText, styles.whitebg]}>
          <Text style={[styles.font34, styles.lightFont, styles.BlackText, styles.mar35Bt]}>
            {EditTraveller.REMOVE_TRAVELLER}
          </Text>
          <View style={styles.mar40Bt}>
            <Text style={[styles.font16, styles.defaultGrey, styles.lineHeight20]}>
            You are about to
              <Text style={[styles.boldFont, styles.BlackText]}> remove {this.props.removePaxNameString} </Text>
               from the list of travellers. You won’t be able to apply visa for these travellers
            </Text>
          </View>
          <View style={[styles.flexRow, styles.spaceBetween]}>
            <TouchableOpacity onPress={this.props.dismissRemoveTravellerPopup}>
              <Text style={[styles.font16, styles.blueLink]}>
                {EditTraveller.CANCEL_ACTION}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={this.remove}>
              <Text style={[styles.font16, styles.blueLink, styles.boldFont]}>
                {EditTraveller.REMOVE_TRAVELLER_ACTION}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }
}

RemoveTraveller.propTypes = {
  removeTravellers: PropTypes.func.isRequired,
  dismissRemoveTravellerPopup: PropTypes.func.isRequired,
  removePaxNameString: PropTypes.string.isRequired
};
