import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity
} from 'react-native';
import PropTypes from 'prop-types';
import tripHeaderBackIcon from '@mmt/legacy-assets/src/trip_header_back_icon.webp';
import styles from '../css/VisaMainCss';

export const EDIT_TRAVELLER = 'Edit Travellers';

export default class StickyHeaderEditTraveller extends React.PureComponent {
    static navigationOptions = {
      header: null
    };

    render() {
      return (
        <View style={[styles.stickyHeaderMain]}>
          <View style={styles.topStatusBar}>
            <View style={styles.stickyBarWrapperDocument}>
              <TouchableOpacity onPress={this.props.onBackPressed}>
                <Image style={[styles.mar3Bt, styles.backarrow]} source={tripHeaderBackIcon} />
              </TouchableOpacity>
              <View style={styles.stickyTextWrapper}>
                <Text numberOfLines={1} ellipsizeMode="tail" style={[styles.font18, styles.BlackText, styles.lightFont]}>
                  {EDIT_TRAVELLER}
                </Text>
              </View>
            </View>
          </View>
        </View>
      );
    }
}

StickyHeaderEditTraveller.propTypes = {
  onBackPressed: PropTypes.func.isRequired
};
