import React from "react";
import {
  View,
  ScrollView,
  Text,
  ActivityIndicator,
  StyleSheet,
} from "react-native";
import PropTypes from "prop-types";
import { isEmpty } from "lodash";
import { Actions } from "react-native-router-flux";
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import ViewState from '@mmt/legacy-commons/Common/constants/ViewState';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import VisaMainCss from "../css/VisaMainCss";
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import {
  PAGENAME,
  PageErrorData,
  TRACKING_PAGENAME,
} from "../VisaConstants";
import {
  getDisplayDate,
  formatNavSubTitle,
  getTravellerText,
} from "../VisaUtils";
import VisaFullPageError from "../Error/VisaFullPageError";
import { getVisaChecklist } from "../VisaNetworkUtils";
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import PageHeader from "./v2/PageHeader";
import DocInfoTag from "./v2/DocInfoTag";
import iconSchedule from '@mmt/legacy-assets/src/Visa/ic_required_docs.webp';
import iconDocs from '@mmt/legacy-assets/src/Visa/ic_documents.webp';
import StickyHeader from "../Common/StickyHeader";
import DocumentDetails from "./v2/DocumentDetails";
import { VisaNavigation } from '../Navigation';
import { logPdtActionEvent } from "../Analytics/Pdt/PdtTracker";
import { PDT_EVENT_NAME, PDT_PAGENAME } from "../Analytics/Pdt/PdtConstants";


export default class VisaDocumentInfoV2 extends BasePage {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props, PAGENAME.DOCUMENT_REQUIRED);
    this.visaParams = { ...this.props.visaParams, page: PAGENAME.DOCUMENT_REQUIRED };
    this.state = {
      viewState: ViewState.LOADING,
      loaderText: "Loading documents required..",
      documentCategoryList: [],
    };
    this.userLoggedIn = false;
    this.previousPage = this.props.visaParams.page;
    this.checklistResponse = null;
  }

  componentDidMount() {
    super.componentDidMount();
    this.fetchVisaDocumentInfo();
  }

  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.DOCUMENT_CHECKLIST,
      clickEvent, this.visaParams, this.previousPage,
    );
  };

  onRefreshPressed = () => {
    this.setState({ viewState: ViewState.LOADING }, () => {
      this.fetchVisaDocumentInfo();
    });
  };

  onBackPressed = () => {
    this.trackClickEvent("back_clicked");
    logPdtActionEvent(PDT_EVENT_NAME.APP_BACK_CLICK, {pageName: PDT_PAGENAME.DETAILS});
    VisaNavigation.pop();
  };

  async fetchVisaDocumentInfo() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET,
      });
      return;
    }

    try {
      const { visaTypeId, countryCode, locusCode } = this.visaParams;
      const request = {
        typeOfVisa: Number.parseInt(visaTypeId),
        countryCode,
        locusCode,
        formResponse: isEmpty(this.props.travelPurpose) ? undefined : { ...this.props.travelPurpose },
      };
      const response = await getVisaChecklist(request);
      let isValidResponse = false;
      if (response) {
        this.checklistResponse = response;
        const documentCategoryList = response.documents;
        if (documentCategoryList) {
          isValidResponse = true;
          this.userLoggedIn = response.isLoggedIn;
          this.setState({
            viewState: ViewState.SHOW_DETAIL,
            documentCategoryList,
          });

          VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
            TRACKING_PAGENAME.DOCUMENT_CHECKLIST,
            this.previousPage, this.visaParams,
          );
        }
      }

      if (!isValidResponse) {
        this.setState({
          viewState: ViewState.ERROR,
        });
        return;
      }
    } catch (e) {
      this.setState({
        viewState: ViewState.ERROR,
      });
    }
  }

  render() {
    return (
      <View style={VisaMainCss.VisaMainContainer}>
        {this.state.viewState === ViewState.LOADING && this.renderProgressView()}
        {this.state.viewState === ViewState.NO_INTERNET && this.renderNoNetworkView()}
        {this.state.viewState === ViewState.ERROR && this.renderErrorView()}
        {this.state.viewState === ViewState.SHOW_DETAIL && this.renderContent()}
      </View>
    );
  }

  renderNoNetworkView = () => (
    <NoInternetView
      onRetry={this.onRefreshPressed}
    />
  );

  renderErrorView = () => (
    <VisaFullPageError
      title={PageErrorData.DOCUMENT_REQUIRED.title}
      subTitle={PageErrorData.DOCUMENT_REQUIRED.subTitle}
      suggestion={PageErrorData.DOCUMENT_REQUIRED.suggestion}
      onRefreshPressed={this.onRefreshPressed}
      renderStickyHeader={this.renderStickyHeader}
      onBackPressed={this.onBackPressed}
    />
  );

  renderProgressView = () => (
    <View style={VisaMainCss.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={VisaMainCss.indicator} size="large" />
      <Text style={VisaMainCss.darkText}> {this.state.loaderText}
      </Text>
    </View>
  );

  getSubTitle() {
    return formatNavSubTitle(
      this.visaParams.visaType,
      getDisplayDate(this.visaParams.startDate),
      getTravellerText(this.visaParams.paxList), "Departs",
    );
  }

  renderStickyHeader = () => (
    <StickyHeader
      title={`${this.visaParams.destCountry}`}
      subtitle={this.getSubTitle()}
      onBackPressed={this.onBackPressed}
    />
  );

  renderContent = () => {
    const { documentCategoryList } = this.state;
    return (
      <View style={styles.pageWrap}>
        <PageHeader
          onBack={this.onBackPressed}
          onDownloadClick={() => {
          }}
        />
        <ScrollView>
          <View style={styles.contentWrap}>
            <View style={styles.persuasions}>
              {this.renderDocumentPersuasions()}
            </View>
            {documentCategoryList.map((list, i) =>
              <View style={styles.docListComponent}>
                <View style={AtomicCss.marginBottom10}>
                  <Text style={styles.componentHdr}>{list.category}</Text>
                </View>
                {list.docsList.map((item, k) =>
                  <DocumentDetails document={item} />,
                )}
              </View>,
            )}

          </View>
        </ScrollView>
      </View>
    );
  };
  // renderDocumentPersuasions = () => {
  //   const persuasions = [];
  //   const { docsReqdOffline, documentDeliveryMode, docsReqdOnline, deliveryDetails, docsReqdPreBooking } = this.checklistResponse;
  //
  //   const onlyUpload = (docsReqdOnline && docsReqdOnline.length > 0) && (!docsReqdOffline || (docsReqdOffline && docsReqdOffline.length === 0));
  //
  //   const email = (docsReqdOffline && docsReqdOffline.length > 0) && documentDeliveryMode === 1;
  //
  //   const courier = (docsReqdOffline && docsReqdOffline.length > 0) && documentDeliveryMode === 2;
  //
  //   const uploadAndOffline = (docsReqdOnline && docsReqdOnline.length > 0) && (docsReqdOffline && docsReqdOffline.length > 0);
  //   if (onlyUpload) {
  //     persuasions.push(<DocInfoTag icon={iconDocs} text='All the documents have to be uploaded on MakeMyTrip Website/App.' />);
  //     persuasions.push(<DocInfoTag icon={iconSchedule} text=' In case all the documents are not available while booking for a VISA, you can upload them after the booking is done.' />);
  //   }
  //   else if (uploadAndOffline) {
  //     persuasions.push(<DocInfoTag icon={iconDocs} text='Some documents have to be uploaded on MakeMyTrip Website/App before booking the Visa.' />);
  //     if (email) {
  //       persuasions.push(
  //         <DocInfoTag icon={iconSchedule} text='Soft copies of some documents are required for VISA clearance which are shared below in the Document Checklist. Kindly send them to us via email after booking the VISA. Our email ID is '
  //           boldText={deliveryDetails} />
  //       );
  //     }
  //     if (courier) {
  //       persuasions.push(
  //         <DocInfoTag icon={iconSchedule} text='Hard copies of some documents are required for VISA clearance which are shared below in the Document Checklist. Kindly send the hard copies to us via courier to our address: '
  //           boldText={deliveryDetails} />
  //       );
  //     }
  //   } else if (email) {
  //     persuasions.push(
  //       <DocInfoTag icon={iconSchedule} text='Kindly send soft copies of all the below mentioned documents to us via email. Our email ID is '
  //         boldText={deliveryDetails} />
  //     );
  //   } else if (courier) {
  //     persuasions.push(
  //       <DocInfoTag icon={iconSchedule} text='Kindly send the hard copies of the below mentioned documents to us via courier. Our address is '
  //         boldText={deliveryDetails} />
  //     );
  //   } else {
  //     persuasions.push(<DocInfoTag icon={iconDocs} text='Some documents have to be uploaded on MakeMyTrip Website/App before booking the Visa.' />);
  //   }
  //
  //   return persuasions;
  // }

  renderDocumentPersuasions = () => {
    const persuasions = [];
    let {
      docsReqdOffline,
      documentDeliveryMode,
      docsReqdOnline,
      deliveryDetails,
      docsReqdPreBooking,
    } = this.checklistResponse;

    const preUpload = docsReqdPreBooking && (docsReqdOnline && docsReqdOnline.length > 0);

    const preUploadOnly = documentDeliveryMode === 0 && preUpload;

    const emailOnly = documentDeliveryMode === 1 && (docsReqdOffline && docsReqdOffline.length > 0);

    const courierOnly = documentDeliveryMode === 2 && (docsReqdOffline && docsReqdOffline.length > 0);

    const preUploadAndPostEmail = emailOnly && preUpload;

    const preUploadAndPostCourier = courierOnly && preUpload;

    const postUpload = !docsReqdPreBooking && docsReqdOnline && docsReqdOnline.length > 0;

    const postUploadOnly = documentDeliveryMode === 0 && postUpload;

    const postUploadAndEmail = emailOnly && postUpload;

    const postUploadAndCourier = courierOnly && postUpload;

    if (preUploadOnly) {
      persuasions.push(<DocInfoTag icon={iconDocs}
                                   text="All the documents have to be uploaded on MakeMyTrip Website/App before making the payment." />);
    } else if (postUploadOnly) {
      persuasions.push(<DocInfoTag icon={iconDocs}
                                   text="All the documents have to be uploaded on MakeMyTrip Website/App." />);
      persuasions.push(<DocInfoTag icon={iconSchedule}
                                   text="You can upload the documents later, after making the payment." />);
    } else if (preUpload ) {
      persuasions.push(<DocInfoTag icon={iconDocs}
                                   text="Some documents have to be uploaded on MakeMyTrip Website/App before making the payment." />);
    } else if (postUpload ) {
      persuasions.push(<DocInfoTag icon={iconDocs}
                                   text="Some documents have to be uploaded on MakeMyTrip Website/App after booking the Visa." />);
    }

    if (preUploadAndPostEmail || postUploadAndEmail) {
      persuasions.push(<DocInfoTag icon={iconSchedule}
                                   text="Soft copies of some documents are required for Visa clearance which are shared below in the Document Checklist. Kindly send these to us via email after booking the Visa. Our email ID is "
                                   boldText={deliveryDetails} />);
    } else if (emailOnly) {
      persuasions.push(<DocInfoTag icon={iconSchedule}
                                   text="Kindly send soft copies of all the documents (shared below in the Document Checklist) to us via email after booking the Visa. Our email ID is "
                                   boldText={deliveryDetails} />);
    } else if (preUploadAndPostCourier || postUploadAndCourier) {
      persuasions.push(
        <DocInfoTag icon={iconSchedule}
                    text="Hard copies of some documents are required for VISA clearance which are shared below in the Document Checklist. Kindly send these to us via courier after booking the Visa. Our address is  "
                    boldText={deliveryDetails} />,
      );
    } else if (courierOnly) {
      persuasions.push(
        <DocInfoTag icon={iconSchedule}
                    text="Kindly send hard copies of all the documents (shared below in the Document Checklist) to us via courier after booking the Visa. Our address is "
                    boldText={deliveryDetails} />,
      );
    }

    return persuasions;
  };
}

VisaDocumentInfoV2.propTypes = {
  visaParams: PropTypes.object.isRequired,
};
const styles = StyleSheet.create({
  pageWrap: {
    flex: 1,
    backgroundColor: "white",
  },
  docListComponent: {
    marginTop: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderColor: "#e5e5e5",
  },
  componentHdr: {
    color: "#878787",
    fontSize: 12,
    fontFamily: fonts.bold,
    textTransform: "uppercase",
  },
  contentWrap: {
    padding: 15,
  },
  viewSampleImg: {
    width: 12,
    height: 12,
    marginRight: 5,
  },
  viewSampleTxt: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.azure,
  },
  persuasions: {
    borderRadius: 4,
    backgroundColor: "#ffedd1",
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 4,
  },
});

