import React from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import FaqsCollapsed from './FaqsCollapsed';
import {trackClickEvent} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

export default class InfoFaqsAll extends React.Component {
  constructor(props) {
    super(props);
    this.tabbingFn = this.tabbingFn.bind(this);
    this.state = {
      activeCard: null,
      collapseActiveCard: true
    };
  }

  tabbingFn(index, nextState) {
    this.setState({
      activeCard: index,
      collapseActiveCard: nextState
    });
    trackClickEvent(this.props.pageName, 'faq_clicked');
  }

  static navigationOptions = {
    header: null
  };

  renderFaqs = () => {
    const faqs = [];
    if (this.props.faqs) {
      this.props.faqs.forEach((element, index) => {
        const isActive = index === this.state.activeCard
          ? this.state.collapseActiveCard
          : false;
        faqs.push(this.faqsCollapsedView(element, index, isActive));
      });
    }
    return faqs;
  }

  faqsCollapsedView = (item, index, isActive) => (
    <FaqsCollapsed
      key={index}
      index={index}
      faq={item}
      isActive={isActive}
      tabbingFn={this.tabbingFn}
    />
  );

  render() {
    return (
      <View style={styles.elevation1}>
        <View style={{}}>
          {
            this.renderFaqs()
          }
        </View>
      </View>
    );
  }
}

InfoFaqsAll.propTypes = {
  faqs: PropTypes.array.isRequired,
  pageName: PropTypes.string.isRequired
};
