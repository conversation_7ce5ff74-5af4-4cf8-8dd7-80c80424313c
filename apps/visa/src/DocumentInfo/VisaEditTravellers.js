import React from 'react';
import {
  View,
  ScrollView,
  Text,
  TouchableWithoutFeedback
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import _ from 'lodash';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import styles from '../css/VisaMainCss';
import StickyHeaderEditTraveller from './StickyHeaderEditTraveller';
import VisaCheckBox from '../Common/VisaCheckBox';
import {
  EditTraveller,
  INFANT_AGE_LIMIT,
  ADULT_AGE_EXTENSION,
  MONTH_AGE_EXTENSION,
  TRAVELLER,
  PAGENAME,
  ADULT,
  INFANT,
  TRACKING_PAGENAME,
  DOCUMENT_TYPE
} from '../VisaConstants';
import AddTraveller from './AddTraveller';
import RemoveTraveller from './RemoveTraveller';
import {getPaxObject, capitalize} from '../VisaUtils';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { VisaNavigation } from '../Navigation';


export default class VisaEditTraveller extends BasePage {
  static navigationOptions = {
    header: null
  };
  constructor(props) {
    super(props, PAGENAME.EDIT_TRAVELLERS);
    this.state = {
      removeTravellerOverlay: false,
      addTravellerOverlay: false,
      paxList: this.props.visaParams.paxList,
      buttonTitle: EditTraveller.ACTION_BUTTON.DONE
    };

    this.paxAge = '';
    this.paxAgeExtension = ADULT_AGE_EXTENSION;
    this.removePaxNameString = '';
    this.paxSelectionArray = _.fill(Array(this.state.paxList.length), false);
  }

  componentDidMount() {
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
      TRACKING_PAGENAME.EDIT_TRAVELLERS,
      'fromDocsReqd', this.visaParams
    );
  }

  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.EDIT_TRAVELLERS,
      clickEvent, this.props.visaParams, 'fromDocsReqd'
    );
  }

  onBackPressed = () => {
    this.props.updatePax(this.state.paxList);
    VisaNavigation.pop();
    this.trackClickEvent('back_clicked');
  };

  setPaxAge = (age, ageExtension) => {
    this.paxAge = age;
    this.paxAgeExtension = ageExtension;
  }

  removeTravellerPopup = () => {
    let selectedPax = 0;
    this.removePaxNameString = '';
    for (let i = 0; i < this.paxSelectionArray.length; i += 1) {
      if (this.paxSelectionArray[i] === true) {
        selectedPax += 1;
        if (this.removePaxNameString === '') {
          if (this.state.paxList[i].name === '') {
            this.removePaxNameString = `${capitalize(TRAVELLER)} ${i + 1}`;
          } else {
            this.removePaxNameString = (this.state.paxList[i]).name;
          }
        }
      }
    }
    if (selectedPax === this.paxSelectionArray.length) {
      showShortToast(EditTraveller.ALL_TRAVELLERS_REMOVE_ERR_MSG);
    } else if (selectedPax === 1) {
      this.setState({
        removeTravellerOverlay: true
      });
    } else if (selectedPax > 1) {
      this.removePaxNameString = `${this.removePaxNameString} + ${String(selectedPax - 1)}`;
      this.setState({
        removeTravellerOverlay: true
      });
    } else {
      // This is DONE scenario
      this.props.updatePax(this.state.paxList);
      VisaNavigation.pop();
      this.trackClickEvent('done');
    }
  }

  dismissRemoveTravellerPopup = () => {
    this.setState({
      removeTravellerOverlay: false
    });
  }

  performAction = (action) => {
    switch (action) {
      case EditTraveller.ACTION_BUTTON.REMOVE:
        {
          const updatedList = [];
          const paxList = _.cloneDeep(this.state.paxList);

          for (let i = 0; i < this.paxSelectionArray.length; i += 1) {
            if (this.paxSelectionArray[i] === false) {
              updatedList.push(paxList[i]);
            }
          }

          updatedList.forEach((pax, index) => {
            const tempPax = pax;
            const tempIndex = index + 1;
            tempPax.id = String(tempIndex);
          });

          this.paxSelectionArray = _.fill(Array(updatedList.length), false);

          this.setState({
            removeTravellerOverlay: false,
            paxList: updatedList,
            buttonTitle: EditTraveller.ACTION_BUTTON.DONE
          });
        }
        this.trackClickEvent('pax_removed');
        break;
      default:
        break;
    }
  }


  addTravellerPopup = () => {
    if ((_.filter(this.paxSelectionArray, element => element === true)).length > 0) {
      showShortToast('Please uncheck/delete traveller to add');
    } else {
      this.setState({
        addTravellerOverlay: true
      });
      this.trackClickEvent('add_pax');
    }
  }

  dismissAddTravellerPopup = () => {
    this.setPaxAge('');
    this.setState({
      addTravellerOverlay: false
    });
  }

  addTraveller = () => {
    if (this.paxAgeExtension === MONTH_AGE_EXTENSION && this.paxAge > INFANT_AGE_LIMIT) {
      showShortToast(EditTraveller.INFANT_AGE_ERROR_MSG);
      return;
    }

    const paxType = this.paxAgeExtension === ADULT_AGE_EXTENSION ? ADULT : INFANT;

    if (this.paxAge > 0) {
      const pax = getPaxObject('', this.paxAge, this.paxAgeExtension, this.state.paxList.length + 1, paxType);
      pax.docs[DOCUMENT_TYPE.ETICKET].uploaded = this.props.eticketAvailStatus;
      pax.docs[DOCUMENT_TYPE.HOTEL_VOUCHERS].uploaded = this.props.hotelVoucherAvailStatus;
      this.setState({
        addTravellerOverlay: false,
        paxList: [...this.state.paxList, pax]
      });

      this.paxSelectionArray = _.fill(Array(this.state.paxList.length + 1), false);
    } else {
      showShortToast(EditTraveller.AGE_ERROR_MSG);
    }
  }

  // Callback from VisaCheckBox state change
  onCheckBoxChange = (index, checked) => {
    this.paxSelectionArray[index] = checked;

    if ((_.filter(this.paxSelectionArray, element => element === true)).length > 0) {
      this.setState({
        buttonTitle: EditTraveller.ACTION_BUTTON.REMOVE
      });
    } else {
      this.setState({
        buttonTitle: EditTraveller.ACTION_BUTTON.DONE
      });
    }

    const params = {};
    params.pax_index = index;
    params.pax_chcked = checked;

    this.trackClickEvent('pax_selected');
  }

  renderPassengerCheckBox = () => {
    const list = [];
    this.state.paxList.forEach((pax, index) => {
      const key = `Checkbox_${index}`;
      let title = pax.name === '' ? `${capitalize(TRAVELLER)} ${index + 1}` : pax.name;
      if (this.props.visaParams.isFromSearch || (!pax.isDefaultAge)) {
        title += ` (${pax.age}${pax.ageMorY})`;
      }
      list.push(this.visaCheckBoxView(title, index, key));
    });
    return list;
  }

  visaCheckBoxView = (title, index, key) => (
    <VisaCheckBox
      key={key}
      index={index}
      title={title}
      checkBoxVal={this.paxSelectionArray[index]}
      onCheckBoxChange={this.onCheckBoxChange}
    />
  );

  renderAddTravellerOverlay = () => (
    <AddTraveller
      addTraveller={this.addTraveller}
      dismissAddTravellerPopup={this.dismissAddTravellerPopup}
      setPaxAge={this.setPaxAge}
    />
  )

  renderRemoveTraverllersOverlay = () => (
    <RemoveTraveller
      removeTravellers={this.performAction}
      dismissRemoveTravellerPopup={this.dismissRemoveTravellerPopup}
      removePaxNameString={this.removePaxNameString}
    />
  )

  render() {
    return (
      <View style={[styles.GreyBg, styles.flex1, styles.spaceBetween]}>
        <StickyHeaderEditTraveller
          onBackPressed={this.onBackPressed}
        />
        <ScrollView>
          <View style={[styles.bgWhite, styles.elevation1, styles.marT20]} >
            <View style={{paddingTop: 18, paddingBottom: 20, paddingHorizontal: 24}}>
              <Text style={styles.removeTxtHead}>
                {EditTraveller.REMOVE_TRAVELLER_MSG}
              </Text>
              {this.renderPassengerCheckBox()}
              <Text style={styles.addNameLater}>{EditTraveller.ADD_LATER_MSG}</Text>
              <TouchableWithoutFeedback onPress={this.addTravellerPopup}>
                <View>
                  <Text style={styles.EditTravellerLink}>{EditTraveller.ADD_TRAVELLER_POPUP}</Text>
                </View>
              </TouchableWithoutFeedback>
            </View>
          </View>
        </ScrollView>
        {this.state.addTravellerOverlay &&
          this.renderAddTravellerOverlay()
        }
        {this.state.removeTravellerOverlay &&
          this.renderRemoveTraverllersOverlay()
        }
        <View style={{alignSelf: 'center', marginBottom: 54}}>
          <TouchableWithoutFeedback onPress={this.removeTravellerPopup}>
            <LinearGradient
              start={{x: 1.0, y: 0.0}}
              end={{x: 0.0, y: 1.0}}
              colors={['#065af3', '#53b2fe']}
              style={styles.RemoveBtn}
            >
              <Text style={[styles.WhiteText, styles.CenterText, styles.bgTransparent, styles.boldFont]}>
                {this.state.buttonTitle}
              </Text>
            </LinearGradient>
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  }
}

VisaEditTraveller.propTypes = {
  updatePax: PropTypes.func.isRequired,
  visaParams: PropTypes.object.isRequired,
  eticketAvailStatus: PropTypes.bool.isRequired,
  hotelVoucherAvailStatus: PropTypes.bool.isRequired
};

