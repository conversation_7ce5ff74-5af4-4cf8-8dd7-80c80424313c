import React from 'react';
import {
  View,
  TouchableOpacity
} from 'react-native';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import FaqsText from './FaqsText';
import FaqsAnswer from './FaqsAnswer';

const FaqsCollapsed = ({
  isActive, index, faq, tabbingFn
}) =>
  (
    <View style={[styles.CollapsedContainer1]}>
      <TouchableOpacity
        onPress={() => tabbingFn(index, !isActive)}
        activeOpacity={0.9}
      >
      <FaqsText question={faq.question} tabbing={isActive} />
      </TouchableOpacity>
      {isActive &&
        <FaqsAnswer answer={faq.answer} />
      }
    </View>
  );

export default FaqsCollapsed;

FaqsCollapsed.propTypes = {
  isActive: PropTypes.bool.isRequired,
  index: PropTypes.number.isRequired,
  faq: PropTypes.object.isRequired,
  tabbingFn: PropTypes.func.isRequired
};
