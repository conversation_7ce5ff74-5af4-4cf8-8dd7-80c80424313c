import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Platform
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import whiteBackarrowIcon from '@mmt/legacy-assets/src/white_backarrow.webp';
import styles from '../css/VisaMainCss';
import {VisaDetail} from '../VisaConstants';

export default class VisaDetailCard extends React.Component {
  shouldComponentUpdate(nextProps) {
    return !(this.props === nextProps);
  }

  getWhiteBackIconStyle = () => {
    if (Platform.OS === 'ios') {
      return [styles.mar10Bt, styles.backarrow, styles.whiteTint];
    }
    return [styles.mar10Bt, styles.backarrow];
  }
  render() {
    const {
      isFromSearch,
      destCountry,
      visaType,
      processingTime,
      validity,
      infoOnly
    } = this.props.docInfoParams;


    return (
      <View style={[styles.topStatusCard, styles.mar20Bt]}>
        <View style={[styles.pad20Bt, styles.whitebg]}>

          <LinearGradient start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} colors={['#3bdff6', '#5998ee']} style={{height: 150}} >
            <View style={styles.pad15}>
              <TouchableOpacity
                onPress={this.props.onBackPressed}
                style={[styles.padding5]}
              >
                <Image style={this.getWhiteBackIconStyle()} source={Platform.OS === 'ios' ? backIcon : whiteBackarrowIcon} />
              </TouchableOpacity>

              <View style={{marginTop: -10}}>
                <Text style={[styles.WhiteText,
                              styles.font22,
                              styles.lightFont,
                              styles.mar3Bt, styles.bgTransparent]}
                >
                  {infoOnly ? VisaDetail.INFO_ONLY : VisaDetail.APPLYING_VISA_FOR}
                </Text>
                <Text style={[styles.WhiteText,
                              styles.font16,
                              styles.boldFont,
                              styles.bgTransparent]}
                >
                  {destCountry}
                </Text>
              </View>
            </View>
          </LinearGradient>

          <View style={styles.ApplyVisaContainer}>
            <View style={[styles.flexRow, styles.borderBtm]}>
              <Text style={[styles.regularFont, styles.width35per,
                              styles.font12,
                              styles.lightGrey,
                              styles.bgTransparent,
                              styles.regularFont]}
              >
                {VisaDetail.VISA_TYPE}
              </Text>
              <Text style={[styles.flex1,
                              styles.font12,
                              styles.defaultGrey,
                              styles.regularFont,
                              styles.bgTransparent]}
              >
                {visaType}
              </Text>
            </View>
            <View style={[styles.flexRow, styles.borderBtm]}>
              <Text style={[styles.width35per,
                              styles.font12,
                              styles.lightGrey,
                              styles.bgTransparent,
                              styles.regularFont]}
              >
                {VisaDetail.PROCESSING_TIME}
              </Text>
              <Text style={[styles.flex1,
                              styles.font12,
                              styles.defaultGrey,
                              styles.regularFont,
                              styles.bgTransparent]}
              >
                {processingTime}
              </Text>
            </View>
            {!this.props.infoOnly && <View style={[styles.flexRow, styles.borderBtm]}>
              <Text
                style={[styles.width35per, styles.font12, styles.lightGrey, styles.regularFont, styles.bgTransparent]}> {VisaDetail.VALIDITY}</Text>
              <Text
                style={[styles.flex1, styles.font12, styles.defaultGrey, styles.regularFont, styles.bgTransparent]}> {validity} </Text>
            </View>
            }
            <View style={[styles.flexRow, styles.mar20Bt]}>
              <Text style={[styles.width35per,
                              styles.font12,
                              styles.regularFont,
                              styles.lightGrey,
                              styles.bgTransparent]}
              >
                {VisaDetail.TRAVELLERS}
              </Text>
              <View style={[styles.flex1, styles.flexRow]}>
                <Text style={[styles.font12,
                                styles.defaultGrey,
                                styles.regularFont,
                                styles.bgTransparent]}
                >
                  {this.props.passengers.length}
                </Text>
              </View>
            </View>
          </View>
          {!isFromSearch && Platform.OS === 'android' &&
          <View>
            <TouchableOpacity
              style={[styles.buttonTextTop, styles.whitebg]}
              onPress={this.props.editVisaTravellers}
            >
              <View >
                <Text style={[styles.blueColor, styles.blackFont]}>
                  {VisaDetail.EDIT_TRAVELLER}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          }
        </View>
      </View>
    );
  }
}

VisaDetailCard.propTypes = {
  docInfoParams: PropTypes.object.isRequired,
  passengers: PropTypes.array.isRequired,
  editVisaTravellers: PropTypes.func.isRequired,
  onBackPressed: PropTypes.func.isRequired
};

