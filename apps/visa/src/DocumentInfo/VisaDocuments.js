import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet, Platform
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import HTMLView from 'react-native-htmlview';
import PropTypes from 'prop-types';
import {upperCase, filter, isEmpty} from 'lodash';
import greenTickIcon from '@mmt/legacy-assets/src/greenTick.webp';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import styles from '../css/VisaMainCss';

export const DOCUMENTS_REQUIRED = 'Documents Required';
export const DOC_MSG = 'Keep all documents handy before proceeding.';

export default class VisaDocuments extends React.Component {
  static navigationOptions = {
    header: null
  };

  renderDocCategories = () => {
    const categories = [];
    this.props.docs.forEach((doc) => {
    const mandatoryDocs = this.getMandatoryDocs(doc.documents);
      if (!isEmpty(mandatoryDocs)) {
        categories.push(this.categoryListView(doc));
      }
    });
    return categories;
  }

  categoryListView = doc => (
    <View key={doc.category} style={styles.VisaSubSection}>

      <Text style={[styles.blueTextLight,
        styles.font12,
        styles.boldFont,
        styles.mar20Bt]}
        >
        {doc.category}
      </Text>
      {this.renderDocsForCategory(doc)}
    </View>
  )

  renderDocsForCategory = (doc) => {
    const docs = [];
    const documentList = this.getMandatoryDocs(doc.documents);
    documentList.forEach((item) => {
      docs.push(this.docsListView(item));
    });

    return docs;
  }

  getMandatoryDocs = (docList) => filter(docList, doc => doc.mandatory === true);

  docsListView = item => (
    <View key={item.id} style={[styles.flexRow]}>
      <Image
        style={{width: 32, height: 32, marginRight: 10}}
        source={{uri: item.imageLink}}
      />
      <View style={{flex: 1}}>
        <View style={[styles.flexRow, styles.mar3Bt]}>
          <Text style={[styles.font16,
          styles.BlackText,
          styles.boldFont,
          styles.mar5Rt]}
          >
            {item.name}
          </Text>
          {upperCase(item.availabilityStatus) === 'YES' &&
            <Image
              style={styles.greenTick}
              source={greenTickIcon}
            />
          }
        </View>
        <HTMLView
          style={styles.VisaFaqHTML}
          stylesheet={htmlStyles}
          value={item.description}
        />
        <View style={styles.pad10Top} />
      </View>
    </View>
  )

  render() {
    return (
      <View style={[styles.whitebg, styles.mar20Bt]}>
        <View style={styles.VisaSection}>
          <Text style={[styles.font22,
          styles.lightFont,
          styles.defaultGrey,
          styles.lineHeight25,
          styles.mar15Bt]}
          >
            {DOCUMENTS_REQUIRED}
          </Text>
          <View style={styles.VisaSectionContainer}>
            {
              this.renderDocCategories()
            }
          </View>

          {!this.props.destInfoOnly &&
              <LinearGradient start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} colors={['#ffd9df', '#ffe6da']} style={styles.gradientTooltip} >
                <Text style={[styles.tooltip,
                styles.bgTransparent,
                styles.redText,
                styles.font13,
                styles.boldFont]}
                >
                  {DOC_MSG}
                </Text>
              </LinearGradient>
            }
        </View>
      </View>
    );
  }
}

VisaDocuments.propTypes = {
  docs: PropTypes.array.isRequired,
  destInfoOnly: PropTypes.bool.isRequired
};

const htmlStyles = StyleSheet.create({
  ul: {
    color: '#4a4a4a',
    fontFamily: fonts.regular,
    fontSize: 14
  }
});
