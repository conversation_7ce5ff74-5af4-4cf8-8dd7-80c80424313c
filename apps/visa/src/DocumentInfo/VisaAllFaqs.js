import React from 'react';
import {
  View,
  ScrollView
} from 'react-native';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import StickyHeaderFaqs from './StickyHeaderFaqs';
import InfoFaqsAll from './InfoFaqsAll';
import {PAGENAME, TRACKING_PAGENAME} from '../VisaConstants';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import { VisaNavigation } from '../Navigation';

export default class VisaAllFaqs extends BasePage {
  constructor(props) {
    super(props, PAGENAME.ALL_FAQS);
  }

  static navigationOptions = {
    header: null
  };

  componentDidMount() {
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
      TRACKING_PAGENAME.ALL_FAQS,
      this.props.visaParams.page, this.props.visaParams
    );
  }

  onCrossPressed = () => {
    VisaNavigation.pop();
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.ALL_FAQS,
      'back_clicked', this.props.visaParams, this.props.visaParams.page
    );
  }

  render() {
    return (
      <View style={[styles.GreyBg, styles.flex1, styles.spaceBetween]}>
        <StickyHeaderFaqs closeFn={this.onCrossPressed} />
        <ScrollView>
          <View style={{}}>
            <View style={[styles.MakeRelative, styles.mar20Bt]}>
              <InfoFaqsAll
                faqs={this.props.faqs}
                pageName={PAGENAME.ALL_FAQS}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }
}

VisaAllFaqs.propsTypes = {
  visaParams: PropTypes.object.isRequired, // Tracking
  faqs: PropTypes.array.isRequired
};
