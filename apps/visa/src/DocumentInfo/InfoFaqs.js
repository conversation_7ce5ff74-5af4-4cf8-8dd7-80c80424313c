import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity
} from 'react-native';
import PropTypes from 'prop-types';
import RightArrowBlue from '@mmt/legacy-assets/src/RightArrowBlue.webp';
import styles from '../css/VisaMainCss';
import FaqsCollapsed from './FaqsCollapsed';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';

export const FREQUENTLY_ASKED_QUESTIONS = 'Frequently Asked Questions';
export const VIEW_ALL = 'VIEW ALL';

export default class InfoFaqs extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      activeCard: null,
      collapseActiveCard: true
    };
    this.top2Faq = this.props.faqs.slice(0, 2);
  }


  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      this.props.trackingPageName,
      clickEvent, this.props.visaParams, 'fromTypeOfVisa'
    );
  }

  tabbingFn = (index, nextState) => {
    this.setState({
      activeCard: index,
      collapseActiveCard: nextState
    });

    this.trackClickEvent('FAQ_clicked');
  }

  static navigationOptions = {
    header: null
  };

  showAllFaqs() {
    VisaNavigation.push(VISA_ROUTE_KEYS.VIEW_ALL_FAQ,{visaParams: this.props.visaParams, faqs: this.props.faqs});
    this.trackClickEvent('view_all_faq');
  }

  render() {
    return (
      <View style={styles.elevation1}>
        <View style={[styles.CardContent, styles.elevation1]}>
          <Text style={[styles.defaultGrey, styles.lightFont, styles.font22, styles.mar5Lt]}>
            {FREQUENTLY_ASKED_QUESTIONS}
          </Text>
        </View>
        <View>
          {
            this.top2Faq.map((item, index) => {
              const isActive = index === this.state.activeCard
              ? this.state.collapseActiveCard : false;
              const key = `${item}_${index}`;
              return (
                <FaqsCollapsed
                  key={key}
                  index={index}
                  faq={item}
                  isActive={isActive}
                  tabbingFn={this.tabbingFn}
                />
              );
            })
          }
        </View>
        <View style={[styles.whitebg, styles.pad20Top, styles.pad20Bt]}>
          <TouchableOpacity onPress={() => this.showAllFaqs()}>
            <View style={[styles.flexRow, styles.CenterText]}>
              <Text style={[styles.font12, styles.blueLink, styles.boldFont, styles.mar5Rt]}>
                {VIEW_ALL}
              </Text>
              <Image style={[styles.RightArrowBlue, styles.mar2Tp]} source={RightArrowBlue} />
            </View>
          </TouchableOpacity>
        </View>
      </View>

    );
  }
}

InfoFaqs.propTypes = {
  visaParams: PropTypes.object.isRequired, // Tracking
  faqs: PropTypes.array.isRequired,
  trackingPageName: PropTypes.string.isRequired
};
