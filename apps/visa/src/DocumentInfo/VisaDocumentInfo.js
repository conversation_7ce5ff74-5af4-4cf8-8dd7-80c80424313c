import React from 'react';
import {
  DeviceEventEmitter,
  View,
  ScrollView,
  Text,
  ActivityIndicator
} from 'react-native';
import PropTypes from 'prop-types';
import _, {cloneDeep, filter, isEmpty} from 'lodash';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import ViewState from '@mmt/legacy-commons/Common/constants/ViewState';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import styles from '../css/VisaMainCss';
import VisaDetailCard from './VisaDetailCard';
import StickyHeader from '../Common/StickyHeader';
import VisaDocuments from './VisaDocuments';
import VisaAppProcess from './VisaAppProcess';
import VisaImpInfo from './VisaImpInfo';
import VisaPriceBreakUp from '../Common/VisaPriceBreakUp';
import VisaBlackFooter from '../Common/VisaBlackFooter';
import InfoFaqs from './InfoFaqs';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import {initiateBooking} from '../VisaNetworkUtils';
import {
  PAGENAME,
  PageErrorData,
  DOCUMENT_TYPE,
  TRACKING_PAGENAME,
  VOUCHERS_TEXT,
  IDENTITY_TEXT,
  DOCUMENT_TYPE_STRING,
  YES,
  EVENTS
} from '../VisaConstants';
import {
  getDisplayDate,
  formatNavSubTitle,
  getUpdatedTravellerPriceBreakUp,
  scanDocuments,
  onDocumentUploadDone,
  onEticketAttachment,
  getTravellerText,
  isResponseValid
} from '../VisaUtils';
import VisaFullPageError from '../Error/VisaFullPageError';
import VisaLoginOverlay from '../Common/VisaLoginOverlay';
import {getVisaDocumentInfo} from '../VisaNetworkUtils';
import {appendAgeOfTravellers} from '../VisaUtils';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';

export default class VisaDocumentInfo extends BasePage {
  static navigationOptions = {
    header: null
  };

  constructor(props) {
    super(props, PAGENAME.DOCUMENT_REQUIRED);
    this.visaParams = {...this.props.visaParams, page: PAGENAME.DOCUMENT_REQUIRED};
    this.paxList = this.props.visaParams.paxList;
    this.state = {
      viewState: ViewState.LOADING,
      loaderText: 'Loading documents required..',
      overlay: false,
      passengers: [...this.visaParams.paxList],
      loginOverlay: false
    };
    this.userLoggedIn = false;
    this.docInfoResponse = [];
    this.eticketAvailablityStatus = true;
    this.hotelVoucherAvailStatus = true;
    this.previousPage = 'fromTypeOfVisa';
    this.isDocRequired = false;
    this.onLoginEventReceivedListener = null;
    this.onEticketAttachEventListener = null;
    this.onDocumentUploadDoneListener = null;
  }

  componentDidMount() {
    super.componentDidMount();
    this.fetchVisaDocumentInfo();
  }

  componentWillMount() {
    this.onLoginEventReceivedListener = DeviceEventEmitter.addListener(EVENTS.DOCUMENT_INFO.LOGIN_EVENT_DOC_INFO, this.onLoginEventReceived);
    this.onEticketAttachEventListener = DeviceEventEmitter.addListener(EVENTS.DOCUMENT_INFO.ETICKET_EVENT_DOC_INFO, this.onEticketAttachEvent);
    this.onDocumentUploadDoneListener = DeviceEventEmitter.addListener(EVENTS.DOCUMENT_INFO.UPLOAD_FINISH_EVENT_DOC_INFO, this.onDocumentUploadDone);
  }

  componentWillUnmount() {
    if(this.onLoginEventReceivedListener){
      this.onLoginEventReceivedListener.remove();
    }
    if(this.onEticketAttachEventListener){
      this.onEticketAttachEventListener.remove();
    }
    if(this.onDocumentUploadDoneListener){
      this.onDocumentUploadDoneListener.remove();
    }
  }

  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.DOCUMENT_REQUIRED,
      clickEvent, this.visaParams, this.previousPage
    );
  }

  onRefreshPressed = () => {
    this.setState({viewState: ViewState.LOADING});
    this.fetchVisaDocumentInfo();
  }

  onDocumentUploadDone = (data) => {
    onDocumentUploadDone(data, this.visaParams, PAGENAME.DOCUMENT_REQUIRED, TRACKING_PAGENAME.DOCUMENT_REQUIRED);
  };

  onEticketAttachEvent = (data) => {
    onEticketAttachment(data, this.visaParams, PAGENAME.DOCUMENT_REQUIRED);
  }

  onLoginEventReceived = (data) => {
    const loggedIn = {...data};
    if (loggedIn) {
      this.userLoggedIn = true;
      scanDocuments(this.visaParams, PAGENAME.DOCUMENT_REQUIRED);
    } else {
      showShortToast('Unable to login, Try again');
    }
  };

  onBackPressed = () => {
    VisaNavigation.pop();
    this.trackClickEvent('back_clicked');
  };

  updatePaxDocStatusFromResponse = () => {

    const documentList = [];
    const {documentCategoryList = []} = this.docInfoResponse
    documentCategoryList
      .filter(row => (row.category === IDENTITY_TEXT || row.category === VOUCHERS_TEXT))
      .forEach((item, index) => {
        documentList.push(...item.documents.filter(document => document.mandatory === true));
      });

    if (!isEmpty(documentList)) {
      this.isDocRequired = true
      const params = cloneDeep(this.visaParams);

      params.paxList.forEach((element) => {
        const tempElem = element;
        tempElem.docs = [];
        documentList.forEach((doc) => {
          tempElem.docs.push(cloneDeep({
            type: doc.type,
            uploaded: doc.availabilityStatus.toLowerCase() === YES
          }));
        });
      });

      const hotelVoucher = documentList.filter(row => row.type === 'HOTEL_VOUCHERS')
      if (hotelVoucher.length > 0) {
        this.hotelVoucherAvailStatus = hotelVoucher[0].availabilityStatus.toLowerCase() === YES;
      }

      this.visaParams = params;
      this.state.passengers = [...this.visaParams.paxList];
    }
  }

  async fetchVisaDocumentInfo() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET
      });
      return;
    }

    try {
      const visaId = this.visaParams['visaId'];
      const visaTypeId = this.visaParams['visaTypeId'];
      const paxAge = appendAgeOfTravellers(this.visaParams['paxList']);
      const response = await getVisaDocumentInfo(visaId, visaTypeId, paxAge);

      let isValidResponse = false;
      if (response) {
        this.docInfoResponse = response.visaDocInfoResponse;
        if (this.docInfoResponse) {
          this.updatePaxDocStatusFromResponse();
          isValidResponse = true;
          this.userLoggedIn = response.isLoggedIn;
          this.setState({
            viewState: ViewState.SHOW_DETAIL
          });

          VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
            TRACKING_PAGENAME.DOCUMENT_REQUIRED,
            this.previousPage, this.visaParams
          );
        }
      }

      if (!isValidResponse) {
        this.setState({
          viewState: ViewState.ERROR
        });
        VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
          TRACKING_PAGENAME.DOCUMENT_REQUIRED_ERROR,
          this.previousPage, this.visaParams
        );
        return;
      }
    } catch (e) {
      this.setState({
        viewState: ViewState.ERROR
      });
    }
  }

  breakUpPopup = () => {
    this.setState({overlay: !this.state.overlay});

    this.trackClickEvent('price_breakup');
  }

  getTravellerCount = () => this.paxList.length

  getUpdatedTravellerCount = () => this.state.passengers.length

  updatePax = (paxList) => {
    this.setState({
      passengers: paxList
    });
  }

  updateVisaPassengers = () => {
    const params = cloneDeep(this.visaParams);
    params.paxList = this.state.passengers;
    return params;
  }

  editVisaTravellers = () => {
    const params = this.updateVisaPassengers();
    VisaNavigation.push(VISA_ROUTE_KEYS,{
      updatePax: this.updatePax,
      visaParams: params,
      eticketAvailStatus: this.eticketAvailablityStatus,
      hotelVoucherAvailStatus: this.hotelVoucherAvailStatus
    });

    this.trackClickEvent('edit_travellers');
  }

  renderLoginOverlay = () => (
    <VisaLoginOverlay
      login={this.login}
      doNotlogin={this.doNotlogin}
    />
  )

  login = () => {
    this.setState({
      visaOverlay: false
    });
    VisaModule.loginUser({page: PAGENAME.DOCUMENT_REQUIRED});
  }

  doNotlogin = () => {
    this.setState({
      visaOverlay: false
    });
  }


  uploadDocuments = async () => {
    const params = this.updateVisaPassengers();
    this.visaParams = params;
    this.visaParams = {
      ...this.visaParams,
      isDocRequired: this.isDocRequired
    };
    if (this.userLoggedIn) {
      this.setState({
        viewState: ViewState.LOADING
      });
      const response = await initiateBooking(cloneDeep(this.visaParams));
      if (response) {
        this.visaParams = {...this.visaParams, visaBookingId: String(response.bookingId)};
        this.setState({
          viewState: ViewState.SHOW_DETAIL
        });
        scanDocuments(this.visaParams, PAGENAME.DOCUMENT_REQUIRED);
      } else {
        this.setState({
          viewState: ViewState.ERROR
        });
      }
    } else {
      this.setState({
        visaOverlay: true
      });
    }
    this.trackClickEvent('upload_now_clicked');
  }

  render() {
    return (
      <View style={styles.VisaMainContainer}>
        {this.state.viewState === ViewState.LOADING && this.renderProgressView()}
        {this.state.viewState === ViewState.NO_INTERNET && this.renderNoNetworkView()}
        {this.state.viewState === ViewState.ERROR && this.renderErrorView()}
        {this.state.viewState === ViewState.SHOW_DETAIL && this.renderContent()}
      </View>
    );
  }

  renderNoNetworkView = () => (
    <NoInternetView
      onRetry={this.onRefreshPressed}
    />
  );

  renderErrorView = () => (
    <VisaFullPageError
      title={PageErrorData.DOCUMENT_REQUIRED.title}
      subTitle={PageErrorData.DOCUMENT_REQUIRED.subTitle}
      suggestion={PageErrorData.DOCUMENT_REQUIRED.suggestion}
      onRefreshPressed={this.onRefreshPressed}
      renderStickyHeader={this.renderStickyHeader}
      onBackPressed={this.onBackPressed}
    />
  )

  renderProgressView = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {this.state.loaderText}
      </Text>
    </View>
  );

  getPerPaxAmount = (previousPaxCount) => {
    const perPaxTotalAmount = (this.docInfoResponse.totalAmount / previousPaxCount);
    const perPaxDiscountedAmount = (this.docInfoResponse.totalDiscAmount / previousPaxCount);

    return {totalAmount: perPaxTotalAmount, discAmount: perPaxDiscountedAmount};
  }

  footerSection = footerPrice => (
    <VisaBlackFooter
      footerPrice={footerPrice}
      onPriceBreakUpClicked={this.breakUpPopup}
      travellerCount={this.getUpdatedTravellerCount()}
      continueClicked={this.isDocRequired ? this.uploadDocuments : this.pushForm}
      nextButtonText={this.isDocRequired ? 'UPLOAD NOW' : 'CONTINUE'}
      isButtonClickable={!this.visaParams.infoOnly}
      priceBreakupArray={!isEmpty(this.docInfoResponse.chargesLabels)}
    />
  )

  renderFooterSection = () => {
    let footerPrice = {};
    if (this.getTravellerCount() === this.getUpdatedTravellerCount()) {
      footerPrice = {
        totalAmount: this.docInfoResponse.totalAmount,
        discountedAmount: this.docInfoResponse.totalDiscAmount
      };
    } else {
      const perPaxAmount = this.getPerPaxAmount(this.getTravellerCount());
      const updatedPaxCount = this.getUpdatedTravellerCount();

      footerPrice = {
        totalAmount: (perPaxAmount.totalAmount * updatedPaxCount),
        discountedAmount: (perPaxAmount.discAmount * updatedPaxCount)
      };
    }
    return this.footerSection(footerPrice);
  }

  priceBreakUp = chargesLabels => (
    <VisaPriceBreakUp
      chargesLabels={chargesLabels}
      breakUpPopup={this.breakUpPopup}
    />
  )

  renderPriceBreakUp = () => {
    const paxCount = this.getTravellerCount();
    const updatedPaxCount = this.getUpdatedTravellerCount();
    const chargesLabels = this.docInfoResponse.chargesLabels;

    if (paxCount === updatedPaxCount) {
      return this.priceBreakUp(chargesLabels);
    }
    return this.priceBreakUp(getUpdatedTravellerPriceBreakUp(chargesLabels, paxCount, updatedPaxCount));
  }

  getSubTitle() {
    return formatNavSubTitle(
      this.visaParams.visaType,
      getDisplayDate(this.visaParams.startDate),
      getTravellerText(this.visaParams.paxList), 'Departs'
    );
  }

  renderStickyHeader = () => (
    <StickyHeader
      title={`${this.visaParams.destCountry}`}
      subtitle={this.getSubTitle()}
      onBackPressed={this.onBackPressed}
    />
  )

  renderContent = () => (
    <View style={styles.flex1}>
      <ScrollView>
        <View>
          <VisaDetailCard
            docInfoParams={this.visaParams}
            passengers={this.state.passengers}
            editVisaTravellers={this.editVisaTravellers}
            onBackPressed={this.onBackPressed}
            key="visaDetailCard"
            infoOnly={this.visaParams.infoOnly}
          />
          <VisaDocuments
            docs={this.docInfoResponse.documentCategoryList}
            destInfoOnly={this.visaParams.infoOnly}
            key="visaDocuments"
          />

          <VisaAppProcess
            dest={this.visaParams.destCountry}
            processArray={this.docInfoResponse.processList}
            key="visaAppProcess"
          />

          {this.docInfoResponse.remarks &&
          <VisaImpInfo
            remarks={this.docInfoResponse.remarks}
            key="visaImpInfo"
          />}

          {this.docInfoResponse.faqs &&
          <View style={[styles.MakeRelative, styles.mar20Bt]}>
            <InfoFaqs
              visaParams={this.visaParams}
              faqs={this.docInfoResponse.faqs}
              trackingPageName={TRACKING_PAGENAME.DOCUMENT_REQUIRED}
              key="visaInfoFaqs"
            />
          </View>
          }
        </View>
      </ScrollView>

      {(this.state.overlay && !isEmpty(this.docInfoResponse.chargesLabels)) && this.renderPriceBreakUp()}

      {this.renderFooterSection()}
      {this.state.visaOverlay && this.renderLoginOverlay()}
    </View>
  )

  pushForm = async () => {
    const response = await initiateBooking(cloneDeep(this.visaParams));
    this.visaParams = {...this.visaParams, visaBookingId: String(response.bookingId)};
    if (response) {
      this.launchForm();
    } else {
      this.setState({viewState: ViewState.ERROR});
    }
  }

  launchForm = () => {
    const response = true;

    this.visaParams = {...this.visaParams, isDocRequired: this.isDocRequired};

    if (response) {
      VisaNavigation.push(VISA_ROUTE_KEYS.FORM_PRE_FILLED_NEW,{
        visaParams: this.visaParams
      })
    } else {
      VisaNavigation.push(VISA_ROUTE_KEYS.FORM_PREFILLED,{
        visaParams: this.visaParams
      })
    }
  }

}

VisaDocumentInfo.propTypes = {
  visaParams: PropTypes.object.isRequired
};
