import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';
import HTMLView from 'react-native-htmlview';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { isEmpty } from 'lodash';
import { isValidURL } from '@mmt/legacy-commons/Helpers/validationHelpers';
import { handleVisaSampleUrl } from '../../VisaUtils';

const DocumentDetails = ({ document, htmlViewStyle }) => {
    const { imageLink, name, description, sampleDocUrl } = document;
    return (
        <View style={styles.subCategory}>
            <Image source={{ uri: imageLink }} style={styles.iconImg} />
            <View style={{marginTop: 4}}>
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                    <Text style={styles.categoryHdr}>{name}</Text>
                    {(!isEmpty(sampleDocUrl) && isValidURL(sampleDocUrl)) ? <TouchableOpacity activeOpacity={0.6} onPress={() => handleVisaSampleUrl(sampleDocUrl, `SAMPLE_${name}`)}>
                        <View style={[AtomicCss.flexRow, AtomicCss.marginLeft10, AtomicCss.alignCenter]}>
                            <Image source={require('@mmt/legacy-assets/src/Visa/ic_full_screen.webp')} style={styles.viewSampleImg} />
                            <Text style={styles.viewSampleTxt}>View Sample</Text>
                        </View>
                    </TouchableOpacity> : null}
                </View>
                <HTMLView
                    style={[styles.htmlView, htmlViewStyle]}
                    stylesheet={htmlStyles}
                    value={description ? `<div>${description.replace(/(\r\n|\n|\r)/gm, "")}</div>` : ''}
                />
            </View>
        </View>
    );
};
const styles = StyleSheet.create({
    subCategory: {
        flexDirection: 'row',
        paddingLeft: 12
    },
    categoryHdr: {
        fontSize: 14,
        fontFamily: fonts.bold,
        fontWeight: 'bold',
        color: 'black'
    },
    iconImg: {
        width: 32,
        height: 32,
        marginRight: 12
    },
    viewSampleImg: {
        width: 12,
        height: 12,
        marginRight: 5,
        tintColor: colors.azure
    },
    viewSampleTxt: {
        fontSize: 12,
        fontFamily: fonts.regular,
        color: colors.azure
    },
    htmlView: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'flex-start',
        flexWrap: 'wrap',
        paddingTop: 6,
        marginRight: 30,
        overflow: 'hidden'
    }
});
const htmlStyles = StyleSheet.create({
    ul: {
        color: '#4a4a4a',
        fontFamily: fonts.regular,
        fontSize: 14
    },
    li: {
        lineHeight: 24
    },
    i: {
        color: '#4a4a4a',
        fontFamily: fonts.regular,
        fontSize: 14,
        fontStyle: 'italic'
    },
    b: {
        color: '#4a4a4a',
        fontFamily: fonts.bold,
        fontSize: 14
    },
    u: {
        color: '#4a4a4a',
        fontFamily: fonts.regular,
        fontSize: 14,
        textDecorationLine: 'underline'
    },
    span: {
        fontFamily: fonts.regular,
        fontSize: 14,
        color: '#4a4a4a'
    },
    strong: {
        color: '#4a4a4a',
        fontFamily: fonts.bold,
        fontSize: 14
    }
});

DocumentDetails.propTypes = {
    document: PropTypes.object.isRequired,
    htmlViewStyle: PropTypes.object
};
export default DocumentDetails;
