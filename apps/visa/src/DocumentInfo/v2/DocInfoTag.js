import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';

const DocInfoTag = (props) => {
    const { icon, text, boldText } = props;
    return (
        <View style={styles.docInfoTag}>
            <Image source={icon} style={styles.iconImage} />
            <Text style={styles.infoTxt}>{text}
                {boldText ? <Text style={styles.boldText}>{boldText}</Text>  : null}
            </Text>
        </View>
    )
}

const styles = StyleSheet.create({
    docInfoTag: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginBottom: 8
    },
    infoTxt: {
        flex: 1,
        fontSize: 14,
        lineHeight: 18,
        color: 'black',
        fontFamily: fonts.regular,
    },
    boldText: {
        fontFamily: fonts.bold
    },
    iconImage: {
        width: 40,
        height: 40,
        resizeMode: 'contain',
        marginRight: 20,
        marginTop: 5
    }
});

export default DocInfoTag;