import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';

import iconClose from '@mmt/legacy-assets/src/Visa/ic_close_black.webp';


const PageHeader = (props) => {
    return(
        <View style={styles.pageHeader}>
            <View style={styles.headerWrap}>
                <TouchableOpacity activeOpacity={0.6} onPress={props.onBack}>
                    <View style={styles.backArrowWrap}>
                        <Image style={[styles.backarrow]} source={iconClose} />
                    </View>
                </TouchableOpacity>
                <View>
                    <Text style={styles.hdrText}>List Of Documents</Text>
                </View>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    pageHeader: {
        padding: 15,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        ...getPlatformElevation(5),
        justifyContent: 'space-between',
    },
    headerWrap: {
        flexDirection: 'row',
        alignItems: 'center'
        
    },
    hdrText: {
        fontSize: 18,
        fontFamily: fonts.bold,
        color: '#4a4a4a'
    },
    hdrSubTxt: {
        fontSize: 12,
        fontFamily: fonts.regular,
        color: '#9b9b9b'
    },
    backarrow: {
        width: 14,
        height: 14,
        tintColor: '#9b9b9b'
    },
    backArrowWrap: {
        paddingLeft: 10,
        paddingRight: 20
    }
});

export default PageHeader;