import React from 'react';
import {
  View,
  Image,
  StyleSheet
} from 'react-native';
import HTMLView from 'react-native-htmlview';
import PropTypes from 'prop-types';
import blueDropIcondown from '@mmt/legacy-assets/src/blueDropIcondown.webp';
import blueDropIconUp from '@mmt/legacy-assets/src/blueDropIconUp.webp';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import styles from '../css/VisaMainCss';

export default class FaqsText extends React.Component {
    static navigationOptions = {
      header: null
    };

    render() {
      return (
        <View style={[styles.CollapsedTapContainer1]}>
          <View style={styles.CollapsedTapLeftSection}>
            <HTMLView
              style={styles.VisaFaqHTML}
              stylesheet={htmlStyles}
              addLineBreaks={false}
              value={this.props.question}
            />
          </View>
          <View style={[styles.CollapsedTapRightSection]}>
            <Image
              style={[styles.AlignSelfRight, styles.mar10Bt, styles.BlueArrow]}
              source={this.props.tabbing ? blueDropIconUp : blueDropIcondown}
            />
          </View>
        </View>

      );
    }
}

FaqsText.propTypes = {
  question: PropTypes.string.isRequired,
  tabbing: PropTypes.bool.isRequired
};

const htmlStyles = StyleSheet.create({
  p: {
    color: '#000000',
    lineHeight: 20,
    fontFamily: fonts.regular
  }
});
