import React from 'react';
import {View, Text, Image} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import uploadDoc from '@mmt/legacy-assets/src/add_meal.webp'; // TO DO: should be replaced with placeholder image
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';

export const SNAPSHOT = 'Visa application process';
export const VISA_APPLICATION_PROCESS = 'Here\'s what you need to do';

export default class VisaAppProcess extends React.Component {
  static navigationOptions = {
    header: null
  };

  renderProcessSteps = () => {
    const processes = [];
    this.props.processArray.forEach((item) => {
      processes.push(this.processStepByStepView(item));
    });
    return processes;
  }

  processStepByStepView = item => (
    <View key={item.step} style={[styles.flexRow, styles.mar20Bt]}>
      <Image
        style={[styles.StepImg,
        styles.mar15Rt,
        styles.bgWhite]}
        source={{uri: item.imageLink}}
        defaultSource={uploadDoc}
      />
      <View style={styles.flex1}>
        <View style={styles.flexRow}>
          <Text style={[styles.font14,
          styles.boldFont,
          styles.BlueText1,
          styles.bgTransparent]}
          >
            {item.headerText}
          </Text>
        </View>
        <Text style={[styles.font14,
        styles.grey,
        styles.bgTransparent,
        styles.regularFont]}
        >
          {item.description}
        </Text>
      </View>
    </View>
  );

  render() {
    return (
      <View style={[styles.whitebg, styles.mar20Bt]}>
        <LinearGradient start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} colors={['#ccf6ff', '#cbe5f9']} style={{}} >
          <View style={styles.VisaSection}>
            <Text style={[styles.font22,
            styles.lightFont,
            styles.defaultGrey,
            styles.DarkBlue,
            styles.bgTransparent]}
            >
              {SNAPSHOT}
            </Text>
            <Text style={[styles.font14,
            styles.boldFont,
            styles.defaultGrey,
            styles.DarkBlue,
            styles.mar15Bt,
            styles.bgTransparent]}
            >
              {VISA_APPLICATION_PROCESS}
            </Text>
            <View style={[styles.VisaSectionContainer, styles.whitebg]}>
              <View style={styles.padd20}>
                <View>
                  {
                    this.renderProcessSteps()
                  }
                </View>
              </View>
            </View>
          </View>
        </LinearGradient>
      </View>
    );
  }
}

VisaAppProcess.propTypes = {
  dest: PropTypes.string.isRequired,
  processArray: PropTypes.array.isRequired
};
