import React from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import icClearBlack from '@mmt/legacy-assets/src/ic_clear_black.webp';
import {
  ADULT_AGE_EXTENSION,
  MONTH_AGE_EXTENSION,
  AGE_EXT_YEARS,
  AGE_EXT_MONTHS,
  EditTraveller,
  DATE_FORMAT_DISPLAY
} from '../VisaConstants';
import VisaCheckBox from '../Common/VisaCheckBox';
import styles from '../css/VisaMainCss';
import {getFormattedDate} from '../VisaUtils';

export default class AddTraveller extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      infant: false
    };
    this.ageExtension = ADULT_AGE_EXTENSION;
  }

  shouldComponentUpdate(nextProps, nextState) {
    return !(this.props === nextProps) || !(this.state === nextState);
  }

  onCheckBoxChange = () => {
    const oldState = this.state.infant;
    if (oldState) {
      this.ageExtension = ADULT_AGE_EXTENSION;
    } else {
      this.ageExtension = MONTH_AGE_EXTENSION;
    }
    this.setState({
      infant: !this.state.infant
    });
  }

  infantCheckBoxView = () => (
    <VisaCheckBox
      title={EditTraveller.INFANT_CHECKBOX_TITLE}
      checkBoxVal={this.state.infant}
      onCheckBoxChange={this.onCheckBoxChange}
    />
  );

  render() {
    return (
      <View style={styles.EditTravellerOverlayMain}>
        <View style={[styles.whitebg]}>
          <View style={styles.TravelleWrap}>
            <View style={[styles.flexRow, styles.spaceBetween, styles.mar20Bt]}>
              <Text style={[styles.font12, styles.boldFont, styles.lightGrey, styles.bgTransparent]}>
                {EditTraveller.ADD_NUMBER_OF_TRAVELLERS}
              </Text>
              <TouchableWithoutFeedback onPress={this.props.dismissAddTravellerPopup}>
                <Image style={{width: 24, height: 24}} source={icClearBlack} />
              </TouchableWithoutFeedback>
            </View>
            {this.infantCheckBoxView()}
            <View style={styles.mar20Bt}>
              <View style={[styles.flexRow, styles.spaceBetween, styles.mar10Bt]}>
                <View style={{marginTop: 15}}>
                  <Text style={[styles.font18, styles.boldFont, styles.defaultGrey, styles.mar3Bt]}>
                  Age of Traveller
                  </Text>
                  <Text style={[styles.font12, styles.lightGrey, styles.bgTransparent]}>
                  As on {getFormattedDate(new Date().getTime(), DATE_FORMAT_DISPLAY)}
                  </Text>
                </View>
                <View style={styles.EditTravellerInputWrap}>
                  <TextInput
                    underlineColorAndroid="transparent"
                    keyboardType="numeric"
                    maxLength={3}
                    style={styles.EditTravellerInput}
                    onChangeText={age => this.props.setPaxAge(age, this.ageExtension)}
                  />
                  <Text style={styles.EditInputText}>{this.ageExtension === ADULT_AGE_EXTENSION ? AGE_EXT_YEARS : AGE_EXT_MONTHS}</Text>
                </View>
              </View>
            </View>
            <View style={[styles.flexRow, styles.spaceBetween]}>
              <Text style={[styles.font12, styles.lightGrey, styles.bgTransparent]}>
                {EditTraveller.ADD_TRAVELLER_POP_UP}
              </Text>
              <View>
                <TouchableOpacity onPress={this.props.addTraveller}>

                  <LinearGradient
                    start={{x: 1.0, y: 0.0}}
                    end={{x: 0.0, y: 1.0}}
                    colors={['#065af3', '#53b2fe']}
                    style={[styles.AddBtn, styles.bgTransparent]}
                  >
                    <Text style={[styles.font13, styles.WhiteText, styles.semiBold, styles.bgTransparent]}>
                      {EditTraveller.ADD_ACTION}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>

              </View>
            </View>
          </View>
        </View>
      </View>
    );
  }
}

AddTraveller.propTypes = {
  addTraveller: PropTypes.func.isRequired,
  dismissAddTravellerPopup: PropTypes.func.isRequired,
  setPaxAge: PropTypes.func.isRequired
};
