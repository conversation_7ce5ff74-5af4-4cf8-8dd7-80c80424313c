import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import HTMLView from 'react-native-htmlview';
import PropTypes from 'prop-types';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import styles from '../css/VisaMainCss';

export const VISADOC_IMPORTANT_INFO = 'Important Information';

export default class ImportantInfo extends React.Component {
  static navigationOptions = {
    header: null
  };

  render() {
    return (
      <View style={[styles.whitebg, styles.mar20Bt]}>
        <View style={[styles.CardContent, styles.elevation1]}>
          <Text style={[styles.defaultGrey, styles.lightFont, styles.font22, styles.mar5Lt]}>
            {VISADOC_IMPORTANT_INFO}
          </Text>
        </View>
        <View style={{
                      paddingTop: 5,
                      paddingBottom: 5,
                      paddingLeft: 30,
                      paddingRight: 30
                    }}
        >
          <HTMLView
            style={styles.VisaFaqHTML}
            stylesheet={htmlStyles}
            value={this.props.remarks}
          />
        </View>
      </View>
    );
  }
}

ImportantInfo.propTypes = {
  remarks: PropTypes.string.isRequired
};

const htmlStyles = StyleSheet.create({
  ul: {
    color: '#4a4a4a',
    fontFamily: fonts.regular
  },
  span: {
    color: '#eb2026'
  },
  li: {
    lineHeight: 20
  }
});
