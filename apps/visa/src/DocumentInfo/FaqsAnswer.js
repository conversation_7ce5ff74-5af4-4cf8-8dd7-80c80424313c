import React from 'react';
import {
  View,
  StyleSheet
} from 'react-native';
import HTMLView from 'react-native-htmlview';
import PropTypes from 'prop-types';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import styles from '../css/VisaMainCss';

export default class FaqsAnswer extends React.Component {
  static navigationOptions = {
    header: null
  };

  render() {
    return (
      <View style={[styles.CollapsedTapDtlsContainer1]}>
        <HTMLView
          style={styles.VisaFaqHTML}
          stylesheet={htmlStyles}
          addLineBreaks={false}
          value={this.props.answer}
        />
      </View>
    );
  }
}

FaqsAnswer.propTypes = {
  answer: PropTypes.string.isRequired
};

const htmlStyles = StyleSheet.create({
  p: {
    fontSize: 13,
    lineHeight: 20,
    color: '#4a4a4a',
    fontFamily: fonts.regular
  }
});
