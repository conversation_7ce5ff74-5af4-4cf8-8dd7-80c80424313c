import React from 'react';
import {
  Platform,
  BackHandler,
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  DeviceEventEmitter,
  TouchableHighlight
} from 'react-native';
import { navigation } from '@mmt/navigation';
import LinearGradient from 'react-native-linear-gradient';
import { Actions, ActionConst } from 'react-native-router-flux';
import { capitalize, cloneDeep, intersection, isEmpty, isEqual, omit, reject } from 'lodash';
import PropTypes from 'prop-types';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { PAGENAME, PageErrorData, EVENTS, TRACKING_PAGENAME } from '../VisaConstants';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import PageHeader from './components/PageHeader';
import TabView from './components/TabView';
import Panel from './components/Panel';
import VisaMainCss from '../css/VisaMainCss';
import ViewState from '@mmt/legacy-commons/Common/constants/ViewState';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import {
  getHeaders,
  getVisaBookingDetailsV2,
  getMultipleApplicationFormResponse,
  submitMultipleApplicationForm,
  removeDocument,
  replicateDocuments,
} from '../VisaNetworkUtils';
import VisaFullPageError from '../Error/VisaFullPageError';
import AddDoc from './components/AddDocView';
import RemoveDoc from './components/RemoveDocView';
import {
  scanDocuments,
  shouldAttach,
  validateDynamicFormV2,
  getFormResponse,
  validateDynamicForm,
  makeURLUnique,
  getPaxTypeByStatus,
} from '../VisaUtils';
import InfoTag from '../Common/InfoTag';
import { showShortToast, showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { getDownloadedImage } from '../VisaImageDownloader';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import AddDocumentsNotLoggedIn from './AddDocumentsNotLoggedIn';
import ReplicateCheckbox from './components/ReplicateCheckbox';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';
import { logPdtActionEvent } from '../Analytics/Pdt/PdtTracker';
import { PDT_EVENT_NAME, PDT_PAGENAME } from '../Analytics/Pdt/PdtConstants';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import InfoMessage from '../InfoMessage';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const REMOVE = 'remove';
const ATTACH = 'attach';
const TAB_DATA_OBJ = {
  name: '',
  docsUploaded: 0,
  maxDocs: 0,
};

class AddDocuments extends BasePage {

  constructor(props) {
    super(props, PAGENAME.ADD_DOCUMENTS);
    if (this.props.deeplink && this.props.deeplink.toLocaleLowerCase().includes('addvisadocuments')) {
      this.previousPage = this.props.fromPage || '';
      this.visaParams = { page: PAGENAME.ADD_DOCUMENTS, fromMIMA: true };
    } else {
      this.previousPage = this.props.visaParams.page || '';
      this.visaParams = {
        ...this.props.visaParams,
        page: PAGENAME.ADD_DOCUMENTS,
        fromMIMA: this.props.fromMIMA
      };
    }
    // this.previousPage = this.props.visaParams.page || '';
    // this.visaParams = { ...this.props.visaParams, page: TRACKING_PAGENAME.ADD_DOCUMENTS_FILLFORM };
    this.bookingResponse = null;
    this.headers = null; // this will be used to download images and other docs to show
    this.formPanelRef = null;
    this.documentCategoryList = null;
    this.state = {
      viewState: ViewState.LOADING,
      loaderText: 'Please wait...',
      selectedTabIndex: this.getDefaultSelectedTabIndex(),
      tabsData: [], // tabs to show
      documents: [], // documents of a tab
      forms: [], // form of a tab
      validateForms: [], // true/false state for showing form errors tab wise
      submittingForms: false,
    };
    this.onLoginEventReceivedListener = null;
    this.onDocumentScanningDoneListener = null;
    this.oonDocumentScannedListener = null;
    this.onDocumentAttachDoneListener = null;
  }
  getDefaultSelectedTabIndex = () => {
    const { paxId } = this.visaParams;
    if (paxId) {
      return Number(paxId) - 1;
    }
    return 0;
  };
  componentDidMount() {
    super.componentDidMount();
    this.fetchData();
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
      TRACKING_PAGENAME.ADD_DOCUMENTS_FILLFORM,
      this.previousPage,
      this.visaParams,
    );
  }
  tagClickEvents = (eventName) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.ADD_DOCUMENTS_FILLFORM,
      eventName,
      this.visaParams,
      this.previousPage,
    );
  };
  onExpand = (category, expanded) => {
    let eventName = `${expanded ? 'expand' : 'collapse'}_${category}`;
    this.tagClickEvents(eventName);
  };
  componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackPressed);
    this.onLoginEventReceivedListener = DeviceEventEmitter.addListener(
      EVENTS.DOCUMENT_INFO.LOGIN_EVENT_DOC_INFO,
      this.onLoginEventReceived,
    );
    this.onDocumentScanningDoneListener = DeviceEventEmitter.addListener(
      EVENTS.ADD_DOCUMENTS.DOCUMENT_SCAN_FINISH_EVENT,
      this.onDocumentScanningDone,
    );
    this.oonDocumentScannedListener =
      DeviceEventEmitter.addListener(
        EVENTS.ADD_DOCUMENTS.DOCUMENT_SCANNED_EVENT,
        this.onDocumentScanned,
      );
    this.onDocumentAttachDoneListener = DeviceEventEmitter.addListener(
      EVENTS.ADD_DOCUMENTS.DOCUMENT_ATTACHED_EVENT,
      this.onDocumentAttachDone,
    );
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackPressed);
    if (this.onLoginEventReceivedListener) {
      this.onLoginEventReceivedListener.remove();
    }
    if (this.onDocumentScanningDoneListener) {
      this.onDocumentScanningDoneListener.remove();
    }
    if (this.oonDocumentScannedListener) {
      this.oonDocumentScannedListener.remove();
    }
    if (this.onDocumentAttachDoneListener) {
      this.onDocumentAttachDoneListener.remove();
    }
  }
  onLoginEventReceived = () => {
    this.onRefreshPressed();
  };
  onDocumentScanned = (data) => {
    const { documents, selectedTabIndex } = this.state;
    const doc = documents[selectedTabIndex][data.type];
    this.tagClickEvents(`upload_${doc.name}_success`);
    this.onRefreshPressed();
  };
  onDocumentScanningDone = (data) => {
    this.onRefreshPressed();
  };
  onDocumentAttachDone = (data) => {
    const { documents, selectedTabIndex } = this.state;
    const doc = documents[selectedTabIndex][data.type];
    if (data.op === ATTACH) {
      if (data.status === true) {
        this.tagClickEvents(`upload_${doc.name}_success`);
        showShortToast('Document attached');
        this.onRefreshPressed();
      } else {
        this.tagClickEvents(`upload_${doc.name}_failed`);
        showShortToast('Unable to attach document, try again');
        this.setState({ viewState: ViewState.SHOW_DETAIL });
      }
    } else if (data.op === REMOVE) {
      if (data.status === true) {
        this.tagClickEvents(`delete_${doc.name}_success`);
        showShortToast('Document removed');
        this.onRefreshPressed();
      } else {
        this.tagClickEvents(`delete_${doc.name}_failed`);
        showShortToast('Unable to remove document, try again');
        this.setState({ viewState: ViewState.SHOW_DETAIL });
      }
    }
  };

  onRefreshPressed = () => {
    this.setState(
      {
        viewState: ViewState.LOADING,
        loaderText: 'Please wait...',
      },
      () => {
        this.fetchData();
      },
    );
  };
  setContentData = async () => {
    const tabsData = this.createInitialTabsData();
    const documents = this.createInitialDocumentsData(tabsData.length);
    let forms = this.state.forms.length
      ? this.state.forms
      : await this.createInitialFormData(tabsData.length);
    this.setState({
      viewState: ViewState.SHOW_DETAIL,
      tabsData,
      documents,
      forms,
    });
  };
  refreshVisaParams = () => {
    const { visaDetail, passengers, bookingId, payRefId } = this.bookingResponse;
    const params = cloneDeep(this.visaParams);

    params.visaBookingId = String(bookingId);
    params.payRefId = payRefId;

    params.startDate = visaDetail.dateOfEntry;
    params.endDate = visaDetail.dateOfExit;
    params.visaReceivingDate = visaDetail.visaReceivingDate;
    params.destCountry = visaDetail.countryName;
    params.countryCode = visaDetail.countryCode
    params.visaId = String(visaDetail.type);

    params.paxList = [];
    passengers.forEach((elem) => {
      const pax = {
        name: elem.name,
        age: String(elem.age),
        id: String(elem.paxIndex),
        status: getPaxTypeByStatus(elem.status),
        email: elem.email,
        phoneNumber: elem.phoneNumber,
      };
      pax.docs = [];
      if (elem.docStatusList) {
        elem.docStatusList.forEach((d) => {
          pax.docs.push({ type: d.type, uploaded: d.uploaded });
        });
      }
      params.paxList.push(pax);
    });
    this.visaParams = params;
  };
  async fetchData() {
    this.headers = cloneDeep(await getHeaders());
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET,
      });
      return;
    }
    const isLoggedIn = await isUserLoggedIn();
    if (!isLoggedIn) {
      this.setState({
        viewState: ViewState.NOT_LOGGED_IN,
      });
      return;
    }
    try {
      const { payRefId, externalRefId } = this.props;
      this.bookingResponse = await getVisaBookingDetailsV2(payRefId, externalRefId);
      if (this.bookingResponse) {
        this.refreshVisaParams();
        this.visaParams.visaBookingId = `${this.bookingResponse.bookingId}`;
        this.onBookingResponse();
      } else {
        this.onError();
      }
    } catch (e) {
      this.onError();
    }
  }
  onBookingResponse = async () => {
      this.documentCategoryList = new Map();
      // Since documents of all pax/passengers will be same, therefore taking first passenger docs catgory wise
      const pax = this.bookingResponse.passengers[0];
      if (pax.docStatusList && pax.docStatusList.length) {
        pax.docStatusList.forEach((doc) => {
          let list = this.documentCategoryList.get(doc.category);
          if (isEmpty(list)) {
            list = [];
            this.documentCategoryList.set(doc.category, list);
          }
          list.push(doc);
        });
      }
    this.setContentData();
  };

  getUpdatedDocList = (paxIndex, category) => {
    const pax = this.bookingResponse.passengers[paxIndex];
    let list = [];
    if (pax.docStatusList && pax.docStatusList.length) {
      
      pax.docStatusList.forEach((doc) => {
        if(doc.category && doc.category.toUpperCase() === category.toUpperCase()){
          list.push(doc);
        }
      });
    }
    return list;
  }


  onError = () => {
    this.setState({
      viewState: ViewState.ERROR,
    });
  };
  onBackPressed = () => {
    this.tagClickEvents('back_clicked');
    logPdtActionEvent(PDT_EVENT_NAME.APP_BACK_CLICK, { pageName: PDT_PAGENAME.UPLOAD_VISA });
    if (this.props.fromMIMA) {
      // if user is coming from MIMA then just close the screen
      const somethingPopped = VisaNavigation.canGoBack();
      if (!somethingPopped) {
        if (Platform.OS === 'ios') {
          ViewControllerModule.popViewController(1);
        } else {
          BackHandler.exitApp();
        }
      } else {
        VisaNavigation.pop();
      }
    } else {
      // If user is not coming from MIMA then close all screens in b/w and refresh landing page
      VisaNavigation.navigate(VISA_ROUTE_KEYS.LANDING_NEW)
    }
    return true;
  };

  createInitialTabsData = () => {
    const maxDocs = this.countMandatoryDocs();
    this.visaParams = {
      ...this.visaParams,
      isDocRequired: maxDocs > 0,
    };
    const tabs = [];
    this.bookingResponse.passengers.forEach((passenger) => {
      const tab = cloneDeep(TAB_DATA_OBJ);
      tab.maxDocs = maxDocs;
      tab.docsUploaded = passenger.documents ? passenger.documents.length : 0;
      tab.name = passenger.name;
      tabs.push(tab);
    });
    return tabs;
  };
  getUploadedDocument = (index, type) => {
    if (
      !this.bookingResponse ||
      !this.bookingResponse.passengers ||
      index < 0 ||
      index >= this.bookingResponse.passengers.length ||
      !this.bookingResponse.passengers[index].documents
    ) {
      return null;
    }
    const { documents } = this.bookingResponse.passengers[index];
    for (const doc of documents) {
      if (type === doc.type) {
        return doc;
      }
    }
    return null;
  };

  createInitialDocumentsData = (maxDocs) => {
    const documentsObj = {};
    this.documentCategoryList.forEach((docList) => {
      docList.forEach((doc) => {
        documentsObj[doc.type] = { ...doc };
      });
    });

    const docTypes = Object.keys(documentsObj);
    const list = [];
    for (let index = 0; index < maxDocs; index++) {
      const documents = cloneDeep(documentsObj);
      for (const type of docTypes) {
        const passengerUploadedDoc = this.getUploadedDocument(index, type);
        const uploadedDoc = {
          docUrl: passengerUploadedDoc ? makeURLUnique(passengerUploadedDoc.url) : undefined,
          uploaded: passengerUploadedDoc ? true : false,
          index: passengerUploadedDoc ? passengerUploadedDoc.index : undefined,
        };
        documents[type] = { ...documents[type], ...uploadedDoc };
      }
      list.push(documents);
    }
    return list;
  };
  createInitialFormData = async (maxForms) => {
    const forms = [];
    for (let index = 0; index < maxForms; index++) {
      const response = await this.downloadForm(index);
      forms.push({ ...response });
    }
    return forms;
  };
  downloadForm = async (tabIndex) => {
    const { payRefId, externalRefId } = this.props;
    const response = await getMultipleApplicationFormResponse(
      payRefId,
      externalRefId,
      this.getPassenger(tabIndex).paxIndex,
    );
    let isValid = false;
    let visaForms = [];
    let commonFormKeys = [];
    if (response && response.forms) {
      isValid = this.isValidForms(response.forms);
      if (response.forms.length > 1) {
        //if there are  mutiple forms then remove common fields from forms which are answered in first form
        commonFormKeys = this.getCommonFormKeys(response.forms);
        visaForms = this.removeCommonFormKeys(response.forms, commonFormKeys);
      } else {
        visaForms = response.forms;
      }
    }
    return {
      isValid,
      visaForms,
      commonFormKeys,
    };
  };
  getCommonFormKeys = (forms) => {
    let index = 0;
    let commonKeys = Object.keys(forms[index].formSchema.fields);
    for (index = 1; index < forms.length; index++) {
      const nextKeys = Object.keys(forms[index].formSchema.fields);
      commonKeys = intersection(commonKeys, nextKeys);
      // it means next form is a sub set of previous form
      forms[index].formSchema.isSubset = isEqual(nextKeys, commonKeys);
    }
    return commonKeys;
  };
  removeCommonFormKeys = (forms, commonFormKeys) => {
    for (let index = 1; index < forms.length; index++) {
      forms[index].formSchema.fields = omit(forms[index].formSchema.fields, commonFormKeys);
      forms[index].formSchema.fieldsOrder = reject(forms[index].formSchema.fieldsOrder, (key) =>
        commonFormKeys.includes(key),
      );
    }
    return forms;
  };
  isValidForms = (forms) => {
    if (!forms || !forms.length) {
      return false;
    }
    for (const form of forms) {
      if (!validateDynamicFormV2(form.formSchema.fields, form.formSchema.fieldValues)) {
        return false;
      }
    }
    return true;
  };
  countMandatoryDocs = () => {
    let maxDocs = 0;
    this.documentCategoryList.forEach((docList) => {
      docList.forEach((doc) => {
        if (doc.mandatory) {
          maxDocs += 1;
        }
      });
    });
    return maxDocs;
  };

  setSelectedTab = (index) => {
    if (this.tabViewRef) {
      this.tabViewRef.scrollToTab(index);
    }
  };
  onTabClicked = (index) => {
    if (this.tabViewRef && this.tabViewRef.getSelectedTabIndex() === index) {
      // avoid loading data on current tab
      return;
    }
    this.tagClickEvents(`switch_traveller_${index + 1}`);
    this.setState({ selectedTabIndex: index }, () => {
      this.setSelectedTab(index);
    });
  };
  /**
   *
   * @param {*} tabIndex Index of a tab to retreive docs
   * @param {*} category If provided only category docs will be check otherwise all docs of a tab will be checked
   */
  isAllDocsUploaded = (tabIndex, category) => {
    const { documents } = this.state;
    const savedDocuments = documents[tabIndex];
    if (category) {
      for (const d of this.documentCategoryList.get(category)) {
        if (!savedDocuments[d.type].uploaded) {
          return false;
        }
      }
    } else {
      for (const d of Object.values(savedDocuments)) {
        if (!d.uploaded) {
          return false;
        }
      }
    }
    return true;
  };

  isAllDocsUploadedCount = (tabIndex, category) => {
    const { documents } = this.state;
    const savedDocuments = documents[tabIndex];
    var count = 0;
    if (category) {
      for (const d of this.documentCategoryList.get(category)) {
        if (savedDocuments[d.type].uploaded) {
          count = count + 1;
        }
      }
    } else {
      for (const d of Object.values(savedDocuments)) {
        if (d.uploaded) {
          count = count;
        }
      }
    }
    return count;
  };

  isFormDownloaded = (tabIndex) => {
    return this.state.forms[tabIndex].visaForms.length > 0;
  };

  isPassengerDocsUploaded = (travellerData) => {
    if (travellerData.docsUploaded === travellerData.maxDocs) {
      return 'COMPLETE';
    }
    else if (travellerData.docsUploaded === 0) {
      return '';
    }
    else if (travellerData.docsUploaded < travellerData.maxDocs) {
      return 'INCOMPLETE';
    }
    else {
      return '';
    }
  };

  alldocsUploaded = () => {
    var allDocsUploaded = true;
    Object.values(this.state.documents).forEach((doc) => {
      Object.values(doc).forEach((d) => {
        if (d.docUrl == undefined) {
          allDocsUploaded = false;
        }
      });
    });
    if (allDocsUploaded) {
      return "success";
    }
    else {
      return "info";
    }
  };

  getCategoryTitle = (category, docsList) => {
    let categoryName = '';
    if (docsList) {
      docsList.forEach((doc) => {  
        if(doc.category && doc.category.toUpperCase() === category.toUpperCase()){      
          categoryName = doc.categoryName;
        }
      }); 
    }
    if(isEmpty(categoryName)) {
      switch (category.toUpperCase()) {
        case "PASSPORT":
          return "Passport Details";
        case "PHOTOGRAPH":
        case "IDENTITY":
          return "Add Photograph";
        default:
          return category;
      }
    }
    return categoryName;
  }

  getCategoryDescription = (category, isSuccess, docsList) => {
    let categoryName = '';
    if (docsList) {
      docsList.forEach((doc) => {  
        if(doc.category &&  doc.category.toUpperCase() === category.toUpperCase()){      
          if (isSuccess){
            categoryName =  doc.categoryDescriptionAfter;
          }
        else{
          categoryName =  doc.categoryDescriptionBefore;
        }
        }
      }); 
    }
    if(isEmpty(categoryName)) {
    switch (category.toUpperCase()) {
      case "PASSPORT":
        if (isSuccess)
          return "Passport photos successfully uploaded";
        else
          return "Upload front and back soft copy of your passport";
      case "PHOTOGRAPH":
      case "IDENTITY":
        if (isSuccess)
          return "Photograph successfully uploaded";
        else
          return "Take a photo or choose from gallery in JPEG, JPG or PNG format";
      default:
        if (isSuccess)
          return "Successfully uploaded";
        else
          return "";
    }
  }
  return categoryName;
  }

  render() {
    switch (this.state.viewState) {
      case ViewState.NO_INTERNET:
        return this.renderNoNetworkView();
      case ViewState.NOT_LOGGED_IN:
        return this.renderNotLoggedInView();
      case ViewState.ERROR:
        return this.renderErrorView();
      case ViewState.SHOW_DETAIL:
        return this.renderContent();
      default:
        return this.renderProgressView();
    }
  }
  renderNoNetworkView = () => <NoInternetView onRetry={this.onRefreshPressed} />;

  renderErrorView = () => (
    <VisaFullPageError
      title={PageErrorData.DOCUMENT_REQUIRED.title}
      subTitle={PageErrorData.DOCUMENT_REQUIRED.subTitle}
      suggestion={PageErrorData.DOCUMENT_REQUIRED.suggestion}
      onRefreshPressed={this.onRefreshPressed()}
      renderStickyHeader={() => null}
      onBackPressed={this.onBackPressed}
    />
  );

  renderProgressView = () => (
    <View style={VisaMainCss.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={VisaMainCss.indicator} size="large" />
      <Text style={VisaMainCss.darkText}> {this.state.loaderText}</Text>
    </View>
  );
  renderNotLoggedInView = () => {
    return (
      <AddDocumentsNotLoggedIn
        onLoginClick={() => {
          this.tagClickEvents('login_now');
          this.setState({
            viewState: ViewState.LOADING,
            loaderText: 'Loading...',
          });
          VisaModule.loginUser({ page: PAGENAME.DOCUMENT_REQUIRED });
        }}
        visaParams={this.visaParams}
      />
    );
  };
  renderContent = () => {
    const { selectedTabIndex } = this.state;
    return (
      <View style={styles.pageWrap}>
        <PageHeader onBack={this.onBackPressed}>
        </PageHeader>
        <ScrollView>
          {this.renderDocuments(selectedTabIndex)}
          <View style={styles.pageFooter} key="submitButton">
            {this.state.submittingForms ? (
              <ActivityIndicator size="large" color="#065af3" />
            ) : (
              <TouchableOpacity onPress={this.submitApplication} activeOpacity={0.8}>
                <LinearGradient
                  start={{ x: 1.0, y: 0.0 }}
                  end={{ x: 0.0, y: 1.0 }}
                  colors={['#065af3', '#53b2fe']}
                  style={styles.footerBtnWrap}
                >
                  <View style={[styles.footerBtn]}>
                    <Text style={styles.footerBtnTxt}>SUBMIT APPLICATION</Text>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </View>
    );
  };




  renderDocuments = (tabIndex) => {
    const { documents, forms } = this.state;
    const savedDocs = documents[tabIndex];
    return (
      <View style={styles.mainContainer}>
        <ScrollView
          vertical={true}
          showsVerticalScrollIndicator={false}
          style={styles.contentWrapper} >
          <View style={{ paddingBottom: 16 }}>

            {this.state.tabsData.map((travellerData, index) => {
              return (
                <View style={styles.cardWrapper}>
                  <View style={styles.headerWrapper}>
                    <Text style={[AtomicCss.font16, AtomicCss.flex1, AtomicCss.blackText, AtomicCss.blackFont]}>{travellerData.name}</Text>
                    {
                      <Text style={[styles.font12, styles.blackFont,
                      this.isPassengerDocsUploaded(travellerData) === 'COMPLETE' ? styles.successText : styles.warningText
                      ]}>{this.isPassengerDocsUploaded(travellerData)}</Text>
                    }
                  </View>
                  <View style={styles.documentWrapper}>
                    {Array.from(this.documentCategoryList).sort((a, b) => a[1].order - b[1].order)
                      .map(([category, docList]) =>
                      (  <Panel
                          key={category}
                          title={this.getCategoryTitle(category,docList)}
                          category={category}
                          description={this.getCategoryDescription(category, this.isAllDocsUploaded(index, category),docList)}
                          isRequired={!this.isAllDocsUploaded(index, category)}
                          onExpand={(expanded) => this.onExpand(category, expanded)}
                          isSubmit={this.isAllDocsUploaded(index, category)}
                          isInfo={!this.isAllDocsUploaded(index, category) && this.isAllDocsUploadedCount(index, category) > 0}
                          params={this.getUpdatedVisaParams(index)}
                          docList={this.getUpdatedDocList(index, category)}
                          savedDocs={documents[index]}
                          tabIndex={index}
                          payRefId={this.props.payRefId}
                          externalRefId={this.props.externalRefId}
                          name={travellerData.name}
                          RefreshPressed={() => this.onRefreshPressed}
                        >
                          {docList
                            .map((item, index) => {
                              const doc = savedDocs[item.type];
                              return (
                                <>
                                  <View key={doc.type} style={{ marginBottom: index !== docList.length - 1 ? 16 : 0 }}>
                                    {doc.uploaded ? (
                                      <RemoveDoc
                                        headers={this.headers}
                                        document={doc}
                                        base64Icon={getDownloadedImage(doc.docUrl)}
                                        onRemoveClick={() => this.onRemoveDocumentClicked(tabIndex, doc.type)}
                                      />
                                    ) : (
                                      <AddDoc
                                        document={doc}
                                        onAddClick={() => this.onAddDocumentClicked(tabIndex, doc.type)}
                                        onAttachClick={() => this.onAttachDocumentClicked(tabIndex, doc.type)}
                                      />
                                    )}
                                  </View>
                                  {this.renderReplicateCheckbox(doc, tabIndex)}
                                </>
                              );
                            })}
                        </Panel>
                      ))}
                    <Panel
                      ref={(ref) => (this.formPanelRef = ref)}
                      title="Fill Visa Application Form"
                      isRequired={!forms[tabIndex].isValid}
                      launchForm={() => this.launchForm(index)}
                      type="FORM"
                    >
                      {this.renderFillVisaFormCTA(tabIndex)}
                    </Panel>
                  </View>
                </View>
              )
            })
            }
          </View>
        </ScrollView>
        <View>
          {this.alldocsUploaded() && <InfoMessage message={this.alldocsUploaded() == "success" ? "Hurray! All documents have been successfully uploaded" : "Please fill details of all passengers to proceed forward"} type={this.alldocsUploaded()} />}
          <View style={styles.btnWrapper}>
          </View>
        </View>
      </View>
    );
  };
  renderReplicateCheckbox = (document, tabIndex) => {
    const { replicate, uploaded } = document;
    if (this.bookingResponse.passengers.length === 1 || tabIndex !== 0 || !replicate) {
      // we will only show this checkbox for 1st traveller and if document can be replicated
      return null;
    }
    // check if document is already replicated
    let replicated = true;
    for (const savedDocs of this.state.documents) {
      const d = savedDocs[document.type];
      if (!document.index || !d.index || document.index !== savedDocs[document.type].index) {
        replicated = false;
        break;
      }
    }
    // This checkbox is enabled till document is not replicated.
    // after than user can only replace the document to replicate it again
    return (
      <ReplicateCheckbox
        selected={replicated}
        enabled={uploaded && !replicated}
        documentName={capitalize(document.name)}
        onSelect={(checked) => {
          if (checked) {
            this.tagClickEvents(`checkbox_clicked_sameDoc_coTraveller_${document.name}`);
            this.replicateDocuments([document.type]);
          }
        }}
      />
    );
  };
  renderFillVisaFormCTA = (tabIndex) => (
    <TouchableOpacity activeOpacity={0.6} onPress={this.launchForm}>
      <View>
        <View style={styles.fillFormCTAContainer}>
          <Image style={{ width: 24, height: 24 }} source={require('@mmt/legacy-assets/src/docIcon.webp')} />
          <Text
            style={{
              flex: 1,
              color: colors.defaultTextColor,
              fontWeight: 'bold',
              fontSize: 14,
              fontFamily: fonts.bold,
              marginHorizontal: 8,
            }}
          >
            Applicant Details, Other Details, Declaration, Antecedant of Applicant
          </Text>
          <Image
            style={{
              width: 14,
              height: 10,
              tintColor: colors.azure,
              alignSelf: 'center',
              marginRight: 10,
            }}
            source={require('@mmt/legacy-assets/src/CabAmendment/rightIcon.webp')}
          />
        </View>
        {this.getValidateFormState(tabIndex) && (
          <Text style={styles.error}>Please fill application form(s).</Text>
        )}
      </View>
    </TouchableOpacity>
  );
  getValidateFormState = (tabIndex) => {
    const shouldValidate = this.state.validateForms[tabIndex];
    return shouldValidate === undefined ? false : shouldValidate;
  };
  setValidateFormState = (tabIndex, validate) => {
    const validateForms = this.state;
    validateForms[tabIndex] = validate;
    this.setState({ validateForms });
  };
  setFormIsValid = (tabIndex, isValid) => {
    const { forms } = this.state;
    forms[tabIndex].isValid = isValid;
    this.setState({ forms });
  };
  getPassenger = (tabIndex) => {
    return this.bookingResponse.passengers[tabIndex];
  };
  getUpdatedVisaParams = (tabIndex, onlyImageDocs) => {
    const params = cloneDeep(this.visaParams);
    params.useV2 = true; // this is required to use V2 api's and new screens in case if we want to drive this using AB testing
    params.paxId = String(tabIndex + 1); // this is required to scan/attach documents
    params.payRefId = this.props.payRefId;
    params.externalRefId = this.props.externalRefId;
    const pax = {
      ...params.paxList[tabIndex],
      name: this.state.tabsData[tabIndex].name,
      docs: [],
    };
    if (!pax.age) {
      pax.age = '25';
    }
    if (!pax.id) {
      pax.id = `${tabIndex + 1}`;
    }
    delete pax.lastName;

    params.paxList = [pax];
    if (onlyImageDocs) {
      Object.values(this.state.documents[tabIndex]).forEach((doc) => {
        if (!shouldAttach(doc.supportedType)) {
          pax.docs.push({
            name: doc.name,
            type: doc.type,
            typekey: 'IMAGE',
            uploaded: doc.uploaded,
            url: doc.docUrl,
          });
        }
      });
    } else {
      Object.values(this.state.documents[tabIndex]).forEach((doc) => {
        pax.docs.push({
          name: doc.name,
          type: doc.type,
          typekey: shouldAttach(doc.supportedType) ? 'PDF' : 'IMAGE',
          uploaded: doc.uploaded,
          url: doc.docUrl,
        });
      });
    }
    return params;
  };

  launchForm = async (index) => {
    const tabIndex = this.state.selectedTabIndex;
    // if (!this.isAllDocsUploaded(tabIndex)) {
    //   showShortToast('Please upload all documents.');
    //   return;
    // }
    this.tagClickEvents('applicationForm_clicked');
    const tabsData = this.createInitialTabsData();
    let forms = await this.createInitialFormData(tabsData.length);
    this.setState({
      tabsData,
      forms,
      selectedTabIndex : index
    });
    if (!this.isFormDownloaded(index)) {
      this.setState({
        viewState: ViewState.LOADING,
        loaderText: 'Downloading Form...',
      });
      const { forms } = this.state;
      const response = await this.downloadForm(index);
      forms[index] = { ...response };
      this.setState(
        {
          viewState: ViewState.SHOW_DETAIL,
          forms,
        },
        () => {
          this.showFormScreen(index);
        },
      );
    } else {
      this.showFormScreen(index);
    }
  };
  showFormScreen = (tabIndex) => {
    const params = this.getUpdatedVisaParams(tabIndex);
    VisaNavigation.push(VISA_ROUTE_KEYS.MULTIPLE_VISA_FORM_PREFILLED, {
      visaForms: this.state.forms[tabIndex].visaForms,
      commonFormKeys: this.state.forms[tabIndex].commonFormKeys,
      validateForm: this.getValidateFormState(tabIndex),
      tabIndex,
      visaParams: params,
      onPartialFormSave: this.onPartialFormSave,
    });
    this.setValidateFormState(tabIndex, false);
  };


  onPartialFormSave = (visaForms) => {
    let isValid = true;
    for (const form of visaForms) {
      if (!validateDynamicForm(form.formSchema.fields, form.formSchema.fieldValues)) {
        isValid = false;
        break;
      }
    }
    const { forms } = this.state;
    forms[this.state.selectedTabIndex].visaForms = visaForms;
    forms[this.state.selectedTabIndex].isValid = isValid;
    this.setState({ forms });
  };
  onAddDocumentClicked = (tabIndex, type) => {
    const { documents } = this.state;
    const doc = documents[tabIndex][type];
    this.tagClickEvents(`add_${doc.name}`);

    const params = this.getUpdatedVisaParams(tabIndex, true);
    params.type = type; // this type prop will be used in single file upload
    scanDocuments(params, PAGENAME.DOCUMENT_REQUIRED);
    logPdtActionEvent(PDT_PAGENAME.ADD_DOCUMENTS, { pageName: PDT_PAGENAME.UPLOAD_VISA });
  };
  onAttachDocumentClicked = (tabIndex, type) => {
    const { documents } = this.state;
    const doc = documents[tabIndex][type];
    this.tagClickEvents(`attach_${doc.name}`);

    this.setState({
      viewState: ViewState.LOADING,
      loaderText: 'Uploading Voucher...',
    });
    const params = this.getUpdatedVisaParams(tabIndex);
    params.type = type; // this type prop will be used in single file upload
    VisaModule.attachDocument(params);
  };
  onRemoveDocumentClicked = (tabIndex, type) => {
    const { documents } = this.state;
    const doc = documents[tabIndex][type];
    this.tagClickEvents(`replace_${doc.name}`);
    this.removeVisaDocument(tabIndex, type, doc.index);
  };
  async removeVisaDocument(tabIndex, type, docIndex) {
    this.setState({
      loaderText: 'Removing Document...',
      viewState: ViewState.LOADING,
    });
    try {
      const response = await removeDocument(
        this.visaParams.visaBookingId,
        this.getPassenger(tabIndex).paxIndex,
        type,
        docIndex,
      );
      if (response && response.removed === true) {
        this.onDocumentAttachDone({ type, op: REMOVE, status: true });
      } else {
        this.onDocumentAttachDone({ type, op: REMOVE, status: false });
      }
    } catch (e) { }
  }
  replicateDocuments = async (documentTypeList) => {
    this.setState({
      viewState: ViewState.LOADING,
      loaderText: 'Please wait...',
    });
    const response = await replicateDocuments(this.visaParams.visaBookingId, documentTypeList);
    if (response && response.paxList && response.paxList.length) {
      this.onRefreshPressed();
      showShortToast('Document replicated.');
    } else {
      this.setState({
        viewState: ViewState.SHOW_DETAIL,
      });
      showShortToast('Failed to replicated Document.');
    }
  };
  submitApplication = async () => {
    const tabIndex = this.state.selectedTabIndex;

    if (!this.isAllDocsUploaded(tabIndex)) {
      showShortToast('Please upload all documents.');
      return;
    }
    if (!this.state.forms[tabIndex].isValid) {
      showShortToast('Fill Visa Application Form');
      return;
    }
    this.setState({ submittingForms: true });
    const isAllFormsValid = this.validateAllForms();
    logPdtActionEvent(PDT_EVENT_NAME.SUBMIT_APPLICATION, { pageName: PDT_PAGENAME.UPLOAD_VISA });
    if (isAllFormsValid) {
      this.tagClickEvents('proceed_clicked');
      const { forms } = this.state;
      const request = {
        saveForms: [],
      };
      forms.forEach((item, index) => {
        item.visaForms.forEach((form, formIndex) => {
          request.saveForms.push({
            payRefId: form.payRefId,
            paxIndex: this.getPassenger(index).paxIndex,
            formResponse:
              formIndex === 0
                ? form.formSchema.fieldValues
                : getFormResponse(item.visaForms[0], form, item.commonFormKeys),
          });
        });
      });
      const response = await submitMultipleApplicationForm(request);
      if (response && response.saved) {
        if (this.props.fromMIMA) {
          // it means user is comming from  post sales , then review and payment is not required
          this.onBackPressed();
        } else {
          VisaNavigation.push(VISA_ROUTE_KEYS.SHOW_REVIEW, {
            visaParams: cloneDeep(this.visaParams),
          });
        }
      } else if (response) {
        if (response.message) {
          showLongToast(response.message);
        }
      }
    }
    this.setState({ submittingForms: false });
  };
  validateAllForms = () => {
    const { forms } = this.state;
    for (let index = 0; index < forms.length; index++) {
      const item = forms[index];
      if (isEmpty(item.visaForms) || !this.isValidForms(item.visaForms)) {
        this.showFormValidationErrors(index);
        return false;
      }
    }
    return true;
  };
  showFormValidationErrors = (tabIndex) => {
    const { forms, validateForms } = this.state;
    validateForms[tabIndex] = true; // show form errors
    forms[tabIndex].isValid = false; // used to show required badge on panel
    this.setState({ forms, validateForms });
    this.formPanelRef.expand();
    this.onTabClicked(tabIndex);
  };
}

const styles = StyleSheet.create({
  pageWrap: {
    flex: 1,
    backgroundColor: '#f6f6f6',
  },
  pageFooter: {
    padding: 20,
  },
  footerBtn: {
    borderRadius: 3,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disableBtn: {
    backgroundColor: '#c2c2c2',
  },
  footerBtnWrap: {
    borderRadius: 4,
  },
  footerBtnTxt: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
    fontFamily: fonts.bold,
  },
  error: {
    marginLeft: 5,
    marginTop: 12,
    color: colors.errorRed,
    fontSize: 14,
    fontFamily: fonts.regular,
  },
  fillFormCTAContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    ...getPlatformElevation(4),
    borderRadius: 4,
    padding: 12,
    marginTop: 20,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    justifyContent: 'space-between',
  },
  cardWrapper: {
    backgroundColor: '#ffffff',
    paddingVertical: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  headerWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#d8d8d8',
    paddingBottom: 16,
    paddingHorizontal: 16
  },
  documentWrapper: {
    paddingHorizontal: 16,
  },
  contentWrapper: {
    marginHorizontal: 16,
    paddingVertical: 16,
  },
  btnWrapper: {
    paddingVertical: 14,
    paddingHorizontal: 15,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#f8f8f8',
  },
  btnTextStyle: {
    fontSize: 16,
    color: 'red',
    borderRadius: 4,
  },
  successText: {
    color: '#007e7d',
  },
  warningText: {
    color: '#cf8100',
  },
});
AddDocuments.propTypes = {
  visaParams: PropTypes.object.isRequired,
  payRefId: PropTypes.string.isRequired,
  externalRefId: PropTypes.string
};
export default AddDocuments;
