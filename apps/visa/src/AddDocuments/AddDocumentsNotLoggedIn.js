import React from 'react';
import { View, Text, Image, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import PageHeader from './components/PageHeader';
import TabView from './components/TabView';
import Panel from './components/Panel';
import AddDoc from "./components/AddDocView";
import InfoTag from '../Common/InfoTag';
import NotLoggedInView from '../Common/NotLoggedInView';
import { VisaNavigation } from '../Navigation';
import { logPdtActionEvent } from '../Analytics/Pdt/PdtTracker';
import { PDT_EVENT_NAME, PDT_PAGENAME } from '../Analytics/Pdt/PdtConstants';



class AddDocumentsNotLoggedIn extends React.Component {
    constructor(props) {
        super(props)
        this.tabsData = [];
        this.props.visaParams.paxList.forEach(pax => {
            this.tabsData.push({
                name: pax.name,
                docsUploaded: 0,
                maxDocs: 0
            });
        });
        this.documentCategoryList = new Map();
        this.documentCategoryList.set('INDENTITY',
            [
                {
                    "type": "PHOTOGRAPH",
                    "name": "Photograph",
                    "imageLink": "https://imgak.mmtcdn.com/visa/images/ic_photo.png",
                    "supportedType": [
                        "jpg",
                        "jpeg",
                        "png"
                    ]
                },
                {
                    "type": "PASSPORT_FRONT",
                    "name": "Passport Front",
                    "imageLink": "https://imgak.mmtcdn.com/visa/images/ic_passportfront.png",
                    "supportedType": [
                        "jpg",
                        "jpeg",
                        "png"
                    ]
                },
                {
                    "type": "PASSPORT_BACK",
                    "name": "Passport Back",
                    "imageLink": "https://imgak.mmtcdn.com/visa/images/ic_passportfront.png",
                    "supportedType": [
                        "jpg",
                        "jpeg",
                        "png"
                    ]
                }
            ]
        );
        this.documentCategoryList.set('VOUCHERS',
            [
                {
                    "type": "ETICKET",
                    "name": "E-Tickets",
                    "imageLink": "https://imgak.mmtcdn.com/visa/images/flight.png",
                    "mandatory": true,
                    "supportedType": [
                        "pdf"
                    ]
                }
            ]
        );
    }

    onBackPressed = () => {
        VisaNavigation.pop();
        logPdtActionEvent(PDT_EVENT_NAME.APP_BACK_CLICK, {pageName: PDT_PAGENAME.UPLOAD_VISA});
    }
    render() {
        return (
            <View style={styles.pageWrap}>
                <PageHeader onBack={this.onBackPressed}>
                    <TabView
                        ref={(ref) => {
                            this.tabViewRef = ref;
                        }}
                        data={this.tabsData}
                        onTabClicked={() => { }}
                        activeTabIndex={0}
                    />
                </PageHeader>
                <ScrollView>
                    <View style={{ marginHorizontal: 16, marginVertical: 20 }}>
                        <NotLoggedInView textList={['To upload Visa Documents & fill Application Form']} onLoginClick={this.props.onLoginClick} />
                    </View>
                    <View style={{flex:1, position: 'relative'}}>
                        {
                            this.renderDocuments()
                        }
                        <View style={styles.pageFooter}>
                            <TouchableOpacity
                                onPress={() => { }}
                                activeOpacity={0.8}
                            >
                                <LinearGradient start={{ x: 1.0, y: 0.0 }} end={{ x: 0.0, y: 1.0 }} colors={['#c2c2c2', '#c2c2c2']} style={styles.footerBtnWrap}>
                                    <View style={[styles.footerBtn]}><Text style={styles.footerBtnTxt}>SUBMIT APPLICATION</Text></View>
                                </LinearGradient>
                            </TouchableOpacity>

                        </View>
                        <View style={styles.overlay}/>
                    </View>
                </ScrollView>
            </View>
        );
    };
    renderDocuments = () => {
        return (
            <View>
                {
                    Array.from(this.documentCategoryList, ([category, docList]) => (
                        <Panel
                            key={category}
                            title={category}
                            isRequired
                            expanded
                        >
                            {
                                docList.map((item, index) => {
                                    return (
                                        <View key={item.type} style={{ marginBottom: index !== (docList.length - 1) ? 16 : 0 }}>
                                            <AddDoc
                                                document={item}
                                                onAddClick={() => { }}
                                                onAttachClick={() => { }}
                                            />
                                        </View>
                                    );
                                })
                            }
                        </Panel>
                    ))
                }
                <Panel
                    ref={ref => this.formPanelRef = ref}
                    title='Fill Visa Application Form'
                    isRequired
                    expanded
                >
                    <InfoTag text='Enter the information exactly as mentioned in the passport' />
                    {this.renderFillVisaFormCTA()}
                </Panel>
            </View>
        )
    }
    renderFillVisaFormCTA = () => (
        <TouchableOpacity activeOpacity={0.6} onPress={() => { }}>
            <View>
                <View style={styles.fillFormCTAContainer}>
                    <Image style={{ width: 24, height: 24 }} source={require('@mmt/legacy-assets/src/docIcon.webp')} />
                    <Text style={{
                        flex: 1,
                        color: colors.defaultTextColor,
                        fontWeight: 'bold',
                        fontSize: 14,
                        fontFamily: fonts.bold,
                        marginHorizontal: 8
                    }}>Applicant Details, Other Details, Declaration, Antecedant of Applicant</Text>
                    <Image style={{ width: 14, height: 10, tintColor: colors.azure, alignSelf: 'center', marginRight: 10 }} source={require('@mmt/legacy-assets/src/CabAmendment/rightIcon.webp')} />
                </View>
            </View>
        </TouchableOpacity>
    )

}

const styles = StyleSheet.create({
    pageWrap: {
        flex: 1,
        backgroundColor: '#f6f6f6'
    },
    pageFooter: {
        padding: 20,
    },
    footerBtn: {
        borderRadius: 3,
        padding: 15,
        alignItems: 'center',
        justifyContent: 'center'
    },
    footerBtnWrap: {
        borderRadius: 4
    },
    footerBtnTxt: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: fonts.bold
    },
    fillFormCTAContainer: {
        flexDirection: 'row',
        backgroundColor: colors.white,
        ...getPlatformElevation(4),
        borderRadius: 4,
        padding: 12,
        marginTop: 20
    },
    overlay: {
        flex: 1,
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
        opacity: 0.5,
        backgroundColor: 'white'
    }
});
AddDocumentsNotLoggedIn.propTypes = {
    visaParams: PropTypes.object.isRequired,
    onLoginClick: PropTypes.func.isRequired
};
export default AddDocumentsNotLoggedIn;
