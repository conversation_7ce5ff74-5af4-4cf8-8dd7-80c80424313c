import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { shouldAttach } from "../../VisaUtils";

export default class AddDocView extends React.Component {

    getDocumentFormats = types => {
        let format = '';
        for (let index = 0; index < types.length; index++) {
            format += types[index];
            if (index !== types.length - 1) {
                format += '/';
            }
        }
        return format;
    }
    render() {
        const { document, onAddClick, onAttachClick } = this.props;
        const { mandatory, name, imageLink, supportedType } = document;
        const format = this.getDocumentFormats(supportedType);
        const canAttach = shouldAttach(supportedType);
        return (
            <View style={styles.container}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Image style={styles.icon} source={{ uri: imageLink }} />
                    <Text style={styles.name}>{name}</Text>
                    {mandatory && <Text style={styles.imp}>*</Text>}
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    {format && <Text style={styles.format}>{format}</Text>}
                    <TouchableOpacity style={styles.addBtn} activeOpacity={0.7} onPress={canAttach ? onAttachClick : onAddClick}>
                        <Text style={styles.addText}>{canAttach ? 'ATTACH' : 'ADD'}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }
}
const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.white,
        ...getPlatformElevation(4),
        borderRadius: 4,
        justifyContent: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12
    },
    icon: {
        width: 24,
        height: 24
    },
    name: {
        fontFamily: fonts.black,
        fontSize: 14,
        color: colors.defaultTextColor
    },
    imp: {
        fontFamily: fonts.black,
        fontSize: 14,
        color: colors.errorRed,
        flex: 1
    },
    addBtn: {

    },
    format: {
        fontFamily: fonts.regular,
        fontSize: 12,
        color: colors.greyLight,
        marginLeft: 24,
        flex: 1
    },
    addText: {
        fontFamily: fonts.bold,
        fontSize: 12,
        color: colors.azure,
        textAlign: 'right'
    },
    documentWrapper: {
        paddingVertical:12,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        borderBottomColor: '#d8d8d8',
    },
    lastIndexStyle: {
        marginBottom: 0,
        borderBottomWidth: 0,
        paddingBottom:0,
    },
    submitIconStyle: {
        width:16,
        height:16,
        marginRight: 8,
        marginTop:1,
    },
    rightArrowStyle: {
        width:24,
        height:24,
    },
    flexOne: {
        flex:1,
    },
    contentWrapper: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        flex:1,
        marginRight:20,
    },
    submitBorder: {
        borderWidth:1,
        borderColor: '#007E7D',
        borderRadius: 8,
    }
});