import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';



const PageHeader = (props) => {
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity activeOpacity={0.6} onPress={props.onBack}>
                    <View style={styles.backArrowWrap}><Image style={[styles.backarrow]} source={require('@mmt/legacy-assets/src/Visa/trip_header_back_icon.webp')} /></View>
                </TouchableOpacity>
                <Text style={styles.hdrText}>Add  Documents & Fill Form</Text>
            </View>
            {props.children}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: 'white',
        ...getPlatformElevation(5),
        paddingTop: 20
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20
    },
    hdrText: {
        fontSize: 18,
        fontFamily: fonts.bold,
        color: '#4a4a4a'
    },
    backarrow: {
        width: 16,
        height: 16,
        tintColor: '#9b9b9b'
    },
    backArrowWrap: {
        paddingLeft: 10,
        paddingRight: 20
    }
});

export default PageHeader;