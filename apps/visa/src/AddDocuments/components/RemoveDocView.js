import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { downloadImage } from '../../VisaImageDownloader';
import { getHeaders } from '../../VisaNetworkUtils';
import { downloadVisaDocument } from '../../VisaUtils';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';

export default class RemoveDocView extends React.Component {
    constructor(props) {
        super(props);
        const documentName = this.getDocumentName();
        const documentFormat = this.getDocumentFormats();
        this.state = {
            base64Icon: this.props.base64Icon,
            documentName,
            documentFormat,
            isPDF: documentName.toLowerCase().includes(".pdf")
        };
        this.callback = {
            onImageLoaded: (base64Icon) => {
                this.setState({ base64Icon });
            },
            onImageLoading: () => { },
            onImageLoadingFailed: () => { },
            onImageQueued: () => { }
        };
    }

    componentDidMount() {
        const { base64Icon, isPDF } = this.state;
        if (Platform.OS === "ios" || isPDF || base64Icon) {
            return;
        }
        downloadImage(this.props.document.docUrl, this.callback);
    }

    downloadDocument = async () => {
        const headers = await getHeaders();
        downloadVisaDocument(this.props.document.docUrl, this.getDocumentName(), headers, filePath => {
            if (filePath && Platform.OS === "android") {
                // preview document
                VisaModule.previewDocument(filePath, 'application/pdf');
            }
        });
    }
    getDocumentName = () => {
        const { docUrl, name } = this.props.document;
        const nameWithExtension = docUrl.split('/').pop().split('#')[0].split('?')[0];
        if (nameWithExtension.length <= 15) {
            return nameWithExtension;
        }
        const extension = nameWithExtension.slice(nameWithExtension.lastIndexOf('.'), nameWithExtension.length);
        return name + extension;
    }
    getDocumentFormats = () => {
        const types = this.props.document.supportedType;
        let format = '';
        for (let index = 0; index < types.length; index++) {
            format += types[index];
            if (index !== types.length - 1) {
                format += '/';
            }
        }
        return format;
    }
    renderImage = () => {
        const { document, headers } = this.props;
        if (this.state.isPDF) {
            return (
                <TouchableOpacity activeOpacity={0.7} onPress={this.downloadDocument}>
                    <Image style={styles.pdf} source={require('@mmt/legacy-assets/src/Visa/pdf-file.webp')} />
                </TouchableOpacity>
            );
        }
        return Platform.OS === "ios" ? (
            <Image style={styles.image} source={{
                uri: document.docUrl,
                method: 'GET',
                headers
            }} />
        ) : (
                <Image style={styles.image} source={{ uri: this.props.base64Icon || this.state.base64Icon }} />
            )
    }
    render() {
        const { document, onRemoveClick } = this.props;
        const { mandatory, name, imageLink } = document;
        const docName = this.state.documentName;
        const format = this.state.documentFormat;
        return (
            <View style={styles.container}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Image style={styles.icon} source={{ uri: imageLink }} />
                    <Text style={styles.name}>{name}</Text>
                    {mandatory && <Text style={styles.imp}>*</Text>}
                </View>
                {format && <Text style={styles.format}>{format}</Text>}
                <View style={styles.imageWrapper}>
                    {this.renderImage()}
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    {docName && <Text style={styles.imageName}>{docName}</Text>}
                    <TouchableOpacity style={styles.addBtn} activeOpacity={0.7} onPress={onRemoveClick}>
                        <Text style={styles.addText}>REPLACE</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }
}
const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.white,
        ...getPlatformElevation(4),
        borderRadius: 4,
        justifyContent: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12
    },
    icon: {
        width: 24,
        height: 24
    },
    name: {
        fontFamily: fonts.black,
        fontSize: 14,
        color: colors.defaultTextColor
    },
    imp: {
        fontFamily: fonts.black,
        fontSize: 14,
        color: colors.errorRed,
        flex: 1
    },
    format: {
        fontFamily: fonts.regular,
        fontSize: 12,
        color: colors.greyLight,
        marginLeft: 24,
        flex: 1
    },
    addText: {
        fontFamily: fonts.bold,
        fontSize: 12,
        color: colors.azure,
        textAlign: 'right'
    },
    imageWrapper: {
        marginLeft: 24,
        flex: 1,
        padding: 12,
        ...getPlatformElevation(4),
        borderRadius: 4,
        alignItems: 'center',
        marginTop: 4,
        marginBottom: 8
    },
    image: {
        width: 100,
        height: 100,
        resizeMode: 'cover',
        backgroundColor: '#d8d8d8'
    },
    pdf: {
        width: 70,
        height: 70,
        resizeMode: 'cover',
        tintColor: colors.azure
    },
    imageName: {
        fontFamily: fonts.bold,
        fontSize: 14,
        color: '#1a7971',
        flex: 1,
        marginLeft: 24
    }
});