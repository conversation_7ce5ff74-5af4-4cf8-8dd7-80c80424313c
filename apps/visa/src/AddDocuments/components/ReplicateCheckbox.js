import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import LinerGradient from 'react-native-linear-gradient';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import BasicCheckbox from '../../Common/BasicCheckbox';

class ReplicateCheckbox extends React.Component {

    render() {
        return (
            <LinerGradient style={styles.container} colors={['#cdf6e8', '#bfe4e0']}>
                <BasicCheckbox {...this.props} style={{tintColor: '#1a7971'}}/>
                <Text style={styles.label}>{`Use the same ${this.props.documentName} for my `}<Text style={styles.labelBold}>co-travellers</Text></Text>
            </LinerGradient>
        );
    }
    renderContent = () => (
        <View style={styles.content}>
            {this.props.children}
        </View>
    )
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 4,
        paddingHorizontal: 16,
        paddingVertical: 10,
        marginTop: 16
    },
    label: {
        color: '#000',
        fontSize: 14,
        marginLeft: 10,
        fontFamily: fonts.regular
    },
    labelBold: {
        fontFamily: fonts.bold
    }
});

ReplicateCheckbox.propTypes = {
    selected: PropTypes.bool.isRequired,
    documentName: PropTypes.string.isRequired,
    enabled: PropTypes.bool.isRequired,
    onSelect: PropTypes.func.isRequired
};


export default ReplicateCheckbox;
