import React from 'react';
import { View, FlatList, Text, TouchableOpacity, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { cloneDeep } from 'lodash';

const ELLEPSIS_COUNT = 3;
const MAX_NAME_CHARS = 15 + ELLEPSIS_COUNT;
export default class TabView extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTabIndex: this.props.activeTabIndex ? this.props.activeTabIndex : 0,
      data: this.normalizeTabData()
    };
  }
  normalizeTabData = () => {
    const {data} = this.props;
    const list = [];
    data.forEach(item => {
      const newItem = cloneDeep(item);
      if (item.name.length > MAX_NAME_CHARS) {
        newItem.name = `${item.name.slice(0, MAX_NAME_CHARS)}...`;
      }
      list.push(newItem);
    });
    return list;
  }
  render() {
    const {data} = this.state;
    return (
      <FlatList
        ref={(ref) => {
          this.tabsList = ref;
        }}
        data={data}
        renderItem={this.renderTabItem}
        keyExtractor={(item, index) => `Tab-${index}`}
        showsHorizontalScrollIndicator={false}
        horizontal
        extraData={this.state.activeTabIndex}
        onScrollToIndexFailed={() => { }}
        initialNumToRender={data.length}
      />);
  }
  renderTabItem = ({ item, index }) => {
    const {name, docsUploaded, maxDocs} = item;
    return (
      <TouchableOpacity activeOpacity={0.6} onPress={() => this.props.onTabClicked(index)} style={index === 0 ? styles.tabItemZeroContainer : styles.tabItemContainer}>
        <View style={this.state.activeTabIndex === index ? styles.selected : {}}>
          <Text style={this.state.activeTabIndex === index ? styles.tabItemSelected : styles.tabItem}>{name}
            {maxDocs!=0 ? <Text style={styles.tabItem}>{` (${docsUploaded}/${maxDocs})`}</Text> : null}
          </Text>
        </View>
      </TouchableOpacity>);
  }
  scrollToTab = (index) => {
    const {data} = this.props;
    if (this.tabsList && index >= 0 && index < data.length) {
      this.setState({ activeTabIndex: index }, () => {
        this.tabsList.scrollToIndex({ index: this.state.activeTabIndex, animated: true });
      });
    }
  }
  getSelectedTabIndex = () => this.state.activeTabIndex
}
const styles = StyleSheet.create({
  tabItemContainer: {
    paddingHorizontal: 10
  },
  tabItemZeroContainer: {
    paddingRight: 10
  },
  tabItem: {
    fontSize: 14,
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    textTransform:'uppercase'
  },
  selected: {
    borderBottomColor: colors.azure,
    borderBottomWidth: 2
  },
  tabItemSelected: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.black,
    paddingBottom: 4,
    paddingHorizontal: 24,
    textTransform:'uppercase'
  }
})
TabView.propTypes = {
  data: PropTypes.array.isRequired, // should be objects array {name, docsUploaded, maxDocs}
  onTabClicked: PropTypes.func.isRequired, // onTabClicked(index)
  activeTabIndex: PropTypes.number
};
