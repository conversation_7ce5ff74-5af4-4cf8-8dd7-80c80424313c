import React from 'react';
import { StyleSheet, Text, View, Image, TouchableHighlight } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../../Navigation';
import {
  getImagePath
} from '../../VisaUtils';

class Panel extends React.Component {
  constructor(props) {
    super(props);

    this.icons = {
      up: require('@mmt/legacy-assets/src/up_arrow_blue.webp'),
      down: require('@mmt/legacy-assets/src/ic_arrow_blue_down.webp'),
    };

    this.state = {
      expanded: props.expanded || false,
      categoryadded: []
    };
  }
  expand = () => {
    this.setState(
      {
        expanded: true,
      },
      () => {
        if (this.props.onExpand) {
          this.props.onExpand(this.state.expanded);
        }
      },
    );
  };
  collapse = () => {
    this.setState(
      {
        expanded: false,
      },
      () => {
        if (this.props.onExpand) {
          this.props.onExpand(this.state.expanded);
        }
      },
    );
  };
  toggle = () => {
    if (this.state.expanded) {
      this.collapse();
    } else {
      this.expand();
    }
  };

  launchPassport = () => {
    let cat = this.state.categoryadded;
    var addComp = true;
    if ((cat.length === 0) && this.props.category === "PASSPORT") {
      cat.push(this.props.category);
      this.setState(
        {
          categoryadded: cat,
        })
    } 
    else if ((cat.length > 0 && cat.includes(this.props.category)) && this.props.category === "PASSPORT") {
      if(cat.length%2 !== 0){
          addComp = false;
      }
      cat.push(this.props.category);
      this.setState(
        {
          categoryadded: cat,
        })
    }
    if (addComp) {
      VisaNavigation.push(VISA_ROUTE_KEYS.PASSPORT_UPLOAD, {
        visaParams: this.props.params,
        docList: this.props.docList,
        savedDocs: this.props.savedDocs,
        tabIndex: this.props.tabIndex,
        payRefId: this.props.payRefId,
        externalRefId: this.props.externalRefId,
        name: this.props.name,
        category: this.props.category,
        RefreshPressed : this.props.RefreshPressed,
      });
    }
  }

  launch = () => {
    var category = this.props.category;
    if (category) {
      category = category.toUpperCase();
    }
    switch (category) {
      case "PASSPORT":
        this.launchPassport();
      case "PHOTOGRAPH":
      case "IDENTITY":
        this.launchPassport();
      default:
        this.toggle();
    }
  };
  render() {
    let icon = this.icons['down'];

    if (this.state.expanded) {
      icon = this.icons['up']; //Step 4
    }
    const { launchForm } = this.props;
    //Step 5
    return (
      <View>
        <TouchableHighlight onPress={this.props.type === 'FORM' ? launchForm : this.launch} underlayColor="#f1f1f1" style={[styles.documentWrapper,
        this.props.lastIndex ? styles.lastIndexStyle : null
        ]}>
          <View style={styles.contentWrapper}>
            {this.props.isSubmit && <Image source={getImagePath('submitImage')} style={styles.submitIconStyle} />}
            {this.props.isInfo && <Image source={getImagePath('warningImage')} style={styles.submitIconStyle} />}
            {this.props.isErrored && <Image source={getImagePath('errorImage')} style={styles.submitIconStyle} />}
            <View style={styles.flexOne}>
              <Text style={styles.title}>{this.props.title}</Text>
              <Text style={[styles.font12, styles.regularFont, styles.defaultText]}>{this.props.description}</Text>
            </View>
            <Image style={styles.rightArrowStyle} source={getImagePath('rightArrow')} />
          </View>
        </TouchableHighlight>
        {this.state.expanded && !(this.props.category === "PASSPORT" || this.props.category === "PHOTOGRAPH" || this.props.category === "IDENTITY" || this.props.type === "FORM"
        ) ? this.renderContent() : null}
      </View>
    );
  }
  renderContent = () => <View style={styles.content}>{this.props.children}</View>;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#d8d8d8',
    marginTop: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 14,
  },
  title: {
    flex: 1,
    color: '#4a4a4a',
    fontSize: 16,
    fontFamily: fonts.bold,
  },
  button: {
    width: 25,
    height: 25,
  },
  buttonImage: {
    width: 25,
    height: 25,
  },
  required: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginHorizontal: 8,
    borderRadius: 2,
  },
  requiredText: {
    color: '#fff',
    fontSize: 10,
    fontFamily: fonts.bold,
    minWidth: 50,
    textAlign: 'center',
  },
  font12: {
    fontSize: 12
  },
  content: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#d8d8d8',
  },
  documentWrapper: {
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#d8d8d8',
  },
  documentWrapper: {
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#d8d8d8',
  },
  lastIndexStyle: {
    marginBottom: 0,
    borderBottomWidth: 0,
    paddingBottom: 0,
  },
  submitIconStyle: {
    width: 16,
    height: 16,
    marginRight: 8,
    marginTop: 1,
  },
  rightArrowStyle: {
    width: 24,
    height: 24,
  },
  flexOne: {
    flex: 1,
  },
  contentWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    marginRight: 20,
  },
  submitBorder: {
    borderWidth: 1,
    borderColor: '#007E7D',
    borderRadius: 8,
  },
  regularFont: {
    fontFamily: fonts.regular
  }
});
export default Panel;
