import { isEmpty } from 'lodash';
import { Platform } from 'react-native';
import NetworkModule from '@mmt/legacy-commons/Native/NetworkModule';
import { generateGUID, getPaxObject, getSessionID, isValidResponse } from './VisaUtils';
import { BASEURL, apiTimeouts, PlatformAuthCode, DEFAULT_ADULT_AGE, ADULT_AGE_EXTENSION, ADULT, COMMID_ENCRYPTION_API_ENDPOINT } from './VisaConstants';

let isLoggedIn = false;
const sessionExpiryTime = 240000 // 4 mins expiry

export const getHeaders = async () => {
  const headers = await NetworkModule.getHeaders();
  isLoggedIn = !isEmpty(headers['mmt-auth']);
  const getUUID = generateGUID();
  const sessionId = await getSessionID('visa_session_id', getUUID, sessionExpiryTime);
  headers['sessionId'] = sessionId;
  headers['Authorization'] = getAuthValue();
  return headers;
};

const getAuthValue = () => {
  if (Platform.OS === 'ios') {
    return PlatformAuthCode.IOS;
  }
  else if (Platform.OS === 'android') {
    return PlatformAuthCode.ANDROID;
  }
  else if (Platform.OS === 'web') {
    return PlatformAuthCode.MWEB;
  }
  return null;
}

export const timeout = (ms, promise) => new Promise((resolve, reject) => {
  setTimeout(() => {
    reject(new Error('api timed out'));
  }, ms);
  promise.then(resolve, reject);
});

export const getVisaLandingData = async () => {
  try {
    const url = `${BASEURL}/landing`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.LANDING, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return {
          visaLandingResponse: responseBody,
          isLoggedIn
        };
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const deletePendingBooking = async (bkngId) => {
  try {
    const url = `${BASEURL}/deleteBooking`;
    const request = { bookingId: bkngId };

    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.DELETE_BOOKING, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));

    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getVisaUserReviews = async () => {
  try {
    const url = `${BASEURL}/userReviews`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.USER_REVIEW, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getVisaPersuasionElements = async () => {
  try {
    const url = `${BASEURL}/persuasionElements`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};


export const initiateBooking = async (visaParams, profileFormResponse) => {
  try {
    const url = `${BASEURL}/initiateBooking`;
    const headers = await getHeaders();
    const passengers = visaParams.paxList.map((item) => {
      return { name: item.name, lastName: item.lastName, age: Number(item.age), gender: item.gender };
    });
    const postBody = JSON.stringify({
      visaType: Number(visaParams.visaTypeId),
      passengers,
      travelStart: visaParams.startDate,
      travelEnd: visaParams.endDate,
      profile: profileFormResponse
    });
    const response = await timeout(apiTimeouts.landing.INITIATE_BOOKING, fetch(url, {
      method: 'POST',
      headers,
      body: postBody
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getTypeOfVisaData = async (countryCode, locusCode, fromDate, paxAge) => {
  try {
    let url = `${BASEURL}/search?countryCode=${countryCode}&fromDate=${fromDate}&paxAge=${paxAge}`
    if(locusCode){
      url += `&locusCode=${locusCode}`;
    }
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getVisaDocumentInfo = async (visaId, visaType, paxAge, receivingDate) => {
  try {
    const url = `${BASEURL}/docsReqd?visaId=${visaId}&visaType=${visaType}&paxAge=${paxAge}&receivingDate=${receivingDate}`;
        const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return {
          visaDocInfoResponse: responseBody,
          isLoggedIn
        };
      }
    }
  } catch (e) {
    return null;
  }
  return null;
}

export const getSchemaApplicationFormResponse = async (bookingId, paxIndex) => {
  try {
    const url = `${BASEURL}/form?bookingId=${bookingId}&paxIndex=${paxIndex}`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const submitApplicationFormResponse = async (bookingId, paxIndex, formResponse) => {
  try {
    const url = `${BASEURL}/saveForm`;
    const headers = await getHeaders();
    const request = { bookingId: Number(bookingId), paxIndex: Number(paxIndex), formResponse: formResponse };
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getVisaReviewResponse = async (bookingId, assurancePriceId) => {
  try {
    const url = `${BASEURL}/review?bookingId=${bookingId}&assurancePriceId=${assurancePriceId}`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    console.log(response);
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};
export const getVisaReviewResponseV2 = async (payRefId, assurancePriceId) => {
  try {
    const url = `${BASEURL}/v2/review?payRefId=${payRefId}&assurancePriceId=${assurancePriceId}`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    console.log(response);
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getThankYouResponse = async (payRefId) => {
  try {
    const url = `${BASEURL}/desktop/thankyou?payRefId=${payRefId}`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    console.log(response);
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const applyPromoResponse = async (bookingId, couponCode) => {
  try {
    const url = `${BASEURL}/applyCoupon`;
    const headers = await getHeaders();
    const request = { bookingId: Number(bookingId), couponCode: couponCode };
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
}

export const getVisaBookingDetails = async (bookingId) => {
  try {
    const url = `${BASEURL}/booking?bookingId=${Number(bookingId)}`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getVisaBookingDetailsV2 = async (payRefId, externalRefId) => {
  try {
    let url = `${BASEURL}/v2/booking?payRefId=${payRefId}`;
    if (externalRefId) {
      url = `${url}&externalRefId=${externalRefId}`;
    }
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const getUpdatedField = async (finalUrl) => {
  try {
    const url = `${BASEURL}${finalUrl.toString()}`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.INITIATE_BOOKING, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const removeDocument = async (bookingId, paxIndex, docType, docIndex) => {
  try {
    const url = `${BASEURL}/removeDoc`;
    const headers = await getHeaders();
    const request = { bookingId: Number(bookingId), paxIndex: Number(paxIndex), docIndex: Number(docIndex), docType: docType };
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));
    console.log('NITIN : removeDocument : ' + response);
    if (response && response.ok) {
      console.log('NITIN : removeDocument1 : ' + response);
      const responseBody = await response.json();
      return responseBody;
    }
  } catch {
    return null;
  }
  return null;
}

export const downloadVisaImage = async (url) => {
  try {
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response) {
      const responseBody1 = await response.blob();
      return responseBody1
    }
  } catch (e) {
    return null;
  }
  return null;
}

export const getVisaPurposeForm = async (countryCode,locusCode) => {
  try {
    let url = `${BASEURL}/getTravellerProfileForm?countryCode=${countryCode}`;
    if(locusCode){
      url += `&locusCode=${locusCode}`;
    }
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return {
          ...responseBody,
          isLoggedIn
        };
      }
    }
  } catch (e) {
    return null;
  }
  return null;
}
export const getVisaChecklist = async (request = {
  countryCode: '',
  locusCode: '',
  typeOfVisa: -1,
  formResponse: {}
}) => {
  try {
    const url = `${BASEURL}/checklist`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (error) {
    return null;
  }
  return null;
}

export const getMultipleApplicationFormResponse = async (payRefId, externalRefId, paxIndex) => {
  try {
    let url = `${BASEURL}/v2/form?paxIndex=${paxIndex}`;
    if (externalRefId) {
      url = `${url}&externalRefId=${externalRefId}`;
    } 
    if (payRefId) {
      url = `${url}&payRefId=${payRefId}`;
    }
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (isValidResponse(responseBody)) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};

export const savePartialFormResponse = async (request) => {
  try {
    const url = `${BASEURL}/v2/savePartialForm`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};
export const submitMultipleApplicationForm = async (request) => {
  try {
    const url = `${BASEURL}/v2/saveForm`;
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
};
export const replicateDocuments = async (bookingId, docTypeList) => {
  try {
    const url = `${BASEURL}/replicateDoc`;
    const headers = await getHeaders();
    const request = {
      bookingId: Number(bookingId),
      docTypeList
    };
    const response = await timeout(apiTimeouts.uploadVisaMain.REPLICATE_DOCS, fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(request)
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      return responseBody;
    }
  } catch (e) {
    return null;
  }
  return null;
}

export const encodeUserInfo = async (commIdList) => {
  try {
    const url = COMMID_ENCRYPTION_API_ENDPOINT;
    const headers = await getHeaders();
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify({commIdList})
    })
    
    if (response && response.ok) {
      const responseBody = await response.json();
      if (responseBody &&  responseBody.success && responseBody.commIdList.length > 0) {
        return responseBody.commIdList;
      }
      return [];
    }
    return [];
  } catch (e) {
    console.log(error);
    return Promise.resolve([]);
  }
};

export const getSampleImageData = async (DocType, countryCode) => {
  try {
    let url = `${BASEURL}/getSampleDoc?docType=${DocType}&countryCode=${countryCode}`;
    
    const headers = await getHeaders();
    const response = await timeout(apiTimeouts.landing.PERSUASION, fetch(url, {
      method: 'GET',
      headers
    }));
    if (response && response.ok) {
      const responseBody = await response.json();
      if (responseBody && responseBody.SampleUrl) {
        return responseBody;
      }
    }
  } catch (e) {
    return null;
  }
  return null;
};