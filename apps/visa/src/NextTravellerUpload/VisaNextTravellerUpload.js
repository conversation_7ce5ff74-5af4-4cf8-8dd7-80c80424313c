import React from 'react';
import {DeviceEventEmitter, View, ScrollView, Text, Image, TouchableOpacity, ActivityIndicator} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import arrowIcon from '@mmt/legacy-assets/src/trip_header_back_icon.webp';
import smileIcon from '@mmt/legacy-assets/src/smileIcon.webp';
import tickIcon from '@mmt/legacy-assets/src/ic_tick.webp';
import styles from '../css/VisaMainCss';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import ViewState from '@mmt/legacy-commons/Common/constants/ViewState';
import {
  PAGENAME,
  DOC_OP,
  TRACKING_PAGENAME,
  EVENTS
} from '../VisaConstants';
import {
  scanDocuments,
  onDocumentUploadDone,
  onEticketAttachment,
  getDocUploadedPaxCount,
  needToUploadHotelVoucher
} from '../VisaUtils';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';


class VisaNextTravellerUpload extends React.Component {
  static navigationOptions = {
    header: null
  }

  constructor(props) {
    super(props, PAGENAME.NEXT_TRAVELLER);
    this.visaParams = this.props.visaParams;
    this.state = {
      viewState: ViewState.SHOW_DETAIL
    };
    this.previousPage = 'applicationForm';
    this.onDocumentUploadDoneListener = null;
    this.onEticketAttachEventListener = null;
  }

  componentWillMount() {
    this.onEticketAttachEventListener = DeviceEventEmitter.addListener(EVENTS.NEXT_TRAVELLER_UPLOAD.ETICKET_EVENT_NEXT_TRAVELLER, this.onEticketAttachEvent);
    this.onDocumentUploadDoneListener = DeviceEventEmitter.addListener(EVENTS.NEXT_TRAVELLER_UPLOAD.UPLOAD_FINISH_NEXT_TRAVELLER, this.onDocumentUploadDone);
  }

  componentWillUnmount() {
    if(this.onDocumentUploadDoneListener){
      this.onDocumentUploadDoneListener.remove();
    }
    if(this.onEticketAttachEventListener){
      this.onEticketAttachEventListener.remove();
    }
  }

  componentDidMount() {
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
      TRACKING_PAGENAME.NEXT_TRAVELLER,
      this.previousPage, this.visaParams
    );
  }

  onEticketAttachEvent = (data) => {
    onEticketAttachment(data, this.visaParams, PAGENAME.NEXT_TRAVELLER);
  }

  onDocumentUploadDone = (data) => {
    onDocumentUploadDone(data, this.visaParams, PAGENAME.NEXT_TRAVELLER, TRACKING_PAGENAME.NEXT_TRAVELLER);
  };

  renderProgressView = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {this.state.loaderText}
      </Text>
    </View>
  );

  render() {
    return (
      <View style={{flex: 1}}>
        {this.state.viewState === ViewState.LOADING && this.renderProgressView()}
        {this.state.viewState === ViewState.SHOW_DETAIL && this.renderContent()}
      </View>
    );
  }

  backPressed = () => {
    VisaNavigation.pop();
  }

  renderContent() {
    return (
      <View style={[styles.VisaMainContainer, styles.MakeRelative]}>
        <ScrollView>
          <View style={styles.TravellerUploadDtls}>
            <LinearGradient start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} colors={['#ccf6ff', '#d5e9f9']} style={styles.TravellerTopGradient}>
              <TouchableOpacity
                style={[styles.backArrowWrapper, styles.padding5]}
                onPress={this.backPressed}
              >
                <Image style={styles.Wtbackarrow} source={arrowIcon} />
              </TouchableOpacity>
              <Text style={[styles.font22, styles.bgTransparent, styles.blueTextLight1, styles.boldFont, styles.mar8Bt]}>Hurray!</Text>
              <View style={styles.flexRow}>
                <Image style={[styles.SmileIcon, styles.mar5Rt]} source={smileIcon} />
                <Text style={[styles.font16, styles.bgTransparent, styles.BlueText1, styles.mar10Tp, styles.regularFont]}><Text style={styles.boldFont}> {getDocUploadedPaxCount(this.visaParams.paxList)}/{this.visaParams.paxList.length} </Text> Travellers Complete</Text>
              </View>
            </LinearGradient>
            <View style={[styles.WhiteCard, styles.minHt130, styles.ChooseTravellerDtls]}>
              <Text style={[styles.DefaultText, styles.mar20Bt, styles.regularFont]}>Documents uploaded and form verified for <Text style={[styles.boldFont, styles.DefaultText]}> {this.visaParams.paxName}</Text></Text>
              <View>
                <View style={[styles.flexRow, styles.mar10Bt]}>
                  <Image
                    style={styles.travellerUploadStyle}
                    source={tickIcon}
                  />
                  <Text style={[styles.greenTxt1, styles.regularFont]}>All Flight E-Tickets </Text>
                </View>
                <View style={[styles.flexRow, styles.mar10Bt]}>
                  <Image
                    style={styles.travellerUploadStyle}
                    source={tickIcon}
                  />
                  <Text style={[styles.greenTxt1, styles.regularFont]}>Complete Verified Form </Text>
                </View>
                <View style={[styles.flexRow, styles.mar10Bt]}>
                  <Image
                    style={styles.travellerUploadStyle}
                    source={tickIcon}
                  />
                  <Text style={[styles.greenTxt1, styles.regularFont]}>Passport Front</Text>
                </View>
                <View style={[styles.flexRow, styles.mar10Bt]}>
                  <Image
                    style={styles.travellerUploadStyle}
                    source={tickIcon}
                  />
                  <Text style={[styles.greenTxt1, styles.regularFont]}>Passport Back</Text>
                </View>
                <View style={[styles.flexRow, styles.mar10Bt]}>
                  <Image
                    style={styles.travellerUploadStyle}
                    source={tickIcon}
                  />
                  <Text style={[styles.greenTxt1, styles.regularFont]}>Photograph</Text>
                </View>
                {needToUploadHotelVoucher(this.visaParams) &&
                <View style={[styles.flexRow, styles.mar10Bt]}>
                  <Image
                    style={styles.travellerUploadStyle}
                    source={tickIcon}
                  />
                  <Text style={[styles.greenTxt1, styles.regularFont]}>All Hotel Vouchers</Text>
                </View>
                   }
              </View>
            </View>
          </View>
        </ScrollView>

        <TouchableOpacity onPress={this.continueNextProcess}>
          <LinearGradient start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} style={styles.btnBottom} colors={['#065af3', '#53b2fe']}>
            <Text style={[styles.BtnText, styles.regularFont]}> {this.visaParams.operation} DOCUMENTS FOR NEXT TRAVELLER </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  }

  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.NEXT_TRAVELLER,
      clickEvent,
      this.visaParams,
      this.previousPage
    );
  }

  continueNextProcess = () => {
    const params = {...this.visaParams};
    if (params.operation === DOC_OP.UPLOAD) {
      scanDocuments(params, PAGENAME.NEXT_TRAVELLER);
    } else if (params.operation === DOC_OP.VERIFY) {
      VisaNavigation.push(VISA_ROUTE_KEYS.OPEN_DOC_VERIFICATION,{
        visaParams: params
      });
    }
    this.trackClickEvent('continue');
  }
}

VisaNextTravellerUpload.propTypes = {
  visaParams: PropTypes.object.isRequired
};


export default VisaNextTravellerUpload;
