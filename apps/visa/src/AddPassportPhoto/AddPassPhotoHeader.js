import { StyleSheet, Text, Image, View } from "react-native";
import React from "react";
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {
  getImagePath
} from '../VisaUtils';

const AddPassPhotoHeader = ({title, subTitle, isError, isSuccess,isWarn}) => {
  return (
    <View style={styles.headerWrapperAPP}>
      {isError && <Image source={getImagePath('errorImage')} style={styles.iconStyle} /> }
      {isSuccess && <Image source={getImagePath('submitImage')} style={styles.iconStyle} /> }
      {isWarn && <Image source={getImagePath('warningImage')} style={styles.iconStyle} /> }
      <View>
        <Text style={[AtomicCss.font16, AtomicCss.boldFont, AtomicCss.blackText, AtomicCss.marginBottom3]}>{title}</Text>
        <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.blackText]}>{subTitle}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    headerWrapperAPP:{
        padding: 16,
        borderBottomColor: '#d8d8d8',
        borderBottomWidth: 1,
        flexDirection: 'row',
    },
    iconStyle: {
      width: 22,
      height: 22,
      marginRight: 8,
  },
});
export default AddPassPhotoHeader;