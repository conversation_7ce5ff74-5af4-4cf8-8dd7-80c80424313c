import { StyleSheet, View, Image, TouchableOpacity, DeviceEventEmitter } from 'react-native';
import React, { useState } from 'react';
import AddPassPhotoHeader from './AddPassPhotoHeader';
import ErrorMessage from './ErrorMessage';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { isEmpty, cloneDeep } from 'lodash';
import { getDownloadedImage } from '../VisaImageDownloader';
import Button from '../Common/Button';
import {
  getImagePath,
  scanDocuments,
  shouldAttach
} from '../VisaUtils';
import { PDT_EVENT_NAME, PDT_PAGENAME } from '../Analytics/Pdt/PdtConstants';
import { PAGENAME, PageErrorData, EVENTS, TRACKING_PAGENAME, VISA_ERRORMESSAGES } from '../VisaConstants';
import { VisaNavigation } from '../Navigation';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import { removeDocument } from '../VisaNetworkUtils';

const getSImagePath = (sampleImage, type) => {
  if (sampleImage && sampleImage.length > 0) {
    var image = sampleImage.find(item => item.type === type);
    if (image) {
      return { uri: image.SampleUrl };
    }
  }
}

const getUploadedImageURL = (data, savedDocs, type) => {
  var item = savedDocs[type];
  if (isEmpty(item.docUrl) || item.docUrl === undefined) {
    return null;
  }
  return { uri: item.docUrl };
}

const imageURLexists = (item, savedDocs, type) => {
  if (isEmpty(getUploadedImageURL(item, savedDocs, type))) {
    return false;
  } else {
    return true;
  }
}

const getErrorMessage = (item) => {
  let criticalError = "";
  if (item.prevError) {
    if (item.prevError.criticalError && !(item.uploaded)) {
      criticalError = item.prevError.criticalError;
    }
  }
  return criticalError;
}

getWarningMessage = (item) => {
  let softError = "";
  if (item.prevError) {
    if (item.prevError.softError && (item.uploaded)) {
      softError = item.prevError.softError;
    }
  }
  return softError;
}

const getSuccess = (item, savedDocs, type) => {
  return imageURLexists(item, savedDocs, type);
}

const getUpdatedVisaParams = (tabIndex, onlyImageDocs, visaParams, payRefId, externalRefId, name, savedDocs) => {
  const params = cloneDeep(visaParams);
  params.useV2 = true; // this is required to use V2 api's and new screens in case if we want to drive this using AB testing
  params.paxId = String(tabIndex + 1); // this is required to scan/attach documents
  params.payRefId = payRefId;
  params.externalRefId = externalRefId;
  const pax = {
    ...params.paxList[tabIndex],
    name: name,
    docs: [],
  };
  if (!pax.age) {
    pax.age = '25';
  }
  if (!pax.id) {
    pax.id = `${tabIndex + 1}`;
  }
  delete pax.lastName;
  params.paxList = [pax];
  if (onlyImageDocs) {
    Object.values(savedDocs).forEach((doc) => {
      if (!shouldAttach(doc.supportedType)) {
        pax.docs.push({
          name: doc.name,
          type: doc.type,
          typekey: 'IMAGE',
          uploaded: doc.uploaded,
          url: doc.docUrl,
        });
      }
    });
  } else {
    Object.values(savedDocs).forEach((doc) => {
      pax.docs.push({
        name: doc.name,
        type: doc.type,
        typekey: shouldAttach(doc.supportedType) ? 'PDF' : 'IMAGE',
        uploaded: doc.uploaded,
        url: doc.docUrl,
      });
    });
  }
  return params;
};

const onRemoveDocumentClicked = (tabIndex, type, item, visaParams) => {
  const doc = item[type];
  removeVisaDocument(tabIndex, type, doc.index, visaParams);
};

const removeVisaDocument = async (tabIndex, type, docIndex, visaParams) => {
  try {
    const response = await removeDocument(
      visaParams.visaBookingId,
      visaParams.paxId,
      type,
      docIndex,
    );
  } catch (e) {
  }
}

const getButtonName = (savedDocs, type) => {
  let doc = savedDocs[type];
  if (doc && doc.uploaded) {
    return 'REPLACE';
  }
  return 'UPLOAD';
}


const onAddDocumentClicked = (tabIndex, type, visaParams, payRefId, externalRefId, name, savedDocs, RefreshPressed, item) => {
  let doc = savedDocs[type];
  if (doc && doc.uploaded) {
    onRemoveDocumentClicked(tabIndex, type, savedDocs, visaParams);
  }
    const params = getUpdatedVisaParams(tabIndex, true, visaParams, payRefId, externalRefId, name, savedDocs);
    params.type = type; // this type prop will be used in single file upload
    scanDocuments(params, PAGENAME.DOCUMENT_REQUIRED);
    RefreshPressed();
    VisaNavigation.pop();
};

const onAttachDocumentClicked = (tabIndex, type) => {
  const { documents } = this.state;
  const doc = documents[tabIndex][type];
  this.tagClickEvents(`attach_${doc.name}`);

  this.setState({
    viewState: ViewState.LOADING,
    loaderText: 'Uploading Voucher...',
  });
  const params = this.getUpdatedVisaParams(tabIndex);
  params.type = type; // this type prop will be used in single file upload
  VisaModule.attachDocument(params);
};

const AddPassportPhoto = ({
  isError,
  isSuccess,
  title,
  subTitle,
  type,
  item,
  sampleImage,
  savedDocs,
  tabIndex,
  visaParams,
  payRefId,
  externalRefId,
  name,
  base64Icon,
  handleSampleOverlay,
  base64Iconfront,
  RefreshPressed
}) => {
  return (
    <View
      style={[
        styles.addPassportPhotoWrapper
      ]}>
      <AddPassPhotoHeader title={title} subTitle={subTitle} isError={isError} isSuccess={getSuccess(item, savedDocs, type)} />
      {imageURLexists(item, savedDocs, type) && (type === "PASSPORT_FRONT") && <View style={styles.middleSectioAPP}><Image
        style={styles.passportStyle}
        source={{ uri: base64Iconfront }}
      /></View>
      }
      {imageURLexists(item, savedDocs, type) && (type === "PASSPORT_BACK") && <View style={styles.middleSectioAPP}><Image
        style={styles.passportStyle}
        source={{ uri: base64Icon }}
      /></View>
      }
      {imageURLexists(item, savedDocs, type) && (type === "IDENTITY" || type === "PHOTOGRAPH") && <View style={styles.middleSectioAPP}><Image
        style={styles.profileImgStyle}
        source={{ uri: base64Icon }}
      />
      </View>
      }
      {!imageURLexists(item, savedDocs, type) && getSImagePath(sampleImage, type) && !(type === "IDENTITY" || type === "PHOTOGRAPH") &&
        <View style={styles.middleSectioAPP}>
          <Image
            style={styles.profileImgStyle}
            source={getSImagePath(sampleImage, type)}
          />
        </View>
      }
      {!imageURLexists(item, savedDocs, type) && getSImagePath(sampleImage, type) && (type === "IDENTITY" || type === "PHOTOGRAPH") &&
        <View style={styles.middleSectioAPP}>
          <View style={styles.profileImgWrapper}>
            <Image
              style={styles.profileImgStyle}
              source={getSImagePath(sampleImage, type)}
            />
            <View style={{ position: 'relative' }}>
              <TouchableOpacity style={styles.sampleIconWrapper} onPress={() => handleSampleOverlay('sample')}>
                <Image style={styles.sampleIconStyle} source={getImagePath('sample')} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      }
      {!isEmpty(getErrorMessage(item)) && <ErrorMessage
        warning={false}
        title={VISA_ERRORMESSAGES.IMAGE_ERROR}
        msg={getErrorMessage(item)}
      />}
      {!isEmpty(getWarningMessage(item)) && <ErrorMessage
        warning={true}
        title={VISA_ERRORMESSAGES.WARNING_ERROR}
        msg={getWarningMessage(item)}
      />}
      <View style={styles.BtnSectionAPP}>
        <Button
          buttonSize="xl"
          buttonText={getButtonName(savedDocs, type)}
          buttonType="outline"
          customStyle={{
            buttonTextStyle: [AtomicCss.azure, AtomicCss.blackFont, AtomicCss.font16],
            buttonWrapperStyle: {
              width: '48%',
            },
          }}
          onButtonPress={() => onAddDocumentClicked(tabIndex, type, visaParams, payRefId, externalRefId, name, savedDocs, RefreshPressed, item)}
          startIcon={getImagePath('uploadIcon')}
        />
          < Button
            buttonSize="xl"
            buttonText="CAMERA"
            buttonType="outline"
            customStyle={{
              buttonTextStyle: [AtomicCss.azure, AtomicCss.blackFont, AtomicCss.font16],
              buttonWrapperStyle: {
                width: '48%',
              },
            }}
            onButtonPress={() => onAddDocumentClicked(tabIndex, type, visaParams, payRefId, externalRefId, name, savedDocs, RefreshPressed, item)}
            startIcon={getImagePath('cameraIcon')}
          />
      </View>
    </View>
  );
};

export default AddPassportPhoto;

const styles = StyleSheet.create({
  addPassportPhotoWrapper: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  middleSectioAPP: {
    height: 240,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#eaf5ff',
  },
  errorContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  BtnSectionAPP: {
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  playBtnWrapper: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#008CFF',
    backgroundColor: '#fff',
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    justifyContent: 'center',
  },
  playIconStyle: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  passportStyle: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  errorBorder: {
    borderColor: 'red',
  },
  profileWrapper: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 4,
    justifyContent: 'center',
  },
  profileImgWrapper: {
    width: 145,
    height: 135,
    justifyContent: 'center',
  },
  profileImgStyle: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
  },
  sampleIconWrapper: {
    width: 42,
    height: 30,
    marginTop: -30,
    alignSelf: 'center'
  },
  sampleIconStyle: {
    width: 42,
    height: 38,
  }
});
