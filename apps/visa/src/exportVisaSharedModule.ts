import VisaSharedModuleHolder, { VisaImageDownloadCallback, VisaSharedUtils } from '@mmt/visa-shared/src';

export function exportVisaSharedModule() {
  VisaSharedModuleHolder.set({
    getVisaSharedUtils(): VisaSharedUtils {
      return {
        downloadImage(url: string, callback: VisaImageDownloadCallback) {
          const { downloadImage: original } = require('./VisaImageDownloader');
          return original(url, callback);
        },
      };
    },
  });
}
