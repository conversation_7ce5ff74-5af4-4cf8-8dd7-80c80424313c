import visaRouteConfig from '../src/Navigation/visaRouteConfig'; //'./VisaRevamp/Navigation/visaRouteConfig';
import store from '@mmt/legacy-commons/AppState/Store';
import { exportVisaSharedModule } from './exportVisaSharedModule';

const visaModule = {
  id: '@mmt-rn/visa',
  routeConfig: visaRouteConfig,
  onMount: () => {
    // @ts-ignore //TODO will fix this while refactoring redux store implementation
    store.addReducers(require('./VisaRevamp/Reducers').default);
    store.addReducers(require('./visaReducers').default);
  },
  onUnmount: () => {},
  onBootstrap() {
    exportVisaSharedModule();
  },
};

export default visaModule;
