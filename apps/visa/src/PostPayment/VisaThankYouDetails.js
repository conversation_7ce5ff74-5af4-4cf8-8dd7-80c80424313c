import React from 'react';
import PropTypes from 'prop-types';
import {View, Text} from 'react-native';
import styles from '../css/VisaMainCss';
import {getDisplayDate} from '../VisaUtils';


const VisaThankYouDetails = (props) => {
  const {
    country,
    visaType,
    visaValidity,
    totalPax,
    travelStartDate,
    travelEndDate,
    travelDateDiff,
    name
  } = props;

  return (
    <View style={[styles.ThankYouWhiteCard, styles.mar10Bt]}>
      <View style={styles.mar25Bt}>
        <Text style={[styles.LightText, styles.font12, styles.mar3Bt, styles.regularFont]}>Visa Application Submitted for</Text>
        <Text style={[styles.BlackText, styles.font18, styles.regularFont, styles.pad2Top]}><Text style={styles.boldFont}>{country}</Text></Text>
      </View>
      <View style={[styles.thankyouApplyVisaDtls, styles.flexRow, styles.mar15Bt]}>
        <Text style={[styles.LightText, styles.font12, styles.mar5Rt, styles.lineHeight18, styles.regularFont]}>VISA TYPE</Text>
        <Text style={[styles.DefaultText, styles.font12, styles.blackFont, styles.lineHeight16, styles.pad2Top]}>{visaType} </Text>
      </View>
      <View style={[styles.thankyouApplyVisaDtls, styles.flexRow, styles.mar15Bt, styles.alignCenter]}>
        <Text style={[styles.LightText, styles.font12, styles.mar5Rt, styles.lineHeight18, styles.regularFont]}>VALIDITY</Text>
        <Text style={[styles.DefaultText, styles.font12, styles.blackFont, styles.lineHeight16, styles.pad2Top]}>{visaValidity} </Text>
        <Text style={[styles.font12, styles.DefaultText, styles.regularFont, styles.pad4Top]}>(from the date of issue)</Text>
      </View>
      <View style={[styles.thankyouApplyVisaDtls, styles.flexRow, styles.mar15Bt]}>

        <Text style={[styles.LightText, styles.font12, styles.mar5Rt, styles.lineHeight18, styles.regularFont]}>TRAVELLERS</Text>
        <Text style={[styles.DefaultText, styles.font12, styles.blackFont, styles.lineHeight16, styles.pad2Top]}>{totalPax} </Text>
        <Text style={[styles.font12, styles.DefaultText, styles.regularFont, styles.pad4Top]}>({name} {totalPax - 1 > 0 ? `+${totalPax - 1}` : ''})</Text>
      </View>
      <View style={[styles.thankyouApplyVisaDtls, styles.flexRow, styles.mar15Bt]}>
        <Text style={[styles.LightText, styles.font12, styles.mar5Rt, styles.lineHeight18, styles.regularFont]}>TRAVEL</Text>
        <Text style={[styles.DefaultText, styles.font12, styles.blackFont, styles.lineHeight16, styles.pad2Top]}>{getDisplayDate(travelStartDate)} - {getDisplayDate(travelEndDate)} </Text>
        <Text style={[styles.font12, styles.DefaultText, styles.regularFont, styles.pad4Top]}>({travelDateDiff} days)</Text>

      </View>
      <View />
    </View>
  );
};

VisaThankYouDetails.propTypes = {
  country: PropTypes.string.isRequired,
  visaType: PropTypes.string.isRequired,
  visaValidity: PropTypes.string.isRequired,
  totalPax: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  travelEndDate: PropTypes.string.isRequired,
  travelStartDate: PropTypes.string.isRequired,
  travelDateDiff: PropTypes.string.isRequired
};

export default VisaThankYouDetails;
