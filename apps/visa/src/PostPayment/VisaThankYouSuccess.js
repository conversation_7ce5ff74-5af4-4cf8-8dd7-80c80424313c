import React, {Component} from 'react';
import {View, ScrollView, Text, Image, TouchableOpacity, Platform} from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import whiteBackArrowIcon from '@mmt/legacy-assets/src/white_backarrow.webp';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import successIcon from '@mmt/legacy-assets/src/ic_payment_successfull.webp';
import paymentIcon from '@mmt/legacy-assets/src/ic_payment_icon.webp';
import styles from '../css/VisaMainCss';
import VisaThankYouDetails from './VisaThankYouDetails';
import VisaThankYouProgress from './VisaThankYouProgress';
import {formatAmount, getDisplayDate} from '../VisaUtils';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import {TRACKING_PAGENAME} from '../VisaConstants';
import * as NpsModule from '@mmt/legacy-commons/Native/NpsModule';
import { logPdtActionEvent } from '../Analytics/Pdt/PdtTracker';
import { PDT_EVENT_NAME, PDT_PAGENAME } from '../Analytics/Pdt/PdtConstants';
import VisaDataHolder from '../VisaDataHolder';

class VisaThankYouSuccess extends Component {
  static navigationOptions = {
    header: null
  }

  constructor(props) {
    super(props);
    this.booking = this.props.booking;
    this.visaAssistance = false;
  }

  populatePdtData(thankYouResponse, visaInformation){
    const paymentDetails = {
      pay_ref_id : thankYouResponse?.payRefId,
      total_amount : thankYouResponse?.totalAmount,
      traveller_count : thankYouResponse?.travellers?.length,
      country : visaInformation?.country,
      date_of_entry : visaInformation?.dateOfEntry,
      date_of_exit : visaInformation?.dateOfExit,
      processing_time : visaInformation?.processingTime,
      is_payment_success : thankYouResponse?.paymentSuccess
    }
    const eventDetails = {
      destination:    visaInformation?.country,
      departure_date: visaInformation?.dateOfEntry,
      return_date:    visaInformation?.dateOfEntry,
      travellers_count: thankYouResponse?.travellers?.length,
      locus_code: visaInformation?.locusCode,
      locus_type: visaInformation?.locusType,
      visa_type: visaInformation?.visaBookingType,
      currency : 'INR' // May need to set currency from api response
    }
    const travellersData = []
    thankYouResponse?.travellers?.forEach((item) => {
      const name = item.name.split(" ");
      travellersData.push({
        firstName: name[0],
        lastName: name[1],
        paxType: item.paxIndex,
        primary: item.primary || false,
        gender: item.gender,
        email: item.email,
        phoneNumber: item.phoneNumber,
      });
    });
    const bookingTimeStamp = VisaDataHolder.getInstance().getBookingTimeStamp();
    const bookingDetails = {
      booking_created_timestamp: bookingTimeStamp,
      total_amount: thankYouResponse?.totalAmount,
      booking_id: thankYouResponse?.bookingId
    };
    const pdtData = { travellersData, bookingDetails, paymentDetails, eventDetails }
    return pdtData
  }
  getBackButtonStyle = () => {
    if (Platform.OS === 'ios') {
      return styles.whiteTint;
    }
    return styles.Wtbackarrow
  }

  render() {
    if(this.props.thankYouResponse && this.props.thankYouResponse.visaInformation){
        this.visaAssistance = this.props.thankYouResponse.visaInformation.visaBookingType === 1
    }
    this.showNpsLayout();
    return (
      <View style={styles.VisaMainContainer}>
        <ScrollView>

          <LinearGradient start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} colors={['#3bd5f6', '#5998ee']} style={styles.ThankYouTopGradient}>
            <View style={styles.MakeRelative}>
              <View>
                <TouchableOpacity
                  onPress={this.props.onBackPressed}
                  style={[styles.backArrowWrapper, styles.padding5]}
                >
                  <Image style={this.getBackButtonStyle()} source={Platform.OS === 'ios' ? backIcon : whiteBackArrowIcon} />
                </TouchableOpacity>
              </View>
              <View style={[styles.flex1, styles.alignCenter]}>
                <Image style={[styles.thankuSuccessIcon, styles.mar15Bt]} source={successIcon} />
                <Text style={[styles.font18, styles.WhiteText, styles.blackFont, styles.bgTransparent, styles.mar3Bt]}>Visa Payment Successful</Text>
                {!this.visaAssistance?<Text style={[styles.font14, styles.WhiteText, styles.regularFont, styles.bgTransparent, styles.mar5Bt, styles.AlignCenter]}>Documents will be sent to {'\n'}embassy on {getDisplayDate(this.booking.applicationDate)}</Text>:<Text style={[styles.font14, styles.WhiteText, styles.regularFont, styles.bgTransparent, styles.mar5Bt, styles.AlignCenter]}>Visa application should be sent to {'\n'}embassy on {getDisplayDate(this.booking.applicationDate)}</Text>}
                <View style={styles.flexRow}>
                  <Text style={[styles.font12, styles.WhiteText, styles.lightFont, styles.bgTransparent]}>BOOKING ID: </Text>
                  <Text style={[styles.font12, styles.WhiteText, styles.boldFont, styles.bgTransparent]}> {this.booking.bookingId}</Text>
                </View>
              </View>
            </View>
          </LinearGradient>
          <VisaThankYouDetails {...this.props.booking} />

          {this.props.isValidResponse?<VisaThankYouProgress {...this.props.booking} thankYouResponse={this.props.thankYouResponse} />:null}

          <View style={[styles.ThankYouWhiteCard, styles.flexRow, styles.PaymentInfoCard]}>
            <Image style={[styles.PaymentIcon, styles.mar10Rt]} source={paymentIcon} />
            <View style={[styles.TotalPaymentDtls, styles.flexRow, styles.spaceBetween]}>
              <View>
                <Text style={[styles.blackFont, styles.DefaultText, styles.mar3Bt]}>TOTAL PRICE</Text>
              </View>
              <View>
                <Text style={[styles.blackFont, styles.DefaultText, styles.font16]}>{formatAmount(this.booking.amountPaid)}</Text>
              </View>
            </View>
          </View>

          <View style={{paddingHorizontal: 14, paddingVertical: 10}}>
            <TouchableOpacity onPress={this.viewVisaApplication}>
              <LinearGradient
                style={{borderRadius: 3}}
                start={{x: 1.0, y: 0.0}}
                end={{x: 0.0, y: 1.0}}
                colors={['#065af3', '#53b2fe']}
              >
                <Text style={styles.BtnText}> VIEW VISA APPLICATION </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>

        </ScrollView>
      </View>
    );
  }

  viewVisaApplication = () => {
    if(this.props.thankYouResponse && this.props.thankYouResponse.visaInformation){
      const {thankYouResponse} = this.props;
      const {visaInformation} = this.props.thankYouResponse
      const pdtData = this.populatePdtData(thankYouResponse, visaInformation)
      logPdtActionEvent(PDT_EVENT_NAME.VIEW_VISA_APPLICATION, { pageName: PDT_PAGENAME.THANK_YOU, data:pdtData });
    }
    VisaTrackingHelper.trackVisaClickEvent(TRACKING_PAGENAME.THANK_YOU, 'mytrips_clicked');
    VisaModule.openMyTripsHomePage();
  }

  showNpsLayout = () => {
    if (Platform.OS === 'android') {
      const params = {
        pageName: TRACKING_PAGENAME.THANK_YOU[0],
        bookingId: String(this.booking.bookingId)
      };
      VisaModule.showNps(params);
    }else if (Platform.OS === 'ios') {
      NpsModule.showNps(this.booking.bookingId, NpsModule.NpsParams.VISA);
    }
  };
}

VisaThankYouSuccess.propTypes = {
  booking: PropTypes.object.isRequired,
  onBackPressed: PropTypes.func.isRequired
};


export default VisaThankYouSuccess;
