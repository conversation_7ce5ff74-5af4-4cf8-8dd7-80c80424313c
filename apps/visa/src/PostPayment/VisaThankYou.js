import React from 'react';
import {Platform} from 'react-native';
import _ from 'lodash';
import {Actions,ActionConst} from 'react-native-router-flux';
import PropTypes from 'prop-types';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import VisaThankYouFailure from './VisaThankYouFailure';
import VisaThankYouSuccess from './VisaThankYouSuccess';
import {PAYMENT_STATUS, TRACKING_PAGENAME} from '../VisaConstants';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import {invalidateTrips} from '@mmt/legacy-commons/Helpers/genericHelper';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';
import { getThankYouResponse, getVisaReviewResponseV2 } from '../VisaNetworkUtils';
import { isVisaResponseValid } from '../VisaUtils';

class VisaThankYou extends BasePage {
    static navigationOptions = {
      header: null
    }

    constructor(props) {
      super(props, 'visaThankYou');
      this.paymentResponse = this.props.paymentResponse;
      this.paymentFailed = false;
      this.previousPage = 'fromReview';
      this.state = {
        isValidResponse: false,
        thankYouResponse: null,
      }
    }

    componentDidMount() {
      this.fetchThankYouResponse()
    }

    async fetchThankYouResponse(){
        const response = await getThankYouResponse(this.props.paymentResponse.bookingId);
        if (response) {
          if (isVisaResponseValid(response)) {
            this.setState({
              thankYouResponse: response,
              isValidResponse: true,
            })
          }
        }
    }

  onHardBackPress = () => {
      if (this.paymentFailed) {
        VisaNavigation.pop();
      } else {
        VisaNavigation.replace(VISA_ROUTE_KEYS.LANDING_NEW)
      }
      return true;
    };

    render() {
      if (Platform.OS === 'android') {
        this.booking = JSON.parse(this.paymentResponse.response);
      } else {
        this.booking = this.paymentResponse;
      }

      this.paymentFailed = _.isEmpty(this.booking) || this.booking.status === PAYMENT_STATUS.FAILURE;
      if (this.paymentFailed) {
        VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
          TRACKING_PAGENAME.THANK_YOU_ERROR,
          this.previousPage, this.visaParams
        );
        const response = _.isEmpty(this.booking) ? this.paymentResponse : this.booking;
        return (<VisaThankYouFailure
          data={response}
          onBackPressed={this.onHardBackPress}
        />);
      }
      invalidateTrips();
      VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
        TRACKING_PAGENAME.THANK_YOU,
        this.previousPage, this.visaParams
      );
      return (<VisaThankYouSuccess
        booking={this.booking}
        onBackPressed={this.onHardBackPress}
        thankYouResponse={this.state.thankYouResponse}
        isValidResponse={this.state.isValidResponse}
      />);
    }
}

VisaThankYou.propTypes = {
  paymentResponse: PropTypes.object.isRequired
};

VisaThankYou.contextTypes = {
  resetRouter: PropTypes.func
};


export default VisaThankYou;
