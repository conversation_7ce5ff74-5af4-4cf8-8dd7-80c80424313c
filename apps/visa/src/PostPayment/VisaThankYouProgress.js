import React from 'react';
import {View, Text, Image} from 'react-native';
import trackerCheckIcon from '@mmt/legacy-assets/src/tracker_check.webp';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';


import {
  getCurrentDate,
  getDisplayDate
} from '../VisaUtils';

const VisaThankYouProgress = (props) => {
  const {
    receivingDate,
    applicationDate,
    countryCode
  } = props;

  const {
    documentDeliveryMode,
    visaBookingType
  } = props.thankYouResponse.visaInformation;
  const documentStatus = props.thankYouResponse.documentStatus;
  const visaAssistance = visaBookingType === 1;
  const prePostBooking = documentDeliveryMode !== 0 || (documentDeliveryMode === 0 && documentStatus === 'DOCUMENTS_PENDING');
  return (
    <View style={[styles.MamiCard, styles.mar10Bt]}>
      <View style={styles.CardContentTracker}>
        {visaAssistance?<Text style={[styles.blackFont, styles.font14, styles.BlackText, styles.mar5Bt, styles.padding15Left]}>
          ON TRACK - VISA ASSISTANCE AVAILABLE
        </Text>:<Text style={[styles.blackFont, styles.font14, styles.BlackText, styles.mar5Bt, styles.padding15Left]}>
              ON TRACK - VISA EXPECTED BY {getDisplayDate(receivingDate)}{prePostBooking?<Text>*</Text>:''}
        </Text>}
        <View style={styles.trackerContainer}>
          <View style={styles.trackerSubContainer}>
            <View style={styles.trackingBall}>
              <Image style={styles.trackerCheck} source={trackerCheckIcon} />
            </View>
            <View style={styles.visaTrackerText}>
              <View>
                {prePostBooking?<Text style={[styles.font12, styles.darkGrey, styles.boldFont, styles.mar3Bt]}>
                    Booking done on {getCurrentDate()}
                </Text>:<Text style={[styles.font12, styles.darkGrey, styles.boldFont, styles.mar3Bt]}>
                  Documents received on {getCurrentDate()}
                  </Text>}
                {prePostBooking?<Text style={[styles.font12, styles.darkGrey, styles.regularFont, styles.mar3Bt]}>
                  Kindly submit the required documents.
                </Text>:null}
                {visaAssistance?<Text style={[styles.font12, styles.darkGrey, styles.regularFont, styles.mar3Bt]}>
                  We will review the documents once submitted and aid you accordingly.
                </Text>:null}
              </View>
            </View>
          </View>
          <View style={styles.trackerSubContainer}>
            <View style={styles.trackingBall}>
              <Text style={[styles.darkGrey2, styles.regularFont]}>2</Text>
            </View>
            <View style={[styles.visaTrackerText, styles.visaTrackerTextlightCream]}>
              <View style={[styles.flexRow, styles.marL20Neg]}>
                <Image style={styles.tipIcon} />
                <View style={styles.lightCreamActiveBg}>
                  {visaAssistance?<Text style={[styles.font12, styles.darkGrey, styles.boldFont, styles.mar3Bt, styles.activeText]}>
                    Visa should be sent to embassy on {getDisplayDate(applicationDate)}
                  </Text>:<Text style={[styles.font12, styles.darkGrey, styles.boldFont, styles.mar3Bt, styles.activeText]}>
                      Visa will be sent to embassy on {getDisplayDate(applicationDate)}
                  </Text>}
                  {prePostBooking && !visaAssistance?<Text style={[styles.font12, styles.defaultGrey, styles.regularFont, styles.mar3Bt, styles.activeTextStatus]}>
                    Visa may be delayed if documents are not submitted on time and/or the embassy requires additional documents.
                  </Text>:null}
                  {visaAssistance?<Text style={[styles.font12, styles.defaultGrey, styles.regularFont, styles.mar3Bt, styles.activeTextStatus]}>
                    We will assist you with the application process.
                  </Text>:null}
                </View>
              </View>
            </View>
          </View>
          <View style={styles.trackerSubContainer}>
            <View style={[styles.trackingBall, styles.trackingBallGrey]}>
              <Text style={[styles.darkGrey2, styles.regularFont]}>3</Text>
            </View>
            <View style={[styles.visaTrackerTextGrey]}>
              <View>
                <View>
                  {visaAssistance?<Text style={[styles.font12, styles.darkGrey, styles.boldFont, styles.mar3Bt]}>
                    Visa to be expected by {getDisplayDate(receivingDate)}
                  </Text>:<Text style={[styles.font12, styles.darkGrey, styles.boldFont, styles.mar3Bt]}>
                      Visa expected by {getDisplayDate(receivingDate)}{prePostBooking?<Text>*</Text>:''}
                  </Text>}
                  {/* if dateOfEntry > 30 days from current date */}
                  {prePostBooking && !visaAssistance?<Text style={[styles.font12, styles.defaultGrey, styles.regularFont, styles.mar3Bt]}>
                    * The Visa will be available by the above-mentioned date if the documents are submitted today and/or no additional documents are required by the embassy.
                  </Text>:null}
                  {visaAssistance?<Text style={[styles.font12, styles.defaultGrey, styles.regularFont, styles.mar3Bt]}>The Visa will be available by the above-mentioned date if Visa is applied by {getDisplayDate(applicationDate)} and/or no additional documents are required by the embassy.</Text>:!prePostBooking?<Text style={[styles.font12, styles.defaultGrey, styles.regularFont, styles.mar3Bt]}>
                    Visa may be delayed if additional documents are required by the embassy.
                  </Text>:null}
                </View>
              </View>
            </View>
          </View>
          { countryCode === 'UAE' &&
            <View style={styles.trackerSubContainer}>
              <View style={[styles.trackingBall, styles.trackingBallGrey]}>
                <Text style={[styles.darkGrey2, styles.regularFont]}>4</Text>
              </View>
              <View style={styles.visaTrackerTextLast}>
                <View>
                  <Text style={[styles.font12, styles.darkGrey, styles.boldFont, styles.mar3Bt]}>
                      Okay To Board check
                  </Text>
                  <Text style={[styles.font12, styles.darkGrey, styles.regularFont, styles.mar3Bt]}>
                      If airline requires okay to board, our agents will process the same.
                  </Text>
                </View>
              </View>
            </View>
        }
        </View>
      </View>
    </View>
  );
};

VisaThankYouProgress.propTypes = {
  receivingDate: PropTypes.string.isRequired,
  applicationDate: PropTypes.string.isRequired,
  countryCode: PropTypes.string.isRequired
};

export default VisaThankYouProgress;
