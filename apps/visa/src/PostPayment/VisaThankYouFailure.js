import React from 'react';
import {View, ScrollView, Text, Image, TouchableOpacity, Platform} from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import arrowIcon from '@mmt/legacy-assets/src/white_backarrow.webp';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import paymentIcon from '@mmt/legacy-assets/src/ic_payment_icon.webp';
import paymentFailedIcon from '@mmt/legacy-assets/src/ic_payment_failed.webp';
import styles from '../css/VisaMainCss';
import {formatAmount} from '../VisaUtils';
class VisaThankYouFailure extends React.Component {
  static navigationOptions = {
    header: null
  }

  constructor(props) {
    super(props);
    this.response = this.props.data;
  }

  getBackButtonStyle = () => {
    if (Platform.OS === 'ios') {
      return styles.whiteTint;
    }
    return styles.Wtbackarrow;
  }

  render() {
    return (
      <View style={styles.VisaMainContainer}>
        <ScrollView>
          <LinearGradient start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} colors={['#ff3e5e', '#ff7f3f']} style={styles.ThankYouTopGradient}>
            <View style={styles.MakeRelative}>
              <TouchableOpacity
                onPress={this.props.onBackPressed}
                style={[styles.backArrowWrapper, styles.padding5]}
              >
                <Image style={this.getBackButtonStyle()} source={Platform.OS === 'ios' ? backIcon : arrowIcon} />
              </TouchableOpacity>
              <View style={[styles.flex1, styles.alignCenter]}>
                <Image style={[styles.thankuFailedIcon, styles.mar15Bt]} source={paymentFailedIcon} />
                <Text style={[styles.font18, styles.WhiteText, styles.blackFont, styles.bgTransparent, styles.mar3Bt]}>Your booking wasn't completed</Text>
                <Text style={[styles.font14, styles.WhiteText, styles.bgTransparent, styles.mar5Bt, styles.regularFont, styles.AlignCenter, styles.lineHeight20]}>Don’t worry we’ll refund the full amount {'\n'} of {formatAmount(this.response.amountPaid)} to your account.</Text>
              </View>
            </View>
          </LinearGradient>

          <View style={[styles.ThankYouWhiteCard, styles.flexRow, styles.PaymentInfoCard]}>
            <Image style={[styles.PaymentIcon, styles.mar10Rt]} source={paymentIcon} />
            <View style={[styles.TotalPaymentDtls, styles.flexRow, styles.spaceBetween]}>
              <View>
                <Text style={[styles.blackFont, styles.DefaultText, styles.mar3Bt]}>TOTAL PRICE</Text>
              </View>
              <View>
                <Text style={[styles.blackFont, styles.DefaultText, styles.font16]}>{formatAmount(this.response.amountPaid)} </Text>
              </View>
            </View>
          </View>


          <View style={styles.applyVisaProcessWrapper}>
            <Text style={[styles.applyVisaProcessDtls, styles.boldFont]}>Payment failed due to a technical error. Your application has been saved, and you can instantly re-apply.  </Text>
          </View>
          <View style={{paddingHorizontal: 14, paddingVertical: 10}}>
            <TouchableOpacity>
              <LinearGradient style={{borderRadius: 3}} start={{x: 1.0, y: 0.0}} end={{x: 0.0, y: 1.0}} colors={['#065af3', '#53b2fe']}>
                <Text style={styles.BtnText}> VIEW VISA APPLICATION </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    );
  }
}

VisaThankYouFailure.propTypes = {
  data: PropTypes.object.isRequired,
  onBackPressed: PropTypes.func.isRequired
};

export default VisaThankYouFailure;

