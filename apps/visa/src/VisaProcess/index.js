import React from 'react';
import {
  View,
  ScrollView,
  Text,
  StyleSheet,
  Image
} from 'react-native';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import PropTypes from 'prop-types';
import StickyHeaderFaqs from '../DocumentInfo/StickyHeaderFaqs';
import {PAGENAME, TRACKING_PAGENAME} from '../VisaConstants';
import VisaSteps from './VisaSteps';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import { VisaNavigation } from '../Navigation';
import { logPdtActionEvent } from '../Analytics/Pdt/PdtTracker';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { PDT_EVENT_NAME, PDT_PAGENAME } from '../Analytics/Pdt/PdtConstants';
import CardSeperator from '@mmt/legacy-assets/src/holidays/hol-card-seperator.png';
 
export default class VisaProcess extends BasePage {
  constructor(props) {
    super(props, PAGENAME.VISA_PROCESS);
  }
 
  static navigationOptions = {
    header: null
  };
  componentDidMount(){
    super.componentDidMount();
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(TRACKING_PAGENAME.DETAIL_VISA_STEPS,this.props.visaParams.page, this.props.visaParams);
  }
  onCrossPressed = () => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.DETAIL_VISA_STEPS,
      'back_clicked', this.props.visaParams, this.props.visaParams.page
    );
    logPdtActionEvent(PDT_EVENT_NAME.APP_BACK_CLICK,{pageName: PDT_PAGENAME.DETAILS} )
    VisaNavigation.pop();
  }
 
  render() {
    return (
      
      <View style={styles.stepCard}>
        <StickyHeaderFaqs closeFn={this.onCrossPressed} title={'Visa Process'}/>
      
        <View style={styles.contentWrapper}>
        <View style={styles.divider}>
        <Text style={[AtomicCss.font16,AtomicCss.lineHeight20, AtomicCss.boldFont, AtomicCss.blackText, AtomicCss.marginBottom8]}>{this.props.visaProcessHeader} {this.props.visaParams.destCountry}</Text>
        <Text style={[AtomicCss.font12, AtomicCss.lineHeight15, AtomicCss.regularFont, AtomicCss.defaultText]}>{this.props.visaProcessSubTitle1} <Text style={[styles.greenText, AtomicCss.boldFont]}>{this.props.visaProcessSubTitle2}</Text></Text>
        <Image source = {CardSeperator} style = {styles.image}/>
      </View>
       <ScrollView>
          <VisaSteps
            dest={this.props.visaParams.destCountry}
            processArray={this.props.processList}
            key="visaAppProcess"
          />
        </ScrollView>
        </View>
        </View>
   
      
    );
  }
}
 
VisaProcess.propsTypes = {
  visaParams: PropTypes.object.isRequired, // Tracking
  processList: PropTypes.array.isRequired
};
const styles = StyleSheet.create({
 
  stepCard: {
    backgroundColor: 'white',
    height:'100%',
},
  contentWrapper: {
    paddingHorizontal:24,
    paddingVertical: 24,
    fontWeight:400,
},
divider: {
    paddingBottom:11,
},
image : {
  height: 1,
  tintColor : '#d8d8d8',
  resizeMode : 'cover',
  width: '100%',
  marginTop:15,
  marginBottom:5,
},
  greenText: {
    color: '#007E7D',
    fontWeight: 'bold',
  },
  
});