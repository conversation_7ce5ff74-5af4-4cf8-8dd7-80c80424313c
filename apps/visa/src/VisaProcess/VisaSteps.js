import React from 'react';
import {View, Text, Image, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';
import VisaMainCss from '../css/VisaMainCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
export default class VisaSteps extends React.Component {
  static navigationOptions = {
    header: null
  };

  renderProcessSteps = () => {
    const processes = [];
    if (this.props.processArray) {
      this.props.processArray.forEach((item) => {
        processes.push(this.processStepByStepView(item));
      });
    }
    const sortedProcessArray = [...this.props.processArray].sort((a, b) => a.step - b.step);

    return sortedProcessArray.map((item, index) => this.processStepByStepView(item, index + 1));
  };

  processStepByStepView = (item, index) => (
    <View key={item.step} style={styles.stepRow}>
    <View style={styles.bulletCircle}><Text style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.blackText, styles.marginBot2]}>{index}</Text></View>
    <View style={AtomicCss.flex1}>
            <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.blackText, AtomicCss.marginBottom3]}>{item.headerText}</Text>
            <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.defaultText]}>{item.description}</Text>
    </View>
    </View>

    
  );
  
  render() {
    return (
      <View style={styles.container}>
        {
            this.renderProcessSteps()
        }
      </View>
    );
  }
}
const styles = StyleSheet.create({
  bulletCircle: {
    width:20,
    height:20,
    borderWidth:1,
    borderColor:'#000000',
    borderRadius: 50,
    alignItems:'center',
    justifyContent:'center',
    marginRight:8,
    marginTop:2,
},
stepRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom:16,
},
  stepImg: {
    width: 40,
    height: 40
  }
});
VisaSteps.propTypes = {
  dest: PropTypes.string.isRequired,
  processArray: PropTypes.array.isRequired
};
