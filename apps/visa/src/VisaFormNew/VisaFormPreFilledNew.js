import React from 'react';
import {View, ScrollView, Text, ActivityIndicator, Keyboard, findNodeHandle, Platform} from 'react-native';
import {KeyboardAwareView} from 'react-native-keyboard-aware-view';
import {set, isEmpty, filter, cloneDeep} from 'lodash';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import VisaFormHeaderNew from './VisaFormHeaderNew';
import VisaFormContainerNew from './VisaFormContainerNew';
import VisaFormButtonNew from './VisaFormButtonNew';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import ViewState from './ViewStateNew';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {PAGENAME, TRACKING_PAGENAME, FORM_DATA_TYPE_NEW, STATUS_CODE_SUCCESS, INT_MAX_VAL, PARENTCURLYBRACES, PROMPT_OVERLAY_POSITIVE, PASSPORT_DATA_VERIFY_TEXT} from '../VisaConstants';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import VisaFormDropdownSelectorNew from './VisaFormDropdownSelectorNew';
import VisaFormOverlayNew from './VisaFormOverlayNew';
import {getSchemaApplicationFormResponse, getUpdatedField} from '../VisaNetworkUtils'


import {
  isResponseValid,
  isVisaResponseValid,
  validateDynamicForm,
  redirectToFormPreview,
  getHypotheticalParentNode,
  isNextKeyGood,
  getScreenTitleForEmptyTraveller,
  formatNavSubTitle,
  getDisplayDate
} from '../VisaUtils';
import { VisaNavigation } from '../Navigation';

class VisaFormPreFilledNew extends BasePage {
  static navigationOptions = {
    header: null
  };

  constructor(props) {
    super(props, 'VisaFormPreFilledNew');
    this.state = {
      viewState: ViewState.LOADING,
      loaderText: 'Loading visa application form...',
      validateForm: false,
      renderOverlay: false
    };

    this.scrollViewWindowOffset = 0;
    this.textInputRefs = {};
    this.scrollIndex = 0;
    this.scrollViewRef = null;
    this.scrollViewContentHeight = 0;

    this.requestsCount = 0;
    this.dropDownSelectedValue = '';
    this.fields = [];
    this.visaParams = {...this.props.visaParams, page: PAGENAME.APPLICATION_FORM_PREFILLED};
    this.visaFormResponse = {};
    this.isFormValid = true;
    this.previousPage = 'fromDocVerification';
    this.visaForm = {};
    this.params = {};
    this.paxName = getScreenTitleForEmptyTraveller(this.visaParams.paxList, this.visaParams.paxId);
  }

  onBackPressed = () => {
    VisaNavigation.pop();
    this.trackClickEvent('back_clicked');
  };


  componentDidMount() {
    super.componentDidMount();
    this.fetchVisaForm();
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
      TRACKING_PAGENAME.APPLICATION_FORM_PREFILLED,
      this.previousPage, this.visaParams
    );
  }

  componentDidUpdate() {
    if (this.scrollViewRef) {
      setTimeout(() => {
        this.scrollViewRef.scrollTo({x: 0, y: this.scrollIndex, animated: false});
      }, 1);
    }
  }

  async fetchPreProcessData(key, parent) {
    try {
      const url = this.visaForm.fields[key].url;
      const fieldParams = {
        visaform_updated_field_url: url.replace(PARENTCURLYBRACES, parent)
      };
      this.requestsCount += 1;
      const newJsonData = await getUpdatedField({...fieldParams});
      if (newJsonData.status === STATUS_CODE_SUCCESS) {
        const newJsonField = newJsonData.field;
        newJsonField.values.forEach((element) => {
          this.visaForm.fields[key].values.push(element);
        });
        Object.keys(newJsonField.dependents).forEach((_key) => {
          this.visaForm.fields[key].dependents[_key] = newJsonField.dependents[_key];
        });
        this.visaForm.fields[key].validations[parent] = newJsonField.validation;
        this.requestsCount -= 1;
      }
      if (this.requestsCount === 0) {
        this.setState({
          viewState: ViewState.SHOW_DETAIL
        });
      }
    } catch (e) {
      showShortToast('Unable to fetch data');
      VisaNavigation.pop();
    }
  }

  recursivelySetValue(parent, values) {
    if (!values) return;
    Object.keys(values).forEach((key) => {
      if (this.visaForm.fields[key] && this.visaForm.fields[key].validations[getHypotheticalParentNode(parent, this.visaForm.fields[key])] === undefined && this.visaForm.fields[key].url !== '') {
        this.fetchPreProcessData(key, getHypotheticalParentNode(parent, this.visaForm.fields[key]));
      }
      values[key].forEach((element) => {
        this.recursivelySetValue(element.value, element.dependents);
      });
    });
  }

  fetchDefaultValues = () => {



    Object.keys(this.visaForm.fields).forEach((key) => {
      const field = this.visaForm.fields[key];
      if (field.defaultValue !== '' && field.url !== '' && filter(field.values, row => row.key === field.defaultValue).length === 0) {
        this.fetchPreProcessData(key, getHypotheticalParentNode('', field));
      }
    });
  }

  preProcessJson = () => {
    this.fetchDefaultValues();
    Object.keys(this.visaForm.fields).forEach((elem) => {
      this.visaForm.fields[elem].validations = {};
      if (isEmpty(this.visaForm.fields[elem].values) && this.visaForm.fields[elem].url === '') {
        this.visaForm.fields[elem].validations[''] = this.visaForm.fields[elem].validation;
      }
      this.visaForm.fields[elem].values.forEach((element) => {
        this.visaForm.fields[elem].validations[element.parent] = this.visaForm.fields[elem].validation;
      });
    });
    this.recursivelySetValue('', this.visaForm.fieldValues);
  };

  async fetchVisaForm() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET
      });
      return;
    }

    try {
      let isValidResponse = false;
      const param = {};
      const response = await getSchemaApplicationFormResponse(this.visaParams.visaBookingId, this.visaParams.paxId);
      if (response && response.formSchema) {
        if (isVisaResponseValid(response)) {
          this.visaForm = response.formSchema;

          isValidResponse = true;
          this.preProcessJson();
          if (this.requestsCount === 0) {
            this.setState({
              viewState: ViewState.SHOW_DETAIL
            });
          }
        }
      }

      if (!isValidResponse) {
        showShortToast('Unable to fetch form data, Try again later');
        VisaNavigation.pop();
        return;
      }
    } catch (e) {
      showShortToast('Unable to form data, Try again later');
      VisaNavigation.pop();
    }
  }

  render() {
    return (
      <View style={{flex: 1}}>
        {this.state.viewState === ViewState.LOADING && this.renderProgressView()}
        {this.state.viewState === ViewState.LOADING_DROPDOWN_DATA && this.renderNewFieldLoader()}
        {this.state.viewState === ViewState.NO_INTERNET && this.renderNoNetworkView()}
        {this.state.viewState === ViewState.NO_INTERNET_WHILE_SELECTING_DROPDOWN && this.renderNoNetworkViewDropDown()}
        {this.state.viewState === ViewState.SHOW_SELECTOR && this.renderSelector()}
        {this.state.viewState === ViewState.SHOW_DETAIL && this.renderContent()}
      </View>
    );
  }

  renderNoNetworkViewDropDown = () => (
    <NoInternetView
      onRetry={() => {
        this.setState({
          viewState: ViewState.LOADING_DROPDOWN_DATA
        });
        this.fetchNewData();
      }}
    />
  )

  renderNoNetworkView = () => (
    <NoInternetView
      onRetry={() => {
        this.setState({viewState: ViewState.LOADING});
        this.fetchVisaForm();
      }}
    />
  );

  renderNewFieldLoader = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {'Loading Information'}
      </Text>
    </View>
  )

  renderProgressView = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {this.state.loaderText}
      </Text>
    </View>
  );

  updateChildValues = (key, val) => {
    set(this, key, val);
  }

  getNextFieldLabel = (label) => {
    let closestElemLabel = `${INT_MAX_VAL}`;
    Object.keys(this.textInputRefs).forEach((key) => {
      if (this.textInputRefs[key] && isNextKeyGood(key, closestElemLabel, label)) {
        closestElemLabel = key;
      }
    });
    if (closestElemLabel === `${INT_MAX_VAL}`) {
      return null;
    }
    return closestElemLabel;
  }

  focusOnNextField = (dataPacket) => {
    Object.keys(this.textInputRefs).forEach((key) => {
      if (this.textInputRefs[key] === null) {
        delete this.textInputRefs[key];
      }
    });
    const nextFieldLabel = this.getNextFieldLabel(dataPacket.labelIndex);
    if (nextFieldLabel === null) {
      Keyboard.dismiss();
    } else {
      this.textInputRefs[nextFieldLabel].focus();
      this.textInputRefs[nextFieldLabel].measureLayout(findNodeHandle(this.scrollViewRef), (ox, oy, width, heigth, px, py) => {
        this.scrollViewRef.scrollTo({x: 0, y: oy - heigth, animated: true});
      });
    }
  }

  renderContent = () => (
    <KeyboardAwareView
      animated
      style={[styles.whitebg, styles.flex1, styles.spaceBetween]}
    >
      <VisaFormHeaderNew
        onBackPressed={this.onBackPressed}
        subTitle={formatNavSubTitle(this.visaParams.destCountry, getDisplayDate(this.visaParams.startDate), this.paxName)}
      />
      <View style={[styles.lightYellow]}>
        <Text style={[styles.font14, styles.marL15, styles.mar10Top, styles.boldFont, styles.mar10Bt, styles.mar20Lt, styles.mar20Rt, styles.darkYellowText]}>{PASSPORT_DATA_VERIFY_TEXT}</Text>
      </View>
      <ScrollView
        onScroll={this.handleScroll}
        ref={(ref) => {
          this.scrollViewRef = ref;
        }}
        keyboardShouldPersistTaps="handled"
      >
        <VisaFormContainerNew
          focusOnNextField={this.focusOnNextField}
          textInputRefs={this.textInputRefs}
          visaForm={this.visaForm}
          validateForm={this.state.validateForm}
          updateChildValues={this.updateChildValues}
          setField={this.setField}
          showDropDown={this.showDropDown}
        />
        <VisaFormButtonNew
          buttonPress={this.onFormPreviewButtonPress}
          btntext={this.getButtonTitle()}
          enable
        />
      </ScrollView>
      {this.state.renderOverlay && this.renderOverlay()}
    </KeyboardAwareView>
  )

   handleScroll = (event) => {
     this.scrollIndex = event.nativeEvent.contentOffset.y;
     this.scrollViewContentHeight = event.nativeEvent.contentSize.height;
   }
   getButtonTitle = () => {
     return this.isFromAddDocuments() ? 'SAVE FORM' : 'PREVIEW FORM';
   }
  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.APPLICATION_FORM_PREFILLED,
      clickEvent, this.visaParams, this.previousPage
    );
  }

  doOverlayOperation = (operation) => {
    this.setState({
      renderOverlay: false
    });
    if (operation === PROMPT_OVERLAY_POSITIVE) {
      this.onFormPreviewOverlayPositivePressed();
    }
  }


  renderOverlay = () => (
    <VisaFormOverlayNew
      doOperation={this.doOverlayOperation}
      text="Before you proceed…"
      name={this.visaForm.fieldValues.firstName[0].value}
      surName={this.visaForm.fieldValues.lastName[0].value}
      positiveButtonText="YES GO AHEAD"
      negativeButtonText="NOT NOW"
    />
  )

  onFormPreviewOverlayPositivePressed = () => {
    this.visaParams.prevPageName = PAGENAME.APPLICATION_FORM;
    redirectToFormPreview(this.visaForm, this.visaParams);
  }

  isFromAddDocuments = () => this.props.onPartialFormSave ? true : false;

  onFormPreviewButtonPress = () => {
    Keyboard.dismiss();
    this.isFormValid = validateDynamicForm(this.visaForm.fields, this.visaForm.fieldValues);
    if (this.isFromAddDocuments()) {
      this.props.onPartialFormSave(this.isFormValid, this.visaForm);
      this.onBackPressed();
      return;
    }
    else if (this.isFormValid) {
      this.setState({
        renderOverlay: true
      });
    } else {
      this.setState({
        validateForm: true
      });
    }
    this.trackClickEvent('preview_form');
  }

  async fetchNewData() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET_WHILE_SELECTING_DROPDOWN
      });
      return;
    }

    const url = this.visaForm.fields[this.params.fieldName].url
    const finalUrl = url.replace(PARENTCURLYBRACES, this.params.parent)
    const newJsonData = await getUpdatedField(finalUrl);
    if (newJsonData.status === STATUS_CODE_SUCCESS) {
      const newJsonField = newJsonData.field;
      newJsonField.values.forEach((element) => {
        this.visaForm.fields[this.params.fieldName].values.push(element);
      });

      Object.keys(newJsonField.dependents).forEach((key) => {
        this.visaForm.fields[this.params.fieldName].dependents[key] = newJsonField.dependents[key];
      });

      this.visaForm.fields[this.params.fieldName].validations[this.params.parent] = newJsonField.validation;

      if (this.visaForm.fields[this.params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN) {
        this.fields = [];
        newJsonField.values.forEach((value) => {
          this.fields.push({
            key: value.key,
            value: value.value
          });
        });
      }

      this.setState({
        viewState: this.visaForm.fields[this.params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN
          ? ViewState.SHOW_SELECTOR : ViewState.SHOW_DETAIL
      });
    } else {
      showShortToast('Cannot fetch field values');
    }
  }

  renderSelector = () => (
    <VisaFormDropdownSelectorNew
      onSelected={this.onSelected}
      fields={this.fields}
      onBackPressed={this.onSelectorBackPressed}
    />
  )

  onSelectorBackPressed = () => {
    this.setState({
      viewState: ViewState.SHOW_DETAIL
    });
  }

  setField = (params) => {
    this.params = params;
    this.setState({
      viewState: ViewState.LOADING_DROPDOWN_DATA
    });
    this.fetchNewData();
  }

  showDropDown = (params) => {
    this.params = params;
    if (this.visaForm.fields[params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN) {
      this.fields = [];
      this.visaForm.fields[params.fieldName].values.forEach((value) => {
        if (this.params.parent === value.parent) {
          this.fields.push({
            key: value.key,
            value: value.value,
            parent: value.parent
          });
        }
      });
    }

    this.setState({
      viewState: this.visaForm.fields[params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN
        ? ViewState.SHOW_SELECTOR : ViewState.SHOW_DETAIL
    });
  }

  onSelected = (field) => {
    this.dropDownSelectedValue = field.key;
    this.updateChildValues(`${this.params.jsonKey}.value`, field.key);
    this.updateChildValues(`${this.params.jsonKey}.parent`, field.parent);
    this.updateChildValues(this.params.jsonKey.dependents, {});
    this.setState({
      viewState: ViewState.SHOW_DETAIL
    });
  }
}


VisaFormPreFilledNew.propTypes = {
  visaParams: PropTypes.object.isRequired
};
export default VisaFormPreFilledNew;
