import React from 'react';
import {View, ScrollView, Keyboard, ActivityIndicator, Text, findNodeHandle} from 'react-native';
import {KeyboardAwareView} from 'react-native-keyboard-aware-view';
import _ from 'lodash';
import PropTypes from 'prop-types';
import VisaFormHeaderNew from './VisaFormHeaderNew';
import VisaFormContainerNew from './VisaFormContainerNew';
import VisaFormButtonNew from './VisaFormButtonNew';
import styles from '../css/VisaMainCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import ViewState from './ViewStateNew';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import {FORM_DATA_TYPE_NEW, STATUS_CODE_SUCCESS, TRACKING_PAGENAME, PAGENAME, INT_MAX_VAL, PARENTCURLYBRACES, PROMPT_OVERLAY_POSITIVE, PASSPORT_DATA_VERIFY_TEXT} from '../VisaConstants';
import VisaFormDropdownSelectorNew from './VisaFormDropdownSelectorNew';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import VisaFormOverlayNew from './VisaFormOverlayNew';
import {getUpdatedField} from '../VisaNetworkUtils';


import {
  validateDynamicForm,
  redirectToFormPreview,
  isNextKeyGood,
  getScreenTitleForEmptyTraveller,
  formatNavSubTitle,
  getDisplayDate
} from '../VisaUtils';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import { VisaNavigation } from '../Navigation';

class VisaFormCompleteNew extends BasePage {
  static navigationOptions = {
    header: null
  };

  constructor(props) {
    super(props, 'VisaFormCompleteNew');

    this.state = {
      validateForm: false,
      viewState: ViewState.SHOW_DETAIL,
      loaderText: 'Loading visa application form...',
      renderOverlay: false
    };


    this.scrollViewWindowOffset = 0;
    this.textInputRefs = {};
    this.scrollIndex = 0;
    this.scrollViewRef = null;
    this.scrollViewContentHeight = 0;


    this.visaParams = this.props.visaParams;
    this.visaForm = _.cloneDeep(this.props.visaForm);
    this.requestsCount = 0;
    this.dropDownSelectedValue = '';
    this.fields = [];
    this.isValidationProcessed = false;
    this.isFormValid = true;
    this.previousPage = 'fromDocVerification';
    this.params = {};
    this.paxName = getScreenTitleForEmptyTraveller(this.visaParams.paxList, this.visaParams.paxId);
  }

  onBackPressed = () => {
    VisaNavigation.pop();
  };

  componentDidMount() {
    super.componentDidMount();
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
      TRACKING_PAGENAME.APPLICATION_FORM,
      this.previousPage, this.visaParams
    );
  }

  componentDidUpdate() {
    if (this.scrollViewRef) {
      setTimeout(() => {
        this.scrollViewRef.scrollTo({x: 0, y: this.scrollIndex, animated: false});
      }, 1);
    }
  }

  getNextFieldLabel = (label) => {
    let closestElemLabel = `${INT_MAX_VAL}`;
    Object.keys(this.textInputRefs).forEach((key) => {
      if (this.textInputRefs[key] && isNextKeyGood(key, closestElemLabel, label)) {
        closestElemLabel = key;
      }
    });
    if (closestElemLabel === `${INT_MAX_VAL}`) {
      return null;
    }
    return closestElemLabel;
  }

  focusOnNextField = (dataPacket) => {
    Object.keys(this.textInputRefs).forEach((key) => {
      if (this.textInputRefs[key] === null) {
        delete this.textInputRefs[key];
      }
    });
    const nextFieldLabel = this.getNextFieldLabel(dataPacket.labelIndex);
    if (nextFieldLabel === null) {
      Keyboard.dismiss();
    } else {
      this.textInputRefs[nextFieldLabel].focus();
      this.textInputRefs[nextFieldLabel].measureLayout(findNodeHandle(this.scrollViewRef), (ox, oy, width, heigth, px, py) => {
        this.scrollViewRef.scrollTo({x: 0, y: oy - heigth, animated: true});
      });
    }
  }

  doOverlayOperation = (operation) => {
    this.setState({
      renderOverlay: false
    });
    if (operation === PROMPT_OVERLAY_POSITIVE) {
      this.onFormPreviewOverlayPositivePressed();
    }
  }

  renderOverlay = () => (
    <VisaFormOverlayNew
      doOperation={this.doOverlayOperation}
      text="Before you proceed…"
      name={this.visaForm.fieldValues.firstName[0].value}
      surName={this.visaForm.fieldValues.lastName[0].value}
      positiveButtonText="YES GO AHEAD"
      negativeButtonText="NOT NOW"
    />
  )

  renderContent = () => (
    <KeyboardAwareView
      animated
      style={[styles.whitebg, styles.flex1, styles.spaceBetween]}
    >
      <VisaFormHeaderNew
        onBackPressed={this.onBackPressed}
        subTitle={formatNavSubTitle(this.visaParams.destCountry, getDisplayDate(this.visaParams.startDate), this.paxName)}
      />
      <View style={[styles.lightYellow]}>
        <Text style={[styles.font14, styles.marL15, styles.mar10Top, styles.boldFont, styles.mar10Bt, styles.mar20Lt, styles.mar20Rt, styles.darkYellowText]}>{PASSPORT_DATA_VERIFY_TEXT}</Text>
      </View>

      <ScrollView
        onScroll={this.handleScroll}
        ref={(ref) => {
          this.scrollViewRef = ref;
        }}
        keyboardShouldPersistTaps="handled"
      >
        <VisaFormContainerNew
          focusOnNextField={this.focusOnNextField}
          textInputRefs={this.textInputRefs}
          visaForm={this.visaForm}
          validateForm={this.state.validateForm}
          updateChildValues={this.updateChildValues}
          setField={this.setField}
          showDropDown={this.showDropDown}
        />
        <VisaFormButtonNew
          buttonPress={this.onFormPreviewButtonPress}
          btntext="PREVIEW FORM"
          enable
        />
      </ScrollView>
      {this.state.renderOverlay && this.renderOverlay()}
    </KeyboardAwareView>
  )

  handleScroll = (event) => {
    this.scrollIndex = event.nativeEvent.contentOffset.y;
    this.scrollViewContentHeight = event.nativeEvent.contentSize.height;
  }

  renderNoNetworkView = () => (
    <NoInternetView
      onRetry={() => {
        this.setState({
          viewState: ViewState.LOADING_DROPDOWN_DATA
        });
        this.fetchNewData();
    }}
    />
  );

  renderNewFieldLoader = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {'Loading Information'}
      </Text>
    </View>
  )

  renderProgressView = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {this.state.loaderText}
      </Text>
    </View>
  );

  render() {
    return (
      <View style={{flex: 1}}>
        {this.state.viewState === ViewState.LOADING && this.renderProgressView()}
        {this.state.viewState === ViewState.LOADING_DROPDOWN_DATA && this.renderNewFieldLoader()}
        {this.state.viewState === ViewState.NO_INTERNET_WHILE_SELECTING_DROPDOWN && this.renderNoNetworkView()}
        {this.state.viewState === ViewState.SHOW_SELECTOR && this.renderSelector()}
        {this.state.viewState === ViewState.SHOW_DETAIL && this.renderContent()}
      </View>
    );
  }

  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.APPLICATION_FORM,
      clickEvent, this.visaParams, this.previousPage
    );
  }

  onFormPreviewOverlayPositivePressed = () => {
    this.visaParams.prevPageName = PAGENAME.APPLICATION_FORM;
    redirectToFormPreview(this.visaForm, this.visaParams);
  }

  onFormPreviewButtonPress = () => {
    Keyboard.dismiss();
    this.isFormValid = validateDynamicForm(this.visaForm.fields, this.visaForm.fieldValues);
    if (this.isFormValid) {
      this.setState({
        renderOverlay: true
      });
    } else {
      this.setState({
        validateForm: true
      });
    }
    this.trackClickEvent('preview_form');
  }

  async fetchNewData() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET_WHILE_SELECTING_DROPDOWN
      });
      return;
    }

    const url = this.visaForm.fields[this.params.fieldName].url;
    const finalUrl = url.replace(PARENTCURLYBRACES, this.params.parent)
    const newJsonData = await getUpdatedField(finalUrl);
    if (newJsonData.status === STATUS_CODE_SUCCESS) {
      const newJsonField = newJsonData.field;
      newJsonField.values.forEach((element) => {
        this.visaForm.fields[this.params.fieldName].values.push(element);
      });

      Object.keys(newJsonField.dependents).forEach((key) => {
        this.visaForm.fields[this.params.fieldName].dependents[key] = newJsonField.dependents[key];
      });

      this.visaForm.fields[this.params.fieldName].validations[this.params.parent] = newJsonField.validation;

      if (this.visaForm.fields[this.params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN) {
        this.fields = [];
        newJsonField.values.forEach((value) => {
          this.fields.push({
            key: value.key,
            value: value.value
          });
        });
      }

      this.setState({
        viewState: this.visaForm.fields[this.params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN
          ? ViewState.SHOW_SELECTOR : ViewState.SHOW_DETAIL
      });
    } else {
      showShortToast('Cannot fetch field values');
    }
  }

  renderSelector = () => (
    <VisaFormDropdownSelectorNew
      onSelected={this.onSelected}
      fields={this.fields}
      onBackPressed={this.onSelectorBackPressed}
    />
  )

  onSelectorBackPressed = () => {
    this.setState({
      viewState: ViewState.SHOW_DETAIL
    });
  }

  setField = (params) => {
    this.params = params;
    this.setState({
      viewState: ViewState.LOADING_DROPDOWN_DATA
    });
    this.fetchNewData();
  }

  showDropDown = (params) => {
    this.params = params;
    if (this.visaForm.fields[params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN) {
      this.fields = [];
      this.visaForm.fields[params.fieldName].values.forEach((value) => {
        if (this.params.parent === value.parent) {
          this.fields.push({
            key: value.key,
            value: value.value,
            parent: value.parent
          });
        }
      });
    }

    this.setState({
      viewState: this.visaForm.fields[params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN
        ? ViewState.SHOW_SELECTOR : ViewState.SHOW_DETAIL
    });
  }

  onSelected = (field) => {
    this.dropDownSelectedValue = field.key;
    this.updateChildValues(`${this.params.jsonKey}.value`, field.key);
    this.updateChildValues(`${this.params.jsonKey}.parent`, field.parent);
    this.updateChildValues(this.params.jsonKey.dependents, {});
    this.setState({
      viewState: ViewState.SHOW_DETAIL
    });
  }

  updateChildValues = (key, val) => {
    _.set(this, key, val);
  }
}

VisaFormCompleteNew.propTypes = {
  visaForm: PropTypes.object.isRequired,
  visaParams: PropTypes.object.isRequired
};
export default VisaFormCompleteNew;
