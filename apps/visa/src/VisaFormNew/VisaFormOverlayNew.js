import React from 'react';
import {View, Text, TouchableHighlight, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import {PROMPT_OVERLAY_POSITIVE, PROMPT_OVERLAY_NEGETIVE} from '../VisaConstants';

class VisaFormOverlayNew extends React.PureComponent {
  static navigationOptions = {
    header: null
  }

  render() {
    const {
      text,
      doOperation,
      positiveButtonText,
      negativeButtonText
    } = this.props;
    return (
      <View style={styles.overlayContainer}>
        <TouchableHighlight style={styles.overlayBg}><Text>.</Text></TouchableHighlight>
        <View style={styles.overlayContent}>
          <View style={[styles.marginBottom32, styles.flexRow]}>
            <Text style={styles.overlayHead}>{text} </Text>
          </View>
          <View style={[styles.marginBottom35, styles.flexRow, styles.alignCenter]}>
            <View style={styles.flex1}>
              <Text style={styles.rightContent}>
                We hope that you have <Text style={styles.redText}>verified and entered all information</Text> as per {this.props.name} {this.props.surName}’s passport. Filling incorrect information could lead to visa rejection.
              </Text>
            </View>
          </View>
          <View style={[styles.flexRow, styles.alignCenter, styles.JustifySpaceBetween]}>
            <TouchableOpacity
              style={[styles.padding5]}
              onPress={() => doOperation(PROMPT_OVERLAY_NEGETIVE)}
            >
              <Text style={[styles.actionBlueText, styles.regularFont]}>{negativeButtonText}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => doOperation(PROMPT_OVERLAY_POSITIVE)}
              style={[styles.padding5]}
            >
              <Text style={styles.actionBlueText}>{positiveButtonText}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

    );
  }
}

VisaFormOverlayNew.propTypes = {
  doOperation: PropTypes.func.isRequired,
  text: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  surName: PropTypes.string.isRequired,
  positiveButtonText: PropTypes.string.isRequired,
  negativeButtonText: PropTypes.string.isRequired
};

export default VisaFormOverlayNew;
