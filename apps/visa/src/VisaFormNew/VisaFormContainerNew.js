import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import InputFieldMaster from './InputFieldMaster';
import { getHypotheticalParentNode } from '../VisaUtils';
import { isEmpty } from 'lodash';
import { getIsEnableSkipDoc } from '../VisaRevamp/Utils/VisaPokusUtils';

class VisaFormContainerNew extends React.Component {
  static navigationOptions = {
    header: null
  };

  constructor(props) {
    super(props);
    this.masterFields = [];
    this.fieldValuesKey = 'visaForm.fieldValues';
  }

  rendercategoryName = (category, newcategory) => {
    if (category !== newcategory) {
      return true;
    }
    else {
      return false;
    };
  }

  renderFormSection = () => {
    this.masterFields = [];
    let category = '';
    this.props.visaForm.fieldsOrder.forEach((element, index) => {
      let newcategory = this.props.visaForm.fields[element].category;
      if (this.rendercategoryName(category, newcategory)) {
        this.masterFields.push(
          <View style={[styles.headerWrapper,getIsEnableSkipDoc() ? {paddingHorizontal:16} : {paddingTop:index === 0 ? 0 : 12}, {marginTop: index === 0 ? 0 : 12, borderTopWidth: index === 0 ? 0 : 8}]}>
            <Text style={styles.MultipleFormSectionTitle}>{newcategory}</Text>
            <Text style={[styles.font12, styles.regularFont, styles.defaultText]}>{this.props.visaForm.fields[element].categoryDescription}</Text>
          </View>
        );
      }
      this.masterFields.push(<View style={getIsEnableSkipDoc() ? {paddingHorizontal: 16} : {paddingHorizontal: 0}}><InputFieldMaster
        focusOnNextField={this.props.focusOnNextField}
        textInputRefs={this.props.textInputRefs}
        labelIndex={`${this.masterFields.length + 1}`}
        key={element}
        parent={getHypotheticalParentNode('', this.props.visaForm.fields[element])}
        jsonKey={this.fieldValuesKey}
        fieldName={element}
        visaForm={this.props.visaForm}
        updateChildValues={this.props.updateChildValues}
        validate={this.props.validateForm}
        setField={this.props.setField}
        showDropDown={this.props.showDropDown}
      /></View>);
      category = this.props.visaForm.fields[element].category;
    });
    return this.masterFields;
  }

  render() {
    return (
      <View>
        <View style={[styles.FormSection]} >
          {this.renderFormSection()}
        </View>
      </View>
    );
  }
}

VisaFormContainerNew.propTypes = {
  visaForm: PropTypes.object.isRequired,
  validateForm: PropTypes.bool.isRequired,
  updateChildValues: PropTypes.func.isRequired,
  setField: PropTypes.func.isRequired,
  showDropDown: PropTypes.func.isRequired,
  textInputRefs: PropTypes.object.isRequired,
  focusOnNextField: PropTypes.func.isRequired
};

export default VisaFormContainerNew;
