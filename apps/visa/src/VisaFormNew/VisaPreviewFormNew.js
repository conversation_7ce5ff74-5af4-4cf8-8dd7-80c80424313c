import React from 'react';
import { View, ScrollView, Text, ActivityIndicator } from 'react-native';
import PropTypes from 'prop-types';
import { cloneDeep, isEmpty } from 'lodash';
import LinearGradient from 'react-native-linear-gradient';
import { showShortToast, showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import styles from '../css/VisaMainCss';
import VisaFormPreviewHeaderNew from './VisaFormPreviewHeaderNew';
import VisaFormButtonNew from './VisaFormButtonNew';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import VisaPartCancelSectionNew from './VisaPartCancelSectionNew';
import {
  getNextPaxIdForDocUpload,
  updatePaxStatus,
  createParamsForNextTravellerNew,
  getVisaFormSubTitle,
  isResponseValid,
  getHypotheticalParentNode,
  getScreenTitleForEmptyTraveller,
  formatNavSubTitle,
  getDisplayDate,
  isVisaResponseValid
} from '../VisaUtils';
import {
  PASSENGER_BOOKING_STATUS,
  DOC_OP,
  PAGENAME,
  PageErrorData,
  TRACKING_PAGENAME,
  STATUS_CODE_SUCCESS,
  FORM_DATA_TYPE_NEW,
  PARENTCURLYBRACES
} from '../VisaConstants';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import ViewState from './ViewStateNew';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import { getMultipleApplicationFormResponse, submitApplicationFormResponse } from '../VisaNetworkUtils';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../Navigation';


class VisaPreviewFormNew extends React.Component {
  static navigationOptions = {
    header: null
  };

  constructor(props) {
    super(props, PAGENAME.APPLICATION_FORM);
    this.visaParams = this.props.visaParams;

    this.requestsCount = 0;
    this.visaForm = {};
    this.isFormConfirmed = false;
    this.state = {
      viewState: ''
    };
    this.paxName = getScreenTitleForEmptyTraveller(this.visaParams.paxList, this.visaParams.paxId);
  }

  componentWillMount() {
    if (this.visaParams.prevPageName === PAGENAME.PASSENGER_DOCS) {
      this.fetchVisaForm();
      this.state.viewState = ViewState.LOADING;
    } else {
      this.visaForm = this.props.visaForm;
      this.state.viewState = ViewState.SHOW_DETAIL;
    }
  }

  componentDidMount() {
    VisaTrackingHelper.trackVisaPageVisit(TRACKING_PAGENAME.APPLICATION_FORM, this.visaParams, this.visaParams.paxList);
  }
  onBackPressed = () => {
    VisaNavigation.pop();
  };

  renderProgressView = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {this.state.loaderText}
      </Text>
    </View>
  );


  addDisplayNameAndVal = data => (
    <View style={[styles.flexRow, styles.mar20Bt, styles.mar5Rt, styles.flex1]} key={`${data.index}${data.displayName}`}>
      <Text style={[styles.font14, styles.regularFont, styles.grey, styles.mar10Rt, styles.width62Percent]}>{data.index}. {data.displayName}:</Text>
      <Text style={[styles.font14, styles.boldFont, styles.BlackText, styles.flex1]}>{this.getVal(data.element, data.value)}</Text>
    </View>
  )

  addDisplayName = data => (
    <View style={[styles.flexRow, styles.mar20Bt, styles.mar5Rt]} key={`${data.index}${data.displayName}`}>
      <Text style={[styles.font14, styles.regularFont, styles.grey, styles.mar10Rt]}>{data.index}. {data.displayName}:</Text>
    </View>
  )

  getValue = (element, _value) => {
    const val = this.visaForm.fields[element].values.find(o => o.key === _value);
    if (val) {
      return val.value;
    }
    return '';
  }


  getVal = (element, value) => {
    switch (this.visaForm.fields[element].type) {
      case FORM_DATA_TYPE_NEW.DATE:
        return value.split('-').join('/');
      case FORM_DATA_TYPE_NEW.TEXTAREA:
      case FORM_DATA_TYPE_NEW.TEXT:
        return value;
      case FORM_DATA_TYPE_NEW.MULTI:
        const retValues = [];
        value.split('|').forEach((_value) => {
          retValues.push(this.getValue(element, _value));
        });
        return retValues.join(', ');
      case FORM_DATA_TYPE_NEW.RADIO:
      case FORM_DATA_TYPE_NEW.SELECT:
      case FORM_DATA_TYPE_NEW.DROPDOWN:
        return this.visaForm.fields[element].values.find(o => o.key === value) ? this.visaForm.fields[element].values.find(o => o.key === value).value : '';
      default: break;
    }
    return '';
  }
  addVal = data => (
    <View style={[styles.flexRow, styles.mar20Bt, styles.mar5Rt]} key={`${data.index}${data.displayName}`}>
      <Text style={[styles.font14, styles.boldFont, styles.BlackText]}>{this.getVal(data.element, data.value)}</Text>
    </View>
  )

  setDataInList = (index, element, list, valueObj) => {
    if (this.visaForm.fields[element] && valueObj) {
      if (this.visaForm.fields[element].type === FORM_DATA_TYPE_NEW.MULTI) {
        const multiValues = [];
        valueObj.forEach((_value) => {
          multiValues.push(_value.value);
        });
        list.push(this.addDisplayNameAndVal({
          index, displayName: this.visaForm.fields[element].displayName, value: multiValues.join('|'), element
        }));
        valueObj.forEach((elem, idx) => {
          if (elem.dependents) {
            Object.keys(elem.dependents).forEach((key, depIdx) => {
              this.setDataInList(`${index}.${idx + 1}.${depIdx + 1}`, key, list, elem.dependents[key]);
            });
          }
        });
      } else if (valueObj.length === 1) {
        list.push(this.addDisplayNameAndVal({
          index, displayName: this.visaForm.fields[element].displayName, value: valueObj[0].value, element
        }));
        if (valueObj[0].dependents) {
          Object.keys(valueObj[0].dependents).forEach((key, depIdx) => {
            if (this.visaForm.fields[element].type === FORM_DATA_TYPE_NEW.GROUP) {
              this.setDataInList(`${index}.${1}.${depIdx + 1}`, key, list, valueObj[0].dependents[key]);
            } else {
              this.setDataInList(`${index}.${depIdx + 1}`, key, list, valueObj[0].dependents[key]);
            }
          });
        }
      } else {
        list.push(this.addDisplayName({ index, displayName: this.visaForm.fields[element].displayName }));
        valueObj.forEach((elem, idx) => {
          list.push(this.addVal({
            index: `${index}.${idx + 1}`, displayName: this.visaForm.fields[element].displayName, value: elem.value, element
          }));
          if (elem.dependents) {
            Object.keys(elem.dependents).forEach((key, depIdx) => {
              this.setDataInList(`${index}.${idx + 1}.${depIdx + 1}`, key, list, elem.dependents[key]);
            });
          }
        });
      }
    }
  }


  renderFormPreviewSection = () => {
    const list = [];
    if (this.visaForm.fieldValues) {
      this.visaForm.fieldsOrder.forEach((element, index) => {
        if (this.visaForm.fieldValues[element]) {
          this.setDataInList(`${index + 1}`, element, list, this.visaForm.fieldValues[element]);
        }
      });
    }
    return list;
  }

  render() {
    return (
      <View style={{ flex: 1 }}>
        {this.state.viewState === ViewState.LOADING && this.renderProgressView()}
        {this.state.viewState === ViewState.NO_INTERNET && this.renderNoNetworkView()}
        {this.state.viewState === ViewState.SHOW_DETAIL && this.renderContent()}
      </View>
    );
  }

  renderNoNetworkView = () => (
    <NoInternetView
      onRetry={() => {
        this.setState({ viewState: ViewState.LOADING });
        this.fetchVisaForm();
      }}
    />
  );

  renderContent() {
    return (
      <View style={[styles.lightGreyBg, styles.flex1, styles.spaceBetween]}>
        <VisaFormPreviewHeaderNew
          onEditFormPress={this.onEditFormPress}
          onBackPressed={this.onBackPressed}
          subtitle={formatNavSubTitle(this.visaParams.destCountry, getDisplayDate(this.visaParams.startDate), this.paxName)}
          headerTitle="Preview From"
          buttonText=""
        />
        <ScrollView>
          <View style={[styles.previewForm, styles.mar20Bt, styles.elevation3]}>
            <View style={[styles.whitebg, { borderRadius: 4 }]}>
              <LinearGradient
                style={{
                  paddingVertical: 16,
                  paddingHorizontal: 14,
                  borderTopLeftRadius: 4,
                  borderTopRightRadius: 4
                }}
                start={{ x: 1.0, y: 0.0 }}
                end={{ x: 0.0, y: 1.0 }}
                colors={['#eff7fd', '#ebf6ff']}
              >
                <Text style={[styles.CenterText, styles.boldFont, styles.font12, styles.DarkBlue, styles.mar3Bt, styles.bgTransparent]}>PLEASE VERIFY THE BELOW INFORMATION</Text>
                <Text style={[styles.grey, styles.AlignCenter, styles.regularFont, styles.Italic, styles.bgTransparent, styles.font12]}>All fields are mandatory and required by the embassy</Text>
              </LinearGradient>
              <View style={{
                paddingVertical: 20,
                paddingHorizontal: 14,
                borderBottomWidth: 1,
                borderBottomColor: '#e0e0e0'
              }}
              >
                {this.renderFormPreviewSection()}
              </View>
              {/* <VisaPartCancelSectionNew
                onFormConfirmation={this.onFormConfirmation}
              /> */}
            </View>
          </View>
          {/* <VisaFormButtonNew
            buttonPress={this.onFormSubmit}
            btntext="SUBMIT"
            enable
          /> */}
        </ScrollView>
      </View>
    );
  }

  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.APPLICATION_FORM,
      clickEvent,
      this.visaParams,
      this.previousPage
    );
  }

  onFormSubmit = () => {
    if (!this.isFormConfirmed) {
      showShortToast('Please confirm before you proceed.');
    } else {
      this.submitVisaForm();
      this.trackClickEvent('submit_form');
      this.isFormConfirmed = false;
    }
  }

  onEditFormPress = () => {
    VisaNavigation.push(VISA_ROUTE_KEYS.ADD_VISA_DOCUMENTS,{
      visaParams: this.visaParams,
      payRefId: this.visaParams.payRefId
    })
    this.trackClickEvent('edit_form');
  };

  onFormConfirmation = (formConfirmed) => {
    this.isFormConfirmed = formConfirmed;
  }

  removeParentTag = (values) => {
    Object.keys(values).forEach((element) => {
      if (element) {
        values[element].forEach((elem, index) => {
          values[element][index] = {
            value: values[element][index].value,
            dependents: (values[element][index].dependents) ? values[element][index].dependents : {}
          };
          this.removeParentTag(values[element][index].dependents);
        });
      }
    });
  }

  async submitVisaForm() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      showShortToast('Please check your internet connection');
      return;
    }
    this.updatePageState(ViewState.LOADING);
    try {
      const result = { ...this.visaForm.fieldValues };
      this.removeParentTag(result);
      const response = await submitApplicationFormResponse(this.visaParams.visaBookingId, this.visaParams.paxId, result);
      let isValidResponse = false;
      let errorMessage = PageErrorData.PREVIEW_FORM.subTitle;

      if (response && response.saved) {
        if (isVisaResponseValid(response)) {
          isValidResponse = true;
          this.continueNextProcess();
        } else if (visaFormResponse) {
          errorMessage = visaFormResponse.status;
        }
      }

      if (!isValidResponse) {
        this.isFormConfirmed = false;
        this.updatePageState(ViewState.SHOW_DETAIL);
        showLongToast(errorMessage);
        return;
      }
    } catch (e) {
      showShortToast('Unable to submit application form. Try again');
      this.updatePageState(ViewState.SHOW_DETAIL);
    }
  }

  continueNextProcess = () => {
    const params = cloneDeep(this.visaParams);
    const currentPaxIndex = params.paxList.findIndex(row => row.id === params.paxId);
    updatePaxStatus(params.paxList[currentPaxIndex]);
    const nextPax = getNextPaxIdForDocUpload(params);

    if (params.isDocRequired === false) {
      const lastPaxId = params.paxList[params.paxList.length - 1].id;
      if (lastPaxId === params.paxId) {
        VisaNavigation.push(VISA_ROUTE_KEYS.SHOW_REVIEW,{
            visaParams: params
          })
      } else {
        params.paxId = String(Number(params.paxId) + 1);
        this.visaParams = params;
        VisaNavigation.push(VISA_ROUTE_KEYS.FORM_PRE_FILLED_NEW,{
          visaParams: this.visaParams
        })
      }
      this.updatePageState(ViewState.SHOW_DETAIL);
      return;
    }

    if (nextPax.status === -1) {
      VisaNavigation.push(VISA_ROUTE_KEYS.SHOW_REVIEW,{
        visaParams: params
      })
    } else {
      var docStatus = (nextPax.status === PASSENGER_BOOKING_STATUS.PASSENGER_INITIATED ||
        nextPax.status === PASSENGER_BOOKING_STATUS.DOCUMENTS_PENDING) ?
        DOC_OP.UPLOAD : DOC_OP.VERIFY;

      createParamsForNextTravellerNew(this.visaForm, params, nextPax.id, docStatus);
      VisaNavigation.push(VISA_ROUTE_KEYS.NEXT_TRAVELLER_UPLOAD,{
        visaParams: params
      })
    }
    this.updatePageState(ViewState.SHOW_DETAIL);
  }

  updatePageState = (state) => {
    this.setState({
      viewState: state
    });
  }


  async fetchPreProcessData(key, parent) {
    try {
      const url = this.visaForm.fields[key].url;
      const finalUrl = url.replace(PARENTCURLYBRACES, parent)
      this.requestsCount += 1;
      const newJsonData = await getUpdatedField(finalUrl);
      if (newJsonData.status === STATUS_CODE_SUCCESS) {
        const newJsonField = newJsonData.field;
        newJsonField.values.forEach((element) => {
          this.visaForm.fields[key].values.push(element);
        });
        Object.keys(newJsonField.dependents).forEach((_key) => {
          this.visaForm.fields[key].dependents[_key] = newJsonField.dependents[_key];
        });
        this.visaForm.fields[key].validations[parent] = newJsonField.validation;
        this.requestsCount -= 1;
      }
      if (this.requestsCount === 0) {
        this.setState({
          viewState: ViewState.SHOW_DETAIL
        });
      }
    } catch (e) {
      showShortToast('Unable to fetch data');
      VisaNavigation.pop();
    }
  }

  recursivelySetValue(parent, values) {
    if (!values) return;
    Object.keys(values).forEach((key) => {
      if (this.visaForm.fields[key] && this.visaForm.fields[key].validations[getHypotheticalParentNode(parent, this.visaForm.fields[key])] === undefined && this.visaForm.fields[key].url !== '') {
        this.fetchPreProcessData(key, getHypotheticalParentNode(parent, this.visaForm.fields[key]));
      }
      values[key].forEach((element) => {
        this.recursivelySetValue(element.value, element.dependents);
      });
    });
  }

  preProcessJson = () => {
    Object.keys(this.visaForm.fields).forEach((elem) => {
      this.visaForm.fields[elem].validations = {};
      if (isEmpty(this.visaForm.fields[elem].values) && this.visaForm.fields[elem].url === '') {
        this.visaForm.fields[elem].validations[''] = this.visaForm.fields[elem].validation;
      }
      this.visaForm.fields[elem].values.forEach((element) => {
        this.visaForm.fields[elem].validations[element.parent] = this.visaForm.fields[elem].validation;
      });
    });
    this.recursivelySetValue('', this.visaForm.fieldValues);
  };

  async fetchVisaForm() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET
      });
      return;
    }

    try {
      let isValidResponse = false;
      const response = await getMultipleApplicationFormResponse(this.visaParams.payRefId, undefined, this.visaParams.paxId);
      if (response) {
        const visaFormResponse = response;
        if (isVisaResponseValid(visaFormResponse) && visaFormResponse.forms && visaFormResponse.forms.length) {
          this.visaForm = visaFormResponse.forms[0].formSchema;

          isValidResponse = true;
          this.preProcessJson();
          if (this.requestsCount === 0) {
            this.setState({
              viewState: ViewState.SHOW_DETAIL
            });
          }
        }
      }

      if (!isValidResponse) {
        showShortToast('Unable to fetch form data, Try again later');
        VisaNavigation.pop();
        return;
      }
    } catch (e) {
      showShortToast('Unable to form data, Try again later');
      VisaNavigation.pop();
    }
  }
}

VisaPreviewFormNew.propTypes = {
  visaForm: PropTypes.object.isRequired,
  visaParams: PropTypes.object.isRequired
};

export default VisaPreviewFormNew;
