import React from 'react';
import { View, Text, ActivityIndicator, Keyboard, findNodeHandle, Modal, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { set, isEmpty, filter } from 'lodash';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import VisaFormContainerNew from './VisaFormContainerNew';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import ViewState from './ViewStateNew';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { FORM_DATA_TYPE_NEW, STATUS_CODE_SUCCESS, INT_MAX_VAL, PARENTCURLYBRACES } from '../VisaConstants';
import VisaFormDropdownSelectorNew from './VisaFormDropdownSelectorNew';
import { getUpdatedField } from '../VisaNetworkUtils';
import BottomSheetWpr from '../VisaRevamp/Components/Common/BottomSheetWpr';
import ButtonWpr from '../VisaRevamp/Components/Common/ButtonWpr';
import {
  getHypotheticalParentNode,
  isNextKeyGood
} from '../VisaUtils';
import { VisaNavigation } from '../Navigation';
import { paddingStyles } from '../VisaRevamp/Styles/Spacing';
import { colors } from '../VisaRevamp/Styles/colors';
import { fontStyles } from '../VisaRevamp/Styles/fonts';
import { PDT_EVENT_TYPES } from '../VisaRevamp/Tracking/pdt/constants';
import { logPDTEvent } from '../VisaRevamp/Tracking/pdt/logger';
class VisaFormPreFilledComponent extends React.Component {
  // 1. Enhanced constructor to properly initialize form values from fieldValues
  constructor(props) {
    super(props);
    const visaForm = this.props.visaForm;

    this.state = {
      viewState: ViewState.SHOW_DETAIL,
      validateForm: false,
      showSelector: false,
      selectedValue: null,
      visaForm: visaForm,
      activeFieldName: null,
    };

    this.textInputRefs = {};
    this.requestsCount = 0;
    this.dropDownSelectedValue = '';
    this.fields = [];
    this.visaFormResponse = {};
    this.isFormValid = true;
    this.previousPage = 'fromDocVerification';
    this.visaForm = this.state.visaForm;
    this.params = {};

    // Initialize values from fieldValues
    this.syncFieldValuesWithForm();
    this.preProcessJson();
  }

  syncFieldValuesWithForm() {
    // Function to recursively get values from fieldValues nested structure
    const extractValuesFromDependents = (item) => {
      if (!item || !item.dependents) return;

      Object.keys(item.dependents).forEach((fieldName) => {
        if (Array.isArray(item.dependents[fieldName]) && item.dependents[fieldName].length > 0) {
          const fieldValue = item.dependents[fieldName][0].value;

          // Update the form field if it exists
          if (this.visaForm.fields[fieldName]) {
            this.visaForm.fields[fieldName].value = fieldValue;
          }

          // Recursively process any deeper dependents
          extractValuesFromDependents(item.dependents[fieldName][0]);
        }
      });
    };

    // Process top-level field values
    if (this.visaForm.fieldValues) {
      Object.keys(this.visaForm.fieldValues).forEach((key) => {
        if (
          Array.isArray(this.visaForm.fieldValues[key]) &&
          this.visaForm.fieldValues[key].length > 0
        ) {
          extractValuesFromDependents(this.visaForm.fieldValues[key][0]);
        }
      });
    }
  }

  async fetchPreProcessData(key, parent) {
    try {
      const url = this.visaForm.fields[key].url;
      const fieldParams = {
        visaform_updated_field_url: url.replace(PARENTCURLYBRACES, parent),
      };
      this.requestsCount += 1;
      const newJsonData = await getUpdatedField(url.replace(PARENTCURLYBRACES, parent));
      if (newJsonData.status === STATUS_CODE_SUCCESS) {
        const newJsonField = newJsonData.field;
        newJsonField.values.forEach((element) => {
          this.visaForm.fields[key].values.push(element);
        });
        Object.keys(newJsonField.dependents).forEach((_key) => {
          this.visaForm.fields[key].dependents[_key] = newJsonField.dependents[_key];
        });
        this.visaForm.fields[key].validations[parent] = newJsonField.validation;
        this.requestsCount -= 1;
      }
      if (this.requestsCount === 0) {
        this.setState({
          viewState: ViewState.SHOW_DETAIL,
        });
      }
    } catch (e) {
      showShortToast('Unable to fetch data');
      VisaNavigation.pop();
    }
  }

  recursivelySetValue(parent, values) {
    if (!values) return;
    Object.keys(values).forEach((key) => {
      if (
        this.visaForm.fields[key] &&
        this.visaForm.fields[key].validations[
          getHypotheticalParentNode(parent, this.visaForm.fields[key])
        ] === undefined &&
        this.visaForm.fields[key].url !== ''
      ) {
        this.fetchPreProcessData(key, getHypotheticalParentNode(parent, this.visaForm.fields[key]));
      }
      values[key].forEach((element) => {
        this.recursivelySetValue(element.value, element.dependents);
      });
    });
  }

  fetchDefaultValues = () => {
    Object.keys(this.visaForm.fields).forEach((key) => {
      const field = this.visaForm.fields[key];
      if (
        field.defaultValue !== '' &&
        field.url !== '' &&
        filter(field.values, (row) => row.key === field.defaultValue).length === 0
      ) {
        this.fetchPreProcessData(key, getHypotheticalParentNode('', field));
      }
    });
  };

  preProcessJson = () => {
    this.fetchDefaultValues();

    // Store field values that we find
    let foundValues = {};

    Object.keys(this.visaForm.fields).forEach((elem) => {
      this.visaForm.fields[elem].validations = {};

      if (isEmpty(this.visaForm.fields[elem].values) && this.visaForm.fields[elem].url === '') {
        this.visaForm.fields[elem].validations[''] = this.visaForm.fields[elem].validation;
      }

      // If field has a value, store it
      if (this.visaForm.fields[elem].value) {
        foundValues[elem] = this.visaForm.fields[elem].value;
      }

      this.visaForm.fields[elem].values.forEach((element) => {
        this.visaForm.fields[elem].validations[element.parent] =
          this.visaForm.fields[elem].validation;

        // Also check if any values match the current field value
        if (String(element.key) === String(this.visaForm.fields[elem].value)) {
          foundValues[elem] = element.key;
        }
      });
    });

    // If we have values, update the selected value (only for the current field)
    if (Object.keys(foundValues).length > 0) {
      // We'll set it when a specific field is activated in showDropDown
    }

    this.recursivelySetValue('', this.visaForm.fieldValues);
  };

  render() {
    return (
      <View style={{ flex: 1 }}>
        {this.state.viewState === ViewState.LOADING_DROPDOWN_DATA && this.renderNewFieldLoader()}
        {this.state.viewState === ViewState.NO_INTERNET_WHILE_SELECTING_DROPDOWN &&
          this.renderNoNetworkViewDropDown()}
        {this.state.showSelector && (
          <BottomSheetWpr
            visible={this.state.showSelector}
            setVisible={(visible) => this.setState({ showSelector: visible })}
            onDismiss={this.onSelectorBackPressed}
            title={
              <Text style={fontStyles.labelMediumBold}>
                Select{' '}
                {this.state.activeFieldName &&
                this.visaForm.fields[this.state.activeFieldName]?.displayName
                  ? this.visaForm.fields[this.state.activeFieldName].displayName
                  : 'OPTION'}
              </Text>
            }
            isCrossIcon={true}
            titleContainerStyles={{
              ...paddingStyles.ph16,
              ...paddingStyles.pv16,
              paddingTop: 24,
              backgroundColor: colors.white,
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
              borderBottomColor: colors.grayBorder,
              borderBottomWidth: 1,
            }}
            containerStyles={{
              backgroundColor: colors.lightGray2,
              paddingHorizontal: 0,
              paddingVertical: 0,
            }}
          >
            <View style={stylesfile.selectorContainer}>
              <ScrollView
                style={styles2(400).allPassengerListContainer}
                showsVerticalScrollIndicator={false}
              >
                {this.fields.map((item, idx) => {
                  // Normalize both values to strings for comparison
                  const normalizedStateValue = String(this.state.selectedValue);
                  const normalizedItemKey = String(item.key);

                  // Simple string equality check after normalization
                  const isSelected = normalizedStateValue === normalizedItemKey;

                  return (
                    <TouchableOpacity
                      key={`${item.key || item.value || idx}`}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: 16,
                        paddingHorizontal: 16,
                        backgroundColor: isSelected ? '#EAF5FF' : '#fff',
                        borderWidth: 1,
                        borderColor: isSelected ? '#008CFF' : '#E0E0E0',
                        marginBottom: 12,
                        borderRadius: 16,
                      }}
                      onPress={() => {
                        this.setState({ selectedValue: item.key });
                      }}
                    >
                      <View
                        style={{
                          width: 18,
                          height: 18,
                          borderRadius: 9,
                          borderWidth: 2,
                          borderColor: isSelected ? '#008CFF' : '#BDBDBD',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginRight: 12,
                          backgroundColor: '#fff',
                        }}
                      >
                        {isSelected && (
                          <View
                            style={{
                              width: 12,
                              height: 12,
                              borderRadius: 6,
                              backgroundColor: '#008CFF',
                            }}
                          />
                        )}
                      </View>
                      <Text
                        style={{
                          fontSize: 18,
                          color: isSelected ? '#008CFF' : '#222',
                          fontWeight: isSelected ? 'bold' : 'normal',
                        }}
                      >
                        {item.value}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
              <View style={stylesfile.buttonContainer}>
                <ButtonWpr
                  buttonText="CONFIRM"
                  onButtonPress={() => {
                    logPDTEvent({
                      eventValue: `click_confirm_from_dropdown_${
                        this.visaForm.fields[this.state.activeFieldName]?.displayName
                      }`,
                      actionType: PDT_EVENT_TYPES.buttonClicked,
                    });
                    const selectedItem = this.fields.find(
                      (f) => String(f.key) === String(this.state.selectedValue),
                    );
                    if (selectedItem) {
                      this.onSelected(selectedItem);
                    }
                    this.setState({ showSelector: false });
                  }}
                  disabled={!this.state.selectedValue}
                />
              </View>
            </View>
          </BottomSheetWpr>
        )}
        {this.state.viewState === ViewState.SHOW_DETAIL && this.renderContent()}
      </View>
    );
  }

  renderNoNetworkViewDropDown = () => (
    <NoInternetView
      onRetry={() => {
        this.setState({
          viewState: ViewState.LOADING_DROPDOWN_DATA,
        });
        this.fetchNewData();
      }}
    />
  );

  renderNewFieldLoader = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {'Loading Information'}</Text>
    </View>
  );

  renderProgressView = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator styleAttr="Inverse" style={styles.indicator} size="large" />
      <Text style={styles.darkText}> {this.state.loaderText}</Text>
    </View>
  );

  updateChildValues = (key, val) => {
    set(this, key, val);
    this.visaForm = this.state.visaForm;
  };

  getNextFieldLabel = (label) => {
    let closestElemLabel = `${INT_MAX_VAL}`;
    Object.keys(this.textInputRefs).forEach((key) => {
      if (this.textInputRefs[key] && isNextKeyGood(key, closestElemLabel, label)) {
        closestElemLabel = key;
      }
    });
    if (closestElemLabel === `${INT_MAX_VAL}`) {
      return null;
    }
    return closestElemLabel;
  };

  focusOnNextField = (dataPacket) => {
    Object.keys(this.textInputRefs).forEach((key) => {
      if (this.textInputRefs[key] === null) {
        delete this.textInputRefs[key];
      }
    });
    const nextFieldLabel = this.getNextFieldLabel(dataPacket.labelIndex);
    if (nextFieldLabel === null) {
      Keyboard.dismiss();
    } else {
      this.textInputRefs[nextFieldLabel].focus();
      if (this.props.scrollViewRef) {
        this.textInputRefs[nextFieldLabel].measureLayout(
          findNodeHandle(this.props.scrollViewRef),
          (ox, oy, width, heigth, px, py) => {
            this.props.scrollViewRef.scrollTo({ x: 0, y: oy - heigth, animated: true });
          },
        );
      }
    }
  };

  renderContent = () => (
    <VisaFormContainerNew
      style={styles.MultipleFormSection}
      focusOnNextField={this.focusOnNextField}
      textInputRefs={this.textInputRefs}
      visaForm={this.state.visaForm}
      validateForm={this.props.validateForm}
      updateChildValues={this.updateChildValues}
      setField={this.setField}
      showDropDown={this.showDropDown}
    />
  );

  async fetchNewData() {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: ViewState.NO_INTERNET_WHILE_SELECTING_DROPDOWN,
      });
      return;
    }

    const url = this.visaForm.fields[this.params.fieldName].url;
    const finalUrl = url.replace(PARENTCURLYBRACES, this.params.parent);
    const newJsonData = await getUpdatedField(finalUrl);
    if (newJsonData.status === STATUS_CODE_SUCCESS) {
      const newJsonField = newJsonData.field;
      newJsonField.values.forEach((element) => {
        this.visaForm.fields[this.params.fieldName].values.push(element);
      });

      Object.keys(newJsonField.dependents).forEach((key) => {
        this.visaForm.fields[this.params.fieldName].dependents[key] = newJsonField.dependents[key];
      });

      this.visaForm.fields[this.params.fieldName].validations[this.params.parent] =
        newJsonField.validation;

      if (this.visaForm.fields[this.params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN) {
        this.fields = [];
        newJsonField.values.forEach((value) => {
          this.fields.push({
            key: value.key,
            value: value.value,
          });
        });
      }
      if (this.visaForm.fields[this.params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN) {
        // Set the active field name
        this.setState({
          showSelector: true,
          activeFieldName: this.params.fieldName,
        });
      } else {
        this.setState({
          viewState: ViewState.SHOW_DETAIL,
        });
      }
    } else {
      showShortToast('Cannot fetch field values');
    }
  }

  renderSelector = () => (
    <VisaFormDropdownSelectorNew
      onSelected={this.onSelected}
      fields={this.fields}
      onBackPressed={this.onSelectorBackPressed}
    />
  );

  onSelectorBackPressed = () => {
    logPDTEvent({
      eventValue: `click_close_from_dropdown_${
        this.visaForm.fields[this.state.activeFieldName]?.displayName
      }`,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });
    this.setState({
      showSelector: false,
    });
  };

  setField = (params) => {
    this.params = params;
    this.setState({
      viewState: ViewState.LOADING_DROPDOWN_DATA,
      activeFieldName: params.fieldName,
    });
    this.fetchNewData();
  };
  // Replace the showDropDown method with this improved version
  showDropDown = (params) => {
    this.params = params;

    if (this.visaForm.fields[params.fieldName].type === FORM_DATA_TYPE_NEW.DROPDOWN) {
      this.fields = [];

      // Get the current value from the field to pre-select it
      const currentValue = this.visaForm.fields[params.fieldName].value || null;

      // Filter and map values
      this.visaForm.fields[params.fieldName].values.forEach((value) => {
        if (this.params.parent === value.parent) {
          this.fields.push({
            key: value.key,
            value: value.value,
            parent: value.parent,
          });
        }
      });

      // Only show selector if we have options
      if (this.fields.length > 0) {
        // Find the option that matches the current value, or default to the first option
        let valueToPreSelect = null;

        // First try to find exact match
        const matchingOption = this.fields.find((f) => String(f.key) === String(currentValue));
        if (matchingOption) {
          valueToPreSelect = matchingOption.key;
        } else if (currentValue) {
          // If we have a current value but no match, still try to use it (in case of string/number type mismatch)
          valueToPreSelect = currentValue;
        } else if (this.fields.length > 0) {
          // Default to first option if no current value
          valueToPreSelect = this.fields[0].key;
        }

        // Update selectedValue state before showing the selector
        this.setState(
          {
            selectedValue: valueToPreSelect,
            activeFieldName: params.fieldName,
            showSelector: true,
          },
          () => {},
        );
      } else {
        showShortToast('No options available');
      }
    }
  };

  onSelected = (field) => {
    this.dropDownSelectedValue = field.key;

    // Create a new copy of the visaForm
    const updatedVisaForm = { ...this.state.visaForm };

    // Update field value in the form
    if (this.params && this.params.fieldName) {
      updatedVisaForm.fields[this.params.fieldName].value = field.key;
      updatedVisaForm.fields[this.params.fieldName].displayValue = field.value;
    }

    // Update child values
    this.updateChildValues(`${this.params.jsonKey}.value`, field.key);
    this.updateChildValues(`${this.params.jsonKey}.parent`, field.parent);
    this.updateChildValues(`${this.params.jsonKey}.displayValue`, field.value);

    // Update state to trigger re-render
    this.setState(
      {
        showSelector: false,
        selectedValue: field.key,
        visaForm: updatedVisaForm,
      },
      () => {
      },
    );
  };
}
const styles2 = (dynamicHeight) => StyleSheet.create({
  allPassengerListContainer: {
      paddingTop: 16,
      flexDirection: 'column',
      maxHeight: dynamicHeight
  },
});
const stylesfile = StyleSheet.create({
  selectorContainer: {
    paddingHorizontal: 16,
    paddingBottom: 0,
    backgroundColor: '#FFFFFF'
  },
  buttonContainer: {
  backgroundColor: colors.white,
    ...paddingStyles.pv16,
    ...paddingStyles.pb24,
  }
})


VisaFormPreFilledComponent.propTypes = {
  visaForm: PropTypes.object.isRequired,
  scrollViewRef: PropTypes.object.isRequired,
  validateForm: PropTypes.bool.isRequired,
};
export default VisaFormPreFilledComponent;