import React, { Component } from 'react';
import { get } from 'lodash';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  Image, 
  Platform, 
  Animated, 
  Easing 
} from 'react-native';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import InputFieldMaster from './InputFieldMaster';
import { FORM_DATA_TYPE_NEW, DAY_SLASH_INDEX, MONTH_SLASH_INDEX, INT_MAX_VAL, DATE_LENGTH } from '../VisaConstants';
import { isValidPattern, getHypotheticalParentNode, getIndex, setErrorField } from '../VisaUtils';
import closeIcon from '@mmt/legacy-assets/src/ic_close_blue.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

class VisaFormTextInputNew extends Component {
  constructor(props) {
    super(props, 'VisaFormPreFilledNew');
    this.textInputRefernce = {};
    this.errorField = '';
    this.text = '';
    this.previousText = '';
    this.formattedText = '';
    this.maxLengthIfTextInput = INT_MAX_VAL;
    
    // Initialize the value from props
    const initialValue = get(this.props, `${this.props.jsonKey}.value`, undefined) || 
      this.props.visaForm.fields[this.props.fieldName].defaultValue;
    
    // Check if placeholder exists
    const hasPlaceholder = !!this.props.visaForm.fields[this.props.fieldName].placeholder;
    
    // Initialize the label animation value
    // If there's a placeholder or initial value, start label at top (1)
    // Otherwise, start label in middle (0)
    this.labelAnim = new Animated.Value(hasPlaceholder || !!initialValue ? 1 : 0);
    
    this.state = {
      updateInput: false,
      validate: this.props.validate,
      isFocused: false,
      hasValue: !!initialValue,
      hasPlaceholder
    };
  }

  componentDidUpdate(prevProps, prevState) {
    // Only animate if there's no placeholder
    if (!this.state.hasPlaceholder) {
      if (prevState.isFocused !== this.state.isFocused || prevState.hasValue !== this.state.hasValue) {
        this.animateLabel();
      }
    }
  }
  
  // Animate the label position (only used when no placeholder is present)
  animateLabel = () => {
    // Only animate if there's no placeholder
    if (!this.state.hasPlaceholder) {
      const toValue = this.state.isFocused || this.state.hasValue ? 1 : 0;
      
      Animated.timing(this.labelAnim, {
        toValue,
        duration: 200,
        easing: Easing.ease,
        useNativeDriver: false
      }).start();
    }
  }

  componentWillReceiveProps(newProps) {
    if (this.props.validate === !newProps.validate) {
      this.setState({
        validate: newProps.validate
      });
    }
  }

  componentWillMount() {
    this.text = get(this.props, `${this.props.jsonKey}.value`, undefined) || this.props.visaForm.fields[this.props.fieldName].defaultValue;
    this.props.updateChildValues(`${this.props.jsonKey}.value`, this.text);
    this.props.updateChildValues(`${this.props.jsonKey}.parent`, getHypotheticalParentNode(this.props.parent, this.props.visaForm.fields[this.props.fieldName]));

    if (this.props.visaForm.fields[this.props.fieldName].type === FORM_DATA_TYPE_NEW.DATE) {
      this.text = this.text.split('-').join('/');
    }
  }

  formatDate = (text) => {
    this.formattedText = text;
    if (this.props.visaForm.fields[this.props.fieldName].type === FORM_DATA_TYPE_NEW.DATE
      && this.previousText.length < this.formattedText.length
      && (this.formattedText.length === DAY_SLASH_INDEX
        || this.formattedText.length === MONTH_SLASH_INDEX)) {
      this.formattedText += '/';
    }
  }

  renderDependents = () => {
    const dependents = [];
    if (this.props.visaForm.fields[this.props.fieldName].dependents &&
      this.props.visaForm.fields[this.props.fieldName].dependents[this.value]) {
      this.props.visaForm.fields[this.props.fieldName].dependents[this.value].forEach((element) => {
        dependents.push(<InputFieldMaster
          focusOnNextField={this.props.focusOnNextField}
          textInputRefs={this.props.textInputRefs}
          labelIndex={`${this.props.labelIndex}.${this.dependents.length + 1}`}
          key={element}
          parent=""
          jsonKey={`${this.props.jsonKey}.dependents`}
          fieldName={element}
          visaForm={this.props.visaForm}
          updateChildValues={this.props.updateChildValues}
          validate={this.state.validate}
          setField={this.props.setField}
          showDropDown={this.props.showDropDown}
        />);
      });
    }
    return dependents;
  }

  getParentJsonKey = (jsonKey) => {
    const keySteps = jsonKey.split('.');
    keySteps.pop();
    return keySteps.join('.');
  }

  shouldShowErrorField = () => {
    if (this.props.validate) {
      this.errorField = setErrorField(this.props.visaForm.fields[this.props.fieldName].mandatory, this.text, this.props.visaForm.fields[this.props.fieldName].error);
      if (this.props.visaForm.fields[this.props.fieldName].mandatory) {
        if (this.text === '') {
          return true;
        }
        return !isValidPattern(new RegExp(this.props.visaForm.fields[this.props.fieldName].validations[getHypotheticalParentNode(this.props.parent, this.props.visaForm.fields[this.props.fieldName])]), this.props.visaForm.fields[this.props.fieldName].type === FORM_DATA_TYPE_NEW.DATE ? this.text.split('/').join('-') : this.text);
      }
      if (this.text === '') {
        return false;
      }
      return !isValidPattern(new RegExp(this.props.visaForm.fields[this.props.fieldName].validations[getHypotheticalParentNode(this.props.parent, this.props.visaForm.fields[this.props.fieldName])]), this.props.visaForm.fields[this.props.fieldName].type === FORM_DATA_TYPE_NEW.DATE ? this.text.split('/').join('-') : this.text);
    }
    return false;
  }

  getDependents = () => {
    const dependents = this.renderDependents();
    if (dependents.length !== 0) {
      return dependents;
    }
  }

  shouldShowCancelIcon = () => {
    if (this.props.visaForm.fields[this.props.fieldName].mandatory) {
      return this.props.visaForm.fields[this.props.fieldName] &&
        this.props.visaForm.fields[this.props.fieldName].addMore &&
        getIndex(this.props.jsonKey) !== '0';
    }
    return this.props.visaForm.fields[this.props.fieldName] &&
      this.props.visaForm.fields[this.props.fieldName].addMore;
  }

  getKeyBoardType = () => {
    if (this.props.visaForm.fields[this.props.fieldName].type === FORM_DATA_TYPE_NEW.DATE) {
      if (Platform.OS === 'ios') {
        return 'numbers-and-punctuation';
      }
      return 'phone-pad';
    }
    return 'default';
  }

  render() {
    if (this.text !== null && this.text !== undefined) {
      this.formatDate(this.text);
      this.previousText = this.formattedText;
      this.text = this.formattedText;
    }
    if (this.props.visaForm.fields[this.props.fieldName].maxLength) {
      this.maxLengthIfTextInput = this.props.visaForm.fields[this.props.fieldName].maxLength;
    }
    
    const { isFocused, hasPlaceholder } = this.state;
    const hasValue = !!this.text;
    const isDisabled = this.props.visaForm.fields[this.props.fieldName].disabled;
    
    // UI color states
    const borderColor = isDisabled ? '#E0E0E0' : (isFocused ? '#008CFF' : '#D8D8D8');
    const backgroundColor = isDisabled ? '#F0F0F0' : (isFocused ? '#EAF5FF' : '#F6F6F6');
    const labelColor = isDisabled ? '#999999' : (isFocused ? '#008CFF' : '#4A4A4A');

    // Animation interpolations for label - only used when no placeholder exists
    const labelTop = this.labelAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [20, 6] // Move from middle to top
    });
    
    const labelFontSize = this.labelAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [14, 12] // Smaller font when at top
    });

    return (
      <View style={[AtomicCss.flex1]}>
        <View
          style={{
            borderWidth: 1,
            borderColor,
            backgroundColor,
            borderRadius: 12,
            paddingTop: 0,
            paddingHorizontal: 12,
            minHeight: 56,
            justifyContent: 'center',
            marginTop: 12,
            opacity: isDisabled ? 0.8 : 1,
          }}>
          
          {/* Label - Either animated (no placeholder) or fixed (with placeholder) */}
          <Animated.Text
            style={{
              position: 'absolute',
              left: 12,
              // If there's a placeholder, always keep label at top (top: 6)
              // Otherwise, animate it between middle and top
              top: hasPlaceholder ? 6 : labelTop,
              fontSize: hasPlaceholder ? 12 : labelFontSize,
              color: labelColor,
              zIndex: 1,
              fontWeight: '700',
              fontFamily: 'Lato',
              lineHeight: 14,
              letterSpacing: 0,
              textTransform: 'uppercase',
              backgroundColor: 'transparent',
            }}
          >
            {this.props.visaForm.fields[this.props.fieldName].displayName}
          </Animated.Text>
          
          <TextInput
            ref={(ref) => {
              if (!this.props.visaForm.fields[this.props.fieldName].disabled) {
                this.props.textInputRefs[`${this.props.labelIndex}`] = ref;
                this.textInputRefernce = ref;
              }
            }}
            editable={!this.props.visaForm.fields[this.props.fieldName].disabled}
            style={{
              fontSize: 16,
              // If there's a placeholder or field has value/focus with no placeholder, 
              // add top padding for the label
              paddingTop: hasPlaceholder || ((hasValue || isFocused) && !hasPlaceholder) ? 18 : 0,
              paddingBottom: 6,
              color: isDisabled ? '#999999' : '#222',
              backgroundColor: 'transparent',
              fontWeight: 'bold',
              // Add margin if no placeholder and label is in center (not focused, no value)
              marginTop: (!hasPlaceholder && !isFocused && !hasValue) ? 14 : 0
            }}
            value={this.text}
            // Only show placeholder if one exists and the field is not focused
            placeholder={hasPlaceholder && !isFocused ? this.props.visaForm.fields[this.props.fieldName].placeholder : ''}
            placeholderTextColor="#AAAAAA"
            onChangeText={text => {
              this.setText(text);
              this.setState({ hasValue: !!text });
            }}
            onFocus={() => this.setState({ isFocused: true })}
            onBlur={() => this.setState({ isFocused: false })}
            blurOnSubmit={false}
            underlineColorAndroid="transparent"
            multiline={this.props.textArea}
            numberOfLines={this.props.textArea ? 4 : 1}
            returnKeyType="next"
            onSubmitEditing={() => {
              this.handleSubmit();
            }}
            keyboardType={this.getKeyBoardType()}
            maxLength={(this.props.visaForm.fields[this.props.fieldName].type === FORM_DATA_TYPE_NEW.DATE) ? (DATE_LENGTH) : this.maxLengthIfTextInput}
          />
          
          {this.shouldShowCancelIcon() &&
            <View style={[styles.flex1, styles.flexRow, styles.mar12Bt, styles.visaFormCloseIcon, { position: 'absolute', right: 0, top: 0, zIndex: 2 }]}> 
              <TouchableOpacity onPress={() => this.props.deleteWRTIndex(this.props.indexInParent)}>
                <Image source={closeIcon} style={styles.visaFormCloseIconImage} />
              </TouchableOpacity>
            </View>}
        </View>
        
        {this.shouldShowErrorField() &&
          <Text style={[styles.ErrorText, styles.font14, styles.regularFont, styles.pad5Top]}>{this.errorField}</Text>}
        
        {this.getDependents()}
      </View>
    );
  }

  handleSubmit = () => {
    if (!this.props.textArea) {
      this.props.focusOnNextField({
        labelIndex: this.props.labelIndex,
        reference: this.textInputRefernce
      });
    }
  }

  setText = (val) => {
    this.text = val;
    const hasValue = !!val;
    
    if (this.props.visaForm.fields[this.props.fieldName].type === FORM_DATA_TYPE_NEW.DATE) {
      this.props.updateChildValues(`${this.props.jsonKey}.value`, this.text.split('/').join('-'));
    } else {
      this.props.updateChildValues(`${this.props.jsonKey}.value`, this.text);
    }
    
    this.props.updateChildValues(`${this.props.jsonKey}.parent`, getHypotheticalParentNode(this.props.parent, this.props.visaForm.fields[this.props.fieldName]));
    this.props.updateChildValues(`${this.props.jsonKey}.dependents`, {});
    
    this.setState({ 
      hasValue,
      updateInput: true 
    });
  }
}

VisaFormTextInputNew.propTypes = {
  labelIndex: PropTypes.string.isRequired,
  parent: PropTypes.string.isRequired,
  jsonKey: PropTypes.string.isRequired,
  visaForm: PropTypes.object.isRequired,
  fieldName: PropTypes.string.isRequired,
  validate: PropTypes.bool.isRequired,
  updateChildValues: PropTypes.func.isRequired,
  textArea: PropTypes.bool.isRequired,
  setField: PropTypes.func.isRequired,
  showDropDown: PropTypes.func.isRequired,
  deleteWRTIndex: PropTypes.func.isRequired,
  indexInParent: PropTypes.number.isRequired,
  textInputRefs: PropTypes.object.isRequired,
  focusOnNextField: PropTypes.func.isRequired
};

export default VisaFormTextInputNew;