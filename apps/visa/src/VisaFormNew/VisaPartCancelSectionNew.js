import React from 'react';
import PropTypes from 'prop-types';
import {View, Text, Image, TouchableOpacity} from 'react-native';
import styles from '../css/VisaMainCss';

const checkedIcon = require('@mmt/legacy-assets/src/tnc_checked.webp');
const unCheckedIcon = require('@mmt/legacy-assets/src/tnc_unchecked.webp');


class VisaPartCancelSectionNew extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      chkboxicon: false
    };
  }

  doConfirmation = () => {
    this.setState({
      chkboxicon: !this.state.chkboxicon
    });
    this.props.onFormConfirmation(!this.state.chkboxicon);
  }

static navigationOptions = {
  header: null
};

render() {
  return (
    <TouchableOpacity style={[styles.flexRow, styles.borderbtm, styles.PickAppCard]} onPress={this.doConfirmation}>
      <Image style={styles.chkboxicon} source={this.state.chkboxicon ? checkedIcon : unCheckedIcon} />
      <View style={styles.VisaContent}>
        <Text style={[styles.defaultGrey, styles.font13, styles.regularFont, styles.lineHeight20]}>I confirm that the <Text style={[styles.BlackText, styles.boldFont]}>documents and information provided are correct</Text> and can directly be used for processing the visa application.</Text>
      </View>
    </TouchableOpacity>
  );
}
}

VisaPartCancelSectionNew.propTypes = {
  onFormConfirmation: PropTypes.func.isRequired
};

export default VisaPartCancelSectionNew;
