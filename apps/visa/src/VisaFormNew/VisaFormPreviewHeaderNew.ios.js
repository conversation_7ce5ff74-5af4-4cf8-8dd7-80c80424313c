import React from 'react';
import {View, Text, Image, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import styles from '../css/VisaMainCss';

const VisaFormPreviewHeaderNew = ({
  onEditFormPress, onBackPressed, subtitle, buttonText, headerTitle
}) => (

  <View style={[styles.stickyHeaderMain]}>
    <View style={[styles.topStatusBar, styles.flexRow, styles.spaceBetween, styles.mar12Rt]}>
      <View style={styles.stickyBarWrapperDocument}>

        <TouchableOpacity
          onPress={onBackPressed}
          style={[styles.padding5]}
        >
          <Image style={[styles.mar3Bt, styles.backarrow]} source={backIcon} />
        </TouchableOpacity>
        <View style={styles.stickyTextWrapper}>
          <Text numberOfLines={1} ellipsizeMode="tail" style={[styles.font18, styles.BlackText, styles.lightFont, styles.mar3Bt]}>
            {headerTitle}
          </Text>
          <Text style={[styles.defaultGrey, styles.regularFont, styles.font11]}>
            {subtitle}
          </Text>
        </View>
      </View>
      <TouchableOpacity style={{marginTop: -25}} onPress={onEditFormPress}>
        <Text style={[styles.blueLink, styles.font14, styles.boldFont]}>{buttonText}</Text>
      </TouchableOpacity>
    </View>
  </View>
);

VisaFormPreviewHeaderNew.propTypes = {
  buttonText: PropTypes.string.isRequired,
  headerTitle: PropTypes.string.isRequired,
  onEditFormPress: PropTypes.func.isRequired,
  onBackPressed: PropTypes.func.isRequired,
  subtitle: PropTypes.string.isRequired
};


export default VisaFormPreviewHeaderNew;
