import React from 'react';
import { View, ScrollView, Text, ActivityIndicator, Keyboard } from 'react-native';
import { KeyboardAwareView } from 'react-native-keyboard-aware-view';
import PropTypes from 'prop-types';
import styles from '../css/VisaMainCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import VisaFormHeaderNew from './VisaFormHeaderNew';
import VisaFormButtonNew from './VisaFormButtonNew';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';

import { showShortToast,showLongToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { PAGENAME, TRACKING_PAGENAME } from '../VisaConstants';
import VisaTrackingHelper from '@mmt/legacy-commons/Helpers/VisaTrackingHelper';
import { savePartialFormResponse } from '../VisaNetworkUtils';
import {
  formatNavSubTitle,
  getDisplayDate,
  getFormResponse
} from '../VisaUtils';
import VisaFormPreFilledComponent from './VisaFormPreFilledComponent';
import { capitalize, cloneDeep } from 'lodash';
import { VisaNavigation } from '../Navigation';
import ErrorMessage from '../AddPassportPhoto/ErrorMessage';

class MultipleVisaFormPreFilled extends BasePage {
  static navigationOptions = {
    header: null
  };

  constructor(props) {
    super(props, 'MultipleVisaFormPreFilled');
    this.visaForms = this.props.visaForms;
    this.state = {
      loaderText: 'Loading visa application form...',
      savingForm: false,
      validateForm: false,
      softErrorMessages: [],
    };

    this.scrollViewWindowOffset = 0;
    this.textInputRefs = {};
    this.scrollIndex = 0;
    this.scrollViewRef = null;
    this.scrollViewContentHeight = 0;

    this.previousPage = this.props.visaParams.page;
    this.visaParams = { ...this.props.visaParams, page: PAGENAME.APPLICATION_FORM_PREFILLED };
    this.params = [];
    this.paxName = capitalize(this.getTraveller().name);
  }
  getTraveller = () => {
    return this.visaParams.paxList[0];
  }
  onBackPressed = () => {
    VisaNavigation.pop();
    this.trackClickEvent('back_clicked');
  };


  componentDidMount() {
    super.componentDidMount();
    if (!this.visaForms) {
      this.fetchVisaForm();
    }
    VisaTrackingHelper.trackVisaTypeOnwardsPageVisit(
      TRACKING_PAGENAME.APPLICATION_FORM_PREFILLED,
      this.previousPage, this.visaParams
    );
  }

  componentDidUpdate() {
    if (this.scrollViewRef) {
      setTimeout(() => {
        if (this.scrollViewRef) {
          this.scrollViewRef.scrollTo({ x: 0, y: this.scrollIndex, animated: false });
        }
      }, 1);
    }
  }

  render() {
    return (
      <View style={{ flex: 1 }}>
        {this.renderContent()}
      </View>
    );
  }


  renderContent = () => (
    <KeyboardAwareView
      animated
      style={[styles.whitebg, styles.flex1, styles.spaceBetween]}
    >
      <VisaFormHeaderNew
        onBackPressed={this.onBackPressed}
        subTitle={formatNavSubTitle(this.visaParams.destCountry, getDisplayDate(this.visaParams.startDate), this.paxName)}
      />
      <ScrollView
        onScroll={this.handleScroll}
        ref={(ref) => {
          this.scrollViewRef = ref;
        }}
        keyboardShouldPersistTaps="handled"
      >
        {this.renderVisaForms()}
        {this.state.softErrorMessages && this.state.softErrorMessages.length > 0 && <ErrorMessage
          warning={false}
          title="Please fix errors"
          msg={this.state.softErrorMessages}
        />}

        {this.state.savingForm ? <ActivityIndicator size="large" color="#065af3" /> :
          <VisaFormButtonNew
            buttonPress={this.onPartialFormSave}
            btntext="SAVE FORM"
            enable
          />}
      </ScrollView>
    </KeyboardAwareView>
  )
  renderVisaForms = () => (
    this.visaForms.map((form, index) => {
      const { countryName, formSchema } = form;
      let shouldValidateForms = false;
      if (this.props.validateForm) {
        shouldValidateForms = true;
      } else if (this.state.validateForm) {
        shouldValidateForms = true;
      }
      // If there are 2  forms and one form's fields are subset of another then there is  no need   toshow  subset  form
      return formSchema.isSubset ? null : (
        <>
          {/* <Text style={styles.MultipleFormSectionTitle}>{`${countryName} Application Form`}</Text> */}
          <VisaFormPreFilledComponent
            key={index}
            index={index}
            visaForm={formSchema}
            scrollViewRef={this.scrollViewRef}
            validateForm={shouldValidateForms}
          />
        </>
      )
    })
  )

  handleScroll = (event) => {
    this.scrollIndex = event.nativeEvent.contentOffset.y;
    this.scrollViewContentHeight = event.nativeEvent.contentSize.height;
  }
  trackClickEvent = (clickEvent) => {
    VisaTrackingHelper.trackVisaTypeOnwardsClickEvent(
      TRACKING_PAGENAME.APPLICATION_FORM_PREFILLED,
      clickEvent, this.visaParams, this.previousPage
    );
  }

  onPartialFormSave = async () => {
    Keyboard.dismiss();
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      showShortToast('No Internet!');
      return;
    }
    this.setState({ savingForm: true });
    const request = {
      saveForms: []
    }
    this.visaForms.forEach((form, index) => {
      request.saveForms.push({
        payRefId: form.payRefId,
        paxIndex: Number(this.getTraveller().id),
        formResponse: index === 0 ? form.formSchema.fieldValues : getFormResponse(this.visaForms[0], form, this.props.commonFormKeys)
      });
    });

    this.trackClickEvent('save_partial_form');
    const response = await savePartialFormResponse(request);
    this.setState({ savingForm: false });
    if (response) {
      if (response.saved) {
        this.props.onPartialFormSave(cloneDeep(this.visaForms));
        this.onBackPressed();
        showShortToast('Application form saved!');
      } else {
        this.setState({ validateForm: true });
        if (response.message) {
          let softErrorMessages = [];
          softErrorMessages.push(response.message);
          this.setState({ softErrorMessages: softErrorMessages });
          showLongToast('Please resolve form errors.\n' + response.message);
        }
        else {
          showShortToast('Please resolve form errors.');
        }
      }
    } else {
      this.setState({ validateForm: true });
      showShortToast('Something went wrong, try again.');
    }
  }
}

MultipleVisaFormPreFilled.propTypes = {
  tabIndex: PropTypes.number,
  visaForm: PropTypes.object,
  commonFormKeys: PropTypes.array.isRequired,
  visaParams: PropTypes.object.isRequired,
  validateForm: PropTypes.bool.isRequired,
  onPartialFormSave: PropTypes.func.isRequired
};
export default MultipleVisaFormPreFilled;
