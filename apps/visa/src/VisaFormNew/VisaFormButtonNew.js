import React, {PureComponent} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import styles from '../css/VisaMainCss';


class VisaFormButtonNew extends PureComponent {
  render() {
    return (
      <View >
        {this.props.enable && this.renderButtonEnable()}
        {!this.props.enable && this.renderButtonDisable()}
      </View>
    );
  }

  renderButtonDisable = () => (
    <View style={[styles.checkAvlBtnDisable, styles.mar10Bt, styles.pad15Top, styles.pad20Bt]} >
      <Text style={[styles.WhiteText, styles.CenterText, styles.font12, styles.boldFont, styles.bgTransparent]}>
        {this.props.btntext}
      </Text>
    </View>
  )

  renderButtonEnable = () => (
    <TouchableOpacity onPress={this.props.buttonPress}>
      <LinearGradient
        start={{x: 1.0, y: 0.0}}
        end={{x: 0.0, y: 1.0}}
        colors={['#065af3', '#53b2fe']}
        style={[styles.checkAvlBtn, styles.mar10Bt]}
      >
        <Text style={[styles.WhiteText, styles.CenterText, styles.font12, styles.boldFont, styles.bgTransparent]}>
          {this.props.btntext}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  );
}

VisaFormButtonNew.propTypes = {
  enable: PropTypes.bool.isRequired,
  buttonPress: PropTypes.func.isRequired,
  btntext: PropTypes.string.isRequired
};

export default VisaFormButtonNew;

