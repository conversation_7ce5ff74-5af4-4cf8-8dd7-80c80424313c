export type QCWidgetViewProps = {
  params: Params;
  onInfoButtonTap: () => void;
  onPayButtonTap: (qcData?: string) => void;
  trackQcEvents: (omnitureEvents: QC_OMNITURE_AND_PDT_EVENT) => void;
  isQCExpanded?: (isExpanded: boolean) => void;
  showPayNowLoading: Boolean
};

export type NativeQCWidgetViewProps = {
  style: { height: number };
  params: string;
  showPayLoader: Boolean;
};

export type QCHeightUpdatedData = {
  isExpanded: boolean;
  height: number;
};

type Params = {
  displayInfo: DisplayInfo;
  requestInfo: RequestInfo;
};

type DisplayInfo = {
  price?: string;
  priceTag?: string;
  subtitle?: string;
  failureCtaText?: string;
  successCtaText?: string;
  showInfo?: boolean;
  amount?: number;
  currency: string;
  subTextHexColor?: string;
};

type RequestInfo = {
  ackId: string;
  refresh: boolean;
};

export type PayModeData = PayModeDataAndroid | PayModeDataIos;

type PayModeDataAndroid = {
  qc_lob_source: string;
  qcData: string;
};

type PayModeDataIos = {
  lobSource: string;
  qcData: string;
};

export type PayModeDataParsed = PayModeDataParsedAndroid | PayModeDataParsedIos;

type PayModeDataParsedAndroid = {
  qc_lob_source: string;
  qc_meta: QCMetaData;
};

type PayModeDataParsedIos = {
  lobSource: string;
  qcData: QCMetaData;
};

type QCMetaData = {
  data?: MetaData;
  trackingData: TrackingData;
};

type MetaData = {
  additionalDiscountAllowed: boolean;
  bankCode: string;
  blockPaymentOption: boolean;
  cardInfo: CardInfo;
  couponApplicable: boolean;
  couponStatus: string;
  currency: string;
  displayName: string;
  downPaymentOption: boolean;
  id: string;
  logoUrl: string;
  maskedCardNo: string;
  networkConsentRequired: boolean;
  payMode: string;
  payOption: string;
  payOptionDisplayDetails: PayOptionDisplayDetails;
  type: string;
  upiDownPaymentOption: boolean;
};

type TrackingData = {
  cardSequence: string;
  cardUsed: string;
};

type CardInfo = {
  atmEnabled: boolean;
  cardType: string;
  cardTypeUrl: string;
  crcBin: boolean;
  cvvLength: number;
  cvvRequired: boolean;
  dpEnabled: boolean;
  emiAvailable: boolean;
  otpEnabled: boolean;
  payOptions: PayOptions;
};

type PayOptions = {
  defaultPayOption: string;
  defaultSelectedPayOption: string;
};

type PayOptionDisplayDetails = {
  subTitle1: string;
  title: string;
};

export type QuickCheckoutTrackingData = QuickCheckoutTrackingDataAndroid | QuickCheckoutTrackingDataIos;

type QuickCheckoutTrackingDataAndroid = {
  qc_tracking_data: string;
};

type QuickCheckoutTrackingDataIos = OmniturePdtObject;

export type QuickCheckoutTrackingDataParsed = {
  qc_tracking_data: OmniturePdtObject;
};

type OmniturePdtObject = {
  omnitureEvent: QC_OMNITURE_AND_PDT_EVENT;
  pdtEvent: QC_OMNITURE_AND_PDT_EVENT;
};

export enum QC_OMNITURE_AND_PDT_EVENT {
  QC_WIDGET_SHOWN = 'quick_checkout_widget_shown', // comes only in android
  QC_LOAD = 'qc_load',
  QC_RELOAD = 'qc_reload',
  QC_INFO_BUTTON_CLICK = 'i_button_clicked',
  QC_ALL_PAYMENT_OPTIONS_CLICK = 'change_all_payment_options_clicked',
  QC_DIFFERENT_PAYMODE_CLICKED = 'different_paymode_clicked',
  QC_CHANGE_CLICKED = 'change_clicked',
  QC_PAY_NOW_CLICKED = 'paynow_clicked',
  QC_CHANGE_CARD_CLICKED = 'change_card_clicked', // comes only in android
}
