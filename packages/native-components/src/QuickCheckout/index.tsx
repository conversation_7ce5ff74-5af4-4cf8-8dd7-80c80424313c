import React, { useEffect, useMemo, useState } from 'react';
import { StyleSheet } from 'react-native';
import { requireNativeComponent, View, DeviceEventEmitter, SafeAreaView } from 'react-native';
import { EVENT_KEYS } from './constants';
import {
  QuickCheckoutTrackingData,
  NativeQCWidgetViewProps,
  PayModeData,
  QCHeightUpdatedData,
  QCWidgetViewProps,
  QuickCheckoutTrackingDataParsed,
} from './types';
import { isAndroid, isIos } from '@mmt/core/helpers/platformHelper';

const NativeQCWidgetView = requireNativeComponent<NativeQCWidgetViewProps>('QCWidgetView');

export type { PayModeDataParsed } from './types';
export { QC_OMNITURE_AND_PDT_EVENT } from './types';
export const QCWidgetView: React.FC<QCWidgetViewProps> = ({
  params,
  onInfoButtonTap,
  onPayButtonTap,
  trackQcEvents = () => { },
  showPayNowLoading = false,
  isQCExpanded = () => {}
}) => {

  const [qcConfig, setQcConfig] = useState<QCHeightUpdatedData>({
    isExpanded: false, // This is only used in IOS to apply qcModal style on the wrapper view
    height: 63,
  });

  useEffect(() => {
    const infoBtnSub = DeviceEventEmitter?.addListener(EVENT_KEYS.infoButtonClick, () => {
      if (onInfoButtonTap) {
        onInfoButtonTap();
      }
    });

    const payBtnSub = DeviceEventEmitter?.addListener(
      EVENT_KEYS.payButtonClick,
      (data: PayModeData) => {
        try {
          if ('qcData' in data && data.qcData && data.qcData !== '') {
            onPayButtonTap(data.qcData);
          } else {
            onPayButtonTap();
          }
        } catch (err) {
          onPayButtonTap();
          console.error('Error in pay button event listener of Quick Checkout: ', e);
        }
      },
    );

    const heightUpdatedSub = DeviceEventEmitter?.addListener(
      EVENT_KEYS.qcHeightUpdated,
      (data: QCHeightUpdatedData) => {
        setQcConfig((prevData) => ({ ...prevData, ...data }));
        isQCExpanded(data?.isExpanded || false);
      },
    );

    const trackUpdateSub = DeviceEventEmitter?.addListener(
      EVENT_KEYS.gettingTrackingData,
      (data: QuickCheckoutTrackingData) => {
        if (isAndroid() && 'qc_tracking_data' in data) {
          const { omnitureEvent }: QuickCheckoutTrackingDataParsed['qc_tracking_data'] = JSON.parse(
            data.qc_tracking_data,
          );
          trackQcEvents(omnitureEvent);
        } else if (isIos() && 'omnitureEvent' in data) {
          const { omnitureEvent } = data;
          trackQcEvents(omnitureEvent);
        }
      },
    );

    return () => {
      infoBtnSub?.remove();
      payBtnSub?.remove();
      heightUpdatedSub?.remove();
      trackUpdateSub?.remove();
    };
  }, [onPayButtonTap]);

  const stringifiedParams = useMemo(() => {
    try {
      return JSON.stringify(params);
    } catch (err) {
      return '';
    }
  }, [params]);

  return (
    <SafeAreaView>
      <View style={qcConfig.isExpanded && styles.qcModal}>
        <NativeQCWidgetView
          params={stringifiedParams}
          style={{
            height: qcConfig.height,
          }}
          showPayLoader={showPayNowLoading}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  qcModal: { position: 'absolute', bottom: 0 },
});
