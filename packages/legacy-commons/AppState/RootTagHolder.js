let _rootTag = [];
export const pushRootTag = rootTag => {
  _rootTag.push(rootTag);
};
export const popRootTag = (topRootTag) => {
  if (typeof topRootTag === 'number') {
    _rootTag = _rootTag.filter(tag => tag !== topRootTag);
    return;
  }
  console.log('@mmt10296:: popRootTag = ', _rootTag);
  if (_rootTag.length) {
    _rootTag.pop();
  }
};
export const getRootTag = () => {
  if (_rootTag.length) {
    return _rootTag[_rootTag.length - 1];
  }
  return 1;
};
