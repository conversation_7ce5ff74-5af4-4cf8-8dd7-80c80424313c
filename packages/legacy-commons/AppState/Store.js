import { applyMiddleware, combineReducers, compose, createStore } from 'redux';
import thunkMiddleware from 'redux-thunk';
import { createLogger } from 'redux-logger';
import { actionsLoggerMiddleware, shouldLogAction } from '../Helpers/actionsLogger';

/*
 FIXME @rajesh.batth Consolidate features of these into single file
    - Store.js
    - Store2.js
    - asyncStore.js
    - asyncStorev2.js
* */

/**
 * ! =============================== NOTE =============================== !
 * `collapsed: true` check has been added because the logs were difficult to read
 * as they were expanded and the amount of data being logged on console made it
 * tricky for eyes to keep track of action names being fired. whereas if we keep
 * collapsed, the console gives a crisp visibility of each action name. the
 * check has been added by `MMT9107`, please let me know if you believe
 * this check should be removed or have a different perspective.
 */

const ASYNC_REDUCERS_PROPERTY = Symbol('asyncReducers');

const loggerMiddleware = createLogger({
  collapsed: true,
  predicate: (getState, action) => __DEV__ && shouldLogAction(action),
});

export function configureStore(initialState = {}) {
  const { reducer: initialReducersMapObj, resetReducer: resetReducerWrapper } =
    global.reduxProviders.default();
  const enhancer =
    process.env.NODE_ENV !== 'test'
      ? compose(
          applyMiddleware(
            thunkMiddleware, // lets us dispatch() functions
            loggerMiddleware,
            actionsLoggerMiddleware,
          ),
        )
      : compose(applyMiddleware(thunkMiddleware));
  const combinedReducers = combineReducers(initialReducersMapObj);

  const asyncStore = createStore(resetReducerWrapper(combinedReducers), initialState, enhancer);

  asyncStore[ASYNC_REDUCERS_PROPERTY] = initialReducersMapObj;
  asyncStore.addReducers = function (newReducersMapObj) {
    const newFinalReducers = {
      ...this[ASYNC_REDUCERS_PROPERTY],
      ...newReducersMapObj,
    };
    this.replaceReducer(combineReducers(newFinalReducers));
    this[ASYNC_REDUCERS_PROPERTY] = newFinalReducers;
  };
  return asyncStore;
}

const store = configureStore({});
export default store;
