import { applyMiddleware, compose, createStore } from '@mmt/redux';
import thunkMiddleware from 'redux-thunk';
import { createLogger } from 'redux-logger';
import { shouldLogAction } from '../Helpers/actionsLogger';

const loggerMiddleware = createLogger({
  predicate: (getState, action) => __DEV__ && shouldLogAction(action),
});

export function configureStore(initialState, reducer) {
  const enhancer = compose(applyMiddleware(
    thunkMiddleware, // lets us dispatch() functions
    loggerMiddleware,
  ));
  return createStore(reducer, initialState, enhancer);
}

