import * as Sentry from '@sentry/react-native';
import { NativeModules } from 'react-native';
import {Platform} from 'react-native';
import { gitHash } from '@mmt/build-info';

export function initErrorReporter(){
    if(!__DEV__) {
        const { GenericModule } = NativeModules;
        listenNonFatalErrors()
        const ignoreErrorTypes = new Set([
              'ApplicationNotResponding' // crashlytics will catch this
          ]
        )
        try {
            Sentry.init({
                dsn: 'https://<EMAIL>/5933270',
                enableNativeCrashHandling: false,
                // based on reduction in crash-count, we can increase the sample rate in sept'24 release
                sampleRate: Platform.select({ ios: 0.5, default: 0.25 }),
                tracesSampleRate: 0,
                beforeSend(event, hint) {
                    const error = hint.originalException
                    if (event.level !== 'fatal') {
                      if (GenericModule && GenericModule.reportNonFatal) {
                        // this will get logged in crashlytics under the LoB group
                        GenericModule.reportNonFatal(error?.toString());
                      }
                      // in the future, instead of ignoring these, we can add sampling logic
                      return null; // ignore non-fatal errors completely
                    }
                    if (ignoreErrorTypes.has(error?.type)) {
                        return null
                    }
                    return event;
                }
            });
            Sentry.setTag('git-hash', gitHash);
        } catch (e) {
            console.log('Sentry init exception', e);
        }
    }

}

export function onPageChange(page, lobId){
    const lobIdCleaned = lobId.replace('@mmt-rn/', '')
      .replace('@trip-money/', 'tm-')
      .replace('@', '')
      .replace('/', '__');
    Sentry.setTag('lob', lobIdCleaned);
    Sentry.setTag('screen', page);

    const { GenericModule } = NativeModules;
    if (GenericModule) {
        if (GenericModule.onPageChanged) {
            GenericModule.onPageChanged(page);
        }
        if (GenericModule.onLobChanged) {
            GenericModule.onLobChanged(lobIdCleaned);
        }
    }
}

function listenNonFatalErrors() {
    global.onUnhandledPromiseRejection = (error) => {
        const { GenericModule } = NativeModules;
        if (GenericModule && GenericModule.reportNonFatal) {
            GenericModule.reportNonFatal(new Error(`Unhandled Promise Rejection: ${error}`).stack);
        }
    }
}

export function reportPageNotFound(pageName){
    const { GenericModule } = NativeModules;
    if (GenericModule && GenericModule.reportGlobalException) {
        GenericModule.reportGlobalException('INVALID_PAGE', pageName);
    }
}
