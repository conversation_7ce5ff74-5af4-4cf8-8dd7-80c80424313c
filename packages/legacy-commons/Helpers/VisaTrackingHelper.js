import {diffDays, today} from './dateHelpers';
import {
  VAR_SRC_DEST,
  VAR_START_DATE,
  VAR_END_DATE,
  VAR_DURATION,
  VAR_AP,
  VAR_SEARCH_PAX,
  VAR_PREVIOUS_PAGENAME,
  VAR_LOB_KEY,
  VAR_CHANNEL,
  trackPageVisits,
  trackClickEvent,
  PROP_1
} from '../Common/utils/OmnitureTrackerUtils';

const getDate = (dateStr) => {
  if (dateStr.indexOf('-') !== -1) {
    const splitDate = dateStr.split('-');
    const date = new Date(parseInt(splitDate[2]), parseInt(splitDate[1]) - 1, parseInt(splitDate[0]));
    return date;
  }
  return dateStr;
};

const EVAR_108 = 'evar108';

class VisaTrackingHelper {
  getLobCommonEVars = (previousPage, pageName, isFromMima=false) => ({
    [VAR_PREVIOUS_PAGENAME]: previousPage,
    [VAR_LOB_KEY]: 'mob visa',
    [VAR_CHANNEL]: 'mob visa funnel',
    [PROP_1]: this.formatPropValue(pageName, isFromMima),
    [EVAR_108]: this.formatPageName(pageName[0], isFromMima)
  })

  pageNameKeyMap = {
    LANDING: 'landing',
    LANDING_NEW: 'visaLandingNew',
    DETAILS: 'details',
    UPLOAD: 'upload',
    VIEW: 'view',
    REVIEW: 'reviewpage',
    THANKYOU: 'thankyoupage',
    MULTI_COUNTRY_NIGHTS: 'schengenmultinights',
    FAQS: 'faqs',
    SEARCH: 'search',
    ADD_DOCUMENTS: 'adddocuments',
    UPLOAD_DOCUMENTS: 'documentupload',
    ADD_TRAVELLER: 'travellerInfo',
    SELECT_PLANS: 'selectplans',
    LEARN_MORE: 'learnmore',
    BULK_UPLOAD: 'bulkupload',
    SAMPLE_DOC: 'sampledoc',
    BULK_UPLOAD_EDIT: 'bulkUploadEdit',
    BULK_UPLOAD_CONFIRM: 'bulkUploadConfirm',
    ADD_DOCUMENTS_REVAMP: 'visaAddDocumentsRevamp',
    UPLOAD_DOCUMENTS_REVAMP: 'visaUploadDocumentsRevamp',
  }
  formatPageName = (pageName, isFromMima) => {
    if(pageName.indexOf('landing') !== -1){
      return `Visa|Landing|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.LANDING_NEW) !== -1){
      return `Visa|Landing|MMT`;
    }
    if(pageName.indexOf('details') !== -1){
      return `Visa|Details|MMT`;
    }
    if(pageName.indexOf('upload') !== -1){
      return isFromMima ? `Visa|Upload Visa Docs|Holidays-Visa-MMT` : 'Visa|Upload Visa Docs|MMT';
    }
    if(pageName.indexOf(this.pageNameKeyMap.REVIEW) !== -1){
      return `Visa|Review|MMT`;
    }
    if(pageName.indexOf('view') !== -1){
      return `Visa|View Visa Docs|MMT`;
    }
    if(pageName.indexOf('review') !== -1){
      return `Visa|Review|MMT`;
    }
    if(pageName.indexOf('thankyou') !== -1){
      return `Visa|Thank You|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.MULTI_COUNTRY_NIGHTS) !== -1){
      return `Visa|Schengen Multi Nights|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.SELECT_PLANS) !== -1){
      return `Visa|Select Plans|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.LEARN_MORE) !== -1){
      return `Visa|Learn More|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.BULK_UPLOAD) !== -1){
      return `Visa|Bulk Upload|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.BULK_UPLOAD_EDIT) !== -1){
      return `Visa|Bulk Upload Edit|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.BULK_UPLOAD_CONFIRM) !== -1){
      return `Visa|Bulk Upload Confirm|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.ADD_DOCUMENTS) !== -1){
      return `Visa|Add Documents|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.ADD_TRAVELLER) !== -1){
      return `Visa|Add Traveller|MMT`;
    }
    if(pageName.indexOf(this.pageNameKeyMap.SEARCH) !== -1){
      return `Visa|Search|MMT`;
    }
  }
  formatPropValue = (page, isFromMima) => {
    const [pageName, eventName] = [page[0], page[1]];
    if(pageName.indexOf('upload') !== -1 || pageName.indexOf('view') !== -1){
      return isFromMima ? `${eventName}|MyTrips` : `${eventName}|funnel`;
    }
    return eventName;
  }
  populateOmnitureParams = (visaParams, paxList = []) => {
    let advancePurchaseDays = '';
    let duration = '';

    let startDate;
    let endDate;
    if (visaParams.startDate) {
      startDate = getDate(visaParams.startDate);
      advancePurchaseDays = parseInt(diffDays(startDate, today()), 10);

      if (visaParams.endDate) {
        endDate = getDate(visaParams.endDate);
        duration = parseInt(diffDays(endDate, startDate), 10);
      }
    }

    let searchPaxValue = '';
    if (paxList) {
      const commaSeparatedAgesString = paxList.map(elem => elem.age).join(',');
      searchPaxValue = (`${paxList.length}|${commaSeparatedAgesString}`);
    }
    return {
      [VAR_SRC_DEST]: visaParams.destCountry,
      [VAR_START_DATE]: visaParams.startDate,
      [VAR_END_DATE]: visaParams.endDate,
      [VAR_DURATION]: duration,
      [VAR_AP]: advancePurchaseDays,
      [VAR_SEARCH_PAX]: searchPaxValue
    };
  }

  trackVisaSearchPageVisit = (pageName, previousPage, destination, depDate, returnDate) => {
    let _visaEVars = this.getLobCommonEVars(previousPage, pageName);
    const visaParams = {
      destCountry: destination,
      startDate: depDate,
      endDate: returnDate
    };
    if (visaParams) {
      const evars = this.populateOmnitureParams(visaParams);
      _visaEVars = {..._visaEVars, ...evars};
    }
    trackPageVisits(pageName[0], _visaEVars);
  }

  trackVisaTypeOnwardsPageVisit = (pageName, previousPage, visaParams) => {
    let _visaEVars = this.getLobCommonEVars(previousPage, pageName);
    if (visaParams) {
      const evars = this.populateOmnitureParams(visaParams, visaParams.paxList);
      evars.m_c15 = visaParams.landingABVariable;
      evars.m_v12 = visaParams.bookingType;
      evars.m_v40 = visaParams.selectedVisa;
      _visaEVars = {..._visaEVars, ...evars};
    }
    trackPageVisits(pageName[0], _visaEVars);
  }

  trackVisaTypeOnwardsClickEvent = (pageName, clickEvent, visaParams, previousPage) => {
    const isFromMima = visaParams && visaParams.fromMIMA;
    let _visaEVars = this.getLobCommonEVars(previousPage, pageName, isFromMima);

    if (visaParams) {
      const evars = this.populateOmnitureParams(visaParams, visaParams.paxList, previousPage);
      evars.m_v12 = visaParams.bookingType;
      evars.m_v40 = visaParams.selectedVisa;
      _visaEVars = {..._visaEVars, ...evars};
    }
    trackClickEvent(pageName[0], clickEvent, _visaEVars);
  };

  trackVisaClickEvent = (pageName, clickEvent, visaParams, paxList, previousPage) => {
    let _visaEVars = this.getLobCommonEVars(previousPage, pageName);
    if (visaParams) {
      const evars = this.populateOmnitureParams(visaParams, paxList, previousPage);
      _visaEVars = {..._visaEVars, ...evars};
    }
    trackClickEvent(pageName[0], clickEvent, _visaEVars);
  };


  trackVisaClickEventV2 = ({pageName, clickEvent, visaParams, paxList, previousPage, extraParams = {}}) => {
    let _visaEVars = this.getLobCommonEVars(previousPage, [pageName]);
    if(_visaEVars && _visaEVars[EVAR_108]) {
      _visaEVars = {..._visaEVars, [EVAR_108]: _visaEVars?.[EVAR_108]?.replace('Visa', 'Visa-V2')};
    }
    if (visaParams) {
      const evars = this.populateOmnitureParams(visaParams, paxList, previousPage);
      _visaEVars = {..._visaEVars, ...evars};
    }
    if(extraParams) {
      _visaEVars = {..._visaEVars, ...extraParams};
    }
    /* only change m_ch for landing page, for all other pages keep the same */
    if(pageName === this.pageNameKeyMap.LANDING_NEW) {
      _visaEVars[VAR_CHANNEL] = 'mob visa landing';
    }
    const omniPageName = `mob:funnel:visa:${pageName}`
    trackClickEvent(omniPageName, clickEvent, _visaEVars);
  };

  trackVisaDocumentReqdPageVisit = (pageName, visaParams, paxList, previousPage) => {
    let _visaEVars = this.getLobCommonEVars(previousPage, pageName);

    const evars = this.populateOmnitureParams(visaParams, paxList, previousPage);
    evars.m_v12 = visaParams.bookingType;
    evars.m_v40 = visaParams.selectedVisa;
    _visaEVars = {..._visaEVars, ...evars};
    trackPageVisits(pageName[0], _visaEVars);
  };


  /*
  Flag used for calculating previousPage
  - isFromSearch: TypeOfVisa screen
  - isFromLanding: DocVerification
  */
  trackVisaPageVisit = (pageName, visaParams, paxList, previousPage) => {
    let _visaEVars = this.getLobCommonEVars(previousPage, pageName);

    const evars = this.populateOmnitureParams(visaParams, paxList, previousPage);
    _visaEVars = {..._visaEVars, ...evars};
    trackPageVisits(pageName[0], _visaEVars);
  };
}


export default new VisaTrackingHelper();
