import React from 'react';
import PropTypes from 'prop-types';
import {Platform, View} from 'react-native';


import {WebView} from 'react-native-webview';


class Canvas extends React.Component {

  render() {
    const contextString = JSON.stringify(this.props.context);
    const renderString = this.props.render.toString();
    return (
      <View style={this.props.style}>
        <WebView
          automaticallyAdjustContentInsets={false}
          scalesPageToFit={Platform.OS === 'android'}
          contentInset={{
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
          }}
          source={{html: `<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>*{margin:0;padding:0;}canvas{transform:translateZ(0);}</style></head><body><canvas></canvas><script>var canvas = document.querySelector('canvas');(${renderString}).call(${contextString}, canvas);</script></body></html>`}}
          opaque={false}
          underlayColor="transparent"
          style={this.props.style}
          javaScriptEnabled
          scrollEnabled={false}
          onLoad={this.props.onLoad}
          onLoadEnd={this.props.onLoadEnd}
          originWhitelist={['*']}
        />
      </View>
    );
  }
}

Canvas.propTypes = {
  style: PropTypes.object,
  context: PropTypes.object,
  render: PropTypes.func.isRequired,
  onLoad: PropTypes.func,
  onLoadEnd: PropTypes.func
};


export default Canvas;
