import React from 'react';
import PropTypes from 'prop-types';
import qr from 'qr.js';
import {View} from 'react-native';
import Canvas from './Canvas';


function renderCanvas(canvas) {
  const ctx = canvas.getContext('2d');
  const size = this.size;
  const fgColor = this.fgColor;
  const bgColor = this.bgColor;
  canvas.width = size;
  canvas.height = size;
  canvas.style.left = `${(window.innerWidth - size) / 2}px`;
  if (window.innerHeight > size) canvas.style.top = `${(window.innerHeight - size) / 2}px`;
  ctx.fillRect(0, 0, size, size);
  const cells = this.cells;
  const cellWidth = this.size / cells.length;
  const cellHeight = this.size / cells.length;
  const nRoundedWidth = Math.round(cellWidth);
  const nRoundedHeight = Math.round(cellHeight);
  cells.forEach((row, rowIndex) => {
    row.forEach((column, columnIndex) => {
      const nLeft = columnIndex * cellWidth;
      const nTop = rowIndex * cellHeight;
      ctx.fillStyle = ctx.strokeStyle = column ? bgColor : fgColor;
      ctx.lineWidth = 1;
      ctx.fillRect(nLeft, nTop, cellWidth, cellHeight);
      ctx.strokeRect(
        Math.floor(nLeft) + 0.5,
        Math.floor(nTop) + 0.5,
        nRoundedWidth,
        nRoundedHeight
      );
      ctx.strokeRect(
        Math.ceil(nLeft) - 0.5,
        Math.ceil(nTop) - 0.5,
        nRoundedWidth,
        nRoundedHeight
      );
    });
  });
}

class QRCode extends React.Component {
  utf16to8(str) {
    let out,
      i,
      len,
      c;
    out = '';
    len = str.length;
    for (i = 0; i < len; i++) {
      c = str.charCodeAt(i);
      if ((c >= 0x0001) && (c <= 0x007F)) {
        out += str.charAt(i);
      } else if (c > 0x07FF) {
        out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
        out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
        out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
      } else {
        out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
        out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
      }
    }
    return out;
  }

  render() {
    const size = this.props.size;
    const value = this.utf16to8(this.props.value);
    return (
      <View>
        <Canvas
          context={{
            size,
            value: this.props.value,
            bgColor: this.props.bgColor,
            fgColor: this.props.fgColor,
            cells: qr(value).modules
          }}
          render={renderCanvas}
          onLoad={this.props.onLoad}
          onLoadEnd={this.props.onLoadEnd}
          style={{
            height: size,
            width: size
          }}
        />
      </View>
    );
  }
}

QRCode.propTypes = {
  value: PropTypes.string,
  size: PropTypes.number,
  bgColor: PropTypes.string,
  fgColor: PropTypes.string,
  onLoad: PropTypes.func,
  onLoadEnd: PropTypes.func
};

QRCode.defaultProps = {
  value: 'https://github.com/cssivision',
  fgColor: 'white',
  bgColor: 'black',
  size: 128,
  onLoad: () => {
  },
  onLoadEnd: () => {
  }
};


export default QRCode;
