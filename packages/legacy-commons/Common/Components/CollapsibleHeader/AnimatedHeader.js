import React from 'react';
import _isNil from 'lodash/isNil';
import {Animated} from 'react-native';
import PropTypes from 'prop-types';

class AnimatedHeader extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      scrollY: new Animated.Value(0),
      cardHeight: 0
    };
  }

  componentDidMount() {
    if (!_isNil(this.props.scrollStateRef)) {
      this.props.scrollStateRef(this.state.scrollY);
    }
  }

  _onLayout = (event) => {
    const {height} = event.nativeEvent.layout;
    this.setState({cardHeight: height});
  }


  render() {
    const {renderer, touchEnabled} = this.props;
    const headerTranslate = Animated.diffClamp(this.state.scrollY, 0, this.state.cardHeight)
      .interpolate({
        inputRange: [0, 1],
        outputRange: [0, -1]
      });
    return (
      <Animated.View
        pointerEvents={touchEnabled ? 'auto' : 'none'}
        style={{
          transform: [{translateY: headerTranslate}],
          position: 'absolute',
          left: 0,
          right: 0,
          overflow: 'hidden'
        }}
        onLayout={this._onLayout}
      >
        {renderer}
      </Animated.View>
    );
  }
}


AnimatedHeader.propTypes = {
  scrollStateRef: PropTypes.func.isRequired,
  touchEnabled: PropTypes.bool,
  renderer: PropTypes.node.isRequired
};
AnimatedHeader.defaultProps = {
  touchEnabled: true
};

export default AnimatedHeader;
