import React from 'react';
import PropTypes from 'prop-types';
import {Animated, View, FlatList} from 'react-native';
import AnimatedHeader from './AnimatedHeader';

// ------
const AnimatedComponent = Animated.createAnimatedComponent(FlatList);

class Collapsible extends React.Component {
  constructor(props) {
    super(props);
    // this.AnimatedComponent = Animated.createAnimatedComponent(FlatList);
  }

  _onScroll = (event) => {
    if (event.nativeEvent.contentOffset.y > 0) {
      Animated.event([{nativeEvent: {contentOffset: {y: this.scrollY}}}])(event);
    }
  }

  getAnimatedListRenderer = () => {
    const {
      flatList, renderList, headerHeight, data, keyExtractor, renderItem, footerHeight
    } = this.props;
    const listFooterPlaceholder = <View style={{height: footerHeight}} />;
    if (flatList) {
      return (
        <AnimatedComponent
          style={{paddingTop: headerHeight}}
          data={data}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          onScroll={this._onScroll}
          ListFooterComponent={listFooterPlaceholder}
        />
      );
    }
    return (
      <Animated.ScrollView
        style={{paddingTop: headerHeight}}
        scrollEventThrottle={16}
        bounces={false}
        onScroll={this._onScroll}
      >
        {renderList}
        {listFooterPlaceholder}
      </Animated.ScrollView>
    );
  }

  render() {
    const {renderHeader, touchEnabled} = this.props;
    return (
      <View style={{flex: 1}}>
        {this.getAnimatedListRenderer()}
        <AnimatedHeader
          scrollStateRef={(scrollState) => {
            this.scrollY = scrollState;
          }}
          renderer={renderHeader}
          touchEnabled={touchEnabled}
        />
      </View>
    );
  }
}

Collapsible.propTypes = {
  flatList: PropTypes.bool,
  renderList: PropTypes.node,
  headerHeight: PropTypes.number,
  footerHeight: PropTypes.number,
  data: PropTypes.array,
  keyExtractor: PropTypes.func,
  renderItem: PropTypes.func,
  renderHeader: PropTypes.node.isRequired,
  touchEnabled: PropTypes.bool
};

Collapsible.defaultProps = {
  flatList: false,
  renderList: null,
  headerHeight: 80,
  footerHeight: 80,
  data: [],
  keyExtractor: () => {},
  renderItem: () => {},
  touchEnabled: true
};

export default Collapsible;
