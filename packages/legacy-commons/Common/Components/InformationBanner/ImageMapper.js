
// eslint-disable-next-line import/no-unresolved
const UserPrecaution = require('@mmt/legacy-assets/src/covid_user.webp');
// eslint-disable-next-line import/no-unresolved
const BusProviderPrecaution = require('@mmt/legacy-assets/src/covid_bus.webp');

// eslint-disable-next-line import/prefer-default-export
export const ImageMap = {
  userPrecaution: UserPrecaution,
  busProviderPrecaution: BusProviderPrecaution
};
