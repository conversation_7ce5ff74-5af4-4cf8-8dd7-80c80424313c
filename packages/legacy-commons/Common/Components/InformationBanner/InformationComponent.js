import React from 'react';
import {StyleSheet, Text, View, Image} from 'react-native';
import PropTypes from 'prop-types';
import {colors, fonts} from '../../../Styles/globalStyles';
import TouchableRipple from '../TouchableRipple';
import {ImageMap} from './ImageMapper';

// eslint-disable-next-line import/no-unresolved
const rightArrow = require('@mmt/legacy-assets/src/right_blue.webp');

const InformationComponent = ({data, onClick}) => {
  if (!(data && data.children && data.children.length > 0)) {
    return null;
  }
  return (
    <View style={styles.container}>
      <Text style={styles.cardHeader}>{data.header.title}</Text>
      <View style={styles.contentContainer}>
        {
          data.children &&
          data.children.length > 0 &&
          data.children.map(item => (
            <View style={{marginTop: 15, flex: 1}} key={`${item.header.title}_${item.position}`}>
              <TouchableRipple
                onPress={() => onClick(item.linkTag.url)}
                style={{flex: 1}}
              >
                <View style={styles.itemContainer}>
                  <View>
                    {item.image.url &&
                    <Image
                      source={{uri: item.image.url}}
                      style={styles.image}
                    />}
                    {(item.image.placeholder) && !item.image.url &&
                    <Image
                      source={ImageMap[item.image.placeholder]}
                      style={styles.image}
                    />}
                  </View>
                  <View style={styles.descriptionContainer}>
                    <Text style={styles.itemHeader}>{item.header.title}</Text>
                    {item.header.description && <Text style={styles.itemContent}>{item.header.description}</Text>}
                  </View>

                  {item.linkTag
                    && item.linkTag.url &&
                    <View>
                      <Image
                        source={rightArrow}
                        style={styles.redirectionImage}
                      />
                    </View>}
                </View>
              </TouchableRipple>
            </View>
            ))
        }
      </View>
    </View>
  );
};

InformationComponent.propTypes = {
  data: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    marginTop: 5,
    marginHorizontal: 10,
    padding: 16,
    borderRadius: 10,
    flex: 1
  },
  itemHeader: {
    fontFamily: fonts.bold,
    fontSize: 14,
    letterSpacing: 0,
    lineHeight: 17,
    color: colors.black,
    flexWrap: 'wrap'
  },
  itemContent: {
    fontFamily: fonts.regular,
    fontSize: 12,
    marginTop: 2,
    lineHeight: 16,
    letterSpacing: 0,
    color: colors.lightTextColor,
    flexWrap: 'wrap'
  },
  descriptionContainer: {
    flex: 1,
    marginHorizontal: 5
  },
  cardHeader: {
    fontFamily: fonts.light,
    fontSize: 16,
    letterSpacing: 0,
    lineHeight: 19,
    color: colors.black
  },
  contentContainer: {
    flex: 1,
  },
  itemContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  image: {
    width: 48,
    height: 48
  },
  redirectionImage: {
    width: 24,
    height: 24
  }
});
export default InformationComponent;
