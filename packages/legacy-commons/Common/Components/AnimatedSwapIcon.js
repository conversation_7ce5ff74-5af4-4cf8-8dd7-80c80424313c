/**
 * Created by mmt6308 on 05/08/19.
 */
import React from 'react';
import PropTypes from 'prop-types';
import {Animated, Easing, Platform, Image} from 'react-native';
import TouchableRipple from './TouchableRipple.ios';
import {colors} from '../../Styles/globalStyles';

// eslint-disable-next-line import/no-unresolved
const swapImage = require('@mmt/legacy-assets/src/swap_search.webp');

class SwapIcon extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      swapSpin: new Animated.Value(0)
    };
    this._spinPosition = 0;
    this._spinAnimation = this.state.swapSpin.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '180deg']
    });
  }

    _onSwapClicked = () => {
      const {enabled, onClick} = this.props;
      if (!enabled) {
        return;
      }
      Animated.timing(
        this.state.swapSpin,
        {
          toValue: 1,
          duration: 300,
          easing: Easing.linear
        }
      ).start(() => {
        this.state.swapSpin.setValue(0);
      });
      setTimeout(() => {
        onClick();
      }, 150);
    }


    render() {
      if (Platform.OS === 'web') {
        return (
          <TouchableRipple
            onPress={this.props.onClick}
            feedbackColor={colors.transparent}
          >
            <Image
              source={swapImage}
              style={{
                  width: 48,
                  height: 48,
                  resizeMode: 'contain'
              }}
            />
          </TouchableRipple>
        );
      }
      return (
        <TouchableRipple
          onPress={this._onSwapClicked}
          feedbackColor={colors.transparent}
        >
          <Animated.Image
            source={swapImage}
            style={{
                        transform: [{rotate: this._spinAnimation}],
                        width: 48,
                        height: 48,
                        resizeMode: 'contain'
                    }}
          />
        </TouchableRipple>
      );
    }
}

SwapIcon.propTypes = {
  enabled: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired
};

export default SwapIcon;
