
import React, { useState } from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { Image } from 'react-native';
import _ from 'lodash';

const loadingGradients = [
    ['#00D2FF','#3A7BD5'],
    ['#43E1A8', '#219393'],
    ['#FF7F3F','#FF3E5E'],
    ['#F5515F', '#9F0469'],
    ['#F3D452','#F09819'],
    ['#3023AE', '#C86DD7'],
    ['#DDD6F3','#FAACA8']
  ];

export const getRandomGradient = (index) => {
    let numberOfGradients = loadingGradients.length
    if (_.isNumber(index)) {
        return loadingGradients[index % numberOfGradients]
    }
    return loadingGradients[Math.floor(Math.random() * numberOfGradients)]
}

export const GradientImageView = (props) => {

    const [gradientColor, setGradientColor] = useState(props?.color)

    return (
        <LinearGradient
            style={props?.style}
            start={props?.start ?? {x: 0, y: 0.5}}
            end={props?.end ?? {x: 1, y: 0.5}}
            colors={gradientColor ?? getRandomGradient(props?.index)}>
            <Image style={props?.style} source={{uri: props?.url}} onLoad={() => {setGradientColor(['#FFFFFF', '#FFFFFF'])}}/>
      </LinearGradient>
    )
}