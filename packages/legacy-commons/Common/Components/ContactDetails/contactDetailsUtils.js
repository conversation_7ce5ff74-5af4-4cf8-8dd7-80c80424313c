import AsyncStorage from '@react-native-async-storage/async-storage';
import compact from 'lodash/compact';
import isEmpty from 'lodash/isEmpty';
import { getUserDetails, isUserLoggedIn } from '../../../Native/UserSession/UserSessionModule';

const PREFIX = 'CONTACT_DETAILS_';

export const getContactDetailsFromLoggedInUser = async () => {
  let contactDetails;
  const userLoggedIn = await isUserLoggedIn();
  if (userLoggedIn) {
    const userDetails = await getUserDetails();
    const { firstName, lastName, email, mobile } = userDetails;
    let mobileNumber = null;
    let isInternationalNumber = false;
    if (mobile) {
      mobileNumber = mobile.mobileNumber;
      if (mobile.countryCode && Number(mobile.countryCode) != 91) {
        isInternationalNumber = true;
      }
    }
    const hasCompleteFields = !(isEmpty(email) || isEmpty(mobileNumber));
    const fullName = compact([firstName, lastName]).join(' ');
    contactDetails = {
      name: fullName,
      email,
      mobile: mobileNumber,
      hasCompleteFields,
      isInternationalNumber,
    };
  } else {
    contactDetails = null;
  }

  return contactDetails;
};

export const getContactDetails = async (lob) => {
  const key = `${PREFIX}${lob}`;
  const paxDetailsStr = await AsyncStorage.getItem(key);
  if (!paxDetailsStr) {
    return null;
  }
  try {
    return JSON.parse(paxDetailsStr);
  } catch (e) {
    return null;
  }
};

export const saveContactDetails = async (contactDetails, lob) => {
  const key = `${PREFIX}${lob}`;
  await AsyncStorage.setItem(key, JSON.stringify(contactDetails));
};
