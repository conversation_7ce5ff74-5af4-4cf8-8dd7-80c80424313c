import React from 'react';
import {Keyboard, StyleSheet, Text, View} from 'react-native';
import PropTypes from 'prop-types';
import {colors, fonts} from '../../../Styles/globalStyles';
import TouchableRipple from '../TouchableRipple';
import {validateEmail, validateMobile} from './contactDetails.utils';
import {isUserLoggedIn} from '../../../Native/UserSession/UserSessionModule';

import InputField from "@Frontend_Ui_Lib_App/InputField";

class ContactDetailsView extends React.Component {
  constructor(props) {
    super(props);
    let {email = '', mobile = ''} = props.contactDetails || {};

    if(props.contactDetails && props.contactDetails.isInternationalNumber){
      mobile = '';
    }
    this.state = {
      email,
      mobile,
      emailError: '',
      mobileError: '',
      editMode: (props.contactDetails === null || !props.contactDetails.hasCompleteFields || props.contactDetails.isInternationalNumber)
    };
  }

  async componentWillReceiveProps(nextProps) {
    const userLoggedIn = await isUserLoggedIn();
    if (nextProps.contactDetails !==  this.props.contactDetails && userLoggedIn) {
      let {email, mobile} = nextProps.contactDetails || {};
      if(nextProps.contactDetails && nextProps.contactDetails.isInternationalNumber){
        mobile = '';
      }
      this.setState({
        email,
        mobile,
        emailError: '',
        mobileError: '',
        editMode: (nextProps.contactDetails === null || !nextProps.contactDetails.hasCompleteFields || nextProps.contactDetails.isInternationalNumber)
      });
    }
  }

  _onEditPress = () => {
    this.setState({editMode: true});
  };

  _onEmailFocused = () => {
    if (this._isIPhone) {
      this.props.adjustScroll();
    }
  };

  _onEmailChange = (val) => {
    this.setState({email: val}, this.handleEmailValidation);
  }

  _onEmailBlur = () => {
    if (!this.mobileInputRef.isFocused()) {
      Keyboard.dismiss();
    }
    this.handleEmailValidation();
  }

  handleEmailValidation = () => {
    const {email} = this.state;
    const emailValidator = this.props.validateEmail || validateEmail;
    const emailValidation = emailValidator(email);
    if (emailValidation.valid) {
      this.setState({emailError: null}, this._onSubmit);
    } else {
      this.setState({emailError: emailValidation.error}, () => this.props.onValidationFailed('email'));
    }
  }

  _onMobileFocused = () => {
    if (this._isIPhone) {
      this.props.adjustScroll();
    }
  };

  _onMobileChange = (val) => {
    this.setState({mobile: val}, this.handleMobileValidation);
  }

  _onMobileBlur = () => {
    if (!this.emailInputRef.isFocused()) {
      Keyboard.dismiss();
    }
    this.handleMobileValidation();
  }

  handleMobileValidation = () => {
    const {mobile} = this.state;
    const mobileNumberValidator = this.props.validateMobile || validateMobile;
    const mobileValidation = mobileNumberValidator(mobile);
    if (mobileValidation.valid) {
      this.setState({mobileError: null}, this._onSubmit);
    } else {
      this.setState({mobileError: mobileValidation.error}, () => this.props.onValidationFailed('mobile'));
    }
  }

  _onSubmit = () => {
    const {
      emailError,
      mobileError,
      email,
      mobile
    } = this.state;
    if (email && mobile && !emailError && !mobileError) {
      this.props.onAdd({
        email,
        mobile,
        hasCompleteFields: true
      });
    }
  }

  _renderContactForm = () => (
    <View style={styles.formField}>
      <InputField
      label={this.props.labels.email}
      placeholder={this.props.labels.emailPlaceholder || "Eg. <EMAIL>"}
      testId={`${this?.props?.id}_email`}
      placeholderTextColor={colors.disabledButton}
      inputProps={{
        autoCapitalize: "none",
        keyboardType: "email-address",
        returnKeyType: "done",
        onSubmitEditing: this._onEmailBlur
      }}
      value={this.state.email}
      onChangeText={this._onEmailChange}
      onBlur={this._onEmailBlur}
      isError={!!this.state.emailError}
      errorMessage={this.state.emailError}
      inputRef={(ref) => { this.emailInputRef = ref; }}
      customStyle={{
        wrapperStyle: styles.inputWrapperStyle,
        labelStyle: !!this.state.emailError ? {...styles.labelStyle,... styles.labelErrorStyle} : styles.labelStyle
      }}
      />

      <InputField
      label={this.props.labels.phone}
      placeholder={this.props.labels.phonePlaceholder || "Eg. 9111111111"}
      testId={`${this?.props?.id}_phoneNumber`}
      placeholderTextColor={colors.disabledButton}
      inputProps={{
        autoCapitalize: "none",
        keyboardType: "phone-pad",
        returnKeyType: "done",
        onSubmitEditing: this._onMobileBlur,
        maxLength: 10
      }}
      value={this.state.mobile}
      onChangeText={this._onMobileChange}
      onFocus={this._onMobileFocused}
      onBlur={this._onMobileBlur}
      isError={!!this.state.mobileError}
      errorMessage={this.state.mobileError}
      inputRef={(ref) => { this.mobileInputRef = ref; }}
      customStyle={{
        labelStyle: !!this.state.mobileError ? {...styles.labelStyle,... styles.labelErrorStyle} : styles.labelStyle,
        inputFieldStyle: styles.inputFieldStyle,
        errorMessageStyle: styles.errorTextStyle
      }}
      />
    </View>
  );

  _renderContactCard = () => (
    <View style={styles.contactDetailsViewContainer}>
      <View style={styles.contactDetailsFields}>
        <Text style={styles.emailId}>{this.state.email}</Text>
        <Text style={styles.phoneNo}>{this.state.mobile}</Text>
      </View>
      <TouchableRipple onPress={this._onEditPress}>
        <Text style={styles.editDetail}>
          {this.props.labels.changeButtonText || "CHANGE"}
        </Text>
      </TouchableRipple>
    </View>
  );

  render() {
    return (
      <View style={styles.formContainer}>
        {(this.state.editMode) &&
          this._renderContactForm()
        }
        {(!this.state.editMode) &&
          this._renderContactCard()
        }

      </View>
    );
  }
}

const styles = StyleSheet.create({
  contactDetailsViewContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8
  },
  contactDetailsFields: {
    flexDirection: 'column'
  },
  container: {
    marginBottom: 8
  },
  formField: {
    borderRadius: 8,
    paddingHorizontal: 8
  },
  formContainer: {
    paddingHorizontal: 12,
    paddingTop: 20,
    paddingBottom: 16
  },
  emailId: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.regular
  },
  phoneNo: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.regular,
    marginTop: 10
  },
  editDetail: {
    color: colors.azure,
    fontSize: 12,
    fontFamily: fonts.bold
  },
  inputWrapperStyle: {
    marginBottom: 20
  },
  labelStyle: {
    fontSize: 14,
    fontFamily: fonts.bold
  },
  inputFieldStyle: {
    fontFamily: fonts.bold
  },
  labelErrorStyle: {
    color: colors.red
  },
  errorTextStyle: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.red
  }
});


ContactDetailsView.propTypes = {
  contactDetails: PropTypes.object,
  onAdd: PropTypes.func.isRequired,
  onValidationFailed: PropTypes.func,
  adjustScroll: PropTypes.func
  // labels: PropTypes.object
};

ContactDetailsView.defaultProps = {
  contactDetails: null,
  adjustScroll: () => {
  },
  onValidationFailed: () => {
  }
};

export default ContactDetailsView;
