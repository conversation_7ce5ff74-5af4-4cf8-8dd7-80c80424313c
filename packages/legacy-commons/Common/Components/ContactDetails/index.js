import React from 'react';
import {Image, Keyboard, Platform, StyleSheet, Text, TextInput, View} from 'react-native';
import PropTypes from 'prop-types';
import _ from 'lodash';
import {colors, fonts, normaliseFont, normalisePx} from '../../../Styles/globalStyles';
import Card from '../Card/index';
import getPlatformElevation from '../Card/getPlatformElevation';
import TouchableRipple from '../TouchableRipple';
import {isValidEmail, isValidMobile} from '../../../Helpers/validationHelpers';

// eslint-disable-next-line import/no-unresolved
const errorIcon = require('@mmt/legacy-assets/src/ic_error_red.webp');

class ContactDetailsView extends React.Component {
  constructor(props) {
    super(props);
    const {name = '', email = '', mobile = ''} = props.contactDetails || {};
    this.state = {
      name,
      email,
      mobile,
      editMode: (props.contactDetails === null || !props.contactDetails.hasCompleteFields),
      formFocussed: true
    };
  }

  componentWillReceiveProps(nextProps) {
    const newDetails = nextProps.contactDetails;
    if (newDetails && !_.isEqual(newDetails, this.props.contactDetails)) {
      const {name = '', email = '', mobile = ''} = newDetails;
      this.setState({
        name,
        email,
        mobile,
        editMode: (newDetails === null || !newDetails.hasCompleteFields),
        formFocussed: true
      });
    }
  }

    _onNameFocused = () => {
      if (this._isIPhone) {
        this.props.adjustScroll();
      }
      this._onFocus('name');
    };
    _onEmailFocused = () => {
      if (this._isIPhone) {
        this.props.adjustScroll();
      }
      this._onFocus('email');
    };
    _onMobileFocused = () => {
      if (this._isIPhone) {
        this.props.adjustScroll();
      }
      this._onFocus('mobile');
    };

    _onFocus = (field) => {
      this.setState({formFocussed: true});
      this.props.onFieldFocus(field);
    };

    _onBlur = () => {
      this.setState({formFocussed: false});
      setTimeout(() => {
        if (!this.state.formFocussed) {
          this._onSubmit();
        }
      }, 1000);
    }

    _onUserNameChanged = (val) => {
      this.setState({name: val});
    };

    _onEmailChanged = (val) => {
      this.setState({email: val});
    };

    _onMobileChanged = (val) => {
      this.setState({mobile: val});
    };

    _onPaxDetailsEdit = () => {
      this.setState({editMode: true});
    };

    _onSubmit = () => {
      Keyboard.dismiss();
      const {name, email, mobile} = this.state;
      if (!_.trim(name)) {
        this.setState({error: 'Empty username'});
        this.props.onValidationFailed('name');
        // this._paxNameInputRef.focus();
        return;
      }
      if (_.trim(name).length > 50) {
        this.setState({error: 'Username is too long, maximum 50 chars allowed'});
        this.props.onValidationFailed('name');
        // this._paxNameInputRef.focus();
        return;
      }
      if (!_.trim(email)) {
        this.setState({error: 'Empty email'});
        this.props.onValidationFailed('email');
        // this._paxNameInputRef.focus();
        return;
      }
      if (!isValidEmail(email)) {
        this.setState({error: 'Invalid email'});
        this.props.onValidationFailed('email');
        // this._paxEmailInputRef.focus();
        return;
      }
      if (!_.trim(mobile)) {
        this.setState({error: 'Empty mobile'});
        this.props.onValidationFailed('mobile');
        // this._paxMobileInputRef.focus();
        return;
      }
      if (_.trim(mobile).length < 10 || !isValidMobile(_.trim(mobile))) {
        this.setState({error: 'Invalid mobile'});
        this.props.onValidationFailed('mobile');
        // this._paxMobileInputRef.focus();
        return;
      }

      this.setState({
        editMode: false,
        error: null
      });
      this.props.onAdd({
        name,
        email,
        mobile,
        hasCompleteFields: true
      });
    };


    _renderContactForm = () => (
      <View>
        <Card
          style={{
                  borderRadius: 8,
                  marginTop: 4,
                  marginBottom: 0,
                  borderWidth: 1
              }}
          elevation={2}
          showBorder
        >
          <View style={styles.formField}>
            <TextInput
              style={styles.formTxt}
              onBlur={this._onBlur}
              onFocus={this._onNameFocused}
              onChangeText={this._onUserNameChanged}
              placeholder={this.props.labels.name}
              returnKeyType="next"
              autoCapitalize="words"
              underlineColorAndroid={colors.lightTextColor}
              ref={(e) => {
                      this._paxNameInputRef = e;
                  }}
              value={this.state.name}
              onSubmitEditing={() => {
                      if (this._isIPhone) {
                          Keyboard.dismiss();
                      }
                      this._paxEmailInputRef.focus();
                  }}
            />
            <TextInput
              style={styles.formTxt}
              onBlur={this._onBlur}
              onFocus={this._onEmailFocused}
              placeholder={this.props.labels.email}
              onChangeText={this._onEmailChanged}
              underlineColorAndroid={colors.lightTextColor}
              keyboardType="email-address"
              autoCapitalize="none"
              returnKeyType="next"
              value={this.state.email}
              ref={(e) => {
                      this._paxEmailInputRef = e;
                  }}
              onSubmitEditing={() => {
                      if (this._isIPhone) {
                          Keyboard.dismiss();
                      }
                      this._paxMobileInputRef.focus();
                  }}
            />
            <TextInput
              style={styles.formTxt}
              onBlur={this._onBlur}
              onFocus={this._onMobileFocused}
              placeholder={this.props.labels.phone}
              underlineColorAndroid={colors.lightTextColor}
              onChangeText={this._onMobileChanged}
              keyboardType="phone-pad"
              returnKeyType="done"
              value={this.state.mobile}
              ref={(e) => {
                      this._paxMobileInputRef = e;
                  }}
              onSubmitEditing={this._onSubmit}
              maxLength={10}
            />
          </View>
        </Card>
        <View style={styles.ctaContainer}>
          <TouchableRipple onPress={this._onSubmit}>
            <View style={[styles.addBtnContainer,
                  this.state.formFocussed ? {} : styles.addBtnDisable]}
            >
              <Text style={styles.addBtn}> DONE </Text>
            </View>
          </TouchableRipple>
        </View>

        {this.state.error &&
        <View style={styles.errorContainer}>
          <Image
            source={errorIcon}
            style={styles.errorIcon}
          />
          <Text style={styles.errorText}>{this.state.error}</Text>
        </View>
            }

      </View>
    );

    _renderContactCard = () => (
      <View>
        <Card style={{borderRadius: 8}} elevation={2} showBorder>
          <View style={[styles.formEditfield]}>
            <View style={styles.formEdit}>
              <Text style={styles.userName}>{this.state.name}</Text>
              <TouchableRipple onPress={this._onPaxDetailsEdit}>
                <Text style={styles.editDetail}>{this.props.labels.edit}</Text>
              </TouchableRipple>
            </View>
            <Text style={styles.emailId}>{this.state.email}</Text>
            <Text style={styles.phoneNo}>{this.state.mobile}</Text>
          </View>
        </Card>
      </View>
    );

    render() {
      return (
        <View style={styles.formContainer}>
          {(this.state.editMode) &&
                this._renderContactForm()
                }
          {(!this.state.editMode) &&
                this._renderContactCard()
                }

        </View>
      );
    }
}

const styles = StyleSheet.create({
  container: {
    marginBottom: normalisePx(8)
  },
  formField: {
    borderRadius: normalisePx(8),
    paddingHorizontal: normalisePx(24),
    paddingBottom: normalisePx(26),
    paddingTop: normalisePx(12)
  },
  formTxt: {
    fontSize: normaliseFont(16),
    height: 42,
    ...Platform.select({
      ios: {
        fontSize: normaliseFont(14),
        height: normalisePx(44),
        marginBottom: normalisePx(2),
        borderBottomWidth: 1,
        borderBottomColor: colors.lightGrey
      },
      web: {
        fontSize: normaliseFont(14),
        height: normalisePx(44),
        marginBottom: normalisePx(2),
        borderBottomWidth: 1,
        borderBottomColor: colors.lightGrey
      }
    }),
    color: colors.defaultTextColor,
    fontFamily: fonts.medium
  },
  addBtnContainer: {
    height: normalisePx(36),
    borderRadius: normalisePx(18),
    alignSelf: 'flex-end',
    paddingHorizontal: normalisePx(24),
    backgroundColor: colors.azure,
    alignItems: 'center',
    justifyContent: 'center',
    ...getPlatformElevation(3)
  },
  addBtn: {
    fontSize: normaliseFont(14),
    fontFamily: fonts.bold,
    color: colors.white,
    textAlign: 'center',
    alignSelf: 'center'
  },
  addBtnDisable: {
    backgroundColor: '#9b9b9b'
  },
  formContainer: {
    paddingHorizontal: normalisePx(12),
    paddingTop: normalisePx(20),
    paddingBottom: normalisePx(16)
  },
  ctaContainer: {
    marginTop: normalisePx(-16),
    marginRight: normalisePx(36),
    alignItems: 'flex-end'
  },
  formEditfield: {paddingHorizontal: normalisePx(12)},
  formEdit: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalisePx(17),
    marginBottom: normalisePx(12)
  },
  userName: {
    fontSize: normaliseFont(16),
    color: colors.defaultTextColor,
    fontFamily: fonts.semiBold
  },
  emailId: {
    fontSize: normaliseFont(14),
    color: colors.defaultTextColor,
    fontFamily: fonts.regular
  },
  phoneNo: {
    fontSize: normaliseFont(14),
    color: colors.defaultTextColor,
    fontFamily: fonts.regular,
    marginTop: normalisePx(10),
    marginBottom: normalisePx(26)
  },
  editDetail: {
    color: colors.azure,
    fontSize: normaliseFont(14),
    fontFamily: fonts.semiBold
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8
  },
  errorIcon: {
    width: 24,
    height: 24
  },
  errorText: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.errorRed,
    marginHorizontal: 8
  }
});


ContactDetailsView.propTypes = {
  contactDetails: PropTypes.object,
  onAdd: PropTypes.func.isRequired,
  isNameOptional: PropTypes.bool,
  onFieldFocus: PropTypes.func,
  onValidationFailed: PropTypes.func,
  adjustScroll: PropTypes.func,
  labels: PropTypes.object
};

ContactDetailsView.defaultProps = {
  contactDetails: null,
  isNameOptional: false,
  adjustScroll: () => {
  },
  onFieldFocus: () => {
  },
  onValidationFailed: () => {
  },
  labels: {
    name: 'Name',
    email: 'Email ID',
    phone: 'Phone Number'
  }
};

export default ContactDetailsView;
