import React from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import _ from 'lodash';
import {colors} from '../../Styles/globalStyles';

// eslint-disable-next-line react/prop-types
const _getMultiColorBg = bgColors => ({style: {height, ...otherProps}, children}) => (
  <LinearGradient
    style={{
      height,
      borderRadius: (height / 2),
      justifyContent: 'center',
      alignItems: 'center',
      ...otherProps
    }}
    colors={[...bgColors]}
    start={{x: 0.1, y: 0.1}}
    end={{x: 1, y: 1}}
  >
    {children}
  </LinearGradient>
);

// eslint-disable-next-line react/prop-types
const _getBg = backgroundColor => ({style: {height, ...otherProps}, children}) => (
  <View style={{
    height,
    borderRadius: (height / 2),
    backgroundColor,
    alignItems: 'center',
    justifyContent: 'center',
    ...otherProps
  }}
  >
    {children}
  </View>
);
const CircularBorderView = ({backgroundColor, style, children}) => {
  if (!_.isNumber(style.height)) {
    throw new Error('Please specify height property');
  }
  let _bgColor = _.isArray(backgroundColor) ? [...backgroundColor] : [backgroundColor];
  if (_bgColor.length === 0) {
    _bgColor = [colors.transparent];
  }
  const multiColored = _bgColor.length > 1;
  const BgComponent = multiColored ? _getMultiColorBg(_bgColor) : _getBg(_bgColor[0]);

  return (
    <BgComponent style={style}>
      {children}
    </BgComponent>
  );
};


CircularBorderView.propTypes = {
  ...View.propTypes,
  backgroundColor: PropTypes.oneOf([PropTypes.array, PropTypes.string])
};

CircularBorderView.defaultProps = {
  backgroundColor: colors.transparent
};


export default CircularBorderView;
