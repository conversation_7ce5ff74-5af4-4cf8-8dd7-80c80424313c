
import React from 'react';
import HTMLView from 'react-native-htmlview';
import {Image, Text, TouchableOpacity, StyleSheet, Platform} from 'react-native';
import {colors, fonts} from '../../Styles/globalStyles';

interface StyleObj {
  [proName: string]: any
}

interface CheckboxProps {
  onChange: () => any;
  label: string;
  labelStyle?: StyleObj,
  containerStyle?: StyleObj;
  checked?: boolean;
  disabled?:boolean;
  tintColor?: string | null;
  id?: string;
  htmlView?:boolean;
}

const CB_CHECKED = require('@mmt/legacy-assets/src/ic-check-box-active.webp');
const CB_UNCHECKED= require('@mmt/legacy-assets/src/ic-check-box-inactive.webp');

export default function Checkbox(props: CheckboxProps) {
  const {
    label,
    labelStyle = {},
    containerStyle = {},
    checked = false,
    onChange,
    disabled,
    tintColor,
    id,
    htmlView=false
  } = props;

  const imageSrc = checked ? CB_CHECKED : CB_UNCHECKED;
  const imageStyle = [styles.imageStyle, disabled ? styles.disabled : {}];
  const finalLabelStyle = [styles.label, disabled ? styles.disabled : {}, labelStyle];
  // @ts-ignore
  return (
    <TouchableOpacity
      testID={id}
      style={[styles.container, containerStyle]}
      onPress={onChange}
      activeOpacity={0.8}
      disabled={disabled}
    >
      <Image
        source={imageSrc}
        style={imageStyle}
        tintColor={tintColor && checked ? tintColor : checked ? colors.pureBlue : colors.disabledBtnBg}
        testID={props?.id}
      />
      {htmlView ? (
        <HTMLView value={label} stylesheet={styles} />
      ) : (
        <Text style={finalLabelStyle}>{label}</Text>
      )}
    </TouchableOpacity>
  );
}

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  label: {
    marginTop: Platform.OS === 'ios' ? 0 : 1,
    flexShrink: 1,
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.regular,
    a: {
      textDecorationLine: 'line-through',
      opacity: 0.5,
    }
  },
  imageStyle: {
    height: 25,
    width: 25,
    marginRight: 10
  },
  disabled: {
    opacity: 0.4
  },
  a: {
    textDecorationLine: 'line-through',
    opacity: 0.5,
  },
  b: {
    fontWeight: '700',
  }
});
