/* eslint-disable react-native/no-inline-styles */
import React, { Component } from 'react';
import { BackHandler, View, ActivityIndicator, StyleSheet,KeyboardAvoidingView } from 'react-native';
import { WebView } from 'react-native-webview';
import PropTypes from 'prop-types';
import CommonHeader from '../Header/CommonHeader';

/**
 * generic class for opening web view page in react native
 * Parameters needed : url, header text,header icon, loaderColor
 */
class WebViewWrapper extends Component {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props);
    this.handleBackButtonClick = this.onBackPress.bind(this);
    this.state = {
      canGoBack: false
    };
    this.timer = null;
  }

  componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
    this.timer = setTimeout(() => {
      this.setState({ invalidate: true });
    }, 300);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
  }

  renderLoadingView = () => {
    return (
      <View
        style={{
          ...StyleSheet.absoluteFill,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <ActivityIndicator size="large" color={this.props.loaderColor || '#008cff'} />
      </View>
    );
  };
  render() {
    const uri = this.props.url;
    return (
      <View style={{ flex: 1, flexDirection: 'column' }}>
        <CommonHeader
          headerText={this.props.headerText}
          imgSrc={this.props.headerIcon}
          backPressHandler={this.onBackPress}
        />
        {!this.state.hideWebView && (
          <KeyboardAvoidingView
          style={styles.container}
          behavior="padding"
          keyboardVerticalOffset={20}
          enabled
        >
          <WebView
            ref={(e) => (this._webView = e)}
            source={{ uri }}
            onNavigationStateChange={this.onNavigationStateChange}
            startInLoadingState
            renderLoading={this.renderLoadingView}
          />
           </KeyboardAvoidingView>
        )}
      </View>
    );
  }

  onNavigationStateChange = (navState) => {
    this.setState({
      canGoBack: navState.canGoBack
    })
  };

  onBackPress = () => {
    if (this.state.canGoBack) {
      this._webView.goBack();
      return true;
    }
    // This is to fix weird segmentation-fault error in C++ layer of RN which comes when using react-native-screens and WebView
    // The issue comes when page is closing if webview is still mounted it crashes, so we're first unmouting webview first and then closing the page
    if (this.state.hideWebView) {
      return false;
    }
    this.setState({ hideWebView: true });
    return true;
  };

  componentDidUpdate() {
    if (this.state.hideWebView) {
      this.props.closeWebView();
    }
  }
}

const styles = StyleSheet.create({
  container: { flex: 1, flexGrow: 1 }
});

WebViewWrapper.propTypes = {
  url: PropTypes.string.isRequired,
  headerText: PropTypes.string.isRequired,
  headerIcon: PropTypes.number.isRequired,
  closeWebView: PropTypes.func.isRequired,
};

export default WebViewWrapper;
