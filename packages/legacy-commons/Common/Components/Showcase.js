import React from 'react';
import {StyleSheet, View, TouchableWithoutFeedback} from 'react-native';
import PropTypes from 'prop-types';
import getPlatformElevation from './Card/getPlatformElevation';
import {colors} from '../../Styles/globalStyles';

const Showcase = ({
  layout, children, padding, onPress
}) => (
  <TouchableWithoutFeedback
    onPress={onPress}
  >
    <View style={{
      backgroundColor: colors.transparent,
      ...StyleSheet.absoluteFillObject,
      ...getPlatformElevation(2)
    }}
    >
      <View style={topLayout(layout)} />

      <View style={bottomLayout(layout)} />

      <View style={leftLayout(layout)} />
      <View style={rightLayout(layout)} />


      {padding > 0 &&

      <View style={{
        opacity: 0.7,
        position: 'absolute',
        top: layout.y,
        left: layout.x,
        height: layout.h,
        width: layout.w,
        borderWidth: padding,
        borderColor: colors.black,
        alignItems: 'center'
      }}
      />
      }

      <View style={{
        position: 'absolute',
        top: (layout.y + layout.h),
        left: 0,
        bottom: 0,
        right: 0,
        alignItems: 'center',
        padding: 16,
        ...getPlatformElevation(14)
      }}
      >
        {children}
      </View>
    </View>
  </TouchableWithoutFeedback>
);

const topLayout = layout => ({
  backgroundColor: colors.black,
  opacity: 0.8,
  position: 'absolute',
  top: 0,
  left: 0,
  height: layout.y,
  right: 0
});


const leftLayout = layout => ({
  backgroundColor: colors.black,
  opacity: 0.8,
  position: 'absolute',
  top: layout.y,
  left: 0,
  width: layout.x,
  height: layout.h
});
const rightLayout = layout => ({
  backgroundColor: colors.black,
  opacity: 0.8,
  position: 'absolute',
  top: layout.y,
  left: (layout.x + layout.w),
  right: 0,
  height: layout.h
});
const bottomLayout = layout => ({
  backgroundColor: colors.black,
  opacity: 0.8,
  position: 'absolute',
  top: (layout.y + layout.h),
  left: 0,
  bottom: 0,
  right: 0,
  alignItems: 'center',
  padding: 16
});

Showcase.propTypes = {
  layout: PropTypes.object.isRequired,
  onPress: PropTypes.func.isRequired,
  padding: PropTypes.number,
  children: PropTypes.node.isRequired
};

Showcase.defaultProps = {
  padding: 8
};

export default Showcase;
