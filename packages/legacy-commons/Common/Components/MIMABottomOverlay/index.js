import React from 'react';
import OverlayMessage from './OverlayMessage';
import MimaBottomOverlayDtls from './MimaBottomOverlayDtls';


export default class MimaBottomOverlay extends React.Component {
  constructor(props) {
    super(props);
    this.MimaBottomOverlay = this.MimaBottomOverlay.bind(this);
    this.CloseOverlay = this.CloseOverlay.bind(this);
    this.state = {
      MimaBottomOverlay: true
    };
  }

    static navigationOptions = {
      header: null
    }

    MimaBottomOverlay() {
      this.setState({
        MimaBottomOverlay: true
      });
    }
    CloseOverlay() {
      this.setState({
        MimaBottomOverlay: !this.state.MimaBottomOverlay
      });
    }


    render() {
      return (
        <View>
          {this.state.MimaBottomOverlay &&
            <OverlayMessage
              CloseOverlay={this.CloseOverlay}
              content={
                <MimaBottomOverlayDtls
                  CloseOverlay={this.CloseOverlay}
                  ContinuePress={this.CloseOverlay}
                />
                                }
            />
                }
        </View>
      );
    }
}
