import React from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';

const StaggeredView = ({children, padding}) => (
  <View style={{flexDirection: 'row', padding, alignItems: 'flex-start'}}>
    {children[0]}
    <View style={{
      flexDirection: 'column',
      alignSelf: 'center',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      paddingVertical: 0,
      paddingHorizontal: 4
    }}
    >
      {children[1]}
      {children.length > 2 &&
      children[2]
      }
    </View>
  </View>
);

StaggeredView.propTypes = {
  children: PropTypes.node.isRequired,
  padding: PropTypes.number
};

StaggeredView.defaultProps = {
  padding: 0
}

export default StaggeredView;
