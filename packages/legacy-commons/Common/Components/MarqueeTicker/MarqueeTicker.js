import React from 'react';
import { View, Text, StyleSheet, Dimensions, Image, FlatList } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AutoScrolling from './AutoScrolling';
import { fonts, colors } from '../../../Styles/globalStyles';
import HTMLView from 'react-native-htmlview';
import { isEmpty } from 'lodash';

const { width } = Dimensions.get("screen");

const MarqueeTicker = ({ marqueeData }) => {

  const renderCard = ({item, index}) => {
    return (
      !isEmpty(item?.text) && 
      <View style={styles.tickerItem}>
        { !isEmpty(item?.icon) && <Image source={{uri: item?.icon}} style={styles.imageStyle}/> }
        <HTMLView value={`<p>${item?.text}</p>`} stylesheet={titleStyles} />
      </View>
    )
  };

  return (
    <View style={styles.cont}>
    <LinearGradient colors={[colors.yellow6, colors.white]} style={[styles.tickerCont]}>
      <AutoScrolling style={styles.scrolling} endPadding={10} delay={1500} duration={15000}>
        <View style={{ flexDirection: 'row'}}>
          <FlatList
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            data={marqueeData}
            renderItem={renderCard}
            style={{paddingVertical: 12}}
            scrollEnabled={false}
          />
        </View>
      </AutoScrolling>
    </LinearGradient>
    </View>
  );
};

const titleStyles = StyleSheet.create({
  p: {
    color: colors.black,
    fontSize: 14,
    fontFamily: fonts.regular,
  },
  b: {
    color: colors.black,
    fontSize: 14,
    fontFamily: fonts.bold,
  }
});

const styles = StyleSheet.create({
  cont: {
    height: 42,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    zIndex: 1,
    marginTop: -42,
    backgroundColor: colors.white
  },
  tickerCont: {
    flex: 1,
    borderRadius: 24
  },
  tickerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
    alignSelf: 'flex-start',
  },
  imageStyle: {
    height: 15,
    width: 12, 
    marginRight: 8
  },
  tickerText: {
    fontFamily: fonts.regular,
    fontSize: 14,
    color: colors.textGrey,
  },
  scrolling: {
    width: width
  }
});

export default MarqueeTicker;