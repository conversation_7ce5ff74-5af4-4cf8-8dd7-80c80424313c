import React from 'react';
import PropTypes from 'prop-types';
import {Image, Text, View} from 'react-native';
import {colors, fonts} from '../../Styles/globalStyles';
import BottomSheetModal from './Modals/BottomSheetModal';
import TouchableRipple from './TouchableRipple';

const closeIcon = require('@mmt/legacy-assets/src/close_icon.webp');


const UserAdvisoryModal = ({onUserAdvisoryModalClick}) => (
  <BottomSheetModal onTouchOutside={onUserAdvisoryModalClick} hardwareBackButtonClose={onUserAdvisoryModalClick}>
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <TouchableRipple onPress={onUserAdvisoryModalClick}>
          <View style={styles.closeContainer}>
            <Image
              style={styles.closeImage}
              source={closeIcon}
            />
          </View>
        </TouchableRipple>
        <Text style={styles.header}>Advisory Notice</Text>
      </View>
      <View style={{marginHorizontal: 24}}>
        <Text style={[{marginTop: 20}, styles.advisoryText]}>We wish to remind you that
          MakeMyTrip never asks for your personal banking and security details like passwords, OTP
          etc.
        </Text>
        <Text style={[{marginVertical: 15}, styles.advisoryText]}>We never request you to install
          third-party apps
          to view your screen.
        </Text>
        <Text style={styles.advisoryText}>For any queries, please reach-out to us via the Help
          section
          on our mobile app or call on our official customer support numbers:
          0124 4628747, 0124 5045105, 0124 4628747 & 0124 2898 000
        </Text>
      </View>
    </View>
  </BottomSheetModal>
);

const styles = {
  container: {
    height: 300,
    backgroundColor: colors.white
  },
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    marginVertical: 16,
    marginHorizontal: 16
  },
  closeContainer: {
    height: 24,
    width: 24,
    padding: 4,
    marginRight: 25,
    marginTop: 2
  },
  closeImage: {
    height: 14,
    width: 14
  },
  header: {
    fontFamily: fonts.black,
    fontSize: 20,
    color: colors.defaultTextColor
  }
};
UserAdvisoryModal.propTypes = {
  onUserAdvisoryModalClick: PropTypes.func.isRequired
};

export default UserAdvisoryModal;
