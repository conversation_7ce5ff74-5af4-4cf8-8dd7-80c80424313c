import React, { Fragment, useEffect, useState } from 'react';
import { Modal } from 'react-native';
import { debounce } from 'lodash';
import StateSelection from '../StateSelectionModal';
import BillingAddress from '../BillingAddress';
import { DispatchProps, StateList, StateProps } from '../../Helpers/interface';
import { DELAY } from '../../Helpers/constants';
import { isInStateList, getSelectedState, getStateList } from '../../Helpers/utils';

type CorpGSTINProps = StateProps & DispatchProps;

const CorpGSTIN: React.FC<CorpGSTINProps> = ({
  state,
  gstNumber,
  companyName,
  companyAddress,
  saveGstDetails,
  errorState,
  initialise,
  stateUpdate,
  gstNumberUpdate,
  companyNameUpdate,
  addressUpdate,
  saveGstDetailToggle,
  resetComapanyNameFieldErr,
  resetAddressFieldErr,
}) => {
  const [selectState, setSelectState] = useState(false);
  const [stateList, setStateList] = useState<StateList[]>([]);

  const updateGstNumber = debounce(gstNumberUpdate, DELAY);
  const updateCompanyName = debounce(companyNameUpdate, DELAY);
  const updateAddress = debounce(addressUpdate, DELAY);

  useEffect(() => {
    initialise();

    (async () => {
      const stateList = await getStateList();
      setStateList(stateList);

      if (stateList?.length > 0) {
        const selectedState = await getSelectedState();

        const isStateAutoSelected = stateList.find(isInStateList(selectedState));

        if (isStateAutoSelected) {
          stateUpdate(selectedState);
        }
      }
    })();
  }, []);

  const handleGstUpdate = (value: string) => {
    if (value === '') {
      saveGstDetailToggle(false);
      resetComapanyNameFieldErr();
      resetAddressFieldErr();
    }

    updateGstNumber(value);
  };

  const selectHandler = (value: string) => {
    stateUpdate(value);
    setSelectState(false);
  };

  const onClose = () => {
    setSelectState(false);
  };

  return (
    <Fragment>
      {selectState && (
        <Modal animationType="slide" onRequestClose={onClose} transparent={true}>
          <StateSelection
            closeHandler={onClose}
            selectHandler={selectHandler}
            stateList={stateList}
          />
        </Modal>
      )}
      <BillingAddress
        state={state}
        gst={gstNumber}
        comapany={companyName}
        address={companyAddress}
        saveGstDetails={saveGstDetails}
        onStateChange={setSelectState}
        onGstChange={handleGstUpdate}
        onCompanyChange={updateCompanyName}
        onAddressChange={updateAddress}
        onSaveDetails={saveGstDetailToggle}
        errorState={errorState}
      />
    </Fragment>
  );
};

export default CorpGSTIN;
