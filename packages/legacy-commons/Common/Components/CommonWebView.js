import React from 'react';
import { Platform, View, BackHandler } from 'react-native';
import { WebView } from 'react-native-webview';
import PropTypes from 'prop-types';
import { Actions } from 'react-native-router-flux';
import ViewControllerModule from '../../Native/ViewControllerModule';
import BasePage from '../navigation/BasePage';
import SimpleHeader from './Header/SimpleHeader';

/**
 * generic class for opening web view page in react native
 * Parameters needed : url, header text,header icon
 */

class CommonWebView extends BasePage {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props);
    this.handleBackButtonClick = this.onBackPress.bind(this);
    this.state = {
      canGoBack: false,
      title: this.props.headerText,
    };
  }

  componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
    if (this.props.onTncClose) {
      this.props.onTncClose();
    }
  }

  render() {
    const uri = this.props.url;
    const headers = this.props.customHeaders || {};
    return (
      <View style={{ flex: 1 }}>
        <SimpleHeader title={this.state.title} iconPress={this.onBackPress} />
        <WebView
          ref={(e) => (this._webView = e)}
          source={{ uri: uri, headers: headers }}
          onNavigationStateChange={this.onNavigationStateChange}
          startInLoadingState
        />
      </View>
    );
  }

  onNavigationStateChange = (navState) => {
    const newTitle = this.props.headerText ?? navState.title;
    this.setState({
      canGoBack: navState.canGoBack,
      title: newTitle,
    });
  };

  onBackPress = () => {
    if (this.state.canGoBack) {
      this._webView.goBack();
      return true;
    }

    //TODO: These lines of code temporarily added here only for React-Navigation
    if (this.props.onBackPressed) {
      this.props.onBackPressed();
      return true;
    }

    const somethingPoped = Actions.pop();
    if (!somethingPoped) {
      if (Platform.OS === 'ios') {
        ViewControllerModule.popViewController(this.props.rootTag);
      } else {
        BackHandler.exitApp();
      }
    }
    return true;
  };
}

CommonWebView.propTypes = {
  url: PropTypes.string.isRequired,
  headerText: PropTypes.string.isRequired,
  customHeaders: PropTypes.object,
  onBackPressed: PropTypes.func,
};
CommonWebView.defaultProps = {
  customHeaders: {},
};
export default CommonWebView;
