import React, { Component } from 'react';
import { BackHandler, Platform, View } from 'react-native';
import { WebView } from 'react-native-webview';
import PropTypes from 'prop-types';
import { Actions } from 'react-native-router-flux';
import CommonHeader from './Header/CommonHeader';
import ViewControllerModule from '../../Native/ViewControllerModule';
import {publishWebViewEvent} from './WebViewEventHandlers';

const WEB_VIEW_REF = 'WEB_VIEW_REF';

/**
 * generic class for opening web view page in react native
 * Parameters needed : url, header text,header icon
 */
class WebViewWrapper extends Component {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props);
    this.handleBackButtonClick = this.onBackPress.bind(this);
    this.onEventReceived = this.onEventReceived.bind(this);
    this.state = {
      canGoBack: false,
    };
    this.timer = null;
  }

  componentWillMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
    this.timer = setTimeout(() => {
      this.setState({invalidate: true});
    }, 300);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
  }

  render() {
    const uri = this.props.url;
    return (
      <View style={{
        flex: 1,
        flexDirection: 'column',
      }}>
        {this.props.showHeader && <CommonHeader
          headerText={this.props.headerText}
          imgSrc={this.props.headerIcon}
          whiteHeader={this.props.whiteHeader}
          backPressHandler={this.onBackPress}
        />}
        <WebView
          style={{
            flex: 1,
            flexGrow: 1,
          }}
          ref={e => this._webView = e}
          source={{
            uri,
            headers: this.props.headers,
          }}
          onNavigationStateChange={this.onNavigationStateChange}
          startInLoadingState
          sharedCookiesEnabled={this.props.sharedCookiesEnabled}
          injectedJavaScript={this.props.injectedJavaScript || null}
          onMessage={(event => this.onEventReceived(event))}
        />
      </View>
    );
  }

  onEventReceived = (event) => {
    const eventData = event.nativeEvent.data;
    if (!eventData) {
      return;
    }
    const {type = '', data = {}} = JSON.parse(eventData);
    publishWebViewEvent(type, data);
  };

  onNavigationStateChange = (navState) => {
    this.setState({
      canGoBack: navState.canGoBack,
    });
  };

  onBackPress = () => {
    if (this.state.canGoBack) {
      this._webView.goBack();
      return true;
    }
    if (this.props.onBackPressed) {
      this.props.onBackPressed();
      return true;
    }
    this.props.backPressCb && this.props.backPressCb();
    const somethingPoped = Actions.pop();
    if (!somethingPoped) {
      if (Platform.OS === 'ios') {
        ViewControllerModule.popViewController(this.props.rootTag);
      } else {
        BackHandler.exitApp();
      }
    }
    return true;
  };
}

WebViewWrapper.propTypes = {
  url: PropTypes.string.isRequired,
  headerText: PropTypes.string.isRequired,
  headerIcon: PropTypes.number.isRequired,
  whiteHeader: PropTypes.bool,
  headers: PropTypes.object,
  sharedCookiesEnabled: PropTypes.bool,
  showHeader: PropTypes.bool,
  onBackPressed: PropTypes.func,
};

WebViewWrapper.defaultProps = {
  whiteHeader: false,
  headers: {},
  sharedCookiesEnabled: false,
  showHeader: true,
};

export default WebViewWrapper;
