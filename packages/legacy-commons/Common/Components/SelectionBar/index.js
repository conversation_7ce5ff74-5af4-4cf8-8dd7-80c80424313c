import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import ToggleBtn from '../Buttons/ToggleBtn';
import PropTypes from 'prop-types';

const SelectionBar = (props) => {
  const btnList = [];

  const {filterObj, description, optionsObj, handleToggle, scrollable, hotelStar} = props;
  const {options, optionsMaster} = optionsObj;

  for (let optionMasterIndex = 0; optionMasterIndex < optionsMaster.length; optionMasterIndex++) {
    let found = false;
    let active;
    for (let optionIndex = 0; optionIndex < options.length; optionIndex++) {
      if (optionsMaster[optionMasterIndex].uniqueId == options[optionIndex].uniqueId) {
        found = true;
        active = options[optionIndex].isActive;
      }
    }
    if (found) {
      btnList.push(getToggleBtn(optionsMaster, optionMasterIndex, hotelStar, handleToggle, active, false, filterObj));
    } else {
      btnList.push(getToggleBtn(optionsMaster, optionMasterIndex, hotelStar, handleToggle, false, true, filterObj));
    }
  }


  return (
    <View style={styles.container}>
      {description && <Text style={styles.description}>{description}</Text>}
      <View style={styles.bar}>
        {scrollable ? <ScrollView horizontal={scrollable}
                                  showsHorizontalScrollIndicator={false}>{btnList}</ScrollView> : btnList}
      </View>
    </View>
  );
};


const getToggleBtn = (optionsMaster, optionMaster, hotelStar, handleToggle, isActive, isDisabled, filterObj) => {

  const toggleBtnObj = {
    hotelStar,
    label: optionsMaster[optionMaster].filterText,
    uniqueId: optionsMaster[optionMaster].uniqueId,
    onToggle: handleToggle,
    isDisabled,
    isActive,
    filterObj
  };

  return (<ToggleBtn key={optionsMaster[optionMaster].uniqueId}
                     {...toggleBtnObj}></ToggleBtn>);
};


SelectionBar.propTypes = {
  filterObj: PropTypes.object,
  description: PropTypes.string,
  optionsObj: PropTypes.object.isRequired,
  handleToggle: PropTypes.func.isRequired,
  hotelStar: PropTypes.bool,
  scrollable: PropTypes.bool
};
const styles = StyleSheet.create({
  description: {
    fontFamily: 'Lato-Italic',
    fontSize: 12,
    fontStyle: 'italic',
    letterSpacing: 0.3,
    color: '#9b9b9b',
    marginBottom: 5
  },
  bar: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  }
});

export default SelectionBar;
