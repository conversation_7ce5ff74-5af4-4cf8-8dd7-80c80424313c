import { isEmpty } from 'lodash';
import React from 'react';
import {
    View,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    Pressable
} from 'react-native';

const withPermissionDialog = (WrappedComponent) => {

    return class AndroidPermissionDialog extends React.Component {
        constructor(props) {
            super(props);
            this.state = {
                data: null
            };
        }
        showPermissionDialog = (data = {
            title,
            message,
            buttonPositive: { title, callback },
            buttonNegative: { title, callback },
        }) => {
            this.setState({ data })
        }
        hidePermissionDialog = () => {
            this.setState({ data: null })
        }
        handlePositiveButton = () => {
            const { data } = this.state;
            data.buttonPositive.callback()
            this.hidePermissionDialog()
        }
        handleNegativeButton = () => {
            const { data } = this.state;
            data.buttonNegative.callback()
            this.hidePermissionDialog()
        }
        render() {
            const { data } = this.state;
            return (
                <View style={styles.centeredView}>
                    <Modal
                        animationType="fade"
                        transparent={true}
                        visible={!isEmpty(data)}
                        onRequestClose={() => {
                            this.hidePermissionDialog()
                        }}
                    >
                        <Pressable style={[styles.androidBackdrop, styles.backdrop]} onPress={this.hidePermissionDialog} />
                        {!isEmpty(data) && <View style={styles.alertBoxContainer}>
                            <View style={styles.alertBox}>
                                {!isEmpty(data.title) && <Text style={styles.title}>{data.title}</Text>}
                                <Text style={styles.message}>{data.message}</Text>
                                <View style={styles.buttonContainer}>
                                    {data.buttonPositive && <TouchableOpacity activeOpacity={0.5} onPress={this.handlePositiveButton}>
                                        <Text style={styles.button}>{data.buttonPositive.title}</Text>
                                    </TouchableOpacity>}
                                    {data.buttonNegative && <TouchableOpacity activeOpacity={0.5} onPress={this.handleNegativeButton}>
                                        <Text style={styles.button}>{data.buttonNegative.title}</Text>
                                    </TouchableOpacity>}
                                </View>
                            </View>
                        </View>}
                    </Modal>
                    <WrappedComponent
                        {...this.props}
                        showPermissionDialog={this.showPermissionDialog}
                        hidePermissionDialog={this.hidePermissionDialog} />
                </View>
            )
        }
    }
}
export default withPermissionDialog;

const styles = StyleSheet.create({
    centeredView: {
       flex: 1
    },
    androidBackdrop: {
        backgroundColor: "#232f34",
        opacity: 0.32
    },
    backdrop: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    alertBoxContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    alertBox: {
        maxWidth: '90%',
        elevation: 24,
        borderRadius: 2,
        backgroundColor: '#fff',
        padding: 16
    },
    title: {
        fontFamily: 'Lato',
        fontWeight: 'bold',
        fontSize: 16,
        color: '#000',
        marginBottom: 12
    },
    message: {
        fontFamily: 'Lato',
        fontWeight: '500',
        fontSize: 14,
        color: '#000'
    },
    buttonContainer: {
        marginTop: 16,
        flexDirection: 'row',
        alignSelf: 'flex-end'
    },
    button: {
        padding: 12,
        fontFamily: 'Lato',
        fontWeight: 'bold',
        fontSize: 14,
        color: '#297e55',
        textTransform: 'uppercase'
    }

});