import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  Modal,
  ImageSourcePropType,
} from 'react-native';
const locationIcon = require('@mmt/legacy-assets/src/ic_location_perm.webp');
import { colors, fonts } from '../../../Styles/globalStyles';

interface PermissionBottomSheetProps {
  title: string;
  subTitle?: string;
  icon?: ImageSourcePropType;
  message: string;
  positiveCTA: string;
  negativeCTA: string;
  onPermissionGranted: () => void;
  onPermissionRejected: () => void;
  visible: boolean;
}

export const PermissionBottomSheet = (props: PermissionBottomSheetProps) => {
  const {
    visible = false,
    title,
    subTitle,
    icon = locationIcon,
    message,
    positiveCTA = 'NOT NOW',
    negativeCTA = 'GO AHEAD',
    onPermissionGranted,
    onPermissionRejected,
  } = props;

  return (
    <Modal
      onRequestClose={onPermissionRejected}
      transparent={true}
      visible={visible}
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <View style={styles.locationRoot}>
          <View style={styles.locationSettingsContent}>
            <Text style={styles.locationSettingsHeader}>{title}</Text>
            <Text style={styles.locationSettingsSubHeader}>{subTitle}</Text>
            <View style={styles.rowContainer}>
              <Image style={styles.locationSettingsLocationIcon} source={icon} />
              <Text style={styles.locationSettingsRationaleText}>{message}</Text>
            </View>
            <View style={styles.locationSettingsActionsContainer}>
              <TouchableOpacity onPress={onPermissionRejected}>
                <Text style={styles.locationSettingsActionButton}>{negativeCTA}</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={onPermissionGranted}>
                <Text
                  style={[
                    styles.locationSettingsActionButton,
                    styles.locationSettingsActionButtonPrimary,
                  ]}
                >
                  {positiveCTA}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    flex: 1,
    justifyContent: 'flex-end',
  },
  locationRoot: {
    backgroundColor: colors.white,
  },
  locationSettingsContent: {
    margin: 24,
  },
  locationSettingsHeader: {
    fontSize: 34,
    fontFamily: fonts.light,
    color: colors.black,
  },
  locationSettingsSubHeader: {
    fontSize: 16,
    marginTop: 16,
    lineHeight: 20,
    marginBottom: 16,
    fontFamily: fonts.light,
    color: colors.black,
  },
  rowContainer: {
    flexDirection: 'row',
    marginRight: 24,
    alignItems: 'center',
  },
  locationSettingsLocationIcon: {
    width: 40,
    resizeMode: 'contain',
  },
  locationSettingsRationaleText: {
    fontSize: 16,
    lineHeight: 18,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    padding: 8,
  },
  locationSettingsActionsContainer: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'space-between',
  },
  locationSettingsActionButton: {
    color: colors.azure,
    fontSize: 16,
    fontFamily: fonts.medium,
    lineHeight: 18,
  },
  locationSettingsActionButtonPrimary: {
    fontFamily: fonts.bold,
  },
});
