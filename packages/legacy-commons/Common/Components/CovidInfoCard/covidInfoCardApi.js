import NetworkModule from '../../../Native/NetworkModule';
import {Platform} from 'react-native';

const getHeaders = async () => {
  const os = Platform.OS;
  if (os === 'web') {
    return {
      ver: '8.0.7',
      deviceid: '6789240662bad67f',
      os,
      'content-type': 'application/json; charset=utf-8'
    };
  }
  const headers = await NetworkModule.getHeaders();
  return headers;
};

const http = {};

http.post = async (url, params = {}) => {
  const body = JSON.stringify(params);
  const response = await fetch(url, {
    method: 'POST',
    headers: await getHeaders(),
    body
  });
  return response.json();
};

export default function getCovidCosmosDataApi(cosmosScope) {
  const req = {
    userEvent: {
      abInfo: '',
      landingLocationLobList: [
        'ALL'
      ]
    }
  };
  let subDomain = 'cp3o';
  if (Platform.OS === 'web') {
    subDomain = 'mapi';
  }
  const url = `https://${subDomain}.makemytrip.com/cosmos_personalization/v2/getScopeData?scope=${cosmosScope}`;
  return http.post(url, req);
}
