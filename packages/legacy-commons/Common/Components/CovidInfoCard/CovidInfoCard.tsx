import React, { useEffect, useState } from 'react';
import { TouchableOpacity, Text, Image, StyleSheet, ScrollView, Dimensions, View, Platform } from 'react-native';
import { backIcon } from '../../../Helpers/displayHelper';
import LinearGradient from 'react-native-linear-gradient';
import { fonts, colors } from '../../../Styles/globalStyles';
import getCovidCosmosDataApi from './covidInfoCardApi';
import { COVID_INFO_CARDS_TEST_ID } from './CovidInfoCardTestIds';
interface CovidInfoCardProps {
  cosmosScope: string;
  trackClickEvent?: (index: number) => void,
  openWebView: (props: object) => void
  id?: string;
}
interface DataInterface {
  subTitle: string;
  title: string;
  imageList: {
    descriptionImg: string;
    detailsLink: string;
    altText: string;
  }[]
}
const defaultWidth = 276
const defaultHeight = 111
function _getHeroImageUrl(url: string): string {
  return url.replace('/hdpi/', '/xhdpi/')
}
export default function CovidInfoCard(props: CovidInfoCardProps) {
  const {
    cosmosScope,
    trackClickEvent = () => { },
    openWebView,
  } = props;
  const [hasError, setError] = useState(false);
  const [data, setData] = useState<DataInterface>({} as DataInterface);
  const [imageErrorList, setImageErrorList] = useState<boolean[]>([]);
  useEffect(() => {
    const getCosmosData = async () => {
      try {
        const response = await getCovidCosmosDataApi(cosmosScope);
        const lobdata = response?.scopeData?.[cosmosScope]?.cardData?.Covid_Info_Banner?.data;
        if (Array.isArray(lobdata)) {
          setData(lobdata[0]);
          const errorList = Array.from(lobdata[0]?.imageList, () => false);
          setImageErrorList(errorList);
        } else {
          setError(true);
        }
      } catch (error) {
        // setError(true);
        // setData(apiData);
        console.log('cosmos error', error);
      }
    };
    getCosmosData();
  }, [cosmosScope]);
  const onMoreDetailsPressed = (detailsUrl: string, index: number) => {
    if (!detailsUrl) {
      return;
    }
    trackClickEvent(index + 1);
    if (typeof openWebView === 'function') {
      openWebView({
        url: detailsUrl,
        headerText: 'MakeMyTrip',
        headerIcon: backIcon
      });
    }
  };
  const handleImageError = (i: number) => {
    const newErroList = imageErrorList.map((errorValue, index) => {
      return i === index || errorValue
    });
    setImageErrorList(newErroList);
  };
  if (hasError || Object.keys(data).length === 0) {
    return null;
  }
  return (
    <LinearGradient
      colors={['#ffffff', '#ffeeed']}
      start={{ x: 0.0, y: 0.0 }}
      end={{ x: 1.0, y: 0.0 }}
      style={styles.gradientContainer}
    >
      <Text style={styles.subHeading}>{data?.subTitle}</Text>
      <Text style={styles.heading} testID={COVID_INFO_CARDS_TEST_ID.COVID_UPDATE}>{data?.title}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={{
          alignItems: 'center',
          justifyContent: 'center',
          flex: 1,
          paddingHorizontal: 12,
          flexDirection: "row"
        }}>
          {data?.imageList.map(({ descriptionImg, detailsLink, altText }, index) => {
            let width = defaultWidth;
            if (data?.imageList.length <= 1) {
              width = Dimensions.get('window').width - 32;
            }
            let height = (defaultHeight / defaultWidth) * width
            return (
              <TouchableOpacity
                activeOpacity={detailsLink ? 0.6 : 1}
                onPress={() => onMoreDetailsPressed(detailsLink, index)}
                key={detailsLink}
              >
                <View style={cardStyle} testID={COVID_INFO_CARDS_TEST_ID.COVID_KNOW_MORE}>
                  {/* have to update rnw else cant use lazy loading for image*/}
                  {!imageErrorList[index] && Platform.OS === 'web' && (
                    // @ts-ignore
                    <img
                      src={_getHeroImageUrl(descriptionImg)}
                      style={{
                        width: width,
                        height: height,
                      }}
                      loading='lazy'
                      alt={altText}
                    />
                  )}
                  {!imageErrorList[index] && Platform.OS !== 'web' && (
                    <Image
                      source={{ uri: _getHeroImageUrl(descriptionImg) }}
                      style={{
                        width: width,
                        height: height,
                        resizeMode: "contain"
                      }}
                      resizeMethod={"scale"}
                      accessibilityLabel={altText}
                      onError={() => handleImageError(index)}
                    />
                  )}
                  {imageErrorList[index] &&
                    <View style={[{ height: 30, width: 270 }, { backgroundColor: colors.white }]}>
                      <Text style={styles.fallbackText}>Know More</Text>
                    </View>
                  }
                </View>
              </TouchableOpacity>
            )
          })}
        </View>
      </ScrollView>
    </LinearGradient>
  );
}
const cardStyle = {
  backgroundColor: colors.transparent,
  marginHorizontal: 0,
  padding: 0,
  marginTop: 16,
};
const styles = StyleSheet.create({
  gradientContainer: {
    paddingVertical: 12
  },
  subHeading: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.black,
    paddingHorizontal: 16,
    lineHeight: 20
  },
  heading: {
    fontFamily: fonts.black,
    fontSize: 22,
    paddingHorizontal: 16,
    color: colors.black
  },
  descImage: {
    borderRadius: 5,
  },
  fallbackText: {
    color: colors.azure, fontSize: 18, textAlign: 'center', marginTop: 16
  }
});
