import {colors} from '../../../Styles/globalStyles';

const styles = {
  checkBtn: {
    flexDirection: 'row',
    paddingBottom: 0,
    paddingHorizontal: 0,
    paddingRight: 30,
    alignItems: 'flex-start'
  },
  checkbox: {
    height: 18,
    width: 18,
    backgroundColor: colors.white,
    borderColor: colors.gray3,
    borderWidth: 1.5,
    borderRadius: 3,
    overflow: 'hidden',
    position: 'absolute',
    top: 2,
    right: 0
  },
  checkboxSelected: {
    borderColor: 'transparent',
    borderWidth: 0
  },
  checkedIcon: {
    position: 'absolute',
    height: 18,
    width: 18,
    left: 0,
    top: 0
  }
};

export default styles;
