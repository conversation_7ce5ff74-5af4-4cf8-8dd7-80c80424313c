import React from 'react';
import PropTypes from 'prop-types';
import { TouchableNativeFeedback } from 'react-native';

const TouchableRipple = ({ testID, feedbackColor, onPress, children, disabled, ...otherProps }) => (
  <TouchableNativeFeedback
    testID={testID}
    onPress={onPress}
    useForeground
    disabled={disabled}
    background={TouchableNativeFeedback.Ripple(feedbackColor, false)}
    {...otherProps}
  >
    {children}
  </TouchableNativeFeedback>
);

TouchableRipple.propTypes = {
  feedbackColor: PropTypes.string,
  disabled: PropTypes.bool,
  onPress: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
};

TouchableRipple.defaultProps = {
  feedbackColor: 'rgba(0, 0, 0, 0.1)',
  disabled: false,
  testID: '',
};

export default TouchableRipple;
