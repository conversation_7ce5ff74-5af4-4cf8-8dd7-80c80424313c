import React from 'react';
import {View} from 'react-native';
import {Player, ControlBar} from 'react-native-video';
import './videoImport';
import styles from './webPlayerCss';


const WebVideoPlayer = ({source, autoPlay, saveRef, aspectRatio}) => (
    <View style={styles.container}>
        <Player
            ref={element => saveRef && saveRef(element)}
            autoPlay={autoPlay}
            src={source.uri}
        >
            <ControlBar autoHide={false} />
        </Player>
    </View>
);

export default WebVideoPlayer;
