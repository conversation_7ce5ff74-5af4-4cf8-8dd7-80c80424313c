import React, {Component} from 'react';
import Video from 'react-native-video';
import {
  TouchableWithoutFeedback,
  TouchableHighlight,
  ImageBackground,
  PanResponder,
  Animated,
  Easing,
  Image,
  View,
  Text,
  BackHandler,
  Platform,
  Dimensions
} from 'react-native';
import _ from 'lodash';
import PropTypes from 'prop-types';
import styles from './appPlayerCss';
import Orientation from 'react-native-orientation';

const USER_PLATFORM = {
  IOS: Platform.OS === 'ios',
  ANDROID: Platform.OS === 'android',
  WEB: Platform.OS === 'web'
};
const DEVICE_WINDOW = Dimensions.get('screen');
export default class VideoPlayer extends Component {
  static defaultProps = {
    toggleResizeModeOnFullscreen: true,
    playInBackground: false,
    playWhenInactive: false,
    showOnStart: true,
    resizeMode: 'contain',
    paused: false,
    repeat: false,
    volume: 1,
    muted: true,
    title: '',
    rate: 1,
    onPause: undefined,
    mmtTracker: undefined,
    renderBottomControls: true,
    renderRestartBtn: true,
    renderGreenViewForBottomControls: true,
    onVideoPress: {}
  };

  constructor(props) {
    super(props);

    /**
     * All of our values that are updated by the
     * methods and listeners in this class
     */
    this.state = {
      // Video
      resizeMode: this.props.resizeMode,
      paused: this.props.paused,
      muted: this.props.muted,
      volume: this.props.volume,
      rate: this.props.rate,
      poster: this.props.poster,
      restartBtn: false,
      // Controls

      isFullscreen: this.props.resizeMode === 'cover' || false,
      showTimeRemaining: true,
      volumeTrackWidth: 0,
      lastScreenPress: 0,
      volumeFillWidth: 0,
      seekerFillWidth: 0,
      showControls: this.props.showOnStart,
      volumePosition: 0,
      seekerPosition: 0,
      volumeOffset: 0,
      seekerOffset: 0,
      seeking: false,
      loading: false,
      currentTime: 0,
      error: false,
      duration: 0
    };

    /**
     * Any options that can be set at init.
     */
    this.opts = {
      playWhenInactive: this.props.playWhenInactive,
      playInBackground: this.props.playInBackground,
      repeat: this.props.repeat,
      title: this.props.title,
      mmtTracker: this.props.mmtTracker
    };

    /**
     * Our app listeners and associated methods
     */
    this.events = {
      onError: this.props.onError || this._onError.bind(this),
      onBack: this.props.onBack || this._onBack.bind(this),
      onEnd: this.props.onEnd || this._onEnd.bind(this),
      onScreenTouch: this._onScreenTouch.bind(this),
      onEnterFullscreen: this.props.onEnterFullscreen,
      onExitFullscreen: this.props.onExitFullscreen,
      onLoadStart: this._onLoadStart.bind(this),
      onProgress: this._onProgress.bind(this),
      onBuffer: this._onBuffer.bind(this),
      onLoad: this._onLoad.bind(this),
      onPause: this.props.onPause,
      onPlay: this.props.onPlay,
      onFullscreenPlayerDidDismiss: this._onFullscreenPlayerDidDismiss.bind(this)
    };

    /**
     * Functions used throughout the application
     */
    this.methods = {
      toggleFullscreen: this._toggleFullscreen.bind(this),
      togglePlayPause: this._togglePlayPause.bind(this),
      toggleControls: this._toggleControls.bind(this),
      toggleTimer: this._toggleTimer.bind(this),
      pauseVideo: this._pauseVideo.bind(this)
    };

    /**
     * Player information
     */
    this.player = {
      controlTimeoutDelay: this.props.controlTimeout || 15000,
      volumePanResponder: PanResponder,
      seekPanResponder: PanResponder,
      controlTimeout: null,
      volumeWidth: 150,
      iconOffset: 7,
      seekWidth: 0,
      ref: Video
    };

    /**
     * Various animations
     */
    const initialValue = this.props.showOnStart ? 1 : 0;

    this.animations = {
      bottomControl: {
        marginBottom: new Animated.Value(0),
        opacity: new Animated.Value(initialValue)
      },
      topControl: {
        marginTop: new Animated.Value(0),
        opacity: new Animated.Value(initialValue)
      },
      video: {
        opacity: new Animated.Value(1)
      },
      loader: {
        rotate: new Animated.Value(0),
        MAX_VALUE: 360
      }
    };

    /**
     * Various styles that be added...
     */
    this.styles = {
      videoStyle: this.props.videoStyle || {},
      containerStyle: this.props.style || {}
    };
  }


  /**
   | -------------------------------------------------------
   | Events
   | -------------------------------------------------------
   |
   | These are the events that the <Video> component uses
   | and can be overridden by assigning it as a prop.
   | It is suggested that you override onEnd.
   |
   */

  /**
   * When load starts we display a loading icon
   * and show the controls.
   */
  _onLoadStart() {
    const state = this.state;
    state.loading = true;
    state.restartBtn = false;
    this.loadAnimation();
    this.setState(state);

    if (typeof this.props.onLoadStart === 'function') {
      this.props.onLoadStart(...arguments);
    }
  }

  _pauseVideo() {
    const state = this.state;
    state.paused = true;
    typeof this.events.onPause === 'function' && this.events.onPause();
    state.restartBtn = false;
    this.setState(state);
  }

  /**
   * When load is finished we hide the load icon
   * and hide the controls. We also set the
   * video duration.
   *
   * @param {object} data The video meta data
   */
  _onLoad(data = {}) {
    const state = this.state;

    state.duration = data.duration;
    state.loading = false;
    state.restartBtn = false;
    state.poster = undefined;
    this.setState(state);

    if (state.showControls) {
      this.setControlTimeout();
    }

    if (typeof this.props.onLoad === 'function') {
      this.props.onLoad(...arguments);
    }
  }

  /**
   * For onprogress we fire listeners that
   * update our seekbar and timer.
   *
   * @param {object} data The video meta data
   */
  _onProgress(data = {}) {
    const state = this.state;
    state.currentTime = data.currentTime;
    if (!state.seeking) {
      const position = this.calculateSeekerPosition();
      this.setSeekerPosition(position);
    }

    if (typeof this.props.onProgress === 'function') {
      this.props.onProgress(...arguments);
    }

    this.setState(state);
  }

  _onBuffer(event) {
    const state = this.state;
    state.loading = true;
    this.setState(state);
  }
  /**
   * It is suggested that you override this
   * command so your app knows what to do.
   * Either close the video or go to a
   * new page.
   */
  _onEnd() {
    const state = this.state;
    state.showControls = false;
    if (this.props.dash) {
      state.restartBtn = false;
      state.paused = false;
    } else {
      if(this.props.repeat) {
        this.seekTo(0);
      } else {
        state.paused = true;
        state.restartBtn = true;
      }
      this.hideControlAnimation();
    }
    this.clearControlTimeout();
    this.setState(state);
    if (state.isFullscreen) {
      this._toggleFullscreen();
    }
  }

  renderRestartBtn() {
    if (this.state.restartBtn) {
      const source = require('@mmt/legacy-assets/src/img/restart.webp');
      return this.renderControl(
        <Image source={source} />,
        this.methods.togglePlayPause,
        styles.restart.container
      );
    }
    return null;
  }

  /**
   * Set the error state to true which then
   * changes our renderError function
   *
   * @param {object} err  Err obj returned from <Video> component
   */
  _onError(err) {
    const state = this.state;
    state.error = true;
    state.loading = false;

    this.setState(state);
  }

  /**
   * This is a single and double tap listener
   * when the user taps the screen anywhere.
   * One tap toggles controls, two toggles
   * fullscreen mode.
   */
  _onScreenTouch() {
    const state = this.state;
    const time = new Date().getTime();
    const delta = time - state.lastScreenPress;

    if (delta < 300) {
      this.methods.toggleFullscreen();
    }

    this.methods.toggleControls();
    state.lastScreenPress = time;

    this.setState(state);
    if(!this.props.toggleResizeModeOnFullscreen && this.props.disableFullscreen) {
      this.props.onVideoPress()
    }
  }


  /**
   | -------------------------------------------------------
   | Methods
   | -------------------------------------------------------
   |
   | These are all of our functions that interact with
   | various parts of the class. Anything from
   | calculating time remaining in a video
   | to handling control operations.
   |
   */

  /*
   * Set a timeout when the controls are shown
   * that hides them after a length of time.
   * Default is 15s
   */
  setControlTimeout() {
    this.player.controlTimeout = setTimeout(() => {
      this._hideControls();
    }, this.player.controlTimeoutDelay);
  }

  /**
   * Clear the hide controls timeout.
   */
  clearControlTimeout() {
    clearTimeout(this.player.controlTimeout);
  }

  /**
   * Reset the timer completely
   */
  resetControlTimeout() {
    this.clearControlTimeout();
    this.setControlTimeout();
  }

  /**
   * Animation to hide controls. We fade the
   * display to 0 then move them off the
   * screen so they're not interactable
   */
  hideControlAnimation() {
    Animated.parallel([
      Animated.timing(
        this.animations.topControl.opacity,
        {toValue: 0}
      ),
      Animated.timing(
        this.animations.topControl.marginTop,
        {toValue: -100}
      ),
      Animated.timing(
        this.animations.bottomControl.opacity,
        {toValue: 0}
      ),
      Animated.timing(
        this.animations.bottomControl.marginBottom,
        {toValue: -100}
      )
    ]).start();
  }

  /**
   * Animation to show controls...opposite of
   * above...move onto the screen and then
   * fade in.
   */
  showControlAnimation() {
    Animated.parallel([
      Animated.timing(
        this.animations.topControl.opacity,
        {toValue: 1}
      ),
      Animated.timing(
        this.animations.topControl.marginTop,
        {toValue: 0}
      ),
      Animated.timing(
        this.animations.bottomControl.opacity,
        {toValue: 1}
      ),
      Animated.timing(
        this.animations.bottomControl.marginBottom,
        {toValue: 0}
      )
    ]).start();
  }

  /**
   * Loop animation to spin loader icon. If not loading then stop loop.
   */
  loadAnimation() {
    if (this.state.loading) {
      Animated.sequence([
        Animated.timing(
          this.animations.loader.rotate,
          {
            toValue: this.animations.loader.MAX_VALUE,
            duration: 1500,
            easing: Easing.linear
          }
        ),
        Animated.timing(
          this.animations.loader.rotate,
          {
            toValue: 0,
            duration: 0,
            easing: Easing.linear
          }
        )
      ]).start(this.loadAnimation.bind(this));
    }
  }

  /**
   * Function to hide the controls. Sets our
   * state then calls the animation.
   */
  _hideControls() {
    const state = this.state;
    state.showControls = false;
    this.hideControlAnimation();

    this.setState(state);
  }

  /**
   * Function to toggle controls based on
   * current state.
   */
  _toggleControls() {
    const state = this.state;
    state.showControls = !state.showControls;

    if (state.showControls) {
      this.showControlAnimation();
      this.setControlTimeout();
    } else {
      this.hideControlAnimation();
      this.clearControlTimeout();
    }

    this.setState(state);
  }

  /**
   * Toggle fullscreen changes resizeMode on
   * the <Video> component then updates the
   * isFullscreen state.
   */
  _toggleFullscreen() {
    if (this.props.disableFullscreen){
      return
    }

    const state = this.state;

    state.isFullscreen = !state.isFullscreen;

    if (this.props.toggleResizeModeOnFullscreen) {
      state.resizeMode = state.isFullscreen === true ? 'cover' : 'contain';
    }

    if (state.isFullscreen) {
      typeof this.events.onEnterFullscreen === 'function' && this.events.onEnterFullscreen();
      BackHandler.addEventListener('hardwareBackPress', this.backPressHandler);
      if (USER_PLATFORM.ANDROID) {
        Orientation.lockToLandscapeLeft();
      }
      this.player.ref.presentFullscreenPlayer();
    } else {
      typeof this.events.onExitFullscreen === 'function' && this.events.onExitFullscreen();
      BackHandler.removeEventListener('hardwareBackPress', this.backPressHandler);
      if (USER_PLATFORM.ANDROID) {
        Orientation.lockToPortrait();
      }
      this.player.ref.dismissFullscreenPlayer();
    }
    this.setState(state);
  }

  _onFullscreenPlayerDidDismiss() {
    if (USER_PLATFORM.IOS) {
      this._toggleFullscreen();
    }
  }
  /**
   * Toggle playing state on <Video> component
   */
  _togglePlayPause() {
    const state = this.state;
    state.paused = !state.paused;

    let stateText = '';
    if (state.paused) {
      stateText = 'video_player_pause_click';
      typeof this.events.onPause === 'function' && this.events.onPause();
    } else {
      stateText = 'video_player_play_click';
      typeof this.events.onPlay === 'function' && this.events.onPlay();
    }

    if (this.opts.mmtTracker) {
      this.opts.mmtTracker(stateText);
    }

    state.restartBtn = false;
    this.setState(state);
    if (this.props.updateVideoPausedState) {
      this.props.updateVideoPausedState(state.paused);
    }
  }


  /**
   * Toggle between showing time remaining or
   * video duration in the timer control
   */
  _toggleTimer() {
    const state = this.state;
    state.showTimeRemaining = !state.showTimeRemaining;
    this.setState(state);
  }

  /**
   * The default 'onBack' function pops the navigator
   * and as such the video player requires a
   * navigator prop by default.
   */
  _onBack() {
    if (this.props.navigator && this.props.navigator.pop) {
      this.props.navigator.pop();
    } else {
      console.warn('Warning: _onBack requires navigator property to function. Either modify the onBack prop or pass a navigator prop');
    }
  }

  /**
   * Calculate the time to show in the timer area
   * based on if they want to see time remaining
   * or duration. Formatted to look as 00:00.
   */
  calculateTime() {
    if (this.state.showTimeRemaining) {
      const time = this.state.duration - this.state.currentTime;
      return `-${this.formatTime(time)}`;
    }

    return this.formatTime(this.state.currentTime);
  }

  getTimePlayed() {
    return this.state.currentTime;
  }

  /**
   * Format a time string as mm:ss
   *
   * @param {int} time time in milliseconds
   * @return {string} formatted time string in mm:ss format
   */
  formatTime(time = 0) {
    const symbol = this.state.showRemainingTime ? '-' : '';
    time = Math.min(
      Math.max(time, 0),
      this.state.duration
    );

    const formattedMinutes = _.padStart(Math.floor(time / 60).toFixed(0), 2, 0);
    const formattedSeconds = _.padStart(Math.floor(time % 60).toFixed(0), 2, 0);

    return `${symbol}${formattedMinutes}:${formattedSeconds}`;
  }

  /**
   * Set the position of the seekbar's components
   * (both fill and handle) according to the
   * position supplied.
   *
   * @param {float} position position in px of seeker handle}
   */
  setSeekerPosition(position = 0) {
    const state = this.state;
    position = this.constrainToSeekerMinMax(position);

    state.seekerFillWidth = position;
    state.seekerPosition = position;

    if (!state.seeking) {
      state.seekerOffset = position;
    }

    this.setState(state);
  }

  /**
   * Contrain the location of the seeker to the
   * min/max value based on how big the
   * seeker is.
   *
   * @param {float} val position of seeker handle in px
   * @return {float} contrained position of seeker handle in px
   */
  constrainToSeekerMinMax(val = 0) {
    if (val <= 0) {
      return 0;
    } else if (val >= this.player.seekerWidth) {
      return this.player.seekerWidth;
    }
    return val;
  }

  /**
   * Calculate the position that the seeker should be
   * at along its track.
   *
   * @return {float} position of seeker handle in px based on currentTime
   */
  calculateSeekerPosition() {
    const percent = this.state.currentTime / this.state.duration;
    return this.player.seekerWidth * percent;
  }

  /**
   * Return the time that the video should be at
   * based on where the seeker handle is.
   *
   * @return {float} time in ms based on seekerPosition.
   */
  calculateTimeFromSeekerPosition() {
    const percent = this.state.seekerPosition / this.player.seekerWidth;
    return this.state.duration * percent;
  }

  /**
   * Seek to a time in the video.
   *
   * @param {float} time time to seek to in ms
   */
  seekTo(time = 0) {
    const state = this.state;
    state.currentTime = time;
    this.player.ref.seek(time);
    this.setState(state);
  }

  /**
   * Set the position of the volume slider
   *
   * @param {float} position position of the volume handle in px
   */
  setVolumePosition(position = 0) {
    const state = this.state;
    position = this.constrainToVolumeMinMax(position);
    state.volumePosition = position + this.player.iconOffset;
    state.volumeFillWidth = position;

    state.volumeTrackWidth = this.player.volumeWidth - state.volumeFillWidth;

    if (state.volumeFillWidth < 0) {
      state.volumeFillWidth = 0;
    }

    if (state.volumeTrackWidth > 150) {
      state.volumeTrackWidth = 150;
    }

    this.setState(state);
  }

  toggleAudio() {
    const state = this.state;
    state.muted = !state.muted;

    let stateText = '';
    if (state.muted) {
      stateText = 'video_player_mute_clicked';
    } else {
      stateText = 'video_player_unmute_clicked';
    }

    if (this.opts.mmtTracker) {
      this.opts.mmtTracker(stateText);
    }
    this.setState(state);
  }

  /**
   * Constrain the volume bar to the min/max of
   * its track's width.
   *
   * @param {float} val position of the volume handle in px
   * @return {float} contrained position of the volume handle in px
   */
  constrainToVolumeMinMax(val = 0) {
    if (val <= 0) {
      return 0;
    } else if (val >= this.player.volumeWidth + 9) {
      return this.player.volumeWidth + 9;
    }
    return val;
  }

  /**
   * Get the volume based on the position of the
   * volume object.
   *
   * @return {float} volume level based on volume handle position
   */
  calculateVolumeFromVolumePosition() {
    return this.state.volumePosition / this.player.volumeWidth;
  }

  /**
   * Get the position of the volume handle based
   * on the volume
   *
   * @return {float} volume handle position in px based on volume
   */
  calculateVolumePositionFromVolume() {
    return this.player.volumeWidth / this.state.volume;
  }


  /**
   | -------------------------------------------------------
   | React Component functions
   | -------------------------------------------------------
   |
   | Here we're initializing our listeners and getting
   | the component ready using the built-in React
   | Component methods
   |
   */

  /**
   * Before mounting, init our seekbar and volume bar
   * pan responders.
   */
  componentWillMount() {
    this.initSeekPanResponder();
    this.initVolumePanResponder();
  }

  /**
   * To allow basic playback management from the outside
   * we have to handle possible props changes to state changes
   */
  componentWillReceiveProps(nextProps) {
    if (this.state.paused !== nextProps.paused) {
      this.setState({
        paused: nextProps.paused
      });
    }
  }

  /**
   * Upon mounting, calculate the position of the volume
   * bar based on the volume property supplied to it.
   */
  componentDidMount() {
    const position = this.calculateVolumePositionFromVolume();
    const state = this.state;
    this.setVolumePosition(position);
    state.volumeOffset = position;

    this.setState(state);
  }

  /**
   * When the component is about to unmount kill the
   * timeout less it fire in the prev/next scene
   */
  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.backPressHandler);
    if (USER_PLATFORM.ANDROID) {
      Orientation.lockToPortrait();
    }
    this.clearControlTimeout();
  }

  backPressHandler = (isAppBackPress) => {
    this._toggleFullscreen();
    return true;
  }
  /**
   * Get our seekbar responder going
   */
  initSeekPanResponder() {
    this.player.seekPanResponder = PanResponder.create({

      // Ask to be the responder.
      onStartShouldSetPanResponder: (evt, gestureState) => true,
      onMoveShouldSetPanResponder: (evt, gestureState) => true,

      /**
       * When we start the pan tell the machine that we're
       * seeking. This stops it from updating the seekbar
       * position in the onProgress listener.
       */
      onPanResponderGrant: (evt, gestureState) => {
        const state = this.state;
        this.clearControlTimeout();
        state.seeking = true;
        this.setState(state);
      },

      /**
       * When panning, update the seekbar position, duh.
       */
      onPanResponderMove: (evt, gestureState) => {
        const position = this.state.seekerOffset + gestureState.dx;
        this.setSeekerPosition(position);
      },

      /**
       * On release we update the time and seek to it in the video.
       * If you seek to the end of the video we fire the
       * onEnd callback
       */
      onPanResponderRelease: (evt, gestureState) => {
        const time = this.calculateTimeFromSeekerPosition();
        const state = this.state;
        if (time >= state.duration && !state.loading) {
          state.paused = true;
          this.events.onEnd();
        } else {
          let currentTime = this.seekTo(time);
          state.currentTime = currentTime
          this.setControlTimeout();
          state.seeking = false;
        }
        this.setState(state);

        if (this.opts.mmtTracker) {
          this.opts.mmtTracker('video_player_on_scrubbed');
        }
      }
    });
  }

  /**
   * Initialize the volume pan responder.
   */
  initVolumePanResponder() {
    this.player.volumePanResponder = PanResponder.create({
      onStartShouldSetPanResponder: (evt, gestureState) => true,
      onMoveShouldSetPanResponder: (evt, gestureState) => true,
      onPanResponderGrant: (evt, gestureState) => {
        this.clearControlTimeout();
      },

      /**
       * Update the volume as we change the position.
       * If we go to 0 then turn on the mute prop
       * to avoid that weird static-y sound.
       */
      onPanResponderMove: (evt, gestureState) => {
        const state = this.state;
        const position = this.state.volumeOffset + gestureState.dx;

        this.setVolumePosition(position);
        state.volume = this.calculateVolumeFromVolumePosition();

        if (state.volume <= 0) {
          state.muted = true;
        } else {
          state.muted = false;
        }

        this.setState(state);
      },

      /**
       * Update the offset...
       */
      onPanResponderRelease: (evt, gestureState) => {
        const state = this.state;
        state.volumeOffset = state.volumePosition;
        this.setControlTimeout();
        this.setState(state);
      }
    });
  }


  /**
   | -------------------------------------------------------
   | Rendering
   | -------------------------------------------------------
   |
   | This section contains all of our render methods.
   | In addition to the typical React render func
   | we also have all the render methods for
   | the controls.
   |
   */

  /**
   * Standard render control function that handles
   * everything except the sliders. Adds a
   * consistent <TouchableHighlight>
   * wrapper and styling.
   */
  renderControl(children, callback, style = {}) {
    return (
      <TouchableHighlight
        underlayColor="transparent"
        activeOpacity={0.3}
        onPress={() => {
          this.resetControlTimeout();
          callback();
        }}
        style={[
          styles.controls.control,
          style
        ]}
      >
        { children }
      </TouchableHighlight>
    );
  }

  /**
   * Renders an empty control, used to disable a control without breaking the view layout.
   */
  renderNullControl() {
    return (
      <View style={[styles.controls.control]} />
    );
  }

  /**
   * Groups the top bar controls together in an animated
   * view and spaces them out.
   */
  renderTopControls() {
    const backControl = this.props.disableBack ? this.renderNullControl() : this.renderBack();
    const fullscreenControl = this.props.disableFullscreen ? this.renderNullControl() : this.renderFullscreen();

    return (
      <Animated.View style={[
        styles.controls.top,
        {
          opacity: this.animations.topControl.opacity,
          marginTop: this.animations.topControl.marginTop
        }
      ]}
      >
        <ImageBackground
          source={require('@mmt/legacy-assets/src/img/top-vignette.webp')}
          style={[styles.controls.column]}
          imageStyle={[styles.controls.vignette]}
        >
          <View style={styles.controls.topControlGroup}>
            { backControl }
            <View style={styles.controls.pullRight}>
              { fullscreenControl }
            </View>
          </View>
        </ImageBackground>
      </Animated.View>
    );
  }

  /**
   * Back button control
   */
  renderBack() {
    return this.renderControl(
      <Image
        source={require('@mmt/legacy-assets/src/img/back.webp')}
        style={styles.controls.back}
      />,
      this.events.onBack,
      styles.controls.back
    );
  }

  /**
   * Render the volume slider and attach the pan handlers
   */
  renderVolume() {
    const volumeIcon = this.state.muted ? require('@mmt/legacy-assets/src/img/volume_off.webp') : require('@mmt/legacy-assets/src/img/volume_on.webp');

    return (
      <View>
        <TouchableHighlight
          underlayColor="transparent"
          activeOpacity={0.3}
          onPress={() => this.toggleAudio()}
          style={styles.volume.volumeBtn}
        >
          <Image style={styles.volume.icon} source={volumeIcon} />
        </TouchableHighlight>
      </View>
    );
  }

  /**
   * Render fullscreen toggle and set icon based on the fullscreen state.
   */
  renderFullscreen() {
    const source = this.state.isFullscreen === true ? require('@mmt/legacy-assets/src/img/shrink.webp') : require('@mmt/legacy-assets/src/img/expand.webp');
    const style = {...styles.controls.fullscreen, width: 24, marginHorizontal: 8};
    return this.renderControl(
      <Image source={source} />,
      this.methods.toggleFullscreen,
      style
    );
  }

  /**
   * Render bottom control group and wrap it in a holder
   */
  renderBottomControls() {
    const {isFullscreen} = this.state;
    const timerControl = this.props.disableTimer ? this.renderNullControl() : this.renderTimer();
    const seekbarControl = this.props.disableSeekbar ? this.renderNullControl() : this.renderSeekbar();
    const playPauseControl = this.props.disablePlayPause ? this.renderNullControl() : this.renderPlayPause();
    const volumeControl = this.props.disableVolume ? this.renderNullControl() : this.renderVolume();
    const fullscreenControl = this.props.disableFullscreen ? this.renderNullControl() : this.renderFullscreen();
    const fullScreenBottomControlsStyle = isFullscreen ? styles.controls.bottomControlGroupFS : {};
    return (
      <Animated.View style={[
        styles.controls.bottom,
        {
          opacity: this.animations.bottomControl.opacity,
          marginBottom: this.animations.bottomControl.marginBottom
        }
      ]}
      >
        <ImageBackground
          source={this.props.renderGreenViewForBottomControls && require('@mmt/legacy-assets/src/img/bottom-vignette.webp')}
          style={[styles.controls.column]}
          imageStyle={[styles.controls.vignette]}
        >
          <View style={[
            styles.controls.row,
            styles.controls.bottomControlGroup,
            fullScreenBottomControlsStyle
          ]}
          >{ playPauseControl }
            { timerControl }
            { seekbarControl }
            { volumeControl }
            { fullscreenControl }
          </View>
        </ImageBackground>
      </Animated.View>
    );
  }

  /**
   * Render the seekbar and attach its handlers
   */
  renderSeekbar() {
    return (
      <View style={styles.seekbar.container}>
        <View
          style={styles.seekbar.track}
          onLayout={event => this.player.seekerWidth = event.nativeEvent.layout.width}
        >
          <View style={[
            styles.seekbar.fill,
            {
              width: this.state.seekerFillWidth,
              backgroundColor: this.props.seekColor || '#FFF'
            }
          ]}
          />
        </View>
        <View
          style={[
            styles.seekbar.handle,
            {left: this.state.seekerPosition}
          ]}
          {...this.player.seekPanResponder.panHandlers}
        >
          <View style={[
            styles.seekbar.circle,
            {backgroundColor: this.props.seekColor || '#FFF'}]}
          />
        </View>
      </View>
    );
  }

  /**
   * Render the play/pause button and show the respective icon
   */
  renderPlayPause() {
    const source = this.state.paused === true ? require('@mmt/legacy-assets/src/img/play.webp') : require('@mmt/legacy-assets/src/img/pause.webp');
    return this.renderControl(
      <Image source={source} style={{width: 20, height: 20}} />,
      this.methods.togglePlayPause,
      styles.controls.playPause
    );
  }

  /**
   * Render our title...if supplied.
   */
  renderTitle() {
    if (this.opts.title) {
      return (
        <View style={[
          styles.controls.control,
          styles.controls.title
        ]}
        >
          <Text
            style={[
            styles.controls.text,
            styles.controls.titleText,
            styles.controls.webEllipsisText
          ]}
            numberOfLines={1}
          >
            { this.opts.title || '' }
          </Text>
        </View>
      );
    }

    return null;
  }

  /**
   * Show our timer.
   */
  renderTimer() {
    return this.renderControl(
      <Text style={styles.controls.timerText}>
        { this.calculateTime() }
      </Text>,
      this.methods.toggleTimer,
      styles.controls.timer
    );
  }

  /**
   * Show loading icon
   */
  renderLoader() {
    if (this.state.loading) {
      return (
        <View style={styles.loader.container}>
          <Animated.Image
            source={require('@mmt/legacy-assets/src/img/loader-icon.webp')}
            style={[
            styles.loader.icon,
            {
 transform: [
                {
 rotate: this.animations.loader.rotate.interpolate({
                    inputRange: [0, 360],
                    outputRange: ['0deg', '360deg']
                  })
}
              ]
}
          ]}
          />
        </View>
      );
    }
    return null;
  }

  renderError() {
    if (this.state.error) {
      return (
        <View style={styles.error.container}>
          <Image source={require('@mmt/legacy-assets/src/img/error-icon.webp')} style={styles.error.icon} />
          <Text style={styles.error.text}>
            Video unavailable
          </Text>
        </View>
      );
    }
    return null;
  }

  /**
   * Provide all of our options and render the whole component.
   */
  render() {
    const {isFullscreen} = this.state;
    const fullScreenStyle = isFullscreen ? {height: DEVICE_WINDOW.width + 20, width: DEVICE_WINDOW.height } : {};
    const containerStyle = USER_PLATFORM.ANDROID ? [this.styles.containerStyle, fullScreenStyle] : this.styles.containerStyle;

    return (
      <TouchableWithoutFeedback
        onPress={this.events.onScreenTouch}
        style={[styles.player.container, containerStyle]}
      >
        <View style={[styles.player.container, containerStyle]}>
          <Video
            {...this.props}
            ref={videoPlayer => this.player.ref = videoPlayer}

            resizeMode={this.state.resizeMode}
            volume={this.state.volume}
            paused={this.state.paused}
            muted={this.state.muted}
            rate={this.state.rate}
            poster={this.state.poster}
            onLoadStart={this.events.onLoadStart}
            onProgress={this.events.onProgress}
            onVideoBuffer={this.events.onBuffer}
            onError={this.events.onError}
            onLoad={this.events.onLoad}
            onEnd={this.events.onEnd}

            style={[styles.player.video, this.styles.videoStyle]}

            source={this.props.source}
            onFullscreenPlayerDidDismiss={this.events.onFullscreenPlayerDidDismiss}
          />
          { this.props.renderRestartBtn && this.renderRestartBtn() }
          { this.renderError() }
          { this.renderLoader() }
          { this.props.renderBottomControls && this.renderBottomControls() }
        </View>
      </TouchableWithoutFeedback>
    );
  }
}

VideoPlayer.propTypes = {
  resizeMode: PropTypes.string.isRequired,
  paused: PropTypes.bool.isRequired,
  muted: PropTypes.bool.isRequired,
  volume: PropTypes.number.isRequired,
  rate: PropTypes.number.isRequired,
  source: PropTypes.object.isRequired,
  disablePlayPause: PropTypes.bool.isRequired,
  disableFullscreen: PropTypes.bool.isRequired,
  disableBack: PropTypes.bool.isRequired,
  showOnStart: PropTypes.bool.isRequired,
  playWhenInactive: PropTypes.bool.isRequired,
  playInBackground: PropTypes.bool.isRequired,
  repeat: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  renderRestartBtn: PropTypes.bool.isRequired,
  renderBottomControls: PropTypes.bool.isRequired,
  onVideoPress: PropTypes.func.isRequired,
  renderGreenViewForBottomControls: PropTypes.bool.isRequired
};
