import {StyleSheet, Platform} from 'react-native';

/**
 * This object houses our styles. There's player
 * specific styles and control specific ones.
 * And then there's volume/seeker styles.
 */
const styles = {
  player: StyleSheet.create({
    container: {
      backgroundColor: '#000',
      flex: 1,
      height: 220,
      alignSelf: 'stretch',
      justifyContent: 'space-between'
    },
    video: {
      overflow: 'hidden',
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    }
  }),
  error: StyleSheet.create({
    container: {
      backgroundColor: 'rgba( 0, 0, 0, 0.5 )',
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      justifyContent: 'center',
      alignItems: 'center'
    },
    icon: {
      marginBottom: 16
    },
    text: {
      backgroundColor: 'transparent',
      color: '#f27474'
    }
  }),
  loader: StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      alignItems: 'center',
      justifyContent: 'center'
    }
  }),
  restart: StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1
    }
  }),
  controls: StyleSheet.create({
    row: {
      flexDirection: 'row'
    },
    column: {
      flexDirection: 'column',
      alignItems: 'center',
      height: null,
      width: '100%'
    },
    vignette: {
      resizeMode: 'stretch'
    },
    control: {
      padding: 0
    },
    text: {
      backgroundColor: 'transparent',
      color: '#FFF',
      fontSize: 14,
      textAlign: 'center'
    },
    pullRight: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center'
    },
    top: {
      flex: 1,
      alignItems: 'stretch',
      justifyContent: 'flex-start'
    },
    bottom: {
      alignItems: 'stretch',
      flex: 2,
      justifyContent: 'flex-end'
    },
    topControlGroup: {
      alignSelf: 'stretch',
      alignItems: 'center',
      justifyContent: 'space-between',
      flexDirection: 'row',
      width: null,
      margin: 12,
      marginBottom: 18
    },
    
    volume: {
      flexDirection: 'row'
    },
    fullscreen: {
      flexDirection: 'row',
      width: 30,
      alignItems: 'center',
      justifyContent: 'center'
    },
    playPause: {
      width: 24,
      alignItems: 'center',
      justifyContent: 'center'
    },
    title: {
      alignItems: 'center',
      flex: 0.6,
      flexDirection: 'column',
      padding: 0
    },
    titleText: {
      textAlign: 'center'
    },
    timer: {
      width: 50,
      alignItems: 'center',
      justifyContent: 'center'
    },
    timerText: {
      backgroundColor: 'transparent',
      color: '#FFF',
      fontSize: 11,
      textAlign: 'left'
    },
    bottomControlGroup: {
      height: 30,
      marginBottom: 10,
      alignItems: 'center',
      marginHorizontal: 30
    },
    bottomControlGroupFS: {
      marginBottom: 20
    },  
    webEllipsisText: {
      ...Platform.select({
        web: {
          overflow: 'hidden',
          display: 'box'
          // lineClamp: 3,
          // boxOrient: 'vertical'
        }
      })
    }
  }),
  volume: StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'flex-start',
      flexDirection: 'row',
      height: 1,
      marginLeft: 20,
      marginRight: 20,
      width: 150
    },
    track: {
      backgroundColor: '#333',
      height: 1,
      marginLeft: 7
    },
    volumeBtn: {
      width: 30,
      alignItems: 'center',
      justifyContent: 'center'
    },
    icon: {
      width: 16,
      height: 16,
      marginRight: 20,
      paddingBottom: 15,
      marginLeft: 20
    },
    fill: {
      backgroundColor: '#FFF',
      height: 1
    },
    handle: {
      position: 'absolute',
      marginTop: -24,
      marginLeft: -24,
      padding: 16
    }
  }),
  seekbar: StyleSheet.create({
    container: {
      alignSelf: 'center',
      height: 28,
      width: '60%',
      marginRight: 10
    },
    track: {
      backgroundColor: '#5b6076',
      height: 2,
      position: 'relative',
      top: 14,
      width: '100%'
    },
    fill: {
      backgroundColor: '#FFF',
      height: 2,
      width: '100%'
    },
    handle: {
      position: 'absolute',
      marginLeft: -7,
      height: 28,
      width: 28
    },
    circle: {
      borderRadius: 12,
      position: 'relative',
      top: 8,
      left: 8,
      height: 14,
      width: 14
    }
  })
};
export default styles;
