import { colors, fonts } from '../../../../../Styles/globalStyles';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    backgroundColor: colors.cabsGreyBg,
    top: 0,
    left: 0,
    zIndex: 30,
  },
  bgImageStyle: {
    height: 200,
  },
  tripMoneyImageView: {
    alignItems: 'center',
  },
  tripMoneyStyle: {
    height: 22,
    width: 95,
  },
  payLaterImageView: {
    alignItems: 'center',
    marginTop: 17,
  },
  payLaterStyle: {
    height: 46,
    width: 44,
  },
  closeButtonView: {
    alignSelf: 'flex-end',
    marginRight: 16,
    marginTop: 16,
  },
  closeIcon: {
    height: 24,
    width: 24,
  },
  headerTextView: {
    alignItems: 'center',
    marginTop: 10,
  },
  headerTitle: {
    fontFamily: fonts.black,
    color: colors.white,
    fontSize: 16,
    lineHeight: 20,
    textAlign: 'center',
  },
  headerSubTitle: {
    fontFamily: fonts.regular,
    color: colors.white,
    fontSize: 12,
    lineHeight: 14,
    textAlign: 'center',
  },
  bodyView: {
    marginTop: 24,
    marginHorizontal: 16,
  },
  bodyTitle: {
    color: colors.azureBlue3,
    fontFamily: fonts.bold,
    fontSize: 14,
    lineHeight: 16,
  },
  bodySubtitle: {
    color: colors.azureBlue3,
    fontFamily: fonts.regular,
    fontSize: 14,
    lineHeight: 14,
    paddingTop: 4,
  },
  partnerItemMainView: {
    marginVertical: 5,
    backgroundColor: colors.white,
    paddingVertical: 12,
    borderRadius: 8,
  },
  partnerLogo: {
    height: 44,
    width: 44
  },
  partnerName: {
    fontFamily: fonts.bold,
    color: colors.black,
    fontSize: 16,
    lineHeight: 20,
  },
  interestRate: {
    fontFamily: fonts.bold,
    color: colors.black,
    fontSize: 12,
    lineHeight: 12,
    paddingTop: 5,
  },
  partnerItemDashedLine: {
    borderTopWidth: 1,
    borderTopColor: colors.lightGreen12,
    marginTop: 12,
  },
  partnerItemDetailsView: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  partnerItemText: {
    fontFamily: fonts.bold,
    color: colors.azureBlue2,
    fontSize: 12,
    lineHeight: 20
  },
  partnerItemSubtext: {
    fontFamily: fonts.regular,
    color: colors.azureBlue2,
    fontSize: 10,
    lineHeight: 14
  }
});

export default styles;
