import React from 'react';
import {
  Image,
  Text,
  View,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Modal,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { RepaymentDetailsDataProps } from '../../BookNowPayLater';
import styles from './RepaymentDetailsCss';
import { colors, fonts } from '../../../../../Styles/globalStyles';
import closeIcon from '@mmt/legacy-assets/src/close-white.webp';
import useStatusBarHeight from '../../../../utils/StatusBarHeight';
import _label from '../BnplLabels';

interface RepaymentDetailsProps {
  repaymentDetails: RepaymentDetailsDataProps;
  trackEvent: (payload: string) => void;
  trackingData: any;
  onClose: () => {};
}

const RepaymentDetails = (props: RepaymentDetailsProps) => {
  const { repaymentDetails, trackEvent, trackingData, onClose } = props;
  const { header = null, body = null } = repaymentDetails;
  const screenWidth = Dimensions.get('screen').width;
  const statusBarHeight = useStatusBarHeight();
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={Boolean(repaymentDetails)}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View>
          <ImageBackground
            source={{ uri: header.backgroundImage }}
            style={[styles.bgImageStyle, { width: screenWidth }]}
          >
            <LinearGradient
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 0.0 }}
              colors={['rgba(0,0,0,1)', 'rgba(0,0,0,0)']}
              style={[styles.bgImageStyle, { opacity: 0.8 }]}
            >
              <View style={[styles.closeButtonView, { marginTop: statusBarHeight }]}>
                <TouchableOpacity
                  onPress={() => {
                    trackEvent(trackingData.REPAYMENT_DETAILS_CLOSED);
                    onClose();
                  }}
                >
                  <Image source={closeIcon} style={styles.closeIcon} />
                </TouchableOpacity>
              </View>
              <View style={styles.tripMoneyImageView}>
                <Image source={{ uri: header.tripMoneyImage }} style={styles.tripMoneyStyle} />
              </View>
              <View style={styles.payLaterImageView}>
                <Image source={{ uri: header.payLaterImage }} style={styles.payLaterStyle} />
              </View>
              <View style={styles.headerTextView}>
                <Text style={styles.headerTitle}>{header.title}</Text>
                <Text style={styles.headerSubTitle}>{header.subTitle}</Text>
              </View>
            </LinearGradient>
          </ImageBackground>
        </View>
        <ScrollView>
          <View style={styles.bodyView}>
            <Text style={styles.bodyTitle}>{body.title}</Text>
            <Text style={styles.bodySubtitle}>{body.subTitle}</Text>
            {Boolean(body?.partnersList?.length) && (
              <View style={{ marginTop: 10 }}>
                {body.partnersList.map((item, index) => (
                  <View style={styles.partnerItemMainView}>
                    <View style={{ paddingLeft: 16, flexDirection: 'row' }}>
                      <Image source={{ uri: item.partnerLogo }} style={styles.partnerLogo} />
                      <View style={{ paddingLeft: 10 }}>
                        <Text style={styles.partnerName}>{item.partnerName}</Text>
                        <Text style={styles.interestRate}>
                          {item.interestRate}{' '}
                          <Text style={{ fontSize: 10, fontFamily: fonts.regular }}>{_label("bnpl_interest")}</Text>
                        </Text>
                      </View>
                    </View>
                    <View style={styles.partnerItemDashedLine} />
                    <View style={styles.partnerItemDetailsView}>
                      <View>
                        <Text style={styles.partnerItemText}>{item.daysText}</Text>
                        <Text style={styles.partnerItemSubtext}>{item.daysSubText}</Text>
                      </View>
                      <View>
                        <Text style={styles.partnerItemText}>{item.lateFeeText}</Text>
                        <Text style={styles.partnerItemSubtext}>{item.lateFeeSubText}</Text>
                      </View>
                      <View>
                        <Text style={styles.partnerItemText}>{item.repayModeText}</Text>
                        <Text style={styles.partnerItemSubtext}>{item.repayModeSubText}</Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

export default RepaymentDetails;
