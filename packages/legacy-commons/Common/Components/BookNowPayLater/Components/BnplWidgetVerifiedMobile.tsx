import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import BnplStyles from '../styles';
import { colors, fonts } from '../../../../Styles/globalStyles';
import _label from './BnplLabels';

const logo = require('@mmt/legacy-assets/src/bnpl.webp');
const tripmoney = require('@mmt/legacy-assets/src/tripmoney.webp');

interface BnplWidgetVerifiedMobileProps {
  lobLabel: string;
  mobileWithCountryCode: any;
  checkEligibility: () => void;
}

const BnplWidgetVerifiedMobile = (props: BnplWidgetVerifiedMobileProps) => {
  const { lobLabel, mobileWithCountryCode, checkEligibility } = props;
  return (
    <View style={BnplStyles.wrapper}>
      <View style={[BnplStyles.flexRow, {alignItems: 'center'}]}>
        <Image source={logo} style={[BnplStyles.bnplLogo, { marginRight: 12 }]} />
        <View>
          <Text style={[BnplStyles.title,{ paddingTop: 5 }]}>{_label("bnpl_title")}</Text>
          <Text style={BnplStyles.subTitle}>{_label("bnpl_subtitle",undefined,{lobLabel:lobLabel})}</Text>
        </View>
      </View>
      <View style={styles.body}>
        <View style={styles.bnplMobileWrapper}>
          <Text style={styles.bnplMobile}>{mobileWithCountryCode}</Text>
        </View>
        <TouchableOpacity onPress={checkEligibility} style={styles.bnplButton}>
          <Text style={styles.bnplButtonText}>{_label("bnpl_check_eligibiliy")}</Text>
        </TouchableOpacity>
      </View>
      <Image source={tripmoney} style={BnplStyles.tripmoneyLarge} />
    </View>
  );
};

const styles = StyleSheet.create({
  body: {
    ...BnplStyles.body,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  bnplMobileWrapper: {
    borderBottomColor: colors.lightGreen12,
    borderBottomWidth: 1,
    paddingRight: 12,
    paddingBottom: 9,
  },
  bnplMobile: {
    paddingTop: 20,
    color: colors.darkGrey2,
    fontSize: 14,
    lineHeight: 14,
  },
  bnplButton: {
    marginTop: 16,
    backgroundColor: colors.azureBlue4,
    borderRadius: 8,
  },
  bnplButtonText: {
    padding: 8,
    color: colors.white,
    fontWeight: '700',
    letterSpacing: 1,
  },
});

export default BnplWidgetVerifiedMobile;
