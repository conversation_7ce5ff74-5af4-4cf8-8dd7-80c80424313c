import React, { useEffect, useState } from 'react';
import { View, Image, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LOADING_STATE } from '../BnplConstants';
import BnplStyles from '../styles';
import { colors, fonts } from '../../../../Styles/globalStyles';
import _label from './BnplLabels';

const logo = require('@mmt/legacy-assets/src/bnpl.webp');
const tripmoney = require('@mmt/legacy-assets/src/tripmoney.webp');

const getErrorMessage = (loadingState: string) => {
  if (loadingState === LOADING_STATE.NETWORK_ERROR) {
    return _label("bnpl_network_error")
  } else {
    return _label("bnpl_error")
  }
};

const getTitle = (loadingState: string) => {
  if (loadingState === LOADING_STATE.NETWORK_ERROR) {
   return _label("bnpl_timeout_message")
  } else {
    return _label("bnpl_sorry_message")
  }
};

interface VerificationFailedProps {
  lobLabel: string;
  dismissWidget: any;
  loadingState: string;
  trackEvent: any;
  trackingData: any;
  hideWidget?: () => void;
}

const VerificationFailed = (props: VerificationFailedProps) => {
  const {
    lobLabel,
    dismissWidget,
    loadingState,
    trackEvent,
    trackingData,
    hideWidget = () => {},
  } = props;
  let timer = null;
  if (loadingState === LOADING_STATE.VERIFICATION_FAILED) {
    timer = setTimeout(() => {
      hideWidget();
      dismissWidget();
    }, 20000);
  }

  useEffect(() => {
    if (loadingState === LOADING_STATE.NETWORK_ERROR) {
      trackEvent(trackingData.BNPL_TIMEOUT);
      trackEvent(trackingData.BNPL_TIMEOUT_CONVERSION);
    } else {
      trackEvent(trackingData.BNPL_FAILURE);
      trackEvent(trackingData.BNPL_FAILURE_CONVERSION);
    }
  }, []);

  useEffect(() => {
    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <View style={[BnplStyles.wrapper, BnplStyles.flexColumn]}>
      <View style={[BnplStyles.flexRow, { alignItems: 'center', marginBottom: 12 }]}>
        <Image source={logo} style={[BnplStyles.bnplLogo, { marginRight: 12 }]} />
        <Text style={BnplStyles.title}>{getTitle(loadingState)}</Text>
        {Boolean(loadingState === LOADING_STATE.NETWORK_ERROR) && (
          <TouchableOpacity
            onPress={() => {
              if (loadingState === LOADING_STATE.NETWORK_ERROR) {
                trackEvent(trackingData.BNPL_TIMEOUT_CLOSE);
              } else {
                trackEvent(trackingData.BNPL_FAILED_CLOSE);
              }
              dismissWidget();
            }}
            style={styles.closeButton}
          >
            <Text style={styles.closeIcon}>X</Text>
          </TouchableOpacity>
        )}
      </View>
      <Text style={styles.failedMessage}>{getErrorMessage(loadingState)}</Text>
      <Image source={tripmoney} style={[BnplStyles.tripmoneyLarge, { marginTop: 13 }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  failedMessage: {
    color: colors.white,
    opacity: 0.7,
  },
  closeButton: {
    flex: 1,
    alignItems: 'flex-end',
  },
  closeIcon: {
    color: colors.white,
    fontWeight: '700',
  },
});

export default VerificationFailed;
