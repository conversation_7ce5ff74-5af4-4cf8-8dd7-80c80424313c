import React, { useEffect, useState } from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import RupeeText from '../../RupeeText';
import BnplStyles from '../styles';
import { colors, fonts } from '../../../../Styles/globalStyles';
import { RepaymentDetailsDataProps } from '../BookNowPayLater';
import RepaymentDetails from './RepaymentDetails/RepaymentDetails';
import _label from './BnplLabels';
const tripmoney = require('@mmt/legacy-assets/src/tripmoney_dark.webp');

interface VerificationSuccessProps {
  bnplEligibleAmount: number;
  repaymentDetails: RepaymentDetailsDataProps;
  trackEvent: any;
  trackingData: any;
}

const VerificationSuccess = (props: VerificationSuccessProps) => {
  const { bnplEligibleAmount, repaymentDetails = null, trackEvent, trackingData } = props;
  useEffect(() => {
    trackEvent(trackingData.BNPL_SUCCESS);
    trackEvent(trackingData.BNPL_SUCCESS_CONVERSION);
  }, []);

  const [showRepaymentDetailsPage, setToShowRepaymentDetailsPage] = useState(false);

  return (
    <View>
      <View style={[BnplStyles.wrapper, { backgroundColor: colors.green4 }]}>
        <Text
          style={[
            BnplStyles.title,
            { color: colors.azureBlue3, marginBottom: 4, fontWeight: '600', paddingTop: 5  },
          ]}
        >
          {_label("bnpl_congratulations")}
        </Text>
        <Text
          style={[BnplStyles.title, { color: colors.azureBlue3, fontWeight: '400', opacity: 0.69, paddingTop: 5 }]}
        >
          {_label("bnpl_eligible")}
        </Text>
        <View style={[BnplStyles.body, { paddingVertical: 12 }]}>
          <View style={styles.horizontal}>
            <RupeeText style={styles.amount}>{_label("bnpl_eligible_amount",undefined,{bnplEligibleAmount:bnplEligibleAmount})}</RupeeText>
            <Text>{_label("bnpl_approved")}</Text>
          </View>
          <View style={styles.horizontal}>
            <Text style={styles.payLater}>
            {_label("bnpl_select")} <Text style={[styles.payLater, styles.bold]}>{`'${_label("bnpl_pay_later")}'`}</Text>
              <Text styles={styles.payLater}> {_label("bnpl_payments_page")}</Text>
            </Text>
          </View>
          <View
            style={[styles.horizontal, { justifyContent: 'space-between', alignItems: 'center' }]}
          >
            <Image source={tripmoney} style={[BnplStyles.tripmoneySmall, { marginTop: 20 }]} />
            {Boolean(repaymentDetails?.body?.partnersList?.length) && (
              <TouchableOpacity
                onPress={() => {
                  setToShowRepaymentDetailsPage(true);
                  trackEvent(trackingData.REPAYMENT_DETAILS_CLICKED);
                }}
              >
                <View style={styles.repaymentBtnView}>
                  <Text style={[styles.repaymentBtnText, { paddingTop: 2 }]}>{_label("bnpl_repayments_details")}</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
        <View style={styles.horizontal}>
          <Text style={[styles.disclaimer]}>
          {_label("bnpl_disclaimer_title")}{' '}
            <Text style={[styles.disclaimer, styles.normalText]}>
            {_label("bnpl_disclaimer_text")}
            </Text>
          </Text>
        </View>
      </View>
      {showRepaymentDetailsPage && (
        <RepaymentDetails
          repaymentDetails={repaymentDetails}
          trackEvent={trackEvent}
          trackingData={trackingData}
          onClose={() => {
            setToShowRepaymentDetailsPage(false);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  horizontal: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 5,
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
    lineHeight: 20,
    color: colors.azureBlue2,
  },
  payLater: {
    fontSize: 12,
    lineHeight: 16,
    color: colors.azureBlue2,
  },
  bold: {
    fontWeight: '700',
  },
  disclaimer: {
    color: colors.azureBlue2,
    fontWeight: '700',
    fontSize: 10,
    lineHeight: 14,
  },
  normalText: {
    fontWeight: '300',
  },
  repaymentBtnView: {
    padding: 8,
    backgroundColor: colors.azureBlue4,
    borderRadius: 4,
    marginTop: 20,
  },
  repaymentBtnText: {
    color: colors.white,
    fontSize: 10,
    lineHeight: 12,
    fontFamily: fonts.bold,
  },
});

export default VerificationSuccess;
