import { useState, useEffect } from 'react';
import { NativeModules } from 'react-native';
import { getCurrentLangKey } from '@mmt/legacy-commons/Common/Components/Vernacular';
let currentLob = "";
let initLang = "eng";

export const setCurrentLob = (lob) => {
    currentLob = lob;
}
export const setBnplInitLang = (lang) => {
    initLang = lang;
}
const fallbackLang = 'eng';
const bnplLanguageLabels =
{
    hin: {
        bnpl_title: "अभी बुक करें बाद में भुगतान करें",
        bnpl_subtitle: "आज ही अपना {{lobLabel}} बुक करें और बाद में भुगतान करें!",
        bnpl_mobile: "इस सुविधा का उपयोग करने के लिए आपको अपने खाते से लॉगिन करना होगा",
        bnpl_button_text: "लॉग इन करें",
        add_bnpl_mobile: "इस सुविधा का उपयोग करने के लिए अपना मोबाइल नंबर जोड़ें",
        add_bnpl_mobile_number: "मोबाइल नंबर डालें",
        bnpl_check_eligibiliy: "पात्रता की जांच करें",
        bnpl_checking_eligibiliy: "पात्रता की जांच",
        bnpl_network_error: "हम इस समय आपके नंबर के लिए पात्रता की जांच करने में असमर्थ हैं। कृपया भुगतान के अन्य उपलब्ध तरीकों का उपयोग करके अपनी बुकिंग पूरी करें।",
        bnpl_error: "हम इस समय आपके नंबर के लिए स्वीकृति प्राप्त नहीं कर सके। कृपया भुगतान के अन्य उपलब्ध तरीकों का उपयोग करके अपनी बुकिंग पूरी करें।\nऔर आपके बुक नाउ पे लेटर अनुरोध को पूरा करने में हमारी मदद करने के लिए, कृपया हमारे द्वारा आपको एसएमएस/व्हाट्सएप पर भेजे गए लिंक के माध्यम से आवश्यक विवरण जमा करें।",
        bnpl_timeout_message: "समय समाप्त!",
        bnpl_sorry_message: 'माफ़ करना!',
        bnpl_congratulations: "बधाई हो!",
        bnpl_eligible: "आप बुक नाउ पे लेटर के पात्र हैं",
        bnpl_eligible_amount: "₹ {{bnplEligibleAmount}}",
        bnpl_approved: "स्वीकृत",
        bnpl_select: "भुगतान पृष्ठ पर",
        bnpl_pay_later: "बाद में",
        bnpl_payments_page: "भुगतान करें का चयन करें",
        bnpl_repayments_details: "चुकौती के लिए विवरण",
        bnpl_disclaimer_title: "कृपया ध्यान दें:",
        bnpl_disclaimer_text: "आपकी MakeMyTrip बुकिंग की राशि आपकी स्वीकृत सीमा के भीतर ही रहनी चाहिए। यह स्वीकृत सीमा एक सामान्य राशि है, जो सभी प्लेटफॉर्म पर आपके उपयोग के लिए उपलब्ध है।",
        bnpl_interest: "रुचि"
    },
    eng: {
        bnpl_title: "Book Now Pay Later",
        bnpl_subtitle: "Book your {{lobLabel}} today and pay back later!",
        bnpl_mobile: "You must log in to your account to use this feature",
        bnpl_button_text: "LOG IN",
        add_bnpl_mobile: "Add your mobile number to use this feature",
        add_bnpl_mobile_number: "ADD MOBILE NUMBER",
        bnpl_check_eligibiliy: "CHECK ELIGIBILITY",
        bnpl_checking_eligibiliy: "CHECKING ELIGIBILITY",
        bnpl_network_error: "We are unable to check the eligibility for your number, at the moment. Please complete your booking using other available modes of payment.",
        bnpl_error: "We could not source an approval for your number, at the moment. Please complete your booking using other available modes of payment.\nAnd to help us complete your Book Now Pay Later request, please submit the required details through the links we’ve sent to you on SMS/WhatsApp.",
        bnpl_timeout_message: "Timeout!",
        bnpl_sorry_message: 'Sorry!',
        bnpl_congratulations: "Congratulations!",
        bnpl_eligible: "You are eligible for Book Now Pay Later",
        bnpl_eligible_amount: "₹ {{bnplEligibleAmount}}",
        bnpl_approved: "approved",
        bnpl_select: "Select",
        bnpl_pay_later: "Pay Later",
        bnpl_payments_page: "on the payments page",
        bnpl_repayments_details: "REPAYMENT DETAILS",
        bnpl_disclaimer_title: "Please note:",
        bnpl_disclaimer_text: "The amount for your MakeMyTrip booking must remain within your approved limit. This approved limit is a common amount, available for your use across platforms.",
        bnpl_interest: "Interest"
    },
    tam: {
        "bnpl_title": "இப்போதே முன்பதிவு செய்து பின்னர் பணம் செலுத்துங்கள்",
        "bnpl_subtitle": "இன்றே உங்கள் {{lobLabel}} ஐ முன்பதிவு செய்து பின்னர் திருப்பிச் செலுத்துங்கள்!",
        "bnpl_mobile": "இந்த அம்சத்தைப் பயன்படுத்த, உங்கள் கணக்கில் உள்நுழைய வேண்டும்",
        "bnpl_button_text": "உள்நுழைய",
        "add_bnpl_mobile": "இந்த அம்சத்தைப் பயன்படுத்த உங்கள் மொபைல் எண்ணைச் சேர்க்கவும்",
        "add_bnpl_mobile_number": "மொபைல் எண்ணைச் சேர்",
        "bnpl_check_eligibiliy": "தகுதியை சரிபார்க்கவும்",
        "bnpl_checking_eligibiliy": "தகுதியை சரிபார்த்தல்",
        "bnpl_network_error": "உங்கள் எண்ணுக்கான தகுதியை எங்களால் சரிபார்க்க முடியவில்லை",
        "bnpl_error": "உங்கள் எண்ணுக்கு எங்களால் ஒப்புதல் பெற முடியவில்லை",
        "bnpl_timeout_message": "நேரம் முடிந்தது!",
        "bnpl_sorry_message": "மன்னிக்கவும்!",
        "bnpl_congratulations": "வாழ்த்துக்கள்!",
        "bnpl_eligible": "இப்போதே முன்பதிவு செய்ய நீங்கள் தகுதி பெற்றுள்ளீர்கள் பின்னர் பணம் செலுத்துங்கள்",
        "bnpl_eligible_amount": "₹ {{bnplEligibleAmount}}",
        "bnpl_approved": "அங்கீகரிக்கப்பட்ட",
        "bnpl_select": "தேர்ந்தெடு",
        "bnpl_pay_later": "பின்னர் செலுத்தவும்",
        "bnpl_payments_page": "பணம் செலுத்தும் பக்கத்தில்",
        "bnpl_repayments_details": "திருப்பிச் செலுத்தும் விவரங்கள்",
        "bnpl_disclaimer_title": "தயவுசெய்து கவனிக்கவும்:",
        "bnpl_disclaimer_text": "உங்கள் MakeMyTrip முன்பதிவுக்கான தொகை நீங்கள் அனுமதிக்கப்பட்ட வரம்பிற்குள் இருக்க வேண்டும். இந்த அங்கீகரிக்கப்பட்ட வரம்பு பொதுவான தொகை",
        "bnpl_interest": "ஆர்வங்கள்",
    }
}

const transformers = {
    defaultLang: (str, key) => {
        return bnplLanguageLabels[fallbackLang][key];
    },
    parameterize: (str, params) => {
        return str.replace(/{{\w*}}/ig, (match) => {
            const key = match.replace(/[{}]/ig, '') || "";
            return params[key];
        });
    }
}

const getLobLanguage = () => {
    if (currentLob === "Rails") {
        return initLang;
    }
    return getCurrentLangKey() || fallbackLang;
}

const _label = (str, options, params) => {
    try {
        let label = bnplLanguageLabels[getLobLanguage()][str] ?? str;
        if (options) {
            const keys = Object.keys(options);
            keys.forEach(k => {
                label = options[k] ? transformers[k](label, str) : label;
            });
        }
        if (params) {
            label = transformers.parameterize(label, params);
        }
        return label.trim();
    } catch (e) {
        return str;
    }
}

export default _label;