import React from 'react';
import { View, Text, Image, StyleSheet, ActivityIndicator } from 'react-native';
import BnplStyles from '../styles';
import { colors, fonts } from '../../../../Styles/globalStyles';
import _label from './BnplLabels';

const logo = require('@mmt/legacy-assets/src/bnpl.webp');

const Loading = () => {
  return (
    <View style={[BnplStyles.wrapper, BnplStyles.flexColumn, BnplStyles.checkingStatus]}>
      <Image source={logo} style={BnplStyles.bnplLogo} />
      <Text style={BnplStyles.title}>{_label("bnpl_checking_eligibiliy")}</Text>
      <ActivityIndicator size="large" color={colors.white} />
    </View>
  );
};

export default Loading;
