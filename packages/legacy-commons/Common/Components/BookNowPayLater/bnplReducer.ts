import { LOADING_STATE, NETWORK_ERR } from './BnplConstants';
import * as constants from './BnplActions';
import _isEqual from 'lodash/isEqual';

const initialState = {
  loadingState: LOADING_STATE.NOT_STARTED,
  bnplEligibleAmount: 0,
  errorDetail: {
    status: false,
    errorType: '',
    errorMessage: '',
  },
  railsBnplAmount: 0,
  userData: null,
  repaymentDetails: null,
  showBnplWidget: true
};

const reducer = (state = initialState, action) => {
  const { type } = action;
  switch (type) {
    case constants.RESET_BNPL:
      if (Boolean(action?.payload) && !_isEqual(action?.payload,state.userData)) {
        return {
          ...initialState,
          railsBnplAmount: state.railsBnplAmount,
          userData: action?.payload,
        };
      } else {
        return {
          ...state,
          loadingState: LOADING_STATE.NOT_STARTED,
          bnplEligibleAmount: 0,
          errorDetail: {
            status: false,
            errorType: '',
            errorMessage: '',
          },
          railsBnplAmount: state.railsBnplAmount,
          repaymentDetails: null,
        };
      }
    case constants.FORCE_RESET_BNPL:
      return initialState;
    case constants.CHECK_ELIGIBILITY_START:
      return {
        ...state,
        loadingState: LOADING_STATE.IN_PROGRESS,
      };
    case constants.HIDE_WIDGET:
      return {
        ...state,
        showBnplWidget: false
      }
    case constants.CHECK_ELIGIBILITY_SUCCESS:
      return {
        ...state,
        loadingState: LOADING_STATE.SUCCESS,
        bnplEligibleAmount: action.payload.eligibleAmount,
        railsBnplAmount: action.payload.eligibleAmount,
        repaymentDetails: action.payload.data,
      };
    case constants.CHECK_ELIGIBILITY_FAILED:
      return {
        ...state,
        loadingState:
          action.payload.errorType === NETWORK_ERR.ERR_TYPE
            ? LOADING_STATE.NETWORK_ERROR
            : LOADING_STATE.VERIFICATION_FAILED,
        errorDetail: {
          ...action.payload,
        },
      };
    case constants.SET_USER_DATA:
      return {
        ...state,
        userData: action.data,
      };
    case constants.RESET_ERROR:
      return {
        ...state,
        loadingState: LOADING_STATE.NOT_STARTED,
        bnplEligibleAmount: 0,
        errorDetail: {
          status: false,
          errorType: '',
          errorMessage: '',
        },
        repaymentDetails: null,
      };
    default:
      return state;
  }
};

export default reducer;
