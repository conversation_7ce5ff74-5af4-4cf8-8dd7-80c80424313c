import { fetchWithTimeout } from '../../utils/NetworkUtils';
import { getCommonHeaders } from '../../../Helpers/genericHelper';
import { isCorporateUser } from '../../../Native/UserSession/UserSessionModule';
import { NETWORK_ERR, PAY_LATER_API, API_BODY_CONSTANTS } from './BnplConstants';

const getChannel = async () => {
  const isB2B = await isCorporateUser();
  return isB2B ? 'B2B' : 'B2C';
};

interface PostBodyProps {
  product: string;
  mobile: string;
  amount: number;
  memoize?: boolean;
}

const getPostBody = async (props: PostBodyProps) => {
  const { product, mobile, amount = 3000, memoize = false } = props;
  try {
    const channel = await getChannel();
    return {
      mobile,
      product,
      amount,
      channel,
      payMode: API_BODY_CONSTANTS.PAY_MODE,
      currency: API_BODY_CONSTANTS.CURRENCY,
      payLaterRequestType: API_BODY_CONSTANTS.REQ_TYPE,
      memoize: memoize,
    };
  } catch (e) {
    return {};
  }
};

interface PostServiceProps {
  page?: string;
  product: string;
  mobile: string;
  amount: number;
  memoize?: boolean;
}

export const postService = async (props: PostServiceProps) => {
  const { page = 'listing', product, mobile, amount, memoize = false } = props;
  try {
    const commonHeaders = await getCommonHeaders();
    const headers = {
      ...commonHeaders,
      'Content-Type': 'application/json',
      page,
      product,
    };
    const body = await getPostBody({
      product,
      mobile,
      amount,
      memoize,
    });
    let response = await fetchWithTimeout(PAY_LATER_API, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      timeout: 8000,
    });
    response = await response.json();
    const data = {
      status: response.status,
      eligibleAmount: response.eligibleAmount,
      data: {
        header: response.header,
        body: response.body
      }
    }
    return data;
  } catch (e) {
    return {
      status: false,
      errorType: NETWORK_ERR.ERR_TYPE,
      errorMessage: NETWORK_ERR.ERR_MSG,
    };
  }
};
