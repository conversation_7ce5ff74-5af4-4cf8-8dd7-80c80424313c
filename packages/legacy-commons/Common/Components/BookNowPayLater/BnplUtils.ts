import { Platform } from 'react-native';
import {
  performEditProfile,
  performLogin,
  getUserDetails,
  isUserLoggedIn,
} from '../../../Native/UserSession/UserSessionModule';
import GenericModule from '../../../Native/GenericModule';
import { BnplOmnitureSchema } from './BnplConstants';

const EDIT_PROFILE_ACTION = 'mmt.intent.action.EDIT_PROFILE';

export const getUserData = async () => {
  const userDetails = await getUserDetails();
  const { mobileVerified = false, mobile = {} } = userDetails;
  const { countryCode = '', mobileNumber = '' } = mobile;
  const logInStatus = await isUserLoggedIn();
  const data = {
    mobileNumber: mobileNumber,
    hasVerifiedMobile: mobileVerified,
    mobileWithCountryCode: `+${countryCode} ${mobileNumber}`,
    isUserLoggedin: logInStatus,
  };
  return data;
};

export const onEditProfilePressed = () => {
  if (Platform.OS === 'android') {
    GenericModule.openActivity(EDIT_PROFILE_ACTION);
  } else {
    performEditProfile();
  }
};

export const onLoginClick = async () => {
  await performLogin();
};

export const getProcessedOmnitureData = (lobData: any) => {
  const data: { [x: string]: any } = {};
  Object.keys(BnplOmnitureSchema).forEach((key) => {
    if (key in lobData) {
      data[key] = lobData[key];
    }
  });
  return data;
};

export const trackBnplEvents = ({ trackEvent, data = {} }: any) => {
  if (Boolean(data?.key)) {
    const { key, value } = data;
    const trackData = {};
    trackData[key] = value;
    trackEvent(trackData);
  }
};
