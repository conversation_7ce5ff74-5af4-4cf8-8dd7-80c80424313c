import React, { useEffect, useState } from 'react';
import { DeviceEventEmitter, EmitterSubscription, Platform } from 'react-native';

import BnplWidgetVerifiedMobile from './Components/BnplWidgetVerifiedMobile';
import BnplWidgetNonVerifiedMobile from './Components/BnplWidgetNonVerifiedMobile';
import BnplWidgetNonLoggedIn from './Components/BnplWidgetNonLoggedIn';
import Loading from './Components/Loading';
import VerificationFailed from './Components/VerificationFailed';
import VerificationSuccess from './Components/VerificationSuccess';

import {
  onEditProfilePressed,
  onLoginClick,
  getProcessedOmnitureData,
  trackBnplEvents,
} from './BnplUtils';
import {
  LOADING_STATE,
  LOGIN_EVENT,
  LOGIN_EVENT_RN,
  EDIT_PROFILE_EVENT,
  VERIFIED_MOBILE_EVENT,
  BnplOmnitureSchema,
} from './BnplConstants';
import { setCurrentLob } from './Components/BnplLabels';

let loginEventRNListener: EmitterSubscription;
let loginEventListener: EmitterSubscription;
let editProfileEventListener: EmitterSubscription;
let verifiedMobileEventListener: EmitterSubscription;

interface RepaymentDetailsHeaderProps {
  backgroundImage: string;
  tripMoneyImage: string;
  payLaterImage: string;
  title: string;
  subTitle: string;
}

interface BnplPartnerProps {
  partnerLogo: string;
  partnerName: string;
  interestRate: string;
  daysText: string;
  daysSubText: string;
  lateFeeText: string;
  lateFeeSubText: string;
  repayModeText: string;
  repayModeSubText: string;
}

interface RepaymentDetailsBodyProps {
  title: string;
  subTitle: string;
  partnersList: Array<BnplPartnerProps> | null;
}

export interface RepaymentDetailsDataProps {
  header?: RepaymentDetailsHeaderProps;
  body?: RepaymentDetailsBodyProps;
}

interface payloadProps {
  mobile: string;
  product: string;
  amount: number;
  memoize?: boolean;
}
interface BnplProps {
  lob: string;
  lobLabel: string;
  loadingState: string;
  checkEligibility: (payload: payloadProps) => {};
  amount: number;
  initialise: any;
  onCheckEligibilityClicked?: any;
  bnplEligibleAmount: number;
  userData: any;
  setUserData: any;
  trackEvent?: (payload: any) => void;
  repaymentDetails: RepaymentDetailsDataProps;
  omnitureData?: any;
  resetError: any;
  showBnplWidget?: boolean;
  hideWidget?: () => void;
  bnplCalledBefore?: boolean;
  setBnplCalledBefore?: (val: boolean) => void;
}

const BookNowPayLater = (props: BnplProps) => {
  const {
    lob,
    lobLabel,
    loadingState,
    checkEligibility,
    amount,
    initialise,
    onCheckEligibilityClicked,
    bnplEligibleAmount,
    userData,
    setUserData,
    trackEvent = () => {},
    repaymentDetails = {},
    omnitureData = BnplOmnitureSchema,
    resetError = () => {},
    showBnplWidget = true,
    hideWidget = () => {},
    bnplCalledBefore = false,
    setBnplCalledBefore
  } = props;

  useEffect(() => {
    setCurrentLob(lob);
  }, [lob])

  const [trackingData, setTrackingData] = useState(BnplOmnitureSchema);

  useEffect(() => {
    const data = getProcessedOmnitureData(omnitureData);
    bnplTracker(data.BNPL_SHOWN);
    setTrackingData(data);
    props.initialise();
  }, [lob]);

  useEffect(() => {
    if ( (!bnplCalledBefore) && Boolean(userData?.mobileNumber && userData?.hasVerifiedMobile)) {
      if (!bnplCalledBefore){
        setBnplCalledBefore && setBnplCalledBefore(true)
      }
      checkEligibility({
        mobile: userData?.mobileNumber,
        product: lob,
        amount,
        memoize: true,
      });
    }
  }, [userData]);

  useEffect(() => {
    return () => {
      if (loadingState === LOADING_STATE.VERIFICATION_FAILED) {
        hideWidget();
        dismissWidget();
      }
    };
  }, []);

  useEffect(() => {
    loginEventRNListener = DeviceEventEmitter?.addListener(LOGIN_EVENT_RN, onLoginStatusUpdatedRN);
    loginEventListener = DeviceEventEmitter?.addListener(LOGIN_EVENT, onLoginStatusUpdated);
    editProfileEventListener = DeviceEventEmitter?.addListener(EDIT_PROFILE_EVENT, onProfileUpdated);
    verifiedMobileEventListener = DeviceEventEmitter?.addListener(VERIFIED_MOBILE_EVENT, onMobileVerified);
    return () => {
      loginEventRNListener?.remove();
      loginEventListener?.remove();
      editProfileEventListener?.remove();
      verifiedMobileEventListener?.remove();
    };
  }, []);

  const onLoginStatusUpdatedRN = async () => {
    setUserData();
    loginEventRNListener?.remove();
  };

  const onLoginStatusUpdated = async () => {
    setUserData();
    loginEventListener?.remove();
  };

  const onProfileUpdated = async () => {
    setUserData();
    editProfileEventListener?.remove();
  };

  const onMobileVerified = async () => {
    setUserData();
    verifiedMobileEventListener?.remove();
  };

  const dismissWidget = () => {
    resetError();
  };

  const bnplTracker = (data: any) => {
    trackBnplEvents({ trackEvent, data });
  };

  const handleClick = () => {
    bnplTracker(trackingData.CHECK_ELIGIBILITY_CLICK);
    checkEligibility({
      mobile: userData?.mobileNumber,
      product: lob,
      amount,
    });
  };

  if (Platform.OS === 'web') {
    return null;
  } else {
    if (showBnplWidget && loadingState === LOADING_STATE.NOT_STARTED) {
      if (userData?.hasVerifiedMobile) {
        return (
          <BnplWidgetVerifiedMobile
            lobLabel={lobLabel}
            mobileWithCountryCode={userData?.mobileWithCountryCode}
            checkEligibility={handleClick}
          />
        );
      } else {
        if (userData?.isUserLoggedin) {
          return (
            <BnplWidgetNonVerifiedMobile
              lobLabel={lobLabel}
              addMobileNumber={onEditProfilePressed}
              trackEvent={bnplTracker}
              trackingData={trackingData}
            />
          );
        } else {
          return (
            <BnplWidgetNonLoggedIn
              lobLabel={lobLabel}
              onLoginClick={onLoginClick}
              trackEvent={bnplTracker}
              trackingData={trackingData}
            />
          );
        }
      }
    }
    if (
      showBnplWidget &&
      (loadingState === LOADING_STATE.VERIFICATION_FAILED ||
        loadingState === LOADING_STATE.NETWORK_ERROR)
    ) {
      return (
        <VerificationFailed
          lobLabel={lobLabel}
          dismissWidget={dismissWidget}
          loadingState={loadingState}
          trackEvent={bnplTracker}
          trackingData={trackingData}
          hideWidget={hideWidget}
        />
      );
    }

    if (showBnplWidget && loadingState === LOADING_STATE.SUCCESS) {
      return (
        <VerificationSuccess
          bnplEligibleAmount={bnplEligibleAmount}
          repaymentDetails={repaymentDetails}
          trackEvent={bnplTracker}
          trackingData={trackingData}
        />
      );
    }

    if (showBnplWidget && loadingState === LOADING_STATE.IN_PROGRESS) {
      return <Loading />;
    }

    return null;
  }
};

export default BookNowPayLater;

BookNowPayLater;
