import { StyleSheet } from 'react-native';
import { colors, fonts } from '../../../Styles/globalStyles';

const BnplStyles = StyleSheet.create({
  flexColumn: {
    display: "flex",
    flexDirection: "column"
  },
  flexRow: {
    display: "flex",
    flexDirection: "row",
  },
  wrapper: {
    backgroundColor: "#012138",
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 12,
  },
  checkingStatus: {
    height: 214,
    alignItems: "center",
    justifyContent: "space-around",
  },
  bnplLogo: {
    width: 35,
    height: 30,
  },
  title: {
    fontSize: 14,
    lineHeight: 18,
    fontWeight: "600",
    color: colors.white,
    marginBottom: 5,
  },
  subTitle: {
    fontSize: 11,
    lineHeight: 16,
    fontWeight: "400",
    color: colors.white,
    opacity: 0.7
  },
  body: {
    paddingHorizontal: 12,
    paddingBottom: 20,
    backgroundColor: colors.white,
    borderRadius: 16,
    marginTop: 18,
    marginBottom: 12,
  },
  tripmoneyLarge: {
    width: 88,
    height: 22,
  },
  tripmoneySmall: {
    width: 64,
    height: 16,
  }
});

export default BnplStyles;