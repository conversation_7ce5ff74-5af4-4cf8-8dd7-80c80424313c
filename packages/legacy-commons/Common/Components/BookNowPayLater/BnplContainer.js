import React from 'react';
import { connect } from 'react-redux';
import { checkBnplEligibility, initialise, setUserData, resetError, hideWidget } from './BnplActions';
import BookNowPayLater from './BookNowPayLater';

const mapStateToProps = ({ bnplReducer }) => {
  const { loadingState, bnplEligibleAmount, userData, repaymentDetails, showBnplWidget } = bnplReducer;
  return {
    loadingState,
    bnplEligibleAmount,
    userData,
    repaymentDetails,
    showBnplWidget
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    checkEligibility: (payload) => {
      dispatch(checkBnplEligibility(payload));
    },
    initialise: () => {
      dispatch(initialise());
    },
    setUserData: () => {
      dispatch(setUserData());
    },
    resetError: () => {
      dispatch(resetError());
    },
    hideWidget: () => {
      dispatch(hideWidget());
    }
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(BookNowPayLater);
