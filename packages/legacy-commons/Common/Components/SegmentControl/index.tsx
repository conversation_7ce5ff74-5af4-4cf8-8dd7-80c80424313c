import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { SegmentControlProps } from './SegmentControlProps';
import { styles } from './Styles';

const SegmentControl: React.FC<SegmentControlProps> = ({
    tabData,
    selectedTab,
    onItemPress,
    containerStyle,
    type,
    borderRequired,
}) => {
    return (
        <View style={[styles.container, containerStyle]}>
               {borderRequired && <View style={styles.border} />}
            {
                tabData.map((item, index) => (

                    <TouchableOpacity
                        key={item.id}
                        style={[styles.tabItem, selectedTab === index && styles.tabItemSelected]} activeOpacity={0.7}
                        onPress={() => onItemPress(type?.[index], index)}
                    >
                        <Text style={[styles.tabItemText, selectedTab === index && styles.tabItemTextSelected]} testID={`tab_${index}`}>{item.text} </Text>
                    </TouchableOpacity>
                ))
            }
        </View>
    )
}

export default SegmentControl;