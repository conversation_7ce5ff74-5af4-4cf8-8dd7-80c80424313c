import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        overflow: 'hidden',
        borderRadius: 8,
        flexDirection: 'row',
        flex:1,
    },
    tabItem: {
        flex:1,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,
    },
    tabItemSelected: {
        borderWidth: 1,
        borderColor: '#008CFF',
        backgroundColor: '#EAF5FF'
    },
    tabItemText: {
        fontFamily: 'Lato-Regular',
        fontSize: 12,
        lineHeight: 14,
        color: '#4a4a4a',
    },
    tabItemTextSelected: {
        fontFamily: 'Lato-Black',
        fontSize: 12,
        fontWeight: '900',
        color: '#008CFF',
    },
    border: {
        width: '100%',
        height: '100%',
        borderWidth: 1,
        borderColor: '#d8d8d8',
        position:'absolute',
        left:0,
        top:0,
        borderRadius: 8,
    },
});