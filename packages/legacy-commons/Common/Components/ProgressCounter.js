import React, {Component} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import ProgressCircle from 'react-native-progress-circle';
import PropTypes from 'prop-types';
import AtomicCss from '../../Styles/AtomicCss';


class ProgressCounter extends Component {
  constructor(props) {
    super(props);
    this.state = {
      counter: 5
    };
    this.timer = null;
  }

  componentDidMount() {
    this.couponCounter();
  }

  componentWillUnmount() {
    window.clearInterval(this.timer);
  }

  couponCounter() {
    this.timer = setInterval(() => {
      if (this.state.counter <= 1) {
        clearInterval(this.timer);
        this.props.onComplete();
        return;
      }
      this.setState({
        counter: this.state.counter - 1
      });
    }, 1000);
  }

  render() {
    return (
      <View style={styles.progressContainer}>
        { <ProgressCircle
          percent={(100 - (this.state.counter * 20))}
          radius={20}
          borderWidth={3}
          color="#e7e7e7"
          shadowColor="#249995"
          bgColor="#e9f4f4"
        >
          <Text style={[styles.progressText, AtomicCss.blackFont]}>{this.state.counter}</Text>
          </ProgressCircle>}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  progressContainer: {alignItems: 'center', marginBottom: 26},
  progressText: {fontSize: 16, color: '#249995'}

});

ProgressCounter.propTypes = {
  onComplete: PropTypes.func.isRequired
};

export default ProgressCounter;
