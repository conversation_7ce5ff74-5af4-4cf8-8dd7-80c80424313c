import React from "react";
import { Pressable } from "react-native";
import { colors } from "../../Styles/globalStyles";

export default function TouchableRipple({
  feedbackColor = colors.grayBg,
  onPress,
  children,
  style = {},
  disabled = false,
  testID = "",
}) {
  return (
    <Pressable
      onPress={onPress}
      disabled={disabled}
      testID={testID}
      android_ripple={{ color: feedbackColor, borderless: false }}
      style={({ pressed }) => [
        style,
        {
          backgroundColor: pressed ? feedbackColor : undefined,
        },
      ]}
    >
      {children}
    </Pressable>
  );
}
