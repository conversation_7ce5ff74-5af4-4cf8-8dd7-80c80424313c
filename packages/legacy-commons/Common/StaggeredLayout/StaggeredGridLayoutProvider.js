import { LayoutProvider } from 'recyclerlistview';
import StaggeredLayoutManager, { _debugLog, _debugLogGroup } from './StaggeredLayoutManager';

export { _debugLog, _debugLogGroup };

class StaggeredGridLayoutProvider extends LayoutProvider {
  /**
   * @param {(index: number) => string | number} getLayoutTypeForIndex
   * @param {(type: string | number, dim: import('recyclerlistview').Dimension, index: number) => void} setLayoutForType
   * @param {number} numberOfColumns
   */
  constructor(getLayoutTypeForIndex, setLayoutForType, numberOfColumns) {
    super(getLayoutTypeForIndex, setLayoutForType);
    this._numberOfColumns = numberOfColumns;
  }
  /**
   * @function
   * @param {import('recyclerlistview').Dimension} renderWindowSize
   * @param {boolean} [isHorizontal]
   * @param {Array<import('recyclerlistview').Layout>} [cachedLayouts]
   * @returns {StaggeredLayoutManager}
   */
  newLayoutManager(renderWindowSize, isHorizontal, cachedLayouts) {
    this._lastLayoutManager1 = new StaggeredLayoutManager(
      this,
      renderWindowSize,
      this._numberOfColumns,
      cachedLayouts,
    );
    return this._lastLayoutManager1;
  }
}
export default StaggeredGridLayoutProvider;
