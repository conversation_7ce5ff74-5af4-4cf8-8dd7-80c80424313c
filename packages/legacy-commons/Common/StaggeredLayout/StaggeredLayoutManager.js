import { LayoutManager } from 'recyclerlistview';

const _DEBUG_LABEL = '@@_RLV_v2';
export const _debugLog = (...args) => {
  console.log(_DEBUG_LABEL, ...args);
};
export const _debugLogGroup = {
  start: (...args) => {
    console.group(_DEBUG_LABEL, ...args);
  },
  end: () => {
    console.groupEnd();
  },
};

export default class StaggeredLayoutManager extends LayoutManager {
  /**
   * @param {import('recyclerlistview').LayoutProvider} layoutProvider
   * @param {import('recyclerlistview').Dimension} renderWindowSize
   * @param {number} [numberOfColumns]
   * @param {Array<import('recyclerlistview').Layout>} [cachedLayouts]
   */
  constructor(layoutProvider, renderWindowSize, numberOfColumns, cachedLayouts) {
    super();
    this._layoutProvider = layoutProvider;
    this._window = renderWindowSize;
    this._totalHeight = 0;
    this._totalWidth = 0;
    this._layouts = cachedLayouts || [];
    this._numberOfColumns = numberOfColumns;
  }

  /**
   * @function
   * @returns {import('recyclerlistview').Dimension}
   */
  getContentDimension() {
    return { height: this._totalHeight, width: this._totalWidth };
  }

  /**
   * @function
   * @returns {Array<import('recyclerlistview').Layout>}
   */
  getLayouts() {
    return this._layouts;
  }

  /**
   * @function
   * @param {number} index
   * @returns {import('recyclerlistview').Point}
   */
  getOffsetForIndex(index) {
    if (this._layouts.length > index) {
      return { x: this._layouts[index].x, y: this._layouts[index].y };
    }
    throw new Error(`No layout available for index: ${index}`);
  }

  /**
   * @function
   * @param {number} index
   * @param {import('recyclerlistview').Dimension} dim
   * @returns {true}
   */
  overrideLayout(index, dim) {
    const layout = this._layouts[index];
    if (layout) {
      layout.isOverridden = true;
      layout.width = dim.width;
      layout.height = dim.height;
    }
    return true;
  }

  /**
   * @param {import('recyclerlistview').Dimension} itemDim
   * @returns {void}
   */
  setMaxBounds(itemDim) {
    itemDim.width = Math.min(this._window.width, itemDim.width);
  }

  // TODO:Talha laziliy calculate in future revisions
  /**
   *
   * @param {number} startIndex
   * @param {number} itemCount
   */
  relayoutFromIndex(startIndex, itemCount) {
    startIndex = this._locateFirstNeighbourIndex(startIndex);
    let startX = 0;
    let startY = 0;
    let maxBound = 0;
    const startVal = this._layouts[startIndex];
    if (startVal) {
      startX = startVal.x;
      startY = startVal.y;
      this._pointDimensionsToRect(startVal);
    }
    const oldItemCount = this._layouts.length;
    const itemDim = { height: 0, width: 0 };
    let itemRect = null;
    let oldLayout = null;
    const horizontalOffsets = [];
    for (let i = 0; i < this._numberOfColumns; i++) {
      const offset = i * (this._window.width / this._numberOfColumns);
      horizontalOffsets.push(offset);
    }
    const verticalOffsets = new Array(this._numberOfColumns).fill(0);
    for (let i = startIndex; i < itemCount; i++) {
      oldLayout = this._layouts[i];
      const layoutType = this._layoutProvider.getLayoutTypeForIndex(i);
      if (oldLayout && oldLayout.isOverridden && oldLayout.type === layoutType) {
        itemDim.height = oldLayout.height;
        itemDim.width = oldLayout.width;
      } else {
        this._layoutProvider.setComputedLayout(layoutType, itemDim, i);
      }
      this.setMaxBounds(itemDim);
      let smallestColumnIndex = 0;
      let minY = Number.MAX_SAFE_INTEGER;
      verticalOffsets.forEach((value, index) => {
        // find lowest val and put new Y into it
        if (minY > value) {
          minY = value;
          smallestColumnIndex = index;
        }
      });
      startX = horizontalOffsets[smallestColumnIndex];
      startY = verticalOffsets[smallestColumnIndex];
      verticalOffsets[smallestColumnIndex] += itemDim.height;
      maxBound = Math.max(...verticalOffsets);
      // TODO: Talha creating array upfront will speed this up
      if (i > oldItemCount - 1) {
        this._layouts.push({
          x: startX,
          y: startY,
          height: itemDim.height,
          width: itemDim.width,
          type: layoutType,
        });
      } else {
        itemRect = this._layouts[i];
        itemRect.x = startX;
        itemRect.y = startY;
        itemRect.type = layoutType;
        itemRect.width = itemDim.width;
        itemRect.height = itemDim.height;
      }
    }
    if (oldItemCount > itemCount) {
      this._layouts.splice(itemCount, oldItemCount - itemCount);
    }
    this._setFinalDimensions(maxBound);
  }
  /**
   * @param {import('recyclerlistview').Layout} itemRect
   * @returns {void}
   */
  _pointDimensionsToRect(itemRect) {
    this._totalHeight = itemRect.y;
  }

  /**
   * @param {number} maxBound
   */
  _setFinalDimensions(maxBound) {
    this._totalWidth = this._window.width;
    this._totalHeight += maxBound;
  }

  /**
   * @function
   * @param {number} startIndex
   * @returns {number}
   */
  _locateFirstNeighbourIndex(startIndex) {
    if (startIndex === 0) {
      return 0;
    }
    let i = startIndex - 1;
    for (; i >= 0; i--) {
      if (this._layouts[i].x === 0) {
        break;
      }
    }
    return i;
  }
}
