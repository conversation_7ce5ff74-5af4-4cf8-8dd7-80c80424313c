import React, { useCallback, useEffect, useState } from 'react';
import * as ErrorReporter from '../AppState/errorReporter';
import { useFocusEffect } from '@react-navigation/native';

export default function bootstrapModule(module, registry) {
  const { id, routeConfig, decorator } = module;
  const wrappedRouteConfig = routeConfig.map((route) => {
    const { component: originalComponent } = route;
    const WrappedComponent = () => {
      function ModuleWrapper(props) {
        useFocusEffect(useCallback(() => {
          ErrorReporter.onPageChange(route.key || route.name, id);
        }, []));
        let promiseToWaitFor = null;
        if (!registry[id]) {
          registry[id] = module;
          if (module.onMount) {
            const retVal = module.onMount(props);
            const isPromise = typeof retVal?.then === 'function';
            if (isPromise) {
              promiseToWaitFor = retVal;
            }
          }
        }
        const [showComponent, setShowComponent] = useState(promiseToWaitFor === null);
        useEffect(() => {
          if (promiseToWaitFor !== null) {
            promiseToWaitFor.finally(function () {
              setShowComponent(true);
            });
          }
        }, [promiseToWaitFor]);

        if (!showComponent) {
          return <></>;
        }
        const Cmp = originalComponent();
        const cmpElement = <Cmp {...props} />;
        return decorator ? decorator(cmpElement, props) : cmpElement;
      }

      return ModuleWrapper;
    };

    return {
      ...route,
      component: WrappedComponent,
    };
  });
  return {
    ...module,
    routeConfig: wrappedRouteConfig,
  };
}
