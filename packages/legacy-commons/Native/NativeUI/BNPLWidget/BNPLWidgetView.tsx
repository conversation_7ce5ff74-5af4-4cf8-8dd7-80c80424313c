import {View, requireNativeComponent} from 'react-native';
import React, { createRef } from 'react';
// Define the propTypes for your custom component
interface BNPLWidgetProps {
  // Add other prop types as needed
}

// Create the native component using requireNativeComponent
const NativeBNPLWidgetView = requireNativeComponent<BNPLWidgetProps>('BNPLWidgetView');

class BNPLWidgetView extends React.Component<BNPLWidgetProps> {
  render() {
    // Pass onClick as a prop to the native component
    return (
  <NativeBNPLWidgetView {...this.props}/>
    );
   }
}

export default BNPLWidgetView;