import {requireNativeComponent} from 'react-native';
import React from 'react';
// Define the propTypes for your custom component
interface InsuranceWidgetProps {
  // Add other prop types as needed
}

// Create the native component using requireNativeComponent
const NativeInsuranceWidgetView = requireNativeComponent<InsuranceWidgetProps>('InsuranceWidgetView');

class InsuranceWidgetView extends React.Component<InsuranceWidgetProps> {
  render() {
    // Pass onClick as a prop to the native component
    return (
  <NativeInsuranceWidgetView {...this.props}/>
    );
   }
}

export default InsuranceWidgetView;
