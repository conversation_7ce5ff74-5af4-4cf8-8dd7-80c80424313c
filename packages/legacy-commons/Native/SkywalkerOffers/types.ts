export type LOB = 'BUS' | 'CABS' | 'RAILS' | 'SELFDRIVE';
export interface SkyWalkerResponse {
  requestId: string;
  sequenceData: SequenceData;
  data: Data;
}

export interface SequenceData {
  BASE_SHEET: BaseSheet;
}

export interface BaseSheet {
  cardSequence: CardSequence;
}

export interface CardSequence {
  dataKey: string;
  data: Daum[];
}

export interface Daum {
  id: string;
  cardId: string;
  template: Template;
}

export interface Template {
  id: string;
}

export interface Data {
  BASE_SHEET: BaseSheet2;
}

export interface BaseSheet2 {
  cardData: CardData;
}

export interface CardData {
  BUS_OFFERS_B2C: OffersB2C;
  RAILS_OFFERS_B2C_IN: OffersB2C;
  CABS_OFFERS_B2C: OffersB2C;
  SELF_DRIVE_OFFERS_B2C: OffersB2C;
}

export interface OffersB2C {
  dataKey: string;
  data: Data2;
  headerData: HeaderData;
  template: Template2;
}

export interface Data2 {
  offerTracker: string[];
  pM: PM[];
  displayTabs: boolean;
}

export interface PM {
  active: boolean;
  upcoming: boolean;
  id: number;
  pTl: string;
  pTx: string;
  lob: string;
  cc?: string;
  url: string;
  RO: string;
  st: number;
  et: number;
  spSt: number;
  spEt: number;
  herodSt: number;
  herodEt: number;
  Fl: string;
  dis: number;
  maxDis: number;
  inv: number;
  hero: boolean;
  dStxt: string;
  heroUrl: string;
  newHeroUrl: string;
  newHeroOfferCardUrl: string;
  dEtxt: string;
  offerTxt: string;
  showExpiry: boolean;
  visibleOnHome: boolean;
  displayStartTime: number;
  displayEndTime: number;
  bank: string;
  heroDisplayTimeDifference: number;
  skyBigFullImgUrl: string;
  cardCTAText: string;
}

export interface HeaderData {
  header: string;
  cta: Cta;
  showCross: boolean;
}

export interface Cta {
  ctaFlag: boolean;
}

export interface Template2 {
  id: string;
  styleId: string;
  type: string;
  scroll: string;
  state: string;
  position: string;
}

export interface OfferItem {
  id: number;
  newHeroUrl: string;
  cardCTAText: string;
  newHeroOfferCardUrl: string;
  promoTitle: string;
  promoMessage: string;
  promoCode?: string;
  dis: number;
  url: string;
  skyBigFullImgUrl: string;
  lob: string;
  isUpcomingOffer: boolean;
}
