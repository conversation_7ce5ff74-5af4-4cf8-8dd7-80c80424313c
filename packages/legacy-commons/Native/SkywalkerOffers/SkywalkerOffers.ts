//@ts-ignore reason : path import error
import { Platform } from 'react-native';
//@ts-ignore reason : path import error
import { getSkyWalkerBody, getSkyWalkerHeaders } from '../../Helpers/genericHelper';
import { LOB, OfferItem, PM, SkyWalkerResponse } from './types';
//@ts-ignore reason : path import error
import { getOffersList } from '../OffersModule';
import GenericModule from '../GenericModule';

/*
 * USAGE
 * import { getOffersFromSkyWalker } from 'src/Native/SkywalkerOffers';
 * const offersList = await getOffersFromSkyWalker('BUS');
 */

/*
 * Creates the lob specific API
 * NOTE: The value should match with the API created by skywalker team. for new API please contact them.
 */

const LOB_API_MAP = {
  BUS: 'buslanding',
  CABS: 'cabslanding',
  RAILS: 'railslanding',
  SELFDRIVE: 'selfdrivelanding',
};

const BASE_URL = 'https://skywalker.makemytrip.com'

const getLobAPI = (lob: LOB, currency:string, language: string): string => {
  return `${BASE_URL}/empeiria/api/v1/${LOB_API_MAP[lob]}?currency=${currency}&language=${language}&brand=MMT`;
};

const checkIfUpcomingOffer = (startDate: any, endDate: any): boolean => {
  const currentDate = new Date();
  const offerStartDate = new Date(startDate);
  const offerEndDate = new Date(endDate);
  return (
    offerStartDate.getTime() >= currentDate.getTime() &&
    currentDate.getTime() <= offerEndDate.getTime()
  );
};

/*
 * This maps the raw API response to more readable response.
 */
const mapOffers = (offers: SkyWalkerResponse, lob: LOB): OfferItem[] => {
  //Key name will be diff for each lob, so getting it from response.
  const offerLobKey = offers?.sequenceData.BASE_SHEET.cardSequence.data[0].id;
  //@ts-ignore Reason : reading dynamic key
  const pMoffers = offers?.data?.BASE_SHEET?.cardData[offerLobKey]?.data?.pM || [];
  try {
    const offersList = pMoffers.map((originalOfferObj: PM) => {
      let promoTitle =
        originalOfferObj?.dStxt && originalOfferObj?.offerTxt && originalOfferObj?.dEtxt
          ? `${originalOfferObj.dStxt} ${originalOfferObj.offerTxt} ${originalOfferObj.dEtxt}`
          : originalOfferObj.pTl;

      const isUpcomingOffer = checkIfUpcomingOffer(originalOfferObj.st, originalOfferObj.et);

      const mappedOfferItem: OfferItem = {
        promoTitle,
        id: originalOfferObj.id,
        newHeroUrl: originalOfferObj.newHeroUrl,
        cardCTAText: originalOfferObj.cardCTAText,
        newHeroOfferCardUrl: originalOfferObj.newHeroOfferCardUrl,
        promoMessage: originalOfferObj.pTl,
        promoCode: originalOfferObj.cc,
        dis: originalOfferObj.dis,
        url: originalOfferObj.url,
        skyBigFullImgUrl: originalOfferObj.skyBigFullImgUrl,
        lob: originalOfferObj.lob,
        isUpcomingOffer,
      };
      return mappedOfferItem;
    });
    return offersList.sort(
      (offerItem1: { dis: number }, offerItem2: { dis: number }) =>
        offerItem2?.dis - offerItem1?.dis,
    );
  } catch (error) {
    console.error('Error while mapping offer response', error);
    return [];
  }
};

/*
 * @param {LOB} lob - Pass lobs name in upper case.
 */
export const getOffersFromSkyWalker = async (lob: LOB, currency = 'inr', language = 'eng'): Promise<OfferItem[]> => {
  try {
    if (!lob) {
      throw 'Please pass the lob name';
    }
    console.log(`Getting offers from skywalker for ${lob}`);
    if(Platform.OS === 'web'){
      return await getOffersList(lob, 'Search');
    }else{
      const commonHeaders = await getSkyWalkerHeaders();
      const headers = {
        ...commonHeaders,
        language
      };
      const body = await getSkyWalkerBody();
      const response = await fetch(getLobAPI(lob, currency,language), {
        method: 'POST',
        headers,
        body,
      });
      const data: SkyWalkerResponse = await response.json();
      return mapOffers(data, lob);
    }
  } catch (error) {
    console.error(`Error while getting offers from skywalker for ${lob}`, error);
    return [];
  }
};

/*
 * @param {LOB} lob - Pass lobs name in upper case.
 */
export const getOffersFromSkyWalkerForPremiumUsers = async (lob: LOB, currency = 'inr', language = 'eng'): Promise<OfferItem[]> => {
  try {
    if (!lob) {
      throw 'Please pass the lob name';
    }
    console.log(`Getting offers from skywalker for ${lob}`);
    if(Platform.OS === 'web'){
      return await getOffersList(lob, 'Search');
    }else{
      const commonHeaders = await getSkyWalkerHeaders();
      const headers = {
        ...commonHeaders,
        language
      };
      const body = await GenericModule.getSkyWalkerRequestBody();
      const response = await fetch(getLobAPI(lob, currency,language), {
        method: 'POST',
        headers,
        body:JSON.stringify(body),
      });
      const data: SkyWalkerResponse = await response.json();
      return mapOffers(data, lob);
    }
  } catch (error) {
    console.error(`Error while getting offers from skywalker for ${lob}`, error);
    return [];
  }
};

