import {NativeModules} from 'react-native';

const {NotificationModule} = NativeModules;

export const NotificationKey = {
  NOTIFICATION_KEY_CABS: "notification_prompt_for_cabs",
  NOTIFICATION_KEY_RAILS: "notification_prompt_for_rails",
  NOTIFICATION_KEY_BUS: "notification_prompt_for_bus"
};

// Sample notification data
// const notification = {
//   cmp: 'applocal',             // default value set to applocal so as to always send notification
//   deeplink: 'mmyt://react/?page=cabs&deeplink=true&tripType=AT&startDate=16-01-2019&
//              cityCode=BLR_AIR&terminal=1&pickupTime=11:30&airportPickupType=from&
//              flightNumber=6E-1234',                // Action to be taken on clicking notification
//   imageUrl: 'http://imgak.mmtcdn.com/mi8/images/slideshow/Innova_assured.png',         //optional
//   lob: 'CAB',                                                            // Lob from which called
//   minDiffInDays: 0,                                                     // Default value set to 0
//   notificationStartTime: startTime.getTime(),                          // Notification start time
//   notificationEndTime: endTime.getTime(),                                // Notification end time
//   notificationTime,                             // Difference between start time and current time
//   subTitle,                                                           // Subtitle to be displayed
//   title,                                                                 // Title to be displayed
//   text,                                                                   // Text to be displayed
//   key,                                                            // One of the above stated keys
// };

export const scheduleNotification = (notification) => {
  NotificationModule.scheduleNotification(notification);
};
