export type GenericModuleWishlistItemInfo =
  | {
      itemId: string;
      itemName: string;
      action: 'add' | 'delete';
      category: string;
      locusType: 'city' | 'poi';
      cityCode: string;
    }
  | {
      itemId: string;
      itemName: string;
      action: 'move';
      category: string;
      wishlistId: string;
      locusType: 'city' | 'poi';
      cityCode: string;
    };

export type GenericModuleWishlistItemResponse = {
  error: null;
  status: 'SUCCESS';
  statusCode: '200';
  // "message": "<b>Ibis Bengaluru Hosur Road - An Accor Brand</b> has been successfully added to <b>trendi</b>",
  message: string;
  wishlist: {
    title: string;
    subtitle: string;
    id: string;
    imgUrl: string;
    deeplink: string;
    desc: string;
    icon: string;
    createdAt: string;
    count: number;
    shareMessage: null;
    containsItem: boolean;
  };
  cta: {
    // "text": "<b>Arnna Hotel - Goregaon</b> has been successfully added to <b>prod wishlist</b>",
    text: string;
    action: 'view' | 'change';
  };
};

export type PhoneGalleryAsset = {
  basic: {
    fileLocation: string;
    filename?: string;
    fileSize: number;
    fileFormat: number;
    duration?: number;
  };
  exif?: {
    location: {
      latitude: number;
      longitude: number;
    };
  };
};

export declare function updateRecentSearchHistory(
  lob: string,
  searchData: Record<string, unknown>,
): Promise<void>;

export type DeviceInfo = Record<string, unknown> & {
  os: string;
  osVersion: string;
  deviceType: string;
  appVersion: string;
  deviceId: string;
  networkType: string;
  deviceName: string;
  mcId: string;
  pdtDeviceId?: string;
  androidVersion: string;
  phoneNumber: string;
  accountEmailId: string;
};

declare module '@mmt/legacy-commons/Native/GenericModule' {
  function openDeepLink(url: string): Promise<void>;
  function openImagePicker(): Promise<Array<unknown>>;
  function getDaySpentValue(): Promise<number | null>;
  function openCabs(options: unknown): Promise<void>;
  function openDialer(phoneNumber: string): Promise<void>;
  function updateSearchEvents(lob: string, searchData: Record<string, unknown>): Promise<void>;
  function updateSectorInfo(lob: string, sectorInfo: Record<string, unknown>): Promise<void>;
  function getDeviceInfo(): Promise<DeviceInfo>;
  function getAppVersion(): Promise<string>;
  function getSessionInfo(): Promise<
    Record<string, unknown> & { sessionCmp: string; launchSource: string }
  >;
  function updateRecentHistory(lob: string, searchData: Record<string, unknown>): Promise<void>;
  function openPhoneGallery(options: unknown[]): Promise<PhoneGalleryAsset[]>;
  function performWishlistAction<
    T extends GenericModuleWishlistItemInfo,
    O extends GenericModuleWishlistItemResponse,
  >(info: T): Promise<O>;
  function showWishlistSelectionBottomSheet<
    T extends GenericModuleWishlistItemInfo,
    O extends GenericModuleWishlistItemResponse,
  >(info: T): Promise<O>;
  function openActivity(activityType: string): Promise<void>;
  function activateScreenOn(): Promise<void>;
  function deactivateScreenOn(): Promise<void>;
}
