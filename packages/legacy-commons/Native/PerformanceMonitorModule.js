import { NativeModules, Platform } from 'react-native';
const PerformanceMonitorModule = Platform.OS === 'android' ? NativeModules.PerformanceMonitorModule : {
    // we will define this module for IOS and web later
    start: () => { console.debug(`******** On Platform '${Platform.OS}' PerformanceMonitorModule.start() = ()=>{}` )},
    stop: () => { console.debug(`******** On  Platform '${Platform.OS}' PerformanceMonitorModule.stop() = ()=>{}` )}
}

export default PerformanceMonitorModule;
