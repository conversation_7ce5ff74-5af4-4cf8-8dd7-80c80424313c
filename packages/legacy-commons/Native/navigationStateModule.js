import {NativeModules} from 'react-native';


export const saveNavigationState = (pageId, state) => {
  if (pageId && state && NativeModules.NavigationStateModule) {
    NativeModules.NavigationStateModule.saveState(pageId, JSON.stringify(state));
  }
};

export const setIsUsingLegacyNav = (isLegacyNav) => {
  if (NativeModules.NavigationStateModule
    && NativeModules.NavigationStateModule.setIsUsingLegacyNav) {
    NativeModules.NavigationStateModule.setIsUsingLegacyNav(isLegacyNav);
  }
};
