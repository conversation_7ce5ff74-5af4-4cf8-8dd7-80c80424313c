import {NativeModules, Platform} from 'react-native';
import { getRootTag } from '../AppState/RootTagHolder';

if (NativeModules.GenericModule) {
  const openMyra = NativeModules.GenericModule.openMyraPage;
  NativeModules.GenericModule.openMyraPage = (additionalInfo) => {
    if (Platform.OS === 'ios') {
      const reactTag = getRootTag();
      openMyra(reactTag, additionalInfo);
    } else {
      openMyra();
    }
  };

  const openDeepLink = NativeModules.GenericModule.openDeepLink;
  NativeModules.GenericModule.openDeepLink = (deeplink, clearStack) => {
    if (Platform.OS === 'ios') {
      const reactTag = clearStack === true ? -100 : getRootTag();
      openDeepLink(deeplink, reactTag);
    } else {
      openDeepLink(deeplink);
    }
  };
}

export const updateRecentSearchHistory = (lob, searchData) => {
  const {GenericModule} = NativeModules;
  if (!GenericModule || !GenericModule.updateRecentHistory) {
    return;
  }
  GenericModule.updateRecentHistory(lob, searchData);
};


export const openViewFileIntentAndroid = ({ uri, mimeType, targetApp = null, newTaskFlag = true, noHistoryFlag = false }) => {
  const {GenericModule} = NativeModules;
  if (!GenericModule || !GenericModule.openViewFileIntentAndroid) {
    throw 'Action not supported! GenericModule.openFileIntentAndroid() doesn\'t exists on native side';
  }
  return GenericModule.openViewFileIntentAndroid({
    uri,
    'mime-type': mimeType,
    'target-app': targetApp,
    'new-task': newTaskFlag,
    'no-history': noHistoryFlag,
  });
};

export const logRNPageLoad = (pageName) => {
  const {GenericModule} = NativeModules;
  if (Platform.OS === "android") {
    if (!GenericModule || !GenericModule.logRNPageLoad) {
      return;
    }
    GenericModule.logRNPageLoad(pageName)
  }
}

export const getCampaignDetails = async () => {
  try {
    const { GenericModule } = NativeModules;
    if (Platform.OS === 'android' && GenericModule?.getSessionInfo) {
      const deepLinkTrackingData = await GenericModule.getSessionInfo();
      if(deepLinkTrackingData?.launchSource !== 'Direct')
      return deepLinkTrackingData;
    }
    return null;
  } catch (err) {
    console.error('Error while getting campaign details')
    return null;
  }
};

/**
 * Retrieves the installation time of the app.
 * 
 * @returns {Promise<number|null>} The installation time of the app, or null if it cannot be retrieved.
 */
export const getFirstInstallTime = async () => {
  try {
    const { GenericModule } = NativeModules;
    if (Platform.OS === 'android' && GenericModule?.getFirstInstallTime) {
      const firstInstallTime = await GenericModule.getFirstInstallTime();
      return firstInstallTime?.['app_first_installed_time'];
    }
    return null;
  } catch (err) {
    console.error('Error while getting first app install time')
    return null;
  }
};


export default NativeModules.GenericModule;
