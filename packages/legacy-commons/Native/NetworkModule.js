import {NativeModules, Platform} from 'react-native';

export const botmanSensorHeader = async () => {
  try {
    const {NetworkModule} = NativeModules;
    // This will return map of sensor data and user agent.
    const sensorData = await NetworkModule.getSensorData();
    if (sensorData) {
      return sensorData;
    }
    return {};
  } catch (e) {
    return {};
  }
};
const getHeaders = NativeModules.NetworkModule.getHeaders;
NativeModules.NetworkModule.getHeaders = (supportedLanguage = null) => {
  return Platform.OS === 'ios' ? getHeaders(supportedLanguage) : getHeaders();
};
export default NativeModules.NetworkModule;
