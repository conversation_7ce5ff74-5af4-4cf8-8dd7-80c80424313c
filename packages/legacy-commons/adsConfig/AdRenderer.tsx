import React from 'react';
import { Platform, View } from 'react-native';
// @ts-ignore
import RNAdWrapper from 'react-native-ad-wrapper';
// @ts-ignore
import AdComponent from 'ad-react-wrapper';

interface AdRendererProps {
  setter: any;
  uuid: Array<string> | string;
  onViewError?: () => void;
}

const AdRenderer = React.memo(function ({
  setter,
  uuid,
  onViewError = () => {}
}: AdRendererProps) {
  if (Platform.OS === 'web') {
    return (
      <View>
        <AdComponent uuid={uuid} />
      </View>
    );
  }
  return (
    <RNAdWrapper
      uuid={uuid}
      topWindowThreshold={80}
      onViewError={onViewError}
      setter={setter}
    />
  );
});

export default AdRenderer;
