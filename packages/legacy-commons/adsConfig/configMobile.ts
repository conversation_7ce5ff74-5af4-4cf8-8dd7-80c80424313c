import { getHeaders } from '../Common/utils/NetworkUtils';
import { getUserDetails } from '../Native/UserSession/UserSessionModule';
import { NativeModules, Platform } from 'react-native';

const configObj = {
  ORG: 'MMT',
  REGION: 'IN',
  mode: 'PROD',
  DEVICE_ID: '',
  OS_VERSION: '',
  TIME_ZONE: 'IST',
  DEVICE_LANGUAGE: 'eng',
  PROFILE_TYPE: 'PERSONAL',
  AUTH_COOKIE_KEY: 'mmt-auth',
  GET_APP_VERSION_CB: NativeModules.GenericModule?.getAppVersion,
  GET_USER_AGENT_CB:  async () => {
    const sensorData = await NativeModules.NetworkModule?.getSensorData?.();
    return sensorData?.["User-Agent"];
  },
  WEB_VIEW_CB: (url: string) => NativeModules.GenericModule?.openDeepLink(url),
};
const getConfigModule = async () => {
  try {
    const headers = await getHeaders();
    const user = await getUserDetails();
    const { mmtAuth } = user;
    if ('region' in headers) configObj.REGION = headers.region;
    if ('language' in headers) configObj.DEVICE_LANGUAGE = headers.language;
    configObj.DEVICE_ID = ((Platform.OS === 'android') ? (headers.deviceid) : (headers.deviceId)) ?? '';
    if ('os' in headers) configObj.OS_VERSION = headers.os;
    configObj.AUTH_COOKIE_KEY = mmtAuth;
    return configObj;
  } catch (e) {
    console.log(e);
    return configObj;
  }
};
export default async () => {
  try {
    return await getConfigModule();
  } catch (error) {
    console.log(error);
  }
};
