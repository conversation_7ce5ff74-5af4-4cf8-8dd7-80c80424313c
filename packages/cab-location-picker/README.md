#Cab Location Picker

A package that allows you to pick a location using user input or map


## Folder Structure

packages/cab-location-picker/
├── src/
│   ├── atoms/
│   │   └── CustomMarker.tsx
│   │   └── FloatingInput.tsx
│   ├── molecules/
│   │   ├── LocationInput.tsx
│   │   ├── LocationPermission.tsx
│   │   ├── MapFooter.tsx
│   │   ├── MapHeader.tsx
│   │   ├── SetLocationCTA.tsx
│   ├── organisms/
│   │   └── InputFieldSection.tsx
│   │   └── MapSection.tsx
│   │   └── SuggestionList.tsx
│   ├── hooks/
│   │   └── useFetchSuggestions.ts
│   │   └── useMapLocations.ts
│   ├── utils/
│   │   └── builder.ts
│   │   └── helpers.ts
│   │   └── constants.ts
│   └── index.tsx
│   └── props.ts
│   └── styles.ts
│   └── types.ts
├── README.md
└── package.json


## Usage

For Detailed info related to the props, please refer to the prop.ts file

```javascript
import { LocationsListData } from '@mmt/cab-location-picker/src/types';

const CabLocationPickerComponent = require('@mmt/cab-location-picker/src').default;

return (
	<CabLocationPicker
		headerText={header}
		onSuggestionsClickCb={onSuggestionsClickCb}
		personalisedLocationsData={personalisedLocationsData}
		mapData={mapData}
		locationFieldsData={locationFieldsData}
		otherData={otherData}
		otherConfig={otherConfig}
		onInputStartIconPress={onInputStartIconActionHandler}
		onMapClosePress={onMapClosePress}
	/>
);
```


## Guidelines

- Avoid hardcoding any business logic or lob specific functionalities to this component
- For navigation, the consumer lob itself needs to handle the logic. This component will provide appropriate callbacks to the consumer.
- While adding any new component make sure to follow Atomic Design principles.
- Wherever necessary, add JSDoc comments to your components and util files


## TODO

- [ ] Add module loading using babel aliases
- [ ] Add testcases
