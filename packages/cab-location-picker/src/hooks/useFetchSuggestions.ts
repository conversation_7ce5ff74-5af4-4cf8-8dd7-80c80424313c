import { useState, useCallback } from 'react';
import debounce from 'lodash/debounce';

import { FetchAutocompleteLiteParams, LocationsListData, useFetchSuggestionsParams } from '../types';
import { AUTOMCOMPLETE_DEBOUNCE_TIME } from '../utils/constants';

import { fetchLiteAutoCompleteResults } from '../api/actions';
import { mixPersonalisedWithSearchResults, prepAutoCompleteParams } from '../utils/builder';

const useFetchSuggestions = ({
  curentFocusedFieldID,
  locationFieldsDataCopy,
  otherConfig,
	personalisedLocationsData,
	otherData,
}: useFetchSuggestionsParams) => {
  const [suggestions, setSuggestions] = useState<LocationsListData[]>(personalisedLocationsData);

	/**
	 * Resets the suggestions to the personalised locations data or an empty array if no data is available.
	*/
  const resetSuggestions = () => {
    setSuggestions(personalisedLocationsData || []);
  };

	/**
	 * Debounced function to fetch and set suggestion list based on the provided parameters.
	 *
	 * This function uses `debounce` to limit the rate at which the suggestions are fetched.
	 * It fetches autocomplete results using `fetchLiteAutoCompleteResults` and then mixes
	 * these results with personalized location data
	 * The mixed suggestions are then set using `setSuggestions`.
	 *
	 * @param params - The parameters to be used for fetching autocomplete results.
	 *
	 */
  const getSuggestionListDebounced = useCallback(
    debounce(async (params: FetchAutocompleteLiteParams) => {
      const res = await fetchLiteAutoCompleteResults(params);
			const mixedSuggestions = mixPersonalisedWithSearchResults({
				personalisedLocationsData,
				searchResults: res,
				query: params.query,
				suggestionListConfig: otherData?.searchListConfig,
			});
      setSuggestions(mixedSuggestions);
    }, AUTOMCOMPLETE_DEBOUNCE_TIME),
    [],
  );

	/**
	 * Handles the search query to fetch location suggestions.
	 *
	 * This function prepares the parameters for the autocomplete request
	 * and fetches the suggestion list if the query length is greater than 1.
	 * If the query is empty or too short, it sets the suggestions to the
	 * personalised locations data.
	 *
	 * @param {string} query - The search query input by the user.
	 */
  const handleSearch = (query: string) => {
		const autoCompleteParams = prepAutoCompleteParams({
			query,
			otherConfig,
			locationFieldsDataCopy,
			curentFocusedFieldID,
		});

    if (query?.trim().length > 1) {
      return getSuggestionListDebounced(autoCompleteParams);
    }
    return setSuggestions(personalisedLocationsData);
  };

  return {
    suggestions,
    handleSearch,
    resetSuggestions,
  };
};

export default useFetchSuggestions;
