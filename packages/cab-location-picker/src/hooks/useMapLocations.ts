import { useState, useEffect } from 'react';
import isEmpty from 'lodash/isEmpty';
import { LocationInfo } from '../../src/types';
import { getCurrentLocation, reverseGeocode } from '../../src/api/actions';

const useMapLocations = (initialSourceData: LocationInfo | Record<string, never>) => {
  const [pinnedLocation, setPinnedLocation] = useState(initialSourceData);
  const [userLocation, setUserLocation] = useState<LocationInfo>();

  const fetchLocationName = async (latLong: { latitude: number; longitude: number } | null) => {
    try {
      if (latLong) {
        const addr = await reverseGeocode(latLong?.latitude, latLong?.longitude);
        setPinnedLocation(addr);
      }
    } catch (err) {
      console.error(err);
    }
  };

  const getCurrLocation = async () => {
    const currentPlaceAddress = await getCurrentLocation();
    if (!isEmpty(currentPlaceAddress) && !pinnedLocation) {
      setPinnedLocation(currentPlaceAddress);
    }
    setUserLocation(currentPlaceAddress);
  };

  useEffect(() => {
    getCurrLocation();
  }, []);

  return {
    pinnedLocation,
    setPinnedLocation,
    userLocation,
    setUserLocation,
    fetchLocationName,
  };
};

export default useMapLocations;
