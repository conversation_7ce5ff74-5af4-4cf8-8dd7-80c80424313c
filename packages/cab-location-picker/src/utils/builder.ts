import { findCurrentFocusedField, findReferenceLocation } from './helpers';
import {
  PrepAutoCompleteParams,
  PrepAutoCompleteResult,
  MixPersonalisedSearchParams,
  LocationsListData,
} from '../types';

/**
 * Prepares the parameters for the autocomplete functionality.
 *
 * @param params - The parameters required to prepare the autocomplete request.
 * @param params.query - The search query string.
 * @param params.otherConfig - Additional configuration for the request.
 * @param params.otherConfig.tripType - The type of trip (e.g., one-way, round-trip).
 * @param params.otherConfig.isB2B - Flag indicating if the request is for a B2B context.
 * @param params.otherConfig.ohlFlag - Flag indicating if only hyperlocation should be considered.
 * @param params.locationFieldsDataCopy - A copy of the location fields data.
 * @param params.curentFocusedFieldID - The ID of the currently focused field.
 *
 */
export const prepAutoCompleteParams = (params: PrepAutoCompleteParams): PrepAutoCompleteResult => {
  const { query, otherConfig, locationFieldsDataCopy, curentFocusedFieldID } = params;
  const { tripType, isB2B, ohlFlag, countryCode, showDomestic } = otherConfig;

  const currentFocusedField = findCurrentFocusedField(locationFieldsDataCopy, curentFocusedFieldID);
  const referenceLocation = findReferenceLocation(locationFieldsDataCopy, currentFocusedField);

  return {
    query,
    tripType,
    requestFor: currentFocusedField?.id,
    refLat: referenceLocation?.latitude ?? null,
    refLng: referenceLocation?.longitude ?? null,
    countryCode,
    isB2B,
    onlyHyperlocation: ohlFlag,
    showDomestic,
  };
};

/**
 * Filters personalised location data based on a query string.
 *
 * This function takes an array of personalised location data and a query string,
 * and returns a new array where each location's address starts with the query string.
 *
 * @param personalisedLocationsData - An array of personalised location data.
 * @param query - The query string to filter locations by.
 * @returns A new array of personalised location data with filtered locations.
 */
export const filterPersonalisedBasedOnQuery = (
  personalisedLocationsData: LocationsListData[],
  query: string,
) => {
  return personalisedLocationsData?.map((locationData) => {
    return {
      ...locationData,
      locations: locationData?.locations.filter((location) =>
        location?.address?.toLowerCase()?.startsWith(query?.toLowerCase()),
      ),
    };
  });
};

/**
 * Mixes personalised location data with search results based on the provided query and configuration.
 *
 * @param {Object} params - The parameters for mixing personalised locations with search results.
 * @param {PersonalisedLocationData[]} params.personalisedLocationsData - The personalised location data.
 * @param {SearchResult[]} params.searchResults - The search results.
 * @param {string} params.query - The search query.
 * @param {SuggestionListConfig} params.suggestionListConfig - The configuration for the suggestion list.
 * @returns {LocationsListData[]} The combined list of locations, including both personalised and search result locations.
 */
export const mixPersonalisedWithSearchResults = ({
  personalisedLocationsData,
  searchResults,
  query,
  suggestionListConfig,
}: MixPersonalisedSearchParams): LocationsListData[] => {
  const filteredPersonalisedData = filterPersonalisedBasedOnQuery(personalisedLocationsData, query);
  const personalisedLocations = filteredPersonalisedData
    ?.map((locationData) => locationData?.locations)
    ?.flat();

  const searchResultsWithoutDuplicates = searchResults?.filter(
    (searchResult) =>
      !personalisedLocations?.find(
        (personalisedLocation) => personalisedLocation?.place_id === searchResult?.place_id,
      ),
  );

  return [
    {
      type: suggestionListConfig?.type,
      header: suggestionListConfig?.header,
      icon: suggestionListConfig?.icon,
      locations: [...personalisedLocations, ...searchResultsWithoutDuplicates],
      shouldShow: true,
    },
  ];
};
