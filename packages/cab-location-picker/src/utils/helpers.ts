import _isNil from 'lodash/isNil';

import { LocationField, LocationFieldsData, LocationInfo, LocationListItem, OtherData } from '../types';
import { DEFAULT_LOCATION_INFO } from './constants';

/**
 * Finds the currently focused field from the location fields data.
 *
 * @param locationFieldsDataCopy - A copy of the location fields data.
 * @param curentFocusedFieldID - The ID of the currently focused field.
 * @returns The currently focused field if found, otherwise undefined.
 */
export const findCurrentFocusedField = (
  locationFieldsDataCopy: LocationFieldsData,
  curentFocusedFieldID: string,
): LocationField | undefined => {
  return locationFieldsDataCopy?.fieldList?.find(
    (field: LocationField) => field.id === curentFocusedFieldID,
  );
};

/**
 * Finds the reference location information from the location fields data.
 *
 * @param locationFieldsDataCopy - A copy of the location fields data.
 * @param currentFocusedField - The currently focused location field, which may be undefined.
 * @returns The location information of the reference location field, or undefined if not found.
 */
export const findReferenceLocation = (
  locationFieldsDataCopy: LocationFieldsData,
  currentFocusedField: LocationField | undefined,
) => {
  return locationFieldsDataCopy?.fieldList?.find(
    (field: LocationField) => field.id === currentFocusedField?.refLocId,
  )?.locationInfo;
};

/**
 * Returns the appropriate icon URL based on the type of location.
 *
 * @param locationInfo - An object containing information about the location.
 * @param suggestionListData - An object containing URLs for different types of location icons.
 * @returns The URL of the icon corresponding to the location type.
 */
export const getIconBasedOnLocationType = (
  locationInfo: LocationListItem,
  suggestionListData: OtherData,
) => {
  const { city, airport, hyperLocation } = suggestionListData?.iconUrls;
  if (locationInfo?.is_city) {
    return city;
  }
  if (locationInfo?.is_airport) {
    return airport;
  }
  return hyperLocation;
};

/**
 * Checks if the location information is incomplete.
 *
 * This function verifies if the provided `locationInfo` object contains all the required fields.
 * The required fields are: 'place_id', 'latitude', 'longitude', and 'address'.
 * If any of these fields are missing or empty, the function returns `true`, indicating that the location information is incomplete.
 *
 * @param locationInfo - The location information object to be checked.
 * @returns `true` if the location information is incomplete, otherwise `false`.
 */
const isLocationInfoIncomplete = (locationInfo: LocationInfo) => {
	if (!locationInfo) {
		return true;
	}
	const requiredFields = ['place_id', 'latitude', 'longitude', 'address'];
  return requiredFields.some(field => _isNil(locationInfo?.[field]) || locationInfo?.[field] === '');
};

/**
 * Sanitizes the location fields data by checking each field's location information.
 * If the location information is incomplete, it replaces it with default location information.
 *
 * @param locationFieldsData - The data containing location fields to be sanitized.
 * @returns The sanitized location fields data with updated field list.
 */
export const sanitizeLocationFieldsData = (locationFieldsData: LocationFieldsData) => {
  const updatedFieldList = locationFieldsData?.fieldList?.map((field) => {
    if (isLocationInfoIncomplete(field?.locationInfo)) {
      return {
        ...field,
        locationInfo: DEFAULT_LOCATION_INFO,
      };
    }
    return field;
  });
	return {
		...locationFieldsData,
		fieldList: updatedFieldList,
	};
};

/**
 * Checks if a suggestion item is invalid by verifying if any of the required fields are empty.
 *
 * @param item - The suggestion item to be checked.
 * @returns `true` if any of the required fields ('main_text', 'place_id') are empty, otherwise `false`.
 */
export const isInvalidSuggestion = (item: any) => {
	const requiredFields = ['main_text', 'place_id'];
	return requiredFields.some(field => item?.[field] === '');
};
