import React, { useState } from 'react';
import _isEmpty from 'lodash/isEmpty';

import { ScrollView, Text, Keyboard } from 'react-native';

import SuggestionList from './components/organisms/SuggestionList';
import MapSection from './components/organisms/MapSection';
import InputFieldSection from './components/organisms/InputFieldSection';

import useFetchSuggestions from './hooks/useFetchSuggestions';
import { sanitizeLocationFieldsData } from './utils/helpers';
import { getLatLongDetails } from './api/actions';
import { LocationFieldsData, LocationInfo } from './types';
import { CabLocationPickerProps } from './props';

import { styles } from './styles';

import {
  checkForLocationPermission,
  requestLocationPerm,
} from '@mmt/legacy-commons/Helpers/locationHelper';

const CabLocationPicker = ({
  headerText,
  onSuggestionsClickCb,
  personalisedLocationsData,
  mapData,
  locationFieldsData,
  otherData,
  otherConfig,
  onInputStartIconPress,
  onLocateMapClickCb,
}: CabLocationPickerProps) => {
  const [locationFieldsDataCopy, setLocationFieldsDataCopy] = useState<LocationFieldsData>(
    sanitizeLocationFieldsData(locationFieldsData),
  );
  const [curentFocusedFieldID, setCurentFocusedFieldID] = useState<string>('');
  const [curentFocusedFieldIDForMap, setCurentFocusedFieldIDForMap] = useState<string>('');
  const [showLocationPermission, setShowLocationPermission] = useState<boolean>(false);
  const [showMap, setShowMap] = useState<boolean>(false);

  const { suggestions, handleSearch, resetSuggestions } = useFetchSuggestions({
    otherConfig,
    locationFieldsDataCopy,
    curentFocusedFieldID,
    personalisedLocationsData,
    otherData,
  });

  //TODO_VS: null checks
  const onSuggestionSelect = async (item: LocationInfo, suggestionType: string) => {
    if (_isEmpty(item)) {
      return;
    }
    //TODO_VS: need to improve this logic, implement without curentFocusedFieldIDForMap state
    const focusedField = !_isEmpty(curentFocusedFieldID)
      ? curentFocusedFieldID
      : curentFocusedFieldIDForMap;
    if (item?.latitude && item?.longitude) {
      updateFieldsData(focusedField, item, suggestionType);
      return;
    }
    const placeDetail = await getLatLongDetails(item?.place_id);
    const updatedLocationInfo: LocationInfo = {
      ...placeDetail,
      address: item.address,
      place_id: item?.place_id,
    };
    updateFieldsData(focusedField, updatedLocationInfo, suggestionType);
  };

  const updateFieldsData = (
    fieldID: string,
    updatedValue: LocationInfo,
    suggestionType: string,
  ) => {
    const updatedFieldList = locationFieldsDataCopy?.fieldList?.map((field) => {
      if (field.id === fieldID) {
        return {
          ...field,
          locationInfo: updatedValue,
          isFocused: false,
        };
      }
      return field;
    });
    setLocationFieldsDataCopy({
      ...locationFieldsDataCopy,
      fieldList: updatedFieldList,
    });

    Keyboard.dismiss();
    resetSuggestions();
    onSuggestionsClickCb?.(updatedValue, suggestionType, fieldID);
    setCurentFocusedFieldID('');
  };

  const onUpdatedFieldFocus = (id: string) => {
    setCurentFocusedFieldID(id);
  };

  const openMap = () => {
    setCurentFocusedFieldIDForMap(curentFocusedFieldID);
    Keyboard.dismiss();
    setTimeout(() => {
      setCurentFocusedFieldID('');
      setShowMap(true);
    }, 200); //Delayed to avoid keyboard flickering before map opens
  };

  const onSetLocationMapClick = async () => {
    const hasPermission = await checkForLocationPermission();
    if (hasPermission) {
      openMap();
    } else {
      setShowLocationPermission(true);
    }
  };

  const onLocationPermGranted = async () => {
    setShowLocationPermission(false);
    try {
      await requestLocationPerm();
      openMap();
      onLocateMapClickCb?.();
    } catch (error) {
      console.log('Error in requesting location permission', error);
    }
  };

  const onMapLocationSelected = (location: LocationInfo) => {
    setShowMap(false);
    onSuggestionSelect(location, 'map'); //TODO_VS: update this static key (check anywhere else if we using it)
  };

  return (
    <ScrollView
      showsHorizontalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      contentContainerStyle={styles.contentWrapper}
    >
      {!!headerText && <Text style={styles.locationTitle}>{headerText}</Text>}
      <InputFieldSection
        locationFieldsDataCopy={locationFieldsDataCopy}
        handleSearch={handleSearch}
        onUpdatedFieldFocus={onUpdatedFieldFocus}
        curentFocusedFieldID={curentFocusedFieldID}
        onInputStartIconPress={onInputStartIconPress}
      />
      <MapSection
        curentFocusedFieldID={curentFocusedFieldID}
        showMap={showMap}
        setShowMap={setShowMap}
        onSetLocationMapClick={onSetLocationMapClick}
        onLocationPermGranted={onLocationPermGranted}
        mapData={mapData}
        showLocationPermission={showLocationPermission}
        setShowLocationPermission={setShowLocationPermission}
        locationFieldsDataCopy={locationFieldsDataCopy}
        curentFocusedFieldIDForMap={curentFocusedFieldIDForMap}
        onMapLocationSelected={onMapLocationSelected}
      />
      <SuggestionList
        suggestions={suggestions}
        onSuggestionSelect={onSuggestionSelect}
        otherData={otherData}
        curentFocusedFieldID={curentFocusedFieldID}
      />
    </ScrollView>
  );
};

export default CabLocationPicker;
