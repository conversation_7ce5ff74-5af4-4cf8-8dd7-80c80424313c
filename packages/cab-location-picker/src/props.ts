import {
  LocationFieldsData,
  LocationInfo,
  LocationsListData,
  MapData,
  OtherConfig,
  OtherData,
  LobData,
} from './types';

/**
 * Properties for the CabLocationPicker component.
 */
export interface CabLocationPickerProps {
  /**
   * Optional text to display as the header.
   */
  headerText?: string;

  /**
   * Data for the location fields.
   * Fields should be passed in an array in order they should be displayed.
   */
  locationFieldsData?: LocationFieldsData;

  /**
   * Static data to be show for map functionality.
   */
  mapData: MapData;

  /**
   * List of personalized locations data.
   * This can be used to display recent searches, favorite locations, etc.
   * This data should be passed in the order they should be displayed.
   */
  personalisedLocationsData: LocationsListData[];

  /**
   * Callback function to handle suggestions click.
   * @param location - The location information.
   * @param suggestionType - The type of suggestion that was clicked.
   * @param fieldID - The ID of the field that was in focus when the suggestion was clicked.
   */
  onSuggestionsClickCb: (location: LocationInfo, suggestionType: string, fieldID: string) => void;

  /**
   * Callback function to handle input start icon press.
   * Each field can have an interactive icon at the start of the input field.
   * @param actionId - The action ID.
   * @param fieldId - The field ID.
   */
  onInputStartIconPress: (actionId: string | null, fieldId: string) => void;

  /**
   * Other configuration settings (this contains autocomplete api related data).
   */
  otherConfig: OtherConfig;

  /**
   * Other static data (assets, keys etc.) required by the component.
   */
  otherData: OtherData;

  /**
   * Optional prop which can be used by each LOB consuming this field
   * Use this to pass any data which is specific to your lob. Don't use any other prop for this.
   */
  lobData?: LobData;

  /**
   * Callback function to handle locate map click.
   */
  onLocateMapClickCb?: () => void;
}
