import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  contentWrapper: {
    padding: 16,
    backgroundColor: colors.white,
    flex: 1,
  },
  locationTitle: {
    color: colors.defaultTextColor,
    fontFamily: fonts.bold,
    fontSize: fontSizes.lg,
    marginBottom: 15,
  },
  locationIconStyle: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
});
