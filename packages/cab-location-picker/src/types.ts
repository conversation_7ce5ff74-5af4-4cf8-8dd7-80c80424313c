export interface Cta {
  text: string;
  id: string;
}

export interface SetLocationCTA {
  icon: string;
  text: string;
}

export interface PermissionOverlay {
  heading: string;
  subHeading: string;
  iconUrl: string;
  desc: string;
  acceptText: string;
  rejectText: string;
}

export interface FooterData {
  locationIcon: string;
  ctaText: string;
  loadingText: string;
}

export interface HeaderData {
  closeIcon: string;
  title: string;
  loadingDesc: string;
}

export interface MarkerDesc {
	[key: string]: string;
}

export interface MapMarkerData {
	icon: string;
	desc: MarkerDesc;
}

export interface MapData {
  shouldShow: boolean;
  setLocationCTA: SetLocationCTA;
  permissionOverlay: PermissionOverlay;
  footer: FooterData;
  header: HeaderData;
	marker: MapMarkerData;
}

export interface SearchListConfig {
	type: string;
	header: string;
	icon: string;
}

export interface OtherData {
	iconUrls: {
		city: string;
		airport: string;
		hyperLocation: string;
	};
	searchListConfig: SearchListConfig;
}

export interface LocationField {
	id: string;
  label: string;
  placeholder: string;
	refLocId: string;
	leftIconUrl: string;
	leftIconAction: string | null;
  locationInfo: LocationInfo;
	isFocused?: boolean;
	updated?: boolean;
}

export interface LocationInfo {
  latitude: number;
  longitude: number;
	address: string;
  place_id: string;
  pincode?: string;
  city?: string;
  city_code?: string;
  is_city?: boolean;
  is_airport?: boolean;
  google_city?: string;
  country_code?: string;
  state?: string;
  country?: string;
  fav_title?: string;
}

export interface LocationListItem {
	is_city: boolean;
	is_airport: boolean;
	main_text: string;
	secondary_text: string;
	place_id: string;
	address: string;
	latitude?: number;
	longitude?: number;
}

export interface LocationsListData {
  type: string;
  header: string;
  icon: string;
  locations: LocationListItem[];
  shouldShow: boolean;
}

export interface LocationFieldsData {
	fieldList: LocationField[];
}

export interface OtherConfig {
	ohlFlag: boolean;
	isB2B: boolean;
	tripType: string;
	countryCode: string;
	showDomestic: boolean;
}

export interface ErrorMessages {
	emptyField: string;
	invalidLocation: string;
	duplicateLocations: string;
	noLocationChange: string;
}

export interface LobData {
	errorMessages: ErrorMessages;
	bottomCTAText: string;
}

//Network utils
export interface DeviceDetails {
  os: string;
  osVersion: string;
  deviceType: string;
  appVersion: string;
  deviceId: string;
  networkType: string;
  deviceName: string;
  mcId: string;
  pdtDeviceId?: string;
}

export interface LocationAutocompleteResult {
  address: string;
  secondary_text: string;
  is_airport: boolean;
  is_city: boolean;
  main_text: string;
  place_id: string;
  city_code: string | null;
}

export interface FetchAutocompleteLiteParams {
  query: string;
  tripType?: string;
  requestFor?: string;
  refLat?: number | null;
  refLng?: number | null;
  isB2B: boolean;
  onlyHyperlocation?: boolean;
  countryCode?: string | null;
	showDomestic?: boolean;
}

/** Builder types **/
export interface PrepAutoCompleteParams {
	query: string;
	otherConfig: OtherConfig;
	locationFieldsDataCopy: any;
	curentFocusedFieldID: string;
}

export interface PrepAutoCompleteResult {
	query: string;
	tripType: string;
	requestFor?: string
	refLat?: number | null;
	refLng?: number | null;
	countryCode: string | null;
	isB2B: boolean;
	onlyHyperlocation: boolean;
	showDomestic: boolean;
}

export interface MixPersonalisedSearchParams {
	personalisedLocationsData: LocationsListData[];
	searchResults: LocationListItem[];
	query: string;
	suggestionListConfig: SearchListConfig;
}

/** Hooks types **/
export interface useFetchSuggestionsParams {
  curentFocusedFieldID: string;
  locationFieldsDataCopy: LocationFieldsData;
  otherConfig: OtherConfig;
	personalisedLocationsData: LocationsListData[];
	otherData: OtherData;
}

export interface OldLocationType {
  types: string[];
  place_id: string;
  formatted_address: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
  address_components: { types: string[]; long_name: string; short_name: string }[];
  geometry: { location: { lat: number; lng: number } };
}
