import { ensureLocationPermGranted, getCurrentLatLng } from '@mmt/legacy-commons/Helpers/locationHelper';
import _intersection from 'lodash/intersection';
import _isFunction from 'lodash/isFunction';
import {
  FetchAutocompleteLiteParams,
  LocationAutocompleteResult,
  LocationInfo,
  LocationListItem,
	OldLocationType,
} from '../types';
import { TripTypes } from '../utils/constants';
import { _get } from './service';
import { AUTO_COMPLETE_V3, PLACE_LOCATION_URL } from './utils';
import { getReverseGeocodes } from '@mmt/cabs/src/utils/MapUtils/mapsApi.android';

const predictionsCache = new WeakMap();

//TODO_VS: separate out response.json parts from both functions below
//TODO_VS: separate out types from both functions below whereever possible
//TODO_VS: check that predictionsCache caching logic is working. Also see if it can be moved to a separate util

/**
 *
 * @param param.query - text for searching the location
 * @param param.tripType - trip type for which search is done
 * @param param.requestFor - request made for a from or to location, based on trip type and requestFor refLat and refLng might be ignored
 * @param param.refLat - latitude for search context
 * @param param.refLng - longitude for search contex, results based on this context will be shown earlier
 * @returns
 */
export const fetchLiteAutoCompleteResults = async ({
  query,
  tripType = TripTypes.OW,
  requestFor,
  refLat = null,
  refLng = null,
  isB2B = false,
  onlyHyperlocation = false,
  countryCode = '',
	showDomestic = false,
}: FetchAutocompleteLiteParams): Promise<Array<LocationAutocompleteResult>> => {
  const requestParams = new URLSearchParams({
    query,
    tripType,
    requestFor: requestFor || '',
    selectedLat: (refLat || '').toString(),
    selectedLng: (refLng || '').toString(),
    isCorporate: isB2B.toString(),
    ohl: onlyHyperlocation ? 'yes' : 'no',
    countryCode: countryCode || '',
		onlyDomestic: (showDomestic || '').toString(),
  }).toString();

  const autoCompleteUrl = `${AUTO_COMPLETE_V3}?${requestParams}`;

  if (predictionsCache.get({ autoCompleteUrl })) {
    return predictionsCache.get([{ autoCompleteUrl }]);
  }

  try {
    const response = await _get(autoCompleteUrl);
    const result = await response.json();

    if (result.status !== 'SUCCESS') {
      return [];
    }

    const predictions = result.response;
    predictionsCache.set({ autoCompleteUrl }, predictions);
    return predictions;
  } catch (e) {
    console.error(e);
    return [];
  }
};

/**
 * Fetches latitude and longitude details for a given place ID.
 *
 * @param placeId - The unique identifier for the place.
 * @returns A promise that resolves to a `LocationListItem` object containing the latitude and longitude details,
 *          or an empty object if an error occurs.
 *
 * @throws Will log an error message to the console if the fetch operation fails.
 */
export const getLatLongDetails = async (
  placeId: string,
): Promise<LocationListItem | Record<string, never>> => {
  const fetchLatLong = `${PLACE_LOCATION_URL}/v3?place_id=${placeId}`;
  try {
    const response = await _get(fetchLatLong);
    return await response.json();
  } catch (e) {
    console.error('Error fetching lat/long details:', e);
    return {};
  }
};

export const getCurrentLocation = async () => {
	await ensureLocationPermGranted();
	return getCurrentPosition();
};

export const getCurrentPosition = async () => {
	const latLng = await getCurrentLatLng();
	return reverseGeocode(latLng.lat, latLng.lng);
};

export const reverseGeocode = async (lat: number, lng: number) => {
	const result = await getReverseGeocodes(lat, lng);
	return getAddress(result);
};

const getAddress = (result: OldLocationType) : LocationInfo => {
	const cityTypes = ['administrative_area_level_2', 'locality', 'administrative_area_level_1'];
	const airportTypes = ['airport'];
	const addressComponents = result.address_components || [];
	const cityName = _filterComponentByType(addressComponents, cityTypes);
	const city = (cityName && cityName.long_name) ?? '';
	const isCity = _intersection(cityTypes, result.types).length > 0;
	const isAirport = _intersection(airportTypes, result.types).length > 0;
	const postalCode = _filterComponentByType(addressComponents, ['postal_code']);
	const pincode = (postalCode && postalCode.short_name) ?? '';
	let { lat, lng } = result.geometry.location;
	if (_isFunction(lat)) {
		lat = lat();
		// @ts-expect-error old code
		lng = lng();
	}
	return {
		address: result.formatted_address,
		place_id: result.place_id,
		latitude: lat,
		longitude: lng,
		pincode,
		city,
		google_city: city,
		is_city: isCity,
		is_airport: isAirport,
	};
};

export const _filterComponentByType = (
	addressComponents: OldLocationType['address_components'],
	types: string[],
) => {
	for (const type of types) {
		for (const addressComponent of addressComponents || []) {
			if (addressComponent?.types?.includes(type)) {
				return addressComponent;
			}
		}
	}
	return null;
};



