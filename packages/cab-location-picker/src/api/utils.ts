import { Platform } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import AsyncStorage from '@react-native-async-storage/async-storage';

import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import OmnitureModule from '@mmt/legacy-commons/Native/OmnitureModule';
import { getUserDetails } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

import { DeviceDetails, OldLocationType } from '../types';
import { APP_CMP_VALUES, APP_DEFAULT_CMP_VALUE, CAB_JOURNEY_ID } from '../utils/constants';
import { generateUUID } from '@mmt/navigation/src/util';

//URLs
const API_SERVER_DOMAIN = 'cabs.makemytrip.com';
const SERVER_URL = Platform.OS === 'web' ? `//${API_SERVER_DOMAIN}` : `https://${API_SERVER_DOMAIN}`;
export const AUTO_COMPLETE_V3 = `${SERVER_URL}/autocomplete/v3`;
export const PLACE_LOCATION_URL = `${SERVER_URL}/fetchLocation`;

//@TODO_VS: check if these variable initialization can be written better
let SESSION_ID: string | null = null;

export const getSessionId = (): string => {
  if (SESSION_ID) {
    return SESSION_ID;
  } else {
    SESSION_ID = `sessionId_${new Date().getTime().toString(16)}_${Math.floor(
      (1 + Math.random()) * 0x10000,
    )
      .toString(16)
      .substring(1)}`;
  }
  return SESSION_ID;
};

export const DEFAULT_HEADERS = {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  session_id: getSessionId(),
};

//Helper functions
export async function getCommonHeaders() {
	const [deviceDetails, userDetails, appLaunchSource, journey_id] = await Promise.all([
		createDeviceDetails(),
		getUserDetails(),
		getAppLaunchSource(),
		getJourneyId(),
	]);

	return {
		'x-app-ver': deviceDetails.appVersion || '',
		'x-app-platform': Platform.OS,
		'x-gommt-brand': 'MMT',
		'x-mmt-auth': userDetails?.mmtAuth ?? '',
		'x-device-id': deviceDetails.deviceId,
		'x-uuid': userDetails?.uuid ?? '',
		'x-funnel-source': appLaunchSource.toLowerCase(),
		'x-profile-type': userDetails.profileType,
		'x-session-id': getSessionId(),
		journey_id: journey_id || '', // @TODO_VS:: Check if this is required
		request_id: generateUUID(), // @TODO_VS:: Check if this is required
	};
}

export const createDeviceDetails = async (): Promise<DeviceDetails | Record<string, never>> => {
  let deviceDetails = {} as DeviceDetails;
  if (Platform.OS === 'ios') {
    const deviceInfo = await OmnitureModule.deviceInfo();
    if (!_isEmpty(deviceInfo)) {
      deviceDetails.os = 'ios';
      deviceDetails.osVersion = deviceInfo.dvc_os_ver;
      deviceDetails.deviceType = deviceInfo.dvc_type;
      deviceDetails.appVersion = deviceInfo.dvc_app_ver;
      deviceDetails.deviceId = deviceInfo.dvc_did;
      deviceDetails.networkType = deviceInfo.dvc_conn_type;
      deviceDetails.deviceName = `${deviceInfo.dvc_manuf} ${deviceInfo.dvc_mdl}`;
    }
  } else if (Platform.OS === 'android') {
    const deviceInfo = await GenericModule.getDeviceInfo();
    if (!_isEmpty(deviceInfo)) {

      const { androidVersion, phoneNumber, accountEmailId, ...rest } = deviceInfo;
      deviceDetails = {
        ...rest,
        os: 'android',
        osVersion: androidVersion,
      };
    }
  }

  return deviceDetails;
};

export const getSessionDetails = async () => {
  try {
    if (GenericModule?.getSessionInfo) {
      const deepLinkTrackingData = await GenericModule.getSessionInfo();

      if (deepLinkTrackingData?.sessionCmp?.toLowerCase()?.includes('not set')) {
        deepLinkTrackingData.sessionCmp = '';
      }
      return deepLinkTrackingData;
    }
    return null;
  } catch (err) {
    return null;
  }
};

export const getAppLaunchSource = async () => {
  let launchSource = APP_DEFAULT_CMP_VALUE;
  try {
    const sessionDetails = await getSessionDetails();
    if (sessionDetails?.sessionCmp?.length > 0) {
      for (let i = 0; i < APP_CMP_VALUES.length; i++) {
        if (sessionDetails?.sessionCmp?.startsWith(APP_CMP_VALUES[i])) {
          launchSource = APP_CMP_VALUES[i];
          break;
        }
      }
    }
    return launchSource;
  } catch (err) {
    return APP_DEFAULT_CMP_VALUE;
  }
};

export const getJourneyId = async () => {
  const journeyId = await getJourneyIdtFromStorage();
  return !_isEmpty(journeyId) ? journeyId : null;
};

export const getJourneyIdtFromStorage = async (): Promise<string | null> => {
  try {
    const data = await AsyncStorage.getItem(CAB_JOURNEY_ID);
    if (data) {
      return JSON.parse(data) || '';
    }
    return null;
  } catch (e) {
    console.log('err', e);
    return null;
  }
};
