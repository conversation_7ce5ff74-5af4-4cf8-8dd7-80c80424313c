import { DEFAULT_HEADERS, getCommonHeaders } from './utils';

/**
 *
 * @param {*} url
 * @param {Record<string, unknown>} body
 * @param {HeadersInit} headers
 * @returns
 */
export const _post = async (url, body) => {
  const _headers = {
    ...(await getCommonHeaders()),
  };
  return await fetch(url, {
    method: 'POST',
    body: JSON.stringify(body),
    headers: _headers,
  });
};

/**
 *
 * @param {*} request
 * @param {HeadersInit} headers
 * @returns
 */
export const _get = async (request, headers = DEFAULT_HEADERS) => {
  const _headers = {
		...headers,
    ...(await getCommonHeaders()),
  };
  return await fetch(request, {
    method: 'get',
    headers: _headers,
  });
};
