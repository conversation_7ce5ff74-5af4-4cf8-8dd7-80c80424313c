import React from 'react';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import { View, TouchableOpacity, Text, Image, StyleSheet } from 'react-native';
import BottomSheet from '@Frontend_Ui_Lib_App/BottomSheet';

import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';

import { PermissionOverlay } from '@mmt/cab-location-picker/src/types';

const LocationPermission = ({
  onPermGranted,
  onPermRejected,
  setShowLocationPermission,
  data,
}: {
  onPermGranted: VoidFunction;
  onPermRejected: VoidFunction;
  setShowLocationPermission: React.Dispatch<React.SetStateAction<boolean>>;
  data: PermissionOverlay;
}) => {
	if (_isEmpty(data)) {return null;}
  const { heading, subHeading, iconUrl, desc, acceptText, rejectText } = data || {};

  return (
    <BottomSheet onDismiss={setShowLocationPermission} visible={true}>
      <View style={styles.locationRoot}>
        <View style={styles.locationSettingsContent}>
          <Text style={styles.locationSettingsHeader}>{heading}</Text>
          <Text style={styles.locationSettingsSubHeader}>{subHeading}</Text>
          <View style={styles.rowContainer}>
            <Image style={styles.locationSettingsLocationIcon} source={{ uri: iconUrl }} />
            <Text style={styles.locationSettingsRationaleText}>{desc}</Text>
          </View>
          <View style={styles.locationSettingsActionsContainer}>
            <TouchableOpacity onPress={onPermRejected}>
              <Text style={styles.locationSettingsActionButton}>{rejectText}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={onPermGranted}>
              <Text
                style={[
                  styles.locationSettingsActionButton,
                  styles.locationSettingsActionButtonPrimary,
                ]}
              >
                {acceptText}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </BottomSheet>
  );
};

LocationPermission.propTypes = {
  onPermRejected: PropTypes.func.isRequired,
  onPermGranted: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  locationRoot: {
    backgroundColor: colors.white,
  },
  locationSettingsContent: {
    margin: 24,
  },
  locationSettingsHeader: {
    fontSize: 34,
    fontFamily: fonts.light,
    color: colors.black,
  },
  locationSettingsSubHeader: {
    fontSize: fontSizes.lg,
    marginTop: 16,
    lineHeight: 20,
    marginBottom: 16,
    fontFamily: fonts.light,
    color: colors.black,
  },
  rowContainer: {
    flexDirection: 'row',
    marginRight: 24,
    alignItems: 'center',
  },
  locationSettingsLocationIcon: {
    width: 40,
    resizeMode: 'contain',
  },
  locationSettingsRationaleText: {
    fontSize: fontSizes.lg,
    lineHeight: 18,
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    padding: 8,
  },
  locationSettingsActionsContainer: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'space-between',
  },
  locationSettingsActionButton: {
    color: colors.azure,
    fontSize: fontSizes.lg,
    fontFamily: fonts.medium,
    lineHeight: 18,
  },
  locationSettingsActionButtonPrimary: {
    fontFamily: fonts.bold,
  },
});

export default LocationPermission;
