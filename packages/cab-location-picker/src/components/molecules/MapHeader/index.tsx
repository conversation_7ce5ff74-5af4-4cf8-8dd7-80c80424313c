import React from 'react';
import _isNil from 'lodash/isNil';
import { View, Image, Text, StyleSheet, TouchableOpacity } from 'react-native';

import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';
import { HeaderData } from '../../../types';

const MapHeaderComponent = ({
  description,
  onMapClosePress,
  isLoading,
  data,
}: {
  description: string;
  onMapClosePress: () => void;
  isLoading?: boolean;
  data: HeaderData;
}) => {
  const {
    closeIcon,
    title = 'Confirm Pickup Location',
    loadingDesc = 'Fetching Location...',
  } = data || {};
  const handleClosePress = () => {
    onMapClosePress();
  };
  return (
    <>
      <View style={headerStyles.container}>
        <TouchableOpacity onPress={handleClosePress}>
          <Image source={{ uri: closeIcon }} style={headerStyles.closeIcon} />
        </TouchableOpacity>
        <Text style={headerStyles.title}>{title}</Text>
      </View>
      <Text style={headerStyles.description} numberOfLines={1}>
        {isLoading ? loadingDesc : description}
      </Text>
    </>
  );
};

const headerStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  closeIcon: {
    marginLeft: 16,
    marginTop: 16.5,
    height: 24,
    width: 24,
  },
  title: {
    fontSize: fontSizes.lg,
    marginLeft: 15,
    fontFamily: fonts.black,
    color: colors.black,
    fontWeight: '700',
  },
  description: {
    marginLeft: 52,
    marginBottom: 9,
    color: colors.textGrey,
    fontSize: fontSizes.md,
    fontFamily: fonts.regular,
    fontWeight: '400',
    marginRight: 10,
  },
});

export default MapHeaderComponent;
