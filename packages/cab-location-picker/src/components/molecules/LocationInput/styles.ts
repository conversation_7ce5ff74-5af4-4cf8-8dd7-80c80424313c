import { Platform, StyleSheet } from 'react-native';
import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  inputContainer: {
    marginBottom: 12,
  },
  locationInputWpr: {
    inputFieldStyle: {
      fontFamily: fonts.black,
      fontSize: fontSizes.lg,
      backgroundColor: colors.white,
      height: 50,
    },
    ...(Platform.OS === 'ios' && {
      textFieldStyle: {
        lineHeight: 36,
      },
    }),
    labelStyle: {
      fontSize: fontSizes.sm,
      fontFamily: fonts.bold,
    },
    startIconHolderStyle: {
      width: 25,
      height: 20,
      top: 13,
    },
  },
  leftIconStyle: {
    height: 24,
    width: 24,
  },
  startIconActive: { tintColor: colors.azure },
  leftIcContainerStyle: {
    position: 'relative',
    top: 0,
    left: 0,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  textInputStyles: {
    marginLeft: 8,
    fontWeight: '400',
    flex: 1,
  },
  custTitleStyle: {
    left: 40,
  },
});
