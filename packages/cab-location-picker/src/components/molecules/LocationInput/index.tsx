import React, { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';

import { styles } from './styles';

import { LocationField } from '../../../types';
import FloatingInput from '../../atoms/FloatingInput';

interface LocationInputProps {
  value: LocationField;
  handleSearch: (val: string) => void;
  updatedFocussedField: (id: string) => void;
  focussedFieldID: string;
  onStartIconPress: (actionId: string | null, fieldId: string) => void;
}

const LocationInput = ({
  value,
  handleSearch,
  updatedFocussedField,
  focussedFieldID,
  onStartIconPress,
}: LocationInputProps) => {
  const { label, placeholder, id, leftIconAction, leftIconUrl, locationInfo, isFocused } = value || {};

  const [isInputActive, setInputActive] = useState(false);
  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    setInputValue(locationInfo?.address);
  }, [value]);

  useEffect(() => {
    if (id === focussedFieldID) {
      setInputActive(true);
    } else {
      setInputActive(false);
    }
  }, [focussedFieldID]);

  const handleFocus = () => {
    setInputActive(true);
    setInputValue('');
    updatedFocussedField(value?.id);
  };

  const handleBlur = () => {
    setInputValue(locationInfo?.address);
  };

  const handleChange = (val: string) => {
    setInputValue(val);
    handleSearch(val);
  };

  return (
    <View style={styles.inputContainer}>
      <FloatingInput
        customStyle={{
          ...styles.locationInputWpr,
          startIconStyle: isInputActive ? styles.startIconActive : styles.leftIconStyle,
        }}
        inputProps={{
          numberOfLines: 1,
          placeholder: !isInputActive && !inputValue ? '' : placeholder,
        }}
        label={label}
        labelAnimationLeftValue={0}
        onChangeText={handleChange}
        startIconVerticalOffset={0}
        value={inputValue}
        onBlur={handleBlur}
        onFocus={handleFocus}
        startIcon={leftIconUrl}
        onStarIconPress={() => onStartIconPress(leftIconAction, id)}
        autoFocus={isFocused}
      />
    </View>
  );
};

export default LocationInput;
