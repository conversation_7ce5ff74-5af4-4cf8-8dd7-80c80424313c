import React, { useState } from 'react';
import _isNil from 'lodash/isNil';
import { View, Text, Image, StyleSheet, TouchableOpacity, Platform } from 'react-native';

import Button from '@Frontend_Ui_Lib_App/Button';
import Shimmer from '@Frontend_Ui_Lib_App/Shimmer';
import ButtonLoader from '@RN_UI_Lib/ButtonLoader';

import { colors, fonts, fontSizes, gradient } from '@mmt/legacy-commons/Styles/globalStyles';

import { FooterData, LocationInfo } from '../../../types';
import {
  currentLocationIcon,
  zoomInIcon,
  zoomOutIcon,
} from '@mmt/post-sales/src/cab/CabImageConstants';

interface MapFooterProps {
  setData: (location: LocationInfo) => void;
  address: string;
  locationData: LocationInfo | Record<string, never>;
  isLoading: boolean;
  mapLoaded: boolean;
  data: FooterData;
  pinToCurrentLocation: () => void;
  toogleHeight: () => void;
  isExpanded: boolean;
}
const MapFooter = ({
  setData,
  address,
  locationData,
  isLoading,
  mapLoaded,
  data,
  pinToCurrentLocation,
  toogleHeight,
  isExpanded,
}: MapFooterProps) => {
  if (!isLoading && !mapLoaded) {
    return null;
  }

  const { locationIcon, ctaText, loadingText } = data || {};

  const handleConfirmClick = () => {
    if (locationData) {
      setData(locationData);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.mapFooterActions}>
        {Platform.OS !== 'ios' && (
          <TouchableOpacity onPress={toogleHeight} style={styles.toggleHeight}>
            <Image source={isExpanded ? zoomOutIcon : zoomInIcon} style={styles.currLocIcon} />
          </TouchableOpacity>
        )}
        {!!locationIcon && (
          <TouchableOpacity onPress={pinToCurrentLocation}>
            <Image source={currentLocationIcon} style={styles.currLocIcon} />
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.content}>
        <View style={styles.row}>
          <View style={styles.locationContainer}>
            <Image source={{ uri: locationIcon }} style={styles.icon} />

            <Text style={styles.mainText} numberOfLines={2}>
              {isLoading && !!loadingText ? loadingText : address}
            </Text>
          </View>
        </View>
        {isLoading ? (
          <>
            <View style={styles.shimmerContainer}>
              <Shimmer width={276} height={12} marginBottom={4} />
              <Shimmer width={171} height={12} />
            </View>
            <View style={styles.loaderContainer}>
              <ButtonLoader gradientColor={gradient.btnBackgroundGradient} />
            </View>
          </>
        ) : (
          <View style={styles.buttonContainer}>
            <Button
              buttonText={ctaText || 'Confirm'}
              buttonType="fill"
              buttonSize="md"
              buttonWidth="full"
              onButtonPress={handleConfirmClick}
              customStyle={{
                buttonTextStyle: styles.buttonText,
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mapFooterActions: {
    position: 'absolute',
    bottom: 120,
    right: 0,
  },
  toggleHeight: {
    marginBottom: 5,
  },
  currLocIcon: {
    width: 45,
    height: 45,
  },
  container: {
    position: 'absolute',
    maxWidth: 328,
    backgroundColor: colors.white,
    borderRadius: 16,
    alignSelf: 'center',
    flexDirection: 'row',
    bottom: 16,
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 4,
    marginLeft: 16,
    marginRight: 16,
  },
  content: {
    padding: 12,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 14,
    height: 18,
    top: 2,
  },
  mainText: {
    fontSize: fontSizes.md,
    fontFamily: fonts.black,
    marginLeft: 10,
    color: colors.black,
    marginRight: 10,
  },
  secondText: {
    fontSize: fontSizes.md,
    fontFamily: fonts.regular,
    marginLeft: 23,
    color: colors.black,
    marginRight: 10,
    marginTop: 5,
  },
  buttonContainer: {
    minWidth: 304,
    marginTop: 16,
    marginRight: 12,
  },
  buttonText: {
    fontSize: fontSizes.md,
    fontFamily: fonts.black,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexBasis: '80%',
  },
  addFavourtiesContainer: {
    flexBasis: '20%',
    alignItems: 'flex-end',
  },
  shimmerContainer: {
    marginLeft: 24,
  },
  loaderContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
});

export default MapFooter;
