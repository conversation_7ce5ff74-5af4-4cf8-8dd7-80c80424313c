import React from 'react';
import { View, Image, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { SetLocationCTA } from '@mmt/cab-location-picker/src/types';

const SetLocationUsingMap = ({
  onSetLocationMapClick,
  data,
}: {
  onSetLocationMapClick: VoidFunction;
  data: SetLocationCTA;
}) => {
	const { icon, text } = data || {};

  const handleOnPress = () => {
    onSetLocationMapClick();
  };

  return (
    <TouchableOpacity onPress={handleOnPress}>
      <View style={styles.optionsHeaderContainer}>
        <Image
          source={{ uri: icon }}
          style={styles.newOptionsHeaderIcon}
        />
        <View style={AtomicStyles.marginLeft10}>
          <Text style={styles.newOptionsHeaderText}>
            {text || 'Locate on Map'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default SetLocationUsingMap;

const styles = StyleSheet.create({
  optionsHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
		marginBottom: 16,
  },
  newOptionsHeaderIcon: {
    width: 14,
    height: 14,
		tintColor: colors.azure,
  },
  newOptionsHeaderText: {
    fontSize: fontSizes.md,
    fontFamily: fonts.regular,
    fontWeight: '700',
		color: colors.azure,
  },
});
