import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';

import { LocationListItem, LocationsListData, OtherData } from '../../../types';
import { getIconBasedOnLocationType, isInvalidSuggestion } from '../../../utils/helpers';

import { styles } from './styles';

interface SuggestedLocationListProps {
  onSuggestionSelect: (item: LocationListItem) => void;
  data: LocationsListData;
  otherData: OtherData;
}

const SuggestedLocationList = ({
  data,
  onSuggestionSelect,
  otherData,
}: SuggestedLocationListProps) => {
  const { header, icon: iconUrl, locations: suggestions, shouldShow } = data || {};
  if (!shouldShow) {
    return null;
  }

  return (
    <View style={styles.container}>
      {!!header && (
        <View style={styles.header}>
          {!!iconUrl && (
            <Image
              style={styles.locationIconStyle}
              source={{
                uri: iconUrl,
              }}
            />
          )}
          <Text style={styles.headerText}>{header}</Text>
        </View>
      )}
      {suggestions?.map((item, index) => {
        if (isInvalidSuggestion(item)) {
          return null;
        }
        const { main_text, secondary_text } = item;
        const icon = getIconBasedOnLocationType(item, otherData);
        return (
          <TouchableOpacity
            key={index}
            style={styles.suggestedLocation}
            onPress={() => onSuggestionSelect(item)}
          >
            {!!icon && <Image style={styles.locationIconStyle} source={{ uri: icon }} />}
            <View>
              {!!main_text && <Text style={styles.suggestionMainText}>{main_text}</Text>}
              {!!secondary_text && (
                <Text style={styles.suggestionSecondaryText}>{secondary_text}</Text>
              )}
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default SuggestedLocationList;
