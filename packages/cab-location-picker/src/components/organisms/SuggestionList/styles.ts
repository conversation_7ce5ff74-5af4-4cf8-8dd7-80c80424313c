import { StyleSheet } from 'react-native';
import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  suggestedLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 16,
    paddingLeft: 16,
    marginTop: 20,
  },
  locationIconStyle: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  header: {
    backgroundColor: colors.grey5,
    padding: 8,
    paddingRight: 16,
    paddingLeft: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    color: colors.black,
    fontSize: fontSizes.reg,
    fontFamily: fonts.bold,
    lineHeight: 16,
  },
  container: { marginBottom: 20 },
  suggestionMainText: {
    color: colors.black,
    fontSize: fontSizes.reg,
    fontWeight: 'bold',
    lineHeight: 16,
    marginBottom: 3,
  },
  suggestionSecondaryText: {
    color: colors.greyLight,
    fontSize: fontSizes.reg,
    lineHeight: 16,
    fontFamily: fonts.regular,
  },
  suggestedLocationContainer: {
    zIndex: 100,
    marginLeft: -16,
    marginRight: -16,
  },
});
