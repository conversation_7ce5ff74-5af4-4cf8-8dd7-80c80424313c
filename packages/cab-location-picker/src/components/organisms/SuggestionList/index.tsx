import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import LocationList from './LocationList';
import { ScrollView } from 'react-native-gesture-handler';

import { styles } from './styles';

interface SuggestionsListProps {
  curentFocusedFieldID: string;
  suggestions: any[];
  onSuggestionSelect: (item: any, suggestionType: string) => void;
  otherData: any;
}

const SuggestionsList = ({
  curentFocusedFieldID,
  suggestions,
  onSuggestionSelect,
  otherData,
}: SuggestionsListProps) => {
  if (!curentFocusedFieldID || _isEmpty(suggestions)) {
    return null;
  }

  return (
    <ScrollView style={styles.suggestedLocationContainer} keyboardShouldPersistTaps="handled">
      {suggestions?.map((locationData) => (
        <LocationList
          key={locationData?.type}
          data={locationData}
          onSuggestionSelect={(item) => onSuggestionSelect(item, locationData?.type)}
          otherData={otherData}
        />
      ))}
    </ScrollView>
  );
};

export default SuggestionsList;
