import React from 'react';
import { View, Platform } from 'react-native';
import BottomSheet from '@Frontend_Ui_Lib_App/BottomSheet';
import MapView from './GoogleMapView';
import LocationPermission from '../../molecules/LocationPermission';
import SetLocationCTA from '../../molecules/SetLocationCTA';
import { LocationField } from '../../../types';

interface MapSectionProps {
  curentFocusedFieldID: string;
  showMap: boolean;
  setShowMap: (val: boolean) => void;
  onSetLocationMapClick: () => void;
  onLocationPermGranted: () => void;
  mapData: any;
  showLocationPermission: boolean;
  setShowLocationPermission: (val: boolean) => void;
  locationFieldsDataCopy: any;
  curentFocusedFieldIDForMap: string;
  onMapLocationSelected: (location: any) => void;
}

const MapSection: React.FC<MapSectionProps> = ({
  curentFocusedFieldID,
  showMap,
  setShowMap,
  onSetLocationMapClick,
  onLocationPermGranted,
  mapData,
  showLocationPermission,
  setShowLocationPermission,
  locationFieldsDataCopy,
  curentFocusedFieldIDForMap,
  onMapLocationSelected,
}) => {
  const handleMapClose = () => setShowMap(false);
	const handleReject = () => setShowLocationPermission(false);

  const sourceData = locationFieldsDataCopy?.fieldList?.find(
    (field: LocationField) => field.id === curentFocusedFieldIDForMap,
  )?.locationInfo || {};

  return (
    <View>
      {!!curentFocusedFieldID && (
        <SetLocationCTA
          onSetLocationMapClick={onSetLocationMapClick}
          data={mapData?.setLocationCTA}
        />
      )}
      {showLocationPermission && (
        <LocationPermission
          onPermGranted={onLocationPermGranted}
          onPermRejected={handleReject}
          setShowLocationPermission={setShowLocationPermission}
          data={mapData?.permissionOverlay}
        />
      )}
      {showMap && (
        <BottomSheet
          visible={showMap}
          setVisible={setShowMap}
          customStyles={{
            containerStyle: [
              {
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
              },
              Platform.OS === 'ios' ? { bottom: 0 } : null,
            ],
          }}
          onRequestClose={handleMapClose}
        >
          <MapView
            onMapClosePress={handleMapClose}
            onLocationSelected={onMapLocationSelected}
            sourceData={sourceData}
            mapData={mapData}
						focusedFieldID={curentFocusedFieldIDForMap}
          />
        </BottomSheet>
      )}
    </View>
  );
};

export default MapSection;
