import { Dimensions } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import Animated, {
  Easing,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

import CustomMarker from '../../atoms/CustomMarker';
import MapFooter from '../../molecules/MapFooter';
import MapHeaderComponent from '../../molecules/MapHeader';

import { LocationInfo, MapData } from '../../../types';
import useMapLocations from '../../../hooks/useMapLocations';

const GoogleMapView = ({
  sourceData,
  onLocationSelected,
  onMapClosePress,
  mapData,
	focusedFieldID,
}: {
  sourceData: LocationInfo | Record<string, never>;
  onLocationSelected: (location: LocationInfo | Record<string, never>) => void;
  onMapClosePress: () => void;
  mapData: MapData;
	focusedFieldID: string;
}) => {
  //TODO_VS: add null checks & fallback values
  const { header, footer, marker } = mapData || {};

  const { pinnedLocation, userLocation, fetchLocationName } = useMapLocations(sourceData);
  const [isLoading, setIsLoading] = useState(false); //TODO_VS: check this logic
  const [mapLoaded, setMapLoaded] = useState(false);
  const [region, setRegion] = useState<Region>();
  const [isExpanded, setIsExpanded] = useState(false);

  const sourceMapRef = useRef<MapView>(null);
  const heightAnim = useSharedValue(Dimensions.get('window').height * 0.9); //TODO_VS: add this value in a constant

  useEffect(() => {
    if (userLocation) {
      setIsLoading(false);
    }
  }, [userLocation]);

  const handleRegionChangeComplete = (newRegion: Region) => {
    if (sourceMapRef) {
      fetchLocationName(newRegion);
      setRegion(newRegion);
    }
  };

  const handleMapLoad = () => {
    setMapLoaded(true);
  };

  const pinToCurrentLocation = () => {
    sourceMapRef?.current &&
      userLocation?.latitude &&
      userLocation?.longitude &&
      sourceMapRef.current.animateToRegion({
        latitude: userLocation?.latitude,
        longitude: userLocation?.longitude,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      });
  };

  const toggleHeight = () => {
    const newHeightPercent = isExpanded ? 0.9 : 1; //TODO_VS: add this value in a constant
    heightAnim.value = withTiming(Dimensions.get('window').height * newHeightPercent, {
      duration: 200,
      easing: Easing.inOut(Easing.ease),
    });
    setIsExpanded(!isExpanded);
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: heightAnim.value,
    };
  });

  return (
    <Animated.View style={[AtomicCss.flex1, animatedStyle]}>
      <MapHeaderComponent
        description={pinnedLocation?.address}
        onMapClosePress={onMapClosePress}
        isLoading={isLoading}
        data={header}
      />
      {isLoading ? (
        <MapView
          initialRegion={{
            latitude: pinnedLocation?.latitude || 20.5937,
            longitude: pinnedLocation?.longitude || 78.9629,
            latitudeDelta: 0.128,
            longitudeDelta: 0.128,
          }}
          showsMyLocationButton={false}
          provider={PROVIDER_GOOGLE}
          style={[AtomicCss.flex1]}
        />
      ) : (
        <>
          <MapView
            ref={sourceMapRef}
            style={[AtomicCss.flex1]}
            initialRegion={
              pinnedLocation
                ? {
                    latitude: pinnedLocation?.latitude,
                    longitude: pinnedLocation?.longitude,
                    latitudeDelta: 0.18893152226362986,
                    longitudeDelta: 0.1253211870789812,
                  }
                : undefined
            }
            onRegionChangeComplete={handleRegionChangeComplete}
            showsUserLocation={true}
            showsBuildings={true}
            zoomEnabled={true}
            showsMyLocationButton={false}
            rotateEnabled={false}
            provider={PROVIDER_GOOGLE}
            onMapLoaded={handleMapLoad}
            onMapReady={handleMapLoad}
          />
          <CustomMarker title={pinnedLocation?.address} data={marker} fieldId={focusedFieldID}/>
        </>
      )}
      <MapFooter
        isLoading={isLoading}
        setData={onLocationSelected}
        address={pinnedLocation?.address}
        locationData={pinnedLocation}
        mapLoaded={mapLoaded}
        data={footer}
        pinToCurrentLocation={pinToCurrentLocation}
        toogleHeight={toggleHeight}
        isExpanded={isExpanded}
      />
    </Animated.View>
  );
};

export default GoogleMapView;
