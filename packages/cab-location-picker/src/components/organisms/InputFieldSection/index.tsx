import React from 'react';
import { StyleSheet, View } from 'react-native';
import LocationInput from '../../molecules/LocationInput';
import { LocationField } from '@mmt/cab-location-picker/src/types';

interface LocationInputProps {
  locationFieldsDataCopy: any;
  handleSearch: any;
  onUpdatedFieldFocus: any;
  curentFocusedFieldID: string;
  onInputStartIconPress: any;
}

function InputFieldSection({
  locationFieldsDataCopy,
  handleSearch,
  onUpdatedFieldFocus,
  curentFocusedFieldID,
  onInputStartIconPress,
}: LocationInputProps) {
  return (
    <View style={styles.searchSection}>
      {locationFieldsDataCopy?.fieldList?.map((field: LocationField) => (
        <LocationInput
          value={field}
          handleSearch={handleSearch}
          updatedFocussedField={(id) => onUpdatedFieldFocus(id)}
          focussedFieldID={curentFocusedFieldID}
          onStartIconPress={onInputStartIconPress}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  searchSection: {
    marginBottom: 10,
  },
});

export default InputFieldSection;
