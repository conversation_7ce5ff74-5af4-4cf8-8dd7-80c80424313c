import {
  ImageSourcePropType,
  ImageStyle,
  StyleProp,
  TextInputProps,
  TextStyle,
  ViewStyle,
} from 'react-native';

/**
 * IconProps interface for defining the props of the Icon component.
 */
export type IconProps = {
  /**
   * Source of the icon, can be a string, ImageSourcePropType, or any.
   */
  iconSrc: React.ReactNode | string | ImageSourcePropType | any;
  /**
   * Style for the icon.
   */
  iconStyle?: StyleProp<ImageStyle>;
};

export interface FloatingInputProps {
  /**
   * The label to show for the text input
   */
  label?: string;
  /**
   * The size of the label when not focused<br/>
   * When fontSize is changed, lineHeight needs to be passed in customStyles (labelStyle)
   * The default value is 14
   */
  labelDefaultSize?: number;
  /**
   * The size of the label when focused or has value<br/>
   * The default value is 12
   */
  labelActiveSize?: number;
  /**
   * The top value for the label animation<br/>
   * The default value is 12 <br/>
   * You can change this value if you want to change the position of the label when it is focused and during animation
   */
  labelAnimationTopValue?: number;
  /**
   * The left value for the label animation<br/>
   * The default value is 32 <br/>
   * This value only works when the startIcon is passed
   */
  labelAnimationLeftValue?: number;
  /**
   * The value to show for the text input. TextInput is a controlled component, which means the native value will be forced to match this value prop if provided. For most uses, this works great, but in some cases this may cause flickering - one common cause is preventing edits by keeping value the same. In addition to simply setting the same value, either set initial values, or listen to onChange events to keep the value up to date.
   */
  value?: string;
  /**
   * ref to the input field
   */
  inputRef?: any;
  /**
   * The icon to show at the start of the input field
   */
  startIcon?: any;
  /**
   * The vertical offset of the start icon when input field is focused and during animation<br/>
   * The default value is: 7
   */
  startIconVerticalOffset?: number;
  /**
   * If true, the input field will be focused when it is mounted. The default value is false.
   */
  autoFocus?: boolean;
  /**
   * Border color of the input field when it is focused
   */
  focusBorderColor?: string;
  /**
   * Label color of the input field when it is focused
   */
  focusLabelColor?: string;
  /**
   * Background color of the input field when it is focused
   */
  focusBgColor?: string;
  /**
   * Callback that is called when the text input is focused
   */
  onFocus?: () => void;
  /**
   * Callback that is called when the text input is blurred
   */
  onBlur?: () => void;
  /**
   * Callback that is called when the text input's text changes. Changed text is passed as a single string argument to the callback handler.
   */
  onChangeText?: (text: string) => void;
  /**
   * start icon press callback
   */
  onStarIconPress?: () => void;
  /**
   * rest of the props for the TextInput component
   */
  inputProps?: TextInputProps;
  /**
   * Custom styles for the input field
   */
  customStyle?: {
    /**
     * Custom style for the container
     */
    wrapperStyle?: StyleProp<ViewStyle>;
    /**
     * Custom style for the input wrapper
     */
    inputFieldWrapperStyle?: StyleProp<ViewStyle>;
    /**
     * Custom style for the label
     */
    labelStyle?: StyleProp<TextStyle>;
    /**
     * Custom style for the input field
     */
    inputFieldStyle?: StyleProp<TextStyle>;
    /**
     * Custom style for the error message
     */
    errorMessageStyle?: StyleProp<TextStyle>;
    /**
     * Custom style for the start icon holder
     */
    startIconHolderStyle?: StyleProp<ViewStyle>;
    /**
     * Custom style for the end icon holder
     */
    endIconHolderStyle?: StyleProp<ViewStyle>;
    /**
     * Custom style for the start icon
     */
    startIconStyle?: StyleProp<ImageStyle | ViewStyle>;
    /**
     * Custom style for the end icon
     */
    endIconStyle?: StyleProp<ImageStyle>;
    /**
     * Custom style for the action text holder
     */
    actionTextHolderStyle?: StyleProp<ViewStyle>;
    /**
     * Custom style for the action text
     */
    actionTextStyle?: StyleProp<TextStyle>;
    /**
     * Custom style for the end text
     */
    endTextStyle?: StyleProp<TextStyle>;
    /**
     * Custom style for the required text
     */
    requiredTextStyle?: StyleProp<TextStyle>;

    /**
     * Custom style for the text
     */
    textFieldStyle?: StyleProp<TextStyle>;
  };
}
