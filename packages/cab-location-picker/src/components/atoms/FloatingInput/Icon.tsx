import React from 'react';
import { IconProps } from './props';
import { Image } from 'react-native';
import { styles } from './styles';

const Icon = ({ iconSrc, iconStyle }: IconProps) => {
  if (React.isValidElement(iconSrc)) {
    return iconSrc;
  }

  return (
    <Image
      source={typeof iconSrc === 'string' ? { uri: iconSrc } : iconSrc}
      style={[styles.iconStyle, iconStyle]}
    />
  );
};
export default Icon;
