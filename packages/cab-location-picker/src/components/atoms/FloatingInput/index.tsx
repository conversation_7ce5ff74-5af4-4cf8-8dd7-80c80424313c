import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Platform,
  Animated,
  StyleProp,
  TextStyle,
  ViewStyle,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { FloatingInputProps } from './props';
import { styles } from './styles';
import Icon from './Icon';

const OS = Platform.OS;

const FloatingInput = ({
  label,
  labelAnimationTopValue = OS === 'ios' ? 12 : 14,
  labelAnimationLeftValue = 32,
  labelDefaultSize = 14,
  labelActiveSize = 12,
  value,
  inputRef,
  startIcon,
  startIconVerticalOffset = 7,
  autoFocus,
  focusBorderColor,
  focusLabelColor,
  focusBgColor,
  onFocus = () => {},
  onBlur = () => {},
  onChangeText = () => {},
  onStarIconPress = () => {},
  customStyle = {},
  inputProps,
}: FloatingInputProps) => {
  const inputFieldRef = useRef<TextInput>(null);

  const [containerHeight, setContainerHeight] = useState(0);
  const [labelHeight, setLabelHeight] = useState(0);
  const [startIconWidth, setStartIconWidth] = useState(0);
  const [startIconHeight, setStartIconHeight] = useState(0);

  const [labelTop, setLabelTop] = useState(0);
  const [startIconTop, setStartIconTop] = useState(0);

  const [text, setText] = useState(value);
  const [isFocused, setIsFocused] = React.useState(autoFocus);
  const labelPosition = useRef(new Animated.Value(value ? 1 : 0)).current;

  useEffect(() => {
    setLabelTop((containerHeight - labelHeight) / 2);
  }, [containerHeight, labelHeight]);

  useEffect(() => {
    setStartIconTop((containerHeight - startIconHeight) / 2);
  }, [containerHeight, startIconHeight]);

  const handleLayout = (event: any, type: string) => {
    let { x, y, height, width } = event.nativeEvent.layout;
    height = Math.floor(height);
    width = Math.floor(width);
    switch (type) {
      case 'inputContainer':
        setContainerHeight(height);
        break;
      case 'startIcon':
        setStartIconWidth(width);
        setStartIconHeight(height);
        break;
      case 'label':
        setLabelHeight(height);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    setText(value);
  }, [value]);

  useEffect(() => {
    Animated.timing(labelPosition, {
      toValue: isFocused || !!text ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isFocused, text]);

  const labelXAnimation = labelPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -labelAnimationLeftValue],
  });
  const labelYAnimation = labelPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -labelAnimationTopValue],
  });

  const fontSizeAnimation = labelPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [labelDefaultSize, labelActiveSize],
  });
  const startIconYAnimation = labelPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [0, startIconVerticalOffset],
  });

  const handleStartIconPress = () => onStarIconPress && onStarIconPress();

  const handleChangeText = (text: string) => {
    setText(text);
    onChangeText && onChangeText(text);
  };

  const handleFocus = () => {
    setIsFocused(true);
    onFocus && onFocus();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur && onBlur();
  };

  const labelViewStyles: StyleProp<ViewStyle> = [
    styles.labelView,
    { top: labelTop },
    startIcon && { left: startIconWidth + 20 },
    { transform: [{ translateY: labelYAnimation }] },
    startIcon && {
      transform: [{ translateX: labelXAnimation }, { translateY: labelYAnimation }],
    },
  ];

  const labelStyles: StyleProp<TextStyle> = [
    styles.label,
    customStyle?.labelStyle,
    { fontSize: fontSizeAnimation },
    isFocused && { color: focusLabelColor || '#008CFF' },
  ];

  const inputFieldStyles = [
    styles.inputField,
    (text || isFocused) && { paddingTop: OS === 'android' ? 22 : 12 },
    Platform.OS === 'android' && { paddingBottom: 6 },
    inputProps?.multiline && { height: 100, paddingTop: 24 },
    Platform.OS === 'web' && { outlineStyle: 'none' },
    customStyle?.inputFieldStyle,
    isFocused && {
      borderColor: focusBorderColor || '#008CFF',
      backgroundColor: focusBgColor || '#EAF5FF',
    },
    startIcon && { paddingLeft: startIconWidth + 20 },
  ];

  const startIconStyle = [
    styles.startIconHolder,
    { top: startIconTop },
    customStyle?.startIconHolderStyle,
  ];

	const textStyle = [
    inputFieldStyles,
    { position: 'absolute', width: '100%' },
    customStyle?.textFieldStyle,
  ];

  return (
    <View>
      {inputProps?.multiline && text && <View style={styles.blankView} />}
      {!!label && (
        <Animated.View
          onLayout={(event) => handleLayout(event, 'label')}
          style={labelViewStyles}
          pointerEvents={'none'}
        >
          <Animated.Text style={labelStyles}>{label}</Animated.Text>
        </Animated.View>
      )}
      <View
        onLayout={(event) => handleLayout(event, 'inputContainer')}
        style={[customStyle?.inputFieldWrapperStyle]}
      >
        {!!startIcon && (
          <TouchableOpacity
            onLayout={(event) => handleLayout(event, 'startIcon')}
            onPress={handleStartIconPress}
            activeOpacity={0.6}
            style={startIconStyle}
          >
            <Animated.View
              style={startIcon && { transform: [{ translateY: startIconYAnimation }] }}
            >
              <Icon iconSrc={startIcon} iconStyle={customStyle?.startIconStyle} />
            </Animated.View>
          </TouchableOpacity>
        )}
        {!isFocused && (
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={textStyle}
            onPress={() => inputFieldRef.current?.focus()}
          >
            {text}
          </Text>
        )}
        <TextInput
          style={[inputFieldStyles, { opacity: isFocused ? 1 : 0 }]}
          value={text}
          ref={inputRef || inputFieldRef}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          spellCheck={false}
          textAlignVertical="top"
          {...inputProps}
        />
      </View>
    </View>
  );
};

export default FloatingInput;
