import { StyleSheet } from 'react-native';
import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  inputField: {
    height: 48,
    borderWidth: 1,
    borderColor: colors.lightSilver,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: colors.smokeWhite,
    fontSize: fontSizes.lg,
    lineHeight: 20,
    color: colors.black,
  },
  blankView: {
    width: '99%',
    height: 24,
    backgroundColor: colors.smokeWhite,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    position: 'absolute',
    left: 1,
    top: 1,
    zIndex: 1,
  },
  labelView: {
    position: 'absolute',
    left: 12,
    zIndex: 1,
  },
  label: {
    fontSize: 14,
    lineHeight: 17,
    color: colors.greyText1,
  },
  iconStyle: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  startIconHolder: {
    position: 'absolute',
    left: 12,
    zIndex: 1,
  },
  endIconHolder: {
    position: 'absolute',
    right: 12,
    zIndex: 1,
  },
  actionTextHolder: {
    position: 'absolute',
    right: 12,
    zIndex: 1,
  },
  endText: {
    position: 'absolute',
    right: 12,
    zIndex: 1,
    fontSize: 12,
    lineHeight: 14,
  },
});
