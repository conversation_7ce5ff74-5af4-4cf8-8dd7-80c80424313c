import { colors, fonts, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';
import React from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';

import { MapMarkerData, MarkerDesc } from '../../../types';

const CustomMarker = ({
  title,
  data,
  fieldId,
}: {
  title: string;
  data: MapMarkerData;
  fieldId: string;
}) => {
  const { desc, icon } = data || {};
  const descText = desc?.[fieldId as keyof MarkerDesc] || '';
  return (
    <View style={styles.markerFixed}>
      <View style={styles.innerContainer}>
        <View style={styles.pointer} />
        {!!title && (
          <Text style={styles.title} numberOfLines={1}>
            {title}
          </Text>
        )}
        {!!descText && <Text style={styles.description}>{descText}</Text>}
      </View>
      {!!icon && <Image style={styles.marker} source={{ uri: icon }} />}
    </View>
  );
};

const styles = StyleSheet.create({
  markerFixed: {
    left: '50%',
    marginLeft: -79, //TODO_VS: check how to make these values dynamic (use onLayout event)
    marginTop: -67, //TODO_VS: check how to make these values dynamic (use onLayout event)
    position: 'absolute',
    top: '50%',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  innerContainer: {
    backgroundColor: colors.black,
    borderRadius: 8,
    width: 157,
    padding: 8,
    marginBottom: 1,
  },
  title: {
    color: colors.white,
    fontFamily: fonts.bold,
    fontSize: fontSizes.md,
  },
  description: {
    color: colors.white,
    textAlign: 'center',
    fontSize: fontSizes.sm,
    lineHeight: 16,
    fontFamily: fonts.regular,
  },
  marker: {
    height: 42,
    width: 16,
    marginTop: 8,
  },
  pointer: {
    position: 'absolute',
    bottom: -10,
    left: '49%',
    width: 0,
    height: 0,
    borderLeftWidth: 9,
    borderRightWidth: 9,
    borderTopWidth: 10,
    borderLeftColor: colors.transparent,
    borderRightColor: colors.transparent,
    borderTopColor: colors.black,
  },
});

export default CustomMarker;
