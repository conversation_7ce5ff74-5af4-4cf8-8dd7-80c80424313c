const ANNOTATION_LABEL = '@pokus-key-check';
const EXPIRY_DURATION = 14170000 * 1000; //(in ms) after 5 months 2 weeks

/**
 *
 * @param {string} comment - Expected format `${ANNOTATION_LABEL} ${TIMESTAMP}`
 */
function checkIfPokusExpired(commentToken) {
  const createdTimestamp = commentToken.value.replace(ANNOTATION_LABEL, '').trim();

  if (createdTimestamp) {
    const todayTimestamp = new Date().getTime();

    return [todayTimestamp - createdTimestamp > EXPIRY_DURATION, false];
  }

  return [false, true];
}

/**
 * Derives pokus key name based on the function which has the comment
 * @returns {string}
 */
function getPokusName(node) {
  if (node.key && node.key.type === 'Identifier') {
    return node.key.name;
  }

  if (node.type === 'FunctionDeclaration' && node.id && node.id.type === 'Identifier') {
    return node.id.name;
  }

  if (node.type === 'VariableDeclaration' && node.declarations.length > 0) {
    return getVariableDecalrationName(node);
  }

  if (node.type === 'ExportNamedDeclaration') {
    return getVariableDecalrationName(node.declaration);
  }
}

function getVariableDecalrationName(node) {
  if (node.type === 'VariableDeclaration' && node.declarations.length > 0) {
    if (node.declarations[0].id.type === 'Identifier') {
      return node.declarations[0].id.name;
    }
  }

  return '';
}

/**
 * Returns the array of comments which has annotation label
 */
function getPokusAnnotatedComments(comments) {
  if (comments.length > 0) {
    return comments.filter((comment) => comment.value.includes(ANNOTATION_LABEL));
  }

  return [];
}

/**
 * Recursively search for parent nodes which has pokus check annotation
 */
function checkIfNodeHasPokusAnnotation(node, sourceCode) {
  const comments = sourceCode.getCommentsBefore(node);

  if (getPokusAnnotatedComments(comments).length > 0) {
    return true;
  } else if (node.parent) {
    return checkIfNodeHasPokusAnnotation(node.parent, sourceCode);
  }

  return false;
}

/**
 * Reads pokus-check annotations and reports error if the pokus key is about to expire.
 * It assumes that all annotations are made only on functions which tells the pokus key value.
 *
 * @type {import("eslint").Rule.RuleModule}
 * */
module.exports = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'Identifies and warn about pokus keys which are going to expire',
    },
    schema: [],
    messages: {
      expiredPokus: "Pokus key '{{pokusKey}}' has expired or is about to expire",
      noAnnotation: `'${ANNOTATION_LABEL}' annotation is required`,
      misingTimestamp: 'Missing timestamp in pokus check rule',
    },
    fixable: 'code',
  },
  create: function (context) {
    const sourceCode = context.getSourceCode();

    return {
      // possible functions declaration - regular, arrow, method, static method
      'FunctionDeclaration, ExportNamedDeclaration, VariableDeclaration, MethodDefinition, ClassProperty:has(:function), Property, PropertyDefinition:has(:function)':
        function (node) {
          const comments = sourceCode.getCommentsBefore(node);
          const pokusAnnotatedComments = getPokusAnnotatedComments(comments);

          for (const comment of pokusAnnotatedComments) {
            const [expired, timestampUnavailable] = checkIfPokusExpired(comment);

            if (expired) {
              context.report({
                node,
                messageId: 'expiredPokus',
                data: {
                  pokusKey: getPokusName(node),
                },
              });
            }


            if (timestampUnavailable) {
              context.report({
                node,
                messageId: 'misingTimestamp',
                fix(fixer) {
                  return fixer.insertTextAfter(comment, ` ${Date.now()}`);
                },
              });
            }
          }
        },
      'CallExpression:has(Identifier[name="getPokusConfig"])': function (node) {
        if (!checkIfNodeHasPokusAnnotation(node, sourceCode)) {
          context.report({
            node,
            messageId: 'noAnnotation',
          });
        }
      },
    };
  },
};
