"use strict";
/**
 * @fileoverview Rule to disallow unnecessary semicolons
 * <AUTHOR>
 */

//------------------------------------------------------------------------------
// Rule Definition
//------------------------------------------------------------------------------
const path = require("path");
const fs = require("fs");
const root = path.resolve(__dirname, "../../../../");

function isSubPathOf(subPath, parentPath) {
  parentPath = normalize(parentPath);
  if (subPath.length <= parentPath.length) {
    return false;
  }
  function normalize(p) {
    p = path.normalize(p);
    if (!p.endsWith(path.sep)) {
      p += path.sep;
    }
    return p;
  }
  subPath = normalize(subPath);
  return subPath.startsWith(parentPath);
}


function readConfig() {
  const configStr = fs.readFileSync(path.resolve(root, "mmt-modules-config.json"), {
    encoding: "utf8",
  });
  const modulesConfig = JSON.parse(configStr);
  return Object.keys(modulesConfig).reduce((acc, modName) => {
    const moduleConfig = modulesConfig[modName];
    return [
      ...acc,
      {
        ...moduleConfig,
        name: modName,
        path: moduleConfig.path.map((p) => path.resolve(root, p)),
        callees: new Set([modName, ...(moduleConfig.dependsOn || [])]),
      },
    ];
  }, []);
}

function ImportValidator() {
  const modulesConfig = readConfig();
  const cache = new Map();

  this.resolveFile = (file, refPath, trimRoot = false) => {
    if (file.startsWith("src")) {
      file = path.resolve(root, file);
    } else if (refPath && file.startsWith(".")) {
      file = path.resolve(path.parse(refPath).dir, file);
    }
    if (trimRoot) {
      return file.replace(root + "/", "");
    }
    return file;
  };

  this.getModule = (file, refPath) => {
    file = this.resolveFile(file, refPath);
    if (cache.has(file)) {
      return cache.get(file);
    }
    const module = modulesConfig.find(
      ({ path: modPaths }) => modPaths.some((modPath) => isSubPathOf(file, modPath)),
    );
    cache.set(file, module);
    return module;
  };

  this.isValidImport = (caller, callee) => {
    const calleeModule = this.getModule(callee, caller);
    if (!calleeModule) { // 3p library
      return true;
    }
    const callerModule = this.getModule(caller, caller);
    if (!callerModule) {
      return false;
    }
    if (callerModule.callees.has(calleeModule.name)) {
      return true
    }
    const calleeFile = this.resolveFile(callee, caller);
    let isPartOfCaller = callerModule.path.some(callerPath => isSubPathOf(calleeFile, callerPath));
    let isPartOfCallee = calleeModule.path.some(calleePath => isSubPathOf(caller, calleePath));
    return isPartOfCaller || isPartOfCallee
  };
}

/** @type {import("eslint").Rule.RuleModule} */
module.exports = {
  meta: {
    messages: {
      invalidImport: "Invalid Import! '{{callee}}' belongs to '{{calleeModule}}' module, which cannot be imported from '{{callerModule}}' module",
    },
  },
  create: function(context) {
    let importValidator = new ImportValidator();
    return {
      // callback functions
      ImportDeclaration: function(node) {
        const importFile = node.source.value;
        const filename = context.getFilename();
        if (importFile.endsWith('.webp')) {
          return;
        }
        if (!importValidator.isValidImport(filename, importFile)) {
          let callee = importValidator.resolveFile(importFile, filename, true);
          let calleeModule = importValidator.getModule(importFile, filename).name;
          let module = importValidator.getModule(filename);
          let callerModule = (module && module.name) || "common";
          context.report({
            node,
            messageId: "invalidImport",
            data: {
              callee,
              calleeModule,
              callerModule,
            },
          });
        }
      },
    };
  },
};
